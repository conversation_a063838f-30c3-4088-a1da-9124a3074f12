﻿//using CefSharp.Web;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using SmartCreator.Models;
using SmartCreator.Models.API;
using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Windows.Forms;
using static System.Net.Mime.MediaTypeNames;

namespace SmartCreator.ViewModels
{
    public class API_Server
    {
        //private string apiBaseAddress6 = "https://api2.alsaidisoft.com/";
        private string apiBaseAddress4 = "https://api.alsaidisoft.com/";
        private string apiBaseAddress3 = "http://api.smartik-manager.com/";
        private string apiBaseAddress2 = "https://api.smrtik.net/";
        private string apiBaseAddress1 = "https://api.ssye.com/";
        //private  string apiBaseAddress = "https://api.smrtik.com/";
        private  string apiBaseAddress = "https://api.smrtye.com/";
        //private string apiBaseAddress = "http://127.0.0.1:8007/";

        private string apiFullAddress = "";
        //private static string apiFullAddress = apiBaseAddress;

        //===========================================================================================
        private Response_api response_result;
        private int kl = 128;
        private string JsonString;
        public API_Server() { }
        public void GetRouter2(string txt)
        {
            try
            {
                string subAddress = "api/SmartDesk9/" + txt;
                var resJsonString = Fun_Response_api_all(subAddress);
                ApiResponse apiResponse = new ApiResponse();
                if (resJsonString != null || resJsonString != "")
                {
                    EasyAES aes = null;
                    var jObj = JObject.Parse(resJsonString);
                    var data2 = jObj["date2"].ToObject<string>();

                    if(data2 == "0")
                        aes = new EasyAES(Global_Variable.Mk_resources.licenseCode, kl, Global_Variable.Mk_resources.RB_SN);
                    else
                        aes = new EasyAES(Global_Variable.Pc_Code, kl, Global_Variable.Pc_Code);

                    var data = jObj["data"].ToObject<string>();
                    string decText = aes.Decrypt(data);
                    //MessageBox.Show("decText\n\n\n" + decText.ToString());

                    response_result = JsonConvert.DeserializeObject<Response_api>(decText);
                    Global_Variable.Response_api  = response_result;
                    Global_Variable.Response_api.IsSuccessStatusCode = true;
                    //MessageBox.Show("decText\n\n\n" + decText.ToString());
                    response_result.setL(response_result);
                    //MessageBox.Show("decText\n\n\n" + decText.ToString());
                }
            }
            catch (Exception ex) { RJMessageBox.Show(" حدثت مشكلة عند الاتصال \n " /*+ ex.Message*/) ; }
        }
        public string Fun_Response_api_all(string subAddress)
        {
            //subAddress= "api/SmartDesk/" + dataUrl;
            //response = new Response_api();
            //var JsonString = "";

            if (Send_Response_all(apiBaseAddress, subAddress) == null)
                if (Send_Response_all(apiBaseAddress1, subAddress) == null)
                    if (Send_Response_all(apiBaseAddress2, subAddress) == null)
                        if (Send_Response_all(apiBaseAddress3, subAddress) == null)
                            if (Send_Response_all(apiBaseAddress4, subAddress) == null)
                                //if (Send_Response_all(apiBaseAddress5, subAddress) == null)
                                    //if (Send_Response_all(apiBaseAddress6, subAddress) == null)
                                    {
                                        Global_Variable.Response_api.IsSuccessStatusCode = false;
                                        RJMessageBox.Show(" خطاء في عملية التحقق");
                                        return null;
                                    }
            return JsonString;
        }
        public string Send_Response_all(string address, string subAddress)
        {
            apiFullAddress = address + subAddress;
            ApiResponse apiResponse = new ApiResponse();
            ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
            //ServicePointManager.SecurityProtocol = SecurityProtocolType.Ssl3 | SecurityProtocolType.Tls | SecurityProtocolType.Tls11 | SecurityProtocolType.Tls12;
            ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
            JsonString = null;
            try
            {
                using (HttpClient httpClient = new HttpClient())
                {
                    apiResponse.IsScuccess = false;
                    using (var response = httpClient.GetAsync(apiFullAddress).Result)
                    {
                        apiResponse.StatusCode = response.StatusCode.ToString();
                        apiResponse.ReasonPhrase = response.ReasonPhrase;
                        if (response.IsSuccessStatusCode)
                        {

                            JsonString = response.Content.ReadAsStringAsync().Result;
                            //response_result = JsonConvert.DeserializeObject<Response_api>(JsonString);
                            //MyDataClass.IsSuccessStatusCode = true;
                            //setLSN(response_result);

                            return JsonString;
                        }
                        else
                        {
                            apiResponse.IsScuccess = false;
                            apiResponse.ErrorMessage = "خطاء في عملية التحقق : \n" + response.StatusCode + "\t" + response.ReasonPhrase;
                            //return null;
                        }
                    }
                }
            }
            catch (Exception ex) {
                string msg = ex.Message;
                return JsonString; 
            }

            return JsonString;

        }

        public  string Send_Response_For_Post(string address, string subAddress, Dictionary<string, string> data)
        {
            apiFullAddress = address + subAddress;
            var json = JsonConvert.SerializeObject(data);
            JsonString = null;
            try
            {
                //ApiResponse apiResponse = new ApiResponse();
                ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
                using (HttpClient httpClient = new HttpClient())
                {
                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Integration", "bhtrdimh=");
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                    //apiResponse.IsScuccess = false;
                    HttpContent content = new StringContent(json, Encoding.UTF8, "application/json");
                    using (var response = httpClient.PostAsync(apiFullAddress, content).Result)
                    {
                        //apiResponse.StatusCode = response.StatusCode.ToString();
                        //apiResponse.ReasonPhrase = response.ReasonPhrase;
                        if (response.IsSuccessStatusCode)
                        {
                            JsonString = response.Content.ReadAsStringAsync().Result;
                            //isSucess = true;  
                            try
                            {
                                return JsonString;
                                //var values = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonString);
                                //isSucess = Convert.ToBoolean(values["status"]);
                            }
                            catch
                            {
                                return null;
                            }
                        }
                        else
                        {
                            //apiResponse.IsScuccess = false;
                            //apiResponse.ErrorMessage = "خطاء في عملية التحقق : \n" + response.StatusCode + "\t" + response.ReasonPhrase;
                        }
                    }
                }
            }
            catch { }


            return JsonString;
        }
      
        public  string Fun_Response_api_For_Post(string subAddress, Dictionary<string, string> data)
        {
            if (Send_Response_For_Post(apiBaseAddress, subAddress, data) == null)
                if (Send_Response_For_Post(apiBaseAddress1, subAddress, data) == null)
                    if (Send_Response_For_Post(apiBaseAddress2, subAddress, data) == null)
                        if (Send_Response_For_Post(apiBaseAddress3, subAddress, data) == null)
                            if (Send_Response_For_Post(apiBaseAddress4, subAddress, data) == null)
                                //if (Send_Response_For_Post(apiBaseAddress5, subAddress, data) == null)
                                    //if (Send_Response_For_Post(apiBaseAddress6, subAddress, data) == null)
                                    {
                                        //MyDataClass.IsSuccessStatusCode = false;
                                        //RJMessageBox.Show(" خطاء في عملية التحقق");
                                        return null;
                                    }
            return JsonString;
        }

        public bool RBAcive(Dictionary<string, string> data)
        {
            bool isSucess = false;
            apiFullAddress = apiBaseAddress + "api/Smartactive9/";
            var json = JsonConvert.SerializeObject(data);
            try
            {
                ApiResponse apiResponse = new ApiResponse();
                ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;
                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

                using (HttpClient httpClient = new HttpClient())
                {

                    httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Integration", "bhtrdimh=");
                    httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                    apiResponse.IsScuccess = false;
                    HttpContent content = new StringContent(json, Encoding.UTF8, "application/json");

                    using (var response = httpClient.PostAsync(apiFullAddress, content).Result)
                    {

                        apiResponse.StatusCode = response.StatusCode.ToString();
                        apiResponse.ReasonPhrase = response.ReasonPhrase;
                        if (response.IsSuccessStatusCode)
                        {
                            var JsonString = response.Content.ReadAsStringAsync().Result;
                            try
                            {
                                var values = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonString);
                                isSucess = Convert.ToBoolean(values["status"]);
                                RJMessageBox.Show(values["msg"]);
                            }
                            catch
                            {
                                return false;
                            }
                        }
                        else
                        {
                            apiResponse.IsScuccess = false;
                            apiResponse.ErrorMessage = "خطاء في عملية التحقق : \n" + response.StatusCode + "\t" + response.ReasonPhrase;
                        }
                    }
                }
            }
            catch { }
            return isSucess;

        }

        public  bool create_session(Dictionary<string, string> data)
        {
            //ApiResponse apiResponse = new ApiResponse();
            bool isSucess = false;
            try
            {
                string subAddress = "api/createSession/";
                var resJsonString = Fun_Response_api_For_Post(subAddress, data);

                if (resJsonString != null || resJsonString != "")
                {
                    var values = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonString);
                    isSucess = Convert.ToBoolean(values["status"]);
                    return isSucess;
                }
            }
            catch { }
            return isSucess;
        }
        public bool rest_msg_Show(Dictionary<string, string> data)
        {
            bool isSucess = false;
            try
            {
                string subAddress = "api/restmsgShow/";
                var resJsonString = Fun_Response_api_For_Post(subAddress, data);
                //ApiResponse apiResponse = new ApiResponse();
                if (resJsonString != null || resJsonString != "")
                {
                    var values = JsonConvert.DeserializeObject<Dictionary<string, string>>(JsonString);
                    isSucess = Convert.ToBoolean(values["status"]);
                    return isSucess;
                }
            }
            catch { }
            return isSucess;
        }



    }
}
