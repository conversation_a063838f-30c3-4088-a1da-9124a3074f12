using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Drawing.Design;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// كنترول تابات صديق للـ Designer - يستخدم SimpleTabPage
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class DesignerFriendlyTabControl : Panel
    {
        private List<SimpleTabPage> _tabs;
        private Panel _tabsPanel;
        private Panel _contentPanel;
        private SimpleTabPage _activeTab;
        private SimpleTabPageCollection _collection;

        public DesignerFriendlyTabControl()
        {
            _tabs = new List<SimpleTabPage>();
            _collection = new SimpleTabPageCollection(this);
            InitializeComponents();
        }

        private void InitializeComponents()
        {
            this.SuspendLayout();

            // Panel التابات
            _tabsPanel = new Panel();
            _tabsPanel.Dock = DockStyle.Top;
            _tabsPanel.Height = 35;
            _tabsPanel.BackColor = Color.FromArgb(55, 55, 58);

            // Panel المحتوى
            _contentPanel = new Panel();
            _contentPanel.Dock = DockStyle.Fill;
            _contentPanel.BackColor = Color.FromArgb(37, 37, 38);

            // الكنترول الرئيسي
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Size = new Size(400, 300);

            // إضافة المكونات
            this.Controls.Add(_contentPanel);
            this.Controls.Add(_tabsPanel);

            this.ResumeLayout(false);
        }

        /// <summary>
        /// مجموعة التابات
        /// </summary>
        [Category("Designer Friendly Tab Control")]
        [Description("Collection of tabs")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Editor(typeof(SimpleTabPageCollectionEditor), typeof(UITypeEditor))]
        public SimpleTabPageCollection Tabs
        {
            get { return _collection; }
        }

        /// <summary>
        /// عدد التابات
        /// </summary>
        [Browsable(false)]
        public int TabCount => _tabs?.Count ?? 0;

        internal void AddTabInternal(SimpleTabPage tab)
        {
            try
            {
                if (tab == null) return;

                tab.Click += Tab_Click;
                _tabs.Add(tab);
                _tabsPanel.Controls.Add(tab);
                _contentPanel.Controls.Add(tab.ContentPanel);

                ArrangeTabs();
                if (_tabs.Count == 1)
                    ActivateTab(tab);
            }
            catch { }
        }

        internal void RemoveTabInternal(SimpleTabPage tab)
        {
            try
            {
                if (tab == null) return;

                tab.Click -= Tab_Click;
                _tabs.Remove(tab);
                _tabsPanel.Controls.Remove(tab);
                _contentPanel.Controls.Remove(tab.ContentPanel);

                if (_activeTab == tab)
                {
                    _activeTab = _tabs.Count > 0 ? _tabs[0] : null;
                    if (_activeTab != null) ActivateTab(_activeTab);
                }

                ArrangeTabs();
            }
            catch { }
        }

        private void Tab_Click(object sender, EventArgs e)
        {
            if (sender is SimpleTabPage tab)
                ActivateTab(tab);
        }

        private void ActivateTab(SimpleTabPage tab)
        {
            try
            {
                if (_activeTab != null)
                {
                    _activeTab.BackColor = Color.FromArgb(70, 70, 70);
                    _activeTab.ContentPanel.Visible = false;
                }

                _activeTab = tab;
                _activeTab.BackColor = Color.FromArgb(0, 122, 204);
                _activeTab.ContentPanel.Visible = true;
                _activeTab.ContentPanel.BringToFront();
            }
            catch { }
        }

        private void ArrangeTabs()
        {
            try
            {
                int x = 2;
                foreach (var tab in _tabs)
                {
                    tab.Location = new Point(x, 2);
                    tab.Size = new Size(100, 31);
                    x += 102;
                }
            }
            catch { }
        }
    }

    /// <summary>
    /// مجموعة SimpleTabPage
    /// </summary>
    public class SimpleTabPageCollection : IList<SimpleTabPage>
    {
        private List<SimpleTabPage> _items;
        private DesignerFriendlyTabControl _parent;

        public SimpleTabPageCollection(DesignerFriendlyTabControl parent)
        {
            _items = new List<SimpleTabPage>();
            _parent = parent;
        }

        public int Count => _items.Count;
        public bool IsReadOnly => false;

        public SimpleTabPage this[int index]
        {
            get => _items[index];
            set
            {
                var oldTab = _items[index];
                _items[index] = value;
                _parent?.RemoveTabInternal(oldTab);
                _parent?.AddTabInternal(value);
            }
        }

        public void Add(SimpleTabPage item)
        {
            if (item != null)
            {
                _items.Add(item);
                _parent?.AddTabInternal(item);
            }
        }

        public void Insert(int index, SimpleTabPage item)
        {
            if (item != null)
            {
                _items.Insert(index, item);
                _parent?.AddTabInternal(item);
            }
        }

        public bool Remove(SimpleTabPage item)
        {
            var removed = _items.Remove(item);
            if (removed) _parent?.RemoveTabInternal(item);
            return removed;
        }

        public void RemoveAt(int index)
        {
            var item = _items[index];
            _items.RemoveAt(index);
            _parent?.RemoveTabInternal(item);
        }

        public void Clear()
        {
            var itemsCopy = new List<SimpleTabPage>(_items);
            _items.Clear();
            foreach (var item in itemsCopy)
                _parent?.RemoveTabInternal(item);
        }

        public bool Contains(SimpleTabPage item) => _items.Contains(item);
        public void CopyTo(SimpleTabPage[] array, int arrayIndex) => _items.CopyTo(array, arrayIndex);
        public int IndexOf(SimpleTabPage item) => _items.IndexOf(item);

        public IEnumerator<SimpleTabPage> GetEnumerator() => _items.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    /// <summary>
    /// محرر مجموعة SimpleTabPage
    /// </summary>
    public class SimpleTabPageCollectionEditor : CollectionEditor
    {
        public SimpleTabPageCollectionEditor(Type type) : base(type) { }

        protected override Type CreateCollectionItemType() => typeof(SimpleTabPage);
        protected override Type[] CreateNewItemTypes() => new Type[] { typeof(SimpleTabPage) };

        protected override object CreateInstance(Type itemType)
        {
            if (itemType == typeof(SimpleTabPage))
            {
                try
                {
                    var tab = new SimpleTabPage($"TabPage{GetItemCount() + 1}");
                    tab.BackColor = Color.FromArgb(70, 70, 70);
                    tab.ForeColor = Color.White;
                    return tab;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Creating tab: {ex.Message}", "Debug");
                    return new SimpleTabPage("TabPage");
                }
            }
            return base.CreateInstance(itemType);
        }

        private int GetItemCount()
        {
            try
            {
                if (Context?.Instance is DesignerFriendlyTabControl tabControl)
                    return tabControl.TabCount;
                return 0;
            }
            catch { return 0; }
        }


        protected override string GetDisplayText(object value) => 
            value is SimpleTabPage tab ? tab.Text : "TabPage";
        protected override bool CanRemoveInstance(object value) => true;
        protected override bool CanSelectMultipleInstances() => false;
    }
}
