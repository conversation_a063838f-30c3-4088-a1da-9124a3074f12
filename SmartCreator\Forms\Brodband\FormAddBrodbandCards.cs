﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Brodband
{
    public partial class FormAddBrodbandCards : RJChildForm
    {
        private bool firstLoad = true;
        //bool isFirstLoadForm = true;
        public Form_PrintUserManagerState Frm_State;
        public FormAddBrodbandCards()
        {
            InitializeComponent();
            InitializeComponent();
            if (Global_Variable.Mk_resources.version >= 7)
            {
                //pnl_attrbut.Visible = true;
                //pnl_customer.Visible = false;
            }
            set_fonts();
        }
        private void set_fonts()
        {
            Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Regular);
            //Font title_font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 13,false);
            lbl_Title2.Font = title_font;
            lbl_Title1.Font = title_font;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            //System.Drawing.Font dgvHeader_font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 9, false);
            rjDataGridView1.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            rjDataGridView1.ColumnHeadersHeight = 35;

            //Font lbl1 = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 9, false);
            Font lbl1 = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            lbl_count.Font = lbl_startCard.Font = lbl_endCards.Font = lbl_UserPassword_Pattern.Font = lbl_group.Font = lbl_Attribute.Font
            = lbl_Customer.Font = lbl_profile.Font = lbl_TemplateCards.Font = lbl_SellingPoint.Font
            = lbl_User_NumberORcharcter.Font = lbl_Pass_NumberORcharcter.Font = lbl_User_NumberORcharcter.Font
            = lbl_ShardUser.Font = lbl_note.Font
            = lbl1;

            btnAdd.Font = btn_add_One.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Bold);
            //btnAdd.Font =btnAdd_Fast.Font=btn_add_One.Font= CustomFonts.Get_Custom_Font("DroidKufi_Regular", 9,true);

            //Font lbl2 = Program.GetCustomFont(Resources.DroidKufi_Regular, 9, FontStyle.Bold);
            Font lbl2 = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
            lbl_FirstUse.Font = lbl_excel.Font = lbl_script_File.Font = lbl_text_File.Font = lbl_RegisterAsBatch.Font
                = lbl_Save_PDF.Font = lbl_OpenAfterPrint.Font = lbl_With_Archive_uniqe.Font = lbl_RegisterAs_LastBatch.Font = lbl2;

            //cbox_User_NumberORcharcter.Font= CBox_SellingPoint.Font = CustomFonts.Get_Custom_Font("Cairo_Regular", 9f,false);
            cbox_User_NumberORcharcter.Font = CBox_SellingPoint.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8, FontStyle.Regular);
            cbox_UserPassword_Pattern.Font = cbox_Pass_NumberORcharcter.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8, FontStyle.Regular);
            //cbox_UserPassword_Pattern.Font= cbox_Pass_NumberORcharcter.Font=  CustomFonts.Get_Custom_Font("Cairo_Regular", 7.75f,false);
            Program.GetCustomFont(Resources.Cairo_ExtraBold, 10, FontStyle.Bold);
            lbl_radio_fast_print.Font = lbl_radio_one_user.Font = lbl_radio_Print.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Bold);
            //groupBox1.ForeColor = UIAppearance.TextColor;

            txtNumberCard.RightToLeft = txt_ShardUser.RightToLeft
                = txt_StartCard.RightToLeft
                = txt_EndCard.RightToLeft = txt_attribute.RightToLeft
                = txt_last_batchNumber.RightToLeft
                = txt_longUsers.RightToLeft
                = txt_longPassword.RightToLeft = RightToLeft.No;

            //lbl_radio_fast_print.Font = lbl_radio_Print.Font = lbl_radio_one_user.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Bold);

        }

        private void FormAddBrodbandCards_Load(object sender, EventArgs e)
        {
            timer1.Start();
            LoadDatagridviewData();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            resize_template_cards_panel();

            Get_TemplateCardsFromDB();
            Get_Cbox_Profile();
            Get_SellingPoint();
            //Get_UMCustomer();

            //isFirstLoadForm = false;
            firstLoad = false;

            loadFromState();
        }
        public void LoadDatagridviewData()
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            List<BatchCard> batch = new List<BatchCard>();
            lock (Smart_DataAccess.Lock_object)
            {
                //using (var db = smart_DataAccess.dbFactory.Open())
                //{
                //    batch = db.Select<BatchCard>(y => y.Server == 0 && y.Rb == Global_Variable.Mk_resources.RB_code).OrderByDescending(y => y.Id).Take(4).ToList();
                //}
                batch = smart_DataAccess.GetListAnyDB<BatchCard>($"select * from BatchCard where Server=0 and Rb='{Global_Variable.Mk_resources.RB_code}' order by Id DESC  LIMIT 4");

            }
            rjDataGridView1.DataSource = batch;
            //rjDataGridView1.DataSource = (from t in batch select t).Take(4).ToList();
            try
            {
                foreach (DataGridViewColumn column in rjDataGridView1.Columns)
                    column.Visible = false;

                rjDataGridView1.Columns["BatchNumber"].Visible = true;
                rjDataGridView1.Columns["Count"].Visible = true;
                rjDataGridView1.Columns["ProfileName"].Visible = true;
                rjDataGridView1.Columns["AddedDate"].Visible = true;

                rjDataGridView1.Columns["BatchNumber"].DisplayIndex = 0;
                rjDataGridView1.Columns["Count"].DisplayIndex = 1;
                rjDataGridView1.Columns["ProfileName"].DisplayIndex = 2;
                rjDataGridView1.Columns["AddedDate"].DisplayIndex = 3;
            }
            catch { }

        }
        private void resize_template_cards_panel()
        {
            //return;  
            //label1.Text=pnlClientArea.Size.ToString();
            //label2.Text= panel_PreviewTempateCards.Size.ToString();
            if (firstLoad || this.WindowState == FormWindowState.Minimized)
                return;
            
            if (pnlClientArea.Height > 680)
            {
                pnl_left2.Height = pnlClientArea.Height - pnl_left.Height - 2;
                pnl_left2.Width = pnl_left.Width;

                //pnl_Right2.Height = 240;
                //pnl_Right2.Refresh();
                pnl_Right3.Height = pnlClientArea.Height - pnl_Right.Height - pnl_Right.Margin.Top - pnl_Right2.Height - pnl_Right2.Margin.Top - 2;
                //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 4);
            }
            else
            {
                pnl_left2.Height = 294;
                pnl_left2.Width = pnl_left.Width;
                //pnl_Right2.Height = 183;
                pnl_Right3.Height = 105;
                //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 2);

            }


            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            tableLayoutPanel1.Refresh();


            lbl_Title2.Location = new Point((pnl_left2.Width / 2) - lbl_Title2.Width / 2, 6);
            lbl_Title1.Location = new Point((pnl_left2.Width / 2) - lbl_Title1.Width / 2, 6);

            if (pnlClientArea.Width > 1000 && pnlClientArea.Width < 1250)
            {
                //this.WindowState == FormWindowState.Normal
                //MessageBox.Show(WindowState.ToString());

                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width) - 10;
                panel_PreviewTempateCards.Location = new Point(8, 43);
            }
            else
            if (pnlClientArea.Width > 1250)
            {
                pnl_left.Refresh();
                panel_PreviewTempateCards.Width = pnl_left2.Width / 2;
                //panel_PreviewTempateCards.Width = pnl_left2.Width / 2 + 150;
                panel_PreviewTempateCards.Location = new Point((pnl_left2.Width / 2) - panel_PreviewTempateCards.Width / 2, 43);
            }
            else
            {
                pnl_left.Refresh();
                //panel_PreviewTempateCards.Width =  (pnl_left2.Width - panel_PreviewTempateCards.Width - 10);
                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width - 10);
                panel_PreviewTempateCards.Location = new Point(8, 43);
            }
            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel4.Refresh();
            panel_PreviewTempateCards.Refresh();
        }
        private void set_BackroundFromPath(string _path)
        {
            try
            {
                //pictureBox1.Image = null;

                if (_path == "")
                    return;
                string sourcePath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";
                string SourcePath_File = System.IO.Path.Combine(sourcePath, _path);
                FileInfo file = new FileInfo(SourcePath_File);
                double sizeInBytes = file.Length;
                try
                {
                    Bitmap img = new Bitmap(SourcePath_File);
                    //pictureBox1.Image = System.Drawing.Image.FromFile(SourcePath_File);
                }
                catch
                {
                    //pictureBox1.Image = null;
                }
            }
            catch
            {
                //pictureBox1.Image = null;
            }

        }

        private void Get_TemplateCardsFromDB()
        {
            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
            }
            catch { }


            //try
            //{
            //    List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("", "0");
            //    if (p.Count > 0)
            //    foreach (SourceCardsTemplate s in p)
            //        comboSource.Add(s.name, s.id.ToString());

            //    CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
            //    CBox_TemplateCards.ValueMember = "Value";
            //    CBox_TemplateCards.DisplayMember = "Key";
            //}
            //catch { }

            //try
            //{
            //    CBox_TemplateCards.Items.Clear();
            //}
            //catch { }
            //try
            //{
            //    List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    CBox_TemplateCards.DataSource = sourceCardsTemplate;
            //    CBox_TemplateCards.DisplayMember = "name";
            //    CBox_TemplateCards.ValueMember = "id";
            //    CBox_TemplateCards.SelectedIndex = -1;

            //}
            //catch { }

            //try
            //{
            //    List<SourceCardsTemplate> sp = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    if (sp.Count == 0)
            //        createDefaultTemplate();
            //    else
            //    {
            //        Dictionary<string, SourceCardsTemplate> comboSource = new Dictionary<string, SourceCardsTemplate>();
            //        foreach (SourceCardsTemplate s in sp)
            //            comboSource.Add(s.name, s);

            //        CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
            //        CBox_TemplateCards.DisplayMember = "Key";
            //        CBox_TemplateCards.ValueMember = "Value";
            //    }
            //}
            //catch { }


            //try
            //{
            //    if (dt_templateCards.Rows.Count <= 0)
            //    {
            //        CreateDefultTemplate();
            //        //SetValuToCardToGraphics();
            //    }
            //}
            //catch { }

        }

        private void CBox_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            //UserManager_Profile_UserManager prf = CBox_Profile.SelectedValue as UserManager_Profile_UserManager;
            //if (prf == null)
            //    return;
            //string profileName = prf.Name;
            string profileName = CBox_Profile.SelectedValue.ToString();

            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);

            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card != null)
                    {
                        if (card.setingCard.proile_link == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                //if (s.name == CBox_TemplateCards.Items[i].ToString())
                                //{
                                //    CBox_TemplateCards.SelectedIndex = i;
                                //    return;
                                //}
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card != null)
                    {
                        if (card.setingCard.proile_link == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                                //DataRowView itemhData = CBox_TemplateCards.Items[i] as DataRowView;

                                //string Pname = "";
                                //if (itemhData != null)
                                //    Pname = (itemhData.Row["name"].ToString());
                                //if (profileName == Pname)
                                //{
                                //    CBox_TemplateCards.SelectedIndex = i;
                                //    return;
                                //}
                            }
                        }
                    }
                }

            }
            //if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) return;

            try { CBox_TemplateCards.SelectedIndex = 1; } catch { }
        }
        private void Get_Cbox_Profile()
        {
            //var categories = new List<SalesAnalysis.SalesByCategory>();
            //categories.AddRange(analysis.SalesByCategoryList);
            //categories.Add(new SalesAnalysis.SalesByCategory { Item = "All" });

            //try
            //{
            //    CBox_Profile.DataSource = Global_Variable.UM_Profile;
            //    CBox_Profile.DisplayMember = "Name";
            //    CBox_Profile.ValueMember = "Name";
            //    CBox_Profile.Text = "";
            //    CBox_Profile.SelectedIndex = -1;

            //}
            //catch { }

            try
            {
                List<UmProfile> p = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile s in p)
                    comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

                CBox_Profile.DataSource = new BindingSource(comboSource, null);
                CBox_Profile.DisplayMember = "Value";
                CBox_Profile.ValueMember = "Key";
            }
            catch { }

            //try
            //{
            //    //List<UserManager_Profile_UserManager> sp = Global_Variable.UM_Profile;
            //    Dictionary<string, UserManager_Profile_UserManager> comboSource = new Dictionary<string, UserManager_Profile_UserManager>();
            //    comboSource.Add("", new UserManager_Profile_UserManager());
            //    foreach (UserManager_Profile_UserManager s in Global_Variable.UM_Profile)
            //        comboSource.Add(s.Name+" ("+s.Price+")", s);

            //    CBox_Profile.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile.DisplayMember = "Key";
            //    CBox_Profile.ValueMember = "Value";
            //}
            //catch { }


        }

        private void Get_SellingPoint()
        {
            try
            {

                Smart_DataAccess da = new Smart_DataAccess();
            CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void loadFromState()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormBrodbandPrint");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());

            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            Frm_State.is_add_batch_cards = false;
            Frm_State.is_add_batch_cards_to_Archive = false;
            Frm_State.is_Add_One_Card = false;
            txtNumberCard.Text = Frm_State.txtNumberCard;
            txt_StartCard.Text = Frm_State.txt_StartCard;
            txt_EndCard.Text = Frm_State.txt_EndCard;
            txt_ShardUser.Text = Frm_State.txt_ShardUser;
            txt_longUsers.Text = Frm_State.txt_longUsers;
            txt_longPassword.Text = Frm_State.txt_longPassword;

            cbox_UserPassword_Pattern.SelectedIndex = Frm_State.cbox_UserPassword_Pattern;
            cbox_User_NumberORcharcter.SelectedIndex = Frm_State.cbox_User_NumberORcharcter;
            cbox_Pass_NumberORcharcter.SelectedIndex = Frm_State.cbox_Pass_NumberORcharcter;

            checkBoxFirstUse.Check = Frm_State.checkBoxFirstUse;
            checkBoxSaveTo_PDF.Check = Frm_State.checkBoxSaveTo_PDF;
            checkBoxSaveTo_excel.Check = Frm_State.checkBoxSaveTo_excel;
            checkBoxOpenAfterPrint.Check = Frm_State.checkBoxOpenAfterPrint;
            checkBoxSaveTo_script_File.Check = Frm_State.checkBoxSaveTo_script_File;
            checkBox_With_Archive_uniqe.Check = Frm_State.checkBox_Create_without_Add_ToMicrotik;
            checkBoxSaveTo_text_File.Check = Frm_State.checkBoxSaveTo_text_File;
            checkBox_note.Check = Frm_State.checkBox_note;
            txt_note.Text = Frm_State.txt_note;

        }

        private void SaveFromState()
        {
            Frm_State.txtNumberCard = txtNumberCard.Text;
            Frm_State.txt_StartCard = txt_StartCard.Text;
            Frm_State.txt_EndCard = txt_EndCard.Text;
            Frm_State.txt_ShardUser = txt_ShardUser.Text;

            Frm_State.txt_longUsers = txt_longUsers.Text;
            Frm_State.txt_longPassword = txt_longPassword.Text;

            Frm_State.cbox_UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;
            Frm_State.cbox_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedIndex;
            Frm_State.cbox_Pass_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedIndex;

            Frm_State.checkBoxFirstUse = checkBoxFirstUse.Check;
            Frm_State.checkBoxSaveTo_PDF = checkBoxSaveTo_PDF.Check;
            Frm_State.checkBoxSaveTo_excel = checkBoxSaveTo_excel.Check;
            Frm_State.checkBoxOpenAfterPrint = checkBoxOpenAfterPrint.Check;
            Frm_State.checkBoxSaveTo_script_File = checkBoxSaveTo_script_File.Check;
            Frm_State.checkBox_Create_without_Add_ToMicrotik = checkBox_With_Archive_uniqe.Check;
            Frm_State.checkBoxSaveTo_text_File = checkBoxSaveTo_text_File.Check;
            Frm_State.checkBox_note = checkBox_note.Check;
            Frm_State.txt_note = txt_note.Text;


            string formSetting = JsonConvert.SerializeObject(Frm_State);
            Smart_DataAccess.Setting_SaveState_Forms_Variables("FormBrodbandPrint", "SaveFromState", formSetting);

        }

    }
}
