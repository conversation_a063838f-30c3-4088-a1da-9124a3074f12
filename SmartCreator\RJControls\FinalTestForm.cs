using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج الاختبار النهائي للتحديثات
    /// </summary>
    public partial class FinalTestForm : Form
    {
        private RJTabControl tabControl;

        public FinalTestForm()
        {
            InitializeComponent();
            SetupTabControl();
            AddTestTabs();
        }

        private void InitializeComponent()
        {
            this.Text = "الاختبار النهائي - RJTabControl مع RJPanel";
            this.Size = new Size(1000, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void SetupTabControl()
        {
            // إنشاء TabControl مع الإعدادات النهائية
            tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 45,
                TabSpacing = 3,
                TabPadding = 25,
                ContentBorderSize = 1,
                ContentBorderColor = Color.FromArgb(200, 200, 200),
                ContentBorderRadius = 0
                // tabsPanel و contentPanel بألوان افتراضية
            };

            this.Controls.Add(tabControl);
        }

        private void AddTestTabs()
        {
            // تاب عرض الميزات
            var featuresTab = tabControl.AddTab("الميزات الجديدة", IconChar.Star);
            var featuresPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };
            
            var featuresLabel = new Label
            {
                Text = "🎉 الميزات الجديدة في RJTabControl:\n\n" +
                       "✅ tabsPanel الآن RJPanel (بخلفية افتراضية)\n" +
                       "✅ contentPanel الآن RJPanel (بخلفية افتراضية)\n" +
                       "✅ RJPanel يدعم BorderSize و BorderColor\n" +
                       "✅ RJTextBox يدعم ReadOnly\n\n" +
                       "🎨 خصائص جديدة للتحكم:\n" +
                       "• TabsPanelBorderSize/Color/Radius\n" +
                       "• ContentBorderSize/Color/Radius\n" +
                       "• TabsPanelBackColor\n" +
                       "• ContentBackColor\n\n" +
                       "🔧 الألوان الافتراضية محفوظة كما طلبت!",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                TextAlign = ContentAlignment.TopLeft
            };
            featuresPanel.Controls.Add(featuresLabel);
            featuresTab.AddControl(featuresPanel);

            // تاب اختبار RJPanel
            var panelTab = tabControl.AddTab("RJPanel", IconChar.Square);
            var testPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 3,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 15,
                Padding = new Padding(30)
                // BackColor افتراضي
            };

            var panelLabel = new Label
            {
                Text = "🎨 RJPanel مع الحدود الجديدة!\n\n" +
                       "BorderSize = 3\n" +
                       "BorderColor = أخضر\n" +
                       "BorderRadius = 15\n" +
                       "BackColor = افتراضي (لم يتم تغييره)\n\n" +
                       "هذا Panel يستخدم الخصائص الجديدة\n" +
                       "مع الحفاظ على اللون الافتراضي!",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80),
                TextAlign = ContentAlignment.MiddleCenter
            };
            testPanel.Controls.Add(panelLabel);
            panelTab.AddControl(testPanel);

            // تاب اختبار RJTextBox
            var textBoxTab = tabControl.AddTab("RJTextBox", IconChar.Edit);
            var textBoxPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var readOnlyTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 RJTextBox مع ReadOnly!\n\n" +
                       "ReadOnly = true\n" +
                       "هذا النص للقراءة فقط\n" +
                       "لا يمكن تعديله أو تغييره\n\n" +
                       "جرب النقر والكتابة - لن يحدث شيء!\n\n" +
                       "مفيد لعرض المعلومات والنصوص\n" +
                       "التي لا تحتاج لتعديل من المستخدم.\n\n" +
                       "🔒 محمي من التعديل!",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(244, 67, 54),
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };
            textBoxPanel.Controls.Add(readOnlyTextBox);
            textBoxTab.AddControl(textBoxPanel);

            // تاب اختبار التحكم في الحدود
            var controlTab = tabControl.AddTab("التحكم", IconChar.Cogs);
            var controlPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // أزرار التحكم
            var tabsBorderButton = new RJButton
            {
                Text = "حدود منطقة التابات",
                IconChar = IconChar.BorderStyle,
                Location = new Point(20, 20),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            tabsBorderButton.Click += (s, e) => ToggleTabsPanelBorder();

            var contentBorderButton = new RJButton
            {
                Text = "حدود منطقة المحتوى",
                IconChar = IconChar.BorderAll,
                Location = new Point(240, 20),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            contentBorderButton.Click += (s, e) => ToggleContentBorder();

            var resetButton = new RJButton
            {
                Text = "إعادة تعيين",
                IconChar = IconChar.Refresh,
                Location = new Point(460, 20),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            resetButton.Click += (s, e) => ResetBorders();

            var infoLabel = new Label
            {
                Text = "🎮 استخدم الأزرار أعلاه لاختبار الحدود الجديدة!\n\n" +
                       "• حدود منطقة التابات (tabsPanel)\n" +
                       "• حدود منطقة المحتوى (contentPanel)\n" +
                       "• إعادة تعيين للحالة الافتراضية\n\n" +
                       "ملاحظة: الألوان الافتراضية محفوظة! 🎨",
                Location = new Point(20, 80),
                Size = new Size(600, 150),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            controlPanel.Controls.Add(tabsBorderButton);
            controlPanel.Controls.Add(contentBorderButton);
            controlPanel.Controls.Add(resetButton);
            controlPanel.Controls.Add(infoLabel);
            controlTab.AddControl(controlPanel);

            // تاب المعلومات النهائية
            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            var infoTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📋 ملخص التحديثات النهائية:\n\n" +
                       "1️⃣ tabsPanel:\n" +
                       "   • تم تغييره من Panel إلى RJPanel\n" +
                       "   • الخلفية افتراضية (لم تتغير)\n" +
                       "   • يدعم BorderSize, BorderColor, BorderRadius\n\n" +
                       "2️⃣ contentPanel:\n" +
                       "   • تم تغييره من Panel إلى RJPanel\n" +
                       "   • الخلفية افتراضية (لم تتغير)\n" +
                       "   • يدعم BorderSize, BorderColor, BorderRadius\n\n" +
                       "3️⃣ RJPanel:\n" +
                       "   • إضافة BorderSize\n" +
                       "   • إضافة BorderColor\n" +
                       "   • تحسين رسم الحدود\n\n" +
                       "4️⃣ RJTextBox:\n" +
                       "   • إضافة ReadOnly\n" +
                       "   • منع التعديل عند الحاجة\n\n" +
                       "✅ جميع التحديثات مكتملة بنجاح!",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 1,
                BorderColor = Color.FromArgb(200, 200, 200),
                BorderRadius = 5,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            infoTab.AddControl(infoTextBox);
        }

        private void ToggleTabsPanelBorder()
        {
            if (tabControl.TabsPanelBorderSize == 0)
            {
                tabControl.TabsPanelBorderSize = 2;
                tabControl.TabsPanelBorderColor = Color.FromArgb(0, 122, 204);
                tabControl.TabsPanelBorderRadius = 8;
            }
            else
            {
                tabControl.TabsPanelBorderSize = 0;
            }
        }

        private void ToggleContentBorder()
        {
            if (tabControl.ContentBorderSize == 1)
            {
                tabControl.ContentBorderSize = 3;
                tabControl.ContentBorderColor = Color.FromArgb(76, 175, 80);
                tabControl.ContentBorderRadius = 12;
            }
            else
            {
                tabControl.ContentBorderSize = 1;
                tabControl.ContentBorderColor = Color.FromArgb(200, 200, 200);
                tabControl.ContentBorderRadius = 0;
            }
        }

        private void ResetBorders()
        {
            // إعادة تعيين حدود التابات
            tabControl.TabsPanelBorderSize = 0;
            tabControl.TabsPanelBorderRadius = 0;

            // إعادة تعيين حدود المحتوى
            tabControl.ContentBorderSize = 1;
            tabControl.ContentBorderColor = Color.FromArgb(200, 200, 200);
            tabControl.ContentBorderRadius = 0;
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new FinalTestForm();
            form.ShowDialog();
        }
    }
}
