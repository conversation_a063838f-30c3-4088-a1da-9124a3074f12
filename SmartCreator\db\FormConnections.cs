﻿using DevComponents.DotNetBar;
using iTextSharp.text;
using Newtonsoft.Json.Linq;

//using ServiceStack;
using SmartCreator.Data;
using SmartCreator.db;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;

//using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
//using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace SmartCreator
{
    public partial class FormConnections : RJForms.RJChildForm
    {

        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;
        public FormConnections()
        {
            InitializeComponent();
            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();
            LoadData();
        }

        private void LoadData()
        {
            try
            {
                lst.Items.Clear();
                Smart_DataAccess smart_DA = new Smart_DataAccess(); 
                List<Connections_Db> connections_Db = smart_DA.Load<Connections_Db>($"select * from Connections_Db");
                //List<Connections_Db> connections_Db = Sql_DataAccess.Get_List_Connections_Db();
                foreach (var connection in connections_Db)
                {
                    //lst.Items.Add(new ListViewItem(new string[] { " " + connection.Name.ToString(), Convert.ToBoolean(connection.Default) ? "1" : ((connections_Db.Count() == 0) ? 1 : 0).ToString(), connection.Id }));

                    lst.Items.Add(" " + connection.Name.ToString(), Convert.ToBoolean(connection.Default) ? 1 : ((connections_Db.Count() == 0) ? 1 : 0)).SubItems.AddRange(new string[] { connection.Type.ToString(), connection.Id });
                    //lst.Items.Add(" " + connection.Name.ToString(), Convert.ToBoolean(connection.Default) ? 1 : ((connections_Db.Count() == 0) ? 1 : 0)).SubItems.Add(connection.Type.ToString());
                }
            }
            catch (Exception)
            {
            }
        }
        private void Save()
        {
            return;
            try
            {
                Connections_Db New_db = (new Connections_Db());
                
                //New_db.Type = "SQLlite";
                //Sql_DataAccess.AddNew_Connection_string_2(New_db);

                File.WriteAllText(Config.ConnectionsFile, Connections._connections.ToString());
            }
            catch (Exception)
            {
            }
        }


        private void FormConnections_Load(object sender, EventArgs e)
        {

        }

        private void contextMenuStrip1_Opening(object sender, CancelEventArgs e)
        {
            e.Cancel = lst.SelectedItems.Count == 0;
        }

 

        private void editToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Connections_Db db = Sql_DataAccess.Get_default_Connections_Db(Convert.ToInt32(lst.SelectedItems[0].SubItems[2].Text.ToString()));
            new FormConnection(db).ShowDialog();
            //new FormConnection(Convert.ToInt32(lst.SelectedItems[0].SubItems[2].Text.ToString())).ShowDialog();
            LoadData();
            Save();
        }

       


        private void deleteToolStripMenuItem_Click(object sender, EventArgs e)
        {
            try
            {

                Connections_Db db = Sql_DataAccess.Get_default_Connections_Db(Convert.ToInt32(lst.SelectedItems[0].SubItems[2].Text.ToString()));
                //Sql_DataAccess.Delete_Any_ById_From_Db(db);
                Smart_DA.DeleteById<Connections_Db>(db.Id, "Connections_Db");
                //Sql_DataAccess.Delete_Any_ById_From_Db("Connections_Db", db.Id);
                LoadData();

                //Connections._connections.RemoveAt(lst.SelectedItems[0].Index);
                //lst.Items.RemoveAt(lst.SelectedItems[0].Index);
                //Save();
            }
            catch (Exception)
            {
            }
        }

        private void lst_AfterLabelEdit(object sender, LabelEditEventArgs e)
        {
            try
            {
                if (e.Label.Trim().Length != 0)
                {
                     
                    string id = lst.SelectedItems[0].SubItems[2].Text.ToString();
                    Connections_Db db = Sql_DataAccess.Get_default_Connections_Db(Convert.ToInt32( id));
                    
                    if (db != null)
                    {
                        db.Name= e.Label;
                        if (Sql_DataAccess.Add_Edit_Connection_string(db,true))
                        {
                            RJMessageBox.Show("تم التعديل بنجاح");
                        }
                    }

                }
                LoadData();
                //if (e.Label.Trim().Length != 0)
                //{
                //    JToken jToken = Connections._connections[lst.SelectedItems[0].Index];
                //    jToken["name"] = e.Label;
                //    Connections._connections[lst.SelectedItems[0].Index] = jToken;
                //    Save();
                //}
            }
            catch (Exception)
            {
            }
        }

        private void setDefaultToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Connections_Db db = Sql_DataAccess.Get_default_Connections_Db(Convert.ToInt32(lst.SelectedItems[0].SubItems[2].Text.ToString()));

            if (db != null)
            {
                db.Default = 1;
                if (Sql_DataAccess.Add_Edit_Connection_string(db,true,true))
                {
                    RJMessageBox.Show("تم التعديل بنجاح");
                }
            }

            //foreach (JObject connection in Connections._connections)
            //{
            //    connection["default"] = false;
            //}

            //Connections._connections[lst.SelectedItems[0].Index]["default"] = true;
            //Save();
            LoadData();
        }

        private void testConnectonToolStripMenuItem_Click(object sender, EventArgs e)
        {
            btnTest_Click(sender, e);
        }

        private void lst_SelectedIndexChanged(object sender, EventArgs e)
        {
            btnTest.Enabled = lst.SelectedItems.Count > 0;
        }
 
        private void btnTest_Click(object sender, EventArgs e)
        {
            TestConnection();
            return;
            //Cursor.Current = Cursors.WaitCursor;
            if (lst.SelectedItems.Count != 0)
            {
                try
                {
                    var ff = lst.SelectedItems[0].Index;
                    string con = Connections._connections[lst.SelectedItems[0].Index]["name"].ToString();
                    Connections.GetConnection(con);
                    MessageBox.Show("Success");
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }

                //Cursor.Current = Cursors.Default;
            }
        }

       private bool TestConnection()
        {
            bool status = false;
            try
            {
                Connections_Db db = Sql_DataAccess.Get_default_Connections_Db(Convert.ToInt32(lst.SelectedItems[0].SubItems[2].Text.ToString()));
                if (db != null)
                {
                    using (IDbConnection con = Sql_DataAccess.GetConnection(db.Connection_string, db.Type))
                    {
                        try
                        {
                            con.Open();
                            //con.CreateTableIfNotExists<BatchCard>();
                            //con.CreateTableIfNotExists<SellingPoint>();
                            //con.CreateTableIfNotExists<HSUser>();
                            //con.CreateTableIfNotExists<UmUser>();
                            //con.CreateTableIfNotExists<UmPyment>();
                            //con.CreateTableIfNotExists<UmSession>();
                            RJMessageBox.Show("تم الاتصال بنجاح");
                            status = true;  
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return status;
        }
        private void btnAddConnection_Click(object sender, EventArgs e)
        {
            new FormConnection().ShowDialog();
            Save();
            LoadData();
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Save();
            Close();
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            contextMenuStrip1.Show();
        }
    }
}
