﻿using SmartCreator.Entities.UserManager;
using SmartCreator.Models.hotspot;
using SmartCreator.Models;
using SmartCreator.RJForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Data;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using Org.BouncyCastle.Asn1.X509;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using SmartCreator.Utils;
using CefSharp.DevTools.Profiler;
using System.Windows.Forms.DataVisualization.Charting;
using System.Security.Cryptography;
using Org.BouncyCastle.Tls;

namespace SmartCreator.Forms.Hotspot
{
    public partial class Form_Add_Profile_Balance_HS : RJChildForm
    {
        bool firstLoad = true;
        bool firstUser = true;
        public bool success = false;

        HashSet<HSUser> Users = new HashSet<HSUser>();
        private HashSet<HSUser> Users_Temp = new HashSet<HSUser>();
        HSLocalProfile public_HProfile=new HSLocalProfile();
        

        public Form_Add_Profile_Balance_HS(HashSet<HSUser> users)
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            //Users_Temp = users;
            Users = users;

            this.Text = "اضافة رصيد";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Add New Balance";
            }
            if (UIAppearance.Theme==UITheme.Dark )
            {
                pnl_Menul_profile.Customizable = false;
                pnl_profile_HS_local.Customizable = false;
                pnl_usermanger.Customizable = false;
                rjPanel2.Customizable = false;
            }
            //btnSave.BackColor = UIAppearance.StyleColor;
            lblTitle.Select();//Not focus on text boxes when starting the form.

            txt_download.Text = "0";
            txt_price.Text = "0";
            txt_houre.Text = "0";
            txt_validatiy.Text = "0";
            txt_Real_size.Text = "0";
            txt_Real_time.Text = "0";
            CBox_SizeDownload.SelectedIndex = 0;
            if (users.Count == 1)
            {
                //rjPanel2.Visible = true;
                //btnSave.Location = new Point(107, 350);
                panel_one_details.Height = 105;
                default_not_Select_Balance();
            }
            else
            {
                //btnSave.Location = new Point(104, 319);
                panel_one_details.Height = 0;

            }
            Cbox_Profile_Select_Typ.SelectedIndex= 0;


            lblTitle.Font = btnSave.Font = Program.GetCustomFont(Resources.DroidSansArabic, 12 , FontStyle.Bold);
            rjLabel1.Font = rjLabel4.Font = rjLabel5.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel3.Font = rjLabel7.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8.25f , FontStyle.Regular);
            rjLabel3.ForeColor = rjLabel7.ForeColor = Color.Red;


            utils.Control_textSize(pnlClientArea);


            

        }
        void default_not_Select_Balance()
        {
            txt_Real_time.Text = "00:00:00";
            txt_Real_validay.Text = "0";
            txt_Real_size.Text = "0";
            return;
            try
            {
                if (Users.First().Limitbytestotal > 0)
                {
                    long size = (Users.First().Limitbytestotal - (Users.First().DownloadUsed + Users.First().UploadUsed));
                    if (size <= 0)
                        size = 0;
                    txt_Real_size.Text = utils.ConvertSize_Get_InArabic(size.ToString());
                }
                else
                    txt_Real_size.Text = "مفتوح";

                if (Users.First().LimitUptime > 0)
                    txt_Real_time.Text = utils.Get_Seconds_By_clock_Mode(Users.First().LimitUptime - Users.First().UptimeUsed).ToString();
                else
                    txt_Real_time.Text = "مفتوح";

                if (Users.First().ValidityLimit > 0)
                    txt_Real_validay.Text = (Users.First().ValidityLimit / 24 / 60 / 60).ToString();
                else
                    txt_Real_validay.Text = "مفتوح";
            }
            catch { }
        }

        private void Get_Cbox_Profile_UserNanager()
        {
            try
            {
            var umProfil = new List<UmProfile>();
            umProfil.Add(new UmProfile { Id = 0, Name = "" });
            umProfil.AddRange(Global_Variable.UM_Profile);

                CBox_Profile_UserMan.DataSource = umProfil;
                CBox_Profile_UserMan.DisplayMember = "Name";
                CBox_Profile_UserMan.ValueMember = "Name";
                CBox_Profile_UserMan.SelectedIndex = -1;
                CBox_Profile_UserMan.Text = "";
            }
            catch { }
        }
        private void Get_Cbox_Profile_Hotspot_local()
        {
            try
            {
                var umProfil = new List<HSLocalProfile>();
                umProfil.Add(new HSLocalProfile { Id = 0, Name = "" });
                HSLocalProfile hotspot = new HSLocalProfile();
                umProfil.AddRange(hotspot.Ge_Local_Hotspot());

                CBox_Profile_HotspotLocal.DataSource = umProfil;
                CBox_Profile_HotspotLocal.DisplayMember = "Name";
                CBox_Profile_HotspotLocal.ValueMember = "Name";
                //CBox_Profile.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            try
            {
                Get_Cbox_Profile_UserNanager();
                Get_Cbox_Profile_Hotspot_local();
            }catch { }
            firstLoad = false;
            firstUser = false;
        }

        private void Form_Add_Profile_Balance_HS_Load(object sender, EventArgs e)
        {
            timer1.Start();
            //firstLoad = false;

        }
        private void Cbox_Profile_Select_Typ_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            //seletFrom_Typ = true;
            if (Cbox_Profile_Select_Typ.SelectedIndex == 0)
            {
                pnl_Menul_profile.Visible = false;
                pnl_usermanger.Visible = false;
                pnl_profile_HS_local.Visible = true;

                CBox_Profile_HotspotLocal.SelectedIndex = 0;
            }
            else if (Cbox_Profile_Select_Typ.SelectedIndex == 1)
            {
                pnl_Menul_profile.Visible = true;
                pnl_usermanger.Visible = false;
                pnl_profile_HS_local.Visible = false;
                txt_houre.Text = "0";
                txt_download.Text = "0";
                txt_price.Text = "0";
                txt_validatiy.Text = "0";
                //if (!check_Menual_profile())
                //    return;
            }
            else
            {
                
                pnl_Menul_profile.Visible = false;
                pnl_usermanger.Visible = true;
                pnl_profile_HS_local.Visible = false;
                CBox_Profile_UserMan.SelectedIndex = 0;

            }

            default_not_Select_Balance();

            //CBox_Profile_HotspotLocal.SelectedIndex = 0;
            //CBox_Profile_UserMan.SelectedIndex = 0;

            firstUser = false;
        }

        private bool check_Menual_profile()
        {
            double numberChik;
            if (!(double.TryParse(txt_houre.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد الساعات بشكل صحيح ");
                return false;
            }
            if (!(double.TryParse(txt_validatiy.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صلاحية الايام بشكل صحيح ");
                return false;
            }
            if (!(double.TryParse(txt_price.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل السعر بشكل صحيح ");
                return false;
            }
            if (!(double.TryParse(txt_download.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل كمية التحميل بشكل صحيح ");
                return false;
            }
            if (Convert.ToInt32(txt_download.Text) > 0)
                if (CBox_SizeDownload.SelectedIndex == -1 || CBox_SizeDownload.Text == "")
                {
                    RJMessageBox.Show("حدد وحده التحميل");
                    return false;
                }

            return true;
        }

        private void CBox_Profile_UserMan_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if(firstLoad)
                return;
            
            HSLocalProfile hSLocalProfile = new HSLocalProfile();
            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile_UserMan.SelectedValue.ToString());
            if(profile != null)
            {
                hSLocalProfile.Name = profile.Name;
                hSLocalProfile.Validity = profile.Validity;
                hSLocalProfile.UptimeLimit = profile.UptimeLimit;
                //hSLocalProfile.UptimeLimit = profile.UptimeLimit;
                hSLocalProfile.TransferLimit = profile.TransferLimit;
                hSLocalProfile.Price= profile.Price;
                public_HProfile = hSLocalProfile;
            }
            Calculate_time_Size_Vailday(hSLocalProfile);
        }

        private void CBox_Profile_HotspotLocal_OnSelectedIndexChanged(object sender, EventArgs e)
        {

            if (firstLoad)
                return;
             
            HSLocalProfile hotspot = new HSLocalProfile();
            HSLocalProfile profile = hotspot.Ge_Local_Hotspot().Find(x => x.Name == CBox_Profile_HotspotLocal.SelectedValue.ToString());
            public_HProfile = profile;

            Calculate_time_Size_Vailday(profile);
            
            return;

          
        }

        [Obsolete]
        private void Calc_Menal_Profile()
        {

            if (firstLoad)
                return;

            //HSLocalProfile hotspot = new HSLocalProfile();
            HSLocalProfile profile = new HSLocalProfile();

            if (!check_Menual_profile())
                return;
            if (CBox_SizeDownload.SelectedIndex == 0)
            {
                profile.TransferLimit = Convert.ToDouble(txt_download.Text) * 1024 * 1024;
            }
            if (CBox_SizeDownload.SelectedIndex == 1)
            {
                profile.TransferLimit = Convert.ToDouble(txt_download.Text) * 1024 * 1024 * 1024;
            }

            if (txt_houre.Text.Contains("."))
            {
                string[] split = txt_houre.Text.Split(new string[] { "." }, StringSplitOptions.None);
                if (split.Length > 0)
                {
                    profile.UptimeLimit = (Convert.ToDouble(split[0]) * 60 * 60) + (Convert.ToInt32(split[1]) * 60);
                }
            }
            else
                profile.UptimeLimit = (Convert.ToDouble(txt_houre.Text) * 60 * 60);


            profile.Validity = Convert.ToDouble(txt_validatiy.Text);
            profile.Price = (float)Convert.ToDecimal( txt_price.Text);
            profile.Name = Users.First().ProfileHotspot;

            if (profile == null)
            {
                default_not_Select_Balance();
                return;
            }
            public_HProfile = profile;

            Calculate_time_Size_Vailday(profile);
            return;
             }

        private string Get_Email_Cards_Edit_Days(string source_email ,int Addday,bool Is_open=false)
        {

            try
            {
                string profil = "";
                int smartValidatiy_Add = 0;
                int day = 0;
                string price = "0";
                string sp = "0";
                string numPrint = "0";
                string datetimePrint1 = "0";
                string datetimePrint = "";
                string byDayOrHour = "0";
                string first_mac = "0";
                string timeSave = "0";
                string sizeSave = "0";
                string sessionSave = "0";
                string @smart = "";
                string descr = "";

                if (source_email.Contains("@smart."))
                {
                    //smartValidatiy_Add = 1;
                    //"profil'day'price'sp'numPrint'datetimePrint'byDayOrHour'mac'timeSave'sizeSave'sessionSave'@smart.befor\r\n";
                    string[] Res_split = source_email.Split(new string[] { "'" }, StringSplitOptions.None);
                    try { profil = Res_split[0]; } catch { };
                    if (Is_open)
                        try { day = 0; } catch { }
                    else
                        try { day = Addday; } catch { }
                    //try { day = int.Parse(Res_split[1]) + Addday ; } catch { }
                    try { price = (Res_split[2]); } catch { }
                    try { sp = (Res_split[3]); } catch { }
                    try { numPrint = (Res_split[4]); } catch { }
                    try { datetimePrint = (Res_split[5]); } catch { }
                    try { byDayOrHour = (Res_split[6]); } catch { }
                    try { first_mac = (Res_split[7]); } catch { }
                    try { timeSave = (Res_split[8]); } catch { }
                    try { sizeSave = (Res_split[9]); } catch { }
                    try { sessionSave = (Res_split[10]); } catch { }

                    source_email = (profil + "'" +
                     day + "'" +
                     price + "'" +
                     sp + "'" +
                     numPrint + "'" +
                     datetimePrint + "'"
                     + byDayOrHour + "'" +
                     firstUser + "'" +
                     timeSave + "'" +
                     sizeSave + "'" +
                     //sessionSave + "'@smart.befor";
                     sessionSave + "'" + Res_split[11]).ToLower();

                }
            }
            catch { }
            return source_email;
        }


        private HSUser Calculate_time_Size_Vailday(HSLocalProfile profile, HSUser Huser = null, bool Save = false)
        {
            if (Huser == null || Users.Count == 1)
            {
               HSUser user = Users.First();
                Huser = new HSUser
                {
                    Limitbytestotal = user.Limitbytestotal,
                    LimitUptime = user.LimitUptime,
                    UploadUsed = user.UploadUsed,
                    DownloadUsed = user.DownloadUsed,
                    ValidityLimit = user.ValidityLimit,
                    Price = user.Price,
                    Email = user.Email,
                    FirsLogin = user.FirsLogin,
                    IdHX = user.IdHX,
                    Percentage = user.Percentage,
                    ProfileHotspot = user.ProfileHotspot,
                    ProfileName = user.ProfileName,
                    RegDate = user.RegDate,
                    SmartValidatiy_Add = user.SmartValidatiy_Add,
                    TransferLimit = user.TransferLimit,
                    UptimeLimit = user.UptimeLimit,
                    UptimeUsed = user.UptimeUsed,
                    UserName = user.UserName,
                    TotalPrice = user.TotalPrice,
                    Sn_Name = user.Sn_Name,
                    CountProfile= user.CountProfile,
                    CountSession= user.CountSession,
                };
                
            }

            if (Convert.ToBoolean(Huser.SmartValidatiy_Add))
            {
                int _Days = 0;
                if (profile.Validity > 0)
                {
                    if ((Huser.ValidityLimit > 0))
                    {
                        //int userValidity = (int)(Users.First().ValidityLimit / 24 / 60 / 60);
                        if (Huser.FirsLogin != null)
                        {
                            if (Huser.FirsLogin.Value.Year > 2020)
                            {
                                DateTime endDate = Huser.FirsLogin.Value.AddSeconds(Huser.ValidityLimit);
                                int reminDay = (DateTime.Now - endDate).Days;

                                if (reminDay < 0) //reminDay = reminDay * -1; عاد الكرت له صلاحيه ايام
                                    _Days = (((int)((int)(profile.Validity) + (Huser.ValidityLimit / 24 / 60 / 60))));
                                else
                                    _Days = (((int)(profile.Validity) + (reminDay)));


                                txt_Real_validay.Text = _Days.ToString();
                                //Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, _Days);
                            }
                            else
                            {
                                _Days = ((int)(profile.Validity + (Huser.ValidityLimit / 24 / 60 / 60)));
                                txt_Real_validay.Text = _Days.ToString();
                                //Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, _Days);
                            }
                        }
                        else
                        {
                            _Days = ((int)(profile.Validity + (Huser.ValidityLimit / 24 / 60 / 60)));
                            txt_Real_validay.Text = _Days.ToString();
                            //Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, _Days);
                        }



                    }
                    else
                    {
                        _Days = (int)profile.Validity;
                        txt_Real_validay.Text = _Days.ToString();
                        //Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, _Days);
                    }
                }
                else
                {
                    txt_Real_validay.Text = "مفتوح";
                    _Days = 0;
                    //Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, 0, true);
                }


                if (Toggle_RemoveAndAddBalacne.Checked)
                {
                    _Days = (int)profile.Validity;
                }
                if (Save)
                {
                    Huser.Email = Get_Email_Cards_Edit_Days(Huser.Email, _Days).ToLower();
                    Huser.ValidityLimit = _Days * 24 * 60 * 60;
                    Huser.RegDate = utils.Datetime_To_DateTime_Format_DB(DateTime.Now);  //في اضافة الباقه تاريخ التسجيل هو تاريخ اضافة الباقة
                    Huser.CountProfile = Huser.CountProfile + 1;
                    Huser.ProfileName = profile.Name;
                }
            }

            if (Toggle_Clear_Profile.Checked || Toggle_RemoveAndAddBalacne.Checked)
            {
                txt_Real_size.Text = utils.ConvertSize_Get_InArabic(profile.TransferLimit.ToString());
                txt_Real_time.Text = utils.Get_Seconds_By_clock_Mode(profile.UptimeLimit).ToString();
                txt_Real_validay.Text = profile.Validity.ToString();
                
                if (Save)
                {
                    Huser.TransferLimit = (long)profile.TransferLimit;
                    Huser.Limitbytestotal = (long)profile.TransferLimit;

                    Huser.UptimeLimit = (long)profile.UptimeLimit;
                    Huser.LimitUptime = (long)profile.UptimeLimit;
                    Huser.ProfileName = profile.Name;
                    Huser.Price = profile.Price;
                    //Huser.ValidityLimit = (long)profile.Validity;
                }
                return Huser;
            }
            else
            {
                try
                {


                    txt_Real_size.Text = utils.ConvertSize_Get_InArabic(profile.TransferLimit.ToString());
                    txt_Real_time.Text = utils.Get_Seconds_By_clock_Mode(profile.UptimeLimit).ToString();
                    txt_Real_validay.Text = profile.Validity.ToString();

                    if (profile.Validity == 0)
                        txt_Real_validay.Text = "مفتوح";

                    if (profile.UptimeLimit == 0)
                        txt_Real_time.Text = "مفتوح";

                    if (profile.TransferLimit == 0)
                        txt_Real_size.Text = "مفتوح";

                    if (Save)
                    {
                        if (profile.TransferLimit <= 0)
                            Huser.Limitbytestotal = (long)profile.TransferLimit;
                        else
                            Huser.Limitbytestotal = (Huser.Limitbytestotal + (long)profile.TransferLimit);


                        if (profile.UptimeLimit <= 0)
                            Huser.LimitUptime = (long)profile.UptimeLimit;
                        else
                            Huser.LimitUptime = (Huser.LimitUptime + (long)profile.UptimeLimit);

                        Huser.ProfileName = profile.Name;
                        Huser.Price = profile.Price;
                    }

                    return Huser;
                    long Limitbytestotal = Huser.Limitbytestotal;
                    long DownloadUsed = Huser.DownloadUsed;
                    long UploadUsed = Huser.UploadUsed;
                    long TransferUsed = Huser.UploadUsed + Huser.DownloadUsed;
                    long LimitUptime = Huser.LimitUptime;
                    long UptimeUsed = Huser.UptimeUsed;
                    long ValidityLimit = Huser.ValidityLimit;

                    long New_Limitbytestotal = (long)profile.TransferLimit;
                    long New_LimitUptime = (long)profile.UptimeLimit;
                   

                    if (Limitbytestotal > 0)
                        New_Limitbytestotal = ((Limitbytestotal - (DownloadUsed + UploadUsed)) + (long)profile.TransferLimit);
                     
                    if (LimitUptime > 0)
                        New_LimitUptime = (LimitUptime - UptimeUsed) + (long)profile.UptimeLimit;


                    if (Save)
                    {
                        Huser.Limitbytestotal = New_Limitbytestotal;
                        Huser.LimitUptime = New_LimitUptime;
                        Huser.ProfileName = profile.Name;
                        Huser.Price = profile.Price; 
                    }

                    //txt_Real_size.Text = utils.ConvertSize_Get_InArabic(New_Limitbytestotal.ToString());
                    //txt_Real_time.Text = utils.Get_Seconds_By_clock_Mode(New_LimitUptime).ToString();
                    ////txt_Real_validay.Text = (New_ValidityLimit / 24 / 60 / 60 / 60).ToString();


                    txt_Real_size.Text = utils.ConvertSize_Get_InArabic((profile.TransferLimit+Huser.TransferLimit).ToString());
                    txt_Real_time.Text = utils.Get_Seconds_By_clock_Mode(profile.UptimeLimit + Huser.UptimeLimit).ToString();
                    txt_Real_validay.Text = (profile.Validity + Huser.ValidityLimit / 24 / 60 / 60).ToString();
                


                    if (profile.Validity == 0)
                        txt_Real_validay.Text = "مفتوح";

                    if (profile.UptimeLimit == 0)
                        txt_Real_time.Text = "مفتوح";

                    if (profile.TransferLimit == 0)
                        txt_Real_size.Text = "مفتوح";
                }
                catch { }
            }
            
            return Huser;
        }

        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (pnl_Menul_profile.Visible)
            {
                if (!check_Menual_profile())
                    return;
            }
            if (pnl_profile_HS_local.Visible)
            {
                if (CBox_Profile_HotspotLocal.Text == "" || CBox_Profile_HotspotLocal.SelectedIndex <= 0)
                {
                    RJMessageBox.Show("اختار الباقة");
                    return;
                }
            }
            if (pnl_usermanger.Visible)
            {
                if (CBox_Profile_UserMan.Text == "" || CBox_Profile_UserMan.SelectedIndex <= 0)
                {
                    RJMessageBox.Show("اختار الباقة");
                    return;
                }
            }

            DialogResult result = RJMessageBox.Show("  هل انت متأكد من  تعبئة الرصيد او اضافة الباقة المحددة", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            HashSet<HSUser> hSUsers = new HashSet<HSUser>();
            foreach (HSUser user in Users)
            {
                HSUser SUser = new HSUser
                {
                    Limitbytestotal = user.Limitbytestotal,
                    LimitUptime = user.LimitUptime,
                    UploadUsed = user.UploadUsed,
                    DownloadUsed = user.DownloadUsed,
                    ValidityLimit = user.ValidityLimit,
                    Price = user.Price,
                    Email = user.Email,
                    FirsLogin = user.FirsLogin,
                    IdHX = user.IdHX,
                    Percentage = user.Percentage,
                    ProfileHotspot = user.ProfileHotspot,
                    ProfileName = user.ProfileName,
                    RegDate = user.RegDate,
                    SmartValidatiy_Add = user.SmartValidatiy_Add,
                    TransferLimit = user.TransferLimit,
                    UptimeLimit = user.UptimeLimit,
                    UptimeUsed = user.UptimeUsed,
                    UserName = user.UserName,
                    TotalPrice = user.TotalPrice,
                    Sn_Name = user.Sn_Name,
                    CountProfile = user.CountProfile,
                    CountSession = user.CountSession,
                };
                hSUsers.Add(Calculate_time_Size_Vailday(public_HProfile, SUser, true));
            }

            if (hSUsers.Count > 0)
            {
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();


                Sql_DataAccess Local_DB = new Sql_DataAccess();
                int Update_Hotspot = 0;
                HashSet<HSUser> users = null;

                if (Toggle_Clear_Profile.Checked)
                {
                    users = mk_DataAccess.Edit_Balance_User_Hotspot(hSUsers, "RestCards");
                    if (users.Count > 0)
                    {
                        foreach (HSUser user in hSUsers)
                        {
                            user.UptimeLimit = user.LimitUptime;
                            user.TransferLimit = user.Limitbytestotal;
                            user.DownloadUsed = 0;
                            user.UploadUsed = 0;
                            user.UptimeUsed = 0;
                        }
                        Update_Hotspot = Local_DB.Execute($"update HSUser set [LimitUptime]=@LimitUptime,[Limitbytestotal]=@Limitbytestotal,[email]=@email,[ProfileName]=@ProfileName,[CountProfile]=@CountProfile," +
                            $"[UptimeLimit]=@UptimeLimit,[TransferLimit]=@TransferLimit," +
                            $"[DownloadUsed]=@DownloadUsed,[UploadUsed]=@UploadUsed,[UptimeUsed]=@UptimeUsed  where Sn_Name=@Sn_Name", hSUsers);
                    }
                }
                else if (Toggle_AddBalance.Checked)
                {
                    users = mk_DataAccess.Edit_Balance_User_Hotspot(hSUsers, "add");
                    if (users.Count > 0)
                    {
                        foreach (HSUser user in hSUsers)
                        {
                            user.UptimeLimit = user.LimitUptime+ user.UptimeLimit;
                            user.TransferLimit = user.Limitbytestotal+ user.TransferLimit;
                            
                        }
                        Update_Hotspot = Local_DB.Execute($"update HSUser set [LimitUptime]=@LimitUptime,[Limitbytestotal]=@Limitbytestotal,[email]=@email,[ProfileName]=@ProfileName,[CountProfile]=@CountProfile," +
                             $"[UptimeLimit]=@UptimeLimit,[TransferLimit]=@TransferLimit " +
                             $"  where Sn_Name=@Sn_Name", hSUsers);
                    }
                }
                else
                {
                    users = mk_DataAccess.Edit_Balance_User_Hotspot(hSUsers, "RemoveCards");
                    if (users.Count > 0)
                    {
                        Update_Hotspot = Local_DB.Execute<HSUser>($"update HSUser set DeleteFromServer=1 where Sn_Name=@Sn_Name", Users);
                        SourceCardsHotspot_fromMK s = new SourceCardsHotspot_fromMK();
                        s.Add_HSUser_toDB(users.ToList(), true, true);
                        Sccess_WthiRefersh = true;
                    }
                    //Update_Hotspot = Local_DB.Execute($"update HSUser set [LimitUptime]=@LimitUptime,[Limitbytestotal]=@Limitbytestotal,[email]=@email,[ProfileName]=@ProfileName,[CountProfile]=@CountProfile  where Sn_Name=@Sn_Name", hSUsers);

                }


                if (users.Count > 0)
                {
                    if (Update_Hotspot > 0)
                    {
                        HsPyment hsPyment = new HsPyment();
                        hsPyment.Add_HS_Pyments_after_print(hSUsers,false);
                        foreach (HSUser user in Users)
                        {
                            user.ProfileName = public_HProfile.Name;
                        }
                        success = true;

                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تمت اضافة الباقة بنجاح");

                        Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                                   (System.Windows.Forms.MethodInvoker)delegate ()
                                   {
                                       //Sccess_WthiRefersh = true;
                                           this.Close();
                                   });
                    }
                }
            }


        }
        public bool Sccess_WthiRefersh = false;
        private void Toggle_AddBalance_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (Toggle_AddBalance.Checked)
            {
                firstUser = true;
             
                Toggle_RemoveAndAddBalacne.Checked = false;
                Toggle_Clear_Profile.Checked = false;
                firstUser = false;

            }
        }

        private void Toggle_Clear_Profile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (Toggle_Clear_Profile.Checked)
            {
                firstUser = true;
                Toggle_RemoveAndAddBalacne.Checked = false;
                Toggle_AddBalance.Checked = false;
                firstUser = false;
            }
        }

        private void Toggle_RemoveAndAddBalacne_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (Toggle_RemoveAndAddBalacne.Checked)
            {
                firstUser = true;

                Toggle_Clear_Profile.Checked = false;
                Toggle_AddBalance.Checked = false;
                firstUser = false;
            }
        }

        [Obsolete]
        private void txt_houre_onTextChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (string.IsNullOrEmpty(txt_houre.Text))
                return;
            //Calculate_time_Size_Vailday();
            Calc_Menal_Profile();
        }

        [Obsolete]
        private void txt_houre_Leave(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (string.IsNullOrEmpty(txt_houre.Text))
                return;
            //Calculate_time_Size_Vailday();
            Calc_Menal_Profile();
        }
    }
}
