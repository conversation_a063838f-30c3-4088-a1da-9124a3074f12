using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار مبسط جداً للـ Designer - للتأكد من حل مشكلة NullReference
    /// </summary>
    public partial class MinimalDesignerTest : Form
    {
        private RJTabControl testTabControl;

        public MinimalDesignerTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // testTabControl
            // 
            this.testTabControl = new RJTabControl();
            this.testTabControl.Dock = DockStyle.Fill;
            this.testTabControl.Location = new Point(0, 0);
            this.testTabControl.Name = "testTabControl";
            this.testTabControl.Size = new Size(600, 400);
            this.testTabControl.TabIndex = 0;
            
            // 
            // MinimalDesignerTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(600, 400);
            this.Controls.Add(this.testTabControl);
            this.Name = "MinimalDesignerTest";
            this.Text = "🧪 اختبار Designer مبسط - RJTabControl";
            this.StartPosition = FormStartPosition.CenterScreen;
            
            this.ResumeLayout(false);
            
            // إضافة محتوى بعد التهيئة
            this.Load += MinimalDesignerTest_Load;
        }

        private void MinimalDesignerTest_Load(object sender, EventArgs e)
        {
            try
            {
                // اختبار إضافة تاب بسيط
                var tab1 = new RJTabPage("اختبار 1");
                tab1.BackColor = Color.FromArgb(0, 122, 204);
                tab1.ForeColor = Color.White;
                
                var label1 = new Label
                {
                    Text = "✅ RJTabControl تم إنشاؤه بنجاح!\n\n" +
                           "لا توجد أخطاء NullReference\n" +
                           "Designer يعمل بمثالية",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(0, 122, 204)
                };
                tab1.AddControl(label1);
                
                this.testTabControl.AddTab(tab1);

                // اختبار إضافة تاب ثاني
                var tab2 = new RJTabPage("اختبار 2");
                tab2.BackColor = Color.FromArgb(76, 175, 80);
                tab2.ForeColor = Color.White;
                
                var label2 = new Label
                {
                    Text = "🎉 التاب الثاني يعمل أيضاً!\n\n" +
                           "جميع الخصائص محمية\n" +
                           "لا توجد مشاكل في Designer",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                tab2.AddControl(label2);
                
                this.testTabControl.AddTab(tab2);

                // تفعيل التاب الأول
                this.testTabControl.SelectedIndex = 0;

                // عرض معلومات النجاح
                this.Text += " - ✅ نجح الاختبار!";
            }
            catch (Exception ex)
            {
                // عرض الخطأ إذا حدث
                MessageBox.Show($"❌ خطأ في الاختبار:\n\n{ex.Message}\n\n{ex.StackTrace}",
                               "خطأ في اختبار Designer", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل الاختبار!";
            }
        }

        /// <summary>
        /// تشغيل الاختبار المبسط
        /// </summary>
        public static void RunMinimalTest()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var form = new MinimalDesignerTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل الاختبار المبسط:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء RJTabControl فقط
        /// </summary>
        public static void TestRJTabControlCreation()
        {
            try
            {
                // محاولة إنشاء RJTabControl
                var tabControl = new RJTabControl();
                
                // اختبار الخصائص الأساسية
                var tabCount = tabControl.TabCount;
                var selectedIndex = tabControl.SelectedIndex;
                var isInitialized = tabControl.IsInitialized;
                
                // اختبار إضافة تاب
                var tab = new RJTabPage("Test");
                tabControl.AddTab(tab);
                
                MessageBox.Show($"✅ اختبار إنشاء RJTabControl نجح!\n\n" +
                               $"TabCount: {tabControl.TabCount}\n" +
                               $"SelectedIndex: {tabControl.SelectedIndex}\n" +
                               $"IsInitialized: {tabControl.IsInitialized}\n\n" +
                               "لا توجد أخطاء NullReference!",
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء RJTabControl:\n\n{ex.Message}\n\n{ex.StackTrace}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
