﻿namespace SmartCreator.Forms.Hotspot
{
    partial class FormAddHotspotCards
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.pnl_Right = new SmartCreator.RJControls.RJPanel();
            this.btn_Script_Smart = new SmartCreator.RJControls.RJButton();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btn_Add_template = new SmartCreator.RJControls.RJButton();
            this.btn_Add_SellingPoint = new SmartCreator.RJControls.RJButton();
            this.lbl_TemplateCards = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.lbl_SellingPoint = new SmartCreator.RJControls.RJLabel();
            this.cbox_User_NumberORcharcter = new SmartCreator.RJControls.RJComboBox();
            this.CBox_TemplateCards = new SmartCreator.RJControls.RJComboBox();
            this.txt_longPassword = new SmartCreator.RJControls.RJTextBox();
            this.lbl_User_NumberORcharcter = new SmartCreator.RJControls.RJLabel();
            this.cbox_Pass_NumberORcharcter = new SmartCreator.RJControls.RJComboBox();
            this.txt_longUsers = new SmartCreator.RJControls.RJTextBox();
            this.lbl_Pass_NumberORcharcter = new SmartCreator.RJControls.RJLabel();
            this.Cbox_Profile_Select_Typ = new SmartCreator.RJControls.RJComboBox();
            this.lbl_Cbox_Profile_Select_Typ = new SmartCreator.RJControls.RJLabel();
            this.CBox_Server_hotspot = new SmartCreator.RJControls.RJComboBox();
            this.CBox_profile_Source_hotspot = new SmartCreator.RJControls.RJComboBox();
            this.lbl_Server_hotspot = new SmartCreator.RJControls.RJLabel();
            this.lbl_profile2 = new SmartCreator.RJControls.RJLabel();
            this.lbl_profile = new SmartCreator.RJControls.RJLabel();
            this.txtNumberCard = new SmartCreator.RJControls.RJTextBox();
            this.lbl_startCard = new SmartCreator.RJControls.RJLabel();
            this.txt_EndCard = new SmartCreator.RJControls.RJTextBox();
            this.lbl_endCards = new SmartCreator.RJControls.RJLabel();
            this.lbl_UserPassword_Pattern = new SmartCreator.RJControls.RJLabel();
            this.txt_StartCard = new SmartCreator.RJControls.RJTextBox();
            this.cbox_UserPassword_Pattern = new SmartCreator.RJControls.RJComboBox();
            this.lbl_By_Number_Cards = new SmartCreator.RJControls.RJLabel();
            this.checkBox_Add_Smart_Validatiy = new SmartCreator.RJControls.RJCheckBox();
            this.pnl_Menul_profile = new SmartCreator.RJControls.RJPanel();
            this.txt_price = new SmartCreator.RJControls.RJTextBox();
            this.CBox_SizeDownload = new SmartCreator.RJControls.RJComboBox();
            this.lbl_price = new SmartCreator.RJControls.RJLabel();
            this.txt_download = new SmartCreator.RJControls.RJTextBox();
            this.lbl_validatiy = new SmartCreator.RJControls.RJLabel();
            this.txt_validatiy = new SmartCreator.RJControls.RJTextBox();
            this.lbl_download = new SmartCreator.RJControls.RJLabel();
            this.txt_houre = new SmartCreator.RJControls.RJTextBox();
            this.lbl_houre = new SmartCreator.RJControls.RJLabel();
            this.pnl_usermanger = new SmartCreator.RJControls.RJPanel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile_UserMan = new SmartCreator.RJControls.RJComboBox();
            this.lbl_Profile_UserMan = new SmartCreator.RJControls.RJLabel();
            this.btn_show_profile = new SmartCreator.RJControls.RJButton();
            this.pnl_profile_HS_local = new SmartCreator.RJControls.RJPanel();
            this.CBox_Profile_HotspotLocal = new SmartCreator.RJControls.RJComboBox();
            this.lbl_Profile_HotspotLocal2 = new SmartCreator.RJControls.RJLabel();
            this.lbl_Profile_HotspotLocal = new SmartCreator.RJControls.RJLabel();
            this.btn_show_profile_hotspot = new SmartCreator.RJControls.RJButton();
            this.btn_OpenFolderDefault = new SmartCreator.RJControls.RJButton();
            this.lbl_OpenAfterPrint = new SmartCreator.RJControls.RJLabel();
            this.checkBoxOpenAfterPrint = new SmartCreator.RJControls.RJCheckBox();
            this.pnl_left = new SmartCreator.RJControls.RJPanel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.lbl_Title1 = new SmartCreator.RJControls.RJLabel();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.lbl_Title2 = new SmartCreator.RJControls.RJLabel();
            this.pnl_Right2 = new SmartCreator.RJControls.RJPanel();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.lbl_With_Archive_uniqe = new SmartCreator.RJControls.RJLabel();
            this.flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            this.lbl_Save_PDF = new SmartCreator.RJControls.RJLabel();
            this.btn_OpenLastFile = new SmartCreator.RJControls.RJButton();
            this.flowLayoutPanel3 = new System.Windows.Forms.FlowLayoutPanel();
            this.checkBox_With_Archive_uniqe = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_script_File = new SmartCreator.RJControls.RJLabel();
            this.checkBoxFirstUse = new SmartCreator.RJControls.RJCheckBox();
            this.checkBoxSaveTo_script_File = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_FirstUse = new SmartCreator.RJControls.RJLabel();
            this.checkBoxSaveTo_excel = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_excel = new SmartCreator.RJControls.RJLabel();
            this.checkBox_RegisterAsBatch = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_RegisterAsBatch = new SmartCreator.RJControls.RJLabel();
            this.checkBoxSaveTo_PDF = new SmartCreator.RJControls.RJCheckBox();
            this.flowLayoutPanel7 = new System.Windows.Forms.FlowLayoutPanel();
            this.flowLayoutPanel4 = new System.Windows.Forms.FlowLayoutPanel();
            this.flowLayoutPanel6 = new System.Windows.Forms.FlowLayoutPanel();
            this.lbl_RegisterAs_LastBatch = new SmartCreator.RJControls.RJLabel();
            this.txt_last_batchNumber = new SmartCreator.RJControls.RJTextBox();
            this.checkBox_RegisterAs_LastBatch = new SmartCreator.RJControls.RJCheckBox();
            this.checkBox_note = new SmartCreator.RJControls.RJCheckBox();
            this.flowLayoutPanel5 = new System.Windows.Forms.FlowLayoutPanel();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.txt_note = new SmartCreator.RJControls.RJTextBox();
            this.radio_fast_print = new SmartCreator.RJControls.RJRadioButton();
            this.btnAdd = new SmartCreator.RJControls.RJButton();
            this.lbl_radio_Print = new SmartCreator.RJControls.RJLabel();
            this.lbl_radio_fast_print = new SmartCreator.RJControls.RJLabel();
            this.radio_Print = new SmartCreator.RJControls.RJRadioButton();
            this.lbl_radio_one_user = new SmartCreator.RJControls.RJLabel();
            this.radio_one_user = new SmartCreator.RJControls.RJRadioButton();
            this.pnl_left2 = new SmartCreator.RJControls.RJPanel();
            this.panel_PreviewTempateCards = new System.Windows.Forms.Panel();
            this.groupBox2 = new System.Windows.Forms.GroupBox();
            this.lbl_Add_Smart_Validatiy = new SmartCreator.RJControls.RJLabel();
            this.checkBoxSaveTo_text_File = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_byDayOrHour = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_session = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_download = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_time = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.btnAdd_Fast = new SmartCreator.RJControls.RJButton();
            this.btn_add_One = new SmartCreator.RJControls.RJButton();
            this.panel2 = new System.Windows.Forms.Panel();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnl_Right3 = new SmartCreator.RJControls.RJPanel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.rjPanel4 = new SmartCreator.RJControls.RJPanel();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.pnlClientArea.SuspendLayout();
            this.pnl_Right.SuspendLayout();
            this.panel1.SuspendLayout();
            this.pnl_Menul_profile.SuspendLayout();
            this.pnl_usermanger.SuspendLayout();
            this.pnl_profile_HS_local.SuspendLayout();
            this.pnl_left.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.pnl_Right2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.flowLayoutPanel2.SuspendLayout();
            this.flowLayoutPanel7.SuspendLayout();
            this.flowLayoutPanel4.SuspendLayout();
            this.flowLayoutPanel5.SuspendLayout();
            this.pnl_left2.SuspendLayout();
            this.panel_PreviewTempateCards.SuspendLayout();
            this.groupBox2.SuspendLayout();
            this.panel2.SuspendLayout();
            this.pnl_Right3.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.rjPanel4);
            this.pnlClientArea.Controls.Add(this.pnl_Right3);
            this.pnlClientArea.Controls.Add(this.pnl_Right2);
            this.pnlClientArea.Controls.Add(this.pnl_Right);
            this.pnlClientArea.Controls.Add(this.pnl_left);
            this.pnlClientArea.Controls.Add(this.pnl_left2);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 584);
            this.pnlClientArea.SizeChanged += new System.EventHandler(this.pnlClientArea_SizeChanged);
            this.pnlClientArea.Resize += new System.EventHandler(this.pnlClientArea_Resize);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(132, 17);
            this.lblCaption.Text = "FormAddHotspotCards";
            // 
            // pnl_Right
            // 
            this.pnl_Right.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_Right.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_Right.BorderRadius = 7;
            this.pnl_Right.Controls.Add(this.btn_Script_Smart);
            this.pnl_Right.Controls.Add(this.panel1);
            this.pnl_Right.Controls.Add(this.Cbox_Profile_Select_Typ);
            this.pnl_Right.Controls.Add(this.lbl_Cbox_Profile_Select_Typ);
            this.pnl_Right.Controls.Add(this.CBox_Server_hotspot);
            this.pnl_Right.Controls.Add(this.CBox_profile_Source_hotspot);
            this.pnl_Right.Controls.Add(this.lbl_Server_hotspot);
            this.pnl_Right.Controls.Add(this.lbl_profile2);
            this.pnl_Right.Controls.Add(this.lbl_profile);
            this.pnl_Right.Controls.Add(this.txtNumberCard);
            this.pnl_Right.Controls.Add(this.lbl_startCard);
            this.pnl_Right.Controls.Add(this.txt_EndCard);
            this.pnl_Right.Controls.Add(this.lbl_endCards);
            this.pnl_Right.Controls.Add(this.lbl_UserPassword_Pattern);
            this.pnl_Right.Controls.Add(this.txt_StartCard);
            this.pnl_Right.Controls.Add(this.cbox_UserPassword_Pattern);
            this.pnl_Right.Controls.Add(this.lbl_By_Number_Cards);
            this.pnl_Right.Controls.Add(this.checkBox_Add_Smart_Validatiy);
            this.pnl_Right.Controls.Add(this.pnl_Menul_profile);
            this.pnl_Right.Controls.Add(this.pnl_usermanger);
            this.pnl_Right.Controls.Add(this.pnl_profile_HS_local);
            this.pnl_Right.Customizable = false;
            this.pnl_Right.Location = new System.Drawing.Point(350, 5);
            this.pnl_Right.Margin = new System.Windows.Forms.Padding(3, 20, 3, 3);
            this.pnl_Right.Name = "pnl_Right";
            this.pnl_Right.Padding = new System.Windows.Forms.Padding(10, 5, 5, 0);
            this.pnl_Right.Size = new System.Drawing.Size(635, 279);
            this.pnl_Right.TabIndex = 21;
            // 
            // btn_Script_Smart
            // 
            this.btn_Script_Smart.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Script_Smart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Script_Smart.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Script_Smart.BorderRadius = 5;
            this.btn_Script_Smart.BorderSize = 1;
            this.btn_Script_Smart.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Script_Smart.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Script_Smart.FlatAppearance.BorderSize = 0;
            this.btn_Script_Smart.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Script_Smart.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Script_Smart.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Script_Smart.Font = new System.Drawing.Font("Droid Sans Arabic", 11F);
            this.btn_Script_Smart.ForeColor = System.Drawing.Color.White;
            this.btn_Script_Smart.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_Script_Smart.IconColor = System.Drawing.Color.White;
            this.btn_Script_Smart.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Script_Smart.IconSize = 1;
            this.btn_Script_Smart.Location = new System.Drawing.Point(356, 240);
            this.btn_Script_Smart.Name = "btn_Script_Smart";
            this.btn_Script_Smart.Size = new System.Drawing.Size(79, 28);
            this.btn_Script_Smart.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Script_Smart.TabIndex = 21;
            this.btn_Script_Smart.Text = "تخصيص";
            this.btn_Script_Smart.UseCompatibleTextRendering = true;
            this.btn_Script_Smart.UseVisualStyleBackColor = false;
            this.btn_Script_Smart.Click += new System.EventHandler(this.btn_Script_Smart_Click);
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.btn_Add_template);
            this.panel1.Controls.Add(this.btn_Add_SellingPoint);
            this.panel1.Controls.Add(this.lbl_TemplateCards);
            this.panel1.Controls.Add(this.CBox_SellingPoint);
            this.panel1.Controls.Add(this.lbl_SellingPoint);
            this.panel1.Controls.Add(this.cbox_User_NumberORcharcter);
            this.panel1.Controls.Add(this.CBox_TemplateCards);
            this.panel1.Controls.Add(this.txt_longPassword);
            this.panel1.Controls.Add(this.lbl_User_NumberORcharcter);
            this.panel1.Controls.Add(this.cbox_Pass_NumberORcharcter);
            this.panel1.Controls.Add(this.txt_longUsers);
            this.panel1.Controls.Add(this.lbl_Pass_NumberORcharcter);
            this.panel1.Location = new System.Drawing.Point(11, 122);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(330, 151);
            this.panel1.TabIndex = 30;
            // 
            // btn_Add_template
            // 
            this.btn_Add_template.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Add_template.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Add_template.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_template.BorderRadius = 5;
            this.btn_Add_template.BorderSize = 1;
            this.btn_Add_template.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Add_template.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Add_template.FlatAppearance.BorderSize = 0;
            this.btn_Add_template.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Add_template.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Add_template.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Add_template.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Add_template.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_template.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btn_Add_template.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_template.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Add_template.IconSize = 18;
            this.btn_Add_template.Location = new System.Drawing.Point(14, 3);
            this.btn_Add_template.Name = "btn_Add_template";
            this.btn_Add_template.Size = new System.Drawing.Size(31, 32);
            this.btn_Add_template.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Add_template.TabIndex = 21;
            this.btn_Add_template.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Add_template.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Add_template.UseVisualStyleBackColor = false;
            this.btn_Add_template.Click += new System.EventHandler(this.btn_Add_template_Click);
            // 
            // btn_Add_SellingPoint
            // 
            this.btn_Add_SellingPoint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Add_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Add_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_SellingPoint.BorderRadius = 5;
            this.btn_Add_SellingPoint.BorderSize = 1;
            this.btn_Add_SellingPoint.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Add_SellingPoint.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Add_SellingPoint.FlatAppearance.BorderSize = 0;
            this.btn_Add_SellingPoint.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Add_SellingPoint.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Add_SellingPoint.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Add_SellingPoint.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Add_SellingPoint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_SellingPoint.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btn_Add_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_SellingPoint.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Add_SellingPoint.IconSize = 18;
            this.btn_Add_SellingPoint.Location = new System.Drawing.Point(14, 41);
            this.btn_Add_SellingPoint.Name = "btn_Add_SellingPoint";
            this.btn_Add_SellingPoint.Size = new System.Drawing.Size(31, 32);
            this.btn_Add_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Add_SellingPoint.TabIndex = 21;
            this.btn_Add_SellingPoint.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Add_SellingPoint.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Add_SellingPoint.UseVisualStyleBackColor = false;
            this.btn_Add_SellingPoint.Click += new System.EventHandler(this.btn_Add_SellingPoint_Click);
            // 
            // lbl_TemplateCards
            // 
            this.lbl_TemplateCards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_TemplateCards.AutoSize = true;
            this.lbl_TemplateCards.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_TemplateCards.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_TemplateCards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_TemplateCards.LinkLabel = false;
            this.lbl_TemplateCards.Location = new System.Drawing.Point(232, 12);
            this.lbl_TemplateCards.Name = "lbl_TemplateCards";
            this.lbl_TemplateCards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_TemplateCards.Size = new System.Drawing.Size(74, 17);
            this.lbl_TemplateCards.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_TemplateCards.TabIndex = 16;
            this.lbl_TemplateCards.Text = "قالب الطباعة";
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.Font = new System.Drawing.Font("Cairo", 8F);
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(46, 40);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.SelectedItem = null;
            this.CBox_SellingPoint.SelectedValue = null;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(178, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 19;
            this.CBox_SellingPoint.Texts = "";
            this.CBox_SellingPoint.OnSelectedIndexChanged += new System.EventHandler(this.CBox_SellingPoint_OnSelectedIndexChanged);
            // 
            // lbl_SellingPoint
            // 
            this.lbl_SellingPoint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_SellingPoint.AutoSize = true;
            this.lbl_SellingPoint.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_SellingPoint.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_SellingPoint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_SellingPoint.LinkLabel = false;
            this.lbl_SellingPoint.Location = new System.Drawing.Point(236, 48);
            this.lbl_SellingPoint.Name = "lbl_SellingPoint";
            this.lbl_SellingPoint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_SellingPoint.Size = new System.Drawing.Size(66, 17);
            this.lbl_SellingPoint.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_SellingPoint.TabIndex = 16;
            this.lbl_SellingPoint.Text = "نقطـــة البيع";
            // 
            // cbox_User_NumberORcharcter
            // 
            this.cbox_User_NumberORcharcter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbox_User_NumberORcharcter.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cbox_User_NumberORcharcter.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cbox_User_NumberORcharcter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cbox_User_NumberORcharcter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_User_NumberORcharcter.BorderRadius = 5;
            this.cbox_User_NumberORcharcter.BorderSize = 1;
            this.cbox_User_NumberORcharcter.Customizable = false;
            this.cbox_User_NumberORcharcter.DataSource = null;
            this.cbox_User_NumberORcharcter.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cbox_User_NumberORcharcter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.cbox_User_NumberORcharcter.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_User_NumberORcharcter.Font = new System.Drawing.Font("Cairo", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbox_User_NumberORcharcter.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cbox_User_NumberORcharcter.Items.AddRange(new object[] {
            "ارقام فقط",
            "حروف فقط",
            "ارقام وحروف"});
            this.cbox_User_NumberORcharcter.Location = new System.Drawing.Point(46, 77);
            this.cbox_User_NumberORcharcter.Name = "cbox_User_NumberORcharcter";
            this.cbox_User_NumberORcharcter.Padding = new System.Windows.Forms.Padding(2);
            this.cbox_User_NumberORcharcter.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.cbox_User_NumberORcharcter.SelectedIndex = -1;
            this.cbox_User_NumberORcharcter.SelectedItem = null;
            this.cbox_User_NumberORcharcter.SelectedValue = null;
            this.cbox_User_NumberORcharcter.Size = new System.Drawing.Size(178, 32);
            this.cbox_User_NumberORcharcter.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cbox_User_NumberORcharcter.TabIndex = 19;
            this.cbox_User_NumberORcharcter.Texts = "";
            // 
            // CBox_TemplateCards
            // 
            this.CBox_TemplateCards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_TemplateCards.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_TemplateCards.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_TemplateCards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_TemplateCards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_TemplateCards.BorderRadius = 5;
            this.CBox_TemplateCards.BorderSize = 1;
            this.CBox_TemplateCards.Customizable = false;
            this.CBox_TemplateCards.DataSource = null;
            this.CBox_TemplateCards.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_TemplateCards.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_TemplateCards.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_TemplateCards.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_TemplateCards.Location = new System.Drawing.Point(46, 5);
            this.CBox_TemplateCards.Name = "CBox_TemplateCards";
            this.CBox_TemplateCards.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_TemplateCards.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_TemplateCards.SelectedIndex = -1;
            this.CBox_TemplateCards.SelectedItem = null;
            this.CBox_TemplateCards.SelectedValue = null;
            this.CBox_TemplateCards.Size = new System.Drawing.Size(177, 32);
            this.CBox_TemplateCards.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_TemplateCards.TabIndex = 19;
            this.CBox_TemplateCards.Texts = "";
            this.CBox_TemplateCards.OnSelectedIndexChanged += new System.EventHandler(this.CBox_TemplateCards_OnSelectedIndexChanged);
            // 
            // txt_longPassword
            // 
            this.txt_longPassword._Customizable = false;
            this.txt_longPassword.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_longPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_longPassword.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_longPassword.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_longPassword.BorderRadius = 5;
            this.txt_longPassword.BorderSize = 1;
            this.txt_longPassword.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_longPassword.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_longPassword.Location = new System.Drawing.Point(11, 115);
            this.txt_longPassword.MultiLine = false;
            this.txt_longPassword.Name = "txt_longPassword";
            this.txt_longPassword.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_longPassword.PasswordChar = false;
            this.txt_longPassword.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_longPassword.PlaceHolderText = null;
            this.txt_longPassword.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_longPassword.Size = new System.Drawing.Size(35, 27);
            this.txt_longPassword.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_longPassword.TabIndex = 27;
            this.txt_longPassword.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_User_NumberORcharcter
            // 
            this.lbl_User_NumberORcharcter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_User_NumberORcharcter.AutoSize = true;
            this.lbl_User_NumberORcharcter.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_User_NumberORcharcter.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_User_NumberORcharcter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_User_NumberORcharcter.LinkLabel = false;
            this.lbl_User_NumberORcharcter.Location = new System.Drawing.Point(231, 84);
            this.lbl_User_NumberORcharcter.Name = "lbl_User_NumberORcharcter";
            this.lbl_User_NumberORcharcter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_User_NumberORcharcter.Size = new System.Drawing.Size(74, 17);
            this.lbl_User_NumberORcharcter.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_User_NumberORcharcter.TabIndex = 16;
            this.lbl_User_NumberORcharcter.Text = "نمـــط الاســـم";
            // 
            // cbox_Pass_NumberORcharcter
            // 
            this.cbox_Pass_NumberORcharcter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbox_Pass_NumberORcharcter.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cbox_Pass_NumberORcharcter.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cbox_Pass_NumberORcharcter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cbox_Pass_NumberORcharcter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_Pass_NumberORcharcter.BorderRadius = 5;
            this.cbox_Pass_NumberORcharcter.BorderSize = 1;
            this.cbox_Pass_NumberORcharcter.Customizable = false;
            this.cbox_Pass_NumberORcharcter.DataSource = null;
            this.cbox_Pass_NumberORcharcter.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cbox_Pass_NumberORcharcter.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.cbox_Pass_NumberORcharcter.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_Pass_NumberORcharcter.Font = new System.Drawing.Font("Droid Sans Arabic", 9.75F);
            this.cbox_Pass_NumberORcharcter.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cbox_Pass_NumberORcharcter.Items.AddRange(new object[] {
            "ارقام فقط",
            "حروف فقط",
            "ارقام وحروف"});
            this.cbox_Pass_NumberORcharcter.Location = new System.Drawing.Point(46, 113);
            this.cbox_Pass_NumberORcharcter.Name = "cbox_Pass_NumberORcharcter";
            this.cbox_Pass_NumberORcharcter.Padding = new System.Windows.Forms.Padding(2);
            this.cbox_Pass_NumberORcharcter.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.cbox_Pass_NumberORcharcter.SelectedIndex = -1;
            this.cbox_Pass_NumberORcharcter.SelectedItem = null;
            this.cbox_Pass_NumberORcharcter.SelectedValue = null;
            this.cbox_Pass_NumberORcharcter.Size = new System.Drawing.Size(178, 32);
            this.cbox_Pass_NumberORcharcter.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cbox_Pass_NumberORcharcter.TabIndex = 19;
            this.cbox_Pass_NumberORcharcter.Texts = "";
            // 
            // txt_longUsers
            // 
            this.txt_longUsers._Customizable = false;
            this.txt_longUsers.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_longUsers.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_longUsers.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_longUsers.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_longUsers.BorderRadius = 5;
            this.txt_longUsers.BorderSize = 1;
            this.txt_longUsers.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_longUsers.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_longUsers.Location = new System.Drawing.Point(11, 78);
            this.txt_longUsers.MultiLine = false;
            this.txt_longUsers.Name = "txt_longUsers";
            this.txt_longUsers.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_longUsers.PasswordChar = false;
            this.txt_longUsers.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_longUsers.PlaceHolderText = null;
            this.txt_longUsers.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_longUsers.Size = new System.Drawing.Size(35, 27);
            this.txt_longUsers.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_longUsers.TabIndex = 26;
            this.txt_longUsers.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_Pass_NumberORcharcter
            // 
            this.lbl_Pass_NumberORcharcter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Pass_NumberORcharcter.AutoSize = true;
            this.lbl_Pass_NumberORcharcter.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Pass_NumberORcharcter.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Pass_NumberORcharcter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Pass_NumberORcharcter.LinkLabel = false;
            this.lbl_Pass_NumberORcharcter.Location = new System.Drawing.Point(231, 116);
            this.lbl_Pass_NumberORcharcter.Name = "lbl_Pass_NumberORcharcter";
            this.lbl_Pass_NumberORcharcter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Pass_NumberORcharcter.Size = new System.Drawing.Size(75, 17);
            this.lbl_Pass_NumberORcharcter.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Pass_NumberORcharcter.TabIndex = 16;
            this.lbl_Pass_NumberORcharcter.Text = "نمـــط المـــرور";
            // 
            // Cbox_Profile_Select_Typ
            // 
            this.Cbox_Profile_Select_Typ.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Cbox_Profile_Select_Typ.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.Cbox_Profile_Select_Typ.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.Cbox_Profile_Select_Typ.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Cbox_Profile_Select_Typ.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_Profile_Select_Typ.BorderRadius = 5;
            this.Cbox_Profile_Select_Typ.BorderSize = 1;
            this.Cbox_Profile_Select_Typ.Customizable = false;
            this.Cbox_Profile_Select_Typ.DataSource = null;
            this.Cbox_Profile_Select_Typ.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Cbox_Profile_Select_Typ.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Cbox_Profile_Select_Typ.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_Profile_Select_Typ.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Cbox_Profile_Select_Typ.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Cbox_Profile_Select_Typ.Items.AddRange(new object[] {
            "باقات هوتسبوت المحلية",
            "عن طريق تحديد يدوي",
            "بواسطة باقات يوزمنجر"});
            this.Cbox_Profile_Select_Typ.Location = new System.Drawing.Point(12, 5);
            this.Cbox_Profile_Select_Typ.Name = "Cbox_Profile_Select_Typ";
            this.Cbox_Profile_Select_Typ.Padding = new System.Windows.Forms.Padding(2);
            this.Cbox_Profile_Select_Typ.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Cbox_Profile_Select_Typ.SelectedIndex = -1;
            this.Cbox_Profile_Select_Typ.SelectedItem = null;
            this.Cbox_Profile_Select_Typ.SelectedValue = null;
            this.Cbox_Profile_Select_Typ.Size = new System.Drawing.Size(223, 35);
            this.Cbox_Profile_Select_Typ.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Cbox_Profile_Select_Typ.TabIndex = 17;
            this.Cbox_Profile_Select_Typ.Texts = "";
            this.Cbox_Profile_Select_Typ.OnSelectedIndexChanged += new System.EventHandler(this.Cbox_Profile_Select_Typ_OnSelectedIndexChanged);
            // 
            // lbl_Cbox_Profile_Select_Typ
            // 
            this.lbl_Cbox_Profile_Select_Typ.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Cbox_Profile_Select_Typ.AutoSize = true;
            this.lbl_Cbox_Profile_Select_Typ.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Cbox_Profile_Select_Typ.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Cbox_Profile_Select_Typ.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Cbox_Profile_Select_Typ.LinkLabel = false;
            this.lbl_Cbox_Profile_Select_Typ.Location = new System.Drawing.Point(241, 16);
            this.lbl_Cbox_Profile_Select_Typ.Name = "lbl_Cbox_Profile_Select_Typ";
            this.lbl_Cbox_Profile_Select_Typ.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Cbox_Profile_Select_Typ.Size = new System.Drawing.Size(71, 17);
            this.lbl_Cbox_Profile_Select_Typ.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Cbox_Profile_Select_Typ.TabIndex = 16;
            this.lbl_Cbox_Profile_Select_Typ.Text = "خيارات الباقة";
            // 
            // CBox_Server_hotspot
            // 
            this.CBox_Server_hotspot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Server_hotspot.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Server_hotspot.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Server_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Server_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_hotspot.BorderRadius = 5;
            this.CBox_Server_hotspot.BorderSize = 1;
            this.CBox_Server_hotspot.Customizable = false;
            this.CBox_Server_hotspot.DataSource = null;
            this.CBox_Server_hotspot.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Server_hotspot.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Server_hotspot.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_hotspot.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Server_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Server_hotspot.Items.AddRange(new object[] {
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"});
            this.CBox_Server_hotspot.Location = new System.Drawing.Point(359, 199);
            this.CBox_Server_hotspot.Name = "CBox_Server_hotspot";
            this.CBox_Server_hotspot.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Server_hotspot.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_Server_hotspot.SelectedIndex = -1;
            this.CBox_Server_hotspot.SelectedItem = null;
            this.CBox_Server_hotspot.SelectedValue = null;
            this.CBox_Server_hotspot.Size = new System.Drawing.Size(182, 33);
            this.CBox_Server_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Server_hotspot.TabIndex = 23;
            this.CBox_Server_hotspot.Texts = "";
            // 
            // CBox_profile_Source_hotspot
            // 
            this.CBox_profile_Source_hotspot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_profile_Source_hotspot.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_profile_Source_hotspot.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_profile_Source_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_profile_Source_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_Source_hotspot.BorderRadius = 5;
            this.CBox_profile_Source_hotspot.BorderSize = 1;
            this.CBox_profile_Source_hotspot.Customizable = false;
            this.CBox_profile_Source_hotspot.DataSource = null;
            this.CBox_profile_Source_hotspot.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_profile_Source_hotspot.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_profile_Source_hotspot.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_Source_hotspot.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_profile_Source_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_profile_Source_hotspot.Items.AddRange(new object[] {
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"});
            this.CBox_profile_Source_hotspot.Location = new System.Drawing.Point(359, 159);
            this.CBox_profile_Source_hotspot.Name = "CBox_profile_Source_hotspot";
            this.CBox_profile_Source_hotspot.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_profile_Source_hotspot.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_profile_Source_hotspot.SelectedIndex = -1;
            this.CBox_profile_Source_hotspot.SelectedItem = null;
            this.CBox_profile_Source_hotspot.SelectedValue = null;
            this.CBox_profile_Source_hotspot.Size = new System.Drawing.Size(182, 33);
            this.CBox_profile_Source_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_profile_Source_hotspot.TabIndex = 23;
            this.CBox_profile_Source_hotspot.Texts = "";
            this.CBox_profile_Source_hotspot.OnSelectedIndexChanged += new System.EventHandler(this.CBox_profile_Source_hotspot_OnSelectedIndexChanged);
            // 
            // lbl_Server_hotspot
            // 
            this.lbl_Server_hotspot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Server_hotspot.AutoSize = true;
            this.lbl_Server_hotspot.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Server_hotspot.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Server_hotspot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Server_hotspot.LinkLabel = false;
            this.lbl_Server_hotspot.Location = new System.Drawing.Point(560, 208);
            this.lbl_Server_hotspot.Name = "lbl_Server_hotspot";
            this.lbl_Server_hotspot.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Server_hotspot.Size = new System.Drawing.Size(54, 17);
            this.lbl_Server_hotspot.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Server_hotspot.TabIndex = 16;
            this.lbl_Server_hotspot.Text = "الســـــيرفر";
            // 
            // lbl_profile2
            // 
            this.lbl_profile2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_profile2.AutoSize = true;
            this.lbl_profile2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_profile2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_profile2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_profile2.LinkLabel = false;
            this.lbl_profile2.Location = new System.Drawing.Point(557, 177);
            this.lbl_profile2.Name = "lbl_profile2";
            this.lbl_profile2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_profile2.Size = new System.Drawing.Size(66, 17);
            this.lbl_profile2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_profile2.TabIndex = 16;
            this.lbl_profile2.Text = "الهوتسبوت";
            // 
            // lbl_profile
            // 
            this.lbl_profile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_profile.AutoSize = true;
            this.lbl_profile.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_profile.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_profile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_profile.LinkLabel = false;
            this.lbl_profile.Location = new System.Drawing.Point(567, 155);
            this.lbl_profile.Name = "lbl_profile";
            this.lbl_profile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_profile.Size = new System.Drawing.Size(46, 17);
            this.lbl_profile.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_profile.TabIndex = 16;
            this.lbl_profile.Text = "بروفايل";
            // 
            // txtNumberCard
            // 
            this.txtNumberCard._Customizable = false;
            this.txtNumberCard.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtNumberCard.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtNumberCard.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtNumberCard.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtNumberCard.BorderRadius = 5;
            this.txtNumberCard.BorderSize = 1;
            this.txtNumberCard.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txtNumberCard.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtNumberCard.Location = new System.Drawing.Point(359, 6);
            this.txtNumberCard.MultiLine = false;
            this.txtNumberCard.Name = "txtNumberCard";
            this.txtNumberCard.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtNumberCard.PasswordChar = false;
            this.txtNumberCard.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtNumberCard.PlaceHolderText = null;
            this.txtNumberCard.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtNumberCard.Size = new System.Drawing.Size(182, 28);
            this.txtNumberCard.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtNumberCard.TabIndex = 23;
            this.txtNumberCard.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_startCard
            // 
            this.lbl_startCard.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_startCard.AutoSize = true;
            this.lbl_startCard.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_startCard.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_startCard.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_startCard.LinkLabel = false;
            this.lbl_startCard.Location = new System.Drawing.Point(550, 50);
            this.lbl_startCard.Name = "lbl_startCard";
            this.lbl_startCard.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_startCard.Size = new System.Drawing.Size(68, 17);
            this.lbl_startCard.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_startCard.TabIndex = 16;
            this.lbl_startCard.Text = "بادئة للكــرت";
            // 
            // txt_EndCard
            // 
            this.txt_EndCard._Customizable = false;
            this.txt_EndCard.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_EndCard.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_EndCard.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_EndCard.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_EndCard.BorderRadius = 5;
            this.txt_EndCard.BorderSize = 1;
            this.txt_EndCard.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_EndCard.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_EndCard.Location = new System.Drawing.Point(359, 81);
            this.txt_EndCard.MultiLine = false;
            this.txt_EndCard.Name = "txt_EndCard";
            this.txt_EndCard.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_EndCard.PasswordChar = false;
            this.txt_EndCard.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_EndCard.PlaceHolderText = null;
            this.txt_EndCard.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_EndCard.Size = new System.Drawing.Size(182, 28);
            this.txt_EndCard.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_EndCard.TabIndex = 23;
            this.txt_EndCard.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_endCards
            // 
            this.lbl_endCards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_endCards.AutoSize = true;
            this.lbl_endCards.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_endCards.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_endCards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_endCards.LinkLabel = false;
            this.lbl_endCards.Location = new System.Drawing.Point(546, 85);
            this.lbl_endCards.Name = "lbl_endCards";
            this.lbl_endCards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_endCards.Size = new System.Drawing.Size(72, 17);
            this.lbl_endCards.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_endCards.TabIndex = 16;
            this.lbl_endCards.Text = "نهــاية للكرت";
            // 
            // lbl_UserPassword_Pattern
            // 
            this.lbl_UserPassword_Pattern.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_UserPassword_Pattern.AutoSize = true;
            this.lbl_UserPassword_Pattern.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_UserPassword_Pattern.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_UserPassword_Pattern.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_UserPassword_Pattern.LinkLabel = false;
            this.lbl_UserPassword_Pattern.Location = new System.Drawing.Point(548, 125);
            this.lbl_UserPassword_Pattern.Name = "lbl_UserPassword_Pattern";
            this.lbl_UserPassword_Pattern.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_UserPassword_Pattern.Size = new System.Drawing.Size(70, 17);
            this.lbl_UserPassword_Pattern.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_UserPassword_Pattern.TabIndex = 16;
            this.lbl_UserPassword_Pattern.Text = "صيــغة الكرت";
            // 
            // txt_StartCard
            // 
            this.txt_StartCard._Customizable = false;
            this.txt_StartCard.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_StartCard.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_StartCard.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_StartCard.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_StartCard.BorderRadius = 5;
            this.txt_StartCard.BorderSize = 1;
            this.txt_StartCard.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_StartCard.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_StartCard.Location = new System.Drawing.Point(359, 43);
            this.txt_StartCard.MultiLine = false;
            this.txt_StartCard.Name = "txt_StartCard";
            this.txt_StartCard.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_StartCard.PasswordChar = false;
            this.txt_StartCard.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_StartCard.PlaceHolderText = null;
            this.txt_StartCard.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_StartCard.Size = new System.Drawing.Size(182, 28);
            this.txt_StartCard.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_StartCard.TabIndex = 23;
            this.txt_StartCard.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // cbox_UserPassword_Pattern
            // 
            this.cbox_UserPassword_Pattern.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.cbox_UserPassword_Pattern.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cbox_UserPassword_Pattern.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cbox_UserPassword_Pattern.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cbox_UserPassword_Pattern.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_UserPassword_Pattern.BorderRadius = 5;
            this.cbox_UserPassword_Pattern.BorderSize = 1;
            this.cbox_UserPassword_Pattern.Customizable = false;
            this.cbox_UserPassword_Pattern.DataSource = null;
            this.cbox_UserPassword_Pattern.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cbox_UserPassword_Pattern.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.cbox_UserPassword_Pattern.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbox_UserPassword_Pattern.Font = new System.Drawing.Font("Cairo", 8F);
            this.cbox_UserPassword_Pattern.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cbox_UserPassword_Pattern.Items.AddRange(new object[] {
            "كلمة سر فارغة",
            "اسم وكلمة متساوين",
            "اسم وكلمة سر مختلفين"});
            this.cbox_UserPassword_Pattern.Location = new System.Drawing.Point(359, 120);
            this.cbox_UserPassword_Pattern.Name = "cbox_UserPassword_Pattern";
            this.cbox_UserPassword_Pattern.Padding = new System.Windows.Forms.Padding(2);
            this.cbox_UserPassword_Pattern.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.cbox_UserPassword_Pattern.SelectedIndex = -1;
            this.cbox_UserPassword_Pattern.SelectedItem = null;
            this.cbox_UserPassword_Pattern.SelectedValue = null;
            this.cbox_UserPassword_Pattern.Size = new System.Drawing.Size(182, 33);
            this.cbox_UserPassword_Pattern.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cbox_UserPassword_Pattern.TabIndex = 22;
            this.cbox_UserPassword_Pattern.Texts = "";
            // 
            // lbl_By_Number_Cards
            // 
            this.lbl_By_Number_Cards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_By_Number_Cards.AutoSize = true;
            this.lbl_By_Number_Cards.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_By_Number_Cards.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_By_Number_Cards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_By_Number_Cards.LinkLabel = false;
            this.lbl_By_Number_Cards.Location = new System.Drawing.Point(554, 12);
            this.lbl_By_Number_Cards.Name = "lbl_By_Number_Cards";
            this.lbl_By_Number_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_By_Number_Cards.Size = new System.Drawing.Size(64, 17);
            this.lbl_By_Number_Cards.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_By_Number_Cards.TabIndex = 1;
            this.lbl_By_Number_Cards.Text = "عدد الكروت";
            // 
            // checkBox_Add_Smart_Validatiy
            // 
            this.checkBox_Add_Smart_Validatiy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBox_Add_Smart_Validatiy.AutoSize = true;
            this.checkBox_Add_Smart_Validatiy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_Add_Smart_Validatiy.BorderSize = 1;
            this.checkBox_Add_Smart_Validatiy.Check = true;
            this.checkBox_Add_Smart_Validatiy.Checked = true;
            this.checkBox_Add_Smart_Validatiy.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_Add_Smart_Validatiy.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_Add_Smart_Validatiy.Customizable = false;
            this.checkBox_Add_Smart_Validatiy.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.3F, System.Drawing.FontStyle.Bold);
            this.checkBox_Add_Smart_Validatiy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_Add_Smart_Validatiy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_Add_Smart_Validatiy.Location = new System.Drawing.Point(434, 243);
            this.checkBox_Add_Smart_Validatiy.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_Add_Smart_Validatiy.Name = "checkBox_Add_Smart_Validatiy";
            this.checkBox_Add_Smart_Validatiy.Padding = new System.Windows.Forms.Padding(0, 0, 12, 0);
            this.checkBox_Add_Smart_Validatiy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkBox_Add_Smart_Validatiy.Size = new System.Drawing.Size(183, 29);
            this.checkBox_Add_Smart_Validatiy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.checkBox_Add_Smart_Validatiy.TabIndex = 47;
            this.checkBox_Add_Smart_Validatiy.Text = "اضافة صلاحيات سمارت";
            this.checkBox_Add_Smart_Validatiy.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_Add_Smart_Validatiy.UseVisualStyleBackColor = true;
            // 
            // pnl_Menul_profile
            // 
            this.pnl_Menul_profile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_Menul_profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_Menul_profile.BorderRadius = 5;
            this.pnl_Menul_profile.Controls.Add(this.txt_price);
            this.pnl_Menul_profile.Controls.Add(this.CBox_SizeDownload);
            this.pnl_Menul_profile.Controls.Add(this.lbl_price);
            this.pnl_Menul_profile.Controls.Add(this.txt_download);
            this.pnl_Menul_profile.Controls.Add(this.lbl_validatiy);
            this.pnl_Menul_profile.Controls.Add(this.txt_validatiy);
            this.pnl_Menul_profile.Controls.Add(this.lbl_download);
            this.pnl_Menul_profile.Controls.Add(this.txt_houre);
            this.pnl_Menul_profile.Controls.Add(this.lbl_houre);
            this.pnl_Menul_profile.Customizable = true;
            this.pnl_Menul_profile.Location = new System.Drawing.Point(6, 43);
            this.pnl_Menul_profile.Name = "pnl_Menul_profile";
            this.pnl_Menul_profile.Size = new System.Drawing.Size(334, 73);
            this.pnl_Menul_profile.TabIndex = 29;
            this.pnl_Menul_profile.Visible = false;
            // 
            // txt_price
            // 
            this.txt_price._Customizable = false;
            this.txt_price.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_price.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_price.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_price.BorderRadius = 5;
            this.txt_price.BorderSize = 1;
            this.txt_price.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_price.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price.Location = new System.Drawing.Point(97, 43);
            this.txt_price.Margin = new System.Windows.Forms.Padding(0);
            this.txt_price.MultiLine = false;
            this.txt_price.Name = "txt_price";
            this.txt_price.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_price.PasswordChar = false;
            this.txt_price.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_price.PlaceHolderText = "السعر";
            this.txt_price.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_price.Size = new System.Drawing.Size(59, 26);
            this.txt_price.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_price.TabIndex = 55;
            this.txt_price.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_price.onTextChanged += new System.EventHandler(this.txt_price_onTextChanged);
            // 
            // CBox_SizeDownload
            // 
            this.CBox_SizeDownload.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SizeDownload.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SizeDownload.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SizeDownload.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SizeDownload.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SizeDownload.BorderRadius = 4;
            this.CBox_SizeDownload.BorderSize = 1;
            this.CBox_SizeDownload.Customizable = false;
            this.CBox_SizeDownload.DataSource = null;
            this.CBox_SizeDownload.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SizeDownload.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SizeDownload.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SizeDownload.Font = new System.Drawing.Font("Segoe UI", 6.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_SizeDownload.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SizeDownload.Items.AddRange(new object[] {
            "ميجابايت",
            "جيجابايت"});
            this.CBox_SizeDownload.Location = new System.Drawing.Point(6, 6);
            this.CBox_SizeDownload.Name = "CBox_SizeDownload";
            this.CBox_SizeDownload.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SizeDownload.SelectedIndex = -1;
            this.CBox_SizeDownload.SelectedItem = null;
            this.CBox_SizeDownload.SelectedValue = null;
            this.CBox_SizeDownload.Size = new System.Drawing.Size(85, 30);
            this.CBox_SizeDownload.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SizeDownload.TabIndex = 114;
            this.CBox_SizeDownload.Texts = "";
            this.CBox_SizeDownload.OnSelectedIndexChanged += new System.EventHandler(this.CBox_SizeDownload_OnSelectedIndexChanged);
            // 
            // lbl_price
            // 
            this.lbl_price.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_price.AutoSize = true;
            this.lbl_price.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_price.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_price.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_price.LinkLabel = false;
            this.lbl_price.Location = new System.Drawing.Point(170, 48);
            this.lbl_price.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_price.Name = "lbl_price";
            this.lbl_price.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_price.Size = new System.Drawing.Size(36, 17);
            this.lbl_price.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_price.TabIndex = 16;
            this.lbl_price.Text = "السعر";
            // 
            // txt_download
            // 
            this.txt_download._Customizable = false;
            this.txt_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_download.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_download.BorderRadius = 5;
            this.txt_download.BorderSize = 1;
            this.txt_download.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_download.Location = new System.Drawing.Point(97, 7);
            this.txt_download.MultiLine = false;
            this.txt_download.Name = "txt_download";
            this.txt_download.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_download.PasswordChar = false;
            this.txt_download.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_download.PlaceHolderText = "التحميل";
            this.txt_download.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_download.Size = new System.Drawing.Size(59, 26);
            this.txt_download.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_download.TabIndex = 55;
            this.txt_download.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_download.onTextChanged += new System.EventHandler(this.txt_download_onTextChanged);
            // 
            // lbl_validatiy
            // 
            this.lbl_validatiy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_validatiy.AutoSize = true;
            this.lbl_validatiy.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_validatiy.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_validatiy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_validatiy.LinkLabel = false;
            this.lbl_validatiy.Location = new System.Drawing.Point(275, 41);
            this.lbl_validatiy.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_validatiy.Name = "lbl_validatiy";
            this.lbl_validatiy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_validatiy.Size = new System.Drawing.Size(51, 17);
            this.lbl_validatiy.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_validatiy.TabIndex = 16;
            this.lbl_validatiy.Text = "الصلاحية";
            // 
            // txt_validatiy
            // 
            this.txt_validatiy._Customizable = false;
            this.txt_validatiy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_validatiy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_validatiy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_validatiy.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_validatiy.BorderRadius = 5;
            this.txt_validatiy.BorderSize = 1;
            this.txt_validatiy.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_validatiy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_validatiy.Location = new System.Drawing.Point(209, 36);
            this.txt_validatiy.Margin = new System.Windows.Forms.Padding(0);
            this.txt_validatiy.MultiLine = false;
            this.txt_validatiy.Name = "txt_validatiy";
            this.txt_validatiy.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_validatiy.PasswordChar = false;
            this.txt_validatiy.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_validatiy.PlaceHolderText = "الصلاحية";
            this.txt_validatiy.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_validatiy.Size = new System.Drawing.Size(59, 26);
            this.txt_validatiy.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_validatiy.TabIndex = 55;
            this.txt_validatiy.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_validatiy.onTextChanged += new System.EventHandler(this.txt_validatiy_onTextChanged);
            // 
            // lbl_download
            // 
            this.lbl_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_download.AutoSize = true;
            this.lbl_download.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_download.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_download.LinkLabel = false;
            this.lbl_download.Location = new System.Drawing.Point(160, 11);
            this.lbl_download.Name = "lbl_download";
            this.lbl_download.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_download.Size = new System.Drawing.Size(46, 17);
            this.lbl_download.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_download.TabIndex = 16;
            this.lbl_download.Text = "التحميل";
            // 
            // txt_houre
            // 
            this.txt_houre._Customizable = false;
            this.txt_houre.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_houre.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_houre.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_houre.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_houre.BorderRadius = 5;
            this.txt_houre.BorderSize = 1;
            this.txt_houre.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_houre.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_houre.Location = new System.Drawing.Point(209, 7);
            this.txt_houre.Margin = new System.Windows.Forms.Padding(0);
            this.txt_houre.MultiLine = false;
            this.txt_houre.Name = "txt_houre";
            this.txt_houre.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_houre.PasswordChar = false;
            this.txt_houre.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_houre.PlaceHolderText = "الساعات";
            this.txt_houre.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_houre.Size = new System.Drawing.Size(59, 26);
            this.txt_houre.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_houre.TabIndex = 55;
            this.txt_houre.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_houre.onTextChanged += new System.EventHandler(this.txt_houre_onTextChanged);
            // 
            // lbl_houre
            // 
            this.lbl_houre.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_houre.AutoSize = true;
            this.lbl_houre.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_houre.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_houre.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_houre.LinkLabel = false;
            this.lbl_houre.Location = new System.Drawing.Point(277, 11);
            this.lbl_houre.Name = "lbl_houre";
            this.lbl_houre.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_houre.Size = new System.Drawing.Size(48, 17);
            this.lbl_houre.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_houre.TabIndex = 16;
            this.lbl_houre.Text = "الساعات";
            // 
            // pnl_usermanger
            // 
            this.pnl_usermanger.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_usermanger.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_usermanger.BorderRadius = 5;
            this.pnl_usermanger.Controls.Add(this.rjLabel1);
            this.pnl_usermanger.Controls.Add(this.CBox_Profile_UserMan);
            this.pnl_usermanger.Controls.Add(this.lbl_Profile_UserMan);
            this.pnl_usermanger.Controls.Add(this.btn_show_profile);
            this.pnl_usermanger.Customizable = false;
            this.pnl_usermanger.Location = new System.Drawing.Point(12, 51);
            this.pnl_usermanger.Name = "pnl_usermanger";
            this.pnl_usermanger.Size = new System.Drawing.Size(326, 55);
            this.pnl_usermanger.TabIndex = 24;
            this.pnl_usermanger.Visible = false;
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(255, 6);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(36, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 16;
            this.rjLabel1.Text = "باقات";
            // 
            // CBox_Profile_UserMan
            // 
            this.CBox_Profile_UserMan.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Profile_UserMan.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile_UserMan.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Profile_UserMan.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile_UserMan.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_UserMan.BorderRadius = 5;
            this.CBox_Profile_UserMan.BorderSize = 1;
            this.CBox_Profile_UserMan.Customizable = false;
            this.CBox_Profile_UserMan.DataSource = null;
            this.CBox_Profile_UserMan.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile_UserMan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile_UserMan.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_UserMan.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile_UserMan.Location = new System.Drawing.Point(45, 11);
            this.CBox_Profile_UserMan.Name = "CBox_Profile_UserMan";
            this.CBox_Profile_UserMan.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile_UserMan.SelectedIndex = -1;
            this.CBox_Profile_UserMan.SelectedItem = null;
            this.CBox_Profile_UserMan.SelectedValue = null;
            this.CBox_Profile_UserMan.Size = new System.Drawing.Size(178, 32);
            this.CBox_Profile_UserMan.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile_UserMan.TabIndex = 22;
            this.CBox_Profile_UserMan.Texts = "";
            this.CBox_Profile_UserMan.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Profile_UserMan_OnSelectedIndexChanged);
            // 
            // lbl_Profile_UserMan
            // 
            this.lbl_Profile_UserMan.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Profile_UserMan.AutoSize = true;
            this.lbl_Profile_UserMan.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Profile_UserMan.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_Profile_UserMan.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Profile_UserMan.LinkLabel = false;
            this.lbl_Profile_UserMan.Location = new System.Drawing.Point(244, 23);
            this.lbl_Profile_UserMan.Name = "lbl_Profile_UserMan";
            this.lbl_Profile_UserMan.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Profile_UserMan.Size = new System.Drawing.Size(55, 22);
            this.lbl_Profile_UserMan.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Profile_UserMan.TabIndex = 16;
            this.lbl_Profile_UserMan.Text = "اليوزمنجر";
            this.lbl_Profile_UserMan.Click += new System.EventHandler(this.lbl_Profile_UserMan_Click);
            // 
            // btn_show_profile
            // 
            this.btn_show_profile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_show_profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_show_profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile.BorderRadius = 5;
            this.btn_show_profile.BorderSize = 1;
            this.btn_show_profile.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_show_profile.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_show_profile.FlatAppearance.BorderSize = 0;
            this.btn_show_profile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_show_profile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_show_profile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_show_profile.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_show_profile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btn_show_profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_show_profile.IconSize = 18;
            this.btn_show_profile.Location = new System.Drawing.Point(14, 10);
            this.btn_show_profile.Name = "btn_show_profile";
            this.btn_show_profile.Size = new System.Drawing.Size(31, 32);
            this.btn_show_profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_show_profile.TabIndex = 21;
            this.btn_show_profile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_show_profile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_show_profile.UseVisualStyleBackColor = false;
            this.btn_show_profile.Click += new System.EventHandler(this.btn_show_profile_Click);
            // 
            // pnl_profile_HS_local
            // 
            this.pnl_profile_HS_local.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_profile_HS_local.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_profile_HS_local.BorderRadius = 5;
            this.pnl_profile_HS_local.Controls.Add(this.CBox_Profile_HotspotLocal);
            this.pnl_profile_HS_local.Controls.Add(this.lbl_Profile_HotspotLocal2);
            this.pnl_profile_HS_local.Controls.Add(this.lbl_Profile_HotspotLocal);
            this.pnl_profile_HS_local.Controls.Add(this.btn_show_profile_hotspot);
            this.pnl_profile_HS_local.Customizable = false;
            this.pnl_profile_HS_local.Location = new System.Drawing.Point(12, 51);
            this.pnl_profile_HS_local.Name = "pnl_profile_HS_local";
            this.pnl_profile_HS_local.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.pnl_profile_HS_local.Size = new System.Drawing.Size(326, 55);
            this.pnl_profile_HS_local.TabIndex = 25;
            // 
            // CBox_Profile_HotspotLocal
            // 
            this.CBox_Profile_HotspotLocal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Profile_HotspotLocal.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile_HotspotLocal.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Profile_HotspotLocal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile_HotspotLocal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_HotspotLocal.BorderRadius = 5;
            this.CBox_Profile_HotspotLocal.BorderSize = 1;
            this.CBox_Profile_HotspotLocal.Customizable = false;
            this.CBox_Profile_HotspotLocal.DataSource = null;
            this.CBox_Profile_HotspotLocal.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile_HotspotLocal.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile_HotspotLocal.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_HotspotLocal.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile_HotspotLocal.Location = new System.Drawing.Point(46, 12);
            this.CBox_Profile_HotspotLocal.Margin = new System.Windows.Forms.Padding(0);
            this.CBox_Profile_HotspotLocal.Name = "CBox_Profile_HotspotLocal";
            this.CBox_Profile_HotspotLocal.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile_HotspotLocal.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_Profile_HotspotLocal.SelectedIndex = -1;
            this.CBox_Profile_HotspotLocal.SelectedItem = null;
            this.CBox_Profile_HotspotLocal.SelectedValue = null;
            this.CBox_Profile_HotspotLocal.Size = new System.Drawing.Size(178, 32);
            this.CBox_Profile_HotspotLocal.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile_HotspotLocal.TabIndex = 22;
            this.CBox_Profile_HotspotLocal.Texts = "";
            this.CBox_Profile_HotspotLocal.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Profile_HotspotLocal_OnSelectedIndexChanged);
            // 
            // lbl_Profile_HotspotLocal2
            // 
            this.lbl_Profile_HotspotLocal2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Profile_HotspotLocal2.AutoSize = true;
            this.lbl_Profile_HotspotLocal2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Profile_HotspotLocal2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Profile_HotspotLocal2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Profile_HotspotLocal2.LinkLabel = false;
            this.lbl_Profile_HotspotLocal2.Location = new System.Drawing.Point(234, 23);
            this.lbl_Profile_HotspotLocal2.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_Profile_HotspotLocal2.Name = "lbl_Profile_HotspotLocal2";
            this.lbl_Profile_HotspotLocal2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Profile_HotspotLocal2.Size = new System.Drawing.Size(66, 17);
            this.lbl_Profile_HotspotLocal2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Profile_HotspotLocal2.TabIndex = 16;
            this.lbl_Profile_HotspotLocal2.Text = "الهوتسبوت";
            // 
            // lbl_Profile_HotspotLocal
            // 
            this.lbl_Profile_HotspotLocal.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Profile_HotspotLocal.AutoSize = true;
            this.lbl_Profile_HotspotLocal.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Profile_HotspotLocal.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Profile_HotspotLocal.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Profile_HotspotLocal.LinkLabel = false;
            this.lbl_Profile_HotspotLocal.Location = new System.Drawing.Point(251, 6);
            this.lbl_Profile_HotspotLocal.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_Profile_HotspotLocal.Name = "lbl_Profile_HotspotLocal";
            this.lbl_Profile_HotspotLocal.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Profile_HotspotLocal.Size = new System.Drawing.Size(36, 17);
            this.lbl_Profile_HotspotLocal.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Profile_HotspotLocal.TabIndex = 16;
            this.lbl_Profile_HotspotLocal.Text = "باقات";
            // 
            // btn_show_profile_hotspot
            // 
            this.btn_show_profile_hotspot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_show_profile_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_show_profile_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile_hotspot.BorderRadius = 5;
            this.btn_show_profile_hotspot.BorderSize = 1;
            this.btn_show_profile_hotspot.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_show_profile_hotspot.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_show_profile_hotspot.FlatAppearance.BorderSize = 0;
            this.btn_show_profile_hotspot.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_show_profile_hotspot.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_show_profile_hotspot.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_show_profile_hotspot.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_show_profile_hotspot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile_hotspot.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btn_show_profile_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_show_profile_hotspot.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_show_profile_hotspot.IconSize = 18;
            this.btn_show_profile_hotspot.Location = new System.Drawing.Point(15, 12);
            this.btn_show_profile_hotspot.Name = "btn_show_profile_hotspot";
            this.btn_show_profile_hotspot.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_show_profile_hotspot.Size = new System.Drawing.Size(31, 32);
            this.btn_show_profile_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_show_profile_hotspot.TabIndex = 21;
            this.btn_show_profile_hotspot.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_show_profile_hotspot.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_show_profile_hotspot.UseVisualStyleBackColor = false;
            this.btn_show_profile_hotspot.Click += new System.EventHandler(this.btn_show_profile_hotspot_Click);
            // 
            // btn_OpenFolderDefault
            // 
            this.btn_OpenFolderDefault.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_OpenFolderDefault.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.BorderRadius = 5;
            this.btn_OpenFolderDefault.BorderSize = 1;
            this.btn_OpenFolderDefault.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_OpenFolderDefault.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_OpenFolderDefault.FlatAppearance.BorderSize = 0;
            this.btn_OpenFolderDefault.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_OpenFolderDefault.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_OpenFolderDefault.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_OpenFolderDefault.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_OpenFolderDefault.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.IconChar = FontAwesome.Sharp.IconChar.FolderOpen;
            this.btn_OpenFolderDefault.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_OpenFolderDefault.IconSize = 20;
            this.btn_OpenFolderDefault.Location = new System.Drawing.Point(68, 3);
            this.btn_OpenFolderDefault.Name = "btn_OpenFolderDefault";
            this.btn_OpenFolderDefault.Size = new System.Drawing.Size(31, 27);
            this.btn_OpenFolderDefault.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_OpenFolderDefault.TabIndex = 21;
            this.btn_OpenFolderDefault.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_OpenFolderDefault.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_OpenFolderDefault.UseVisualStyleBackColor = false;
            this.btn_OpenFolderDefault.Click += new System.EventHandler(this.btn_OpenFolderDefault_Click);
            // 
            // lbl_OpenAfterPrint
            // 
            this.lbl_OpenAfterPrint.AutoSize = true;
            this.lbl_OpenAfterPrint.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_OpenAfterPrint.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_OpenAfterPrint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_OpenAfterPrint.LinkLabel = false;
            this.lbl_OpenAfterPrint.Location = new System.Drawing.Point(105, 5);
            this.lbl_OpenAfterPrint.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_OpenAfterPrint.Name = "lbl_OpenAfterPrint";
            this.lbl_OpenAfterPrint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_OpenAfterPrint.Size = new System.Drawing.Size(190, 17);
            this.lbl_OpenAfterPrint.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_OpenAfterPrint.TabIndex = 16;
            this.lbl_OpenAfterPrint.Text = "فتح الملف بعد الاضافة الي النظام   ";
            // 
            // checkBoxOpenAfterPrint
            // 
            this.checkBoxOpenAfterPrint.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxOpenAfterPrint.AutoSize = true;
            this.checkBoxOpenAfterPrint.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxOpenAfterPrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxOpenAfterPrint.BorderSize = 1;
            this.checkBoxOpenAfterPrint.Check = true;
            this.checkBoxOpenAfterPrint.Checked = true;
            this.checkBoxOpenAfterPrint.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxOpenAfterPrint.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxOpenAfterPrint.Customizable = false;
            this.checkBoxOpenAfterPrint.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxOpenAfterPrint.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxOpenAfterPrint.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxOpenAfterPrint.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxOpenAfterPrint.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxOpenAfterPrint.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxOpenAfterPrint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxOpenAfterPrint.IconColor = System.Drawing.Color.White;
            this.checkBoxOpenAfterPrint.Location = new System.Drawing.Point(285, 42);
            this.checkBoxOpenAfterPrint.Margin = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.checkBoxOpenAfterPrint.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxOpenAfterPrint.Name = "checkBoxOpenAfterPrint";
            this.checkBoxOpenAfterPrint.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxOpenAfterPrint.Size = new System.Drawing.Size(21, 26);
            this.checkBoxOpenAfterPrint.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxOpenAfterPrint.TabIndex = 46;
            this.checkBoxOpenAfterPrint.Text = ".";
            this.checkBoxOpenAfterPrint.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBoxOpenAfterPrint.UseVisualStyleBackColor = false;
            // 
            // pnl_left
            // 
            this.pnl_left.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_left.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_left.BorderRadius = 7;
            this.pnl_left.Controls.Add(this.dgv);
            this.pnl_left.Controls.Add(this.lbl_Title1);
            this.pnl_left.Customizable = false;
            this.pnl_left.Location = new System.Drawing.Point(7, 5);
            this.pnl_left.Margin = new System.Windows.Forms.Padding(3, 30, 20, 3);
            this.pnl_left.Name = "pnl_left";
            this.pnl_left.Padding = new System.Windows.Forms.Padding(0, 5, 5, 0);
            this.pnl_left.Size = new System.Drawing.Size(340, 279);
            this.pnl_left.TabIndex = 22;
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = true;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 10;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.PowderBlue;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 47;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.PowderBlue;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.Padding = new System.Windows.Forms.Padding(15, 0, 0, 0);
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 47;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(11, 37);
            this.dgv.MultiSelect = false;
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 37;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Microsoft Sans Serif", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.Padding = new System.Windows.Forms.Padding(15, 0, 0, 0);
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 37;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(316, 236);
            this.dgv.TabIndex = 3;
            // 
            // lbl_Title1
            // 
            this.lbl_Title1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Title1.AutoSize = true;
            this.lbl_Title1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Title1.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.lbl_Title1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lbl_Title1.LinkLabel = false;
            this.lbl_Title1.Location = new System.Drawing.Point(63, 6);
            this.lbl_Title1.Name = "lbl_Title1";
            this.lbl_Title1.Size = new System.Drawing.Size(201, 22);
            this.lbl_Title1.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lbl_Title1.TabIndex = 33;
            this.lbl_Title1.Text = "اخر عمليات الطباعة هوتسبوت";
            // 
            // rjButton1
            // 
            this.rjButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjButton1.BackColor = System.Drawing.Color.WhiteSmoke;
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 2;
            this.rjButton1.BorderSize = 2;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(230)))), ((int)(((byte)(230)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(215)))), ((int)(((byte)(215)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Cairo Black", 10F, System.Drawing.FontStyle.Bold);
            this.rjButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.rjButton1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 23;
            this.rjButton1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton1.Location = new System.Drawing.Point(10, 20);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(115, 45);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton1.TabIndex = 28;
            this.rjButton1.Text = "اضــــافـة";
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton1.UseVisualStyleBackColor = false;
            // 
            // lbl_Title2
            // 
            this.lbl_Title2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Title2.AutoSize = true;
            this.lbl_Title2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Title2.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.lbl_Title2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lbl_Title2.LinkLabel = false;
            this.lbl_Title2.Location = new System.Drawing.Point(119, 4);
            this.lbl_Title2.Name = "lbl_Title2";
            this.lbl_Title2.Size = new System.Drawing.Size(96, 22);
            this.lbl_Title2.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lbl_Title2.TabIndex = 29;
            this.lbl_Title2.Text = "قالب الطباعة";
            // 
            // pnl_Right2
            // 
            this.pnl_Right2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_Right2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_Right2.BorderRadius = 7;
            this.pnl_Right2.Controls.Add(this.tableLayoutPanel2);
            this.pnl_Right2.Customizable = false;
            this.pnl_Right2.Location = new System.Drawing.Point(350, 289);
            this.pnl_Right2.Name = "pnl_Right2";
            this.pnl_Right2.Padding = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.pnl_Right2.Size = new System.Drawing.Size(635, 182);
            this.pnl_Right2.TabIndex = 23;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 4;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 32F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 289F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 21F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 298F));
            this.tableLayoutPanel2.Controls.Add(this.lbl_With_Archive_uniqe, 3, 2);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel2, 3, 0);
            this.tableLayoutPanel2.Controls.Add(this.checkBox_With_Archive_uniqe, 2, 2);
            this.tableLayoutPanel2.Controls.Add(this.lbl_script_File, 1, 1);
            this.tableLayoutPanel2.Controls.Add(this.checkBoxFirstUse, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.checkBoxSaveTo_script_File, 0, 1);
            this.tableLayoutPanel2.Controls.Add(this.lbl_FirstUse, 1, 0);
            this.tableLayoutPanel2.Controls.Add(this.checkBoxSaveTo_excel, 0, 2);
            this.tableLayoutPanel2.Controls.Add(this.lbl_excel, 1, 2);
            this.tableLayoutPanel2.Controls.Add(this.checkBox_RegisterAsBatch, 0, 3);
            this.tableLayoutPanel2.Controls.Add(this.lbl_RegisterAsBatch, 1, 3);
            this.tableLayoutPanel2.Controls.Add(this.checkBoxSaveTo_PDF, 2, 0);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel7, 3, 1);
            this.tableLayoutPanel2.Controls.Add(this.checkBoxOpenAfterPrint, 2, 1);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel4, 3, 3);
            this.tableLayoutPanel2.Controls.Add(this.checkBox_RegisterAs_LastBatch, 2, 3);
            this.tableLayoutPanel2.Controls.Add(this.checkBox_note, 0, 4);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel5, 1, 4);
            this.tableLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel2.Location = new System.Drawing.Point(0, 5);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.Padding = new System.Windows.Forms.Padding(0, 0, 8, 0);
            this.tableLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tableLayoutPanel2.RowCount = 5;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 22.42424F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20.60606F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 18.18182F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 20F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 18.78788F));
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 20F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(635, 177);
            this.tableLayoutPanel2.TabIndex = 139;
            // 
            // lbl_With_Archive_uniqe
            // 
            this.lbl_With_Archive_uniqe.AutoSize = true;
            this.lbl_With_Archive_uniqe.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_With_Archive_uniqe.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_With_Archive_uniqe.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_With_Archive_uniqe.LinkLabel = false;
            this.lbl_With_Archive_uniqe.Location = new System.Drawing.Point(55, 80);
            this.lbl_With_Archive_uniqe.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_With_Archive_uniqe.Name = "lbl_With_Archive_uniqe";
            this.lbl_With_Archive_uniqe.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_With_Archive_uniqe.Size = new System.Drawing.Size(227, 17);
            this.lbl_With_Archive_uniqe.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_With_Archive_uniqe.TabIndex = 16;
            this.lbl_With_Archive_uniqe.Text = "تجنب تكرار الكروت مع ارشيف الكروت المعلقة";
            // 
            // flowLayoutPanel2
            // 
            this.flowLayoutPanel2.Controls.Add(this.lbl_Save_PDF);
            this.flowLayoutPanel2.Controls.Add(this.btn_OpenLastFile);
            this.flowLayoutPanel2.Controls.Add(this.flowLayoutPanel3);
            this.flowLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel2.Location = new System.Drawing.Point(-13, 0);
            this.flowLayoutPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel2.Name = "flowLayoutPanel2";
            this.flowLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel2.Size = new System.Drawing.Size(298, 39);
            this.flowLayoutPanel2.TabIndex = 56;
            // 
            // lbl_Save_PDF
            // 
            this.lbl_Save_PDF.AutoSize = true;
            this.lbl_Save_PDF.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Save_PDF.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Save_PDF.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Save_PDF.LinkLabel = false;
            this.lbl_Save_PDF.Location = new System.Drawing.Point(107, 5);
            this.lbl_Save_PDF.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_Save_PDF.Name = "lbl_Save_PDF";
            this.lbl_Save_PDF.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Save_PDF.Size = new System.Drawing.Size(188, 17);
            this.lbl_Save_PDF.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Save_PDF.TabIndex = 16;
            this.lbl_Save_PDF.Text = "حفظ الى ملف PDF  بعد الطباعة     ";
            // 
            // btn_OpenLastFile
            // 
            this.btn_OpenLastFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_OpenLastFile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.BorderRadius = 5;
            this.btn_OpenLastFile.BorderSize = 1;
            this.btn_OpenLastFile.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_OpenLastFile.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_OpenLastFile.FlatAppearance.BorderSize = 0;
            this.btn_OpenLastFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_OpenLastFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_OpenLastFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_OpenLastFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_OpenLastFile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconChar = FontAwesome.Sharp.IconChar.File;
            this.btn_OpenLastFile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_OpenLastFile.IconSize = 18;
            this.btn_OpenLastFile.Location = new System.Drawing.Point(70, 3);
            this.btn_OpenLastFile.Name = "btn_OpenLastFile";
            this.btn_OpenLastFile.Size = new System.Drawing.Size(31, 27);
            this.btn_OpenLastFile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_OpenLastFile.TabIndex = 21;
            this.btn_OpenLastFile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_OpenLastFile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_OpenLastFile.UseVisualStyleBackColor = false;
            this.btn_OpenLastFile.Click += new System.EventHandler(this.btn_OpenLastFile_Click);
            // 
            // flowLayoutPanel3
            // 
            this.flowLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel3.Location = new System.Drawing.Point(13, 33);
            this.flowLayoutPanel3.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel3.Name = "flowLayoutPanel3";
            this.flowLayoutPanel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel3.Size = new System.Drawing.Size(285, 0);
            this.flowLayoutPanel3.TabIndex = 51;
            // 
            // checkBox_With_Archive_uniqe
            // 
            this.checkBox_With_Archive_uniqe.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBox_With_Archive_uniqe.AutoSize = true;
            this.checkBox_With_Archive_uniqe.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBox_With_Archive_uniqe.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_With_Archive_uniqe.BorderSize = 1;
            this.checkBox_With_Archive_uniqe.Check = true;
            this.checkBox_With_Archive_uniqe.Checked = true;
            this.checkBox_With_Archive_uniqe.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_With_Archive_uniqe.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_With_Archive_uniqe.Customizable = false;
            this.checkBox_With_Archive_uniqe.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_With_Archive_uniqe.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_With_Archive_uniqe.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBox_With_Archive_uniqe.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBox_With_Archive_uniqe.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBox_With_Archive_uniqe.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_With_Archive_uniqe.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_With_Archive_uniqe.IconColor = System.Drawing.Color.White;
            this.checkBox_With_Archive_uniqe.Location = new System.Drawing.Point(285, 75);
            this.checkBox_With_Archive_uniqe.Margin = new System.Windows.Forms.Padding(0);
            this.checkBox_With_Archive_uniqe.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_With_Archive_uniqe.Name = "checkBox_With_Archive_uniqe";
            this.checkBox_With_Archive_uniqe.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBox_With_Archive_uniqe.Size = new System.Drawing.Size(21, 26);
            this.checkBox_With_Archive_uniqe.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBox_With_Archive_uniqe.TabIndex = 46;
            this.checkBox_With_Archive_uniqe.Text = ".";
            this.checkBox_With_Archive_uniqe.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_With_Archive_uniqe.UseVisualStyleBackColor = false;
            // 
            // lbl_script_File
            // 
            this.lbl_script_File.AutoSize = true;
            this.lbl_script_File.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_script_File.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_script_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_script_File.LinkLabel = false;
            this.lbl_script_File.Location = new System.Drawing.Point(431, 44);
            this.lbl_script_File.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_script_File.Name = "lbl_script_File";
            this.lbl_script_File.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_script_File.Size = new System.Drawing.Size(161, 17);
            this.lbl_script_File.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_script_File.TabIndex = 16;
            this.lbl_script_File.Text = "تصدير الكروت الى ملف سكربت";
            // 
            // checkBoxFirstUse
            // 
            this.checkBoxFirstUse.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxFirstUse.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxFirstUse.AutoSize = true;
            this.checkBoxFirstUse.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxFirstUse.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxFirstUse.BorderSize = 1;
            this.checkBoxFirstUse.Check = false;
            this.checkBoxFirstUse.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxFirstUse.Customizable = false;
            this.checkBoxFirstUse.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxFirstUse.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxFirstUse.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxFirstUse.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxFirstUse.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxFirstUse.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxFirstUse.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxFirstUse.IconColor = System.Drawing.Color.White;
            this.checkBoxFirstUse.Location = new System.Drawing.Point(597, 0);
            this.checkBoxFirstUse.Margin = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxFirstUse.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxFirstUse.Name = "checkBoxFirstUse";
            this.checkBoxFirstUse.Padding = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxFirstUse.Size = new System.Drawing.Size(22, 26);
            this.checkBoxFirstUse.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxFirstUse.TabIndex = 46;
            this.checkBoxFirstUse.Text = ".";
            this.checkBoxFirstUse.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBoxFirstUse.UseVisualStyleBackColor = false;
            // 
            // checkBoxSaveTo_script_File
            // 
            this.checkBoxSaveTo_script_File.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_script_File.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_script_File.AutoSize = true;
            this.checkBoxSaveTo_script_File.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_script_File.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_script_File.BorderSize = 1;
            this.checkBoxSaveTo_script_File.Check = false;
            this.checkBoxSaveTo_script_File.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_script_File.Customizable = false;
            this.checkBoxSaveTo_script_File.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_script_File.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_script_File.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_script_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_script_File.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_script_File.Location = new System.Drawing.Point(597, 39);
            this.checkBoxSaveTo_script_File.Margin = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxSaveTo_script_File.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_script_File.Name = "checkBoxSaveTo_script_File";
            this.checkBoxSaveTo_script_File.Padding = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxSaveTo_script_File.Size = new System.Drawing.Size(22, 26);
            this.checkBoxSaveTo_script_File.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_script_File.TabIndex = 46;
            this.checkBoxSaveTo_script_File.Tag = "";
            this.checkBoxSaveTo_script_File.Text = ".";
            this.checkBoxSaveTo_script_File.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBoxSaveTo_script_File.UseVisualStyleBackColor = false;
            // 
            // lbl_FirstUse
            // 
            this.lbl_FirstUse.AutoSize = true;
            this.lbl_FirstUse.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_FirstUse.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_FirstUse.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_FirstUse.LinkLabel = false;
            this.lbl_FirstUse.Location = new System.Drawing.Point(480, 5);
            this.lbl_FirstUse.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_FirstUse.Name = "lbl_FirstUse";
            this.lbl_FirstUse.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_FirstUse.Size = new System.Drawing.Size(112, 17);
            this.lbl_FirstUse.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_FirstUse.TabIndex = 16;
            this.lbl_FirstUse.Text = "ربط الكروت بأول ماك";
            // 
            // checkBoxSaveTo_excel
            // 
            this.checkBoxSaveTo_excel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_excel.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_excel.AutoSize = true;
            this.checkBoxSaveTo_excel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_excel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_excel.BorderSize = 1;
            this.checkBoxSaveTo_excel.Check = false;
            this.checkBoxSaveTo_excel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_excel.Customizable = false;
            this.checkBoxSaveTo_excel.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_excel.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_excel.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_excel.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_excel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_excel.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_excel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_excel.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_excel.Location = new System.Drawing.Point(597, 75);
            this.checkBoxSaveTo_excel.Margin = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxSaveTo_excel.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_excel.Name = "checkBoxSaveTo_excel";
            this.checkBoxSaveTo_excel.Padding = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBoxSaveTo_excel.Size = new System.Drawing.Size(22, 26);
            this.checkBoxSaveTo_excel.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_excel.TabIndex = 46;
            this.checkBoxSaveTo_excel.Text = ".";
            this.checkBoxSaveTo_excel.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBoxSaveTo_excel.UseVisualStyleBackColor = false;
            // 
            // lbl_excel
            // 
            this.lbl_excel.AutoSize = true;
            this.lbl_excel.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_excel.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_excel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_excel.LinkLabel = false;
            this.lbl_excel.Location = new System.Drawing.Point(399, 80);
            this.lbl_excel.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_excel.Name = "lbl_excel";
            this.lbl_excel.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_excel.Size = new System.Drawing.Size(193, 17);
            this.lbl_excel.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_excel.TabIndex = 16;
            this.lbl_excel.Text = "تصدير الكروت الى ملف نصي + اكسل";
            // 
            // checkBox_RegisterAsBatch
            // 
            this.checkBox_RegisterAsBatch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBox_RegisterAsBatch.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBox_RegisterAsBatch.AutoSize = true;
            this.checkBox_RegisterAsBatch.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBox_RegisterAsBatch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAsBatch.BorderSize = 1;
            this.checkBox_RegisterAsBatch.Check = true;
            this.checkBox_RegisterAsBatch.Checked = true;
            this.checkBox_RegisterAsBatch.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_RegisterAsBatch.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_RegisterAsBatch.Customizable = false;
            this.checkBox_RegisterAsBatch.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_RegisterAsBatch.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_RegisterAsBatch.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBox_RegisterAsBatch.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBox_RegisterAsBatch.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBox_RegisterAsBatch.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_RegisterAsBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_RegisterAsBatch.IconColor = System.Drawing.Color.White;
            this.checkBox_RegisterAsBatch.Location = new System.Drawing.Point(597, 107);
            this.checkBox_RegisterAsBatch.Margin = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBox_RegisterAsBatch.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_RegisterAsBatch.Name = "checkBox_RegisterAsBatch";
            this.checkBox_RegisterAsBatch.Padding = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBox_RegisterAsBatch.Size = new System.Drawing.Size(22, 26);
            this.checkBox_RegisterAsBatch.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBox_RegisterAsBatch.TabIndex = 46;
            this.checkBox_RegisterAsBatch.Text = ".";
            this.checkBox_RegisterAsBatch.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_RegisterAsBatch.UseVisualStyleBackColor = false;
            this.checkBox_RegisterAsBatch.CheckedChanged += new System.EventHandler(this.checkBox_RegisterAsBatch_CheckedChanged);
            // 
            // lbl_RegisterAsBatch
            // 
            this.lbl_RegisterAsBatch.AutoSize = true;
            this.lbl_RegisterAsBatch.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_RegisterAsBatch.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_RegisterAsBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_RegisterAsBatch.LinkLabel = false;
            this.lbl_RegisterAsBatch.Location = new System.Drawing.Point(404, 112);
            this.lbl_RegisterAsBatch.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_RegisterAsBatch.Name = "lbl_RegisterAsBatch";
            this.lbl_RegisterAsBatch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_RegisterAsBatch.Size = new System.Drawing.Size(188, 17);
            this.lbl_RegisterAsBatch.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_RegisterAsBatch.TabIndex = 16;
            this.lbl_RegisterAsBatch.Text = "تسجيل عمليه الاضافة كدفعه جديده";
            // 
            // checkBoxSaveTo_PDF
            // 
            this.checkBoxSaveTo_PDF.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_PDF.AutoSize = true;
            this.checkBoxSaveTo_PDF.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_PDF.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_PDF.BorderSize = 1;
            this.checkBoxSaveTo_PDF.Check = true;
            this.checkBoxSaveTo_PDF.Checked = true;
            this.checkBoxSaveTo_PDF.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxSaveTo_PDF.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_PDF.Customizable = false;
            this.checkBoxSaveTo_PDF.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_PDF.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_PDF.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_PDF.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_PDF.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_PDF.Location = new System.Drawing.Point(285, 3);
            this.checkBoxSaveTo_PDF.Margin = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.checkBoxSaveTo_PDF.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_PDF.Name = "checkBoxSaveTo_PDF";
            this.checkBoxSaveTo_PDF.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_PDF.Size = new System.Drawing.Size(21, 26);
            this.checkBoxSaveTo_PDF.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_PDF.TabIndex = 46;
            this.checkBoxSaveTo_PDF.Text = ".";
            this.checkBoxSaveTo_PDF.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBoxSaveTo_PDF.UseVisualStyleBackColor = false;
            // 
            // flowLayoutPanel7
            // 
            this.flowLayoutPanel7.Controls.Add(this.lbl_OpenAfterPrint);
            this.flowLayoutPanel7.Controls.Add(this.btn_OpenFolderDefault);
            this.flowLayoutPanel7.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel7.Location = new System.Drawing.Point(-13, 39);
            this.flowLayoutPanel7.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel7.Name = "flowLayoutPanel7";
            this.flowLayoutPanel7.Size = new System.Drawing.Size(298, 36);
            this.flowLayoutPanel7.TabIndex = 52;
            // 
            // flowLayoutPanel4
            // 
            this.flowLayoutPanel4.Controls.Add(this.flowLayoutPanel6);
            this.flowLayoutPanel4.Controls.Add(this.lbl_RegisterAs_LastBatch);
            this.flowLayoutPanel4.Controls.Add(this.txt_last_batchNumber);
            this.flowLayoutPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel4.Location = new System.Drawing.Point(-13, 107);
            this.flowLayoutPanel4.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel4.Name = "flowLayoutPanel4";
            this.flowLayoutPanel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel4.Size = new System.Drawing.Size(298, 35);
            this.flowLayoutPanel4.TabIndex = 50;
            // 
            // flowLayoutPanel6
            // 
            this.flowLayoutPanel6.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel6.Location = new System.Drawing.Point(32, 0);
            this.flowLayoutPanel6.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel6.Name = "flowLayoutPanel6";
            this.flowLayoutPanel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel6.Size = new System.Drawing.Size(266, 0);
            this.flowLayoutPanel6.TabIndex = 50;
            // 
            // lbl_RegisterAs_LastBatch
            // 
            this.lbl_RegisterAs_LastBatch.AutoSize = true;
            this.lbl_RegisterAs_LastBatch.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_RegisterAs_LastBatch.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_RegisterAs_LastBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_RegisterAs_LastBatch.LinkLabel = false;
            this.lbl_RegisterAs_LastBatch.Location = new System.Drawing.Point(134, 8);
            this.lbl_RegisterAs_LastBatch.Margin = new System.Windows.Forms.Padding(3, 8, 3, 0);
            this.lbl_RegisterAs_LastBatch.Name = "lbl_RegisterAs_LastBatch";
            this.lbl_RegisterAs_LastBatch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_RegisterAs_LastBatch.Size = new System.Drawing.Size(161, 17);
            this.lbl_RegisterAs_LastBatch.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_RegisterAs_LastBatch.TabIndex = 52;
            this.lbl_RegisterAs_LastBatch.Text = "التسجيل الي دفعة سابقة برقم";
            // 
            // txt_last_batchNumber
            // 
            this.txt_last_batchNumber._Customizable = false;
            this.txt_last_batchNumber.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_last_batchNumber.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_last_batchNumber.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_last_batchNumber.BorderRadius = 5;
            this.txt_last_batchNumber.BorderSize = 1;
            this.txt_last_batchNumber.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_last_batchNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_last_batchNumber.Location = new System.Drawing.Point(63, 3);
            this.txt_last_batchNumber.MultiLine = false;
            this.txt_last_batchNumber.Name = "txt_last_batchNumber";
            this.txt_last_batchNumber.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_last_batchNumber.PasswordChar = false;
            this.txt_last_batchNumber.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_last_batchNumber.PlaceHolderText = null;
            this.txt_last_batchNumber.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.txt_last_batchNumber.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_last_batchNumber.Size = new System.Drawing.Size(65, 27);
            this.txt_last_batchNumber.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_last_batchNumber.TabIndex = 54;
            this.txt_last_batchNumber.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // checkBox_RegisterAs_LastBatch
            // 
            this.checkBox_RegisterAs_LastBatch.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBox_RegisterAs_LastBatch.AutoSize = true;
            this.checkBox_RegisterAs_LastBatch.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBox_RegisterAs_LastBatch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAs_LastBatch.BorderSize = 1;
            this.checkBox_RegisterAs_LastBatch.Check = false;
            this.checkBox_RegisterAs_LastBatch.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_RegisterAs_LastBatch.Customizable = false;
            this.checkBox_RegisterAs_LastBatch.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_RegisterAs_LastBatch.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_RegisterAs_LastBatch.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBox_RegisterAs_LastBatch.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBox_RegisterAs_LastBatch.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBox_RegisterAs_LastBatch.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_RegisterAs_LastBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_RegisterAs_LastBatch.IconColor = System.Drawing.Color.White;
            this.checkBox_RegisterAs_LastBatch.Location = new System.Drawing.Point(285, 110);
            this.checkBox_RegisterAs_LastBatch.Margin = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.checkBox_RegisterAs_LastBatch.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_RegisterAs_LastBatch.Name = "checkBox_RegisterAs_LastBatch";
            this.checkBox_RegisterAs_LastBatch.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBox_RegisterAs_LastBatch.Size = new System.Drawing.Size(21, 26);
            this.checkBox_RegisterAs_LastBatch.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBox_RegisterAs_LastBatch.TabIndex = 53;
            this.checkBox_RegisterAs_LastBatch.Text = ".";
            this.checkBox_RegisterAs_LastBatch.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_RegisterAs_LastBatch.UseVisualStyleBackColor = false;
            this.checkBox_RegisterAs_LastBatch.CheckedChanged += new System.EventHandler(this.checkBox_RegisterAs_LastBatch_CheckedChanged);
            // 
            // checkBox_note
            // 
            this.checkBox_note.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBox_note.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBox_note.AutoSize = true;
            this.checkBox_note.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBox_note.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_note.BorderSize = 1;
            this.checkBox_note.Check = false;
            this.checkBox_note.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_note.Customizable = false;
            this.checkBox_note.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_note.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_note.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBox_note.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBox_note.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBox_note.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_note.IconColor = System.Drawing.Color.White;
            this.checkBox_note.Location = new System.Drawing.Point(597, 142);
            this.checkBox_note.Margin = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBox_note.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_note.Name = "checkBox_note";
            this.checkBox_note.Padding = new System.Windows.Forms.Padding(0, 0, 2, 0);
            this.checkBox_note.Size = new System.Drawing.Size(22, 26);
            this.checkBox_note.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBox_note.TabIndex = 46;
            this.checkBox_note.Text = ".";
            this.checkBox_note.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_note.UseVisualStyleBackColor = false;
            // 
            // flowLayoutPanel5
            // 
            this.flowLayoutPanel5.Controls.Add(this.lbl_note);
            this.flowLayoutPanel5.Controls.Add(this.txt_note);
            this.flowLayoutPanel5.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel5.Location = new System.Drawing.Point(306, 142);
            this.flowLayoutPanel5.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel5.Name = "flowLayoutPanel5";
            this.flowLayoutPanel5.Size = new System.Drawing.Size(289, 35);
            this.flowLayoutPanel5.TabIndex = 51;
            // 
            // lbl_note
            // 
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(239, 8);
            this.lbl_note.Margin = new System.Windows.Forms.Padding(3, 8, 3, 0);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(47, 17);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 49;
            this.lbl_note.Text = "ملاحظة";
            // 
            // txt_note
            // 
            this.txt_note._Customizable = false;
            this.txt_note.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_note.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_note.BorderRadius = 5;
            this.txt_note.BorderSize = 1;
            this.txt_note.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.Location = new System.Drawing.Point(59, 3);
            this.txt_note.MultiLine = false;
            this.txt_note.Name = "txt_note";
            this.txt_note.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_note.PasswordChar = false;
            this.txt_note.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_note.PlaceHolderText = "";
            this.txt_note.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.txt_note.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_note.Size = new System.Drawing.Size(174, 27);
            this.txt_note.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_note.TabIndex = 28;
            this.txt_note.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // radio_fast_print
            // 
            this.radio_fast_print.AutoSize = true;
            this.radio_fast_print.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.radio_fast_print.Cursor = System.Windows.Forms.Cursors.Hand;
            this.radio_fast_print.Customizable = false;
            this.radio_fast_print.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radio_fast_print.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.radio_fast_print.Location = new System.Drawing.Point(403, 11);
            this.radio_fast_print.MinimumSize = new System.Drawing.Size(0, 21);
            this.radio_fast_print.Name = "radio_fast_print";
            this.radio_fast_print.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.radio_fast_print.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.radio_fast_print.Size = new System.Drawing.Size(24, 21);
            this.radio_fast_print.TabIndex = 55;
            this.toolTip1.SetToolTip(this.radio_fast_print, "الطباعه السريعه تعمل بواسطة  البورت 22 اذا تستخدم دخول عن بعد ولديك دمج خطوط اي ل" +
        "ا تتصل بالروتر مباشره  اعمل توجيه لهذا البورت بنفس بورت النظام الرئيسي");
            this.radio_fast_print.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.radio_fast_print.UseVisualStyleBackColor = true;
            // 
            // btnAdd
            // 
            this.btnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderRadius = 7;
            this.btnAdd.BorderSize = 2;
            this.btnAdd.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAdd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAdd.Font = new System.Drawing.Font("Droid Sans Arabic", 12F, System.Drawing.FontStyle.Bold);
            this.btnAdd.ForeColor = System.Drawing.Color.White;
            this.btnAdd.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAdd.IconColor = System.Drawing.Color.White;
            this.btnAdd.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAdd.IconSize = 20;
            this.btnAdd.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAdd.Location = new System.Drawing.Point(3, 34);
            this.btnAdd.Margin = new System.Windows.Forms.Padding(3, 2, 3, 3);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnAdd.Size = new System.Drawing.Size(112, 39);
            this.btnAdd.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAdd.TabIndex = 28;
            this.btnAdd.Text = "اضــــافـة";
            this.btnAdd.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAdd.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // lbl_radio_Print
            // 
            this.lbl_radio_Print.AutoSize = true;
            this.lbl_radio_Print.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_radio_Print.Font = new System.Drawing.Font("Droid Sans Arabic", 14F, System.Drawing.FontStyle.Bold);
            this.lbl_radio_Print.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(96)))), ((int)(((byte)(99)))));
            this.lbl_radio_Print.LinkLabel = false;
            this.lbl_radio_Print.Location = new System.Drawing.Point(431, 8);
            this.lbl_radio_Print.Margin = new System.Windows.Forms.Padding(5, 0, 0, 0);
            this.lbl_radio_Print.Name = "lbl_radio_Print";
            this.lbl_radio_Print.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_radio_Print.Size = new System.Drawing.Size(145, 24);
            this.lbl_radio_Print.Style = SmartCreator.RJControls.LabelStyle.Title2;
            this.lbl_radio_Print.TabIndex = 16;
            this.lbl_radio_Print.Text = "طباعة افتراضية";
            // 
            // lbl_radio_fast_print
            // 
            this.lbl_radio_fast_print.AutoSize = true;
            this.lbl_radio_fast_print.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_radio_fast_print.Font = new System.Drawing.Font("Droid Sans Arabic", 14F, System.Drawing.FontStyle.Bold);
            this.lbl_radio_fast_print.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(96)))), ((int)(((byte)(99)))));
            this.lbl_radio_fast_print.LinkLabel = false;
            this.lbl_radio_fast_print.Location = new System.Drawing.Point(94, 11);
            this.lbl_radio_fast_print.Margin = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.lbl_radio_fast_print.Name = "lbl_radio_fast_print";
            this.lbl_radio_fast_print.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_radio_fast_print.Size = new System.Drawing.Size(296, 21);
            this.lbl_radio_fast_print.Style = SmartCreator.RJControls.LabelStyle.Title2;
            this.lbl_radio_fast_print.TabIndex = 16;
            this.lbl_radio_fast_print.Text = "طباعة سريعة اكثر من 4 الف كرت";
            this.toolTip1.SetToolTip(this.lbl_radio_fast_print, "الطباعه السريعه تعمل بواسطة  البورت 22 اذا تستخدم دخول عن بعد ولديك دمج خطوط اي ل" +
        "ا تتصل بالروتر مباشره  اعمل توجيه لهذا البورت بنفس بورت النظام الرئيسي");
            // 
            // radio_Print
            // 
            this.radio_Print.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.radio_Print.AutoSize = true;
            this.radio_Print.CheckAlign = System.Drawing.ContentAlignment.TopRight;
            this.radio_Print.Checked = true;
            this.radio_Print.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.radio_Print.Cursor = System.Windows.Forms.Cursors.Hand;
            this.radio_Print.Customizable = false;
            this.radio_Print.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radio_Print.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.radio_Print.Location = new System.Drawing.Point(584, 11);
            this.radio_Print.MinimumSize = new System.Drawing.Size(0, 21);
            this.radio_Print.Name = "radio_Print";
            this.radio_Print.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.radio_Print.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.radio_Print.Size = new System.Drawing.Size(24, 21);
            this.radio_Print.TabIndex = 55;
            this.radio_Print.TabStop = true;
            this.radio_Print.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.radio_Print.UseVisualStyleBackColor = true;
            this.radio_Print.CheckedChanged += new System.EventHandler(this.radio_Print_CheckedChanged);
            // 
            // lbl_radio_one_user
            // 
            this.lbl_radio_one_user.AutoSize = true;
            this.lbl_radio_one_user.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_radio_one_user.Font = new System.Drawing.Font("Droid Sans Arabic", 14F, System.Drawing.FontStyle.Bold);
            this.lbl_radio_one_user.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(99)))), ((int)(((byte)(96)))), ((int)(((byte)(99)))));
            this.lbl_radio_one_user.LinkLabel = false;
            this.lbl_radio_one_user.Location = new System.Drawing.Point(470, 39);
            this.lbl_radio_one_user.Margin = new System.Windows.Forms.Padding(5, 7, 0, 0);
            this.lbl_radio_one_user.Name = "lbl_radio_one_user";
            this.lbl_radio_one_user.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_radio_one_user.Size = new System.Drawing.Size(106, 45);
            this.lbl_radio_one_user.Style = SmartCreator.RJControls.LabelStyle.Title2;
            this.lbl_radio_one_user.TabIndex = 16;
            this.lbl_radio_one_user.Text = "طباعة كرت واحد";
            // 
            // radio_one_user
            // 
            this.radio_one_user.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.radio_one_user.AutoSize = true;
            this.radio_one_user.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.radio_one_user.Cursor = System.Windows.Forms.Cursors.Hand;
            this.radio_one_user.Customizable = false;
            this.radio_one_user.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.radio_one_user.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.radio_one_user.Location = new System.Drawing.Point(584, 40);
            this.radio_one_user.Margin = new System.Windows.Forms.Padding(3, 8, 3, 3);
            this.radio_one_user.MinimumSize = new System.Drawing.Size(0, 21);
            this.radio_one_user.Name = "radio_one_user";
            this.radio_one_user.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.radio_one_user.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.radio_one_user.Size = new System.Drawing.Size(24, 21);
            this.radio_one_user.TabIndex = 55;
            this.radio_one_user.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.radio_one_user.UseVisualStyleBackColor = true;
            // 
            // pnl_left2
            // 
            this.pnl_left2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_left2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_left2.BorderRadius = 7;
            this.pnl_left2.Controls.Add(this.panel_PreviewTempateCards);
            this.pnl_left2.Controls.Add(this.panel2);
            this.pnl_left2.Customizable = false;
            this.pnl_left2.Location = new System.Drawing.Point(7, 289);
            this.pnl_left2.Name = "pnl_left2";
            this.pnl_left2.Size = new System.Drawing.Size(340, 269);
            this.pnl_left2.TabIndex = 34;
            this.pnl_left2.Resize += new System.EventHandler(this.rjPanel1_Resize);
            // 
            // panel_PreviewTempateCards
            // 
            this.panel_PreviewTempateCards.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel_PreviewTempateCards.BackColor = System.Drawing.Color.WhiteSmoke;
            this.panel_PreviewTempateCards.Controls.Add(this.groupBox2);
            this.panel_PreviewTempateCards.Location = new System.Drawing.Point(6, 33);
            this.panel_PreviewTempateCards.Name = "panel_PreviewTempateCards";
            this.panel_PreviewTempateCards.Size = new System.Drawing.Size(317, 225);
            this.panel_PreviewTempateCards.TabIndex = 32;
            // 
            // groupBox2
            // 
            this.groupBox2.Controls.Add(this.rjButton1);
            this.groupBox2.Controls.Add(this.lbl_Add_Smart_Validatiy);
            this.groupBox2.Controls.Add(this.checkBoxSaveTo_text_File);
            this.groupBox2.Controls.Add(this.CheckBox_byDayOrHour);
            this.groupBox2.Controls.Add(this.CheckBox_Save_session);
            this.groupBox2.Controls.Add(this.CheckBox_Save_download);
            this.groupBox2.Controls.Add(this.CheckBox_Save_time);
            this.groupBox2.Controls.Add(this.rjLabel2);
            this.groupBox2.Controls.Add(this.btnAdd_Fast);
            this.groupBox2.Controls.Add(this.btn_add_One);
            this.groupBox2.Location = new System.Drawing.Point(155, 27);
            this.groupBox2.Name = "groupBox2";
            this.groupBox2.Size = new System.Drawing.Size(130, 93);
            this.groupBox2.TabIndex = 55;
            this.groupBox2.TabStop = false;
            this.groupBox2.Visible = false;
            // 
            // lbl_Add_Smart_Validatiy
            // 
            this.lbl_Add_Smart_Validatiy.AutoSize = true;
            this.lbl_Add_Smart_Validatiy.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Add_Smart_Validatiy.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Add_Smart_Validatiy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Add_Smart_Validatiy.LinkLabel = false;
            this.lbl_Add_Smart_Validatiy.Location = new System.Drawing.Point(6, 65);
            this.lbl_Add_Smart_Validatiy.Margin = new System.Windows.Forms.Padding(3, 7, 3, 0);
            this.lbl_Add_Smart_Validatiy.Name = "lbl_Add_Smart_Validatiy";
            this.lbl_Add_Smart_Validatiy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Add_Smart_Validatiy.Size = new System.Drawing.Size(122, 17);
            this.lbl_Add_Smart_Validatiy.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Add_Smart_Validatiy.TabIndex = 16;
            this.lbl_Add_Smart_Validatiy.Text = "اضافة صلاحيات سمارت";
            // 
            // checkBoxSaveTo_text_File
            // 
            this.checkBoxSaveTo_text_File.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_text_File.AutoSize = true;
            this.checkBoxSaveTo_text_File.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_text_File.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_text_File.BorderSize = 1;
            this.checkBoxSaveTo_text_File.Check = false;
            this.checkBoxSaveTo_text_File.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_text_File.Customizable = false;
            this.checkBoxSaveTo_text_File.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_text_File.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_text_File.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_text_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_text_File.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_text_File.Location = new System.Drawing.Point(313, 14);
            this.checkBoxSaveTo_text_File.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_text_File.Name = "checkBoxSaveTo_text_File";
            this.checkBoxSaveTo_text_File.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_text_File.Size = new System.Drawing.Size(22, 26);
            this.checkBoxSaveTo_text_File.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_text_File.TabIndex = 46;
            this.checkBoxSaveTo_text_File.Text = ".";
            this.checkBoxSaveTo_text_File.UseVisualStyleBackColor = false;
            this.checkBoxSaveTo_text_File.Visible = false;
            // 
            // CheckBox_byDayOrHour
            // 
            this.CheckBox_byDayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_byDayOrHour.AutoSize = true;
            this.CheckBox_byDayOrHour.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.BorderSize = 1;
            this.CheckBox_byDayOrHour.Check = false;
            this.CheckBox_byDayOrHour.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDayOrHour.Customizable = false;
            this.CheckBox_byDayOrHour.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_byDayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDayOrHour.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.Location = new System.Drawing.Point(107, 23);
            this.CheckBox_byDayOrHour.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDayOrHour.Name = "CheckBox_byDayOrHour";
            this.CheckBox_byDayOrHour.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_byDayOrHour.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_byDayOrHour.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDayOrHour.TabIndex = 123;
            this.CheckBox_byDayOrHour.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_session
            // 
            this.CheckBox_Save_session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_session.AutoSize = true;
            this.CheckBox_Save_session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.BorderSize = 1;
            this.CheckBox_Save_session.Check = false;
            this.CheckBox_Save_session.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_session.Customizable = false;
            this.CheckBox_Save_session.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_session.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.Location = new System.Drawing.Point(77, 16);
            this.CheckBox_Save_session.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_session.Name = "CheckBox_Save_session";
            this.CheckBox_Save_session.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_session.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_session.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_session.TabIndex = 124;
            this.CheckBox_Save_session.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_download
            // 
            this.CheckBox_Save_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_download.AutoSize = true;
            this.CheckBox_Save_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.BorderSize = 1;
            this.CheckBox_Save_download.Check = false;
            this.CheckBox_Save_download.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_download.Customizable = false;
            this.CheckBox_Save_download.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.Location = new System.Drawing.Point(31, 23);
            this.CheckBox_Save_download.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_download.Name = "CheckBox_Save_download";
            this.CheckBox_Save_download.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_download.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_download.TabIndex = 125;
            this.CheckBox_Save_download.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_time
            // 
            this.CheckBox_Save_time.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_time.AutoSize = true;
            this.CheckBox_Save_time.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.BorderSize = 1;
            this.CheckBox_Save_time.Check = false;
            this.CheckBox_Save_time.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_time.Customizable = false;
            this.CheckBox_Save_time.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_time.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_time.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.Location = new System.Drawing.Point(57, 23);
            this.CheckBox_Save_time.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_time.Name = "CheckBox_Save_time";
            this.CheckBox_Save_time.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_time.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_time.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_time.TabIndex = 126;
            this.CheckBox_Save_time.UseVisualStyleBackColor = true;
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(20, 28);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(77, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 16;
            this.rjLabel2.Text = "وحدة التحميل";
            // 
            // btnAdd_Fast
            // 
            this.btnAdd_Fast.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAdd_Fast.BackColor = System.Drawing.Color.WhiteSmoke;
            this.btnAdd_Fast.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd_Fast.BorderRadius = 10;
            this.btnAdd_Fast.BorderSize = 2;
            this.btnAdd_Fast.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnAdd_Fast.FlatAppearance.BorderSize = 0;
            this.btnAdd_Fast.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(230)))), ((int)(((byte)(230)))));
            this.btnAdd_Fast.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(215)))), ((int)(((byte)(215)))));
            this.btnAdd_Fast.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAdd_Fast.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnAdd_Fast.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd_Fast.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAdd_Fast.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd_Fast.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAdd_Fast.IconSize = 25;
            this.btnAdd_Fast.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAdd_Fast.Location = new System.Drawing.Point(20, 17);
            this.btnAdd_Fast.Name = "btnAdd_Fast";
            this.btnAdd_Fast.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnAdd_Fast.Size = new System.Drawing.Size(92, 40);
            this.btnAdd_Fast.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btnAdd_Fast.TabIndex = 28;
            this.btnAdd_Fast.Text = "اضافة سريعة ";
            this.btnAdd_Fast.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAdd_Fast.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAdd_Fast.UseVisualStyleBackColor = false;
            this.btnAdd_Fast.Visible = false;
            // 
            // btn_add_One
            // 
            this.btn_add_One.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_add_One.BackColor = System.Drawing.Color.WhiteSmoke;
            this.btn_add_One.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_add_One.BorderRadius = 10;
            this.btn_add_One.BorderSize = 2;
            this.btn_add_One.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_add_One.FlatAppearance.BorderSize = 0;
            this.btn_add_One.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(230)))), ((int)(((byte)(230)))), ((int)(((byte)(230)))));
            this.btn_add_One.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(215)))), ((int)(((byte)(215)))), ((int)(((byte)(215)))));
            this.btn_add_One.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_add_One.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_add_One.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_add_One.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btn_add_One.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_add_One.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_add_One.IconSize = 25;
            this.btn_add_One.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_add_One.Location = new System.Drawing.Point(34, 17);
            this.btn_add_One.Name = "btn_add_One";
            this.btn_add_One.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_add_One.Size = new System.Drawing.Size(60, 40);
            this.btn_add_One.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_add_One.TabIndex = 28;
            this.btn_add_One.Text = "كرت واحد";
            this.btn_add_One.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_add_One.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_add_One.UseVisualStyleBackColor = false;
            this.btn_add_One.Visible = false;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.lbl_Title2);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2.Location = new System.Drawing.Point(0, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(340, 27);
            this.panel2.TabIndex = 2;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // pnl_Right3
            // 
            this.pnl_Right3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_Right3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_Right3.BorderRadius = 0;
            this.pnl_Right3.Controls.Add(this.tableLayoutPanel1);
            this.pnl_Right3.Customizable = false;
            this.pnl_Right3.Location = new System.Drawing.Point(350, 474);
            this.pnl_Right3.Name = "pnl_Right3";
            this.pnl_Right3.Size = new System.Drawing.Size(635, 82);
            this.pnl_Right3.TabIndex = 35;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 4;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 26.63043F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 73.36957F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 389F));
            this.tableLayoutPanel1.Controls.Add(this.radio_one_user, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.btnAdd, 3, 1);
            this.tableLayoutPanel1.Controls.Add(this.radio_fast_print, 2, 0);
            this.tableLayoutPanel1.Controls.Add(this.lbl_radio_fast_print, 3, 0);
            this.tableLayoutPanel1.Controls.Add(this.lbl_radio_Print, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.lbl_radio_one_user, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.radio_Print, 0, 0);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.Padding = new System.Windows.Forms.Padding(0, 8, 0, 0);
            this.tableLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tableLayoutPanel1.RowCount = 2;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 31.57895F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 68.42105F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(635, 84);
            this.tableLayoutPanel1.TabIndex = 34;
            // 
            // rjPanel4
            // 
            this.rjPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel4.BorderRadius = 0;
            this.rjPanel4.Customizable = false;
            this.rjPanel4.Location = new System.Drawing.Point(7, 561);
            this.rjPanel4.Name = "rjPanel4";
            this.rjPanel4.Size = new System.Drawing.Size(337, 20);
            this.rjPanel4.TabIndex = 121;
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 0;
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(350, 561);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(635, 28);
            this.rjPanel1.TabIndex = 122;
            // 
            // FormAddHotspotCards
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "FormAddHotspotCards";
            this.ClientSize = new System.Drawing.Size(1000, 634);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "FormAddHotspotCards";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "FormAddHotspotCards";
            this.Load += new System.EventHandler(this.FormAddHotspotCards_Load);
            this.SizeChanged += new System.EventHandler(this.FormAddHotspotCards_SizeChanged);
            this.pnlClientArea.ResumeLayout(false);
            this.pnl_Right.ResumeLayout(false);
            this.pnl_Right.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.pnl_Menul_profile.ResumeLayout(false);
            this.pnl_Menul_profile.PerformLayout();
            this.pnl_usermanger.ResumeLayout(false);
            this.pnl_usermanger.PerformLayout();
            this.pnl_profile_HS_local.ResumeLayout(false);
            this.pnl_profile_HS_local.PerformLayout();
            this.pnl_left.ResumeLayout(false);
            this.pnl_left.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.pnl_Right2.ResumeLayout(false);
            this.tableLayoutPanel2.ResumeLayout(false);
            this.tableLayoutPanel2.PerformLayout();
            this.flowLayoutPanel2.ResumeLayout(false);
            this.flowLayoutPanel2.PerformLayout();
            this.flowLayoutPanel7.ResumeLayout(false);
            this.flowLayoutPanel7.PerformLayout();
            this.flowLayoutPanel4.ResumeLayout(false);
            this.flowLayoutPanel4.PerformLayout();
            this.flowLayoutPanel5.ResumeLayout(false);
            this.flowLayoutPanel5.PerformLayout();
            this.pnl_left2.ResumeLayout(false);
            this.panel_PreviewTempateCards.ResumeLayout(false);
            this.groupBox2.ResumeLayout(false);
            this.groupBox2.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.pnl_Right3.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel pnl_Right;
        public RJControls.RJTextBox txtNumberCard;
        public RJControls.RJComboBox Cbox_Profile_Select_Typ;
        private RJControls.RJLabel lbl_Cbox_Profile_Select_Typ;
        private RJControls.RJButton btn_Add_SellingPoint;
        private RJControls.RJLabel lbl_startCard;
        private RJControls.RJLabel lbl_Pass_NumberORcharcter;
        private RJControls.RJButton btn_Add_template;
        public RJControls.RJTextBox txt_EndCard;
        private RJControls.RJLabel lbl_endCards;
        public RJControls.RJTextBox txt_longUsers;
        public RJControls.RJComboBox cbox_Pass_NumberORcharcter;
        private RJControls.RJLabel lbl_User_NumberORcharcter;
        private RJControls.RJLabel lbl_UserPassword_Pattern;
        public RJControls.RJTextBox txt_StartCard;
        public RJControls.RJTextBox txt_longPassword;
        private RJControls.RJLabel lbl_profile;
        public RJControls.RJComboBox CBox_TemplateCards;
        public RJControls.RJComboBox cbox_User_NumberORcharcter;
        private RJControls.RJLabel lbl_SellingPoint;
        public RJControls.RJComboBox CBox_SellingPoint;
        public RJControls.RJComboBox cbox_UserPassword_Pattern;
        private RJControls.RJLabel lbl_TemplateCards;
        public RJControls.RJComboBox CBox_profile_Source_hotspot;
        private RJControls.RJLabel lbl_By_Number_Cards;
        public RJControls.RJComboBox CBox_Profile_UserMan;
        private RJControls.RJLabel lbl_Profile_UserMan;
        private RJControls.RJPanel pnl_left;
        public RJControls.RJDataGridView dgv;
        private RJControls.RJLabel lbl_Title1;
        private RJControls.RJPanel pnl_Right2;
        public RJControls.RJTextBox txt_last_batchNumber;
        private RJControls.RJLabel lbl_RegisterAs_LastBatch;
        public RJControls.RJCheckBox checkBox_RegisterAs_LastBatch;
        private RJControls.RJLabel lbl_note;
        public RJControls.RJTextBox txt_note;
        private RJControls.RJButton btn_OpenFolderDefault;
        private RJControls.RJButton btn_OpenLastFile;
        private RJControls.RJLabel lbl_FirstUse;
        private RJControls.RJLabel lbl_OpenAfterPrint;
        private RJControls.RJLabel lbl_Save_PDF;
        private RJControls.RJLabel lbl_excel;
        public RJControls.RJCheckBox checkBoxOpenAfterPrint;
        public RJControls.RJCheckBox checkBoxSaveTo_PDF;
        private RJControls.RJLabel lbl_RegisterAsBatch;
        private RJControls.RJLabel lbl_script_File;
        private RJControls.RJLabel lbl_With_Archive_uniqe;
        public RJControls.RJCheckBox checkBoxFirstUse;
        public RJControls.RJCheckBox checkBoxSaveTo_excel;
        public RJControls.RJCheckBox checkBox_RegisterAsBatch;
        public RJControls.RJCheckBox checkBox_note;
        public RJControls.RJCheckBox checkBox_With_Archive_uniqe;
        public RJControls.RJCheckBox checkBoxSaveTo_script_File;
        private RJControls.RJPanel pnl_left2;
        private RJControls.RJLabel lbl_Title2;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJLabel lbl_houre;
        public RJControls.RJTextBox txt_validatiy;
        private RJControls.RJLabel lbl_validatiy;
        public RJControls.RJTextBox txt_houre;
        public RJControls.RJTextBox txt_download;
        public RJControls.RJTextBox txt_price;
        private RJControls.RJLabel lbl_price;
        private RJControls.RJLabel lbl_download;
        private RJControls.RJButton btn_show_profile;
        public RJControls.RJComboBox CBox_Profile_HotspotLocal;
        private RJControls.RJLabel lbl_Profile_HotspotLocal;
        private RJControls.RJButton btn_show_profile_hotspot;
        private RJControls.RJLabel lbl_profile2;
        private RJControls.RJLabel lbl_Profile_HotspotLocal2;
        public RJControls.RJPanel pnl_usermanger;
        public RJControls.RJPanel pnl_Menul_profile;
        public RJControls.RJPanel pnl_profile_HS_local;
        public RJControls.RJComboBox CBox_SizeDownload;
        public RJControls.RJComboBox CBox_Server_hotspot;
        private RJControls.RJLabel lbl_Server_hotspot;
        private RJControls.RJButton btn_Script_Smart;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel4;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel5;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel3;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel6;
        public RJControls.RJButton rjButton1;
        private RJControls.RJLabel lbl_radio_Print;
        private RJControls.RJRadioButton radio_one_user;
        private RJControls.RJLabel lbl_radio_fast_print;
        private RJControls.RJRadioButton radio_Print;
        private RJControls.RJRadioButton radio_fast_print;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel lbl_radio_one_user;
        private RJControls.RJPanel pnl_Right3;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel7;
        private System.Windows.Forms.Panel panel1;
        private RJControls.RJPanel rjPanel4;
        public RJControls.RJCheckBox checkBox_Add_Smart_Validatiy;
        private RJControls.RJPanel rjPanel1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel_PreviewTempateCards;
        private System.Windows.Forms.GroupBox groupBox2;
        private RJControls.RJLabel lbl_Add_Smart_Validatiy;
        public RJControls.RJCheckBox checkBoxSaveTo_text_File;
        public RJControls.RJCheckBox CheckBox_byDayOrHour;
        public RJControls.RJCheckBox CheckBox_Save_session;
        public RJControls.RJCheckBox CheckBox_Save_download;
        public RJControls.RJCheckBox CheckBox_Save_time;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJButton btnAdd_Fast;
        private RJControls.RJButton btn_add_One;
        public RJControls.RJButton btnAdd;
        public System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
    }
}