using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار آمن للـ Designer
    /// </summary>
    public partial class DesignerSafeTestForm : Form
    {
        private RJTabControl rjTabControl1;

        public DesignerSafeTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.rjTabControl1 = new RJTabControl();
            this.SuspendLayout();
            
            // 
            // rjTabControl1
            // 
            this.rjTabControl1.ContentBorderColor = Color.FromArgb(0, 122, 204);
            this.rjTabControl1.ContentBorderRadius = 8;
            this.rjTabControl1.ContentBorderSize = 2;
            this.rjTabControl1.Dock = DockStyle.Fill;
            this.rjTabControl1.Location = new Point(0, 0);
            this.rjTabControl1.Name = "rjTabControl1";
            this.rjTabControl1.SelectedIndex = 0;
            this.rjTabControl1.Size = new Size(800, 600);
            this.rjTabControl1.TabHeight = 45;
            this.rjTabControl1.TabIndex = 0;
            this.rjTabControl1.TabPadding = 25;
            this.rjTabControl1.TabSpacing = 5;
            
            // 
            // DesignerSafeTestForm
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.rjTabControl1);
            this.Name = "DesignerSafeTestForm";
            this.Text = "🛡️ اختبار Designer الآمن - RJTabControl";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.ResumeLayout(false);

            // إضافة تابات تجريبية بعد التهيئة
            AddTestTabs();
        }

        private void AddTestTabs()
        {
            try
            {
                // تاب الترحيب
                var welcomeTab = new RJTabPage("مرحباً", IconChar.Home);
                welcomeTab.BackColor = Color.FromArgb(0, 122, 204);
                welcomeTab.ForeColor = Color.White;
                welcomeTab.IconSize = 20;

                var welcomeLabel = new Label
                {
                    Text = "🛡️ اختبار Designer الآمن نجح!\n\n" +
                           "✅ تم إنشاء RJTabControl بدون أخطاء\n" +
                           "✅ جميع الخصائص محمية من NullReference\n" +
                           "✅ Constructor آمن للـ Designer\n" +
                           "✅ يمكن إضافة التابات بأمان\n\n" +
                           "🎉 RJTabControl جاهز للاستخدام في Designer!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(0, 122, 204),
                    Padding = new Padding(20)
                };
                welcomeTab.AddControl(welcomeLabel);
                this.rjTabControl1.AddTab(welcomeTab);

                // تاب الخصائص
                var propertiesTab = new RJTabPage("الخصائص", IconChar.Cogs);
                propertiesTab.BackColor = Color.FromArgb(76, 175, 80);
                propertiesTab.ForeColor = Color.White;
                propertiesTab.IconSize = 18;

                var propertiesPanel = new RJPanel
                {
                    Dock = DockStyle.Fill,
                    BorderSize = 2,
                    BorderColor = Color.FromArgb(76, 175, 80),
                    BorderRadius = 10,
                    Padding = new Padding(20)
                };

                var propertiesLabel = new Label
                {
                    Text = "🔧 الخصائص المحمية:\n\n" +
                           "✅ TabsPanelBorderColor - محمي من null\n" +
                           "✅ TabsPanelBorderSize - محمي من null\n" +
                           "✅ TabsPanelBorderRadius - محمي من null\n" +
                           "✅ TabsPanelBackColor - محمي من null\n" +
                           "✅ ContentBorderColor - محمي من null\n" +
                           "✅ ContentBorderSize - محمي من null\n" +
                           "✅ ContentBorderRadius - محمي من null\n" +
                           "✅ ContentBackColor - محمي من null\n" +
                           "✅ TabCount - محمي من null\n\n" +
                           "🛡️ جميع الخصائص آمنة للـ Designer!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleLeft,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    ForeColor = Color.FromArgb(76, 175, 80)
                };
                propertiesPanel.Controls.Add(propertiesLabel);
                propertiesTab.AddControl(propertiesPanel);
                this.rjTabControl1.AddTab(propertiesTab);

                // تاب الطرق
                var methodsTab = new RJTabPage("الطرق", IconChar.Code);
                methodsTab.BackColor = Color.FromArgb(156, 39, 176);
                methodsTab.ForeColor = Color.White;
                methodsTab.IconSize = 20;

                var methodsTextBox = new RJTextBox
                {
                    Dock = DockStyle.Fill,
                    MultiLine = true,
                    ReadOnly = true,
                    Text = "🔧 الطرق المحمية:\n\n" +
                           "✅ AddTabInternal - فحص null للمعاملات\n" +
                           "✅ ArrangeTabs - فحص null للقوائم\n" +
                           "✅ CalculateTabWidth - فحص null للتاب\n" +
                           "✅ Constructor - ترتيب صحيح للتهيئة\n\n" +
                           "🛡️ الحماية المضافة:\n" +
                           "• فحص tabs != null\n" +
                           "• فحص tabsPanel != null\n" +
                           "• فحص contentPanel != null\n" +
                           "• فحص tabStyle != null\n" +
                           "• قيم افتراضية آمنة\n\n" +
                           "🎯 النتيجة:\n" +
                           "لا توجد NullReferenceException في Designer!",
                    Style = TextBoxStyle.MatteBorder,
                    BorderSize = 2,
                    BorderColor = Color.FromArgb(156, 39, 176),
                    BorderRadius = 8,
                    Font = new Font("Segoe UI", 9),
                    TextAlign = HorizontalAlignment.Left
                };
                methodsTab.AddControl(methodsTextBox);
                this.rjTabControl1.AddTab(methodsTab);

                // تاب الاختبار
                var testTab = new RJTabPage("اختبار", IconChar.Flask);
                testTab.BackColor = Color.FromArgb(255, 152, 0);
                testTab.ForeColor = Color.White;
                testTab.IconSize = 22;

                var testPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

                var testButton = new RJButton
                {
                    Text = "اختبار إضافة تاب",
                    IconChar = IconChar.Plus,
                    Location = new Point(20, 20),
                    Size = new Size(200, 50),
                    BackColor = Color.FromArgb(244, 67, 54),
                    ForeColor = Color.White,
                    BorderRadius = 10,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold)
                };
                testButton.Click += TestButton_Click;

                var statusLabel = new Label
                {
                    Text = "🧪 اضغط الزر أعلاه لاختبار إضافة تاب جديد\n\n" +
                           "سيتم إضافة تاب جديد بأمان بدون أخطاء!",
                    Location = new Point(20, 90),
                    Size = new Size(400, 60),
                    Font = new Font("Segoe UI", 11),
                    ForeColor = Color.FromArgb(70, 70, 70),
                    TextAlign = ContentAlignment.TopLeft
                };

                testPanel.Controls.Add(testButton);
                testPanel.Controls.Add(statusLabel);
                testTab.AddControl(testPanel);
                this.rjTabControl1.AddTab(testTab);

                // تفعيل التاب الأول
                this.rjTabControl1.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة التابات: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TestButton_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = new RJTabPage($"تاب جديد {DateTime.Now:HH:mm:ss}", IconChar.Star);
                newTab.BackColor = Color.FromArgb(63, 81, 181);
                newTab.ForeColor = Color.White;

                var label = new Label
                {
                    Text = $"🌟 تاب جديد تم إنشاؤه في {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ تم إضافة التاب بنجاح\n" +
                           "✅ لا توجد أخطاء NullReference\n" +
                           "✅ جميع الطرق تعمل بأمان",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.FromArgb(63, 81, 181)
                };
                newTab.AddControl(label);

                this.rjTabControl1.AddTab(newTab);
                this.rjTabControl1.SelectedTab = newTab;

                MessageBox.Show("✅ تم إضافة التاب الجديد بنجاح!", "نجح الاختبار", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب: {ex.Message}", "خطأ", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            try
            {
                var form = new DesignerSafeTestForm();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل النموذج: {ex.Message}\n\n" +
                               "تأكد من أن جميع الحماية مضافة بشكل صحيح.", 
                               "خطأ Designer", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
