﻿using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;

namespace SmartCreator.Forms.Accounting.Partners
{
    public partial class Form_CashPayment_Add_Edit : RJChildForm
    {
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes = false;
        Entities.Accounts.Partner partner;
        public Form_CashPayment_Add_Edit()
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            lblTitle.Text = "اضافة خزينة / دفع جديد";
            btnSave.Text = "اضافة";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Plus;

            btnSave.BackColor = RJColors.Confirm;

            //txt_line.Text = "بيانات التواصل";
            txt_code.Text = (Smart_DA.Get_BatchCards_My_Sequence("Partner") + 1).ToString();

            Set_Font();
        }
        public Form_CashPayment_Add_Edit(Entities.Accounts.Partner _partner)
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();

            lblTitle.Text = "تعديل خزينة";
            btnSave.Text = "تعديل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Edit;
            btnSave.BackColor = RJColors.DefaultFormBorderColor;

            partner = _partner;

            txt_code.Text = partner.Code;
            txt_code.Enabled = false;
            txt_Description.Text = partner.Description;
            txt_name.Text = partner.Name;
            check_Active.Checked = Convert.ToBoolean(partner.Active);

            //Set_AccountType();
            Set_Font();

        }


        private void Set_Font()
        {


            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;

            //System.Drawing.Font DGV_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, false);
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel5.Font = rjLabel1.Font = rjLabel5.Font = rjLabel7.Font =
           txt_code.Font = txt_name.Font =
           lbl_font;
            this.Focus();

            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            int type = 1;

            if (add)
            {
                long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Partner where Code='{txt_code.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");

                if (foundsp > 0)
                {
                    RJMessageBox.Show("رقم التسلسل موجود مسبقا");
                    return;
                }
                if (txt_code.Text == "")
                {
                    RJMessageBox.Show("رقم  التسلسل مطلوب");
                    return;
                }
                if (txt_name.Text == "")
                {
                    RJMessageBox.Show("الاسم  مطلوب");
                    return;
                }

                Entities.Accounts.Partner sp = new Entities.Accounts.Partner();
                sp.Code = txt_code.Text;
                sp.Name = txt_name.Text;
                sp.Description = txt_Description.Text;
                sp.Partner_type = type;
                sp.Active = Convert.ToInt16(check_Active.Checked);
                sp.Rb = Global_Variable.Mk_resources.RB_SN;



                lock (Smart_DataAccess.Lock_object)
                {
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Code", "Name", "Description", "Partner_type", "Rb", "Active" };

                    Fields.AddRange(aFields);


                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Partner");
                    if (new_sp > 0)
                    {
                        //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                        RJMessageBox.Show("تمت عمليه الاضافة");
                        succes = true;
                        int x = Smart_DA.Update_MySequence("Partner", Convert.ToInt32(txt_code.Text));
                        this.Close();
                        return;
                    }
                    RJMessageBox.Show("خطاء");
                    return;
                }
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = { "Name", "Description", "Active" };
                Fields.AddRange(aFields);
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var dataa = new Entities.Accounts.Partner();
                        dataa.Id = partner.Id;
                        dataa.Name = txt_name.Text;
                        dataa.Partner_type = type;

                        dataa.Active = Convert.ToInt16(check_Active.Checked);
                        dataa.Description = txt_Description.Text;

                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Partner>("Partner", Fields, $" where Id=@Id and   Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(dataa, sqlquery);
                        if (r > 0)
                        {
                            succes = true;
                            this.Close();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch { }
            }
            //succes = true;
            //this.Close();

        }
    }
}

