﻿using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator
{
    public partial class Form_Custom_Login : RJForms.RJChildForm
    {
        bool Toggle_load_by_DownloadDB=false;
        public Form_Custom_Login(bool toggle_load_by_DownloadDB=false)
        {
            this.Toggle_load_by_DownloadDB = toggle_load_by_DownloadDB;
            InitializeComponent(); //487, 355

            //if (toggle_load_by_DownloadDB)
            //    this.Size = new Size(487, 355);
            //else
            //    this.Size = new Size(487, 298);
     

            if (toggle_load_by_DownloadDB)
                this.rjLabel1.Visible = true;

            this.Resizable = false;

            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in tableLayoutPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJCheckBox))
                    {
                        RJCheckBox lbl = (RJCheckBox)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
            rjLabel1.Font= Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            if(UIAppearance.Theme==UITheme.Light) 
                rjLabel1.ForeColor=Color.Red;

            utils.Control_textSize(this);
            int nw = CheckBox_DisableLoad_HSSession.Size.Width;
            int hw = CheckBox_DisableLoad_HSSession.Size.Height;

            int w= (int)(nw + (nw - (nw * 96f / Global_Variable.Graphics_dpi)));
            int h= (int)(hw + (hw - (hw * 96f / Global_Variable.Graphics_dpi)));
            CheckBox_DisableLoad_HSSession.Size = new Size(w,h);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
