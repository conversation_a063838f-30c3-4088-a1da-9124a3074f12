﻿using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.Forms.Hotspot
{
    public partial class FormReportHotspot : RJChildForm
    {
        int PW = 230;
        Image bgImage;
        //OrmLiteConnectionFactory dbFactory = null;
        Smart_DataAccess Smart_DA;
        Sql_DataAccess Local_DA;
        public FormReportHotspot()
        {
            InitializeComponent();
            //dbFactory = Sql_DataAccess.Get_dbFactory();
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            sideMenu();
            bgImage = Image.FromFile(@"D:\MyProjects\SmartCreator\SmartCreatorDesktop\SmartCreator\bin\Debug\tempCards\cardsBack\1.jpg");
            this.Text = "تقارير الهوتسبوت";
            //if (UIAppearance.Theme == UITheme.Dark)
            //    pnl_side_sn.Customizable = false;
            if (UIAppearance.Language_ar)
            {
                rjPanel_btns.RightToLeftLayout = false;
                rjPanel_btns.RightToLeft = RightToLeft.No;
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
                //tableLayoutPanel_Contains.RightToLeft = RightToLeft.No;
                //tableLayoutPanel3.RightToLeft = RightToLeft.No;
            }
            else
            {
                this.Text = "Reports UserManager";
                this.dgv.RightToLeft = RightToLeft.No;
                //this.dmAll_Cards.RightToLeft = RightToLeft.No;


                rjPanel_btns.RightToLeftLayout = true;
                rjPanel_btns.RightToLeft = RightToLeft.Yes;
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
                //tableLayoutPanel_Contains.RightToLeft = RightToLeft.Yes;
                //tableLayoutPanel3.RightToLeft = RightToLeft.Yes;
            }

            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Bold);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 40;
            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");


            Control_Loop(pnlClientArea);

        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        void sideMenu()
        {
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                PW = 0;
                panel1.Location = new Point(10, rjPanel_back_side.Location.Y);
                panel1.Width = pnlClientArea.Width-30;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width-pnlClientArea.Padding.Left-pnlClientArea.Padding.Right-10;
                //rjPanel_back_side.Location = new Point(pnlClientArea.Width - rjPanel_back_side.Width - 10, panel1.Location.Y);

            }
            else
            {
                rjPanel_back_side.Width = 230;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 30;
                panel1.Location = new Point(rjPanel_back_side.Width + 20, rjPanel_back_side.Location.Y);

                //panel1.Width =  705;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);

            }
            rjPanel_back_side.Refresh();
            panel1.Refresh();
            rjPanel2.Refresh();
            rjPanel_btns.Refresh();

        }

        private void FormReportHotspot_SizeChanged(object sender, EventArgs e)
        {
            //sideMenu();
        }

        private void btn_more_Click(object sender, EventArgs e)
        {
            sideMenu();
        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
            rjPanel_back_side.Refresh();
            panel1.Refresh();
            rjPanel2.Refresh();
            rjPanel_btns.Refresh();
        }

        private void dgv_Paint(object sender, PaintEventArgs e)
        {
            //e.Graphics.DrawImageUnscaled(bgImage, new Point(0, 0));
        }


        private void get_report()
        {
            dgv.DataSource = null;
            double sum = 0; double sum_download = 0; double sum_uptime = 0; double count_cards = 0;
            txt_avg.Text = "0"; txt_count_Cards.Text = "0"; txt_download.Text = "0"; txt_sum_Sales.Text = "0"; txt_uptime.Text = "00:00:00";

            string Query_firstUse = condition_detail_firstUse();
            if (ToggleButton_Detail.Checked)
            {

                string Qury = "SELECT u.*  FROM HSUser u  " + Query_firstUse;

                 
                    var res = Local_DA.Load<HSUser>(Qury);
                    //var res = db.SqlList<HSUser>(Qury);
                    dgv.DataSource = res;
                
                loadDgvState();
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    sum += Convert.ToDouble(row.Cells["TotalPrice"].Value);
                    row.Cells["Str_TotalPrice"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["TotalPrice"].Value));
                }
                txt_sum_Sales.Text = String.Format("{0:n0}", sum);
                txt_count_Cards.Text = dgv.Rows.Count.ToString();
                txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));
                dgv.Columns["TotalPrice"].Visible = false;

            }
            else
            {
                string Query_conditon = condition_Session_By_Days_for_firstUse();
                string fitler = "'%Y-%m-%d'";
                if (jToggleButton_Year.Checked)
                    fitler = "'%Y-%m'";

                List<class_Report_monthly_or_Dayliy> um = new List<class_Report_monthly_or_Dayliy>();
                //List<class_Report_monthly_or_Dayliy> totalDownload = new List<class_Report_monthly_or_Dayliy>();

                //using (var db = dbFactory.Open())
                //{
                    string Qury = "SELECT " +
                      "strftime(" + fitler + ", u.FirsLogin) Date," +
                      "sum(u.TotalPrice) as TotalPrice ," +
                      "sum(u.DownloadUsed + u.UploadUsed) as Download ," +
                      "sum(u.UptimeUsed) as Uptime , " +
                      "count(u.id) as count " +
                      "FROM HSUser u  " +
                      Query_firstUse + " " +
                      " group by strftime(" + fitler + ", u.FirsLogin);";
                    um = Local_DA.Load<class_Report_monthly_or_Dayliy>(Qury);

                    //string Qury_str = "SELECT " +
                    //      "strftime(" + fitler + ", u.FirsLogin) date," +
                    //      "sum(BytesDownload + BytesUpload) as Download ," +
                    //      "sum(UpTime) as Uptime " +
                    //      "FROM UmSession s " +
                    //      " INNER JOIN HSUser u ON u.Id  = s.HSUser  " +
                    //      //Query_conditon + " " +
                    //      " group by strftime(" + fitler + ", u.FirsLogin);";

                    //totalDownload = db.SqlList<class_Report_monthly_or_Dayliy>(Qury_str);
                //}

                //var all = (from u in um
                //           join s in totalDownload on u.Date equals s.Date
                //           select new class_Report_monthly_or_Dayliy
                //           {
                //               Date = u.Date,
                //               TotalPrice = u.TotalPrice,
                //               count = u.count,
                //               Uptime = s.Uptime,
                //               Download = s.Download,
                //           }).ToList();
                //dgv.DataSource = all;
                dgv.DataSource = um;

                loadDgvState();
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    //row.Cells["date"].Value = row.Cells["date"].Value;

                    sum += Convert.ToDouble(row.Cells["TotalPrice"].Value);

                    row.Cells["Str_TotalPrice"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["TotalPrice"].Value));

                    sum_download += Convert.ToDouble(row.Cells["Download"].Value);
                    sum_uptime += Convert.ToDouble(row.Cells["Uptime"].Value);
                    count_cards += Convert.ToDouble(row.Cells["count"].Value);
                }
                txt_sum_Sales.Text = String.Format("{0:n0}", sum);
                txt_count_Cards.Text = count_cards.ToString();
                double avg = sum / dgv.Rows.Count;
                txt_avg.Text = String.Format("{0:n0}", avg);
                txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));

            }



            //string Query_firstUse = condition_detail_firstUse();
            //CardsUserHotspot_Display_FromDB cls_HS = new CardsUserHotspot_Display_FromDB();

            //double sum = 0;
            //double sum_download = 0;
            //double sum_uptime = 0;
            //double count_cards = 0;
            //dgv.DataSource = null;
            //txt_avg.Text = "0";
            //txt_count_Cards.Text = "0";
            //txt_download.Text = "0";
            //txt_sum_Sales.Text = "0";
            //txt_uptime.Text = "00:00:00";
            //if (ToggleButton_Detail.Checked)
            //{
            //    List<HSUser> um = cls_HS.Get_UsersHotspot_By_FirstUse(Query_firstUse);
            //    List<CardsUserHotspot_Display_FromDB> um = cls_HS.Get_UsersHotspot_By_FirstUse(Query_firstUse);

            //    dgv.DataSource = um;
            //    loadDgvState();
            //    foreach (DataGridViewRow row in dgv.Rows)
            //    {
            //        sum += Convert.ToInt32(row.Cells["price"].Value); 
            //    }
            //    txt_sum_Sales.Text = String.Format("{0:n0}", sum);
            //    txt_count_Cards.Text = dgv.Rows.Count.ToString();
            //    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
            //    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));
            //}
            //else
            //{
            //    string Query_conditon = condition_Session_By_Days_for_firstUse();

            //    string fitler = "'%Y-%m-%d'";
            //    if (jToggleButton_Year.Checked)
            //        fitler = "'%Y-%m'";

            //    string Qury_str = "SELECT " +
            //      "strftime(" + fitler + ", firstUse, 'unixepoch') date," +
            //      "sum(u.price) as TotalPrice ," +
            //      "count(u.id) as count, " +
            //      "sum(bytesOut + bytesIn) as download ," +
            //      "sum(uptime) as uptime " +
            //      "FROM userHS u " +

            //      Query_conditon +
            //      " group by date;";

            //    List<class_Report_monthly_or_Dayliy> um = cls_HS.Get_UsersHotspot_By_FirstUse_byDays(Query_firstUse, fitler);
            //    List<class_Report_monthly_or_Dayliy> totalDownload = cls_HS.Get_UsersHotspot_By_byDays_Downloads_UptimeUsed(Qury_str, fitler);

            //    //var all = (from u in um
            //    //           join s in totalDownload on u.Date equals s.Date
            //    //           select new class_Report_monthly_or_Dayliy
            //    //           {
            //    //               Date = u.Date,
            //    //               TotalPrice = u.TotalPrice,
            //    //               count = u.count,
            //    //               Uptime = s.Uptime,
            //    //               Download = s.Download,

            //    //           }).ToList();
            //    var all = (from u in totalDownload
            //               //join s in totalDownload on u.Date equals s.Date
            //               select new class_Report_monthly_or_Dayliy
            //               {
            //                   Date = u.Date,
            //                   TotalPrice = u.TotalPrice,
            //                   count = u.count,
            //                   Uptime = u.Uptime,
            //                   Download = u.Download,

            //               }).ToList();
            //    dgv.DataSource = all;
            //    //dgv.DataSource = um;

            //    loadDgvState();
            //    foreach (DataGridViewRow row in dgv.Rows)
            //    {
            //        row.Cells["date"].Value = row.Cells["date"].Value;
            //        //row.Cells["date"].Value = DateTime.ParseExact(row.Cells["date"].Value.ToString(), fromatDate_DGV, CultureInfo.CurrentCulture);
            //        sum += Convert.ToInt32(row.Cells["TotalPrice"].Value);
            //        sum_download += Convert.ToDouble(row.Cells["Download"].Value);
            //        sum_uptime += Convert.ToDouble(row.Cells["Uptime"].Value);
            //        count_cards += Convert.ToDouble(row.Cells["count"].Value);
            //    }
            //    txt_sum_Sales.Text = String.Format("{0:n0}", sum);
            //    txt_count_Cards.Text = count_cards.ToString();
            //    double avg = sum / dgv.Rows.Count;
            //    txt_avg.Text = String.Format("{0:n0}", avg);
            //    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
            //    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));

            //}
        }
        private void loadDgvState()
        {
            //return;
            Init_dgv_to_Default();
        }
        private void Init_dgv_to_Default()
        {
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.Visible = false;
                }
                try
                {
                    foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                    { m.Checked = false; }
                }
                catch { }

                if (ToggleButton_Detail.Checked)
                {
                    try
                    {
                        dgv.Columns["UserName"].Visible = true;
                        dgv.Columns["Str_TotalPrice"].Visible = true;
                        //dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["FirsLogin"].Visible = true;
                        UserName_ToolStripMenuItem.Checked = true;
                        Price_ToolStripMenuItem.Checked = true;
                        dt_FirstUse_ToolStripMenuItem.Checked = true;

                        if (CheckBox_SN.Check)
                            dgv.Columns["Sn"].Visible = true;
                        if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_profile_Source_hotspot.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked|| CBox_Server_hotspot.Text!="")

                            dgv.Columns["ProfileName"].Visible = true;
                        if (CBox_SellingPoint.SelectedIndex != 0 || CBox_SellingPoint.SelectedIndex != -1 || CBox_SellingPoint.Text != "")

                            dgv.Columns["SpName"].Visible = true;
                        if (/*CBox_Batch.SelectedIndex != 0 || CBox_Batch.SelectedIndex != -1 ||*/ CBox_Batch.Text != "")
                            dgv.Columns["BatchCardId"].Visible = true;
                        if (CBox_profile_Source_hotspot.SelectedIndex != 0 && CBox_profile_Source_hotspot.SelectedIndex != -1 && CBox_profile_Source_hotspot.Text != "")

                            dgv.Columns["CustomerName"].Visible = true;
                        if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")

                            dgv.Columns["NasPortId"].Visible = true;
                        if (CBox_Server_hotspot.SelectedIndex != 0 && CBox_Server_hotspot.SelectedIndex != -1 && CBox_Server_hotspot.Text != "")

                            dgv.Columns["Server"].Visible = true;

                        dgv.Columns["Sn"].DisplayIndex = 0;
                        dgv.Columns["UserName"].DisplayIndex = 1;
                        dgv.Columns["TotalPrice"].DisplayIndex = 2;
                        dgv.Columns["FirsLogin"].DisplayIndex = 3;
                        dgv.Columns["ProfileName"].DisplayIndex = 4;
                        //dgv.Columns["Str_Status"].Visible = true;
                        //dgv.Columns["Str_UptimeUsed"].Visible = true;
                        //dgv.Columns["Str_Up_Down"].Visible = true;
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                else
                {
                    try
                    {
                        dgv.Columns["Str_TotalPrice"].Visible = true;
                        //dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["date"].Visible = true;
                        dgv.Columns["Str_UptimeUsed"].Visible = true;
                        dgv.Columns["Str_Up_Down"].Visible = true;
                        dgv.Columns["count"].Visible = true;

                        DateToolStripMenuItem.Checked = true;
                        Price_ToolStripMenuItem.Checked = true;
                        Str_UptimeUsed_ToolStripMenuItem.Checked = true;
                        Str_Up_Down_ToolStripMenuItem.Checked = true;
                        count_ToolStripMenuItem.Checked = true;

                        dgv.Columns["date"].DisplayIndex = 0;
                        dgv.Columns["TotalPrice"].DisplayIndex = 1;
                        //dgv.Columns["MoneyTotal"].DisplayIndex = 2;
                        dgv.Columns["Str_UptimeUsed"].DisplayIndex = 3;
                        dgv.Columns["Str_Up_Down"].DisplayIndex = 4;
                        dgv.Columns["count"].DisplayIndex = 5;
                    }
                    catch { }
                }
            }
            catch { }
        }

        private string condition_detail_firstUse()
        {
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            //string str_to_Date = (rjDateTime_From.Value.AddDays(1)).ToString("yyyy-MM-dd hh:mm:ss", CultureInfo.InvariantCulture);
            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);


            //string conditon_date = "";

            //string str_from_Date = (rjDateTime_From.Value).ToString("MM-dd-yyyy", CultureInfo.InvariantCulture);
            //DateTime dt_to = Convert.ToDateTime(str_from_Date + " " + "23:59:59").ToUniversalTime();
            //Int32 From_DT = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(rjDateTime_From.Value).ToUniversalTime());
            //Int32 To_DT = utils.DateTimeToUnixTimeStamp(dt_to);

            //if (CheckBox_To_Date.Checked)
            //    To_DT = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(rjDateTime_To.Value).ToUniversalTime());
            //if (ToggleButton_Monthly.Checked)
            //{
            //    DateTime firstDayOfMonth;
            //    DateTime lastDayOfMonth;
            //    utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    From_DT = utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}

            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";
            //conditon_date = " WHERE u.firstUse >=" + From_DT + " AND u.firstUse<=" + To_DT + "  ";
            string profile = " ";
            string sp = "";
            string nas_port = "";
            string server = "";
            string profileHotspot = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_profile_Source_hotspot.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Server_hotspot.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpId=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Server_hotspot.SelectedIndex != 0 && CBox_Server_hotspot.SelectedIndex != -1 && CBox_Server_hotspot.Text != "")
                        server = " AND u.Server='" + CBox_Server_hotspot.Text.ToString() + "'  ";

                    if (CBox_profile_Source_hotspot.SelectedIndex != 0 && CBox_profile_Source_hotspot.SelectedIndex != -1 && CBox_profile_Source_hotspot.Text != "")
                        profileHotspot = " AND u.ProfileHotspot='" + CBox_profile_Source_hotspot.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (check_Filed_Intiger(txt_SN_Start.Text) && check_Filed_Intiger(txt_SN_End.Text))
                                SN = "AND (u.SN BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + server + profileHotspot + batch + SN;

            return conditon;
        }
        private string condition_Session_By_Days_for_firstUse()
        {

            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if (ToggleButton_Monthly.Checked)
            {
                //DateTime firstDayOfMonth;
                //DateTime lastDayOfMonth;
                //utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                //From_DT=utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
                //To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            }
            if (jToggleButton_Year.Checked)
            {
                //From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
                //To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            }
            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";




            //if (ToggleButton_Monthly.Checked)
            //{
            //    DateTime firstDayOfMonth;
            //    DateTime lastDayOfMonth;
            //    utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    From_DT = utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}
            //conditon_date = " WHERE u.firstUse >=" + From_DT + " AND u.firstUse<=" + To_DT + "  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_profile_Source_hotspot.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Server_hotspot.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpId=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Server_hotspot.SelectedIndex != 0 && CBox_Server_hotspot.SelectedIndex != -1 && CBox_Server_hotspot.Text != "")
                        radius = " AND u.Server='" + CBox_Server_hotspot.Text.ToString() + "'  ";

                    if (CBox_profile_Source_hotspot.SelectedIndex != 0 && CBox_profile_Source_hotspot.SelectedIndex != -1 && CBox_profile_Source_hotspot.Text != "")
                        customer = " AND u.ProfileHotspot='" + CBox_profile_Source_hotspot.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (check_Filed_Intiger(txt_SN_Start.Text) && check_Filed_Intiger(txt_SN_End.Text))
                                SN = "AND (u.SN BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = "AND (u.SN <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }

        private bool check_Filed_Intiger(string ctrl)
        {
            int numberChik;
            if (!(int.TryParse(ctrl, out numberChik)))
            {
                RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                return false;
            }
            return true;
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (ToggleButton_Detail.Checked)
            {
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                    ToggleButton_Detail.Checked = true;
            }

            lbl_avg.Visible = false;
            txt_avg.Visible = false;

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "dd-MM-yyyy  وقت  hh:mm:ss tt";
            rjDateTime_To.CustomFormat = "dd-MM-yyyy  وقت  hh:mm:ss tt";

            string today = DateTime.Now.ToString("MM-dd-yyyy");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
            //rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");


        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                pnl_size_time_count.Visible = true;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                    ToggleButton_Monthly.Checked = true;
            }
            lbl_avg.Visible = true;
            txt_avg.Visible = true;
            lbl_avg.Text = "المتوسط اليومي";

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "MM/yyyy";
            //rjDateTime_To.CustomFormat = "MM/yyyy";
            DateTime firstDayOfMonth;
            DateTime lastDayOfMonth;
            utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            string first = firstDayOfMonth.ToString("MM-dd-yyyy");
            string last = lastDayOfMonth.ToString("MM-dd-yyyy");

            rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
            rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");


        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox_To_Date.Check = true;
            if (jToggleButton_Year.Checked)
            {
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                pnl_size_time_count.Visible = true;

            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                    jToggleButton_Year.Checked = true;
            }
            lbl_avg.Visible = true;
            txt_avg.Visible = true;

            lbl_avg.Text = "المتوسط الشهري";

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "yyyy";
            //rjDateTime_To.CustomFormat = "yyyy";

            //rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
            //rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
            rjDateTime_From.Value = new DateTime(DateTime.Now.Year, 1, 1);
            rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31);
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            CBox_SellingPoint = Smart_DA.Get_ComboBox_SellingPoint();
        }

        private void FormReportHotspot_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }
    }
}
