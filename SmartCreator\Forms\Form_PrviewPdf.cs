﻿using DevComponents.DotNetBar.Metro;
using SmartCreator.Models;
using SmartCreator.RJControls;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class Form_PrviewPdf : MetroForm
    {
        public CardsTemplate card;
        public CardsTableDesg1 cardTable1;
        public string pathfile = "";
        public string type_template;

        public Form_PrviewPdf()
        {
            InitializeComponent();
            utils.Control_textSize(this);

            //Control_Loop(this); 
        }

        private void btnAddCards_Click(object sender, EventArgs e)
        {
            preview_print();
            this.Close();
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel) || C.GetType() != typeof(Form)|| C.GetType() != typeof(MetroForm))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        void preview_print()
        {
            RJMessageBox.Show(" ملاحظة : لن يتم اضافة هذه الكروت الى النظام");
            saveFileDialogAddUser.FileName = "file1.pdf";
            saveFileDialogAddUser.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialogAddUser.FileName = "test_Date_" + DateTime.Now.ToString("HHmmss") + "_" + DateTime.Now.ToString("yyyyMMdd");
            if (Properties.Settings.Default.PathFolderPrint.ToString() != "")
                saveFileDialogAddUser.InitialDirectory = Properties.Settings.Default.PathFolderPrint;
            else
                saveFileDialogAddUser.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
            if (saveFileDialogAddUser.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialogAddUser.FileName;
                Properties.Settings.Default.PathFolderPrint = Path.GetDirectoryName(saveFileDialogAddUser.FileName);
                Properties.Settings.Default.Save();
            }
            else
                return;
            Dictionary<string, NewUserToAdd> dicUsers = get_template_user();

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = "5h30m";
            string price = "100";
            string Validity = "10";
            string time = "2304000";  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            string sizeTransfer = "50000";
            string sp = "بقالة النجوم";
            string BatchNumber = "30";
            string numberPrint = "82";
            string DatePrint = "1/1/2000";
            string Note_On_Pages_text = "1/1/2000";

            if (type_template == "design")
            {
                if (card.cardsItems.Price.unit_show)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    if (card.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                        }
                    }
                    if (card.cardsItems.Validity.title_show)
                    {
                        Validity = card.cardsItems.Validity.title_text + " " + Validity;
                    }
                }
                if (card.cardsItems.Time.Enable)
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                        if (card.cardsItems.Time.title_show)
                        {
                            time = card.cardsItems.Time.title_text + " " + time;
                        }

                    }
                }
                if (card.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                        if (card.cardsItems.Size.title_show)
                        {
                            sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                        }

                    }
                }
                if (card.cardsItems.SP.Enable)
                {
                    if (sp != "")
                    {
                        if (card.cardsItems.SP.title_show)
                        {
                            sp = card.cardsItems.SP.title_text + " " + sp;
                        }
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrint != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = sp;
                    }
                }
            }
            else
            {
                if (cardTable1.cardsItems.Price.unit_show)
                {
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = price + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    if (cardTable1.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                        }
                    }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    try
                    {
                        if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        }
                    }
                    catch { }
                }

                if (cardTable1.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                    }
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = sp;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", sp);
            Cardsdata.Add("BatchNumber", BatchNumber);
            Cardsdata.Add("numberPrint", numberPrint);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (type_template == "design")
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);
            else if (type_template == "table_Desigen1")
            {
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            }


            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            try
            {
                System.Diagnostics.Process.Start(pathfile);
            }
            catch { }
        }
        public string GenerateUser(int length, Random random)
        {
            string Numbers = "0123456789";
            string characters = "abcdefghijklmnopqrstuvwxyz";
            string charactersNumber = "0123456789abcdefghijklmnopqrstuvwxyz";
            string rand = "";
            if (cbox_User_NumberORcharcter.SelectedIndex == 1)
                rand += characters;

            else if (cbox_User_NumberORcharcter.SelectedIndex == 2)
                rand += charactersNumber;
            else
                rand += Numbers;



            StringBuilder result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(rand[random.Next(rand.Length)]);
            }
            return result.ToString();
        }
        public string GeneratePassword(int length, Random random)
        {
            string Numbers = "0123456789";
            string characters = "abcdefghijklmnopqrstuvwxyz";
            string charactersNumber = "0123456789abcdefghijklmnopqrstuvwxyz";
            string rand = "";
            if (cbox_Pass_NumberORcharcter.SelectedIndex == 1)
                rand += characters;

            else if (cbox_Pass_NumberORcharcter.SelectedIndex == 2)
                rand += charactersNumber;
            else
                rand += Numbers;



            StringBuilder result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(rand[random.Next(rand.Length)]);
            }
            return result.ToString();
        }
        private Dictionary<string, NewUserToAdd> get_template_user()
        {
            Dictionary<string, NewUserToAdd> dicUser = new Dictionary<string, NewUserToAdd>();

            Random rndU = new Random();
            for (int i = 0; i < Convert.ToInt16(txtNumberCard.Text); i++)
            {
                string RandUser = GenerateUser(Convert.ToInt32(txt_longUsers.Text), rndU);
                string RandPassword = GeneratePassword(Convert.ToInt32(txt_longPassword.Text), rndU);
                string Randsn = GeneratePassword(Convert.ToInt32("6"), rndU);

                NewUserToAdd Objuser = new NewUserToAdd
                {
                    SN = 555,
                    Name = RandUser,
                    Password = Randsn
                };
                dicUser.Add(Objuser.Name, Objuser);
            }

            return dicUser;
        }
        private void Get_Cbox_SellingPoing()
        {
            //if (selectTemplateFromDrowpDown || firstLoad)
            //    return;

            DataTable Selling_points = new DataTable();
            //try
            //{
            //    CLS_DBAcess_V2 sp = new CLS_DBAcess_V2();
            //    Selling_points = sp.GetAll_Selling_points2();
            //}
            //catch { }
            //try
            //{
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    Dictionary<string, object> comb_sp = utils.Convert_DataTable_To_Dict(Selling_points, 0);

            //    //comboSource.Add(0, "");
            //    //for (int i = 0; i < comb_sp.Rows.Count; i++)
            //    //{
            //    //    comboSource.Add(Convert.ToInt32(Selling_points.Rows[i]["id"]), Selling_points.Rows[i]["Name"].ToString());
            //    //}
            //    CBox_SellingPoint.DataSource = new BindingSource(comb_sp, null);
            //    CBox_SellingPoint.DisplayMember = "Value";
            //    CBox_SellingPoint.ValueMember = "Key";
            //}
            //catch { }
        }

        private void Form_PrviewPdf_Load(object sender, EventArgs e)
        {
            cbox_Pass_NumberORcharcter.SelectedIndex = 0;
            cbox_UserPassword_Pattern.SelectedIndex = 0;
            cbox_User_NumberORcharcter.SelectedIndex = 0;
            Get_Cbox_SellingPoing();

            btnAddCards.Focus();
        }
    }
}
