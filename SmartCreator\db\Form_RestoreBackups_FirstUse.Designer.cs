﻿namespace SmartCreator.db
{
    partial class Form_RestoreBackups_FirstUse
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.gPanelMain = new DevComponents.DotNetBar.Controls.GroupPanel();
            this.picBoxWait = new System.Windows.Forms.PictureBox();
            this.lblWait = new DevComponents.DotNetBar.LabelX();
            this.btnNo = new DevComponents.DotNetBar.ButtonX();
            this.btnYes = new DevComponents.DotNetBar.ButtonX();
            this.PicIcon = new System.Windows.Forms.PictureBox();
            this.PicHeader = new System.Windows.Forms.PictureBox();
            this.gPanelMain.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.picBoxWait)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PicIcon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.PicHeader)).BeginInit();
            this.SuspendLayout();
            // 
            // gPanelMain
            // 
            this.gPanelMain.BackColor = System.Drawing.Color.White;
            this.gPanelMain.ColorSchemeStyle = DevComponents.DotNetBar.eDotNetBarStyle.Office2007;
            this.gPanelMain.Controls.Add(this.picBoxWait);
            this.gPanelMain.Controls.Add(this.lblWait);
            this.gPanelMain.DisabledBackColor = System.Drawing.Color.Empty;
            this.gPanelMain.DrawTitleBox = false;
            this.gPanelMain.Location = new System.Drawing.Point(11, 88);
            this.gPanelMain.Name = "gPanelMain";
            this.gPanelMain.Size = new System.Drawing.Size(527, 76);
            // 
            // 
            // 
            this.gPanelMain.Style.BackColor2SchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground2;
            this.gPanelMain.Style.BackColorGradientAngle = 90;
            this.gPanelMain.Style.BackColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBackground;
            this.gPanelMain.Style.BorderBottom = DevComponents.DotNetBar.eStyleBorderType.Solid;
            this.gPanelMain.Style.BorderBottomWidth = 1;
            this.gPanelMain.Style.BorderColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelBorder;
            this.gPanelMain.Style.BorderLeft = DevComponents.DotNetBar.eStyleBorderType.Solid;
            this.gPanelMain.Style.BorderLeftWidth = 1;
            this.gPanelMain.Style.BorderRight = DevComponents.DotNetBar.eStyleBorderType.Solid;
            this.gPanelMain.Style.BorderRightWidth = 1;
            this.gPanelMain.Style.BorderTop = DevComponents.DotNetBar.eStyleBorderType.Solid;
            this.gPanelMain.Style.BorderTopWidth = 1;
            this.gPanelMain.Style.CornerDiameter = 4;
            this.gPanelMain.Style.CornerType = DevComponents.DotNetBar.eCornerType.Rounded;
            this.gPanelMain.Style.TextAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Center;
            this.gPanelMain.Style.TextColorSchemePart = DevComponents.DotNetBar.eColorSchemePart.PanelText;
            this.gPanelMain.Style.TextLineAlignment = DevComponents.DotNetBar.eStyleTextAlignment.Near;
            // 
            // 
            // 
            this.gPanelMain.StyleMouseDown.CornerType = DevComponents.DotNetBar.eCornerType.Square;
            // 
            // 
            // 
            this.gPanelMain.StyleMouseOver.CornerType = DevComponents.DotNetBar.eCornerType.Square;
            this.gPanelMain.TabIndex = 96;
            // 
            // picBoxWait
            // 
            this.picBoxWait.BackColor = System.Drawing.Color.Transparent;
            this.picBoxWait.ForeColor = System.Drawing.Color.Black;
            this.picBoxWait.Image = global::SmartCreator.Properties.Resources.data_managementSmall;
            this.picBoxWait.Location = new System.Drawing.Point(478, 14);
            this.picBoxWait.Name = "picBoxWait";
            this.picBoxWait.Size = new System.Drawing.Size(42, 42);
            this.picBoxWait.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.picBoxWait.TabIndex = 91;
            this.picBoxWait.TabStop = false;
            // 
            // lblWait
            // 
            this.lblWait.BackColor = System.Drawing.Color.Transparent;
            // 
            // 
            // 
            this.lblWait.BackgroundStyle.CornerType = DevComponents.DotNetBar.eCornerType.Square;
            this.lblWait.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(178)));
            this.lblWait.ForeColor = System.Drawing.Color.Black;
            this.lblWait.Location = new System.Drawing.Point(5, 3);
            this.lblWait.Name = "lblWait";
            this.lblWait.Size = new System.Drawing.Size(442, 64);
            this.lblWait.Style = DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
            this.lblWait.TabIndex = 90;
            this.lblWait.Text = "Data entered the maximum allowed in the trial version ,\r\n Do you want to activate" +
    " Profits business management system now ?";
            this.lblWait.WordWrap = true;
            // 
            // btnNo
            // 
            this.btnNo.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton;
            this.btnNo.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground;
            this.btnNo.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnNo.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.btnNo.Image = global::SmartCreator.Properties.Resources.exit;
            this.btnNo.Location = new System.Drawing.Point(421, 171);
            this.btnNo.MinimumSize = new System.Drawing.Size(117, 27);
            this.btnNo.Name = "btnNo";
            this.btnNo.Shape = new DevComponents.DotNetBar.RoundRectangleShapeDescriptor(2);
            this.btnNo.Size = new System.Drawing.Size(117, 27);
            this.btnNo.Style = DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
            this.btnNo.TabIndex = 95;
            this.btnNo.Text = "&No";
            this.btnNo.Click += new System.EventHandler(this.btnNo_Click);
            // 
            // btnYes
            // 
            this.btnYes.AccessibleRole = System.Windows.Forms.AccessibleRole.PushButton;
            this.btnYes.ColorTable = DevComponents.DotNetBar.eButtonColor.OrangeWithBackground;
            this.btnYes.Cursor = System.Windows.Forms.Cursors.Hand;
            this.btnYes.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.btnYes.Image = global::SmartCreator.Properties.Resources.TestSend;
            this.btnYes.Location = new System.Drawing.Point(298, 171);
            this.btnYes.MinimumSize = new System.Drawing.Size(117, 27);
            this.btnYes.Name = "btnYes";
            this.btnYes.Shape = new DevComponents.DotNetBar.RoundRectangleShapeDescriptor(2);
            this.btnYes.Size = new System.Drawing.Size(117, 27);
            this.btnYes.Style = DevComponents.DotNetBar.eDotNetBarStyle.StyleManagerControlled;
            this.btnYes.TabIndex = 97;
            this.btnYes.Text = "&Yes";
            this.btnYes.Click += new System.EventHandler(this.btnYes_Click);
            // 
            // PicIcon
            // 
            this.PicIcon.BackColor = System.Drawing.Color.White;
            this.PicIcon.ForeColor = System.Drawing.Color.Black;
            this.PicIcon.Image = global::SmartCreator.Properties.Resources.warning;
            this.PicIcon.Location = new System.Drawing.Point(17, 14);
            this.PicIcon.Name = "PicIcon";
            this.PicIcon.Size = new System.Drawing.Size(58, 46);
            this.PicIcon.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.PicIcon.TabIndex = 94;
            this.PicIcon.TabStop = false;
            // 
            // PicHeader
            // 
            this.PicHeader.BackColor = System.Drawing.Color.White;
            this.PicHeader.ForeColor = System.Drawing.Color.Black;
            this.PicHeader.Image = global::SmartCreator.Properties.Resources.header;
            this.PicHeader.Location = new System.Drawing.Point(-9, 5);
            this.PicHeader.Name = "PicHeader";
            this.PicHeader.Size = new System.Drawing.Size(566, 66);
            this.PicHeader.SizeMode = System.Windows.Forms.PictureBoxSizeMode.StretchImage;
            this.PicHeader.TabIndex = 93;
            this.PicHeader.TabStop = false;
            // 
            // Form_RestoreBackups_FirstUse
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(549, 198);
            this.Controls.Add(this.btnNo);
            this.Controls.Add(this.btnYes);
            this.Controls.Add(this.gPanelMain);
            this.Controls.Add(this.PicIcon);
            this.Controls.Add(this.PicHeader);
            this.DoubleBuffered = true;
            this.Name = "Form_RestoreBackups_FirstUse";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "تهيئة قاعدة البيانات";
            this.Load += new System.EventHandler(this.Form_RestoreBackups_FirstUse_Load);
            this.gPanelMain.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.picBoxWait)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PicIcon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.PicHeader)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        internal System.Windows.Forms.PictureBox PicIcon;
        internal System.Windows.Forms.PictureBox PicHeader;
        private DevComponents.DotNetBar.ButtonX btnNo;
        private DevComponents.DotNetBar.ButtonX btnYes;
        private DevComponents.DotNetBar.Controls.GroupPanel gPanelMain;
        internal System.Windows.Forms.PictureBox picBoxWait;
        public DevComponents.DotNetBar.LabelX lblWait;
    }
}