﻿using Microsoft.Win32;
using SmartCreator.DAL;

using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace SmartCreator
{

    static class Program
    {
        static private List<PrivateFontCollection> _fontCollections;
        //static public  IConfiguration Configuration { get; private set; }

        /* Note: When executing the application from visual studio, the configuration file is saved 
                 * in the folder SmartCreator.vshost.exe. And when restarting the application the configuration
                 * file is obtained from the SmartCreator.exe folder, since after restarting the application it
                 * runs independently of visual studio, so it will not load the settings you established on the 
                 * first restart since it will take the file of SmartCreator.exe configuration. If you want to 
                 * test and apply the settings established when you are developing the application, I recommend 
                 * you close the application (or stop debugging) and rerun from visual studio or compile the project
                 * and run the application directly from the project's bin folder.*/

        //Fields
        public static Form MainForm;//Gets or sets the primary form of the application

        //Main method
        [STAThread]
        static void Main()
        {
            Settings.SettingsManager.LoadApperanceSettings();//Load current appearance settings.
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);

            // Initialize database on startup
            try
            {
                //var dbHelper = new Helpers.DatabaseHelper();
                //var dbHelper = new DAL.DatabaseInitializer();
                //dbHelper.InitializeDatabase();
                DatabaseInitializer.InitializeAllDatabases();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة قاعدة البيانات: {ex.Message}", "خطأ",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return;
            }


            try
            {
                //Application.Run(MainForm = new MainForm());//Run form
                Application.Run(MainForm = new LoginForm());//Run form
            }
            catch (Exception ex) { MessageBox.Show("Programs Main\n" + ex.Message); }
        }

        static public Font GetCustomFont(byte[] fontData, float size, FontStyle style)
        {
            PrivateFontCollection fontCol = new PrivateFontCollection();
            try
            {
                if (_fontCollections == null)
                    _fontCollections = new List<PrivateFontCollection>();

                IntPtr fontPtr = Marshal.AllocCoTaskMem(fontData.Length);
                Marshal.Copy(fontData, 0, fontPtr, fontData.Length);
                fontCol.AddMemoryFont(fontPtr, fontData.Length);
                Marshal.FreeCoTaskMem(fontPtr);     //<-- It works!
                _fontCollections.Add(fontCol);
            }
            catch (Exception ex) { MessageBox.Show("GetCustomFont\n" + ex.Message); }
            return new Font(fontCol.Families[0], size, style);
        }
        static public Font GetCustomFont(byte[] fontData, float size, FontStyle style, GraphicsUnit pint, byte byt)
        {
            //this.Font = new Font("Verdana", 10F, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));

            if (_fontCollections == null) _fontCollections = new List<PrivateFontCollection>();
            PrivateFontCollection fontCol = new PrivateFontCollection();
            IntPtr fontPtr = Marshal.AllocCoTaskMem(fontData.Length);
            Marshal.Copy(fontData, 0, fontPtr, fontData.Length);
            fontCol.AddMemoryFont(fontPtr, fontData.Length);
            Marshal.FreeCoTaskMem(fontPtr);     //<-- It works!
            _fontCollections.Add(fontCol);
            return new Font(fontCol.Families[0], size, style, pint, byt);
        }

    }

}
