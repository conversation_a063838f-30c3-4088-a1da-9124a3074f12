﻿using Microsoft.Win32;

//using Microsoft.Extensions.Configuration;
//using Microsoft.Extensions.DependencyInjection;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.Drawing.Text;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace SmartCreator
{

    static class Program
    {
        static private List<PrivateFontCollection> _fontCollections;
        //static public  IConfiguration Configuration { get; private set; }

        /* Note: When executing the application from visual studio, the configuration file is saved 
                 * in the folder SmartCreator.vshost.exe. And when restarting the application the configuration
                 * file is obtained from the SmartCreator.exe folder, since after restarting the application it
                 * runs independently of visual studio, so it will not load the settings you established on the 
                 * first restart since it will take the file of SmartCreator.exe configuration. If you want to 
                 * test and apply the settings established when you are developing the application, I recommend 
                 * you close the application (or stop debugging) and rerun from visual studio or compile the project
                 * and run the application directly from the project's bin folder.*/

        //Fields
        public static Form MainForm;//Gets or sets the primary form of the application



        //Main method
        [STAThread]
        [Obsolete]
        static void Main()
        {
            //int scale = (int)(100 * Screen.PrimaryScreen.Bounds.Width / System.Windows.SystemParameters.PrimaryScreenWidth);
            //MessageBox.Show($"Scale = {scale}");
            set_Dpi();
            if (Environment.OSVersion.Version.Major >= 6)
                SetProcessDPIAware();


            //if (Environment.OSVersion.Version.Major >= 6) SetProcessDPIAware();
            try
            {
                Application.ApplicationExit += delegate
                {
                    if (_fontCollections != null)
                    {
                        foreach (var fc in _fontCollections) if (fc != null) fc.Dispose();
                        _fontCollections = null;
                    }
                };


                Settings.SettingsManager.LoadApperanceSettings();//Load current appearance settings.

                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                //Application.Run(MainForm = new Form_CardsDesigen_Graghics());//Run form




                //Application.SetHighDpiMode(HighDpiMode.PerMonitorV2);
                //Application.Run(MainForm = new Form1());//Run form
                Application.Run(MainForm = new LoginForm());//Run form
            }
            catch (Exception ex) { MessageBox.Show("Programs Main\n" + ex.Message); }
        }

        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern bool SetProcessDPIAware();


        static  void set_Dpi()
        {
            
            return;
            try
            {
                Process.Start("DpiSmart.exe");
                //Thread.Sleep(1000);
            }
            catch { }
            int scale = Red_Fix_DPI_reg();
            if ((scale) > 100)
            {
                //string content = File.ReadAllText(FileName);
                if (scale >= 120)
                {
                    utils.ScaleFactor = .9f;
                    utils.ScaleFactor_lbl = .85f;
                    utils.ScaleFactor_combo = .99f;
                    utils.ScaleFactor_dgv = .8f;
                    utils.ScaleFactor_sideMenu = .75f;
                }
                if (scale == 150)
                {
                    //utils.ScaleFactor = .7f;
                    //utils.ScaleFactor_lbl = .80f;
                    //utils.ScaleFactor_combo = .90f;
                    //utils.ScaleFactor_dgv = .6f;
                    utils.ScaleFactor_sideMenu = .58f;
                }
                if (scale >= 175)
                {
                    //utils.ScaleFactor = .6f;
                    //utils.ScaleFactor_dgv = .5f;
                    utils.ScaleFactor_sideMenu = .48f;
                }
                //_scaleFactor = utils.ScaleFactor;
            }


        }

        static private int Red_Fix_DPI_reg()
        {
            int scale = 100;
            try
            {
                RegistryKey key = Registry.CurrentUser.OpenSubKey(@"SOFTWARE\SmartCreator");
                if (key != null)
                {
                    //MessageBox.Show("Scale from reg = " + key.GetValue("dpi"));
                    scale = int.Parse(key.GetValue("dpi").ToString());
                    //scale =Convert.ToInt32( key.GetValue("dpi"));
                    key.Close();
                }
            }
            catch { }
            return scale;
        }
        //[System.Runtime.InteropServices.DllImport("user32.dll")]
        //private static extern bool SetProcessDPIAware();



        static public Font GetCustomFont(byte[] fontData, float size, FontStyle style)
        {
            PrivateFontCollection fontCol = new PrivateFontCollection();
            try
            {
                if (_fontCollections == null)
                    _fontCollections = new List<PrivateFontCollection>();

                IntPtr fontPtr = Marshal.AllocCoTaskMem(fontData.Length);
                Marshal.Copy(fontData, 0, fontPtr, fontData.Length);
                fontCol.AddMemoryFont(fontPtr, fontData.Length);
                Marshal.FreeCoTaskMem(fontPtr);     //<-- It works!
                _fontCollections.Add(fontCol);
            }
            catch (Exception ex) { MessageBox.Show("GetCustomFont\n" + ex.Message); }
            return new Font(fontCol.Families[0], size, style);
        }
        static public Font GetCustomFont(byte[] fontData, float size, FontStyle style, GraphicsUnit pint, byte byt)
        {
            //this.Font = new Font("Verdana", 10F, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));

            if (_fontCollections == null) _fontCollections = new List<PrivateFontCollection>();
            PrivateFontCollection fontCol = new PrivateFontCollection();
            IntPtr fontPtr = Marshal.AllocCoTaskMem(fontData.Length);
            Marshal.Copy(fontData, 0, fontPtr, fontData.Length);
            fontCol.AddMemoryFont(fontPtr, fontData.Length);
            Marshal.FreeCoTaskMem(fontPtr);     //<-- It works!
            _fontCollections.Add(fontCol);
            return new Font(fontCol.Families[0], size, style, pint, byt);
        }
        //static public Font GetCustomFont(string fontFile, float size, FontStyle style)
        //{
        //    if (_fontCollections == null) _fontCollections = new List<PrivateFontCollection>();
        //    PrivateFontCollection fontCol = new PrivateFontCollection();
        //    fontCol.AddFontFile(fontFile);
        //    _fontCollections.Add(fontCol);
        //    return new Font(fontCol.Families[0], size, style);
        //}

        static public Font GetCustomFont2(byte[] fontData, float size, FontStyle style)
        {
            if (_fontCollections == null) _fontCollections = new List<PrivateFontCollection>();
            PrivateFontCollection fontCol = new PrivateFontCollection();
            IntPtr fontPtr = Marshal.AllocCoTaskMem(fontData.Length);
            Marshal.Copy(fontData, 0, fontPtr, fontData.Length);
            fontCol.AddMemoryFont(fontPtr, fontData.Length);
            Marshal.FreeCoTaskMem(fontPtr);     //<-- It works!
            _fontCollections.Add(fontCol);
            return new Font(fontCol.Families[0], size, style);
        }


        static public Font GetCustomFont(string fontFile, float size, FontStyle style)
        {
            if (_fontCollections == null) _fontCollections = new List<PrivateFontCollection>();
            PrivateFontCollection fontCol = new PrivateFontCollection();
            fontCol.AddFontFile(fontFile);
            _fontCollections.Add(fontCol);
            return new Font(fontCol.Families[0], size, style);
        }

        //public static void ConfigureServices(IServiceCollection services)
        //{
        //    // Read the connection string from appsettings.
        //    string dbConnectionString = Configuration.GetConnectionString("dbConnection1");

        //    // Inject IDbConnection, with implementation from SqlConnection class.
        //    services.AddTransient<IDbConnection>((sp) => new SqlConnection(dbConnectionString));

        //    // Register your regular repositories
        //    services.AddScoped<IDiameterRepository, DiameterRepository>();

        //    // ......
        //}
    }

}
