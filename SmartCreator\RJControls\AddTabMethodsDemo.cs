using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// عرض توضيحي لطرق إضافة التابات المختلفة
    /// </summary>
    public class AddTabMethodsDemo : Form
    {
        public AddTabMethodsDemo()
        {
            InitializeForm();
            DemonstrateAddTabMethods();
        }

        private void InitializeForm()
        {
            this.Text = "طرق إضافة التابات - AddTab Methods";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void DemonstrateAddTabMethods()
        {
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 45,
                TabSpacing = 5,
                TabPadding = 25,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 8
            };

            // الطريقة الأولى: AddTab(string text)
            var method1Tab = tabControl.AddTab("الطريقة الأولى");
            method1Tab.BackColor = Color.FromArgb(0, 122, 204);
            method1Tab.ForeColor = Color.White;
            method1Tab.IconChar = IconChar.Play; // إضافة الأيقونة يدوياً

            var method1Label = new Label
            {
                Text = "📝 الطريقة الأولى: AddTab(string text)\n\n" +
                       "الكود:\n" +
                       "var tab = tabControl.AddTab(\"الطريقة الأولى\");\n" +
                       "tab.BackColor = Color.Blue;\n" +
                       "tab.IconChar = IconChar.Play; // يدوياً\n\n" +
                       "المميزات:\n" +
                       "✅ بسيط وسريع\n" +
                       "✅ يمكن تخصيص الأيقونة لاحقاً\n" +
                       "✅ مناسب للتابات البسيطة",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70),
                Padding = new Padding(20)
            };
            method1Tab.AddControl(method1Label);

            // الطريقة الثانية: AddTab(string text, IconChar icon)
            var method2Tab = tabControl.AddTab("الطريقة الثانية", IconChar.Cogs);
            method2Tab.BackColor = Color.FromArgb(76, 175, 80);
            method2Tab.ForeColor = Color.White;
            method2Tab.IconSize = 22;

            var method2Panel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 2,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 10,
                Padding = new Padding(20)
            };

            var method2Label = new Label
            {
                Text = "⚙️ الطريقة الثانية: AddTab(string text, IconChar icon)\n\n" +
                       "الكود:\n" +
                       "var tab = tabControl.AddTab(\"الطريقة الثانية\", IconChar.Cogs);\n" +
                       "tab.BackColor = Color.Green;\n" +
                       "tab.IconSize = 22;\n\n" +
                       "المميزات:\n" +
                       "✅ النص والأيقونة معاً\n" +
                       "✅ الأكثر استخداماً\n" +
                       "✅ سريع ومباشر\n" +
                       "✅ مناسب لمعظم الحالات",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            method2Panel.Controls.Add(method2Label);
            method2Tab.AddControl(method2Panel);

            // الطريقة الثالثة: AddTab(RJTabPage tab) - الجديدة!
            var customTab = new RJTabPage("الطريقة الثالثة", IconChar.Star);
            customTab.BackColor = Color.FromArgb(156, 39, 176);
            customTab.ForeColor = Color.White;
            customTab.BorderRadius = 15;
            customTab.Style = ControlStyle.Glass;
            customTab.IconSize = 20;
            customTab.Font = new Font("Segoe UI", 12, FontStyle.Bold);

            // إضافة التاب المخصص
            tabControl.AddTab(customTab); // الطريقة الجديدة!

            var method3TextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "⭐ الطريقة الثالثة: AddTab(RJTabPage tab) - جديدة!\n\n" +
                       "الكود:\n" +
                       "var customTab = new RJTabPage(\"الطريقة الثالثة\", IconChar.Star);\n" +
                       "customTab.BackColor = Color.Purple;\n" +
                       "customTab.BorderRadius = 15;\n" +
                       "customTab.Style = ControlStyle.Glass;\n" +
                       "customTab.Font = new Font(\"Segoe UI\", 12, FontStyle.Bold);\n\n" +
                       "tabControl.AddTab(customTab); // إضافة التاب المخصص\n\n" +
                       "المميزات:\n" +
                       "✅ تحكم كامل في التاب قبل الإضافة\n" +
                       "✅ يمكن تخصيص كل شيء مسبقاً\n" +
                       "✅ مرونة عالية\n" +
                       "✅ مناسب للتابات المعقدة\n" +
                       "✅ يمكن إعادة استخدام التاب\n\n" +
                       "هذه الطريقة تم إضافتها لحل مشكلة ConstructorTestForm! 🎉",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            customTab.AddControl(method3TextBox);

            // تاب المقارنة
            var comparisonTab = tabControl.AddTab("مقارنة", IconChar.BalanceScale);
            comparisonTab.BackColor = Color.FromArgb(255, 152, 0);
            comparisonTab.ForeColor = Color.White;

            var comparisonPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var comparisonLabel = new Label
            {
                Text = "⚖️ مقارنة بين الطرق الثلاث:\n\n" +
                       "1️⃣ AddTab(string text):\n" +
                       "   • الأبسط\n" +
                       "   • للتابات البسيطة\n" +
                       "   • الأيقونة يدوياً\n\n" +
                       "2️⃣ AddTab(string text, IconChar icon):\n" +
                       "   • الأكثر استخداماً\n" +
                       "   • سريع ومباشر\n" +
                       "   • مناسب لمعظم الحالات\n\n" +
                       "3️⃣ AddTab(RJTabPage tab):\n" +
                       "   • الأكثر مرونة\n" +
                       "   • تحكم كامل\n" +
                       "   • للتابات المعقدة\n" +
                       "   • جديد! 🆕",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            comparisonPanel.Controls.Add(comparisonLabel);
            comparisonTab.AddControl(comparisonPanel);

            // تاب التجربة التفاعلية
            var interactiveTab = tabControl.AddTab("تجربة تفاعلية", IconChar.Play);
            interactiveTab.BackColor = Color.FromArgb(244, 67, 54);
            interactiveTab.ForeColor = Color.White;

            var interactivePanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            // أزرار لتجربة الطرق المختلفة
            var method1Button = new RJButton
            {
                Text = "إضافة بالطريقة الأولى",
                IconChar = IconChar.Plus,
                Location = new Point(20, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            method1Button.Click += (s, e) => {
                var newTab = tabControl.AddTab($"تاب جديد {tabControl.TabCount + 1}");
                newTab.BackColor = Color.FromArgb(0, 122, 204);
                newTab.ForeColor = Color.White;
                var label = new Label { 
                    Text = "تم إنشاؤه بالطريقة الأولى!", 
                    Dock = DockStyle.Fill, 
                    TextAlign = ContentAlignment.MiddleCenter 
                };
                newTab.AddControl(label);
            };

            var method2Button = new RJButton
            {
                Text = "إضافة بالطريقة الثانية",
                IconChar = IconChar.PlusCircle,
                Location = new Point(240, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            method2Button.Click += (s, e) => {
                var icons = new[] { IconChar.Heart, IconChar.Star, IconChar.Lightbulb, IconChar.Rocket };
                var random = new Random();
                var newTab = tabControl.AddTab($"تاب أيقونة {tabControl.TabCount + 1}", 
                    icons[random.Next(icons.Length)]);
                newTab.BackColor = Color.FromArgb(76, 175, 80);
                newTab.ForeColor = Color.White;
                var label = new Label { 
                    Text = "تم إنشاؤه بالطريقة الثانية مع أيقونة عشوائية!", 
                    Dock = DockStyle.Fill, 
                    TextAlign = ContentAlignment.MiddleCenter 
                };
                newTab.AddControl(label);
            };

            var method3Button = new RJButton
            {
                Text = "إضافة بالطريقة الثالثة",
                IconChar = IconChar.Magic,
                Location = new Point(460, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            method3Button.Click += (s, e) => {
                var customTab = new RJTabPage($"تاب مخصص {tabControl.TabCount + 1}", IconChar.Magic);
                customTab.BackColor = Color.FromArgb(156, 39, 176);
                customTab.ForeColor = Color.White;
                customTab.BorderRadius = 12;
                customTab.Style = ControlStyle.Glass;
                customTab.IconSize = 18;
                
                var panel = new RJPanel {
                    Dock = DockStyle.Fill,
                    BorderSize = 3,
                    BorderColor = Color.FromArgb(156, 39, 176),
                    BorderRadius = 10,
                    Padding = new Padding(15)
                };
                var label = new Label { 
                    Text = "تاب مخصص بالكامل!\nتم إنشاؤه بالطريقة الثالثة الجديدة! 🎨", 
                    Dock = DockStyle.Fill, 
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.FromArgb(156, 39, 176)
                };
                panel.Controls.Add(label);
                customTab.AddControl(panel);
                
                tabControl.AddTab(customTab); // الطريقة الجديدة!
            };

            var instructionLabel = new Label
            {
                Text = "🎮 جرب الأزرار أعلاه لإضافة تابات بالطرق المختلفة!\n\n" +
                       "ستلاحظ الفرق في المرونة والتحكم بين الطرق الثلاث.",
                Location = new Point(20, 90),
                Size = new Size(640, 60),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            interactivePanel.Controls.Add(method1Button);
            interactivePanel.Controls.Add(method2Button);
            interactivePanel.Controls.Add(method3Button);
            interactivePanel.Controls.Add(instructionLabel);
            interactiveTab.AddControl(interactivePanel);

            this.Controls.Add(tabControl);
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunDemo()
        {
            var form = new AddTabMethodsDemo();
            form.ShowDialog();
        }
    }
}
