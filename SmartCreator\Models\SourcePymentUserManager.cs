﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using tik4net;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SmartCreator.Models
{
    public class SourcePymentUserManager_fromDB
    {
        public SourcePymentUserManager_fromDB() { }
		public int id {  get; set; }
		public string idHX {  get; set; }
		public double sn {  get; set; }
		public int userId { get; set; }
		public string userName { get; set; }
		public string sn_userName { get; set; }
		public int custId { get; set; }
        public int cusName { get; set; }
        public int profileId { get; set; }
        public string profileName { get; set; }
		public int price { get; set; } 
		public double actualLimTransfer { get; set; }
		public double actualLimUptime { get; set; }
		public double profileValiday { get; set; }
		public double validUntil { get; set; }
		public double added { get; set; } 
		public double activated { get; set; } 
		public int state { get; set; }
		public double endTime { get; set; } 
		public int paused { get; set; } 
		public string fk_sn_userName_User { get; set; } 
		public int fk_User_localDB_id { get; set; } 
		public double percentage { get; set; } 
		public double price_percentage { get; set; } 
		public int Delet_fromServer { get; set; } 
		public double date_added_Localdb { get; set; }
		public double uptimeLimit { get; set; }
		public double transferLimit { get; set; }
		 
         
    }

    public class SourcePymentUserManager_fromMK
    {
        public SourcePymentUserManager_fromMK() { }
        public string id { get; set; }
        public string idHX { get; set; }
        public string userName { get; set; }
        public string profile { get; set; }
        public string price { get; set; } = "0";
        public string added { get; set; }
        public string validUntil { get; set; }
        public string state { get; set; }
        public string endTime { get; set; }
        ITikConnection connection { get; set; }
        public void MkConnClose()
        {
            Mk_DataAccess.Mk_Conn_Close(this.connection);
        }
        [Obsolete]
        public static List<SourcePymentUserManager_fromMK> get_Pyment_user(string name = "", bool is_syn = false)
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return get_Pyment_user_V7(name,is_syn);

            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/tool/user-manager/payment/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                            loadCmd = connection.CreateCommandAndParameters(code, "user", name);
                    var response = loadCmd.ExecuteList();
                   
                    pyment=Get_Response_PyMent(response);

                    stopwatch.Stop();
                    string ss =
                       (
                         (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                          " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                         " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                       );

                  

                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب المبيعات والحسابات", "");


                    if (pyment.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_Pyments_to_LocalDB(pyment);
                        }
                    }
                    return pyment;
                }

            }
            catch  
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return pyment;
        }

        [Obsolete]
        public  List<SourcePymentUserManager_fromMK> get_Pyment_user2(string name = "", bool is_syn = false)
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return get_Pyment_user_V7(name, is_syn);

            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/tool/user-manager/payment/print";
                using (connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                        loadCmd = connection.CreateCommandAndParameters(code, "user", name);
                    var response = loadCmd.ExecuteList();

                    pyment = Get_Response_PyMent(response);

                    stopwatch.Stop();
                    string ss =
                     (
                       (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                        " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                       " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                     );

                   
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب المبيعات والحسابات", "");


                    if (pyment.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_Pyments_to_LocalDB(pyment);
                        }
                    }
                    return pyment;
                }

            }
            catch
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return pyment;
        }

        [Obsolete]
        public static List<SourcePymentUserManager_fromMK> get_Pyment_user(string name)
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return get_Pyment_user_V7(name);

            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/tool/user-manager/payment/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    var loadCmd = connection.CreateCommandAndParameters(code,"user",name);
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        try
                        {
                            SourcePymentUserManager_fromMK card = new SourcePymentUserManager_fromMK();
                            string id = item.GetResponseFieldOrDefault(".id", "");
                            card.id = id;
                            card.idHX = id;
                            card.userName = item.GetResponseFieldOrDefault("user", "");
                            string price = (item.GetResponseFieldOrDefault("price", "0"));
                            try
                            {
                                card.price = price.Substring(0, price.Length - 2);
                            }
                            catch { card.price = "0"; }
                            card.added = item.GetResponseFieldOrDefault("trans-start", "0");
                            card.validUntil = item.GetResponseFieldOrDefault("trans-end", "0");
                            card.state = (item.GetResponseFieldOrDefault("trans-status", ""));
                            pyment.Add(card);
                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    if (pyment.Count > 0)
                    {
                        UserManagerProcess u= new UserManagerProcess();
                        u.Syn_Pyments_to_LocalDB(pyment);
                    }
                    return pyment;
                }

            }
            catch
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return pyment;
        }

        private static List<SourcePymentUserManager_fromMK> Get_Response_PyMent(IEnumerable<ITikReSentence> response)
        {
            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();

            foreach (var item in response)
            {
                try
                {
                    SourcePymentUserManager_fromMK card = new SourcePymentUserManager_fromMK();
                    string id = item.GetResponseFieldOrDefault(".id", "0");
                    card.id = id;
                    card.idHX = id;
                    card.userName = item.GetResponseFieldOrDefault("user", "");
                    
                    try
                    {
                        string price = (item.GetResponseFieldOrDefault("price", "0"));
                        string nx = price.Substring(0, price.Length - 2);

                        if (float.TryParse(nx,out float xx))
                            card.price = price.Substring(0, price.Length - 2);
                            //card.price = price.Substring(0, price.Length - 2);
                        
                    }
                    catch { card.price = "0"; }
                    card.added = item.GetResponseFieldOrDefault("trans-start", null);
                    //card.added = item.GetResponseFieldOrDefault("trans-start", "0");
                    card.validUntil = item.GetResponseFieldOrDefault("trans-end", "0");
                    card.state = (item.GetResponseFieldOrDefault("trans-status", ""));
                    pyment.Add(card);
                }
                catch { }
            }
            return pyment;  
        }

        [Obsolete]
        public static List<SourcePymentUserManager_fromMK> Get_UMPyment_FromSN_ToSN(double SN_Start, double SN_To)
        {
            Stopwatch stopwatch = new Stopwatch(); stopwatch.Start();
            List<SourcePymentUserManager_fromMK> users = new List<SourcePymentUserManager_fromMK>();
            try
            {
                string code = "/tool/user-manager/payment/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    for (double i = SN_Start; i <= SN_To; i++)
                    {
                        try
                        {
                            SourcePymentUserManager_fromMK us = new SourcePymentUserManager_fromMK();
                            //Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();
                            string idx = "*" + Convert.ToInt64(i).ToString("X");
                            var loadCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, idx);
                            var response = loadCmd.ExecuteSingleRow();
                            if (response != null)
                            {
                                us = Get_SingleRow_UmPyment(response);
                                users.Add(us);
                            }
                        }
                        catch { }
                    }
                    return users;
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message);
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                //else
                //    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;
        }

        private static SourcePymentUserManager_fromMK Get_SingleRow_UmPyment(ITikReSentence item)
        {

            SourcePymentUserManager_fromMK card = new SourcePymentUserManager_fromMK();
            try
            {
                string id = item.GetResponseFieldOrDefault(".id", "");
                card.id = id;
                card.idHX = id;
                card.userName = item.GetResponseFieldOrDefault("user", "");
                string price = (item.GetResponseFieldOrDefault("price", "0"));
                try
                {
                    //card.price = ""+ Convert.ToInt32(price) / 100;

                    card.price = price.Substring(0, price.Length - 2);
                }
                catch { card.price = "0"; }
                card.added = item.GetResponseFieldOrDefault("trans-start", null);
                //card.added = item.GetResponseFieldOrDefault("trans-start", "0");
                card.validUntil = item.GetResponseFieldOrDefault("trans-end", "0");
                card.state = (item.GetResponseFieldOrDefault("trans-status", ""));
            }
            catch { }
            return card;
        }
        //==================================

        [Obsolete]
        public static List<SourcePymentUserManager_fromMK> get_Pyment_user_V7(string name = "", bool is_syn = false)
        {
            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/user-manager/user-profile/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                        loadCmd = connection.CreateCommandAndParameters(code, "user", name);
                    var response = loadCmd.ExecuteList();

                    pyment = Get_Response_PyMent_V7(response);

                    stopwatch.Stop();
                    string ss =
                      (
                        (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                         " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                        " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                      );

                    
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب المبيعات والحسابات", "");


                    if (pyment.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_Pyments_to_LocalDB(pyment);
                        }
                    }
                    return pyment;
                }

            }
            catch
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return pyment;
        }
        private static List<SourcePymentUserManager_fromMK> Get_Response_PyMent_V7(IEnumerable<ITikReSentence> response)
        {
            List<SourcePymentUserManager_fromMK> pyment = new List<SourcePymentUserManager_fromMK>();

            foreach (var item in response)
            {
                try
                {
                    SourcePymentUserManager_fromMK card = new SourcePymentUserManager_fromMK();
                    string id = item.GetResponseFieldOrDefault(".id", "");
                    card.id = id; card.idHX = id;

                    card.userName = item.GetResponseFieldOrDefault("user", "");
                    card.profile = (item.GetResponseFieldOrDefault("profile", null));
                    card.state  = (item.GetResponseFieldOrDefault("state", ""));

                    card.validUntil = (item.GetResponseFieldOrDefault("end-time", ""));
                     
                    //string price2 = (item.GetResponseFieldOrDefault("price", "0"));
                    try
                    {
                        UmProfile um = Global_Variable.UM_Profile.Find(x => x.Name == card.profile);
                        if(um!=null)
                        {
                            float pric = um.Price;
                            card.price = pric.ToString();


                        }
                        //float price = (from p in Global_Variable.UM_Profile
                        //               where (p.Name == profile)
                        //               select p.Price).FirstOrDefault();
                        //card.price = price.Substring(0, price.Length - 2);

                    }
                    catch { card.price = "0"; }
                    //card.added = item.GetResponseFieldOrDefault("trans-start", null);
                    //card.added = item.GetResponseFieldOrDefault("trans-start", "0");
                    //card.validUntil = item.GetResponseFieldOrDefault("trans-end", "0");
                    //card.state = (item.GetResponseFieldOrDefault("trans-status", ""));
                    pyment.Add(card);
                }
                catch { }
            }
            return pyment;
        }
    }

}
