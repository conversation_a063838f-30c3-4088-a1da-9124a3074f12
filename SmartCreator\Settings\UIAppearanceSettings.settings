﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="SmartCreator.Settings" GeneratedClassName="UIAppearanceSettings">
  <Profiles />
  <Settings>
    <Setting Name="Theme" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="ColorFormBorder" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="ChildFormMarker" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="FormIconActiveMenuItem" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="MultiChildForms" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="FormBorderSize" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">5</Value>
    </Setting>
    <Setting Name="Language_en" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="Style" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">3</Value>
    </Setting>
    <Setting Name="Language_ar" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
  </Settings>
</SettingsFile>