﻿using FontAwesome.Sharp;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.RJControls;
using SmartCreator.Utils;
using SmartCreator.Settings;
using Org.BouncyCastle.Utilities;
using SmartCreator.Properties;
using SmartCreator.Models;

namespace SmartCreator.RJForms.Private
{
    public partial class RJMessageForm : Form
    {
        /// <summary>
        /// More information about the DialogResult property.
        /// https://docs.microsoft.com/en-us/dotnet/api/system.windows.forms.button.dialogresult?view=net-5.0
        /// https://docs.microsoft.com/en-us/dotnet/api/system.windows.forms.form.dialogresult?view=net-5.0
        /// </summary>
        /// 

        #region Fields Definition

        private Button button1;//Button number 1
        private Button button2;//Button number 2
        private Button button3;//Button number 3
        #endregion

        #region Constructor

        public RJMessageForm(string text, string caption,
            MessageBoxButtons buttons, MessageBoxIcon icon,
            MessageBoxDefaultButton defaultButton)//Parameters required for a message box
        {
            InitializeComponent();//This form was made with the form designer
            
            if (UIAppearance.Language_ar)
            {
                //this.RightToLeft = RightToLeft.Yes;
                //this.RightToLeftLayout = true;
                //lblMessageText.RightToLeft = RightToLeft.Yes;
                //lblMessageText.rt = RightToLeft.Yes;
                lblCaption.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                lblMessageText.Font = Program.GetCustomFont(Resources.Cairo_SemiBold, 9, FontStyle.Regular);
            }

            //utils.Control_textSize(this);

            this.FormBorderStyle = FormBorderStyle.None; //Remove form border
            this.lblCaption.Text = caption;//Set caption
            this.lblMessageText.Text = text.Trim();//Set message text 
             
              
            CreateIcon(icon);//Create message box icon
            CreateButtons(buttons, defaultButton);//Create message box icon
            ApplyAppearanceSettings();//Apply settings

            lblMessageText_MeasureString();

            utils utils = new utils();
            utils.Control_textSize1(this);


        }
        #endregion

        #region Methods Definition
        private void lblMessageText_MeasureString()
        {
            if (UIAppearance.Language_ar)
            {
                try
                {
                    Font lblMessageText_font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                    lblMessageText.Font = lblMessageText_font;
                    Bitmap b = new Bitmap(20200, 20200);
                    Graphics g = Graphics.FromImage(b);
                    SizeF sizeOfString = new SizeF();
                    sizeOfString = g.MeasureString(lblMessageText.Text, lblMessageText_font);
                    lblMessageText.Location = new Point((int)(pnlMessageText.Width - sizeOfString.Width - 20), 5);
                }
                catch { }
            }

        }
        

        private void ApplyAppearanceSettings()
        {//load theme settings
            
            this.pnlTittleBar.BackColor = UIAppearance.PrimaryStyleColor;//Set title bar backcolor
            this.BackColor = RJColors.DefaultFormBorderColor;//Set form border color
            

            if (UIAppearance.Theme == UITheme.Light)
            {
                this.pnlMessageText.BackColor = RJColors.LightBackground;//Set BackgroundColor
                this.pnlIcon.BackColor = RJColors.LightBackground;
                this.pnlButtons.BackColor = RJColors.LightActiveBackground;//Set buttons panel backcolor
                this.lblMessageText.ForeColor = UIAppearance.TextColor;
            }
            else
            {
                this.pnlMessageText.BackColor = RJColors.DarkBackground;//Set BackgroundColor
                this.pnlIcon.BackColor = RJColors.DarkBackground;
                this.pnlButtons.BackColor =Utils.ColorEditor.Darken( RJColors.DarkBackground,3);//Set buttons panel backcolor
                this.lblMessageText.ForeColor = Utils.ColorEditor.Lighten(UIAppearance.TextColor,12);
            }
            this.lblMessageText.MaximumSize = new Size(utils.Control_Mesur_DPI(800), 0);//Set 600 pixels as maximum width of the text message label
            //this.Size = new Size(//Set message box size
            //   width: this.lblMessageText.Width + this.pnlIcon.Width + 50,//Message box widht
            //   height: this.lblMessageText.Height + this.pnlTittleBar.Height + this.pnlButtons.Height + 5);//Message box eight

            this.Size = new Size(//Set message box size
              width: this.lblMessageText.Width + this.pnlIcon.Width + utils.Control_Mesur_DPI(50),//Message box widht
              height: this.lblMessageText.Height + utils.Control_Mesur_DPI(this.pnlTittleBar.Height) + utils.Control_Mesur_DPI(this.pnlButtons.Height) + utils.Control_Mesur_DPI(5));//Message box eight

            //this.Size = new Size(//Set message box size
            //   width: utils.Control_Mesur_DPI(utils.Control_Mesur_DPI(this.lblMessageText.Width) + utils.Control_Mesur_DPI(this.pnlIcon.Width) + 50),//Message box widht
            //   height: utils.Control_Mesur_DPI(this.lblMessageText.Height) + utils.Control_Mesur_DPI(this.pnlTittleBar.Height) + utils.Control_Mesur_DPI(this.pnlButtons.Height) + 5);//Message box eight

        }
        private void CreateIcon(MessageBoxIcon icon)
        {//set message box icon

            switch (icon)
            {
                case MessageBoxIcon.Error: //Error
                    this.pbIcon.IconChar = IconChar.TimesCircle;
                    this.pbIcon.IconColor = Color.FromArgb(241, 98, 96);
                    break;
                case MessageBoxIcon.Information: //Information
                    this.pbIcon.IconChar = IconChar.InfoCircle;
                    this.pbIcon.IconColor = Color.FromArgb(20, 161, 228);
                    break;
                case MessageBoxIcon.Question://Question
                    this.pbIcon.IconChar = IconChar.QuestionCircle;
                    this.pbIcon.IconColor = Color.CornflowerBlue;
                    break;
                case MessageBoxIcon.Exclamation://Exclamation
                    this.pbIcon.IconChar = IconChar.ExclamationTriangle;
                    this.pbIcon.IconColor = Color.FromArgb(255, 177, 17);
                    break;
                case MessageBoxIcon.None: //None
                    this.pbIcon.IconChar = IconChar.CommentDots;
                    this.pbIcon.IconColor = Color.FromArgb(236, 93, 123);
                    break;

            }
        }
        private void CreateButtons(MessageBoxButtons buttons, MessageBoxDefaultButton defaultButton)
        {//Set message box buttons and indicate  the default button for the message box
            int y = utils.Control_Mesur_DPI(13);
            if (Global_Variable.Graphics_dpi != 96)
                y = y - utils.Control_Mesur_DPI(5);
            //int y = utils.Control_Mesur_DPI(13);
            //y= pnlButtons.Height/2-(button1.Height/2);
            switch (buttons)
            {
                case MessageBoxButtons.OK:
                    button1 = OKButton(utils.Control_Mesur_DPI(143), y);
                    break;
                case MessageBoxButtons.OKCancel:
                    button1 = OKButton(utils.Control_Mesur_DPI(79), y);
                    button2 = CancelButton(utils.Control_Mesur_DPI(205),y);
                    break;
                case MessageBoxButtons.YesNo:
                    button1 = YesButton(utils.Control_Mesur_DPI(79), y);
                    button2 = NoButton(utils.Control_Mesur_DPI(205), y);
                    break;
                case MessageBoxButtons.YesNoCancel:
                    button1 = YesButton(utils.Control_Mesur_DPI(20), y);
                    button2 = NoButton(utils.Control_Mesur_DPI(146), y);
                    button3 = CancelButton(utils.Control_Mesur_DPI(272), y);
                    break;
                case MessageBoxButtons.RetryCancel:
                    button1 = RetryButton(utils.Control_Mesur_DPI(79), y);
                    button2 = CancelButton(utils.Control_Mesur_DPI(205), y);
                    break;
                case MessageBoxButtons.AbortRetryIgnore:
                    button1 = AbortButton(utils.Control_Mesur_DPI(20), y);
                    button2 = RetryButton(utils.Control_Mesur_DPI(146), y);
                    button3 = IgnoreButton(utils.Control_Mesur_DPI(272), y);
                    break;
            }
            ActivateButton(defaultButton);//Specify the default button for the message box.
        }
        private void ActivateButton(MessageBoxDefaultButton defaultButton)
        {//Focus the default button and user can directly press Enter key to perform button action
            switch (defaultButton)
            {
                case MessageBoxDefaultButton.Button1://Focus button 1
                    button1.Select();
                    button1.ForeColor = Color.White;
                    button1.Text = "> " + button1.Text;
                    break;
                case MessageBoxDefaultButton.Button2://Focus button 2
                    if (button2 != null)
                    {
                        button2.Select();
                        button2.Text = "> " + button2.Text;
                        button2.ForeColor = Color.White;

                    }
                    else //If the button 2 does not exist, Focus button 1
                    {
                        button1.Select();
                        button1.ForeColor = Color.White;
                        button1.Text = "> " + button1.Text;
                    }
                    break;
                case MessageBoxDefaultButton.Button3://Focus button 3
                    if (button3 != null)
                    {
                        button3.Select();
                        button3.ForeColor = Color.White;
                        button3.Text = "> " + button3.Text;
                    }
                    else //If the button 3 does not exist, Focus button 1
                    {
                        button1.Select();
                        button1.ForeColor = Color.White;
                        button1.Text = "> " + button1.Text;
                    }
                    break;
            }
        }
        //Message box buttons
        private Button OKButton(int locationX, int locationY)
        {//Create Ok Button

            Button btnOk;
            btnOk = new Button();
            btnOk.Anchor = AnchorStyles.None;
            btnOk.BackColor = UIAppearance.StyleColor;
            btnOk.FlatAppearance.BorderSize= 0;
            btnOk.FlatStyle = FlatStyle.Flat;
            btnOk.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnOk.ForeColor = Color.WhiteSmoke;
            btnOk.Location = new Point(locationX, locationY);
            btnOk.Name = "btnOk";
            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);
            btnOk.Size = new Size(w, h);

            btnOk.Text = "Ok";
            if (UIAppearance.Language_ar)
            {
                btnOk.Text = "موافق";
                btnOk.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
            }
            btnOk.UseVisualStyleBackColor = false;

            btnOk.DialogResult = DialogResult.OK;//Set DialogResult

            pnlButtons.Controls.Add(btnOk);//Add button

            return btnOk;

        }
        private new Button CancelButton(int locationX, int locationY)
        {//Create Cancel Button

            Button btnCancel;
            btnCancel = new Button();
            btnCancel.Anchor = AnchorStyles.None;
            btnCancel.BackColor = RJColors.Cancel;
            btnCancel.FlatAppearance.BorderSize= 0;
            btnCancel.FlatStyle = FlatStyle.Flat;
            btnCancel.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor   , FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnCancel.ForeColor = Color.WhiteSmoke;
            btnCancel.Location = new Point(locationX, locationY);
            btnCancel.Name = "btnCancel";

            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);

            btnCancel.Size = new Size(w, h);
            btnCancel.Text = "Cancel";
            if (UIAppearance.Language_ar)
            {
                btnCancel.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                btnCancel.Text = "إلغاء";
            }
            btnCancel.UseVisualStyleBackColor = false;

            btnCancel.DialogResult = DialogResult.Cancel;//Set DialogResult

            pnlButtons.Controls.Add(btnCancel);//Add button

            return btnCancel;
        }
        private Button YesButton(int locationX, int locationY)
        {//Create Yes Button

            Button btnYes;
            btnYes = new Button();
            btnYes.Anchor = AnchorStyles.None;
            btnYes.BackColor = RJColors.Confirm;
            btnYes.FlatAppearance.BorderSize= 0;
            btnYes.FlatStyle = FlatStyle.Flat;
            btnYes.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnYes.ForeColor = Color.WhiteSmoke;
            btnYes.Location = new Point(locationX, locationY);
            btnYes.Name = "btnYes";
            //btnYes.Size = new Size(110, 35);

            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);

            btnYes.Size = new Size(w, h);
            btnYes.Text = "Yes";
            if (UIAppearance.Language_ar)
            {
                btnYes.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                btnYes.Text = "نعم";
            }
           
               
            btnYes.UseVisualStyleBackColor = false;

            btnYes.DialogResult = DialogResult.Yes;//Set DialogResult

            pnlButtons.Controls.Add(btnYes);//Add button

            return btnYes;
        }
        private Button NoButton(int locationX, int locationY)
        {//Create No Button

            Button btnNo;
            btnNo = new Button();
            btnNo.Anchor = AnchorStyles.None;
            btnNo.BackColor = RJColors.Delete;
            btnNo.FlatAppearance.BorderSize= 0;
            btnNo.FlatStyle = FlatStyle.Flat;
            btnNo.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnNo.ForeColor = Color.WhiteSmoke;
            btnNo.Location = new Point(locationX, locationY);
            btnNo.Name = "btnNo";
            //btnNo.Size = new Size(110, 35);
            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);
            btnNo.Size = new Size(w, h);
            btnNo.Text = "No";
            if (UIAppearance.Language_ar)
            {
                btnNo.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                btnNo.Text = "لا";
            }
            
            btnNo.UseVisualStyleBackColor = false;

            btnNo.DialogResult = DialogResult.No;//Set DialogResult

            pnlButtons.Controls.Add(btnNo);//Add button

            return btnNo;
        }
        private Button RetryButton(int locationX, int locationY)
        {//Create RetryButton

            Button btnRetry;
            btnRetry = new Button();
            btnRetry.Anchor = AnchorStyles.None;
            btnRetry.BackColor = UIAppearance.StyleColor;
            btnRetry.FlatAppearance.BorderSize= 0;
            btnRetry.FlatStyle = FlatStyle.Flat;
            btnRetry.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnRetry.ForeColor = Color.WhiteSmoke;
            btnRetry.Location = new Point(locationX, locationY);
            btnRetry.Name = "btnRetry";
            //btnRetry.Size = new Size(110, 35);

            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);
            btnRetry.Size = new Size(w, h);

            btnRetry.Text = "Retry";
            if (UIAppearance.Language_ar)
            {
                btnRetry.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                btnRetry.Text = "أعد المحاولة";
            }
            
            btnRetry.UseVisualStyleBackColor = false;

            btnRetry.DialogResult = DialogResult.Retry;//Set DialogResult

            pnlButtons.Controls.Add(btnRetry);//Add button

            return btnRetry;
        }
        private Button AbortButton(int locationX, int locationY)
        {//Create Abort Button
            Button btnAbort;
            btnAbort = new Button();
            btnAbort.Anchor = AnchorStyles.None;
            btnAbort.BackColor = RJColors.Delete;
            btnAbort.FlatAppearance.BorderSize= 0;
            btnAbort.FlatStyle = FlatStyle.Flat;
            btnAbort.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnAbort.ForeColor = Color.WhiteSmoke;
            btnAbort.Location = new Point(locationX, locationY);
            btnAbort.Name = "btnAbort";
            btnAbort.Text = " Abort";
            //btnAbort.Size = new Size(110, 35);

            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);
            btnAbort.Size = new Size(w, h);

            if (UIAppearance.Language_ar)
            {
                btnAbort.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor, FontStyle.Regular);
                btnAbort.Text = " أحبط";
            }

             
            btnAbort.UseVisualStyleBackColor = false;

            btnAbort.DialogResult = DialogResult.Abort;//Set DialogResult

            pnlButtons.Controls.Add(btnAbort);//Add button

            return btnAbort;
        }
        private Button IgnoreButton(int locationX, int locationY)
        {//Create Ignore Button

            Button btnIgnore;
            btnIgnore = new Button();
            btnIgnore.Anchor = AnchorStyles.None;
            btnIgnore.BackColor = RJColors.Cancel;
            btnIgnore.FlatAppearance.BorderSize= 0;
            btnIgnore.FlatStyle = FlatStyle.Flat;
            btnIgnore.Font = new Font("Microsoft Sans Serif", 10F * utils.ScaleFactor, FontStyle.Regular, GraphicsUnit.Point, ((byte)(0)));
            btnIgnore.ForeColor = Color.WhiteSmoke;
            btnIgnore.Location = new Point(locationX, locationY);
            btnIgnore.Name = "btnIgnore";
            btnIgnore.Size = new Size(110, 35);
            //btnIgnore.Text = "Ignore";

            int w = utils.Control_Mesur_DPI(110);
            int h = utils.Control_Mesur_DPI(35);
            btnIgnore.Size = new Size(w, h);

            if (UIAppearance.Language_ar)
            {
                btnIgnore.Font = Program.GetCustomFont(Resources.Cairo_Medium, 9 * utils.ScaleFactor , FontStyle.Regular);
                btnIgnore.Text = "جاهل";
            }
             
            btnIgnore.UseVisualStyleBackColor = false;

            btnIgnore.DialogResult = DialogResult.Ignore;//Set DialogResult

            pnlButtons.Controls.Add(btnIgnore);//Add Button

            return btnIgnore;
        }
        #endregion

        #region Event Methods Definition 

        private void btnExit_Click(object sender, EventArgs e)
        {
            this.Close();//Message Box Close            
        }
        #endregion

        private void RJMessageForm_Load(object sender, EventArgs e)
        {

        }
    }

}
