﻿using SmartCreator.Forms.Hotspot;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_Archive_Cards : RJChildForm
    {
        FormAllBatchsCards_Archive formAllBatchsCards_Archive;
        FormAllCards_ِArchive formAllCards_ِArchive;
        bool First_formAllBatchsCards_Archive = true;
        bool First_formAllCards_Archive = true;


        public Form_Archive_Cards()
        {
            InitializeComponent();
            utils utils = new utils();
            utils.Control_textSize1(this);

            this.Text = "ادارة الكروت المعلقة - الارشيف";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Cards Archive";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
        }

        private void btn_Batch_Cards_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Batch_Cards_Title);

            if (First_formAllBatchsCards_Archive)
            {
                First_formAllBatchsCards_Archive = false;
                formAllBatchsCards_Archive = new FormAllBatchsCards_Archive();
                formAllBatchsCards_Archive.TopLevel = false;
                formAllBatchsCards_Archive.IsChildForm = true;
                formAllBatchsCards_Archive.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAllBatchsCards_Archive);
                this.panel_Tab_Container.Tag = formAllBatchsCards_Archive;
                formAllBatchsCards_Archive.Show(); //show on desktop panel  
                formAllBatchsCards_Archive.BringToFront();
                formAllBatchsCards_Archive.Focus();
                formAllBatchsCards_Archive.LoadDataGridviewData();
            }
            else
            {
                formAllBatchsCards_Archive.BringToFront();
                formAllBatchsCards_Archive.Show();
                formAllBatchsCards_Archive.Focus();
            }


        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void Form_Batch_Archive_Load(object sender, EventArgs e)
        {
            btn_Batch_Cards_Title_Click(sender,e);
        }

        private void btn_All_Cards_RB_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_All_Cards_RB_Title);

            if (First_formAllCards_Archive)
            {
                First_formAllCards_Archive = false;
                formAllCards_ِArchive = new FormAllCards_ِArchive();
                formAllCards_ِArchive.TopLevel = false;
                formAllCards_ِArchive.IsChildForm = true;
                formAllCards_ِArchive.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAllCards_ِArchive);
                this.panel_Tab_Container.Tag = formAllCards_ِArchive;
                formAllCards_ِArchive.Show(); //show on desktop panel  
                formAllCards_ِArchive.BringToFront();
                formAllCards_ِArchive.Focus();
                formAllCards_ِArchive.LoadDataGridviewData();
            }
            else
            {
                formAllCards_ِArchive.BringToFront();
                formAllCards_ِArchive.Show();
                formAllCards_ِArchive.Focus();
            }

        }
    }
}
