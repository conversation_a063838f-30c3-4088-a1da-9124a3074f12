﻿using SmartCreator.Models.hotspot;
using SmartCreator.Models;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using Renci.SshNet.Sftp;
using Renci.SshNet;
using System.IO;
using SmartCreator.Data;
using CefSharp.WinForms;
using CefSharp;
using SmartCreator.Properties;
using System.Security.Cryptography;
using System.Windows.Markup;
using System.Diagnostics;
using System.Threading;
using SmartCreator.Utils;
using CefSharp.DevTools.IO;
using System.Threading.Tasks;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.PageHtml
{
    public partial class Form_Page_Editor : RJChildForm
    {
        //string destLocalPath1 = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\hsprof1\\hotspot2023\\login.html";
        bool ChangFromLoad = false;
        string BasePath = "";
        string BaseFile = "";
        bool Firstload = true;
        Hotspot_Server_Profile hp;

        HashSet<Dictionary<string, string>> ImageAddNew = new HashSet<Dictionary<string, string>>();



        public Form_Page_Editor()
        {
            InitializeComponent();
            set_font();
            try
            {
                //string destLocalPath = utils.Get_Ftp_Directory()  + "\\"  + "Pages" + "\\" + Global_Variable.Mk_resources.RB_SN + "\\" + @"hsprof1\hotspot2023" + "\\";

                //string destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\hsprof1\\hotspot2023\\login.html";

                //BasePath = destLocalPath;
                //BaseFile = BasePath + "login.html";

                //BaseFile = BasePath + "login.html";
                //chromiumWebBrowser1.LoadUrl(destLocalPath);

                this.Text = "محرر الصفحات";
                if (UIAppearance.Language_ar == false)
                {
                    this.Text = "Page Editor";
                }
                CBox_all_Page_Html.SelectedIndex = 0;
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }
        void set_font()
        {

           
            Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            btn_download_Page.Font= btn_temp_save.Font = btn_UploadFiles.Font = rjLabel2.Font= rjLabel7.Font = title_font;


            Font lbl = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel13.Font= rjLabel6.Font = lbl;

            utils.Control_textSize(pnlClientArea);
            return;

            Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        [Obsolete]
        private void Get_Cbox_Server_Profile_hotspot()
        {
            try
            {
                //ThreadStart theprogress = new ThreadStart(() => load_Server_Profile());
                //Thread startprogress = new Thread(theprogress);
                //startprogress.Name = "Update ProgressBar";
                //startprogress.Start();

                load_Server_Profile();

                ////List<Hotspot_Source_Profile> profile = Global_Variable.Source_HS_Profile;

                //Hotspot_Server_Profile hotspot = new Hotspot_Server_Profile();
                //Dictionary<string, Hotspot_Server_Profile> comboSource = new Dictionary<string, Hotspot_Server_Profile>();

                ////comboSource.Add("", new Hotspot_Server_Profile());

                //foreach (Hotspot_Server_Profile s in hotspot.Get_Hotspot_Server_Profil_from_Router())
                //    comboSource.Add(s.Name, s);
                //CBox_Server_Profile.DataSource = new BindingSource(comboSource, null);
                //CBox_Server_Profile.DisplayMember = "Key";
                //CBox_Server_Profile.ValueMember = "Value";

                //hp = (Hotspot_Server_Profile)CBox_Server_Profile.SelectedValue;
            }
            catch { }
        }

        [Obsolete]
        void load_Server_Profile()
        {
            try
            {
                
                //List<Hotspot_Source_Profile> profile = Global_Variable.Source_HS_Profile;

                Hotspot_Server_Profile hotspot = new Hotspot_Server_Profile();
                Dictionary<string, Hotspot_Server_Profile> comboSource = new Dictionary<string, Hotspot_Server_Profile>();

                //comboSource.Add("", new Hotspot_Server_Profile());

                foreach (Hotspot_Server_Profile s in hotspot.Get_Hotspot_Server_Profil_from_Router())
                    comboSource.Add(s.Name, s);
                CBox_Server_Profile.DataSource = new BindingSource(comboSource, null);
                CBox_Server_Profile.DisplayMember = "Key";
                CBox_Server_Profile.ValueMember = "Value";

                hp = (Hotspot_Server_Profile)CBox_Server_Profile.SelectedValue;
            }
            catch { }
        }

        [Obsolete]
        private void Form_Page_Editor_Load(object sender, EventArgs e)
        {
            timer1.Start();
            //Get_Cbox_Server_Profile_hotspot();
        }

        bool startProcess=false;
        [Obsolete]
        private void btn_download_Page_Click(object sender, EventArgs e)
        {
            try
            {
                Firstload = true;
                pictureBox1.Image = null;
                txt_CurrentAddress.Text = "";
                txt_path.Text = "";
                CBox_all_Page_Html.SelectedIndex = 0;

                Firstload = false;
            }
            catch { Firstload = false; }

            if (CBox_Server_Profile.SelectedValue == null)
                return;

            if(startProcess)
            {
                RJMessageBox.Show("انتظر حتى انتهاء العملية السابقة");
            }
            ThreadStart theprogress = new ThreadStart(() => DownloadPages());
            Thread startprogress = new Thread(theprogress);
            startprogress.Name = "Update ProgressBar";
            startprogress.Start();


           

        }

        [Obsolete]
        private void DownloadPages()
        {
            startProcess = true;
            //hp = (Hotspot_Server_Profile)CBox_Server_Profile.SelectedValue;
            check_port_mk_befor();
            using (var client = new SftpClient(Global_Variable.Server_IP, ssh_port, Global_Variable.Server_Username, Global_Variable.Server_Password))
            {
                try { client.Connect(); }
                catch (Exception ex)
                {
                    if (ex.Message == "The connection was closed by the server:  (ServiceNotAvailable)")
                    {
                        MessageBox.Show("للكتابة او القراءه علي الملفات ftp المستخدم ليس لديه صلاحيات ssh   \n\n" + ex.Message); return;
                    }
                    MessageBox.Show("خطاء بالاتصال بالمايكروتك قد يكون المستخدم محدود الصلاحيات \n\n" + ex.Message);
                }
                if (client.IsConnected)
                {
                    string folderName = hp.Name + "\\" + Path.GetFileName(hp.html_directory);
                    string destLocalPath = utils.Get_Ftp_Directory() + "\\" + "Pages" + "\\" + Global_Variable.Mk_resources.RB_SN + "\\" + folderName + "\\";

                    DownloadDirectory(client, hp.html_directory, destLocalPath);
 

                    Global_Variable.Update_Um_StatusBar_Prograss($"تم تحميل ملفات الصفحه ", 0);

                    Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                    {
                        if (CBox_all_Page_Html.SelectedIndex == 0)
                            chromiumWebBrowser1.LoadUrl(destLocalPath + "\\login.html");
                        else if (CBox_all_Page_Html.SelectedIndex == 1)
                            chromiumWebBrowser1.LoadUrl(destLocalPath + "\\status.html");
                        else if (CBox_all_Page_Html.SelectedIndex == 2)
                            chromiumWebBrowser1.LoadUrl(destLocalPath + "\\logout.html");
                        else
                            chromiumWebBrowser1.LoadUrl(destLocalPath);

                        BasePath = destLocalPath;
                        BaseFile = destLocalPath + "\\login.html";
                        txt_CurrentAddress.Text = BaseFile;

                    });


                }
            }
            startProcess = false;
            rest_port_mk_after();
        }
        public  void DownloadDirectory(SftpClient sftpClient, string sourceRemotePath, string destLocalPath)
        {
            try
            {
                //destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\" + destLocalPath + "\\";
                //sourceRemotePath = "/" + sourceRemotePath + "\\";
                Directory.CreateDirectory(destLocalPath);
                foreach (ISftpFile file in sftpClient.ListDirectory(sourceRemotePath))
                {
                    if ((file.Name != ".") && (file.Name != ".."))
                    {
                        string sourceFilePath = sourceRemotePath + "/" + file.Name;
                        string destFilePath = Path.Combine(destLocalPath, file.Name);
                        if (file.IsDirectory)
                        {
                            DownloadDirectory(sftpClient, sourceFilePath, destFilePath);
                        }
                        else
                        {
                            using (Stream fileStream = System.IO.File.Create(destFilePath))
                            {
                                Global_Variable.Update_Um_StatusBar_Prograss($"تحميل المف  {sourceFilePath}", 0);

                                var fileSize = sftpClient.GetAttributes(sourceFilePath).Size;
                                long downloadedBytes = 0;
                                int progress = 0;
                                Action<ulong> progressCallback = (downloaded) =>
                                {
                                    downloadedBytes = (long)downloaded;
                                    progress = (int)((downloadedBytes * 100) / fileSize);
                                    Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke((System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progress; });
                                };
                                sftpClient.DownloadFile(sourceFilePath, fileStream, progressCallback);
                                //sftpClient.DownloadFile(sourceFilePath, fileStream);
                            }
                        }
                    }
                }
            }
            catch (Exception e) { MessageBox.Show(e.Message); }
        }
        public string ssh_last_state = "false";
        public string id_ssh = "";
        public int ssh_port = 22;
        public DataTable dt_service = null;


        [Obsolete]
        private void btn_UploadFiles_Click(object sender, EventArgs e)
        {
            if (chromiumWebBrowser1.Address == null || CBox_Server_Profile.Text=="")
            {
                RJMessageBox.Show("اختر الصفحه ");
                return;
            }

            DialogResult result = RJMessageBox.Show("  هل انت متأكد من رفع تعديلات الصفحة الحالية الي المايكروتك   ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            if (CBox_Server_Profile.SelectedValue == null)
                return;

            hp = (Hotspot_Server_Profile)CBox_Server_Profile.SelectedValue;


           

            //UploadFiles(BaseFile, hp.html_directory + "/" + Path.GetFileName(BaseFile));
            //if (ImageAddNew.Count > 0)
            //{
            //    foreach (var img in ImageAddNew)
            //    {
            //        UploadFiles(img["LocalUrl"], img["ServerUrl"]);
            //    }
            //}

            ThreadStart theprogress = new ThreadStart(() => UploadSaveFiles());
            Thread startprogress = new Thread(theprogress);
            startprogress.Name = "Update ProgressBar";
            startprogress.Start();


            //rest_port_mk_after();
        }

        [Obsolete]
        void UploadSaveFiles()
        {

            check_port_mk_befor();
            using (var client = new SftpClient(Global_Variable.Server_IP, ssh_port, Global_Variable.Server_Username, Global_Variable.Server_Password))
            {
                try { client.Connect(); }
                catch (Exception ex)
                {
                    if (ex.Message == "The connection was closed by the server:  (ServiceNotAvailable)")
                    {
                        MessageBox.Show("للكتابة او القراءه علي الملفات ftp المستخدم ليس لديه صلاحيات ssh   \n\n" + ex.Message); return;
                    }
                    MessageBox.Show("خطاء بالاتصال بالمايكروتك قد يكون المستخدم محدود الصلاحيات \n\n" + ex.Message);
                }
                if (client.IsConnected)
                {
                    //string folderName = hp.Name + "\\" + Path.GetFileName(hp.html_directory);
                    //string destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\" + "Pages" + "\\" + Global_Variable.Mk_resources.RB_SN + "\\" + folderName + "\\";

                    UploadFiles(client, BaseFile, hp.html_directory + "/" + Path.GetFileName(BaseFile) );
                    if (ImageAddNew.Count > 0)
                    {
                        foreach (var img in ImageAddNew)
                        {
                            UploadFiles(client, img["LocalUrl"], img["ServerUrl"]);
                        }
                    }

                    //UploadFiles(BaseFile, hp.html_directory + "/" + Path.GetFileName(BaseFile));

                    Global_Variable.Update_Um_StatusBar_Prograss($"تم رفع ملفات الصفحه ", 0);

                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                    //{
                    //    if (CBox_all_Page_Html.SelectedIndex == 0)
                    //        chromiumWebBrowser1.LoadUrl(destLocalPath + "\\login.html");
                    //    else if (CBox_all_Page_Html.SelectedIndex == 1)
                    //        chromiumWebBrowser1.LoadUrl(destLocalPath + "\\status.html");
                    //    else if (CBox_all_Page_Html.SelectedIndex == 2)
                    //        chromiumWebBrowser1.LoadUrl(destLocalPath + "\\logout.html");
                    //    else
                    //        chromiumWebBrowser1.LoadUrl(destLocalPath);

                    //    BasePath = destLocalPath;
                    //    BaseFile = destLocalPath + "\\login.html";
                    //    txt_CurrentAddress.Text = BaseFile;

                    //});


                }
            }
            rest_port_mk_after();



            //check_port_mk_befor();

            //UploadFiles(BaseFile, hp.html_directory + "/" + Path.GetFileName(BaseFile));
            //if (ImageAddNew.Count > 0)
            //{
            //    foreach (var img in ImageAddNew)
            //    {
            //        UploadFiles(img["LocalUrl"], img["ServerUrl"]);
            //    }
            //}

            //rest_port_mk_after();

            Global_Variable.Update_Um_StatusBar_Prograss($"تم رفع ملفات الصفحة", 0);


        }
        bool UploadFiles(SftpClient client, string LocalFile, string UploadFile)
        {
            bool add = false;
            string workingdirectory = "/";
            try
            {
                //using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                //{
                    //client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(LocalFile, FileMode.Open))
                    {
                        Global_Variable.Update_Um_StatusBar_Prograss($"رفع المف  {UploadFile} ", 0);
                        
                    FileInfo file = new FileInfo(LocalFile);

                    var fileSize = file.Length;
                        //var fileSize = client.GetAttributes(UploadFile).Size;
                        long downloadedBytes = 0;
                        int progress = 0;
                        Action<ulong> progressCallback = (downloaded) =>
                        {
                            downloadedBytes = (long)downloaded;
                            progress = (int)((downloadedBytes * 100) / fileSize);
                            Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke((System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progress; });
                        };


                    client.BufferSize = 4 * 1024; // bypass Payload error large files
                        client.UploadFile(fileStream, (UploadFile), progressCallback);
                    }
                    add = true;
                //}
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            return add;
        }

 bool UploadFiles2(string LocalFile, string UploadFile)
        {
            bool add = false;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(LocalFile, FileMode.Open))
                    {
                        Global_Variable.Update_Um_StatusBar_Prograss($"رفع المف  {UploadFile} ", 0);

                        var fileSize = client.GetAttributes(UploadFile).Size;
                        long downloadedBytes = 0;
                        int progress = 0;
                        Action<ulong> progressCallback = (downloaded) =>
                        {
                            downloadedBytes = (long)downloaded;
                            progress = (int)((downloadedBytes * 100) / fileSize);
                            Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke((System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progress; });
                        };


                        client.BufferSize = 4 * 1024; // bypass Payload error large files
                        client.UploadFile(fileStream, (UploadFile));
                    }
                    add = true;
                }
            }
            catch (Exception ex) { }
            return add;
        }


        [Obsolete]
        public bool check_port_mk_befor()
        {
            bool result = false;
            Mk_DataAccess DA2 = new Mk_DataAccess();
            dt_service = DA2.GetService();
            try
            {
                DataRow[] foundRows = dt_service.Select("[name] = " + "'ssh'");
                ssh_last_state = foundRows[0]["disabled"].ToString();
                ssh_port = Convert.ToInt32(foundRows[0]["port"].ToString());
                Global_Variable.Mk_Login_data.Mk_Port_ssh = ssh_port;
                id_ssh = foundRows[0]["id"].ToString();
                result = true;
            }
            catch { }

            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "false");
                result = true;
            }
            return result;
        }

        [Obsolete]
        public void rest_port_mk_after()
        {
            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "true");
            }
        }

        private void txt_PathImage_onTextChanged(object sender, EventArgs e)
        {
            //return;
            //var script = @"Array.from(document.getElementsByTagName('span')).map(x => ( x.innerText));";
            //JavascriptResponse response =  chromiumWebBrowser1.EvaluateScriptAsync(script);

            //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementById('ClickSmart').value = '{txtName.Text}';");
            //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementById('ClickSmart').innerHTML = '{txtName.Text}';");
            chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].innerText = '{txt_innerText.Text}';");
            //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].innerHTML = '{txtName.Text}';");

            //chromiumWebBrowser1.ViewSource();
            //chromiumWebBrowser1.GetSourceAsync().ContinueWith(taskHtml =>
            //{
            //    var html = taskHtml.Result;
            //});

            //ChromiumWebBrowser browserSender = (ChromiumWebBrowser)sender;

            //string destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\hsprof1\\hotspot2023\\login.html";

            //string html = await chromiumWebBrowser1.GetMainFrame().GetSourceAsync();
            ////Debug.WriteLine(html);
            //chromiumWebBrowser1.LoadHtml(html,destLocalPath);

        }

        private void chromiumWebBrowser1_MouseDown(object sender, MouseEventArgs e)
        {
            //MessageBox.Show(e.Delta.ToString());
        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjPanel2.Refresh();
            rjPanel1.Refresh();
            rjPanel3.Refresh();
            rjPanel4.Refresh();
            pnl_tag_imge.Refresh();
            pnl_tag_text.Refresh();
        }

        private void Form_Page_Editor_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();

            rjPanel2.Refresh();
            pnl_tag_imge.Refresh();
            pnl_tag_text.Refresh();
        }

        private async void btnPreviewTemplate_Click(object sender, EventArgs e)
        {
            //string destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\hsprof1\\hotspot2023\\login.html";

            string html2 = await chromiumWebBrowser1.GetMainFrame().GetSourceAsync();
            //Debug.WriteLine(html);
            chromiumWebBrowser1.LoadHtml(html2, BaseFile);

            chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementById('ClickSmart').value = '{txt_innerText.Text}';");
            //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart') = '{txtName.Text}';");

            string html = await chromiumWebBrowser1.GetMainFrame().GetSourceAsync();
            //Debug.WriteLine(html);
            chromiumWebBrowser1.LoadHtml(html, BaseFile);

            Set_texts_from_Html(html);
        }

        private void chromiumWebBrowser1_FrameLoadEnd(object sender, FrameLoadEndEventArgs e)
        {
            if (e.Frame.IsMain)
            {
                chromiumWebBrowser1.ExecuteScriptAsync(@"
                            let prev = null;
	                        document.body.addEventListener(
                            'click',
                            //'mouseover',
                            (event) => {
		                        //console.log(event.target);
		                        //console.log(event.target.tagName);
		                        //console.log(event.target.children);
		                        //console.log(event.target.children.length);
                                if (event.target === document.body || (prev && prev === event.target) ||( event.target.tagName == 'FORM') ) {return;}
		                        if(event.target.tagName == 'DIV' ) {
			                        if (event.target.children.length > 0){return;}
		                        }
                                if (prev) {
			                        prev.classList.remove('ClickSmart');
			                        //prev.style.backgroundColor = 'transparent';
                                    prev = null;
                                }
                                if (event.target) {
                                    prev = event.target;
			                        prev.classList.add('ClickSmart');
			                        //prev.style.backgroundColor = 'yellow';
                                }
		                        event.preventDefault();
     	                        return false;
                            },
                            false
                        );
	                        const addCSS = css => document.head.appendChild(document.createElement('style')).innerHTML=css;
	                        addCSS('.ClickSmart {background-color: #FFFFE0;}');



                            document.addEventListener('click', function(e) { 
		                    var element = e.target;
		                    var valuse = {
			                    //parent:element.parentElement,
			                    nodeType:element.nodeType,
			                    nodeName:element.nodeName,
			                    tagName: element.tagName,
			                    textContent: element.textContent,
			                    innerHTML: element.innerHTML,
			                    innerText: element.innerText,
			                    href: element.href,
			                    src1: element.src,
			                    src: element.getAttribute('src'),
			                    value: element.value,
			                    childrenlength: element.children.length,
		                      };
		  
                           // alert(valuse);
		                   // console.log(valuse);
		                    CefSharp.PostMessage(JSON.stringify(valuse));
		                    //CefSharp.PostMessage(e.target.children);
		                    //CefSharp.PostMessage(e.target);
		                    //CefSharp.PostMessage(e.target.innerHTML);
		                    //CefSharp.PostMessage(parent.innerHTML);
		                    //CefSharp.PostMessage(parent.outerHTML);

	                    }, false);
                       
                           
                "
               );
            }

            //if (e.Frame.IsMain)
            //{CefSharp.PostMessage(e.target.firstChild);
            //    ChromiumWebBrowser browserSender = (ChromiumWebBrowser)sender;
            //    string html = await browserSender.GetMainFrame().GetSourceAsync();
            //    Debug.WriteLine(html);
            //    chromiumWebBrowser1.LoadHtml( html);
            //}
        }
        public string trimStart(string target, string trimString)
        {
            if (string.IsNullOrEmpty(trimString)) return target;

            string result = target;
            while (result.StartsWith(trimString))
            {
                result = result.Substring(trimString.Length);
            }

            return result;
        }
        void Set_texts_from_Html(string html)
        {
            //var doc = new HtmlAgilityPack.HtmlDocument();
            //doc.LoadHtml(html);
            //doc.Save(destLocalPath1);

            //IEnumerable<HtmlNode> node_1 = doc.DocumentNode.Descendants(0).Where(n => n.HasClass(className));
            //foreach (var input in node_1)
            //{
            //    string nameNode = input.Name;
            //    string f = "//" + nameNode + "[contains(@class, '" + className + "')]//text()";
            //    HtmlTextNode Hnode = null;
            //    Hnode = doc.DocumentNode.SelectSingleNode(f) as HtmlTextNode;
            //    Hnode.Text = text;
            //    doc.Save(path_html);
            //}





            Text = "CefSharp";
            WindowState = FormWindowState.Maximized;

            chromiumWebBrowser1 = new ChromiumWebBrowser("https://cefsharp.github.io/");
            //toolStripContainer.ContentPanel.Controls.Add(browser);

            //chromiumWebBrowser1.IsBrowserInitializedChanged += OnIsBrowserInitializedChanged;
            //chromiumWebBrowser1.LoadingStateChanged += OnLoadingStateChanged;
            //chromiumWebBrowser1.ConsoleMessage += OnBrowserConsoleMessage;
            //browser.StatusMessage += OnBrowserStatusMessage;
            //browser.TitleChanged += OnBrowserTitleChanged;
            //browser.AddressChanged += OnBrowserAddressChanged;
            // Add this  for request handler
            CefSharp.Handler.RequestHandler requestHandler = new MyBasicRequestHandler();
            chromiumWebBrowser1.RequestHandler = requestHandler;

            var version = string.Format("Chromium: {0}, CEF: {1}, CefSharp: {2}",
               Cef.ChromiumVersion, Cef.CefVersion, Cef.CefSharpVersion);

            var bitness = Environment.Is64BitProcess ? "x64" : "x86";
            var environment = String.Format("Environment: {0}", bitness);

            string DisplayOutput = (string.Format("{0}, {1}", version, environment));
            //DisplayOutput(string.Format("{0}, {1}", version, environment));

        }

        string src_img = "";
        private void chromiumWebBrowser1_JavascriptMessageReceived(object sender, JavascriptMessageReceivedEventArgs e)
        {
            //src_img = "";
            if (e.Message != null)
            {
                ChangFromLoad = true;
                var data = Newtonsoft.Json.JsonConvert.DeserializeObject<dynamic>(e.Message.ToString());
                if (data != null)
                    if (((string)data.tagName == "DIV" && (int)data.childrenlength > 0) || (string)data.tagName == "FORM" || (string)data.tagName == "BODY")
                    {
                        ChangFromLoad = false;
                        return;
                    }

                Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                {
                    //ChangFromLoad = false;

                    txt_path.Text = "";
                    txt_innerText.Text = "";
                    pictureBox1.Image = null;
                    //ChangFromLoad = false;

                });

                if (data != null)
                {
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { lbl_type_tag.Text = (string)data.tagName; });
                    if ((string)data.tagName == "IMG")
                    {
                        Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                        {
                            pnl_tag_imge.Visible = true;
                            pnl_tag_text.Visible = false;
                            try
                            {
                                src_img = (string)data.src;
                                //src_img_file = (string)data.src;
                                //pictureBox1.Load(data.src);
                                string path = trimStart((string)data.src1, @"file:///");
                                //pictureBox1.Image = System.Drawing.Image.FromFile(path);
                                using (FileStream fs = new FileStream(path, FileMode.Open))
                                {
                                    Image img = Image.FromStream(fs);
                                    pictureBox1.Image = img;
                                }
                                //pictureBox1.Image = new Bitmap(path);
                                txt_path.Text = path;
                            }
                            catch { }
                        });
                    }
                    if (
                        (string)data.tagName == "H1" || (string)data.tagName == "H2" || (string)data.tagName == "H3" || (string)data.tagName == "H4" ||
                        (string)data.tagName == "P" || (string)data.tagName == "B" || (string)data.tagName == "TH" || (string)data.tagName == "TD" ||
                        (string)data.tagName == "U" || (string)data.tagName == "UL" || (string)data.tagName == "LI" || (string)data.tagName == "I" ||
                        (string)data.tagName == "LABEL" || (string)data.tagName == "OL" || (string)data.tagName == "OPTION" || (string)data.tagName == "SMALL" ||
                        (string)data.tagName == "STRONG" || (string)data.tagName == "TEXTAREA" || (string)data.tagName == "A" || (string)data.tagName == "SPAN" || (string)data.tagName == "DIV" ||  //(string)data.tagName == "MARQUEE" ||
                        (string)data.tagName == "INPUT" || (string)data.tagName == "BUTTON" || (string)data.tagName == "MARQUEE" ||//(string)data.tagName == "MARQUEE" ||
                        (string)data.tagName == "FONT")   //||(string)data.tagName == "IFRAME" ||
                    {
                        Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                          {
                              pnl_tag_imge.Visible = false;
                              pnl_tag_text.Visible = true;
                              //src_img = "";
                              txt_innerText.Text = data.innerText;
                              if ((string)data.tagName == "INPUT")
                                  txt_innerText.Text = data.value;
                          });
                    }
                }
                ChangFromLoad = false;

                // Extract data from e.Message.toString() and use delegates/callbacks/Invokes 
                // to reference the main UI thread for updating the necessary controls.
            }
        }

        private void btn_temp_save_Click(object sender, EventArgs e)
        {
            Task ts = getSource();
            //return;
            //var script = @"Array.from(document.getElementsByTagName('span')).map(x => ( x.innerText));";
            //JavascriptResponse response =  chromiumWebBrowser1.EvaluateScriptAsync(script);
            if (chromiumWebBrowser1.Address==null)
            {
                RJMessageBox.Show("اختر الصفحه ");
                return;
            }
            try
            {
                DialogResult result = RJMessageBox.Show("  هل انت متأكد من حفظ التعديلات   ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.No)
                    return;

                

                string script = @"var elems = document.querySelectorAll("".ClickSmart"");
                                [].forEach.call(elems, function(el) {
                                    el.classList.remove(""ClickSmart"");
                                });";
                chromiumWebBrowser1.ExecuteScriptAsync($"{script}");


                string html = "";
                chromiumWebBrowser1.ViewSource();
                chromiumWebBrowser1.GetSourceAsync().ContinueWith(taskHtml =>
                {
                    html = taskHtml.Result;
                    
                    try
                    {
                        StreamWriter sw = new StreamWriter(BaseFile);
                        sw.WriteLine(html);
                        sw.Close();
                    }
                    catch (Exception e)
                    {
                        MessageBox.Show("Exception:\n " + e.Message);
                    }
                    RJMessageBox.Show("تم حفظ التعديلات في الصفحة محليا ");
                });

                //Clear_Control();
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
            //ChromiumWebBrowser browserSender = (ChromiumWebBrowser)sender;
            //string destLocalPath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\ftp\\hsprof1\\hotspot2023\\login.html";
            //string html = await chromiumWebBrowser1.GetMainFrame().GetSourceAsync();
            ////Debug.WriteLine(html);
            //chromiumWebBrowser1.LoadHtml(html,destLocalPath);
        }


        private async Task getSource()
        {
            try
            {
                //
                string source = await chromiumWebBrowser1.GetBrowser().MainFrame.GetSourceAsync();
                //
                string f = Application.StartupPath + "\\currentSource.txt";
                //
                StreamWriter wr = new StreamWriter(f, false, System.Text.Encoding.Default);
                wr.Write(source);
                wr.Close();
                //
                System.Diagnostics.Process.Start(f);
                //
            }
            catch (Exception)
            {
                //Error !
            }
        }

        private void txt_innerText_onTextChanged(object sender, EventArgs e)
        {
            if (ChangFromLoad)
                return;

            if (string.IsNullOrEmpty(txt_innerText.Text.Trim()))
                return;

            if (lbl_type_tag.Text == "INPUT")
                chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].value = '{txt_innerText.Text}';");
            else
                //if()
                chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].textContent = '{txt_innerText.Text}';");
            //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].innerText = '{txt_innerText.Text}';");
        }

        private void addImage()
        {
            using (OpenFileDialog ofd = new OpenFileDialog() { Filter = "Image Files(*.jpg; *.jpeg; *.gif; *.bmp; *.png)|*.jpg; *.jpeg; *.gif; *.bmp; *.png", InitialDirectory = Path.GetDirectoryName(txt_path.Text) })
            {
                if (ofd.ShowDialog() == DialogResult.OK)
                {
                    try
                    {
                        pictureBox1.Image = new Bitmap(ofd.FileName);

                        //======================================
                        FileInfo file = new FileInfo(ofd.FileName);
                        string Image_New_Name = "copy_" + DateTime.Now.ToString("MMddhhmmss") + Path.GetExtension(file.Extension);
                        string PathCopy = System.IO.Path.Combine(Path.GetDirectoryName(txt_path.Text), Image_New_Name);

                        System.IO.File.Copy(txt_path.Text, PathCopy, true);
                        //string onPagePathCopy = System.IO.Path.Combine(Path.GetDirectoryName(src_img), Path.GetFileName(PathCopy));
                        //chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].src = '{onPagePathCopy}';");

                        //=================================================

                        System.IO.File.Copy(ofd.FileName, txt_path.Text, true);
                        //string onPagePath = System.IO.Path.Combine(Path.GetDirectoryName(src_img), Path.GetFileName(src_img));
                        //if (onPagePath.Contains("\\"))
                            //onPagePath = onPagePath.Replace("\\", "/");

                        chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].src = '{src_img}';");
                        chromiumWebBrowser1.LoadUrl(BaseFile);


                        string img_html_Dir = hp.html_directory + "/" + src_img;
                        Dictionary<string, string> dic = new Dictionary<string, string>();
                        dic["ServerUrl"] = img_html_Dir;
                        dic["LocalUrl"] = txt_path.Text;
                        ImageAddNew.Add(dic);
                    }
                    catch { }
                }
            }
        }
        private void btnAdd_Image_Click(object sender, EventArgs e)
        {
            try
            {
                if (string.IsNullOrEmpty(src_img) || string.IsNullOrEmpty(txt_path.Text))
                    return;

                addImage();
                return;

                string currentDirectory = "";
                if (txt_path.Text != "")
                    currentDirectory = Path.GetDirectoryName(txt_path.Text);

                //openFileDialog1.Filter = "jpeg|*.jpg|bmp|*.bmp|png|*.png|all files|*.*";
                openFileDialog1.Filter = "Image Files|*.jpg;*.jpeg;*.png;*.gif|all files|*.*";
                openFileDialog1.RestoreDirectory = true;
                openFileDialog1.InitialDirectory = currentDirectory;
                DialogResult res = openFileDialog1.ShowDialog();
                if (res == DialogResult.OK)
                {
                    FileInfo file = new FileInfo(openFileDialog1.FileName);
                    double sizeInBytes = file.Length;
                    try
                    {
                        string Image_New_Name = DateTime.Now.ToString("MMddhhmmss") + Path.GetExtension(file.Extension);
                        string onPagePath = System.IO.Path.Combine(Path.GetDirectoryName(src_img), Image_New_Name);
                        string destinationFile = System.IO.Path.Combine(currentDirectory, Image_New_Name);
                        System.IO.File.Copy(openFileDialog1.FileName, destinationFile, true);
                        try
                        {
                            pictureBox1.Image = new Bitmap(openFileDialog1.FileName);
                            txt_path.Text = destinationFile;
                            chromiumWebBrowser1.ExecuteScriptAsync($"document.getElementsByClassName('ClickSmart')[0].src = '{onPagePath}';");
                            string img_html_Dir = hp.html_directory + "/" + onPagePath;
                            Dictionary<string, string> dic = new Dictionary<string, string>();
                            dic["ServerUrl"] = img_html_Dir;
                            dic["LocalUrl"] = destinationFile;
                            ImageAddNew.Add(dic);
                            //ImageAddNew.Add(img_html_Dir);
                        }
                        catch { }
                    }
                    catch (Exception)
                    {
                        pictureBox1.Image = null;
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            Get_Cbox_Server_Profile_hotspot();
            Firstload = false;
            try
            {
                CBox_Server_Profile.SelectedIndex = 1;
            }
            catch { }
            try
            {
                CBox_all_Page_Html.SelectedIndex = 1;
                CBox_all_Page_Html.SelectedIndex = 0;
            }
            catch { }
        }

        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            chromiumWebBrowser1.LoadUrl(BaseFile);
            
            txt_CurrentAddress.Text = BaseFile;
            Clear_Control();

        }

        private void CBox_Server_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (Firstload)
                return;

            if (CBox_Server_Profile.SelectedValue == null)
                return;
            try
            {
                hp = (Hotspot_Server_Profile)CBox_Server_Profile.SelectedValue;
                string folderName = hp.Name + "\\" + Path.GetFileName(hp.html_directory);
                string destLocalPath = utils.Get_Ftp_Directory() + "\\" + "Pages" + "\\" + Global_Variable.Mk_resources.RB_SN + "\\" + folderName + "\\";
                BaseFile = destLocalPath + "\\login.html";
                BasePath = destLocalPath;

                ChangFromLoad = true;
                Clear_Control();

                if (CBox_all_Page_Html.SelectedIndex > 0)
                    CBox_all_Page_Html.SelectedIndex = 0;

                //if (CBox_all_Page_Html.SelectedIndex == 0)
                chromiumWebBrowser1.LoadUrl(BaseFile);
                //else if (CBox_all_Page_Html.SelectedIndex == 1)
                //    chromiumWebBrowser1.LoadUrl(destLocalPath + "\\status.html");
                //else if (CBox_all_Page_Html.SelectedIndex == 2)
                //    chromiumWebBrowser1.LoadUrl(destLocalPath + "\\logout.html");
                //else
                //    chromiumWebBrowser1.LoadUrl(destLocalPath);

                //BasePath = destLocalPath;
                //BaseFile = destLocalPath + "\\login.html";
                txt_CurrentAddress.Text = BaseFile;

                ChangFromLoad = false;

                //chromiumWebBrowser/1.LoadUrl(_BaseFolder);
                //txt_CurrentAddress.Text = BaseFile;
                //Clear_Control();

                //if (CBox_all_Page_Html.SelectedIndex > 0)
                //{
                //    Firstload = true;
                //    CBox_all_Page_Html.SelectedIndex = 0;
                //    Firstload = false;
                //}
            }
            catch { Firstload = false; }
        }
        //private void Check_Folder_found(Connections_Db conn = null)
        //{
        //    string folder = Directory.GetCurrentDirectory() + "\\db";
        //    try
        //    {
        //        if (!System.IO.Directory.Exists("db"))
        //            System.IO.Directory.CreateDirectory("db");
        //    }
        //    catch { }
        //    var files = new HashSet<string>(Directory.GetFiles(folder));

        //    var fullPath = System.IO.Path.Combine(folder, Global_Variable.Mk_Router.localDB_fileName);

        //    clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
        //    ff.create_default_db(conn);

        //    if (files.Contains(fullPath) == false)
        //    {
        //        //clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
        //        //string new_Connection_string = "Data Source=db\\" + conn.FileName + "; Version = 3; New = True; Compress = True;";
        //        ff.create_default_db(conn);
        //    }
        //}

        private void CBox_all_Page_Html_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (Firstload)
                return;

            if (CBox_all_Page_Html.SelectedIndex == 0)
                chromiumWebBrowser1.LoadUrl(BasePath + "\\login.html");
            else if (CBox_all_Page_Html.SelectedIndex == 1)
                chromiumWebBrowser1.LoadUrl(BasePath + "\\status.html");
            else if (CBox_all_Page_Html.SelectedIndex == 2)
                chromiumWebBrowser1.LoadUrl(BasePath + "\\logout.html");
            else
                chromiumWebBrowser1.LoadUrl(BasePath);

            Clear_Control();
            //txt_CurrentAddress.Text = BaseFile;

        }

        private void chromiumWebBrowser1_AddressChanged(object sender, AddressChangedEventArgs e)
        {
            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate ()
                {
                    this.txt_CurrentAddress.Text = e.Address;
                    BaseFile = trimStart(e.Address, @"file:///");
                    BasePath = Path.GetDirectoryName(BaseFile);
                });
            }
            catch { }
        }

        private void btn_GoBack_Click(object sender, EventArgs e)
        {
            //chromiumWebBrowser1.CanGoBack;
            chromiumWebBrowser1.Back();
            Clear_Control();
        }

        private void btn_Forward_Click(object sender, EventArgs e)
        {
            chromiumWebBrowser1.Forward();
            Clear_Control();
        }

        private void Clear_Control()
        {
            src_img = "";
            txt_path.Text = "";
            pictureBox1.Image = null;
            ImageAddNew.Clear();
            txt_innerText.Text = "";
            lbl_type_tag.Text = "";
        }
    }
}
public class MyCustomResourceRequestHandler : CefSharp.Handler.ResourceRequestHandler
{
    private readonly System.IO.MemoryStream memoryStream = new System.IO.MemoryStream();

    protected override IResponseFilter GetResourceResponseFilter(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response)
    {
        return new CefSharp.ResponseFilter.StreamResponseFilter(memoryStream);
    }

    protected override void OnResourceLoadComplete(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, IResponse response, UrlRequestStatus status, long receivedContentLength)
    {
        //You can now get the data from the stream
        var bytes = memoryStream.ToArray();

        if (response.Charset == "utf-8")
        {
            var str = System.Text.Encoding.UTF8.GetString(bytes);
            Console.WriteLine("In OnResourceLoadComplete : " + str.Substring(0, 10) + " <...>");
        }
        else
        {
            //Deal with different encoding here
        }
    }
}

public class MyBasicRequestHandler : CefSharp.Handler.RequestHandler
{
    protected override IResourceRequestHandler GetResourceRequestHandler(IWebBrowser chromiumWebBrowser, IBrowser browser, IFrame frame, IRequest request, bool isNavigation, bool isDownload, string requestInitiator, ref bool disableDefaultHandling)
    {
        Console.WriteLine("In GetResourceRequestHandler : " + request.Url);
        //Only intercept specific Url's
        if (request.Url == "http://cefsharp.github.io/" || request.Url == "https://cefsharp.github.io/")
        {
            return new MyCustomResourceRequestHandler();
        }
        //Default behaviour, url will be loaded normally.
        return null;
    }

 

}
