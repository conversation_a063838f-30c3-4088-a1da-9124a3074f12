using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نسخة آمنة جداً من RJTabControl - بدون أي تعقيدات
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class UltraSafeRJTabControl : Panel
    {
        #region Fields - أساسية فقط
        private List<RJTabPage> _tabs;
        private RJPanel _tabsPanel;
        private RJPanel _contentPanel;
        private RJTabPage _activeTab;
        private int _tabHeight = 35;
        private int _tabSpacing = 2;
        private int _tabPadding = 15;
        private bool _defaultTabAdded = false; // حماية من إضافة تاب افتراضي مضاعف
        #endregion

        #region Constructor - مبسط جداً
        public UltraSafeRJTabControl()
        {
            try
            {
                // تهيئة فورية وآمنة
                _tabs = new List<RJTabPage>();
                
                // إنشاء المكونات
                InitializeComponents();
                
                // تاب افتراضي للـ Designer - مرة واحدة فقط
                if (DesignMode && !_defaultTabAdded)
                {
                    AddDefaultTabSafely();
                    _defaultTabAdded = true;
                }
            }
            catch
            {
                // تجاهل جميع الأخطاء في Constructor
            }
        }
        #endregion

        #region Properties - آمنة تماماً
        /// <summary>
        /// ارتفاع التابات
        /// </summary>
        [Category("Ultra Safe Tab Control")]
        [Description("Height of tabs")]
        [DefaultValue(35)]
        public int TabHeight
        {
            get { return _tabHeight; }
            set
            {
                try
                {
                    _tabHeight = value;
                    if (_tabsPanel != null)
                    {
                        _tabsPanel.Height = _tabHeight;
                        ArrangeTabsSafely();
                    }
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// المسافة بين التابات
        /// </summary>
        [Category("Ultra Safe Tab Control")]
        [Description("Spacing between tabs")]
        [DefaultValue(2)]
        public int TabSpacing
        {
            get { return _tabSpacing; }
            set
            {
                try
                {
                    _tabSpacing = value;
                    ArrangeTabsSafely();
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// الحشو الداخلي للتابات
        /// </summary>
        [Category("Ultra Safe Tab Control")]
        [Description("Padding inside tabs")]
        [DefaultValue(15)]
        public int TabPadding
        {
            get { return _tabPadding; }
            set
            {
                try
                {
                    _tabPadding = value;
                    ArrangeTabsSafely();
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// عدد التابات - للقراءة فقط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int TabCount
        {
            get 
            { 
                try
                {
                    return _tabs?.Count ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// فهرس التاب النشط
        /// </summary>
        [Category("Ultra Safe Tab Control")]
        [Description("Index of selected tab")]
        [DefaultValue(-1)]
        public int SelectedIndex
        {
            get 
            { 
                try
                {
                    if (_tabs == null || _activeTab == null) return -1;
                    return _tabs.IndexOf(_activeTab);
                }
                catch
                {
                    return -1;
                }
            }
            set
            {
                try
                {
                    if (_tabs == null || value < 0 || value >= _tabs.Count) return;
                    ActivateTabSafely(_tabs[value]);
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// التاب النشط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public RJTabPage SelectedTab
        {
            get 
            { 
                try
                {
                    return _activeTab;
                }
                catch
                {
                    return null;
                }
            }
            set 
            { 
                try
                {
                    ActivateTabSafely(value);
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }
        #endregion

        #region Methods - آمنة تماماً
        /// <summary>
        /// تهيئة المكونات بطريقة آمنة
        /// </summary>
        private void InitializeComponents()
        {
            try
            {
                this.SuspendLayout();

                // إنشاء panel التابات
                _tabsPanel = new RJPanel();
                _tabsPanel.Dock = DockStyle.Top;
                _tabsPanel.Height = _tabHeight;
                _tabsPanel.BackColor = Color.FromArgb(55, 55, 58);
                _tabsPanel.BorderSize = 0;

                // إنشاء panel المحتوى
                _contentPanel = new RJPanel();
                _contentPanel.Dock = DockStyle.Fill;
                _contentPanel.BackColor = Color.FromArgb(37, 37, 38);
                _contentPanel.BorderSize = 1;
                _contentPanel.BorderColor = Color.FromArgb(0, 122, 204);

                // إعدادات الكنترول الرئيسي
                this.BackColor = Color.FromArgb(45, 45, 48);
                this.Size = new Size(400, 300);

                // إضافة المكونات
                this.Controls.Add(_contentPanel);
                this.Controls.Add(_tabsPanel);

                this.ResumeLayout(false);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إضافة تاب افتراضي بطريقة آمنة - مرة واحدة فقط
        /// </summary>
        private void AddDefaultTabSafely()
        {
            try
            {
                // فحص مضاعف للتأكد من عدم وجود تابات
                if (_tabsPanel == null || _contentPanel == null || _tabs == null) return;
                if (_tabs.Count > 0) return; // إذا كان هناك تابات بالفعل، لا تضيف

                var defaultTab = new RJTabPage("TabPage1");
                defaultTab.BackColor = Color.FromArgb(0, 122, 204);
                defaultTab.ForeColor = Color.White;
                defaultTab.Height = _tabHeight - 4;
                defaultTab.Click += TabClickHandler;

                // إضافة للقائمة
                _tabs.Add(defaultTab);

                // إضافة للواجهة
                _tabsPanel.Controls.Add(defaultTab);
                _contentPanel.Controls.Add(defaultTab.ContentPanel);

                // ترتيب وتفعيل
                ArrangeTabsSafely();
                ActivateTabSafely(defaultTab);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إضافة تاب جديد بطريقة آمنة
        /// </summary>
        public RJTabPage AddTab(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text) || _tabsPanel == null || _contentPanel == null)
                    return null;

                var tab = new RJTabPage(text);
                tab.BackColor = Color.FromArgb(70, 70, 70);
                tab.ForeColor = Color.White;
                tab.Height = _tabHeight - 4;
                tab.Click += TabClickHandler;

                // إضافة للقائمة
                _tabs.Add(tab);

                // إضافة للواجهة
                _tabsPanel.Controls.Add(tab);
                _contentPanel.Controls.Add(tab.ContentPanel);

                // ترتيب وتفعيل
                ArrangeTabsSafely();
                if (_tabs.Count == 1)
                    ActivateTabSafely(tab);

                return tab;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تفعيل تاب بطريقة آمنة
        /// </summary>
        private void ActivateTabSafely(RJTabPage tab)
        {
            try
            {
                if (tab == null || _tabs == null || !_tabs.Contains(tab)) return;

                // إلغاء تفعيل التاب السابق
                if (_activeTab != null)
                {
                    _activeTab.BackColor = Color.FromArgb(70, 70, 70);
                    if (_activeTab.ContentPanel != null)
                        _activeTab.ContentPanel.Visible = false;
                }

                // تفعيل التاب الجديد
                _activeTab = tab;
                _activeTab.BackColor = Color.FromArgb(0, 122, 204);
                if (_activeTab.ContentPanel != null)
                {
                    _activeTab.ContentPanel.Visible = true;
                    _activeTab.ContentPanel.BringToFront();
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// ترتيب التابات بطريقة آمنة
        /// </summary>
        private void ArrangeTabsSafely()
        {
            try
            {
                if (_tabs == null || _tabs.Count == 0) return;

                int x = _tabSpacing;
                foreach (var tab in _tabs)
                {
                    if (tab == null) continue;

                    // حساب عرض التاب
                    int tabWidth = CalculateTabWidthSafely(tab);

                    // تحديد موقع التاب
                    tab.Location = new Point(x, 2);
                    tab.Size = new Size(tabWidth, _tabHeight - 4);

                    x += tabWidth + _tabSpacing;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// حساب عرض التاب بطريقة آمنة
        /// </summary>
        private int CalculateTabWidthSafely(RJTabPage tab)
        {
            try
            {
                if (tab == null) return 100;

                using (Graphics g = this.CreateGraphics())
                {
                    SizeF textSize = g.MeasureString(tab.Text, tab.Font);
                    return (int)textSize.Width + (_tabPadding * 2) + 10;
                }
            }
            catch
            {
                return 100; // عرض افتراضي
            }
        }

        /// <summary>
        /// معالج النقر على التاب
        /// </summary>
        private void TabClickHandler(object sender, EventArgs e)
        {
            try
            {
                if (sender is RJTabPage tab)
                {
                    ActivateTabSafely(tab);
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// مسح جميع التابات - مفيد لإعادة التعيين
        /// </summary>
        public void ClearAllTabs()
        {
            try
            {
                if (_tabs == null) return;

                // إزالة من الواجهة
                if (_tabsPanel != null)
                    _tabsPanel.Controls.Clear();
                if (_contentPanel != null)
                    _contentPanel.Controls.Clear();

                // مسح القائمة
                _tabs.Clear();
                _activeTab = null;
                _defaultTabAdded = false; // إعادة تعيين العلامة
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion

        #region Override Methods - آمنة
        protected override void OnHandleCreated(EventArgs e)
        {
            try
            {
                base.OnHandleCreated(e);

                // إضافة تاب افتراضي إذا لم يكن موجود وكنا في DesignMode
                if (DesignMode && !_defaultTabAdded && (_tabs == null || _tabs.Count == 0))
                {
                    AddDefaultTabSafely();
                    _defaultTabAdded = true;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // تنظيف الموارد
                    if (_tabs != null)
                    {
                        foreach (var tab in _tabs)
                        {
                            if (tab != null)
                                tab.Click -= TabClickHandler;
                        }
                        _tabs.Clear();
                    }
                }
                base.Dispose(disposing);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion
    }
}
