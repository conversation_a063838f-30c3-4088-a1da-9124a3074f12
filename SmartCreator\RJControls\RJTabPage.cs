using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// صفحة تاب ترث من RJButton - كل تاب هو RJButton!
    /// جميع ميزات RJButton متاحة تلقائياً
    /// </summary>
    [ToolboxItem(false)]
    public class RJTabPage : RJButton
    {
        #region Fields
        private Control contentPanel;
        private bool isActive = false;
        private bool canClose = true;
        private string tooltip = "";
        #endregion

        #region Constructor
        public RJTabPage() : base()
        {
            // إعداد RJButton كتاب
            this.Dock = DockStyle.None;
            this.FlatStyle = FlatStyle.Flat;
            this.FlatAppearance.BorderSize = 0;
            
            // إنشاء Panel للمحتوى
            contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Visible = false
            };
        }

        public RJTabPage(string text) : this()
        {
            this.Text = text;
        }

        public RJTabPage(string text, IconChar icon) : this(text)
        {
            this.IconChar = icon;
        }
        #endregion

        #region Properties
        /// <summary>
        /// Panel المحتوى - هنا يتم إضافة العناصر
        /// </summary>
        [Browsable(false)]
        public Control ContentPanel
        {
            get { return contentPanel; }
        }

        /// <summary>
        /// هل التاب نشط؟
        /// </summary>
        [Browsable(false)]
        public bool IsActive
        {
            get { return isActive; }
            set
            {
                if (isActive != value)
                {
                    isActive = value;
                    UpdateActiveState();
                }
            }
        }

        /// <summary>
        /// هل يمكن إغلاق التاب؟
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether the tab can be closed")]
        [DefaultValue(true)]
        public bool CanClose
        {
            get { return canClose; }
            set { canClose = value; }
        }

        /// <summary>
        /// نص التلميح
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tooltip text")]
        [DefaultValue("")]
        public string Tooltip
        {
            get { return tooltip; }
            set { tooltip = value; }
        }

        /// <summary>
        /// إضافة عنصر للتاب
        /// </summary>
        public void AddControl(Control control)
        {
            contentPanel.Controls.Add(control);
        }

        /// <summary>
        /// إزالة عنصر من التاب
        /// </summary>
        public void RemoveControl(Control control)
        {
            contentPanel.Controls.Remove(control);
        }

        /// <summary>
        /// مسح جميع العناصر
        /// </summary>
        public void ClearControls()
        {
            contentPanel.Controls.Clear();
        }
        #endregion

        #region Events
        /// <summary>
        /// حدث طلب إغلاق التاب
        /// </summary>
        public event EventHandler<TabCloseEventArgs> TabClosing;

        /// <summary>
        /// حدث إغلاق التاب
        /// </summary>
        public event EventHandler TabClosed;

        /// <summary>
        /// حدث تفعيل التاب
        /// </summary>
        public event EventHandler TabActivated;

        /// <summary>
        /// حدث إلغاء تفعيل التاب
        /// </summary>
        public event EventHandler TabDeactivated;
        #endregion

        #region Methods
        /// <summary>
        /// تحديث حالة التاب (نشط/غير نشط)
        /// </summary>
        private void UpdateActiveState()
        {
            if (isActive)
            {
                // التاب النشط - Solid Style
                this.Style = ControlStyle.Solid;
                contentPanel.Visible = true;
                contentPanel.BringToFront();
                TabActivated?.Invoke(this, EventArgs.Empty);
            }
            else
            {
                // التاب غير النشط - Glass Style
                this.Style = ControlStyle.Glass;
                contentPanel.Visible = false;
                TabDeactivated?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// إغلاق التاب
        /// </summary>
        public void Close()
        {
            if (!canClose) return;

            var args = new TabCloseEventArgs(this);
            TabClosing?.Invoke(this, args);

            if (!args.Cancel)
            {
                // إخفاء المحتوى
                contentPanel.Visible = false;
                
                // إزالة من الوالد
                if (Parent != null)
                {
                    Parent.Controls.Remove(this);
                    Parent.Controls.Remove(contentPanel);
                }

                TabClosed?.Invoke(this, EventArgs.Empty);
            }
        }

        /// <summary>
        /// تفعيل التاب
        /// </summary>
        public void Activate()
        {
            IsActive = true;
        }

        /// <summary>
        /// إلغاء تفعيل التاب
        /// </summary>
        public void Deactivate()
        {
            IsActive = false;
        }

        /// <summary>
        /// عرض المحتوى
        /// </summary>
        public void ShowContent()
        {
            contentPanel.Visible = true;
            contentPanel.BringToFront();
        }

        /// <summary>
        /// إخفاء المحتوى
        /// </summary>
        public void HideContent()
        {
            contentPanel.Visible = false;
        }
        #endregion

        #region Overrides
        protected override void OnClick(EventArgs e)
        {
            base.OnClick(e);
            
            // تفعيل التاب عند النقر
            if (Parent is RJTabControl tabControl)
            {
                tabControl.ActivateTab(this);
            }
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                contentPanel?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }

    /// <summary>
    /// معاملات حدث إغلاق التاب
    /// </summary>
    public class TabCloseEventArgs : EventArgs
    {
        public RJTabPage TabPage { get; }
        public bool Cancel { get; set; }

        public TabCloseEventArgs(RJTabPage tabPage)
        {
            TabPage = tabPage;
            Cancel = false;
        }
    }
}
