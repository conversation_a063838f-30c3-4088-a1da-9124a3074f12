﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using Newtonsoft.Json;
using SmartCreator.Forms.UserManager;
using SmartCreator.RJControls;
using System.Threading;
using SmartCreator.Utils;
using DevComponents.DotNetBar.Controls;

namespace SmartCreator.Forms.BatchCards
{
    public partial class Form_import_batch_cards_UM : RJChildForm
    {
        private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA = null;
        private FormAddUsersManager frm;
        private Form_PrintUserManagerState Frm_State ;
        private UserManagerProcess ump ;
        private Clss_InfoPrint clss_InfoPrint;


        public Form_import_batch_cards_UM()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv_freq.RightToLeft = RightToLeft.No;
            }
            this.Text = "استيراد الكروت من ملف خارجي الي اليوزمنجر";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Import  Cards From Out to UserManager";
            }

            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel4.Customizable = false;
                pnl_customer.Customizable = false;
                pnl_attrbut.Customizable = false;
            }
            if (UIAppearance.Theme == UITheme.Light)
                lbl_DgvNot.ForeColor = Color.Red;
            //sideMenu();


            set_fonts();
            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);


            dgv.Width = panel1.Width - 5;
            dgv_freq.Visible = false;
            dgv.Refresh();

        }
        private void set_fonts()
        {
            Font title_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 12 , FontStyle.Regular);
            rjLabel25Title.Font = title_font;

            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 35;

            dgv_freq.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv_freq.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_freq.ColumnHeadersHeight = 35;


            Font lbl1 = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            checkBox_RegisterAsBatch.Font = lbl_Attribute.Font = lbl_Co1.Font = lbl_Co2.Font
            = lbl_Customer.Font = lbl_Profile.Font = lbl_TemplateCards.Font = lbl_OpenAfterPrint.Font
            = lbl_SellingPoint.Font = lbl_ShardUser.Font = lbl_DgvNot.Font = lbl_count.Font
            = lbl_Save_PDF.Font = checkBox_note.Font = checkBox_RegisterAs_LastBatch.Font = checkBox_RegisterAsBatch.Font
            = lbl1;

            Font lbl2 = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Bold);
            rjLabel4.Font = rjLabel3.Font =  
                  lbl2;
            cb_col4.Font=cb_col1.Font= cb_col2.Font =cb_col3.Font= Program.GetCustomFont(Resources.DroidSansArabic, 8f , FontStyle.Regular);

            //lbl_DgvNot.ForeColor = Color.Red;
            btnAdd.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 9f  , FontStyle.Bold);

            //utils.Control_textSize(pnlClientArea);
            

            //Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> p = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile s in p)
                    comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

                CBox_Profile.DataSource = new BindingSource(comboSource, null);
                CBox_Profile.DisplayMember = "Value";
                CBox_Profile.ValueMember = "Key";
            }
            catch { }
        }

        private void Get_SellingPoint()
        {
            Smart_DataAccess da = new Smart_DataAccess();
            CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";
        }

        private void Get_UMCustomer()
        {
            try
            {
                CBox_CustomerUserMan.DataSource = Global_Variable.UM_Customer;
                CBox_CustomerUserMan.DisplayMember = "Name";
                CBox_CustomerUserMan.ValueMember = "Name";

            }
            catch { }

        }

        private void Get_TemplateCardsFromDB()
        {
            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
            }
            catch { }
        }

        [Obsolete]
        private void btnAdd_Click(object sender, EventArgs e)
        {
            //FormAddUsersManager frm = new FormAddUsersManager();
            //frm.ShowDialog();

            //Global_Variable.StartThreadProcessFromMK = false;

            txt_searchBySales.Text = string.Empty;
            if (dgv.Rows.Count <= 0)
            {
                RJMessageBox.Show("لا يوجد كروت  للاضافة");
                return;
            }
            if(CBox_Profile.Text=="")
            {
                RJMessageBox.Show("اختار باقة افتراضية للكروت");
                return;
            }
            if (CBox_TemplateCards.Text == "")
            {
                RJMessageBox.Show("اختار قالب افتراضية للكروت");
                return;
            }

            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }

            DialogResult result = RJMessageBox.Show("هل متاكد من اضافة  الكروت الي الروتر", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            btnAdd.Enabled = false;
            Thread thread = new Thread(AddUserUserd_batch_cards);
            //Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();


            //AddUserUserd_batch_cards();

        }

        private New_Generate_Cards new_Generate_Cards;
        private string strProfile = "";
        private static int inext = 0;
        private string Public_file_Name = "";
        private string pathfile = "";


        private bool init_Virable()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormUserManagerPrint");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());
            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            clss_InfoPrint = get_data_from_interface2();
            
            ump = new UserManagerProcess();
            ump.clss_InfoPrint = clss_InfoPrint;
            ump.Frm_State = Frm_State;
            frm = new FormAddUsersManager();
            ump.frm= frm;

            if (clss_InfoPrint.Save_To_PDF)
            {
                ump.frm.txtNumberCard.Text = clss_InfoPrint.Number_Cards_ToAdd.ToString();
                ump.frm.CBox_Profile.Text = clss_InfoPrint.profile.Name;
                if (ump.init_file_pdf() == false)
                    return false;

            }



            return true;
        }
        [Obsolete]
        public void AddUserUserd_batch_cards()
        {
            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                    (MethodInvoker)delegate ()
                    {
                        dgv.Width = panel1.Width - 5;
                        dgv_freq.Visible = false;
                  

                if (init_Virable() == false)
                {
                    Global_Variable.StartThreadProcessFromMK = false;
                            btnAdd.Enabled = true;
                    return;
                }
                    });
                Global_Variable.StartThreadProcessFromMK = true;

                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

                string mesgBtn = "يتم الان اضافة الكروت الى اليوزرمنجر";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + clss_InfoPrint.Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

                // ====== فحص وتجميع الكروت من الجدول الكروت العشوائية ============================
                //CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
                
                new_Generate_Cards = Generate_Cards(clss_InfoPrint.Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    //startPrint = false;
                    return;
                }
                ump.new_Generate_Cards = new_Generate_Cards;

                //========= تجهيز سكربت الاضافة الي المايكروتك =================
                Dictionary<string, string> variableScript = ump.Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
                //=========== فحص اذا طريقة الاضافة سكربت واحد لليوزر والبروفايل او فصل سكربت اضافة البروفايل وحده =========
                //bool _check_if_use_2Scritp_add = ump.check_if_use_2Scritp_add();

                //======= الاضافة الي المايكروتك ========================

                Dictionary<string, string> res = null;
                //if (_check_if_use_2Scritp_add == true)
                //    return;
                //FormAddUsersManager fr = new FormAddUsersManager();
                //ump.Frm_State = Frm_State;
                
                ump.frm.check_port_mk_befor();
                res = ump.GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
                ump.frm.rest_port_mk_after();

                if (res["status"] == "false")
                {
                    //startPrint = false;
                    Global_Variable.StartThreadProcessFromMK = false;
                    btnAdd.Enabled = true;
                    return;
                }

                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                //string path = @"tempCards\script\batch\";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch\\";
                string path_SmartErorrCards =  path + "SmartErorrCards.rsc";
                string path_SmartErorrProfile =  path + "SmartErorrProfile.rsc";
                var Users_lines = File.ReadAllLines(path_SmartErorrCards);
                List<string> user_erorr = new List<string>();
                for (var i = 0; i < Users_lines.Length; i += 1)
                {
                    var line = Users_lines[i];
                    user_erorr.Add(line.Trim());
                }
                var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
                List<string> profile_erorr = new List<string>();

                for (var i = 0; i < Profiles_lines.Length; i += 1)
                {
                    var line = Profiles_lines[i];
                    profile_erorr.Add(line.Trim());
                }

                string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
                string[] profile_split = profile_erorr.ToArray();

                //if (user_split.Length > 0)
                //{
                //    for (int i = 0; i < user_split.Length; i++)
                //        new_Generate_Cards.dicUser.Remove(user_split[i]);
                //    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                //    CountTry = 5;
                //    new_Generate_Cards.dicUser = ump.GenerateIfLastErorr_batch_cards(new_Generate_Cards.dicUser, user_split.Length, cLS_Genrate_Cards);
                //}
                //====== check if error add profile   or  _check_if_use_2Scritp_add ================== 
                if (profile_split.Length > 1)
                {
                    ump.AddProfile_ErorreCards(profile_split.ToList());
                }


                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");

                List<UmUser> dbUser = ump.add_sn_to_local_dbUser(new_Generate_Cards);
                ump.add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    ump.print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {

                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { ump.Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    ump.Add_to_NumberPrint_cards_toDB(dbUser);
                }

                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { ump.add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.SaveTo_excel) { }
                if (clss_InfoPrint.SaveTo_script_File) { }
                if (clss_InfoPrint.SaveTo_text_File) { }

                try { File.Delete(path_SmartErorrCards); } catch { }
                try { File.Delete(path_SmartErorrProfile); } catch { }

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                            (MethodInvoker)delegate ()
                            {
                               //dgv.Rows.Clear();
                            });


                //==== refresh datagridview batch Number
                //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                //(System.Windows.Forms.MethodInvoker)delegate ()
                //{
                //    frm.LoadDatagridviewData();
                //});

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الطباعة بنجاح");

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\n" /*+ ex.ToString()*/); 
                //is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                Global_Variable.StartThreadProcessFromMK = false;
                btnAdd.Enabled = true;
                //startPrint = false;
            }
            //startPrint = false;
            Global_Variable.StartThreadProcessFromMK = false;
            btnAdd.Enabled = true;
        }
        string Public_Profile_Name = "";
        string Public_Custumer_UserMan = "";
        string Public_SellingPoint_Value = "";


        public New_Generate_Cards Generate_Cards(int Number_Cards_ToAdd, string mesgBtn = "يتم الاضافة", bool useProgressBar = true)
        {
            HashSet<string> card_copy = new HashSet<string>();

            if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
            {
                //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                //{
                //    card_copy = db.ColumnDistinct<string>(db.From<UmUser>().Where(x => x.DeleteFromServer == 0).Select(x => x.UserName));
                //    //card_copy = Local_DA.Load<string>("select Distinct UserName from UmUser where DeleteFromServer=0 ");
                //}
                card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));
            }
            else
            {
                if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                    card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
            }
            if (clss_InfoPrint.With_Archive_uniqe)
            {
                //=========  get cards from archive  and copy  to hashset card_copy
                //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
            }

            HashSet<string> card_freq = new HashSet<string>();
            New_Generate_Cards new_Generate_Cards;
            string strUser = ""; string strPass = "";
            Dictionary<string, NewUserToAdd> dicUser = new Dictionary<string, NewUserToAdd>();


            new_Generate_Cards = new New_Generate_Cards();
            try
            {
                DataTable dt_card_freq = new DataTable();
                dt_card_freq.Columns.Add("الاسم", typeof(string));
                dt_card_freq.Columns.Add("index", typeof(int));

                for (int i = 0; i < clss_InfoPrint.Number_Cards_ToAdd; i++)
                {
                    string username = dgv.Rows[i].Cells["الاسم"].Value.ToString().Trim();
                    string Password = dgv.Rows[i].Cells["كلمة المرور"].Value.ToString().Trim();
                    string profile = dgv.Rows[i].Cells["الباقة"].Value.ToString().Trim();
                    string sp = dgv.Rows[i].Cells["نقطة البيع"].Value.ToString().Trim();

                    if (username.Trim() == "") { continue; }
                    if (profile.Trim() == "") profile = Public_Profile_Name;
                    if (sp.Trim() == "") sp = Public_SellingPoint_Value;
                    try
                    {
                        if (card_copy.Add(username) == false)
                        {
                            card_freq.Add(username);
                            DataRow r = dt_card_freq.NewRow();
                            r[0] = username;
                            r[1] = i;
                            dt_card_freq.Rows.Add(r);
                            continue;
                        }
                    }
                    catch
                    {
                        card_freq.Add(username);
                        continue;

                    }


                    NewUserToAdd NewUser_Generate = new NewUserToAdd
                    {
                        Name = username,
                        Password = Password
                    };

                    dicUser.Add(NewUser_Generate.Name, NewUser_Generate);
                    strUser += "\"" + NewUser_Generate.Name + "\"" + ",";
                    strPass += "\"" + NewUser_Generate.Password + "\"" + ",";

                    //if (useProgressBar)
                    //Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Number_Cards_ToAdd + "  /  " + (i + 1) + " )", mesgBtn);
                }

                if (card_freq.Count > 0)
                {
                    string message = "هناك بعض الكروت مكرره موجوده مسبقا في الروتر \n هل تريد  عرض الكروت في جدول جانبي ومعالجتها يدويا \n اذا ضغطت لا سيتم تجاهل الكروت المكرروه وسيتم اضافة البقية";
                    DialogResult result = RJMessageBox.Show(message, "تحذير", MessageBoxButtons.YesNoCancel);

                    if (result == DialogResult.Yes)
                    {

                        Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                            (MethodInvoker)delegate ()
                            {
                                dgv_freq.DataSource = dt_card_freq;
                                dgv_freq.Columns["index"].Visible = false;
                                dgv_freq.Visible = true;
                                dgv.Width = panel1.Width - dgv_freq.Width - 15;
                            });

                        Global_Variable.StartThreadProcessFromMK = false;
                        return null;
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        Global_Variable.StartThreadProcessFromMK = false;
                        return null;
                    }
                    //========= حذف المكرره
                    int cont = 0;
                    for (int i = 0; i < dt_card_freq.Rows.Count; i++)
                    {
                        Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                            (MethodInvoker)delegate ()
                            {
                                dgv.Rows.RemoveAt(Convert.ToInt32(dt_card_freq.Rows[0]["index"].ToString()));
                                cont = dgv.Rows.Count;
                            });
                    }

                    if (cont == 0)
                    {
                        RJMessageBox.Show("لا يوجد كروت للاضافة قد تكون جميع الكروت مكرره");
                        Global_Variable.StartThreadProcessFromMK = false;
                        return null;
                    }
                    clss_InfoPrint.Number_Cards_ToAdd = cont;




                }
                new_Generate_Cards.status = true;
                new_Generate_Cards.strUser = strUser;
                new_Generate_Cards.strPass = strPass;
                new_Generate_Cards.dicUser = dicUser;

            }
            catch (Exception ex)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                RJMessageBox.Show("Generate_Cards\n" + ex.Message);
                return null;
            }
            return new_Generate_Cards;
        }

        private Clss_InfoPrint get_data_from_interface2()
        {
            
            //======================


            clss_InfoPrint = new Clss_InfoPrint();
            clss_InfoPrint.is_add_batch_cards = true;
            clss_InfoPrint.is_add_batch_cards_to_Archive = false;
            clss_InfoPrint.is_Add_One_Card = false;
            clss_InfoPrint.Number_Cards_ToAdd = dgv.Rows.Count;

            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile.SelectedValue.ToString());
            clss_InfoPrint.profile = profile;
            int BatchNumber = 0;
            int NumberPrint = 0;
            if (checkBox_RegisterAsBatch.Checked)
            {
                BatchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence("BatchCards");
                BatchNumber += 1;
                clss_InfoPrint.BatchNumber = BatchNumber;
            }
            if (checkBox_RegisterAs_LastBatch.Checked)
            {
                BatchNumber = Convert.ToInt32(txt_last_batchNumber.Text);
                clss_InfoPrint.BatchNumber = BatchNumber;
                clss_InfoPrint.is_RegisterAs_LastBatch = true;

            }

            NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint");
            NumberPrint += 1;
            clss_InfoPrint.NumberPrint = NumberPrint;



            //int NumberPrint = 0;
            //if (checkBox_RegisterAsBatch.Checked)
            //{
            //    //NumberPrint = SqlDataAccess.Get_lastID_Batch_cards();
            //    NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence();
            //    NumberPrint += 1;
            //    clss_InfoPrint.NumberPrint = NumberPrint;
            //}
            //if (checkBox_RegisterAs_LastBatch.Checked)
            //{
            //    NumberPrint = Convert.ToInt32(txt_last_batchNumber.Text);
            //    clss_InfoPrint.NumberPrint = NumberPrint;
            //    clss_InfoPrint.is_RegisterAs_LastBatch = true;

            //}
            if (Global_Variable.Mk_resources.version >= 7)
            {
                if (CBox_Attribute.Text != "" && CBox_Attribute.Text != "")
                {
                    clss_InfoPrint.is_use_Attribut = true;
                    clss_InfoPrint.Attribut = CBox_Attribute.SelectedItem.ToString() + ":" + txt_attribute.Text.Trim();
                }
            }

            if (CBox_group.Text != "")
                clss_InfoPrint.Group = CBox_group.Text.ToString();

            clss_InfoPrint.Selected_template_item = CBox_TemplateCards.SelectedIndex;
            //clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(txtNumberCard.Text);
            //clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(frm.txtNumberCard.Text);
            clss_InfoPrint.Profile_Name = CBox_Profile.SelectedValue.ToString();
            //clss_InfoPrint.Mode_User_NumberORcharcter = frm.cbox_User_NumberORcharcter.SelectedItem.ToString();
            //clss_InfoPrint.Mode_User_NumberORcharcter_Value = frm.cbox_User_NumberORcharcter.SelectedIndex;
            //clss_InfoPrint.User_Long = (Convert.ToInt32(frm.txt_longUsers.Text));
            //clss_InfoPrint.Mode_Password_NumberORcharcter = frm.cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            //clss_InfoPrint.Mode_Password_NumberORcharcter_Value = frm.cbox_Pass_NumberORcharcter.SelectedIndex;
            //clss_InfoPrint.Password_Long = Convert.ToInt32(frm.txt_longPassword.Text);
            //clss_InfoPrint.UserPassword_Pattern = frm.cbox_UserPassword_Pattern.SelectedIndex;
            clss_InfoPrint.SellingPoint_Name = CBox_SellingPoint.Text;
            if (CBox_SellingPoint.Text != "")
            {
                try
                {
                    clss_InfoPrint.SellingPoint_Name = CBox_SellingPoint.Text;
                    clss_InfoPrint.SellingPoint_Value = CBox_SellingPoint.SelectedValue.ToString();
                    //clss_InfoPrint.SellingPoint_Value = (int?)frm.CBox_SellingPoint.SelectedValue;
                    clss_InfoPrint.SellingPoint = Smart_DA.LoadSingleByNullId<SellingPoint>((int?)CBox_SellingPoint.SelectedValue, "SellingPoint");
                    //clss_InfoPrint.SellingPoint = Smart_DA.Get_Any_byId<SellingPoint>((int?)CBox_SellingPoint.SelectedValue);
                    clss_InfoPrint.SellingPoint_Value_str = CBox_SellingPoint.SelectedValue.ToString();
                }
                catch { }
            }
            //clss_InfoPrint.StartCard = frm.txt_StartCard.Text.Trim();
            //clss_InfoPrint.EndCard = frm.txt_EndCard.Text.Trim();
            clss_InfoPrint.ShardUser = txt_ShardUser.Text.Trim();
            clss_InfoPrint.FirstUse = checkBoxFirstUse.Checked;
            clss_InfoPrint.is_comment = checkBox_note.Checked;
            clss_InfoPrint.pathfile = Frm_State.path_saved_file;

            //ump.frm.checkBox_note = checkBox_note.Checked;
            if (checkBox_note.Checked)
                clss_InfoPrint.Comment = txt_note.Text.Trim().ToString();

            if (Global_Variable.Mk_resources.version <= 6)
                clss_InfoPrint.Custumer_UserMan = CBox_CustomerUserMan.Text.ToString();

            clss_InfoPrint.Save_To_PDF = checkBoxSaveTo_PDF.Checked;
            clss_InfoPrint.Open_PDF_file = checkBoxOpenAfterPrint.Checked;
            //clss_InfoPrint.SaveTo_excel = checkBoxSaveTo_excel.Checked;
            //clss_InfoPrint.SaveTo_script_File = frm.checkBoxSaveTo_script_File.Checked;
            //clss_InfoPrint.SaveTo_text_File = frm.checkBoxSaveTo_text_File.Checked;
            clss_InfoPrint.RegisterAsBatch = checkBox_RegisterAsBatch.Checked;
            clss_InfoPrint.RegisterAs_LasBatch = checkBox_RegisterAs_LastBatch.Checked;
            //clss_InfoPrint.With_Archive_uniqe = checkBox_With_Archive_uniqe.Checked;
            clss_InfoPrint.TemplateId = CBox_TemplateCards.SelectedValue.ToString();
            clss_InfoPrint.TemplateName = CBox_TemplateCards.Text.ToString();



           
            return clss_InfoPrint;
        }

        private DataTable DataTableImport()
        {
            DataTable table = new DataTable();
            table.Columns.Add("الاسم", typeof(string));
            table.Columns.Add("كلمة المرور", typeof(string));
            table.Columns.Add("الباقة", typeof(string));
            table.Columns.Add("نقطة البيع", typeof(string));

            table.Columns["الاسم"].SetOrdinal(cb_col1.SelectedIndex);
            table.Columns["كلمة المرور"].SetOrdinal(cb_col2.SelectedIndex);
            table.Columns["الباقة"].SetOrdinal(cb_col3.SelectedIndex);
            table.Columns["نقطة البيع"].SetOrdinal(cb_col4.SelectedIndex);


            DataTable dt_freq = new DataTable();
            dt_freq.Columns.Add("الاسم",typeof(string));
            dgv_freq.DataSource = dgv_freq;
            return table;

        }
        private void btnAddFile_Click(object sender, EventArgs e)
        {
            try
            {
                dgv.Width = panel1.Width - 5;
                dgv_freq.Visible = false;


                DataTable table =DataTableImport();  

                OpenFileDialog openFileDialog1 = new OpenFileDialog();
                openFileDialog1.InitialDirectory = @"D:\";
                openFileDialog1.Title = "حدد الملف";
                openFileDialog1.CheckFileExists = true;
                openFileDialog1.CheckPathExists = true;
                openFileDialog1.FilterIndex = 2;
                openFileDialog1.RestoreDirectory = true;
                openFileDialog1.ReadOnlyChecked = true;
                openFileDialog1.ShowReadOnly = true;
                if (radio_txt.Checked)
                {
                    openFileDialog1.DefaultExt = "txt";
                    openFileDialog1.Filter = "txt files (*.txt)|*.txt";
                }
                else
                {
                    openFileDialog1.DefaultExt = "csv";
                    openFileDialog1.Filter = "csv files (*.csv)|*.csv";
                }

                if (openFileDialog1.ShowDialog() == DialogResult.OK)
                {
                    txt_PathFile.Text = openFileDialog1.FileName;
                   
                    if (radio_txt.Checked)
                    {
                        string[] lines = File.ReadAllLines(openFileDialog1.FileName);
                        string[] values;
                        try
                        {
                            for (int i = 0; i < lines.Length; i++)
                            {
                                values = lines[i].ToString().Trim().Split(' ');
                                string[] row = new string[4];
                                int n = 0;
                                for (int j = 0; j < values.Length; j++)
                                {
                                    if (values[j].Trim() == "")
                                        continue;
                                    row[n] = values[j].Trim();
                                    n++;
                                }
                                table.Rows.Add(row);
                            }
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); }

                        table.Columns["الاسم"].SetOrdinal(0);
                        table.Columns["كلمة المرور"].SetOrdinal(1);
                        table.Columns["الباقة"].SetOrdinal(2);
                        table.Columns["نقطة البيع"].SetOrdinal(3);
                        dgv.DataSource = table;
                    }
                    else
                    {

                        DataTable dataTable = new DataTable();
                        using (StreamReader reader = new StreamReader(openFileDialog1.FileName))
                        {
                            //string[] headers = reader.ReadLine().Split(',');
                            //foreach (string header in headers)
                            //{
                            //    dataTable.Columns.Add(header);
                            //}
                            try
                            {
                                while (!reader.EndOfStream)
                                {
                                    string[] row = new string[4];
                                    string[] rows = reader.ReadLine().Trim().Split(',');
                                    int m = 0;
                                    for (int i = 0; i < 4; i++)
                                    {
                                        //if (rows[i].Trim() == "")
                                        //    continue;
                                        row[i] = rows[i].Replace("=\"", "").Replace("\"", "");
                                        //if(m < 4)
                                        //     m++;
                                    }
                                    table.Rows.Add(row);
                                }
                            }
                            catch { }
                        }

                        table.Columns["الاسم"].SetOrdinal(0);
                        table.Columns["كلمة المرور"].SetOrdinal(1);
                        table.Columns["الباقة"].SetOrdinal(2);
                        table.Columns["نقطة البيع"].SetOrdinal(3);


                        dgv.DataSource = table;

                    }

                    try { txt_count_Cards.Text = (dgv.Rows.Count).ToString(); } catch (Exception ex) { RJMessageBox.Show(ex.Message); }



                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }

        private void timer_SideBar_Tick(object sender, EventArgs e)
        {

        }

        private void rjPanel3_Paint(object sender, PaintEventArgs e)
        {

        }

        private void btn_more_Click(object sender, EventArgs e)
        {
            sideMenu();

        }
        void sideMenu()
        {
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 20;

                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width-pnlClientArea.Padding.Left-pnlClientArea.Padding.Right-10;
                //rjPanel_back_side.Location = new Point(pnlClientArea.Width - rjPanel_back_side.Width - 10, panel1.Location.Y);
                rjPanel_back_side.Location = new Point(panel1.Width+15, panel1.Location.Y);

            }
            else
            {
                rjPanel_back_side.Width = 273;
                //panel1.Width =  705;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 20;

                rjPanel_back_side.Location = new Point(panel1.Width+15, panel1.Location.Y );

            }
            this.Refresh();
            panel1.Refresh();
            rjPanel2.Refresh();
            rjPanel3.Refresh();
            rjPanel5.Refresh();


        }

        private void Form_import_batch_cards_UM_SizeChanged(object sender, EventArgs e)
        {
            panel1.Refresh();
            rjPanel2.Refresh();
            rjPanel3.Refresh();
            rjPanel5.Refresh();
        }

        private void Form_import_batch_cards_UM_Load(object sender, EventArgs e)
        {
            cb_col1.SelectedIndex = 0;
            cb_col2.SelectedIndex = 1;
            cb_col3.SelectedIndex = 2;
            cb_col4.SelectedIndex = 3;

            dgv.DataSource= DataTableImport();
            timer1.Start();
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();

            //dgv.BorderStyle = BorderStyle.FixedSingle;
        }

        private void rjPanel_back_side_Paint(object sender, PaintEventArgs e)
        {

        }

        private void rjPanel2_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjPanel2.Refresh();
            txt_PathFile.Refresh();
        }

        private void checkBox_RegisterAsBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAsBatch.Checked == true)
                checkBox_RegisterAs_LastBatch.Checked = false;
            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAsBatch.Checked = true;

        }

        private void checkBox_RegisterAs_LastBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAs_LastBatch.Checked == true)
                checkBox_RegisterAsBatch.Checked = false;

            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAs_LastBatch.Checked = true;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_TemplateCardsFromDB();
        }

        public bool init_file_pdf2()
        {
            SaveFileDialog saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Title = "حدد مكان حفظ الملف";
            try
            {
                if (Frm_State.PathFolderPrint != "")
                    saveFileDialog1.InitialDirectory = Frm_State.PathFolderPrint;
                else
                    saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
            }
            catch
            {
                saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
                Frm_State.PathFolderPrint = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
            }
            try
            {
                if (!Directory.Exists(Frm_State.PathFolderPrint))
                {
                    Directory.CreateDirectory(Frm_State.PathFolderPrint);
                }
            }
            catch
            {
                Frm_State.path_saved_file = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
            }
            Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + clss_InfoPrint.NumberPrint+ "Cards)" + "_(" + clss_InfoPrint.Profile_Name + ")";
            pathfile = Frm_State.path_saved_file + "\\" + "Cards_" + Public_file_Name + ".pdf";
            clss_InfoPrint.pathfile = pathfile;
            ump.pathfile = pathfile;
            saveFileDialog1.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialog1.FileName = "Cards_" + Public_file_Name;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialog1.FileName;
                Frm_State.PathFolderPrint = Path.GetDirectoryName(saveFileDialog1.FileName);
                Frm_State.path_saved_file = pathfile;

            }
            else
            {
                //startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                return false;
            }
            return true;
        }

        private void dgv_freq_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)  
            {
                try
                {
                    int indexCell = Convert.ToInt32(dgv_freq.Rows[e.RowIndex].Cells["index"].Value);
                    dgv.ClearSelection();
                    dgv.Rows[indexCell].Selected = true;
                }
                catch { }
            }

        }

        private void Copy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }
        DataGridViewCell ActiveCell = null;
        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void Copy_AllRowToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        private void DeleteCards_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                dgv.Rows.Remove(row);
            }
        }
        private void update_select_DGV(RJDataGridView _dgv)
        {
            try
            {
                string ListAll = _dgv.Rows.Count.ToString();
                string ListSelected = _dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }

        private void dgv_freq_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV(dgv_freq);
        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV(dgv);
        }

        private void txt_searchBySales_onTextChanged(object sender, EventArgs e)
        {
            try
            {
                BindingSource bs = new BindingSource();
                bs.DataSource = dgv.DataSource;
                bs.Filter = dgv.Columns["الاسم"].HeaderText.ToString() + " LIKE '%" + txt_searchBySales.Text + "%'";
                dgv.DataSource = bs;
            }
            catch { }
        }
    }
}
