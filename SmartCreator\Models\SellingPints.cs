﻿//using SmartCreator.Data;
//using System;
//using System.Collections.Generic;
//using System.Linq;
//using System.Text;
//using System.Threading.Tasks;
//using static System.Net.Mime.MediaTypeNames;

//namespace SmartCreator.Models
//{
//    public class SellingPints
//    {
//        public SellingPints() { }
//		public int? id { get; set; }
//		public string code { get; set; }
//		public string name { get; set; }
//		public string prefixes { get; set; }
//		public string suffixes { get; set; }
//		public string address { get; set; }
//		public int is_percentage { get; set; }
//		public double sp_percentage { get; set; }

//		public List<SellingPints> GetSellingPints() 
//		{
//			return SqlDataAccess.GetSellingPints();
//        }
//        public void AddSellingPints()
//        {
//            //return SqlDataAccess.GetSellingPints();
//        }
//    }
//}
