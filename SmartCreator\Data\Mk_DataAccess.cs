﻿using CefSharp.DevTools.IO;
using CefSharp.DevTools.Network;
using iTextSharp.text.pdf;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.RJForms;
using SmartCreator.RJForms.Private;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Web.UI.WebControls;
using System.Windows.Forms;
using System.Windows.Interop;
using tik4net;
//using SmartCreator
namespace SmartCreator.Data
{
    public class Mk_DataAccess : IDisposable
    {
        private bool disposed = false;
        private ITikConnection connection { get; set; }
        public int inext = 0;
        Smart_DataAccess Smart_DA = null;
        //ITikConnection connection;

        [Obsolete]
        public Mk_DataAccess()
        {
            Smart_DA = new Smart_DataAccess();
            //connection = ConnectionFactory.CreateConnection(TikConnectionType.Api);
        }
        public static bool Mk_Conn(ITikConnection connection)
        {
            //bool status = false; 
            //A socket operation was attempted to an unreachable host **********:8728  

            try
            {
                connection.Open(Global_Variable.Server_IP, Global_Variable.Server_Port, Global_Variable.Server_Username, Global_Variable.Server_Password);
                connection.Encoding = Encoding.GetEncoding("UTF-8");
                return true;
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("No connection") || ex.Message.Contains("A connection attempt failed") || ex.Message.Contains("A socket operation was attempted"))
                {
                    RJMessageBox.Show("تاكد من اتصالك بالشبكة او عنوان الراوتر او منفذ ال api");
                    return false;
                }
                else if (ex.Message.Contains("Specified argument was out of the range of"))
                    RJMessageBox.Show("رقم البورت غير صحيح اكتب قيمة صحيحة");

                else if (ex.Message.Contains("invalid user name"))
                    RJMessageBox.Show("تاكد من اسم المستخدم او كلمة المرور");

                else if (ex.Message.Contains("No such host is known"))
                    RJMessageBox.Show("العنوان غير صالح");
                else
                    RJMessageBox.Show(ex.Message);

                return false;
            }
        }
        public static bool Mk_Conn_Close(ITikConnection connection)
        {
            //bool status = false; 
            //A socket operation was attempted to an unreachable host **********:8728  

            try
            {
                connection.Close();
                return true;
            }
            catch (Exception ex)
            {
                //if (ex.Message.Contains("No connection") || ex.Message.Contains("A connection attempt failed") || ex.Message.Contains("A socket operation was attempted"))
                //{
                //    RJMessageBox.Show("تاكد من اتصالك بالشبكة او عنوان الراوتر او منفذ ال api");
                //    return false;
                //}
                //else if (ex.Message.Contains("Specified argument was out of the range of"))
                //    RJMessageBox.Show("رقم البورت غير صحيح اكتب قيمة صحيحة");

                //else if (ex.Message.Contains("invalid user name"))
                //    RJMessageBox.Show("تاكد من اسم المستخدم او كلمة المرور");

                //else if (ex.Message.Contains("No such host is known"))
                //    RJMessageBox.Show("العنوان غير صالح");
                //else
                //    RJMessageBox.Show(ex.Message);

                return false;
            }
        }

        public void MkConnClose()
        {
            Mk_Conn_Close(this.connection);
        }

        [Obsolete]
        public bool GetResources()
        {
            bool statu = false;
            using (connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                try
                {
                    if (Mk_Conn(connection) == false)
                        return false;
                    //============================================================================
                    var loadCmd = connection.CreateCommandAndParameters("/system/resource/print");
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        Global_Variable.Mk_resources.uptime = item.GetResponseFieldOrDefault("uptime", "");
                        Global_Variable.Mk_resources.version_str = item.GetResponseFieldOrDefault("version", "");
                        Global_Variable.Mk_resources.factorySoftware = item.GetResponseFieldOrDefault("factory-software", "");
                        Global_Variable.Mk_resources.freeMemory = item.GetResponseFieldOrDefault("free-memory", "");
                        Global_Variable.Mk_resources.totalMemory = item.GetResponseFieldOrDefault("total-memory", "");
                        Global_Variable.Mk_resources.cpu = item.GetResponseFieldOrDefault("cpu", "");
                        Global_Variable.Mk_resources.cpuCount = item.GetResponseFieldOrDefault("cpu-count", "");
                        Global_Variable.Mk_resources.cpuFrequency = item.GetResponseFieldOrDefault("cpu-frequency", "");
                        Global_Variable.Mk_resources.cpuLoad = item.GetResponseFieldOrDefault("cpu-load", "");
                        Global_Variable.Mk_resources.freeHddSpace = item.GetResponseFieldOrDefault("free-hdd-space", "");
                        Global_Variable.Mk_resources.totalHddSpace = item.GetResponseFieldOrDefault("total-hdd-space", "");
                        Global_Variable.Mk_resources.architectureName = item.GetResponseFieldOrDefault("architecture-name", "");
                        Global_Variable.Mk_resources.boardName = item.GetResponseFieldOrDefault("board-name", "");
                        Global_Variable.Mk_resources.platform = item.GetResponseFieldOrDefault("platform", "");
                    }
                    try
                    {
                        Global_Variable.Mk_resources.version = Convert.ToInt32(Global_Variable.Mk_resources.version_str.Substring(0, 1));
                        Global_Variable.Mk_resources.verisonAfter_Float = Convert.ToInt32(Global_Variable.Mk_resources.version_str.Substring(2, 2));
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                    //===================================================================================
                    var loadCmd2 = connection.CreateCommandAndParameters("/system/identity/print");
                    string response2 = loadCmd2.ExecuteScalar();
                    Global_Variable.Mk_resources.identity = response2;
                    //==================================================================================
                    try
                    {
                        var loadCmd_sof_id = connection.CreateCommandAndParameters("system/license/print", "software-id");
                        var response_sof_id = loadCmd_sof_id.ExecuteList();
                        Global_Variable.Mk_resources.RB_Soft_id = response_sof_id.Single().GetResponseField("software-id").ToString();
                    }
                    catch
                    {
                        try
                        {
                            var loadCmd_sof_id = connection.CreateCommandAndParameters("system/license/print", "system-id");
                            var response_sof_id = loadCmd_sof_id.ExecuteList();
                            Global_Variable.Mk_resources.RB_Soft_id = response_sof_id.Single().GetResponseField("system-id").ToString();
                        }
                        catch (Exception ex2)
                        { MessageBox.Show(ex2.Message); }
                    }
                    //===============================================orig-mac-address=====================================
                    try
                    {
                        var loadCmd_MAC = connection.CreateCommandAndParameters("interface/ethernet/print", "default-name", "ether1");
                        var response_MAC = loadCmd_MAC.ExecuteList();
                        Global_Variable.Mk_resources.ether1_MAC = response_MAC.Single().GetResponseField("orig-mac-address").ToString();
                    }
                    catch { }

                    if (Global_Variable.Mk_resources.version >= 6 && Global_Variable.Mk_resources.architectureName == "x86" || Global_Variable.Mk_resources.architectureName == "x86_64")
                    {
                        Global_Variable.Mk_resources.RB_SN = Global_Variable.Mk_resources.RB_Soft_id;
                    }
                    else
                    {
                        try
                        {
                            var loadCmd_RB_SN = connection.CreateCommandAndParameters("system/routerboard/print", "serial-number");
                            var response_RB_SN = loadCmd_RB_SN.ExecuteList();
                            Global_Variable.Mk_resources.RB_SN = response_RB_SN.Single().GetResponseField("serial-number").ToString();

                            //MessageBox.Show(MyDataClass.RB_SN);

                        }
                        catch (Exception ex) { MessageBox.Show(ex.Message); }
                    }

                    //if (MyDataClass.RB_SN == "TI09-7WK3")
                    //    MyDataClass.RB_SN = "TI09-7WK3" +"ds!&"+ MyDataClass.ether1_MAC;

                    //===========================================================
                    if (Global_Variable.Mk_resources.RB_SN == "")
                        return false;
                    else
                        statu = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }

                if (Global_Variable.Mk_resources.RB_SN != "")
                {
                    Global_Variable.Mk_resources.RB_code = utils.Base64Encode((Global_Variable.Mk_resources.RB_SN + "ds").ToUpper());
                    Global_Variable.Mk_resources.RB_code_v2 = utils.Base64Encode((Global_Variable.Mk_resources.RB_SN + "ds&" + Global_Variable.Mk_resources.ether1_MAC).ToUpper());
                    Global_Variable.Mk_resources.licenseCode = Global_Variable.Mk_resources.RB_code;

                    if (Global_Variable.Mk_resources.architectureName == "x86" || Global_Variable.Mk_resources.architectureName == "x86_64")
                    {
                        Global_Variable.Mk_resources.licenseCode = Global_Variable.Mk_resources.RB_code_v2;
                        Global_Variable.Mk_resources.RB_code = Global_Variable.Mk_resources.RB_code_v2;
                    }

                    // Create instance routers
                    Global_Variable.Mk_Router = new Mk_Routers();
                    Global_Variable.Mk_Router.mk_code = Global_Variable.Mk_resources.RB_code;
                    Global_Variable.Mk_Router.mk_sn = Global_Variable.Mk_resources.RB_SN;
                    Global_Variable.Mk_Router.soft_id = Global_Variable.Mk_resources.RB_Soft_id;

                }
                return statu;
            }
        }
        [Obsolete]
        public static Dictionary<string,string> Get_main_info()
        {
            Dictionary<string,string> info = new Dictionary<string,string>();
            info["active"] = "0";
            info["count_um_user"] = "0";
            info["count_um_session"] = "0";
            info["count_hs_users"] = "0";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                
                try
                {
                    if (Mk_Conn(connection) == false)
                        return null;

                    var findResponse = connection.CallCommandSync("/ip/hotspot/active/print", "=count-only=");
                    //var id = findResponse.OfType<ITikReSentence>().Single().GetId();
                    var itemInterface = findResponse.OfType<ITikReSentence>().Single().GetResponseField("");

                    //=====================================active=======================================
                    var loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/active/print", "=count-only=");
                    var active = loadCmd.ExecuteScalar();
                    info["active"] = active;

                    //====================================count userman user ===============================================
                    string command = "/tool/user-man/user/print";
                    if (Global_Variable.Mk_resources.version >= 7)
                        command = "/user-man/user/print";
                    
                    loadCmd = connection.CreateCommandAndParameters(command, "count-only");
                    string count_um = loadCmd.ExecuteScalar();
                    info["count_um_user"] = count_um;
                    //====================================count userman session ===============================================
                    command = "/tool/user-man/session/print";
                    if (Global_Variable.Mk_resources.version >= 7)
                        command = "/user-man/session/print";

                    loadCmd = connection.CreateCommandAndParameters(command, "count-only");
                    string count_um_session = loadCmd.ExecuteScalar();
                    info["count_um_session"] = count_um_session;
                    //=====================================hotspot count user =======================================
                    loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/print", "count-only");
                    var count_hs_users = loadCmd.ExecuteScalar();
                    info["count_hs_users"] = count_hs_users;

                    return info;
                    //====================================count userman session ===============================================
                    //command = "/tool/user-man/user/session";
                    //if (Global_Variable.Mk_resources.version >= 7)
                    //    command = "/user-man/session/print";

                    //loadCmd = connection.CreateCommandAndParameters(command, "count-only");
                    //string count_um_session = loadCmd.ExecuteScalar();
                    //info["count_um_session"] = count_um_session;
                   //===============================================orig-mac-address=====================================
                    //try
                    //{
                    //    var loadCmd_MAC = connection.CreateCommandAndParameters("interface/ethernet/print", "default-name", "ether1");
                    //    var response_MAC = loadCmd_MAC.ExecuteList();
                    //    Global_Variable.Mk_resources.ether1_MAC = response_MAC.Single().GetResponseField("orig-mac-address").ToString();
                    //}
                    //catch { }

                    //if (Global_Variable.Mk_resources.version >= 6 && Global_Variable.Mk_resources.architectureName == "x86" || Global_Variable.Mk_resources.architectureName == "x86_64")
                    //{
                    //    Global_Variable.Mk_resources.RB_SN = Global_Variable.Mk_resources.RB_Soft_id;
                    //}
                    //else
                    //{
                    //    try
                    //    {
                    //        var loadCmd_RB_SN = connection.CreateCommandAndParameters("system/routerboard/print", "serial-number");
                    //        var response_RB_SN = loadCmd_RB_SN.ExecuteList();
                    //        Global_Variable.Mk_resources.RB_SN = response_RB_SN.Single().GetResponseField("serial-number").ToString();

                    //        //MessageBox.Show(MyDataClass.RB_SN);

                    //    }
                    //    catch (Exception ex) { MessageBox.Show(ex.Message); }
                    //}

                    //if (MyDataClass.RB_SN == "TI09-7WK3")
                    //    MyDataClass.RB_SN = "TI09-7WK3" +"ds!&"+ MyDataClass.ether1_MAC;

                    
                    //===========================================================
                    
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
            }
            return info;
        }

        [Obsolete]
        public void FirstLoadDataFromMK()
        {    
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            if(Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("انتظر حتى انتهاء العملية السابقة");
                return;
            }

            Global_Variable.StartThreadProcessFromMK = true;
            inext = 1;
            int count_process = 8;
            try
            {
                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب الاعدادت والباقات", Convert.ToInt32(1 * (100.0 / count_process)));


                try { Global_Variable.Source_profile = GetSource_UserManager_Profile(); } catch { };
                try { Global_Variable.Source_limtition = GetSource_UserManager_Limit(); } catch { }
                try { Global_Variable.Source_profile_limtition = GetSource_UserManager_Profile_Limtition(); } catch { }

                //Source_HS_Profile
                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                //using (var db = smart_DataAccess.dbFactory.Open())
                //{

                //Global_Variable.Source_profile = db.Select<UmProfile>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                try { Global_Variable.Source_profile = Smart_DA.Load<UmProfile>($"select * from UmProfile where DeleteFromServer=0 and (Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') "); } catch { }

                //Global_Variable.Source_limtition = db.Select<UmLimitation>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);

                try { Global_Variable.Source_limtition = Smart_DA.Load<UmLimitation>($"select * from UmLimitation where DeleteFromServer=0 and (Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') "); } catch { }

                //Global_Variable.Source_profile_limtition = db.Select<UmProfile_Limtition>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                try { Global_Variable.Source_profile_limtition = Smart_DA.Load<UmProfile_Limtition>($"select * from UmProfile_Limtition where DeleteFromServer=0 and (Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') "); } catch { }
                 
                //} 
                UmProfile uprofile = new UmProfile();
                try { Global_Variable.UM_Profile = uprofile.Get_UMProfile(); } catch { }

                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب بروفايلات الهوتسبوت", Convert.ToInt32(2 * (100.0 / count_process)));
                Global_Variable.Source_HS_Profile = GetProfileHotspot();
                
                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب مسؤولي يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                if (Global_Variable.Mk_resources.version <= 6)
                    try { Global_Variable.UM_Customer = Get_UserManager_Customer(); } catch { }
                else
                {
                    try { Global_Variable.UM_Attribute = Get_UM_Attribute(); } catch { }
                    try { Global_Variable.UM_Group = Get_UM_Group(); } catch { }
                }

                try
                {
                    //lock ( Smart_DataAccess.Lock_object)
                        Global_Variable.Hotspot_Profile = smart_DataAccess.Load<HSLocalProfile>($"select * from HSLocalProfile where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'");
                }
                catch { }
                //=======================================================================================
                Global_Variable.Update_StatusBar_StartSyn();
                UserManager_Customer customerUserManager = new UserManager_Customer();
                customerUserManager.Syn_Customer_to_LocalDB();

                UserManager_SourceProfile_UserManager syn_sp = new UserManager_SourceProfile_UserManager();
                syn_sp.Syn_Profile_UserManager_to_LocalDB();

                UserManager_Source_limitation sourcelimitation = new UserManager_Source_limitation();
                sourcelimitation.Syn_limitation_to_LocalDB();

                UserManager_Source_Profile_Limtition profileLimtition = new UserManager_Source_Profile_Limtition();
                profileLimtition.Syn_ProfileLimtition_to_LocalDB();

                Hotspot_Source_Profile Profilehotspot = new Hotspot_Source_Profile();
                Profilehotspot.Syn_Source_ProfileHotspot_to_LocalDB();

                Global_Variable.Update_Um_StatusBar_Prograss("تم عرض بيانات الباقات", Convert.ToInt32(3 * (100.0 / count_process)));
                Global_Variable.Update_StatusBar_StopSyn();
            }
            catch (Exception ex) { /*Global_Variable.StartThreadProcessFromMK = false;*/ /*RJMessageBox.Show(ex.Message);*/ }

            bool Syn_Users_FromFasrDB = false;
            bool Syn_Pyment_FromFasrDB = false;
            bool Syn_Session_FromFasrDB = false;


            //====================
            //if (Global_Variable.RunOffline)
            //{
            //    Global_Variable.Update_Um_StatusBar_Prograss("تم عرض البيانات الاساسية ", Convert.ToInt32(1 * (100.0 / 1)));
            //    return;
            //}
            //else 
            if (Global_Variable.load_by_DownloadDB && Global_Variable.Mk_resources.version <= 6)
            {
                count_process = 7;
                Fast_Load_From_Mikrotik fast = new Fast_Load_From_Mikrotik();


                Global_Variable.Update_Um_StatusBar_Prograss("تنزيل قاعدة بيانات اليوزمنجر  -  لتستفيد من الميزه افتح السرعه للكمبيوتر", Convert.ToInt32(1 * (100.0 / count_process)));

                if (fast.Download_Sql_From_Mikrotik())
                {

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر", Convert.ToInt32(2 * (100.0 / count_process)));
                    //Thread.Sleep(1000);
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه الكروت يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                    Syn_Users_FromFasrDB = fast.Syn_UmUser_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه المبيعات والحسابات يوزمنجر", Convert.ToInt32(4 * (100.0 / count_process)));
                    Syn_Pyment_FromFasrDB = fast.Syn_Pyments_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه  الجلسات يوزمنجر", Convert.ToInt32(5 * (100.0 / count_process)));
                    Syn_Session_FromFasrDB = fast.Syn_Session_From_FastDB();

                    //---======================================================================================================

                    //Global_Variable.Update_Um_StatusBar_Prograss("يتم الان جلب كروت الهوتسبوت من الروتر", Convert.ToInt32(6 * (100.0 / count_process)));
                    //Global_Variable.Source_Users_HotSpot = SourceCardsHotspot_fromMK.Get_HS_user();
                    //Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة كروت الهوتسبوت", Convert.ToInt32(7 * (100.0 / count_process)));

                    //Global_Variable.Update_StatusBar_StartSyn();
                    //SourceCardsHotspot_fromMK hh = new SourceCardsHotspot_fromMK();
                    //if (Global_Variable.Source_Users_HotSpot != null && Global_Variable.Source_Users_HotSpot.Count > 0)
                    //    hh.Syn_HS_Users_to_LocalDB();
                    //Global_Variable.Update_StatusBar_StopSyn();
                    //Global_Variable.Update_Um_StatusBar_Prograss("تم  عرض ومزامنة كروت الهوتسبوت", Convert.ToInt32(7 * (100.0 / count_process)));

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب ومزامنه بيانات اليوزمنجر من الروتر", Convert.ToInt32(0 * (100.0 / count_process)));

                    fast.Create_Indexs();
                }
                try
                {
                    string Downloadfile = utils.Get_Database_Directory() + "\\" + "dbs\\temp.db";
                    string Downloadfile2 = utils.Get_Database_Directory() + "\\" + "dbs\\temp2.db";
                    string Downloadfile3 = Directory.GetCurrentDirectory() + "\\sql.bat";
                    if (File.Exists(Downloadfile))
                        File.Delete(Downloadfile);
                    if (File.Exists(Downloadfile2))
                        File.Delete(Downloadfile2);
                    if (File.Exists(Downloadfile3))
                        File.Delete(Downloadfile3);
                }
                catch { }

            }

            //---======================================================================================================
            UserManagerProcess u = new UserManagerProcess();


            if (Syn_Users_FromFasrDB == false)
            {
                if ( 
                    ((Global_Variable.Mk_Login_data.load_by_Custom_Login==false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmUsers==false) )
                    //&& Global_Variable.Mk_Login_data.DisableLoad_UmUsers == false
                   )
                {
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب كروت  اليوزر مانجر من الروتر", Convert.ToInt32(4 * (100.0 / count_process)));
                    //SourceCardsUserManager_fromMK MkSourceUM = new SourceCardsUserManager_fromMK();
                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        //MyDataClass.SourceForPrint_UM_Users = DA_v7.get_UM_user();
                        //MyDataClass.SourceUM_Payments = MyDataClass.SourceUM_Users_profile_v7;
                        Global_Variable.Source_Users_UserManager = SourceCardsUserManager_fromMK.Get_UM_user();
                    }
                    else
                    {
                        Global_Variable.Source_Users_UserManager = SourceCardsUserManager_fromMK.Get_UM_user();
                    }

                    //IsFinshLoadUM_Users = true;


                    Global_Variable.Update_StatusBar_StartSyn();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة كروت  اليوزر مانجر", Convert.ToInt32(2 * (100.0 / count_process)));
                    
                    if (Global_Variable.Source_Users_UserManager != null && Global_Variable.Source_Users_UserManager.Count > 0)
                        u.Syn_UM_Users_to_LocalDB();
                    Global_Variable.Update_StatusBar_StopSyn();
                    Global_Variable.Update_Um_StatusBar_Prograss(" تم عرض ومزامنة كروت  اليوزر مانجر", Convert.ToInt32(2 * (100.0 / count_process)));
                }
            }
            //---======================================================================================================
            if ( (Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_HSUsers == false))  
            {
                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان جلب كروت الهوتسبوت من الروتر", Convert.ToInt32(4 * (100.0 / count_process)));
                Global_Variable.Source_Users_HotSpot = SourceCardsHotspot_fromMK.Get_HS_user();

                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة كروت الهوتسبوت", Convert.ToInt32(4 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StartSyn();
                SourceCardsHotspot_fromMK h = new SourceCardsHotspot_fromMK();
                if (Global_Variable.Source_Users_HotSpot != null && Global_Variable.Source_Users_HotSpot.Count > 0)
                {
                    h.Syn_HS_Users_to_LocalDB();
                    HsPyment hsPyment = new HsPyment();
                    hsPyment.Syn_HS_Pyments_to_FirstUsers();
                }

                Global_Variable.Update_StatusBar_StopSyn();

                Global_Variable.Update_Um_StatusBar_Prograss("تم  عرض ومزامنة كروت الهوتسبوت", Convert.ToInt32(4 * (100.0 / count_process)));
            }
            if ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_HSSession == false))
            {
                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان جلب جلسات الهوتسبوت من الروتر", Convert.ToInt32(4 * (100.0 / count_process)));
                SourceCardsHotspot sourceCardsHotspot = new SourceCardsHotspot();
             
                List<HsSession> hsSessin= SourceCardsHotspot_fromMK.Get_HS_Session();

                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة جلسات الهوتسبوت", Convert.ToInt32(4 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StartSyn();
               HsSession cls_Session = new HsSession();
                cls_Session.Syn_HS_Session(hsSessin);
                
                //SourceCardsHotspot_fromMK h = new SourceCardsHotspot_fromMK();
                //if (Global_Variable.Source_Users_HotSpot != null && Global_Variable.Source_Users_HotSpot.Count > 0)
                //{
                //    h.Syn_HS_Users_to_LocalDB();
                //    HsPyment hsPyment = new HsPyment();
                //    hsPyment.Syn_HS_Pyments_to_FirstUsers();
                //}

                Global_Variable.Update_StatusBar_StopSyn();
                //Global_Variable.Update_Um_StatusBar_Prograss("تم  عرض ومزامنة جلسات الهوتسبوت", Convert.ToInt32(4 * (100.0 / count_process)));
            }
            //---======================================================================================================
            if (Syn_Pyment_FromFasrDB == false)
                if (
                    ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmPyment == false))
                   )

                    //if (Global_Variable.Mk_Login_data.load_by_Custom_Login==false && Global_Variable.Mk_Login_data.DisableLoad_UmPyment == false)
                {
                    if (Global_Variable.Ddiable_LoadSession == false)
                    {
                        Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب المبيعات  والحسابات", Convert.ToInt32(5 * (100.0 / count_process)));
                        Global_Variable.Source_Pyment_UserManager = SourcePymentUserManager_fromMK.get_Pyment_user();
                        Global_Variable.Update_StatusBar_StartSyn();

                        if (Global_Variable.Source_Pyment_UserManager != null && Global_Variable.Source_Pyment_UserManager.Count > 0)
                            u.Syn_Pyments_to_LocalDB();
                        Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة المبيعات والحسابات", Convert.ToInt32(5 * (100.0 / count_process)));

                        Global_Variable.Update_StatusBar_StopSyn();
                    }
                    //===================================================================

                }
            if (Syn_Session_FromFasrDB == false)
                if (
                   ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false))
                   //&& Global_Variable.Mk_Login_data.DisableLoad_UmUsers == false
                  )
                    //if (Global_Variable.Mk_Login_data.load_by_Custom_Login == false && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false && Global_Variable.Mk_Login_data.DisableLoad_UmUsers == false)
                {
                    if (Global_Variable.Ddiable_LoadSession == false)
                    {
                        Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(6 * (100.0 / count_process)));

                        Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                        //RJMessageBox.Show("يتم finsh get session");

                        Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(6 * (100.0 / count_process)));

                        Global_Variable.Update_StatusBar_StartSyn();


                        Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(7 * (100.0 / count_process)));

                        if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                            u.Syn_Session_to_LocalDB();
                        Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(7 * (100.0 / count_process)));

                        Global_Variable.Update_StatusBar_StartSyn();
                    }
                }
            //---======================================================================================================


            //if (Global_Variable.Source_Users_UserManager != null && Global_Variable.Source_Users_UserManager.Count > 0)
            //    u.Syn_UM_Users_to_LocalDB();
            //if (Global_Variable.Source_Pyment_UserManager != null && Global_Variable.Source_Pyment_UserManager.Count > 0)
            //    u.Syn_Pyments_to_LocalDB();

            //if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
            //    u.Syn_Session_to_LocalDB();

            //SourceCardsHotspot_fromMK h = new SourceCardsHotspot_fromMK();
            //if (Global_Variable.Source_Users_HotSpot != null && Global_Variable.Source_Users_HotSpot.Count > 0)
            //    h.Syn_HS_Users_to_LocalDB();

            Global_Variable.Update_Um_StatusBar_Prograss(" تم عرض ومزامنة البيانات من الروتر", 0);
            Global_Variable.Update_StatusBar_StopSyn();


            stopwatch.Stop();
            string ss =
                      (
                        (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                         " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                        " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                      );
 

            loadAllForms_Data();

            //Thread.Sleep(4000);
            Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") اجمالي  جلب ومزامنة البيانات من الروتر", "");

            Global_Variable.StartThreadProcessFromMK = false;
            //loadAllForms_Data();

        }

        private void loadAllForms_Data()
        {
            //return;
            try
            {
                FormDashboard formDashBoard = (FormDashboard)RJMainForm.listChildForms.Find(x => x.Name == "FormDashboard");
                if (formDashBoard != null)
                {
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                      (System.Windows.Forms.MethodInvoker)delegate ()
                      {
                          //formDashBoard.LoadData();
                          formDashBoard.CboxPeriod.SelectedIndex=3;

                      });

                    //formDashBoard.LoadData();
                    //Thread thread = new Thread(formDashBoard.LoadAll); 
                    //Thread thread = new Thread(formDashBoard.LoadData); 
                    //thread.Start();
                }
                Form_Cards_UserManager formcards = (Form_Cards_UserManager)RJMainForm.listChildForms.Find(x => x.Name == "Form_Cards_UserManager");
                if (formcards != null)
                {
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                     (System.Windows.Forms.MethodInvoker)delegate ()
                     {
                         if (formcards.formAllCardsUserManager != null)
                             formcards.formAllCardsUserManager.loadData();

                         if (formcards.formAllCardsUserManager_RB_Archive != null)
                             formcards.formAllCardsUserManager_RB_Archive.loadData();

                         if (formcards.formAll_From_Finsh_Cards != null)
                             formcards.formAll_From_Finsh_Cards.loadData();

                         if (formcards.formAllBatchsCards != null)
                             formcards.formAllBatchsCards.LoadDataGridviewData();

                         if (formcards.formAll_From_Session_Cards != null)
                             formcards.formAll_From_Session_Cards.LoadDataGridviewData();

                     });

                   
                }
            }
            catch { }
        }

        [Obsolete]
        public static List<UserManager_Customer> Get_UserManager_Customer()
        {

            List<UserManager_Customer> customer = new List<UserManager_Customer>();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {

                    if (Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters("/tool/user-manager/customer/print");
                    var response = loadCmd.ExecuteList();

                    foreach (var item in response)
                    {
                        try
                        {
                            UserManager_Customer sourceCustomer = new UserManager_Customer();
                            double sn = 0;
                            string Number_sequence = item.GetResponseFieldOrDefault(".id", "0");
                            sourceCustomer.idHX = Number_sequence;

                            Number_sequence = Number_sequence.TrimStart(new char[] { '*' });
                            Number_sequence = Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();

                            sn = Convert.ToDouble(Number_sequence);
                            sourceCustomer.id = (int)sn;
                            sourceCustomer.Name = item.GetResponseFieldOrDefault("login", "");
                            sourceCustomer.rb = Global_Variable.Mk_resources.RB_SN;
                            sourceCustomer.Delet_fromServer = 0;

                            customer.Add(sourceCustomer);
                        }
                        catch (Exception ex) { MessageBox.Show("UserManager_Customer\n" + ex.Message); }
                    }
                    Global_Variable.UM_Customer=customer;
                    return customer;
                }
            }
            catch { }
            return customer;



        }

        [Obsolete]
        public  List<UmProfile> GetSource_UserManager_Profile()  
        {
            List<UmProfile> profile2 = new List<UmProfile>();
            //List<UserManager_SourceProfile_UserManager> profile = new List<UserManager_SourceProfile_UserManager>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {

                if (Mk_Conn(connection) == false)
                    return null;

                if (Properties.Settings.Default.script_add != utils.Base64Decode("L3N5c3RlbS9zY3JpcHQvYWRk"))
                    return null;


                string script = "/tool/user-manager/profile/print";
                if (Global_Variable.Mk_resources.version>=7)
                    script= "/user-manager/profile/print";
               
                var loadCmd = connection.CreateCommandAndParameters(script);
                var response = loadCmd.ExecuteList();
                string msgErorrs = "";
                foreach (var item in response)
                {
                    try
                    {
                        UserManager_SourceProfile_UserManager sourceProfile = new UserManager_SourceProfile_UserManager();
                        UmProfile umProfile = new UmProfile();
                        double sn = 0;
                        string IdHX = item.GetResponseFieldOrDefault(".id", "0");
                        sn = Int32.Parse(IdHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                        sourceProfile.idHX = IdHX; 
                        string Name = item.GetResponseFieldOrDefault("name", ""); 
                        int validay = (utils.get_number_Days_form_validity_profile(item.GetResponseFieldOrDefault("validity", "0")));
                        string NameForUser = item.GetResponseFieldOrDefault("name-for-users", "");
                        int Price = 0;
                        try { Price = Convert.ToInt32(item.GetResponseFieldOrDefault("price", "0")); } catch { }
                        string SharedUsers = item.GetResponseFieldOrDefault("override-shared-users", "");
                        int Delet_fromServer = 0;
                        string rb = Global_Variable.Mk_resources.RB_SN;

                        umProfile.IdHX = IdHX;
                        umProfile.Name = Name;
                        umProfile.Sn = sn;
                        umProfile.Sn_Name = sn + "-" + Name;
                        umProfile.NameForUser = NameForUser;
                        umProfile.Validity = validay;
                        //umProfile.TransferLimit = validay;
                        umProfile.Price = Price;
                        umProfile.Price_Disply = Price + "";
                        umProfile.SharedUsers = SharedUsers;
                        umProfile.DeleteFromServer = Delet_fromServer;
                        umProfile.Rb = rb;


                        //sourceProfile.Name = Name;
                        //sourceProfile.idHX = IdHX;
                        ////sourceProfile.id = (int)sn;
                        //sourceProfile.Validity = validay;
                        //sourceProfile.NameForUser = NameForUser;
                        //sourceProfile.Price = Price;
                        //sourceProfile.SharedUsers = SharedUsers;
                        //sourceProfile.Delet_fromServer = 0;
                        //sourceProfile.rb = Global_Variable.Mk_resources.RB_code;

                        profile2.Add(umProfile);
                        //profile.Add(sourceProfile);
                    }
                    catch (Exception ex) 
                    { 
                        //MessageBox.Show($"خطاء في الباقة {item} \n Source_UserManager_Profile\n" + ex.Message);
                    msgErorrs=msgErorrs+ $"خطاء في بيانات الباقة {item} \n Source_UserManager_Profile\n" + ex.Message+"\n-------\n";
                    }
                }
                stopwatch.Stop();

                if (msgErorrs != "")
                    MessageBox.Show(msgErorrs);

                //Global_Variable.Source_profile2 = profile2;
                Global_Variable.Source_profile = profile2;

                Smart_DataAccess da = new Smart_DataAccess();
                if(profile2 != null && profile2.Count > 0)
                if (da.Syn_UmProfile(profile2))//نضيف الجديد
                {
                    da.Set_DeletFromServer_As_disable("UmProfile"); // نعطل الكل
                    if (da.Syn_UmProfile(profile2,false,false) == false) // نحدث الجديد الي غير محذوف مع التعديلات التي حدثت
                    {
                        da.Syn_UmProfile(profile2, false, false);
                    }
                }
                return profile2;
            }

        }

        [Obsolete]
        public List<UmLimitation> GetSource_UserManager_Limit()
        {
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //List<UserManager_Source_limitation> SLimit = new List<UserManager_Source_limitation>();
                List<UmLimitation> SLimit2 = new List<UmLimitation>();
                DataTable dt = new DataTable();

                if (Mk_Conn(connection) == false)
                    return null;


                string script = "/tool/user-manager/profile/limitation/print";
                if (Global_Variable.Mk_resources.version >= 7)
                    script = "/user-manager/limitation/print";

                var loadCmd = connection.CreateCommandAndParameters(script);
                var response = loadCmd.ExecuteList();

                foreach (var item in response)
                {
                    try
                    {
                        UserManager_Source_limitation sourcelimt = new UserManager_Source_limitation();
                        UmLimitation slimt = new UmLimitation();
                        string IdHX = item.GetResponseFieldOrDefault(".id", "0");
                        slimt.IdHX = IdHX;
                        string Name = item.GetResponseFieldOrDefault("name", "");
                        slimt.Name = Name;
                        double sn = Int32.Parse(IdHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                        slimt.Sn = sn;
                        slimt.Sn_Name = sn + "-" + Name;
                        //slimt.DownloadLimit = sn;
                        //slimt.UploadLimit = sn;
                        slimt.TransferLimit = Convert.ToDouble(item.GetResponseFieldOrDefault("transfer-limit", "0"));
                        slimt.UptimeLimit = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime-limit", "0"));

                        slimt.Download_tx = item.GetResponseFieldOrDefault("rate-limit-tx", "0");
                        slimt.Upload_rx = item.GetResponseFieldOrDefault("rate-limit-rx", "0");
                        slimt.GroupName = item.GetResponseFieldOrDefault("group-name", "");
                        slimt.Rb = Global_Variable.Mk_resources.RB_SN;
                        slimt.DeleteFromServer = 0;

                        //string Number_sequence = item.GetResponseFieldOrDefault(".id", null);
                        //sourcelimt.idHX = Number_sequence;
                        //sourcelimt.Name = item.GetResponseFieldOrDefault("name", "");
                        ////row["uptime_limit"] = item.GetResponseFieldOrDefault("uptime-limit", ""); 
                        //sourcelimt.uptimeLimit = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime-limit", "0"));
                        //sourcelimt.transferLimit = Convert.ToDouble(item.GetResponseFieldOrDefault("transfer-limit", "0"));
                        //sourcelimt.groupName = item.GetResponseFieldOrDefault("group-name", "");
                        //sourcelimt.download_tx = item.GetResponseFieldOrDefault("rate-limit-tx", "0");
                        //sourcelimt.upload_rx = item.GetResponseFieldOrDefault("rate-limit-rx", "0");
                        //sourcelimt.Delet_fromServer = 0;
                        //sourcelimt.rb = Global_Variable.Mk_resources.RB_code;
                        //SLimit.Add(sourcelimt);

                        SLimit2.Add(slimt);
                    }
                    catch(Exception ex) { MessageBox.Show("GetSource_UserManager_Limit\n" +ex.Message); }
                }
                //Global_Variable.Source_limtition2 = SLimit2;
                Global_Variable.Source_limtition = SLimit2;

                Smart_DataAccess da = new Smart_DataAccess();
                if (SLimit2 != null && SLimit2.Count > 0)
                    if (da.Syn_UmLimit(SLimit2))//نضيف الجديد
                    {
                        da.Set_DeletFromServer_As_disable("UmLimitation"); // نعطل الكل
                        if (da.Syn_UmLimit(SLimit2, false,false) == false) // نحدث الجديد الي غير محذوف مع التعديلات التي حدثت
                        {
                            da.Syn_UmLimit(SLimit2, false, false);
                        }
                    }
                return SLimit2;

            }
        }

        [Obsolete]
        public  List<UmProfile_Limtition> GetSource_UserManager_Profile_Limtition()
        {
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                List<UmProfile_Limtition> SLimit = new List<UmProfile_Limtition>();
                if (Mk_Conn(connection) == false)
                    return null;
                string script = "/tool/user-manager/profile/profile-limitation/print";
                if (Global_Variable.Mk_resources.version >= 7)
                    script = "/user-manager/profile-limitation/print";

                var loadCmd = connection.CreateCommandAndParameters(script);
                var response = loadCmd.ExecuteList();

                foreach (var item in response)
                {
                    try
                    {
                        UmProfile_Limtition spl = new UmProfile_Limtition();
                        string IdHX = item.GetResponseFieldOrDefault(".id", null);
                        spl.IdHX = IdHX;
                        double sn = Int32.Parse(IdHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                        spl.Sn = sn;
                        string Profile = item.GetResponseFieldOrDefault("profile", "");
                        spl.Profile = Profile;
                        string Limitation = item.GetResponseFieldOrDefault("limitation", "");
                        spl.Limitation = Limitation;
                        spl.From_time = item.GetResponseFieldOrDefault("from-time", "0");
                        spl.Till_time = item.GetResponseFieldOrDefault("till-time", "0");
                        spl.Weekdays = item.GetResponseFieldOrDefault("weekdays", "");
                        spl.Sn_Profile_Limitation = sn + "-" + Profile + "-" + Limitation;
                        spl.DeleteFromServer = 0;
                        spl.Rb = Global_Variable.Mk_resources.RB_SN;


                        SLimit.Add(spl);
                    }
                    catch(Exception ex) { MessageBox.Show("GetSource_UserManager_Profile_Limtition\n"+ex.Message); }
                }
                //Global_Variable.Source_profile_limtition = SLimit2;
                Global_Variable.Source_profile_limtition = SLimit;

                Smart_DataAccess da = new Smart_DataAccess();
                if (SLimit != null && SLimit.Count > 0)
                    if (da.Syn_UmProfile_Limtition(SLimit))//نضيف الجديد
                    {
                        da.Set_DeletFromServer_As_disable("UmProfile_Limtition"); // نعطل الكل
                        if (da.Syn_UmProfile_Limtition(SLimit, false, false) == false) // نحدث الجديد الي غير محذوف مع التعديلات التي حدثت
                        {
                            da.Syn_UmProfile_Limtition(SLimit, false, false);
                        }
                    }
                return SLimit;
            }
        }
        [Obsolete]
        public static Dictionary<string, string> add_Script_Smart_AndRun(string source ,bool ExecuteScalar=true)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["result"] = "";
            //bool status = false;
            //string result = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return res;

                delete_Script_Smart();
                //try
                //{
                //    var addSmartErorrProfile = connection.CreateCommandAndParameters("/system/script/add", "name", "SmartErorrProfile", "source", "profile=");
                //    var itemId = addSmartErorrProfile.ExecuteScalarOrDefault();

                //    var addSmartErorrCards = connection.CreateCommandAndParameters("/system/script/add", "name", "SmartErorrCards", "source", "card=");
                //    var itemId2 = addSmartErorrCards.ExecuteScalarOrDefault();
                //}
                //catch { }
                try
                {
                    string code = Properties.Settings.Default.script_add;
                    var addCmd = connection.CreateCommandAndParameters(code, "name", "SmartScript", "source", source);
                    var itemId = addCmd.ExecuteScalar();

                    var RunCmd = connection.CreateCommandAndParameters("/system/script/run", TikSpecialProperties.Id, itemId);
                    if (RunCmd != null)
                    {
                        if (ExecuteScalar)
                        {
                            string cmdresult = RunCmd.ExecuteScalar();
                            //string cmdresult = RunCmd.ExecuteScalarOrDefault();
                            res["status"] = "true";
                            res["result"] = cmdresult;
                        }
                        else
                        {
                            //string cmdresult = RunCmd.ExecuteScalar();
                            RunCmd.ExecuteNonQuery();
                            res["status"] = "true";
                            res["result"] = "";
                        }
                    }
                    else
                    {
                        RJMessageBox.Show("خطاء في اضافة السكربت للمايكروتك");
                        delete_Script_Smart();
                        return res;
                    }

                    ////============= Run From Old Method ============  ";;0=;"  == "!done=ret=;;0=-1=0=;"
                    //Mk_DataAccess_old mk = new Mk_DataAccess_old();
                    //result = mk.RuntScrip2(itemId);
                    ////=================================
                }
                catch (Exception ex)
                {string msg = ex.Message;   
                    if (ex.Message.Contains("Can not read sentence from connection") || ex.Message.Contains("Unable to read data from the transport connec"))
                    {
                        MessageBox.Show("خطاء قد تكون كميه الكروت كبيره او ليست هناك صلاحية للمستخدم \n" + ex.Message);
                        delete_Script_Smart();
                        return res;
                    }
                    else MessageBox.Show("خطاء\n" + ex.Message);
                }
                delete_Script_Smart();
            }

            return res;
        }
        [Obsolete]
        public static Dictionary<string, string> add_scheduler_mikrotik(string name, string source, string StartTime, string interval)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["result"] = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return res;
                try
                {
                    var loadCmd = connection.CreateCommandAndParameters("/system/scheduler/print", "name", name);
                    var response = loadCmd.ExecuteList();
                    if (response.Count() > 0)
                    {
                        var itemId = response.Single().GetId();
                        var deleteCmd = connection.CreateCommandAndParameters("/system/scheduler/remove", TikSpecialProperties.Id, itemId);
                        deleteCmd.ExecuteNonQuery();
                    }
                }
                catch { }

                try
                {
                    var addCmd = connection.CreateCommandAndParameters("/system/scheduler/add", "name", name, "on-event", source, "start-time", StartTime, "interval", interval);
                    var itemId = addCmd.ExecuteScalar();
                    res["status"] = "true";
                    res["result"] = "";
                }
                catch (Exception ex)
                {
                }
            }

            return res;
        }

        [Obsolete]
        public static bool AddScheduler_mikrotik(string name, string source, string StartTime, string interval, ITikConnection connection)
        {
            bool Status = false;
            //using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            //{
            //if (Mk_Conn(connection) == false)
            //    return false;
            try
            {
                var loadCmd = connection.CreateCommandAndParameters("/system/scheduler/print", "name", name);
                var response = loadCmd.ExecuteList();
                if (response.Count() > 0)
                {
                    var itemId = response.Single().GetId();
                    var deleteCmd = connection.CreateCommandAndParameters("/system/scheduler/remove", TikSpecialProperties.Id, itemId);
                    deleteCmd.ExecuteNonQuery();
                }
            }
            catch { }

            try
            {
                var addCmd = connection.CreateCommandAndParameters("/system/scheduler/add", "name", name, "on-event", source, "start-time", StartTime, "interval", interval);
                var itemId = addCmd.ExecuteScalar();
                Status = true;
            }
            catch 
            {
            }
            //}

            return Status;
        }
         
        [Obsolete]
        private static void delete_Script_Smart()
        {
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return;
                try
                {

                    var loadCmd = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartScript");
                    var response = loadCmd.ExecuteList();
                    if (response.Count() > 0)
                    {
                        var itemId = response.Single().GetId();
                        var deleteCmd = connection.CreateCommandAndParameters("/system/script/remove", TikSpecialProperties.Id, itemId);
                        deleteCmd.ExecuteNonQuery();
                    }

                    //var loadSmartErorrProfile = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrProfile");
                    //var responseSmartErorrProfile = loadSmartErorrProfile.ExecuteList();
                    //if (responseSmartErorrProfile.Count() > 0)
                    //{
                    //    var itemId = responseSmartErorrProfile.Single().GetId();
                    //    var deleteCmd = connection.CreateCommandAndParameters("/system/script/remove", TikSpecialProperties.Id, itemId);
                    //    deleteCmd.ExecuteNonQuery();
                    //}

                    //var loadSmartErorrCards = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrCards");
                    //var responseSmartErorrCards = loadSmartErorrCards.ExecuteList();
                    //if (responseSmartErorrCards.Count() > 0)
                    //{
                    //    var itemId = responseSmartErorrCards.Single().GetId();
                    //    var deleteCmd = connection.CreateCommandAndParameters("/system/script/remove", TikSpecialProperties.Id, itemId);
                    //    deleteCmd.ExecuteNonQuery();
                    //}

                }
                catch { }
            }
        }

        [Obsolete]
        public UserManager_Database_Info Get_Info_UserManager_Database()
        {
            UserManager_Database_Info dbinf = new UserManager_Database_Info();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                try
                {
                    //{ApiReSentence:db-path=user-manager|size=40737|in-use=80|log-size=6|log-in-use=100|
                    //last-rebuild=nov/28/2024 08:00:40|last-clear=jan/02/1970 03:42:30|last-save=dec/04/2024 11:58:53|last-load=dec/04/2024 19:53:10}
                    string quer = "/tool/user-manager/database/print";
                    if (Global_Variable.Mk_resources.version >= 7)
                        quer = "/user-manager/database/print";
                    var loadCmd = connection.CreateCommandAndParameters(quer);
                    var response = loadCmd.ExecuteList();

                    Dictionary<string, string> dbinfo = new Dictionary<string, string>();
                    foreach (var item in response)
                    {
                        ////              osv7
                        ///{ApiReSentence:db-path=/user-manager5|found-legacy-db-path=user-manager|db-size=15161944|free-disk-space=49680384}
                        
                        dbinf.db_path = item.GetResponseFieldOrDefault("db-path", "");
                        if (Global_Variable.Mk_resources.version >= 7)
                        {
                            dbinf.size = Convert.ToInt32(item.GetResponseFieldOrDefault("db-size", "0"));
                            dbinf.foundlegacydbpath = (item.GetResponseFieldOrDefault("found-legacy-db-path=user-manager", null));
                            dbinf.freediskspace = (item.GetResponseFieldOrDefault("free-disk-space", ""));
                        }
                        else
                            dbinf.size = Convert.ToInt32(item.GetResponseFieldOrDefault("size", "0"));
                        dbinf.log_size = Convert.ToInt32(item.GetResponseFieldOrDefault("log-size", "0"));
                        dbinf.log_in_use = Convert.ToInt32(item.GetResponseFieldOrDefault("log-in-use", "0"));
                        dbinf.last_rebuild = (item.GetResponseFieldOrDefault("last-rebuild", null));
                        dbinf.last_clear = (item.GetResponseFieldOrDefault("last-clear", null));
                        dbinf.last_save = (item.GetResponseFieldOrDefault("last-save", null));
                        dbinf.last_load = (item.GetResponseFieldOrDefault("last-load", null));
                    }
                }
                catch { Global_Variable.Update_Um_StatusBar(false, true, -1, "", "خطأ في قراءه قاعدة بيانات اليوزمنجر");Thread.Sleep(3000); }
            }
            return dbinf;
        }


        [Obsolete]
        public  bool add_Edit_Profile2(UserManager_Profile_UserManager profle, bool add = true)
        {
            if (Global_Variable.Mk_resources.version>=7)
                return add_Edit_Profile_v7(profle,add);

            bool status = false;
            //mk.Send("=profile=" + profile);
            //mk.Send("=limitation=" + limit, true);

            if (add)
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return false;
                    try
                    {
                        string script = "/tool/user-manager/profile/add";

                        var createProfile = connection.CreateCommandAndParameters(script,
                            "owner", profle.owner,
                            "name", profle.Name,
                            "name-for-users", profle.Name,
                            "validity", profle.Validity_str.ToString(),
                            "price", profle.Price.ToString(),
                            "override-shared-users", profle.SharedUsers.ToString()
                            );
                        var id_profile = createProfile.ExecuteScalar();
                        if (id_profile == null) return false;
                        else
                        {
                            var createLimt = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/add",
                               "owner", profle.owner,
                               "name", profle.Name,
                               "uptime-limit", profle.uptimeLimit_str.ToString(),
                               "transfer-limit", profle.transferLimit.ToString(),
                               "group-name", profle.groupName.ToString(),
                               "rate-limit-tx", profle.download_tx.ToString(),
                                "rate-limit-min-tx", profle.download_tx.ToString(),
                                "rate-limit-rx", profle.upload_rx.ToString(),
                                "rate-limit-min-rx", profle.upload_rx.ToString()
                            );
                            var id_limt = createLimt.ExecuteScalar();
                            if (id_limt == null) return false;
                            else
                            {
                                var createLimt_profle = connection.CreateCommandAndParameters("/tool/user-manager/profile/profile-limitation/add",
                               "profile", profle.Name,
                               "limitation", profle.Name,
                               "weekdays", profle.weekdays,
                               "from-time", profle.from_time,
                               "till-time", profle.till_time

                            );
                                var id_limt_profile = createLimt_profle.ExecuteScalar();
                                if (id_limt_profile == null) return false;
                                else
                                {
                                    return true;
                                }
                            }

                            //status = true;
                        }



                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
            }
            else
            {
                try
                {
                    using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                    {
                        if (Mk_Conn(connection) == false)
                            return false;
                        try
                        {
                            var updateProfile = connection.CreateCommandAndParameters("/tool/user-manager/profile/set",
                                "owner", profle.owner,
                                "name", profle.Name,
                                "name-for-users", profle.Name,
                                "validity", profle.Validity_str.ToString(),
                                "price", profle.Price.ToString(),
                                "override-shared-users", profle.SharedUsers.ToString(),
                                TikSpecialProperties.Id, profle.IdHX);
                            updateProfile.ExecuteNonQuery();

                            if(profle.IdHX_prfileLimt != null)
                            {
                                var updateLimt = connection.CreateCommandAndParameters("/tool/user-manager/profile/profile-limitation/set",
                                    "profile", profle.Name,
                                   "limitation", profle.Name,
                                    "weekdays", profle.weekdays,
                                    "from-time", profle.from_time,
                                    "till-time", profle.till_time,
                                   TikSpecialProperties.Id, profle.IdHX_prfileLimt);

                                updateLimt.ExecuteNonQuery();
                            }

                            if (profle.IdHX_limt != null)
                            {
                                var updateLimt = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/set",
                                    "owner", profle.owner,
                                    //"name", profle.Name,
                                    "uptime-limit", profle.uptimeLimit_str.ToString(),
                                    "transfer-limit", profle.transferLimit.ToString(),
                                    "group-name", profle.groupName.ToString(),
                                    "rate-limit-tx", profle.download_tx.ToString(),
                                    "rate-limit-min-tx", profle.download_tx.ToString(),
                                    "rate-limit-rx", profle.upload_rx.ToString(),
                                    "rate-limit-min-rx", profle.upload_rx.ToString(),
                                    TikSpecialProperties.Id, profle.IdHX_limt);

                                updateLimt.ExecuteNonQuery();


                            }
                            else
                            {
                                var createLimt = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/add",
                                   "owner", profle.owner,
                                   "name", profle.Name,
                                   "uptime-limit", profle.uptimeLimit_str.ToString(),
                                   "transfer-limit", profle.transferLimit.ToString(),
                                   "group-name", profle.groupName.ToString(),
                                   "rate-limit-tx", profle.download_tx.ToString(),
                                    "rate-limit-min-tx", profle.download_tx.ToString(),
                                    "rate-limit-rx", profle.upload_rx.ToString(),
                                    "rate-limit-min-rx", profle.upload_rx.ToString()
                                );
                                var id_limt = createLimt.ExecuteScalar();
                                if (id_limt == null) return false;
                                else
                                {
                                    var createLimt_profle = connection.CreateCommandAndParameters("/tool/user-manager/profile/profile-limitation/add",
                                   "profile", profle.Name,
                                   "limitation", profle.Name,
                                    "weekdays", profle.weekdays,
                                    "from-time", profle.from_time,
                                    "till-time", profle.till_time
                                );
                                    var id_limt_profile = createLimt_profle.ExecuteScalar();
                                    if (id_limt_profile == null) return false;
                                    else
                                    {
                                        return true;
                                    }
                                }
                            }
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message, "Error"); status = false; }
            }
            return status;
        }
        [Obsolete]
        public string Add_UserManager_Profile(UmProfile profle, bool add = true)
        {
            string id_profile = null;

            //if (Global_Variable.Mk_resources.version >= 7)
            //    return add_Edit_Profile_v7(profle, add);

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;

                id_profile = _Add_UserManager_Profile(connection, profle);
                if (id_profile != null)
                {
                    if (_Add_UserManager_Limition(connection, profle) == null)
                    {
                        Random rnd = new Random();
                        //profle.Name = profle.Name + rnd.Next(10, 20);
                        profle.Name_limt= profle.Name + rnd.Next(10, 20);
                        if (_Add_UserManager_Limition(connection, profle) == null)
                            return null;
                    }
                    if (_Add_UserManager_Profile_Limition(connection, profle) == null)
                        return null;
                }
            }
            return id_profile;
        }

        [Obsolete]
        public bool Edit_UserManager_Profile(UmProfile profle, bool add = true)
        {
            bool status = false;

            //if (Global_Variable.Mk_resources.version >= 7)
            //    return add_Edit_Profile_v7(profle, add);

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;

                if( _Edit_UserManager_Profile(connection, profle))
                {
                    if (profle.IdHX_limt != null)
                        _Edit_UserManager_Limition(connection, profle);
                    else
                    {
                        profle.Name_limt = profle.Name;
                        if (_Add_UserManager_Limition(connection, profle) == null)
                        {
                            Random rnd = new Random();
                            //profle.Name = profle.Name + rnd.Next(1, 99);
                            profle.Name_limt = profle.Name + rnd.Next(1, 99);
                            //profle.Name_limt= profle.Name;  
                            if (_Add_UserManager_Limition(connection, profle) == null)
                                return false;
                        }

                        if (_Add_UserManager_Profile_Limition(connection, profle) == null)
                            return false;
                        return true;
                    }

                    if (_Edit_UserManager_Profile_Limition(connection, profle))
                        return true;
                }
                
            }
            return status;
        }

        public string _Add_UserManager_Profile(ITikConnection connection, UmProfile profle)
        {
            string res = null;
            try
            {
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    //string script = "/tool/user-manager/profile/add";
                    var createProfile = connection.CreateCommandAndParameters("/user-manager/profile/add",
                        //"owner", profle.Owner,
                        "name", profle.Name,
                        "name-for-users", profle.Name,
                        "validity", profle.Str_Validity.ToString(),
                        "price", profle.Price.ToString(),
                        "starts-when", "first-auth",
                        "override-shared-users", profle.SharedUsers.ToString()
                        );
                    res = createProfile.ExecuteScalar();
                }
                else
                {
                    //string script = "/tool/user-manager/profile/add";
                    var createProfile = connection.CreateCommandAndParameters("/tool/user-manager/profile/add",
                        "owner", profle.Owner,
                        "name", profle.Name,
                        "name-for-users", profle.Name,
                        "validity", profle.Str_Validity.ToString(),
                        "price", profle.Price.ToString(),
                        "override-shared-users", profle.SharedUsers.ToString()
                        );
                    res = createProfile.ExecuteScalar();
                }
            }
            catch { }
            return res;
        }
        public string _Add_UserManager_Limition(ITikConnection connection, UmProfile profle)
        {
            string res = null;
            try
            {
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    var createLimt = connection.CreateCommandAndParameters("/user-manager/limitation/add",
                                //"owner", profle.Owner,
                                "name", profle.Name_limt,
                                "uptime-limit", profle.Str_uptimeLimit.ToString(),
                                "transfer-limit", profle.TransferLimit.ToString(),
                                //"group-name", profle.GroupName.ToString(),
                                "rate-limit-tx", profle.Download_tx.ToString(),
                                 "rate-limit-min-tx", profle.Download_tx.ToString(),
                                 "rate-limit-rx", profle.Upload_rx.ToString(),
                                 "rate-limit-min-rx", profle.Upload_rx.ToString()
                             );
                    res = createLimt.ExecuteScalar();
                }
                else
                {
                    var createLimt = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/add",
                                "owner", profle.Owner,
                                "name", profle.Name_limt,
                                "uptime-limit", profle.Str_uptimeLimit.ToString(),
                                "transfer-limit", profle.TransferLimit.ToString(),
                                "group-name", profle.GroupName.ToString(),
                                "rate-limit-tx", profle.Download_tx.ToString(),
                                 "rate-limit-min-tx", profle.Download_tx.ToString(),
                                 "rate-limit-rx", profle.Upload_rx.ToString(),
                                 "rate-limit-min-rx", profle.Upload_rx.ToString()
                             );
                    res = createLimt.ExecuteScalar();
                }
            }
            catch { }
            return res;
        }
        public string _Add_UserManager_Profile_Limition(ITikConnection connection, UmProfile profle)
        {
            string res = null;
            try
            {
                string code = "/tool/user-manager/profile/profile-limitation/add";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/profile-limitation/add";
                var createLimt_profle = connection.CreateCommandAndParameters(code,
                           "profile", profle.Name,
                           "limitation", profle.Name_limt,
                           "weekdays", profle.Weekdays,
                           "from-time", profle.From_time,
                           "till-time", profle.Till_time
                        );
                res = createLimt_profle.ExecuteScalar();
            }
            catch { }
            return res;
        }

        public bool _Edit_UserManager_Profile(ITikConnection connection, UmProfile profle)
        {
            bool status = false;
            try
            {
                string code = "/tool/user-manager/profile/set";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/profile/set";
                var updateProfile = connection.CreateCommandAndParameters(code,
                               //"owner", profle.Owner,
                               //"name", profle.Name,
                               "name-for-users", profle.Name,
                               "validity", profle.Str_Validity.ToString(),
                               "price", profle.Price.ToString(),
                               "override-shared-users", profle.SharedUsers.ToString(),
                               TikSpecialProperties.Id, profle.IdHX);
                updateProfile.ExecuteNonQuery();
                status = true;
            }
            catch { }
            return status;
        }
        public bool _Edit_UserManager_Limition(ITikConnection connection, UmProfile profle)
        {
            bool res = false;
            try
            {
                string code = "/tool/user-manager/profile/limitation/set";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/limitation/set";

                if (Global_Variable.Mk_resources.version <= 6)
                {
                    var updateLimt = connection.CreateCommandAndParameters(code,
                                     //"owner", profle.Owner,
                                     //"name", profle.Name,
                                     "uptime-limit", profle.Str_uptimeLimit.ToString(),
                                     "transfer-limit", profle.TransferLimit.ToString(),
                                     "group-name", profle.GroupName.ToString(),
                                     "rate-limit-tx", profle.Download_tx.ToString(),
                                     "rate-limit-min-tx", profle.Download_tx.ToString(),
                                     "rate-limit-rx", profle.Upload_rx.ToString(),
                                     "rate-limit-min-rx", profle.Upload_rx.ToString(),
                                     TikSpecialProperties.Id, profle.IdHX_limt);

                    updateLimt.ExecuteNonQuery();
                }
                else
                {
                    var updateLimt_V7 = connection.CreateCommandAndParameters(code,
                                    //"owner", profle.Owner,
                                    //"name", profle.Name,
                                    "uptime-limit", profle.Str_uptimeLimit.ToString(),
                                    "transfer-limit", profle.TransferLimit.ToString(),
                                    //"group-name", profle.GroupName.ToString(),
                                    "rate-limit-tx", profle.Download_tx.ToString(),
                                    "rate-limit-min-tx", profle.Download_tx.ToString(),
                                    "rate-limit-rx", profle.Upload_rx.ToString(),
                                    "rate-limit-min-rx", profle.Upload_rx.ToString(),
                                    TikSpecialProperties.Id, profle.IdHX_limt);

                    updateLimt_V7.ExecuteNonQuery();
                }
                res = true;
            }
            catch { }
            return res;
        }
        public bool _Edit_UserManager_Profile_Limition(ITikConnection connection, UmProfile profle)
        {
            bool status = false;
            try
            {
                string code = "/tool/user-manager/profile/profile-limitation/set";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/profile-limitation/set";


                var updateLimt = connection.CreateCommandAndParameters(code,
                                   //"profile", profle.Name,
                                  //"limitation", profle.Name,
                                   "weekdays", profle.Weekdays,
                                   "from-time", profle.From_time,
                                   "till-time", profle.Till_time,
                                  TikSpecialProperties.Id, profle.IdHX_prfileLimt);

                updateLimt.ExecuteNonQuery();
                status = true;
            }
            catch { }
            return status;
        }

        [Obsolete]
        public  bool add_Edit_Profile_v7(UserManager_Profile_UserManager profle, bool add = true)
        {
            bool status = false;
            if (add)
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return false;
                    try
                    {

                        string script = "/user-manager/profile/add";
                        var createProfile = connection.CreateCommandAndParameters(script,
                            //"owner", profle.owner,
                            "name", profle.Name,
                            "name-for-users", profle.Name,
                            "validity", profle.Validity_str.ToString(),
                            "price", profle.Price.ToString(),
                             "starts-when", "first-auth",
                            "override-shared-users", profle.SharedUsers.ToString()
                            );
                        var id_profile = createProfile.ExecuteScalar();
                        if (id_profile == null) return false;
                        else
                        {
                            var createLimt = connection.CreateCommandAndParameters("/user-manager/limitation/add",
                               //"owner", profle.owner,
                               "name", profle.Name,
                               "uptime-limit", profle.uptimeLimit_str.ToString(),
                               "transfer-limit", profle.transferLimit.ToString(),
                               //"group-name", profle.groupName.ToString(),
                               "rate-limit-tx", profle.download_tx.ToString(),
                                "rate-limit-min-tx", profle.download_tx.ToString(),
                                "rate-limit-rx", profle.upload_rx.ToString(),
                                "rate-limit-min-rx", profle.upload_rx.ToString()
                            );
                            var id_limt = createLimt.ExecuteScalar();
                            if (id_limt == null) return false;
                            else
                            {
                                var createLimt_profle = connection.CreateCommandAndParameters("/user-manager/profile-limitation/add",
                               "profile", profle.Name,
                               "limitation", profle.Name

                            );
                                var id_limt_profile = createLimt_profle.ExecuteScalar();
                                if (id_limt_profile == null) return false;
                                else
                                {
                                    return true;
                                }
                            }

                            //status = true;
                        }



                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
            }
            else
            {
                try
                {
                    using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                    {
                        if (Mk_Conn(connection) == false)
                            return false;
                        try
                        {
                            var updateProfile = connection.CreateCommandAndParameters("/user-manager/profile/set",
                                //"owner", profle.owner,
                                "name", profle.Name,
                                "name-for-users", profle.Name,
                                "validity", profle.Validity_str.ToString(),
                                "price", profle.Price.ToString(),
                                "override-shared-users", profle.SharedUsers.ToString(),
                                TikSpecialProperties.Id, profle.IdHX);
                            updateProfile.ExecuteNonQuery();

                            if (profle.IdHX_limt != null)
                            {
                                var updateLimt = connection.CreateCommandAndParameters("/user-manager/limitation/set",
                                    //"owner", profle.owner,
                                    //"name", profle.Name,
                                    "uptime-limit", profle.uptimeLimit_str.ToString(),
                                    "transfer-limit", profle.transferLimit.ToString(),
                                    //"group-name", profle.groupName.ToString(),
                                    "rate-limit-tx", profle.download_tx.ToString(),
                                    "rate-limit-min-tx", profle.download_tx.ToString(),
                                    "rate-limit-rx", profle.upload_rx.ToString(),
                                    "rate-limit-min-rx", profle.upload_rx.ToString(),
                                    TikSpecialProperties.Id, profle.IdHX_limt);

                                updateLimt.ExecuteNonQuery();


                            }
                            else
                            {
                                var createLimt = connection.CreateCommandAndParameters("/user-manager/profile-limitation/add",
                                   "owner", profle.owner,
                                   "name", profle.Name,
                                   "uptime-limit", profle.uptimeLimit_str.ToString(),
                                   "transfer-limit", profle.transferLimit.ToString(),
                                   "group-name", profle.groupName.ToString(),
                                   "rate-limit-tx", profle.download_tx.ToString(),
                                    "rate-limit-min-tx", profle.download_tx.ToString(),
                                    "rate-limit-rx", profle.upload_rx.ToString(),
                                    "rate-limit-min-rx", profle.upload_rx.ToString()
                                );
                                var id_limt = createLimt.ExecuteScalar();
                                if (id_limt == null) return false;
                                else
                                {
                                    var createLimt_profle = connection.CreateCommandAndParameters("/user-manager/profile/profile-limitation/add",
                                   "profile", profle.Name,
                                   "limitation", profle.Name
                                );
                                    var id_limt_profile = createLimt_profle.ExecuteScalar();
                                    if (id_limt_profile == null) return false;
                                    else
                                    {
                                        return true;
                                    }
                                }
                            }
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message, "Error"); status = false; }
            }
            return status;
        }
        [Obsolete]
        public List<Hotspot_Source_Profile> Get_Hotspot_Server_Profil()
        {
            List<Hotspot_Source_Profile> customer = new List<Hotspot_Source_Profile>();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                var loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/profile/print");
                var response = loadCmd.ExecuteList();

                foreach (var item in response)
                {
                    Hotspot_Source_Profile sourceCustomer = new Hotspot_Source_Profile();
                    //double sn = 0;
                    string Number_sequence = item.GetResponseFieldOrDefault(".id", "0");
                    //Number_sequence = Number_sequence.TrimStart(new char[] { '*' });
                    sourceCustomer.idHX = Number_sequence;

                    //Number_sequence = Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();

                    //sn = Convert.ToDouble(Number_sequence);
                    sourceCustomer.Name = item.GetResponseFieldOrDefault("name", "");
                    sourceCustomer.Delet_fromServer = 0;
                    sourceCustomer.rb = Global_Variable.Mk_resources.RB_SN;

                    customer.Add(sourceCustomer);
                }
                return customer;
            }


        }
       
        [Obsolete]
        public List<Hotspot_Source_Profile> GetProfileHotspot()
        {
            List<Hotspot_Source_Profile> customer = new List<Hotspot_Source_Profile>();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                var loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/profile/print");
                var response = loadCmd.ExecuteList();

                //{ApiReSentence:.id=*0|name=default|idle-timeout=none|keepalive-timeout=2m|status-autorefresh=1m|shared-users=1|add-mac-cookie=true|mac-cookie-timeout=3d|insert-queue-before=first|queue-type=sfq|on-login=/import SmartValidity/Scripts/SmartLogIn.rsc;|on-logout=/import SmartValidity/Scripts/SmartLogOut.rsc|transparent-proxy=false|default=true}
                foreach (var item in response)
                {
                    Hotspot_Source_Profile sourceCustomer = new Hotspot_Source_Profile();
                    //double sn = 0;
                    string Number_sequence = item.GetResponseFieldOrDefault(".id", "0");
                    //Number_sequence = Number_sequence.TrimStart(new char[] { '*' });
                    sourceCustomer.idHX = Number_sequence;

                    //Number_sequence = Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();

                    //sn = Convert.ToDouble(Number_sequence);
                    sourceCustomer.Name = item.GetResponseFieldOrDefault("name", "");
                    sourceCustomer.Onlogin = item.GetResponseFieldOrDefault("on-login", "");
                    sourceCustomer.Onlogout = item.GetResponseFieldOrDefault("on-logout", "");
                    sourceCustomer.Delet_fromServer = 0;
                    sourceCustomer.rb = Global_Variable.Mk_resources.RB_SN;

                    customer.Add(sourceCustomer);
                }

                Global_Variable.Source_HS_Profile = customer;
                return customer;
            }


        }

        [Obsolete]
        public bool Update_ProfileHotspot(string idx,string onlogin,string onlogout)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;

                try
                {
                    var updateProfile = connection.CreateCommandAndParameters("/ip/hotspot/user/profile/set",
                        "on-login", onlogin,
                        "on-logout", onlogout,
                        TikSpecialProperties.Id, idx
                        );
                    updateProfile.ExecuteNonQuery();
                    status = true;
                }
                catch { }
                return status;


                var loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/profile/print");
                var response = loadCmd.ExecuteList();

                foreach (var item in response)
                {
                    Hotspot_Source_Profile sourceCustomer = new Hotspot_Source_Profile();
                    //double sn = 0;
                    string Number_sequence = item.GetResponseFieldOrDefault(".id", "0");
                    //Number_sequence = Number_sequence.TrimStart(new char[] { '*' });
                    sourceCustomer.idHX = Number_sequence;

                    //Number_sequence = Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();

                    //sn = Convert.ToDouble(Number_sequence);
                    sourceCustomer.Name = item.GetResponseFieldOrDefault("name", "");
                    sourceCustomer.Onlogin = item.GetResponseFieldOrDefault("on-login", "");
                    sourceCustomer.Onlogout = item.GetResponseFieldOrDefault("on-logout", "");
                    sourceCustomer.Delet_fromServer = 0;
                    sourceCustomer.rb = Global_Variable.Mk_resources.RB_SN;

                    //customer.Add(sourceCustomer);
                }
                //return customer;
            }


        }

        [Obsolete]
        public  bool DeletProfileUsermanager(UmProfile profile)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
                //try
                //{
                //    string code = "/tool/user-manager/profile/profile-limitation/remove";
                //    if (Global_Variable.Mk_resources.version >= 7)
                //        code = "/user-manager/profile-limitation/remove";
                //    var deleteProfle_limt = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, profile.IdHX_prfileLimt);
                //    deleteProfle_limt.ExecuteNonQuery();
                //    status=true;
                //}
                //catch { }
                try
                {
                    string code = "/tool/user-manager/profile/remove";
                    if (Global_Variable.Mk_resources.version >= 7)
                        code = "/user-manager/profile/remove";

                    var deleteProfile = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, profile.IdHX);
                    deleteProfile.ExecuteNonQuery();
                    status = true;

                }
                catch { }
                //try
                //{
                //    string code = "/tool/user-manager/profile/limitation/remove";
                //    if (Global_Variable.Mk_resources.version >= 7)
                //        code = "/user-manager/limitation/remove";

                //    var deleteLimte = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, profile.IdHX_limt);
                //    deleteLimte.ExecuteNonQuery();
                //}
                //catch { }
                return status;
            }
        }
      
        [Obsolete]
        public bool DeletLimition_Usermanager(UmProfile profile)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
                //try
                //{
                //    var deleteProfle_limt = connection.CreateCommandAndParameters("/tool/user-manager/profile/profile-limitation/remove", TikSpecialProperties.Id, profile.IdHX_prfileLimt);
                //    deleteProfle_limt.ExecuteNonQuery();
                //}
                //catch { }
                //try
                //{
                //    var deleteProfile = connection.CreateCommandAndParameters("/tool/user-manager/profile/remove", TikSpecialProperties.Id, profile.IdHX);
                //    deleteProfile.ExecuteNonQuery();
                //}
                //catch { }
                for(int i = 0; i < profile.Limits.Count; i++)
                try
                {
                        string code = "/tool/user-manager/profile/limitation/remove";
                        if (Global_Variable.Mk_resources.version >= 7)
                            code = "/user-manager/limitation/remove";

                        var deleteLimte = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, profile.Limits[i].IdHX);
                    //var deleteLimte = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/remove", TikSpecialProperties.Id, profile.IdHX_limt);
                    deleteLimte.ExecuteNonQuery();
                        status= true;
                }
                catch { }


                return status;
            }
        }
     
        [Obsolete]
        public bool DeletLimition_Usermanager(string id_limit)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
               
                    try
                    {
                    string code = "/tool/user-manager/profile/limitation/remove";
                    if (Global_Variable.Mk_resources.version >= 7)
                        code = "/user-manager/limitation/remove";

                    //var deleteLimte = connection.CreateCommandAndParameters("/tool/user-manager/profile/limitation/remove", TikSpecialProperties.Id, profile.Limits[i].IdHX);
                    var deleteLimte = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, id_limit);
                    deleteLimte.ExecuteNonQuery();
                        status = true;
                    }
                    catch { }


                return status;
            }
        }
     
        [Obsolete]
        public bool Delet_ProfileLimition_Usermanager(string id_Profile_limit)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
               
                    try
                    {
                    string code = "/tool/user-manager/profile/profile-limitation/remove";
                    if (Global_Variable.Mk_resources.version >= 7)
                        code = "/user-manager/profile-limitation/remove";

                    var deleteLimte = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, id_Profile_limit);
                    deleteLimte.ExecuteNonQuery();
                        status = true;
                    }
                    catch { }


                return status;
            }
        }

        [Obsolete]
        public bool Delet_ProfileLimition_Usermanager(UmProfile profile)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;

                for (int i = 0; i < profile.Limits.Count; i++)
                    try
                    {
                        string code = "/tool/user-manager/profile/profile-limitation/remove";
                        if (Global_Variable.Mk_resources.version >= 7)
                            code = "/user-manager/profile-limitation/remove";

                        var deleteLimte = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, profile.Profile_Limtitions[i].IdHX);
                        deleteLimte.ExecuteNonQuery();
                        status = true;
                    }
                    catch { }


                return status;
            }
        }
        [Obsolete]
        public HashSet<UmUser> add_one_user_manager(UmUser User)
        {
            HashSet<UmUser> users = new HashSet<UmUser>();
            users.Add(User);

            HashSet<UmUser> Nusers = Add_Group_UserManager_WithProfile(users, User.ProfileName);



            return Nusers;
            string status = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                try
                {
                    var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/add",
                        "username", User.UserName,
                        "password", User.Password,
                        "customer", User.CustomerName,
                        "shared-users", User.SharedUsers,
                        "location", User.Location,
                        "last-name", User.LastName,
                        "comment",User.Comment,
                        "caller-id-bind-on-first-use", User.CallerMac
                        );
                    var id_user = createUser.ExecuteScalar();
                    status = id_user;

                    if (id_user == null)
                    {
                        //return id_user;
                    }
                    else
                    {
                        var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                               //"numbers", id_user,
                               //"numbers", User["username"],
                               "customer", User.CustomerName,
                               "profile", User.ProfileName,
                               //".id", id_user
                               TikSpecialProperties.Id, id_user
                            );
                        addProfile.ExecuteNonQuery();
                    }
                    status = id_user;
                    //return id_user;


                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("failure: such username already exists"))
                        RJMessageBox.Show("الكرت موجود مسبقا");
                    else
                        RJMessageBox.Show(ex.Message);

                    status = null;

                }
            }

            //return status;
        }
        [Obsolete]
        public string Add_one_user_hotspot(HSUser User)
        {
            //HashSet<HSUser> users = new HashSet<HSUser>();
            //users.Add(User);
            string status = "";

            try
            {
                var createUser = connection.CreateCommandAndParameters("/ip/hotspot/user/add",
                    "name", User.UserName,
                    "password", User.Password,
                    "limit-uptime", User.LimitUptime.ToString(),
                    "limit-bytes-total", User.Limitbytestotal.ToString(),
                    "email", User.Email,
                    "server", User.Server,
                    "profile", User.ProfileHotspot,
                    "mac-address", User.CallerMac,
                    "comment", User.Comment

                    );
                var id_user = createUser.ExecuteScalar();
                if (id_user!=null)
                {
                    status = id_user;
                }
                return status;


            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("failure: such username already exists"))
                    RJMessageBox.Show("الكرت موجود مسبقا");
                else
                    RJMessageBox.Show(ex.Message, "خطاء");

                return status;

            }

             
        }

        [Obsolete]
        public HashSet<UmUser> Add_Profile_To_Users(HashSet<UmUser> user,string ProfileName)
        {
            HashSet<UmUser> us = user.ToHashSet();

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                UmUser itm = new UmUser();

                if (Mk_Conn(connection) == false)
                    return null;

                foreach (var item in user)
                {
                    try
                    {
                        itm = item;


                        if (Global_Variable.Mk_resources.version <= 6)
                        {
                            var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                         "customer", item.CustomerName, "profile", ProfileName,
                                         TikSpecialProperties.Id, item.IdHX);
                            addProfile.ExecuteNonQuery();
                        }
                        else
                        {
                            var addProfile = connection.CreateCommandAndParameters("/user-manager/user-profile/add",
                                         "user", item.UserName, 
                                         "profile", ProfileName);
                            //addProfile.ExecuteNonQuery();
                            var id_user = addProfile.ExecuteScalar();
                            if (id_user == null)
                                us.Remove(itm);
                            else
                            {
                                Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
                               long sn = Int32.Parse(id_user.Replace("*",""), System.Globalization.NumberStyles.HexNumber);
                                UmProfile Profile = Global_Variable.UM_Profile.Find(f => f.Name == ProfileName);
                                UmPyment umPyment = new UmPyment
                                {
                                    IdHX=id_user,
                                    AddedDate=new DateTime(DateTime.Now.Year,DateTime.Now.Month,DateTime.Now.Day,DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second),
                                    //AddedDate=DateTime.Now,
                                    UserName=item.UserName,
                                    Fk_Sn_Name=item.Sn_Name,
                                    Sn_Name= sn + "-"+item.UserName,
                                    Sn=sn,
                                    MkId=Global_Variable.Mk_resources.RB_SN,
                                    DeleteFromServer=0,
                                    ProfileName = ProfileName,
                                    ProfileTransferLimit = (long)Profile.TransferLimit,
                                    ProfileUptimeLimit = (long)Profile.UptimeLimit,
                                    ProfileValidity= (long)(Profile.Validity*24*60*60),
                                    Price=Profile.Price,
                                    TotalPrice=Profile.Price,
                                    state= "waiting",
                                };
                                //int r = sql_DataAccess.Execute<UmPyment>("", umPyment);

                                List<UmPyment> py = new List<UmPyment>();
                                py.Add(umPyment);
                               bool res= sql_DataAccess.Add_UMPyement_ToDB(py);

                            }
                        }

                        //var createCommand = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                        //                                    "customer", item.CustomerName,
                        //                                    "profile", ProfileName,
                        //                                    ".id", item.IdHX
                        //                                    );
                        //var id = createCommand.ExecuteScalar();

                        //var response = connection.CallCommandSync("/tool/user-manager/user/create-and-activate-profile", "=profile=" + ProfileName + "", "=customer=admin", "=.id=" + item.IdHX + "");
                        //string ids = ((ITikDoneSentence)response.Single()).GetResponseWord();

                    }
                    catch { us.Remove(itm); }
                }
            }
            return us;
        }

        
        [Obsolete]
        public bool Edit_one_user_manager(UmUser User)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
                try
                {
                    if (string.IsNullOrEmpty(User.CustomerName))
                        User.CustomerName = "admin";
                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/set",
                            "username", User.UserName,
                            "password", User.Password,
                            "customer", User.CustomerName,
                            "shared-users", User.SharedUsers,
                            "location", User.Location,
                            "last-name", User.LastName,
                            "comment", User.Comment,
                            "disabled", User.Disabled == 0 ? "no" : "yes",
                            "caller-id", User.CallerMac,
                            "caller-id-bind-on-first-use", User.Caller_id_yes_no,
                            TikSpecialProperties.Id, User.IdHX
                            );
                        createUser.ExecuteNonQuery();
                    }
                    else
                    {
                        var createUser = connection.CreateCommandAndParameters("/user-manager/user/set",
                            "name", User.UserName,
                            "password", User.Password,
                            //"customer", User.CustomerName,
                            "shared-users", User.SharedUsers,
                            //"location", User.Location,
                            //"last-name", User.LastName,
                            "comment", User.Comment,
                            "disabled", User.Disabled == 0 ? "no" : "yes",
                            "caller-id", User.CallerMac,
                            //"caller-id-bind-on-first-use", User.Caller_id_yes_no,
                            TikSpecialProperties.Id, User.IdHX
                            );
                        createUser.ExecuteNonQuery();
                    }
                    status = true;
                    //var id_user = createUser.ExecuteScalar();
                    //status = id_user;

                    //if (id_user == null) return id_user;
                    //else
                    //{
                    //    var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                    //           //"numbers", id_user,
                    //           //"numbers", User["username"],
                    //           "customer", User.CustomerName,
                    //           "profile", User.ProfileName,
                    //           //".id", id_user
                    //           TikSpecialProperties.Id, id_user
                    //        );
                    //    addProfile.ExecuteNonQuery();
                    //}
                    //status = id_user;
                    //return id_user;


                }
                catch (Exception ex)
                {
                    //if (ex.Message.Contains("failure: such username already exists"))
                    //    RJMessageBox.Show("الكرت موجود مسبقا");
                    //else
                        RJMessageBox.Show(ex.Message);

                    status = false;

                }
            }

            return status;
        }
        [Obsolete]
        public bool Edit_one_user_Hotspot( HSUser User)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
                try
                {


                    var UpdateUser = connection.CreateCommandAndParameters("/ip/hotspot/user/set",
                    //"name", User.UserName,
                    "password", User.Password,
                    "limit-uptime", User.LimitUptime.ToString(),
                    "limit-bytes-total", User.Limitbytestotal.ToString(),
                    "email", User.Email,
                    "server", User.Server,
                    "profile", User.ProfileHotspot,
                    "comment", User.Comment,
                    "mac-address", User.CallerMac,
                    "disabled", ( User.Disabled == 1 ? "yes" : "no"),
                    TikSpecialProperties.Id, User.IdHX

                    );
                    UpdateUser.ExecuteNonQuery();
                    status = true;
                }
                
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    status = false;

                }
            }

            return status;
        }

        public bool Edit_User_Hotspot(List<HSUser> Users)
        {
            bool status = false;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;
                try
                {

                    foreach (HSUser User in Users)
                    {
                        var UpdateUser = connection.CreateCommandAndParameters("/ip/hotspot/user/set",
                        //"name", User.UserName,
                        "password", User.Password,
                        "limit-uptime", User.LimitUptime.ToString(),
                        "limit-bytes-total", User.Limitbytestotal.ToString(),
                        "email", User.Email,
                        "server", User.Server,
                        "profile", User.ProfileHotspot,
                        "comment", User.Comment,
                        "mac-address", User.CallerMac,
                        "disabled", (User.Disabled == 1 ? "yes" : "no"),
                        TikSpecialProperties.Id, User.IdHX

                        );
                        UpdateUser.ExecuteNonQuery();
                        status = true;
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                    status = false;

                }
            }

            return status;
        }
        private string Get_Email_Cards(HSUser user)
        {
            string source_email = "";
            try
            {
                string profil = "";
                int day = 0;
                string price = "0";
                string sp = "0";
                string numPrint = "0";
                string datetimePrint = "";
                string byDayOrHour = "0";
                string first_mac = "0";
                string timeSave = "0";
                string sizeSave = "0";
                string sessionSave = "0";

                 
                    //smartValidatiy_Add = 1;
                    //"profil'day'price'sp'numPrint'datetimePrint'byDayOrHour'mac'timeSave'sizeSave'sessionSave'@smart.befor\r\n";
                    //string[] Res_split = source_email.Split(new string[] { "'" }, StringSplitOptions.None);
                    try { profil = user.ProfileName; } catch { };
                    
                    try { day= (int)user.ValidityLimit /24/60/60; } catch { }
                    
                    //try { day = int.Parse(Res_split[1]) + Addday ; } catch { }
                    try { price = (user.Price.ToString()); } catch { }
                    try { sp = user.SpCode; } catch { }
                    try { numPrint = "0"; } catch { }
                    try { datetimePrint = (DateTime.Now.ToString("yyyy-MM-dd-hh-mm")); } catch { }
                    try { byDayOrHour = Convert.ToBoolean(user.SmartValidatiy_ByDayOrHour).ToString(); } catch { }
                    try { first_mac = Convert.ToBoolean(user.First_mac).ToString(); } catch { }
                    try { timeSave = Convert.ToBoolean(user.SmartValidatiy_timeSave).ToString(); } catch { }
                    try { sizeSave = Convert.ToBoolean(user.SmartValidatiy_sizeSave).ToString(); } catch { }
                    try { sessionSave = Convert.ToBoolean(user.SmartValidatiy_sessionSave).ToString(); ; } catch { }

                     source_email = (profil + "'" +
                     day + "'" +
                     price + "'" +
                     sp + "'" +
                     numPrint + "'" +
                     datetimePrint + "'"
                     + byDayOrHour + "'" +
                     first_mac + "'" +
                     timeSave + "'" +
                     sizeSave + "'" +
                     sessionSave + "'@smart.befor");
                     //sessionSave + "'" + Res_split[11]).ToLower();

                
            }
            catch { }
            return source_email;
        }


        public HashSet<HSUser> Edit_Balance_User_Hotspot(HashSet<HSUser> Users, string typeProcee = "add")
        {
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                HSUser item = null;


                if (typeProcee == "RemoveCards")
                {
                    try
                    {
                        foreach (HSUser User in Users)
                        {
                            item = User;
                            string email = "";
                            var deleteCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/remove", TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();

                            if  (Convert.ToBoolean( User.SmartValidatiy_Add))
                                email = Get_Email_Cards(User);
                            string mac = User.CallerMac;
                            if (string.IsNullOrEmpty(User.CallerMac))
                                mac = "00:00:00:00:00:00";
                            var createUser = connection.CreateCommandAndParameters("/ip/hotspot/user/add",
                                            "name", User.UserName,
                                            "password", User.Password,
                                            "limit-uptime", User.LimitUptime.ToString(),
                                            "limit-bytes-total", User.Limitbytestotal.ToString(),
                                            "email", email,
                                            "server", User.Server,
                                            "profile", User.ProfileHotspot,
                                            "mac-address", mac,
                                            "comment", User.Descr

                    );
                            var id_user = createUser.ExecuteScalar();
                            if (id_user != null)
                            {
                                User.IdHX=id_user;
                                User.Email = email;
                                long sn = Int32.Parse(id_user.Replace("*", ""), System.Globalization.NumberStyles.HexNumber);
                                User.SN = sn;
                                User.Sn_Name = sn + "-" + User.UserName;
                                
                                //==================add payment to db========
                                //HashSet<HSUser> us = new HashSet<HSUser>();
                                //us.Add(User);
                                //HsPyment hsPyment = new HsPyment();
                                //hsPyment.Add_HS_Pyments_after_print(us); 
                            }
                          
                        }
                    }
                    catch (Exception ex)
                    {
                        Users.Remove(item);
                        MessageBox.Show(ex.Message);
                    }

                }
                else
                {
                    try
                    {
                        foreach (HSUser User in Users)
                        {
                            item = User;
                            if (typeProcee == "RestCards")
                            {
                                var deleteCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/reset-counters", TikSpecialProperties.Id, item.IdHX);
                                deleteCmd.ExecuteNonQuery();
                            }
                            var UpdateUser = connection.CreateCommandAndParameters("/ip/hotspot/user/set", "limit-uptime", User.LimitUptime.ToString(), "limit-bytes-total", User.Limitbytestotal.ToString(), "email", User.Email, TikSpecialProperties.Id, item.IdHX);
                            UpdateUser.ExecuteNonQuery();
                        }
                    }
                    catch (Exception ex)
                    {
                        Users.Remove(item);
                        MessageBox.Show(ex.Message);
                    }
                }
            }
            return Users;
        }

        [Obsolete]
        public static string add_one_user_manager(Dictionary<string, string> dUser)
        {
            string status = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;

                //"username", User["username"],
                //            "password", User["password"],
                //            //"customer", User["customer"],
                //            "shared-users", User["shardUser"],
                //            //"location", User["location"],
                //            //"last-name", User["lastName"],
                //            "comment", User["comment"],
                //            "caller-id-bind-on-first-use", User["firstUser"]
                //if (Global_Variable.Mk_resources.version >= 7)
                //{
                //    var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user/set"
                //                                            , "caller-id", "bind",
                //                                            TikSpecialProperties.Id, item.IdHX
                //                                            );
                //    deleteCmd.ExecuteNonQuery();
                //}
                //else
                //{
                //    var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/set"
                //                                            , "caller-id-bind-on-first-use", "yes",
                //                                            TikSpecialProperties.Id, item.IdHX
                //                                            );
                //    deleteCmd.ExecuteNonQuery();
                //}
                //User["username"] = username;
                //User["password"] = password;
                //User["profile"] = clss_InfoPrint.Profile_Name;
                //User["customer"] = clss_InfoPrint.Custumer_UserMan;
                //User["shardUser"] = clss_InfoPrint.ShardUser;
                //User["location"] = clss_InfoPrint.SellingPoint_Value_str;
                //User["lastName"] = clss_InfoPrint.SellingPoint_Value_str;
                //User["comment"] = clss_InfoPrint.Comment;
                //if (clss_InfoPrint.FirstUse)
                //    User["firstUser"] = "yes";
                //else
                //    User["firstUser"] = "no";



                UmUser User = new UmUser
                {
                    UserName = dUser["username"],
                    Password = dUser["password"],
                    CustomerName = dUser["customer"],
                    SharedUsers = dUser["shardUser"],
                    Location = dUser["location"],
                    LastName = dUser["lastName"],
                    Comment = dUser["comment"],
                    ProfileName = dUser["profile"],
                    Attributes = Global_Variable.Mk_resources.version >= 7 ? dUser["attributes"]:"",
                    Group = Global_Variable.Mk_resources.version >= 7 ? dUser["group"] :"default",
                };

                try
                {
                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/add",
                            "username", User.UserName,
                            "password", User.Password,
                            "customer", User.CustomerName,
                            "shared-users", User.SharedUsers,
                            "location", User.Location,
                            "last-name", User.LastName,
                            "comment", User.Comment,
                            "caller-id-bind-on-first-use", dUser["firstUser"]
                            );
                        var id_user = createUser.ExecuteScalar();
                        if (id_user != null)
                        {
                            User.IdHX = id_user;
                            User.SN = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                            User.Sn_Name = User.SN + "-" + User.UserName;
                            bool addedProfile = true;
                            try
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "customer", User.CustomerName,
                                       "profile", User.ProfileName,
                                       TikSpecialProperties.Id, id_user
                                    );
                                addProfile.ExecuteNonQuery();
                                addedProfile = true;
                            }
                            catch
                            {

                            }
                            if (addedProfile == false)
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "customer", User.CustomerName,
                                       "profile", User.ProfileName,
                                       TikSpecialProperties.Id, id_user
                                    );
                                addProfile.ExecuteNonQuery();
                            }
                        }
                        return id_user;
                    }

                    else
                    {
                        string bind = "";
                        if (dUser["firstUser"] == "yes")
                            bind = "";
                        
                        var createUser = connection.CreateCommandAndParameters("/user-manager/user/add",
                           "name", User.UserName,
                           "password", User.Password,
                           "shared-users", User.SharedUsers,
                           "comment", User.Comment,
                            "caller-id", bind
                           );
                        var id_user = createUser.ExecuteScalar();
                        if (id_user != null)
                        {
                            

                            User.IdHX = id_user;
                            User.SN = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                            User.Sn_Name = User.SN + "-" + User.UserName;

                            var addProfile = connection.CreateCommandAndParameters("/user-manager/user-profile/add",
                                   "profile", User.ProfileName,
                                   "user", User.UserName
                                );
                            var id_profile = addProfile.ExecuteScalar();
                            if (id_profile != null)
                            {
                                if (User.ProfileName != "")
                                {
                                    long sn = Int32.Parse(id_profile.Replace("*", ""), System.Globalization.NumberStyles.HexNumber);
                                    UmProfile Profile = Global_Variable.UM_Profile.Find(f => f.Name == User.ProfileName);
                                    if (Profile != null)
                                    {
                                        //UmPyment umPyment = new UmPyment
                                        //{
                                        //    IdHX = id_profile,
                                        //    AddedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second),
                                        //    //AddedDate=DateTime.Now,
                                        //    UserName = User.UserName,
                                        //    Fk_Sn_Name = User.Sn_Name,
                                        //    Sn_Name = sn + "-" + User.UserName,
                                        //    Sn = sn,
                                        //    MkId = Global_Variable.Mk_resources.RB_SN,
                                        //    DeleteFromServer = 0,
                                        //    ProfileName = User.ProfileName,
                                        //    ProfileTransferLimit = (long)Profile.TransferLimit,
                                        //    ProfileUptimeLimit = (long)Profile.UptimeLimit,
                                        //    ProfileValidity = (long)(Profile.Validity * 24 * 60 * 60),
                                        //    Price = Profile.Price,
                                        //    TotalPrice = Profile.Price,
                                        //    state = "waiting",
                                        //};

                                        //List<UmPyment> py = new List<UmPyment>();
                                        //py.Add(umPyment);
                                        //bool res = sql_DataAccess.Add_UMPyement_ToDB(py);
                                    }
                                }
                            }


                            return id_user;
                        }

                    }

                }
                catch (Exception ex)
                {
                    //us.Remove(itm);
                }

                //try
                //{
                //    if (Global_Variable.Mk_resources.version >= 7)
                //    {
                //        var createUser = connection.CreateCommandAndParameters("/user-manager/user/add",
                //            "username", User["username"],
                //            "password", User["password"],
                //            //"customer", User["customer"],
                //            "shared-users", User["shardUser"],
                //            //"location", User["location"],
                //            //"last-name", User["lastName"],
                //            "comment", User["comment"],
                //            "caller-id-bind-on-first-use", User["firstUser"]
                //            );
                //        var id_user = createUser.ExecuteScalar();
                //        status = id_user;

                //        if (id_user == null) return id_user;
                //        else
                //        {
                //            var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                //                   //"numbers", id_user,
                //                   //"numbers", User["username"],
                //                   "customer", User["customer"],
                //                   "profile", User["profile"],
                //                   //".id", id_user
                //                   TikSpecialProperties.Id, id_user
                //                );
                //            addProfile.ExecuteNonQuery();
                //        }
                //        status = id_user;
                //        return id_user;
                //    }



                //    else
                //    {
                //        var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/add",
                //            "username", User["username"],
                //            "password", User["password"],
                //            "customer", User["customer"],
                //            "shared-users", User["shardUser"],
                //            "location", User["location"],





                //            "last-name", User["lastName"],
                //            "comment", User["comment"],
                //            "caller-id-bind-on-first-use", User["firstUser"]
                //            );
                //        var id_user = createUser.ExecuteScalar();
                //        status = id_user;

                //        if (id_user == null) return id_user;
                //        else
                //        {
                //            var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                //                   //"numbers", id_user,
                //                   //"numbers", User["username"],
                //                   "customer", User["customer"],
                //                   "profile", User["profile"],
                //                   //".id", id_user
                //                   TikSpecialProperties.Id, id_user
                //                );
                //            addProfile.ExecuteNonQuery();
                //        }
                //        status = id_user;
                //        return id_user;

                //    }
                //    //return id_user;


                //}
                //catch (Exception ex) 
                //{ 
                //    if(ex.Message.Contains("failure: such username already exists"))
                //        RJMessageBox.Show("الكرت موجود مسبقا"); 
                //    else
                //        RJMessageBox.Show(ex.Message);

                //    status = null; 
                    
                //}
           
            
            
            }

            return status;
        }
        [Obsolete]
        public static string add_one_user_hotspot(Dictionary<string, string> User)
        {
            string status = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;

                //var user = new HotspotUser()
                //{
                //    Name = User["username"],
                //    Password = User["password"],
                //    LimitUptime = User["limtUptime"],
                //    LimitBytesTotal =Convert.ToInt32( User["limtTotal"].ToString()),
                //    Email = User["email"],
                //    Server = User["server"],
                //    Profile = User["Profile"],
                //    Comment = User["note"]

                //};
                //connection.Save(user);

                try
                {
                    var createUser = connection.CreateCommandAndParameters("/ip/hotspot/user/add",
                        "name", User["username"],
                        "password", User["password"],
                        "limit-uptime", User["limtUptime"],
                        "limit-bytes-total", User["limtTotal"],
                        "email", User["email"],
                        "server", User["server"],
                        "profile", User["Profile"],
                        "comment", User["comment"]
                        
                        );
                    var id_user = createUser.ExecuteScalar();
                    status = id_user;

                    
                    status = id_user;
                    return id_user;


                }
                catch (Exception ex)
                {
                    if (ex.Message.Contains("failure: such username already exists"))
                        RJMessageBox.Show("الكرت موجود مسبقا");
                    else
                        RJMessageBox.Show(ex.Message,"خطاء");

                    status = null;

                }
            }

            return status;
        }

        [Obsolete]
        public static string add_Profile_User_ToUserManager(List<string> user, string customer, string profile)
        {
            string status = "";
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;
                if (Global_Variable.Mk_resources.version <= 6)
                {
                    foreach (string card in user)
                    {
                        try
                        {
                            string id_user = "";

                            string script = Properties.Settings.Default.userman_print;
                            if (Global_Variable.Mk_resources.version >= 7)
                                script = Properties.Settings.Default.userman7_print;

                            //script= "/user-manager/profile/print";
                            var createUser = connection.CreateCommandAndParameters(script, "username", card);
                            var response = createUser.ExecuteList();
                            if (response.Count() > 0)
                            {
                                id_user = response.Single().GetId();
                            }
                            if (id_user != null)
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "customer", customer,
                                       "profile", profile,
                                    TikSpecialProperties.Id, id_user
                                    );
                                addProfile.ExecuteNonQuery();
                            }
                            else
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "numbers", card,
                                       "customer", customer,
                                       "profile", profile
                                    );
                                addProfile.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = null; }
                    }
                }
                else
                {
                    foreach (string card in user)
                    {
                        try
                        {
                            string id_user = "";
                            string script = Properties.Settings.Default.userman_print;
                            if (Global_Variable.Mk_resources.version >= 7)
                                script = Properties.Settings.Default.userman7_print;


                            var createUser = connection.CreateCommandAndParameters(script, "username", card);
                            var response = createUser.ExecuteList();
                            if (response.Count() > 0)
                            {
                                id_user = response.Single().GetId();
                            }
                            if (id_user != null)
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "customer", customer,
                                       "profile", profile,
                                    TikSpecialProperties.Id, id_user
                                    );
                                addProfile.ExecuteNonQuery();
                            }
                            else
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "numbers", card,
                                       "customer", customer,
                                       "profile", profile
                                    );
                                addProfile.ExecuteNonQuery();
                            }
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = null; }
                    }
                }

            }

            return status;
        }

        [Obsolete]
        public static string Add_To_UserHotspot_SmartScript(List<SourceCardsHotspot_fromMK> User)
        {
            string status = "";
            int inext = 0;
            int Count = User.Count;
            //Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Count + "  /  " + (0) + " )", "يتم اضافة صلاحيات الهوتسبوت الاصدار الثاني الي الكروت");

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return null;

                foreach (SourceCardsHotspot_fromMK hs in User)
                {
                    inext++;
                    try
                    {
                        var createUser = connection.CreateCommandAndParameters("/ip/hotspot/user/set",
                            "email", hs.email,
                            "comment", hs.comment,
                            TikSpecialProperties.Id, hs.id
                            );
                         createUser.ExecuteNonQuery();
                        Global_Variable.Update_Um_StatusBar(true, false, inext, "( " + Count + "  /  " + (inext) + " )", "يتم اضافة صلاحيات الهوتسبوت الي الكروت");

                        
                    }
                    catch (Exception ex)
                    {
                        //if (ex.Message.Contains("failure: such username already exists"))
                        //    RJMessageBox.Show("الكرت موجود مسبقا");
                        //else
                        //    RJMessageBox.Show(ex.Message, "خطاء");

                        //status = null;

                    }
                }
            }

            return status;
        }

        [Obsolete]
        public static bool add_Profile_User_ToUserManager(New_Generate_Cards user, string customer, string profile)
        {
            bool status = true;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_Conn(connection) == false)
                    return false;

                for (int i = 0; i < user.dicUser.Count; i++)
                {
                     
                        try
                        {
                        string id_u = "";
                        var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/print", "username", user.dicUser.ElementAt(i).Key);
                        var response = createUser.ExecuteList();
                        if (response.Count() > 0)
                        {
                            id_u = response.Single().GetId();
                        }
                        if (id_u != null)
                        {
                            var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                   "customer", customer,
                                   "profile", profile,
                                TikSpecialProperties.Id, id_u
                                );
                            addProfile.ExecuteNonQuery();
                        }



                        Double id = user.dicUser.ElementAt(i).Value.SN;
                            string id_user = "*"+Convert.ToInt32(id).ToString("X");
                            if (id_user != null)
                            {
                                var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                       "customer", customer,
                                       "profile", profile,
                                        "numbers", id_user
                                    //TikSpecialProperties.Id, id_user
                                    );
                                 addProfile.ExecuteNonQuery();
                                 
                            }

                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    
                }

                return status;
            }


        }
        [Obsolete]
        public DataTable GetService()
        {
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                DataTable dt = new DataTable();
                dt.Columns.Add("id");
                dt.Columns.Add("name");
                dt.Columns.Add("port");
                dt.Columns.Add("address");
                dt.Columns.Add("disabled");

                try
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    //============================================================================/ip service print
                    var loadCmd = connection.CreateCommandAndParameters("/ip/service/print");

                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        DataRow row = dt.NewRow();
                        //row[1] = response.Single().GetId();
                        row["id"] = item.GetResponseFieldOrDefault(".id", "");
                        row["name"] = item.GetResponseFieldOrDefault("name", "");
                        row["port"] = item.GetResponseFieldOrDefault("port", "");
                        row["address"] = item.GetResponseFieldOrDefault("address", "");
                        row["disabled"] = item.GetResponseFieldOrDefault("disabled", "");

                        dt.Rows.Add(row);
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
                return dt;
            }
        }
        [Obsolete]
        public string enable_disable_port_ssh(string id, string disabled = "false")
        {
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;

                    var updateCmd = connection.CreateCommandAndParameters("/ip/service/set",
                    "disabled", disabled,
                    TikSpecialProperties.Id, id);
                    updateCmd.ExecuteNonQuery();
                }
            }
            catch { }
            return "";
        }
        [Obsolete]
        public string remove_file_import_after_print(string fileName)
        {
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;

                    //var updateCmd = connection.CreateCommandAndParameters("/file/remove",
                    //"numbers", fileName
                    ////TikSpecialProperties.Id, id
                    //);
                    //updateCmd.ExecuteNonQuery();



                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    var findResponse = loadCmd.ExecuteList();
                    var id = findResponse.Single().GetId();
                    var deleteCmd = connection.CreateCommandAndParameters("/file/remove", TikSpecialProperties.Id, id);
                    deleteCmd.ExecuteNonQuery();
                }
            }
            catch { }
            return "";
        }
        public string Remove_SmartValidity()
        {
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    List<string> rsc = new List<string>();
                    rsc.Add("SmartValidity/Scripts/SmartLogIn.rsc");
                    rsc.Add("SmartValidity/Scripts/SmartLogOut.rsc");
                    rsc.Add("SmartValidity/Scripts/SmartHotspotMangment.rsc");
                    rsc.Add("SmartValidity/Scripts/SmartSaveTimeSizeLeft.rsc");
                    rsc.Add("SmartValidity/Scripts/SmartDisableExpirHSUser.rsc");
                    rsc.Add("SmartValidity/Scripts/SmartCheckAfterShutdown.rsc");

                   foreach(var itm in rsc)
                    {
                        try
                        {
                            var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", itm);
                            var findResponse = loadCmd.ExecuteList();
                            if (findResponse.Count() > 0)
                            {
                                var id = findResponse.Single().GetId();
                                var deleteCmd = connection.CreateCommandAndParameters("/file/remove", TikSpecialProperties.Id, id);
                                deleteCmd.ExecuteNonQuery();
                            }
                        }
                        catch { }
                    }

                    try
                    {
                        //Mk_DataAccess.add_scheduler_mikrotik("SmartImportAllFunction", "/import SmartValidity/Scripts/SmartHotspotMangment.rsc;", "startup", "00:00:00");
                        //Mk_DataAccess.add_scheduler_mikrotik("SmartDisableExpirHSUser", "/import SmartValidity/Scripts/SmartDisableExpirHSUser.rsc;", "startup", "04:00:00");
                        //Mk_DataAccess.add_scheduler_mikrotik("SmartSaveTimeSizeLeft", "/import SmartValidity/Scripts/SmartSaveTimeSizeLeft.rsc;", "startup", "00:10:00");
                        //Mk_DataAccess.add_scheduler_mikrotik("SmartCheckAfterShutdown", "delay 5s; \n /import SmartValidity/Scripts/SmartCheckAfterShutdown.rsc;", "startup", "00:00:00");


                        //var loadCmdImportAll = connection.CreateCommandAndParameters("/system/scheduler/print", "name", "SmartImportAllFunction");
                        //var response = loadCmdImportAll.ExecuteList();
                        //if (response.Count() > 0)
                        //{
                        //    var deleteCmd = connection.CreateCommandAndParameters("/system/scheduler/remove", TikSpecialProperties.Id, response.Single().GetId());
                        //    deleteCmd.ExecuteNonQuery();
                        //}
                        rsc[0] = "SmartImportAllFunction";
                        rsc.Remove("SmartValidity/Scripts/SmartLogOut.rsc");
                        rsc.Remove("SmartValidity/Scripts/SmartHotspotMangment.rsc");
                        foreach (var itm in rsc)
                        {
                            try
                            {
                                string filename= Path.GetFileNameWithoutExtension(itm);
                                var loadCmd = connection.CreateCommandAndParameters("/system/scheduler/print", "name", filename);
                                var findResponse = loadCmd.ExecuteList();
                                if (findResponse.Count() > 0)
                                {
                                    var deleteCmd = connection.CreateCommandAndParameters("/system/scheduler/remove", TikSpecialProperties.Id, findResponse.Single().GetId());
                                    deleteCmd.ExecuteNonQuery();
                                }
                            }
                            catch { }
                        }


                    }
                    catch { }

                    //var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    //var findResponse = loadCmd.ExecuteList();
                    //var id = findResponse.Single().GetId();
                    //var deleteCmd = connection.CreateCommandAndParameters("/file/remove", TikSpecialProperties.Id, id);
                    //deleteCmd.ExecuteNonQuery();
                }
            }
            catch { }
            return "";
        }
        [Obsolete]
        public  bool Check_file_found(string fileName)
        {
            bool Status=false;
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return false;

                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                    if (findResponse.Count() > 0)
                        Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }

        [Obsolete]
        public List< Mk_Files> Get_files_From_Mikrotik()
        {
            //bool Status = false;
            List< Mk_Files> Mk_Files = new List< Mk_Files>();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    Mk_Files Files = new Mk_Files();

                    if (Mk_Conn(connection) == false)
                        return null;
                     
                    var loadCmd = connection.CreateCommandAndParameters("/file/print","type", "userman backup");
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)
                        {
                            foreach (var item in findResponse)
                            {
                                try
                                {
                                    Files.FullName = item.GetResponseFieldOrDefault("name", "");
                                    try { Files.Name = Path.GetFileName(item.GetResponseFieldOrDefault("name", "")); } catch { }
                                    Files.Size = Convert.ToDouble(item.GetResponseFieldOrDefault("size", "0"));
                                    //Files.Contents = item.GetResponseFieldOrDefault("contents", "");
                                    Files.Creation_time = utils.String_To_Datetime_By_V_MK(item.GetResponseFieldOrDefault("creation-time", ""));
                                    Files.Type = item.GetResponseFieldOrDefault("Type", "");
                                    Mk_Files.Add(Files);
                                }
                                catch { }
                            }
                        }


                    loadCmd = connection.CreateCommandAndParameters("/file/print","type", "backup");



                }
            }
            catch { }
            return Mk_Files;
        }
        [Obsolete]

        public List<Mk_Files> Get_files_From_Mikrotik_By_Type(string type)
        {
            List<Mk_Files> Mk_Files = new List<Mk_Files>();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    Mk_Files Files = new Mk_Files();

                    if (Mk_Conn(connection) == false)
                        return null;

                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "type", type);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)
                        {
                            foreach (var item in findResponse)
                            {
                                try
                                {
                                    Files.FullName = item.GetResponseFieldOrDefault("name", "");
                                    try { Files.Name = Path.GetFileName(item.GetResponseFieldOrDefault("name", "")); } catch { }
                                    Files.Size = Convert.ToDouble(item.GetResponseFieldOrDefault("size", "0"));
                                    //Files.Contents = item.GetResponseFieldOrDefault("contents", "");
                                    Files.Creation_time = utils.String_To_Datetime_By_V_MK(item.GetResponseFieldOrDefault("creation-time", ""));
                                    Files.Type = item.GetResponseFieldOrDefault("Type", "");
                                    Mk_Files.Add(Files);
                                }
                                catch { }
                            }
                        }
                }
            }
            catch { }
            return Mk_Files;
        }

        [Obsolete]
        public bool Check_File_Found(string fileName, ITikConnection connection)
        {
            bool Status = false;
            try
            {
                //using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    //if (Mk_Conn(connection) == false)
                        //return false;

                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)
                            Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }

        [Obsolete]
        public  bool Check_scheduler_found(string name)
        {
            bool Status=false;
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return false;

                    var loadCmd = connection.CreateCommandAndParameters("/system/scheduler/print", "name", name);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)

                            Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }

        [Obsolete]
        public bool Check_scheduler_found(string name, ITikConnection connection)
        {
            bool Status = false;
            try
            {
                //using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    //if (Mk_Conn(connection) == false)
                        //return false;

                    var loadCmd = connection.CreateCommandAndParameters("/system/scheduler/print", "name", name);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)

                            Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }


        [Obsolete]
        public bool Check_environment_Script_found(string name)
        {
            bool Status = false;
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return false;

                    var loadCmd = connection.CreateCommandAndParameters("/system/script/environment/print", "name", name);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)
                            Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }

 [Obsolete]
        public bool Check_environment_Script_found(string name, ITikConnection connection)
        {
            bool Status = false;
            try
            {
                //using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    //if (Mk_Conn(connection) == false)
                        //return false;

                    var loadCmd = connection.CreateCommandAndParameters("/system/script/environment/print", "name", name);
                    var findResponse = loadCmd.ExecuteList();
                    if (findResponse != null)
                        if (findResponse.Count() > 0)
                            Status = true;

                    //var id = findResponse.Single().GetId();
                }
            }
            catch { }
            return Status;
        }


        [Obsolete]
        public string remove_files_Session(string fileName)
        {
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;

                    //var updateCmd = connection.CreateCommandAndParameters("/file/remove",
                    //"numbers", fileName
                    ////TikSpecialProperties.Id, id
                    //);
                    //updateCmd.ExecuteNonQuery();



                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    var findResponse = loadCmd.ExecuteList();
                    var id = findResponse.Single().GetId();
                    var deleteCmd = connection.CreateCommandAndParameters("/file/remove", TikSpecialProperties.Id, id);
                    deleteCmd.ExecuteNonQuery();
                }
            }
            catch { }
            return "";
        }

        [Obsolete]
        public  Dictionary<string, string> run_import_file(string fileName)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["result"] = "";
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;

                    var loadCmd = connection.CreateCommandAndParameters("/file/print", "name", fileName);
                    var findResponse = loadCmd.ExecuteList();
                    var id = findResponse.Single().GetId();

                    if (id == null)
                        return res;

                    //var RunCmd = connection.CreateCommandAndParameters("/import", TikSpecialProperties.Id, id);
                    var RunCmd = connection.CreateCommandAndParameters("/import", "file-name", fileName);
                    //var findResponse = loadCmd.ExecuteList();
                    string cmdresult = RunCmd.ExecuteScalar();
                    res["status"] = "true";
                    res["result"] = cmdresult;
                }
            }
            catch { }
            return res;
        }

        [Obsolete]
        public Dictionary<string, string> Get_environment_scripts()
        {
            Dictionary<string,string> res = new Dictionary<string,string>();
            res["status"] = "false";
            res["SmartErorrCards"] = "";
            res["SmartErorrProfile"] = "";
   
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                try
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    //==========================================/system script environment print=====name=SmartErorrCards=============================
                    var loadCmd = connection.CreateCommandAndParameters("/system/script/environment/print","name", "SmartErorrCards");
                    var response = loadCmd.ExecuteList();
                    if (response != null) 

                    foreach (var item in response)
                    {
                        res["SmartErorrCards"] = item.GetResponseFieldOrDefault("value", "");
                    }
                     
                    //===================================================================================
                    var loadCmd2 = connection.CreateCommandAndParameters("/system/script/environment/print", "name", "SmartErorrProfile");
                    var response2 = loadCmd2.ExecuteList();
                    if (response2 != null)
                        foreach (var item in response2)
                        {
                            res["SmartErorrProfile"] = item.GetResponseFieldOrDefault("value", "");
                        }
                    res["status"] = "true";

                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }

                 
                return res;
            }
        }
        [Obsolete]
        public Dictionary<string, string> Get_scripts_content()
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["SmartErorrCards"] = "";
            res["SmartErorrProfile"] = "";

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                try
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    //==========================================/system script environment print=====name=SmartErorrCards=============================
                    var loadCmd = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrCards");
                    var response = loadCmd.ExecuteList();
                    if (response != null)

                        foreach (var item in response)
                        {
                            res["SmartErorrCards"] = item.GetResponseFieldOrDefault("source", "");
                        }

                    //===================================================================================
                    var loadCmd2 = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrProfile");
                    var response2 = loadCmd2.ExecuteList();
                    if (response2 != null)
                        foreach (var item in response2)
                        {
                            res["SmartErorrProfile"] = item.GetResponseFieldOrDefault("source", "");
                        }
                    res["status"] = "true";

                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }


                return res;
            }
        }
        [Obsolete]
        public Dictionary<string, string> Get_files_content()
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["SmartErorrCards"] = "";
            res["SmartErorrProfile"] = "";

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                try
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    //==========================================/system script environment print=====name=SmartErorrCards=============================
                    var loadCmd = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrCards");
                    var response = loadCmd.ExecuteList();
                    if (response != null)

                        foreach (var item in response)
                        {
                            res["SmartErorrCards"] = item.GetResponseFieldOrDefault("source", "");
                        }

                    //===================================================================================
                    var loadCmd2 = connection.CreateCommandAndParameters("/system/script/print", "name", "SmartErorrProfile");
                    var response2 = loadCmd2.ExecuteList();
                    if (response2 != null)
                        foreach (var item in response2)
                        {
                            res["SmartErorrProfile"] = item.GetResponseFieldOrDefault("source", "");
                        }
                    res["status"] = "true";

                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }

                return res;
            }
        }
        [Obsolete]
        public HashSet<UmSession> Delete_UmSession_ByID(HashSet<UmSession> user)
        {
            HashSet<UmSession> us = user.ToHashSet();
            int all = user.Count;
            int index = 0;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                UmSession itm = new UmSession();

                if (Mk_Conn(connection) == false)
                    return null;

                string code = "/tool/user-manager/session/remove";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/session/remove";

                foreach (var item in user)
                {
                    try
                    {
                        itm = item;
                       
                        var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                        deleteCmd.ExecuteNonQuery();
                        index++;
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم حذف {index} من {all}");
                        //erorr.Remove(item);
                    }
                    catch { us.Remove(itm); }
                }
            }
            return us;
        }

        [Obsolete]
        public HashSet<UmUser> Delete_UserManager_ByID(HashSet<UmUser> user)
        {
            HashSet<UmUser> us = user.ToHashSet();
            int all = user.Count;
            int index = 0;
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                
                UmUser itm =new UmUser();

                if (Mk_Conn(connection) == false)
                    return null;

                if (Global_Variable.Mk_resources.version >= 7)
                {
                    Sql_DataAccess da= new Sql_DataAccess();
                    foreach (var item in user)
                    {
                       
                        List<UmSession> session = da.Load<UmSession>($"select * from UmSession where UserName='{item.UserName}' and DeleteFromServer=0");
                        List<UmPyment> userProfile = da.Load<UmPyment>($"select * from UmPyment where UserName='{item.UserName}' and DeleteFromServer=0");
                        try
                        {
                            itm = item;
                            var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user/remove", TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            foreach (var up in userProfile)
                            {
                                try
                                {
                                    var delet = connection.CreateCommandAndParameters("/user-manager/user-profile/remove", TikSpecialProperties.Id, up.IdHX);
                                    delet.ExecuteNonQuery();

                                }
                                catch { }
                            }
                            foreach (var uss in session)
                            {
                                try
                                {
                                    var delet = connection.CreateCommandAndParameters("/user-manager/session/remove", TikSpecialProperties.Id, uss.IdHX);
                                    delet.ExecuteNonQuery();
                                }
                                catch { }
                            }
                            //deleteCmd.ExecuteNonQuery();
                            //erorr.Remove(item);
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم حذف {index} من {all}");
                        }
                        catch { us.Remove(itm); }


                    }
                }
                else
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/remove", TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم حذف {index} من {all}");

                            //erorr.Remove(item);
                        }
                        catch { us.Remove(itm); }
                    }
                }
            }
            return us;
        }
        [Obsolete]
        public HashSet<UmProfile> Delete_UmProfile_ByID(HashSet<UmProfile> user)
        {
            HashSet<UmProfile> us = user.ToHashSet();

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                UmProfile itm = new UmProfile();

                if (Mk_Conn(connection) == false)
                    return null;
                foreach (var item in user)
                {
                    try
                    {
                        itm = item;
                        var deleteCmd = connection.CreateCommandAndParameters("/user-manager/userprofile/remove", TikSpecialProperties.Id, item.IdHX);
                        deleteCmd.ExecuteNonQuery();
                        //erorr.Remove(item);
                    }
                    catch { us.Remove(itm); }
                }
            }
            return us;
        }

        [Obsolete]
        public HashSet<UmUser> Process_UserManager_ByID(HashSet<UmUser> user,string typeProcess= "DeleteFromServer")
        {
            HashSet<UmUser> us = user.ToHashSet();

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                //Global_Variable.mk_resources=new Mk_Resources();
                Sql_DataAccess da = new Sql_DataAccess();
                UmUser itm = new UmUser();

                if (Mk_Conn(connection) == false)
                    return null;

                if (typeProcess == "DeleteFromServer")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/tool/user-manager/user/remove";
                            if (Global_Variable.Mk_resources.version >= 7)
                                code = "/user-manager/user/remove";

                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();

                            //==============================================
                            //var delectfromDB = da.Execute<UmUser>($"update UmUser set DeleteFromServer=1 where Sn_Name=@Sn_Name", itm);
                            //==============================================
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "Disabled")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/tool/user-manager/user/disable";
                            if (Global_Variable.Mk_resources.version >= 7)
                                code = "/user-manager/user/disable";
                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();

                            //==============================================
                            //var delectfromDB = da.Execute<UmUser>($"update UmUser set Disabled=1 where Sn_Name=@Sn_Name", itm);
                            //==============================================

                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "Enabled")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/tool/user-manager/user/enable";
                            if (Global_Variable.Mk_resources.version >= 7)
                                code = "/user-manager/user/enable";

                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();

                            //==============================================
                            //var delectfromDB = da.Execute<UmUser>($"update UmUser set Disabled=0 where Sn_Name=@Sn_Name", itm);
                            //==============================================

                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "clearProfile")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            List<UmPyment> umPyments = da.Load<UmPyment>($"select * from UmPyment where UserName='{item.UserName}' and DeleteFromServer=0;");
                            if (Global_Variable.Mk_resources.version >= 7)
                            {
                                foreach(var umPyment in umPyments)
                                {
                                    try
                                    {
                                        var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user-profile/remove", TikSpecialProperties.Id, umPyment.IdHX);
                                        deleteCmd.ExecuteNonQuery();
                                       var delectfromDB = da.Execute<UmPyment>($"update UmPyment set DeleteFromServer=1 where Sn_Name=@Sn_Name",umPyment);

                                    }
                                    catch {  }
                                }
                            }
                            else
                            {
                                string code = "/tool/user-manager/user/clear-profiles";
                                var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                                deleteCmd.ExecuteNonQuery();
                                foreach (var umPyment in umPyments)
                                {
                                    try
                                    {
                                        var delectfromDB = da.Execute<UmPyment>($"update UmPyment set DeleteFromServer=1 where Sn_Name=@Sn_Name", umPyment);

                                    }
                                    catch { }
                                }
                            }
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "RestCards")
                {
                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        RJMessageBox.Show("غير مدعوم في مايكروتك اصدار 7 ");
                        us.Clear();
                        return us;

                    }

                    foreach (var item in user)
                    {
                        try
                        {
                                //itm = item;
                                //string code = "/tool/user-manager/user/disable";
                                //if (Global_Variable.Mk_resources.version >= 7)
                                //    code = "/user-manager/user/disable";
                            //    if (Global_Variable.Mk_resources.version <=6)
                            //{
                                var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/reset-counters", TikSpecialProperties.Id, item.IdHX);
                                deleteCmd.ExecuteNonQuery();
                            //}
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "caller_bind")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            //caller-id=bind
                            itm = item;
                            if (Global_Variable.Mk_resources.version >= 7)
                            {
                                var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user/set"
                                                                        , "caller-id", "bind",
                                                                        TikSpecialProperties.Id, item.IdHX
                                                                        );
                                deleteCmd.ExecuteNonQuery();
                            }
                            else
                            {
                                var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/set"
                                                                        , "caller-id-bind-on-first-use", "yes",
                                                                        TikSpecialProperties.Id, item.IdHX
                                                                        );
                                deleteCmd.ExecuteNonQuery();
                            }
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "Remove_caller_bind")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;

                            if (Global_Variable.Mk_resources.version >= 7)
                            {
                                var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user/set"                                                                        
                                                                       , "caller-id", ""
                                                                       , TikSpecialProperties.Id, item.IdHX
                                                                       );
                                deleteCmd.ExecuteNonQuery();
                            }
                            else
                            {

                                var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/set",
                                                                        "caller-id-bind-on-first-use", "no"
                                                                        , "caller-id", ""
                                                                        , TikSpecialProperties.Id, item.IdHX
                                                                        );
                                deleteCmd.ExecuteNonQuery();
                            }
                        }
                        catch { us.Remove(itm); }
                    }
                }
            }
            return us;
        }
        [Obsolete]
        public HashSet<HSUser> Process_Hotspot_ByID(HashSet<HSUser> user, string typeProcess = "DeleteFromServer")
        {
            int all=user.Count;
            int index = 0;
            HashSet<HSUser> us = user.ToHashSet();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                Sql_DataAccess da = new Sql_DataAccess();
                HSUser itm = new HSUser();

                if (Mk_Conn(connection) == false)
                    return null;

                if (typeProcess == "DeleteFromServer")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/ip/hotspot/user/remove";
                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم حذف {index} من {all}");

                            //==============================================
                            //var delectfromDB = da.Execute<HSUser>($"update HSUser set DeleteFromServer=1 where Sn_Name=@Sn_Name", itm);
                            //==============================================
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "Disabled")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/ip/hotspot/user/disable";
                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم تعطيل {index} من {all}");
                            //==============================================
                            //var delectfromDB = da.Execute<HSUser>($"update HSUser set Disabled=1 where Sn_Name=@Sn_Name", itm);
                            //==============================================

                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "Enabled")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;
                            string code = "/ip/hotspot/user/enable";
                            var deleteCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم تفعيل {index} من {all}");
                            //==============================================
                            //var delectfromDB = da.Execute<HSUser>($"update HSUser set Disabled=0 where Sn_Name=@Sn_Name", itm);
                            //==============================================

                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "RestCards")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            var deleteCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/reset-counters", TikSpecialProperties.Id, item.IdHX);
                            deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم تصفير عداد {index} من {all}");
                        }
                        catch { us.Remove(itm); }
                    }
                }
                else if (typeProcess == "caller_bind")
                {
                    //    foreach (var item in user)
                    //    {
                    //        try
                    //        {
                    //            //caller-id=bind
                    //            itm = item;
                    //            if (Global_Variable.Mk_resources.version >= 7)
                    //            {
                    //                var deleteCmd = connection.CreateCommandAndParameters("/user-manager/user/set"
                    //                                                        , "caller-id", "bind",
                    //                                                        TikSpecialProperties.Id, item.IdHX
                    //                                                        );
                    //                deleteCmd.ExecuteNonQuery();
                    //            }
                    //            else
                    //            {
                    //                var deleteCmd = connection.CreateCommandAndParameters("/tool/user-manager/user/set"
                    //                                                        , "caller-id-bind-on-first-use", "yes",
                    //                                                        TikSpecialProperties.Id, item.IdHX
                    //                                                        );
                    //                deleteCmd.ExecuteNonQuery();
                    //            }
                    //        }
                    //        catch { us.Remove(itm); }
                    //    }
                }
                else if (typeProcess == "Remove_caller_bind")
                {
                    foreach (var item in user)
                    {
                        try
                        {
                            itm = item;

                                var deleteCmd = connection.CreateCommandAndParameters("/ip/hotspot/user/set"
                                                                       , "mac-address", "00:00:00:00:00:00"
                                                                       , TikSpecialProperties.Id, item.IdHX
                                                                       );
                                deleteCmd.ExecuteNonQuery();
                            index++;
                            Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم الغاء ماك  {index} من {all}");
                        }
                        catch { us.Remove(itm); }
                    }
                }
            }
            return us;
        }
        
        public List<string> Get_UM_Attribute()
        {
            List<string> _Customer = new List<string>();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters("/user-manager/attribute/print");
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        _Customer.Add(item.GetResponseFieldOrDefault("name", ""));
                    }
                    Global_Variable.UM_Attribute = _Customer;
                }
            }
            catch { }
            return _Customer;
        }
        [Obsolete]
        public List<string> Get_UM_Group()
        {
            List<string> group =new List<string>();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters("/user-manager/user/group/print");
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        group.Add(item.GetResponseFieldOrDefault("name", ""));
                    }
                    Global_Variable.UM_Group = group;

                }
                return group;
            }
            catch { }

            return group;
        }
        [Obsolete]
        public HashSet<UmUser> Add_Group_UserManager_WithProfile(HashSet<UmUser> Users,string ProfileName)
        {
            HashSet<UmUser> us = Users.ToHashSet();
            UmUser itm = new UmUser();

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {

                if (Mk_Conn(connection) == false)
                    return null;

                Sql_DataAccess sql_DataAccess = new Sql_DataAccess();

                foreach (var User in Users)
                {
                    itm = User;
                    try
                    {
                        if (Global_Variable.Mk_resources.version <= 6)
                        {
                            var createUser = connection.CreateCommandAndParameters("/tool/user-manager/user/add",
                                "username", User.UserName,
                                "password", User.Password,
                                "customer", User.CustomerName,
                                "shared-users", User.SharedUsers,
                                "location", User.Location,
                                "last-name", User.LastName,
                                "comment", User.Comment
                                //"caller-id-bind-on-first-use", User.CallerMac
                                );
                            var id_user = createUser.ExecuteScalar();
                            if (id_user != null)
                            {
                                User.IdHX = id_user;
                                User.SN = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                                User.Sn_Name = User.SN + "-" + User.UserName;
                                bool addedProfile=true;
                                try
                                {
                                    var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                           "customer", User.CustomerName,
                                           "profile", ProfileName,
                                           TikSpecialProperties.Id, id_user
                                        );
                                    addProfile.ExecuteNonQuery();
                                    addedProfile = true;
                                }
                                catch
                                {

                                }
                                if (addedProfile==false)
                                {
                                    var addProfile = connection.CreateCommandAndParameters("/tool/user-manager/user/create-and-activate-profile",
                                           "customer", User.CustomerName,
                                           "profile", ProfileName,
                                           TikSpecialProperties.Id, id_user
                                        );
                                    addProfile.ExecuteNonQuery();
                                }
                            }
                        }

                        else
                        {
                            var createUser = connection.CreateCommandAndParameters("/user-manager/user/add",
                               "name", User.UserName,
                               "password", User.Password,
                               "shared-users", User.SharedUsers,
                               "comment", User.Comment
                               );
                            var id_user = createUser.ExecuteScalar();
                            if (id_user != null)
                            {
                                User.IdHX = id_user;
                                User.SN = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                                User.Sn_Name = User.SN + "-" + User.UserName;

                                var addProfile = connection.CreateCommandAndParameters("/user-manager/user-profile/add",
                                       "profile", ProfileName,
                                       "user", User.UserName
                                    );
                                var id_profile = addProfile.ExecuteScalar();
                                if (id_profile != null)
                                {
                                    if (ProfileName != "")
                                    {
                                        long sn = Int32.Parse(id_profile.Replace("*", ""), System.Globalization.NumberStyles.HexNumber);
                                        UmProfile Profile = Global_Variable.UM_Profile.Find(f => f.Name == ProfileName);
                                        if (Profile != null)
                                        {
                                            UmPyment umPyment = new UmPyment
                                            {
                                                IdHX = id_profile,
                                                AddedDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, DateTime.Now.Day, DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second),
                                                //AddedDate=DateTime.Now,
                                                UserName = User.UserName,
                                                Fk_Sn_Name = User.Sn_Name,
                                                Sn_Name = sn + "-" + User.UserName,
                                                Sn = sn,
                                                MkId = Global_Variable.Mk_resources.RB_SN,
                                                DeleteFromServer = 0,
                                                ProfileName = ProfileName,
                                                ProfileTransferLimit = (long)Profile.TransferLimit,
                                                ProfileUptimeLimit = (long)Profile.UptimeLimit,
                                                ProfileValidity = (long)(Profile.Validity * 24 * 60 * 60),
                                                Price = Profile.Price,
                                                TotalPrice = Profile.Price,
                                                state = "waiting",
                                            };

                                            List<UmPyment> py = new List<UmPyment>();
                                            py.Add(umPyment);
                                            bool res = sql_DataAccess.Add_UMPyement_ToDB(py);
                                        }
                                    }
                                }
                                 
                            }

                        }

                    }
                    catch (Exception ex)
                    {
                        us.Remove(itm);
                    }

                }
            }
            return us;
        }

        //public void Dispose()
        //{
        //    throw new NotImplementedException();
        //}
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        protected virtual void Dispose(bool disposing)
        {
            if (!disposed)
            {
                if (disposing)
                {
                    // Release managed resources
                }

                // Release unmanaged resources

                disposed = true;
            }
        }

        ~Mk_DataAccess()
        {
            Dispose(false);
        }
    }
}
public class UserManager_Database_Info
{
    //{ApiReSentence:db-path=user-manager|size=40737|in-use=80|log-size=6|log-in-use=100|last-rebuild=nov/28/2024 08:00:40
    //|last-clear=jan/02/1970 03:42:30|last-save=dec/04/2024 11:58:53|last-load=dec/04/2024 19:53:10}

    // os v7
    //{ApiReSentence:db-path=/user-manager5|found-legacy-db-path=user-manager|db-size=15161944|free-disk-space=49680384}
    public string db_path { get; set; }
    public long size { get; set; } = 0;
    public long in_use { get; set; } = 0;
    public long log_size { get; set; } = 0;
    public long log_in_use { get; set; } = 0;
    public string last_rebuild { get; set; } 
    public string last_clear { get; set; } 
    public string last_save { get; set; } 
    public string last_load { get; set; } 
    public string foundlegacydbpath { get; set; } 
    public string freediskspace { get; set; } 
}