﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.SellingPoints
{
    public partial class Form_SellingPoint_Alert : RJForms.RJChildForm
    {
        SellingPoint SP = null;
        Alert_SellingPoint sp_alert = null;
        public Form_SellingPoint_Alert(SellingPoint _sp)
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            SP = _sp;
            Set_Font();
            btnSave.Design = ButtonDesign.Delete;
            btnSave.Text = "تفعيل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Download;
        }

        private void Set_Font()
        {

            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Regular);
            lblTitle.Font = title_font;

            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 8 , FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel5.Font = rjLabel1.Font = rjLabel8.Font = 
                lbl_font;

            dgv.AllowUserToOrderColumns = true;
            dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = (int)(40 );

            //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);

            this.Focus();

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            this.Focus();
            return;

        }
        private void getData()
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.Load<Alert_SellingPoint>($"select * from Alert_SellingPoint where  Rb='{Global_Variable.Mk_resources.RB_code}' and SpCode='{SP.Code}' ");

                if (sp.Count > 0)
                {
                    lblTitle.Text = "تعديل تنبيهات الكروت لـ :-  " + SP.UserName;
                    btnSave.Design = ButtonDesign.Confirm;
                    btnSave.Text = "حفظ";
                }

                dgv.DataSource = sp;

                dgv.Columns["Id"].Visible = false;
                dgv.Columns["SpCode"].Visible = false;
                dgv.Columns["Is_Alert"].Visible = false;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void Form_SellingPoint_Alert_Load(object sender, EventArgs e)
        {
            getData();
        }

        private void rjDataGridView2_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1) //not click  on header
            {
                foreach (DataGridViewRow dr in dgv.SelectedRows)
                {
                    Alert_SellingPoint sp = dr.DataBoundItem as Alert_SellingPoint;
                    if (sp != null)
                    {
                        sp_alert = sp;
                        txt_ProfileName.Text = sp.ProfileName.ToString();
                        txt_Count_Soon.Text = sp.Count_Soon.ToString();
                        txt_Count_Finsh.Text = sp.Count_Finsh.ToString();
                        Toggle_Active.Checked = Convert.ToBoolean(sp.Is_Alert);
                    }
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                if (btnSave.Text == "تفعيل")
                {
                    List<Alert_SellingPoint> SPComm = new List<Alert_SellingPoint>();
                    foreach (UmProfile profile in Global_Variable.UM_Profile)
                    {
                        Alert_SellingPoint comm = new Alert_SellingPoint();
                        comm.SpCode = SP.Code;
                        comm.ProfileName = profile.Name;
                        comm.Count_Soon = 75;
                        comm.Count_Finsh = 95;
                        comm.Rb_Sn = Global_Variable.Mk_resources.RB_SN;
                        comm.Rb = Global_Variable.Mk_resources.RB_code;
                        comm.Is_Alert = 0;
                        SPComm.Add(comm);
                    }
                    string commndInsert = $"INSERT OR IGNORE into Alert_SellingPoint (SpCode,ProfileName,Count_Soon,Count_Finsh,Rb_Sn,Rb) values(@SpCode,@ProfileName,@Count_Soon,@Count_Finsh,@Rb_Sn,@Rb) ;";
                    int InsertEffecs = smart_DataAccess.Execute<Alert_SellingPoint>(commndInsert, SPComm.ToHashSet());
                    if (InsertEffecs > 0)
                    {
                        btnSave.Text = "حفظ";
                        getData();
                    }
                }

                else
                {
                    if (sp_alert == null)
                        return;

                    if (!int.TryParse(txt_Count_Finsh.Text, out int value)  || !int.TryParse(txt_Count_Soon.Text, out int value2))
                    {
                        RJMessageBox.Show("النسبه يجب ان تكون رقم صحيح ");
                        return;

                    }

                    sp_alert.Count_Soon = (int)Convert.ToDouble(txt_Count_Soon.Text);
                    sp_alert.Count_Finsh = (int)Convert.ToDouble(txt_Count_Finsh.Text);
                    sp_alert.Is_Alert = Convert.ToInt32(Toggle_Active.Checked);

                    if(sp_alert.Count_Soon<0 || sp_alert.Count_Soon>100)
                    {
                        RJMessageBox.Show("النسبه يجب ان تكون اكبر من الصفر واقل من 100 ");
                        return;
                    }
                    if (sp_alert.Count_Finsh < 0 || sp_alert.Count_Finsh > 100)
                    {
                        RJMessageBox.Show("النسبه يجب ان تكون اكبر من الصفر واقل من 100 ");
                        return;
                    }

                    string comnd = $"update Alert_SellingPoint set Is_Alert=@Is_Alert,Count_Soon=@Count_Soon,Count_Finsh=@Count_Finsh where Id={sp_alert.Id}";
                    int effecs = smart_DataAccess.Execute<Alert_SellingPoint>(comnd, sp_alert);
                    if (effecs > 0)
                        RJMessageBox.Show("تم تعديل تنبية الباقه بنجاح");
                    else
                        RJMessageBox.Show("حدث خطاء");
                    getData();
                }

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }
    }
}
