using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// المشغل الرئيسي لجميع الاختبارات - بدون مشاكل Designer
    /// </summary>
    public static class MainTestRunner
    {
        /// <summary>
        /// تشغيل الاختبار الرئيسي
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                RunMainTest();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل الاختبار:\n{ex.Message}", "خطأ", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل الاختبار الرئيسي
        /// </summary>
        public static void RunMainTest()
        {
            var form = new Form
            {
                Text = "🧪 RJTabControl - الاختبار الرئيسي",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(45, 45, 48),
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false
            };

            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(30)
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "🎉 RJTabControl جاهز للاستخدام!",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(30, 30),
                Size = new Size(540, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الوصف
            var descLabel = new Label
            {
                Text = "جميع الأخطاء تم إصلاحها وجميع الميزات تعمل بشكل مثالي.\n" +
                       "اختر نوع الاختبار الذي تريد تشغيله:",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(30, 80),
                Size = new Size(540, 60),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // زر الاختبار البسيط
            var simpleTestButton = new RJButton
            {
                Text = "اختبار بسيط",
                IconChar = IconChar.Play,
                Location = new Point(80, 160),
                Size = new Size(180, 60),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 12,
                Font = new Font("Segoe UI", 12, FontStyle.Bold)
            };
            simpleTestButton.Click += (s, e) => {
                form.Hide();
                SimpleTabTestForm.RunTest();
                form.Show();
            };

            // زر الاختبار المتقدم
            var advancedTestButton = new RJButton
            {
                Text = "اختبار متقدم",
                IconChar = IconChar.Cogs,
                Location = new Point(280, 160),
                Size = new Size(180, 60),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 12,
                Font = new Font("Segoe UI", 12, FontStyle.Bold)
            };
            advancedTestButton.Click += (s, e) => {
                form.Hide();
                FinalTestForm.RunTest();
                form.Show();
            };

            // زر اختبار سريع
            var quickTestButton = new RJButton
            {
                Text = "اختبار سريع",
                IconChar = IconChar.Bolt,
                Location = new Point(80, 240),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            quickTestButton.Click += (s, e) => {
                form.Hide();
                TestRJTabControl.QuickTest();
                form.Show();
            };

            // زر اختبار التحديثات
            var updatesTestButton = new RJButton
            {
                Text = "اختبار التحديثات",
                IconChar = IconChar.Sync,
                Location = new Point(280, 240),
                Size = new Size(180, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            updatesTestButton.Click += (s, e) => {
                form.Hide();
                TestUpdatesForm.RunTest();
                form.Show();
            };

            // معلومات الحالة
            var statusLabel = new Label
            {
                Text = "✅ جميع الأخطاء مصلحة\n" +
                       "✅ RJPanel يدعم BorderSize و BorderColor\n" +
                       "✅ RJTextBox يدعم ReadOnly\n" +
                       "✅ tabsPanel و contentPanel الآن RJPanel\n" +
                       "✅ الألوان الافتراضية محفوظة",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(150, 150, 150),
                Location = new Point(30, 320),
                Size = new Size(540, 100),
                TextAlign = ContentAlignment.TopCenter
            };

            // زر الخروج
            var exitButton = new RJButton
            {
                Text = "خروج",
                IconChar = IconChar.Times,
                Location = new Point(250, 430),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10, FontStyle.Bold)
            };
            exitButton.Click += (s, e) => {
                form.Close();
            };

            // إضافة العناصر
            panel.Controls.Add(titleLabel);
            panel.Controls.Add(descLabel);
            panel.Controls.Add(simpleTestButton);
            panel.Controls.Add(advancedTestButton);
            panel.Controls.Add(quickTestButton);
            panel.Controls.Add(updatesTestButton);
            panel.Controls.Add(statusLabel);
            panel.Controls.Add(exitButton);

            form.Controls.Add(panel);

            // تشغيل النموذج
            form.ShowDialog();
        }

        /// <summary>
        /// اختبار سريع بدون واجهة
        /// </summary>
        public static void QuickConsoleTest()
        {
            Console.WriteLine("🧪 اختبار RJTabControl السريع...");
            
            try
            {
                // إنشاء TabControl
                var tabControl = new RJTabControl();
                Console.WriteLine("✅ تم إنشاء RJTabControl بنجاح");

                // إضافة تاب
                var tab = tabControl.AddTab("اختبار", IconChar.Home);
                Console.WriteLine("✅ تم إضافة تاب بنجاح");

                // إنشاء RJPanel مع حدود
                var panel = new RJPanel
                {
                    BorderSize = 2,
                    BorderColor = Color.Blue,
                    BorderRadius = 10
                };
                Console.WriteLine("✅ تم إنشاء RJPanel مع حدود بنجاح");

                // إنشاء RJTextBox مع ReadOnly
                var textBox = new RJTextBox
                {
                    ReadOnly = true,
                    Text = "اختبار ReadOnly"
                };
                Console.WriteLine("✅ تم إنشاء RJTextBox مع ReadOnly بنجاح");

                Console.WriteLine("🎉 جميع الاختبارات نجحت!");
                Console.WriteLine("✅ RJTabControl جاهز للاستخدام!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
            }
        }
    }
}
