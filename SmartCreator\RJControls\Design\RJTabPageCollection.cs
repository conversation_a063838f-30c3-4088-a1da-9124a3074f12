using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls.Design
{
    /// <summary>
    /// مجموعة التابات للـ Designer - نسخة آمنة
    /// </summary>
    [Editor(typeof(RJTabPageCollectionEditor), typeof(UITypeEditor))]
    public class RJTabPageCollection : IList<RJTabPage>, ICollection<RJTabPage>, IEnumerable<RJTabPage>
    {
        private readonly List<RJTabPage> items;
        private RJTabControl parentControl; // بدون readonly لتجنب المراجع الدائرية

        public RJTabPageCollection(RJTabControl owner)
        {
            this.parentControl = owner; // بدون throw لتجنب مشاكل Designer
            this.items = new List<RJTabPage>();
        }

        #region Properties
        public int Count => items.Count;
        public bool IsReadOnly => false;

        public RJTabPage this[int index]
        {
            get
            {
                if (index < 0 || index >= items.Count) return null;
                return items[index];
            }
            set
            {
                if (value == null || index < 0 || index >= items.Count) return;

                try
                {
                    var oldTab = items[index];
                    items[index] = value;

                    // إشعار التغيير للـ Designer - آمن
                    if (parentControl != null)
                    {
                        parentControl.OnTabPageChanged(oldTab, value, index);
                    }
                }
                catch
                {
                    // تجاهل الأخطاء في Designer
                }
            }
        }

        public RJTabPage this[string name]
        {
            get => items.Find(tab => tab.Name == name);
        }
        #endregion

        #region Methods
        public void Add(RJTabPage item)
        {
            if (item == null) return;

            try
            {
                items.Add(item);
                if (parentControl != null)
                {
                    parentControl.AddTabInternal(item);
                }
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public RJTabPage Add(string text)
        {
            var tab = new RJTabPage(text);
            Add(tab);
            return tab;
        }

        public RJTabPage Add(string text, IconChar icon)
        {
            var tab = new RJTabPage(text, icon);
            Add(tab);
            return tab;
        }

        public void Insert(int index, RJTabPage item)
        {
            if (item == null || index < 0 || index > items.Count) return;

            try
            {
                items.Insert(index, item);
                if (parentControl != null)
                {
                    parentControl.AddTabInternal(item);
                }
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public bool Remove(RJTabPage item)
        {
            if (item == null) return false;
            
            var removed = items.Remove(item);
            if (removed && parentControl != null)
            {
                parentControl.RemoveTabInternal(item);
            }
            return removed;
        }

        public void RemoveAt(int index)
        {
            if (index < 0 || index >= items.Count) return;
            
            var tab = items[index];
            items.RemoveAt(index);
            owner.RemoveTabInternal(tab);
        }

        public void Clear()
        {
            var tabsToRemove = new List<RJTabPage>(items);
            items.Clear();
            
            foreach (var tab in tabsToRemove)
            {
                owner.RemoveTabInternal(tab);
            }
        }

        public bool Contains(RJTabPage item) => items.Contains(item);
        public int IndexOf(RJTabPage item) => items.IndexOf(item);
        public void CopyTo(RJTabPage[] array, int arrayIndex) => items.CopyTo(array, arrayIndex);
        #endregion

        #region IEnumerable
        public IEnumerator<RJTabPage> GetEnumerator() => items.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
        #endregion
    }

    /// <summary>
    /// محرر مجموعة التابات للـ Designer
    /// </summary>
    public class RJTabPageCollectionEditor : CollectionEditor
    {
        public RJTabPageCollectionEditor(Type type) : base(type) { }

        protected override Type CreateCollectionItemType() => typeof(RJTabPage);

        protected override object CreateInstance(Type itemType)
        {
            if (itemType == typeof(RJTabPage))
            {
                try
                {
                    // إنشاء تاب بسيط وآمن مثل SimpleTabCollectionEditor
                    var tab = new RJTabPage($"TabPage{GetItemCount() + 1}");
                    tab.BackColor = System.Drawing.Color.FromArgb(70, 70, 70);
                    tab.ForeColor = System.Drawing.Color.White;
                    tab.IconChar = IconChar.None;
                    return tab;
                }
                catch
                {
                    // في حالة الخطأ، إنشاء تاب أساسي
                    return new RJTabPage("TabPage");
                }
            }
            return base.CreateInstance(itemType);
        }

        private int GetItemCount()
        {
            try
            {
                return Context?.Instance is RJTabControl tabControl ? tabControl.TabCount : 0;
            }
            catch
            {
                return 0;
            }
        }

        protected override string GetDisplayText(object value)
        {
            if (value is RJTabPage tab)
            {
                return !string.IsNullOrEmpty(tab.Text) ? tab.Text : $"TabPage{tab.GetHashCode()}";
            }
            return base.GetDisplayText(value);
        }

        /// <summary>
        /// تحديد ما إذا كان يمكن إزالة العناصر
        /// </summary>
        protected override bool CanRemoveInstance(object value)
        {
            return true; // يمكن إزالة أي تاب
        }

        /// <summary>
        /// تحديد ما إذا كان يمكن تحديد عناصر متعددة
        /// </summary>
        protected override bool CanSelectMultipleInstances()
        {
            return false; // تحديد تاب واحد فقط
        }

        protected override Type[] CreateNewItemTypes()
        {
            return [typeof(RJTabPage)];
        }

        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value)
        {
            // التأكد من أن السياق صحيح
            if (context?.Instance is RJTabControl)
            {
                // فتح محرر المجموعة
                return base.EditValue(context, provider, value);
            }
            return value;
        }
    }
}
