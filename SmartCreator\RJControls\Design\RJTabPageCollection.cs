using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls.Design
{
    /// <summary>
    /// مجموعة التابات للـ Designer
    /// </summary>
    [Editor(typeof(RJTabPageCollectionEditor), typeof(UITypeEditor))]
    public class RJTabPageCollection : IList<RJTabPage>, ICollection<RJTabPage>, IEnumerable<RJTabPage>
    {
        private readonly RJTabControl owner;
        private readonly List<RJTabPage> items;

        public RJTabPageCollection(RJTabControl owner)
        {
            this.owner = owner ?? throw new ArgumentNullException(nameof(owner));
            this.items = new List<RJTabPage>();
        }

        #region Properties
        public int Count => items.Count;
        public bool IsReadOnly => false;

        public RJTabPage this[int index]
        {
            get => items[index];
            set
            {
                if (value == null) throw new ArgumentNullException(nameof(value));
                
                var oldTab = items[index];
                items[index] = value;
                
                // إشعار التغيير للـ Designer
                owner.OnTabPageChanged(oldTab, value, index);
            }
        }

        public RJTabPage this[string name]
        {
            get => items.Find(tab => tab.Name == name);
        }
        #endregion

        #region Methods
        public void Add(RJTabPage item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));
            
            items.Add(item);
            owner.AddTabInternal(item);
        }

        public RJTabPage Add(string text)
        {
            var tab = new RJTabPage(text);
            Add(tab);
            return tab;
        }

        public RJTabPage Add(string text, IconChar icon)
        {
            var tab = new RJTabPage(text, icon);
            Add(tab);
            return tab;
        }

        public void Insert(int index, RJTabPage item)
        {
            if (item == null) throw new ArgumentNullException(nameof(item));
            
            items.Insert(index, item);
            owner.InsertTabInternal(index, item);
        }

        public bool Remove(RJTabPage item)
        {
            if (item == null) return false;
            
            var removed = items.Remove(item);
            if (removed)
            {
                owner.RemoveTabInternal(item);
            }
            return removed;
        }

        public void RemoveAt(int index)
        {
            if (index < 0 || index >= items.Count) return;
            
            var tab = items[index];
            items.RemoveAt(index);
            owner.RemoveTabInternal(tab);
        }

        public void Clear()
        {
            var tabsToRemove = new List<RJTabPage>(items);
            items.Clear();
            
            foreach (var tab in tabsToRemove)
            {
                owner.RemoveTabInternal(tab);
            }
        }

        public bool Contains(RJTabPage item) => items.Contains(item);
        public int IndexOf(RJTabPage item) => items.IndexOf(item);
        public void CopyTo(RJTabPage[] array, int arrayIndex) => items.CopyTo(array, arrayIndex);
        #endregion

        #region IEnumerable
        public IEnumerator<RJTabPage> GetEnumerator() => items.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
        #endregion
    }

    /// <summary>
    /// محرر مجموعة التابات للـ Designer
    /// </summary>
    public class RJTabPageCollectionEditor : CollectionEditor
    {
        public RJTabPageCollectionEditor(Type type) : base(type) { }

        protected override Type CreateCollectionItemType() => typeof(RJTabPage);

        protected override object CreateInstance(Type itemType)
        {
            var tab = new RJTabPage($"TabPage{(Collection?.Count ?? 0) + 1}");
            tab.IconChar = IconChar.None;
            return tab;
        }

        protected override string GetDisplayText(object value)
        {
            if (value is RJTabPage tab)
            {
                return string.IsNullOrEmpty(tab.Text) ? tab.Name : tab.Text;
            }
            return base.GetDisplayText(value);
        }

        protected override Type[] CreateNewItemTypes()
        {
            return new Type[] { typeof(RJTabPage) };
        }

        public override object EditValue(ITypeDescriptorContext context, IServiceProvider provider, object value)
        {
            // التأكد من أن السياق صحيح
            if (context?.Instance is RJTabControl tabControl)
            {
                // فتح محرر المجموعة
                return base.EditValue(context, provider, value);
            }
            return value;
        }
    }
}
