﻿using SmartCreator.Data;
using SmartCreator.Entities.Accounts;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting.Accounts
{
    public partial class Form_Account_Add_Edit : RJChildForm
    {
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes = false;
        Entities.Accounts.Account account;

        public Form_Account_Add_Edit()
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            //Get_Umo();
            lblTitle.Text = "اضافة حساب جديد";
            btnSave.Text = "اضافة";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Plus;

            btnSave.BackColor = RJColors.Confirm;

            txt_code.Text = (Smart_DA.Get_BatchCards_My_Sequence("Account") + 1).ToString();
            fill_AccountType();
            Set_Font();
        }
        public Form_Account_Add_Edit(Entities.Accounts.Account _account)
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();

            lblTitle.Text = "تعديل الحساب";
            btnSave.Text = "تعديل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Edit;
            btnSave.BackColor = RJColors.DefaultFormBorderColor;

            account = _account;
            fill_AccountType();
            txt_code.Text = account.Code;
            txt_code.Enabled = false;
            txt_Description.Text = account.Description;
            txt_name.Text = account.Name;
            //check_Active.Checked = Convert.ToBoolean(account.Active);

            Set_AccountType();
            Set_Font();

        }
        private void fill_AccountType()
        {
            try
            {
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("1", "مصروفات");
                comboSource.Add("3", "مصروفات عامة");
                comboSource.Add("5", "مبيعات");
                comboSource.Add("6", "مردود مبيعات");
                comboSource.Add("9", "ايرادات اخرى");

                rjComboBox1.DataSource = new BindingSource(comboSource, null);
                rjComboBox1.DisplayMember = "Value";
                rjComboBox1.ValueMember = "Key";
                rjComboBox1.SelectedIndex = 0;
                //CBox_OrderBy.Text = "";

            }
            catch { }

        }

        private void Set_AccountType()
        {
            if (account.AccountType == "1")
                rjComboBox1.SelectedIndex = 0;
            else if (account.AccountType == "3")
                rjComboBox1.SelectedIndex = 1;
            else if (account.AccountType == "5")
                rjComboBox1.SelectedIndex = 2;
            else if (account.AccountType == "6")
                rjComboBox1.SelectedIndex = 3;
            else if (account.AccountType == "9")
                rjComboBox1.SelectedIndex = 4;

            

        }

        private void Set_Font()
        {
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel5.Font = rjLabel1.Font =  rjLabel3.Font =  rjLabel5.Font = rjLabel7.Font =
           txt_code.Font = txt_name.Font =
           lbl_font;
            this.Focus();

             rjComboBox1.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            string type = "1";

            if (rjComboBox1.SelectedIndex == 0)
                type = "1";
            else if (rjComboBox1.SelectedIndex == 1)
                type = "3";
            else if (rjComboBox1.SelectedIndex == 2)
                type = "5";
             
            else if (rjComboBox1.SelectedIndex == 3)
                type = "6";
             
            else if (rjComboBox1.SelectedIndex == 4)
                type = "9";
             

            if (add)
            {
                long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Account where Code='{txt_code.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");

                if (foundsp > 0)
                {
                    RJMessageBox.Show("رقم او كود الحساب موجود مسبقا");
                    return;
                }
                if (txt_code.Text == "")
                {
                    RJMessageBox.Show("رقم الحساب مطلوب");
                    return;
                }
                if (txt_name.Text == "")
                {
                    RJMessageBox.Show("اسم الحساب مطلوب");
                    return;
                }

                if (rjComboBox1.Text == "")
                {
                    RJMessageBox.Show("حدد نوع الحساب");
                    return;
                }

                Entities.Accounts.Account sp = new Entities.Accounts.Account();
                sp.Code = txt_code.Text;
                sp.Name = txt_name.Text;
                sp.Description = txt_Description.Text;

                sp.Rb = Global_Variable.Mk_resources.RB_SN;



                lock (Smart_DataAccess.Lock_object)
                {
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Code", "Description", "Name", "AccountType", "Rb" };

                    Fields.AddRange(aFields);


                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Account");
                    if (new_sp > 0)
                    {
                        //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                        RJMessageBox.Show("تمت عمليه الاضافة");
                        succes = true;
                        int x = Smart_DA.Update_MySequence("Account", Convert.ToInt32(txt_code.Text));
                        this.Close();
                        return;
                    }
                    RJMessageBox.Show("خطاء");
                    return;
                }
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = {"Name", "AccountType", "Description" };
                Fields.AddRange(aFields);
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var dataa = new Entities.Accounts.Account();
                        dataa.Id = account.Id;
                        dataa.Name = txt_name.Text;
                        dataa.AccountType = type;

                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Partner>("Account", Fields, $" where Id=@Id and   Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(dataa, sqlquery);
                        if (r > 0)
                        {
                            //RJMessageBox.Show("تمت عمليه الاضافة");
                            succes = true;
                            this.Close();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch { }
            }
            //succes = true;
            //this.Close();
        }
    }
}
