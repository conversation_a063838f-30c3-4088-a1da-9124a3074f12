﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.ComponentModel.DataAnnotations;

namespace SmartCreator.Models
{
    /// <summary>
    /// نموذج للإعدادات العامة للتطبيق
    /// </summary>
    public class AppSetting
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// مجموعة الإعدادات (مثل: Database, UI, Network, etc.)
        /// </summary>
        [Required]
        [MaxLength(50)]
        public string Category { get; set; }

        /// <summary>
        /// مفتاح الإعداد
        /// </summary>
        [Required]
        [MaxLength(100)]
        public string SettingKey { get; set; }

        /// <summary>
        /// قيمة الإعداد
        /// </summary>
        public string SettingValue { get; set; }

        /// <summary>
        /// نوع البيانات
        /// </summary>
        [MaxLength(50)]
        public string DataType { get; set; } = "String";

        /// <summary>
        /// القيمة الافتراضية
        /// </summary>
        public string DefaultValue { get; set; }

        /// <summary>
        /// وصف الإعداد
        /// </summary>
        [MaxLength(200)]
        public string Description { get; set; }

        /// <summary>
        /// هل الإعداد مطلوب
        /// </summary>
        public bool IsRequired { get; set; } = false;

        /// <summary>
        /// هل يمكن تعديل الإعداد
        /// </summary>
        public bool IsEditable { get; set; } = true;

        /// <summary>
        /// تاريخ الإنشاء
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        public DateTime UpdatedAt { get; set; } = DateTime.Now;
    }
}
