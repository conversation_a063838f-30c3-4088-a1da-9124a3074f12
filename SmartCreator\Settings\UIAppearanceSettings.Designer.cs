﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace SmartCreator.Settings {
    
    
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.11.0.0")]
    internal sealed partial class UIAppearanceSettings : global::System.Configuration.ApplicationSettingsBase {
        
        private static UIAppearanceSettings defaultInstance = ((UIAppearanceSettings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new UIAppearanceSettings())));
        
        public static UIAppearanceSettings Default {
            get {
                return defaultInstance;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("0")]
        public int Theme {
            get {
                return ((int)(this["Theme"]));
            }
            set {
                this["Theme"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool ColorFormBorder {
            get {
                return ((bool)(this["ColorFormBorder"]));
            }
            set {
                this["ColorFormBorder"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool ChildFormMarker {
            get {
                return ((bool)(this["ChildFormMarker"]));
            }
            set {
                this["ChildFormMarker"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool FormIconActiveMenuItem {
            get {
                return ((bool)(this["FormIconActiveMenuItem"]));
            }
            set {
                this["FormIconActiveMenuItem"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool MultiChildForms {
            get {
                return ((bool)(this["MultiChildForms"]));
            }
            set {
                this["MultiChildForms"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("5")]
        public int FormBorderSize {
            get {
                return ((int)(this["FormBorderSize"]));
            }
            set {
                this["FormBorderSize"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool Language_en {
            get {
                return ((bool)(this["Language_en"]));
            }
            set {
                this["Language_en"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("3")]
        public int Style {
            get {
                return ((int)(this["Style"]));
            }
            set {
                this["Style"] = value;
            }
        }
        
        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool Language_ar {
            get {
                return ((bool)(this["Language_ar"]));
            }
            set {
                this["Language_ar"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("True")]
        public bool DGV_RTL
        {
            get
            {
                return ((bool)(this["DGV_RTL"]));
            }
            set
            {
                this["DGV_RTL"] = value;
            }
        }
    }
}
