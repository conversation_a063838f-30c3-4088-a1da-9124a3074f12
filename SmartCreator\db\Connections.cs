﻿using Newtonsoft.Json.Linq;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using ConnectionInfo = SmartCreator.Data.ConnectionInfo;

namespace SmartCreator.db
{
    //[DebuggerStepThrough]
    public class Connections
    {
        internal static JArray _connections = new JArray();

        private static Dictionary<string, IDbConnection> buffer = new Dictionary<string, IDbConnection>();
        public Connections() { }
        public static void Show()
        {
            new FormConnections().ShowDialog();
        }

        private static IEnumerable<Type> GetTypesByAttribute()
        {
            List<TableInfo> list = new List<TableInfo>();
            Assembly[] assemblies = AppDomain.CurrentDomain.GetAssemblies();
            for (int i = 0; i < assemblies.Length; i++)
            {
                Type[] types = assemblies[i].GetTypes();
                foreach (Type type in types)
                {
                    if (type.GetCustomAttributes(typeof(AutoGenerateTableAttribute), inherit: true).Length == 0)
                    {
                        continue;
                    }

                    int order = 0;
                    using (IEnumerator<CustomAttributeData> enumerator = type.GetCustomAttributesData().GetEnumerator())
                    {
                        if (enumerator.MoveNext())
                        {
                            CustomAttributeData current = enumerator.Current;
                            if (current.AttributeType == typeof(AutoGenerateTableAttribute))
                            {
                                order = (int)current.ConstructorArguments[0].Value;
                            }
                        }
                    }

                    list.Add(new TableInfo
                    {
                        Order = order,
                        type = type
                    });
                }
            }

            return (from r in list
                    orderby r.Order
                    select r.type).ToList();
        }

        public static ConnectionInfo GetConnectionInfo(string name = null)
        {
            if (File.Exists(Config.ConnectionsFile))
            {
                _connections = JArray.Parse(File.ReadAllText(Config.ConnectionsFile));
            }

            if (_connections.Count() == 0)
            {
                throw new Exception("No connections added");
            }

            List<ConnectionInfo> list = _connections.ToObject<List<ConnectionInfo>>();
            if (name != null)
            {
                return list.Where((ConnectionInfo r) => r.Name.Trim().ToLower() == name.Trim().ToLower())?.FirstOrDefault() ?? throw new Exception("Connection '" + name + "' not found");
            }

            if (_connections.Count() == 1)
            {
                return list[0];
            }

            IEnumerable<ConnectionInfo> source = list.Where((ConnectionInfo r) => r.Default);
            if (source.Count() > 0)
            {
                return source.ToList()[0];
            }

            return list[0];
        }

        public static List<ConnectionInfo> GetConnectionsInfo()
        {
            if (File.Exists(Config.ConnectionsFile))
            {
                _connections = JArray.Parse(File.ReadAllText(Config.ConnectionsFile));
            }

            if (_connections.Count() == 0)
            {
                return null;
            }

            return _connections.ToObject<List<ConnectionInfo>>();
        }

        public static IDbConnection GetConnection(string name = null)
        {
            ////return new IDbConnection(); 
            //string key = "default";
            //if (name != null)
            //{
            //    key = name;
            //}

            //if (buffer.ContainsKey(key))
            //{
            //    IDbConnection dbConnection = buffer[key];
            //    if (dbConnection.State != 0 && dbConnection.State != ConnectionState.Broken)
            //    {
            //        while (dbConnection.State != ConnectionState.Open)
            //        {
            //            Thread.Sleep(500);
            //            try
            //            {
            //                Application.DoEvents();
            //            }
            //            catch (Exception)
            //            {
            //            }
            //        }

            //    return dbConnection;
            //    }
            //}

            //ConnectionInfo connectionInfo = GetConnectionInfo(name);
            //Dictionary<string, IOrmLiteDialectProvider> dictionary = new Dictionary<string, IOrmLiteDialectProvider>();
            ////dictionary.Add("SQL Server", SqlServerDialect.Provider);
            ////dictionary.Add("SQL Server Compact", SqlServerDialect.Provider);
            ////dictionary.Add("MySql", MySqlDialect.Provider);
            ////dictionary.Add("MariaDb", MySqlDialect.Provider);
            ////dictionary.Add("PostgreSql", PostgreSqlDialect.Provider);
            ////dictionary.Add("SQLlite", SqliteDialect.Provider);
            ////dictionary.Add("Oracle", OracleDialect.Provider);
            ////dictionary.Add("Firebird", FirebirdDialect.Provider);
            //try
            //{
            //    //StringConverter converter = OrmLiteConfig.DialectProvider.GetStringConverter();
            //    //converter.UseUnicode = true;
            //    //converter.StringLength = 200;


            //    IOrmLiteDialectProvider provider = (dictionary[connectionInfo.Type]);
            //    string ConnectionString = connectionInfo.ConnectionString;

            //    //if (provider == SqliteDialect.Provider)
            //    //{
            //    //    OrmLiteConfig.OnModelDefinitionInit = modelDef =>
            //    //    {
            //    //        modelDef.FieldDefinitions
            //    //            .Where(x => x.ColumnType == typeof(DateTime) || x.ColumnType==typeof(string)||x.Name== "PercentageType" || x.Name== "Server")
            //    //            .Each(x =>
            //    //            {
            //    //                x.CustomFieldDefinition = "text";

            //    //                if (x.Name == "PercentageType")
            //    //                {
            //    //                    x.DefaultValue = "0";
            //    //                }
            //    //                if (x.Name == "Server")
            //    //                {
            //    //                    x.DefaultValue = "0";
            //    //                }
            //    //                //x.DefaultValue = "'0000-01-01 09:00:00.000000'";
            //    //                //x.FieldTypeDefaultValue = "text";
            //    //                //x.CustomFieldDefinition = "text";
            //    //                //x.ColumnType = DbType.String;
            //    //            });
            //    //    };

            //    //}
            //    //else
            //    //{
            //    provider.GetStringConverter().UseUnicode = true;
            //    provider.GetStringConverter().StringLength = 255;
            //    //}
            //    using (IDbConnection dbConnection2 = new OrmLiteConnectionFactory(ConnectionString, provider).Open())
            //    {




            //        //type.GetProperty(nameof(UmUser)).AddAttributes(new DateTime()); // ==> TEXT

            //        if (Config.AutoGenerateTables)
            //        {
            //            //dbConnection2.CreateTableIfNotExists(GetTypesByAttribute().ToArray());

            //            dbConnection2.CreateTableIfNotExists<BatchCard>();
            //            dbConnection2.CreateTableIfNotExists<SellingPoint>();
            //            dbConnection2.CreateTableIfNotExists<UmUser>();
            //            dbConnection2.CreateTableIfNotExists<UmPyment>();
            //            dbConnection2.CreateTableIfNotExists<UmSession>();

            //            dbConnection2.CreateTableIfNotExists<HSUser>();


            //            //dbConnection2.ExecuteSql("ALTER TABLE UmUser MODIFY COLUMN LastSeenAt VARCHAR(255);");
            //            //dbConnection2.ExecuteSql(" ALTER TABLE UmUser RENAME TO tmpUmUser;");
            //            //dbConnection2.ExecuteSql(" CREATE TABLE UmUser(field_a INT, field_b INT);");
            //            //ALTER TABLE users MODIFY COLUMN email VARCHAR(255);

            //            //dbConnection2.ExecuteSql("ALTER TABLE UmUser ALTER COLUMN LastSeenAt text");
            //            //Alter table TableName Alter Column ColumnName nvarchar(100)
            //            dbConnection2.Close();

            //        }


            //        if (buffer.ContainsKey(key))
            //        {
            //            buffer[key] = dbConnection2;
            //        }
            //        else
            //        {
            //            buffer.Add(key, dbConnection2);
            //        }

            //        return dbConnection2;
            //    }

            //}
            //catch (Exception)
            //{
            //    throw;
            //}
            return null;
        }

    }
    //public class AlfaDateTimeConverter : DateTimeConverter
    //{
    //    public override DbType DbType => DbType.DateTime;
    //    public override string ColumnDefinition => "text";
    //}


}
