﻿using Newtonsoft.Json;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Models.API
{
    public class ApiResponse
    {
        //public ApiResponse() { }
        public string StatusCode;
        public string ErrorMessage;
        public string ReasonPhrase;
        public Boolean IsScuccess { get; set; }

        public dynamic responseObjects;

        public License License { get; set; }
        //public orderApp OrderFromApp { get; set; }
        public string Respons_ads { get; set; }
        public ICollection<Notify> Notifies { get; set; }
        //public ICollection<orderApp> OrderFromApp { get; set; }
        public ICollection<Session> Sessions { get; set; }
        public ICollection<License> Customers { get; set; }

        public string toJsonString()
        {
            return JsonConvert.SerializeObject(responseObjects);
        }
        public string toDeJsonString()
        {
            return JsonConvert.SerializeObject(responseObjects);
        }


        public override string ToString()
        {
            return String.Format(" StatusCode: {0} \n ReasonPhrase: {1} \n ErrorMessage: {2} \n Is Success: {3}", StatusCode, ReasonPhrase, ErrorMessage, IsScuccess);
        }

    }
    public class Notify
    {
        //public int NotifyID { get; set; }
        public string NotifyType { get; set; }
        public string Title { get; set; }
        public string Message { get; set; }
        public string Phones { get; set; }
        public string Websites { get; set; }
        public string Emails { get; set; }
        public string Notes { get; set; }
        public string imgeUrl { get; set; }
        public string htmlCode { get; set; }
        public string webBrowser { get; set; }
        public Boolean Status { get; set; }
        public Boolean use_Duration { get; set; }
        //public string date_start { get; set; }
        //public string date_end { get; set; }

        private DateTime? date_start;
        public DateTime Date_start
        {
            get { return date_start ?? DateTime.UtcNow; }
            set { date_start = value; }
        }

        private DateTime? date_end;
        public DateTime Date_end
        {
            get { return date_end ?? DateTime.UtcNow; }
            set { date_end = value; }
        }
        //public IList<appVerion> product { get; set; }




        //public double? AppVersion { get; set; }
    }
    public class Session
    {
        public int ID { get; set; }
        public string RouterID { get; set; }

        private DateTime? sessionDate;
        public DateTime SessionDate
        {
            get { return sessionDate ?? DateTime.UtcNow; }
            set { sessionDate = value; }
        }
        public string Description { get; set; }
    }
    public class License
    {
        //public int LicenseID { get; set; }
        public string Lsn_k { get; set; }
        public string lsn_type { get; set; }
        public bool rb_dis { get; set; }
        public int rb_exp { get; set; }

        public bool App_dis { get; set; }
        public string App_lsn_Type_name { get; set; }
        public int App_lsn_Type_id { get; set; }
        public int App_exp { get; set; }

        //public int identifyRequest { get; set; }


        //private DateTime? licenseDate;
        //public DateTime LicenseDate
        //{
        //    get { return licenseDate ?? DateTime.UtcNow; }
        //    set { licenseDate = value; }
        //}
        //public int LicenseDuration { get; set; }
        //public string LicenseNotes { get; set; }
    }



    public class Response_api
    {
        
        //==============================================
        public bool IsSuccessStatusCode = false;

        public string Respons_ads { get; set; }
        public string Userman { get; set; }
        public string Userman7 { get; set; }
        public string Hotprt { get; set; }
        public string Script { get; set; }
        //==========================
        public string Lk { get; set; }
        public string LkT { get; set; }
        public bool Apdi { get; set; }
        public bool Rbdi { get; set; }
        public int Rbex { get; set; }
        public int RbAexp { get; set; }
        public DateTime? ActivationDate { get; set; }
        public bool RbState { get; set; }
        public int ActivePeriod { get; set; }
        public int TrialPeriod { get; set; }
        public bool RestCash { get; set; }
        public double AppVerion { get; set; }
        public string AppVerionName { get; set; }
        public string licenseCode { get; set; }
        //================
        public string RigesterName { get; set; }
        public string NetworkName { get; set; }
        public string Mobail { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Country { get; set; }
        //===================
        public bool IsMsgShow { get; set; }
        public int CountMsgShow { get; set; }
        public string MsgShow { get; set; }
        public bool IsReadMsg { get; set; }
       
        public bool ForceUpdate_Updater { get; set; }
        public string Updater_Path_App { get; set; }
        public bool ForceUpdate_App { get; set; }
        public string UpdatePath { get; set; }
        public double Next_AppVerion { get; set; }
        public double Next_AppBuilder { get; set; }
        public string ChangLog { get; set; }
        public string FielsReplace { get; set; }
        public string Identity { get; set; } = "";

        public string MsgNextAppVerion { get; set; }
        public bool SowMsgNext_AppVerion_pop { get; set; }
        public bool Notify_finsh { get; set; }=false;
        public int Notify_finsh_days { get; set; }

        //public string RigesterEndDate { get; set; }

        // "date_Register":encoder_sr(str(RB.date_Register)),

        public void setL(Response_api res) 
        {

            //if (Global_Variable.Response_api != null)
            //MessageBox.Show("setLSN1\n\n\n" + res.ToString());
            //MessageBox.Show("setLSN1\n\n\n" + res.IsSuccessStatusCode.ToString());

            if (Global_Variable.Response_api.IsSuccessStatusCode)
            {
                if (Global_Variable.Response_api.IsSuccessStatusCode == false && Properties.Settings.Default.isActive)
                {
                    Properties.Settings.Default.countOpenoffline = Properties.Settings.Default.countOpenoffline + 1;
                    Properties.Settings.Default.Save();
                }
            }
            //if (res == null && Global_Variable.Response_api.IsSuccessStatusCode)
            //    clear_cash();


            try
            {
                //    //if (Convert.ToBoolean(Base64Decode(res.restCash)))
                //    //    clear_cash();


                //Global_Variable.Response_api.Lk = utils.Base64Decode(res.Lk);
                //    Global_Variable.Response_api.App_lce.App_dis = true;

                //    Global_Variable.Response_api.App_lce.rb_dis = Convert.ToBoolean(utils.Base64Decode(res.rbdi));
                //    Global_Variable.Response_api.App_lce.rb_exp = Convert.ToInt32(utils.Base64Decode(res.rbex));
                //    Global_Variable.Response_api.App_lce.App_dis = Convert.ToBoolean(utils.Base64Decode(res.Apdi));

                
                Properties.Settings.Default.rb_active_exp = res.RbAexp;
                //    //MyDataClass.rb_active_exp = Convert.ToInt32(Base64Decode(res.rbAexp));
                Properties.Settings.Default.snactrue = (res.Lk);
                Properties.Settings.Default.Lsn_k_type = (res.LkT);
                Properties.Settings.Default.activationType = (res.LkT);
                Properties.Settings.Default.isActive = res.RbState;
                Properties.Settings.Default.RigesterName = (res.RigesterName);
                Properties.Settings.Default.NetworkName = (res.NetworkName);
                Properties.Settings.Default.mobail = (res.Mobail);
                Properties.Settings.Default.email = (res.Email);
                Properties.Settings.Default.address = (res.Address);
                Properties.Settings.Default.country = (res.Country);
                //    //Properties.Settings.Default. = Base64Decode(res.NetworkName);
                if (res.ActivationDate != null)
                    Properties.Settings.Default.RigesterStartDate = (DateTime)res.ActivationDate;
                //    Properties.Settings.Default.RigesterEndDate = utils.Base64Decode(res.activationDate);
                Properties.Settings.Default.active_Period = (res.ActivePeriod);
                Properties.Settings.Default.userman_print = (res.Userman);
                Properties.Settings.Default.userman7_print = (res.Userman7);
                Properties.Settings.Default.hotspot_print = (res.Hotprt);
                Properties.Settings.Default.script_add = (res.Script);
                //    //Properties.Settings.Default.licenseCodeForActive = Base64Decode(Base64Decode(res.licenseCode));
                Properties.Settings.Default.licenseCodeForActive = (res.licenseCode);

                Properties.Settings.Default.restCash = (res.RestCash);
                Properties.Settings.Default.is_msg_Show = res.IsMsgShow;
                //Properties.Settings.Default.msg_Show =  (Base64Decode(res.msgShow));
                Properties.Settings.Default.msg_Show = res.MsgShow;
                //    //Properties.Settings.Default.msg_Show =  (Base64Decode(DecodeServerName((res.msgShow))));
                Properties.Settings.Default.count_msg_Show = res.CountMsgShow;
                //    //Encoding.UTF8.GetString(Encoding.Default.GetBytes(str));

                //    //Global_Variable.Response_api.AppVerion_from_server = Convert.ToDouble(utils.Base64Decode(res.AppVerion));
                Global_Variable.Response_api.AppVerion = (res.AppVerion);

                Global_Variable.Response_api.ForceUpdate_Updater = ((res.ForceUpdate_Updater));
                Global_Variable.Response_api.Updater_Path_App = (res.Updater_Path_App);
                Global_Variable.Response_api.ForceUpdate_App = ((res.ForceUpdate_App));
                Global_Variable.Response_api.UpdatePath = (res.UpdatePath);
                Global_Variable.Response_api.Next_AppVerion = ((res.Next_AppVerion));
                Global_Variable.Response_api.ChangLog = (res.ChangLog);
                Global_Variable.Response_api.FielsReplace = ((res.FielsReplace));
                //Global_Variable.Response_api.identif = (utils.Base64Decode(res.identif));
                Global_Variable.Response_api.MsgNextAppVerion = ((res.MsgNextAppVerion));

                Global_Variable.Response_api.Next_AppBuilder = ((res.Next_AppBuilder));

                Global_Variable.Response_api.SowMsgNext_AppVerion_pop = (/*Convert.ToBoolean*/(res.SowMsgNext_AppVerion_pop));
                //Properties.Settings.Default.Show_updaeForm = (/*Convert.ToBoolean*/(res.SowMsgNext_AppVerion_pop));

                Global_Variable.Response_api.Notify_finsh = (/*Convert.ToBoolean*/((res.Notify_finsh)));
                Global_Variable.Response_api.Notify_finsh_days = (/*Convert.ToInt32*/((res.Notify_finsh_days)));

                Properties.Settings.Default.countOpenoffline = 0;
                Properties.Settings.Default.Save();

                //MessageBox.Show("setLSN\n\n\n" + Global_Variable.Response_api.ToString());

            }
            catch { Properties.Settings.Default.countOpenoffline = Properties.Settings.Default.countOpenoffline + 1; Properties.Settings.Default.Save(); }
        }



    }

}
