﻿using SmartCreator.RJControls;
using SmartCreator.RJForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator
{
    public partial class Form1 : RJChildForm
    {
        public Form1()
        {
            InitializeComponent();

            //RJGroupBoxTest rJGroupBoxTest = new RJGroupBoxTest();
            //rJGroupBoxTest.ShowDialog();

            //// اختبار الكنترول المبسط
            //DesignerComparisonTest.TestSimpleControlOnly();
            //// اختبار مفصل لكل خاصية
            //DesignerComparisonTest.TestComplexControlDetailed();
            //// مقارنة بين البسيط والمعقد
            //DesignerComparisonTest.RunComparison();


            //// تشغيل جميع الاختبارات من SafeTestRunner
            //SafeTestRunner.ShowTestMenu();

            //// أو اختبار مباشر
            //DesignerComparisonTest.TestComplexControlDetailed();

            return;
            // تشغيل النموذج الرئيسي الجديد
            MainTestForm.ShowTestDialog();

            // إغلاق هذا النموذج
            this.WindowState = FormWindowState.Minimized;
            this.ShowInTaskbar = false;
            this.Hide();
        }
    }
}
