﻿namespace SmartCreator.Forms.UserManager
{
    partial class FormAllCardsUserManager
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        //[System.Obsolete]
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            this.lblNumberItems = new SmartCreator.RJControls.RJLabel();
            this.btnPreviousPage = new SmartCreator.RJControls.RJButton();
            this.btnNextPage = new SmartCreator.RJControls.RJButton();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.View_Hide_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SaveDGVToolStripMenuItem = new FontAwesome.Sharp.IconMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.Status_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SN_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.UserName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Password_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Price_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BachCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.NumberPrint_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SellingPoint_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_TransferLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_DownloadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UploadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_Up_Down_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_RegDate_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_LastSeenAt_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_FirstUse_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTillTime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.الايامالمتبقيةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTimeLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTransferLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Descr_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CusName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CountSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.LastSynDb_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.PageNumber_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Sn_Archive_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Restor_ColumnToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.Copy_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_AllRowToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportText_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمفقطToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.DeleteCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteCardsArchive_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteServerArchiveToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.DisableCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.EnableCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RestCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BindMAC_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RemoveBindMAC_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.PrintCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.AddProfile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ChangeSP_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Remove_SP_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تعديلرقمالطبعةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تعديلرقمالدفعةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.timer_SideBar = new System.Windows.Forms.Timer(this.components);
            this.CBox_Didabled = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Staus = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_Count_Session = new SmartCreator.RJControls.RJPanel();
            this.fpnl_showArchive = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_Show_Archive = new SmartCreator.RJControls.RJToggleButton();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_ByCountSession = new SmartCreator.RJControls.RJToggleButton();
            this.flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_ByCountProfile = new SmartCreator.RJControls.RJToggleButton();
            this.fpnl_showServer = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_Show_onlyServer = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.lbl_to = new SmartCreator.RJControls.RJLabel();
            this.Date_To = new SmartCreator.RJControls.RJDatePicker();
            this.Date_From = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_byDatePrint = new SmartCreator.RJControls.RJCheckBox();
            this.CBox_Customer = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.btnDisable = new SmartCreator.RJControls.RJButton();
            this.btnRefresh_DB = new SmartCreator.RJControls.RJButton();
            this.btnEdit = new SmartCreator.RJControls.RJButton();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.btn_MenuProcess = new SmartCreator.RJControls.RJButton();
            this.btnFirst = new SmartCreator.RJControls.RJButton();
            this.btnNext = new SmartCreator.RJControls.RJButton();
            this.btnLast = new SmartCreator.RJControls.RJButton();
            this.btnPrev = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_Validaty = new SmartCreator.RJControls.RJButton();
            this.btnEnable = new SmartCreator.RJControls.RJButton();
            this.lbl_Filter = new SmartCreator.RJControls.RJLabel();
            this.btnDelete = new SmartCreator.RJControls.RJButton();
            this.rjPanel_back_side = new SmartCreator.RJControls.RJPanel();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.CBox_NumberPrint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_Finsh_Cards = new SmartCreator.RJControls.RJPanel();
            this.btn_RemoveFinsh_Download = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_Uptime = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_All = new SmartCreator.RJControls.RJButton();
            this.panel1 = new System.Windows.Forms.Panel();
            this.rjLabel27 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel_Page = new SmartCreator.RJControls.RJPanel();
            this.Panel_Pages = new System.Windows.Forms.Panel();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.CBox_PageCount = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.txtAllCountRows = new SmartCreator.RJControls.RJTextBox();
            this.txtCurrentPageindex = new SmartCreator.RJControls.RJTextBox();
            this.txtTotalPages = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.rjPane_Top = new SmartCreator.RJControls.RJPanel();
            this.CBox_OrderBy = new SmartCreator.RJControls.RJComboBox();
            this.CBox_SearchBy = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_orderBy = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.dmAll_Cards.SuspendLayout();
            this.pnl_side_Count_Session.SuspendLayout();
            this.fpnl_showArchive.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.flowLayoutPanel2.SuspendLayout();
            this.fpnl_showServer.SuspendLayout();
            this.rjPanel_back_side.SuspendLayout();
            this.pnl_side_Finsh_Cards.SuspendLayout();
            this.panel1.SuspendLayout();
            this.rjPanel_Page.SuspendLayout();
            this.Panel_Pages.SuspendLayout();
            this.panel2.SuspendLayout();
            this.rjPane_Top.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.lbl_note);
            this.pnlClientArea.Controls.Add(this.rjPane_Top);
            this.pnlClientArea.Controls.Add(this.rjPanel_Page);
            this.pnlClientArea.Controls.Add(this.panel1);
            this.pnlClientArea.Controls.Add(this.rjPanel_back_side);
            this.pnlClientArea.Controls.Add(this.lblNumberItems);
            this.pnlClientArea.Controls.Add(this.btnPreviousPage);
            this.pnlClientArea.Controls.Add(this.btnNextPage);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 550);
            this.pnlClientArea.Paint += new System.Windows.Forms.PaintEventHandler(this.pnlClientArea_Paint);
            this.pnlClientArea.Resize += new System.EventHandler(this.pnlClientArea_Resize);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(158, 17);
            this.lblCaption.Text = "FormAllCardsUserManager";
            // 
            // lblNumberItems
            // 
            this.lblNumberItems.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lblNumberItems.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblNumberItems.Font = new System.Drawing.Font("Verdana", 9F);
            this.lblNumberItems.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblNumberItems.LinkLabel = false;
            this.lblNumberItems.Location = new System.Drawing.Point(682, 550);
            this.lblNumberItems.Name = "lblNumberItems";
            this.lblNumberItems.Size = new System.Drawing.Size(166, 25);
            this.lblNumberItems.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblNumberItems.TabIndex = 38;
            this.lblNumberItems.Text = "0";
            this.lblNumberItems.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnPreviousPage
            // 
            this.btnPreviousPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnPreviousPage.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPreviousPage.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPreviousPage.BorderRadius = 5;
            this.btnPreviousPage.BorderSize = 1;
            this.btnPreviousPage.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnPreviousPage.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnPreviousPage.FlatAppearance.BorderSize = 0;
            this.btnPreviousPage.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnPreviousPage.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnPreviousPage.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPreviousPage.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnPreviousPage.ForeColor = System.Drawing.Color.White;
            this.btnPreviousPage.IconChar = FontAwesome.Sharp.IconChar.BackwardStep;
            this.btnPreviousPage.IconColor = System.Drawing.Color.White;
            this.btnPreviousPage.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnPreviousPage.IconSize = 20;
            this.btnPreviousPage.Location = new System.Drawing.Point(854, 550);
            this.btnPreviousPage.Name = "btnPreviousPage";
            this.btnPreviousPage.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnPreviousPage.Size = new System.Drawing.Size(40, 25);
            this.btnPreviousPage.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnPreviousPage.TabIndex = 37;
            this.btnPreviousPage.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnPreviousPage.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnPreviousPage.UseVisualStyleBackColor = false;
            // 
            // btnNextPage
            // 
            this.btnNextPage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnNextPage.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNextPage.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNextPage.BorderRadius = 5;
            this.btnNextPage.BorderSize = 1;
            this.btnNextPage.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnNextPage.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnNextPage.FlatAppearance.BorderSize = 0;
            this.btnNextPage.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnNextPage.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnNextPage.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNextPage.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnNextPage.ForeColor = System.Drawing.Color.White;
            this.btnNextPage.IconChar = FontAwesome.Sharp.IconChar.ForwardStep;
            this.btnNextPage.IconColor = System.Drawing.Color.White;
            this.btnNextPage.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnNextPage.IconSize = 20;
            this.btnNextPage.Location = new System.Drawing.Point(901, 550);
            this.btnNextPage.Name = "btnNextPage";
            this.btnNextPage.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnNextPage.Size = new System.Drawing.Size(40, 25);
            this.btnNextPage.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnNextPage.TabIndex = 36;
            this.btnNextPage.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnNextPage.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnNextPage.UseVisualStyleBackColor = false;
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 2;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(586, 7);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 30);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 30;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(605, 8);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(120, 28);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 29;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle4.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv.ContextMenuStrip = this.dmAll_Cards;
            this.dgv.Customizable = false;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(10, 7);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleLeft;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle5.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 35;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 35;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle6;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 35;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(699, 414);
            this.dgv.TabIndex = 28;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_Users_CellClick);
            this.dgv.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellContentClick);
            this.dgv.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellDoubleClick);
            this.dgv.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dgv_CellFormatting);
            this.dgv.CellPainting += new System.Windows.Forms.DataGridViewCellPaintingEventHandler(this.dgv_CellPainting);
            this.dgv.ColumnHeaderMouseClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dgv_ColumnHeaderMouseClick);
            this.dgv.SelectionChanged += new System.EventHandler(this.dgv_Users_SelectionChanged);
            this.dgv.DoubleClick += new System.EventHandler(this.dgv_DoubleClick);
            this.dgv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv_MouseDown);
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.View_Hide_toolStripMenuItem,
            this.Restor_ColumnToolStripMenuItem,
            this.toolStripMenuItem2,
            this.toolStripSeparator1,
            this.Copy_ToolStripMenuItem,
            this.Copy_AllRowToolStripMenuItem,
            this.ExportExcelToolStripMenuItem,
            this.ExportText_ToolStripMenuItem,
            this.toolStripSeparator2,
            this.DeleteCards_ToolStripMenuItem,
            this.DeleteCardsArchive_ToolStripMenuItem,
            this.DeleteServerArchiveToolStripMenuItem,
            this.DeleteSession_ToolStripMenuItem,
            this.toolStripSeparator3,
            this.DisableCards_ToolStripMenuItem,
            this.EnableCards_ToolStripMenuItem,
            this.RestCards_ToolStripMenuItem,
            this.BindMAC_ToolStripMenuItem,
            this.RemoveBindMAC_ToolStripMenuItem,
            this.toolStripSeparator4,
            this.PrintCards_ToolStripMenuItem,
            this.AddProfile_ToolStripMenuItem,
            this.ChangeSP_ToolStripMenuItem,
            this.Remove_SP_ToolStripMenuItem,
            this.تعديلرقمالطبعةToolStripMenuItem,
            this.تعديلرقمالدفعةToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(278, 512);
            this.dmAll_Cards.Opening += new System.ComponentModel.CancelEventHandler(this.dmAll_Cards_Opening);
            // 
            // View_Hide_toolStripMenuItem
            // 
            this.View_Hide_toolStripMenuItem.Checked = true;
            this.View_Hide_toolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.View_Hide_toolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.SaveDGVToolStripMenuItem,
            this.toolStripSeparator5,
            this.Status_ToolStripMenuItem,
            this.SN_ToolStripMenuItem,
            this.UserName_ToolStripMenuItem,
            this.Password_ToolStripMenuItem,
            this.Price_ToolStripMenuItem,
            this.Profile_ToolStripMenuItem,
            this.BachCards_ToolStripMenuItem,
            this.NumberPrint_toolStripMenuItem,
            this.SellingPoint_ToolStripMenuItem,
            this.Str_UptimeLimit_ToolStripMenuItem,
            this.Str_TransferLimit_ToolStripMenuItem,
            this.Str_UptimeUsed_ToolStripMenuItem,
            this.Str_DownloadUsed_ToolStripMenuItem,
            this.Str_UploadUsed_ToolStripMenuItem,
            this.Str_Up_Down_ToolStripMenuItem,
            this.dt_RegDate_ToolStripMenuItem,
            this.dt_LastSeenAt_ToolStripMenuItem,
            this.dt_FirstUse_ToolStripMenuItem,
            this.Str_ProfileTillTime_ToolStripMenuItem,
            this.الايامالمتبقيةToolStripMenuItem,
            this.Str_ProfileTimeLeft_ToolStripMenuItem,
            this.Str_ProfileTransferLeft_ToolStripMenuItem,
            this.Descr_ToolStripMenuItem,
            this.CusName_ToolStripMenuItem,
            this.Count_profile_ToolStripMenuItem,
            this.CountSession_ToolStripMenuItem,
            this.LastSynDb_ToolStripMenuItem,
            this.PageNumber_ToolStripMenuItem,
            this.Sn_Archive_ToolStripMenuItem});
            this.View_Hide_toolStripMenuItem.Name = "View_Hide_toolStripMenuItem";
            this.View_Hide_toolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.View_Hide_toolStripMenuItem.Text = "عرض واخفاء الاعمدة";
            this.View_Hide_toolStripMenuItem.Click += new System.EventHandler(this.View_Hide_toolStripMenuItem_Click);
            // 
            // SaveDGVToolStripMenuItem
            // 
            this.SaveDGVToolStripMenuItem.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.SaveDGVToolStripMenuItem.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.SaveDGVToolStripMenuItem.IconColor = System.Drawing.Color.White;
            this.SaveDGVToolStripMenuItem.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.SaveDGVToolStripMenuItem.IconSize = 70;
            this.SaveDGVToolStripMenuItem.Name = "SaveDGVToolStripMenuItem";
            this.SaveDGVToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SaveDGVToolStripMenuItem.Text = "حفظ ترتيب الجدول كافتراضي";
            this.SaveDGVToolStripMenuItem.Click += new System.EventHandler(this.SaveDGVToolStripMenuItem_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(242, 6);
            // 
            // Status_ToolStripMenuItem
            // 
            this.Status_ToolStripMenuItem.Checked = true;
            this.Status_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Status_ToolStripMenuItem.Name = "Status_ToolStripMenuItem";
            this.Status_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Status_ToolStripMenuItem.Tag = "Str_Status";
            this.Status_ToolStripMenuItem.Text = "الحــــالــــة";
            this.Status_ToolStripMenuItem.CheckedChanged += new System.EventHandler(this.Status_ToolStripMenuItem_CheckedChanged);
            this.Status_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SN_ToolStripMenuItem
            // 
            this.SN_ToolStripMenuItem.Name = "SN_ToolStripMenuItem";
            this.SN_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SN_ToolStripMenuItem.Tag = "Sn";
            this.SN_ToolStripMenuItem.Text = "الرقم التسلسلي";
            this.SN_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // UserName_ToolStripMenuItem
            // 
            this.UserName_ToolStripMenuItem.Checked = true;
            this.UserName_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.UserName_ToolStripMenuItem.Name = "UserName_ToolStripMenuItem";
            this.UserName_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.UserName_ToolStripMenuItem.Tag = "UserName";
            this.UserName_ToolStripMenuItem.Text = "الاســـــــم";
            this.UserName_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Password_ToolStripMenuItem
            // 
            this.Password_ToolStripMenuItem.Checked = true;
            this.Password_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Password_ToolStripMenuItem.Name = "Password_ToolStripMenuItem";
            this.Password_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Password_ToolStripMenuItem.Tag = "Password";
            this.Password_ToolStripMenuItem.Text = "كلمة المرور";
            this.Password_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Price_ToolStripMenuItem
            // 
            this.Price_ToolStripMenuItem.Name = "Price_ToolStripMenuItem";
            this.Price_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Price_ToolStripMenuItem.Tag = "Str_Price";
            this.Price_ToolStripMenuItem.Text = "الســـــــعر";
            this.Price_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Profile_ToolStripMenuItem
            // 
            this.Profile_ToolStripMenuItem.Name = "Profile_ToolStripMenuItem";
            this.Profile_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Profile_ToolStripMenuItem.Tag = "ProfileName";
            this.Profile_ToolStripMenuItem.Text = "البــــــــاقة";
            this.Profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // BachCards_ToolStripMenuItem
            // 
            this.BachCards_ToolStripMenuItem.Name = "BachCards_ToolStripMenuItem";
            this.BachCards_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.BachCards_ToolStripMenuItem.Tag = "BatchCardId";
            this.BachCards_ToolStripMenuItem.Text = "رقم الـــــدفعــــــه";
            this.BachCards_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // NumberPrint_toolStripMenuItem
            // 
            this.NumberPrint_toolStripMenuItem.Name = "NumberPrint_toolStripMenuItem";
            this.NumberPrint_toolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.NumberPrint_toolStripMenuItem.Tag = "NumberPrint";
            this.NumberPrint_toolStripMenuItem.Text = "رقم الـــطبــــعــــة";
            this.NumberPrint_toolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SellingPoint_ToolStripMenuItem
            // 
            this.SellingPoint_ToolStripMenuItem.Name = "SellingPoint_ToolStripMenuItem";
            this.SellingPoint_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SellingPoint_ToolStripMenuItem.Tag = "SpName";
            this.SellingPoint_ToolStripMenuItem.Text = "نقـــــطة البيع";
            this.SellingPoint_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeLimit_ToolStripMenuItem
            // 
            this.Str_UptimeLimit_ToolStripMenuItem.Name = "Str_UptimeLimit_ToolStripMenuItem";
            this.Str_UptimeLimit_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UptimeLimit_ToolStripMenuItem.Tag = "Str_UptimeLimit";
            this.Str_UptimeLimit_ToolStripMenuItem.Text = "الوقت المسموح";
            this.Str_UptimeLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_TransferLimit_ToolStripMenuItem
            // 
            this.Str_TransferLimit_ToolStripMenuItem.Name = "Str_TransferLimit_ToolStripMenuItem";
            this.Str_TransferLimit_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_TransferLimit_ToolStripMenuItem.Tag = "Str_TransferLimit";
            this.Str_TransferLimit_ToolStripMenuItem.Text = "التنزيل المسموح";
            this.Str_TransferLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeUsed_ToolStripMenuItem
            // 
            this.Str_UptimeUsed_ToolStripMenuItem.Name = "Str_UptimeUsed_ToolStripMenuItem";
            this.Str_UptimeUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UptimeUsed_ToolStripMenuItem.Tag = "Str_UptimeUsed";
            this.Str_UptimeUsed_ToolStripMenuItem.Text = "الوقت المستخدم";
            this.Str_UptimeUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_DownloadUsed_ToolStripMenuItem
            // 
            this.Str_DownloadUsed_ToolStripMenuItem.Name = "Str_DownloadUsed_ToolStripMenuItem";
            this.Str_DownloadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_DownloadUsed_ToolStripMenuItem.Tag = "Str_DownloadUsed";
            this.Str_DownloadUsed_ToolStripMenuItem.Text = "التحميل المستخدم";
            this.Str_DownloadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UploadUsed_ToolStripMenuItem
            // 
            this.Str_UploadUsed_ToolStripMenuItem.Name = "Str_UploadUsed_ToolStripMenuItem";
            this.Str_UploadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UploadUsed_ToolStripMenuItem.Tag = "Str_UploadUsed";
            this.Str_UploadUsed_ToolStripMenuItem.Text = "الرقع المستخدم";
            this.Str_UploadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_Up_Down_ToolStripMenuItem
            // 
            this.Str_Up_Down_ToolStripMenuItem.Name = "Str_Up_Down_ToolStripMenuItem";
            this.Str_Up_Down_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_Up_Down_ToolStripMenuItem.Tag = "Str_Up_Down";
            this.Str_Up_Down_ToolStripMenuItem.Text = "تحميـــــل+رفــــع";
            this.Str_Up_Down_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_RegDate_ToolStripMenuItem
            // 
            this.dt_RegDate_ToolStripMenuItem.Name = "dt_RegDate_ToolStripMenuItem";
            this.dt_RegDate_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_RegDate_ToolStripMenuItem.Tag = "RegDate";
            this.dt_RegDate_ToolStripMenuItem.Text = "تـــاريخ الاضــــافة";
            this.dt_RegDate_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_LastSeenAt_ToolStripMenuItem
            // 
            this.dt_LastSeenAt_ToolStripMenuItem.Name = "dt_LastSeenAt_ToolStripMenuItem";
            this.dt_LastSeenAt_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_LastSeenAt_ToolStripMenuItem.Tag = "LastSeenAt";
            this.dt_LastSeenAt_ToolStripMenuItem.Text = "اخــــــر ظهــــور";
            this.dt_LastSeenAt_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_FirstUse_ToolStripMenuItem
            // 
            this.dt_FirstUse_ToolStripMenuItem.Name = "dt_FirstUse_ToolStripMenuItem";
            this.dt_FirstUse_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_FirstUse_ToolStripMenuItem.Tag = "FirsLogin";
            this.dt_FirstUse_ToolStripMenuItem.Text = "اول دخـــــــــول";
            this.dt_FirstUse_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTillTime_ToolStripMenuItem
            // 
            this.Str_ProfileTillTime_ToolStripMenuItem.Name = "Str_ProfileTillTime_ToolStripMenuItem";
            this.Str_ProfileTillTime_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTillTime_ToolStripMenuItem.Tag = "Str_ProfileTillTime";
            this.Str_ProfileTillTime_ToolStripMenuItem.Text = "تــــاريخ الانتــــهاء";
            this.Str_ProfileTillTime_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // الايامالمتبقيةToolStripMenuItem
            // 
            this.الايامالمتبقيةToolStripMenuItem.Name = "الايامالمتبقيةToolStripMenuItem";
            this.الايامالمتبقيةToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.الايامالمتبقيةToolStripMenuItem.Tag = "Str_DaysLeft";
            this.الايامالمتبقيةToolStripMenuItem.Text = "الايام المتبقية";
            this.الايامالمتبقيةToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTimeLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Name = "Str_ProfileTimeLeft_ToolStripMenuItem";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Tag = "Str_ProfileTimeLeft";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Text = "الوقت المتبقي";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTransferLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Name = "Str_ProfileTransferLeft_ToolStripMenuItem";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Tag = "Str_ProfileTransferLeft";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Text = "التحميل المتبقي";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Descr_ToolStripMenuItem
            // 
            this.Descr_ToolStripMenuItem.Name = "Descr_ToolStripMenuItem";
            this.Descr_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Descr_ToolStripMenuItem.Tag = "Comment";
            this.Descr_ToolStripMenuItem.Text = "تعلـــــــيق";
            this.Descr_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // CusName_ToolStripMenuItem
            // 
            this.CusName_ToolStripMenuItem.Name = "CusName_ToolStripMenuItem";
            this.CusName_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.CusName_ToolStripMenuItem.Tag = "CustomerName";
            this.CusName_ToolStripMenuItem.Text = "عميل يوزمنجر";
            this.CusName_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Count_profile_ToolStripMenuItem
            // 
            this.Count_profile_ToolStripMenuItem.Name = "Count_profile_ToolStripMenuItem";
            this.Count_profile_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Count_profile_ToolStripMenuItem.Tag = "CountProfile";
            this.Count_profile_ToolStripMenuItem.Text = "عدد الباقات";
            this.Count_profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // CountSession_ToolStripMenuItem
            // 
            this.CountSession_ToolStripMenuItem.Name = "CountSession_ToolStripMenuItem";
            this.CountSession_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.CountSession_ToolStripMenuItem.Tag = "CountSession";
            this.CountSession_ToolStripMenuItem.Text = "عدد جلسات الكرت";
            this.CountSession_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // LastSynDb_ToolStripMenuItem
            // 
            this.LastSynDb_ToolStripMenuItem.Name = "LastSynDb_ToolStripMenuItem";
            this.LastSynDb_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.LastSynDb_ToolStripMenuItem.Tag = "LastSynDb";
            this.LastSynDb_ToolStripMenuItem.Text = "اخر تحديث او مزامنه للكرت";
            this.LastSynDb_ToolStripMenuItem.Visible = false;
            this.LastSynDb_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // PageNumber_ToolStripMenuItem
            // 
            this.PageNumber_ToolStripMenuItem.Name = "PageNumber_ToolStripMenuItem";
            this.PageNumber_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.PageNumber_ToolStripMenuItem.Tag = "PageNumber";
            this.PageNumber_ToolStripMenuItem.Text = "رقم الصفحة";
            this.PageNumber_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Sn_Archive_ToolStripMenuItem
            // 
            this.Sn_Archive_ToolStripMenuItem.Name = "Sn_Archive_ToolStripMenuItem";
            this.Sn_Archive_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Sn_Archive_ToolStripMenuItem.Tag = "Sn_Archive";
            this.Sn_Archive_ToolStripMenuItem.Text = "تسلسل ارشيف المعلقات";
            this.Sn_Archive_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Restor_ColumnToolStripMenuItem
            // 
            this.Restor_ColumnToolStripMenuItem.Name = "Restor_ColumnToolStripMenuItem";
            this.Restor_ColumnToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Restor_ColumnToolStripMenuItem.Text = "استعادة الاعمده الي الافتراضي";
            this.Restor_ColumnToolStripMenuItem.Click += new System.EventHandler(this.Restor_ColumnToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(277, 22);
            this.toolStripMenuItem2.Text = "عرض معلومات الكرت المحدد";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.btnEdit_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(274, 6);
            // 
            // Copy_ToolStripMenuItem
            // 
            this.Copy_ToolStripMenuItem.Name = "Copy_ToolStripMenuItem";
            this.Copy_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Copy_ToolStripMenuItem.Text = "نسخ                 ctrl+c";
            this.Copy_ToolStripMenuItem.Click += new System.EventHandler(this.Copy_ToolStripMenuItem_Click);
            // 
            // Copy_AllRowToolStripMenuItem
            // 
            this.Copy_AllRowToolStripMenuItem.Name = "Copy_AllRowToolStripMenuItem";
            this.Copy_AllRowToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Copy_AllRowToolStripMenuItem.Text = "نسخ السطر كامل ";
            this.Copy_AllRowToolStripMenuItem.Click += new System.EventHandler(this.Copy_AllRowToolStripMenuItem_Click);
            // 
            // ExportExcelToolStripMenuItem
            // 
            this.ExportExcelToolStripMenuItem.Name = "ExportExcelToolStripMenuItem";
            this.ExportExcelToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ExportExcelToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            this.ExportExcelToolStripMenuItem.Click += new System.EventHandler(this.ExportExcelToolStripMenuItem_Click);
            // 
            // ExportText_ToolStripMenuItem
            // 
            this.ExportText_ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.تصديرالاسمفقطToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem});
            this.ExportText_ToolStripMenuItem.Name = "ExportText_ToolStripMenuItem";
            this.ExportText_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ExportText_ToolStripMenuItem.Text = "تصدير الى ملف نصي";
            // 
            // تصديرالاسمفقطToolStripMenuItem
            // 
            this.تصديرالاسمفقطToolStripMenuItem.Name = "تصديرالاسمفقطToolStripMenuItem";
            this.تصديرالاسمفقطToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمفقطToolStripMenuItem.Text = "تصدير الاسم فقط";
            this.تصديرالاسمفقطToolStripMenuItem.Click += new System.EventHandler(this.نسخالاسمفقطToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور";
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Click += new System.EventHandler(this.نسخالاسمكلمةالسرToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورالباقةToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة";
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Click += new System.EventHandler(this.نسخالاسمكملةكلمةالسرالباقةToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة + نقطة البيع";
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Click += new System.EventHandler(this.تصديرجميعالاعمدةفيالجدولToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(274, 6);
            // 
            // DeleteCards_ToolStripMenuItem
            // 
            this.DeleteCards_ToolStripMenuItem.Name = "DeleteCards_ToolStripMenuItem";
            this.DeleteCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteCards_ToolStripMenuItem.Text = "حذف الكروت المحددة";
            this.DeleteCards_ToolStripMenuItem.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // DeleteCardsArchive_ToolStripMenuItem
            // 
            this.DeleteCardsArchive_ToolStripMenuItem.Name = "DeleteCardsArchive_ToolStripMenuItem";
            this.DeleteCardsArchive_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteCardsArchive_ToolStripMenuItem.Text = "حذف الكروت المحددة من  الارشيف";
            this.DeleteCardsArchive_ToolStripMenuItem.Click += new System.EventHandler(this.DeleteCardsArchive_ToolStripMenuItem_Click);
            // 
            // DeleteServerArchiveToolStripMenuItem
            // 
            this.DeleteServerArchiveToolStripMenuItem.Name = "DeleteServerArchiveToolStripMenuItem";
            this.DeleteServerArchiveToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteServerArchiveToolStripMenuItem.Text = "حذف الكروت المحددة من الراوتر والارشيف";
            this.DeleteServerArchiveToolStripMenuItem.Click += new System.EventHandler(this.DeleteServerArchiveToolStripMenuItem_Click);
            // 
            // DeleteSession_ToolStripMenuItem
            // 
            this.DeleteSession_ToolStripMenuItem.Name = "DeleteSession_ToolStripMenuItem";
            this.DeleteSession_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteSession_ToolStripMenuItem.Text = "حذف جلسات الكروت المحددة";
            this.DeleteSession_ToolStripMenuItem.Click += new System.EventHandler(this.DeleteSession_ToolStripMenuItem_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(274, 6);
            // 
            // DisableCards_ToolStripMenuItem
            // 
            this.DisableCards_ToolStripMenuItem.Name = "DisableCards_ToolStripMenuItem";
            this.DisableCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DisableCards_ToolStripMenuItem.Text = "تعطيل الكروت المحددة ";
            this.DisableCards_ToolStripMenuItem.Click += new System.EventHandler(this.btnDisable_Click);
            // 
            // EnableCards_ToolStripMenuItem
            // 
            this.EnableCards_ToolStripMenuItem.Name = "EnableCards_ToolStripMenuItem";
            this.EnableCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.EnableCards_ToolStripMenuItem.Text = "تفعيل الكروت المحددة";
            this.EnableCards_ToolStripMenuItem.Click += new System.EventHandler(this.btnEnable_Click);
            // 
            // RestCards_ToolStripMenuItem
            // 
            this.RestCards_ToolStripMenuItem.Name = "RestCards_ToolStripMenuItem";
            this.RestCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.RestCards_ToolStripMenuItem.Text = "تصفير عداد الكروت المحددة";
            this.RestCards_ToolStripMenuItem.Click += new System.EventHandler(this.RestCards_ToolStripMenuItem_Click);
            // 
            // BindMAC_ToolStripMenuItem
            // 
            this.BindMAC_ToolStripMenuItem.Name = "BindMAC_ToolStripMenuItem";
            this.BindMAC_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.BindMAC_ToolStripMenuItem.Text = "ربط الكروت باول جهاز استخدام";
            this.BindMAC_ToolStripMenuItem.Click += new System.EventHandler(this.BindMAC_ToolStripMenuItem_Click);
            // 
            // RemoveBindMAC_ToolStripMenuItem
            // 
            this.RemoveBindMAC_ToolStripMenuItem.Name = "RemoveBindMAC_ToolStripMenuItem";
            this.RemoveBindMAC_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.RemoveBindMAC_ToolStripMenuItem.Text = "الغاء ربط الكروت باول جهاز استخدام";
            this.RemoveBindMAC_ToolStripMenuItem.Click += new System.EventHandler(this.RemoveBindMAC_ToolStripMenuItem_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(274, 6);
            // 
            // PrintCards_ToolStripMenuItem
            // 
            this.PrintCards_ToolStripMenuItem.Name = "PrintCards_ToolStripMenuItem";
            this.PrintCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.PrintCards_ToolStripMenuItem.Text = "طباعة الكروت المحددة";
            this.PrintCards_ToolStripMenuItem.Click += new System.EventHandler(this.PrintCards_ToolStripMenuItem_Click);
            // 
            // AddProfile_ToolStripMenuItem
            // 
            this.AddProfile_ToolStripMenuItem.Name = "AddProfile_ToolStripMenuItem";
            this.AddProfile_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.AddProfile_ToolStripMenuItem.Text = "اضافه باقة للكروت المحددة";
            this.AddProfile_ToolStripMenuItem.Click += new System.EventHandler(this.AddProfile_ToolStripMenuItem_Click);
            // 
            // ChangeSP_ToolStripMenuItem
            // 
            this.ChangeSP_ToolStripMenuItem.Name = "ChangeSP_ToolStripMenuItem";
            this.ChangeSP_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ChangeSP_ToolStripMenuItem.Text = "تغير نقطة البيع للكروت المحددة";
            this.ChangeSP_ToolStripMenuItem.Click += new System.EventHandler(this.ChangeSP_ToolStripMenuItem_Click);
            // 
            // Remove_SP_ToolStripMenuItem
            // 
            this.Remove_SP_ToolStripMenuItem.Name = "Remove_SP_ToolStripMenuItem";
            this.Remove_SP_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Remove_SP_ToolStripMenuItem.Text = "حذف نقطة البيع من الكروت المحدده";
            this.Remove_SP_ToolStripMenuItem.Click += new System.EventHandler(this.Remove_SP_ToolStripMenuItem_Click);
            // 
            // تعديلرقمالطبعةToolStripMenuItem
            // 
            this.تعديلرقمالطبعةToolStripMenuItem.Name = "تعديلرقمالطبعةToolStripMenuItem";
            this.تعديلرقمالطبعةToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.تعديلرقمالطبعةToolStripMenuItem.Text = "تعديل رقم الطبعة";
            this.تعديلرقمالطبعةToolStripMenuItem.Visible = false;
            this.تعديلرقمالطبعةToolStripMenuItem.Click += new System.EventHandler(this.تعديلرقمالطبعةToolStripMenuItem_Click);
            // 
            // تعديلرقمالدفعةToolStripMenuItem
            // 
            this.تعديلرقمالدفعةToolStripMenuItem.Name = "تعديلرقمالدفعةToolStripMenuItem";
            this.تعديلرقمالدفعةToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.تعديلرقمالدفعةToolStripMenuItem.Text = "تعديل رقم الدفعة";
            this.تعديلرقمالدفعةToolStripMenuItem.Visible = false;
            this.تعديلرقمالدفعةToolStripMenuItem.Click += new System.EventHandler(this.تعديلرقمالدفعةToolStripMenuItem_Click);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // timer_SideBar
            // 
            this.timer_SideBar.Interval = 15;
            this.timer_SideBar.Tick += new System.EventHandler(this.timer_SideBar_Tick);
            // 
            // CBox_Didabled
            // 
            this.CBox_Didabled.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Didabled.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Didabled.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Didabled.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Didabled.BorderRadius = 5;
            this.CBox_Didabled.BorderSize = 1;
            this.CBox_Didabled.Customizable = false;
            this.CBox_Didabled.DataSource = null;
            this.CBox_Didabled.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Didabled.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Didabled.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Didabled.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Didabled.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Didabled.Location = new System.Drawing.Point(137, 135);
            this.CBox_Didabled.Name = "CBox_Didabled";
            this.CBox_Didabled.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Didabled.SelectedIndex = -1;
            this.CBox_Didabled.Size = new System.Drawing.Size(113, 32);
            this.CBox_Didabled.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Didabled.TabIndex = 56;
            this.CBox_Didabled.Texts = "";
            this.CBox_Didabled.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(189, 117);
            this.rjLabel2.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(45, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 57;
            this.rjLabel2.Text = "التفعيل";
            this.rjLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Staus
            // 
            this.CBox_Staus.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Staus.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Staus.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Staus.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Staus.BorderRadius = 5;
            this.CBox_Staus.BorderSize = 1;
            this.CBox_Staus.Customizable = false;
            this.CBox_Staus.DataSource = null;
            this.CBox_Staus.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Staus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Staus.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Staus.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Staus.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Staus.Location = new System.Drawing.Point(7, 24);
            this.CBox_Staus.Name = "CBox_Staus";
            this.CBox_Staus.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Staus.SelectedIndex = -1;
            this.CBox_Staus.Size = new System.Drawing.Size(120, 32);
            this.CBox_Staus.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Staus.TabIndex = 56;
            this.CBox_Staus.Texts = "";
            this.CBox_Staus.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(75, 7);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel14.Size = new System.Drawing.Size(37, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 57;
            this.rjLabel14.Text = "الحالة";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnl_side_Count_Session
            // 
            this.pnl_side_Count_Session.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_Count_Session.BorderRadius = 10;
            this.pnl_side_Count_Session.Controls.Add(this.fpnl_showArchive);
            this.pnl_side_Count_Session.Controls.Add(this.flowLayoutPanel1);
            this.pnl_side_Count_Session.Controls.Add(this.flowLayoutPanel2);
            this.pnl_side_Count_Session.Controls.Add(this.fpnl_showServer);
            this.pnl_side_Count_Session.Customizable = true;
            this.pnl_side_Count_Session.Location = new System.Drawing.Point(0, 339);
            this.pnl_side_Count_Session.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_side_Count_Session.Name = "pnl_side_Count_Session";
            this.pnl_side_Count_Session.Padding = new System.Windows.Forms.Padding(2);
            this.pnl_side_Count_Session.Size = new System.Drawing.Size(258, 141);
            this.pnl_side_Count_Session.TabIndex = 53;
            this.pnl_side_Count_Session.Visible = false;
            // 
            // fpnl_showArchive
            // 
            this.fpnl_showArchive.Controls.Add(this.ToggleButton_Show_Archive);
            this.fpnl_showArchive.Dock = System.Windows.Forms.DockStyle.Top;
            this.fpnl_showArchive.Location = new System.Drawing.Point(2, 105);
            this.fpnl_showArchive.Name = "fpnl_showArchive";
            this.fpnl_showArchive.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.fpnl_showArchive.Size = new System.Drawing.Size(254, 29);
            this.fpnl_showArchive.TabIndex = 58;
            this.fpnl_showArchive.Visible = false;
            // 
            // ToggleButton_Show_Archive
            // 
            this.ToggleButton_Show_Archive.Activated = false;
            this.ToggleButton_Show_Archive.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Show_Archive.Customizable = false;
            this.ToggleButton_Show_Archive.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Show_Archive.Location = new System.Drawing.Point(5, 3);
            this.ToggleButton_Show_Archive.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_Show_Archive.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_Archive.Name = "ToggleButton_Show_Archive";
            this.ToggleButton_Show_Archive.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_Archive.OFF_Text = "عرض الكروت المحذوفه - المارشفة";
            this.ToggleButton_Show_Archive.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Show_Archive.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_Archive.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_Archive.ON_Text = "عرض الكروت المحذوفه - المارشفة";
            this.ToggleButton_Show_Archive.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Show_Archive.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_Archive.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.ToggleButton_Show_Archive.Size = new System.Drawing.Size(249, 25);
            this.ToggleButton_Show_Archive.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Show_Archive.TabIndex = 52;
            this.ToggleButton_Show_Archive.Tag = "تفصيلي";
            this.ToggleButton_Show_Archive.Text = "#";
            this.ToggleButton_Show_Archive.UseVisualStyleBackColor = true;
            this.ToggleButton_Show_Archive.CheckedChanged += new System.EventHandler(this.ToggleButton_Show_Archive_CheckedChanged);
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.Controls.Add(this.ToggleButton_ByCountSession);
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(2, 72);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel1.Size = new System.Drawing.Size(254, 33);
            this.flowLayoutPanel1.TabIndex = 56;
            // 
            // ToggleButton_ByCountSession
            // 
            this.ToggleButton_ByCountSession.Activated = false;
            this.ToggleButton_ByCountSession.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_ByCountSession.Customizable = false;
            this.ToggleButton_ByCountSession.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_ByCountSession.Location = new System.Drawing.Point(5, 3);
            this.ToggleButton_ByCountSession.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_ByCountSession.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountSession.Name = "ToggleButton_ByCountSession";
            this.ToggleButton_ByCountSession.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountSession.OFF_Text = "عرض الكروت بحسب اكثر عدد جلسات";
            this.ToggleButton_ByCountSession.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_ByCountSession.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountSession.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountSession.ON_Text = "عرض الكروت بحسب اكثر عدد جلسات";
            this.ToggleButton_ByCountSession.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_ByCountSession.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.ToggleButton_ByCountSession.Size = new System.Drawing.Size(249, 25);
            this.ToggleButton_ByCountSession.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_ByCountSession.TabIndex = 52;
            this.ToggleButton_ByCountSession.Tag = "تفصيلي";
            this.ToggleButton_ByCountSession.Text = "#";
            this.ToggleButton_ByCountSession.UseVisualStyleBackColor = true;
            this.ToggleButton_ByCountSession.CheckedChanged += new System.EventHandler(this.ToggleButton_ByCountSession_CheckedChanged);
            // 
            // flowLayoutPanel2
            // 
            this.flowLayoutPanel2.Controls.Add(this.ToggleButton_ByCountProfile);
            this.flowLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.flowLayoutPanel2.Location = new System.Drawing.Point(2, 37);
            this.flowLayoutPanel2.Name = "flowLayoutPanel2";
            this.flowLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel2.Size = new System.Drawing.Size(254, 35);
            this.flowLayoutPanel2.TabIndex = 57;
            // 
            // ToggleButton_ByCountProfile
            // 
            this.ToggleButton_ByCountProfile.Activated = false;
            this.ToggleButton_ByCountProfile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_ByCountProfile.Customizable = false;
            this.ToggleButton_ByCountProfile.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_ByCountProfile.Location = new System.Drawing.Point(5, 3);
            this.ToggleButton_ByCountProfile.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_ByCountProfile.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountProfile.Name = "ToggleButton_ByCountProfile";
            this.ToggleButton_ByCountProfile.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountProfile.OFF_Text = "عرض الكروت بحسب اكثر عدد باقات";
            this.ToggleButton_ByCountProfile.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_ByCountProfile.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountProfile.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountProfile.ON_Text = "عرض الكروت بحسب اكثر عدد باقات";
            this.ToggleButton_ByCountProfile.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_ByCountProfile.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountProfile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.ToggleButton_ByCountProfile.Size = new System.Drawing.Size(249, 25);
            this.ToggleButton_ByCountProfile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_ByCountProfile.TabIndex = 52;
            this.ToggleButton_ByCountProfile.Tag = "تفصيلي";
            this.ToggleButton_ByCountProfile.Text = "#";
            this.ToggleButton_ByCountProfile.UseVisualStyleBackColor = true;
            this.ToggleButton_ByCountProfile.CheckedChanged += new System.EventHandler(this.ToggleButton_ByCountProfile_CheckedChanged);
            // 
            // fpnl_showServer
            // 
            this.fpnl_showServer.Controls.Add(this.ToggleButton_Show_onlyServer);
            this.fpnl_showServer.Dock = System.Windows.Forms.DockStyle.Top;
            this.fpnl_showServer.Location = new System.Drawing.Point(2, 2);
            this.fpnl_showServer.Name = "fpnl_showServer";
            this.fpnl_showServer.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.fpnl_showServer.Size = new System.Drawing.Size(254, 35);
            this.fpnl_showServer.TabIndex = 59;
            this.fpnl_showServer.Visible = false;
            // 
            // ToggleButton_Show_onlyServer
            // 
            this.ToggleButton_Show_onlyServer.Activated = false;
            this.ToggleButton_Show_onlyServer.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Show_onlyServer.Customizable = false;
            this.ToggleButton_Show_onlyServer.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Show_onlyServer.Location = new System.Drawing.Point(5, 3);
            this.ToggleButton_Show_onlyServer.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_Show_onlyServer.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_onlyServer.Name = "ToggleButton_Show_onlyServer";
            this.ToggleButton_Show_onlyServer.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_onlyServer.OFF_Text = "عرض كروت السيرفر فقط";
            this.ToggleButton_Show_onlyServer.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Show_onlyServer.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_onlyServer.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_onlyServer.ON_Text = "عرض كروت السيرفر فقط";
            this.ToggleButton_Show_onlyServer.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Show_onlyServer.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_onlyServer.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.ToggleButton_Show_onlyServer.Size = new System.Drawing.Size(249, 25);
            this.ToggleButton_Show_onlyServer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Show_onlyServer.TabIndex = 52;
            this.ToggleButton_Show_onlyServer.Tag = "تفصيلي";
            this.ToggleButton_Show_onlyServer.Text = "#";
            this.ToggleButton_Show_onlyServer.UseVisualStyleBackColor = true;
            this.ToggleButton_Show_onlyServer.CheckedChanged += new System.EventHandler(this.ToggleButton_Show_onlyServer_CheckedChanged);
            // 
            // rjLabel13
            // 
            this.rjLabel13.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(414, 399);
            this.rjLabel13.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.Size = new System.Drawing.Size(204, 17);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 55;
            this.rjLabel13.Text = "عرض كروت القاعده المحلية (المؤرشفة)";
            this.rjLabel13.Visible = false;
            this.rjLabel13.Click += new System.EventHandler(this.rjLabel4_Click);
            // 
            // rjLabel4
            // 
            this.rjLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(414, 364);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(183, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 55;
            this.rjLabel4.Text = "عرض الكروت بحسب اكثر عدد جلسات";
            this.rjLabel4.Visible = false;
            this.rjLabel4.Click += new System.EventHandler(this.rjLabel4_Click);
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(442, 322);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(177, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 55;
            this.rjLabel1.Text = "عرض الكروت بحسب اكثر عدد باقات";
            this.rjLabel1.Visible = false;
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Tahoma", 9.75F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(65, 279);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(64, 27);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 43;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_SN_End.onTextChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Droid Sans Arabic", 8F);
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_SN.Location = new System.Drawing.Point(170, 283);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(86, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 42;
            this.CheckBox_SN.Text = "التسلسل";
            this.CheckBox_SN.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(134, 279);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(60, 27);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 43;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(126, 287);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.Font = new System.Drawing.Font("Tahoma", 8F);
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(5, 279);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(58, 28);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 31;
            this.CBox_SN_Compar.Texts = "";
            // 
            // lbl_to
            // 
            this.lbl_to.AutoSize = true;
            this.lbl_to.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_to.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_to.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_to.LinkLabel = false;
            this.lbl_to.Location = new System.Drawing.Point(85, 171);
            this.lbl_to.Name = "lbl_to";
            this.lbl_to.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_to.Size = new System.Drawing.Size(27, 17);
            this.lbl_to.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_to.TabIndex = 54;
            this.lbl_to.Text = "الي";
            // 
            // Date_To
            // 
            this.Date_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_To.BorderRadius = 3;
            this.Date_To.BorderSize = 1;
            this.Date_To.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_To.Customizable = false;
            this.Date_To.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_To.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_To.Location = new System.Drawing.Point(7, 195);
            this.Date_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_To.Name = "Date_To";
            this.Date_To.Padding = new System.Windows.Forms.Padding(2);
            this.Date_To.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.Date_To.Size = new System.Drawing.Size(120, 32);
            this.Date_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_To.TabIndex = 51;
            this.Date_To.Value = new System.DateTime(2024, 9, 24, 20, 3, 55, 357);
            this.Date_To.OnValueChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // Date_From
            // 
            this.Date_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_From.BorderRadius = 3;
            this.Date_From.BorderSize = 1;
            this.Date_From.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_From.Customizable = false;
            this.Date_From.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_From.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_From.Location = new System.Drawing.Point(133, 195);
            this.Date_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_From.Name = "Date_From";
            this.Date_From.Padding = new System.Windows.Forms.Padding(2);
            this.Date_From.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.Date_From.Size = new System.Drawing.Size(120, 32);
            this.Date_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_From.TabIndex = 52;
            this.Date_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            this.Date_From.OnValueChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CheckBox_byDatePrint
            // 
            this.CheckBox_byDatePrint.AutoSize = true;
            this.CheckBox_byDatePrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.BorderSize = 1;
            this.CheckBox_byDatePrint.Check = false;
            this.CheckBox_byDatePrint.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDatePrint.Customizable = false;
            this.CheckBox_byDatePrint.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_byDatePrint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDatePrint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_byDatePrint.Location = new System.Drawing.Point(149, 171);
            this.CheckBox_byDatePrint.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDatePrint.Name = "CheckBox_byDatePrint";
            this.CheckBox_byDatePrint.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_byDatePrint.Size = new System.Drawing.Size(85, 21);
            this.CheckBox_byDatePrint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDatePrint.TabIndex = 42;
            this.CheckBox_byDatePrint.Text = "الطباعة";
            this.CheckBox_byDatePrint.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_byDatePrint.UseVisualStyleBackColor = true;
            this.CheckBox_byDatePrint.CheckedChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CBox_Customer
            // 
            this.CBox_Customer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Customer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Customer.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.BorderRadius = 5;
            this.CBox_Customer.BorderSize = 1;
            this.CBox_Customer.Customizable = false;
            this.CBox_Customer.DataSource = null;
            this.CBox_Customer.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Customer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Customer.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Customer.Location = new System.Drawing.Point(7, 135);
            this.CBox_Customer.Name = "CBox_Customer";
            this.CBox_Customer.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Customer.SelectedIndex = -1;
            this.CBox_Customer.Size = new System.Drawing.Size(120, 32);
            this.CBox_Customer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Customer.TabIndex = 33;
            this.CBox_Customer.Texts = "";
            this.CBox_Customer.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(65, 117);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel17.Size = new System.Drawing.Size(42, 17);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 35;
            this.rjLabel17.Text = "العميل";
            this.rjLabel17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(6, 241);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(188, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            this.CBox_SellingPoint.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(199, 249);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel15.Size = new System.Drawing.Size(52, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Location = new System.Drawing.Point(137, 80);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(113, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 33;
            this.CBox_Batch.Texts = "";
            this.CBox_Batch.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Batch_OnSelectedIndexChanged);
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(191, 59);
            this.rjLabel3.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(43, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 35;
            this.rjLabel3.Text = "الدفعه";
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(137, 25);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(113, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            this.CBox_Profile.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(197, 7);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(37, 17);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 8;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(612, 231);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_apply.Size = new System.Drawing.Size(86, 33);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 51;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Visible = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 1;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRefresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 18;
            this.btnRefresh.Location = new System.Drawing.Point(9, 6);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(29, 34);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 51;
            this.btnRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh, "تحديث الكروت من الروتر");
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // btnDisable
            // 
            this.btnDisable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDisable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderRadius = 5;
            this.btnDisable.BorderSize = 1;
            this.btnDisable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnDisable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnDisable.FlatAppearance.BorderSize = 0;
            this.btnDisable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDisable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDisable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDisable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDisable.ForeColor = System.Drawing.Color.White;
            this.btnDisable.IconChar = FontAwesome.Sharp.IconChar.Remove;
            this.btnDisable.IconColor = System.Drawing.Color.White;
            this.btnDisable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDisable.IconSize = 18;
            this.btnDisable.Location = new System.Drawing.Point(752, 5);
            this.btnDisable.Name = "btnDisable";
            this.btnDisable.Size = new System.Drawing.Size(29, 34);
            this.btnDisable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDisable.TabIndex = 52;
            this.btnDisable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDisable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnDisable.UseVisualStyleBackColor = false;
            this.btnDisable.Click += new System.EventHandler(this.btnDisable_Click);
            // 
            // btnRefresh_DB
            // 
            this.btnRefresh_DB.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh_DB.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh_DB.BorderRadius = 5;
            this.btnRefresh_DB.BorderSize = 1;
            this.btnRefresh_DB.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnRefresh_DB.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.FlatAppearance.BorderSize = 0;
            this.btnRefresh_DB.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnRefresh_DB.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnRefresh_DB.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh_DB.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnRefresh_DB.ForeColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconChar = FontAwesome.Sharp.IconChar.Recycle;
            this.btnRefresh_DB.IconColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh_DB.IconSize = 18;
            this.btnRefresh_DB.Location = new System.Drawing.Point(807, 5);
            this.btnRefresh_DB.Name = "btnRefresh_DB";
            this.btnRefresh_DB.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnRefresh_DB.Size = new System.Drawing.Size(87, 34);
            this.btnRefresh_DB.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh_DB.TabIndex = 50;
            this.btnRefresh_DB.Text = "تحديث";
            this.btnRefresh_DB.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnRefresh_DB.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh_DB, "تحديث الكروت في الواجة");
            this.btnRefresh_DB.UseVisualStyleBackColor = false;
            this.btnRefresh_DB.Click += new System.EventHandler(this.btnRefresh_DB_Click);
            // 
            // btnEdit
            // 
            this.btnEdit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEdit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEdit.BorderRadius = 5;
            this.btnEdit.BorderSize = 1;
            this.btnEdit.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnEdit.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEdit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEdit.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnEdit.ForeColor = System.Drawing.Color.White;
            this.btnEdit.IconChar = FontAwesome.Sharp.IconChar.Edit;
            this.btnEdit.IconColor = System.Drawing.Color.White;
            this.btnEdit.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEdit.IconSize = 18;
            this.btnEdit.Location = new System.Drawing.Point(43, 24);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new System.Drawing.Size(29, 34);
            this.btnEdit.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEdit.TabIndex = 52;
            this.btnEdit.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnEdit.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Visible = false;
            this.btnEdit.Click += new System.EventHandler(this.btnEdit_Click);
            // 
            // btn_Filter
            // 
            this.btn_Filter.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.White;
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.White;
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 17;
            this.btn_Filter.Location = new System.Drawing.Point(894, 5);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Filter.Size = new System.Drawing.Size(76, 34);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Filter.TabIndex = 56;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btn_Filter, "فلتره");
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // btn_MenuProcess
            // 
            this.btn_MenuProcess.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_MenuProcess.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_MenuProcess.BorderRadius = 5;
            this.btn_MenuProcess.BorderSize = 1;
            this.btn_MenuProcess.ContextMenuStrip = this.dmAll_Cards;
            this.btn_MenuProcess.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_MenuProcess.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_MenuProcess.FlatAppearance.BorderSize = 0;
            this.btn_MenuProcess.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_MenuProcess.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_MenuProcess.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_MenuProcess.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_MenuProcess.ForeColor = System.Drawing.Color.White;
            this.btn_MenuProcess.IconChar = FontAwesome.Sharp.IconChar.EllipsisVertical;
            this.btn_MenuProcess.IconColor = System.Drawing.Color.White;
            this.btn_MenuProcess.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_MenuProcess.IconSize = 18;
            this.btn_MenuProcess.Location = new System.Drawing.Point(8, 23);
            this.btn_MenuProcess.Name = "btn_MenuProcess";
            this.btn_MenuProcess.Size = new System.Drawing.Size(29, 34);
            this.btn_MenuProcess.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_MenuProcess.TabIndex = 59;
            this.btn_MenuProcess.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_MenuProcess.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btn_MenuProcess, "القائمة");
            this.btn_MenuProcess.UseVisualStyleBackColor = false;
            this.btn_MenuProcess.Visible = false;
            this.btn_MenuProcess.Click += new System.EventHandler(this.btn_MenuProcess_Click);
            // 
            // btnFirst
            // 
            this.btnFirst.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnFirst.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnFirst.BorderRadius = 5;
            this.btnFirst.BorderSize = 1;
            this.btnFirst.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnFirst.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnFirst.FlatAppearance.BorderSize = 0;
            this.btnFirst.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnFirst.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnFirst.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnFirst.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnFirst.ForeColor = System.Drawing.Color.White;
            this.btnFirst.IconChar = FontAwesome.Sharp.IconChar.BackwardStep;
            this.btnFirst.IconColor = System.Drawing.Color.White;
            this.btnFirst.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnFirst.IconSize = 18;
            this.btnFirst.Location = new System.Drawing.Point(-3, 3);
            this.btnFirst.Name = "btnFirst";
            this.btnFirst.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnFirst.Size = new System.Drawing.Size(29, 32);
            this.btnFirst.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnFirst.TabIndex = 89;
            this.btnFirst.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnFirst.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnFirst, "اخر صفحة");
            this.btnFirst.UseVisualStyleBackColor = false;
            this.btnFirst.Click += new System.EventHandler(this.btnFirst_Click);
            // 
            // btnNext
            // 
            this.btnNext.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNext.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNext.BorderRadius = 5;
            this.btnNext.BorderSize = 1;
            this.btnNext.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnNext.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnNext.FlatAppearance.BorderSize = 0;
            this.btnNext.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnNext.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnNext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNext.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnNext.ForeColor = System.Drawing.Color.White;
            this.btnNext.IconChar = FontAwesome.Sharp.IconChar.ArrowLeft;
            this.btnNext.IconColor = System.Drawing.Color.White;
            this.btnNext.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnNext.IconSize = 18;
            this.btnNext.Location = new System.Drawing.Point(26, 3);
            this.btnNext.Name = "btnNext";
            this.btnNext.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnNext.Size = new System.Drawing.Size(29, 32);
            this.btnNext.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnNext.TabIndex = 89;
            this.btnNext.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnNext.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnNext, "الصفحة التاليه");
            this.btnNext.UseVisualStyleBackColor = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnLast
            // 
            this.btnLast.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLast.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLast.BorderRadius = 5;
            this.btnLast.BorderSize = 1;
            this.btnLast.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnLast.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnLast.FlatAppearance.BorderSize = 0;
            this.btnLast.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnLast.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnLast.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLast.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnLast.ForeColor = System.Drawing.Color.White;
            this.btnLast.IconChar = FontAwesome.Sharp.IconChar.ForwardStep;
            this.btnLast.IconColor = System.Drawing.Color.White;
            this.btnLast.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnLast.IconSize = 18;
            this.btnLast.Location = new System.Drawing.Point(82, 3);
            this.btnLast.Name = "btnLast";
            this.btnLast.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnLast.Size = new System.Drawing.Size(29, 32);
            this.btnLast.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnLast.TabIndex = 90;
            this.btnLast.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnLast.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnLast, "اول صفحة");
            this.btnLast.UseVisualStyleBackColor = false;
            this.btnLast.Click += new System.EventHandler(this.btnLast_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPrev.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPrev.BorderRadius = 5;
            this.btnPrev.BorderSize = 1;
            this.btnPrev.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnPrev.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnPrev.FlatAppearance.BorderSize = 0;
            this.btnPrev.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnPrev.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnPrev.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPrev.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnPrev.ForeColor = System.Drawing.Color.White;
            this.btnPrev.IconChar = FontAwesome.Sharp.IconChar.ArrowRight;
            this.btnPrev.IconColor = System.Drawing.Color.White;
            this.btnPrev.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnPrev.IconSize = 18;
            this.btnPrev.Location = new System.Drawing.Point(55, 3);
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnPrev.Size = new System.Drawing.Size(29, 32);
            this.btnPrev.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnPrev.TabIndex = 90;
            this.btnPrev.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnPrev.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnPrev, "الصفحة السابقة");
            this.btnPrev.UseVisualStyleBackColor = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // btn_RemoveFinsh_Validaty
            // 
            this.btn_RemoveFinsh_Validaty.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btn_RemoveFinsh_Validaty.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btn_RemoveFinsh_Validaty.BorderRadius = 8;
            this.btn_RemoveFinsh_Validaty.BorderSize = 1;
            this.btn_RemoveFinsh_Validaty.Design = SmartCreator.RJControls.ButtonDesign.Delete;
            this.btn_RemoveFinsh_Validaty.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Validaty.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Validaty.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btn_RemoveFinsh_Validaty.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btn_RemoveFinsh_Validaty.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Validaty.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F, System.Drawing.FontStyle.Bold);
            this.btn_RemoveFinsh_Validaty.ForeColor = System.Drawing.Color.White;
            this.btn_RemoveFinsh_Validaty.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btn_RemoveFinsh_Validaty.IconColor = System.Drawing.Color.White;
            this.btn_RemoveFinsh_Validaty.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Validaty.IconSize = 1;
            this.btn_RemoveFinsh_Validaty.Location = new System.Drawing.Point(296, 4);
            this.btn_RemoveFinsh_Validaty.Name = "btn_RemoveFinsh_Validaty";
            this.btn_RemoveFinsh_Validaty.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Validaty.Size = new System.Drawing.Size(186, 34);
            this.btn_RemoveFinsh_Validaty.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_RemoveFinsh_Validaty.TabIndex = 52;
            this.btn_RemoveFinsh_Validaty.Text = "حذف الكروت المنتيهة الصلاحية";
            this.btn_RemoveFinsh_Validaty.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_RemoveFinsh_Validaty.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.toolTip1.SetToolTip(this.btn_RemoveFinsh_Validaty, "حذف الكروت التي انتهت صلاحيات الايام");
            this.btn_RemoveFinsh_Validaty.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Validaty.Visible = false;
            this.btn_RemoveFinsh_Validaty.Click += new System.EventHandler(this.btn_RemoveFinsh_Validaty_Click);
            // 
            // btnEnable
            // 
            this.btnEnable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEnable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderRadius = 5;
            this.btnEnable.BorderSize = 1;
            this.btnEnable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnEnable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnEnable.FlatAppearance.BorderSize = 0;
            this.btnEnable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEnable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEnable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEnable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnEnable.ForeColor = System.Drawing.Color.White;
            this.btnEnable.IconChar = FontAwesome.Sharp.IconChar.Check;
            this.btnEnable.IconColor = System.Drawing.Color.White;
            this.btnEnable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEnable.IconSize = 18;
            this.btnEnable.Location = new System.Drawing.Point(724, 5);
            this.btnEnable.Name = "btnEnable";
            this.btnEnable.Size = new System.Drawing.Size(29, 34);
            this.btnEnable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEnable.TabIndex = 57;
            this.btnEnable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnEnable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnEnable.UseVisualStyleBackColor = false;
            this.btnEnable.Click += new System.EventHandler(this.btnEnable_Click);
            // 
            // lbl_Filter
            // 
            this.lbl_Filter.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lbl_Filter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Filter.LinkLabel = false;
            this.lbl_Filter.Location = new System.Drawing.Point(5, 57);
            this.lbl_Filter.Name = "lbl_Filter";
            this.lbl_Filter.Size = new System.Drawing.Size(43, 26);
            this.lbl_Filter.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.lbl_Filter.TabIndex = 58;
            this.lbl_Filter.Text = "فلترة";
            this.lbl_Filter.Visible = false;
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderRadius = 5;
            this.btnDelete.BorderSize = 1;
            this.btnDelete.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnDelete.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btnDelete.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btnDelete.IconColor = System.Drawing.Color.White;
            this.btnDelete.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDelete.IconSize = 20;
            this.btnDelete.Location = new System.Drawing.Point(780, 5);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnDelete.Size = new System.Drawing.Size(29, 34);
            this.btnDelete.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDelete.TabIndex = 86;
            this.btnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDelete.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // rjPanel_back_side
            // 
            this.rjPanel_back_side.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel_back_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_back_side.BorderRadius = 13;
            this.rjPanel_back_side.Controls.Add(this.pnl_side_Count_Session);
            this.rjPanel_back_side.Controls.Add(this.rjButton1);
            this.rjPanel_back_side.Controls.Add(this.CBox_Customer);
            this.rjPanel_back_side.Controls.Add(this.rjLabel17);
            this.rjPanel_back_side.Controls.Add(this.CBox_Didabled);
            this.rjPanel_back_side.Controls.Add(this.rjLabel2);
            this.rjPanel_back_side.Controls.Add(this.txt_SN_End);
            this.rjPanel_back_side.Controls.Add(this.txt_SN_Start);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_SN);
            this.rjPanel_back_side.Controls.Add(this.CBox_SN_Compar);
            this.rjPanel_back_side.Controls.Add(this.CBox_Profile);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_byDatePrint);
            this.rjPanel_back_side.Controls.Add(this.Date_To);
            this.rjPanel_back_side.Controls.Add(this.Date_From);
            this.rjPanel_back_side.Controls.Add(this.CBox_Staus);
            this.rjPanel_back_side.Controls.Add(this.rjLabel14);
            this.rjPanel_back_side.Controls.Add(this.CBox_NumberPrint);
            this.rjPanel_back_side.Controls.Add(this.CBox_Batch);
            this.rjPanel_back_side.Controls.Add(this.CBox_SellingPoint);
            this.rjPanel_back_side.Controls.Add(this.rjLabel11);
            this.rjPanel_back_side.Controls.Add(this.rjLabel15);
            this.rjPanel_back_side.Controls.Add(this.rjLabel3);
            this.rjPanel_back_side.Controls.Add(this.rjLabel9);
            this.rjPanel_back_side.Controls.Add(this.lbl_to);
            this.rjPanel_back_side.Controls.Add(this.rjLabel10);
            this.rjPanel_back_side.Controls.Add(this.pnl_side_Finsh_Cards);
            this.rjPanel_back_side.Customizable = false;
            this.rjPanel_back_side.Location = new System.Drawing.Point(721, 55);
            this.rjPanel_back_side.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel_back_side.Name = "rjPanel_back_side";
            this.rjPanel_back_side.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjPanel_back_side.Size = new System.Drawing.Size(260, 482);
            this.rjPanel_back_side.TabIndex = 98;
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 8;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.None;
            this.rjButton1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.rjButton1.IconSize = 24;
            this.rjButton1.Location = new System.Drawing.Point(47, 309);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(147, 28);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton1.TabIndex = 51;
            this.rjButton1.Text = "مسح جميع الفلترة";
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            this.rjButton1.UseVisualStyleBackColor = false;
            this.rjButton1.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // CBox_NumberPrint
            // 
            this.CBox_NumberPrint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_NumberPrint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_NumberPrint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_NumberPrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_NumberPrint.BorderRadius = 5;
            this.CBox_NumberPrint.BorderSize = 1;
            this.CBox_NumberPrint.Customizable = false;
            this.CBox_NumberPrint.DataSource = null;
            this.CBox_NumberPrint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_NumberPrint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_NumberPrint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_NumberPrint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_NumberPrint.Location = new System.Drawing.Point(7, 80);
            this.CBox_NumberPrint.Name = "CBox_NumberPrint";
            this.CBox_NumberPrint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_NumberPrint.SelectedIndex = -1;
            this.CBox_NumberPrint.Size = new System.Drawing.Size(120, 32);
            this.CBox_NumberPrint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_NumberPrint.TabIndex = 33;
            this.CBox_NumberPrint.Texts = "";
            this.CBox_NumberPrint.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel11
            // 
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(68, 59);
            this.rjLabel11.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.rjLabel11.Size = new System.Drawing.Size(43, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 35;
            this.rjLabel11.Text = "الطبعة";
            // 
            // pnl_side_Finsh_Cards
            // 
            this.pnl_side_Finsh_Cards.AutoScroll = true;
            this.pnl_side_Finsh_Cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_Finsh_Cards.BorderRadius = 10;
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_Download);
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_Uptime);
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_All);
            this.pnl_side_Finsh_Cards.Customizable = true;
            this.pnl_side_Finsh_Cards.Location = new System.Drawing.Point(5, 339);
            this.pnl_side_Finsh_Cards.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_side_Finsh_Cards.Name = "pnl_side_Finsh_Cards";
            this.pnl_side_Finsh_Cards.Padding = new System.Windows.Forms.Padding(2);
            this.pnl_side_Finsh_Cards.Size = new System.Drawing.Size(251, 103);
            this.pnl_side_Finsh_Cards.TabIndex = 56;
            this.pnl_side_Finsh_Cards.Visible = false;
            // 
            // btn_RemoveFinsh_Download
            // 
            this.btn_RemoveFinsh_Download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_Download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.BorderRadius = 8;
            this.btn_RemoveFinsh_Download.BorderSize = 1;
            this.btn_RemoveFinsh_Download.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_Download.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_Download.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Download.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Download.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_Download.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_Download.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Download.Font = new System.Drawing.Font("Droid Arabic Kufi", 8.765218F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_Download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_Download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Download.IconSize = 24;
            this.btn_RemoveFinsh_Download.Location = new System.Drawing.Point(2, 68);
            this.btn_RemoveFinsh_Download.Name = "btn_RemoveFinsh_Download";
            this.btn_RemoveFinsh_Download.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Download.Size = new System.Drawing.Size(247, 33);
            this.btn_RemoveFinsh_Download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_Download.TabIndex = 51;
            this.btn_RemoveFinsh_Download.Text = "حذف الكروت المنتهية التحميل";
            this.btn_RemoveFinsh_Download.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Download.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // btn_RemoveFinsh_Uptime
            // 
            this.btn_RemoveFinsh_Uptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_Uptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.BorderRadius = 8;
            this.btn_RemoveFinsh_Uptime.BorderSize = 1;
            this.btn_RemoveFinsh_Uptime.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_Uptime.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_Uptime.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Uptime.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Uptime.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_Uptime.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_Uptime.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Uptime.Font = new System.Drawing.Font("Droid Arabic Kufi", 8.765218F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_Uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_Uptime.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Uptime.IconSize = 24;
            this.btn_RemoveFinsh_Uptime.Location = new System.Drawing.Point(2, 35);
            this.btn_RemoveFinsh_Uptime.Name = "btn_RemoveFinsh_Uptime";
            this.btn_RemoveFinsh_Uptime.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Uptime.Size = new System.Drawing.Size(247, 33);
            this.btn_RemoveFinsh_Uptime.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_Uptime.TabIndex = 51;
            this.btn_RemoveFinsh_Uptime.Text = "حذف الكروت المنتهية الوقت";
            this.btn_RemoveFinsh_Uptime.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Uptime.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // btn_RemoveFinsh_All
            // 
            this.btn_RemoveFinsh_All.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_All.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.BorderRadius = 8;
            this.btn_RemoveFinsh_All.BorderSize = 1;
            this.btn_RemoveFinsh_All.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_All.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_All.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_All.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_All.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_All.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_All.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_All.Font = new System.Drawing.Font("Droid Arabic Kufi", 8.765218F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_All.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_All.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_All.IconSize = 24;
            this.btn_RemoveFinsh_All.Location = new System.Drawing.Point(2, 2);
            this.btn_RemoveFinsh_All.Name = "btn_RemoveFinsh_All";
            this.btn_RemoveFinsh_All.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_All.Size = new System.Drawing.Size(247, 33);
            this.btn_RemoveFinsh_All.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_All.TabIndex = 51;
            this.btn_RemoveFinsh_All.Text = "حذف جميع الكروت المنتيهة";
            this.btn_RemoveFinsh_All.UseVisualStyleBackColor = false;
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.rjLabel27);
            this.panel1.Controls.Add(this.btnEdit);
            this.panel1.Controls.Add(this.rjLabel13);
            this.panel1.Controls.Add(this.rjLabel4);
            this.panel1.Controls.Add(this.rjLabel1);
            this.panel1.Controls.Add(this.rjLabel18);
            this.panel1.Controls.Add(this.lbl_Filter);
            this.panel1.Controls.Add(this.btn_MenuProcess);
            this.panel1.Controls.Add(this.dgv);
            this.panel1.Controls.Add(this.btn_apply);
            this.panel1.Location = new System.Drawing.Point(3, 55);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(714, 433);
            this.panel1.TabIndex = 99;
            this.panel1.SizeChanged += new System.EventHandler(this.panel1_SizeChanged);
            this.panel1.Resize += new System.EventHandler(this.panel1_Resize);
            // 
            // rjLabel27
            // 
            this.rjLabel27.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel27.AutoSize = true;
            this.rjLabel27.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel27.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel27.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel27.LinkLabel = false;
            this.rjLabel27.Location = new System.Drawing.Point(444, 290);
            this.rjLabel27.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel27.Name = "rjLabel27";
            this.rjLabel27.Size = new System.Drawing.Size(118, 17);
            this.rjLabel27.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel27.TabIndex = 55;
            this.rjLabel27.Text = "عرض كروت الروتر فقط";
            this.rjLabel27.Visible = false;
            // 
            // rjLabel18
            // 
            this.rjLabel18.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(78, 34);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel18.Size = new System.Drawing.Size(49, 17);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel18.TabIndex = 93;
            this.rjLabel18.Text = "بواسطة";
            this.rjLabel18.Visible = false;
            // 
            // rjPanel_Page
            // 
            this.rjPanel_Page.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel_Page.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_Page.BorderRadius = 0;
            this.rjPanel_Page.Controls.Add(this.Panel_Pages);
            this.rjPanel_Page.Controls.Add(this.panel2);
            this.rjPanel_Page.Customizable = false;
            this.rjPanel_Page.Location = new System.Drawing.Point(3, 482);
            this.rjPanel_Page.Name = "rjPanel_Page";
            this.rjPanel_Page.Size = new System.Drawing.Size(714, 42);
            this.rjPanel_Page.TabIndex = 100;
            // 
            // Panel_Pages
            // 
            this.Panel_Pages.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Panel_Pages.Controls.Add(this.rjLabel6);
            this.Panel_Pages.Controls.Add(this.CBox_PageCount);
            this.Panel_Pages.Controls.Add(this.rjLabel5);
            this.Panel_Pages.Location = new System.Drawing.Point(488, 0);
            this.Panel_Pages.Name = "Panel_Pages";
            this.Panel_Pages.Size = new System.Drawing.Size(220, 38);
            this.Panel_Pages.TabIndex = 92;
            // 
            // rjLabel6
            // 
            this.rjLabel6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(186, 8);
            this.rjLabel6.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(32, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 35;
            this.rjLabel6.Text = "عرض";
            this.rjLabel6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_PageCount
            // 
            this.CBox_PageCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_PageCount.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_PageCount.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_PageCount.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_PageCount.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_PageCount.BorderRadius = 12;
            this.CBox_PageCount.BorderSize = 1;
            this.CBox_PageCount.Customizable = false;
            this.CBox_PageCount.DataSource = null;
            this.CBox_PageCount.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_PageCount.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_PageCount.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_PageCount.Font = new System.Drawing.Font("Tahoma", 9F);
            this.CBox_PageCount.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_PageCount.Items.AddRange(new object[] {
            "100",
            "200",
            "500",
            "1000",
            "2000",
            "5000",
            "10000",
            "20000",
            "30000",
            "50000",
            "100000",
            "500000"});
            this.CBox_PageCount.Location = new System.Drawing.Point(85, 3);
            this.CBox_PageCount.Name = "CBox_PageCount";
            this.CBox_PageCount.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_PageCount.SelectedIndex = -1;
            this.CBox_PageCount.Size = new System.Drawing.Size(97, 30);
            this.CBox_PageCount.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_PageCount.TabIndex = 91;
            this.CBox_PageCount.Texts = "";
            this.CBox_PageCount.OnSelectedIndexChanged += new System.EventHandler(this.CBox_PageCount_OnSelectedIndexChanged);
            // 
            // rjLabel5
            // 
            this.rjLabel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(6, 9);
            this.rjLabel5.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(68, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 35;
            this.rjLabel5.Text = "في الصفحة";
            this.rjLabel5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.rjLabel8);
            this.panel2.Controls.Add(this.txtAllCountRows);
            this.panel2.Controls.Add(this.btnFirst);
            this.panel2.Controls.Add(this.btnNext);
            this.panel2.Controls.Add(this.txtCurrentPageindex);
            this.panel2.Controls.Add(this.btnLast);
            this.panel2.Controls.Add(this.btnPrev);
            this.panel2.Controls.Add(this.txtTotalPages);
            this.panel2.Controls.Add(this.rjLabel16);
            this.panel2.Controls.Add(this.rjLabel12);
            this.panel2.Location = new System.Drawing.Point(3, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(479, 38);
            this.panel2.TabIndex = 93;
            // 
            // rjLabel8
            // 
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(397, 10);
            this.rjLabel8.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(68, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 35;
            this.rjLabel8.Text = "العدد الكلي";
            this.rjLabel8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txtAllCountRows
            // 
            this.txtAllCountRows._Customizable = false;
            this.txtAllCountRows.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtAllCountRows.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtAllCountRows.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtAllCountRows.BorderRadius = 5;
            this.txtAllCountRows.BorderSize = 1;
            this.txtAllCountRows.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtAllCountRows.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtAllCountRows.Location = new System.Drawing.Point(319, 7);
            this.txtAllCountRows.MultiLine = false;
            this.txtAllCountRows.Name = "txtAllCountRows";
            this.txtAllCountRows.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtAllCountRows.PasswordChar = false;
            this.txtAllCountRows.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtAllCountRows.PlaceHolderText = null;
            this.txtAllCountRows.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtAllCountRows.Size = new System.Drawing.Size(70, 25);
            this.txtAllCountRows.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtAllCountRows.TabIndex = 95;
            this.txtAllCountRows.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtCurrentPageindex
            // 
            this.txtCurrentPageindex._Customizable = false;
            this.txtCurrentPageindex.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtCurrentPageindex.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCurrentPageindex.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtCurrentPageindex.BorderRadius = 5;
            this.txtCurrentPageindex.BorderSize = 1;
            this.txtCurrentPageindex.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtCurrentPageindex.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCurrentPageindex.Location = new System.Drawing.Point(194, 7);
            this.txtCurrentPageindex.MultiLine = false;
            this.txtCurrentPageindex.Name = "txtCurrentPageindex";
            this.txtCurrentPageindex.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtCurrentPageindex.PasswordChar = false;
            this.txtCurrentPageindex.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtCurrentPageindex.PlaceHolderText = null;
            this.txtCurrentPageindex.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtCurrentPageindex.Size = new System.Drawing.Size(47, 25);
            this.txtCurrentPageindex.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtCurrentPageindex.TabIndex = 95;
            this.txtCurrentPageindex.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtTotalPages
            // 
            this.txtTotalPages._Customizable = false;
            this.txtTotalPages.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtTotalPages.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtTotalPages.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtTotalPages.BorderRadius = 5;
            this.txtTotalPages.BorderSize = 1;
            this.txtTotalPages.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtTotalPages.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtTotalPages.Location = new System.Drawing.Point(114, 6);
            this.txtTotalPages.MultiLine = false;
            this.txtTotalPages.Name = "txtTotalPages";
            this.txtTotalPages.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtTotalPages.PasswordChar = false;
            this.txtTotalPages.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtTotalPages.PlaceHolderText = null;
            this.txtTotalPages.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtTotalPages.Size = new System.Drawing.Size(47, 25);
            this.txtTotalPages.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtTotalPages.TabIndex = 95;
            this.txtTotalPages.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(245, 10);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel16.Size = new System.Drawing.Size(39, 17);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 35;
            this.rjLabel16.Text = "صفحة";
            this.rjLabel16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel12
            // 
            this.rjLabel12.AutoSize = true;
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(166, 10);
            this.rjLabel12.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel12.Size = new System.Drawing.Size(23, 17);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel12.TabIndex = 35;
            this.rjLabel12.Text = "من";
            this.rjLabel12.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPane_Top
            // 
            this.rjPane_Top.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPane_Top.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPane_Top.BorderRadius = 13;
            this.rjPane_Top.Controls.Add(this.btn_RemoveFinsh_Validaty);
            this.rjPane_Top.Controls.Add(this.btn_search);
            this.rjPane_Top.Controls.Add(this.btn_Filter);
            this.rjPane_Top.Controls.Add(this.CBox_OrderBy);
            this.rjPane_Top.Controls.Add(this.CBox_SearchBy);
            this.rjPane_Top.Controls.Add(this.btnDelete);
            this.rjPane_Top.Controls.Add(this.btnRefresh_DB);
            this.rjPane_Top.Controls.Add(this.btnDisable);
            this.rjPane_Top.Controls.Add(this.btnEnable);
            this.rjPane_Top.Controls.Add(this.txt_search);
            this.rjPane_Top.Controls.Add(this.btnRefresh);
            this.rjPane_Top.Controls.Add(this.rjLabel7);
            this.rjPane_Top.Controls.Add(this.CheckBox_orderBy);
            this.rjPane_Top.Customizable = false;
            this.rjPane_Top.Location = new System.Drawing.Point(3, 6);
            this.rjPane_Top.Name = "rjPane_Top";
            this.rjPane_Top.Size = new System.Drawing.Size(978, 45);
            this.rjPane_Top.TabIndex = 97;
            // 
            // CBox_OrderBy
            // 
            this.CBox_OrderBy.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_OrderBy.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_OrderBy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_OrderBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_OrderBy.BorderRadius = 5;
            this.CBox_OrderBy.BorderSize = 1;
            this.CBox_OrderBy.Customizable = false;
            this.CBox_OrderBy.DataSource = null;
            this.CBox_OrderBy.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_OrderBy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_OrderBy.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_OrderBy.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.CBox_OrderBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_OrderBy.Location = new System.Drawing.Point(103, 5);
            this.CBox_OrderBy.Name = "CBox_OrderBy";
            this.CBox_OrderBy.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_OrderBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_OrderBy.SelectedIndex = -1;
            this.CBox_OrderBy.Size = new System.Drawing.Size(145, 32);
            this.CBox_OrderBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_OrderBy.TabIndex = 94;
            this.CBox_OrderBy.Texts = "";
            this.CBox_OrderBy.OnSelectedIndexChanged += new System.EventHandler(this.CBox_OrderBy_OnSelectedIndexChanged);
            // 
            // CBox_SearchBy
            // 
            this.CBox_SearchBy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SearchBy.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SearchBy.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SearchBy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SearchBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.BorderRadius = 5;
            this.CBox_SearchBy.BorderSize = 1;
            this.CBox_SearchBy.Customizable = false;
            this.CBox_SearchBy.DataSource = null;
            this.CBox_SearchBy.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SearchBy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SearchBy.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.CBox_SearchBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SearchBy.Items.AddRange(new object[] {
            "الاسم",
            "المرور",
            "التسلسل",
            " نقطة بيع"});
            this.CBox_SearchBy.Location = new System.Drawing.Point(503, 7);
            this.CBox_SearchBy.Name = "CBox_SearchBy";
            this.CBox_SearchBy.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SearchBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SearchBy.SelectedIndex = -1;
            this.CBox_SearchBy.Size = new System.Drawing.Size(86, 30);
            this.CBox_SearchBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SearchBy.TabIndex = 92;
            this.CBox_SearchBy.Texts = "";
            // 
            // rjLabel7
            // 
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(243, 12);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(48, 17);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 95;
            this.rjLabel7.Text = "ترتيب بــ:";
            // 
            // CheckBox_orderBy
            // 
            this.CheckBox_orderBy.AutoSize = true;
            this.CheckBox_orderBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_orderBy.BorderSize = 1;
            this.CheckBox_orderBy.Check = true;
            this.CheckBox_orderBy.Checked = true;
            this.CheckBox_orderBy.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_orderBy.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_orderBy.Customizable = false;
            this.CheckBox_orderBy.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_orderBy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_orderBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_orderBy.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_orderBy.Location = new System.Drawing.Point(18, 10);
            this.CheckBox_orderBy.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_orderBy.Name = "CheckBox_orderBy";
            this.CheckBox_orderBy.Padding = new System.Windows.Forms.Padding(0, 0, 18, 0);
            this.CheckBox_orderBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_orderBy.Size = new System.Drawing.Size(77, 21);
            this.CheckBox_orderBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_orderBy.TabIndex = 58;
            this.CheckBox_orderBy.Text = "تنازلي";
            this.CheckBox_orderBy.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_orderBy.UseVisualStyleBackColor = true;
            this.CheckBox_orderBy.CheckedChanged += new System.EventHandler(this.CheckBox_orderBy_CheckedChanged);
            // 
            // lbl_note
            // 
            this.lbl_note.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(21, 529);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(447, 17);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 101;
            this.lbl_note.Text = "لمزيد من الخيارات اضغط بزر الماوس الايمن  -  لتفاصيل الكرت نقرتين بالماوس علي الس" +
    "طر\r\n";
            // 
            // FormAllCardsUserManager
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "FormAllCardsUserManager";
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.HelpMessage = "";
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "FormAllCardsUserManager";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "FormAllCardsUserManager";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormAllCardsUserManager_FormClosing);
            this.Load += new System.EventHandler(this.FormAllCardsUserManager_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.dmAll_Cards.ResumeLayout(false);
            this.pnl_side_Count_Session.ResumeLayout(false);
            this.fpnl_showArchive.ResumeLayout(false);
            this.flowLayoutPanel1.ResumeLayout(false);
            this.flowLayoutPanel2.ResumeLayout(false);
            this.fpnl_showServer.ResumeLayout(false);
            this.rjPanel_back_side.ResumeLayout(false);
            this.rjPanel_back_side.PerformLayout();
            this.pnl_side_Finsh_Cards.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.rjPanel_Page.ResumeLayout(false);
            this.Panel_Pages.ResumeLayout(false);
            this.Panel_Pages.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.rjPane_Top.ResumeLayout(false);
            this.rjPane_Top.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private RJControls.RJLabel lblNumberItems;
        private RJControls.RJButton btnPreviousPage;
        private RJControls.RJButton btnNextPage;
        private RJControls.RJButton btn_search;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJDataGridView dgv;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem Status_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SN_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Price_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Count_profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SellingPoint_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BachCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_TransferLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_DownloadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UploadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_Up_Down_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_RegDate_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_LastSeenAt_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_FirstUse_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTillTime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTimeLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTransferLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Descr_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem UserName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem View_Hide_toolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CusName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem Copy_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Copy_AllRowToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportExcelToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportText_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem DeleteCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteCardsArchive_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteServerArchiveToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteSession_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem DisableCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem EnableCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RestCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BindMAC_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RemoveBindMAC_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem PrintCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem AddProfile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ChangeSP_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Password_ToolStripMenuItem;
        private System.Windows.Forms.Timer timer_SideBar;
        private RJControls.RJButton btnRefresh;
        private RJControls.RJButton btnDisable;
        private RJControls.RJLabel lbl_to;
        private RJControls.RJDatePicker Date_To;
        private RJControls.RJDatePicker Date_From;
        private RJControls.RJCheckBox CheckBox_byDatePrint;
        private RJControls.RJComboBox CBox_Customer;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJButton btn_apply;
        private RJControls.RJPanel pnl_side_Count_Session;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJButton btnRefresh_DB;
        private RJControls.RJToggleButton ToggleButton_ByCountSession;
        private RJControls.RJLabel rjLabel4;
        private System.Windows.Forms.ToolStripMenuItem CountSession_ToolStripMenuItem;
        private RJControls.RJButton btnEdit;
        private System.Windows.Forms.ToolTip toolTip1;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJButton btnEnable;
        private System.Windows.Forms.ToolStripMenuItem LastSynDb_ToolStripMenuItem;
        private RJControls.RJLabel lbl_Filter;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJToggleButton ToggleButton_ByCountProfile;
        private RJControls.RJButton btn_MenuProcess;
        private RJControls.RJButton btnDelete;
        private System.Windows.Forms.ToolStripMenuItem PageNumber_ToolStripMenuItem;
        private RJControls.RJComboBox CBox_Staus;
        private RJControls.RJLabel rjLabel14;
        private System.Windows.Forms.ToolStripMenuItem Restor_ColumnToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمفقطToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem;
        private RJControls.RJComboBox CBox_Didabled;
        private RJControls.RJLabel rjLabel2;
        private System.Windows.Forms.ToolStripMenuItem Remove_SP_ToolStripMenuItem;
        private RJControls.RJPanel rjPanel_back_side;
        private System.Windows.Forms.Panel panel1;
        private RJControls.RJPanel rjPanel_Page;
        private System.Windows.Forms.Panel Panel_Pages;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJComboBox CBox_PageCount;
        private RJControls.RJLabel rjLabel5;
        private System.Windows.Forms.Panel panel2;
        private RJControls.RJTextBox txtAllCountRows;
        private RJControls.RJButton btnFirst;
        private RJControls.RJButton btnNext;
        private RJControls.RJTextBox txtCurrentPageindex;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJLabel rjLabel12;
        private RJControls.RJButton btnLast;
        private RJControls.RJButton btnPrev;
        private RJControls.RJTextBox txtTotalPages;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJPanel rjPane_Top;
        private RJControls.RJLabel rjLabel18;
        private RJControls.RJComboBox CBox_SearchBy;
        private RJControls.RJButton rjButton1;
        private RJControls.RJComboBox CBox_OrderBy;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJCheckBox CheckBox_orderBy;
        private FontAwesome.Sharp.IconMenuItem SaveDGVToolStripMenuItem;
        private RJControls.RJButton btn_RemoveFinsh_Validaty;
        private System.Windows.Forms.ToolStripMenuItem Sn_Archive_ToolStripMenuItem;
        private RJControls.RJLabel rjLabel13;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.ToolStripMenuItem الايامالمتبقيةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem NumberPrint_toolStripMenuItem;
        private RJControls.RJLabel rjLabel11;
        public RJControls.RJComboBox CBox_Batch;
        public RJControls.RJComboBox CBox_NumberPrint;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private RJControls.RJLabel lbl_note;
        private System.Windows.Forms.ToolStripMenuItem تعديلرقمالطبعةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تعديلرقمالدفعةToolStripMenuItem;
        private System.Windows.Forms.FlowLayoutPanel fpnl_showArchive;
        private RJControls.RJToggleButton ToggleButton_Show_Archive;
        private RJControls.RJPanel pnl_side_Finsh_Cards;
        private RJControls.RJButton btn_RemoveFinsh_Download;
        private RJControls.RJButton btn_RemoveFinsh_Uptime;
        private RJControls.RJButton btn_RemoveFinsh_All;
        private RJControls.RJLabel rjLabel27;
        private System.Windows.Forms.FlowLayoutPanel fpnl_showServer;
        private RJControls.RJToggleButton ToggleButton_Show_onlyServer;
    }
}