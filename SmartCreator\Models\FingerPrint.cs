﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows;

namespace SmartCreator.Models
{
    public class FingerPrint
    {
        public string Value()
        {
            string b64 = "";
            try
            {
                string id = cpuId() + biosId() + baseId();
                string fingerPrint = GetHash(id);
                byte[] data = Encoding.UTF8.GetBytes(fingerPrint.Replace("-", ""));
                b64 = Convert.ToBase64String(data);
            }
            catch (Exception ex) { MessageBox.Show("FingerPrint\n" + ex.Message); }
            return (b64);
        }
        private string cpuId()
        {
            string retVal = "";
            try
            {
                retVal = identifier("Win32_Processor", "UniqueId");
                if (retVal == "") //If no UniqueID, use ProcessorID
                {
                    retVal = identifier("Win32_Processor", "ProcessorId");
                    if (retVal == "") //If no ProcessorId, use Name
                    {
                        retVal = identifier("Win32_Processor", "Name");
                        if (retVal == "") //If no Name, use Manufacturer
                        {
                            retVal = identifier("Win32_Processor", "Manufacturer");
                        }
                        //Add clock speed for extra security
                        retVal += identifier("Win32_Processor", "MaxClockSpeed");
                    }
                }
            }
            catch { }
            return retVal;
        }
        //BIOS Identifier
        private string biosId()
        {
            string resutl = "";
            try
            {
                resutl = identifier("Win32_BIOS", "Manufacturer")
                + identifier("Win32_BIOS", "SMBIOSBIOSVersion")
                + identifier("Win32_BIOS", "IdentificationCode")
                + identifier("Win32_BIOS", "SerialNumber")
                + identifier("Win32_BIOS", "ReleaseDate")
                + identifier("Win32_BIOS", "Version");
            }
            catch { }
            return resutl;
        }
        //Main physical hard drive ID
        private string diskId()
        {
            string resutl = "";
            try
            {
                resutl = identifier("Win32_DiskDrive", "Model")
            + identifier("Win32_DiskDrive", "Manufacturer")
            + identifier("Win32_DiskDrive", "Signature")
            + identifier("Win32_DiskDrive", "TotalHeads");
            }
            catch { }
            return resutl;
        }
        //Motherboard ID
        private string baseId()
        {
            string resutl = "";
            try
            {
                resutl = identifier("Win32_BaseBoard", "Model")
            + identifier("Win32_BaseBoard", "Manufacturer")
            + identifier("Win32_BaseBoard", "Name")
            + identifier("Win32_BaseBoard", "SerialNumber");
            }
            catch { }
            return resutl;
        }
        //Primary video controller ID
        private string identifier(string wmiClass, string wmiProperty)
        {
            string result = "";
            try
            {
                System.Management.ManagementClass mc = new System.Management.ManagementClass(wmiClass);
                System.Management.ManagementObjectCollection moc = mc.GetInstances();
                foreach (System.Management.ManagementObject mo in moc)
                {
                    //Only get the first one
                    if (result == "")
                    {
                        try
                        {
                            if(mo[wmiProperty]!=null)
                                result = mo[wmiProperty].ToString();
                            break;
                        }
                        catch
                        {
                        }
                    }
                }
            }
            catch { }
            return result;
        }
        private string GetHash(string s)
        {
            string resutl = "";
            try
            {
                MD5 sec = new MD5CryptoServiceProvider();
                ASCIIEncoding enc = new ASCIIEncoding();
                byte[] bt = enc.GetBytes(s);
                resutl = GetHexString(sec.ComputeHash(bt));
            }
            catch { }
            return resutl;
        }
        private string GetHexString(byte[] bt)
        {
            string s = string.Empty;
            try
            {
                for (int i = 0; i < bt.Length; i++)
                {
                    byte b = bt[i];
                    int n, n1, n2;
                    n = (int)b;
                    n1 = n & 15;
                    n2 = (n >> 4) & 15;
                    if (n2 > 9)
                        s += ((char)(n2 - 10 + (int)'A')).ToString();
                    else
                        s += n2.ToString();
                    if (n1 > 9)
                        s += ((char)(n1 - 10 + (int)'A')).ToString();
                    else
                        s += n1.ToString();
                    if ((i + 1) != bt.Length && (i + 1) % 2 == 0) s += "-";
                }
                return s;
            }
            catch { }
            return s;
        }


    }
}
