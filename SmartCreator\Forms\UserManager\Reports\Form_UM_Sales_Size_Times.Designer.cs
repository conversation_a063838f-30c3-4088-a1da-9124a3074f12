﻿namespace SmartCreator.Forms.UserManager
{
    partial class Form_UM_Sales_Size_Times
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.txt_uptime = new SmartCreator.RJControls.RJTextBox();
            this.txt_download = new SmartCreator.RJControls.RJTextBox();
            this.txt_count_Cards = new SmartCreator.RJControls.RJTextBox();
            this.txt_Total_Session = new SmartCreator.RJControls.RJTextBox();
            this.lbl_count_Cards = new SmartCreator.RJControls.RJLabel();
            this.lbl_Total_Session = new SmartCreator.RJControls.RJLabel();
            this.timer_SideBar = new System.Windows.Forms.Timer(this.components);
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.View_Hide_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Status_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SN_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.UserName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Price_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SellingPoint_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BachCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_TransferLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_DownloadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UploadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_Up_Down_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_FirstUse_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.الجهازPortToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Descr_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CusName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_RegDate_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CountSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.count_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DateToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخCtrlcToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخالسطركاملToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالىملفاكسلToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالىملفنصيToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.lbl_uptime = new SmartCreator.RJControls.RJLabel();
            this.lbl_download = new SmartCreator.RJControls.RJLabel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.rjPanel_topFilter = new SmartCreator.RJControls.RJPanel();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.btn_ = new SmartCreator.RJControls.RJButton();
            this.rjButton3 = new SmartCreator.RJControls.RJButton();
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.rjDateTime_To = new SmartCreator.RJControls.RJDatePicker();
            this.rjDateTime_From = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_To_Date = new SmartCreator.RJControls.RJCheckBox();
            this.rjPanel12 = new SmartCreator.RJControls.RJPanel();
            this.ToggleButton_Detail = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Monthly = new SmartCreator.RJControls.RJToggleButton();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.dgv2 = new SmartCreator.RJControls.RJDataGridView();
            this.Toggle_By_Group = new SmartCreator.RJControls.RJToggleButton();
            this.lbl_countSession = new SmartCreator.RJControls.RJLabel();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.txt_countSession = new SmartCreator.RJControls.RJTextBox();
            this.Spanel = new SmartCreator.RJControls.RJPanel();
            this.panel1_side = new System.Windows.Forms.Panel();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.CBox_Port = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.panel3_side = new System.Windows.Forms.Panel();
            this.panel2_side = new System.Windows.Forms.Panel();
            this.pnl_side_sn = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.CBox_Customer = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Radius = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.rjButton4 = new SmartCreator.RJControls.RJButton();
            this.btn_Fix = new SmartCreator.RJControls.RJButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.rjButton2 = new SmartCreator.RJControls.RJButton();
            this.rjDropdownMenu1 = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.SaveDGVToolStripMenuItem = new FontAwesome.Sharp.IconMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem5 = new System.Windows.Forms.ToolStripMenuItem();
            this.Password_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem6 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem7 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem8 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem9 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem11 = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_LastSeenAt_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem12 = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTillTime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTimeLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTransferLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem13 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem14 = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.LastSynDb_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.PageNumber_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Restor_ColumnToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem15 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.Copy_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_AllRowToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportText_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمفقطToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.DeleteCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteCardsArchive_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteServerArchiveToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.DeleteSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.DisableCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.EnableCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RestCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BindMAC_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RemoveBindMAC_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator6 = new System.Windows.Forms.ToolStripSeparator();
            this.PrintCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.AddProfile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ChangeSP_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Remove_SP_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnlClientArea.SuspendLayout();
            this.dmAll_Cards.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.rjPanel_topFilter.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).BeginInit();
            this.rjPanel2.SuspendLayout();
            this.Spanel.SuspendLayout();
            this.pnl_side_sn.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.rjDropdownMenu1.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.Spanel);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.dgv2);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Controls.Add(this.rjPanel_topFilter);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 550);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(174, 17);
            this.lblCaption.Text = "Form_UM_Sales_Size_Times";
            // 
            // toolTip1
            // 
            this.toolTip1.BackColor = System.Drawing.Color.Red;
            this.toolTip1.ForeColor = System.Drawing.Color.White;
            this.toolTip1.ToolTipIcon = System.Windows.Forms.ToolTipIcon.Info;
            // 
            // txt_uptime
            // 
            this.txt_uptime._Customizable = false;
            this.txt_uptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_uptime.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptime.BorderRadius = 3;
            this.txt_uptime.BorderSize = 1;
            this.txt_uptime.Enabled = false;
            this.txt_uptime.Font = new System.Drawing.Font("Tahoma", 10.8F, System.Drawing.FontStyle.Bold);
            this.txt_uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptime.Location = new System.Drawing.Point(6, 83);
            this.txt_uptime.MultiLine = false;
            this.txt_uptime.Name = "txt_uptime";
            this.txt_uptime.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptime.PasswordChar = false;
            this.txt_uptime.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptime.PlaceHolderText = null;
            this.txt_uptime.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptime.Size = new System.Drawing.Size(140, 29);
            this.txt_uptime.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_uptime.TabIndex = 3;
            this.txt_uptime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.toolTip1.SetToolTip(this.txt_uptime, "اجمالي وقت جميع الجلسات التي دخلت في التاريخ المحدد");
            // 
            // txt_download
            // 
            this.txt_download._Customizable = false;
            this.txt_download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_download.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_download.BorderRadius = 3;
            this.txt_download.BorderSize = 1;
            this.txt_download.Enabled = false;
            this.txt_download.Font = new System.Drawing.Font("Tahoma", 10.8F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_download.Location = new System.Drawing.Point(6, 119);
            this.txt_download.MultiLine = false;
            this.txt_download.Name = "txt_download";
            this.txt_download.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_download.PasswordChar = false;
            this.txt_download.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_download.PlaceHolderText = null;
            this.txt_download.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_download.Size = new System.Drawing.Size(140, 29);
            this.txt_download.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_download.TabIndex = 3;
            this.txt_download.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.toolTip1.SetToolTip(this.txt_download, "اجمالي التحميل + الرقع لجميع الجلسات التي دخلت في التاريخ المحدد");
            // 
            // txt_count_Cards
            // 
            this.txt_count_Cards._Customizable = false;
            this.txt_count_Cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Cards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Cards.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Cards.BorderRadius = 3;
            this.txt_count_Cards.BorderSize = 1;
            this.txt_count_Cards.Enabled = false;
            this.txt_count_Cards.Font = new System.Drawing.Font("Tahoma", 10.8F, System.Drawing.FontStyle.Bold);
            this.txt_count_Cards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Cards.Location = new System.Drawing.Point(6, 10);
            this.txt_count_Cards.MultiLine = false;
            this.txt_count_Cards.Name = "txt_count_Cards";
            this.txt_count_Cards.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Cards.PasswordChar = false;
            this.txt_count_Cards.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Cards.PlaceHolderText = null;
            this.txt_count_Cards.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Cards.Size = new System.Drawing.Size(140, 29);
            this.txt_count_Cards.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_count_Cards.TabIndex = 85;
            this.txt_count_Cards.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.toolTip1.SetToolTip(this.txt_count_Cards, "جميع الكروت التي اشتغلت في الفتره المحدده سواء اول دخول او لا ");
            // 
            // txt_Total_Session
            // 
            this.txt_Total_Session._Customizable = false;
            this.txt_Total_Session.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Total_Session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Total_Session.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Total_Session.BorderRadius = 3;
            this.txt_Total_Session.BorderSize = 1;
            this.txt_Total_Session.Enabled = false;
            this.txt_Total_Session.Font = new System.Drawing.Font("Tahoma", 10.8F, System.Drawing.FontStyle.Bold);
            this.txt_Total_Session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Total_Session.Location = new System.Drawing.Point(6, 45);
            this.txt_Total_Session.MultiLine = false;
            this.txt_Total_Session.Name = "txt_Total_Session";
            this.txt_Total_Session.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Total_Session.PasswordChar = false;
            this.txt_Total_Session.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Total_Session.PlaceHolderText = null;
            this.txt_Total_Session.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Total_Session.Size = new System.Drawing.Size(140, 29);
            this.txt_Total_Session.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Total_Session.TabIndex = 85;
            this.txt_Total_Session.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.toolTip1.SetToolTip(this.txt_Total_Session, "اجمالي الجسلات التي اشتغلت في التاريخ المحدد");
            // 
            // lbl_count_Cards
            // 
            this.lbl_count_Cards.AutoSize = true;
            this.lbl_count_Cards.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_count_Cards.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_count_Cards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_count_Cards.LinkLabel = false;
            this.lbl_count_Cards.Location = new System.Drawing.Point(158, 16);
            this.lbl_count_Cards.Name = "lbl_count_Cards";
            this.lbl_count_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_count_Cards.Size = new System.Drawing.Size(83, 17);
            this.lbl_count_Cards.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_count_Cards.TabIndex = 83;
            this.lbl_count_Cards.Text = "اجمالي الكروت";
            this.lbl_count_Cards.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.toolTip1.SetToolTip(this.lbl_count_Cards, "جميع الكروت التي اشتغلت في الفتره المحدده سواء اول دخول او لا ");
            // 
            // lbl_Total_Session
            // 
            this.lbl_Total_Session.AutoSize = true;
            this.lbl_Total_Session.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Total_Session.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Total_Session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Total_Session.LinkLabel = false;
            this.lbl_Total_Session.Location = new System.Drawing.Point(153, 54);
            this.lbl_Total_Session.Name = "lbl_Total_Session";
            this.lbl_Total_Session.Size = new System.Drawing.Size(90, 17);
            this.lbl_Total_Session.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Total_Session.TabIndex = 83;
            this.lbl_Total_Session.Text = "اجمالي الجلسات";
            this.toolTip1.SetToolTip(this.lbl_Total_Session, "اجمالي الجسلات التي اشتغلت في التاريخ المحدد");
            // 
            // timer_SideBar
            // 
            this.timer_SideBar.Interval = 15;
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripSeparator1,
            this.View_Hide_toolStripMenuItem,
            this.toolStripMenuItem2,
            this.نسخCtrlcToolStripMenuItem,
            this.نسخالسطركاملToolStripMenuItem,
            this.تصديرالىملفاكسلToolStripMenuItem,
            this.تصديرالىملفنصيToolStripMenuItem,
            this.toolStripSeparator2});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(217, 148);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(213, 6);
            // 
            // View_Hide_toolStripMenuItem
            // 
            this.View_Hide_toolStripMenuItem.Checked = true;
            this.View_Hide_toolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.View_Hide_toolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.Status_ToolStripMenuItem,
            this.SN_ToolStripMenuItem,
            this.UserName_ToolStripMenuItem,
            this.Price_ToolStripMenuItem,
            this.Profile_ToolStripMenuItem,
            this.SellingPoint_ToolStripMenuItem,
            this.BachCards_ToolStripMenuItem,
            this.Str_UptimeLimit_ToolStripMenuItem,
            this.Str_TransferLimit_ToolStripMenuItem,
            this.Str_DownloadUsed_ToolStripMenuItem,
            this.Str_UploadUsed_ToolStripMenuItem,
            this.Str_UptimeUsed_ToolStripMenuItem,
            this.Str_Up_Down_ToolStripMenuItem,
            this.dt_FirstUse_ToolStripMenuItem,
            this.الجهازPortToolStripMenuItem,
            this.Descr_ToolStripMenuItem,
            this.CusName_ToolStripMenuItem,
            this.dt_RegDate_ToolStripMenuItem,
            this.CountSession_ToolStripMenuItem,
            this.count_ToolStripMenuItem,
            this.DateToolStripMenuItem});
            this.View_Hide_toolStripMenuItem.Name = "View_Hide_toolStripMenuItem";
            this.View_Hide_toolStripMenuItem.Size = new System.Drawing.Size(216, 22);
            this.View_Hide_toolStripMenuItem.Text = "عرض واخفاء الاعمدة";
            // 
            // Status_ToolStripMenuItem
            // 
            this.Status_ToolStripMenuItem.Name = "Status_ToolStripMenuItem";
            this.Status_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Status_ToolStripMenuItem.Tag = "Str_Status";
            this.Status_ToolStripMenuItem.Text = "الحــــالــــة";
            this.Status_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // SN_ToolStripMenuItem
            // 
            this.SN_ToolStripMenuItem.Name = "SN_ToolStripMenuItem";
            this.SN_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.SN_ToolStripMenuItem.Tag = "Sn";
            this.SN_ToolStripMenuItem.Text = "الرقم التسلسلي";
            this.SN_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // UserName_ToolStripMenuItem
            // 
            this.UserName_ToolStripMenuItem.Checked = true;
            this.UserName_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.UserName_ToolStripMenuItem.Name = "UserName_ToolStripMenuItem";
            this.UserName_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.UserName_ToolStripMenuItem.Tag = "UserName";
            this.UserName_ToolStripMenuItem.Text = "الاســـــــم";
            this.UserName_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Price_ToolStripMenuItem
            // 
            this.Price_ToolStripMenuItem.Checked = true;
            this.Price_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Price_ToolStripMenuItem.Name = "Price_ToolStripMenuItem";
            this.Price_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Price_ToolStripMenuItem.Tag = "Str_MoneyTotal";
            this.Price_ToolStripMenuItem.Text = "الســـــــعر";
            this.Price_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Profile_ToolStripMenuItem
            // 
            this.Profile_ToolStripMenuItem.Name = "Profile_ToolStripMenuItem";
            this.Profile_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Profile_ToolStripMenuItem.Tag = "ProfileName";
            this.Profile_ToolStripMenuItem.Text = "البــــــــاقة";
            this.Profile_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // SellingPoint_ToolStripMenuItem
            // 
            this.SellingPoint_ToolStripMenuItem.Name = "SellingPoint_ToolStripMenuItem";
            this.SellingPoint_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.SellingPoint_ToolStripMenuItem.Tag = "SpName";
            this.SellingPoint_ToolStripMenuItem.Text = "نقـــــطة البيع";
            this.SellingPoint_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // BachCards_ToolStripMenuItem
            // 
            this.BachCards_ToolStripMenuItem.Name = "BachCards_ToolStripMenuItem";
            this.BachCards_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.BachCards_ToolStripMenuItem.Tag = "BatchCardId";
            this.BachCards_ToolStripMenuItem.Text = "الـــــدفعــــــه";
            this.BachCards_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_UptimeLimit_ToolStripMenuItem
            // 
            this.Str_UptimeLimit_ToolStripMenuItem.Name = "Str_UptimeLimit_ToolStripMenuItem";
            this.Str_UptimeLimit_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_UptimeLimit_ToolStripMenuItem.Tag = "Str_UptimeLimit";
            this.Str_UptimeLimit_ToolStripMenuItem.Text = "الوقت المسموح";
            this.Str_UptimeLimit_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_TransferLimit_ToolStripMenuItem
            // 
            this.Str_TransferLimit_ToolStripMenuItem.Name = "Str_TransferLimit_ToolStripMenuItem";
            this.Str_TransferLimit_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_TransferLimit_ToolStripMenuItem.Tag = "Str_TransferLimit";
            this.Str_TransferLimit_ToolStripMenuItem.Text = "التنزيل المسموح";
            this.Str_TransferLimit_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_DownloadUsed_ToolStripMenuItem
            // 
            this.Str_DownloadUsed_ToolStripMenuItem.Name = "Str_DownloadUsed_ToolStripMenuItem";
            this.Str_DownloadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_DownloadUsed_ToolStripMenuItem.Tag = "Str_DownloadUsed";
            this.Str_DownloadUsed_ToolStripMenuItem.Text = "التحميل المستخدم";
            this.Str_DownloadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_UploadUsed_ToolStripMenuItem
            // 
            this.Str_UploadUsed_ToolStripMenuItem.Name = "Str_UploadUsed_ToolStripMenuItem";
            this.Str_UploadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_UploadUsed_ToolStripMenuItem.Tag = "Str_UploadUsed";
            this.Str_UploadUsed_ToolStripMenuItem.Text = "الرقع المستخدم";
            this.Str_UploadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_UptimeUsed_ToolStripMenuItem
            // 
            this.Str_UptimeUsed_ToolStripMenuItem.Name = "Str_UptimeUsed_ToolStripMenuItem";
            this.Str_UptimeUsed_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_UptimeUsed_ToolStripMenuItem.Tag = "Str_UptimeUsed";
            this.Str_UptimeUsed_ToolStripMenuItem.Text = "الوقت المستخدم";
            this.Str_UptimeUsed_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Str_Up_Down_ToolStripMenuItem
            // 
            this.Str_Up_Down_ToolStripMenuItem.Name = "Str_Up_Down_ToolStripMenuItem";
            this.Str_Up_Down_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Str_Up_Down_ToolStripMenuItem.Tag = "Str_Up_Down";
            this.Str_Up_Down_ToolStripMenuItem.Text = "تحميـــــل+رفــــع (الاستهلاك)";
            this.Str_Up_Down_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // dt_FirstUse_ToolStripMenuItem
            // 
            this.dt_FirstUse_ToolStripMenuItem.Checked = true;
            this.dt_FirstUse_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.dt_FirstUse_ToolStripMenuItem.Name = "dt_FirstUse_ToolStripMenuItem";
            this.dt_FirstUse_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.dt_FirstUse_ToolStripMenuItem.Tag = "FirsLogin";
            this.dt_FirstUse_ToolStripMenuItem.Text = "اول دخـــــــــول";
            this.dt_FirstUse_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // الجهازPortToolStripMenuItem
            // 
            this.الجهازPortToolStripMenuItem.Name = "الجهازPortToolStripMenuItem";
            this.الجهازPortToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.الجهازPortToolStripMenuItem.Tag = "NasPortId";
            this.الجهازPortToolStripMenuItem.Text = "الجهاز - البورت";
            this.الجهازPortToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // Descr_ToolStripMenuItem
            // 
            this.Descr_ToolStripMenuItem.Name = "Descr_ToolStripMenuItem";
            this.Descr_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.Descr_ToolStripMenuItem.Tag = "Comment";
            this.Descr_ToolStripMenuItem.Text = "تعلـــــــيق";
            this.Descr_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // CusName_ToolStripMenuItem
            // 
            this.CusName_ToolStripMenuItem.Name = "CusName_ToolStripMenuItem";
            this.CusName_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.CusName_ToolStripMenuItem.Tag = "CustomerName";
            this.CusName_ToolStripMenuItem.Text = "عميل يوزمنجر";
            this.CusName_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // dt_RegDate_ToolStripMenuItem
            // 
            this.dt_RegDate_ToolStripMenuItem.Name = "dt_RegDate_ToolStripMenuItem";
            this.dt_RegDate_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.dt_RegDate_ToolStripMenuItem.Tag = "RegDate";
            this.dt_RegDate_ToolStripMenuItem.Text = "تـــاريخ الاضــــافة";
            this.dt_RegDate_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // CountSession_ToolStripMenuItem
            // 
            this.CountSession_ToolStripMenuItem.Name = "CountSession_ToolStripMenuItem";
            this.CountSession_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.CountSession_ToolStripMenuItem.Tag = "CountSession";
            this.CountSession_ToolStripMenuItem.Text = "عدد جلسات الكرت";
            this.CountSession_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // count_ToolStripMenuItem
            // 
            this.count_ToolStripMenuItem.Checked = true;
            this.count_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.count_ToolStripMenuItem.Name = "count_ToolStripMenuItem";
            this.count_ToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.count_ToolStripMenuItem.Tag = "count";
            this.count_ToolStripMenuItem.Text = "عدد الكروت";
            this.count_ToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // DateToolStripMenuItem
            // 
            this.DateToolStripMenuItem.Checked = true;
            this.DateToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.DateToolStripMenuItem.Name = "DateToolStripMenuItem";
            this.DateToolStripMenuItem.Size = new System.Drawing.Size(205, 22);
            this.DateToolStripMenuItem.Tag = "date";
            this.DateToolStripMenuItem.Text = "الناريخ";
            this.DateToolStripMenuItem.Click += new System.EventHandler(this.UserName_ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(216, 22);
            this.toolStripMenuItem2.Text = "عرض معلومات الكرت المحدد";
            // 
            // نسخCtrlcToolStripMenuItem
            // 
            this.نسخCtrlcToolStripMenuItem.Name = "نسخCtrlcToolStripMenuItem";
            this.نسخCtrlcToolStripMenuItem.Size = new System.Drawing.Size(216, 22);
            this.نسخCtrlcToolStripMenuItem.Text = "نسخ                 ctrl+c";
            // 
            // نسخالسطركاملToolStripMenuItem
            // 
            this.نسخالسطركاملToolStripMenuItem.Name = "نسخالسطركاملToolStripMenuItem";
            this.نسخالسطركاملToolStripMenuItem.Size = new System.Drawing.Size(216, 22);
            this.نسخالسطركاملToolStripMenuItem.Text = "نسخ السطر كامل ";
            // 
            // تصديرالىملفاكسلToolStripMenuItem
            // 
            this.تصديرالىملفاكسلToolStripMenuItem.Name = "تصديرالىملفاكسلToolStripMenuItem";
            this.تصديرالىملفاكسلToolStripMenuItem.Size = new System.Drawing.Size(216, 22);
            this.تصديرالىملفاكسلToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            // 
            // تصديرالىملفنصيToolStripMenuItem
            // 
            this.تصديرالىملفنصيToolStripMenuItem.Name = "تصديرالىملفنصيToolStripMenuItem";
            this.تصديرالىملفنصيToolStripMenuItem.Size = new System.Drawing.Size(216, 22);
            this.تصديرالىملفنصيToolStripMenuItem.Text = "تصدير الى ملف نصي";
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(213, 6);
            // 
            // lbl_uptime
            // 
            this.lbl_uptime.AutoSize = true;
            this.lbl_uptime.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_uptime.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_uptime.LinkLabel = false;
            this.lbl_uptime.Location = new System.Drawing.Point(160, 91);
            this.lbl_uptime.Name = "lbl_uptime";
            this.lbl_uptime.Size = new System.Drawing.Size(80, 17);
            this.lbl_uptime.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_uptime.TabIndex = 57;
            this.lbl_uptime.Text = "اجمالي الوقت";
            // 
            // lbl_download
            // 
            this.lbl_download.AutoSize = true;
            this.lbl_download.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_download.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_download.LinkLabel = false;
            this.lbl_download.Location = new System.Drawing.Point(147, 124);
            this.lbl_download.Name = "lbl_download";
            this.lbl_download.Size = new System.Drawing.Size(101, 17);
            this.lbl_download.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_download.TabIndex = 57;
            this.lbl_download.Text = "اجمالي الاستهلاك";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 10;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 45;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv.ColumnHeadersHeight = 45;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(289, 117);
            this.dgv.Margin = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.dgv.MultiSelect = false;
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 30;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F);
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 30;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(694, 168);
            this.dgv.TabIndex = 80;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellClick);
            this.dgv.SelectionChanged += new System.EventHandler(this.dgv_SelectionChanged);
            // 
            // rjPanel_topFilter
            // 
            this.rjPanel_topFilter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_topFilter.BorderRadius = 10;
            this.rjPanel_topFilter.Controls.Add(this.btn_search);
            this.rjPanel_topFilter.Controls.Add(this.txt_search);
            this.rjPanel_topFilter.Controls.Add(this.btn_Refresh);
            this.rjPanel_topFilter.Controls.Add(this.btn_);
            this.rjPanel_topFilter.Controls.Add(this.rjButton3);
            this.rjPanel_topFilter.Controls.Add(this.btn_Filter);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel1);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel12);
            this.rjPanel_topFilter.Customizable = false;
            this.rjPanel_topFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjPanel_topFilter.Location = new System.Drawing.Point(0, 0);
            this.rjPanel_topFilter.Margin = new System.Windows.Forms.Padding(1, 0, 0, 0);
            this.rjPanel_topFilter.Name = "rjPanel_topFilter";
            this.rjPanel_topFilter.Size = new System.Drawing.Size(990, 108);
            this.rjPanel_topFilter.TabIndex = 79;
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 1;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(660, 67);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(35, 25);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 93;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(699, 67);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث باسم الكرت";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(272, 28);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 92;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(10, 67);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(35, 33);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 75;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // btn_
            // 
            this.btn_.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.BorderRadius = 4;
            this.btn_.BorderSize = 1;
            this.btn_.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_.FlatAppearance.BorderSize = 0;
            this.btn_.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_.IconSize = 1;
            this.btn_.ImageAlign = System.Drawing.ContentAlignment.TopCenter;
            this.btn_.Location = new System.Drawing.Point(160, 67);
            this.btn_.Margin = new System.Windows.Forms.Padding(0);
            this.btn_.Name = "btn_";
            this.btn_.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_.Size = new System.Drawing.Size(88, 33);
            this.btn_.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_.TabIndex = 76;
            this.btn_.Text = "عــرض";
            this.btn_.UseVisualStyleBackColor = false;
            this.btn_.Click += new System.EventHandler(this.btn__Click);
            // 
            // rjButton3
            // 
            this.rjButton3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderRadius = 4;
            this.rjButton3.BorderSize = 1;
            this.rjButton3.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton3.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton3.FlatAppearance.BorderSize = 0;
            this.rjButton3.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton3.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton3.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton3.IconSize = 25;
            this.rjButton3.Location = new System.Drawing.Point(46, 67);
            this.rjButton3.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton3.Name = "rjButton3";
            this.rjButton3.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton3.Size = new System.Drawing.Size(35, 33);
            this.rjButton3.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton3.TabIndex = 74;
            this.rjButton3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton3.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton3.UseVisualStyleBackColor = false;
            this.rjButton3.Click += new System.EventHandler(this.rjButton3_Click);
            // 
            // btn_Filter
            // 
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 17;
            this.btn_Filter.Location = new System.Drawing.Point(82, 67);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Filter.Size = new System.Drawing.Size(76, 33);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Filter.TabIndex = 77;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel1.BorderRadius = 5;
            this.rjPanel1.Controls.Add(this.rjLabel3);
            this.rjPanel1.Controls.Add(this.rjDateTime_To);
            this.rjPanel1.Controls.Add(this.rjDateTime_From);
            this.rjPanel1.Controls.Add(this.CheckBox_To_Date);
            this.rjPanel1.Customizable = true;
            this.rjPanel1.Location = new System.Drawing.Point(402, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(581, 55);
            this.rjPanel1.TabIndex = 73;
            // 
            // rjLabel3
            // 
            this.rjLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(532, 18);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(25, 22);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 49;
            this.rjLabel3.Text = "من";
            // 
            // rjDateTime_To
            // 
            this.rjDateTime_To.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjDateTime_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjDateTime_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjDateTime_To.BorderRadius = 7;
            this.rjDateTime_To.BorderSize = 1;
            this.rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_To.Customizable = false;
            this.rjDateTime_To.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjDateTime_To.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_To.Location = new System.Drawing.Point(82, 10);
            this.rjDateTime_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_To.Name = "rjDateTime_To";
            this.rjDateTime_To.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_To.Size = new System.Drawing.Size(216, 37);
            this.rjDateTime_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjDateTime_To.TabIndex = 80;
            this.rjDateTime_To.Value = new System.DateTime(2024, 9, 27, 23, 59, 59, 0);
            // 
            // rjDateTime_From
            // 
            this.rjDateTime_From.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjDateTime_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjDateTime_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjDateTime_From.BorderRadius = 7;
            this.rjDateTime_From.BorderSize = 1;
            this.rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_From.Customizable = false;
            this.rjDateTime_From.Font = new System.Drawing.Font("Segoe UI", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjDateTime_From.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_From.Location = new System.Drawing.Point(356, 11);
            this.rjDateTime_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_From.Name = "rjDateTime_From";
            this.rjDateTime_From.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_From.Size = new System.Drawing.Size(216, 37);
            this.rjDateTime_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjDateTime_From.TabIndex = 79;
            this.rjDateTime_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            // 
            // CheckBox_To_Date
            // 
            this.CheckBox_To_Date.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_To_Date.AutoSize = true;
            this.CheckBox_To_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.BorderSize = 1;
            this.CheckBox_To_Date.Check = true;
            this.CheckBox_To_Date.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.Checked = true;
            this.CheckBox_To_Date.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_To_Date.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_To_Date.Customizable = false;
            this.CheckBox_To_Date.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.CheckBox_To_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_To_Date.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.Location = new System.Drawing.Point(280, 17);
            this.CheckBox_To_Date.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_To_Date.Name = "CheckBox_To_Date";
            this.CheckBox_To_Date.Padding = new System.Windows.Forms.Padding(0, 0, 18, 0);
            this.CheckBox_To_Date.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_To_Date.Size = new System.Drawing.Size(66, 26);
            this.CheckBox_To_Date.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_To_Date.TabIndex = 94;
            this.CheckBox_To_Date.Text = "الى";
            this.CheckBox_To_Date.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.UseVisualStyleBackColor = true;
            // 
            // rjPanel12
            // 
            this.rjPanel12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel12.BorderRadius = 5;
            this.rjPanel12.Controls.Add(this.ToggleButton_Detail);
            this.rjPanel12.Controls.Add(this.ToggleButton_Monthly);
            this.rjPanel12.Controls.Add(this.jToggleButton_Year);
            this.rjPanel12.Controls.Add(this.rjLabel11);
            this.rjPanel12.Customizable = true;
            this.rjPanel12.Location = new System.Drawing.Point(0, 6);
            this.rjPanel12.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel12.Name = "rjPanel12";
            this.rjPanel12.Size = new System.Drawing.Size(386, 55);
            this.rjPanel12.TabIndex = 58;
            // 
            // ToggleButton_Detail
            // 
            this.ToggleButton_Detail.Activated = true;
            this.ToggleButton_Detail.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Detail.Checked = true;
            this.ToggleButton_Detail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToggleButton_Detail.Customizable = false;
            this.ToggleButton_Detail.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Detail.Location = new System.Drawing.Point(241, 16);
            this.ToggleButton_Detail.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Detail.Name = "ToggleButton_Detail";
            this.ToggleButton_Detail.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.OFF_Text = "تفصيلي";
            this.ToggleButton_Detail.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Detail.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.ON_Text = "تفصيلي";
            this.ToggleButton_Detail.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Detail.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.Size = new System.Drawing.Size(88, 25);
            this.ToggleButton_Detail.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Detail.TabIndex = 52;
            this.ToggleButton_Detail.Tag = "تفصيلي";
            this.ToggleButton_Detail.Text = "#";
            this.ToggleButton_Detail.UseVisualStyleBackColor = true;
            this.ToggleButton_Detail.CheckedChanged += new System.EventHandler(this.ToggleButton_Detail_CheckedChanged);
            // 
            // ToggleButton_Monthly
            // 
            this.ToggleButton_Monthly.Activated = false;
            this.ToggleButton_Monthly.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Monthly.Customizable = false;
            this.ToggleButton_Monthly.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Monthly.Location = new System.Drawing.Point(138, 15);
            this.ToggleButton_Monthly.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Monthly.Name = "ToggleButton_Monthly";
            this.ToggleButton_Monthly.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.OFF_Text = "يومي";
            this.ToggleButton_Monthly.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Monthly.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.ON_Text = "يومي";
            this.ToggleButton_Monthly.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Monthly.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.Size = new System.Drawing.Size(88, 25);
            this.ToggleButton_Monthly.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Monthly.TabIndex = 52;
            this.ToggleButton_Monthly.Tag = "تفصيلي";
            this.ToggleButton_Monthly.Text = "#";
            this.ToggleButton_Monthly.UseVisualStyleBackColor = true;
            this.ToggleButton_Monthly.CheckedChanged += new System.EventHandler(this.ToggleButton_Monthly_CheckedChanged);
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.jToggleButton_Year.Location = new System.Drawing.Point(40, 15);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = "شهري";
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = "شهري";
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(88, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 52;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            this.jToggleButton_Year.CheckedChanged += new System.EventHandler(this.jToggleButton_Year_CheckedChanged);
            // 
            // rjLabel11
            // 
            this.rjLabel11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(339, 17);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.Size = new System.Drawing.Size(32, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 53;
            this.rjLabel11.Text = "عرض";
            // 
            // dgv2
            // 
            this.dgv2.AllowUserToAddRows = false;
            this.dgv2.AllowUserToDeleteRows = false;
            this.dgv2.AllowUserToOrderColumns = true;
            this.dgv2.AllowUserToResizeRows = false;
            this.dgv2.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv2.AlternatingRowsColorApply = false;
            this.dgv2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv2.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.BorderRadius = 10;
            this.dgv2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv2.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv2.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv2.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv2.ColumnHeaderHeight = 45;
            this.dgv2.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv2.ColumnHeadersHeight = 45;
            this.dgv2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv2.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv2.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv2.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv2.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.EnableHeadersVisualStyles = false;
            this.dgv2.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv2.Location = new System.Drawing.Point(14, 340);
            this.dgv2.Margin = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.dgv2.MultiSelect = false;
            this.dgv2.Name = "dgv2";
            this.dgv2.ReadOnly = true;
            this.dgv2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv2.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv2.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv2.RowHeadersVisible = false;
            this.dgv2.RowHeadersWidth = 30;
            this.dgv2.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv2.RowHeight = 30;
            this.dgv2.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv2.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv2.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv2.RowTemplate.Height = 30;
            this.dgv2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv2.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv2.Size = new System.Drawing.Size(969, 194);
            this.dgv2.TabIndex = 80;
            this.dgv2.SelectionChanged += new System.EventHandler(this.dgv2_SelectionChanged);
            // 
            // Toggle_By_Group
            // 
            this.Toggle_By_Group.Activated = false;
            this.Toggle_By_Group.Customizable = false;
            this.Toggle_By_Group.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_By_Group.Location = new System.Drawing.Point(50, 12);
            this.Toggle_By_Group.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_By_Group.Name = "Toggle_By_Group";
            this.Toggle_By_Group.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_Group.OFF_Text = "تجميعي";
            this.Toggle_By_Group.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_By_Group.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_Group.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_Group.ON_Text = "تفصيلي";
            this.Toggle_By_Group.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_By_Group.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_Group.Size = new System.Drawing.Size(96, 25);
            this.Toggle_By_Group.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_By_Group.TabIndex = 87;
            this.Toggle_By_Group.Tag = "تفصيلي";
            this.Toggle_By_Group.Text = "#";
            this.Toggle_By_Group.UseVisualStyleBackColor = true;
            this.Toggle_By_Group.CheckedChanged += new System.EventHandler(this.Toggle_By_Group_CheckedChanged);
            // 
            // lbl_countSession
            // 
            this.lbl_countSession.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_countSession.AutoSize = true;
            this.lbl_countSession.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_countSession.Enabled = false;
            this.lbl_countSession.Font = new System.Drawing.Font("Cairo", 9F);
            this.lbl_countSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_countSession.LinkLabel = false;
            this.lbl_countSession.Location = new System.Drawing.Point(786, 11);
            this.lbl_countSession.Name = "lbl_countSession";
            this.lbl_countSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_countSession.Size = new System.Drawing.Size(135, 23);
            this.lbl_countSession.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_countSession.TabIndex = 90;
            this.lbl_countSession.Text = "عدد جلسات الكرت  المحدد";
            this.lbl_countSession.Visible = false;
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.lbl_download);
            this.rjPanel2.Controls.Add(this.lbl_count_Cards);
            this.rjPanel2.Controls.Add(this.txt_count_Cards);
            this.rjPanel2.Controls.Add(this.lbl_uptime);
            this.rjPanel2.Controls.Add(this.txt_uptime);
            this.rjPanel2.Controls.Add(this.txt_download);
            this.rjPanel2.Controls.Add(this.lbl_Total_Session);
            this.rjPanel2.Controls.Add(this.txt_Total_Session);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(12, 117);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(274, 168);
            this.rjPanel2.TabIndex = 91;
            // 
            // txt_countSession
            // 
            this.txt_countSession._Customizable = false;
            this.txt_countSession.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_countSession.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_countSession.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_countSession.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_countSession.BorderRadius = 3;
            this.txt_countSession.BorderSize = 1;
            this.txt_countSession.Enabled = false;
            this.txt_countSession.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txt_countSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_countSession.Location = new System.Drawing.Point(584, 7);
            this.txt_countSession.MultiLine = false;
            this.txt_countSession.Name = "txt_countSession";
            this.txt_countSession.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_countSession.PasswordChar = false;
            this.txt_countSession.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_countSession.PlaceHolderText = null;
            this.txt_countSession.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_countSession.Size = new System.Drawing.Size(182, 28);
            this.txt_countSession.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_countSession.TabIndex = 85;
            this.txt_countSession.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_countSession.Visible = false;
            // 
            // Spanel
            // 
            this.Spanel.AutoScroll = true;
            this.Spanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Spanel.BorderRadius = 0;
            this.Spanel.Controls.Add(this.panel1_side);
            this.Spanel.Controls.Add(this.rjLabel25Title);
            this.Spanel.Controls.Add(this.CBox_Port);
            this.Spanel.Controls.Add(this.rjLabel16);
            this.Spanel.Controls.Add(this.panel3_side);
            this.Spanel.Controls.Add(this.panel2_side);
            this.Spanel.Controls.Add(this.pnl_side_sn);
            this.Spanel.Controls.Add(this.CBox_Customer);
            this.Spanel.Controls.Add(this.rjLabel17);
            this.Spanel.Controls.Add(this.CBox_Radius);
            this.Spanel.Controls.Add(this.rjLabel14);
            this.Spanel.Controls.Add(this.CBox_SellingPoint);
            this.Spanel.Controls.Add(this.rjLabel15);
            this.Spanel.Controls.Add(this.CBox_Batch);
            this.Spanel.Controls.Add(this.rjButton4);
            this.Spanel.Controls.Add(this.btn_Fix);
            this.Spanel.Controls.Add(this.rjLabel4);
            this.Spanel.Controls.Add(this.CBox_Profile);
            this.Spanel.Controls.Add(this.rjLabel9);
            this.Spanel.Controls.Add(this.btn_apply);
            this.Spanel.Customizable = false;
            this.Spanel.Dock = System.Windows.Forms.DockStyle.Left;
            this.Spanel.Location = new System.Drawing.Point(0, 108);
            this.Spanel.Name = "Spanel";
            this.Spanel.Size = new System.Drawing.Size(10, 442);
            this.Spanel.TabIndex = 92;
            // 
            // panel1_side
            // 
            this.panel1_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel1_side.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1_side.Location = new System.Drawing.Point(0, 425);
            this.panel1_side.Name = "panel1_side";
            this.panel1_side.Size = new System.Drawing.Size(238, 10);
            this.panel1_side.TabIndex = 54;
            this.panel1_side.Visible = false;
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Cairo Medium", 12F);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(6695, 395);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(92, 30);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 31;
            this.rjLabel25Title.Text = "فلتره بحسب";
            this.rjLabel25Title.Visible = false;
            // 
            // CBox_Port
            // 
            this.CBox_Port.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Port.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems;
            this.CBox_Port.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Port.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.BorderRadius = 5;
            this.CBox_Port.BorderSize = 1;
            this.CBox_Port.Customizable = false;
            this.CBox_Port.DataSource = null;
            this.CBox_Port.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Port.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Port.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Port.Location = new System.Drawing.Point(20, 6);
            this.CBox_Port.Name = "CBox_Port";
            this.CBox_Port.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Port.SelectedIndex = -1;
            this.CBox_Port.Size = new System.Drawing.Size(155, 32);
            this.CBox_Port.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Port.TabIndex = 33;
            this.CBox_Port.Texts = "";
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(191, 11);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(38, 23);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 35;
            this.rjLabel16.Text = "الجهاز";
            this.rjLabel16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel3_side
            // 
            this.panel3_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.panel3_side.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3_side.Location = new System.Drawing.Point(238, 1);
            this.panel3_side.Name = "panel3_side";
            this.panel3_side.Size = new System.Drawing.Size(1, 434);
            this.panel3_side.TabIndex = 52;
            // 
            // panel2_side
            // 
            this.panel2_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel2_side.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2_side.Location = new System.Drawing.Point(0, 0);
            this.panel2_side.Name = "panel2_side";
            this.panel2_side.Size = new System.Drawing.Size(239, 1);
            this.panel2_side.TabIndex = 55;
            // 
            // pnl_side_sn
            // 
            this.pnl_side_sn.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_sn.BorderRadius = 10;
            this.pnl_side_sn.Controls.Add(this.CheckBox_SN);
            this.pnl_side_sn.Controls.Add(this.rjLabel10);
            this.pnl_side_sn.Controls.Add(this.CBox_SN_Compar);
            this.pnl_side_sn.Controls.Add(this.txt_SN_Start);
            this.pnl_side_sn.Controls.Add(this.rjLabel1);
            this.pnl_side_sn.Controls.Add(this.txt_SN_End);
            this.pnl_side_sn.Customizable = true;
            this.pnl_side_sn.Location = new System.Drawing.Point(9, 225);
            this.pnl_side_sn.Name = "pnl_side_sn";
            this.pnl_side_sn.Size = new System.Drawing.Size(229, 90);
            this.pnl_side_sn.TabIndex = 53;
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.Location = new System.Drawing.Point(187, 11);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 42;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(81, 15);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(10, 40);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(155, 32);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 31;
            this.CBox_SN_Compar.Texts = "";
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(96, 9);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 43;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(169, 43);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(57, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 35;
            this.rjLabel1.Text = "التسلسلي";
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(12, 9);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 43;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // CBox_Customer
            // 
            this.CBox_Customer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Customer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Customer.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.BorderRadius = 5;
            this.CBox_Customer.BorderSize = 1;
            this.CBox_Customer.Customizable = false;
            this.CBox_Customer.DataSource = null;
            this.CBox_Customer.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Customer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Customer.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Customer.Location = new System.Drawing.Point(21, 187);
            this.CBox_Customer.Name = "CBox_Customer";
            this.CBox_Customer.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Customer.SelectedIndex = -1;
            this.CBox_Customer.Size = new System.Drawing.Size(155, 32);
            this.CBox_Customer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Customer.TabIndex = 33;
            this.CBox_Customer.Texts = "";
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(186, 190);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(44, 23);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 35;
            this.rjLabel17.Text = "العميل";
            this.rjLabel17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Radius
            // 
            this.CBox_Radius.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Radius.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Radius.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Radius.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.BorderRadius = 5;
            this.CBox_Radius.BorderSize = 1;
            this.CBox_Radius.Customizable = false;
            this.CBox_Radius.DataSource = null;
            this.CBox_Radius.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Radius.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Radius.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Radius.Location = new System.Drawing.Point(21, 151);
            this.CBox_Radius.Name = "CBox_Radius";
            this.CBox_Radius.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Radius.SelectedIndex = -1;
            this.CBox_Radius.Size = new System.Drawing.Size(155, 32);
            this.CBox_Radius.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Radius.TabIndex = 33;
            this.CBox_Radius.Texts = "";
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(185, 155);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(47, 23);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 35;
            this.rjLabel14.Text = "راديوس";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(21, 114);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(155, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(180, 118);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(56, 23);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "001-12-09-2024",
            "002-15-09-2024",
            "003-05-09-2024"});
            this.CBox_Batch.Location = new System.Drawing.Point(21, 78);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(155, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 33;
            this.CBox_Batch.Texts = "";
            // 
            // rjButton4
            // 
            this.rjButton4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton4.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.BorderRadius = 4;
            this.rjButton4.BorderSize = 1;
            this.rjButton4.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton4.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton4.FlatAppearance.BorderSize = 0;
            this.rjButton4.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton4.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton4.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton4.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconChar = FontAwesome.Sharp.IconChar.CreativeCommonsSampling;
            this.rjButton4.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton4.IconSize = 20;
            this.rjButton4.Location = new System.Drawing.Point(97, 379);
            this.rjButton4.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton4.Name = "rjButton4";
            this.rjButton4.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton4.Size = new System.Drawing.Size(25, 31);
            this.rjButton4.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton4.TabIndex = 49;
            this.rjButton4.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton4.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton4.UseVisualStyleBackColor = false;
            this.rjButton4.Visible = false;
            // 
            // btn_Fix
            // 
            this.btn_Fix.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Fix.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.BorderRadius = 4;
            this.btn_Fix.BorderSize = 1;
            this.btn_Fix.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Fix.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Fix.FlatAppearance.BorderSize = 0;
            this.btn_Fix.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Fix.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Fix.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Fix.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Fix.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconChar = FontAwesome.Sharp.IconChar.Unlock;
            this.btn_Fix.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Fix.IconSize = 20;
            this.btn_Fix.Location = new System.Drawing.Point(123, 379);
            this.btn_Fix.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Fix.Name = "btn_Fix";
            this.btn_Fix.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Fix.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Fix.Size = new System.Drawing.Size(25, 31);
            this.btn_Fix.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Fix.TabIndex = 49;
            this.btn_Fix.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Fix.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Fix.UseVisualStyleBackColor = false;
            this.btn_Fix.Visible = false;
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(186, 85);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(44, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 35;
            this.rjLabel4.Text = "الدفعه";
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(21, 42);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(155, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(189, 47);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.Size = new System.Drawing.Size(38, 23);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 15;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Cairo Medium", 12F, System.Drawing.FontStyle.Bold);
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(63, 321);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.Size = new System.Drawing.Size(101, 40);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 51;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_apply.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 5;
            this.rjPanel3.Controls.Add(this.rjButton2);
            this.rjPanel3.Controls.Add(this.txt_countSession);
            this.rjPanel3.Controls.Add(this.lbl_countSession);
            this.rjPanel3.Controls.Add(this.Toggle_By_Group);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(12, 290);
            this.rjPanel3.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(968, 45);
            this.rjPanel3.TabIndex = 93;
            // 
            // rjButton2
            // 
            this.rjButton2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderRadius = 4;
            this.rjButton2.BorderSize = 1;
            this.rjButton2.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton2.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton2.FlatAppearance.BorderSize = 0;
            this.rjButton2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton2.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton2.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton2.IconSize = 25;
            this.rjButton2.Location = new System.Drawing.Point(8, 4);
            this.rjButton2.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton2.Name = "rjButton2";
            this.rjButton2.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton2.Size = new System.Drawing.Size(35, 37);
            this.rjButton2.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton2.TabIndex = 49;
            this.rjButton2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton2.UseVisualStyleBackColor = false;
            // 
            // rjDropdownMenu1
            // 
            this.rjDropdownMenu1.ActiveMenuItem = false;
            this.rjDropdownMenu1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.rjDropdownMenu1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.rjDropdownMenu1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.Restor_ColumnToolStripMenuItem,
            this.toolStripMenuItem15,
            this.toolStripSeparator3,
            this.Copy_ToolStripMenuItem,
            this.Copy_AllRowToolStripMenuItem,
            this.ExportExcelToolStripMenuItem,
            this.ExportText_ToolStripMenuItem,
            this.toolStripSeparator4,
            this.DeleteCards_ToolStripMenuItem,
            this.DeleteCardsArchive_ToolStripMenuItem,
            this.DeleteServerArchiveToolStripMenuItem,
            this.DeleteSession_ToolStripMenuItem,
            this.toolStripSeparator5,
            this.DisableCards_ToolStripMenuItem,
            this.EnableCards_ToolStripMenuItem,
            this.RestCards_ToolStripMenuItem,
            this.BindMAC_ToolStripMenuItem,
            this.RemoveBindMAC_ToolStripMenuItem,
            this.toolStripSeparator6,
            this.PrintCards_ToolStripMenuItem,
            this.AddProfile_ToolStripMenuItem,
            this.ChangeSP_ToolStripMenuItem,
            this.Remove_SP_ToolStripMenuItem});
            this.rjDropdownMenu1.Name = "dmExample";
            this.rjDropdownMenu1.OwnerIsMenuButton = false;
            this.rjDropdownMenu1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjDropdownMenu1.Size = new System.Drawing.Size(278, 468);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Checked = true;
            this.toolStripMenuItem1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.toolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.SaveDGVToolStripMenuItem,
            this.toolStripMenuItem3,
            this.toolStripMenuItem4,
            this.toolStripMenuItem5,
            this.Password_ToolStripMenuItem,
            this.toolStripMenuItem6,
            this.toolStripMenuItem7,
            this.toolStripMenuItem8,
            this.toolStripMenuItem9,
            this.toolStripMenuItem11,
            this.dt_LastSeenAt_ToolStripMenuItem,
            this.toolStripMenuItem12,
            this.Str_ProfileTillTime_ToolStripMenuItem,
            this.Str_ProfileTimeLeft_ToolStripMenuItem,
            this.Str_ProfileTransferLeft_ToolStripMenuItem,
            this.toolStripMenuItem13,
            this.toolStripMenuItem14,
            this.Count_profile_ToolStripMenuItem,
            this.LastSynDb_ToolStripMenuItem,
            this.PageNumber_ToolStripMenuItem});
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(277, 22);
            this.toolStripMenuItem1.Text = "عرض واخفاء الاعمدة";
            // 
            // SaveDGVToolStripMenuItem
            // 
            this.SaveDGVToolStripMenuItem.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.SaveDGVToolStripMenuItem.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.SaveDGVToolStripMenuItem.IconColor = System.Drawing.Color.Black;
            this.SaveDGVToolStripMenuItem.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.SaveDGVToolStripMenuItem.IconSize = 70;
            this.SaveDGVToolStripMenuItem.Name = "SaveDGVToolStripMenuItem";
            this.SaveDGVToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.SaveDGVToolStripMenuItem.Text = "حفظ ترتيب الجدول كافتراضي";
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Checked = true;
            this.toolStripMenuItem3.CheckState = System.Windows.Forms.CheckState.Checked;
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem3.Tag = "Str_Status";
            this.toolStripMenuItem3.Text = "الحــــالــــة";
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem4.Tag = "Sn";
            this.toolStripMenuItem4.Text = "الرقم التسلسلي";
            // 
            // toolStripMenuItem5
            // 
            this.toolStripMenuItem5.Checked = true;
            this.toolStripMenuItem5.CheckState = System.Windows.Forms.CheckState.Checked;
            this.toolStripMenuItem5.Name = "toolStripMenuItem5";
            this.toolStripMenuItem5.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem5.Tag = "UserName";
            this.toolStripMenuItem5.Text = "الاســـــــم";
            // 
            // Password_ToolStripMenuItem
            // 
            this.Password_ToolStripMenuItem.Checked = true;
            this.Password_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Password_ToolStripMenuItem.Name = "Password_ToolStripMenuItem";
            this.Password_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.Password_ToolStripMenuItem.Tag = "Password";
            this.Password_ToolStripMenuItem.Text = "كلمة المرور";
            // 
            // toolStripMenuItem6
            // 
            this.toolStripMenuItem6.Name = "toolStripMenuItem6";
            this.toolStripMenuItem6.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem6.Tag = "Str_TotalPrice";
            this.toolStripMenuItem6.Text = "الســـــــعر";
            // 
            // toolStripMenuItem7
            // 
            this.toolStripMenuItem7.Name = "toolStripMenuItem7";
            this.toolStripMenuItem7.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem7.Tag = "ProfileName";
            this.toolStripMenuItem7.Text = "البــــــــاقة";
            // 
            // toolStripMenuItem8
            // 
            this.toolStripMenuItem8.Name = "toolStripMenuItem8";
            this.toolStripMenuItem8.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem8.Tag = "SpName";
            this.toolStripMenuItem8.Text = "نقـــــطة البيع";
            // 
            // toolStripMenuItem9
            // 
            this.toolStripMenuItem9.Name = "toolStripMenuItem9";
            this.toolStripMenuItem9.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem9.Tag = "BatchCardId";
            this.toolStripMenuItem9.Text = "الـــــدفعــــــه";
            // 
            // toolStripMenuItem11
            // 
            this.toolStripMenuItem11.Name = "toolStripMenuItem11";
            this.toolStripMenuItem11.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem11.Tag = "Str_Up_Down";
            this.toolStripMenuItem11.Text = "تحميـــــل+رفــــع";
            // 
            // dt_LastSeenAt_ToolStripMenuItem
            // 
            this.dt_LastSeenAt_ToolStripMenuItem.Name = "dt_LastSeenAt_ToolStripMenuItem";
            this.dt_LastSeenAt_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.dt_LastSeenAt_ToolStripMenuItem.Tag = "LastSeenAt";
            this.dt_LastSeenAt_ToolStripMenuItem.Text = "اخــــــر ظهــــور";
            // 
            // toolStripMenuItem12
            // 
            this.toolStripMenuItem12.Name = "toolStripMenuItem12";
            this.toolStripMenuItem12.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem12.Tag = "FirsLogin";
            this.toolStripMenuItem12.Text = "اول دخـــــــــول";
            // 
            // Str_ProfileTillTime_ToolStripMenuItem
            // 
            this.Str_ProfileTillTime_ToolStripMenuItem.Name = "Str_ProfileTillTime_ToolStripMenuItem";
            this.Str_ProfileTillTime_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.Str_ProfileTillTime_ToolStripMenuItem.Tag = "Str_ProfileTillTime";
            this.Str_ProfileTillTime_ToolStripMenuItem.Text = "تــــاريخ الانتــــهاء";
            // 
            // Str_ProfileTimeLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Name = "Str_ProfileTimeLeft_ToolStripMenuItem";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Tag = "Str_ProfileTimeLeft";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Text = "الوقت المتبقي";
            // 
            // Str_ProfileTransferLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Name = "Str_ProfileTransferLeft_ToolStripMenuItem";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Tag = "Str_ProfileTransferLeft";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Text = "التحميل المتبقي";
            // 
            // toolStripMenuItem13
            // 
            this.toolStripMenuItem13.Name = "toolStripMenuItem13";
            this.toolStripMenuItem13.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem13.Tag = "Comment";
            this.toolStripMenuItem13.Text = "تعلـــــــيق";
            // 
            // toolStripMenuItem14
            // 
            this.toolStripMenuItem14.Name = "toolStripMenuItem14";
            this.toolStripMenuItem14.Size = new System.Drawing.Size(246, 26);
            this.toolStripMenuItem14.Tag = "CustomerName";
            this.toolStripMenuItem14.Text = "عميل يوزمنجر";
            // 
            // Count_profile_ToolStripMenuItem
            // 
            this.Count_profile_ToolStripMenuItem.Name = "Count_profile_ToolStripMenuItem";
            this.Count_profile_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.Count_profile_ToolStripMenuItem.Tag = "CountProfile";
            this.Count_profile_ToolStripMenuItem.Text = "عدد الباقات";
            // 
            // LastSynDb_ToolStripMenuItem
            // 
            this.LastSynDb_ToolStripMenuItem.Name = "LastSynDb_ToolStripMenuItem";
            this.LastSynDb_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.LastSynDb_ToolStripMenuItem.Tag = "LastSynDb";
            this.LastSynDb_ToolStripMenuItem.Text = "اخر تحديث او مزامنه للكرت";
            // 
            // PageNumber_ToolStripMenuItem
            // 
            this.PageNumber_ToolStripMenuItem.Name = "PageNumber_ToolStripMenuItem";
            this.PageNumber_ToolStripMenuItem.Size = new System.Drawing.Size(246, 26);
            this.PageNumber_ToolStripMenuItem.Tag = "PageNumber";
            this.PageNumber_ToolStripMenuItem.Text = "رقم الصفحة";
            // 
            // Restor_ColumnToolStripMenuItem
            // 
            this.Restor_ColumnToolStripMenuItem.Name = "Restor_ColumnToolStripMenuItem";
            this.Restor_ColumnToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Restor_ColumnToolStripMenuItem.Text = "استعادة الاعمده الي الافتراضي";
            // 
            // toolStripMenuItem15
            // 
            this.toolStripMenuItem15.Name = "toolStripMenuItem15";
            this.toolStripMenuItem15.Size = new System.Drawing.Size(277, 22);
            this.toolStripMenuItem15.Text = "عرض معلومات الكرت المحدد";
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(274, 6);
            // 
            // Copy_ToolStripMenuItem
            // 
            this.Copy_ToolStripMenuItem.Name = "Copy_ToolStripMenuItem";
            this.Copy_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Copy_ToolStripMenuItem.Text = "نسخ                 ctrl+c";
            // 
            // Copy_AllRowToolStripMenuItem
            // 
            this.Copy_AllRowToolStripMenuItem.Name = "Copy_AllRowToolStripMenuItem";
            this.Copy_AllRowToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Copy_AllRowToolStripMenuItem.Text = "نسخ السطر كامل ";
            // 
            // ExportExcelToolStripMenuItem
            // 
            this.ExportExcelToolStripMenuItem.Name = "ExportExcelToolStripMenuItem";
            this.ExportExcelToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ExportExcelToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            // 
            // ExportText_ToolStripMenuItem
            // 
            this.ExportText_ToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.تصديرالاسمفقطToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem});
            this.ExportText_ToolStripMenuItem.Name = "ExportText_ToolStripMenuItem";
            this.ExportText_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ExportText_ToolStripMenuItem.Text = "تصدير الى ملف نصي";
            // 
            // تصديرالاسمفقطToolStripMenuItem
            // 
            this.تصديرالاسمفقطToolStripMenuItem.Name = "تصديرالاسمفقطToolStripMenuItem";
            this.تصديرالاسمفقطToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمفقطToolStripMenuItem.Text = "تصدير الاسم فقط";
            // 
            // تصديرالاسمكلمةالمرورToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور";
            // 
            // تصديرالاسمكلمةالمرورالباقةToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة";
            // 
            // تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة + نقطة البيع";
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(274, 6);
            // 
            // DeleteCards_ToolStripMenuItem
            // 
            this.DeleteCards_ToolStripMenuItem.Name = "DeleteCards_ToolStripMenuItem";
            this.DeleteCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteCards_ToolStripMenuItem.Text = "حذف الكروت المحددة";
            // 
            // DeleteCardsArchive_ToolStripMenuItem
            // 
            this.DeleteCardsArchive_ToolStripMenuItem.Name = "DeleteCardsArchive_ToolStripMenuItem";
            this.DeleteCardsArchive_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteCardsArchive_ToolStripMenuItem.Text = "حذف الكروت المحددة من  الارشيف";
            // 
            // DeleteServerArchiveToolStripMenuItem
            // 
            this.DeleteServerArchiveToolStripMenuItem.Name = "DeleteServerArchiveToolStripMenuItem";
            this.DeleteServerArchiveToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteServerArchiveToolStripMenuItem.Text = "حذف الكروت المحددة من الراوتر والارشيف";
            // 
            // DeleteSession_ToolStripMenuItem
            // 
            this.DeleteSession_ToolStripMenuItem.Name = "DeleteSession_ToolStripMenuItem";
            this.DeleteSession_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DeleteSession_ToolStripMenuItem.Text = "حذف جلسات الكروت المحددة";
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(274, 6);
            // 
            // DisableCards_ToolStripMenuItem
            // 
            this.DisableCards_ToolStripMenuItem.Name = "DisableCards_ToolStripMenuItem";
            this.DisableCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.DisableCards_ToolStripMenuItem.Text = "تعطيل الكروت المحددة ";
            // 
            // EnableCards_ToolStripMenuItem
            // 
            this.EnableCards_ToolStripMenuItem.Name = "EnableCards_ToolStripMenuItem";
            this.EnableCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.EnableCards_ToolStripMenuItem.Text = "تفعيل الكروت المحددة";
            // 
            // RestCards_ToolStripMenuItem
            // 
            this.RestCards_ToolStripMenuItem.Name = "RestCards_ToolStripMenuItem";
            this.RestCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.RestCards_ToolStripMenuItem.Text = "تصفير عداد الكروت المحددة";
            // 
            // BindMAC_ToolStripMenuItem
            // 
            this.BindMAC_ToolStripMenuItem.Name = "BindMAC_ToolStripMenuItem";
            this.BindMAC_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.BindMAC_ToolStripMenuItem.Text = "ربط الكروت باول جهاز استخدام";
            // 
            // RemoveBindMAC_ToolStripMenuItem
            // 
            this.RemoveBindMAC_ToolStripMenuItem.Name = "RemoveBindMAC_ToolStripMenuItem";
            this.RemoveBindMAC_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.RemoveBindMAC_ToolStripMenuItem.Text = "الغاء ربط الكروت باول جهاز استخدام";
            // 
            // toolStripSeparator6
            // 
            this.toolStripSeparator6.Name = "toolStripSeparator6";
            this.toolStripSeparator6.Size = new System.Drawing.Size(274, 6);
            // 
            // PrintCards_ToolStripMenuItem
            // 
            this.PrintCards_ToolStripMenuItem.Name = "PrintCards_ToolStripMenuItem";
            this.PrintCards_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.PrintCards_ToolStripMenuItem.Text = "طباعة الكروت المحددة";
            // 
            // AddProfile_ToolStripMenuItem
            // 
            this.AddProfile_ToolStripMenuItem.Name = "AddProfile_ToolStripMenuItem";
            this.AddProfile_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.AddProfile_ToolStripMenuItem.Text = "اضافه باقة للكروت المحددة";
            // 
            // ChangeSP_ToolStripMenuItem
            // 
            this.ChangeSP_ToolStripMenuItem.Name = "ChangeSP_ToolStripMenuItem";
            this.ChangeSP_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.ChangeSP_ToolStripMenuItem.Text = "تغير نقطة البيع للكروت المحددة";
            // 
            // Remove_SP_ToolStripMenuItem
            // 
            this.Remove_SP_ToolStripMenuItem.Name = "Remove_SP_ToolStripMenuItem";
            this.Remove_SP_ToolStripMenuItem.Size = new System.Drawing.Size(277, 22);
            this.Remove_SP_ToolStripMenuItem.Text = "حذف نقطة البيع من الكروت المحدده";
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // Form_UM_Sales_Size_Times
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "Form_UM_Sales_Size_Times";
            this.ClientSize = new System.Drawing.Size(1000, 600);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_UM_Sales_Size_Times";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_UM_Sales_Size_Times";
            this.Load += new System.EventHandler(this.Form_UM_Sales_Size_Times_Load);
            this.SizeChanged += new System.EventHandler(this.Form_UM_Sales_Size_Times_SizeChanged);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.dmAll_Cards.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.rjPanel_topFilter.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel12.ResumeLayout(false);
            this.rjPanel12.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).EndInit();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.Spanel.ResumeLayout(false);
            this.Spanel.PerformLayout();
            this.pnl_side_sn.ResumeLayout(false);
            this.pnl_side_sn.PerformLayout();
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            this.rjDropdownMenu1.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Timer timer_SideBar;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem View_Hide_toolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Status_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SN_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem UserName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Price_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SellingPoint_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BachCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_Up_Down_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_FirstUse_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Descr_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CusName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DateToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem count_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem الجهازPortToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem نسخCtrlcToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخالسطركاملToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالىملفاكسلToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالىملفنصيToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private RJControls.RJLabel lbl_uptime;
        private RJControls.RJTextBox txt_uptime;
        private RJControls.RJTextBox txt_download;
        private RJControls.RJLabel lbl_download;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJPanel rjPanel_topFilter;
        private RJControls.RJPanel rjPanel12;
        private RJControls.RJToggleButton ToggleButton_Detail;
        private RJControls.RJToggleButton ToggleButton_Monthly;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJTextBox txt_count_Cards;
        private RJControls.RJLabel lbl_count_Cards;
        private RJControls.RJDataGridView dgv2;
        private RJControls.RJToggleButton Toggle_By_Group;
        private RJControls.RJLabel lbl_countSession;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJTextBox txt_countSession;
        private RJControls.RJPanel Spanel;
        private System.Windows.Forms.Panel panel1_side;
        private RJControls.RJLabel rjLabel25Title;
        private RJControls.RJComboBox CBox_Port;
        private RJControls.RJLabel rjLabel16;
        private System.Windows.Forms.Panel panel3_side;
        private System.Windows.Forms.Panel panel2_side;
        private RJControls.RJPanel pnl_side_sn;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJComboBox CBox_Customer;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJComboBox CBox_Radius;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJComboBox CBox_Batch;
        private RJControls.RJButton rjButton4;
        private RJControls.RJButton btn_Fix;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJButton btn_apply;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJButton rjButton2;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJDatePicker rjDateTime_To;
        private RJControls.RJDatePicker rjDateTime_From;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJButton btn_;
        private RJControls.RJButton rjButton3;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJButton btn_search;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJLabel lbl_Total_Session;
        private RJControls.RJTextBox txt_Total_Session;
        private RJControls.RJDropdownMenu rjDropdownMenu1;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private FontAwesome.Sharp.IconMenuItem SaveDGVToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem5;
        private System.Windows.Forms.ToolStripMenuItem Password_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem6;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem7;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem8;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem9;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem11;
        private System.Windows.Forms.ToolStripMenuItem dt_LastSeenAt_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem12;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTillTime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTimeLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTransferLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem13;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem14;
        private System.Windows.Forms.ToolStripMenuItem Count_profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem LastSynDb_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem PageNumber_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Restor_ColumnToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem15;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem Copy_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Copy_AllRowToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportExcelToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportText_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمفقطToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem DeleteCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteCardsArchive_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteServerArchiveToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem DeleteSession_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private System.Windows.Forms.ToolStripMenuItem DisableCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem EnableCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RestCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BindMAC_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RemoveBindMAC_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator6;
        private System.Windows.Forms.ToolStripMenuItem PrintCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem AddProfile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ChangeSP_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Remove_SP_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_TransferLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_DownloadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UploadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_RegDate_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CountSession_ToolStripMenuItem;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJCheckBox CheckBox_To_Date;
    }
}