﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.props" Condition="Exists('..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.props')" />
  <Import Project="..\packages\chromiumembeddedframework.runtime.win-x86.136.1.4\build\chromiumembeddedframework.runtime.win-x86.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x86.136.1.4\build\chromiumembeddedframework.runtime.win-x86.props')" />
  <Import Project="..\packages\chromiumembeddedframework.runtime.win-x64.136.1.4\build\chromiumembeddedframework.runtime.win-x64.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x64.136.1.4\build\chromiumembeddedframework.runtime.win-x64.props')" />
  <Import Project="..\packages\Obfuscar.2.2.46\build\obfuscar.props" Condition="Exists('..\packages\Obfuscar.2.2.46\build\obfuscar.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{6BF6386B-B046-428A-BB35-B78A0198BD3D}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartCreator</RootNamespace>
    <AssemblyName>SmartCreator</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
    <CefSharpAnyCpuSupport>true</CefSharpAnyCpuSupport>
    <LangVersion>latest</LangVersion>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ProductName>SmartCreator</ProductName>
    <PublisherName>SmartCreator</PublisherName>
    <SuiteName>SmartCreator</SuiteName>
    <WebPage>smrtye.com</WebPage>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>9.0.6.0</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>false</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <StartupObject>SmartCreator.Program</StartupObject>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>SmartCreator1.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x86'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x86\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x86'">
    <OutputPath>bin\x86\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x86</PlatformTarget>
    <LangVersion>latest</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>D2D634DC3EBE6579E79DB33DBF0091A7D0673350</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>SmartCreator_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <ApplicationManifest>app.manifest</ApplicationManifest>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="AltoHttp, Version=1.0.7945.3063, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\AltoHttp.1.5.2\lib\net45\AltoHttp.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>..\packages\BouncyCastle.Cryptography.2.5.0\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp, Version=136.1.40.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.Common.136.1.40\lib\net462\CefSharp.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.Core, Version=136.1.40.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.Common.136.1.40\lib\net462\CefSharp.Core.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.WinForms, Version=136.1.40.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.WinForms.136.1.40\lib\net462\CefSharp.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="CircularProgressBar, Version=2.8.0.16, Culture=neutral, PublicKeyToken=310fd07b25df79b3, processorArchitecture=MSIL">
      <HintPath>..\packages\CircularProgressBar.2.8.0.16\lib\net40\CircularProgressBar.dll</HintPath>
    </Reference>
    <Reference Include="Dapper, Version=2.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Dapper.2.1.35\lib\net461\Dapper.dll</HintPath>
    </Reference>
    <Reference Include="DevComponents.DotNetBar2, Version=12.9.0.0, Culture=neutral, PublicKeyToken=c39c3242a43eee2b, processorArchitecture=MSIL" />
    <Reference Include="FontAwesome.Sharp, Version=6.6.0.0, Culture=neutral, PublicKeyToken=d16d1e4e568ec10f, processorArchitecture=MSIL">
      <HintPath>..\packages\FontAwesome.Sharp.6.6.0\lib\net48\FontAwesome.Sharp.dll</HintPath>
    </Reference>
    <Reference Include="HtmlAgilityPack, Version=1.11.72.0, Culture=neutral, PublicKeyToken=bd319b19eaf3b43a, processorArchitecture=MSIL">
      <HintPath>..\packages\HtmlAgilityPack.1.11.72\lib\Net45\HtmlAgilityPack.dll</HintPath>
    </Reference>
    <Reference Include="itextsharp, Version=5.5.13.4, Culture=neutral, PublicKeyToken=8354ae6d2174ddca, processorArchitecture=MSIL">
      <HintPath>..\packages\iTextSharp.5.5.13.4\lib\net461\itextsharp.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.6\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="Renci.SshNet, Version=2024.2.0.1, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>..\packages\SSH.NET.2024.2.0\lib\net462\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.SQLite, Version=1.0.66.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=x86">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Design" />
    <Reference Include="System.Formats.Asn1, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Formats.Asn1.9.0.1\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.Compression.FileSystem" />
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.6\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.6\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.6\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.0\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Windows.Forms.DataVisualization" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="UIAutomationClient" />
    <Reference Include="WindowsBase" />
    <Reference Include="WinFormAnimation, Version=1.6.0.4, Culture=neutral, PublicKeyToken=310fd07b25df79b3, processorArchitecture=MSIL">
      <HintPath>..\packages\WinFormAnimation.1.6.0.4\lib\net40\WinFormAnimation.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="DAL\BaseRepository.cs" />
    <Compile Include="DAL\DatabaseConnection.cs" />
    <Compile Include="DAL\DatabaseInitializer.cs" />
    <Compile Include="Data\Archive_DataAccess.cs" />
    <Compile Include="Data\Class_Reports.cs" />
    <Compile Include="Data\CLS_BulkInsert_db.cs" />
    <Compile Include="Data\CreateNewSqliteDatabase.cs" />
    <Compile Include="Data\DatabaseHelper.cs" />
    <Compile Include="Data\GenericEntity.cs" />
    <Compile Include="Data\Helper.cs" />
    <Compile Include="Data\MK.cs" />
    <Compile Include="Data\Mk_DataAccess.cs" />
    <Compile Include="Data\Mk_DataAccess_old.cs" />
    <Compile Include="Data\SmartDBAttribute.cs" />
    <Compile Include="Data\SmartDbContext.cs" />
    <Compile Include="Data\Smart_DataAccess.cs" />
    <Compile Include="Data\SqlDataAccess.cs" />
    <Compile Include="Data\Sql_DataAccess.cs" />
    <Compile Include="Data\UtilsSql.cs" />
    <Compile Include="db\Connections.cs" />
    <Compile Include="db\FormConnection.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="db\FormConnection.Designer.cs">
      <DependentUpon>FormConnection.cs</DependentUpon>
    </Compile>
    <Compile Include="db\Form_RestoreBackups_FirstUse.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="db\Form_RestoreBackups_FirstUse.Designer.cs">
      <DependentUpon>Form_RestoreBackups_FirstUse.cs</DependentUpon>
    </Compile>
    <Compile Include="Entities\Accounting\Account.cs" />
    <Compile Include="Entities\Accounts\Account.cs" />
    <Compile Include="Entities\Accounts\AccountMove.cs" />
    <Compile Include="Entities\Accounts\Class_Core.cs" />
    <Compile Include="Entities\Accounts\Partner.cs" />
    <Compile Include="Entities\Accounts\Product.cs" />
    <Compile Include="Entities\Security\AuditLog.cs" />
    <Compile Include="Entities\BaseEntity.cs" />
    <Compile Include="Entities\CardsArtchive\CardsArtchive.cs" />
    <Compile Include="Entities\BaseCard.cs" />
    <Compile Include="Entities\BaseProfile.cs" />
    <Compile Include="Entities\BasePyment.cs" />
    <Compile Include="Entities\BaseSession.cs" />
    <Compile Include="Entities\BatchCard.cs" />
    <Compile Include="Entities\EnumType\EnumType.cs" />
    <Compile Include="Entities\Hotspot\HSProfile.cs" />
    <Compile Include="Entities\Hotspot\HsPyment.cs" />
    <Compile Include="Entities\Hotspot\HsSession.cs" />
    <Compile Include="Entities\Hotspot\HSUser.cs" />
    <Compile Include="Entities\SellingPoint.cs" />
    <Compile Include="Entities\UserManager\UmProfile.cs" />
    <Compile Include="Entities\UserManager\UmPyment.cs" />
    <Compile Include="Entities\UserManager\UmSession.cs" />
    <Compile Include="Entities\UserManager\UmUser.cs" />
    <Compile Include="db\FormConnections.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="db\FormConnections.Designer.cs">
      <DependentUpon>FormConnections.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="FormCustomLogin.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormCustomLogin.Designer.cs">
      <DependentUpon>FormCustomLogin.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Form_AddChildAccount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Form_AddChildAccount.Designer.cs">
      <DependentUpon>Form_AddChildAccount.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Frm_Account_Manual.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Frm_Account_Manual.Designer.cs">
      <DependentUpon>Frm_Account_Manual.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Frm_AddEditAccount.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Accounting\Accounts\Frm_AddEditAccount.Designer.cs">
      <DependentUpon>Frm_AddEditAccount.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Add_BatchCards.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Add_BatchCards.Designer.cs">
      <DependentUpon>Form_Add_BatchCards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt.Designer.cs">
      <DependentUpon>Form_Out_IN_Receipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt_Add_Edit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt_Add_Edit.Designer.cs">
      <DependentUpon>Form_Out_IN_Receipt_Add_Edit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Account.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Account.Designer.cs">
      <DependentUpon>Form_Account.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Account_Add_Edit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Account_Add_Edit.Designer.cs">
      <DependentUpon>Form_Account_Add_Edit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_ExpenseIncome_Add_Edit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_ExpenseIncome_Add_Edit.Designer.cs">
      <DependentUpon>Form_ExpenseIncome_Add_Edit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Expense_Income.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Account\Form_Expense_Income.Designer.cs">
      <DependentUpon>Form_Expense_Income.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_CashPayment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_CashPayment.Designer.cs">
      <DependentUpon>Form_CashPayment.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_CashPayment_Add_Edit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_CashPayment_Add_Edit.Designer.cs">
      <DependentUpon>Form_CashPayment_Add_Edit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_Party_Add_Edit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_Party_Add_Edit.Designer.cs">
      <DependentUpon>Form_Party_Add_Edit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Form_Company.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Form_Company.Designer.cs">
      <DependentUpon>Form_Company.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Invoices\Form_Invoice.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Invoices\Form_Invoice.Designer.cs">
      <DependentUpon>Form_Invoice.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_Party.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Partners\Form_Party.Designer.cs">
      <DependentUpon>Form_Party.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_Product.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_Product.Designer.cs">
      <DependentUpon>Form_Product.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_ProductMaintenance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_ProductMaintenance.Designer.cs">
      <DependentUpon>Form_ProductMaintenance.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Form_Receipt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Form_Receipt.Designer.cs">
      <DependentUpon>Form_Receipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_UoM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\_Accounting\Product\Form_UoM.Designer.cs">
      <DependentUpon>Form_UoM.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_AciveUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_AciveUser.Designer.cs">
      <DependentUpon>Form_AciveUser.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_Acive_Host_Bar.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_Acive_Host_Bar.Designer.cs">
      <DependentUpon>Form_Acive_Host_Bar.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_HostUser.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_HostUser.Designer.cs">
      <DependentUpon>Form_HostUser.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_IpBinding.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_IpBinding.Designer.cs">
      <DependentUpon>Form_IpBinding.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_WalledGarden.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Acive_Host\Form_WalledGarden.Designer.cs">
      <DependentUpon>Form_WalledGarden.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Add_Cards_ToArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Add_Cards_ToArchive.Designer.cs">
      <DependentUpon>Form_Add_Cards_ToArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Print_FromArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Print_FromArchive.Designer.cs">
      <DependentUpon>Form_Print_FromArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Brodband\FormAddBrodbandCards.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Brodband\FormAddBrodbandCards.Designer.cs">
      <DependentUpon>FormAddBrodbandCards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\CardsDesigen\Form_PDF_Prview.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\CardsDesigen\Form_PDF_Prview.Designer.cs">
      <DependentUpon>Form_PDF_Prview.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Devices\Form_Device_Management.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Devices\Form_Device_Management.Designer.cs">
      <DependentUpon>Form_Device_Management.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Devices\Form_OpentWrt_Manage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Devices\Form_OpentWrt_Manage.Designer.cs">
      <DependentUpon>Form_OpentWrt_Manage.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Devices\Form_Ubiquiti_Manage.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Devices\Form_Ubiquiti_Manage.Designer.cs">
      <DependentUpon>Form_Ubiquiti_Manage.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_AciveInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_AciveInfo.Designer.cs">
      <DependentUpon>Form_AciveInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_CardsDesigen_Custom_items.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_CardsDesigen_Custom_items.Designer.cs">
      <DependentUpon>Form_CardsDesigen_Custom_items.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_CardsDesigen_Graghics.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_CardsDesigen_Graghics.Designer.cs">
      <DependentUpon>Form_CardsDesigen_Graghics.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_PrviewPdf.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_PrviewPdf.Designer.cs">
      <DependentUpon>Form_PrviewPdf.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_TemplateTable1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_TemplateTable1.Designer.cs">
      <DependentUpon>Form_TemplateTable1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_UsersInfo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_UsersInfo.Designer.cs">
      <DependentUpon>Form_UsersInfo.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Form_WaitForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Form_WaitForm.Designer.cs">
      <DependentUpon>Form_WaitForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_Input_Dailog_New_Template.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_Input_Dailog_New_Template.Designer.cs">
      <DependentUpon>frm_Input_Dailog_New_Template.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\frm_Input_Dailog_New_User.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\frm_Input_Dailog_New_User.Designer.cs">
      <DependentUpon>frm_Input_Dailog_New_User.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAddHotspotCards.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAddHotspotCards.Designer.cs">
      <DependentUpon>FormAddHotspotCards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAdd_Edit_Profile_Hotspot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAdd_Edit_Profile_Hotspot.Designer.cs">
      <DependentUpon>FormAdd_Edit_Profile_Hotspot.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAllCardsHotspot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormAllCardsHotspot.Designer.cs">
      <DependentUpon>FormAllCardsHotspot.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormProfileHotspotLocal.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormProfileHotspotLocal.Designer.cs">
      <DependentUpon>FormProfileHotspotLocal.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormReportHotspot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormReportHotspot.Designer.cs">
      <DependentUpon>FormReportHotspot.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\FormReportHotspot1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\FormReportHotspot1.Designer.cs">
      <DependentUpon>FormReportHotspot1.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Add_Profile_Balance_HS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Add_Profile_Balance_HS.Designer.cs">
      <DependentUpon>Form_Add_Profile_Balance_HS.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_CardsDetailsHS.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_CardsDetailsHS.Designer.cs">
      <DependentUpon>Form_CardsDetailsHS.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Cards_Hotspot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Cards_Hotspot.Designer.cs">
      <DependentUpon>Form_Cards_Hotspot.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\form_Edit_SmartScript_WhenAdd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\form_Edit_SmartScript_WhenAdd.Designer.cs">
      <DependentUpon>form_Edit_SmartScript_WhenAdd.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Smart_Validatiy_Hotspot.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\Hotspot\Form_Smart_Validatiy_Hotspot.Designer.cs">
      <DependentUpon>Form_Smart_Validatiy_Hotspot.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\MessageForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\MessageForm.Designer.cs">
      <DependentUpon>MessageForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_ads_Pages.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_ads_Pages.Designer.cs">
      <DependentUpon>Form_ads_Pages.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Check_Library_Html.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Check_Library_Html.Designer.cs">
      <DependentUpon>Form_Check_Library_Html.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Html_Mangament.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Html_Mangament.Designer.cs">
      <DependentUpon>Form_Html_Mangament.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Page_Editor.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\PageHtml\Form_Page_Editor.Designer.cs">
      <DependentUpon>Form_Page_Editor.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Add_Edit_SellingPoint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Add_Edit_SellingPoint.Designer.cs">
      <DependentUpon>Form_Add_Edit_SellingPoint.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Sales_SellingPoint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Sales_SellingPoint.Designer.cs">
      <DependentUpon>Form_Sales_SellingPoint.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SelleingPoint_Commissions.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SelleingPoint_Commissions.Designer.cs">
      <DependentUpon>Form_SelleingPoint_Commissions.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SellingPoint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SellingPoint.Designer.cs">
      <DependentUpon>Form_SellingPoint.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SellingPoint_Alert.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_SellingPoint_Alert.Designer.cs">
      <DependentUpon>Form_SellingPoint_Alert.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SettingsForms\Form_Backup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForms\Form_Backup.Designer.cs">
      <DependentUpon>Form_Backup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SettingsForms\Form_RestorLastBackup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SettingsForms\Form_RestorLastBackup.Designer.cs">
      <DependentUpon>Form_RestorLastBackup.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\FormAddUsersManager2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\FormAddUsersManager2.Designer.cs">
      <DependentUpon>FormAddUsersManager2.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Backup_Files_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Backup_Files_UserManager.Designer.cs">
      <DependentUpon>Form_Backup_Files_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Maintenances_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Maintenances_UserManager.Designer.cs">
      <DependentUpon>Form_Maintenances_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Process_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Process_UserManager.Designer.cs">
      <DependentUpon>Form_Process_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Scheduler_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Maintenance\Form_Scheduler_UserManager.Designer.cs">
      <DependentUpon>Form_Scheduler_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_Custome_Device_Print.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_Custome_Device_Print.Designer.cs">
      <DependentUpon>Form_Custome_Device_Print.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Report_ByBatch.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Report_ByBatch.Designer.cs">
      <DependentUpon>Form_UM_Report_ByBatch.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Report_ByPrint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Report_ByPrint.Designer.cs">
      <DependentUpon>Form_UM_Report_ByPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales.Designer.cs">
      <DependentUpon>Form_UM_Sales.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales_Device.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales_Device.Designer.cs">
      <DependentUpon>Form_UM_Sales_Device.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales_Size_Times.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Sales_Size_Times.Designer.cs">
      <DependentUpon>Form_UM_Sales_Size_Times.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormAdd_Edit_Profile_Multi_Limit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormAdd_Edit_Profile_Multi_Limit.Designer.cs">
      <DependentUpon>FormAdd_Edit_Profile_Multi_Limit.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\Form_Add_Profile_Balance.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\Form_Add_Profile_Balance.Designer.cs">
      <DependentUpon>Form_Add_Profile_Balance.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Form_AllSession_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Form_AllSession_UserManager.Designer.cs">
      <DependentUpon>Form_AllSession_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Form_CardsDetails.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Form_CardsDetails.Designer.cs">
      <DependentUpon>Form_CardsDetails.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Change_Cards_SellingPoint.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\SellingPoints\Form_Change_Cards_SellingPoint.Designer.cs">
      <DependentUpon>Form_Change_Cards_SellingPoint.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\Form_Edit_Limit_Profile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\Form_Edit_Limit_Profile.Designer.cs">
      <DependentUpon>Form_Edit_Limit_Profile.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Form_PrintForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Form_PrintForm.Designer.cs">
      <DependentUpon>Form_PrintForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Users_Devices.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\Form_UM_Users_Devices.Designer.cs">
      <DependentUpon>Form_UM_Users_Devices.cs</DependentUpon>
    </Compile>
    <Compile Include="Form_Custom_Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form_Custom_Login.Designer.cs">
      <DependentUpon>Form_Custom_Login.cs</DependentUpon>
    </Compile>
    <Compile Include="Helpers\DatabaseHelper.cs" />
    <Compile Include="Models\Acive_Host_Users.cs" />
    <Compile Include="Models\API\ApiResponse.cs" />
    <Compile Include="Models\AppSetting.cs" />
    <Compile Include="Models\CLS_Enpher.cs" />
    <Compile Include="Models\DashboardSettings.cs" />
    <Compile Include="Models\FingerPrint.cs" />
    <Compile Include="Models\LoginSettings.cs" />
    <Compile Include="RJControls\AddTabMethodsDemo.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\CollectionEditorTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\ConstructorTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\DesignerComparisonTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\DesignerSafeTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\DesignerTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\Design\RJTabControlDesigner.cs" />
    <Compile Include="RJControls\Design\RJTabPageCollection.cs" />
    <Compile Include="RJControls\Design\TabStyle.cs" />
    <Compile Include="RJControls\FinalRJTabControlTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\FinalTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\FinalValidationTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\FixedRJTabControlTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\MainTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\MainTestRunner.cs" />
    <Compile Include="RJControls\MinimalDesignerTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\ProgressiveDesignerTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\QuickErrorTest.cs" />
    <Compile Include="RJControls\QuickTestRunner.cs" />
    <Compile Include="RJControls\RJTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJTabControlTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\RJTabPage.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\SafeRJTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\SafeTabControlTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\SafeTestRunner.cs" />
    <Compile Include="RJControls\SimpleRJTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\SimpleRJTabPageCollection.cs" />
    <Compile Include="RJControls\SimpleTabCollectionEditor.cs" />
    <Compile Include="RJControls\SimpleTabTestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\SimplifiedRJTabControlTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\SuperSafeRJTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\SuperSafeTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\TestFormSimple.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\TestProgram.cs" />
    <Compile Include="RJControls\TestRJTabControl.cs" />
    <Compile Include="RJControls\TestUpdatesForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJControls\UltraSafeRJTabControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\UltraSafeTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Service\AccountService.cs" />
    <Compile Include="Service\AppSettingsService.cs" />
    <Compile Include="Service\AuditService.cs" />
    <Compile Include="Service\UserService.cs" />
    <Compile Include="SSHClient\ClientManager.cs" />
    <Compile Include="SSHClient\DataModel.cs" />
    <Compile Include="SSHClient\ftp.cs" />
    <Compile Include="SSHClient\IClientManager.cs" />
    <Compile Include="Forms\UC_PreviewTemplateCards.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\UC_PreviewTemplateCards.Designer.cs">
      <DependentUpon>UC_PreviewTemplateCards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UC_PreviewTemplateCardsTable.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\UC_PreviewTemplateCardsTable.Designer.cs">
      <DependentUpon>UC_PreviewTemplateCardsTable.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UC_StatusBar_Info.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Forms\UC_StatusBar_Info.Designer.cs">
      <DependentUpon>UC_StatusBar_Info.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\FormAddUsersManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\FormAddUsersManager.Designer.cs">
      <DependentUpon>FormAddUsersManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormAdd_Edit_Profile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormAdd_Edit_Profile.Designer.cs">
      <DependentUpon>FormAdd_Edit_Profile.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllBatchsCards.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllBatchsCards.Designer.cs">
      <DependentUpon>FormAllBatchsCards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllBatchsCards_Archive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllBatchsCards_Archive.Designer.cs">
      <DependentUpon>FormAllBatchsCards_Archive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\FormAllCardsUserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\FormAllCardsUserManager.Designer.cs">
      <DependentUpon>FormAllCardsUserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllCards_ِArchive.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\FormAllCards_ِArchive.Designer.cs">
      <DependentUpon>FormAllCards_ِArchive.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormProfileUserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Profile\FormProfileUserManager.Designer.cs">
      <DependentUpon>FormProfileUserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\FormReportUserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Reports\FormReportUserManager.Designer.cs">
      <DependentUpon>FormReportUserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Archive_Cards.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_Archive_Cards.Designer.cs">
      <DependentUpon>Form_Archive_Cards.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\UserManager\Form_Cards_UserManager.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\UserManager\Form_Cards_UserManager.Designer.cs">
      <DependentUpon>Form_Cards_UserManager.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_import_batch_cards_UM.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\BatchCards\Form_import_batch_cards_UM.Designer.cs">
      <DependentUpon>Form_import_batch_cards_UM.cs</DependentUpon>
    </Compile>
    <Compile Include="LoginForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LoginForm.Designer.cs">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </Compile>
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Models\CardsTemplate.cs" />
    <Compile Include="Models\CLS_Generate_Random_Cards.cs" />
    <Compile Include="Models\CLS_Print.cs" />
    <Compile Include="Models\Dashboard.cs" />
    <Compile Include="Models\Hotspot\ProfileHotspot.cs" />
    <Compile Include="Models\Hotspot\SourceCardsHotspot.cs" />
    <Compile Include="Models\SellingPints.cs" />
    <Compile Include="Models\SourceCardsUserManager.cs" />
    <Compile Include="Models\Customer.cs" />
    <Compile Include="Models\Global_Variable.cs" />
    <Compile Include="Models\Mk_Resources.cs" />
    <Compile Include="Models\Mk_Routers.cs" />
    <Compile Include="Models\Product.cs" />
    <Compile Include="Models\ProfileUserManager.cs" />
    <Compile Include="Models\SalesAnalysis.cs" />
    <Compile Include="Models\SalesOrder.cs" />
    <Compile Include="Models\SaveStateFormsVariable.cs" />
    <Compile Include="Models\SourcePymentUserManager.cs" />
    <Compile Include="Models\SourceSessionUserManager.cs" />
    <Compile Include="Entities\Security\User.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="RJControls\Design\ButtonDesign.cs" />
    <Compile Include="RJControls\Design\ControlStyle.cs" />
    <Compile Include="RJControls\Design\DropdownMenuColors.cs" />
    <Compile Include="RJControls\Design\DropdownMenuPosition.cs" />
    <Compile Include="RJControls\Design\DropdownMenuRenderer.cs" />
    <Compile Include="RJControls\Design\LabelStyle.cs" />
    <Compile Include="RJControls\Design\TextBoxStyle.cs" />
    <Compile Include="RJControls\Design\TextPosition.cs" />
    <Compile Include="RJControls\RJMenuIcon.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJChart.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJCheckBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJCircularPictureBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJComboBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RJControls\RJDataGridView.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJDatePicker.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RJControls\RJDragControl.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJDropdownMenu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJImageColorOverlay.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJLabel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJMenuButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJPictureBox.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJProgressBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJTextBox.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="RJControls\RJToggleButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJControls\RJTrackBar.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJForms\Private\RJMessageForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\Private\RJMessageForm.designer.cs">
      <DependentUpon>RJMessageForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RJForms\RJBaseForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\RJChildForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\RJMainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\RJMessageBox.cs" />
    <Compile Include="RJForms\RJPrintForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\RJPrintForm.designer.cs">
      <DependentUpon>RJPrintForm.cs</DependentUpon>
    </Compile>
    <Compile Include="RJControls\RJRadioButton.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="RJForms\RJSettingsForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="RJForms\RJSettingsForm.Designer.cs">
      <DependentUpon>RJSettingsForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Settings\SettingsManager.cs" />
    <Compile Include="Settings\UIAppearanceSettings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>UIAppearanceSettings.settings</DependentUpon>
    </Compile>
    <Compile Include="Settings\UIAppearance.cs" />
    <Compile Include="Settings\RJColors.cs" />
    <Compile Include="Settings\UIStyle.cs" />
    <Compile Include="Settings\UITheme.cs" />
    <Compile Include="Forms\FormDashboard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormDashboard.Designer.cs">
      <DependentUpon>FormDashboard.cs</DependentUpon>
    </Compile>
    <Compile Include="Forms\FormUserProfile.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Forms\FormUserProfile.Designer.cs">
      <DependentUpon>FormUserProfile.cs</DependentUpon>
    </Compile>
    <Compile Include="Utils\Class_install_fonts.cs" />
    <Compile Include="Utils\ColorEditor.cs" />
    <Compile Include="Utils\CustomFonts.cs" />
    <Compile Include="Utils\RoundedControl.cs" />
    <Compile Include="Utils\TableClass.cs" />
    <Compile Include="Utils\TCResize.cs" />
    <Compile Include="Utils\utils.cs" />
    <Compile Include="ViewModels\API_Server.cs" />
    <Compile Include="ViewModels\CountryCodes.cs" />
    <Compile Include="ViewModels\EasyAES.cs" />
    <Compile Include="ViewModels\CLS_FirstLoad.cs" />
    <Compile Include="ViewModels\Fast_Load_From_Mikrotik.cs" />
    <Compile Include="ViewModels\UserHotspotProcess.cs" />
    <Compile Include="ViewModels\UserManagerProcess.cs" />
    <Content Include="icon1.ico" />
    <Content Include="SmartCreator1.ico" />
    <Content Include="Smart_Icon_New2.ico" />
    <Content Include="Smart_Icon_New3.ico" />
    <None Include="Resources\Smart_Icon_New.ico" />
    <Content Include="Smart_Icon_New.ico" />
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Accounting\Accounts\Form_AddChildAccount.resx">
      <DependentUpon>Form_AddChildAccount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Accounting\Accounts\Frm_Account_Manual.resx">
      <DependentUpon>Frm_Account_Manual.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Accounting\Accounts\Frm_AddEditAccount.resx">
      <DependentUpon>Frm_AddEditAccount.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\Form_Add_BatchCards.resx">
      <DependentUpon>Form_Add_BatchCards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt.resx">
      <DependentUpon>Form_Out_IN_Receipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\AccountMove\Form_Out_IN_Receipt_Add_Edit.resx">
      <DependentUpon>Form_Out_IN_Receipt_Add_Edit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Account\Form_Account.resx">
      <DependentUpon>Form_Account.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Account\Form_Account_Add_Edit.resx">
      <DependentUpon>Form_Account_Add_Edit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Account\Form_ExpenseIncome_Add_Edit.resx">
      <DependentUpon>Form_ExpenseIncome_Add_Edit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Account\Form_Expense_Income.resx">
      <DependentUpon>Form_Expense_Income.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Partners\Form_CashPayment.resx">
      <DependentUpon>Form_CashPayment.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Partners\Form_CashPayment_Add_Edit.resx">
      <DependentUpon>Form_CashPayment_Add_Edit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Partners\Form_Party_Add_Edit.resx">
      <DependentUpon>Form_Party_Add_Edit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Form_Company.resx">
      <DependentUpon>Form_Company.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Invoices\Form_Invoice.resx">
      <DependentUpon>Form_Invoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Partners\Form_Party.resx">
      <DependentUpon>Form_Party.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Product\Form_Product.resx">
      <DependentUpon>Form_Product.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Product\Form_ProductMaintenance.resx">
      <DependentUpon>Form_ProductMaintenance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Form_Receipt.resx">
      <DependentUpon>Form_Receipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\_Accounting\Product\Form_UoM.resx">
      <DependentUpon>Form_UoM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Maintenance\Form_Backup_Files_UserManager.resx">
      <DependentUpon>Form_Backup_Files_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Maintenance\Form_Maintenances_UserManager.resx">
      <DependentUpon>Form_Maintenances_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Maintenance\Form_Process_UserManager.resx">
      <DependentUpon>Form_Process_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Maintenance\Form_Scheduler_UserManager.resx">
      <DependentUpon>Form_Scheduler_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="RJControls\SimpleTabTestForm.resx">
      <DependentUpon>SimpleTabTestForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJControls\TestFormSimple.resx">
      <DependentUpon>TestFormSimple.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="تغير الاقام من الهندي الي العربي.PNG">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <Content Include="db\localDB.db" />
    <None Include="Resources\warning.gif" />
    <None Include="Resources\data_managementSmall.png" />
    <None Include="Resources\TestSend.png" />
    <None Include="Resources\exit.png" />
    <Content Include="obfuscar.xml" />
    <None Include="Resources\Icon1.ico" />
    <None Include="Resources\Smart_Creator.ico" />
    <Content Include="Smart_Creator.ico" />
    <None Include="Resources\Smart Creator-21.png" />
    <None Include="App.config" />
    <None Include="app.manifest" />
    <None Include="Properties\app.manifest" />
    <None Include="Resources\Spin%401x-1.0s-200px-200px.gif" />
    <None Include="connections.json" />
    <None Include="Resources\DroidKufi-Bold.ttf" />
    <None Include="Resources\DroidKufi_Regular.ttf" />
    <None Include="Resources\DroidNaskh-Bold.ttf" />
    <None Include="Resources\DroidNaskh-Regular.ttf" />
    <None Include="Resources\DroidSansArabic.ttf" />
    <None Include="Resources\Untitled-1.png" />
    <None Include="Resources\header.png" />
    <None Include="Resources\Smart Creator-2.png" />
    <None Include="Resources\hedar.jpg" />
    <EmbeddedResource Include="db\FormConnection.resx">
      <DependentUpon>FormConnection.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="db\FormConnections.resx">
      <DependentUpon>FormConnections.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="db\Form_RestoreBackups_FirstUse.resx">
      <DependentUpon>Form_RestoreBackups_FirstUse.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormCustomLogin.resx">
      <DependentUpon>FormCustomLogin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Acive_Host\Form_AciveUser.resx">
      <DependentUpon>Form_AciveUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Acive_Host\Form_Acive_Host_Bar.resx">
      <DependentUpon>Form_Acive_Host_Bar.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Acive_Host\Form_HostUser.resx">
      <DependentUpon>Form_HostUser.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Acive_Host\Form_IpBinding.resx">
      <DependentUpon>Form_IpBinding.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Acive_Host\Form_WalledGarden.resx">
      <DependentUpon>Form_WalledGarden.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\Form_Add_Cards_ToArchive.resx">
      <DependentUpon>Form_Add_Cards_ToArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\Form_Print_FromArchive.resx">
      <DependentUpon>Form_Print_FromArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Brodband\FormAddBrodbandCards.resx">
      <DependentUpon>FormAddBrodbandCards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\CardsDesigen\Form_PDF_Prview.resx">
      <DependentUpon>Form_PDF_Prview.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Devices\Form_Device_Management.resx">
      <DependentUpon>Form_Device_Management.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Devices\Form_OpentWrt_Manage.resx">
      <DependentUpon>Form_OpentWrt_Manage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Devices\Form_Ubiquiti_Manage.resx">
      <DependentUpon>Form_Ubiquiti_Manage.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_AciveInfo.resx">
      <DependentUpon>Form_AciveInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_CardsDesigen_Custom_items.resx">
      <DependentUpon>Form_CardsDesigen_Custom_items.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_CardsDesigen_Graghics.resx">
      <DependentUpon>Form_CardsDesigen_Graghics.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_PrviewPdf.resx">
      <DependentUpon>Form_PrviewPdf.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_TemplateTable1.resx">
      <DependentUpon>Form_TemplateTable1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_UsersInfo.resx">
      <DependentUpon>Form_UsersInfo.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Form_WaitForm.resx">
      <DependentUpon>Form_WaitForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_Input_Dailog_New_Template.resx">
      <DependentUpon>frm_Input_Dailog_New_Template.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\frm_Input_Dailog_New_User.resx">
      <DependentUpon>frm_Input_Dailog_New_User.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormAddHotspotCards.resx">
      <DependentUpon>FormAddHotspotCards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormAdd_Edit_Profile_Hotspot.resx">
      <DependentUpon>FormAdd_Edit_Profile_Hotspot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormAllCardsHotspot.resx">
      <DependentUpon>FormAllCardsHotspot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormProfileHotspotLocal.resx">
      <DependentUpon>FormProfileHotspotLocal.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormReportHotspot.resx">
      <DependentUpon>FormReportHotspot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\FormReportHotspot1.resx">
      <DependentUpon>FormReportHotspot1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\Form_Add_Profile_Balance_HS.resx">
      <DependentUpon>Form_Add_Profile_Balance_HS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\Form_CardsDetailsHS.resx">
      <DependentUpon>Form_CardsDetailsHS.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\Form_Cards_Hotspot.resx">
      <DependentUpon>Form_Cards_Hotspot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\form_Edit_SmartScript_WhenAdd.resx">
      <DependentUpon>form_Edit_SmartScript_WhenAdd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\Hotspot\Form_Smart_Validatiy_Hotspot.resx">
      <DependentUpon>Form_Smart_Validatiy_Hotspot.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\MessageForm.resx">
      <DependentUpon>MessageForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PageHtml\Form_ads_Pages.resx">
      <DependentUpon>Form_ads_Pages.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PageHtml\Form_Check_Library_Html.resx">
      <DependentUpon>Form_Check_Library_Html.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PageHtml\Form_Html_Mangament.resx">
      <DependentUpon>Form_Html_Mangament.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\PageHtml\Form_Page_Editor.resx">
      <DependentUpon>Form_Page_Editor.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_Add_Edit_SellingPoint.resx">
      <DependentUpon>Form_Add_Edit_SellingPoint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_Sales_SellingPoint.resx">
      <DependentUpon>Form_Sales_SellingPoint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_SelleingPoint_Commissions.resx">
      <DependentUpon>Form_SelleingPoint_Commissions.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_SellingPoint.resx">
      <DependentUpon>Form_SellingPoint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_SellingPoint_Alert.resx">
      <DependentUpon>Form_SellingPoint_Alert.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SettingsForms\Form_Backup.resx">
      <DependentUpon>Form_Backup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SettingsForms\Form_RestorLastBackup.resx">
      <DependentUpon>Form_RestorLastBackup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UC_PreviewTemplateCards.resx">
      <DependentUpon>UC_PreviewTemplateCards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UC_PreviewTemplateCardsTable.resx">
      <DependentUpon>UC_PreviewTemplateCardsTable.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UC_StatusBar_Info.resx">
      <DependentUpon>UC_StatusBar_Info.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\FormAddUsersManager.resx">
      <DependentUpon>FormAddUsersManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\FormAddUsersManager2.resx">
      <DependentUpon>FormAddUsersManager2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_Custome_Device_Print.resx">
      <DependentUpon>Form_Custome_Device_Print.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Report_ByBatch.resx">
      <DependentUpon>Form_UM_Report_ByBatch.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Report_ByPrint.resx">
      <DependentUpon>Form_UM_Report_ByPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Sales.resx">
      <DependentUpon>Form_UM_Sales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Sales_Device.resx">
      <DependentUpon>Form_UM_Sales_Device.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Sales_Size_Times.resx">
      <DependentUpon>Form_UM_Sales_Size_Times.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Profile\FormAdd_Edit_Profile.resx">
      <DependentUpon>FormAdd_Edit_Profile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Profile\FormAdd_Edit_Profile_Multi_Limit.resx">
      <DependentUpon>FormAdd_Edit_Profile_Multi_Limit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\FormAllBatchsCards.resx">
      <DependentUpon>FormAllBatchsCards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\FormAllBatchsCards_Archive.resx">
      <DependentUpon>FormAllBatchsCards_Archive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\FormAllCardsUserManager.resx">
      <DependentUpon>FormAllCardsUserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\FormAllCards_ِArchive.resx">
      <DependentUpon>FormAllCards_ِArchive.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Profile\FormProfileUserManager.resx">
      <DependentUpon>FormProfileUserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\FormReportUserManager.resx">
      <DependentUpon>FormReportUserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Profile\Form_Add_Profile_Balance.resx">
      <DependentUpon>Form_Add_Profile_Balance.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Form_AllSession_UserManager.resx">
      <DependentUpon>Form_AllSession_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\Form_Archive_Cards.resx">
      <DependentUpon>Form_Archive_Cards.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Form_CardsDetails.resx">
      <DependentUpon>Form_CardsDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Form_Cards_UserManager.resx">
      <DependentUpon>Form_Cards_UserManager.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\SellingPoints\Form_Change_Cards_SellingPoint.resx">
      <DependentUpon>Form_Change_Cards_SellingPoint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Profile\Form_Edit_Limit_Profile.resx">
      <DependentUpon>Form_Edit_Limit_Profile.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\BatchCards\Form_import_batch_cards_UM.resx">
      <DependentUpon>Form_import_batch_cards_UM.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Form_PrintForm.resx">
      <DependentUpon>Form_PrintForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\UserManager\Reports\Form_UM_Users_Devices.resx">
      <DependentUpon>Form_UM_Users_Devices.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form_Custom_Login.resx">
      <DependentUpon>Form_Custom_Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LoginForm.resx">
      <DependentUpon>LoginForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\Private\RJMessageForm.resx">
      <DependentUpon>RJMessageForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\RJBaseForm.resx">
      <DependentUpon>RJBaseForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\RJChildForm.resx">
      <DependentUpon>RJChildForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\RJMainForm.resx">
      <DependentUpon>RJMainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\RJPrintForm.resx">
      <DependentUpon>RJPrintForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="RJForms\RJSettingsForm.resx">
      <DependentUpon>RJSettingsForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormDashboard.resx">
      <DependentUpon>FormDashboard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Forms\FormUserProfile.resx">
      <DependentUpon>FormUserProfile.cs</DependentUpon>
    </EmbeddedResource>
    <Content Include="Files\Documentation.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Files\License.pdf">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="packages.config">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Settings\UIAppearanceSettings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>UIAppearanceSettings.Designer.cs</LastGenOutput>
    </None>
    <EmbeddedResource Include="SmartSetting.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </EmbeddedResource>
    <None Include="SmartCreator_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RestoreWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RJTitleBarLogo.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\BackImage.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CloseDark.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\CloseWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MaximizeDark.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MaximizeWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MinimizeDark.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\MinimizeWhite.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RestoreDark.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\logoultimate.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\AralyProfile.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\userProfile.png" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RummerProfile.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RJTitleBarLogoColor.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Models\UserManager\" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\tik4net\tik4net\tik4net.csproj">
      <Project>{A1E3DA4A-FB06-48E7-8994-ED819A33536A}</Project>
      <Name>tik4net</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Obfuscar.2.2.46\build\obfuscar.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Obfuscar.2.2.46\build\obfuscar.props'))" />
    <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x64.136.1.4\build\chromiumembeddedframework.runtime.win-x64.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x64.136.1.4\build\chromiumembeddedframework.runtime.win-x64.props'))" />
    <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x86.136.1.4\build\chromiumembeddedframework.runtime.win-x86.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x86.136.1.4\build\chromiumembeddedframework.runtime.win-x86.props'))" />
    <Error Condition="!Exists('..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.props'))" />
    <Error Condition="!Exists('..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.targets'))" />
  </Target>
  <PropertyGroup>
    <PostBuildEvent>if $(ConfigurationName) == Release  "$(Obfuscar)" ..\..\obfuscar.xml</PostBuildEvent>
  </PropertyGroup>
  <Import Project="..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.targets" Condition="Exists('..\packages\CefSharp.Common.136.1.40\build\CefSharp.Common.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>