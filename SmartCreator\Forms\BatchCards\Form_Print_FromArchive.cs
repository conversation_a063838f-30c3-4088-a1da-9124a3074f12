﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;

namespace SmartCreator.Forms.BatchCards
{
    public partial class Form_Print_FromArchive : RJChildForm
    {
        BatchArtchive batchArtchive = null;
        Archive_DataAccess ArchiveDB = null;


        private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA = null;
        private FormAddUsersManager frm;
        private Form_PrintUserManagerState Frm_State;
        private UserManagerProcess ump;
        private Clss_InfoPrint clss_InfoPrint;
        bool firstLoad = true;

        BatchArtchive batch = null;

        public Form_Print_FromArchive()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv_freq.RightToLeft = RightToLeft.No;
            }

            ArchiveDB = new Archive_DataAccess();
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            txt_Page_From.Text = "0";
            txt_Page_To.Text = "0";
            txt_Sn_From.Text = "0";
            txt_Sn_From.Text = "0";
            set_font();

            dgv.Width = pnlClientArea.Width - 10;
            dgv_freq.Visible = false;
            dgv.Refresh();
        }
        public Form_Print_FromArchive(BatchArtchive _batch)
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv_freq.RightToLeft = RightToLeft.No;
            }
            batch = _batch;

            ArchiveDB = new Archive_DataAccess();
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            txt_Page_From.Text = "0";
            txt_Page_To.Text = "0";
            txt_Sn_From.Text = "0";
            txt_Sn_From.Text = "0";

            if (batch != null)
            {
                batchArtchive = batch;
                CBox_Batch.Text = batchArtchive.Str_Name.ToString();
                try
                {
                    Dictionary<string, string> comboSource = new Dictionary<string, string>();
                    comboSource.Add(batch.Str_Name, batch.BatchNumber.ToString());
                    CBox_Batch.DataSource = new BindingSource(comboSource, null);
                    CBox_Batch.DisplayMember = "Key";
                    CBox_Batch.ValueMember = "Value";
                    //CBox_Batch.Text = "";
                    CBox_Batch.SelectedIndex = -1;

                    //txt_Page_From.Text = batchArtchive._from.ToString();
                    //txt_Page_To.Text = batchArtchive.Sn_to.ToString();
                    txt_Sn_From.Text = batchArtchive.Sn_from.ToString();
                    txt_Sn_To.Text = batchArtchive.Sn_to.ToString();


                }
                catch { }

                //CBox_Batch.Enabled = false;
            }
            //lbl_Count.Text = "العدد : " + dgv.Rows.Count;
            //loadData();

            set_font();

            dgv.Width = pnlClientArea.Width - 10;
            dgv_freq.Visible = false;
            dgv.Refresh();
        }

        private void set_font()
        {
            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 35;

            dgv.DefaultCellStyle.Font = new System.Drawing.Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size    , dgv.DefaultCellStyle.Font.Style);

            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            //CardsArtchive cardsArtchive = new CardsArtchive();
            //dgv.DataSource = cardsArtchive;

            rjLabel1.Font = Radio_SN.Font = lbl_Attribute.Font = lbl_Batch.Font = lbl_Count.Font = lbl_Customer.Font =
                lbl_FirstUse.Font = Radio_Page.Font = checkBox_RegisterAs_LastBatch.Font = lbl_From_To.Font = lbl_group.Font =
                lbl_note.Font = checkBox_RegisterAsBatch.Font = lbl_Save_PDF.Font = lbl_script_File.Font = lbl_SellingPoint.Font =

               Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            btnAdd.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10 , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);

        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_SellingPoint()
        {
            //try
            //{

            Smart_DataAccess da = new Smart_DataAccess();
            CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

            return;
            CBox_SellingPoint = da.Get_ComboBox_SellingPoint();
            CBox_SellingPoint.Refresh();


            //List<SellingPoint> sp = da.Get_SellingPoints();
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    comboSource.Add(0, "");
            //    foreach (SellingPoint s in sp)
            //        comboSource.Add((int)s.Id, s.UserName);

            //    CBox_SellingPoint.DataSource = new BindingSource(comboSource, null);
            //    CBox_SellingPoint.DisplayMember = "Value";
            //    CBox_SellingPoint.ValueMember = "Key";
            //    CBox_SellingPoint.SelectedIndex = 0;
            //    CBox_SellingPoint.Text = "";

            //}
            //catch { }
        }
        private void Get_Batch()
        {
            if (batchArtchive != null)
                return;
            CBox_Batch.DataSource = ArchiveDB.Get_BindingSource_Archive_Batch();
            CBox_Batch.ValueMember = "Value";
            CBox_Batch.DisplayMember = "Key";
            //CBox_Batch.SelectedIndex = 0;
            //CBox_Batch.Text = "";
        }
        private void Get_UMCustomer()
        {
            try
            {
                CBox_CustomerUserMan.DataSource = Global_Variable.UM_Customer;
                CBox_CustomerUserMan.DisplayMember = "Name";
                CBox_CustomerUserMan.ValueMember = "Name";

            }
            catch { }
        }

        private DataTable DataTableArchive()
        {
            DataTable table = new DataTable();
            table.Columns.Add("التسلسل", typeof(string));
            table.Columns.Add("اسم المستخدم", typeof(string));
            table.Columns.Add("كلمة المرور", typeof(string));
            table.Columns.Add("الباقة", typeof(string));
            table.Columns.Add("الدفعة", typeof(string));
            table.Columns.Add("رقم الصفحة", typeof(string));


            //DataTable dt_freq = new DataTable();
            //dt_freq.Columns.Add("الاسم", typeof(string));
            //dgv_freq.DataSource = dgv_freq;
            return table;

        }

        private void loadData()
        {
            string Rb = Global_Variable.Mk_resources.RB_SN;
            string Rb_code = Global_Variable.Mk_resources.RB_code;
            string filter = " ";
            if (txt_search.Text.Trim() != "")
            {
                //WHERE SALARY LIKE '%200%'
                filter += "and UserName LIKE '%" + txt_search.Text + "%' "; 
            } 
            if (CBox_Batch.Text != "")
            { 
                if (Radio_Page.Checked)
                {
                    if (utils.check_Filed_Intiger_with_Msg(txt_Page_From.Text, "ادخل  رقم الصفحة بشكل صحيح") && utils.check_Filed_Intiger_with_Msg(txt_Page_To.Text, "ادخل  رقم الصفحة بشكل صحيح"))
                        filter += " and  (PageNumber BETWEEN " + txt_Page_From.Text + " and  " + txt_Page_To.Text + ") ";
                }
                if (Radio_SN.Checked)
                {
                    if (utils.check_Filed_Intiger_with_Msg(txt_Sn_From.Text) && utils.check_Filed_Intiger(txt_Sn_To.Text))
                        filter += " and  (SN BETWEEN " + txt_Sn_From.Text + " and  " + txt_Sn_To.Text + ") ";
                }

                //string sql = "select * from CardsArtchive where BatchCardId=" + CBox_Batch.SelectedValue + " and Status=0 and Rb='" + Rb + "' " + filter + " ;";
                string sql = @$" select Sn as 'التسلسل' 
                                ,UserName as 'اسم المستخدم'
                                ,Password as 'كلمة المرور'
                                ,ProfileName as 'الباقة'
                                ,BatchCardId as 'الدفعة'
                                ,PageNumber as 'رقم الصفحة'
                                from CardsArtchive where BatchCardId={CBox_Batch.SelectedValue} and Status=0 and (Rb='{Rb}' or Rb='{Rb_code}') {filter} ;";

                //if (batchArtchive != null)
                DataTable table = DataTableArchive();

                dgv.DataSource = ArchiveDB.RunSqlCommandAsDatatable(sql);
                //dgv.DataSource = ArchiveDB.GetListAnyDB<CardsArtchive>(sql);
                //dgv.DataSource = ArchiveDB.GetListAnyDB<CardsArtchive>("select * from CardsArtchive where BatchCardId=" + batchArtchive.BatchNumber + " and Status=0 and Rb='" + Rb + "' ;");
           
            
            }
            lbl_Count.Text = "العدد : " + dgv.Rows.Count;
            update_header_DGV();

        }
        private void update_header_DGV()
        {
            try
            {
                if (dgv.DataSource == null)
                    return;
                //foreach (DataGridViewColumn column in dgv.Columns)
                //    column.Visible = false;
                //dgv.Columns["ProfileName"].Visible = false;

                //dgv.Columns["Id"].Visible = false;
                //try { dgv.Columns["RegDate"].Visible = false; } catch { }
                //dgv.Columns["Rb"].Visible = false;
                //dgv.Columns["Status"].Visible = false;
                //dgv.Columns["Str_Status"].Visible = false;

                //dgv.Columns["Str_Status"].DisplayIndex = 0;
                //dgv.Columns["Str_Status"].Width = 150;
                //dgv.Columns["SN"].DisplayIndex = 0;
                //dgv.Columns["BatchCardId"].DisplayIndex = 1;
                //dgv.Columns["RegDate"].Width = 150;
                //dgv.Columns["PageNumber"].Width = 120;

            }
            catch { }
        }
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            loadData();


            lbl_Count.Text = "العدد : " + dgv.Rows.Count;
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            loadFromState();
            set_font();
            Get_Batch();
            Get_UMCustomer();
            Get_SellingPoint();
            if (UIAppearance.Theme == UITheme.Light)
                lbl_Count.ForeColor = Color.Red;

            if (batchArtchive != null)
            {
                CBox_Batch.SelectedIndex = 0;
                CBox_Batch.Text = batchArtchive.Str_Name;
            }
            loadData();


            try
            {

                txt_Sn_From.Text = dgv.Rows[0].Cells["Sn"].Value.ToString();
                txt_Sn_To.Text = dgv.Rows[dgv.Rows.Count - 1].Cells["Sn"].Value.ToString();

                txt_Page_From.Text = dgv.Rows[0].Cells["PageNumber"].Value.ToString();
                txt_Page_To.Text = dgv.Rows[dgv.Rows.Count - 1].Cells["PageNumber"].Value.ToString();

            }
            catch { }

            firstLoad = false;
        }
        private void Form_Print_FromArchive_Load(object sender, EventArgs e)
        {

            timer1.Start();

            //set_font();
            //Get_Batch();
            //Get_UMCustomer();
            //Get_SellingPoint();
            //if (UIAppearance.Theme == UITheme.Light)
            //    lbl_Count.ForeColor = Color.Red;

            //if (batchArtchive != null)
            //{
            //    CBox_Batch.SelectedIndex = 0;
            //    CBox_Batch.Text=batchArtchive.Str_Name;
            //}
            //loadData();

        }

        private void loadFromState()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Form_Print_FromArchive");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());

            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            //Frm_State.is_add_batch_cards = false;
            //Frm_State.is_add_batch_cards_to_Archive = false;
            //Frm_State.is_Add_One_Card = false;


            checkBoxSaveTo_PDF.Check = false;
            //checkBoxSaveTo_PDF.Check = Frm_State.checkBoxSaveTo_PDF;
            checkBoxSaveTo_excel.Check = Frm_State.checkBoxSaveTo_excel;
            checkBoxSaveTo_script_File.Check = Frm_State.checkBoxSaveTo_script_File;
            checkBoxSaveTo_text_File.Check = Frm_State.checkBoxSaveTo_text_File;
            checkBox_Remove_FromArchive.Check = Frm_State.checkBox_Remove_FromArchive;
            Radio_SN.Checked = Frm_State.Archive_Radio_SN;
            Radio_Page.Checked = Frm_State.Archive_Radio_Page;
        }
        private void SaveFromState()
        {
            

            //Frm_State.checkBoxSaveTo_PDF = checkBoxSaveTo_PDF.Check;
            Frm_State.checkBoxSaveTo_excel = checkBoxSaveTo_excel.Check;
            Frm_State.checkBoxSaveTo_script_File = checkBoxSaveTo_script_File.Check;
            Frm_State.checkBoxSaveTo_text_File = checkBoxSaveTo_text_File.Check;
            Frm_State.checkBox_Remove_FromArchive = checkBox_Remove_FromArchive.Check;
            Frm_State.Archive_Radio_SN = Radio_SN.Checked;
            Frm_State.Archive_Radio_Page = Radio_Page.Checked;


            string formSetting = JsonConvert.SerializeObject(Frm_State);
            Smart_DataAccess.Setting_SaveState_Forms_Variables("Form_Print_FromArchive", "SaveFromState", formSetting);

        }

   

        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }

        private void dgv_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {
            update_select_DGV();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            loadData();
        }



        private void txt_Sn_To_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            loadData();
        }

        private void txt_Page_To_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            loadData();
        }

        private void dgv_freq_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
            {
                try
                {
                    int indexCell = Convert.ToInt32(dgv_freq.Rows[e.RowIndex].Cells["index"].Value);
                    dgv.ClearSelection();
                    dgv.Rows[indexCell].Selected = true;
                }
                catch { }
                }
        }
        DataGridViewCell ActiveCell = null;
        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void Copy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            loadData();
        }

        private void Radio_SN_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            loadData();
        }

        private void Radio_Page_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            loadData();
        }

        [Obsolete]
        private void btnAdd_Click(object sender, EventArgs e)
        {
            txt_search.Text = string.Empty;
            dgv.Width = pnlClientArea.Width - 10;
            dgv_freq.Visible = false;


            if (dgv.Rows.Count <= 0)
            {
                RJMessageBox.Show("لا يوجد كروت  للاضافة");
                return;
            }
           
            init_Virable();

            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }

            DialogResult result = RJMessageBox.Show("هل متاكد من اضافة  الكروت الي الروتر", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            btnAdd.Enabled = false;
            Thread thread = new Thread(AddUserUserd_batch_cards);
            Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();


            //using (Form_WaitForm frm = new Form_WaitForm(AddUserUserd_batch_cards))
            //{
            //    frm.ShowDialog();
               
            //}
            //SaveFromState();
            //loadData();
            ////AddUserUserd_batch_cards();
        }
        private bool init_Virable()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormUserManagerPrint");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());
            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            clss_InfoPrint = get_data_from_interface2();

            ump = new UserManagerProcess();
            ump.clss_InfoPrint = clss_InfoPrint;
            ump.Frm_State = Frm_State;
            frm = new FormAddUsersManager();
            ump.frm = frm;
            ump.Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + clss_InfoPrint.Number_Cards_ToAdd + "Cards)" + "_(" + clss_InfoPrint.Profile_Name + ")";
            //ump.Public_file_Name = "";

            if (clss_InfoPrint.Save_To_PDF)
            {
                ump.frm.txtNumberCard.Text = clss_InfoPrint.Number_Cards_ToAdd.ToString();
                ump.frm.CBox_Profile.Text = clss_InfoPrint.profile.Name;
                //if (ump.init_file_pdf() == false)
                //    return false;

            }



            return true;
        }

        private New_Generate_Cards new_Generate_Cards;
        [Obsolete]
        public void AddUserUserd_batch_cards()
        {
            try
            {
                //if (init_Virable() == false)
                //    return;
                clss_InfoPrint = get_data_from_interface2();
                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

                string mesgBtn = "يتم الان اضافة الكروت الى اليوزرمنجر";
                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + clss_InfoPrint.Number_Cards_ToAdd + "  /  " + (20) + " )", mesgBtn);

                // ====== فحص وتجميع الكروت من الجدول الكروت العشوائية ============================
                //CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);

                new_Generate_Cards = Generate_Cards(clss_InfoPrint.Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    //startPrint = false;
                    Global_Variable.StartThreadProcessFromMK = false;
                    btnAdd.Enabled = true;
                    return;
                }
                ump.new_Generate_Cards = new_Generate_Cards;

                //========= تجهيز سكربت الاضافة الي المايكروتك =================
                Dictionary<string, string> variableScript = ump.Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
                //=========== فحص اذا طريقة الاضافة سكربت واحد لليوزر والبروفايل او فصل سكربت اضافة البروفايل وحده =========
                //bool _check_if_use_2Scritp_add = ump.check_if_use_2Scritp_add();

                //======= الاضافة الي المايكروتك ========================

                Dictionary<string, string> res = null;
                //if (_check_if_use_2Scritp_add == true)
                //    return;
                //FormAddUsersManager fr = new FormAddUsersManager();
                //ump.Frm_State = Frm_State;

                ump.frm.check_port_mk_befor();
                res = ump.GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
                ump.frm.rest_port_mk_after();

                if (res["status"] == "false")
                {
                    //startPrint = false;
                    Global_Variable.StartThreadProcessFromMK = false;
                    btnAdd.Enabled = true;
                    return;
                }

                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                //string path = @"tempCards\script\batch\";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch\\";
                string path_SmartErorrCards =  path + "SmartErorrCards.rsc";
                string path_SmartErorrProfile =  path + "SmartErorrProfile.rsc";
                var Users_lines = File.ReadAllLines(path_SmartErorrCards);
                List<string> user_erorr = new List<string>();
                for (var i = 0; i < Users_lines.Length; i += 1)
                {
                    var line = Users_lines[i];
                    user_erorr.Add(line.Trim());
                }
                var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
                List<string> profile_erorr = new List<string>();

                for (var i = 0; i < Profiles_lines.Length; i += 1)
                {
                    var line = Profiles_lines[i];
                    profile_erorr.Add(line.Trim());
                }

                string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
                string[] profile_split = profile_erorr.ToArray();

                //if (user_split.Length > 0)
                //{
                //    for (int i = 0; i < user_split.Length; i++)
                //        new_Generate_Cards.dicUser.Remove(user_split[i]);
                //    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                //    CountTry = 5;
                //    new_Generate_Cards.dicUser = ump.GenerateIfLastErorr_batch_cards(new_Generate_Cards.dicUser, user_split.Length, cLS_Genrate_Cards);
                //}
                //====== check if error add profile   or  _check_if_use_2Scritp_add ================== 
                if (profile_split.Length > 1)
                {
                    ump.AddProfile_ErorreCards(profile_split.ToList());
                }


                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");

                List<UmUser> dbUser = ump.add_sn_to_local_dbUser(new_Generate_Cards, true);
                ump.add_to_db(dbUser);


                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    ump.print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {


                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }

                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { ump.Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    ump.Add_to_NumberPrint_cards_toDB(dbUser);
                }

                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { ump.add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.SaveTo_excel) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف اكسل"); ump.CreateExcel(dbUser); }
                if (clss_InfoPrint.SaveTo_script_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف سكربت"); ump.Create_Script_File(dbUser); }
                if (clss_InfoPrint.SaveTo_text_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف نصي"); ump.Create_Text_File(dbUser); }

                try { File.Delete(path_SmartErorrCards); } catch { }
                try { File.Delete(path_SmartErorrProfile); } catch { }


                //if (clss_InfoPrint.checkBox_Remove_FromArchive)
                //{
                Change_Status_CardsfromDB_ToPrint_In_Server(dbUser);

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                           (MethodInvoker)delegate ()
                           {
                               //dgv.Rows.Clear();
                           });
                //}

                //==== refresh datagridview batch Number
                //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                //(System.Windows.Forms.MethodInvoker)delegate ()
                //{
                //    frm.LoadDatagridviewData();
                //});

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + 0 + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الطباعة بنجاح");

                Global_Variable.StartThreadProcessFromMK = false;
                btnAdd.Enabled = true;

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\n" /*+ ex.ToString()*/);
                //is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                Global_Variable.StartThreadProcessFromMK = false;
                btnAdd.Enabled = true;
                //startPrint = false;
            }
            //startPrint = false;
            Global_Variable.StartThreadProcessFromMK = false;
            btnAdd.Enabled = true;
            loadData();
        }
        string Public_Profile_Name = "";
        string Public_Custumer_UserMan = "";
        string Public_SellingPoint_Value = "";


        public New_Generate_Cards Generate_Cards(int Number_Cards_ToAdd, string mesgBtn = "يتم الاضافة", bool useProgressBar = true)
        {
            HashSet<string> card_copy = new HashSet<string>();

            if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
            {
                //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                //{
                //    card_copy = db.ColumnDistinct<string>(db.From<UmUser>().Where(x => x.DeleteFromServer == 0).Select(x => x.UserName));
                //    //card_copy = Local_DA.Load<string>("select Distinct UserName from UmUser where DeleteFromServer=0 ");
                //}
                card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));
            }
            else
            {
                if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                    card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
            }
            if (clss_InfoPrint.With_Archive_uniqe)
            {
                //=========  get cards from archive  and copy  to hashset card_copy
                //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
            }

            HashSet<string> card_freq = new HashSet<string>();
            New_Generate_Cards new_Generate_Cards;
            string strUser = ""; string strPass = "";
            Dictionary<string, NewUserToAdd> dicUser = new Dictionary<string, NewUserToAdd>();


            new_Generate_Cards = new New_Generate_Cards();
            try
            {
                DataTable dt_card_freq = new DataTable();
                dt_card_freq.Columns.Add("الاسم", typeof(string));
                dt_card_freq.Columns.Add("index", typeof(int));

                for (int i = 0; i < clss_InfoPrint.Number_Cards_ToAdd; i++)
                {
                    string username = dgv.Rows[i].Cells["اسم المستخدم"].Value.ToString().Trim();
                    string Password = dgv.Rows[i].Cells["كلمة المرور"].Value.ToString().Trim();
                    string profile = dgv.Rows[i].Cells["الباقة"].Value.ToString().Trim();
                    string? sp = clss_InfoPrint.SellingPoint_Value;
                    int? PageNumber = string.IsNullOrEmpty((string?)dgv.Rows[i].Cells["رقم الصفحة"].Value.ToString()) ? (int?)null : Convert.ToInt32(dgv.Rows[i].Cells["رقم الصفحة"].Value.ToString());
                    //string? PageNumber = (string?)dgv.Rows[i].Cells["رقم الصفحة"].Value.ToString();
                    //int? PageNumber = (int?)dgv.Rows[i].Cells["رقم الصفحة"].Value;
                    long SN = (long)dgv.Rows[i].Cells["التسلسل"].Value;

                    if (username.Trim() == "") { continue; }
                    //if (profile.Trim() == "") profile = Public_Profile_Name;
                    //if (sp.Trim() == "") sp = Public_SellingPoint_Value;
                    try
                    {
                        if (card_copy.Add(username) == false)
                        {
                            card_freq.Add(username);
                            DataRow r = dt_card_freq.NewRow();
                            r[0] = username;
                            r[1] = i;
                            dt_card_freq.Rows.Add(r);
                            continue;
                        }
                    }
                    catch
                    {
                        card_freq.Add(username);
                        continue;

                    }


                    NewUserToAdd NewUser_Generate = new NewUserToAdd
                    {
                        
                        Name = username,
                        Password = Password,
                        PageNumber = PageNumber,
                        //PageNumber = (int?)PageNumber,
                        //PageNumber = PageNumber == null ? (int?)null : Convert.ToInt32(PageNumber),
                        SN_Archive = SN,
                    };

                    dicUser.Add(NewUser_Generate.Name, NewUser_Generate);
                    strUser += "\"" + NewUser_Generate.Name + "\"" + ",";
                    strPass += "\"" + NewUser_Generate.Password + "\"" + ",";

                    //if (useProgressBar)
                    //Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Number_Cards_ToAdd + "  /  " + (i + 1) + " )", mesgBtn);
                }

                if (card_freq.Count > 0)
                {
                    string message = "هناك بعض الكروت مكرره موجوده مسبقا في الروتر \n هل تريد  عرض الكروت في جدول جانبي ومعالجتها يدويا \n اذا ضغطت لا سيتم تجاهل الكروت المكرروه وسيتم اضافة البقية";
                    DialogResult result = RJMessageBox.Show(message, "تحذير", MessageBoxButtons.YesNoCancel);

                    if (result == DialogResult.Yes)
                    {
                        Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                           (System.Windows.Forms.MethodInvoker)delegate ()
                           {
                               dgv_freq.DataSource = dt_card_freq;
                               dgv_freq.Columns["index"].Visible = false;
                               dgv_freq.Visible = true;

                               dgv.Width = pnlClientArea.Width - dgv_freq.Width - 15;
                           });


                        return null;
                    }
                    else if (result == DialogResult.Cancel)
                    {
                        return null;
                    }
                    //========= حذف المكرره

                    for (int i = 0; i < dt_card_freq.Rows.Count; i++)
                    {
                        dgv.Rows.RemoveAt(Convert.ToInt32(dt_card_freq.Rows[0]["index"].ToString()));
                    }
                    if (dgv.Rows.Count == 0)
                    {
                        RJMessageBox.Show("لا يوجد كروت للاضافة قد تكون جميع الكروت مكرره");
                        return null;
                    }
                    clss_InfoPrint.Number_Cards_ToAdd = dgv.Rows.Count;


                }
                new_Generate_Cards.status = true;
                new_Generate_Cards.strUser = strUser;
                new_Generate_Cards.strPass = strPass;
                new_Generate_Cards.dicUser = dicUser;

            }
            catch (Exception ex)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                RJMessageBox.Show("Generate_Cards\n" + ex.Message);
                return null;
            }
            return new_Generate_Cards;
        }

        private Clss_InfoPrint get_data_from_interface2()
        {

            //======================

            clss_InfoPrint = new Clss_InfoPrint();
            clss_InfoPrint.is_add_batch_cards = true;
            clss_InfoPrint.is_add_batch_cards_to_Archive = false;
            clss_InfoPrint.is_Add_One_Card = false;
            clss_InfoPrint.Number_Cards_ToAdd = dgv.Rows.Count;

            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == dgv.Rows[0].Cells["الباقة"].Value.ToString());
            //UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile.SelectedValue.ToString());
            clss_InfoPrint.profile = profile;
            int NumberPrint = 0;
            int BatchNumber = 0;
            //if (checkBox_RegisterAsBatch.Checked)
            //{
            //    //NumberPrint = SqlDataAccess.Get_lastID_Batch_cards();
            //    NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence();
            //    NumberPrint += 1;
            //    clss_InfoPrint.NumberPrint = NumberPrint;
            //}
            //if (checkBox_RegisterAs_LastBatch.Checked)
            //{
            //    NumberPrint = Convert.ToInt32(txt_last_batchNumber.Text);
            //    clss_InfoPrint.NumberPrint = NumberPrint;
            //    clss_InfoPrint.is_RegisterAs_LastBatch = true;

            //}


            if (checkBox_RegisterAsBatch.Checked)
            {
                BatchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence("BatchCards");
                BatchNumber += 1;
                clss_InfoPrint.BatchNumber = BatchNumber;
            }
            if (checkBox_RegisterAs_LastBatch.Checked)
            {
                BatchNumber = Convert.ToInt32(frm.txt_last_batchNumber.Text);
                clss_InfoPrint.BatchNumber = BatchNumber;
                clss_InfoPrint.is_RegisterAs_LastBatch = true;

            }

            NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint");
            NumberPrint += 1;
            clss_InfoPrint.NumberPrint = NumberPrint;



            if (Global_Variable.Mk_resources.version >= 7)
            {
                if (CBox_Attribute.Text != "" && CBox_Attribute.Text != "")
                {
                    clss_InfoPrint.is_use_Attribut = true;
                    clss_InfoPrint.Attribut = CBox_Attribute.SelectedItem.ToString() + ":" + txt_attribute.Text.Trim();
                }
            }

            if (CBox_group.Text != "")
                clss_InfoPrint.Group = CBox_group.Text.ToString();

            //clss_InfoPrint.Selected_template_item = CBox_TemplateCards.SelectedIndex;
            //clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(txtNumberCard.Text);
            //clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(frm.txtNumberCard.Text);
            clss_InfoPrint.Profile_Name = dgv.Rows[0].Cells["الباقة"].Value.ToString();
            //clss_InfoPrint.Profile_Name = CBox_Profile.SelectedValue.ToString();
            //clss_InfoPrint.Mode_User_NumberORcharcter = frm.cbox_User_NumberORcharcter.SelectedItem.ToString();
            //clss_InfoPrint.Mode_User_NumberORcharcter_Value = frm.cbox_User_NumberORcharcter.SelectedIndex;
            //clss_InfoPrint.User_Long = (Convert.ToInt32(frm.txt_longUsers.Text));
            //clss_InfoPrint.Mode_Password_NumberORcharcter = frm.cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            //clss_InfoPrint.Mode_Password_NumberORcharcter_Value = frm.cbox_Pass_NumberORcharcter.SelectedIndex;
            //clss_InfoPrint.Password_Long = Convert.ToInt32(frm.txt_longPassword.Text);
            //clss_InfoPrint.UserPassword_Pattern = frm.cbox_UserPassword_Pattern.SelectedIndex;
            clss_InfoPrint.SellingPoint_Name = CBox_SellingPoint.Text;
            if (CBox_SellingPoint.Text != "")
            {
                try
                {
                    clss_InfoPrint.SellingPoint_Name = CBox_SellingPoint.Text;
                    clss_InfoPrint.SellingPoint_Value = CBox_SellingPoint.SelectedValue.ToString();
                    //clss_InfoPrint.SellingPoint_Value = (int?)frm.CBox_SellingPoint.SelectedValue;
                    clss_InfoPrint.SellingPoint = Smart_DA.LoadSingleByNullId<SellingPoint>((int?)CBox_SellingPoint.SelectedValue, "SellingPoint");
                    //clss_InfoPrint.SellingPoint = Smart_DA.Get_Any_byId<SellingPoint>((int?)CBox_SellingPoint.SelectedValue);
                    clss_InfoPrint.SellingPoint_Value_str = CBox_SellingPoint.SelectedValue.ToString();
                }
                catch { }
            }
            //clss_InfoPrint.StartCard = frm.txt_StartCard.Text.Trim();
            //clss_InfoPrint.EndCard = frm.txt_EndCard.Text.Trim();
            clss_InfoPrint.ShardUser = "1";
            //clss_InfoPrint.ShardUser = txt_ShardUser.Text.Trim();
            clss_InfoPrint.FirstUse = checkBoxFirstUse.Checked;
            clss_InfoPrint.is_comment = checkBox_note.Checked;
            //clss_InfoPrint.pathfile = Frm_State.path_saved_file;

            //ump.frm.checkBox_note = checkBox_note.Checked;
            if (checkBox_note.Checked)
                clss_InfoPrint.Comment = txt_note.Text.Trim().ToString();

            if (Global_Variable.Mk_resources.version <= 6)
                clss_InfoPrint.Custumer_UserMan = CBox_CustomerUserMan.Text.ToString();

            clss_InfoPrint.Save_To_PDF = false;
            //clss_InfoPrint.Save_To_PDF = checkBoxSaveTo_PDF.Checked;
            //clss_InfoPrint.Open_PDF_file = checkBoxOpenAfterPrint.Checked;
            //clss_InfoPrint.SaveTo_excel = checkBoxSaveTo_excel.Checked;
            //clss_InfoPrint.SaveTo_script_File = frm.checkBoxSaveTo_script_File.Checked;
            //clss_InfoPrint.SaveTo_text_File = frm.checkBoxSaveTo_text_File.Checked;
            clss_InfoPrint.RegisterAsBatch = checkBox_RegisterAsBatch.Checked;
            clss_InfoPrint.RegisterAs_LasBatch = checkBox_RegisterAs_LastBatch.Checked;
            //clss_InfoPrint.With_Archive_uniqe = checkBox_With_Archive_uniqe.Checked;
            //clss_InfoPrint.TemplateId = CBox_TemplateCards.SelectedValue.ToString();
            //clss_InfoPrint.TemplateName = CBox_TemplateCards.Text.ToString();
            clss_InfoPrint.checkBox_Remove_FromArchive = checkBox_Remove_FromArchive.Check;




            return clss_InfoPrint;
        }


        public void Change_Status_CardsfromDB_ToPrint_In_Server(List<UmUser> dbUser)
        {

            if (clss_InfoPrint.checkBox_Remove_FromArchive == false)
            {
                string time = DateTime.Now.ToString("yyyy-MM-dd");
                int effect = ArchiveDB.Execute($"update CardsArtchive set [Status]=1 , [RegDate]='{time}'  where UserName=@UserName", dbUser);

            }
            else
            {
                int effect = ArchiveDB.Execute("DELETE FROM CardsArtchive WHERE  UserName=@UserName", dbUser);

            }
            //SqlDataAccess.Add_Batch_Cards(data,data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);

        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV();
        }

        private void CBox_Batch_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            //if (batch != null)
            //{

            batchArtchive = ArchiveDB.LoadSingle<BatchArtchive>($"select * from BatchArtchive where BatchNumber={CBox_Batch.SelectedValue}");
            //CBox_Batch.Text = batchArtchive.Str_Name.ToString();
            try
            {
                //Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //comboSource.Add(batch.Str_Name, batch.BatchNumber.ToString());
                //CBox_Batch.DataSource = new BindingSource(comboSource, null);
                //CBox_Batch.DisplayMember = "Key";
                //CBox_Batch.ValueMember = "Value";
                ////CBox_Batch.Text = "";
                //CBox_Batch.SelectedIndex = -1;

                //txt_Page_From.Text = batchArtchive._from.ToString();
                //txt_Page_To.Text = batchArtchive.Sn_to.ToString();

                firstLoad = true;

                txt_Sn_From.Text = batchArtchive.Sn_from.ToString();
                txt_Sn_To.Text = batchArtchive.Sn_to.ToString();

                txt_Page_From.Text ="0";
                txt_Page_To.Text = "0";
                loadData();
                try
                {

                    txt_Sn_From.Text = dgv.Rows[0].Cells["Sn"].Value.ToString();
                    txt_Sn_To.Text = dgv.Rows[dgv.Rows.Count - 1].Cells["Sn"].Value.ToString();

                    txt_Page_From.Text = dgv.Rows[0].Cells["PageNumber"].Value.ToString();
                    txt_Page_To.Text = dgv.Rows[dgv.Rows.Count - 1].Cells["PageNumber"].Value.ToString();
                    firstLoad = false;

                }
                catch { firstLoad = false; }

            }
            catch { firstLoad = false; }

            //CBox_Batch.Enabled = false;
            //}
        }

        private void checkBox_RegisterAsBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAsBatch.Checked == true)
                checkBox_RegisterAs_LastBatch.Checked = false;
            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAsBatch.Checked = true;
        }

        private void checkBox_RegisterAs_LastBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAs_LastBatch.Checked == true)
                checkBox_RegisterAsBatch.Checked = false;

            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAs_LastBatch.Checked = true;
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            try
            {
                BindingSource bs = new BindingSource();
                bs.DataSource = dgv.DataSource;
                bs.Filter = dgv.Columns[1].HeaderText.ToString() + " LIKE '%" + txt_search.Text + "%'";
                dgv.DataSource = bs;
            }
            catch { }
        }
    }
}
