using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار RJTabControl النهائي مع Collection آمن
    /// </summary>
    public partial class FinalRJTabControlTest : Form
    {
        private RJTabControl finalTabControl;

        public FinalRJTabControlTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // finalTabControl
            // 
            this.finalTabControl = new RJTabControl();
            this.finalTabControl.Dock = DockStyle.Fill;
            this.finalTabControl.TabHeight = 45;
            this.finalTabControl.TabSpacing = 3;
            this.finalTabControl.TabPadding = 20;
            this.finalTabControl.ContentBorderSize = 2;
            this.finalTabControl.ContentBorderColor = Color.FromArgb(0, 122, 204);
            this.finalTabControl.ContentBorderRadius = 8;

            // 
            // FinalRJTabControlTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1200, 800);
            this.Controls.Add(this.finalTabControl);
            this.Name = "FinalRJTabControlTest";
            this.Text = "🏆 اختبار RJTabControl النهائي - مع Collection آمن";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += FinalRJTabControlTest_Load;
        }

        private void FinalRJTabControlTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية
                AddFinalTestTabs();

                // عرض معلومات النجاح
                this.Text += " - 🎉 النسخة النهائية جاهزة!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل RJTabControl النهائي:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل التحميل!";
            }
        }

        private void AddFinalTestTabs()
        {
            // تاب النجاح النهائي
            var successTab = this.finalTabControl.AddTab("🏆 نجح الحل!", IconChar.Trophy);
            successTab.BackColor = Color.FromArgb(76, 175, 80);
            successTab.ForeColor = Color.White;
            successTab.IconSize = 28;

            var successLabel = new Label
            {
                Text = "🏆 تم حل مشكلة RJTabControl نهائياً!\n\n" +
                       "✅ SimpleRJTabPageCollection - مجموعة آمنة\n" +
                       "✅ SimpleRJTabPageCollectionEditor - محرر مبسط\n" +
                       "✅ AddTabSafely & RemoveTabSafely - طرق آمنة\n" +
                       "✅ معالجة شاملة للأخطاء\n" +
                       "✅ لا توجد مراجع دائرية\n" +
                       "✅ آمن تماماً للـ Designer\n\n" +
                       "🎯 الآن RJTabControl يعمل في Designer:\n" +
                       "• يمكن سحبه من Toolbox ✅\n" +
                       "• خاصية Tabs مرئية في Properties ✅\n" +
                       "• Collection Editor يفتح ويعمل ✅\n" +
                       "• يمكن إضافة/تعديل/حذف التابات ✅\n" +
                       "• جميع الخصائص متاحة ✅\n\n" +
                       "🚀 RJTabControl جاهز للإنتاج!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Padding = new Padding(20)
            };
            successTab.AddControl(successLabel);

            // تاب اختبار Collection
            var collectionTab = this.finalTabControl.AddTab("اختبار Collection", IconChar.List);
            collectionTab.BackColor = Color.FromArgb(0, 122, 204);
            collectionTab.ForeColor = Color.White;

            var collectionPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var collectionInfoLabel = new Label
            {
                Text = "📋 اختبار SimpleRJTabPageCollection:\n\n" +
                       $"عدد التابات: {this.finalTabControl.Tabs.Count}\n" +
                       $"التاب النشط: {this.finalTabControl.SelectedIndex}\n\n" +
                       "جرب الأزرار أدناه لاختبار Collection:",
                Location = new Point(0, 0),
                Size = new Size(1000, 100),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            var addViaCollectionButton = new RJButton
            {
                Text = "إضافة عبر Collection",
                IconChar = IconChar.Plus,
                Location = new Point(20, 120),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            addViaCollectionButton.Click += AddViaCollection_Click;

            var addWithIconButton = new RJButton
            {
                Text = "إضافة مع أيقونة",
                IconChar = IconChar.Star,
                Location = new Point(240, 120),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            addWithIconButton.Click += AddWithIcon_Click;

            var removeViaCollectionButton = new RJButton
            {
                Text = "إزالة عبر Collection",
                IconChar = IconChar.Minus,
                Location = new Point(460, 120),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            removeViaCollectionButton.Click += RemoveViaCollection_Click;

            var testCollectionButton = new RJButton
            {
                Text = "اختبار جميع طرق Collection",
                IconChar = IconChar.CheckCircle,
                Location = new Point(680, 120),
                Size = new Size(250, 50),
                BackColor = Color.FromArgb(63, 81, 181),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            testCollectionButton.Click += TestCollection_Click;

            collectionPanel.Controls.Add(collectionInfoLabel);
            collectionPanel.Controls.Add(addViaCollectionButton);
            collectionPanel.Controls.Add(addWithIconButton);
            collectionPanel.Controls.Add(removeViaCollectionButton);
            collectionPanel.Controls.Add(testCollectionButton);
            collectionTab.AddControl(collectionPanel);

            // تاب التعليمات النهائية
            var instructionsTab = this.finalTabControl.AddTab("التعليمات النهائية", IconChar.BookOpen);
            instructionsTab.BackColor = Color.FromArgb(63, 81, 181);
            instructionsTab.ForeColor = Color.White;

            var instructionsTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "📖 التعليمات النهائية لاستخدام RJTabControl:\n\n" +
                       "🎯 في Visual Studio Designer:\n" +
                       "1. اسحب RJTabControl من Toolbox\n" +
                       "2. افتح Properties Panel\n" +
                       "3. ابحث عن خاصية Tabs\n" +
                       "4. انقر على [...] لفتح Collection Editor\n" +
                       "5. أضف/عدل/احذف التابات كما تشاء\n\n" +
                       "💻 في الكود:\n" +
                       "// إضافة تاب بسيط\n" +
                       "var tab = tabControl.AddTab(\"تاب جديد\");\n\n" +
                       "// إضافة تاب مع أيقونة\n" +
                       "var tab = tabControl.AddTab(\"تاب\", IconChar.Home);\n\n" +
                       "// إضافة عبر Collection\n" +
                       "tabControl.Tabs.Add(new RJTabPage(\"تاب\"));\n\n" +
                       "// الوصول للتابات\n" +
                       "var firstTab = tabControl.Tabs[0];\n" +
                       "var tabByName = tabControl.Tabs[\"TabName\"];\n\n" +
                       "🔧 الخصائص المتاحة:\n" +
                       "• TabHeight - ارتفاع التابات\n" +
                       "• TabSpacing - المسافة بين التابات\n" +
                       "• TabPadding - الحشو الداخلي\n" +
                       "• ShowCloseButtons - أزرار الإغلاق\n" +
                       "• ContentBorderSize - حجم حدود المحتوى\n" +
                       "• ContentBorderColor - لون الحدود\n" +
                       "• ContentBorderRadius - انحناء الحدود\n\n" +
                       "🎨 تخصيص التابات:\n" +
                       "• BackColor - لون الخلفية\n" +
                       "• ForeColor - لون النص\n" +
                       "• IconChar - الأيقونة\n" +
                       "• IconSize - حجم الأيقونة\n" +
                       "• Font - خط النص\n\n" +
                       "🚀 RJTabControl الآن جاهز للاستخدام في جميع مشاريعك!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(63, 81, 181),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            instructionsTab.AddControl(instructionsTextBox);

            // تفعيل التاب الأول
            this.finalTabControl.SelectedIndex = 0;
        }

        private void AddViaCollection_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = new RJTabPage($"Collection Tab {this.finalTabControl.Tabs.Count + 1}");
                newTab.BackColor = Color.FromArgb(233, 30, 99);
                newTab.ForeColor = Color.White;

                var label = new Label
                {
                    Text = $"📋 تاب من Collection!\n\n" +
                           $"رقم التاب: {this.finalTabControl.Tabs.Count + 1}\n" +
                           $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ SimpleRJTabPageCollection يعمل!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                newTab.AddControl(label);

                // إضافة عبر Collection
                this.finalTabControl.Tabs.Add(newTab);

                // تفعيل التاب الجديد
                this.finalTabControl.SelectedIndex = this.finalTabControl.Tabs.Count - 1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب عبر Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddWithIcon_Click(object sender, EventArgs e)
        {
            try
            {
                var icons = new[] { IconChar.Star, IconChar.Heart, IconChar.Diamond, IconChar.Crown, IconChar.Gem };
                var random = new Random();
                var selectedIcon = icons[random.Next(icons.Length)];

                var newTab = this.finalTabControl.Tabs.Add($"أيقونة {this.finalTabControl.Tabs.Count + 1}", selectedIcon);
                newTab.BackColor = Color.FromArgb(255, 152, 0);
                newTab.ForeColor = Color.White;
                newTab.IconSize = 20;

                var label = new Label
                {
                    Text = $"⭐ تاب مع أيقونة!\n\n" +
                           $"الأيقونة: {selectedIcon}\n" +
                           $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ إضافة التابات مع الأيقونات تعمل!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                newTab.AddControl(label);

                // تفعيل التاب الجديد
                this.finalTabControl.SelectedTab = newTab;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب مع أيقونة:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RemoveViaCollection_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.finalTabControl.Tabs.Count > 1) // الاحتفاظ بتاب واحد على الأقل
                {
                    var lastTab = this.finalTabControl.Tabs[this.finalTabControl.Tabs.Count - 1];
                    this.finalTabControl.Tabs.Remove(lastTab);
                    
                    MessageBox.Show($"✅ تم حذف التاب: {lastTab.Text}",
                                   "تم الحذف", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("لا يمكن حذف جميع التابات!\nيجب الاحتفاظ بتاب واحد على الأقل.",
                                   "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في حذف التاب:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TestCollection_Click(object sender, EventArgs e)
        {
            try
            {
                var collection = this.finalTabControl.Tabs;
                var results = "🧪 نتائج اختبار Collection:\n\n";

                // اختبار الخصائص
                results += $"Count: {collection.Count} ✅\n";
                results += $"IsReadOnly: {collection.IsReadOnly} ✅\n";

                // اختبار الوصول بالفهرس
                if (collection.Count > 0)
                {
                    results += $"First Tab: {collection[0]?.Text} ✅\n";
                    results += $"Last Tab: {collection[collection.Count - 1]?.Text} ✅\n";
                }

                // اختبار البحث
                results += $"Contains Test: {collection.Contains(collection[0])} ✅\n";
                results += $"IndexOf Test: {collection.IndexOf(collection[0])} ✅\n";

                results += "\n🎉 جميع طرق Collection تعمل بمثالية!";

                MessageBox.Show(results, "نتائج اختبار Collection", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار RJTabControl النهائي
        /// </summary>
        public static void RunFinalTest()
        {
            try
            {
                var form = new FinalRJTabControlTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار RJTabControl النهائي:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
