using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار بسيط لـ RJTabControl - بدون Designer
    /// </summary>
    public class SimpleTabTestForm : Form
    {
        private RJTabControl tabControl;

        public SimpleTabTestForm()
        {
            InitializeForm();
            SetupTabControl();
            AddSampleTabs();
        }

        private void InitializeForm()
        {
            // إعداد النموذج
            this.Text = "RJTabControl - اختبار بسيط";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.MinimumSize = new Size(600, 400);
        }

        private void SetupTabControl()
        {
            // إنشاء TabControl
            tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                TabSpacing = 3,
                TabPadding = 20,
                ContentBorderSize = 1,
                ContentBorderColor = Color.FromArgb(200, 200, 200),
                ContentBorderRadius = 0
            };

            // ربط الأحداث
            tabControl.TabChanged += TabControl_TabChanged;
            tabControl.TabAdded += TabControl_TabAdded;
            tabControl.TabRemoved += TabControl_TabRemoved;

            this.Controls.Add(tabControl);
        }

        private void AddSampleTabs()
        {
            // تاب الرئيسية
            var homeTab = tabControl.AddTab("الرئيسية", IconChar.Home);
            var homePanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 2,
                BorderColor = Color.FromArgb(0, 122, 204),
                BorderRadius = 10,
                Padding = new Padding(20)
            };

            var homeLabel = new Label
            {
                Text = "🏠 مرحباً بك في الصفحة الرئيسية!\n\n" +
                       "✅ RJTabControl يعمل بشكل مثالي\n" +
                       "✅ كل تاب هو RJButton\n" +
                       "✅ RJPanel مع حدود جديدة\n" +
                       "✅ لا توجد أخطاء!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            homePanel.Controls.Add(homeLabel);
            homeTab.AddControl(homePanel);

            // تاب الإعدادات
            var settingsTab = tabControl.AddTab("الإعدادات", IconChar.Cog);
            var settingsPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 2,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 15,
                Padding = new Padding(20)
            };

            var settingsLabel = new Label
            {
                Text = "⚙️ صفحة الإعدادات\n\n" +
                       "هنا يمكنك تخصيص التطبيق\n" +
                       "وتغيير الإعدادات المختلفة",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            settingsPanel.Controls.Add(settingsLabel);
            settingsTab.AddControl(settingsPanel);

            // تاب النص
            var textTab = tabControl.AddTab("نص", IconChar.Edit);
            var textBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 RJTextBox مع ReadOnly!\n\n" +
                       "ReadOnly = true\n" +
                       "هذا النص للقراءة فقط\n" +
                       "لا يمكن تعديله أو تغييره\n\n" +
                       "جرب النقر والكتابة - لن يحدث شيء!\n\n" +
                       "مفيد لعرض المعلومات والنصوص\n" +
                       "التي لا تحتاج لتعديل من المستخدم.\n\n" +
                       "🔒 محمي من التعديل!",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(244, 67, 54),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Center
            };
            textTab.AddControl(textBox);

            // تاب المعلومات
            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            var infoPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var infoLabel = new Label
            {
                Text = "ℹ️ معلومات التطبيق\n\n" +
                       "🎉 RJTabControl جديد كلياً:\n" +
                       "• كل تاب هو RJButton\n" +
                       "• لا تكرار في الكود\n" +
                       "• استخدام مباشر لـ RJControls\n" +
                       "• tabsPanel و contentPanel الآن RJPanel\n" +
                       "• RJPanel يدعم BorderSize و BorderColor\n" +
                       "• RJTextBox يدعم ReadOnly\n" +
                       "• الألوان الافتراضية محفوظة\n\n" +
                       "✅ جميع الأخطاء تم إصلاحها!\n" +
                       "✅ جاهز للاستخدام!",
                Dock = DockStyle.Fill,
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            // أزرار التحكم
            var buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 50,
                Padding = new Padding(10)
            };

            var addTabButton = new RJButton
            {
                Text = "إضافة تاب",
                IconChar = IconChar.Plus,
                Size = new Size(100, 30),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 5
            };
            addTabButton.Click += AddTabButton_Click;

            var removeTabButton = new RJButton
            {
                Text = "إزالة تاب",
                IconChar = IconChar.Minus,
                Size = new Size(100, 30),
                Location = new Point(120, 10),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 5
            };
            removeTabButton.Click += RemoveTabButton_Click;

            var styleButton = new RJButton
            {
                Text = "تغيير النمط",
                IconChar = IconChar.Palette,
                Size = new Size(100, 30),
                Location = new Point(230, 10),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 5
            };
            styleButton.Click += StyleButton_Click;

            buttonPanel.Controls.Add(addTabButton);
            buttonPanel.Controls.Add(removeTabButton);
            buttonPanel.Controls.Add(styleButton);

            infoPanel.Controls.Add(infoLabel);
            infoPanel.Controls.Add(buttonPanel);
            infoTab.AddControl(infoPanel);
        }

        #region Event Handlers
        private void TabControl_TabChanged(object sender, TabChangedEventArgs e)
        {
            Console.WriteLine($"تم تغيير التاب من '{e.PreviousTab?.Text}' إلى '{e.CurrentTab?.Text}'");
        }

        private void TabControl_TabAdded(object sender, TabEventArgs e)
        {
            Console.WriteLine($"تم إضافة التاب: {e.Tab.Text}");
        }

        private void TabControl_TabRemoved(object sender, TabEventArgs e)
        {
            Console.WriteLine($"تم إزالة التاب: {e.Tab.Text}");
        }

        private void AddTabButton_Click(object sender, EventArgs e)
        {
            var tabNumber = tabControl.TabCount + 1;
            var newTab = tabControl.AddTab($"تاب {tabNumber}", IconChar.File);

            var label = new Label
            {
                Text = $"📄 محتوى التاب رقم {tabNumber}\n\nتم إنشاؤه ديناميكياً! 🎉",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(50, 50, 50)
            };
            newTab.AddControl(label);
        }

        private void RemoveTabButton_Click(object sender, EventArgs e)
        {
            if (tabControl.ActiveTab != null)
            {
                tabControl.RemoveTab(tabControl.ActiveTab);
            }
        }

        private void StyleButton_Click(object sender, EventArgs e)
        {
            // تدوير الأنماط
            var currentStyle = tabControl.TabStyle.StyleType;
            switch (currentStyle)
            {
                case TabStyleType.Modern:
                    tabControl.TabStyle = TabStyle.Blue;
                    tabControl.TabStyle.StyleType = TabStyleType.Chrome;
                    break;
                case TabStyleType.Chrome:
                    tabControl.TabStyle = TabStyle.Green;
                    tabControl.TabStyle.StyleType = TabStyleType.VSCode;
                    break;
                case TabStyleType.VSCode:
                    tabControl.TabStyle = TabStyle.Dark;
                    tabControl.TabStyle.StyleType = TabStyleType.Classic;
                    break;
                default:
                    tabControl.TabStyle = TabStyle.Default;
                    tabControl.TabStyle.StyleType = TabStyleType.Modern;
                    break;
            }
        }
        #endregion

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new SimpleTabTestForm();
            form.ShowDialog();
        }
    }
}
