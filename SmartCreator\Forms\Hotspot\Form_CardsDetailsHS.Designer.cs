﻿namespace SmartCreator.Forms.Hotspot
{
    partial class Form_CardsDetailsHS
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.btnAddProfile = new SmartCreator.RJControls.RJButton();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.txt_validay = new SmartCreator.RJControls.RJTextBox();
            this.txt_Real_TransferLimit = new SmartCreator.RJControls.RJTextBox();
            this.txt_TransferLeft = new SmartCreator.RJControls.RJTextBox();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.txt_TimeLeft = new SmartCreator.RJControls.RJTextBox();
            this.txt_Real_uptimeLimit = new SmartCreator.RJControls.RJTextBox();
            this.txt_mac = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.Toggle_Status = new SmartCreator.RJControls.RJToggleButton();
            this.txt_Till_Date = new SmartCreator.RJControls.RJTextBox();
            this.txt_RegDate = new SmartCreator.RJControls.RJTextBox();
            this.txt_Password = new SmartCreator.RJControls.RJTextBox();
            this.txt_username = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.lbl_username = new SmartCreator.RJControls.RJLabel();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.dgv_profiles = new SmartCreator.RJControls.RJDataGridView();
            this.dm_profile = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.اضافةباقةجديدةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RemoveProfile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.txt_TotalPrice = new SmartCreator.RJControls.RJTextBox();
            this.txt_CountProfile = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel19 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel20 = new SmartCreator.RJControls.RJLabel();
            this.txt_TransferLimit = new SmartCreator.RJControls.RJTextBox();
            this.txt_uptimeLimit = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel22 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel21 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.Radio_WithSession = new SmartCreator.RJControls.RJRadioButton();
            this.Radio_Baisc = new SmartCreator.RJControls.RJRadioButton();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.btn_addSmartValidity = new SmartCreator.RJControls.RJButton();
            this.CheckBox_byDayOrHour = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_session = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_download = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_time = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_SmartScript = new SmartCreator.RJControls.RJCheckBox();
            this.Toggle_Bind_Mac = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.txt_CountSession = new SmartCreator.RJControls.RJTextBox();
            this.txt_TotalUptime = new SmartCreator.RJControls.RJTextBox();
            this.txt_TotalDownload = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.dgv_Sessions = new SmartCreator.RJControls.RJDataGridView();
            this.dm_Session = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.ترتيبالاعمدةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبتاريخبدايةالجلسةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبتاريخنهايةالجلسةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالوقتالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالتحميلالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالرفعالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبايبيالجلسهIPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالماكToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالبورتالجهازToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حذفجميعجلساتالكرتToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تحديثالجلساتمنالروترToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخالسطركاملToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_profiles)).BeginInit();
            this.dm_profile.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Sessions)).BeginInit();
            this.dm_Session.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.txt_TotalUptime);
            this.pnlClientArea.Controls.Add(this.CheckBox_SmartScript);
            this.pnlClientArea.Controls.Add(this.rjLabel5);
            this.pnlClientArea.Controls.Add(this.txt_CountSession);
            this.pnlClientArea.Controls.Add(this.groupBox1);
            this.pnlClientArea.Controls.Add(this.lbl_note);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.txt_TotalDownload);
            this.pnlClientArea.Controls.Add(this.rjLabel7);
            this.pnlClientArea.Controls.Add(this.rjLabel6);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(921, 581);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(149, 22);
            this.lblCaption.Text = "Form_CardsDetailsHS";
            // 
            // rjPanel1
            // 
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 10;
            this.rjPanel1.Controls.Add(this.btnAddProfile);
            this.rjPanel1.Controls.Add(this.btn_Refresh);
            this.rjPanel1.Controls.Add(this.txt_validay);
            this.rjPanel1.Controls.Add(this.txt_Real_TransferLimit);
            this.rjPanel1.Controls.Add(this.txt_TransferLeft);
            this.rjPanel1.Controls.Add(this.btnSave);
            this.rjPanel1.Controls.Add(this.txt_TimeLeft);
            this.rjPanel1.Controls.Add(this.txt_Real_uptimeLimit);
            this.rjPanel1.Controls.Add(this.txt_mac);
            this.rjPanel1.Controls.Add(this.rjLabel16);
            this.rjPanel1.Controls.Add(this.rjLabel2);
            this.rjPanel1.Controls.Add(this.rjLabel10);
            this.rjPanel1.Controls.Add(this.Toggle_Status);
            this.rjPanel1.Controls.Add(this.txt_Till_Date);
            this.rjPanel1.Controls.Add(this.txt_RegDate);
            this.rjPanel1.Controls.Add(this.txt_Password);
            this.rjPanel1.Controls.Add(this.txt_username);
            this.rjPanel1.Controls.Add(this.rjLabel17);
            this.rjPanel1.Controls.Add(this.rjLabel1);
            this.rjPanel1.Controls.Add(this.rjLabel9);
            this.rjPanel1.Controls.Add(this.lbl_username);
            this.rjPanel1.Controls.Add(this.rjLabel15);
            this.rjPanel1.Controls.Add(this.rjLabel13);
            this.rjPanel1.Controls.Add(this.rjLabel12);
            this.rjPanel1.Controls.Add(this.rjLabel11);
            this.rjPanel1.Controls.Add(this.rjPanel3);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(12, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(900, 270);
            this.rjPanel1.TabIndex = 1;
            // 
            // btnAddProfile
            // 
            this.btnAddProfile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btnAddProfile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.BorderRadius = 8;
            this.btnAddProfile.BorderSize = 1;
            this.btnAddProfile.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAddProfile.FlatAppearance.BorderSize = 0;
            this.btnAddProfile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btnAddProfile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btnAddProfile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddProfile.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnAddProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAddProfile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddProfile.IconSize = 17;
            this.btnAddProfile.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAddProfile.Location = new System.Drawing.Point(221, 228);
            this.btnAddProfile.Name = "btnAddProfile";
            this.btnAddProfile.Size = new System.Drawing.Size(129, 35);
            this.btnAddProfile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btnAddProfile.TabIndex = 131;
            this.btnAddProfile.Text = "اضافة رصيد";
            this.btnAddProfile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAddProfile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAddProfile.UseVisualStyleBackColor = false;
            this.btnAddProfile.Click += new System.EventHandler(this.btnAddProfile_Click);
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 8;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.RotateForward;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 20;
            this.btn_Refresh.Location = new System.Drawing.Point(32, 228);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Size = new System.Drawing.Size(158, 35);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 59;
            this.btn_Refresh.Text = "تحديث البيانات";
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // txt_validay
            // 
            this.txt_validay._Customizable = false;
            this.txt_validay.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_validay.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_validay.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_validay.BorderRadius = 5;
            this.txt_validay.BorderSize = 1;
            this.txt_validay.Enabled = false;
            this.txt_validay.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_validay.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_validay.Location = new System.Drawing.Point(383, 186);
            this.txt_validay.MultiLine = false;
            this.txt_validay.Name = "txt_validay";
            this.txt_validay.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_validay.PasswordChar = false;
            this.txt_validay.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_validay.PlaceHolderText = null;
            this.txt_validay.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_validay.Size = new System.Drawing.Size(141, 29);
            this.txt_validay.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_validay.TabIndex = 91;
            this.txt_validay.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Real_TransferLimit
            // 
            this.txt_Real_TransferLimit._Customizable = false;
            this.txt_Real_TransferLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Real_TransferLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Real_TransferLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Real_TransferLimit.BorderRadius = 5;
            this.txt_Real_TransferLimit.BorderSize = 1;
            this.txt_Real_TransferLimit.Enabled = false;
            this.txt_Real_TransferLimit.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_Real_TransferLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Real_TransferLimit.Location = new System.Drawing.Point(632, 141);
            this.txt_Real_TransferLimit.MultiLine = false;
            this.txt_Real_TransferLimit.Name = "txt_Real_TransferLimit";
            this.txt_Real_TransferLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Real_TransferLimit.PasswordChar = false;
            this.txt_Real_TransferLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Real_TransferLimit.PlaceHolderText = null;
            this.txt_Real_TransferLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Real_TransferLimit.Size = new System.Drawing.Size(155, 29);
            this.txt_Real_TransferLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Real_TransferLimit.TabIndex = 91;
            this.txt_Real_TransferLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_TransferLeft
            // 
            this.txt_TransferLeft._Customizable = false;
            this.txt_TransferLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TransferLeft.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TransferLeft.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TransferLeft.BorderRadius = 5;
            this.txt_TransferLeft.BorderSize = 1;
            this.txt_TransferLeft.Enabled = false;
            this.txt_TransferLeft.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TransferLeft.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TransferLeft.Location = new System.Drawing.Point(383, 141);
            this.txt_TransferLeft.MultiLine = false;
            this.txt_TransferLeft.Name = "txt_TransferLeft";
            this.txt_TransferLeft.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TransferLeft.PasswordChar = false;
            this.txt_TransferLeft.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TransferLeft.PlaceHolderText = null;
            this.txt_TransferLeft.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TransferLeft.Size = new System.Drawing.Size(141, 29);
            this.txt_TransferLeft.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TransferLeft.TabIndex = 91;
            this.txt_TransferLeft.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btnSave
            // 
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 8;
            this.btnSave.BorderSize = 1;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.btnSave.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 25;
            this.btnSave.Location = new System.Drawing.Point(389, 228);
            this.btnSave.Name = "btnSave";
            this.btnSave.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnSave.Size = new System.Drawing.Size(157, 35);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btnSave.TabIndex = 49;
            this.btnSave.Text = "حفظ التغيرات";
            this.btnSave.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSave.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // txt_TimeLeft
            // 
            this.txt_TimeLeft._Customizable = false;
            this.txt_TimeLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TimeLeft.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TimeLeft.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TimeLeft.BorderRadius = 5;
            this.txt_TimeLeft.BorderSize = 1;
            this.txt_TimeLeft.Enabled = false;
            this.txt_TimeLeft.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TimeLeft.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TimeLeft.Location = new System.Drawing.Point(383, 99);
            this.txt_TimeLeft.MultiLine = false;
            this.txt_TimeLeft.Name = "txt_TimeLeft";
            this.txt_TimeLeft.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TimeLeft.PasswordChar = false;
            this.txt_TimeLeft.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TimeLeft.PlaceHolderText = null;
            this.txt_TimeLeft.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TimeLeft.Size = new System.Drawing.Size(141, 29);
            this.txt_TimeLeft.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TimeLeft.TabIndex = 91;
            this.txt_TimeLeft.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Real_uptimeLimit
            // 
            this.txt_Real_uptimeLimit._Customizable = false;
            this.txt_Real_uptimeLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Real_uptimeLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Real_uptimeLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Real_uptimeLimit.BorderRadius = 5;
            this.txt_Real_uptimeLimit.BorderSize = 1;
            this.txt_Real_uptimeLimit.Enabled = false;
            this.txt_Real_uptimeLimit.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_Real_uptimeLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Real_uptimeLimit.Location = new System.Drawing.Point(632, 99);
            this.txt_Real_uptimeLimit.MultiLine = false;
            this.txt_Real_uptimeLimit.Name = "txt_Real_uptimeLimit";
            this.txt_Real_uptimeLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Real_uptimeLimit.PasswordChar = false;
            this.txt_Real_uptimeLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Real_uptimeLimit.PlaceHolderText = null;
            this.txt_Real_uptimeLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Real_uptimeLimit.Size = new System.Drawing.Size(155, 29);
            this.txt_Real_uptimeLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Real_uptimeLimit.TabIndex = 91;
            this.txt_Real_uptimeLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_mac
            // 
            this.txt_mac._Customizable = false;
            this.txt_mac.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_mac.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_mac.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_mac.BorderRadius = 5;
            this.txt_mac.BorderSize = 1;
            this.txt_mac.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txt_mac.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_mac.Location = new System.Drawing.Point(632, 188);
            this.txt_mac.MultiLine = false;
            this.txt_mac.Name = "txt_mac";
            this.txt_mac.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_mac.PasswordChar = false;
            this.txt_mac.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_mac.PlaceHolderText = null;
            this.txt_mac.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_mac.Size = new System.Drawing.Size(155, 28);
            this.txt_mac.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_mac.TabIndex = 91;
            this.txt_mac.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(807, 195);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel16.Size = new System.Drawing.Size(76, 17);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 90;
            this.rjLabel16.Text = "الماك المرتبط";
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(798, 234);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(85, 23);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 47;
            this.rjLabel2.Text = "الحــــــــــــــــالة";
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(794, 105);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(89, 17);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 90;
            this.rjLabel10.Text = "الوقت المسموح";
            // 
            // Toggle_Status
            // 
            this.Toggle_Status.Activated = true;
            this.Toggle_Status.Checked = true;
            this.Toggle_Status.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Toggle_Status.Customizable = false;
            this.Toggle_Status.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_Status.Location = new System.Drawing.Point(642, 227);
            this.Toggle_Status.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Status.Name = "Toggle_Status";
            this.Toggle_Status.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Status.OFF_Text = "";
            this.Toggle_Status.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Status.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Status.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Status.ON_Text = "";
            this.Toggle_Status.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Status.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Status.Size = new System.Drawing.Size(141, 37);
            this.Toggle_Status.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Status.TabIndex = 89;
            this.Toggle_Status.Tag = "تفصيلي";
            this.Toggle_Status.Text = "#";
            this.Toggle_Status.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.Toggle_Status.UseVisualStyleBackColor = true;
            this.Toggle_Status.CheckedChanged += new System.EventHandler(this.Toggle_Status_CheckedChanged);
            // 
            // txt_Till_Date
            // 
            this.txt_Till_Date._Customizable = false;
            this.txt_Till_Date.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Till_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Till_Date.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Till_Date.BorderRadius = 5;
            this.txt_Till_Date.BorderSize = 1;
            this.txt_Till_Date.Enabled = false;
            this.txt_Till_Date.Font = new System.Drawing.Font("Arial", 9.25F);
            this.txt_Till_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Till_Date.Location = new System.Drawing.Point(383, 55);
            this.txt_Till_Date.MultiLine = false;
            this.txt_Till_Date.Name = "txt_Till_Date";
            this.txt_Till_Date.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Till_Date.PasswordChar = false;
            this.txt_Till_Date.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Till_Date.PlaceHolderText = null;
            this.txt_Till_Date.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Till_Date.Size = new System.Drawing.Size(141, 27);
            this.txt_Till_Date.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Till_Date.TabIndex = 48;
            this.txt_Till_Date.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_RegDate
            // 
            this.txt_RegDate._Customizable = false;
            this.txt_RegDate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_RegDate.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_RegDate.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_RegDate.BorderRadius = 5;
            this.txt_RegDate.BorderSize = 1;
            this.txt_RegDate.Enabled = false;
            this.txt_RegDate.Font = new System.Drawing.Font("Arial", 9.25F);
            this.txt_RegDate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_RegDate.Location = new System.Drawing.Point(632, 55);
            this.txt_RegDate.MultiLine = false;
            this.txt_RegDate.Name = "txt_RegDate";
            this.txt_RegDate.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_RegDate.PasswordChar = false;
            this.txt_RegDate.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_RegDate.PlaceHolderText = null;
            this.txt_RegDate.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_RegDate.Size = new System.Drawing.Size(155, 27);
            this.txt_RegDate.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_RegDate.TabIndex = 48;
            this.txt_RegDate.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Password
            // 
            this.txt_Password._Customizable = false;
            this.txt_Password.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Password.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Password.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Password.BorderRadius = 5;
            this.txt_Password.BorderSize = 1;
            this.txt_Password.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_Password.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Password.Location = new System.Drawing.Point(383, 18);
            this.txt_Password.MultiLine = false;
            this.txt_Password.Name = "txt_Password";
            this.txt_Password.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Password.PasswordChar = false;
            this.txt_Password.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Password.PlaceHolderText = null;
            this.txt_Password.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Password.Size = new System.Drawing.Size(141, 27);
            this.txt_Password.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Password.TabIndex = 0;
            this.txt_Password.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_username
            // 
            this.txt_username._Customizable = false;
            this.txt_username.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_username.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_username.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_username.BorderRadius = 5;
            this.txt_username.BorderSize = 1;
            this.txt_username.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_username.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_username.Location = new System.Drawing.Point(632, 18);
            this.txt_username.MultiLine = false;
            this.txt_username.Name = "txt_username";
            this.txt_username.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_username.PasswordChar = false;
            this.txt_username.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_username.PlaceHolderText = null;
            this.txt_username.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_username.Size = new System.Drawing.Size(155, 27);
            this.txt_username.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_username.TabIndex = 0;
            this.txt_username.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(536, 192);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel17.Size = new System.Drawing.Size(86, 17);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 90;
            this.rjLabel17.Text = "الصلاحية بالايام";
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(799, 60);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(84, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 47;
            this.rjLabel1.Text = "تــــاريخ الطبــاعة";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(796, 147);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(87, 17);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 90;
            this.rjLabel9.Text = "الحجم المسموح";
            // 
            // lbl_username
            // 
            this.lbl_username.AutoSize = true;
            this.lbl_username.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_username.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_username.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_username.LinkLabel = false;
            this.lbl_username.Location = new System.Drawing.Point(812, 23);
            this.lbl_username.Name = "lbl_username";
            this.lbl_username.Size = new System.Drawing.Size(71, 17);
            this.lbl_username.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_username.TabIndex = 47;
            this.lbl_username.Text = "الاســـــــــــــــــم";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(540, 147);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel15.Size = new System.Drawing.Size(82, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 90;
            this.rjLabel15.Text = "الحجم المتبقي";
            // 
            // rjLabel13
            // 
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(538, 105);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel13.Size = new System.Drawing.Size(84, 17);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 90;
            this.rjLabel13.Text = "الوقت المتبقي";
            // 
            // rjLabel12
            // 
            this.rjLabel12.AutoSize = true;
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(549, 60);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel12.Size = new System.Drawing.Size(73, 17);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel12.TabIndex = 47;
            this.rjLabel12.Text = "تاريخ الانتهاء";
            // 
            // rjLabel11
            // 
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(549, 21);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(73, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 47;
            this.rjLabel11.Text = "كلمـــة المرور";
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 10;
            this.rjPanel3.Controls.Add(this.dgv_profiles);
            this.rjPanel3.Controls.Add(this.txt_TotalPrice);
            this.rjPanel3.Controls.Add(this.txt_CountProfile);
            this.rjPanel3.Controls.Add(this.rjLabel18);
            this.rjPanel3.Controls.Add(this.rjLabel19);
            this.rjPanel3.Controls.Add(this.rjLabel20);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(6, 5);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjPanel3.Size = new System.Drawing.Size(371, 218);
            this.rjPanel3.TabIndex = 132;
            // 
            // dgv_profiles
            // 
            this.dgv_profiles.AllowUserToAddRows = false;
            this.dgv_profiles.AllowUserToDeleteRows = false;
            this.dgv_profiles.AllowUserToOrderColumns = true;
            this.dgv_profiles.AllowUserToResizeRows = false;
            this.dgv_profiles.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_profiles.AlternatingRowsColorApply = false;
            this.dgv_profiles.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_profiles.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_profiles.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_profiles.BorderRadius = 13;
            this.dgv_profiles.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_profiles.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_profiles.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_profiles.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_profiles.ColumnHeaderHeight = 35;
            this.dgv_profiles.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_profiles.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv_profiles.ColumnHeadersHeight = 35;
            this.dgv_profiles.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_profiles.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_profiles.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_profiles.ContextMenuStrip = this.dm_profile;
            this.dgv_profiles.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_profiles.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv_profiles.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_profiles.EnableHeadersVisualStyles = false;
            this.dgv_profiles.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_profiles.Location = new System.Drawing.Point(8, 27);
            this.dgv_profiles.Name = "dgv_profiles";
            this.dgv_profiles.ReadOnly = true;
            this.dgv_profiles.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_profiles.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_profiles.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_profiles.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv_profiles.RowHeadersVisible = false;
            this.dgv_profiles.RowHeadersWidth = 35;
            this.dgv_profiles.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_profiles.RowHeight = 33;
            this.dgv_profiles.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_profiles.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv_profiles.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_profiles.RowTemplate.Height = 33;
            this.dgv_profiles.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_profiles.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_profiles.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_profiles.Size = new System.Drawing.Size(355, 127);
            this.dgv_profiles.TabIndex = 51;
            // 
            // dm_profile
            // 
            this.dm_profile.ActiveMenuItem = false;
            this.dm_profile.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dm_profile.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dm_profile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.اضافةباقةجديدةToolStripMenuItem,
            this.RemoveProfile_ToolStripMenuItem,
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem});
            this.dm_profile.Name = "dm_profile";
            this.dm_profile.OwnerIsMenuButton = false;
            this.dm_profile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dm_profile.Size = new System.Drawing.Size(215, 70);
            // 
            // اضافةباقةجديدةToolStripMenuItem
            // 
            this.اضافةباقةجديدةToolStripMenuItem.Name = "اضافةباقةجديدةToolStripMenuItem";
            this.اضافةباقةجديدةToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.اضافةباقةجديدةToolStripMenuItem.Text = "اضافة باقة جديدة";
            this.اضافةباقةجديدةToolStripMenuItem.Click += new System.EventHandler(this.اضافةباقةجديدةToolStripMenuItem_Click);
            // 
            // RemoveProfile_ToolStripMenuItem
            // 
            this.RemoveProfile_ToolStripMenuItem.Name = "RemoveProfile_ToolStripMenuItem";
            this.RemoveProfile_ToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.RemoveProfile_ToolStripMenuItem.Text = "حذف الباقة المحدده";
            this.RemoveProfile_ToolStripMenuItem.Visible = false;
            // 
            // تحديثباقاتالكرتمنالروترToolStripMenuItem
            // 
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Name = "تحديثباقاتالكرتمنالروترToolStripMenuItem";
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Text = "تحديث باقات الكرت من الروتر";
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Visible = false;
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Click += new System.EventHandler(this.تحديثباقاتالكرتمنالروترToolStripMenuItem_Click);
            // 
            // txt_TotalPrice
            // 
            this.txt_TotalPrice._Customizable = false;
            this.txt_TotalPrice.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalPrice.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalPrice.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalPrice.BorderRadius = 8;
            this.txt_TotalPrice.BorderSize = 1;
            this.txt_TotalPrice.Enabled = false;
            this.txt_TotalPrice.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalPrice.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalPrice.Location = new System.Drawing.Point(38, 181);
            this.txt_TotalPrice.MultiLine = false;
            this.txt_TotalPrice.Name = "txt_TotalPrice";
            this.txt_TotalPrice.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalPrice.PasswordChar = false;
            this.txt_TotalPrice.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalPrice.PlaceHolderText = null;
            this.txt_TotalPrice.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txt_TotalPrice.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalPrice.Size = new System.Drawing.Size(140, 29);
            this.txt_TotalPrice.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalPrice.TabIndex = 49;
            this.txt_TotalPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_CountProfile
            // 
            this.txt_CountProfile._Customizable = false;
            this.txt_CountProfile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_CountProfile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_CountProfile.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_CountProfile.BorderRadius = 8;
            this.txt_CountProfile.BorderSize = 1;
            this.txt_CountProfile.Enabled = false;
            this.txt_CountProfile.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_CountProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_CountProfile.Location = new System.Drawing.Point(215, 182);
            this.txt_CountProfile.MultiLine = false;
            this.txt_CountProfile.Name = "txt_CountProfile";
            this.txt_CountProfile.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_CountProfile.PasswordChar = false;
            this.txt_CountProfile.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_CountProfile.PlaceHolderText = null;
            this.txt_CountProfile.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_CountProfile.Size = new System.Drawing.Size(129, 29);
            this.txt_CountProfile.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_CountProfile.TabIndex = 49;
            this.txt_CountProfile.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel18
            // 
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(76, 157);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel18.Size = new System.Drawing.Size(77, 23);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel18.TabIndex = 47;
            this.rjLabel18.Text = "اجمالي المبلغ";
            // 
            // rjLabel19
            // 
            this.rjLabel19.AutoSize = true;
            this.rjLabel19.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel19.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel19.LinkLabel = false;
            this.rjLabel19.Location = new System.Drawing.Point(141, 3);
            this.rjLabel19.Name = "rjLabel19";
            this.rjLabel19.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel19.Size = new System.Drawing.Size(91, 23);
            this.rjLabel19.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel19.TabIndex = 47;
            this.rjLabel19.Text = "حركة رصيد الكرت";
            // 
            // rjLabel20
            // 
            this.rjLabel20.AutoSize = true;
            this.rjLabel20.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel20.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel20.LinkLabel = false;
            this.rjLabel20.Location = new System.Drawing.Point(246, 157);
            this.rjLabel20.Name = "rjLabel20";
            this.rjLabel20.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel20.Size = new System.Drawing.Size(66, 23);
            this.rjLabel20.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel20.TabIndex = 47;
            this.rjLabel20.Text = "عدد الباقات";
            // 
            // txt_TransferLimit
            // 
            this.txt_TransferLimit._Customizable = false;
            this.txt_TransferLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TransferLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TransferLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TransferLimit.BorderRadius = 5;
            this.txt_TransferLimit.BorderSize = 1;
            this.txt_TransferLimit.Enabled = false;
            this.txt_TransferLimit.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TransferLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TransferLimit.Location = new System.Drawing.Point(352, 81);
            this.txt_TransferLimit.MultiLine = false;
            this.txt_TransferLimit.Name = "txt_TransferLimit";
            this.txt_TransferLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TransferLimit.PasswordChar = false;
            this.txt_TransferLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TransferLimit.PlaceHolderText = null;
            this.txt_TransferLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TransferLimit.Size = new System.Drawing.Size(158, 29);
            this.txt_TransferLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TransferLimit.TabIndex = 91;
            this.txt_TransferLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_TransferLimit.Visible = false;
            // 
            // txt_uptimeLimit
            // 
            this.txt_uptimeLimit._Customizable = false;
            this.txt_uptimeLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptimeLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_uptimeLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptimeLimit.BorderRadius = 5;
            this.txt_uptimeLimit.BorderSize = 1;
            this.txt_uptimeLimit.Enabled = false;
            this.txt_uptimeLimit.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_uptimeLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptimeLimit.Location = new System.Drawing.Point(569, 27);
            this.txt_uptimeLimit.MultiLine = false;
            this.txt_uptimeLimit.Name = "txt_uptimeLimit";
            this.txt_uptimeLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptimeLimit.PasswordChar = false;
            this.txt_uptimeLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptimeLimit.PlaceHolderText = null;
            this.txt_uptimeLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptimeLimit.Size = new System.Drawing.Size(158, 29);
            this.txt_uptimeLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_uptimeLimit.TabIndex = 91;
            this.txt_uptimeLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_uptimeLimit.Visible = false;
            // 
            // rjLabel22
            // 
            this.rjLabel22.AutoSize = true;
            this.rjLabel22.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel22.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel22.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel22.LinkLabel = false;
            this.rjLabel22.Location = new System.Drawing.Point(666, 33);
            this.rjLabel22.Name = "rjLabel22";
            this.rjLabel22.Size = new System.Drawing.Size(45, 17);
            this.rjLabel22.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel22.TabIndex = 90;
            this.rjLabel22.Text = "الفعلي";
            this.rjLabel22.Visible = false;
            // 
            // rjLabel21
            // 
            this.rjLabel21.AutoSize = true;
            this.rjLabel21.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel21.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel21.LinkLabel = false;
            this.rjLabel21.Location = new System.Drawing.Point(598, 27);
            this.rjLabel21.Name = "rjLabel21";
            this.rjLabel21.Size = new System.Drawing.Size(45, 17);
            this.rjLabel21.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel21.TabIndex = 90;
            this.rjLabel21.Text = "الفعلي";
            this.rjLabel21.Visible = false;
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(519, 86);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(87, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 90;
            this.rjLabel14.Text = "الحجم المسموح";
            this.rjLabel14.Visible = false;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(734, 33);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.Size = new System.Drawing.Size(89, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 90;
            this.rjLabel3.Text = "الوقت المسموح";
            this.rjLabel3.Visible = false;
            // 
            // Radio_WithSession
            // 
            this.Radio_WithSession.AutoSize = true;
            this.Radio_WithSession.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_WithSession.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_WithSession.Customizable = false;
            this.Radio_WithSession.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_WithSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_WithSession.Location = new System.Drawing.Point(76, 10);
            this.Radio_WithSession.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_WithSession.Name = "Radio_WithSession";
            this.Radio_WithSession.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_WithSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Radio_WithSession.Size = new System.Drawing.Size(93, 21);
            this.Radio_WithSession.TabIndex = 62;
            this.Radio_WithSession.Text = "مع الجلسات";
            this.Radio_WithSession.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_WithSession.UseVisualStyleBackColor = true;
            this.Radio_WithSession.Visible = false;
            // 
            // Radio_Baisc
            // 
            this.Radio_Baisc.AutoSize = true;
            this.Radio_Baisc.Checked = true;
            this.Radio_Baisc.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_Baisc.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_Baisc.Customizable = false;
            this.Radio_Baisc.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_Baisc.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_Baisc.Location = new System.Drawing.Point(76, 81);
            this.Radio_Baisc.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_Baisc.Name = "Radio_Baisc";
            this.Radio_Baisc.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_Baisc.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Radio_Baisc.Size = new System.Drawing.Size(156, 21);
            this.Radio_Baisc.TabIndex = 63;
            this.Radio_Baisc.TabStop = true;
            this.Radio_Baisc.Text = "تحديث البيانات الاساسية";
            this.Radio_Baisc.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_Baisc.UseVisualStyleBackColor = true;
            this.Radio_Baisc.Visible = false;
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.groupBox1.Controls.Add(this.btn_addSmartValidity);
            this.groupBox1.Controls.Add(this.CheckBox_byDayOrHour);
            this.groupBox1.Controls.Add(this.CheckBox_Save_session);
            this.groupBox1.Controls.Add(this.CheckBox_Save_download);
            this.groupBox1.Controls.Add(this.CheckBox_Save_time);
            this.groupBox1.Location = new System.Drawing.Point(12, 334);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(900, 78);
            this.groupBox1.TabIndex = 130;
            this.groupBox1.TabStop = false;
            // 
            // btn_addSmartValidity
            // 
            this.btn_addSmartValidity.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_addSmartValidity.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_addSmartValidity.BorderRadius = 8;
            this.btn_addSmartValidity.BorderSize = 1;
            this.btn_addSmartValidity.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_addSmartValidity.Enabled = false;
            this.btn_addSmartValidity.FlatAppearance.BorderSize = 0;
            this.btn_addSmartValidity.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_addSmartValidity.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_addSmartValidity.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_addSmartValidity.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_addSmartValidity.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_addSmartValidity.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_addSmartValidity.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_addSmartValidity.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_addSmartValidity.IconSize = 1;
            this.btn_addSmartValidity.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_addSmartValidity.Location = new System.Drawing.Point(9, 21);
            this.btn_addSmartValidity.Name = "btn_addSmartValidity";
            this.btn_addSmartValidity.Size = new System.Drawing.Size(60, 35);
            this.btn_addSmartValidity.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_addSmartValidity.TabIndex = 133;
            this.btn_addSmartValidity.Text = "اضافة";
            this.btn_addSmartValidity.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_addSmartValidity.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_addSmartValidity.UseVisualStyleBackColor = false;
            this.btn_addSmartValidity.Click += new System.EventHandler(this.btn_addSmartValidity_Click);
            // 
            // CheckBox_byDayOrHour
            // 
            this.CheckBox_byDayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_byDayOrHour.AutoSize = true;
            this.CheckBox_byDayOrHour.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.BorderSize = 1;
            this.CheckBox_byDayOrHour.Check = false;
            this.CheckBox_byDayOrHour.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDayOrHour.Customizable = false;
            this.CheckBox_byDayOrHour.Enabled = false;
            this.CheckBox_byDayOrHour.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_byDayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDayOrHour.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_byDayOrHour.Location = new System.Drawing.Point(59, 35);
            this.CheckBox_byDayOrHour.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDayOrHour.Name = "CheckBox_byDayOrHour";
            this.CheckBox_byDayOrHour.Padding = new System.Windows.Forms.Padding(0, 0, 21, 0);
            this.CheckBox_byDayOrHour.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_byDayOrHour.Size = new System.Drawing.Size(177, 21);
            this.CheckBox_byDayOrHour.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDayOrHour.TabIndex = 14;
            this.CheckBox_byDayOrHour.Text = "احتساب وقت الكرت بالايام";
            this.CheckBox_byDayOrHour.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_byDayOrHour.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_session
            // 
            this.CheckBox_Save_session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_session.AutoSize = true;
            this.CheckBox_Save_session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.BorderSize = 1;
            this.CheckBox_Save_session.Check = true;
            this.CheckBox_Save_session.Checked = true;
            this.CheckBox_Save_session.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_session.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_session.Customizable = false;
            this.CheckBox_Save_session.Enabled = false;
            this.CheckBox_Save_session.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_session.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_session.Location = new System.Drawing.Point(249, 36);
            this.CheckBox_Save_session.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_session.Name = "CheckBox_Save_session";
            this.CheckBox_Save_session.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_session.Size = new System.Drawing.Size(164, 21);
            this.CheckBox_Save_session.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_session.TabIndex = 13;
            this.CheckBox_Save_session.Text = "حفظ  جلسات الاستخدام";
            this.CheckBox_Save_session.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_session.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_download
            // 
            this.CheckBox_Save_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_download.AutoSize = true;
            this.CheckBox_Save_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.BorderSize = 1;
            this.CheckBox_Save_download.Check = true;
            this.CheckBox_Save_download.Checked = true;
            this.CheckBox_Save_download.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_download.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_download.Customizable = false;
            this.CheckBox_Save_download.Enabled = false;
            this.CheckBox_Save_download.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_download.Location = new System.Drawing.Point(437, 37);
            this.CheckBox_Save_download.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_download.Name = "CheckBox_Save_download";
            this.CheckBox_Save_download.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_download.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_download.Size = new System.Drawing.Size(167, 21);
            this.CheckBox_Save_download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_download.TabIndex = 12;
            this.CheckBox_Save_download.Text = "حفظ الاستهلاك والتنزيل";
            this.CheckBox_Save_download.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_download.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_time
            // 
            this.CheckBox_Save_time.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_time.AutoSize = true;
            this.CheckBox_Save_time.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.BorderSize = 1;
            this.CheckBox_Save_time.Check = true;
            this.CheckBox_Save_time.Checked = true;
            this.CheckBox_Save_time.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_time.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_time.Customizable = false;
            this.CheckBox_Save_time.Enabled = false;
            this.CheckBox_Save_time.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_time.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_time.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_time.Location = new System.Drawing.Point(620, 40);
            this.CheckBox_Save_time.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_time.Name = "CheckBox_Save_time";
            this.CheckBox_Save_time.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_time.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_time.Size = new System.Drawing.Size(273, 21);
            this.CheckBox_Save_time.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_time.TabIndex = 11;
            this.CheckBox_Save_time.Text = "حفظ الوقت في حال الانطفاء المفاجئ للروتر";
            this.CheckBox_Save_time.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_time.UseVisualStyleBackColor = true;
            // 
            // CheckBox_SmartScript
            // 
            this.CheckBox_SmartScript.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_SmartScript.AutoSize = true;
            this.CheckBox_SmartScript.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CheckBox_SmartScript.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SmartScript.BorderSize = 1;
            this.CheckBox_SmartScript.Check = true;
            this.CheckBox_SmartScript.Checked = true;
            this.CheckBox_SmartScript.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_SmartScript.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SmartScript.Customizable = false;
            this.CheckBox_SmartScript.Enabled = false;
            this.CheckBox_SmartScript.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_SmartScript.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SmartScript.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SmartScript.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_SmartScript.Location = new System.Drawing.Point(734, 346);
            this.CheckBox_SmartScript.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SmartScript.Name = "CheckBox_SmartScript";
            this.CheckBox_SmartScript.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_SmartScript.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_SmartScript.Size = new System.Drawing.Size(170, 21);
            this.CheckBox_SmartScript.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SmartScript.TabIndex = 129;
            this.CheckBox_SmartScript.Text = "استخدام نظام الصلاحيات";
            this.CheckBox_SmartScript.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_SmartScript.UseVisualStyleBackColor = false;
            // 
            // Toggle_Bind_Mac
            // 
            this.Toggle_Bind_Mac.Activated = false;
            this.Toggle_Bind_Mac.CheckAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.Toggle_Bind_Mac.Customizable = false;
            this.Toggle_Bind_Mac.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_Bind_Mac.Location = new System.Drawing.Point(193, 27);
            this.Toggle_Bind_Mac.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.Toggle_Bind_Mac.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Bind_Mac.Name = "Toggle_Bind_Mac";
            this.Toggle_Bind_Mac.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Bind_Mac.OFF_Text = "ربط بالماك";
            this.Toggle_Bind_Mac.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Bind_Mac.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Bind_Mac.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Bind_Mac.ON_Text = "ربط بالماك";
            this.Toggle_Bind_Mac.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Bind_Mac.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Bind_Mac.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Toggle_Bind_Mac.Size = new System.Drawing.Size(104, 29);
            this.Toggle_Bind_Mac.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Bind_Mac.TabIndex = 89;
            this.Toggle_Bind_Mac.Tag = "تفصيلي";
            this.Toggle_Bind_Mac.Text = "#";
            this.Toggle_Bind_Mac.ThreeState = true;
            this.Toggle_Bind_Mac.UseVisualStyleBackColor = true;
            this.Toggle_Bind_Mac.Visible = false;
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(73, 61);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(82, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 90;
            this.rjLabel4.Text = "ربــــــــط بالمــاك";
            this.rjLabel4.Visible = false;
            // 
            // txt_CountSession
            // 
            this.txt_CountSession._Customizable = false;
            this.txt_CountSession.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_CountSession.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_CountSession.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_CountSession.BorderRadius = 8;
            this.txt_CountSession.BorderSize = 1;
            this.txt_CountSession.Enabled = false;
            this.txt_CountSession.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_CountSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_CountSession.Location = new System.Drawing.Point(38, 300);
            this.txt_CountSession.MultiLine = false;
            this.txt_CountSession.Name = "txt_CountSession";
            this.txt_CountSession.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_CountSession.PasswordChar = false;
            this.txt_CountSession.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_CountSession.PlaceHolderText = null;
            this.txt_CountSession.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_CountSession.Size = new System.Drawing.Size(186, 29);
            this.txt_CountSession.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_CountSession.TabIndex = 58;
            this.txt_CountSession.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_TotalUptime
            // 
            this.txt_TotalUptime._Customizable = false;
            this.txt_TotalUptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalUptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalUptime.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalUptime.BorderRadius = 8;
            this.txt_TotalUptime.BorderSize = 1;
            this.txt_TotalUptime.Enabled = false;
            this.txt_TotalUptime.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalUptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalUptime.Location = new System.Drawing.Point(681, 300);
            this.txt_TotalUptime.MultiLine = false;
            this.txt_TotalUptime.Name = "txt_TotalUptime";
            this.txt_TotalUptime.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalUptime.PasswordChar = false;
            this.txt_TotalUptime.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalUptime.PlaceHolderText = null;
            this.txt_TotalUptime.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalUptime.Size = new System.Drawing.Size(186, 29);
            this.txt_TotalUptime.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalUptime.TabIndex = 60;
            this.txt_TotalUptime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_TotalDownload
            // 
            this.txt_TotalDownload._Customizable = false;
            this.txt_TotalDownload.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalDownload.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalDownload.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalDownload.BorderRadius = 8;
            this.txt_TotalDownload.BorderSize = 1;
            this.txt_TotalDownload.Enabled = false;
            this.txt_TotalDownload.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalDownload.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalDownload.Location = new System.Drawing.Point(378, 300);
            this.txt_TotalDownload.MultiLine = false;
            this.txt_TotalDownload.Name = "txt_TotalDownload";
            this.txt_TotalDownload.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalDownload.PasswordChar = false;
            this.txt_TotalDownload.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalDownload.PlaceHolderText = null;
            this.txt_TotalDownload.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalDownload.Size = new System.Drawing.Size(186, 29);
            this.txt_TotalDownload.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalDownload.TabIndex = 61;
            this.txt_TotalDownload.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel7
            // 
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(388, 281);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(165, 17);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 55;
            this.rjLabel7.Text = "اجمالي تحميل+الرقع المستخدم";
            // 
            // rjLabel6
            // 
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(711, 281);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(134, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 56;
            this.rjLabel6.Text = "اجمالي الوقت المستخدم";
            // 
            // rjLabel5
            // 
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(88, 281);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(90, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 57;
            this.rjLabel5.Text = "اجمالي الجلسات";
            // 
            // lbl_note
            // 
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(20, 560);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(333, 17);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 65;
            this.lbl_note.Text = "* اضغط بزر الماوس الايمن علي اي جدول لاضهار الخيارات المتاحة";
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.rjLabel21);
            this.rjPanel2.Controls.Add(this.rjLabel22);
            this.rjPanel2.Controls.Add(this.txt_TransferLimit);
            this.rjPanel2.Controls.Add(this.rjLabel14);
            this.rjPanel2.Controls.Add(this.txt_uptimeLimit);
            this.rjPanel2.Controls.Add(this.rjLabel3);
            this.rjPanel2.Controls.Add(this.Radio_Baisc);
            this.rjPanel2.Controls.Add(this.Radio_WithSession);
            this.rjPanel2.Controls.Add(this.Toggle_Bind_Mac);
            this.rjPanel2.Controls.Add(this.rjLabel4);
            this.rjPanel2.Controls.Add(this.dgv_Sessions);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(12, 417);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(900, 140);
            this.rjPanel2.TabIndex = 64;
            // 
            // dgv_Sessions
            // 
            this.dgv_Sessions.AllowUserToAddRows = false;
            this.dgv_Sessions.AllowUserToDeleteRows = false;
            this.dgv_Sessions.AllowUserToOrderColumns = true;
            this.dgv_Sessions.AllowUserToResizeRows = false;
            this.dgv_Sessions.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_Sessions.AlternatingRowsColorApply = false;
            this.dgv_Sessions.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_Sessions.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_Sessions.BorderRadius = 13;
            this.dgv_Sessions.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_Sessions.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_Sessions.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_Sessions.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_Sessions.ColumnHeaderHeight = 40;
            this.dgv_Sessions.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv_Sessions.ColumnHeadersHeight = 40;
            this.dgv_Sessions.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_Sessions.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_Sessions.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv_Sessions.ContextMenuStrip = this.dm_Session;
            this.dgv_Sessions.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv_Sessions.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_Sessions.EnableHeadersVisualStyles = false;
            this.dgv_Sessions.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_Sessions.Location = new System.Drawing.Point(14, 10);
            this.dgv_Sessions.Name = "dgv_Sessions";
            this.dgv_Sessions.ReadOnly = true;
            this.dgv_Sessions.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_Sessions.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_Sessions.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv_Sessions.RowHeadersVisible = false;
            this.dgv_Sessions.RowHeadersWidth = 35;
            this.dgv_Sessions.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_Sessions.RowHeight = 33;
            this.dgv_Sessions.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv_Sessions.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.RowTemplate.Height = 33;
            this.dgv_Sessions.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_Sessions.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_Sessions.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.Size = new System.Drawing.Size(873, 122);
            this.dgv_Sessions.TabIndex = 52;
            // 
            // dm_Session
            // 
            this.dm_Session.ActiveMenuItem = false;
            this.dm_Session.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dm_Session.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dm_Session.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ترتيبالاعمدةToolStripMenuItem,
            this.حذفجميعجلساتالكرتToolStripMenuItem,
            this.تحديثالجلساتمنالروترToolStripMenuItem,
            this.نسخToolStripMenuItem,
            this.نسخالسطركاملToolStripMenuItem});
            this.dm_Session.Name = "dm_Session";
            this.dm_Session.OwnerIsMenuButton = false;
            this.dm_Session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dm_Session.Size = new System.Drawing.Size(199, 114);
            // 
            // ترتيبالاعمدةToolStripMenuItem
            // 
            this.ترتيبالاعمدةToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.حسبتاريخبدايةالجلسةToolStripMenuItem,
            this.حسبتاريخنهايةالجلسةToolStripMenuItem,
            this.حسبالوقتالمستخدمToolStripMenuItem,
            this.حسبالتحميلالمستخدمToolStripMenuItem,
            this.حسبالرفعالمستخدمToolStripMenuItem,
            this.حسبايبيالجلسهIPToolStripMenuItem,
            this.ترتيببحسبالماكToolStripMenuItem,
            this.ترتيببحسبالبورتالجهازToolStripMenuItem,
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem});
            this.ترتيبالاعمدةToolStripMenuItem.Name = "ترتيبالاعمدةToolStripMenuItem";
            this.ترتيبالاعمدةToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.ترتيبالاعمدةToolStripMenuItem.Text = "ترتيب الجلسات ";
            // 
            // حسبتاريخبدايةالجلسةToolStripMenuItem
            // 
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Name = "حسبتاريخبدايةالجلسةToolStripMenuItem";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Tag = "FromTime";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Text = "حسب تاريخ بداية الجلسة";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حسبتاريخنهايةالجلسةToolStripMenuItem
            // 
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Name = "حسبتاريخنهايةالجلسةToolStripMenuItem";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Tag = "TillTime";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Text = "حسب تاريخ نهاية الجلسة";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حسبالوقتالمستخدمToolStripMenuItem
            // 
            this.حسبالوقتالمستخدمToolStripMenuItem.Name = "حسبالوقتالمستخدمToolStripMenuItem";
            this.حسبالوقتالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالوقتالمستخدمToolStripMenuItem.Tag = "UpTime";
            this.حسبالوقتالمستخدمToolStripMenuItem.Text = "حسب الوقت المستخدم";
            this.حسبالوقتالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حسبالتحميلالمستخدمToolStripMenuItem
            // 
            this.حسبالتحميلالمستخدمToolStripMenuItem.Name = "حسبالتحميلالمستخدمToolStripMenuItem";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالتحميلالمستخدمToolStripMenuItem.Tag = "BytesDownload";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Text = "حسب التحميل المستخدم";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حسبالرفعالمستخدمToolStripMenuItem
            // 
            this.حسبالرفعالمستخدمToolStripMenuItem.Name = "حسبالرفعالمستخدمToolStripMenuItem";
            this.حسبالرفعالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالرفعالمستخدمToolStripMenuItem.Tag = "BytesUpload";
            this.حسبالرفعالمستخدمToolStripMenuItem.Text = "حسب الرفع المستخدم";
            this.حسبالرفعالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حسبايبيالجلسهIPToolStripMenuItem
            // 
            this.حسبايبيالجلسهIPToolStripMenuItem.Name = "حسبايبيالجلسهIPToolStripMenuItem";
            this.حسبايبيالجلسهIPToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبايبيالجلسهIPToolStripMenuItem.Tag = "IpUser";
            this.حسبايبيالجلسهIPToolStripMenuItem.Text = "حسب اي بي الجلسه(IP)";
            this.حسبايبيالجلسهIPToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // ترتيببحسبالماكToolStripMenuItem
            // 
            this.ترتيببحسبالماكToolStripMenuItem.Name = "ترتيببحسبالماكToolStripMenuItem";
            this.ترتيببحسبالماكToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالماكToolStripMenuItem.Tag = "CallingStationId";
            this.ترتيببحسبالماكToolStripMenuItem.Text = "ترتيب بحسب الماك";
            this.ترتيببحسبالماكToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // ترتيببحسبالبورتالجهازToolStripMenuItem
            // 
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Name = "ترتيببحسبالبورتالجهازToolStripMenuItem";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Tag = "NasPortId";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Text = "ترتيب بحسب البورت(الجهاز)";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // ترتيببحسبالسيرفرراديوسToolStripMenuItem
            // 
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Name = "ترتيببحسبالسيرفرراديوسToolStripMenuItem";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Tag = "IpRouter";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Text = "ترتيب بحسب السيرفر(راديوس)";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Visible = false;
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Click += new System.EventHandler(this.حسبتاريخبدايةالجلسةToolStripMenuItem_Click);
            // 
            // حذفجميعجلساتالكرتToolStripMenuItem
            // 
            this.حذفجميعجلساتالكرتToolStripMenuItem.Name = "حذفجميعجلساتالكرتToolStripMenuItem";
            this.حذفجميعجلساتالكرتToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.حذفجميعجلساتالكرتToolStripMenuItem.Text = "حذف جميع جلسات الكرت";
            this.حذفجميعجلساتالكرتToolStripMenuItem.Visible = false;
            this.حذفجميعجلساتالكرتToolStripMenuItem.Click += new System.EventHandler(this.حذفجميعجلساتالكرتToolStripMenuItem_Click);
            // 
            // تحديثالجلساتمنالروترToolStripMenuItem
            // 
            this.تحديثالجلساتمنالروترToolStripMenuItem.Name = "تحديثالجلساتمنالروترToolStripMenuItem";
            this.تحديثالجلساتمنالروترToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.تحديثالجلساتمنالروترToolStripMenuItem.Text = "تحديث الجلسات من الروتر";
            this.تحديثالجلساتمنالروترToolStripMenuItem.Visible = false;
            this.تحديثالجلساتمنالروترToolStripMenuItem.Click += new System.EventHandler(this.تحديثالجلساتمنالروترToolStripMenuItem_Click);
            // 
            // نسخToolStripMenuItem
            // 
            this.نسخToolStripMenuItem.Name = "نسخToolStripMenuItem";
            this.نسخToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.نسخToolStripMenuItem.Text = "نسخ الخلية المحدده";
            this.نسخToolStripMenuItem.Click += new System.EventHandler(this.نسخToolStripMenuItem_Click);
            // 
            // نسخالسطركاملToolStripMenuItem
            // 
            this.نسخالسطركاملToolStripMenuItem.Name = "نسخالسطركاملToolStripMenuItem";
            this.نسخالسطركاملToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.نسخالسطركاملToolStripMenuItem.Text = "نسخ السطر كامل";
            this.نسخالسطركاملToolStripMenuItem.Click += new System.EventHandler(this.نسخالسطركاملToolStripMenuItem_Click);
            // 
            // Form_CardsDetailsHS
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.BorderSize = 5;
            this.Caption = "Form_CardsDetailsHS";
            this.ClientSize = new System.Drawing.Size(931, 631);
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.Name = "Form_CardsDetailsHS";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Resizable = false;
            this.Text = "Form_CardsDetailsHS";
            this.Load += new System.EventHandler(this.Form_CardsDetailsHS_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_profiles)).EndInit();
            this.dm_profile.ResumeLayout(false);
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Sessions)).EndInit();
            this.dm_Session.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel1;
        public RJControls.RJTextBox txt_TransferLeft;
        public RJControls.RJButton btnSave;
        public RJControls.RJTextBox txt_TimeLeft;
        private RJControls.RJToggleButton Toggle_Bind_Mac;
        private RJControls.RJLabel rjLabel4;
        public RJControls.RJTextBox txt_mac;
        public RJControls.RJTextBox txt_TransferLimit;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJLabel rjLabel2;
        public RJControls.RJTextBox txt_uptimeLimit;
        private RJControls.RJToggleButton Toggle_Status;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJLabel rjLabel13;
        private RJControls.RJLabel rjLabel3;
        public RJControls.RJTextBox txt_Till_Date;
        public RJControls.RJTextBox txt_RegDate;
        public RJControls.RJTextBox txt_Password;
        private RJControls.RJLabel rjLabel12;
        public RJControls.RJTextBox txt_username;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel lbl_username;
        private RJControls.RJRadioButton Radio_Baisc;
        private RJControls.RJRadioButton Radio_WithSession;
        private RJControls.RJTextBox txt_CountSession;
        public RJControls.RJButton btn_Refresh;
        private RJControls.RJTextBox txt_TotalUptime;
        private RJControls.RJTextBox txt_TotalDownload;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJLabel lbl_note;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJDataGridView dgv_Sessions;
        private System.Windows.Forms.GroupBox groupBox1;
        private RJControls.RJCheckBox CheckBox_byDayOrHour;
        private RJControls.RJCheckBox CheckBox_Save_session;
        private RJControls.RJCheckBox CheckBox_Save_download;
        private RJControls.RJCheckBox CheckBox_SmartScript;
        private RJControls.RJCheckBox CheckBox_Save_time;
        private RJControls.RJDropdownMenu dm_profile;
        private System.Windows.Forms.ToolStripMenuItem اضافةباقةجديدةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RemoveProfile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تحديثباقاتالكرتمنالروترToolStripMenuItem;
        private RJControls.RJDropdownMenu dm_Session;
        private System.Windows.Forms.ToolStripMenuItem ترتيبالاعمدةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبتاريخبدايةالجلسةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبتاريخنهايةالجلسةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالوقتالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالتحميلالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالرفعالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبايبيالجلسهIPToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالماكToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالبورتالجهازToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالسيرفرراديوسToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حذفجميعجلساتالكرتToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تحديثالجلساتمنالروترToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخالسطركاملToolStripMenuItem;
        private System.Windows.Forms.ToolTip toolTip1;
        public RJControls.RJButton btnAddProfile;
        public RJControls.RJTextBox txt_Real_TransferLimit;
        public RJControls.RJTextBox txt_Real_uptimeLimit;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJLabel rjLabel9;
        public RJControls.RJTextBox txt_validay;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJDataGridView dgv_profiles;
        private RJControls.RJTextBox txt_TotalPrice;
        private RJControls.RJTextBox txt_CountProfile;
        private RJControls.RJLabel rjLabel18;
        private RJControls.RJLabel rjLabel19;
        private RJControls.RJLabel rjLabel20;
        private RJControls.RJLabel rjLabel21;
        private RJControls.RJLabel rjLabel22;
        public RJControls.RJButton btn_addSmartValidity;
    }
}