using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// صفحة تاب مبسطة للـ Designer - بدون وراثة معقدة
    /// </summary>
    [ToolboxItem(false)]
    public class SimpleTabPage : Button
    {
        #region Fields
        private Panel _contentPanel;
        private string _tabText = "TabPage";
        private IconChar _iconChar = IconChar.None;
        private int _iconSize = 16;
        #endregion

        #region Constructor
        public SimpleTabPage()
        {
            // إعداد Button كتاب
            this.FlatStyle = FlatStyle.Flat;
            this.FlatAppearance.BorderSize = 0;
            this.BackColor = Color.FromArgb(70, 70, 70);
            this.ForeColor = Color.White;
            this.Font = new Font("Segoe UI", 9, FontStyle.Regular);
            this.Size = new Size(100, 30);
            
            // إنشاء Panel للمحتوى
            _contentPanel = new Panel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.White,
                Visible = false
            };
        }

        public SimpleTabPage(string text) : this()
        {
            _tabText = text;
            this.Text = text;
        }

        public SimpleTabPage(string text, IconChar icon) : this(text)
        {
            _iconChar = icon;
        }
        #endregion

        #region Properties
        /// <summary>
        /// نص التاب
        /// </summary>
        [Category("Simple Tab")]
        [Description("Text displayed on the tab")]
        public new string Text
        {
            get { return _tabText; }
            set
            {
                _tabText = value;
                base.Text = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// أيقونة التاب
        /// </summary>
        [Category("Simple Tab")]
        [Description("Icon displayed on the tab")]
        public IconChar IconChar
        {
            get { return _iconChar; }
            set
            {
                _iconChar = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// حجم الأيقونة
        /// </summary>
        [Category("Simple Tab")]
        [Description("Size of the icon")]
        [DefaultValue(16)]
        public int IconSize
        {
            get { return _iconSize; }
            set
            {
                _iconSize = value;
                UpdateDisplay();
            }
        }

        /// <summary>
        /// Panel المحتوى
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public Panel ContentPanel
        {
            get { return _contentPanel; }
        }
        #endregion

        #region Methods
        /// <summary>
        /// تحديث عرض التاب
        /// </summary>
        private void UpdateDisplay()
        {
            try
            {
                // تحديث النص
                base.Text = _tabText;

                // تحديث الأيقونة (مبسط)
                if (_iconChar != IconChar.None)
                {
                    // يمكن إضافة منطق الأيقونة هنا إذا لزم الأمر
                    base.Text = $"{_tabText}"; // مبسط بدون أيقونة فعلية
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إضافة كنترول للمحتوى
        /// </summary>
        public void AddControl(Control control)
        {
            try
            {
                if (control != null && _contentPanel != null)
                {
                    _contentPanel.Controls.Add(control);
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// مسح محتوى التاب
        /// </summary>
        public void ClearContent()
        {
            try
            {
                _contentPanel?.Controls.Clear();
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion

        #region Override
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    _contentPanel?.Dispose();
                }
                base.Dispose(disposing);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion
    }
}
