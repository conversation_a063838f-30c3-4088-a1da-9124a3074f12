using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Direct database context for ORM-less database access
    /// </summary>
    public class DirectDbContext : IDisposable
    {
        private readonly IDbConnection _connection;
        private readonly bool _ownsConnection;
        public static Action<string>? SqlLogger { get; set; }

        /// <summary>
        /// Constructor with connection string
        /// </summary>
        public DirectDbContext(string connectionString)
        {
            _connection = new SQLiteConnection(connectionString);
            _connection.Open();
            _ownsConnection = true;
        }

        /// <summary>
        /// Constructor with existing connection
        /// </summary>
        public DirectDbContext(IDbConnection connection)
        {
            _connection = connection;
            _ownsConnection = false;
        }

        /// <summary>
        /// Get the underlying database connection
        /// </summary>
        public IDbConnection Connection => _connection;

        /// <summary>
        /// Select records using lambda expression
        /// </summary>
        public List<T> Select<T>(System.Linq.Expressions.Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            return _connection.Select(predicate);
        }

        /// <summary>
        /// Get single record
        /// </summary>
        public T Single<T>(System.Linq.Expressions.Expression<Func<T, bool>> predicate) where T : class, new()
        {
            return _connection.Single(predicate);
        }

        /// <summary>
        /// Get single record by ID
        /// </summary>
        public T SingleById<T>(object id) where T : class, new()
        {
            return _connection.SingleById<T>(id);
        }

        /// <summary>
        /// Get first record or default
        /// </summary>
        public T? FirstOrDefault<T>(System.Linq.Expressions.Expression<Func<T, bool>> predicate) where T : class, new()
        {
            return _connection.FirstOrDefault(predicate);
        }

        /// <summary>
        /// Count records
        /// </summary>
        public int Count<T>(System.Linq.Expressions.Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            return _connection.Count(predicate);
        }

        /// <summary>
        /// Check if records exist
        /// </summary>
        public bool Exists<T>(System.Linq.Expressions.Expression<Func<T, bool>> predicate) where T : class, new()
        {
            return _connection.Exists(predicate);
        }

        /// <summary>
        /// Get scalar value
        /// </summary>
        public TResult Scalar<T, TResult>(System.Linq.Expressions.Expression<Func<T, TResult>> selector, System.Linq.Expressions.Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            return _connection.Scalar(selector, predicate);
        }

        /// <summary>
        /// Get scalar value with SqlExpression
        /// </summary>
        public TResult Scalar<TResult>(SqlExpressionResult<TResult> expression)
        {
            return _connection.Scalar(expression);
        }

        /// <summary>
        /// Start building SqlExpression
        /// </summary>
        public SqlExpression<T> From<T>() where T : class, new()
        {
            return _connection.From<T>();
        }

        /// <summary>
        /// Get column values
        /// </summary>
        public List<TColumn> Column<TColumn>(SqlExpression expression) where TColumn : new()
        {
            return _connection.Column<TColumn>(expression.ToSql());
        }

        /// <summary>
        /// Get distinct column values
        /// </summary>
        public HashSet<TColumn> ColumnDistinct<TColumn>(SqlExpression expression) where TColumn : new()
        {
            return _connection.ColumnDistinct<TColumn>(expression.ToSql());
        }

        /// <summary>
        /// Get dictionary from query
        /// </summary>
        public Dictionary<TKey, TValue> Dictionary<TKey, TValue>(SqlExpression expression) where TKey : notnull
        {
            return _connection.Dictionary<TKey, TValue>(expression.ToSql());
        }

        /// <summary>
        /// Get lookup (one-to-many dictionary)
        /// </summary>
        public Dictionary<TKey, List<TValue>> Lookup<TKey, TValue>(SqlExpression expression) where TKey : notnull
        {
            return _connection.Lookup<TKey, TValue>(expression.ToSql());
        }

        /// <summary>
        /// Insert record
        /// </summary>
        public void Insert<T>(T entity) where T : class
        {
            _connection.Insert(entity);
        }

        /// <summary>
        /// Update record
        /// </summary>
        public void Update<T>(T entity) where T : class
        {
            _connection.Update(entity);
        }

        /// <summary>
        /// Delete records
        /// </summary>
        public int Delete<T>(System.Linq.Expressions.Expression<Func<T, bool>> predicate) where T : class, new()
        {
            return _connection.Delete(predicate);
        }

        /// <summary>
        /// Execute raw SQL query
        /// </summary>
        public List<T> Query<T>(string sql, object? parameters = null) where T : new()
        {
            using var command = _connection.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            var results = new List<T>();
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var item = SqlHelper.MapFromReader<T>(reader);
                results.Add(item);
            }
            return results;
        }

        /// <summary>
        /// Execute raw SQL command
        /// </summary>
        public int Execute(string sql, object? parameters = null)
        {
            using var command = _connection.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            return command.ExecuteNonQuery();
        }

        /// <summary>
        /// Execute scalar query
        /// </summary>
        public T QueryScalar<T>(string sql, object? parameters = null)
        {
            using var command = _connection.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            var result = command.ExecuteScalar();
            return (T)Convert.ChangeType(result!, typeof(T));
        }

        /// <summary>
        /// Begin transaction
        /// </summary>
        public IDbTransaction BeginTransaction()
        {
            return _connection.BeginTransaction();
        }

        /// <summary>
        /// Dispose resources
        /// </summary>
        public void Dispose()
        {
            if (_ownsConnection)
            {
                _connection?.Dispose();
            }
        }
    }
}
