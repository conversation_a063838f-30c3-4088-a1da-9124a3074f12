﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using SmartCreator.Models;
using SmartCreator.Data;
using SmartCreator.Utils;
using SmartCreator.Forms.UserManager;
using System.Windows.Forms;
using System.IO;
using System.Threading;
using Newtonsoft.Json;
using Renci.SshNet;
using SmartCreator.Entities.UserManager;
using SmartCreator.Entities;
using SmartCreator.Forms;
using SmartCreator.Entities.Hotspot;
using Dapper;
using SmartCreator.Forms.CardsDesigen;
using Org.BouncyCastle.Asn1.Cms;
using SmartCreator.RJForms;
using SmartCreator.TestAndDemo;

namespace SmartCreator.ViewModels
{
    public class UserManagerProcess
    {
        public bool startPrint = false;
        public bool is_Add_One_Card = false;
        public bool is_add_batch_cards = false;
        public bool is_add_batch_cards_to_Archive = false;
        public bool is_rest_sn = false;
        public int inext = 0;

        public FormAddUsersManager frm = null;
        public Form_PrintUserManagerState Frm_State = null;
        Dictionary<string, object> info_print2;
        public Clss_InfoPrint clss_InfoPrint;
        Random rndU;

        public string Public_file_Name = "";
        public string pathfile = "";
        //private Sql_DataAccess sql_DataAccess= null;
        private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA= null;
        List<Comm_SellingPoint> comm_Sellings = new List<Comm_SellingPoint>();

        public UserManagerProcess() { Local_DA = new Sql_DataAccess(); Smart_DA = new Smart_DataAccess();comm_Sellings = new List<Comm_SellingPoint>(); comm_Sellings = Smart_DA.Load<Comm_SellingPoint>($"select * from Comm_SellingPoint where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'  "); }
        public UserManagerProcess(FormAddUsersManager _frm, Form_PrintUserManagerState _Frm_State)
        {
            frm = _frm;
            Frm_State = _Frm_State;
            is_add_batch_cards=_Frm_State.is_add_batch_cards;
            is_add_batch_cards_to_Archive=Frm_State.is_add_batch_cards_to_Archive;
            is_Add_One_Card = _Frm_State.is_Add_One_Card;

            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();
            comm_Sellings = new List<Comm_SellingPoint>();
            comm_Sellings = Smart_DA.Load<Comm_SellingPoint>($"select * from Comm_SellingPoint where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ");
        }
      
        public void Syn_UM_Users_to_LocalDB2(List<SourceCardsUserManager_fromMK> users = null)
        {
            bool by_search = true;
            double date_added_Localdb = utils.DateTimeToUnixTimeStamp(DateTime.Now);
            List<SellingPoint> sp = new List<SellingPoint>();
            List<UmProfile> profile = Global_Variable.UM_Profile;

            if (users == null)
            {
                users = Global_Variable.Source_Users_UserManager;
                by_search = false;
            }
            var UM_Users = (from u in users.AsEnumerable()
                            select new SourceCardsUserManager_fromDB
                            {
                                idHX = u.id,
                                sn = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                sn_userName = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + u.userName,
                                cusName = u.cusName,
                                userName = u.userName,
                                password = u.password,
                                disabled = Convert.ToInt16(Convert.ToBoolean(u.disabled)),
                                firstName = u.firstName,
                                lastName = u.lastName,
                                descr = u.descr,
                                phone = u.phone,
                                location = u.location,
                                email = u.email,
                                callerId = u.callerId,
                                uptimeUsed = utils.GetTimeCard_InSeconds(u.uptimeUsed),
                                downloadUsed = Convert.ToDouble(u.downloadUsed),
                                uploadUsed = Convert.ToDouble(u.uploadUsed),
                                lastSeenAt = (u.lastSeenAt != null && u.lastSeenAt != "never") ? utils.StringDatetimeToUnixTimeStamp(u.lastSeenAt) : 0,
                                activeSessions = u.activeSessions,
                                sharedUsers = u.sharedUsers,
                                spId = Convert.ToInt32((from v in sp.AsEnumerable() where (v.Code == u.location) select v.Code).FirstOrDefault() ?? null),
                                //spId = (int)_spId.FirstOrDefault() ?? null,
                                spName = (from v in sp.AsEnumerable() where (v.Code == u.location) select v.UserName).FirstOrDefault() ?? "",
                                //spPercentage = (from v in sp.AsEnumerable() where (v.Code == u.location) select v.sp_percentage).FirstOrDefault(),
                                //is_spPercentage = (from v in sp.AsEnumerable() where (v.Code == u.location) select v.is_percentage).FirstOrDefault(),
                                date_added_Localdb = date_added_Localdb,
                                Delet_fromServer = 0,
                                //=========================
                                actualProfileName = u.actualProfileName,

                                //status = (((u.actualProfileName == null || u.actualProfileName == "") && Convert.ToDouble(u.downloadUsed) > 0) ? "انتهى الرصيد" :
                                //(((u.actualProfileName != null && u.actualProfileName != "") && Convert.ToDouble(u.downloadUsed) == 0) ? "انتظار" :
                                //(((u.actualProfileName != null && u.actualProfileName != "") && Convert.ToDouble(u.downloadUsed) > 0) ? "نشط" :
                                //(((u.actualProfileName == null || u.actualProfileName == "") && Convert.ToDouble(u.downloadUsed) == 0) ? "خطأ في الباقة" : ""
                                //)))
                                //),

                                //actualLimDownload=0,
                                //actualLimUpload = 0,

                                actualLimTransfer = u.actualProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.TransferLimit).LastOrDefault(),
                                actualLimUptime = u.actualProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.UptimeLimit).LastOrDefault(),

                                //profileTillTime = 0,
                                //profileTimeLeft = u.actualProfileName == null ? 0 : (((from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.uptimeLimit).LastOrDefault()) - (utils.GetTimeCard_InSeconds(u.uptimeUsed))),

                            }).ToList();

            SqlDataAccess.Add_UM_user_to_LocalDB_sqlite2(UM_Users);
            List<SourceCardsUserManager_fromDB> sourceCardsUsers = SqlDataAccess.GetUsersManager_NotDeleteFromServer();
            // set  Delet_fromServer=1 

            if (by_search == false)
                SqlDataAccess.Set_Users_disable_LocalDB();

            // inner join  becaus  عشان نظمن ان كل عنصر معه الايدي حق قاعدة البيانات تبعه
            var um = (from u in UM_Users
                      join s in sourceCardsUsers on u.sn_userName equals s.sn_userName
                      where u.sn_userName == s.sn_userName
                      select new SourceCardsUserManager_fromDB
                      {
                          id = s.id,
                          disabled = u.disabled,
                          firstName = u.firstName,
                          lastName = u.lastName,
                          descr = u.descr,
                          phone = u.phone,
                          location = u.location,
                          email = u.email,
                          callerId = u.callerId,
                          uptimeUsed = u.uptimeUsed,
                          downloadUsed = u.downloadUsed,
                          uploadUsed = u.uploadUsed,
                          lastSeenAt = u.lastSeenAt,
                          activeSessions = u.activeSessions,
                          sharedUsers = u.sharedUsers,
                          spId = s.spId != null ? s.spId : u.spId,
                          spName = u.spName,
                          spPercentage = s.spPercentage != 0 ? s.spPercentage : s.spPercentage,
                          is_spPercentage = s.is_spPercentage != 0 ? s.is_spPercentage : s.is_spPercentage,
                          //spPercentage = u.spPercentage,
                          //is_spPercentage = u.is_spPercentage,
                          status =
                                (((u.actualProfileName == null || u.actualProfileName == "") && (u.downloadUsed) > 0) ? 2 ://"انتهى الرصيد" :
                                (((u.actualProfileName != null && u.actualProfileName != "") && (u.downloadUsed) == 0) ? 0 :  //"انتظار"
                                (((u.actualProfileName != null && u.actualProfileName != "") && (u.downloadUsed) > 0) ? 1 : // "نشط" :
                                (((u.actualProfileName == null || u.actualProfileName == "") && (u.downloadUsed) == 0) ? 3 : 5 //"خطأ في الباقة" :
                                )))
                                ),
                          Delet_fromServer = 0,
                          //=========================
                          actualProfileName = u.actualProfileName == "" || u.actualProfileName == null ? s.actualProfileName : u.actualProfileName,

                          //actualProfileName = u.actualProfileName,
                          //actualLimDownload=0,
                          //actualLimUpload = 0,
                          //actualLimTransfer = u.actualLimTransfer,
                          //actualLimUptime = u.actualLimUptime,
                          //profileTillTime = 0,
                          //profileTimeLeft = u.profileTimeLeft,
                      }).ToList();
            if (um.Count > 0)
                SqlDataAccess.Update_UM_user_to_LocalDB_sqlite2(um);
            else if (UM_Users.Count > 0)
                SqlDataAccess.Update_UM_user_to_LocalDB_sqlite2(UM_Users, false);




            //Syn_UM_Users_to_LocalDB_v2();

        }
        
        public void Syn_UM_Users_to_LocalDB(List<SourceCardsUserManager_fromMK> users = null)
        {
            if(Global_Variable.Mk_resources.version>=7)
            {
                Syn_UM_Users_to_LocalDB_V7(users);
                return;
            }
            bool by_search = true;
            List<SellingPoint> sp = new List<SellingPoint>();
            List<UmProfile> profile = Global_Variable.UM_Profile;

            if (users == null)
            {
                users = Global_Variable.Source_Users_UserManager;
                by_search = false;
            }
            var umUser = (from u in users.AsEnumerable()
                            select new UmUser
                            {
                                IdHX = u.id,
                                SN = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                Sn_Name = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + u.userName,
                                CustomerName = u.cusName,
                                UserName = u.userName,
                                Password = u.password,
                                Disabled = Convert.ToInt16(Convert.ToBoolean(u.disabled)),
                                FirstName = u.firstName,
                                LastName = u.lastName,
                                Comment = u.descr,
                                Phone = u.phone,
                                Location = u.location,
                                Email = u.email,
                                CallerMac = u.callerId,
                                UptimeUsed = utils.GetTimeCard_InSeconds(u.uptimeUsed),
                                DownloadUsed = long.Parse(u.downloadUsed),
                                //DownloadUsed = (long)Convert.ToDouble(u.downloadUsed),
                                UploadUsed = long.Parse(u.uploadUsed),
                                //UploadUsed = (long)Convert.ToDouble(u.uploadUsed),
                                LastSeenAt = utils.String_To_Datetime_By_V_MK(u.lastSeenAt),
                                //LastSeenAt = (u.lastSeenAt != null && u.lastSeenAt != "never") ? utils.String_To_Datetime_By_V_MK(u.lastSeenAt) : DateTime.Now,
                                //LastSeenAt = (u.lastSeenAt != null && u.lastSeenAt != "never") ? utils.StringDatetimeToUnixTimeStamp(u.lastSeenAt) : 0,
                                //ActiveSessions = Convert.ToBoolean(u.activeSessions),
                                SharedUsers = u.sharedUsers,

                                //SpId = u.location!=null? (from v in sp.AsEnumerable() where v.Code == u.location select v.Id).FirstOrDefault():null,
                                SpCode = u.location != null ? (from v in sp.AsEnumerable() where v.Code == u.location select v.Code).FirstOrDefault() : null,
                                SpName = u.location != null ? (from v in sp.AsEnumerable() where (v.Code == u.location) select v.UserName).FirstOrDefault() ?? null : null,

                                //Percentage = (from v in sp.AsEnumerable() where v.Code == u.location select v.Percentage).FirstOrDefault() ,
                                //PercentageType = (int)(Entities.EnumType.TypePercentage)(from v in sp.AsEnumerable() where (v.Code == u.location) select v.Percentage).FirstOrDefault(),
                                //AddedDb = DateTime.Now,
                                DeleteFromServer = 0,
                                //=========================
                                ProfileName = u.actualProfileName,
                                AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                //status = (((u.actualProfileName == null || u.actualProfileName == "") && Convert.ToDouble(u.downloadUsed) > 0) ? "انتهى الرصيد" :
                                //(((u.actualProfileName != null && u.actualProfileName != "") && Convert.ToDouble(u.downloadUsed) == 0) ? "انتظار" :
                                //(((u.actualProfileName != null && u.actualProfileName != "") && Convert.ToDouble(u.downloadUsed) > 0) ? "نشط" :
                                //(((u.actualProfileName == null || u.actualProfileName == "") && Convert.ToDouble(u.downloadUsed) == 0) ? "خطأ في الباقة" : ""
                                //)))
                                //),

                                //actualLimDownload=0,
                                //actualLimUpload = 0,

                                TransferLimit = (long)( string.IsNullOrEmpty(u.actualProfileName) ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.TransferLimit).LastOrDefault()),
                                UptimeLimit = (long)(string.IsNullOrEmpty(u.actualProfileName) ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.UptimeLimit).LastOrDefault()),
                                ValidityLimit = (long)(u.actualProfileName == null || u.actualProfileName == "" ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.Validity).LastOrDefault()),
                                MkId = Global_Variable.Mk_resources.RB_SN,
                                //profileTillTime = 0,
                                //profileTimeLeft = u.actualProfileName == null ? 0 : (((from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.uptimeLimit).LastOrDefault()) - (utils.GetTimeCard_InSeconds(u.uptimeUsed))),

                            }).ToList();
            if (umUser.Count == 0)
                return;

            Local_DA.Add_UMUser_ToDB(umUser);
            if (by_search == false) Local_DA.Set_DeletFromServer_AsDisable<UmUser>("UmUser");
            Local_DA.Set_NotDeletFromServer("UmUser", umUser);    //=======نعدل علي الكروت انها ليسست محذوف من السيرفر لنظمن الكروت التي تم ادخالها سابقا 
            List<UmUser> um_not_delet = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");    //=== نرجع الكروت القديمة التي لم تحذف والكروت التي ادخلناها او عدلنا من السيرفر في هذه العمليه 
         
            var um = (from u in umUser
                      join s in um_not_delet on u.Sn_Name equals s.Sn_Name
                      where u.Sn_Name == s.Sn_Name
                      select new UmUser
                      {
                          //Id = s.Id,

                          Sn_Name=s.Sn_Name,
                          Disabled = u.Disabled,
                          Password = u.Password,
                          FirstName = u.FirstName,
                          LastName = u.LastName,
                          Comment = u.Comment,
                          Phone = u.Phone,
                          Location = u.Location,
                          Email = u.Email,
                          CallerMac = u.CallerMac,
                          UptimeUsed = u.UptimeUsed,
                          DownloadUsed = u.DownloadUsed,
                          UploadUsed = u.UploadUsed,
                          LastSeenAt = u.LastSeenAt,
                          ActiveSessions = u.ActiveSessions,
                          SharedUsers = u.SharedUsers,

                          CustomerName=u.CustomerName,

                          SpCode = s.SpCode != null ? s.SpCode : u.SpCode,
                          //SpId = s.SpId != null ? s.SpId : u.SpId,
                          SpName = s.SpName != null ? s.SpName : u.SpName,

                          //Percentage = s.Percentage != 0 ? s.Percentage : s.Percentage,
                          //is_sPercentage = s.is_spPercentage != 0 ? s.is_spPercentage : s.is_spPercentage,

                          Status =
                                (((string.IsNullOrEmpty(u.ProfileName)) && (u.DownloadUsed) > 0) ? 2 ://"انتهى الرصيد" :
                                (((u.ProfileName != null && u.ProfileName != "") && (u.DownloadUsed) == 0) ? 0 :  //"انتظار"
                                (((u.ProfileName != null && u.ProfileName != "") && (u.DownloadUsed) > 0) ? 1 : // "نشط" :
                                (((u.ProfileName == null || u.ProfileName == "") && (u.DownloadUsed) == 0) ? 3 : 5 //"خطأ في الباقة" :
                                )))
                                ),
                          DeleteFromServer = 0,
                          LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                          //=========================
                          //ProfileName = u.ProfileName == "" || u.ProfileName == null ? s.ProfileName : u.ProfileName,
                          ProfileName = string.IsNullOrEmpty(u.ProfileName) ? s.ProfileName : u.ProfileName,

                          //actualProfileName = u.actualProfileName,
                          //actualLimDownload=0,
                          //actualLimUpload = 0,
                          //actualLimTransfer = u.actualLimTransfer,
                          //actualLimUptime = u.actualLimUptime,
                          //profileTillTime = 0,
                          //profileTimeLeft = u.profileTimeLeft,
                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMUser_ToDB(um, false);
            else if (umUser.Count > 0)
                Local_DA.Add_UMUser_ToDB(umUser, false, false);
            
            
            // if (um.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(um);
            //else if (umUser.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(umUser, false);

        }
        public void Syn_UM_Users_to_LocalDB_V7(List<SourceCardsUserManager_fromMK> users = null)
        {
            bool by_search = true;
            //List<SellingPoint> sp = new List<SellingPoint>();
            //List<UmProfile> profile = Global_Variable.UM_Profile;

            if (users == null)
            {
                users = Global_Variable.Source_Users_UserManager;
                by_search = false;
            }
            var umUser = (from u in users.AsEnumerable()
                          select new UmUser
                          {
                              IdHX = u.id,
                              SN = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                              Sn_Name = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + u.userName,
                              UserName = u.userName,
                              Password = u.password,
                              Disabled = Convert.ToInt16(Convert.ToBoolean(u.disabled)),
                              Attributes = u.attributes,
                              Group = u.@group,
                              Comment = u.descr,
                              CallerMac = u.callerId,
                              SharedUsers = u.sharedUsers,
                              DeleteFromServer = 0,
                              //=========================
                              MkId = Global_Variable.Mk_resources.RB_SN,

                          }).ToList();
            if (umUser.Count == 0)
                return;

            Local_DA.Add_UMUser_ToDB_v7(umUser);
            if (by_search == false) Local_DA.Set_DeletFromServer_AsDisable<UmUser>("UmUser");
            Local_DA.Set_NotDeletFromServer("UmUser", umUser);    //=======نعدل علي الكروت انها ليسست محذوف من السيرفر لنظمن الكروت التي تم ادخالها سابقا 
            List<UmUser> um_not_delet = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");    //=== نرجع الكروت القديمة التي لم تحذف والكروت التي ادخلناها او عدلنا من السيرفر في هذه العمليه 

            var um = (from u in umUser
                      join s in um_not_delet on u.Sn_Name equals s.Sn_Name
                      where u.Sn_Name == s.Sn_Name
                      select new UmUser
                      {
                          Disabled = u.Disabled,
                          Password = u.Password,
                          Group = u.Group,
                          Attributes = u.Attributes,
                          Comment = u.Comment,
                          CallerMac = u.CallerMac,
                          SharedUsers = u.SharedUsers,
                          DeleteFromServer = 0,
                          Sn_Name=u.Sn_Name,
                          //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMUser_ToDB_v7(um, false);
            else if (umUser.Count > 0)
                Local_DA.Add_UMUser_ToDB_v7(umUser, false, false);

            // if (um.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(um);
            //else if (umUser.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(umUser, false);

        }

        public void Syn_Pyments_to_LocalDB(List<SourcePymentUserManager_fromMK> payments = null)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                Syn_Pyments_to_LocalDB_V7(payments);
                return;
            }
            bool by_search = true;
            List<SellingPoint> sp = new List<SellingPoint>();
            List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");
           
            if (payments == null)
            {
                payments = Global_Variable.Source_Pyment_UserManager;
                by_search = false;
            }
            if (payments == null || sourceCardsUsers == null)
                return;

            List<UmProfile> profile = Global_Variable.UM_Profile;

            var PY_Users = (from pyment in payments
                            join umuser in sourceCardsUsers on pyment.userName equals umuser.UserName /*into user*/
                            where umuser.UserName == pyment.userName
                            select new UmPyment
                            {
                                IdHX = pyment.idHX,
                                Sn = Int32.Parse(pyment.idHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                UserName = pyment.userName,
                                Sn_Name = Int32.Parse(pyment.idHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + pyment.userName,
                                Price = Convert.ToInt32(pyment.price),
                                //TotalPrice = Get_TotalPrice_str(umuser.ProfileName, pyment.price),

                                //TotalPrice = Convert.ToInt32(pyment.price),
                                //TotalPrice = Get_TotalPrice(umuser.ProfileName, pyment.price),
                                AddedDate = pyment.added != null ? utils.String_To_Datetime_By_V_MK(pyment.added) : null,
                                Fk_Sn_Name = umuser.Sn_Name ,
                                DeleteFromServer = 0,
                                ProfileName = (string.IsNullOrEmpty(umuser.ProfileName) ? ((from v in profile where (v.Price.ToString() == pyment.price.ToString()) select v.Name).FirstOrDefault() ?? null)  : umuser.ProfileName),

                                AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                MkId = Global_Variable.Mk_resources.RB_SN,


                            }).ToList();

            if (PY_Users.Count == 0)
               return;
            Local_DA.Add_UMPyement_ToDB(PY_Users, true);
            if (by_search == false) Local_DA.Set_Delet_fromServer_As_disable<UmPyment>("UmPyment");
            Local_DA.Set_NotDeletFromServer("UmPyment", PY_Users);
            
            //============ update pyement to not delete from server and id UserLocalDB ============
            List<UmPyment> sourcePayment = Local_DA.Get_Not_Delet_fromServer<UmPyment>("UmPyment");


            var um = (from py in PY_Users
                      join s in sourcePayment on py.Sn_Name equals s.Sn_Name
                      where py.Sn_Name == s.Sn_Name
                      select new UmPyment
                      {
                          Sn_Name=py.Sn_Name,
                          UserName = py.UserName,
                          Price = py.Price,
                          TotalPrice = py.TotalPrice == 0 ? Get_TotalPrice(py.ProfileName, py.Price) : py.TotalPrice,
                          //TotalPrice = py.Price,
                          ProfileName = py.ProfileName,
                          DeleteFromServer = 0,
                          ProfileTransferLimit = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select v.TransferLimit).LastOrDefault()) : 0)),
                          //ProfileTransferLimit = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select v.TransferLimit).LastOrDefault()) : 0)),
                          ProfileUptimeLimit = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select v.UptimeLimit).LastOrDefault()) : 0)),
                          ProfileValidity = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select (v.Validity * 24 * 60 * 60)).LastOrDefault()) : 0)),
                          AddedDate = py.AddedDate,
                          LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),


                          //uptimeUsed=sp!=null ? sp.uptimeUsed
                          //downloadUsed
                          //uploadUsed

                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMPyement_ToDB(um,false);
            //else if (PY_Users.Count > 0)
            //    Local_DA.Add_UMPyement_ToDB(PY_Users, false, false);

            //====================  update users if  profie not set on table user and regDate ==============================
            var profile_groub = (from b in um
                                 group b by b.UserName into g
                                 select new
                                 {
                                      
                                     //id = g.First().Id,
                                     userName = g.Key,
                                     moneyTotal = g.Sum(x => (x.TotalPrice)),
                                     price = g.Sum(x => (x.Price)),
                                     //price = g.Last().Price,
                                     
                                     uptimeLimit = g.Sum(x => x.ProfileUptimeLimit),
                                     transferLimit = g.Sum(x => x.ProfileTransferLimit),
                                     ValidityLimit = g.Sum(x => x.ProfileValidity),
                                     
                                     profileName = g.Last().ProfileName,
                                     //actualLimTransfer = g.Last().ProfileTransferLimit,
                                     //actualLimUptime = g.Last().ProfileUptimeLimit,
                                     //profileValidity = g.Last().ProfileValidity,

                                     regDate = g.First().AddedDate,
                                     countProfile = g.Count()
                                 }).ToList();


            var Users_update = (from pg in profile_groub
                                join u in sourceCardsUsers on pg.userName equals u.UserName
                                where pg.userName == u.UserName
                                select new UmUser
                                {
                                    //Id = u.Id,
                                    
                                    Sn_Name=u.Sn_Name,
                                    RegDate = (u.RegDate == null ? pg.regDate : u.RegDate),
                                    //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    TotalPrice = u.TotalPrice == 0 || u.TotalPrice < pg.moneyTotal ? pg.moneyTotal : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    Price = u.Price == 0 || u.Price < pg.price ? pg.price : u.Price, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                   
                                    //UptimeLimit = u.UptimeLimit < pg.uptimeLimit ? pg.uptimeLimit: u.UptimeLimit,
                                    //TransferLimit = u.TransferLimit < pg.transferLimit ? pg.transferLimit : u.TransferLimit,
                                    //ValidityLimit = u.ValidityLimit < pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,
                                    ProfileName =string.IsNullOrEmpty( u.ProfileName) ?pg.profileName:u.ProfileName,

                                    UptimeLimit = u.UptimeLimit,
                                    TransferLimit = u.TransferLimit,
                                    ValidityLimit = u.ValidityLimit,
                                    //ProfileValidity = u.ValidityLimit,



                                    //actualLimTransfer = pg.actualLimTransfer,
                                    //actualLimUptime = pg.actualLimUptime,

                                    CountProfile = pg.countProfile,
                                    //ProfileTimeLeft = (pg.uptimeLimit - u.UptimeUsed),
                                    //ProfileTransferLeft = (pg.transferLimit - (u.DownloadUsed + u.UploadUsed)),
                                    //ProfileTillTime=u.ProfileTillTime!=null?(u.ProfileTillTime.Value.AddDays(pg.ValidityLimit)):null,
                                    LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                }).ToList();

            //SqlDataAccess.Update_UM_user_to_LocalDB_AfterPymentGet(Users_update);
            Local_DA.Add_UMUser_ToDB(Users_update, false, true, true,false);

        }
        private float Get_TotalPrice(string profile, float price)
        {
            //string ProfileName = (string.IsNullOrEmpty(profile) ? ((from v in Global_Variable.UM_Profile where (v.Price.ToString() == price.ToString()) select v.Name).FirstOrDefault() ?? null)
            //                                                                                    : profile);
            //if (string.IsNullOrEmpty(ProfileName))
            //    return price;
            if (price <= 0)
                return price;

            string ProfileName = profile;
            float totalPrice = (price);
            try
            {
                UmProfile profle = Global_Variable.UM_Profile.Find(x => x.Name == ProfileName);
                float percentage = 0;
                if (profle != null)
                {
                    if (profle.Is_percentage == 1)
                    {

                        percentage = profle.Percentage;
                        if (profle.PercentageType == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                    }
                }
            }
            catch { }
            return totalPrice;
        }

        private float Get_TotalPrice_str(string profile, string _price)
        {

            if ( !float.TryParse(_price, out float valu))
                return 0;

            string ProfileName = (string.IsNullOrEmpty(profile) ? ((from v in Global_Variable.UM_Profile where (v.Price.ToString() == _price.ToString()) select v.Name).FirstOrDefault() ?? null) : profile);
            if (string.IsNullOrEmpty(ProfileName))
                return (float)Convert.ToDouble(_price);


            float price = (float)Convert.ToDouble( _price);
            float totalPrice =(float)Convert.ToDouble( price);
            try
            {
                UmProfile profle = Global_Variable.UM_Profile.Find(x => x.Name == profile);
                float percentage = 0;
                if (profle != null)
                {
                    if (profle.Is_percentage == 1)
                    {
                        
                        percentage = profle.Percentage;
                        if (profle.PercentageType == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                    }
                }
            }
            catch { }
            return totalPrice;
        }
       
        public void Syn_Pyments_to_LocalDB_V7(List<SourcePymentUserManager_fromMK> payments = null)
        {
            bool by_search = true;
            //List<SellingPoint> sp = new List<SellingPoint>();
            List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");


            if (payments == null)
            {
                payments = Global_Variable.Source_Pyment_UserManager;
                by_search = false;
            }
            if (payments == null || sourceCardsUsers == null)
                return;

            //List<UmProfile> profile = Global_Variable.UM_Profile;

            var PY_ment = (from pyment in payments
                            join umuser in sourceCardsUsers on pyment.userName equals umuser.UserName /*into user*/
                            where umuser.UserName == pyment.userName
                            select new UmPyment
                            {
                                IdHX = pyment.idHX,
                                Sn = Int32.Parse(pyment.idHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                UserName = pyment.userName,
                                Sn_Name = Int32.Parse(pyment.idHX.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + pyment.userName,
                                Price = Convert.ToInt32(pyment.price),
                                //TotalPrice = Convert.ToInt32(pyment.price),
                                AddedDate = umuser.RegDate,
                                Fk_Sn_Name = umuser.Sn_Name,
                                DeleteFromServer = 0,
                                ProfileName = pyment.profile,
                                AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                MkId = Global_Variable.Mk_resources.RB_SN,
                               
                                state = pyment.state,

                            }).ToList();

            if (PY_ment.Count == 0)
                return;
            Local_DA.Add_UMPyement_ToDB(PY_ment, true);
            if (by_search == false) Local_DA.Set_Delet_fromServer_As_disable<UmPyment>("UmPyment");
            Local_DA.Set_NotDeletFromServer("UmPyment", PY_ment);

            //============ update pyement to not delete from server and id UserLocalDB ============
            List<UmPyment> sourcePayment = Local_DA.Get_Not_Delet_fromServer<UmPyment>("UmPyment");


            var um = (from py in PY_ment
                      join s in sourcePayment on py.Sn_Name equals s.Sn_Name
                      where py.Sn_Name == s.Sn_Name
                      select new UmPyment
                      {
                          //Id =  s.Id ,
                          //UmUserId =  py.UmUserId ,
                          Sn_Name = py.Sn_Name,
                          UserName = py.UserName,
                          Price = py.Price,
                          //TotalPrice = py.Price,
                          TotalPrice = py.TotalPrice == 0 ? Get_TotalPrice(py.ProfileName, py.Price) : py.TotalPrice,
                          //TotalPrice = Get_TotalPrice(py.ProfileName, py.Price),
                          ProfileName = py.ProfileName,
                          DeleteFromServer = 0,
                          ProfileTransferLimit =((long)((Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName))==null?0: (Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName).TransferLimit))),
                          ProfileUptimeLimit = ((long)((Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName))==null?0: (Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName).UptimeLimit))),
                          ProfileValidity = ((long)((Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName))==null?0: (Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName).Validity) * 24 * 60 * 60)),
                          //ProfileUptimeLimit = ((long)Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName).UptimeLimit),
                          //ProfileValidity = ((long)Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName).Validity) * 24 * 60 * 60,
                          AddedDate = py.AddedDate,
                          state=py.state,

                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMPyement_ToDB(um, false);
            //else if (PY_Users.Count > 0)
            //    Local_DA.Add_UMPyement_ToDB(PY_Users, false, false);

            //====================  update users if  profie not set on table user and regDate ==============================
            var profile_groub = (from b in um
                                 group b by b.UserName into g
                                 select new
                                 {

                                     //id = g.First().Id,
                                     userName = g.Key,
                                     moneyTotal = g.Sum(x => (x.TotalPrice)),
                                     price = g.Sum(x => (x.Price)),
                                     //price = g.Last().Price,

                                     uptimeLimit = g.Sum(x => x.ProfileUptimeLimit),
                                     transferLimit = g.Sum(x => x.ProfileTransferLimit),
                                     ValidityLimit = g.Sum(x => x.ProfileValidity),

                                     profileName = g.Last().ProfileName,
                                     //actualLimTransfer = g.Last().ProfileTransferLimit,
                                     //actualLimUptime = g.Last().ProfileUptimeLimit,
                                     //profileValidity = g.Last().ProfileValidity,
                                     state=g.Last().state,
                                     regDate = g.First().AddedDate,
                                     countProfile = g.Count()
                                 }).ToList();


            var Users_update = (from pg in profile_groub
                                join u in sourceCardsUsers on pg.userName equals u.UserName
                                where pg.userName == u.UserName
                                select new UmUser
                                {
                                    Sn_Name = u.Sn_Name,
                                    RegDate = (u.RegDate == null ? pg.regDate : u.RegDate),
                                    //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    TotalPrice = u.TotalPrice == 0 || u.TotalPrice < pg.moneyTotal ? pg.moneyTotal : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    Price = u.Price == 0 || u.Price < pg.price ? pg.price : u.Price, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها

                                    UptimeLimit = u.UptimeLimit < pg.uptimeLimit ? pg.uptimeLimit : u.UptimeLimit,
                                    TransferLimit = u.TransferLimit < pg.transferLimit ? pg.transferLimit : u.TransferLimit,
                                    ValidityLimit = u.ValidityLimit < pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,
                                    ProfileName = string.IsNullOrEmpty(u.ProfileName) ? pg.profileName : u.ProfileName,

                                    //actualLimTransfer = pg.actualLimTransfer,
                                    //actualLimUptime = pg.actualLimUptime,

                                    CountProfile = pg.countProfile>u.CountProfile?pg.countProfile:u.CountProfile,
                                    ProfileTimeLeft = (pg.uptimeLimit - u.UptimeUsed),
                                    ProfileTransferLeft = (pg.transferLimit - (u.DownloadUsed + u.UploadUsed)),
                                    ProfileTillTime = u.ProfileTillTime != null ? (u.ProfileTillTime.Value.AddDays(pg.ValidityLimit)) : null,
                                    Status = status_v7(pg.state),

                                }).ToList();

            //SqlDataAccess.Update_UM_user_to_LocalDB_AfterPymentGet(Users_update);
            Local_DA.Add_UMUser_ToDB_v7(Users_update, false, true, true, false);

        }
        private int status_v7(string state)
        {
            int state1 = 4;
            try
            {
                state1 = state == "waiting" ? 0 :
                            state == "running-active" ? 1 :
                            state == "running" ? 1 :
                            state == "used" ? 2 : 3;
                //state == "used" ? 3 : 4 ;
            }
            catch { }
            return  state1;

            //state = (disable == "true") ? "معطل" :
            //     (state == "running-active") ? "نشط" :
            //     (state == "running-active") ? "نشط" :
            // (state == "running") ? "نشط" :
            // (state == "used") ? "انتهى الرصيد" :
            // (state == "waiting") ? "انتظار" : "";
            //return state;
        }
        public void Syn_Session_to_LocalDB(List<SourceSessionUserManager_fromMK> Ssession=null)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                Syn_Session_to_LocalDB_V7(Ssession);
                return;
            }
            bool by_search = true;
            try
            {
                List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");

                if (Ssession == null)
                {
                    Ssession = Global_Variable.Source_Session_UserManager;
                    by_search = false;
                }
                if (Ssession == null || sourceCardsUsers == null)
                    return;

                var sess_Users = (from session in Global_Variable.Source_Session_UserManager
                                  join umuser in sourceCardsUsers on session.userName equals umuser.UserName /*into user*/
                                  //where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")
                                  //from us in user.DefaultIfEmpty()
                                  select new UmSession
                                  {
                                      IdHX = session.id,
                                      Sn = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                      UserName = session.userName,
                                      Sn_Name = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + session.userName,
                                      //userId = us != null ? Convert.ToInt32(us.sn) : 0,
                                      NasPortId = session.nasPortId,
                                      CallingStationId = session.callingStationId,
                                      IpUser = session.ipUser,
                                      IpRouter = session.ipRouter,
                                      //Status = session.status,
                                      //status_str = session.status,
                                      Active = (session.active == "yes" ? 1 : 0),
                                      //Active = session.active == null ? 0 : (session.active == "no" ? 0 : 1),
                                      FromTime = session.fromTime != null ? utils.String_To_Datetime_By_V_MK(session.fromTime) : null,
                                      TillTime = session.tillTime != null ? utils.String_To_Datetime_By_V_MK(session.tillTime) : null,
                                      UpTime = utils.GetTimeCard_InSeconds(session.upTime),
                                      BytesDownload = long.Parse(session.bytesDownload),
                                      BytesUpload = long.Parse(session.bytesUpload),

                                      //Fk_Sn_Name = us != null ? us.Sn_Name : "",
                                      Fk_Sn_Name = umuser.Sn_Name,
                                      //UmUserId = umuser.Id,
                                      AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      MkId = Global_Variable.Mk_resources.RB_SN,
                                      DeleteFromServer = 0
                                  }).ToList();

                if (Local_DA.Add_UMSession_ToDB(sess_Users))
                {

                }

               Local_DA.Set_Delet_fromServer_As_disable<UmSession>("UmSession");
                Local_DA.Set_NotDeletFromServer("UmSession", sess_Users);
                List<UmSession> sourceSession = Local_DA.Get_Not_Delet_fromServer<UmSession>("UmSession");


                var um = (from u in sess_Users
                          join s in sourceSession on u.Sn_Name equals s.Sn_Name
                          where u.Sn_Name == s.Sn_Name
                          select new UmSession
                          {
                              //Id = s.Id,

                              CallingStationId = u.CallingStationId,
                              IpRouter = u.IpRouter,
                              IpUser = u.IpUser,
                              NasPortId = u.NasPortId,
                              
                              UpTime = u.UpTime,
                              FromTime = u.FromTime,
                              TillTime = u.TillTime,
                              Status = u.Status,
                              Active = u.Active,
                              DeleteFromServer = 0,
                              Sn_Name=s.Sn_Name
                              //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),


                              //=========================

                          }).ToList();
                if (um.Count > 0)
                    Local_DA.Add_UMSession_ToDB(um, false);
                //else if (sess_Users.Count > 0)
                //    Local_DA.Add_UMSession_ToDB(sess_Users, false, false);


                //sql_DataAccess.Add_UMSession_ToDB(um,false);

                //============== update user table first use ============
                if (um.Count > 0)
                {
                    //var SessionLQ = (from r in sess_Users
                    //                 group r by r.UserName into g
                    //                 select g.OrderBy(r => r.FromTime).First()).ToList();

                    //var Users_update = (from ses in SessionLQ
                    //                    join u in sourceCardsUsers on ses.UserName equals u.UserName
                    //                    where u.FirsLogin == null
                    //                    select new UmUser
                    //                    {

                    //                        //Id = u.Id,
                    //                        Sn_Name=u.Sn_Name,
                    //                        FirsLogin =ses.FromTime,
                    //                        Radius = ses.IpRouter ,
                    //                        NasPortId =  ses.NasPortId,

                    //                       //FirsLogin =u.FirsLogin==null? ses.FromTime:u.FirsLogin,
                    //                       // Radius = string.IsNullOrEmpty(u.Radius) ? ses.IpRouter : u.Radius,
                    //                       // NasPortId = string.IsNullOrEmpty(u.NasPortId) /*u.NasPortId != "" || u.NasPortId != null*/ ? ses.NasPortId : u.NasPortId,
                    //                        //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                    //                    }).ToList();


                    ////SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                    //Local_DA.Add_UMUser_ToDB(Users_update, false, true, false, true);
                    //============== update pyment table first use ============

                    var SessionLQ = (from r in sess_Users
                                     group r by r.UserName into g
                                     select new
                                     {
                                         UserName = g.Key,
                                         Sn_Name = g.First().Sn_Name,
                                         FromTime = g.OrderBy(x => x.FromTime).First().FromTime,
                                         UptimeUsed = g.Sum(a => a.UpTime),
                                         DownloadUsed = g.Sum(r => r.BytesDownload),
                                         UploadUsed = g.Sum(r => r.BytesUpload),
                                         Radius = g.First().IpRouter,
                                         NasPortId = g.First().NasPortId,
                                         CountSession = g.Count(),
                                     });

                    var Users_update = (from ses in SessionLQ
                                        join u in sourceCardsUsers on ses.UserName equals u.UserName
                                        //where u.FirsLogin == null
                                        select new UmUser
                                        {
                                            UserName = u.UserName,
                                            Sn_Name = u.Sn_Name,

                                            FirsLogin = u.FirsLogin == null ? ses.FromTime : u.FirsLogin,
                                            Radius = string.IsNullOrEmpty(u.Radius) ? ses.Radius : u.Radius,
                                            NasPortId = string.IsNullOrEmpty(u.NasPortId) ? ses.NasPortId : u.NasPortId,
                                            CountSession = ses.CountSession > u.CountSession ? ses.CountSession : u.CountSession,
                                        }).ToList();

                    Local_DA.Add_UMUser_ToDB(Users_update, false, true, false, true);



                }
            }
            catch(Exception ex) { RJMessageBox.Show(ex.Message+ "\n Syn_Session"); }
        }
        public void Syn_Session_to_LocalDB_V7(List<SourceSessionUserManager_fromMK> Ssession = null)
        {
            
            bool by_search = true;
            try
            {
                List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");

                if (Ssession == null)
                {
                    Ssession = Global_Variable.Source_Session_UserManager;
                    by_search = false;
                }
                if (Ssession == null || sourceCardsUsers == null)
                    return;

                var sess_Users = (from session in Global_Variable.Source_Session_UserManager
                                  join umuser in sourceCardsUsers on session.userName equals umuser.UserName /*into user*/
                                  //where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")
                                  //from us in user.DefaultIfEmpty()
                                  select new UmSession
                                  {
                                      IdHX = session.id,
                                      Sn = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                      UserName = session.userName,
                                      Sn_Name = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + session.userName,
                                      //userId = us != null ? Convert.ToInt32(us.sn) : 0,
                                      NasPortId = session.nasPortId,
                                      CallingStationId = session.callingStationId,
                                      IpUser = session.ipUser,
                                      IpRouter = session.ipRouter,
                                      //Status = session.status,
                                      //status_str = session.status,
                                      Active = (session.active == "yes" ? 1 : 0),
                                      //Active = session.active == null ? 0 : (session.active == "no" ? 0 : 1),
                                      FromTime = session.fromTime != null ? utils.String_To_Datetime_By_V_MK(session.fromTime) : null,
                                      TillTime = session.tillTime != null ? utils.String_To_Datetime_By_V_MK(session.tillTime) : null,
                                      UpTime = utils.GetTimeCard_InSeconds(session.upTime),
                                      BytesDownload = long.Parse(session.bytesDownload),
                                      BytesUpload = long.Parse(session.bytesUpload),

                                      //Fk_Sn_Name = us != null ? us.Sn_Name : "",
                                      Fk_Sn_Name = umuser.Sn_Name,
                                      //UmUserId = umuser.Id,
                                      AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      MkId = Global_Variable.Mk_resources.RB_SN,
                                      DeleteFromServer = 0
                                  }).ToList();

                if (Local_DA.Add_UMSession_ToDB(sess_Users))
                {

                }

                Local_DA.Set_Delet_fromServer_As_disable<UmSession>("UmSession");
                Local_DA.Set_NotDeletFromServer("UmSession", sess_Users);
                List<UmSession> sourceSession = Local_DA.Get_Not_Delet_fromServer<UmSession>("UmSession");


                var um = (from u in sess_Users
                          join s in sourceSession on u.Sn_Name equals s.Sn_Name
                          where u.Sn_Name == s.Sn_Name
                          select new UmSession
                          {
                              //Id = s.Id,

                              CallingStationId = u.CallingStationId,
                              IpRouter = u.IpRouter,
                              IpUser = u.IpUser,
                              NasPortId = u.NasPortId,

                              UpTime = u.UpTime,
                              FromTime = u.FromTime,
                              TillTime = u.TillTime,
                              Status = u.Status,
                              Active = u.Active,
                              DeleteFromServer = 0,
                              Sn_Name = s.Sn_Name
                              //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),


                              //=========================

                          }).ToList();
                if (um.Count > 0)
                    Local_DA.Add_UMSession_ToDB(um, false);
                //else if (sess_Users.Count > 0)
                //    Local_DA.Add_UMSession_ToDB(sess_Users, false, false);


                //sql_DataAccess.Add_UMSession_ToDB(um,false);

                //============== update user table first use ============
                if (um.Count > 0)
                {
                    //var SessionLQ = (from r in sess_Users
                    //                 group r by r.UserName into g
                    //                 select g.OrderBy(r => r.FromTime).First()).ToList();

                    var SessionLQ = (from r in sess_Users
                                      group r by r.UserName into g 
                                      //orderby(a => a.FromTime)
                                      //orderby(g.FromTime)
                                     select new
                                     {
                                         UserName = g.Key,
                                         Sn_Name = g.First().Sn_Name,
                                         FromTime = g.OrderBy(x => x.FromTime).First().FromTime,
                                         UptimeUsed = g.Sum(a => a.UpTime),
                                         DownloadUsed = g.Sum(r => r.BytesDownload),
                                         UploadUsed = g.Sum(r => r.BytesUpload),
                                         Radius = g.First().IpRouter,
                                         NasPortId = g.First().NasPortId,
                                         CountSession = g.Count(),
                                     });

                    var Users_update = (from ses in SessionLQ
                                        join u in sourceCardsUsers on ses.UserName equals u.UserName
                                        //where u.FirsLogin == null
                                        select new UmUser
                                        {
                                            UserName = u.UserName,
                                            Sn_Name = u.Sn_Name,
                                            
                                            FirsLogin = u.FirsLogin == null ? ses.FromTime : u.FirsLogin,
                                            Radius = string.IsNullOrEmpty(u.Radius) ? ses.Radius : u.Radius,
                                            NasPortId = string.IsNullOrEmpty(u.NasPortId) ? ses.NasPortId : u.NasPortId,
                                            UptimeUsed = (ses.UptimeUsed > u.UptimeUsed) ? ses.UptimeUsed:u.UptimeUsed,
                                            DownloadUsed = (ses.DownloadUsed > u.DownloadUsed) ? ses.DownloadUsed : u.DownloadUsed,
                                            UploadUsed = (ses.UploadUsed > u.UploadUsed) ? ses.UploadUsed : u.UploadUsed,
                                            CountSession=ses.CountSession>u.CountSession?ses.CountSession:u.CountSession,

                                        }).ToList();


                    //SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                    Local_DA.Add_UMUser_ToDB_v7(Users_update, false, true, false, true);
                    //============== update pyment table first use ============
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Syn_Session"); }
        }

        public void Syn_Session_to_LocalDB2()
        {
            List<SourceCardsUserManager_fromDB> sourceCardsUsers = SqlDataAccess.GetUsersManager_NotDeleteFromServer();
            var sess_Users = (from session in Global_Variable.Source_Session_UserManager
                              join umuser in sourceCardsUsers on session.userName equals umuser.userName into user
                              where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")
                              from us in user.DefaultIfEmpty()
                              select new SourceSessionUserManager_FromDB
                              {
                                  idHX = session.id,
                                  sn = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                  userName = session.userName,
                                  sn_userName = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + session.userName,
                                  userId = us != null ? Convert.ToInt32(us.sn) : 0,
                                  nasPortId = session.nasPortId,
                                  callingStationId = session.callingStationId,
                                  ipUser = session.ipUser,
                                  ipRouter = session.ipRouter,
                                  status_str = session.status,
                                  active = session.active == null ? 0 : (session.active == "no" ? 0 : 1),
                                  fromTime = session.fromTime != null ? utils.StringDatetimeToUnixTimeStamp(session.fromTime) : 0,
                                  tillTime = session.tillTime != null ? utils.StringDatetimeToUnixTimeStamp(session.tillTime) : 0,
                                  upTime = utils.GetTimeCard_InSeconds(session.upTime),
                                  bytesDownload = Convert.ToDouble(session.bytesDownload),
                                  bytesUpload = Convert.ToDouble(session.bytesUpload),
                                  fk_sn_userName_User = us != null ? us.sn_userName : "",
                                  fk_User_localDB_id = us != null ? us.id : 0,
                                  Delet_fromServer = 0
                              }).ToList();

            SqlDataAccess.Add_UM_Session_to_LocalDB(sess_Users);
            //============ update session to not delete from server  ============
            List<SourceSessionUserManager_FromDB> sourceSession = SqlDataAccess.GetSession_NotDeleteFromServer();
            SqlDataAccess.Set_Session_disable_LocalDB();

            var um = (from u in sess_Users
                      join s in sourceSession on u.sn_userName equals s.sn_userName
                      //where u.sn_userName == s.sn_userName
                      select new SourceSessionUserManager_FromDB
                      {
                          id = s.id,
                          Delet_fromServer = 0,
                          //=========================

                      }).ToList();
            SqlDataAccess.Add_UM_Session_to_LocalDB(um, false);

            //============== update user table first use ============
           var SessionLQ = (from r in sess_Users
                                group r by r.userName into g
                                select g.OrderBy(r => r.fromTime).First()).ToList();

            var Users_update = (from ses in SessionLQ
                                join u in sourceCardsUsers on ses.userName equals u.userName
                                where u.firstUse <= 0
                                select new SourceCardsUserManager_fromDB
                                {
                                    id = u.id,
                                    firstUse = u.firstUse <= 0 ? ses.fromTime : u.firstUse,
                                    radius = u.radius != "" || u.radius != null ? ses.ipRouter : u.radius,
                                    nasPortId = u.nasPortId != "" || u.nasPortId != null ? ses.nasPortId : u.nasPortId,
                                }).ToList();
            SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);

            //============== update pyment table first use ============




        }
        void update_lastCode_toSN()
        {
            try
            {
                lock (Smart_DataAccess.Lock_object)
                    using (var conn = Smart_DataAccess.GetConnSmart())
                    {
                        try { var update_last_sn = conn.ExecuteScalar<My_Sequence>($"update My_Sequence set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var CardsTemplate = conn.ExecuteScalar($"update CardsTemplate set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Hotspot_Profile_Hotspot_local = conn.ExecuteScalar($"update Hotspot_Profile_Hotspot_local set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Hotspot_Source_Profile = conn.ExecuteScalar($"update Hotspot_Source_Profile set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Comm_SellingPoint = conn.ExecuteScalar($"update Comm_SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var HSLocalProfile = conn.ExecuteScalar($"update HSLocalProfile set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var NumberPrintCard = conn.ExecuteScalar($"update NumberPrintCard set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var BatchCard = conn.ExecuteScalar($"update BatchCard set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Alert_SellingPoint = conn.ExecuteScalar($"update Alert_SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var SellingPoint = conn.ExecuteScalar($"update SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmLimitation = conn.ExecuteScalar($"update UmLimitation set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmProfile = conn.ExecuteScalar($"update UmProfile set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmProfile_Limtition = conn.ExecuteScalar($"update UmProfile_Limtition set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Customer = conn.ExecuteScalar($"update UserManager_Customer set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_SourceProfile_UserManager = conn.ExecuteScalar($"update UserManager_SourceProfile_UserManager set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Source_Profile_Limtition = conn.ExecuteScalar($"update UserManager_Source_Profile_Limtition set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Source_limitation = conn.ExecuteScalar($"update UserManager_Source_limitation set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                    }
            }
            catch { }
        }
        [Obsolete]
        public bool AddCardMk_Usermanager(FormAddUsersManager _frm, Form_PrintUserManagerState _Frm_State,string username="",string password="")
        
        {
             
            //update_lastCode_toSN();

            frm = _frm;
            Frm_State = _Frm_State;


            if (startPrint == true)
            {
                RJMessageBox.Show("الرجاء الانتضار حتى اكتمال العملية السابقة");
                return false;
            }
           
            if (check_fields() == false)
                return false;

            clss_InfoPrint = get_data_from_interface2();

            if (clss_InfoPrint.Save_To_PDF)
                if (init_file_pdf() == false)
                    return false;


            //Dictionary<string, object> info_print = get_data_from_interface();
        
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من انشاء الكروت ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 3, "", "يرجى الانتضار حتى تجميع البيانات");

                frm.btnAdd.Enabled = false;
                frm.tableLayoutPanel1.Enabled = false;

                try
                {
                    if (Frm_State.is_Add_One_Card)
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserUser_one(username,password));
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                    }
                    else if (Frm_State.is_add_batch_cards)
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserUserd_batch_cards());
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                      

                        //using (Form_WaitForm frm = new Form_WaitForm(AddUserUserd_batch_cards))
                        //{
                        //    frm.ShowDialog();
                        //}
                    }
                    else 
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserUserManagerScript_3());
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();

                        //using (Form_WaitForm frm = new Form_WaitForm(AddUserUserManagerScript_3))
                        //{
                        //    frm.ShowDialog();
                        //}
                    }
                }

                catch { startPrint = false; }
            }
            else
            {
                //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الفاء عملية الاضافة");
                startPrint = false;
                return false;
            }
            startPrint = false;
            return true;
        }
        private bool check_fields()
        {
            if (startPrint == true)
            {
                RJMessageBox.Show("الرجاء الانتضار حتى اكتمال العملية السابقة");
                return false;
            }
            if (frm.CBox_Profile.SelectedIndex == -1 || frm.CBox_Profile.Text == "")
            {
                RJMessageBox.Show("حدد البروفايل");
                return false;
            }
            //if (Add_One_Card)
            //    frm.txtNumberCard.Text = "1";
            if (Global_Variable.Mk_resources.version < 7)
            {
                try
                {
                    if (frm.CBox_CustomerUserMan.SelectedIndex == -1)
                    {
                        MessageBox.Show(" حدد عميل اليوزمنجر");
                        return false;
                    }
                }
                catch { }
            }
            int numberChik;
            if (Frm_State.is_Add_One_Card == false)
            {
                if (!(int.TryParse(frm.txtNumberCard.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل عدد الكروت بشكل صحيح ");
                    return false;
                }
                if (Convert.ToInt32(frm.txtNumberCard.Text) < 2)
                {
                    RJMessageBox.Show("عند اضافة كروت عشوائي يجب ان يكون اقل عدد للكروت 2 كروت");
                    return false;
                }
            }
            if (!(int.TryParse(frm.txt_longUsers.Text, out numberChik)) && is_Add_One_Card == false)
            {
                RJMessageBox.Show(" ادخل عدد صحيح الى طول اسم المستخدم");
                return false;
            }
            if ((Convert.ToInt16(frm.txt_longUsers.Text) + frm.txt_StartCard.Text.Length + frm.txt_EndCard.Text.Length) < 6)
            {
                RJMessageBox.Show("يجب ان يكون طول رقم الكرت مع البادئة والاحقة اكبر من 5");
                //return false;
            }
            if (!(int.TryParse(frm.txt_longPassword.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح في طول كلمة السر ");
                return false;
            }
            if (frm.cbox_User_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم المستخدم");
                return false;
            }
            if (frm.cbox_Pass_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم كلمة السر");
                return false;
            }
            if (frm.cbox_UserPassword_Pattern.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد صيغة الكرت ");
                return false;
            }
            if (frm.CBox_TemplateCards.SelectedIndex == -1 || frm.CBox_TemplateCards.Text == "")
            {
                //RJMessageBox.Show("لم تختر اي قالب للطباعة");
                DialogResult result2 = RJMessageBox.Show("  لم تقم باختيار قالب للطباعة \n هل تريد المتابعة بدون اخراج الكروت الي ملف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result2 == DialogResult.Yes)
                {
                    frm.checkBoxSaveTo_PDF.Checked = false;
                    frm.checkBoxOpenAfterPrint.Checked = false;
                }
                else
                {
                    startPrint = false;
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                    return false; 
                }

            }


            if (frm.checkBox_RegisterAs_LastBatch.Checked)
            {
                if (!(int.TryParse(frm.txt_last_batchNumber.Text, out numberChik)) && is_Add_One_Card == false)
                {
                    RJMessageBox.Show(" ادخل رقم صحيح للدفعة  السابقه بشكل صحيح");
                    return false;
                }

                var found= Smart_DA.Get_Batch_byBatchNumber_And_Server(Convert.ToInt32(frm.txt_last_batchNumber.Text),0);
                if(found==null || found.Count == 0)
                {
                    RJMessageBox.Show("رقم الطبعة السابقة التي ادخلتها غير موجود");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");

                    return false;
                }
            }
            return true;

        }

        private Dictionary<string, object> get_data_from_interface()
        {
            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == frm.CBox_Profile.SelectedValue.ToString());
            //int NumberPrint = SqlDataAccess.Get_lastID_Batch_cards();  
            //int NumberPrint = SqlDataAccess.get_BatchCards_my_sequence();    
            //long NumberPrint = Local_DA.get_BatchCards_my_sequence();    
            long NumberPrint = Smart_DA.Get_BatchCards_My_Sequence();    
             NumberPrint += 1;
           

            info_print2 = new Dictionary<string, object>();
            //info_print.Add("profile", profile);

            //Int32 regDate = utils.DateTimeToUnixTimeStamp(DateTime.Now);
            //info_print.Add("regDate", regDate);
            //info_print.Add("NumberPrint",  NumberPrint);
            //info_print.Add("Public_is_use_Attribut", false);
            //info_print.Add("Public_Attribut", "");

            //if (Global_Variable.Mk_resources.version >= 7)
            //{
            //    if (frm.CBox_Attribute.Text != "" && frm.CBox_Attribute.Text != "")
            //    {
            //        info_print.Add("Public_is_use_Attribut", true);
            //        info_print.Add("Public_Attribut", frm.CBox_Attribute.SelectedItem.ToString() + ":" + frm.txt_attribute.Text.Trim());
            //    }
            //    else
            //    {
            //        info_print.Add("Public_is_use_Attribut", false);
            //        info_print.Add("Public_Attribut", "");
            //    }
            //}

            ////info_print.Add("Public_txt_Attribut", Public_txt_Attribut);
            //if (frm.CBox_group.Text == "")
            //    info_print.Add("Public_Group", "default");
            //else
            //    info_print.Add("Public_Group", frm.CBox_group.Text.ToString());
            
            //info_print.Add("Public_Selected_template_item", frm.CBox_TemplateCards.SelectedIndex);
            //info_print.Add("Public_Number_Cards_ToAdd", Convert.ToInt32(frm.txtNumberCard.Text));
            //info_print.Add("Public_Number_Cards_ToAdd_DB", Convert.ToInt32(frm.txtNumberCard.Text));
            //info_print.Add("Public_Profile_Name", frm.CBox_Profile.SelectedValue.ToString());
            ////info_print.Add("Public_Profile_Name", MyDataClass.SourceUM_Profiles.Rows[CBox_Profile.SelectedIndex]["Name"].ToString());
            //info_print.Add("Public_Mode_User_NumberORcharcter", frm.cbox_User_NumberORcharcter.SelectedItem.ToString());
            //info_print.Add("Public_Mode_User_NumberORcharcter_Value", frm.cbox_User_NumberORcharcter.SelectedIndex);
            //info_print.Add("Public_User_Long", (Convert.ToInt32(frm.txt_longUsers.Text)));
            //info_print.Add("Public_Mode_Password_NumberORcharcter", frm.cbox_Pass_NumberORcharcter.SelectedItem.ToString());
            //info_print.Add("Public_Mode_Password_NumberORcharcter_Value", frm.cbox_Pass_NumberORcharcter.SelectedIndex);
            //info_print.Add("Public_Password_Long", Convert.ToInt32(frm.txt_longPassword.Text));
            //info_print.Add("Public_UserPassword_Pattern", frm.cbox_UserPassword_Pattern.SelectedIndex);
            //info_print.Add("Public_SellingPoint_Name", frm.CBox_SellingPoint.Text);

            //if (frm.CBox_SellingPoint.Text != "")
            //    info_print.Add("Public_SellingPoint_Value", frm.CBox_SellingPoint.SelectedValue);
            //else
            //    info_print.Add("Public_SellingPoint_Value", string.Empty);

            //info_print.Add("Public_StartCard", frm.txt_StartCard.Text.Trim());
            //info_print.Add("Public_EndCard", frm.txt_EndCard.Text.Trim());
            //info_print.Add("Public_ShardUser", frm.txt_ShardUser.Text.Trim());
            ////info_print.Add("Public_Is_First_Use", frm.checkBoxFirstUse.Checked);
            //info_print.Add("Public_FirstUse", frm.checkBoxFirstUse.Checked);
            //info_print.Add("Public_is_comment", frm.checkBox_note.Checked);
            
            //info_print.Add("Public_file_Name", "");
            //info_print.Add("pathfile", pathfile);
           
            //if ((bool)info_print["Public_is_comment"])
            //    info_print.Add("Public_Comment", frm.txt_note.Text.Trim().ToString());
            //else
            //    info_print.Add("Public_Comment",string.Empty);

            //if (Global_Variable.Mk_resources.version <= 6)
            //    info_print.Add("Public_Custumer_UserMan", frm.CBox_CustomerUserMan.Text.ToString());
            //else
            //    info_print.Add("Public_Custumer_UserMan", "");
            ////info_print.Add("Public_Save_Requir_Location", checkBoxFirstUse.Checked);
            //info_print.Add("Public_Save_To_PDF", frm.checkBoxSaveTo_PDF.Checked);
            ////info_print.Add("pathfile", checkBoxFirstUse.Checked);
            //info_print.Add("TemplateId", frm.CBox_TemplateCards.SelectedValue);
            //info_print.Add("TemplateName", frm.CBox_TemplateCards.Text.ToString());
            return info_print2;
        }

        private Clss_InfoPrint get_data_from_interface2()
        {
            
            clss_InfoPrint = new Clss_InfoPrint();
            clss_InfoPrint.is_add_batch_cards = Frm_State.is_add_batch_cards;
            clss_InfoPrint.is_add_batch_cards_to_Archive = Frm_State.is_add_batch_cards_to_Archive;
            clss_InfoPrint.is_Add_One_Card = Frm_State.is_Add_One_Card;

            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == frm.CBox_Profile.SelectedValue.ToString());
            clss_InfoPrint.profile = profile;

            if (Frm_State.is_Add_One_Card == false)
            {
                int BatchNumber = 0;
                int NumberPrint = 0;
                if (frm.checkBox_RegisterAsBatch.Checked)
                {
                    //BatchNumber = 35;
                    BatchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence("BatchCards");
                    BatchNumber += 1;
                    clss_InfoPrint.BatchNumber = BatchNumber;
                }
                if (frm.checkBox_RegisterAs_LastBatch.Checked)
                {
                    BatchNumber = Convert.ToInt32(frm.txt_last_batchNumber.Text);
                    clss_InfoPrint.BatchNumber = BatchNumber;
                    clss_InfoPrint.is_RegisterAs_LastBatch = true;

                }

                NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint");
                NumberPrint += 1;
                clss_InfoPrint.NumberPrint = NumberPrint;
            }
            if (Global_Variable.Mk_resources.version >= 7)
            {
                if (frm.CBox_Attribute.Text != "" && frm.CBox_Attribute.Text != "")
                {
                    clss_InfoPrint.is_use_Attribut = true;
                    clss_InfoPrint.Attribut = frm.CBox_Attribute.SelectedItem.ToString() + ":" + frm.txt_attribute.Text.Trim();
                }
            }

            if (frm.CBox_group.Text != "")
                clss_InfoPrint.Group = frm.CBox_group.Text.ToString();

            clss_InfoPrint.Selected_template_item = frm.CBox_TemplateCards.SelectedIndex;
            clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(frm.txtNumberCard.Text);
            clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(frm.txtNumberCard.Text);
            clss_InfoPrint.Profile_Name = frm.CBox_Profile.SelectedValue.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter = frm.cbox_User_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter_Value = frm.cbox_User_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.User_Long = (Convert.ToInt32(frm.txt_longUsers.Text));
            clss_InfoPrint.Mode_Password_NumberORcharcter = frm.cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_Password_NumberORcharcter_Value = frm.cbox_Pass_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.Password_Long = Convert.ToInt32(frm.txt_longPassword.Text);
            clss_InfoPrint.UserPassword_Pattern = frm.cbox_UserPassword_Pattern.SelectedIndex;
            clss_InfoPrint.SellingPoint_Name = frm.CBox_SellingPoint.Text;
            if (frm.CBox_SellingPoint.Text != "")
            {
                try
                {
                    clss_InfoPrint.SellingPoint_Name = frm.CBox_SellingPoint.Text;
                    clss_InfoPrint.SellingPoint_Value = frm.CBox_SellingPoint.SelectedValue.ToString();
                    clss_InfoPrint.SellingPoint_Value_str = frm.CBox_SellingPoint.SelectedValue.ToString();

                    clss_InfoPrint.SellingPoint = Smart_DA.Get_SellingPoint_Code(frm.CBox_SellingPoint.SelectedValue.ToString());
                }
                catch { }
            }
            clss_InfoPrint.StartCard = frm.txt_StartCard.Text.Trim();
            clss_InfoPrint.EndCard = frm.txt_EndCard.Text.Trim();
            clss_InfoPrint.ShardUser = frm.txt_ShardUser.Text.Trim();
            clss_InfoPrint.FirstUse = frm.checkBoxFirstUse.Checked;
            clss_InfoPrint.is_comment = frm.checkBox_note.Checked;
            clss_InfoPrint.pathfile = pathfile;

            if (frm.checkBox_note.Checked)
                clss_InfoPrint.Comment = frm.txt_note.Text.Trim().ToString();

            if (Global_Variable.Mk_resources.version <= 6)
                clss_InfoPrint.Custumer_UserMan = frm.CBox_CustomerUserMan.Text.ToString();

            clss_InfoPrint.Save_To_PDF = frm.checkBoxSaveTo_PDF.Checked;
            clss_InfoPrint.Open_PDF_file = frm.checkBoxOpenAfterPrint.Checked;
            clss_InfoPrint.SaveTo_excel = frm.checkBoxSaveTo_excel.Checked;
            clss_InfoPrint.SaveTo_script_File = frm.checkBoxSaveTo_script_File.Checked;
            clss_InfoPrint.SaveTo_text_File = frm.checkBoxSaveTo_text_File.Checked;
            clss_InfoPrint.RegisterAsBatch = frm.checkBox_RegisterAsBatch.Checked;
            clss_InfoPrint.RegisterAs_LasBatch = frm.checkBox_RegisterAs_LastBatch.Checked;
            clss_InfoPrint.With_Archive_uniqe = frm.checkBox_With_Archive_uniqe.Checked;
            clss_InfoPrint.TemplateId = frm.CBox_TemplateCards.SelectedValue.ToString();
            clss_InfoPrint.TemplateName = frm.CBox_TemplateCards.Text.ToString();

            return clss_InfoPrint;
        }

        public bool init_file_pdf()
        {
            SaveFileDialog saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Title = "حدد مكان حفظ الملف";
            try
            {
                if (Frm_State.PathFolderPrint != "")
                    saveFileDialog1.InitialDirectory = Frm_State.PathFolderPrint;
                else
                    //saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
                    saveFileDialog1.InitialDirectory = $"{utils.Get_TempCards_Pdf_Directory()}\\UserManager";
            }
            catch
            {
                saveFileDialog1.InitialDirectory = $"{utils.Get_TempCards_Pdf_Directory()}\\UserManager";
                Frm_State.PathFolderPrint = $"{utils.Get_TempCards_Pdf_Directory()}\\UserManager";
            }
            try
            {
                if (!Directory.Exists(Frm_State.PathFolderPrint))
                {
                    Directory.CreateDirectory(Frm_State.PathFolderPrint);
                }
            }
            catch
            {
                Frm_State.path_saved_file = $"{utils.Get_TempCards_Pdf_Directory()}\\UserManager";
            }
            //string FileCreateTime= DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + frm.txtNumberCard.Text + "Cards)" + "_(" + frm.CBox_Profile.Text + ")";

            Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + frm.txtNumberCard.Text + "Cards)" + "_(" + frm.CBox_Profile.Text + ")";
            pathfile = Frm_State.path_saved_file + "\\" + "Cards_" + Public_file_Name + ".pdf";

            saveFileDialog1.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialog1.FileName = "Cards_" + Public_file_Name;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialog1.FileName;
                Frm_State.PathFolderPrint = Path.GetDirectoryName(saveFileDialog1.FileName);
                Frm_State.path_saved_file = pathfile;

            }
            else
            {
                startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                return false;
            }

            clss_InfoPrint.pathfile = pathfile;

            return true;
        }
        
        public New_Generate_Cards new_Generate_Cards;
        private string strProfile = "";

        [Obsolete]
        public void AddUserUserManagerScript_3()
        {
            startPrint = true;
            try
            {
                HashSet<string> card_copy = new HashSet<string>();
                if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
                {
                    //using(var db=Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    //    card_copy = db.ColumnDistinct<string>(db.From<UmUser>().Where(x=>x.DeleteFromServer==0).Select(x => x.UserName));
                    //}
                    Sql_DataAccess da = new Sql_DataAccess();

                    card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_if_RunOffline());
                }
                else
                {
                    if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                        card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
                }
                if (clss_InfoPrint.With_Archive_uniqe)
                {
                    //=========  get cards from archive  and copy  to hashset card_copy
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
                    Archive_DataAccess Archive_DA = new Archive_DataAccess();
                    var ArchiveCards = Archive_DA.Load<string>($"SELECT UserName FROM CardsArtchive WHERE Status=0;");
                    card_copy.UnionWith(ArchiveCards);
                }

                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

                string mesgBtn = "يتم الان اضافة الكروت الى اليوزرمنجر";
                if (is_add_batch_cards_to_Archive)
                    mesgBtn = "يتم الان اضافة الكروت الى الارشيف";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

                // ====== توليد الكروت العشوائية ============================
                CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
                new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Public_Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }
                //========= تجهيز سكربت الاضافة الي المايكروتك =================  
                Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);

                //=========== فحص اذا طريقة الاضافة سكربت واحد لليوزر والبروفايل او فصل سكربت اضافة البروفايل وحده =========
                bool _check_if_use_2Scritp_add = check_if_use_2Scritp_add();

                //======= الاضافة الي المايكروتك ========================
                Dictionary<string, string> res = null;
                if (_check_if_use_2Scritp_add == false || Global_Variable.Mk_resources.version >= 7)
                {
                    res = GenerateBachScriptUser(variableScript);
                }
                else
                {
                    res = GenerateBachScriptUser_2Scritp_add(variableScript);

                }

                if (res["status"] == "false")
                {
                    startPrint = false;
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تم الغاء العملية");
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }

                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                string[] Res_split = res["result"].Split(new string[] { ";" }, StringSplitOptions.None);
                string[] user_split = Res_split[2].Split(new string[] { "|" }, StringSplitOptions.None);  //====== check if error add user ========
                if (user_split.Length > 1)
                {
                    for (int i = 1; i < user_split.Length; i++)
                        new_Generate_Cards.dicUser.Remove(user_split[i]);
                    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                    new_Generate_Cards.dicUser = GenerateIfLastErorr(new_Generate_Cards.dicUser, user_split.Length - 1, cLS_Genrate_Cards);
                }

                //===== الاستعلام عن الكرت الاول ومعرفه الرقم التسلسلي تبعه عشان التسلسل للبقية =======
                //SourceCardsUserManager_fromMK firstUser = SourceCardsUserManager_fromMK.Get_one_UM_User(new_Generate_Cards.dicUser.ElementAt(0).Key,false).First();
                //double sn = 0;
                //if (firstUser.id != null)
                //sn = Int32.Parse(firstUser.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);

                //new_Generate_Cards.dicUser.ElementAt(0).Value.SN = sn;
                //Global_Variable.Source_Users_UserManager_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(0).Key);
                if (new_Generate_Cards.dicUser.Count == 0)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }
                strProfile = "";
                //List<SourceCardsUserManager_fromDB> dbUser = add_sn_to_local_dbUser(new_Generate_Cards);
                List<UmUser> dbUser = add_sn_to_local_dbUser(new_Generate_Cards);

                //====== check if error add profile   or  _check_if_use_2Scritp_add ==================

                if (_check_if_use_2Scritp_add == false || Global_Variable.Mk_resources.version >= 7)
                {
                    string[] profile_split = Res_split[1].Split(new string[] { "|" }, StringSplitOptions.None);
                    if (profile_split.Length > 1)
                        AddProfile_ErorreCards(profile_split.ToList());
                }
                else

                    GenerateBachScript_Profile_add(new_Generate_Cards, strProfile);
                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");

                add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");


                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }

                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    Add_to_NumberPrint_cards_toDB(dbUser);
                }

                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.SaveTo_excel) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف اكسل"); CreateExcel(dbUser); }
                if (clss_InfoPrint.SaveTo_script_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف سكربت"); Create_Script_File(dbUser); }
                if (clss_InfoPrint.SaveTo_text_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف نصي"); Create_Text_File(dbUser); }

                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     frm.LoadDatagridviewData();
                 });

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الطباعة بنجاح");

                Refesh_DGV_User();

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\nAddUserUserManagerScript" /*+ ex.ToString()*/);
                is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                startPrint = false;
            }
            startPrint = false;
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (MethodInvoker)delegate ()
                {
                    frm.btnAdd.Enabled = true;
                    frm.tableLayoutPanel1.Enabled = true;
                });
        }      

        private void Refesh_DGV_User()
        {
            try
            {

                Form_Cards_UserManager Openform2 = (Form_Cards_UserManager)RJMainForm.listChildForms.Find(x => x.Name == "Form_Cards_UserManager");
                if (Openform2 != null)
                {
                    Thread thread = new Thread(Openform2.formAllCardsUserManager.loadData);
                    thread.Start();
                }
            }
            catch { }

        }
        [Obsolete]
        public void AddUserUserd_batch_cards()
        {
            startPrint = true;
            try
            {
                HashSet<string> card_copy = new HashSet<string>();
                if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
                {
                    //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    //    card_copy = db.ColumnDistinct<string>(db.From<UmUser>().Where(x => x.DeleteFromServer == 0).Select(x => x.UserName));
                    //    card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));

                    //}
                    card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));

                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_if_RunOffline());
                }
                else
                {
                    if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                        card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
                }

                if (clss_InfoPrint.With_Archive_uniqe)
                {
                    //=========  get cards from archive  and copy  to hashset card_copy
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
                }

                //int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;
                //if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
                //    card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_if_RunOffline());
                //else
                //{
                //    if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                //        card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
                //}

                //if (clss_InfoPrint.With_Archive_uniqe)
                //{
                //    //=========  get cards from archive  and copy  to hashset card_copy
                //    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
                //}

                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

                string mesgBtn = "يتم الان اضافة الكروت الى اليوزرمنجر";
                if (is_add_batch_cards_to_Archive)
                    mesgBtn = "يتم الان اضافة الكروت الى الارشيف";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);
                     
                // ====== توليد الكروت العشوائية ============================
                CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
                new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Public_Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return;
                }
               
                //========= تجهيز سكربت الاضافة الي المايكروتك =================
                Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);

                //=========== فحص اذا طريقة الاضافة سكربت واحد لليوزر والبروفايل او فصل سكربت اضافة البروفايل وحده =========
                bool _check_if_use_2Scritp_add = check_if_use_2Scritp_add();

                //======= الاضافة الي المايكروتك ========================

                Dictionary<string, string> res = null;
                if (_check_if_use_2Scritp_add == true)
                {
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    startPrint = false;
                    return;
                }
                res = GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
                if (res["status"] == "false")
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return;
                }
              
                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                //string path = @"tempCards\script\batch\";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch\\";
                string path_SmartErorrCards = path + "SmartErorrCards.rsc";
                string path_SmartErorrProfile =  path + "SmartErorrProfile.rsc";
                var Users_lines = File.ReadAllLines(path_SmartErorrCards);
                List<string> user_erorr = new List<string>();
                for (var i = 0; i < Users_lines.Length; i += 1)
                {
                    var line = Users_lines[i];
                    user_erorr.Add(line.Trim());
                }
                var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
                List<string> profile_erorr = new List<string>();

                for (var i = 0; i < Profiles_lines.Length; i += 1)
                {
                    var line = Profiles_lines[i];
                    profile_erorr.Add(line.Trim());
                }

                string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
                string[] profile_split = profile_erorr.ToArray();

                if (user_split.Length > 0)
                {
                    for (int i = 0; i < user_split.Length; i++)
                        new_Generate_Cards.dicUser.Remove(user_split[i]);
                    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                    CountTry = 5;
                    new_Generate_Cards.dicUser = GenerateIfLastErorr_batch_cards(new_Generate_Cards.dicUser, user_split.Length, cLS_Genrate_Cards);
                }
                //====== check if error add profile   or  _check_if_use_2Scritp_add ================== 
                if (profile_split.Length > 1)
                {
                    AddProfile_ErorreCards(profile_split.ToList());
                }

                try { File.Delete(path_SmartErorrCards); } catch { }
                try { File.Delete(path_SmartErorrProfile); } catch { }
                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");

                List<UmUser> dbUser = add_sn_to_local_dbUser(new_Generate_Cards);
                add_to_db(dbUser);
                


                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    Add_to_NumberPrint_cards_toDB(dbUser);
                }

                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.SaveTo_excel) { CreateExcel(dbUser); }
                if (clss_InfoPrint.SaveTo_script_File) { Create_Script_File(dbUser); }
                if (clss_InfoPrint.SaveTo_text_File) { Create_Text_File(dbUser); }

                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    frm.LoadDatagridviewData();
                });

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الطباعة بنجاح");

                Refesh_DGV_User();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\nAddUserUserd_batch_cards" /*+ ex.ToString()*/); is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                startPrint = false;
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
            }
            startPrint = false;

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
        }

        [Obsolete]
        public void AddUserUser_one(string username="",string password= "")
        {
            startPrint = true;
            try
            {
                int Public_Number_Cards_ToAdd = 1;
                clss_InfoPrint.Number_Cards_ToAdd=1;
                string mesgBtn = "يتم   اضافة الكرت الى اليوزرمنجر";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

                Dictionary<string, NewUserToAdd> dicUser=new Dictionary<string, NewUserToAdd>();
                dicUser.Add(username, new NewUserToAdd { Name = username,Password=password,SN=0 });
                New_Generate_Cards new_Generate_Cards=new New_Generate_Cards {dicUser=dicUser,strUser="",strPass="",status=true };
                Dictionary<string,string> User=new Dictionary<string,string>();

                //Attributes = Global_Variable.Mk_resources.version >= 7 ? dUser["attributes"] : "",
                //    Group = Global_Variable.Mk_resources.version >= 7 ? dUser["group"] : "",

                User["attributes"] = "";
                User["group"] = "default";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    if (clss_InfoPrint.is_use_Attribut)
                        User["attributes"] = clss_InfoPrint.Attribut;

                    User["group"] = clss_InfoPrint.Group;

                    //result["Public_Group"] = " group=" + result["Public_Group"];
                    //result["note"] = " comment=\"" + Comment_not7 + "\"";
                }



                User["username"] = username;
                User["password"] = password;
                User["profile"] = clss_InfoPrint.Profile_Name;
                User["customer"] = clss_InfoPrint.Custumer_UserMan;
                User["shardUser"] = clss_InfoPrint.ShardUser;
                User["location"]=clss_InfoPrint.SellingPoint_Value_str;
                User["lastName"] = clss_InfoPrint.SellingPoint_Value_str;
                User["comment"] = clss_InfoPrint.Comment;
                if (clss_InfoPrint.FirstUse)
                 User["firstUser"] = "yes";
                else
                    User["firstUser"] = "no";

                if(Global_Variable.Mk_resources.version >= 7)
                {

                }

               string id_user= Mk_DataAccess.add_one_user_manager(User);
                double sn = 0;
                if (id_user == null || id_user =="")
                {
                    RJMessageBox.Show("خطاء قد يكون الاسم مكرر");
                    Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "خطاء");


                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (MethodInvoker)delegate ()
                {
                    frm.btnAdd.Enabled = true;
                    frm.tableLayoutPanel1.Enabled = true;
                });
                    return;
                }
                 sn = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
               
                new_Generate_Cards.dicUser.ElementAt(0).Value.SN = sn;
                Global_Variable.Source_Users_UserManager_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(0).Key);
                if (new_Generate_Cards.dicUser.Count == 0)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return;
                }
                #region تجهيز كلاس اليوزر عشان الاضافة الي قاعده البيانات المحلية واضافة اول كرت قبل الدخول للدوارة
                float totalPrice = clss_InfoPrint.profile.Price;
                float price = clss_InfoPrint.profile.Price;
                string Price_Disply = clss_InfoPrint.profile.Price_Disply;
                float percentage = 0;
                int percentage_type = 0;
                if (clss_InfoPrint.SellingPoint != null)
                {
                    if (clss_InfoPrint.SellingPoint.Is_percentage == 1)
                    {
                        if (clss_InfoPrint.SellingPoint.Is_percentage_Custom == 1)
                        {
                            if (comm_Sellings.Count > 0)
                            {
                                Comm_SellingPoint com=comm_Sellings.Find(x=>(x.SpCode == clss_InfoPrint.SellingPoint.Code) && (x.ProfileName == clss_InfoPrint.profile.Name) && (x.Is_percentage==1));
                                if(com!=null)
                                {
                                    percentage_type = com.PercentageType;
                                    percentage = com.Percentage;
                                    if (percentage_type == 0)
                                    {
                                        float percentage_value = (price * percentage) / 100;
                                        totalPrice = price - percentage_value;
                                    }
                                    else
                                    {
                                        totalPrice = price - percentage;
                                    }
                                }

                            }
                        }
                        else
                        {
                            percentage_type = clss_InfoPrint.SellingPoint.PercentageType;
                            percentage = clss_InfoPrint.SellingPoint.Percentage;
                            if (percentage_type == 0)
                            {
                                float percentage_value = (price * percentage) / 100;
                                totalPrice = price - percentage_value;
                            }
                            else
                            {
                                totalPrice = price - percentage;
                            }
                        }
                    }
                    else if (clss_InfoPrint.profile.Is_percentage == 1)
                    {
                        percentage_type = clss_InfoPrint.profile.PercentageType;
                        percentage = clss_InfoPrint.profile.Percentage;
                        if (percentage_type == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                    }
                }
                else if (clss_InfoPrint.profile.Is_percentage == 1)
                {
                    percentage_type = clss_InfoPrint.profile.PercentageType;
                    percentage = clss_InfoPrint.profile.Percentage;
                    if (percentage_type == 0)
                    {
                        float percentage_value = (price * percentage) / 100;
                        totalPrice = price - percentage_value;
                    }
                    else
                    {
                        totalPrice = price - percentage;
                    }
                }

                List<UmUser> dbUser = new List<UmUser>();
                UmUser db = new UmUser();

                db.IdHX = "*" + Convert.ToInt32(sn).ToString("X");
                db.SN = (long)sn;
                db.UserName = new_Generate_Cards.dicUser.ElementAt(0).Value.Name;
                db.Sn_Name = sn + "-" + new_Generate_Cards.dicUser.ElementAt(0).Value.Name;
                db.Password = new_Generate_Cards.dicUser.ElementAt(0).Value.Password;
                db.ProfileName = clss_InfoPrint.profile.Name;
                db.CustomerName = clss_InfoPrint.Custumer_UserMan;
                //db.regDate = (Int32)info_print["regDate"];
                db.Comment = clss_InfoPrint.Comment;
                //db.lastName = info_print["lastName"].ToString();
                db.Location = clss_InfoPrint.SellingPoint_Value_str;
                db.UptimeLimit = (long)clss_InfoPrint.profile.UptimeLimit;
                db.TransferLimit = (long)clss_InfoPrint.profile.TransferLimit;
                db.ValidityLimit = (long)clss_InfoPrint.profile.Validity * 24 * 60 * 60;
                db.ProfileValidity = (long)clss_InfoPrint.profile.Validity * 24 * 60 * 60;


                db.ProfileTimeLeft = (long)clss_InfoPrint.profile.UptimeLimit;
                db.CountProfile = 1;

                db.SharedUsers = clss_InfoPrint.ShardUser;
                db.TotalPrice = totalPrice;
                db.Price = price;
                db.Price_Disply = clss_InfoPrint.profile.Price_Disply;
                db.Percentage = percentage;
                db.PercentageType = percentage_type;

                //db.SpId = clss_InfoPrint.SellingPoint.Id;
                db.SpCode = clss_InfoPrint.SellingPoint_Value;
                db.SpName = clss_InfoPrint.SellingPoint_Name;
                //db.BatchCardId = clss_InfoPrint.NumberPrint;

                //db.NumberPrint = clss_InfoPrint.NumberPrint;
                //db.BatchCardId = clss_InfoPrint.BatchNumber;

                db.DeleteFromServer = 0;
                //db.AddedDb = clss_InfoPrint.regDate;
                db.RegDate = clss_InfoPrint.regDate;

                if(clss_InfoPrint.FirstUse)
                {
                    db.Caller_id_yes_no = "yes";
                    db.CallerMac = "bind";
                }
                
                db.Status = 0;
                //db.regDate = clss_InfoPrint.regDate;
                dbUser.Add(db);

                #endregion

                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");
                add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { add_to_Batch_cards_toDB(dbUser); }
                //if (clss_InfoPrint.SaveTo_excel) { }
                //if (clss_InfoPrint.SaveTo_script_File) { }
                //if (clss_InfoPrint.SaveTo_text_File) { }

                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    frm.LoadDatagridviewData();
                });

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الاضافة بنجاح");

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\nAddUserUser_one" /*+ ex.ToString()*/); is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                startPrint = false;
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
            }
            startPrint = false;
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });

            Refesh_DGV_User();
        }

        private int  CountTry = 5;
        [Obsolete]
        private Dictionary<string, NewUserToAdd> GenerateIfLastErorr(Dictionary<string, NewUserToAdd> dicUser, int number_user, CLS_Generate_Random_Cards cLS_Genrate_Cards,int CountTry = 5)
        {
            if (CountTry < 1)
                return dicUser;
            CountTry = CountTry - 1;

            Dictionary<string, NewUserToAdd> _dicUser = dicUser;
            New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(number_user, "", false);
            if (new_Generate_Cards == null)
                return _dicUser;

            Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
            Dictionary<string, string> res = GenerateBachScriptUser(variableScript);
            if (res["status"] == "false")
                return _dicUser;

            string[] Res_split = res["result"].Split(new string[] { ";" }, StringSplitOptions.None);
            string[] user_split = Res_split[2].Split(new string[] { "|" }, StringSplitOptions.None);  //====== check if error add user ========

            if (user_split.Length > 1)
            {
                for (int i = 1; i < user_split.Length; i++)
                    new_Generate_Cards.dicUser.Remove(user_split[i]);

                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                GenerateIfLastErorr(_dicUser, user_split.Length-1, cLS_Genrate_Cards);
            }
            else
            {
                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                return _dicUser;
            }

        
            return _dicUser;
        }
        [Obsolete]
        public Dictionary<string, NewUserToAdd> GenerateIfLastErorr_batch_cards(Dictionary<string, NewUserToAdd> dicUser, int number_user, CLS_Generate_Random_Cards cLS_Genrate_Cards)
        {
            if (CountTry < 1)
                return dicUser;
            CountTry = CountTry - 1;

            Dictionary<string, NewUserToAdd> _dicUser = dicUser;
            New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(number_user, "", false);
            if (new_Generate_Cards == null)
                return _dicUser;

            Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
            Dictionary<string, string> res = GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
            if (res["status"] == "false")
                return _dicUser;

            //string path = @"tempCards\script\batch\";
            string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch\\";

            string path_SmartErorrCards = path + "SmartErorrCards.rsc";
            string path_SmartErorrProfile = path + "SmartErorrProfile.rsc";
            var Users_lines = File.ReadAllLines(path_SmartErorrCards);
            List<string> user_erorr = new List<string>();
            for (var i = 0; i < Users_lines.Length; i += 1)
            {
                var line = Users_lines[i];
                user_erorr.Add(line.Trim());
            }

            var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
            List<string> profile_erorr = new List<string>();

            for (var i = 0; i < Profiles_lines.Length; i += 1)
            {
                var line = Profiles_lines[i];
                profile_erorr.Add(line.Trim());
            }

            string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
            string[] profile_split = profile_erorr.ToArray();

            if (user_split.Length > 1)
            {
                for (int i = 0; i < user_split.Length; i++)
                    new_Generate_Cards.dicUser.Remove(user_split[i]);

                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                GenerateIfLastErorr_batch_cards(_dicUser, user_split.Length - 1, cLS_Genrate_Cards);
            }
            else
            {
                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                return _dicUser;
            }


            return _dicUser;
        }

        //[Obsolete]
        [Obsolete]
        public List<UmUser> add_sn_to_local_dbUser(New_Generate_Cards new_Generate_Cards,bool formArchive=false)
        {
            strProfile = "";
            //===== الاستعلام عن الكرت الاول ومعرفه الرقم التسلسلي تبعه عشان التسلسل للبقية =======

            SourceCardsUserManager_fromMK firstUser = SourceCardsUserManager_fromMK.Get_one_UM_User(new_Generate_Cards.dicUser.ElementAt(0).Key, false).FirstOrDefault();
            double sn = 0;
            if (firstUser.id != null)
                sn = Int32.Parse(firstUser.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);

            #region تجهيز كلاس اليوزر عشان الاضافة الي قاعده البيانات المحلية واضافة اول كرت قبل الدخول للدوارة
            List<UmUser> dbUser = new List<UmUser>();

            //============= حساب النسبه للباقة او البقالة ===============================-==-
        
            float totalPrice = clss_InfoPrint.profile.Price;
            float price = clss_InfoPrint.profile.Price;
            string Price_Disply = clss_InfoPrint.profile.Price_Disply;
            float percentage = 0;
            int percentage_type = 0;
            
            if(clss_InfoPrint.SellingPoint != null)
            {
                if( clss_InfoPrint.SellingPoint.Is_percentage == 1)
                {

                    if (clss_InfoPrint.SellingPoint.Is_percentage_Custom == 1)
                    {
                        if (comm_Sellings.Count > 0)
                        {
                            Comm_SellingPoint com = comm_Sellings.Find(x => (x.SpCode == clss_InfoPrint.SellingPoint.Code) && (x.ProfileName == clss_InfoPrint.profile.Name) && (x.Is_percentage == 1));
                            if (com != null)
                            {
                                percentage_type = com.PercentageType;
                                percentage = com.Percentage;
                                if (percentage_type == 0)
                                {
                                    float percentage_value = (price * percentage) / 100;
                                    totalPrice = price - percentage_value;
                                }
                                else
                                {
                                    totalPrice = price - percentage;
                                }
                            }

                        }
                    }
                    
                }
                else if(clss_InfoPrint.profile.Is_percentage==1)
                {
                    percentage_type = clss_InfoPrint.profile.PercentageType;
                    percentage = clss_InfoPrint.profile.Percentage;
                    if (percentage_type == 0)
                    {
                        float percentage_value = (price * percentage) / 100;
                        totalPrice = price - percentage_value;
                    }
                    else
                    {
                        totalPrice = price - percentage;
                    }
                }
            }
            else if (clss_InfoPrint.profile.Is_percentage == 1)
            {
                percentage_type = clss_InfoPrint.profile.PercentageType;
                percentage = clss_InfoPrint.profile.Percentage;
                if (percentage_type == 0)
                {
                    float percentage_value = (price * percentage) / 100;
                    totalPrice = price - percentage_value;
                }
                else
                {
                    totalPrice = price - percentage;
                }
            }


            if (clss_InfoPrint.SellingPoint != null)
            {
                if (Convert.ToBoolean(clss_InfoPrint.SellingPoint.Is_percentage))
                {
                    percentage_type = clss_InfoPrint.SellingPoint.PercentageType;
                    percentage = clss_InfoPrint.SellingPoint.Percentage;
                    if (clss_InfoPrint.SellingPoint.PercentageType == 0)
                    {
                        percentage = (clss_InfoPrint.SellingPoint.Percentage * clss_InfoPrint.profile.Price) / 100;
                        totalPrice = totalPrice - percentage;
                    }
                    else
                    {
                        totalPrice = totalPrice - percentage;
                    }
                }
            }
            //==============================
            int? Number_Card_In_Page = Calclate_Number_Card_In_Page();

            for (int i = 0; i < new_Generate_Cards.dicUser.Count; i++)
            {
                //sn = sn + 1;
                int? PageNumber = null;
                new_Generate_Cards.dicUser.ElementAt(i).Value.SN = sn;
                Global_Variable.Source_Users_UserManager_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(i).Key);
                string id_user = "*" + Convert.ToInt32(sn).ToString("X");
                strProfile += "\"" + id_user + "\"" + ",";


                UmUser db = new UmUser();
                db.IdHX = id_user;
                db.SN = (long)sn;
                db.UserName = new_Generate_Cards.dicUser.ElementAt(i).Value.Name;
                db.Sn_Name = sn + "-" + new_Generate_Cards.dicUser.ElementAt(i).Value.Name;
                db.Password = new_Generate_Cards.dicUser.ElementAt(i).Value.Password;
                db.ProfileName = clss_InfoPrint.profile.Name;
                db.CustomerName = clss_InfoPrint.Custumer_UserMan;

                db.Comment = clss_InfoPrint.Comment;
                //db.LastName = clss_InfoPrint.
                db.Location = clss_InfoPrint.SellingPoint_Value_str;

                db.UptimeLimit = (long)(clss_InfoPrint.profile.UptimeLimit);
                db.TransferLimit = (long)clss_InfoPrint.profile.TransferLimit;
                db.ValidityLimit = (long)clss_InfoPrint.profile.Validity*24*60*60;
                db.ProfileValidity = (long)clss_InfoPrint.profile.Validity*24*60*60;

                db.ProfileTimeLeft = (long)clss_InfoPrint.profile.UptimeLimit;
                db.ProfileTransferLeft = (long)clss_InfoPrint.profile.TransferLimit;
                db.CountProfile = 1;
                db.SharedUsers = clss_InfoPrint.ShardUser;

                db.TotalPrice = totalPrice;
                db.Price = price;
                db.Price_Disply = clss_InfoPrint.profile.Price_Disply;
                db.Percentage = percentage;
                db.PercentageType = percentage_type;

                //db.SpId = clss_InfoPrint.SellingPoint.Id;
                db.SpCode = clss_InfoPrint.SellingPoint_Value;
                db.SpName = clss_InfoPrint.SellingPoint_Name;
                //db.BatchCardId = clss_InfoPrint.NumberPrint;

                db.NumberPrint = clss_InfoPrint.NumberPrint;
                db.BatchCardId = clss_InfoPrint.BatchNumber;

                db.AddedDb = clss_InfoPrint.regDate;
                db.RegDate = clss_InfoPrint.regDate;
                db.LastSynDb = clss_InfoPrint.regDate;

                db.Status = 0;
                db.DeleteFromServer = 0;

                if (clss_InfoPrint.FirstUse)
                {
                    db.Caller_id_yes_no = "yes";
                    db.CallerMac = "bind";
                }


                if (formArchive)
                    db.PageNumber = new_Generate_Cards.dicUser.ElementAt(i).Value.PageNumber;
                else
                {
                    try
                    {
                        PageNumber = (int?)((i) / Number_Card_In_Page) + 1;
                    }
                    catch { }
                    db.PageNumber = PageNumber;
                }
                db.Sn_Archive= new_Generate_Cards.dicUser.ElementAt(i).Value.SN_Archive;

                dbUser.Add(db);
                sn = sn + 1;
            }
            return dbUser;
            #endregion


        }

        [Obsolete]
        public void AddProfile_ErorreCards(List<string> user)
        {
            Mk_DataAccess.add_Profile_User_ToUserManager(user, clss_InfoPrint.Custumer_UserMan, clss_InfoPrint.profile.Name);
        }
        public Dictionary<string, string> Get_VariableGenerateBachScriptUser(string strUser, string strPass)
        {
            Dictionary<string, string> result= new Dictionary<string, string>();
            result["strUser"] = "";
            result["strPass"] = "";
            result["valName"] = "";
            result["firstUser"] = "";
            result["location"] = "";
            result["lastName"] = "";
            result["shardUser"] = "";
            result["note"] = "";
            result["Custumer"] = "";
            result["Profile"] = "";
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(new char[] { ';' });
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(',');
            strPass = strPass.TrimEnd(',');

            result["strUser"]= strUser;
            result["strPass"] = strPass; 
            result["shardUser"] = " shared-users=" + clss_InfoPrint.ShardUser;
            if(Global_Variable.Mk_resources.version <= 6 )
                result["Custumer"] = clss_InfoPrint.Custumer_UserMan;
            result["Profile"] = clss_InfoPrint.profile.Name;

            result["Public_Attribut"] = "";
            result["Public_Group"] = "default";
            result["Public_is_use_Attribut"] = clss_InfoPrint.is_use_Attribut.ToString();

            result["valName"] = "username";
            if (Global_Variable.Mk_resources.version <= 5 || Global_Variable.Mk_resources.version >=7)
                result["valName"] = "name";
            if (Global_Variable.Mk_resources.version == 6 && Global_Variable.Mk_resources.verisonAfter_Float <= 30)
                result["valName"] = "name";

            CultureInfo English = new CultureInfo("en-US");
            string Comment_not7 = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss", English);
           
            if (clss_InfoPrint.FirstUse)
            {
                if (Global_Variable.Mk_resources.version <= 6)
                    result["firstUser"] = " caller-id-bind-on-first-use=yes";
                else
                    result["firstUser"] = " caller-id=bind";
            }
            
            if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value!= "-1" && clss_InfoPrint.SellingPoint_Value != null)
            //if (((int)(info_print["Public_SellingPoint_Value"]) != -1 && (int)(info_print["Public_SellingPoint_Value"]) != 0) || (info_print["Public_SellingPoint_Name"].ToString() != ""))
            {
                result["location"] = " location=" + clss_InfoPrint.SellingPoint_Value.ToString();
                Comment_not7 = Comment_not7 + "#" + clss_InfoPrint.SellingPoint_Value.ToString();
            }
            if (clss_InfoPrint.is_comment)
            {
                result["note"] = " comment=\"" + clss_InfoPrint.Comment.ToString().Trim() + "\"";
                if (clss_InfoPrint.Comment != null )
                if (clss_InfoPrint.Comment != "" )
                    Comment_not7 = Comment_not7 + "#" + clss_InfoPrint.Comment.ToString().Trim();
                    
                
            }
            if (Global_Variable.Mk_resources.version >= 7)
            {
                if (clss_InfoPrint.is_use_Attribut)
                    result["Public_Attribut"] = " attributes=" + clss_InfoPrint.Attribut;

                result["Public_Group"] = " group=" + clss_InfoPrint.Group;
                //result["Public_Group"] = " group=" + result["Public_Group"];
                result["note"] = " comment=\"" + Comment_not7 + "\"";
            }
          
            return result;
        }
        public bool check_if_use_2Scritp_add()
        {
            if (clss_InfoPrint.Mode_User_NumberORcharcter_Value != 0  )
            return false;
            int numberChik;
            int len = Convert.ToInt32(clss_InfoPrint.User_Long);
            if (clss_InfoPrint.StartCard.ToString() != "")
            {
                if (!(int.TryParse(clss_InfoPrint.StartCard.ToString(), out numberChik)))
                   return false;
                else 
                    len = len+ clss_InfoPrint.StartCard.ToString().Length; 
            }
            if (clss_InfoPrint.EndCard.ToString() != "")
            {
                if (!(int.TryParse(clss_InfoPrint.EndCard.ToString(), out numberChik)))
                    return false;
                else
                    len = len + clss_InfoPrint.EndCard.ToString().Length;
            }
            
            //double lastSn_db = Local_DA.Get_last_SN_UserManager();
            long lastSn_db =0;
              HashSet<long> users = new HashSet<long>(Local_DA.Load<long>("SELECT Sn FROM UmUser WHERE DeleteFromServer=0;"));
            if (users.Count > 0)
                lastSn_db = users.First();


            //double lastSn_db = SqlDataAccess.Get_last_SN_UserManager();
            int digits = lastSn_db == 0 ? 1 : (int)Math.Floor(Math.Log10(Math.Abs(lastSn_db)) + 1);
            if (clss_InfoPrint.is_add_batch_cards || clss_InfoPrint.is_add_batch_cards_to_Archive)
            {
                if (len <= digits)
                {
                    RJMessageBox.Show("يجب ان يكون طول الكرت اكبر من " + (len - digits)+len);
                    return true;
                }
            }
            if (len <= digits)
                return true;
                
            return false;
        }
        [Obsolete] 
        private Dictionary<string, string> GenerateBachScriptUser(Dictionary<string,string> varible)
        {
            string script = "";
            if (Global_Variable.Mk_resources.version >= 7)
            {
                if (clss_InfoPrint.UserPassword_Pattern == 0)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                         ":for i from=0 to=([:len $usr]-1) do={:do {[/user-manager/user/add  "+ varible["valName"] + "=[:pick $usr $i] " + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + "] ; " +
                         ":do {[/user-manager user-profile add user=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }
                if (clss_InfoPrint.UserPassword_Pattern == 1)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                          ":for i from=0 to=([:len $usr]-1) do={:do {[/user-manager/user/add " + varible["valName"] + "=[:pick $usr $i] password=[:pick $usr $i] " + varible["firstUser"] + varible["shardUser"] + varible["note"]  + varible["Public_Attribut"] + varible["Public_Group"] + " ]; " +
                          ":do {[/user-manager user-profile add user=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }
                if (clss_InfoPrint.UserPassword_Pattern == 2)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local Pwd [:toarray (" + varible["strPass"] + ")];:local us ;:local ps;" +
                        ":for i from=0 to=([:len $usr]-1) do={:do {[/user-manager/user add " + varible["valName"] + "=[:pick $usr $i] password=[:pick $Pwd $i] " + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + "] ; " +
                        ":do {[/user-manager user-profile add user=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }
            }
            else
            {
                if (clss_InfoPrint.UserPassword_Pattern == 0)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                      ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + "] ; " +
                      ":do {[/tool user-manager user create-and-activate-profile customer=\"" + varible["Custumer"] + "\"" + " numbers=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }

                if (clss_InfoPrint.UserPassword_Pattern == 1)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                      ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] password=[:pick $usr $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + " ]; " +
                      ":do {[/tool user-manager user create-and-activate-profile customer=\"" + varible["Custumer"] + "\"" + " numbers=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }
                if (clss_InfoPrint.UserPassword_Pattern == 2)
                {
                    script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local Pwd [:toarray (" + varible["strPass"] + ")];:local us ;:local ps;" +
                         ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] password=[:pick $Pwd $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + "] ; " +
                         ":do {[/tool user-manager user create-and-activate-profile customer=\"" + varible["Custumer"] + "\"" + " numbers=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
                }
            }
                
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script);

            return res;
        }
        [Obsolete]
        public Dictionary<string, string> GenerateBachScriptUser_batch_cards(New_Generate_Cards users, Dictionary<string, string> varible)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false"; res["result"] = "";

            string mk_File_Name = DateTime.Now.ToString("ddMMyyHHmmss") + ".rsc";
            //string path = @"tempCards\script\batch\";
            string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch";
            string file_Path_PC = path + "\\" + mk_File_Name;
            string SmartErorrCards_Path_PC = path + "\\" + "SmartErorrCards.rsc";
            string SmartErorrProfile_Path_PC = path + "\\" + "SmartErorrProfile.rsc";
           
            try{if (!Directory.Exists(path)){Directory.CreateDirectory(path);}}catch (Exception ex) { MessageBox.Show(" tempCards\\script\\batch\\ خطا في مسار حفظ الملف \n" + ex.Message.ToString()); }
            string script = "";
            if (Global_Variable.Mk_resources.version >= 7)
            {
                string row_user = "";
                for (int i = 0; i < users.dicUser.Count; i++)
                {
                    if (clss_InfoPrint.UserPassword_Pattern == 0)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /user-manager/user/add " + varible["valName"] + "=" + userName  + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + ";"
                        + " :do {/user-manager/user-profile add profile=" + varible["Profile"] + " user=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 1)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /user-manager/user/add " + varible["valName"] + "=" + userName + " password=" + userName  + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + ";"
                        + " :do {/user-manager/user-profile add profile=" + varible["Profile"] + " user=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 2)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        string password = users.dicUser.ElementAt(i).Value.Password;
                        row_user = ":do { /user-manager/user/add " + varible["valName"] + "=" + userName + " password=" + password + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + ";"
                        + " :do {/user-manager/user-profile add profile=" + varible["Profile"] + " user=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    File.AppendAllText(file_Path_PC, row_user + "\n", Encoding.ASCII);
                }
            }
            else
            {
                //string row_variable = " [:global SmartErorrCards ({});];[:global SmartErorrProfile ({});];";
                //string remove_variable = "[/system script environment remove [find name=\"SmartErorrCards\"]];[/system script environment remove [find name=\"SmartErorrProfile\"]];";
                //string row_variable = ":global SmartErorrCards [:toarray \"\"];:global SmartErorrProfile [:toarray \"\"];";
                string row_user = "";
                //File.AppendAllText(file_Path_PC,  row_variable + "\n", Encoding.ASCII);
                for (int i = 0; i < users.dicUser.Count; i++)
                {
                    if (clss_InfoPrint.UserPassword_Pattern == 0)
                    {
                        //string userName = users.dicUser.ElementAt(i).Value.Name;
                        //row_user = ":do { /tool user-manager user add " + varible["valName"] + "=" + userName + " customer=" + varible["Custumer"] + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["location"] + varible["lastName"] + ";"
                        //+ " :do {/tool user-manager user create-and-activate-profile customer=" + varible["Custumer"] + " profile=" + varible["Profile"] + " numbers=\"" + userName + "\";} on-error={:set SmartErorrProfile ($SmartErorrProfile,\"" + userName+"\");}} on-error={:set SmartErorrCards ($SmartErorrCards,\"" + userName+"\");}; ";

                        //string userName = users.dicUser.ElementAt(i).Value.Name;
                        //row_user = ":do { /tool user-manager user add " + varible["valName"] + "=" + userName + " customer=" + varible["Custumer"] + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["location"] + varible["lastName"] + ";"
                        //+ " :do {/tool user-manager user create-and-activate-profile customer=" + varible["Custumer"] + " profile=" + varible["Profile"] + " numbers=\"" + userName + "\";} on-error={:local num [/system script get [find name=\"SmartErorrProfile\"] source];[/system script set [find name=\"SmartErorrProfile\"] source=($num .\"" + userName + "=\")]}} on-error={:local num [/system script get [find name=\"SmartErorrCards\"] source];[/system script set [find name=\"SmartErorrCards\"] source=($num .\"" + userName + "=\")]}; ";

                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /tool user-manager user add " + varible["valName"] + "=" + userName +" customer=" + varible["Custumer"] + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["location"] + varible["lastName"] + ";"
                        + " :do {/tool user-manager user create-and-activate-profile customer=" + varible["Custumer"] + " profile=" + varible["Profile"] + " numbers=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 1)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /tool user-manager user add " + varible["valName"] + "=" + userName  +" password=" + userName + " customer=" + varible["Custumer"] + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["location"] + varible["lastName"] + ";"
                        + " :do {/tool user-manager user create-and-activate-profile customer=" + varible["Custumer"] + " profile=" + varible["Profile"] + " numbers=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 2)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        string password = users.dicUser.ElementAt(i).Value.Password;
                        row_user = ":do { /tool user-manager user add " + varible["valName"] + "=" + userName + " password=" + password + " customer=" + varible["Custumer"] + varible["firstUser"] + varible["shardUser"] + varible["note"] + varible["location"] + varible["lastName"] + ";"
                        + " :do {/tool user-manager user create-and-activate-profile customer=" + varible["Custumer"] + " profile=" + varible["Profile"] + " numbers=\"" + userName + "\";} on-error={/file set SmartErorrProfile.rsc contents=([/file get SmartErorrProfile.rsc contents] . \"" + userName + "\\n\");}} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\");}; ";
                    }
                    File.AppendAllText(file_Path_PC, row_user + "\n", Encoding.ASCII);
                }
            }
            
            bool added_file = add_batch_to_Mikrotik(file_Path_PC);
            if (added_file == false)
            {
                RJMessageBox.Show("خطا في ملف الاضافة");
                return res;
            }
            //========= add files =================
            using (File.Create(SmartErorrCards_Path_PC))
            {
                using (File.Create(SmartErorrProfile_Path_PC))
                {

                }
            }
             add_batch_to_Mikrotik(SmartErorrCards_Path_PC);
             add_batch_to_Mikrotik(SmartErorrProfile_Path_PC);

            Mk_DataAccess_old mk_DataAccess_Old = new Mk_DataAccess_old();
            Dictionary<string,string> res_mik = mk_DataAccess_Old.run_import_file(mk_File_Name);

            if (res_mik["status"] == "false")
            {
                RJMessageBox.Show("خطا في تنفيذ ملف الاضافة");

                frm.rest_port_mk_after();
                remove_file_import_after_print(mk_File_Name);
                remove_file_import_after_print("SmartErorrCards.rsc");
                remove_file_import_after_print("SmartErorrProfile.rsc");
                return res;
            }
           
            string path_SmartErorrCards = path + "\\" + "SmartErorrCards.rsc";
            string path_SmartErorrProfile = path + "\\" + "SmartErorrProfile.rsc";
            download_files_from_Mikrotik("SmartErorrCards.rsc");
            download_files_from_Mikrotik("SmartErorrProfile.rsc");
 
            
            
            res["status"] = "true";
            frm.rest_port_mk_after();
            remove_file_import_after_print(mk_File_Name);
            remove_file_import_after_print("SmartErorrCards.rsc");
            remove_file_import_after_print("SmartErorrProfile.rsc");

            try {File.Delete(file_Path_PC);}catch { }
            

            return res;
        }
        bool add_batch_to_Mikrotik(string file_ScriptName)
        {
            bool add = false;
            string CurrentDirectory = Directory.GetCurrentDirectory();
            //string path_html = CurrentDirectory + "\\" + "test";

            string path_html = file_ScriptName;
            //string path_html = CurrentDirectory + "\\" + file_ScriptName;
            //Process.Start(path_html); 
            string uploadfile = path_html;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(uploadfile, FileMode.Open))
                    {
                        client.BufferSize = 4 * 1024; // bypass Payload error large files
                        client.UploadFile(fileStream, Path.GetFileName(uploadfile));
                    }
                    add = true;
                }
            }
            catch { }
            return add;
        }
        bool download_files_from_Mikrotik(string file_ScriptName)
        {
            bool add = false;
            //string CurrentDirectory = Directory.GetCurrentDirectory()+"\\"+ "tempCards\\script\\batch\\";

            string CurrentDirectory = $"{utils.Get_TempCards_Script_Directory()}\\UserManager\\batch\\";

            //string path_html = file_ScriptName;
            string path_html = CurrentDirectory + file_ScriptName;
            string uploadfile = path_html;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(uploadfile, FileMode.Create))
                    {
                        client.BufferSize = 4 * 1024*1024; // bypass Payload error large files
                        //client.UploadFile(fileStream, Path.GetFileName(uploadfile));
                        client.DownloadFile(file_ScriptName, fileStream);
                    }
                    add = true;
                }
            }
            catch { }
            return add;
        }
        public void Download(string fileToDownload,string pathToDownload)
        {
            try
            {
                using (var sftpClient = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                using (var fs = new FileStream(Path.GetFileName(pathToDownload), FileMode.OpenOrCreate))
                {
                    sftpClient.Connect();
                    
                    sftpClient.DownloadFile(fileToDownload,fs,downloaded =>
                        {
                            //MessageBox.Show($"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                            //Global_Variable.Update_Um_StatusBar(false, true, 0, $"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                            Console.WriteLine($"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                        });

                    sftpClient.Disconnect();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
     
        }
        [Obsolete]
        public void remove_file_import_after_print(string fileName)
        {
            Mk_DataAccess da = new Mk_DataAccess();
            string result2 = da.remove_file_import_after_print(fileName);
        }

        public string BachScriptUser_ol_v7(string strUser, string strPass)
        {

           

            //DataAccess.CLS_DataAccess DAr = new DataAccess.CLS_DataAccess();
            //DataAccess.CLS_DataAccess DA = new DataAccess.CLS_DataAccess();

            //int aa= System.Text.ASCIIEncoding.Unicode.GetByteCount(script);
            //var howManyBytes = script.Length * sizeof(Char);
            //MessageBox.Show(aa.ToString()+"\n"+howManyBytes);
            ////MessageBox.Show(howManyBytes);
            //return "";
            //string result = DA.AddUserScript_smart(script);

            //DataAcess_V2 drun = new DataAcess_V2();
            //string result = drun.AddUserScript_smart_and_run(script);

            ////string id = DAr.RuntScrip_smart();
            ////string id = DA.AddUserScript(script);

            return "result";
        }
        [Obsolete]
        private Dictionary<string, string> GenerateBachScriptUser_2Scritp_add(Dictionary<string, string> varible)
        {

            if (Global_Variable.Mk_resources.version >= 7)
            {
            }

            string script = "";

            if (clss_InfoPrint.UserPassword_Pattern == 0)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;" +
                  ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + "] ; " +
                  "} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
            }

            if (clss_InfoPrint.UserPassword_Pattern == 1)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;" +
                  ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] password=[:pick $usr $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + " ]; " +
                  "} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
            }
            if (clss_InfoPrint.UserPassword_Pattern == 2)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local Pwd [:toarray (" + varible["strPass"] + ")];:local us ;" +
                     ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user add customer=\"" + varible["Custumer"] + "\" " + varible["valName"] + "=[:pick $usr $i] password=[:pick $Pwd $i] " + varible["firstUser"] + varible["location"] + varible["shardUser"] + varible["note"] + varible["lastName"] + "] ; " +
                     "} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
            }

            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script);

            return res;
        }
        [Obsolete]
        private Dictionary<string, string> GenerateBachScript_Profile_add(New_Generate_Cards user,string strUser)
        {
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(new char[] { ';' });
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(',');
            string customer = clss_InfoPrint.Custumer_UserMan.ToString();
            string profile = clss_InfoPrint.profile.Name.ToString();
            string script = "";
            if (Global_Variable.Mk_resources.version >= 7)
            {
            }
            else
            {
                

                script = "{:local usr [:toarray (" + strUser + ")];:local us ;" +
                      ":for i from=0 to=([:len $usr]-1) do={:do {[/tool user-manager user create-and-activate-profile customer=\"" + customer + "\"" + " numbers=[:pick $usr $i] profile=\"" + profile + "\"" + " ]; " +
                      "} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";
            }
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script);

            return res;
        }

        //[Obsolete]
        public void print_pdf(Dictionary<string, NewUserToAdd> dicUsers)
        {
            CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            CardsTemplate card = new CardsTemplate();
            string TemplateId = clss_InfoPrint.TemplateId.ToString();
            string TemplateName = clss_InfoPrint.TemplateName.ToString();
            SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);
            //Form_PDF_Prview pdf = new Form_PDF_Prview();


            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);
                CLS_Print.print_pdf(dicUsers, clss_InfoPrint, card, null);
                //pdf.print_pdf(dicUsers, clss_InfoPrint, card, null);
            }
            else if (Sourcecard.type == "table_Desigen1")
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                CLS_Print.print_pdf(dicUsers, clss_InfoPrint, null, cardTable1);
                //pdf.print_pdf(dicUsers, clss_InfoPrint, null, cardTable1);
            }

            return;
            //CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            //CardsTemplate card = new CardsTemplate();
            //string TemplateId = clss_InfoPrint.TemplateId.ToString();
            //string TemplateName = clss_InfoPrint.TemplateName.ToString();
            //SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);

           

            UmProfile profile = clss_InfoPrint.profile;
            //UserManager_Profile_UserManager profile = Global_Variable.UM_Profile.Find(x => x.Name == info_print["Public_Profile_Name"].ToString());
            
            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = profile.Name;
            string price = profile.Price.ToString();
            string Validity = profile.Validity.ToString();
            string time = profile.UptimeLimit.ToString();  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            string sizeTransfer = profile.TransferLimit.ToString();
            string SP = "";
            string numberPrint = "";
            string BatchNumber = "";
            string DatePrint = "";
            string Note_On_Pages_text = "";
            

            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);
               
                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    if (card.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                        }
                    }
                    if (card.cardsItems.Validity.title_show)
                    {
                        Validity = card.cardsItems.Validity.title_text + " " + Validity;
                    }
                }
                if (card.cardsItems.Time.Enable)
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                        if (card.cardsItems.Time.title_show)
                        {
                            time = card.cardsItems.Time.title_text + " " + time;
                        }

                    }
                }
                if (card.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                        if (card.cardsItems.Size.title_show)
                        {
                            sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                        }

                    }
                }
                
                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrint != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.get_BatchCards_my_sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();  
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (card.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value !=null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                         
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (Show_sp.Code).ToString();
                        else
                            SP = (Show_sp.UserName).ToString();
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                if (cardTable1.cardsItems.Price.Enable)
                {
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = price + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    if (cardTable1.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                        }
                    }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    try
                    {
                        if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        }
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                    }
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (cardTable1.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                         
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                        if (Show_sp != null)
                        {
                            if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                                SP = (Show_sp.Code).ToString();
                            else
                                SP = (Show_sp.UserName).ToString();
                        }
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("numberPrint", numberPrint);
            Cardsdata.Add("BatchNumber", BatchNumber);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (Sourcecard.type == "design")
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);
            else if (Sourcecard.type == "table_Desigen1")
            {
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            }


            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            //MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            //try
            //{
            //    System.Diagnostics.Process.Start(pathfile);
            //}
            //catch { }
        }
      
        private string generate_BachScriptUser_As_script(Dictionary<string, string> varible)
        {
            string result = "";
            //try
            //{
            //    string valName = "username";
            //    string valPassword = "";
            //    string last_name = "";
            //    string location = "";
            //    string crated = DateTime.Now.ToString("dd-MM-yyyy");
            //    string time_crated = DateTime.Now.ToString("HH:mm:ss");
            //    string date_crate = crated + " " + time_crated;
            //    string batch = "";
            //    int page_num = 1;


            //    string _file_Name2 = clss_InfoPrint.Number_Cards_ToAdd + "-" + clss_InfoPrint.profile.Name + "-" + crated + "-" + time_crated.Replace(":", "");
            //    string path = @"tempCards\script\batch\";
            //    string file_ScriptName = path + "\\" + _file_Name2 + ".rsc";
            //    try
            //    {
            //        if (!Directory.Exists(path))
            //        {
            //            Directory.CreateDirectory(path);
            //        }
            //    }
            //    catch (Exception ex) { MessageBox.Show(" خطا في مسار حفظ الملف \n" + ex.Message.ToString()); }

            //    //MessageBox.Show(file_ScriptName);
            //    if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
            //    FileStream fs = File.Create(file_ScriptName);
            //    fs.Close();
            //    TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
            //    writeFile.Close();
            //    int sqence = 0;
            //    string comment = "";
            //    string phone = "";
            //    if (is_add_batch_cards)
            //    {
            //        if (clss_InfoPrint.UserPassword_Pattern == 1)
            //            valPassword = " password=" + user[0, 0];
            //        if (clss_InfoPrint.UserPassword_Pattern == 2)
            //            valPassword = " password=" + user[0, 1];

            //        string row_user = "";
            //        string row_prof = "";

            //        row_user = ":do { /tool user-manager user add " + valName + "=" + user[0, 0] + valPassword + " customer=" + varible["Custumer"] + last_name + location + ";";
            //        row_prof = " /tool user-manager user create-and-activate-profile customer=" + Public_Custumer_UserMan + " profile=" + Public_Profile_Name + " numbers=\"" + user[0, 0] + "\";} on-error={:log info \"erorr add " + user[0, 0] + "\"}; ";

            //        if (MyDataClass.verison > 6)
            //        {
            //            row_user = ":do {/user-manager/user/add " + "name" + "=" + user[0, 0] + valPassword + note + ";";
            //            row_prof = "/user-manager/user-profile/add user=" + user[0, 0] + " profile=\"" + Public_Profile_Name + "\"; } on-error={:log info \"erorr add " + user[0, 0] + "\"}; ";
            //        }

            //        File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);

            //    }
            //    sqence = 1;
            //    for (int i = sqence; i < user.GetLength(0); i++)
            //    {
            //        if (Public_UserPassword_Pattern == "1")
            //            valPassword = " password=" + user[i, 0];
            //        if (Public_UserPassword_Pattern == "2")
            //            valPassword = " password=" + user[i, 1];

            //        string row_user = "";
            //        string row_prof = "";
            //        if (MyDataClass.verison <= 6)
            //        {
            //            row_user = "/tool user-manager user add " + valName + "=" + user[i, 0] + valPassword + " customer=" + Public_Custumer_UserMan + last_name + location + ";";
            //            row_prof = " /tool user-manager user create-and-activate-profile customer=" + Public_Custumer_UserMan + " profile=" + Public_Profile_Name + " numbers=\"" + user[i, 0] + "\"; ";
            //            File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);

            //        }
            //        //string row_user = ":do { /tool user-manager user add " + valName + "=" + user[i,0] + valPassword + " customer=" + Public_Custumer_UserMan + last_name + location + ";";
            //        //string row_prof = " /tool user-manager user create-and-activate-profile customer=" + Public_Custumer_UserMan + " profile=" + Public_Profile_Name + " numbers=\"" + user[i,0] + "\";} on-error={:put \"erorr add " + user[i,0] + "\"}; ";

            //        else
            //        {

            //            row_user = "/user-manager/user/add " + "name" + "=" + user[i, 0] + valPassword + note + ";";
            //            row_prof = " /user-manager/user-profile/add user=" + user[i, 0] + " profile=\"" + Public_Profile_Name + "\"; ";

            //            //row_user = ":do {/user-manager/user/add " + "name" + "=" + user[i, 0] + valPassword + note + ";";
            //            //row_prof = " /user-manager/user-profile/add user=" + user[i, 0] + " profile=\"" + Public_Profile_Name + "\"; } on-error={:log warning \"erorr add " + user[i, 0] + "\"}; ";


            //            File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);

            //        }

            //    }




            //    if (is_add_batch_cards || Add_One_Card)
            //    {
            //        bool added_file = add_batch_to_Mikrotik(file_ScriptName);
            //        CLS_DataAccess da = new CLS_DataAccess();
            //        string tt = da.run_import_file(_file_Name2 + ".rsc");
            //        result = tt;
            //        rest_port_mk_after();
            //        remove_file_import_after_print(_file_Name2 + ".rsc");

            //    }
            //    else
            //    {
            //        result = add_batch_Archive_to_DB(user, sq, Public_Profile_Name, Public_Custumer_UserMan, comment, Public_SellingPoint_Value, phone, date_crate, batch, page_num);
            //    }
            //    //string script = "/import " + _file_Name2 + ".rsc";
            //    //string script = "/import " + "t.rsc";
            //    //DataAccess.CLS_DataAccess DA2 = new DataAccess.CLS_DataAccess();
            //    //result = DA2.AddUserScript2(script);

            //    return result;


            //}
            //catch (IOException ex)
            //{
            //    MessageBox.Show(ex.Message.ToString());
            //}
            
            return result;
        }


       public void add_to_db(List<UmUser> dbUser)
        {
            //Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
            Local_DA.Add_UMUser_ToDB(dbUser,true);
            //SqlDataAccess.Add_UM_user_to_LocalDB_sqlite2(dbUser,false,true);

            //SqlDataAccess.Add_UM_Pyement_to_LocalDB(List < SourcePymentUserManager_fromDB > UM_Pyement, bool is_insert = true)
        }
       public void Add_to_Batch_cards_toDB(List<UmUser> dbUser)
        {
            long sn_from = (long)dbUser.First().SN;
            long sn_to = (long)dbUser.Last().SN;

            BatchCard data = new BatchCard();
            //data.Id = clss_InfoPrint.BatchNumber;
            data.Id = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);

            data.Sn_from = sn_from;
            data.Sn_to = sn_to;
            //data.BatchNumber = clss_InfoPrint.BatchNumber;
            data.BatchNumber = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);

            data.ProfileName = clss_InfoPrint.profile.Name;

            data.AddedDate = clss_InfoPrint.regDate;
            data.Count = clss_InfoPrint.Number_Cards_ToAdd;
            //data.Rb = Global_Variable.Mk_Router.mk_code;
            data.Rb = Global_Variable.Mk_Router.mk_sn;
            data.SpCode = clss_InfoPrint.SellingPoint_Value;
            //data.Server = "hotspot"; 
            data.Server = 0;//userManager;
            data.BatchType = 0;//print;
            Smart_DA.Add_Batch_Cards(data, data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);

        }
       public void Add_to_NumberPrint_cards_toDB(List<UmUser> dbUser)
        {
            long sn_from = (long)dbUser.First().SN;
            long sn_to = (long)dbUser.Last().SN;

            NumberPrintCard data = new NumberPrintCard();
            data.Id = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);
            data.Sn_from = sn_from;
            data.Sn_to = sn_to;
            data.BatchNumber = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);
            data.NumberPrint = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);
            data.ProfileName = clss_InfoPrint.profile.Name;

            data.AddedDate = clss_InfoPrint.regDate;
            data.Count = clss_InfoPrint.Number_Cards_ToAdd;
            //data.Rb = Global_Variable.Mk_Router.mk_code;
            data.Rb = Global_Variable.Mk_Router.mk_sn;
            data.SpCode = clss_InfoPrint.SellingPoint_Value;
            data.Server = 0;//UserManager;
            data.BatchType = 0;//print;
            Smart_DA.Add_NumberPrint_Cards(data, data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);

        }

        //public void add_to_Batch_cards_toDB(List<UmUser> dbUser)
        //{
        //    long  sn_from=dbUser.First().SN;
        //    long sn_to = dbUser.Last().SN;

        //    //Class_Batch_cards data=new Class_Batch_cards();
        //    BatchCard data=new BatchCard();
        //    data.Id = clss_InfoPrint.NumberPrint;
        //    data.Sn_from = sn_from;
        //    data.Sn_to = sn_to;
        //    data.BatchNumber = clss_InfoPrint.NumberPrint;
        //    data.ProfileName = clss_InfoPrint.profile.Name;
        //    data.AddedDate = clss_InfoPrint.regDate;
        //    data.Count = clss_InfoPrint.Number_Cards_ToAdd;
        //    data.Rb = Global_Variable.Mk_Router.mk_code;
        //    data.SpCode = clss_InfoPrint.SellingPoint_Value;
        //    //data.Server = "usermanager"; 
        //    data.Server = 0;
        //    data.BatchType = 2;
        //    Smart_DA.Add_Batch_Cards(data,data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);  
        //    //SqlDataAccess.Add_Batch_Cards(data,data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);

        //}

        private int? Calclate_Number_Card_In_Page()
        {
            if (clss_InfoPrint.TemplateId == null)
                return null;

            CardsTemplate tcard = new CardsTemplate();
            SourceCardsTemplate sorceTemplates = SqlDataAccess.Get_template_cards_By_id(clss_InfoPrint.TemplateId);
            if (sorceTemplates == null)
                return null;
            if (sorceTemplates.type == "design")
            {
                CardsTemplate cardsTemplate = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplates.values);
                if (cardsTemplate == null)
                {
                    return null;
                }
                tcard = cardsTemplate;
            }
            else
                return null;

            //==========================================================================================  
            float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.space_horizontal_margin.ToString()));
            float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.Space_vertical_margin.ToString()));
            float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
            float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_height.ToString()));


            float Pictur_width_orginal = Pictur_width;
            float Pictur_height__orginal = Pictur_height;

            float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
            float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);

            //int NuberCards = 51;
            int NumberCard_for_OneColum = 0;
            int NumberCard_in_Page = 0;
            //double NumberPages = 0;

            int ColumNumber = 0;
            //float CardNumber = 0;

            ColumNumber = (int)(595 / (Pictur_width + Space_Y));
            if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                ColumNumber = ColumNumber - 1;

            NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
            if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                NumberCard_for_OneColum = NumberCard_for_OneColum - 1;


            NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

            //txt_NumberCard.Text = NumberCard_in_Page.ToString();
            //txt_NumberCulum.Text = ColumNumber.ToString();

            iTextSharp.text.Image jpg = null;

            return NumberCard_in_Page;


        }

        //===========================================================
        public void CreateExcel(List<UmUser> dbUser )
        {
            //string path = @"tempCards\Excel";
            string path = $"{utils.Get_TempCards_Excel_Directory()}\\UserManager";
            string sp = "";
            string file_ExceltName = "";
            if (clss_InfoPrint.SellingPoint != null)
                sp = clss_InfoPrint.SellingPoint.Code;

            //if (is_add_batch_cards_to_Archive)
            //    path = Cards_setting.path_saved_file;


            //string pathC = Directory.GetCurrentDirectory() + "\\tempCards\\Excel";
            //string time= DateTime.Now.Day.ToString()+ DateTime.Now.Month.ToString()+ DateTime.Now.Year.ToString();
            //file_ExceltName = path + $"\\Excel_{clss_InfoPrint.profile.Name}_{DateTime.Now.Day}.csv";
            file_ExceltName = path + "\\" + "Excel_" + Public_file_Name + ".csv";
            try
            {
                //int columnCount = dgvUserManager.Columns.Count;
                //string columnNames = "";
               
                string[] outputCsv = new string[dbUser.Count];
                int row = 0;
                foreach (UmUser user in dbUser)
                {
                    string UserName = "";
                    string Password = "";
                    string ProfileName = "";
                    string SpCode = "";

                    if (!string.IsNullOrEmpty(user.UserName))
                        UserName = user.UserName.ToString();
                    if (!string.IsNullOrEmpty(user.Password))
                        Password = user.Password.ToString();
                    if (!string.IsNullOrEmpty(user.ProfileName))
                        ProfileName = user.ProfileName.ToString();
                    if (!string.IsNullOrEmpty(user.SpCode))
                        SpCode = user.SpCode.ToString();




                    //outputCsv[i] += UserName + ",";
                    //outputCsv[i] += Password + ",";
                    //outputCsv[i] += ProfileName + ",";
                    //try { outputCsv[i] += SpCode + ","; } catch { }


                    outputCsv[row] += $"=\"{UserName ?? ""}\",=\"{Password ?? ""}\",=\"{ProfileName ?? ""}\",=\"{SpCode ?? ""}\"";


                    //outputCsv[row] += user.UserName.ToString() + ",";
                    //outputCsv[row] += user.Password.ToString() + ",";
                    //outputCsv[row] += clss_InfoPrint.profile.Name + ",";
                    //outputCsv[row] +=  sp + ",";

                    //outputCsv[row] += "=\"" + user.UserName.ToString() + "\",";
                    //outputCsv[row] += "=\"" + user.Password.ToString() + "\",";
                    //outputCsv[row] += "=\"" + clss_InfoPrint.profile.Name + "\",";
                    //outputCsv[row] += "=\"" + sp + "\",";

                    row++;
                }
               
                File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                //MessageBox.Show("Data Exported Successfully !!!", "Info");

                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                        System.Diagnostics.Process.Start(path);
                    }
                    catch { }
            }
            catch (Exception ex)
            {
                MessageBox.Show("export_execl :" + ex.Message);
            }



        }
        public void Create_Script_File(List<UmUser> dbUser)
        {
            try
            {
                string valName = "username";
                string valPassword = "";
                string last_name = "";
                string location = "";

                if (Global_Variable.Mk_resources.version <= 5)
                    valName = "name";
                if (Global_Variable.Mk_resources.version == 6 && Global_Variable.Mk_resources.verisonAfter_Float <= 30)
                    valName = "name";

                //if (checkBox_Disable_Printed_OR_notPrit.Checked)
                //    last_name = " last-name=0";
                if (clss_InfoPrint.SellingPoint !=null)
                    location = " location=" + clss_InfoPrint.SellingPoint.Code;

                //string path = @"tempCards\script";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\UserManager";


                //if (is_add_batch_cards_to_Archive)
                //    path = Cards_setting.path_saved_file;

                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "Script_" + Public_file_Name + ".rsc";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show("  خطا في مسار حفظ الملف النصي \n" + ex.Message.ToString()); }

                //MessageBox.Show(file_ScriptName);
                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);

                fs.Close();

                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();

                int row = 0;
                foreach (UmUser user in dbUser)
                {
                   
                        if (clss_InfoPrint.Mode_Password_NumberORcharcter == "1")
                            valPassword = " password=" + dbUser[row].UserName;
                        if (clss_InfoPrint.Mode_Password_NumberORcharcter == "2")
                            valPassword = " password=" + dbUser[row].Password;

                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        string row_user = ":do { /tool user-manager user add " + valName + "=" + dbUser[row].UserName + valPassword + " customer=" + clss_InfoPrint.Custumer_UserMan + last_name + location + ";";
                        string row_prof = " /tool user-manager user create-and-activate-profile customer=" + clss_InfoPrint.Custumer_UserMan + " profile=\"" + clss_InfoPrint.profile.Name+"\"" + " numbers=\"" + dbUser[row].UserName + "\";} on-error={:put \"erorr add " + dbUser[row].UserName + "\"}; ";
                        File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);
                    }
                    else
                    {
                       // script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                       //":for i from=0 to=([:len $usr]-1) do={:do {[/user-manager/user/add  " + varible["valName"] + "=[:pick $usr $i] " + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + "] ; " +
                       //":do {[/user-manager/user-profile/add user=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";


                        string row_user = ":do { /user-manager/user/add " + valName + "=" + dbUser[row].UserName + valPassword + " ;";
                        string row_prof = " /user-manager/user-profile/add user="+dbUser[row].UserName+"  profile="+"\\"+clss_InfoPrint.profile.Name+"\\"+ " ; on-error={:put \"erorr add " + dbUser[row].UserName + "\"}; ";
                        File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);

                    }
                    row = row + 1;
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show("Create_Script_File"+ex.Message.ToString());
            }
        }
        public void Create_Text_File(List<UmUser> dbUser)
        {
            try
            {
                //string path = @"tempCards\text";
                string path = $"{utils.Get_TempCards_Text_Directory()}\\UserManager";
                //if (is_add_batch_cards_to_Archive)
                //path = Cards_setting.path_saved_file;

                //if (is_add_batch_cards_to_Archive)
                //{
                //    path = @"tempCards\script\batch\";
                //}
                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "text_" + Public_file_Name + ".txt";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show(" خطا في مسار حفظ الملف النصي\n" + ex.Message.ToString()); }


                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);

                fs.Close();

                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();
                foreach (UmUser user in dbUser)
                {
                    File.AppendAllText(file_ScriptName, user.UserName + " " + user.Password + " " + clss_InfoPrint.profile.Name + "\n", Encoding.ASCII);
                }
                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ScriptName);
                    }
                    catch { }
            }
            catch (IOException ex)
            {
                MessageBox.Show(ex.Message.ToString());
            }
            //=========================================================================== :do {/interface bridge add name=loopback; } on-error={:put "erorr"}
            //}
        }

    }
}
