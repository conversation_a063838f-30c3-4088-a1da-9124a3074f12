﻿using SmartCreator.Forms.UserManager;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Hotspot
{
    public partial class Form_Cards_Hotspot : RJChildForm
    {
        FormAllBatchsCards formAllBatchsCards;
       public FormAllCardsHotspot formAllCardsUserHotspot;
        FormAllCardsHotspot formAll_From_RB_Archive;
        FormAllCardsHotspot formAll_From_Finsh_Cards;
        Form_AllSession_UserManager formAll_From_Session_Cards;

        bool First_formAllBatchsCards = true;
        bool First_formAllCards_From_RB = true;
        bool First_formAllCards_From_RB_Archive = true;
        bool First_formAllCards_From_Finsh_Cards = true;
        bool First_formAllCards_From_Session_Cards = true;
 


        public Form_Cards_Hotspot()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            this.Text = "ادارة كروت الهوتسبوت";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Cards Hotspot Manage";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
            Set_Font();
        }
        private void Set_Font()
        {
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 8 * utils.ScaleFactor, FontStyle.Bold);
            //Font fnt = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 8f, true);
            btn_All_Cards_RB_Title.Font = fnt;
            btn_Batch_Cards_Title.Font = fnt;
            btn_Finsh_Cards_Title.Font = fnt;
            btn_Sessions_Cards_Title.Font = fnt;
            btn_All_Cards_From_RB_Archive_Title.Font = fnt;
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_All_Cards_RB_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_All_Cards_RB_Title);

            if (First_formAllCards_From_RB)
            {
                First_formAllCards_From_RB = false;
                formAllCardsUserHotspot = new FormAllCardsHotspot("From_Server");
                formAllCardsUserHotspot.TopLevel = false;
                formAllCardsUserHotspot.IsChildForm = true;
                formAllCardsUserHotspot.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAllCardsUserHotspot);
                this.panel_Tab_Container.Tag = formAllCardsUserHotspot;
                formAllCardsUserHotspot.Show(); //show on desktop panel  
                formAllCardsUserHotspot.BringToFront();
                formAllCardsUserHotspot.Focus();
                //formAllCardsUserHotspot.LoadDataGridviewData();

            }
            else
            {
                formAllCardsUserHotspot.BringToFront();
                formAllCardsUserHotspot.Show();
                formAllCardsUserHotspot.Focus();
            }

        }

        private void Form_Cards_Hotspot_FormClosing(object sender, FormClosingEventArgs e)
        {
            //if (formAllCardsUserHotspot != null)
            //    formAllCardsUserHotspot.SaveFromState();
            //if (formAllBatchsCards != null)
            //    formAllBatchsCards.SaveFromState();

        }

        private void Form_Cards_Hotspot_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            btn_All_Cards_RB_Title_Click(sender, e);

        }

        private void btn_Batch_Cards_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Batch_Cards_Title);

            if (First_formAllBatchsCards)
            {
                First_formAllBatchsCards = false;
                formAllBatchsCards = new FormAllBatchsCards("HS");
                formAllBatchsCards.TopLevel = false;
                //formCardsByNumberPrint.FormBorderStyle = FormBorderStyle.None;
                formAllBatchsCards.IsChildForm = true;
                formAllBatchsCards.Dock = DockStyle.Fill;

                //form_AddUserManger.dgvUsersUM.DataSource = MyDataClass.SourceForPrint_UM_Users;

                this.panel_Tab_Container.Controls.Add(formAllBatchsCards);
                this.panel_Tab_Container.Tag = formAllBatchsCards;
                //formCardsByNumberPrint.BringToFront();
                //formCardsByNumberPrint.Show();

                formAllBatchsCards.Show(); //show on desktop panel  
                formAllBatchsCards.BringToFront();
                formAllBatchsCards.Focus();

                //formAllBatchsCards.LoadDataGridviewData();
            }
            else
            {
                formAllBatchsCards.BringToFront();
                formAllBatchsCards.Show();
                formAllBatchsCards.Focus();
            }

        }

        private void btn_All_Cards_From_RB_Archive_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_All_Cards_From_RB_Archive_Title);

            if (First_formAllCards_From_RB_Archive)
            {
                First_formAllCards_From_RB_Archive = false;
                formAll_From_RB_Archive = new FormAllCardsHotspot("From_RB_Archive");
                formAll_From_RB_Archive.TopLevel = false;
                formAll_From_RB_Archive.IsChildForm = true;
                formAll_From_RB_Archive.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAll_From_RB_Archive);
                this.panel_Tab_Container.Tag = formAll_From_RB_Archive;
                formAll_From_RB_Archive.Show(); //show on desktop panel  
                formAll_From_RB_Archive.BringToFront();
                formAll_From_RB_Archive.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAll_From_RB_Archive.BringToFront();
                formAll_From_RB_Archive.Show();
                formAll_From_RB_Archive.Focus();
            }
        }

        private void btn_Finsh_Cards_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Finsh_Cards_Title);

            if (First_formAllCards_From_Finsh_Cards)
            {
                First_formAllCards_From_Finsh_Cards = false;
                formAll_From_Finsh_Cards = new FormAllCardsHotspot("From_Finsh_Cards");
                formAll_From_Finsh_Cards.TopLevel = false;
                formAll_From_Finsh_Cards.IsChildForm = true;
                formAll_From_Finsh_Cards.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAll_From_Finsh_Cards);
                this.panel_Tab_Container.Tag = formAll_From_Finsh_Cards;
                formAll_From_Finsh_Cards.Show(); //show on desktop panel  
                formAll_From_Finsh_Cards.BringToFront();
                formAll_From_Finsh_Cards.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAll_From_Finsh_Cards.BringToFront();
                formAll_From_Finsh_Cards.Show();
                formAll_From_Finsh_Cards.Focus();
            }
        }

        private void btn_Sessions_Cards_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Sessions_Cards_Title);

            if (First_formAllCards_From_Session_Cards)
            {
                First_formAllCards_From_Session_Cards = false;
                formAll_From_Session_Cards = new Form_AllSession_UserManager("HS");
                formAll_From_Session_Cards.TopLevel = false;
                formAll_From_Session_Cards.IsChildForm = true;
                formAll_From_Session_Cards.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAll_From_Session_Cards);
                this.panel_Tab_Container.Tag = formAll_From_Finsh_Cards;
                formAll_From_Session_Cards.Show(); //show on desktop panel  
                formAll_From_Session_Cards.BringToFront();
                formAll_From_Session_Cards.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAll_From_Session_Cards.BringToFront();
                formAll_From_Session_Cards.Show();
                formAll_From_Session_Cards.Focus();
            }
            this.Text = "جلسات الهوتسبوت";
            lblCaption.Text = "جلسات الهوتسبوت";
            this.Refresh();
        }
    }
}
