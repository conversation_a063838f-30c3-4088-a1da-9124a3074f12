﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.SellingPoints;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SmartCreator.Forms.Hotspot
{
    public partial class FormAddHotspotCards : RJChildForm
    {
        private bool firstLoad = true;
        bool isFirstLoadForm = true;
        Form_PrintHotSpotState Frm_State;
        public FormAddHotspotCards()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            lbl_RegisterAsBatch.ForeColor = utils.Dgv_DarkColor;
            lbl_RegisterAs_LastBatch.ForeColor = utils.Dgv_DarkColor;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                pnl_Menul_profile.Customizable = false;
                pnl_usermanger.Customizable = false;
                pnl_profile_HS_local.Customizable = false;
            }
            this.Text = "اضافة كروت هوتسبوت";
            if (UIAppearance.Language_ar)
            {
            }
            else
            {
                this.Text = "Add Hotspot Users";
            }
            set_fonts();
            this.Refresh();
            
        }
        private void set_fonts()
        {
            if (UIAppearance.Theme == UITheme.Dark)
            {

                pnl_Menul_profile.Customizable = false;
            }

            txtNumberCard.RightToLeft 
                = txt_StartCard.RightToLeft
                = txt_EndCard.RightToLeft 
                = txt_last_batchNumber.RightToLeft
                = txt_longUsers.RightToLeft
                = txt_longPassword.RightToLeft = RightToLeft.No;

            lbl_radio_fast_print.Font = lbl_radio_Print.Font = lbl_radio_one_user.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10 , FontStyle.Bold);
            checkBox_Add_Smart_Validatiy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9.4f, FontStyle.Bold);

            Font lbl1 = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            lbl_Cbox_Profile_Select_Typ.Font = lbl_startCard.Font = lbl_endCards.Font = lbl_UserPassword_Pattern.Font
            = lbl_Profile_UserMan.Font = lbl_profile.Font = lbl_TemplateCards.Font = lbl_SellingPoint.Font
            = lbl_User_NumberORcharcter.Font = lbl_Pass_NumberORcharcter.Font = lbl_User_NumberORcharcter.Font = lbl_By_Number_Cards.Font
            = lbl_Profile_HotspotLocal.Font = lbl_Profile_HotspotLocal2.Font = lbl_profile2.Font = lbl_Server_hotspot.Font
            = btn_Script_Smart.Font= rjLabel1.Font
            = lbl1;

            lbl_FirstUse.Font = lbl_excel.Font = lbl_script_File.Font = lbl_RegisterAsBatch.Font
                = lbl_Save_PDF.Font = lbl_OpenAfterPrint.Font = lbl_With_Archive_uniqe.Font = lbl_RegisterAs_LastBatch.Font =
                Program.GetCustomFont(Resources.DroidSansArabic, 9.75f, FontStyle.Regular); 

            cbox_User_NumberORcharcter.Font = cbox_UserPassword_Pattern.Font = cbox_Pass_NumberORcharcter.Font = 
                Cbox_Profile_Select_Typ.Font = CBox_SellingPoint.Font= CBox_SizeDownload.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            //checkBox_Add_Smart_Validatiy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
            lbl_price.Font = rjLabel2.Font = lbl_houre.Font = lbl_validatiy.Font = lbl_download.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            btnAdd.Font = btn_add_One.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
            //lbl_price.Font = rjLabel2.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            lbl_Title2.Font = lbl_Title1.Font  = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Regular);


            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersHeight = 40;


            utils.Control_textSize(pnlClientArea);   
            utils.dgv_textSize(dgv);
            //utils.(dgv);
            //utils.dgv_textSize(dgv);

            return;



            utils.Control_textSize(tableLayoutPanel2);
            utils.Control_textSize(pnl_Right);
            utils.Control_textSize(tableLayoutPanel1);
            utils.Control_textSize(pnl_left);
            utils.Control_textSize(pnl_left2);
            utils.Control_textSize(panel2);
            utils.dgv_textSize(dgv);

            return;
            
            Font lbl_add_script_smart = Program.GetCustomFont(Resources.DroidKufi_Bold, 9 * utils.ScaleFactor, FontStyle.Bold);
            lbl_Add_Smart_Validatiy.Font = lbl_add_script_smart;

            dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size * utils.ScaleFactor, dgv.DefaultCellStyle.Font.Style);
           
            //Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            pnl_left2.Height = pnl_Right2.Height + pnl_Right3.Height + pnl_Right3.Margin.Top;
            pnl_left2.Refresh();
            rjPanel4.Location = new System.Drawing.Point(rjPanel4.Location.X, rjPanel1.Location.Y);
            rjPanel4.Height = rjPanel1.Height;

            resize_template_cards_panel();


            Get_TemplateCardsFromDB();
            Get_Cbox_Profile_UserNanager();
            Get_Cbox_Profile_Hotspot_local();
            Get_Cbox_Profile_Source_hotspot();
          
            Get_SellingPoint();
            Get_Server_Hotspot();
           
            isFirstLoadForm = false;
            firstLoad = false;

            loadFromState();
            Cbox_Profile_Select_Typ.SelectedIndex = 0;
            //resize_template_cards_panel();
            //try { CBox_TemplateCards.SelectedIndex = 0; } catch { }
            try { CBox_TemplateCards.SelectedIndex = 1; } catch { }


           
        }
        private void Get_TemplateCardsFromDB()
        {

            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
            }
            catch { }

            //try
            //{
            //    CBox_TemplateCards.Items.Clear();
            //}
            //catch { }
            //try
            //{
            //    List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    CBox_TemplateCards.DataSource = sourceCardsTemplate;
            //    CBox_TemplateCards.DisplayMember = "name";
            //    CBox_TemplateCards.ValueMember = "id";
            //    CBox_TemplateCards.SelectedIndex = -1;

            //}
            //catch { }

            //try
            //{
            //    List<SourceCardsTemplate> sp = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    if (sp.Count == 0)
            //        createDefaultTemplate();
            //    else
            //    {
            //        Dictionary<string, SourceCardsTemplate> comboSource = new Dictionary<string, SourceCardsTemplate>();
            //        foreach (SourceCardsTemplate s in sp)
            //            comboSource.Add(s.name, s);

            //        CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
            //        CBox_TemplateCards.DisplayMember = "Key";
            //        CBox_TemplateCards.ValueMember = "Value";
            //    }
            //}
            //catch { }


            //try
            //{
            //    if (dt_templateCards.Rows.Count <= 0)
            //    {
            //        CreateDefultTemplate();
            //        //SetValuToCardToGraphics();
            //    }
            //}
            //catch { }

        }
     
        private void Get_Cbox_Profile_Hotspot_local()
        {
            var umProfil = new List<HSLocalProfile>();
            umProfil.Add(new HSLocalProfile { Id = 0, Name = "" });
            HSLocalProfile hotspot = new HSLocalProfile();
            umProfil.AddRange(hotspot.Ge_Local_Hotspot());
            try
            {
                CBox_Profile_HotspotLocal.DataSource = umProfil;
                CBox_Profile_HotspotLocal.DisplayMember = "Name";
                CBox_Profile_HotspotLocal.ValueMember = "Name";
                //CBox_Profile.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }




            //List<Hotspot_Local_Profile> p = hotspot.Ge_ProfileHotspotLocal_FromDB();
            //Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //comboSource.Add("", "0");

            //if (p.Count > 0)
            //    foreach (Hotspot_Local_Profile s in p)
            //        comboSource.Add(s.Name, s.Name);
            //try
            //{
            //    CBox_Profile_HotspotLocal.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile_HotspotLocal.ValueMember = "Value";
            //    CBox_Profile_HotspotLocal.DisplayMember = "Key";

            //    //CBox_profile_Source_hotspot.DataSource = umProfil;
            //    //CBox_Profile_HotspotLocal.DisplayMember = "Name";
            //    //CBox_Profile_HotspotLocal.ValueMember = "Name";
            //    ////CBox_Profile.Text = "";
            //    Cbox_Profile_Select_Typ.SelectedIndex = 0;

            //}
            //catch { }
        }

        private void Get_Cbox_Profile_UserNanager()
        {
            
            try
            {
                var umProfil = new List<UmProfile>();
                umProfil.Add(new UmProfile { Id = 0, Name = "" });

                if (Global_Variable.UM_Profile == null)
                    return;

                umProfil.AddRange(Global_Variable.UM_Profile);
                CBox_Profile_UserMan.DataSource = umProfil;
                CBox_Profile_UserMan.DisplayMember = "Name";
                CBox_Profile_UserMan.ValueMember = "Name";
                CBox_Profile_UserMan.SelectedIndex = -1;
                CBox_Profile_UserMan.Text = "";


            }
            catch { }

            //try
            //{
            //    List<UserManager_Profile_UserManager> p = Global_Variable.UM_Profile;
            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("", "");
            //    foreach (UserManager_Profile_UserManager s in p)
            //        comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

            //    CBox_Profile.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile.DisplayMember = "Value";
            //    CBox_Profile.ValueMember = "Key";
            //}
            //catch { }


        }
        private void Get_Cbox_Profile_Source_hotspot()
        {
            Hotspot_Source_Profile hotspot = new Hotspot_Source_Profile();
            //umProfil.AddRange(hotspot.Ge_ProfileHotspotLocal_FromDB());

            List<Hotspot_Source_Profile> p = Global_Variable.Source_HS_Profile;
            Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //comboSource.Add("", "0");

            if (p.Count > 0)
                foreach (Hotspot_Source_Profile s in p)
                    comboSource.Add(s.Name, s.Name);
            try
            {
                CBox_profile_Source_hotspot.DataSource = new BindingSource(comboSource, null);
                CBox_profile_Source_hotspot.ValueMember = "Value";
                CBox_profile_Source_hotspot.DisplayMember = "Key";

                //CBox_profile_Source_hotspot.DataSource = umProfil;
                //CBox_Profile_HotspotLocal.DisplayMember = "Name";
                //CBox_Profile_HotspotLocal.ValueMember = "Name";
                ////CBox_Profile.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }
            //try
            //{
            //    var umProfil = new List<Hotspot_Source_Profile>();
            //    umProfil.Add(new Hotspot_Source_Profile { id = 0, Name = "" });
            //    umProfil.AddRange(Global_Variable.Source_HS_Profile);

            //    try
            //    {
            //        CBox_profile_Source_hotspot.DataSource = umProfil;
            //        CBox_profile_Source_hotspot.DisplayMember = "Name";
            //        CBox_profile_Source_hotspot.ValueMember = "Name";
            //        CBox_profile_Source_hotspot.SelectedIndex = -1;
            //        CBox_profile_Source_hotspot.Text = "";


            //    }
            //    catch { }

                //CBox_profile_Source_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                //CBox_profile_Source_hotspot.DisplayMember = "Name";
                //CBox_profile_Source_hotspot.ValueMember = "Name";

                //CBox_profile_Source_hotspot.SelectedIndex = 0;

            //}
            //catch { }

        }       
        private void Get_Server_Hotspot()
        {
            try
            {
                CBox_Server_hotspot.Items.Clear();
                CBox_Server_hotspot.Items.Add("all");
                CBox_Server_hotspot.SelectedIndex=0;
                return;
                CBox_Server_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_Server_hotspot.DisplayMember = "Name";
                CBox_Server_hotspot.ValueMember = "ID";

                CBox_Server_hotspot.SelectedIndex = 0;
                

            }
            catch { }

        }
       
        private void Get_SellingPoint()
        {
            //try
            //{
            //    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            //    CBox_SellingPoint = smart_DataAccess.Get_ComboBox_SellingPoint();
            //}
            //catch { }

            Smart_DataAccess da = new Smart_DataAccess();
            try
            {


                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }
            catch { }

        }
        private void loadFromState()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormHotspotPrint");
            if (sourceSaveState == null)
            {
                Frm_State = new Form_PrintHotSpotState();
                Frm_State.Name = "FormHotspotPrint";
            }
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintHotSpotState>(sourceSaveState.values.ToString());

            if (Frm_State == null)
            {
                Frm_State = new Form_PrintHotSpotState();
                Frm_State.Name = "FormHotspotPrint";
            }
            Frm_State.is_add_batch_cards = false;
            Frm_State.is_add_batch_cards_to_Archive = false;
            Frm_State.is_Add_One_Card = false;
            txtNumberCard.Text = Frm_State.txtNumberCard;
            txt_StartCard.Text = Frm_State.txt_StartCard;
            txt_EndCard.Text = Frm_State.txt_EndCard;
            //txt_ShardUser.Text = Frm_State.txt_ShardUser;
            txt_longUsers.Text = Frm_State.txt_longUsers;
            txt_longPassword.Text = Frm_State.txt_longPassword;

            cbox_UserPassword_Pattern.SelectedIndex = Frm_State.cbox_UserPassword_Pattern;
            cbox_User_NumberORcharcter.SelectedIndex = Frm_State.cbox_User_NumberORcharcter;
            cbox_Pass_NumberORcharcter.SelectedIndex = Frm_State.cbox_Pass_NumberORcharcter;

            //checkBoxFirstUse.Check = Frm_State.checkBoxFirstUse;
            checkBoxSaveTo_PDF.Check = Frm_State.checkBoxSaveTo_PDF;
            checkBoxSaveTo_excel.Check = Frm_State.checkBoxSaveTo_excel;
            checkBoxOpenAfterPrint.Check = Frm_State.checkBoxOpenAfterPrint;
            checkBoxSaveTo_script_File.Check = Frm_State.checkBoxSaveTo_script_File;
            checkBox_With_Archive_uniqe.Check = Frm_State.checkBox_Create_without_Add_ToMicrotik;
            checkBoxSaveTo_text_File.Check = Frm_State.checkBoxSaveTo_text_File;
            //checkBox_note.Check = Frm_State.checkBox_note;
            txt_note.Text = Frm_State.txt_note;

            checkBox_Add_Smart_Validatiy.Check = Frm_State.checkBox_Add_Smart_Validatiy;
            txt_houre.Text= Frm_State.txt_houre ;
            txt_validatiy.Text = Frm_State.txt_validatiy;
            txt_price.Text = Frm_State.txt_price;
            txt_download.Text = Frm_State.txt_download;
            CBox_SizeDownload.SelectedIndex = Frm_State.CBox_SizeDownload;

        }
        private void SaveFromState()
        {
            Frm_State.txtNumberCard = txtNumberCard.Text;
            Frm_State.txt_StartCard = txt_StartCard.Text;
            Frm_State.txt_EndCard = txt_EndCard.Text;
            //Frm_State.txt_ShardUser = txt_ShardUser.Text;

            Frm_State.txt_longUsers = txt_longUsers.Text;
            Frm_State.txt_longPassword = txt_longPassword.Text;

            Frm_State.cbox_UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;
            Frm_State.cbox_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedIndex;
            Frm_State.cbox_Pass_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedIndex;

            Frm_State.checkBoxFirstUse = checkBoxFirstUse.Check;
            Frm_State.checkBoxSaveTo_PDF = checkBoxSaveTo_PDF.Check;
            Frm_State.checkBoxSaveTo_excel = checkBoxSaveTo_excel.Check;
            Frm_State.checkBoxOpenAfterPrint = checkBoxOpenAfterPrint.Check;
            Frm_State.checkBoxSaveTo_script_File = checkBoxSaveTo_script_File.Check;
            Frm_State.checkBox_Create_without_Add_ToMicrotik = checkBox_With_Archive_uniqe.Check;
            Frm_State.checkBoxSaveTo_text_File = checkBoxSaveTo_text_File.Check;
            Frm_State.checkBox_note = checkBox_note.Check;
            Frm_State.txt_note = txt_note.Text;
            Frm_State.checkBox_Add_Smart_Validatiy = checkBox_Add_Smart_Validatiy.Check;
            Frm_State.txt_houre = txt_houre.Text;
            Frm_State.txt_validatiy = txt_validatiy.Text;
            Frm_State.txt_price = txt_price.Text;
            Frm_State.txt_download = txt_download.Text;
            Frm_State.CBox_SizeDownload = CBox_SizeDownload.SelectedIndex;


            string formSetting = JsonConvert.SerializeObject(Frm_State);
            Smart_DataAccess.Setting_SaveState_Forms_Variables("FormHotspotPrint", "SaveFromState", formSetting);


            //if (rj_Remember_user.Check)
            //    txtUser.Text = Frm_State.Mk_UserName;
            //if (rj_Remember_pass.Check)
            //    txtPassword.Text = Frm_State.mk_password;

            //txt_note.Text = Frm_State.note;

            //if (Frm_State.is_check_Port_api || Frm_State.is_check_Port_ssh)
            //{
            //    txtPort_api.Text = Frm_State.Mk_Port_api.ToString();
            //    rjBy_Port.Check = true;

            //}
            //rjRB_SSH.Checked = Frm_State.is_check_Port_ssh;

            //if (rjRB_SSH.Checked)
            //    txtPort_api.Text = Frm_State.Mk_Port_ssh.ToString();

            //rjDdiable_LoadSession.Check = Frm_State.Disable_Load_Session;
            //rjRunOffline.Check = Frm_State.LogIn_Without_mk;

            //List<Form_LoingState> frmUser = SqlDataAccess.GeUser_Login();
            //if (frmUser.Count > 0)
            //{
            //    dgvMicrotikSaved.Rows.Clear();
            //    dgvMicrotikSaved.DataSource = null;

            //    foreach (var itm in frmUser)
            //    {
            //        this.dgvMicrotikSaved.Rows.Add(itm.Mk_IP, itm.Mk_UserName, itm.note, itm.note);
            //    }
            //}

            //txtDomain.TextAlign = txtPassword.TextAlign = txtPortSSH.TextAlign = txtPort_api.TextAlign = txtUser.TextAlign = txt_IP_1.TextAlign = txt_IP_2.TextAlign = txt_IP_3.TextAlign = txt_IP_4.TextAlign = txt_note.TextAlign = HorizontalAlignment.Center;

            //rjComboBox1.DataSource = SqlDataAccess.GetRouters();
            //rjComboBox1.DisplayMember = "mk_sn";
            ////rjComboBox1
        }

        private void FormAddHotspotCards_Load(object sender, EventArgs e)
        {
            timer1.Start();
            LoadDatagridviewData();
            //resize_template_cards_panel();
        }
        public void LoadDatagridviewData2()
        {
            List<BatchCard> batch = new List<BatchCard>();
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

            lock (Smart_DataAccess.Lock_object)
            {
                //batch = smart_DataAccess.Load<BatchCard>($"Server * BatchCard where Server=1 and Rb ='{Global_Variable.Mk_resources.RB_code}').OrderByDescending(y => y.Id).Take(4).ToList()");
                batch = smart_DataAccess.GetListAnyDB<BatchCard>($"select * from BatchCard where Server=1 and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' )  order by Id DESC  LIMIT 4");

                //using (var db = smart_DataAccess.dbFactory.Open())
                //{
                //    batch = db.Select<BatchCard>(y => y.Server == 1 && y.Rb == Global_Variable.Mk_resources.RB_code).OrderByDescending(y => y.Id).Take(4).ToList();
                //}
            }
            dgv.DataSource = batch;
            //rjDataGridView1.DataSource = (from t in batch select t).Take(4).ToList();
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                    column.Visible = false;

                dgv.Columns["BatchNumber"].Visible = true;
                dgv.Columns["Count"].Visible = true;
                dgv.Columns["ProfileName"].Visible = true;
                dgv.Columns["AddedDate"].Visible = true;

                dgv.Columns["BatchNumber"].DisplayIndex = 0;
                dgv.Columns["Count"].DisplayIndex = 1;
                dgv.Columns["ProfileName"].DisplayIndex = 2;
                dgv.Columns["AddedDate"].DisplayIndex = 3;
            }
            catch { }
             
        }

        public void LoadDatagridviewData()
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            List<NumberPrintCard> batch = new List<NumberPrintCard>();
            lock (Smart_DataAccess.Lock_object)
            {
                batch = smart_DataAccess.GetListAnyDB<NumberPrintCard>($"select * from NumberPrintCard where Server=1 and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) order by Id DESC  LIMIT 4");
                //batch = smart_DataAccess.GetListAnyDB<BatchCard>($"select * from BatchCard where Server=0 and Rb='{Global_Variable.Mk_resources.RB_code}' order by Id DESC  LIMIT 4");

            }
            dgv.DataSource = batch;
            //rjDataGridView1.DataSource = (from t in batch select t).Take(4).ToList();
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                    column.Visible = false;

                dgv.Columns["BatchNumber"].Visible = true;
                //rjDataGridView1.Columns["NumberPrint"].Visible = true;
                dgv.Columns["NumberPrint"].Visible = false;
                dgv.Columns["Count"].Visible = true;
                dgv.Columns["ProfileName"].Visible = true;
                dgv.Columns["AddedDate"].Visible = true;

                dgv.Columns["BatchNumber"].DisplayIndex = 0;
                dgv.Columns["NumberPrint"].DisplayIndex = 1;
                dgv.Columns["Count"].DisplayIndex = 2;
                dgv.Columns["ProfileName"].DisplayIndex = 3;
                dgv.Columns["AddedDate"].DisplayIndex = 4;
            }
            catch { }

        }



        public void resize_template_cards_panel()
        {
            //return;  
            if (isFirstLoadForm)
                return;

            //if (pnlClientArea.Height > 680)
            //{
            //    pnl_left2.Height = pnlClientArea.Height - pnl_left.Height - 2;
            //    pnl_left2.Width = pnl_left.Width;

            //    pnl_Right3.Height = pnlClientArea.Height - pnl_Right.Height - pnl_Right.Margin.Top - pnl_Right2.Height - pnl_Right2.Margin.Top - 4;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 4);
            //}
            //else
            //{
            //    pnl_left2.Height = 294;
            //    pnl_left2.Width = pnl_left.Width;
            //    //pnl_Right2.Height = 183;
            //    pnl_Right3.Height = 105;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 2);

            //}


            panel_PreviewTempateCards.Refresh();
            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel1.Refresh();
            rjPanel4.Refresh();
            panel_PreviewTempateCards.Refresh();

            lbl_Title2.Location = new Point((pnl_left2.Width / 2) - lbl_Title2.Width / 2, 4);
            lbl_Title1.Location = new Point((pnl_left2.Width / 2) - lbl_Title1.Width / 2, 6);
            if (pnlClientArea.Width > 1000 && pnlClientArea.Width < 1250)
            {
                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width) - 10;
                panel_PreviewTempateCards.Location = new Point(6, 33);
            }
            else if (pnlClientArea.Width > 1250)
            {
                pnl_left.Refresh();
                panel_PreviewTempateCards.Width = pnl_left2.Width / 2;
                //panel_PreviewTempateCards.Width = pnl_left2.Width / 2 + 150;
                panel_PreviewTempateCards.Location = new Point((pnl_left2.Width / 2) - panel_PreviewTempateCards.Width / 2, 33);

                pnl_left.Refresh();
                panel_PreviewTempateCards.Width = pnl_left2.Width / 2 + 150;
                panel_PreviewTempateCards.Location = new Point((pnl_left2.Width / 2) - panel_PreviewTempateCards.Width / 2, 35);

            }
            else
            {
                pnl_left.Refresh();
                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width - 10);
                panel_PreviewTempateCards.Location = new Point(6, 33);
            }
            //panel_PreviewTempateCards.Height = panel_PreviewTempateCards.Height-1;
            panel_PreviewTempateCards.Refresh();
            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel1.Refresh();
            rjPanel4.Refresh();

            panel_PreviewTempateCards.Refresh();
        }

        private void btn_AddProfile_Click(object sender, EventArgs e)
        {
            FormProfileHotspotLocal formProfileHotspotLocal = new FormProfileHotspotLocal();
            formProfileHotspotLocal.ShowDialog();
        }

        bool seletFrom_Typ=false;
        private void Cbox_Profile_Select_Typ_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            seletFrom_Typ = true;
            if (Cbox_Profile_Select_Typ.SelectedIndex == 0)
            {
                pnl_Menul_profile.Visible = false;
                pnl_usermanger.Visible = false;
                pnl_profile_HS_local.Visible = true;
                panel1.Location = new Point(pnl_profile_HS_local.Location.X, pnl_profile_HS_local.Location.Y + pnl_profile_HS_local.Height + 9);
                //groupBox1.Controls.Add(pnl_profile_HS_local);
                //pnl_profile_HS_local.Visible = true;
                //pnl_profile_HS_local.Location = new Point(2, 11);
                //pnl_profile_HS_local.Size = new Size(200, 85);
            }
            else if (Cbox_Profile_Select_Typ.SelectedIndex == 1)
            {
                pnl_Menul_profile.Visible = true;
                pnl_usermanger.Visible = false;
                pnl_profile_HS_local.Visible = false;

                panel1.Location = new Point(pnl_Menul_profile.Location.X, pnl_Menul_profile.Location.Y + pnl_Menul_profile.Height + 2);

                //groupBox1.Controls.Add(pnl_Menul_profile);
                //pnl_Menul_profile.Visible = true;
                //pnl_usermanger.Visible = false;
                //pnl_profile_HS_local.Visible = false;
                //pnl_Menul_profile.Location = new Point(2, 11);
                //pnl_Menul_profile.Size = new Size(200, 85);
                //get_template_priview();
                //for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                //{
                //    var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                //    string text = selectedItem.Key;
                //    string Value = selectedItem.Value;
                //    if (text=="default")
                //    {
                //        CBox_TemplateCards.SelectedIndex = i; 
                //    }
                //}

                //edit_txt_menual();
            }
            else
            {
                pnl_Menul_profile.Visible = false;
                pnl_usermanger.Visible = true;
                pnl_profile_HS_local.Visible = false;

                panel1.Location = new Point(pnl_profile_HS_local.Location.X, pnl_profile_HS_local.Location.Y + pnl_profile_HS_local.Height + 9);

                //pnl_Menul_profile.Visible = false;
                //groupBox1.Controls.Add(pnl_usermanger);
                //pnl_usermanger.Visible = true;
                //pnl_profile_HS_local.Visible = false;
                //pnl_usermanger.Location = new Point(2, 11);
                //CBox_TemplateCards.SelectedIndex = 1;

                //if (tuCTable == null && tuCD == null)
                //    return;

            }

            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
            {
                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                string text = selectedItem.Key;
                string Value = selectedItem.Value;
                if (text == "default")
                {
                    CBox_TemplateCards.SelectedIndex = i;
                }
            }
            //tuCTable = null;
            //tuCD = null;
            CBox_profile_Source_hotspot.SelectedIndex = 0;
            CBox_Profile_HotspotLocal.SelectedIndex = 0;
            CBox_Profile_UserMan.SelectedIndex = 0;
            edit_txt_menual();
            seletFrom_Typ = false;

        }

        private void FormAddHotspotCards_SizeChanged(object sender, EventArgs e)
        {
            //this.ShowInTaskbar = true;
            //if (pnlClientArea.Height>680)
            //{
            //    pnl_left2.Height = pnlClientArea.Height - pnl_left.Height - pnl_left.Margin.Top -10;

            //    //pnl_left2.Height = pnlClientArea.Height - pnl_left.Height-2;
                
            //    //pnl_Right2.Height =240 ;
            //    //pnl_Right2.Refresh();
            //    pnl_Right3.Height = pnlClientArea.Height - pnl_Right.Height - pnl_Right.Margin.Top - pnl_Right2.Height - pnl_Right2.Margin.Top - 10;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 4);
            //}
            //else
            //{
            //    pnl_left2.Height = 253;
            //    //pnl_Right2.Height = 172;
            //    pnl_Right3.Height = 79;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 2);

            //}
            resize_template_cards_panel();
            //panel_PreviewTempateCards.Refresh();
        }

        private void btn_show_profile_hotspot_Click(object sender, EventArgs e)
        {
            FormProfileHotspotLocal formProfileHotspotLocal = new FormProfileHotspotLocal();
            formProfileHotspotLocal.ShowDialog();
            Get_Cbox_Profile_Hotspot_local();
            CBox_Profile_HotspotLocal.SelectedIndex = CBox_Profile_HotspotLocal.Items.Count - 1;
        }

        private void btn_show_profile_Click(object sender, EventArgs e)
        {
            FormProfileUserManager formProfileUserManager = new FormProfileUserManager();
            formProfileUserManager.ShowDialog();
            Get_Cbox_Profile_UserNanager();
            CBox_Profile_UserMan.SelectedIndex = CBox_Profile_UserMan.Items.Count - 1;
        }

        private void btn_Add_template_Click(object sender, EventArgs e)
        {
            Form_CardsDesigen_Graghics form_CardsDesigen_Graghics = new Form_CardsDesigen_Graghics();
            form_CardsDesigen_Graghics.ShowDialog();
            Get_TemplateCardsFromDB();
            CBox_TemplateCards.SelectedIndex = CBox_TemplateCards.Items.Count - 1;
        }

        private void btn_Add_SellingPoint_Click(object sender, EventArgs e)
        {
            try
            {
                Form_SellingPoint form_SellingPoint = new Form_SellingPoint();
                form_SellingPoint.ShowDialog();
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                CBox_SellingPoint = dataAccess.Get_ComboBox_SellingPoint();
                CBox_SellingPoint.SelectedIndex = CBox_SellingPoint.Items.Count - 1;
                //SellingPints sp = (SellingPints)CBox_SellingPoint.SelectedValue;
            }
            catch { }
        }
       
        private  RJComboBox rJComboBox;
        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            get_template_priview();
        }

        private void select_template_by_ProfileSelected(string search_In_filed="usermanager")
        {
            if (firstLoad)
                return;
            string profileName = rJComboBox.SelectedValue.ToString();
            //CBox_TemplateCards.SelectedIndex = 0;
            //CBox_TemplateCards.Text = "";
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);

            
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                string fildName = "";
                if (s.type == "design")
                {
                    CardsTemplate card = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card != null)
                    {
                        if (search_In_filed== "usermanager") fildName = card.setingCard.proile_link;
                        else if (search_In_filed == "smartHS") fildName = card.setingCard.proile_HS_Local_link;
                        else fildName = card.setingCard.proile_HS_link;

                        if (fildName == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card != null)
                    {
                        if (search_In_filed == "usermanager") fildName = card.setingCard.proile_link;
                        else if (fildName == "smartHS") fildName = card.setingCard.proile_HS_Local_link;
                        else fildName = card.setingCard.proile_HS_link;


                        if (fildName == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                            }
                        }
                    }
                }

            }

            CBox_TemplateCards.SelectedIndex = 0;
            CBox_TemplateCards.Text = "";

            //if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) return;

            try { CBox_TemplateCards.SelectedIndex = 1; } catch { }


        }
        private bool check_Menual_profile()
        {
            int numberChik;
            if (!(int.TryParse(txt_houre.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد الساعات بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_validatiy.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صلاحية الايام بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_price.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل السعر بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_download.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل كمية التحميل بشكل صحيح ");
                return false;
            }
            if (Convert.ToInt32(txt_download.Text) > 0)
                if (CBox_SizeDownload.SelectedIndex == -1 || CBox_SizeDownload.Text == "")
                {
                    RJMessageBox.Show("حدد وحده التحميل");
                    return false;
                }

            return true;
        }

        private static UC_PreviewTemplateCards tuCD = null;
        private static UC_PreviewTemplateCardsTable tuCTable = null;
        private void get_template_priview()
        {
            if (firstLoad)
                return;

            if (pnl_Menul_profile.Visible)
                if (check_Menual_profile() == false)
                    return;

            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) { panel_PreviewTempateCards.Controls.Clear(); return; };
            
            string templateID="0"; string _Name=""; string price="0"; string transferLimit = "0"; string uptimeLimit = "0"; string Validity="0"; string numberPrint = "0"; string SP_id = "0";
            CBox_profile_Source_hotspot.SelectedIndex = 0;
           
            if (pnl_usermanger.Visible)
            {
                UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile_UserMan.SelectedValue.ToString());
                if (profile != null)
                {
                    templateID = CBox_TemplateCards.SelectedValue.ToString();
                    _Name = profile.Name;
                    price = profile.Price.ToString();
                    transferLimit = profile.TransferLimit.ToString();
                    uptimeLimit = profile.UptimeLimit.ToString();
                    Validity = profile.Validity.ToString();
                    numberPrint = numberPrint.ToString();
                    SP_id = CBox_SellingPoint.SelectedValue.ToString();

                    checkBox_Add_Smart_Validatiy.Check = true;
                    CheckBox_Save_time.Check = true;
                    CheckBox_Save_download.Check = true;
                    CheckBox_Save_session.Check = true;
                    CheckBox_byDayOrHour.Check = false;

                }
            }
            else if (pnl_profile_HS_local.Visible)
            {
                //if (CBox_Profile_HotspotLocal.Text == "")
                //    return;

                HSLocalProfile hotspot = new HSLocalProfile();
                HSLocalProfile profile = hotspot.Ge_Local_Hotspot_ByName(CBox_Profile_HotspotLocal.SelectedValue.ToString());
                if (profile != null)
                {
                    templateID = CBox_TemplateCards.SelectedValue.ToString();
                    _Name = profile.Name;
                    price = profile.Price_Display.ToString();
                    transferLimit = profile.TransferLimit.ToString();
                    uptimeLimit = profile.UptimeLimit.ToString();
                    Validity = profile.Validity.ToString();
                    numberPrint = numberPrint.ToString();
                    SP_id = CBox_SellingPoint.SelectedValue.ToString();

                    checkBox_Add_Smart_Validatiy.Check = Convert.ToBoolean(profile.Add_Smart_Scripts);
                    CheckBox_Save_time.Check = Convert.ToBoolean(profile.Save_time);
                    CheckBox_Save_download.Check = Convert.ToBoolean(profile.Save_download);
                    CheckBox_Save_session.Check = Convert.ToBoolean(profile.Save_session);
                    CheckBox_byDayOrHour.Check = Convert.ToBoolean(profile.ByDayOrHour);

                    //if (profile.link_hotspot_profile != null)
                    //    CBox_profile_hotspot.Text = profile.link_hotspot_profile.ToString();

                     

                    if (profile.Link_hotspot_profile.ToString() != null)
                        if (profile.Link_hotspot_profile.ToString() != "")
                        {
                            //CBox_profile_Source_hotspot.SelectedValue = profile.link_hotspot_profile;
                            //CBox_profile_Source_hotspot.Text = profile.link_hotspot_profile;

                            for (int i = 0; i < CBox_profile_Source_hotspot.Items.Count; ++i)
                            {
                                var selectedItem = (KeyValuePair<string, string>)CBox_profile_Source_hotspot.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (profile.Link_hotspot_profile == text)
                                {
                                    CBox_profile_Source_hotspot.SelectedIndex = i;
                                }
                            }
                        }
                }
            }
            else
            {
                templateID = CBox_TemplateCards.SelectedValue.ToString();
                _Name = CBox_profile_Source_hotspot.Text;
                price = txt_price.Text;
                if (CBox_SizeDownload.SelectedIndex == 0)
                    transferLimit = (Convert.ToInt32(txt_download.Text) * 1024 * 1024).ToString();
                else
                    transferLimit = (Convert.ToInt32(txt_download.Text) * 1024 * 1024*1024).ToString();

                uptimeLimit =(Convert.ToInt32( txt_houre.Text)*60*60 ).ToString();
                Validity = txt_validatiy.Text;
                numberPrint = numberPrint.ToString();
                SP_id = CBox_SellingPoint.SelectedValue.ToString();

                checkBox_Add_Smart_Validatiy.Check = true;
                CheckBox_Save_time.Check = true;
                CheckBox_Save_download.Check = true;
                CheckBox_Save_session.Check = true;
                CheckBox_byDayOrHour.Check = false;

            }
            try
            {
                //UC_PreviewTemplateCards tuCD = null;
                //UC_PreviewTemplateCardsTable tuCTable = null;
                panel_PreviewTempateCards.Controls.Clear();
                panel_PreviewTempateCards.Refresh();
                SourceCardsTemplate sorceTemplates = SqlDataAccess.Get_template_cards_By_id(CBox_TemplateCards.SelectedValue.ToString());
                if (sorceTemplates.type == "design")
                {
                    //UC_PreviewTemplateCards tuCD = null;
                    //UC_PreviewTemplateCardsTable tuCTable = null;
                    CardsTemplate card = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplates.values);
                    if (card != null)
                    {
                        //UC_PreviewTemplateCards tuC2 = new UC_PreviewTemplateCards(CBox_TemplateCards.SelectedValue.ToString(), rJComboBox.SelectedValue.ToString(), CBox_SellingPoint.SelectedValue.ToString());
                        tuCD = new UC_PreviewTemplateCards(templateID,_Name,price,transferLimit,uptimeLimit,Validity, numberPrint,  SP_id);
                        
                        panel_PreviewTempateCards.Controls.Add(tuCD);
                        tuCD.Dock = DockStyle.Fill;
                        tuCD.Show();
                        tuCD.Refresh();
                        //panel_PreviewTempateCards.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
                        //panel_PreviewTempateCards.Height = 140;

                        if (panel_PreviewTempateCards.Height >= 140)
                            panel_PreviewTempateCards.Height = 141;
                    }
                }
                else if (sorceTemplates.type == "table_Desigen1")
                {
                    //UC_PreviewTemplateCards tuCD = null;
                    //UC_PreviewTemplateCardsTable tuCTable = null;
                    //panel_PreviewTempateCards.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;

                    panel_PreviewTempateCards.Height = 195;
                    tuCTable = new UC_PreviewTemplateCardsTable(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);
                    panel_PreviewTempateCards.Controls.Add(tuCTable);
                    tuCTable.Show();
                    tuCTable.Dock = DockStyle.Fill;

                    //panel_PreviewTempateCards.Height = 200;

                    if (pnlClientArea.Height >= 600)
                        panel_PreviewTempateCards.Height = 240;
                    //panel_PreviewTempateCards.Height = 225;
                    //panel_PreviewTempateCards.Height = pnl_left2.Height- panel2.Height - panel2.Margin.Bottom;

                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            panel_PreviewTempateCards.Refresh();
            pnl_left.Refresh();
        }

        private void CBox_Profile_HotspotLocal_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad || seletFrom_Typ)
                return;

           rJComboBox =  CBox_Profile_HotspotLocal;
            select_template_by_ProfileSelected("smartHS");
        }

        private void CBox_Profile_UserMan_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad || seletFrom_Typ)
                return;
            rJComboBox = CBox_Profile_UserMan;
            select_template_by_ProfileSelected("usermanager");
        }
        
        UserHotspotProcess um;
        bool startPrint = false;
        string lastFile = "";
        string lastFolder = "";
        [Obsolete]
        private void btnAdd_Click(object sender, EventArgs e)
        {
            Frm_State.is_add_batch_cards = false;
            Frm_State.is_Add_One_Card = false;
          
            if (radio_one_user.Checked)
            {
                btn_add_One_Click(sender, e);
                return;
            }
            if (radio_fast_print.Checked)
            {
                btnAdd_Fast_Click(sender, e);
                return;
            }
            um = new UserHotspotProcess();
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }

            if (um.AddCardMk_Usermanager(this, Frm_State))
            {
                Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                //startPrint=false ;
                SaveFromState();
            }
            return;
            um = new UserHotspotProcess();
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }

            if (um.AddCardMk_Usermanager(this, Frm_State))
            {
                Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                //startPrint=false ;
                SaveFromState();
            }
            //else
            //{
            //    startPrint = false;
            //}
        }

        private void checkBox_RegisterAs_LastBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAs_LastBatch.Checked == true)
                checkBox_RegisterAsBatch.Checked = false;

            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAs_LastBatch.Checked = true;
        }

        private void checkBox_RegisterAsBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAsBatch.Checked == true)
                checkBox_RegisterAs_LastBatch.Checked = false;
            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAsBatch.Checked = true;
        }

        private void txt_houre_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            if (tuCTable == null && tuCD==null)
                return;

            if (pnl_Menul_profile.Visible)
            {
                if (txt_houre.Text != "")
                    if (check_Menual_profile() == false && txt_validatiy.Text != "")
                        return;

                edit_txt_menual();
            }
            edit_txt_menual();
           }
        private void edit_txt_menual()
        {
            string templateID = "0"; string _Name = ""; string price = "0"; string transferLimit = "0"; string uptimeLimit = "0"; string Validity = "0"; string numberPrint = "0"; string SP_id = "0";
            templateID = CBox_TemplateCards.SelectedValue.ToString();
            _Name = CBox_profile_Source_hotspot.Text;
            price = txt_price.Text.ToString();
            if (txt_download.Text != "")
            {
                if (CBox_SizeDownload.SelectedIndex == 0)
                    transferLimit = (Convert.ToDouble(txt_download.Text) * 1024 * 1024).ToString();
                else
                    transferLimit = (Convert.ToDouble(txt_download.Text) * 1024 * 1024 * 1024).ToString();
            }
            if (txt_houre.Text != "")
                uptimeLimit = (Convert.ToDouble(txt_houre.Text) * 60 * 60).ToString();
            Validity = txt_validatiy.Text;

            //numberPrint = numberPrint;
            SP_id = CBox_SellingPoint.SelectedValue.ToString();
            if (tuCTable != null)
                tuCTable.show_Profile_info(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);
            else if (tuCD != null)
                tuCD.show_Profile_info(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);


        }

        private void txt_validatiy_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (tuCTable == null && tuCD == null)
                return;


           if (pnl_Menul_profile.Visible)
            {
                if (txt_validatiy.Text != "")
                    if (check_Menual_profile() == false && txt_validatiy.Text != "")
                        return;

                edit_txt_menual();
            }
        }

        private void txt_price_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (tuCTable == null && tuCD == null)
                return;

            if (pnl_Menul_profile.Visible)
            {
                if (txt_price.Text != "")
                    if (check_Menual_profile() == false && txt_price.Text != "")
                        return;

                edit_txt_menual();
            }
        }

        private void txt_download_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (tuCTable == null && tuCD == null)
                return;

            if (pnl_Menul_profile.Visible)
            {
                if (txt_download.Text != "")
                    if (check_Menual_profile() == false && txt_download.Text != "")
                        return;

                edit_txt_menual();
            }
        }

        private void CBox_SizeDownload_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (tuCTable == null && tuCD == null)
                return;

            if (pnl_Menul_profile.Visible)
            {
                if (txt_download.Text != "")
                    if (check_Menual_profile() == false && txt_download.Text != "")
                        return;

                edit_txt_menual();
            }
        }

        private void btn_Script_Smart_Click(object sender, EventArgs e)
        {
            form_Edit_SmartScript_WhenAdd form = new form_Edit_SmartScript_WhenAdd(CheckBox_Save_time.Check, CheckBox_Save_download.Check, CheckBox_Save_session.Check, CheckBox_byDayOrHour.Check);
            form.ShowDialog();
            if (form.is_save)
            {
                CheckBox_Save_time.Check = form.CheckBox_Save_time.Check;
                CheckBox_Save_download.Check = form.CheckBox_Save_download.Check;
                CheckBox_Save_session.Check = form.CheckBox_Save_session.Check;
                CheckBox_byDayOrHour.Check = form.CheckBox_byDayOrHour.Check;
            }
        }

        private void CBox_profile_Source_hotspot_OnSelectedIndexChanged(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void btnAdd_Fast_Click(object sender, EventArgs e)
        {
            Frm_State.is_add_batch_cards = true;
            um = new UserHotspotProcess();
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }


            if (!check_port_mk_befor())
            {
                MessageBox.Show(" ssh خطا بالاتصال مع بورت");
                return;
            }
            if (um.AddCardMk_Usermanager(this, Frm_State))
            {
                Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                Frm_State.is_add_batch_cards = false;

                //startPrint=false ;
                SaveFromState();
            }
        }
        public string ssh_last_state = "false";
        public string id_ssh = "";
        public int ssh_port = 22;
        public DataTable dt_service = null;
        [Obsolete]
        public bool check_port_mk_befor()
        {
            bool result = false;
            Mk_DataAccess DA2 = new Mk_DataAccess();
            dt_service = DA2.GetService();
            try
            {
                DataRow[] foundRows = dt_service.Select("[name] = " + "'ssh'");
                ssh_last_state = foundRows[0]["disabled"].ToString();
                ssh_port = Convert.ToInt32(foundRows[0]["port"].ToString());
                Global_Variable.Mk_Login_data.Mk_Port_ssh = ssh_port;
                id_ssh = foundRows[0]["id"].ToString();
                result = true;
            }
            catch { }

            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "false");
                result = true;
            }
            return result;
        }
        [Obsolete]
        public void rest_port_mk_after()
        {
            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "true");
            }
        }

        [Obsolete]
        private void btn_add_One_Click(object sender, EventArgs e)
        {
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }

            Frm_State.is_Add_One_Card = true;
            try
            {
                int Userlength = 8;
                int Passwordlength = 6;
                int Usermode = 0;
                int Passwordmode = 0;
                if (utils.check_Filed_Intiger_with_Msg(txt_longUsers.Text))
                {
                    Userlength = Convert.ToInt32(txt_longUsers.Text);
                }
                if (utils.check_Filed_Intiger_with_Msg(txt_longPassword.Text))
                {
                    Passwordmode = Convert.ToInt32(txt_longPassword.Text);
                }
                if (string.IsNullOrEmpty(cbox_User_NumberORcharcter.Text) || cbox_User_NumberORcharcter.SelectedIndex == -1)
                {
                    RJMessageBox.Show("حدد نمط اسم المستخدم");
                    return;
                }
                if (string.IsNullOrEmpty(cbox_Pass_NumberORcharcter.Text) || cbox_Pass_NumberORcharcter.SelectedIndex == -1)
                {
                    RJMessageBox.Show("حدد نمط  كلمة المرور  ");
                    return;
                }

                Usermode = cbox_User_NumberORcharcter.SelectedIndex;
                Passwordmode = cbox_Pass_NumberORcharcter.SelectedIndex;

                frm_Input_Dailog_New_User frm = new frm_Input_Dailog_New_User(Userlength, Passwordlength, Usermode, Passwordmode);

                frm.ShowDialog();
                if (frm.add)
                {
                    um = new UserHotspotProcess();
                    if (um.AddCardMk_Usermanager(this, Frm_State, frm.txt_Name.Text, frm.txt_password.Text))
                    {
                        Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                        Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                        Frm_State.is_Add_One_Card = false;

                        //startPrint=false ;
                        SaveFromState();
                    }
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            return;
        }

        private void rjPanel1_Resize(object sender, EventArgs e)
        {
            panel_PreviewTempateCards.Refresh();
            //panel_PreviewTempateCards.Refresh();
        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            set_loaction_left_panel();
        }
        public void set_loaction_left_panel()
        {

            if (isFirstLoadForm)
                return;
            pnl_left.Refresh();
            pnl_Right.Refresh();
            pnl_left2.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel4.Refresh();

        }

        private void btn_OpenFolderDefault_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("explorer.exe", (Frm_State.PathFolderPrint));
                //System.Diagnostics.Process.Start("explorer.exe", Path.GetDirectoryName(Frm_State.PathFolderPrint));
            }
            catch (Exception ex) { RJMessageBox.Show("btn_OpenFolderDefault_Click \n" + ex.Message.ToString()); }

        }

        private void btn_OpenLastFile_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start(Frm_State.path_saved_file);
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message.ToString()); }

        }

        private void lbl_Profile_UserMan_Click(object sender, EventArgs e)
        {

        }

        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            //panel_PreviewTempateCards.Refresh();
        }

        private void radio_Print_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void CBox_SellingPoint_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            
            if (firstLoad)
                return;

            try
            {
                Smart_DataAccess dba = new Smart_DataAccess();
                SellingPoint sellingPoint = dba.Get_SellingPoint_Code(CBox_SellingPoint.SelectedValue.ToString());

                if (sellingPoint != null)
                {
                    txt_StartCard.Text = sellingPoint.Prefixes.Trim();
                    txt_EndCard.Text = sellingPoint.Suffixes.Trim();
                }
            }
            catch { }
        }
    }
}
