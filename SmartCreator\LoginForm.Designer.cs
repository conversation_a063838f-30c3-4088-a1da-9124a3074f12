﻿namespace SmartCreator
{
    partial class LoginForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(LoginForm));
            this.dragControl1 = new SmartCreator.RJControls.RJDragControl(this.components);
            this.txtPassword = new SmartCreator.RJControls.RJTextBox();
            this.dragControl2 = new SmartCreator.RJControls.RJDragControl(this.components);
            this.pnlTopTitle = new SmartCreator.RJControls.RJPanel();
            this.lbl_Viriion = new SmartCreator.RJControls.RJLabel();
            this.lblMessage = new System.Windows.Forms.Label();
            this.btnLogin = new SmartCreator.RJControls.RJButton();
            this.lbl_username = new SmartCreator.RJControls.RJLabel();
            this.lbl_Password = new SmartCreator.RJControls.RJLabel();
            this.txtDomain = new SmartCreator.RJControls.RJTextBox();
            this.lbl_Address = new SmartCreator.RJControls.RJLabel();
            this.rjRB_SSH = new SmartCreator.RJControls.RJRadioButton();
            this.rjRB_API = new SmartCreator.RJControls.RJRadioButton();
            this.txtPort = new SmartCreator.RJControls.RJTextBox();
            this.txtPortSSH = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.pnlBootom = new SmartCreator.RJControls.RJPanel();
            this.pnlTopBorder = new SmartCreator.RJControls.RJPanel();
            this.pnlRightBorder = new SmartCreator.RJControls.RJPanel();
            this.pnlLeftBorder = new SmartCreator.RJControls.RJPanel();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.lbl_SmartCreator = new SmartCreator.RJControls.RJLabel();
            this.pnl_domain = new System.Windows.Forms.Panel();
            this.rjTextBox5 = new SmartCreator.RJControls.RJTextBox();
            this.lbl_loginType = new SmartCreator.RJControls.RJLabel();
            this.btnShowLoginDate = new SmartCreator.RJControls.RJButton();
            this.btn_ShowNighboor = new SmartCreator.RJControls.RJButton();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.pnl_port = new SmartCreator.RJControls.RJPanel();
            this.lbl_by_ip = new SmartCreator.RJControls.RJLabel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.lbl_By_Api = new SmartCreator.RJControls.RJLabel();
            this.lbl_Ssh = new SmartCreator.RJControls.RJLabel();
            this.Toggle_RunOffline = new SmartCreator.RJControls.RJToggleButton();
            this.dgvMicrotikSaved = new SmartCreator.RJControls.RJDataGridView();
            this.rjComboBox1 = new SmartCreator.RJControls.RJComboBox();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.chkRemember_user = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.Toggle_load_by_DownloadDB = new SmartCreator.RJControls.RJToggleButton();
            this.txt_note = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.pnl_ips = new System.Windows.Forms.Panel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.txt_IP_4 = new SmartCreator.RJControls.RJTextBox();
            this.txt_IP_3 = new SmartCreator.RJControls.RJTextBox();
            this.txt_IP_1 = new SmartCreator.RJControls.RJTextBox();
            this.txt_IP_2 = new SmartCreator.RJControls.RJTextBox();
            this.txt_back_ips = new SmartCreator.RJControls.RJTextBox();
            this.txtUsername = new SmartCreator.RJControls.RJTextBox();
            this.btnDeleteRB = new SmartCreator.RJControls.RJButton();
            this.btnAddRB = new SmartCreator.RJControls.RJButton();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.rjButton2 = new SmartCreator.RJControls.RJButton();
            this.rjButton3 = new SmartCreator.RJControls.RJButton();
            this.Toggle_Ddiable_LoadSession = new SmartCreator.RJControls.RJToggleButton();
            this.Radio_ByIP = new SmartCreator.RJControls.RJRadioButton();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.Radio_ByDomain = new SmartCreator.RJControls.RJRadioButton();
            this.Radio_BySmart = new SmartCreator.RJControls.RJRadioButton();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.Toggle_Custom_Login = new SmartCreator.RJControls.RJToggleButton();
            this.btn_Custom_Login = new SmartCreator.RJControls.RJButton();
            this.pnl_Smart = new System.Windows.Forms.Panel();
            this.txt_SmartCloud = new SmartCreator.RJControls.RJTextBox();
            this.rjTextBox2 = new SmartCreator.RJControls.RJTextBox();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.chkRemember_pass = new SmartCreator.RJControls.RJCheckBox();
            this.rjBy_Port = new SmartCreator.RJControls.RJCheckBox();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            this.tableLayoutPanel2 = new System.Windows.Forms.TableLayoutPanel();
            this.flowLayoutPanel4 = new System.Windows.Forms.FlowLayoutPanel();
            this.flowLayoutPanel3 = new System.Windows.Forms.FlowLayoutPanel();
            this.txtPort_SmartCloud = new SmartCreator.RJControls.RJTextBox();
            this.txtPort_Domain = new SmartCreator.RJControls.RJTextBox();
            this.rjBy_Port_Domain = new SmartCreator.RJControls.RJCheckBox();
            this.rjBy_Port_SmartCloud = new SmartCreator.RJControls.RJCheckBox();
            this.btn_Save = new SmartCreator.RJControls.RJButton();
            this.rjDragControl1 = new SmartCreator.RJControls.RJDragControl(this.components);
            this.iconSplitButton1 = new FontAwesome.Sharp.IconSplitButton();
            this.rjProgressBar1 = new SmartCreator.RJControls.RJProgressBar();
            this.pnlTopTitle.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.pnl_domain.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.pnl_port.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMicrotikSaved)).BeginInit();
            this.pnl_ips.SuspendLayout();
            this.pnl_Smart.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.flowLayoutPanel2.SuspendLayout();
            this.tableLayoutPanel2.SuspendLayout();
            this.flowLayoutPanel4.SuspendLayout();
            this.flowLayoutPanel3.SuspendLayout();
            this.SuspendLayout();
            // 
            // dragControl1
            // 
            this.dragControl1.DragControl = this;
            // 
            // txtPassword
            // 
            this.txtPassword._Customizable = false;
            this.txtPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPassword.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPassword.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPassword.BorderRadius = 7;
            this.txtPassword.BorderSize = 1;
            this.txtPassword.Font = new System.Drawing.Font("Verdana", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPassword.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPassword.Location = new System.Drawing.Point(188, 210);
            this.txtPassword.Margin = new System.Windows.Forms.Padding(2);
            this.txtPassword.MaxLength = 100;
            this.txtPassword.MultiLine = false;
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPassword.PasswordChar = true;
            this.txtPassword.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPassword.PlaceHolderText = "";
            this.txtPassword.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPassword.Size = new System.Drawing.Size(225, 28);
            this.txtPassword.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPassword.TabIndex = 6;
            this.txtPassword.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPassword.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // dragControl2
            // 
            this.dragControl2.DragControl = this.pnlTopTitle;
            // 
            // pnlTopTitle
            // 
            this.pnlTopTitle.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlTopTitle.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnlTopTitle.BorderRadius = 0;
            this.pnlTopTitle.BorderSize = 0;
            this.pnlTopTitle.Controls.Add(this.lbl_Viriion);
            this.pnlTopTitle.Customizable = false;
            this.pnlTopTitle.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTopTitle.Location = new System.Drawing.Point(2, 2);
            this.pnlTopTitle.Margin = new System.Windows.Forms.Padding(2);
            this.pnlTopTitle.Name = "pnlTopTitle";
            this.pnlTopTitle.Size = new System.Drawing.Size(547, 30);
            this.pnlTopTitle.TabIndex = 85;
            this.pnlTopTitle.MouseDown += new System.Windows.Forms.MouseEventHandler(this.pnlTopTitle_MouseDown);
            // 
            // lbl_Viriion
            // 
            this.lbl_Viriion.AutoSize = true;
            this.lbl_Viriion.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Viriion.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_Viriion.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Viriion.LinkLabel = false;
            this.lbl_Viriion.Location = new System.Drawing.Point(9, 8);
            this.lbl_Viriion.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_Viriion.Name = "lbl_Viriion";
            this.lbl_Viriion.Size = new System.Drawing.Size(60, 14);
            this.lbl_Viriion.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Viriion.TabIndex = 92;
            this.lbl_Viriion.Text = "9.02.09";
            // 
            // lblMessage
            // 
            this.lblMessage.AutoSize = true;
            this.lblMessage.ForeColor = System.Drawing.Color.IndianRed;
            this.lblMessage.Location = new System.Drawing.Point(26, 393);
            this.lblMessage.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lblMessage.Name = "lblMessage";
            this.lblMessage.Size = new System.Drawing.Size(53, 15);
            this.lblMessage.TabIndex = 9;
            this.lblMessage.Text = "Message";
            this.lblMessage.Visible = false;
            // 
            // btnLogin
            // 
            this.btnLogin.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLogin.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLogin.BorderRadius = 15;
            this.btnLogin.BorderSize = 2;
            this.btnLogin.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnLogin.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnLogin.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnLogin.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnLogin.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLogin.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.btnLogin.ForeColor = System.Drawing.Color.White;
            this.btnLogin.IconChar = FontAwesome.Sharp.IconChar.Sign;
            this.btnLogin.IconColor = System.Drawing.Color.White;
            this.btnLogin.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnLogin.IconSize = 24;
            this.btnLogin.Location = new System.Drawing.Point(217, 373);
            this.btnLogin.Margin = new System.Windows.Forms.Padding(2);
            this.btnLogin.Name = "btnLogin";
            this.btnLogin.Size = new System.Drawing.Size(142, 39);
            this.btnLogin.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnLogin.TabIndex = 9;
            this.btnLogin.Text = "تسجيل الدخول";
            this.btnLogin.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnLogin.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnLogin.UseVisualStyleBackColor = false;
            this.btnLogin.Click += new System.EventHandler(this.btnLogin_Click);
            this.btnLogin.KeyDown += new System.Windows.Forms.KeyEventHandler(this.btnLogin_KeyDown);
            // 
            // lbl_username
            // 
            this.lbl_username.AutoSize = true;
            this.lbl_username.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_username.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_username.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_username.LinkLabel = false;
            this.lbl_username.Location = new System.Drawing.Point(426, 181);
            this.lbl_username.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_username.Name = "lbl_username";
            this.lbl_username.Size = new System.Drawing.Size(83, 17);
            this.lbl_username.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_username.TabIndex = 14;
            this.lbl_username.Text = "اسم المستخدم";
            // 
            // lbl_Password
            // 
            this.lbl_Password.AutoSize = true;
            this.lbl_Password.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Password.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Password.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Password.LinkLabel = false;
            this.lbl_Password.Location = new System.Drawing.Point(430, 213);
            this.lbl_Password.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_Password.Name = "lbl_Password";
            this.lbl_Password.Size = new System.Drawing.Size(77, 17);
            this.lbl_Password.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Password.TabIndex = 15;
            this.lbl_Password.Text = "كلـــــمة المرور";
            // 
            // txtDomain
            // 
            this.txtDomain._Customizable = false;
            this.txtDomain.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtDomain.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtDomain.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtDomain.BorderRadius = 0;
            this.txtDomain.BorderSize = 0;
            this.txtDomain.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.txtDomain.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtDomain.Location = new System.Drawing.Point(5, 7);
            this.txtDomain.Margin = new System.Windows.Forms.Padding(2);
            this.txtDomain.MultiLine = false;
            this.txtDomain.Name = "txtDomain";
            this.txtDomain.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtDomain.PasswordChar = false;
            this.txtDomain.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtDomain.PlaceHolderText = null;
            this.txtDomain.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtDomain.Size = new System.Drawing.Size(334, 25);
            this.txtDomain.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtDomain.TabIndex = 16;
            this.txtDomain.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtDomain.onTextChanged += new System.EventHandler(this.txtDomain_onTextChanged);
            this.txtDomain.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // lbl_Address
            // 
            this.lbl_Address.AutoSize = true;
            this.lbl_Address.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Address.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Address.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Address.LinkLabel = false;
            this.lbl_Address.Location = new System.Drawing.Point(430, 141);
            this.lbl_Address.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_Address.Name = "lbl_Address";
            this.lbl_Address.Size = new System.Drawing.Size(74, 17);
            this.lbl_Address.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Address.TabIndex = 17;
            this.lbl_Address.Text = "العنـــــــــــــــوان";
            // 
            // rjRB_SSH
            // 
            this.rjRB_SSH.AutoSize = true;
            this.rjRB_SSH.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjRB_SSH.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjRB_SSH.Customizable = false;
            this.rjRB_SSH.Font = new System.Drawing.Font("Verdana", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjRB_SSH.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjRB_SSH.Location = new System.Drawing.Point(119, 11);
            this.rjRB_SSH.Margin = new System.Windows.Forms.Padding(2);
            this.rjRB_SSH.MinimumSize = new System.Drawing.Size(0, 20);
            this.rjRB_SSH.Name = "rjRB_SSH";
            this.rjRB_SSH.Padding = new System.Windows.Forms.Padding(9, 0, 0, 0);
            this.rjRB_SSH.Size = new System.Drawing.Size(23, 20);
            this.rjRB_SSH.TabIndex = 69;
            this.rjRB_SSH.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.rjRB_SSH.UseVisualStyleBackColor = true;
            this.rjRB_SSH.CheckedChanged += new System.EventHandler(this.rjRB_SSH_CheckedChanged);
            // 
            // rjRB_API
            // 
            this.rjRB_API.AutoSize = true;
            this.rjRB_API.Checked = true;
            this.rjRB_API.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjRB_API.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjRB_API.Customizable = false;
            this.rjRB_API.Font = new System.Drawing.Font("Verdana", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjRB_API.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjRB_API.Location = new System.Drawing.Point(203, 11);
            this.rjRB_API.Margin = new System.Windows.Forms.Padding(2);
            this.rjRB_API.MinimumSize = new System.Drawing.Size(0, 20);
            this.rjRB_API.Name = "rjRB_API";
            this.rjRB_API.Padding = new System.Windows.Forms.Padding(9, 0, 0, 0);
            this.rjRB_API.Size = new System.Drawing.Size(23, 20);
            this.rjRB_API.TabIndex = 68;
            this.rjRB_API.TabStop = true;
            this.rjRB_API.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.rjRB_API.UseVisualStyleBackColor = true;
            this.rjRB_API.CheckedChanged += new System.EventHandler(this.rjRB_API_CheckedChanged);
            // 
            // txtPort
            // 
            this.txtPort._Customizable = false;
            this.txtPort.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPort.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPort.BorderRadius = 7;
            this.txtPort.BorderSize = 1;
            this.txtPort.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F);
            this.txtPort.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort.Location = new System.Drawing.Point(77, 210);
            this.txtPort.Margin = new System.Windows.Forms.Padding(2);
            this.txtPort.MaxLength = 100;
            this.txtPort.MultiLine = false;
            this.txtPort.Name = "txtPort";
            this.txtPort.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPort.PasswordChar = false;
            this.txtPort.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPort.PlaceHolderText = "";
            this.txtPort.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPort.Size = new System.Drawing.Size(54, 26);
            this.txtPort.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPort.TabIndex = 76;
            this.txtPort.Tag = "";
            this.txtPort.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPort.Visible = false;
            // 
            // txtPortSSH
            // 
            this.txtPortSSH._Customizable = false;
            this.txtPortSSH.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPortSSH.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPortSSH.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPortSSH.BorderRadius = 10;
            this.txtPortSSH.BorderSize = 1;
            this.txtPortSSH.Font = new System.Drawing.Font("Verdana", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtPortSSH.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPortSSH.Location = new System.Drawing.Point(26, 384);
            this.txtPortSSH.Margin = new System.Windows.Forms.Padding(2);
            this.txtPortSSH.MaxLength = 100;
            this.txtPortSSH.MultiLine = false;
            this.txtPortSSH.Name = "txtPortSSH";
            this.txtPortSSH.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPortSSH.PasswordChar = false;
            this.txtPortSSH.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPortSSH.PlaceHolderText = "";
            this.txtPortSSH.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPortSSH.Size = new System.Drawing.Size(33, 28);
            this.txtPortSSH.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPortSSH.TabIndex = 76;
            this.txtPortSSH.Tag = "";
            this.txtPortSSH.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPortSSH.Visible = false;
            // 
            // rjLabel6
            // 
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(58, 5);
            this.rjLabel6.Margin = new System.Windows.Forms.Padding(3, 5, 0, 0);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(264, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 77;
            this.rjLabel6.Text = "دخول سريع   ( تعطيل جلب جلسات وتقارير اليوزمنجر )";
            this.toolTip1.SetToolTip(this.rjLabel6, "دخول سريع (تعطيل تحميل الجلسات والتقارير)");
            // 
            // rjLabel7
            // 
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(106, 90);
            this.rjLabel7.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(196, 23);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 79;
            this.rjLabel7.Text = "جلب البيانات الاساسية فقط  - الباقات ";
            this.toolTip1.SetToolTip(this.rjLabel7, "جلب ال");
            this.rjLabel7.Visible = false;
            // 
            // pnlBootom
            // 
            this.pnlBootom.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlBootom.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnlBootom.BorderRadius = 0;
            this.pnlBootom.BorderSize = 0;
            this.pnlBootom.Customizable = false;
            this.pnlBootom.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.pnlBootom.Location = new System.Drawing.Point(0, 677);
            this.pnlBootom.Margin = new System.Windows.Forms.Padding(0);
            this.pnlBootom.Name = "pnlBootom";
            this.pnlBootom.Size = new System.Drawing.Size(551, 3);
            this.pnlBootom.TabIndex = 81;
            // 
            // pnlTopBorder
            // 
            this.pnlTopBorder.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlTopBorder.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnlTopBorder.BorderRadius = 0;
            this.pnlTopBorder.BorderSize = 0;
            this.pnlTopBorder.Customizable = false;
            this.pnlTopBorder.Dock = System.Windows.Forms.DockStyle.Top;
            this.pnlTopBorder.Location = new System.Drawing.Point(0, 0);
            this.pnlTopBorder.Margin = new System.Windows.Forms.Padding(0);
            this.pnlTopBorder.Name = "pnlTopBorder";
            this.pnlTopBorder.Size = new System.Drawing.Size(551, 2);
            this.pnlTopBorder.TabIndex = 0;
            // 
            // pnlRightBorder
            // 
            this.pnlRightBorder.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlRightBorder.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnlRightBorder.BorderRadius = 0;
            this.pnlRightBorder.BorderSize = 0;
            this.pnlRightBorder.Customizable = false;
            this.pnlRightBorder.Dock = System.Windows.Forms.DockStyle.Right;
            this.pnlRightBorder.Location = new System.Drawing.Point(549, 2);
            this.pnlRightBorder.Margin = new System.Windows.Forms.Padding(0);
            this.pnlRightBorder.Name = "pnlRightBorder";
            this.pnlRightBorder.Size = new System.Drawing.Size(2, 675);
            this.pnlRightBorder.TabIndex = 82;
            // 
            // pnlLeftBorder
            // 
            this.pnlLeftBorder.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnlLeftBorder.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnlLeftBorder.BorderRadius = 0;
            this.pnlLeftBorder.BorderSize = 0;
            this.pnlLeftBorder.Customizable = false;
            this.pnlLeftBorder.Dock = System.Windows.Forms.DockStyle.Left;
            this.pnlLeftBorder.Location = new System.Drawing.Point(0, 2);
            this.pnlLeftBorder.Margin = new System.Windows.Forms.Padding(0);
            this.pnlLeftBorder.Name = "pnlLeftBorder";
            this.pnlLeftBorder.Size = new System.Drawing.Size(2, 675);
            this.pnlLeftBorder.TabIndex = 83;
            // 
            // rjPanel1
            // 
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BackgroundImage = global::SmartCreator.Properties.Resources.header;
            this.rjPanel1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.rjPanel1.BorderColor = System.Drawing.Color.RoyalBlue;
            this.rjPanel1.BorderRadius = 0;
            this.rjPanel1.BorderSize = 0;
            this.rjPanel1.Controls.Add(this.lbl_SmartCreator);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjPanel1.Location = new System.Drawing.Point(2, 32);
            this.rjPanel1.Margin = new System.Windows.Forms.Padding(2);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(547, 54);
            this.rjPanel1.TabIndex = 86;
            this.rjPanel1.MouseDown += new System.Windows.Forms.MouseEventHandler(this.rjPanel1_MouseDown);
            // 
            // lbl_SmartCreator
            // 
            this.lbl_SmartCreator.AutoSize = true;
            this.lbl_SmartCreator.BackColor = System.Drawing.Color.Transparent;
            this.lbl_SmartCreator.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_SmartCreator.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.lbl_SmartCreator.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_SmartCreator.LinkLabel = false;
            this.lbl_SmartCreator.Location = new System.Drawing.Point(7, 12);
            this.lbl_SmartCreator.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_SmartCreator.Name = "lbl_SmartCreator";
            this.lbl_SmartCreator.Size = new System.Drawing.Size(106, 27);
            this.lbl_SmartCreator.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.lbl_SmartCreator.TabIndex = 66;
            this.lbl_SmartCreator.Text = "سمارت كريتور";
            this.lbl_SmartCreator.MouseDown += new System.Windows.Forms.MouseEventHandler(this.lbl_SmartCreator_MouseDown);
            // 
            // pnl_domain
            // 
            this.pnl_domain.BackColor = System.Drawing.Color.Transparent;
            this.pnl_domain.Controls.Add(this.txtDomain);
            this.pnl_domain.Controls.Add(this.rjTextBox5);
            this.pnl_domain.Location = new System.Drawing.Point(76, 129);
            this.pnl_domain.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_domain.Name = "pnl_domain";
            this.pnl_domain.Size = new System.Drawing.Size(341, 43);
            this.pnl_domain.TabIndex = 94;
            this.pnl_domain.Visible = false;
            // 
            // rjTextBox5
            // 
            this.rjTextBox5._Customizable = false;
            this.rjTextBox5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjTextBox5.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox5.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.rjTextBox5.BorderRadius = 7;
            this.rjTextBox5.BorderSize = 1;
            this.rjTextBox5.Font = new System.Drawing.Font("Arial", 14F);
            this.rjTextBox5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox5.Location = new System.Drawing.Point(0, 4);
            this.rjTextBox5.Margin = new System.Windows.Forms.Padding(2);
            this.rjTextBox5.MultiLine = false;
            this.rjTextBox5.Name = "rjTextBox5";
            this.rjTextBox5.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.rjTextBox5.PasswordChar = false;
            this.rjTextBox5.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.rjTextBox5.PlaceHolderText = null;
            this.rjTextBox5.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.rjTextBox5.Size = new System.Drawing.Size(341, 33);
            this.rjTextBox5.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.rjTextBox5.TabIndex = 95;
            this.rjTextBox5.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            // 
            // lbl_loginType
            // 
            this.lbl_loginType.AutoSize = true;
            this.lbl_loginType.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_loginType.Font = new System.Drawing.Font("Cairo Medium", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_loginType.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_loginType.LinkLabel = false;
            this.lbl_loginType.Location = new System.Drawing.Point(16, 11);
            this.lbl_loginType.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_loginType.Name = "lbl_loginType";
            this.lbl_loginType.Size = new System.Drawing.Size(37, 23);
            this.lbl_loginType.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_loginType.TabIndex = 71;
            this.lbl_loginType.Text = " رابط";
            // 
            // btnShowLoginDate
            // 
            this.btnShowLoginDate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnShowLoginDate.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnShowLoginDate.BorderRadius = 10;
            this.btnShowLoginDate.BorderSize = 0;
            this.btnShowLoginDate.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnShowLoginDate.FlatAppearance.BorderSize = 0;
            this.btnShowLoginDate.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnShowLoginDate.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnShowLoginDate.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnShowLoginDate.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.btnShowLoginDate.ForeColor = System.Drawing.Color.White;
            this.btnShowLoginDate.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnShowLoginDate.IconColor = System.Drawing.Color.White;
            this.btnShowLoginDate.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnShowLoginDate.IconSize = 24;
            this.btnShowLoginDate.Location = new System.Drawing.Point(415, 418);
            this.btnShowLoginDate.Margin = new System.Windows.Forms.Padding(2);
            this.btnShowLoginDate.Name = "btnShowLoginDate";
            this.btnShowLoginDate.Size = new System.Drawing.Size(114, 30);
            this.btnShowLoginDate.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnShowLoginDate.TabIndex = 92;
            this.btnShowLoginDate.Text = "بينانات الدخول";
            this.btnShowLoginDate.UseVisualStyleBackColor = false;
            this.btnShowLoginDate.Click += new System.EventHandler(this.btnShowLoginDate_Click);
            // 
            // btn_ShowNighboor
            // 
            this.btn_ShowNighboor.BackColor = System.Drawing.SystemColors.Control;
            this.btn_ShowNighboor.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_ShowNighboor.BorderRadius = 10;
            this.btn_ShowNighboor.BorderSize = 1;
            this.btn_ShowNighboor.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_ShowNighboor.FlatAppearance.BorderSize = 0;
            this.btn_ShowNighboor.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(225)))), ((int)(((byte)(225)))));
            this.btn_ShowNighboor.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(211)))), ((int)(((byte)(211)))));
            this.btn_ShowNighboor.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_ShowNighboor.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.btn_ShowNighboor.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_ShowNighboor.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_ShowNighboor.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_ShowNighboor.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_ShowNighboor.IconSize = 24;
            this.btn_ShowNighboor.Location = new System.Drawing.Point(306, 418);
            this.btn_ShowNighboor.Margin = new System.Windows.Forms.Padding(2);
            this.btn_ShowNighboor.Name = "btn_ShowNighboor";
            this.btn_ShowNighboor.Size = new System.Drawing.Size(114, 30);
            this.btn_ShowNighboor.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_ShowNighboor.TabIndex = 92;
            this.btn_ShowNighboor.Text = "الروترات المتصله";
            this.btn_ShowNighboor.UseVisualStyleBackColor = false;
            this.btn_ShowNighboor.Click += new System.EventHandler(this.btn_ShowNighboor_Click);
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderColor = System.Drawing.Color.RoyalBlue;
            this.rjPanel2.BorderRadius = 0;
            this.rjPanel2.BorderSize = 0;
            this.rjPanel2.Controls.Add(this.pnl_port);
            this.rjPanel2.Controls.Add(this.Toggle_RunOffline);
            this.rjPanel2.Controls.Add(this.rjLabel7);
            this.rjPanel2.Controls.Add(this.dgvMicrotikSaved);
            this.rjPanel2.Controls.Add(this.rjComboBox1);
            this.rjPanel2.Controls.Add(this.rjButton1);
            this.rjPanel2.Controls.Add(this.rjLabel15);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(8, 449);
            this.rjPanel2.Margin = new System.Windows.Forms.Padding(2);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Padding = new System.Windows.Forms.Padding(5, 0, 5, 0);
            this.rjPanel2.Size = new System.Drawing.Size(536, 174);
            this.rjPanel2.TabIndex = 94;
            // 
            // pnl_port
            // 
            this.pnl_port.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_port.BorderColor = System.Drawing.Color.RoyalBlue;
            this.pnl_port.BorderRadius = 7;
            this.pnl_port.BorderSize = 0;
            this.pnl_port.Controls.Add(this.lbl_loginType);
            this.pnl_port.Controls.Add(this.lbl_by_ip);
            this.pnl_port.Controls.Add(this.rjRB_SSH);
            this.pnl_port.Controls.Add(this.rjLabel8);
            this.pnl_port.Controls.Add(this.lbl_By_Api);
            this.pnl_port.Controls.Add(this.rjRB_API);
            this.pnl_port.Controls.Add(this.lbl_Ssh);
            this.pnl_port.Customizable = false;
            this.pnl_port.Location = new System.Drawing.Point(215, 136);
            this.pnl_port.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_port.Name = "pnl_port";
            this.pnl_port.Size = new System.Drawing.Size(240, 44);
            this.pnl_port.TabIndex = 104;
            this.pnl_port.Visible = false;
            // 
            // lbl_by_ip
            // 
            this.lbl_by_ip.AutoSize = true;
            this.lbl_by_ip.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_by_ip.Font = new System.Drawing.Font("Cairo Medium", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_by_ip.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_by_ip.LinkLabel = false;
            this.lbl_by_ip.Location = new System.Drawing.Point(0, 8);
            this.lbl_by_ip.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_by_ip.Name = "lbl_by_ip";
            this.lbl_by_ip.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_by_ip.Size = new System.Drawing.Size(60, 23);
            this.lbl_by_ip.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_by_ip.TabIndex = 103;
            this.lbl_by_ip.Text = "عنوان IP";
            this.lbl_by_ip.Visible = false;
            // 
            // rjLabel8
            // 
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(131, 8);
            this.rjLabel8.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.Size = new System.Drawing.Size(95, 14);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 108;
            this.rjLabel8.Text = "240, 245, 249";
            this.rjLabel8.Visible = false;
            // 
            // lbl_By_Api
            // 
            this.lbl_By_Api.AutoSize = true;
            this.lbl_By_Api.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_By_Api.Font = new System.Drawing.Font("Cairo Medium", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_By_Api.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_By_Api.LinkLabel = false;
            this.lbl_By_Api.Location = new System.Drawing.Point(170, 10);
            this.lbl_By_Api.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_By_Api.Name = "lbl_By_Api";
            this.lbl_By_Api.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_By_Api.Size = new System.Drawing.Size(30, 23);
            this.lbl_By_Api.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_By_Api.TabIndex = 77;
            this.lbl_By_Api.Text = "API";
            // 
            // lbl_Ssh
            // 
            this.lbl_Ssh.AutoSize = true;
            this.lbl_Ssh.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Ssh.Font = new System.Drawing.Font("Cairo Medium", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_Ssh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Ssh.LinkLabel = false;
            this.lbl_Ssh.Location = new System.Drawing.Point(84, 10);
            this.lbl_Ssh.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.lbl_Ssh.Name = "lbl_Ssh";
            this.lbl_Ssh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Ssh.Size = new System.Drawing.Size(33, 23);
            this.lbl_Ssh.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Ssh.TabIndex = 77;
            this.lbl_Ssh.Text = "SSL";
            // 
            // Toggle_RunOffline
            // 
            this.Toggle_RunOffline.Activated = false;
            this.Toggle_RunOffline.AutoSize = true;
            this.Toggle_RunOffline.Customizable = false;
            this.Toggle_RunOffline.Location = new System.Drawing.Point(308, 90);
            this.Toggle_RunOffline.Margin = new System.Windows.Forms.Padding(2);
            this.Toggle_RunOffline.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_RunOffline.Name = "Toggle_RunOffline";
            this.Toggle_RunOffline.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_RunOffline.OFF_Text = null;
            this.Toggle_RunOffline.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_RunOffline.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_RunOffline.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_RunOffline.ON_Text = null;
            this.Toggle_RunOffline.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_RunOffline.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_RunOffline.Size = new System.Drawing.Size(50, 25);
            this.Toggle_RunOffline.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_RunOffline.TabIndex = 106;
            this.Toggle_RunOffline.Tag = "";
            this.Toggle_RunOffline.Text = "#";
            this.Toggle_RunOffline.UseVisualStyleBackColor = true;
            this.Toggle_RunOffline.Visible = false;
            this.Toggle_RunOffline.CheckedChanged += new System.EventHandler(this.Toggle_RunOffline_CheckedChanged);
            // 
            // dgvMicrotikSaved
            // 
            this.dgvMicrotikSaved.AllowUserToAddRows = false;
            this.dgvMicrotikSaved.AllowUserToDeleteRows = false;
            this.dgvMicrotikSaved.AllowUserToResizeRows = false;
            this.dgvMicrotikSaved.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgvMicrotikSaved.AlternatingRowsColorApply = false;
            this.dgvMicrotikSaved.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvMicrotikSaved.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgvMicrotikSaved.BorderRadius = 10;
            this.dgvMicrotikSaved.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgvMicrotikSaved.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgvMicrotikSaved.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgvMicrotikSaved.ColumnHeaderFont = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dgvMicrotikSaved.ColumnHeaderHeight = 35;
            this.dgvMicrotikSaved.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgvMicrotikSaved.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgvMicrotikSaved.ColumnHeadersHeight = 35;
            this.dgvMicrotikSaved.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgvMicrotikSaved.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgvMicrotikSaved.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgvMicrotikSaved.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvMicrotikSaved.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgvMicrotikSaved.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgvMicrotikSaved.Dock = System.Windows.Forms.DockStyle.Fill;
            this.dgvMicrotikSaved.EnableHeadersVisualStyles = false;
            this.dgvMicrotikSaved.GridColor = System.Drawing.Color.Gainsboro;
            this.dgvMicrotikSaved.Location = new System.Drawing.Point(5, 0);
            this.dgvMicrotikSaved.Margin = new System.Windows.Forms.Padding(2);
            this.dgvMicrotikSaved.Name = "dgvMicrotikSaved";
            this.dgvMicrotikSaved.ReadOnly = true;
            this.dgvMicrotikSaved.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgvMicrotikSaved.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgvMicrotikSaved.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgvMicrotikSaved.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgvMicrotikSaved.RowHeadersVisible = false;
            this.dgvMicrotikSaved.RowHeadersWidth = 35;
            this.dgvMicrotikSaved.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgvMicrotikSaved.RowHeight = 33;
            this.dgvMicrotikSaved.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Segoe UI", 9.5F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.Padding = new System.Windows.Forms.Padding(15, 0, 0, 0);
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgvMicrotikSaved.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgvMicrotikSaved.RowsTextColor = System.Drawing.Color.Gray;
            this.dgvMicrotikSaved.RowTemplate.Height = 33;
            this.dgvMicrotikSaved.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgvMicrotikSaved.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgvMicrotikSaved.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgvMicrotikSaved.Size = new System.Drawing.Size(526, 174);
            this.dgvMicrotikSaved.TabIndex = 105;
            this.dgvMicrotikSaved.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.rjDataGridView1_CellDoubleClick);
            this.dgvMicrotikSaved.CellPainting += new System.Windows.Forms.DataGridViewCellPaintingEventHandler(this.dgvMicrotikSaved_CellPainting);
            this.dgvMicrotikSaved.Paint += new System.Windows.Forms.PaintEventHandler(this.dgvMicrotikSaved_Paint);
            // 
            // rjComboBox1
            // 
            this.rjComboBox1.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.rjComboBox1.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.rjComboBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjComboBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjComboBox1.BorderRadius = 10;
            this.rjComboBox1.BorderSize = 1;
            this.rjComboBox1.Customizable = false;
            this.rjComboBox1.DataSource = null;
            this.rjComboBox1.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjComboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.rjComboBox1.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjComboBox1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjComboBox1.Location = new System.Drawing.Point(50, 58);
            this.rjComboBox1.Margin = new System.Windows.Forms.Padding(2);
            this.rjComboBox1.Name = "rjComboBox1";
            this.rjComboBox1.Padding = new System.Windows.Forms.Padding(2);
            this.rjComboBox1.SelectedIndex = -1;
            this.rjComboBox1.SelectedItem = null;
            this.rjComboBox1.SelectedValue = null;
            this.rjComboBox1.Size = new System.Drawing.Size(252, 32);
            this.rjComboBox1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjComboBox1.TabIndex = 102;
            this.rjComboBox1.Texts = "";
            this.rjComboBox1.Visible = false;
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 0;
            this.rjButton1.BorderSize = 0;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton1.ForeColor = System.Drawing.Color.White;
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.None;
            this.rjButton1.IconColor = System.Drawing.Color.White;
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 24;
            this.rjButton1.Location = new System.Drawing.Point(7, 58);
            this.rjButton1.Margin = new System.Windows.Forms.Padding(2);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.Size = new System.Drawing.Size(79, 38);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton1.TabIndex = 104;
            this.rjButton1.Text = "rjButton1";
            this.rjButton1.UseVisualStyleBackColor = false;
            this.rjButton1.Visible = false;
            this.rjButton1.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(329, 64);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(64, 23);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 75;
            this.rjLabel15.Text = "الــــــــــروتر";
            this.rjLabel15.Visible = false;
            // 
            // chkRemember_user
            // 
            this.chkRemember_user.AutoSize = true;
            this.chkRemember_user.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkRemember_user.BorderSize = 1;
            this.chkRemember_user.Check = true;
            this.chkRemember_user.Checked = true;
            this.chkRemember_user.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRemember_user.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkRemember_user.Customizable = false;
            this.chkRemember_user.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkRemember_user.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.chkRemember_user.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkRemember_user.Location = new System.Drawing.Point(112, 179);
            this.chkRemember_user.Margin = new System.Windows.Forms.Padding(2);
            this.chkRemember_user.MinimumSize = new System.Drawing.Size(0, 17);
            this.chkRemember_user.Name = "chkRemember_user";
            this.chkRemember_user.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.chkRemember_user.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.chkRemember_user.Size = new System.Drawing.Size(71, 26);
            this.chkRemember_user.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.chkRemember_user.TabIndex = 107;
            this.chkRemember_user.Text = "تذكر";
            this.chkRemember_user.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.chkRemember_user.UseVisualStyleBackColor = true;
            // 
            // rjLabel11
            // 
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(34, 37);
            this.rjLabel11.Margin = new System.Windows.Forms.Padding(3, 5, 0, 0);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(288, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 77;
            this.rjLabel11.Text = "دخول سريع (بطريقة تحميل ونسخ قاعدة بيانات اليوزمنجر)";
            // 
            // Toggle_load_by_DownloadDB
            // 
            this.Toggle_load_by_DownloadDB.Activated = false;
            this.Toggle_load_by_DownloadDB.AutoSize = true;
            this.Toggle_load_by_DownloadDB.Customizable = false;
            this.Toggle_load_by_DownloadDB.Location = new System.Drawing.Point(327, 34);
            this.Toggle_load_by_DownloadDB.Margin = new System.Windows.Forms.Padding(2);
            this.Toggle_load_by_DownloadDB.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_load_by_DownloadDB.Name = "Toggle_load_by_DownloadDB";
            this.Toggle_load_by_DownloadDB.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_load_by_DownloadDB.OFF_Text = null;
            this.Toggle_load_by_DownloadDB.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_load_by_DownloadDB.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_load_by_DownloadDB.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_load_by_DownloadDB.ON_Text = null;
            this.Toggle_load_by_DownloadDB.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_load_by_DownloadDB.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_load_by_DownloadDB.Size = new System.Drawing.Size(50, 25);
            this.Toggle_load_by_DownloadDB.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_load_by_DownloadDB.TabIndex = 10;
            this.Toggle_load_by_DownloadDB.Tag = "";
            this.Toggle_load_by_DownloadDB.Text = "#";
            this.Toggle_load_by_DownloadDB.UseVisualStyleBackColor = true;
            this.Toggle_load_by_DownloadDB.CheckedChanged += new System.EventHandler(this.Toggle_DownloadDB_CheckedChanged);
            // 
            // txt_note
            // 
            this.txt_note._Customizable = false;
            this.txt_note.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_note.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_note.BorderRadius = 7;
            this.txt_note.BorderSize = 1;
            this.txt_note.Font = new System.Drawing.Font("Tahoma", 11.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.Location = new System.Drawing.Point(188, 242);
            this.txt_note.Margin = new System.Windows.Forms.Padding(2);
            this.txt_note.MaxLength = 100;
            this.txt_note.MultiLine = false;
            this.txt_note.Name = "txt_note";
            this.txt_note.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_note.PasswordChar = false;
            this.txt_note.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_note.PlaceHolderText = "";
            this.txt_note.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_note.Size = new System.Drawing.Size(225, 29);
            this.txt_note.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_note.TabIndex = 7;
            this.txt_note.Tag = "";
            this.txt_note.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(430, 249);
            this.rjLabel3.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.Size = new System.Drawing.Size(71, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 96;
            this.rjLabel3.Text = "ملاحــــــــــــظة";
            // 
            // pnl_ips
            // 
            this.pnl_ips.BackColor = System.Drawing.Color.Transparent;
            this.pnl_ips.Controls.Add(this.rjLabel5);
            this.pnl_ips.Controls.Add(this.rjLabel16);
            this.pnl_ips.Controls.Add(this.rjLabel17);
            this.pnl_ips.Controls.Add(this.txt_IP_4);
            this.pnl_ips.Controls.Add(this.txt_IP_3);
            this.pnl_ips.Controls.Add(this.txt_IP_1);
            this.pnl_ips.Controls.Add(this.txt_IP_2);
            this.pnl_ips.Controls.Add(this.txt_back_ips);
            this.pnl_ips.Location = new System.Drawing.Point(79, 129);
            this.pnl_ips.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_ips.Name = "pnl_ips";
            this.pnl_ips.Size = new System.Drawing.Size(337, 43);
            this.pnl_ips.TabIndex = 94;
            // 
            // rjLabel5
            // 
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(83, 15);
            this.rjLabel5.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.Size = new System.Drawing.Size(11, 14);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 105;
            this.rjLabel5.Text = ".";
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(163, 15);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(11, 14);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 105;
            this.rjLabel16.Text = ".";
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(244, 15);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(11, 14);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 105;
            this.rjLabel17.Text = ".";
            // 
            // txt_IP_4
            // 
            this.txt_IP_4._Customizable = false;
            this.txt_IP_4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_IP_4.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_4.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_IP_4.BorderRadius = 0;
            this.txt_IP_4.BorderSize = 0;
            this.txt_IP_4.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.txt_IP_4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_4.Location = new System.Drawing.Point(254, 8);
            this.txt_IP_4.Margin = new System.Windows.Forms.Padding(2);
            this.txt_IP_4.MaxLength = 100;
            this.txt_IP_4.MultiLine = false;
            this.txt_IP_4.Name = "txt_IP_4";
            this.txt_IP_4.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_IP_4.PasswordChar = false;
            this.txt_IP_4.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_IP_4.PlaceHolderText = "";
            this.txt_IP_4.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_IP_4.Size = new System.Drawing.Size(61, 25);
            this.txt_IP_4.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_IP_4.TabIndex = 4;
            this.txt_IP_4.Tag = "";
            this.txt_IP_4.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txt_IP_4.onTextChanged += new System.EventHandler(this.txt_IP_4_onTextChanged);
            this.txt_IP_4.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt_IP_4_KeyPress);
            this.txt_IP_4.Leave += new System.EventHandler(this.txt_IP_4_Leave);
            // 
            // txt_IP_3
            // 
            this.txt_IP_3._Customizable = false;
            this.txt_IP_3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_IP_3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_3.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_IP_3.BorderRadius = 0;
            this.txt_IP_3.BorderSize = 0;
            this.txt_IP_3.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.txt_IP_3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_3.Location = new System.Drawing.Point(180, 8);
            this.txt_IP_3.Margin = new System.Windows.Forms.Padding(2);
            this.txt_IP_3.MaxLength = 100;
            this.txt_IP_3.MultiLine = false;
            this.txt_IP_3.Name = "txt_IP_3";
            this.txt_IP_3.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_IP_3.PasswordChar = false;
            this.txt_IP_3.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_IP_3.PlaceHolderText = "";
            this.txt_IP_3.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_IP_3.Size = new System.Drawing.Size(61, 25);
            this.txt_IP_3.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_IP_3.TabIndex = 3;
            this.txt_IP_3.Tag = "";
            this.txt_IP_3.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txt_IP_3.onTextChanged += new System.EventHandler(this.txt_IP_3_onTextChanged);
            this.txt_IP_3.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt_IP_3_KeyPress);
            this.txt_IP_3.Leave += new System.EventHandler(this.txt_IP_3_Leave);
            // 
            // txt_IP_1
            // 
            this.txt_IP_1._Customizable = false;
            this.txt_IP_1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_IP_1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_1.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_IP_1.BorderRadius = 0;
            this.txt_IP_1.BorderSize = 0;
            this.txt_IP_1.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.txt_IP_1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_1.Location = new System.Drawing.Point(2, 8);
            this.txt_IP_1.Margin = new System.Windows.Forms.Padding(2);
            this.txt_IP_1.MaxLength = 100;
            this.txt_IP_1.MultiLine = false;
            this.txt_IP_1.Name = "txt_IP_1";
            this.txt_IP_1.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_IP_1.PasswordChar = false;
            this.txt_IP_1.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_IP_1.PlaceHolderText = "";
            this.txt_IP_1.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_IP_1.Size = new System.Drawing.Size(78, 25);
            this.txt_IP_1.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_IP_1.TabIndex = 1;
            this.txt_IP_1.Tag = "";
            this.txt_IP_1.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txt_IP_1.onTextChanged += new System.EventHandler(this.txt_IP_1_onTextChanged);
            this.txt_IP_1.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt_IP_1_KeyPress);
            this.txt_IP_1.Leave += new System.EventHandler(this.txt_IP_1_Leave);
            // 
            // txt_IP_2
            // 
            this.txt_IP_2._Customizable = false;
            this.txt_IP_2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_IP_2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_2.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_IP_2.BorderRadius = 0;
            this.txt_IP_2.BorderSize = 0;
            this.txt_IP_2.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Bold);
            this.txt_IP_2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_IP_2.Location = new System.Drawing.Point(99, 8);
            this.txt_IP_2.Margin = new System.Windows.Forms.Padding(2);
            this.txt_IP_2.MaxLength = 100;
            this.txt_IP_2.MultiLine = false;
            this.txt_IP_2.Name = "txt_IP_2";
            this.txt_IP_2.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_IP_2.PasswordChar = false;
            this.txt_IP_2.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_IP_2.PlaceHolderText = "";
            this.txt_IP_2.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_IP_2.Size = new System.Drawing.Size(61, 25);
            this.txt_IP_2.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_IP_2.TabIndex = 2;
            this.txt_IP_2.Tag = "";
            this.txt_IP_2.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txt_IP_2.onTextChanged += new System.EventHandler(this.txt_IP_2_onTextChanged);
            this.txt_IP_2.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.txt_IP_2_KeyPress);
            this.txt_IP_2.Leave += new System.EventHandler(this.txt_IP_2_Leave);
            // 
            // txt_back_ips
            // 
            this.txt_back_ips._Customizable = false;
            this.txt_back_ips.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_back_ips.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_back_ips.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_back_ips.BorderRadius = 7;
            this.txt_back_ips.BorderSize = 1;
            this.txt_back_ips.Font = new System.Drawing.Font("Verdana", 15F, System.Drawing.FontStyle.Bold);
            this.txt_back_ips.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_back_ips.Location = new System.Drawing.Point(0, 4);
            this.txt_back_ips.Margin = new System.Windows.Forms.Padding(2);
            this.txt_back_ips.MultiLine = false;
            this.txt_back_ips.Name = "txt_back_ips";
            this.txt_back_ips.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_back_ips.PasswordChar = false;
            this.txt_back_ips.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_back_ips.PlaceHolderText = null;
            this.txt_back_ips.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_back_ips.Size = new System.Drawing.Size(334, 36);
            this.txt_back_ips.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_back_ips.TabIndex = 95;
            this.txt_back_ips.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            // 
            // txtUsername
            // 
            this.txtUsername._Customizable = false;
            this.txtUsername.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtUsername.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtUsername.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtUsername.BorderRadius = 7;
            this.txtUsername.BorderSize = 1;
            this.txtUsername.Font = new System.Drawing.Font("Verdana", 10.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txtUsername.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtUsername.Location = new System.Drawing.Point(188, 178);
            this.txtUsername.Margin = new System.Windows.Forms.Padding(2);
            this.txtUsername.MaxLength = 100;
            this.txtUsername.MultiLine = false;
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtUsername.PasswordChar = false;
            this.txtUsername.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtUsername.PlaceHolderText = "";
            this.txtUsername.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtUsername.Size = new System.Drawing.Size(225, 28);
            this.txtUsername.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtUsername.TabIndex = 5;
            this.txtUsername.Tag = "";
            this.txtUsername.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtUsername.KeyDown += new System.Windows.Forms.KeyEventHandler(this.txtPassword_KeyDown);
            // 
            // btnDeleteRB
            // 
            this.btnDeleteRB.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeleteRB.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDeleteRB.BorderRadius = 5;
            this.btnDeleteRB.BorderSize = 1;
            this.btnDeleteRB.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnDeleteRB.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDeleteRB.FlatAppearance.BorderSize = 0;
            this.btnDeleteRB.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDeleteRB.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDeleteRB.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDeleteRB.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnDeleteRB.ForeColor = System.Drawing.Color.White;
            this.btnDeleteRB.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btnDeleteRB.IconColor = System.Drawing.Color.White;
            this.btnDeleteRB.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDeleteRB.IconSize = 25;
            this.btnDeleteRB.Location = new System.Drawing.Point(17, 418);
            this.btnDeleteRB.Margin = new System.Windows.Forms.Padding(2);
            this.btnDeleteRB.Name = "btnDeleteRB";
            this.btnDeleteRB.Padding = new System.Windows.Forms.Padding(1, 2, 0, 0);
            this.btnDeleteRB.Size = new System.Drawing.Size(28, 30);
            this.btnDeleteRB.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDeleteRB.TabIndex = 98;
            this.btnDeleteRB.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDeleteRB.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnDeleteRB.UseVisualStyleBackColor = false;
            this.btnDeleteRB.Click += new System.EventHandler(this.btnDeleteRB_Click);
            // 
            // btnAddRB
            // 
            this.btnAddRB.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddRB.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddRB.BorderRadius = 5;
            this.btnAddRB.BorderSize = 1;
            this.btnAddRB.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAddRB.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddRB.FlatAppearance.BorderSize = 0;
            this.btnAddRB.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAddRB.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAddRB.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddRB.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAddRB.ForeColor = System.Drawing.Color.White;
            this.btnAddRB.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAddRB.IconColor = System.Drawing.Color.White;
            this.btnAddRB.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddRB.IconSize = 25;
            this.btnAddRB.Location = new System.Drawing.Point(72, 418);
            this.btnAddRB.Margin = new System.Windows.Forms.Padding(2);
            this.btnAddRB.Name = "btnAddRB";
            this.btnAddRB.Padding = new System.Windows.Forms.Padding(1, 2, 0, 0);
            this.btnAddRB.Size = new System.Drawing.Size(28, 30);
            this.btnAddRB.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddRB.TabIndex = 97;
            this.btnAddRB.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAddRB.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAddRB.UseVisualStyleBackColor = false;
            this.btnAddRB.Click += new System.EventHandler(this.btnAddRB_Click);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // toolTip1
            // 
            this.toolTip1.AutoPopDelay = 8000;
            this.toolTip1.InitialDelay = 100;
            this.toolTip1.ReshowDelay = 100;
            // 
            // rjButton2
            // 
            this.rjButton2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderRadius = 0;
            this.rjButton2.BorderSize = 0;
            this.rjButton2.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton2.FlatAppearance.BorderSize = 0;
            this.rjButton2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton2.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton2.ForeColor = System.Drawing.Color.White;
            this.rjButton2.IconChar = FontAwesome.Sharp.IconChar.None;
            this.rjButton2.IconColor = System.Drawing.Color.White;
            this.rjButton2.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton2.IconSize = 24;
            this.rjButton2.Location = new System.Drawing.Point(65, 374);
            this.rjButton2.Margin = new System.Windows.Forms.Padding(2);
            this.rjButton2.Name = "rjButton2";
            this.rjButton2.Size = new System.Drawing.Size(75, 29);
            this.rjButton2.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton2.TabIndex = 105;
            this.rjButton2.Text = "rjButton2";
            this.rjButton2.UseVisualStyleBackColor = false;
            this.rjButton2.Click += new System.EventHandler(this.rjButton2_Click);
            // 
            // rjButton3
            // 
            this.rjButton3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderRadius = 0;
            this.rjButton3.BorderSize = 0;
            this.rjButton3.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton3.FlatAppearance.BorderSize = 0;
            this.rjButton3.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton3.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton3.ForeColor = System.Drawing.Color.White;
            this.rjButton3.IconChar = FontAwesome.Sharp.IconChar.None;
            this.rjButton3.IconColor = System.Drawing.Color.White;
            this.rjButton3.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton3.IconSize = 24;
            this.rjButton3.Location = new System.Drawing.Point(18, 386);
            this.rjButton3.Margin = new System.Windows.Forms.Padding(2);
            this.rjButton3.Name = "rjButton3";
            this.rjButton3.Size = new System.Drawing.Size(41, 28);
            this.rjButton3.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton3.TabIndex = 105;
            this.rjButton3.Text = "rjButton2";
            this.rjButton3.UseVisualStyleBackColor = false;
            this.rjButton3.Visible = false;
            this.rjButton3.Click += new System.EventHandler(this.rjButton3_Click);
            // 
            // Toggle_Ddiable_LoadSession
            // 
            this.Toggle_Ddiable_LoadSession.Activated = false;
            this.Toggle_Ddiable_LoadSession.AutoSize = true;
            this.Toggle_Ddiable_LoadSession.Customizable = false;
            this.Toggle_Ddiable_LoadSession.Location = new System.Drawing.Point(327, 2);
            this.Toggle_Ddiable_LoadSession.Margin = new System.Windows.Forms.Padding(2);
            this.Toggle_Ddiable_LoadSession.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Ddiable_LoadSession.Name = "Toggle_Ddiable_LoadSession";
            this.Toggle_Ddiable_LoadSession.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Ddiable_LoadSession.OFF_Text = null;
            this.Toggle_Ddiable_LoadSession.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Ddiable_LoadSession.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Ddiable_LoadSession.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Ddiable_LoadSession.ON_Text = null;
            this.Toggle_Ddiable_LoadSession.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Ddiable_LoadSession.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Ddiable_LoadSession.Size = new System.Drawing.Size(50, 25);
            this.Toggle_Ddiable_LoadSession.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Ddiable_LoadSession.TabIndex = 9;
            this.Toggle_Ddiable_LoadSession.Tag = "";
            this.Toggle_Ddiable_LoadSession.Text = "#";
            this.Toggle_Ddiable_LoadSession.UseVisualStyleBackColor = true;
            this.Toggle_Ddiable_LoadSession.CheckedChanged += new System.EventHandler(this.Toggle_Ddiable_LoadSession_CheckedChanged);
            // 
            // Radio_ByIP
            // 
            this.Radio_ByIP.AutoSize = true;
            this.Radio_ByIP.Checked = true;
            this.Radio_ByIP.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_ByIP.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_ByIP.Customizable = false;
            this.Radio_ByIP.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Radio_ByIP.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_ByIP.Location = new System.Drawing.Point(136, 2);
            this.Radio_ByIP.Margin = new System.Windows.Forms.Padding(2);
            this.Radio_ByIP.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_ByIP.Name = "Radio_ByIP";
            this.Radio_ByIP.Padding = new System.Windows.Forms.Padding(9, 0, 0, 0);
            this.Radio_ByIP.Size = new System.Drawing.Size(23, 21);
            this.Radio_ByIP.TabIndex = 107;
            this.Radio_ByIP.TabStop = true;
            this.Radio_ByIP.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_ByIP.UseVisualStyleBackColor = true;
            this.Radio_ByIP.CheckedChanged += new System.EventHandler(this.Radio_ByIP_CheckedChanged);
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(428, 102);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(2, 0, 2, 0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(80, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 79;
            this.rjLabel1.Text = "طريقة الاتصال";
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(64, 4);
            this.rjLabel2.Margin = new System.Windows.Forms.Padding(2, 4, 2, 0);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(68, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 79;
            this.rjLabel2.Text = "عنوان (IP)";
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(25, 4);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(2, 4, 2, 0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(97, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 79;
            this.rjLabel4.Text = "رابط (Domain)";
            // 
            // Radio_ByDomain
            // 
            this.Radio_ByDomain.AutoSize = true;
            this.Radio_ByDomain.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_ByDomain.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_ByDomain.Customizable = false;
            this.Radio_ByDomain.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Radio_ByDomain.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_ByDomain.Location = new System.Drawing.Point(126, 2);
            this.Radio_ByDomain.Margin = new System.Windows.Forms.Padding(2);
            this.Radio_ByDomain.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_ByDomain.Name = "Radio_ByDomain";
            this.Radio_ByDomain.Padding = new System.Windows.Forms.Padding(9, 0, 0, 0);
            this.Radio_ByDomain.Size = new System.Drawing.Size(23, 21);
            this.Radio_ByDomain.TabIndex = 107;
            this.Radio_ByDomain.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_ByDomain.UseVisualStyleBackColor = true;
            this.Radio_ByDomain.CheckedChanged += new System.EventHandler(this.Radio_ByDomain_CheckedChanged);
            // 
            // Radio_BySmart
            // 
            this.Radio_BySmart.AutoSize = true;
            this.Radio_BySmart.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_BySmart.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_BySmart.Customizable = false;
            this.Radio_BySmart.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Radio_BySmart.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_BySmart.Location = new System.Drawing.Point(25, 2);
            this.Radio_BySmart.Margin = new System.Windows.Forms.Padding(2);
            this.Radio_BySmart.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_BySmart.Name = "Radio_BySmart";
            this.Radio_BySmart.Padding = new System.Windows.Forms.Padding(9, 0, 0, 0);
            this.Radio_BySmart.Size = new System.Drawing.Size(23, 21);
            this.Radio_BySmart.TabIndex = 110;
            this.Radio_BySmart.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_BySmart.UseVisualStyleBackColor = true;
            this.Radio_BySmart.Visible = false;
            this.Radio_BySmart.CheckedChanged += new System.EventHandler(this.Radio_BySmart_CheckedChanged);
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(10, 29);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(2, 4, 2, 0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(38, 51);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 109;
            this.rjLabel9.Text = "سمارت كلود";
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(101, 5);
            this.rjLabel10.Margin = new System.Windows.Forms.Padding(3, 5, 0, 0);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel10.Size = new System.Drawing.Size(221, 17);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 77;
            this.rjLabel10.Text = "دخول عبر تخصيص البيانات التي سيتم جلبها";
            // 
            // Toggle_Custom_Login
            // 
            this.Toggle_Custom_Login.Activated = false;
            this.Toggle_Custom_Login.AutoSize = true;
            this.Toggle_Custom_Login.Customizable = false;
            this.Toggle_Custom_Login.Location = new System.Drawing.Point(327, 66);
            this.Toggle_Custom_Login.Margin = new System.Windows.Forms.Padding(2);
            this.Toggle_Custom_Login.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Custom_Login.Name = "Toggle_Custom_Login";
            this.Toggle_Custom_Login.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Custom_Login.OFF_Text = null;
            this.Toggle_Custom_Login.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Custom_Login.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Custom_Login.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Custom_Login.ON_Text = null;
            this.Toggle_Custom_Login.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Custom_Login.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Custom_Login.Size = new System.Drawing.Size(50, 25);
            this.Toggle_Custom_Login.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Custom_Login.TabIndex = 11;
            this.Toggle_Custom_Login.Tag = "";
            this.Toggle_Custom_Login.Text = "#";
            this.Toggle_Custom_Login.UseVisualStyleBackColor = true;
            this.Toggle_Custom_Login.CheckedChanged += new System.EventHandler(this.Toggle_Custom_Login_CheckedChanged);
            // 
            // btn_Custom_Login
            // 
            this.btn_Custom_Login.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Custom_Login.BackColor = System.Drawing.SystemColors.Control;
            this.btn_Custom_Login.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Custom_Login.BorderRadius = 5;
            this.btn_Custom_Login.BorderSize = 1;
            this.btn_Custom_Login.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Custom_Login.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Custom_Login.FlatAppearance.BorderSize = 0;
            this.btn_Custom_Login.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(225)))), ((int)(((byte)(225)))));
            this.btn_Custom_Login.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(211)))), ((int)(((byte)(211)))));
            this.btn_Custom_Login.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Custom_Login.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Custom_Login.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Custom_Login.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_Custom_Login.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Custom_Login.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Custom_Login.IconSize = 18;
            this.btn_Custom_Login.Location = new System.Drawing.Point(38, 2);
            this.btn_Custom_Login.Margin = new System.Windows.Forms.Padding(2);
            this.btn_Custom_Login.Name = "btn_Custom_Login";
            this.btn_Custom_Login.Size = new System.Drawing.Size(61, 26);
            this.btn_Custom_Login.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Custom_Login.TabIndex = 12;
            this.btn_Custom_Login.Text = "تخصيص";
            this.btn_Custom_Login.UseCompatibleTextRendering = true;
            this.btn_Custom_Login.UseVisualStyleBackColor = false;
            this.btn_Custom_Login.Click += new System.EventHandler(this.btn_Custom_Login_Click);
            // 
            // pnl_Smart
            // 
            this.pnl_Smart.BackColor = System.Drawing.Color.Transparent;
            this.pnl_Smart.Controls.Add(this.txt_SmartCloud);
            this.pnl_Smart.Controls.Add(this.rjTextBox2);
            this.pnl_Smart.Location = new System.Drawing.Point(76, 129);
            this.pnl_Smart.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_Smart.Name = "pnl_Smart";
            this.pnl_Smart.Size = new System.Drawing.Size(341, 43);
            this.pnl_Smart.TabIndex = 96;
            this.pnl_Smart.Visible = false;
            // 
            // txt_SmartCloud
            // 
            this.txt_SmartCloud._Customizable = false;
            this.txt_SmartCloud.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SmartCloud.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SmartCloud.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SmartCloud.BorderRadius = 0;
            this.txt_SmartCloud.BorderSize = 0;
            this.txt_SmartCloud.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Bold);
            this.txt_SmartCloud.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SmartCloud.Location = new System.Drawing.Point(5, 7);
            this.txt_SmartCloud.Margin = new System.Windows.Forms.Padding(2);
            this.txt_SmartCloud.MultiLine = false;
            this.txt_SmartCloud.Name = "txt_SmartCloud";
            this.txt_SmartCloud.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SmartCloud.PasswordChar = false;
            this.txt_SmartCloud.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SmartCloud.PlaceHolderText = "قم باعداد رابط كلود  خاص بتطبيق سمارت من الاعدادت";
            this.txt_SmartCloud.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SmartCloud.Size = new System.Drawing.Size(334, 25);
            this.txt_SmartCloud.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SmartCloud.TabIndex = 16;
            this.txt_SmartCloud.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            // 
            // rjTextBox2
            // 
            this.rjTextBox2._Customizable = false;
            this.rjTextBox2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjTextBox2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox2.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.rjTextBox2.BorderRadius = 7;
            this.rjTextBox2.BorderSize = 1;
            this.rjTextBox2.Font = new System.Drawing.Font("Arial", 14F);
            this.rjTextBox2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox2.Location = new System.Drawing.Point(0, 4);
            this.rjTextBox2.Margin = new System.Windows.Forms.Padding(2);
            this.rjTextBox2.MultiLine = false;
            this.rjTextBox2.Name = "rjTextBox2";
            this.rjTextBox2.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.rjTextBox2.PasswordChar = false;
            this.rjTextBox2.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.rjTextBox2.PlaceHolderText = null;
            this.rjTextBox2.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.rjTextBox2.Size = new System.Drawing.Size(341, 33);
            this.rjTextBox2.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.rjTextBox2.TabIndex = 95;
            this.rjTextBox2.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 5;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.White;
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.White;
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(100, 418);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(2);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(1, 2, 0, 0);
            this.btn_Refresh.Size = new System.Drawing.Size(28, 30);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Refresh.TabIndex = 112;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.rjButton4_Click);
            // 
            // chkRemember_pass
            // 
            this.chkRemember_pass.AutoSize = true;
            this.chkRemember_pass.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkRemember_pass.BorderSize = 1;
            this.chkRemember_pass.Check = true;
            this.chkRemember_pass.Checked = true;
            this.chkRemember_pass.CheckState = System.Windows.Forms.CheckState.Checked;
            this.chkRemember_pass.Cursor = System.Windows.Forms.Cursors.Hand;
            this.chkRemember_pass.Customizable = false;
            this.chkRemember_pass.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.chkRemember_pass.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.chkRemember_pass.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.chkRemember_pass.Location = new System.Drawing.Point(113, 212);
            this.chkRemember_pass.Margin = new System.Windows.Forms.Padding(2);
            this.chkRemember_pass.MinimumSize = new System.Drawing.Size(0, 17);
            this.chkRemember_pass.Name = "chkRemember_pass";
            this.chkRemember_pass.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.chkRemember_pass.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.chkRemember_pass.Size = new System.Drawing.Size(71, 26);
            this.chkRemember_pass.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.chkRemember_pass.TabIndex = 107;
            this.chkRemember_pass.Text = "تذكر";
            this.chkRemember_pass.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.chkRemember_pass.UseVisualStyleBackColor = true;
            // 
            // rjBy_Port
            // 
            this.rjBy_Port.AutoSize = true;
            this.rjBy_Port.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port.BorderSize = 1;
            this.rjBy_Port.Check = false;
            this.rjBy_Port.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjBy_Port.Customizable = false;
            this.rjBy_Port.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjBy_Port.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjBy_Port.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port.Location = new System.Drawing.Point(49, 179);
            this.rjBy_Port.Margin = new System.Windows.Forms.Padding(2);
            this.rjBy_Port.MinimumSize = new System.Drawing.Size(0, 17);
            this.rjBy_Port.Name = "rjBy_Port";
            this.rjBy_Port.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.rjBy_Port.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjBy_Port.Size = new System.Drawing.Size(71, 21);
            this.rjBy_Port.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjBy_Port.TabIndex = 107;
            this.rjBy_Port.Text = "بورت";
            this.rjBy_Port.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjBy_Port.UseVisualStyleBackColor = true;
            this.rjBy_Port.Visible = false;
            this.rjBy_Port.CheckedChanged += new System.EventHandler(this.rjBy_Port_CheckedChanged);
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 14.19491F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 85.80508F));
            this.tableLayoutPanel1.Controls.Add(this.rjLabel11, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.Toggle_load_by_DownloadDB, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.Toggle_Ddiable_LoadSession, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.rjLabel6, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.Toggle_Custom_Login, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.flowLayoutPanel1, 1, 2);
            this.tableLayoutPanel1.Location = new System.Drawing.Point(65, 275);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(2);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tableLayoutPanel1.RowCount = 3;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(378, 95);
            this.tableLayoutPanel1.TabIndex = 113;
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.Controls.Add(this.rjLabel10);
            this.flowLayoutPanel1.Controls.Add(this.btn_Custom_Login);
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(0, 64);
            this.flowLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Size = new System.Drawing.Size(325, 31);
            this.flowLayoutPanel1.TabIndex = 107;
            // 
            // flowLayoutPanel2
            // 
            this.flowLayoutPanel2.Controls.Add(this.Radio_ByIP);
            this.flowLayoutPanel2.Controls.Add(this.rjLabel2);
            this.flowLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel2.Location = new System.Drawing.Point(201, 0);
            this.flowLayoutPanel2.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel2.Name = "flowLayoutPanel2";
            this.flowLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel2.Size = new System.Drawing.Size(161, 29);
            this.flowLayoutPanel2.TabIndex = 107;
            // 
            // tableLayoutPanel2
            // 
            this.tableLayoutPanel2.ColumnCount = 3;
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 151F));
            this.tableLayoutPanel2.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 161F));
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel4, 1, 0);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel3, 0, 0);
            this.tableLayoutPanel2.Controls.Add(this.flowLayoutPanel2, 2, 0);
            this.tableLayoutPanel2.Location = new System.Drawing.Point(39, 97);
            this.tableLayoutPanel2.Margin = new System.Windows.Forms.Padding(2);
            this.tableLayoutPanel2.Name = "tableLayoutPanel2";
            this.tableLayoutPanel2.RowCount = 1;
            this.tableLayoutPanel2.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Percent, 50F));
            this.tableLayoutPanel2.Size = new System.Drawing.Size(362, 29);
            this.tableLayoutPanel2.TabIndex = 108;
            // 
            // flowLayoutPanel4
            // 
            this.flowLayoutPanel4.Controls.Add(this.Radio_ByDomain);
            this.flowLayoutPanel4.Controls.Add(this.rjLabel4);
            this.flowLayoutPanel4.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel4.Location = new System.Drawing.Point(50, 0);
            this.flowLayoutPanel4.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel4.Name = "flowLayoutPanel4";
            this.flowLayoutPanel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel4.Size = new System.Drawing.Size(151, 29);
            this.flowLayoutPanel4.TabIndex = 109;
            // 
            // flowLayoutPanel3
            // 
            this.flowLayoutPanel3.Controls.Add(this.Radio_BySmart);
            this.flowLayoutPanel3.Controls.Add(this.rjLabel9);
            this.flowLayoutPanel3.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel3.Location = new System.Drawing.Point(0, 0);
            this.flowLayoutPanel3.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel3.Name = "flowLayoutPanel3";
            this.flowLayoutPanel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel3.Size = new System.Drawing.Size(50, 29);
            this.flowLayoutPanel3.TabIndex = 108;
            this.flowLayoutPanel3.Visible = false;
            // 
            // txtPort_SmartCloud
            // 
            this.txtPort_SmartCloud._Customizable = false;
            this.txtPort_SmartCloud.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPort_SmartCloud.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort_SmartCloud.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPort_SmartCloud.BorderRadius = 7;
            this.txtPort_SmartCloud.BorderSize = 1;
            this.txtPort_SmartCloud.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F);
            this.txtPort_SmartCloud.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort_SmartCloud.Location = new System.Drawing.Point(77, 210);
            this.txtPort_SmartCloud.Margin = new System.Windows.Forms.Padding(2);
            this.txtPort_SmartCloud.MaxLength = 100;
            this.txtPort_SmartCloud.MultiLine = false;
            this.txtPort_SmartCloud.Name = "txtPort_SmartCloud";
            this.txtPort_SmartCloud.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPort_SmartCloud.PasswordChar = false;
            this.txtPort_SmartCloud.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPort_SmartCloud.PlaceHolderText = "";
            this.txtPort_SmartCloud.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPort_SmartCloud.Size = new System.Drawing.Size(54, 26);
            this.txtPort_SmartCloud.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPort_SmartCloud.TabIndex = 8;
            this.txtPort_SmartCloud.Tag = "";
            this.txtPort_SmartCloud.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPort_SmartCloud.Visible = false;
            // 
            // txtPort_Domain
            // 
            this.txtPort_Domain._Customizable = false;
            this.txtPort_Domain.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPort_Domain.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort_Domain.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPort_Domain.BorderRadius = 7;
            this.txtPort_Domain.BorderSize = 1;
            this.txtPort_Domain.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F);
            this.txtPort_Domain.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort_Domain.Location = new System.Drawing.Point(77, 210);
            this.txtPort_Domain.Margin = new System.Windows.Forms.Padding(2);
            this.txtPort_Domain.MaxLength = 100;
            this.txtPort_Domain.MultiLine = false;
            this.txtPort_Domain.Name = "txtPort_Domain";
            this.txtPort_Domain.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPort_Domain.PasswordChar = false;
            this.txtPort_Domain.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPort_Domain.PlaceHolderText = "";
            this.txtPort_Domain.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPort_Domain.Size = new System.Drawing.Size(54, 26);
            this.txtPort_Domain.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPort_Domain.TabIndex = 115;
            this.txtPort_Domain.Tag = "";
            this.txtPort_Domain.TextAlign = System.Windows.Forms.HorizontalAlignment.Left;
            this.txtPort_Domain.Visible = false;
            // 
            // rjBy_Port_Domain
            // 
            this.rjBy_Port_Domain.AutoSize = true;
            this.rjBy_Port_Domain.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port_Domain.BorderSize = 1;
            this.rjBy_Port_Domain.Check = false;
            this.rjBy_Port_Domain.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjBy_Port_Domain.Customizable = false;
            this.rjBy_Port_Domain.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjBy_Port_Domain.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjBy_Port_Domain.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port_Domain.Location = new System.Drawing.Point(49, 179);
            this.rjBy_Port_Domain.Margin = new System.Windows.Forms.Padding(2);
            this.rjBy_Port_Domain.MinimumSize = new System.Drawing.Size(0, 17);
            this.rjBy_Port_Domain.Name = "rjBy_Port_Domain";
            this.rjBy_Port_Domain.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.rjBy_Port_Domain.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjBy_Port_Domain.Size = new System.Drawing.Size(71, 21);
            this.rjBy_Port_Domain.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjBy_Port_Domain.TabIndex = 116;
            this.rjBy_Port_Domain.Text = "بورت";
            this.rjBy_Port_Domain.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjBy_Port_Domain.UseVisualStyleBackColor = true;
            this.rjBy_Port_Domain.Visible = false;
            this.rjBy_Port_Domain.CheckedChanged += new System.EventHandler(this.rjBy_Port_Domain_CheckedChanged);
            // 
            // rjBy_Port_SmartCloud
            // 
            this.rjBy_Port_SmartCloud.AutoSize = true;
            this.rjBy_Port_SmartCloud.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port_SmartCloud.BorderSize = 1;
            this.rjBy_Port_SmartCloud.Check = false;
            this.rjBy_Port_SmartCloud.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjBy_Port_SmartCloud.Customizable = false;
            this.rjBy_Port_SmartCloud.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjBy_Port_SmartCloud.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjBy_Port_SmartCloud.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjBy_Port_SmartCloud.Location = new System.Drawing.Point(49, 179);
            this.rjBy_Port_SmartCloud.Margin = new System.Windows.Forms.Padding(2);
            this.rjBy_Port_SmartCloud.MinimumSize = new System.Drawing.Size(0, 17);
            this.rjBy_Port_SmartCloud.Name = "rjBy_Port_SmartCloud";
            this.rjBy_Port_SmartCloud.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.rjBy_Port_SmartCloud.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjBy_Port_SmartCloud.Size = new System.Drawing.Size(71, 21);
            this.rjBy_Port_SmartCloud.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjBy_Port_SmartCloud.TabIndex = 117;
            this.rjBy_Port_SmartCloud.Text = "بورت";
            this.rjBy_Port_SmartCloud.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjBy_Port_SmartCloud.UseVisualStyleBackColor = true;
            this.rjBy_Port_SmartCloud.Visible = false;
            this.rjBy_Port_SmartCloud.CheckedChanged += new System.EventHandler(this.rjBy_Port_SmartCloud_CheckedChanged);
            // 
            // btn_Save
            // 
            this.btn_Save.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Save.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Save.BorderRadius = 5;
            this.btn_Save.BorderSize = 1;
            this.btn_Save.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Save.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_Save.FlatAppearance.BorderSize = 0;
            this.btn_Save.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Save.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Save.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Save.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Save.ForeColor = System.Drawing.Color.White;
            this.btn_Save.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.btn_Save.IconColor = System.Drawing.Color.White;
            this.btn_Save.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Save.IconSize = 25;
            this.btn_Save.Location = new System.Drawing.Point(44, 418);
            this.btn_Save.Margin = new System.Windows.Forms.Padding(2);
            this.btn_Save.Name = "btn_Save";
            this.btn_Save.Padding = new System.Windows.Forms.Padding(1, 2, 0, 0);
            this.btn_Save.Size = new System.Drawing.Size(28, 30);
            this.btn_Save.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Save.TabIndex = 118;
            this.btn_Save.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Save.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Save.UseVisualStyleBackColor = false;
            this.btn_Save.Click += new System.EventHandler(this.btn_Save_Click);
            // 
            // rjDragControl1
            // 
            this.rjDragControl1.DragControl = this.lbl_SmartCreator;
            // 
            // iconSplitButton1
            // 
            this.iconSplitButton1.Flip = FontAwesome.Sharp.FlipOrientation.Normal;
            this.iconSplitButton1.IconChar = FontAwesome.Sharp.IconChar.None;
            this.iconSplitButton1.IconColor = System.Drawing.Color.Black;
            this.iconSplitButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.iconSplitButton1.IconSize = 48;
            this.iconSplitButton1.Name = "iconSplitButton1";
            this.iconSplitButton1.Rotation = 0D;
            this.iconSplitButton1.Size = new System.Drawing.Size(23, 23);
            this.iconSplitButton1.Text = "iconSplitButton1";
            // 
            // rjProgressBar1
            // 
            this.rjProgressBar1.ChannelColor = System.Drawing.Color.LightSteelBlue;
            this.rjProgressBar1.ChannelHeight = 6;
            this.rjProgressBar1.Customizable = true;
            this.rjProgressBar1.ForeBackColor = System.Drawing.Color.RoyalBlue;
            this.rjProgressBar1.ForeColor = System.Drawing.Color.White;
            this.rjProgressBar1.Location = new System.Drawing.Point(8, 628);
            this.rjProgressBar1.Name = "rjProgressBar1";
            this.rjProgressBar1.ShowMaximun = false;
            this.rjProgressBar1.ShowValue = SmartCreator.RJControls.TextPosition.Right;
            this.rjProgressBar1.Size = new System.Drawing.Size(265, 23);
            this.rjProgressBar1.SliderColor = System.Drawing.Color.RoyalBlue;
            this.rjProgressBar1.SliderHeight = 6;
            this.rjProgressBar1.SymbolAfter = "";
            this.rjProgressBar1.SymbolBefore = "";
            this.rjProgressBar1.TabIndex = 119;
            // 
            // LoginForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(551, 680);
            this.Controls.Add(this.rjProgressBar1);
            this.Controls.Add(this.btn_Save);
            this.Controls.Add(this.tableLayoutPanel2);
            this.Controls.Add(this.tableLayoutPanel1);
            this.Controls.Add(this.btn_Refresh);
            this.Controls.Add(this.btnLogin);
            this.Controls.Add(this.rjLabel1);
            this.Controls.Add(this.rjButton3);
            this.Controls.Add(this.rjButton2);
            this.Controls.Add(this.rjLabel3);
            this.Controls.Add(this.txt_note);
            this.Controls.Add(this.btnDeleteRB);
            this.Controls.Add(this.btnAddRB);
            this.Controls.Add(this.btnShowLoginDate);
            this.Controls.Add(this.rjPanel2);
            this.Controls.Add(this.btn_ShowNighboor);
            this.Controls.Add(this.rjPanel1);
            this.Controls.Add(this.pnlTopTitle);
            this.Controls.Add(this.pnlLeftBorder);
            this.Controls.Add(this.pnlRightBorder);
            this.Controls.Add(this.pnlBootom);
            this.Controls.Add(this.lbl_Address);
            this.Controls.Add(this.lbl_Password);
            this.Controls.Add(this.lbl_username);
            this.Controls.Add(this.lblMessage);
            this.Controls.Add(this.txtPassword);
            this.Controls.Add(this.txtUsername);
            this.Controls.Add(this.pnlTopBorder);
            this.Controls.Add(this.txtPortSSH);
            this.Controls.Add(this.pnl_ips);
            this.Controls.Add(this.pnl_Smart);
            this.Controls.Add(this.pnl_domain);
            this.Controls.Add(this.txtPort);
            this.Controls.Add(this.txtPort_Domain);
            this.Controls.Add(this.chkRemember_pass);
            this.Controls.Add(this.rjBy_Port);
            this.Controls.Add(this.rjBy_Port_SmartCloud);
            this.Controls.Add(this.rjBy_Port_Domain);
            this.Controls.Add(this.chkRemember_user);
            this.Controls.Add(this.txtPort_SmartCloud);
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Location = new System.Drawing.Point(0, 0);
            this.Margin = new System.Windows.Forms.Padding(2);
            this.Name = "LoginForm";
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "تسجيل الدخول";
            this.Load += new System.EventHandler(this.LoginForm_Load);
            this.MouseDown += new System.Windows.Forms.MouseEventHandler(this.LoginForm_MouseDown);
            this.pnlTopTitle.ResumeLayout(false);
            this.pnlTopTitle.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.pnl_domain.ResumeLayout(false);
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.pnl_port.ResumeLayout(false);
            this.pnl_port.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgvMicrotikSaved)).EndInit();
            this.pnl_ips.ResumeLayout(false);
            this.pnl_ips.PerformLayout();
            this.pnl_Smart.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.flowLayoutPanel1.PerformLayout();
            this.flowLayoutPanel2.ResumeLayout(false);
            this.flowLayoutPanel2.PerformLayout();
            this.tableLayoutPanel2.ResumeLayout(false);
            this.flowLayoutPanel4.ResumeLayout(false);
            this.flowLayoutPanel4.PerformLayout();
            this.flowLayoutPanel3.ResumeLayout(false);
            this.flowLayoutPanel3.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private RJControls.RJDragControl dragControl1;
        private RJControls.RJTextBox txtPassword;
        private RJControls.RJButton btnLogin;
        private RJControls.RJDragControl dragControl2;
        private System.Windows.Forms.Label lblMessage;
        private RJControls.RJPanel pnlTopBorder;
        private RJControls.RJLabel lbl_username;
        private RJControls.RJLabel lbl_Password;
        private RJControls.RJTextBox txtDomain;
        private RJControls.RJLabel lbl_Address;
        private RJControls.RJRadioButton rjRB_SSH;
        private RJControls.RJRadioButton rjRB_API;
        private RJControls.RJTextBox txtPort;
        private RJControls.RJTextBox txtPortSSH;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJPanel pnlBootom;
        private RJControls.RJPanel pnlRightBorder;
        private RJControls.RJPanel pnlLeftBorder;
        private RJControls.RJPanel pnlTopTitle;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJLabel lbl_loginType;
        private RJControls.RJLabel lbl_Viriion;
        private RJControls.RJButton btnShowLoginDate;
        private RJControls.RJButton btn_ShowNighboor;
        private RJControls.RJPanel rjPanel2;
        private System.Windows.Forms.Panel pnl_ips;
        private RJControls.RJTextBox txt_back_ips;
        private RJControls.RJTextBox txt_IP_4;
        private RJControls.RJTextBox txt_IP_3;
        private RJControls.RJTextBox txt_IP_2;
        private RJControls.RJTextBox txt_IP_1;
        private RJControls.RJTextBox txt_note;
        private RJControls.RJTextBox txtUsername;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJButton btnDeleteRB;
        private RJControls.RJButton btnAddRB;
        private RJControls.RJLabel lbl_Ssh;
        private RJControls.RJLabel lbl_By_Api;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.ToolTip toolTip1;
        private RJControls.RJComboBox rjComboBox1;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJLabel lbl_by_ip;
        private RJControls.RJButton rjButton1;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJPanel pnl_port;
        private System.Windows.Forms.Panel pnl_domain;
        private RJControls.RJTextBox rjTextBox5;
        private RJControls.RJDataGridView dgvMicrotikSaved;
        private RJControls.RJButton rjButton2;
        private RJControls.RJButton rjButton3;
        private RJControls.RJToggleButton Toggle_Ddiable_LoadSession;
        private RJControls.RJToggleButton Toggle_RunOffline;
        private RJControls.RJToggleButton Toggle_load_by_DownloadDB;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJRadioButton Radio_ByDomain;
        private RJControls.RJRadioButton Radio_ByIP;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJRadioButton Radio_BySmart;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJToggleButton Toggle_Custom_Login;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJButton btn_Custom_Login;
        private System.Windows.Forms.Panel pnl_Smart;
        private RJControls.RJTextBox txt_SmartCloud;
        private RJControls.RJTextBox rjTextBox2;
        private RJControls.RJLabel lbl_SmartCreator;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJCheckBox chkRemember_user;
        private RJControls.RJCheckBox rjBy_Port;
        private RJControls.RJCheckBox chkRemember_pass;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel2;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel4;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel3;
        private RJControls.RJTextBox txtPort_Domain;
        private RJControls.RJTextBox txtPort_SmartCloud;
        private RJControls.RJCheckBox rjBy_Port_SmartCloud;
        private RJControls.RJCheckBox rjBy_Port_Domain;
        private RJControls.RJButton btn_Save;
        private RJControls.RJDragControl rjDragControl1;
        private FontAwesome.Sharp.IconSplitButton iconSplitButton1;
        private RJControls.RJProgressBar rjProgressBar1;
    }
}