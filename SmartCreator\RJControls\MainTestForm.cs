using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// النموذج الرئيسي للاختبار - بديل كامل لـ Form1
    /// </summary>
    public partial class MainTestForm : Form
    {
        public MainTestForm()
        {
            InitializeComponent();
            SetupMainInterface();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // MainTestForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1200, 800);
            this.Name = "MainTestForm";
            this.Text = "🚀 RJTabControl - النموذج الرئيسي للاختبار";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.MinimumSize = new Size(1000, 700);
            this.WindowState = FormWindowState.Maximized;
            
            this.ResumeLayout(false);
        }

        private void SetupMainInterface()
        {
            // إنشاء TabControl رئيسي
            var mainTabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 50,
                TabSpacing = 3,
                TabPadding = 30,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 10,
                TabsPanelBackColor = Color.FromArgb(55, 55, 58),
                ContentBackColor = Color.FromArgb(37, 37, 38)
            };

            // تاب الترحيب والمعلومات
            CreateWelcomeTab(mainTabControl);

            // تاب الاختبارات السريعة
            CreateQuickTestsTab(mainTabControl);

            // تاب اختبارات Designer
            CreateDesignerTestsTab(mainTabControl);

            // تاب العروض التوضيحية
            CreateDemosTab(mainTabControl);

            // تاب المعلومات التقنية
            CreateTechnicalInfoTab(mainTabControl);

            // إضافة TabControl للنموذج
            this.Controls.Add(mainTabControl);

            // تفعيل التاب الأول
            mainTabControl.SelectedIndex = 0;
        }

        private void CreateWelcomeTab(RJTabControl tabControl)
        {
            var welcomeTab = tabControl.AddTab("مرحباً", IconChar.Home);
            welcomeTab.BackColor = Color.FromArgb(0, 122, 204);
            welcomeTab.ForeColor = Color.White;
            welcomeTab.IconSize = 24;
            welcomeTab.BorderRadius = 12;

            var welcomePanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 0,
                Padding = new Padding(40)
            };

            var titleLabel = new Label
            {
                Text = "🎉 مرحباً بك في RJTabControl!",
                Font = new Font("Segoe UI", 24, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                Location = new Point(0, 20),
                Size = new Size(800, 50),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var infoLabel = new Label
            {
                Text = "✅ تم حل جميع مشاكل Designer بنجاح!\n\n" +
                       "🛡️ الحماية المضافة:\n" +
                       "• إصلاح NullReferenceException\n" +
                       "• حماية جميع الخصائص من null\n" +
                       "• Constructor آمن للـ Designer\n" +
                       "• إزالة مراجع RJChildForm\n\n" +
                       "🎨 الميزات المتاحة:\n" +
                       "• دعم كامل للـ Visual Studio Designer\n" +
                       "• Collection Editor مخصص\n" +
                       "• جميع خصائص RJButton متاحة\n" +
                       "• 3 طرق مختلفة لإضافة التابات\n" +
                       "• أحداث شاملة ومفيدة\n\n" +
                       "🚀 RJTabControl جاهز للاستخدام في الإنتاج!",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70),
                Location = new Point(0, 90),
                Size = new Size(800, 400),
                TextAlign = ContentAlignment.TopCenter
            };

            var statusLabel = new Label
            {
                Text = "🎯 اختر من التابات أعلاه لاستكشاف جميع الميزات والاختبارات",
                Font = new Font("Segoe UI", 11, FontStyle.Italic),
                ForeColor = Color.FromArgb(100, 100, 100),
                Location = new Point(0, 520),
                Size = new Size(800, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            welcomePanel.Controls.Add(titleLabel);
            welcomePanel.Controls.Add(infoLabel);
            welcomePanel.Controls.Add(statusLabel);
            welcomeTab.AddControl(welcomePanel);
        }

        private void CreateQuickTestsTab(RJTabControl tabControl)
        {
            var testsTab = tabControl.AddTab("اختبارات سريعة", IconChar.Bolt);
            testsTab.BackColor = Color.FromArgb(76, 175, 80);
            testsTab.ForeColor = Color.White;
            testsTab.IconSize = 22;

            var testsPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(30) };

            // صف الأزرار الأول
            var quickTestButton = CreateTestButton("اختبار سريع", IconChar.Zap, 
                Color.FromArgb(255, 152, 0), new Point(30, 30));
            quickTestButton.Click += (s, e) => QuickErrorTest.RunQuickTest();

            var collectionTestButton = CreateTestButton("اختبار Collection", IconChar.List, 
                Color.FromArgb(156, 39, 176), new Point(280, 30));
            collectionTestButton.Click += (s, e) => QuickErrorTest.TestDesignerCollection();

            var comprehensiveTestButton = CreateTestButton("اختبار شامل", IconChar.CheckCircle, 
                Color.FromArgb(244, 67, 54), new Point(530, 30));
            comprehensiveTestButton.Click += (s, e) => QuickErrorTest.RunComprehensiveTest();

            // صف الأزرار الثاني
            var validationTestButton = CreateTestButton("اختبار التحقق", IconChar.Shield, 
                Color.FromArgb(63, 81, 181), new Point(30, 120));
            validationTestButton.Click += (s, e) => FinalValidationTest.RunValidation();

            var safeDesignerButton = CreateTestButton("Designer آمن", IconChar.PaintBrush, 
                Color.FromArgb(121, 85, 72), new Point(280, 120));
            safeDesignerButton.Click += (s, e) => DesignerSafeTestForm.RunTest();

            var allTestsButton = CreateTestButton("جميع الاختبارات", IconChar.PlayCircle, 
                Color.FromArgb(0, 150, 136), new Point(530, 120));
            allTestsButton.Click += (s, e) => SafeTestRunner.ShowTestMenu();

            var instructionsLabel = new Label
            {
                Text = "🧪 اختبارات سريعة للتأكد من عمل جميع الميزات:\n\n" +
                       "• الاختبار السريع - فحص أساسي لجميع الوظائف\n" +
                       "• اختبار Collection - فحص مجموعة التابات\n" +
                       "• الاختبار الشامل - فحص متقدم مع النتائج\n" +
                       "• اختبار التحقق - فحص نهائي شامل\n" +
                       "• Designer آمن - اختبار حماية NullReference\n" +
                       "• جميع الاختبارات - قائمة كاملة بجميع الاختبارات\n\n" +
                       "✅ جميع الاختبارات تعمل بدون أخطاء!",
                Location = new Point(30, 220),
                Size = new Size(700, 200),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            testsPanel.Controls.Add(quickTestButton);
            testsPanel.Controls.Add(collectionTestButton);
            testsPanel.Controls.Add(comprehensiveTestButton);
            testsPanel.Controls.Add(validationTestButton);
            testsPanel.Controls.Add(safeDesignerButton);
            testsPanel.Controls.Add(allTestsButton);
            testsPanel.Controls.Add(instructionsLabel);

            testsTab.AddControl(testsPanel);
        }

        private void CreateDesignerTestsTab(RJTabControl tabControl)
        {
            var designerTab = tabControl.AddTab("اختبارات Designer", IconChar.PaintBrush);
            designerTab.BackColor = Color.FromArgb(156, 39, 176);
            designerTab.ForeColor = Color.White;
            designerTab.IconSize = 20;

            var designerTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "🎨 اختبارات Visual Studio Designer:\n\n" +
                       "✅ المشاكل التي تم حلها:\n" +
                       "• NullReferenceException عند إنشاء RJTabControl\n" +
                       "• خطأ 'Could not find type RJChildForm'\n" +
                       "• مشاكل Collection Editor\n" +
                       "• خصائص غير مرئية في Properties Panel\n\n" +
                       "🛡️ الحماية المضافة:\n" +
                       "• فحص null لجميع الخصائص\n" +
                       "• قيم افتراضية آمنة\n" +
                       "• Constructor محمي ومرتب\n" +
                       "• طرق داخلية آمنة\n\n" +
                       "🎯 كيفية الاختبار في Designer:\n" +
                       "1. افتح Visual Studio\n" +
                       "2. أنشئ Windows Form جديد\n" +
                       "3. اسحب RJTabControl من Toolbox\n" +
                       "4. افتح Properties → Tabs → Collection\n" +
                       "5. أضف تابات وعدل خصائصها\n" +
                       "6. انقر على التابات في Designer للتنقل\n\n" +
                       "✅ النتيجة المتوقعة:\n" +
                       "• لا توجد أخطاء عند الإنشاء\n" +
                       "• جميع الخصائص مرئية ومتاحة\n" +
                       "• Collection Editor يعمل بمثالية\n" +
                       "• التنقل بين التابات يعمل\n" +
                       "• جميع خصائص RJButton متاحة\n\n" +
                       "🚀 RJTabControl الآن يدعم Designer بالكامل!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };

            designerTab.AddControl(designerTextBox);
        }

        private void CreateDemosTab(RJTabControl tabControl)
        {
            var demosTab = tabControl.AddTab("العروض التوضيحية", IconChar.Play);
            demosTab.BackColor = Color.FromArgb(255, 152, 0);
            demosTab.ForeColor = Color.White;
            demosTab.IconSize = 22;

            // إنشاء TabControl فرعي للعروض
            var demoTabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                TabSpacing = 2,
                TabPadding = 20,
                ContentBorderSize = 1,
                ContentBorderColor = Color.FromArgb(255, 152, 0),
                ContentBorderRadius = 5
            };

            // عرض الألوان
            var colorsDemo = demoTabControl.AddTab("الألوان", IconChar.Palette);
            colorsDemo.BackColor = Color.FromArgb(244, 67, 54);
            colorsDemo.ForeColor = Color.White;
            colorsDemo.AddControl(new Label 
            { 
                Text = "🎨 عرض الألوان المختلفة", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White
            });

            // عرض الأيقونات
            var iconsDemo = demoTabControl.AddTab("الأيقونات", IconChar.Star);
            iconsDemo.BackColor = Color.FromArgb(63, 81, 181);
            iconsDemo.ForeColor = Color.White;
            iconsDemo.IconSize = 24;
            iconsDemo.AddControl(new Label 
            { 
                Text = "⭐ عرض الأيقونات المختلفة", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White
            });

            // عرض الأنماط
            var stylesDemo = demoTabControl.AddTab("الأنماط", IconChar.Magic);
            stylesDemo.BackColor = Color.FromArgb(0, 150, 136);
            stylesDemo.ForeColor = Color.White;
            stylesDemo.Style = ControlStyle.Glass;
            stylesDemo.BorderRadius = 15;
            stylesDemo.AddControl(new Label 
            { 
                Text = "✨ عرض الأنماط المختلفة", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White
            });

            demosTab.AddControl(demoTabControl);
        }

        private void CreateTechnicalInfoTab(RJTabControl tabControl)
        {
            var techTab = tabControl.AddTab("معلومات تقنية", IconChar.Code);
            techTab.BackColor = Color.FromArgb(121, 85, 72);
            techTab.ForeColor = Color.White;
            techTab.IconSize = 20;

            var techTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "💻 معلومات تقنية عن RJTabControl:\n\n" +
                       "🏗️ البنية:\n" +
                       "• يرث من Panel\n" +
                       "• يحتوي على RJPanel للتابات\n" +
                       "• يحتوي على RJPanel للمحتوى\n" +
                       "• كل تاب يرث من RJButton\n\n" +
                       "🎨 الخصائص المتاحة:\n" +
                       "• TabHeight - ارتفاع التابات\n" +
                       "• TabSpacing - المسافة بين التابات\n" +
                       "• TabPadding - الحشو الداخلي للتابات\n" +
                       "• ContentBorderSize/Color/Radius - حدود المحتوى\n" +
                       "• TabsPanelBorderSize/Color/Radius - حدود منطقة التابات\n" +
                       "• SelectedTab/SelectedIndex - التحكم في التاب النشط\n" +
                       "• Tabs Collection - مجموعة التابات للـ Designer\n\n" +
                       "🔧 الطرق المتاحة:\n" +
                       "• AddTab(string) - إضافة تاب بسيط\n" +
                       "• AddTab(string, IconChar) - إضافة تاب مع أيقونة\n" +
                       "• AddTab(RJTabPage) - إضافة تاب مخصص\n" +
                       "• RemoveTab(RJTabPage) - إزالة تاب\n" +
                       "• ActivateTab(RJTabPage) - تفعيل تاب\n" +
                       "• ClearTabs() - مسح جميع التابات\n\n" +
                       "📡 الأحداث المتاحة:\n" +
                       "• TabChanged - عند تغيير التاب النشط\n" +
                       "• TabAdded - عند إضافة تاب جديد\n" +
                       "• TabRemoved - عند إزالة تاب\n" +
                       "• TabClosing - قبل إغلاق تاب (قابل للإلغاء)\n" +
                       "• TabClosed - بعد إغلاق تاب\n\n" +
                       "🛡️ الحماية والأمان:\n" +
                       "• فحص null لجميع المعاملات\n" +
                       "• قيم افتراضية آمنة\n" +
                       "• معالجة الأخطاء الشاملة\n" +
                       "• دعم DesignMode\n\n" +
                       "⚡ الأداء:\n" +
                       "• رسم محسن للتابات\n" +
                       "• إدارة ذاكرة فعالة\n" +
                       "• تحديث سريع للواجهة\n" +
                       "• دعم عدد كبير من التابات\n\n" +
                       "🎯 الاستخدام الموصى به:\n" +
                       "• واجهات المستخدم المتقدمة\n" +
                       "• تطبيقات إدارة البيانات\n" +
                       "• أنظمة التحكم والمراقبة\n" +
                       "• برامج التصميم والإبداع",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(121, 85, 72),
                BorderRadius = 8,
                Font = new Font("Consolas", 9),
                TextAlign = HorizontalAlignment.Left
            };

            techTab.AddControl(techTextBox);
        }

        private RJButton CreateTestButton(string text, IconChar icon, Color backColor, Point location)
        {
            return new RJButton
            {
                Text = text,
                IconChar = icon,
                Location = location,
                Size = new Size(220, 60),
                BackColor = backColor,
                ForeColor = Color.White,
                BorderRadius = 12,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                IconSize = 20,
                TextAlign = ContentAlignment.MiddleRight,
                TextImageRelation = TextImageRelation.ImageBeforeText,
                ImageAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(10, 0, 10, 0)
            };
        }

        /// <summary>
        /// تشغيل النموذج الرئيسي
        /// </summary>
        public static void RunMain()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                Application.Run(new MainTestForm());
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل النموذج الرئيسي:\n\n{ex.Message}", 
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل النموذج كـ Dialog
        /// </summary>
        public static void ShowDialog()
        {
            try
            {
                var form = new MainTestForm();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في عرض النموذج:\n\n{ex.Message}", 
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
