﻿using Microsoft.Win32;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SmartCreator.Forms
{
    public partial class UC_PreviewTemplateCards : UserControl
    {
        public string TemplateCards_name = "";
        CardsTemplate card;
        decimal pnl_x;
        decimal pnl_y;
        //string SP_code1 = "";

        UmProfile profile;
        SourceCardsTemplate sorcCard;

        public UC_PreviewTemplateCards()
        {
            InitializeComponent();
            Set_Font();
            setControlToPict();
            Get_TemplateCardsItemsGraphics(TemplateCards_name);
            SetValuToCardToGraphics();
        }
        string templateID; string _Name; string price; string transferLimit; string uptimeLimit; string Validity; string numberPrint; string SP_id;

        public UC_PreviewTemplateCards(string templateID, string profileName, string _SP_code, bool view_From_profile_HS_local = false)
        {
            InitializeComponent();
            //if (view_From_profile_HS_local)
            //   {
            //    Hotspot_Profile_hotspot hotspot = SqlDataAccess.Get_Profile_Hotspot_By_Name(profileName);
            //    profile = new UserManager_Profile_UserManager { Name = hotspot.Name, Price = hotspot.Price_display, transferLimit = hotspot.transferLimit, uptimeLimit = hotspot.uptimeLimit, Validity = hotspot.Validity };
            //}
            //else
            profile = Global_Variable.UM_Profile.Find(x => x.Name == profileName);

            //SP_code = _SP_code;
            if (profile == null)
                return;

            //templateID = _templateID;
            _Name = profile.Name;
            price = profile.Price.ToString();
            transferLimit = profile.TransferLimit.ToString();
            uptimeLimit = profile.UptimeLimit.ToString();
            Validity = profile.Validity.ToString();
            //numberPrint = _numberBrint;
            SP_id = _SP_code;

            Set_Font();
            setControlToPict();
            Get_TemplateCardsItemsGraphics(templateID);
            SetValuToCardToGraphics();

            show_Profile_info(templateID,_Name,price,transferLimit,uptimeLimit,Validity,numberPrint,SP_id);

        }
        public UC_PreviewTemplateCards(string _templateID, string _name, string _price, string _transferLimit, string _uptimeLimit, string _Validity, string _numberBrint, string _sp)
        {
            InitializeComponent();
            templateID = _templateID;
            _name = _Name;
            price = _price;
            transferLimit = _transferLimit;
            uptimeLimit = _uptimeLimit;
            Validity = _Validity;
            numberPrint = _numberBrint;
            SP_id = _sp;

            Set_Font();

            setControlToPict();
            Get_TemplateCardsItemsGraphics(templateID);
            SetValuToCardToGraphics();

            show_Profile_info(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);

        }


        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            //CBox_Profile.SelectedIndex = -1;
            //selectTemplateFromDrowpDown = true;
            //if (!firstLoad)
            //{
            disableAll_Lable();
            Get_TemplateCardsItemsGraphics(TemplateCards_name);
            SetValuToCardToGraphics();
            //}
            ////firstLoad = false;
            //selectTemplateFromDrowpDown = false;
        }
        private void disableAll_Lable()
        {
            //foreach (Control lb in pictureBox1.Controls)
            //{
            //    lb.Visible = false;
            //}
            //foreach (Control lb in panel2.Controls)
            //{
            //    if (lb.GetType() == typeof(System.Windows.Forms.CheckBox))
            //    {
            //        RJCheckBox ch = new RJCheckBox();
            //        ch = (RJCheckBox)lb;
            //        ch.Checked = false;
            //    }
            //}
        }

        private void Get_TemplateCardsItemsGraphics(string templateName)
        {

            SourceCardsTemplate sorceTemplate = SqlDataAccess.Get_template_cards_By_id(templateName);
            if (sorceTemplate == null)
                return;
            if (sorceTemplate.type == "design")
            {
                card = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values);
            }

            //try
            //{
            //    card = SqlDataAccess.Get_template_cards_By_Name(CBox_TemplateCards.SelectedValue.ToString());
            //}
            //catch (Exception ex) { MessageBox.Show("Get_TemplateCardsItemsGraphics   " + ex.Message); }

        }
        void SetValuToCardToGraphics()
        {
            if (card == null) return;
            pictureBox1.Image = null;

            //BackgroundImgCard_Chbox.Checked = card.setingCard.enable_background;
            //txt_Pathfile.Text = card.setingCard.path_saved_file.ToString();
            //txt_PathImage.Text = card.setingCard.path_background.ToString();
            if (card.setingCard.enable_background) set_BackroundFromPath(true);

            //comboBox_quilty_image.SelectedIndex = card.setingCard.quilty_image;

            //CBox_Curncey.Text = card.setingCard.currency;
            //TextCard_W.Value = card.setingCard.card_width;
            //TextCard_Y.Value = card.setingCard.card_height;
            //txt_Space_X.Value = card.setingCard.space_horizontal_margin;
            //txt_Space_Y.Value = card.setingCard.Space_vertical_margin;
            //checkBoxBorderCard.Checked = card.setingCard.card_border_enable;
            //txt_SizeBorder.Value = (decimal)card.setingCard.card_border_Size;
            //btn_BorderColor.BackColor = System.Drawing.ColorTranslator.FromHtml(card.setingCard.card_border_Color);

            //check_Number_Pages.Checked = card.setingCard.Number_Pages;
            //txt_Number_Page_Size.Value = card.setingCard.Number_Pages_Size;
            //txt_Number_Page_X.Value = card.setingCard.Number_Pages_X;
            //txt_Number_Page_Y.Value = card.setingCard.Number_Pages_Y;

            //checkNoteOnPage.Checked = card.setingCard.Note_On_Pages;
            //txt_Note_Page_Size.Value = card.setingCard.Note_On_Pages_Size;
            //txt_Note_Page_X.Value = card.setingCard.Note_On_Pages_X;
            //txt_Note_Page_Y.Value = card.setingCard.Note_On_Pages_Y;
            //txt_Note_onPage.Text = card.setingCard.Note_On_Pages_text;
            //CBox_NoteType_onPage.SelectedIndex = card.setingCard.NoteType_onPage;
            pictureBox1.Refresh();
            this.Refresh();
            pnl_x = pictureBox1.Width / card.setingCard.card_width;
            pnl_y = pictureBox1.Height / card.setingCard.card_height;

            pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(card.setingCard.card_width)) * pnl_x);
            //pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(card.setingCard.card_width)) * (decimal)4.5);
            //pictureBox1.Width = (int)pnl_x;
            pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(card.setingCard.card_height)) * pnl_y);
            //pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(card.setingCard.card_height)) * (decimal)4.5);
            //pictureBox1.Height = (int)pnl_y;

            //try { CBox_Profile.Text = card.setingCard.proile_link; } catch { }
            ////try { CBox_Profile.Text = card.setingCard.proile_link; } catch { }
            //try { CBox_Profile_HS.Text = card.setingCard.proile_HS_link; } catch { }
            ////try { CBox_Profile.SelectedItem = card.setingCard.proile_link; } catch { }
            ////try { CBox_Profile_HS.SelectedItem = card.setingCard.proile_HS_link; } catch { }

            //txt_LogoImage.Text = card.cardsItems.logo.Path;
            set_value_For_item();
        }
        void set_value_For_item()
        {
            //============login=================
            Set_Proprties_For_Item(Login_Lbl, card.cardsItems.login);
            ChangPositonControl_In_Imag(Login_Lbl, card.cardsItems.login);
            Change_Font_Size_Bold_Color_Control_In_Imag(Login_Lbl, card.cardsItems.login);
            //Login_Chbox.Checked = card.cardsItems.login.Enable;

            //checkShowAddresItem.Checked = card.cardsItems.login.title_show;
            //txt_AddresItem.Text = card.cardsItems.login.title_text;


            //============password=================
            Set_Proprties_For_Item(Password_Lbl, card.cardsItems.Password);
            ChangPositonControl_In_Imag(Password_Lbl, card.cardsItems.Password);
            Change_Font_Size_Bold_Color_Control_In_Imag(Password_Lbl, card.cardsItems.Password);
            //Password_Chbox.Checked = card.cardsItems.Password.Enable;

            //checkShowAddresItem.Checked = card.cardsItems.Password.title_show;
            //txt_AddresItem.Text = card.cardsItems.Password.title_text;

            //============Price=================
            Set_Proprties_For_Item(Lbl_Price, card.cardsItems.Price);
            ChangPositonControl_In_Imag(Lbl_Price, card.cardsItems.Price);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Price, card.cardsItems.Price);
            //Price_Chbox.Checked = card.cardsItems.Price.Enable;
            //============Lbl_Time=================
            Set_Proprties_For_Item(Lbl_Time, card.cardsItems.Time);
            ChangPositonControl_In_Imag(Lbl_Time, card.cardsItems.Time);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Time, card.cardsItems.Time);
            //Time_Chbox.Checked = card.cardsItems.Time.Enable;
            //CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;

            //============Lbl_SizeTransfer=================
            Set_Proprties_For_Item(Lbl_SizeTransfer, card.cardsItems.Size);
            ChangPositonControl_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            //Size_Chbox.Checked = card.cardsItems.Size.Enable;
            ////CBox_UniteTransfer_format.Visible = true;
            //CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;

            //============Lbl_validity=================
            Set_Proprties_For_Item(Lbl_validity, card.cardsItems.Validity);
            ChangPositonControl_In_Imag(Lbl_validity, card.cardsItems.Validity);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_validity, card.cardsItems.Validity);
            //validity_Chbox.Checked = card.cardsItems.Validity.Enable;
            //CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;

            //============lbl_Squ_Nuber=================
            Set_Proprties_For_Item(lbl_Squ_Nuber, card.cardsItems.SN);
            ChangPositonControl_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            Change_Font_Size_Bold_Color_Control_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            //Squnce_Number_Chbox.Checked = card.cardsItems.SN.Enable;
            //============SP=================
            Set_Proprties_For_Item(Lbl_SP, card.cardsItems.SP);
            ChangPositonControl_In_Imag(Lbl_SP, card.cardsItems.SP);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SP, card.cardsItems.SP);
            //SP_Chbox.Checked = card.cardsItems.SP.Enable;
            //try
            //{
            //    CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);

            //}
            //catch { }

            //============Lbl_Number_Print=================
            Set_Proprties_For_Item(Lbl_Number_Print, card.cardsItems.Number_Print);
            ChangPositonControl_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            //Number_Print_Chbox.Checked = card.cardsItems.Number_Print.Enable;
            //============Lbl_OtherText1=================
            Set_Proprties_For_Item(Lbl_OtherText1, card.cardsItems.Other_Text1);
            ChangPositonControl_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            //OtherText1_Chbox.Checked = card.cardsItems.Other_Text1.Enable;
            //txt_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            Lbl_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            //Lbl_OtherText1.Text=card.cardsItems.Other_Text1.title_text;
            //checkShowAddresItem.Checked = false;
            //txt_AddresItem.Text = "";

            ////============Lbl_OtherText2=================
            //Set_Proprties_For_Item(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //ChangPositonControl_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //OtherText1_Chbox.Checked = card.cardsItems.Other_Text2.Enable;
            //============Lbl_Date_Print=================
            Set_Proprties_For_Item(Lbl_Date_Print, card.cardsItems.Date_Print);
            ChangPositonControl_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            //Date_Print_Chbox.Checked = card.cardsItems.Date_Print.Enable;
            ////card.cardsItems.SP.Show_ByNumber_OR_Name = !Convert.ToBoolean(CBox_SellingPoint.SelectedIndex);
            //CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;
            //============QR=================
            Set_Proprties_For_Item_img(pictureBox_QR, card.cardsItems.QR);
            ChangPositonControl_IMAGE_In_Imag(pictureBox_QR, card.cardsItems.QR);
            //QR_Chbox.Checked = card.cardsItems.QR.Enable;
            //============logo=================
            Set_Proprties_For_Item_img(pictureBox_logo, card.cardsItems.logo);
            ChangPositonControl_IMAGE_In_Imag(pictureBox_logo, card.cardsItems.logo);
            //Logo_Chbox.Checked = card.cardsItems.logo.Enable;
            try
            {
                //Bitmap img = new Bitmap(card.cardsItems.logo.Path);
                string pathfile = utils.Get_CardsBack_Directory() + "\\" + Path.GetFileName(card.cardsItems.logo.Path);
                Bitmap img = new Bitmap(pathfile);
                pictureBox_logo.Image = System.Drawing.Image.FromFile(pathfile);

            }
            catch { /*MessageBox.Show("خطأ في صوره الشعار"); */}


        }
        void Set_Proprties_For_Item(System.Windows.Forms.Label lbl, PropertyItemText loc)
        {
            try
            {
                lbl.Visible = loc.Enable;

                //txt_Element_W.Value = loc.x;
                //txt_Element_Y.Value = loc.y;

                Color color = ColorTranslator.FromHtml(loc.Color);
                //btn_ElementColor.BackColor = color;
                lbl.ForeColor = color;

                //CB_ElementSize.Text = loc.font_size.ToString();
                //txt_font.Text = loc.Font;
                //CB_Fonts.Text = loc.Font;
                //CB_Fonts.SelectedItem = loc.Font;

                //checkShowAddresItem.Checked = loc.title_show;
                //txt_AddresItem.Text = loc.title_text;
                //txt_AddresItem.Text = loc.title_text;
                //checkShowAddresItem.Checked = loc.title_show;

                //checkShowUnit_Item.Checked = loc.unit_show;
                //checkBoxISBlod.Checked = loc.Blod;
                //checkBoxIsItalic.Checked = loc.italic;

                //CBox_UniteTime_format.Visible = false;
                //CBox_UniteTransfer_format.Visible = false;
                //CBox_UniteValidaty_format.Visible = false;
                //checkShowUnit_Item


            }
            catch { }

            //if(lbl.Name == "Lbl_Date_Print")
            //{
            //    CBox_Date_print.SelectedItem = loc.address_text;
            //}
            ////ChangPositonControl_In_Imag(lbl);
        }
        void Set_Proprties_For_Item_img(Control item, PropertyItemImage property)
        {
            try
            {
                item.Visible = property.Enable;

                //txt_Element_W.Value = Convert.ToDecimal(property.x);
                //txt_Element_Y.Value = Convert.ToDecimal(property.y);

                //txt_dimension_w.Value = Convert.ToDecimal(property.item_dimension_w);
                //txt_dimension_H.Value = Convert.ToDecimal(property.item_dimension_y);


                //Color color = ColorTranslator.FromHtml(property.Color);
                //btn_Element_panelColor.BackColor = color;

                item.Width = Convert.ToInt16((Convert.ToDecimal(property.item_dimension_w)) * (decimal)6.0);
                item.Height = Convert.ToInt16((Convert.ToDecimal(property.item_dimension_w)) * (decimal)6.0);

                item.BringToFront();

                //if (item.GetType() == typeof(Panel))
                //{
                //    item.BackColor = color;
                //}
                //if (item.GetType() == typeof(PictureBox))
                //{
                //    item.BackColor = color;
                //}


                //CB_ElementSize.Text = loc.Size_text.ToString();
                //txt_font.Text = loc.Font;
                //CB_Fonts.SelectedItem = loc.Font;

                //checkShowAddresItem.Checked = loc.address_show;
                //txt_AddresItem.Text = loc.address_text;


                //checkShowUnit_Item.Checked = loc.unit_show;
                //checkBoxISBlod.Checked = loc.Blod;
                //checkBoxIsItalic.Checked = loc.italic;
            }
            catch (Exception e) { MessageBox.Show("Set_Proprties_For_Item_img erroooor"); }
            //ChangPositonControl_In_Imag(lbl);
        }
        void ChangPositonControl_In_Imag(Control Control_lable, PropertyItemText loc)
        {
            try
            {

                Point point = new Point();
                point = Control_lable.Location;
                //int X = (int)(float.Parse(loc.x.ToString()) * (float)4.5);
                int X = (int)(float.Parse(loc.x.ToString()) * (float)pnl_x);
                int Y = (int)(float.Parse(loc.y.ToString()) * (float)pnl_y);
                //int Y = (int)(float.Parse(loc.y.ToString()) * (float)4.5);

                Control_lable.Location = new Point(X, Y);

                //loc_itemtemp.x = X/5;
                //loc_itemtemp.y = Y/5;
                //Loc_item_Save_in_struct();

            }
            catch (Exception ex) { MessageBox.Show("ChangPositonControl_In_Imag  " + loc); }
            //loc_itemtemp.x = Publi_lbl_Use.Location.X ;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y ;
            //loc_itemtemp.x = Publi_lbl_Use.Location.X / 5;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y / 5;

        }
        void Change_Font_Size_Bold_Color_Control_In_Imag(System.Windows.Forms.Label lbl, PropertyItemText loc)
        {
            //loc.Font = txt_font.Text;
            //loc.Size_text = Convert.ToInt32(CB_ElementSize.Text);
            //loc.Blod = checkBoxISBlod.Checked;
            //loc.italic = checkBoxISBlod.Checked;
            int s_font = (loc.font_size + 3) + ((int)pnl_x / 2);
            if (loc.Blod)
            {
                if (loc.italic)
                    lbl.Font = new System.Drawing.Font(loc.Font, s_font, FontStyle.Italic | FontStyle.Bold);
                else lbl.Font = new System.Drawing.Font(loc.Font, s_font, FontStyle.Bold);
            }
            else
            {
                if (loc.italic) lbl.Font = new System.Drawing.Font(loc.Font, s_font, FontStyle.Italic | FontStyle.Regular);
                else lbl.Font = new System.Drawing.Font(loc.Font, s_font, FontStyle.Regular);
            }
            Color color = ColorTranslator.FromHtml(loc.Color);
            //String Color = System.Drawing.ColorTranslator.ToHtml(color);            
            lbl.ForeColor = color;
            //btn_ElementColor.BackColor = color;

            //#############
            if (loc.title_show)
            {

            }
        }
        private void set_BackroundFromPath(bool fromDB)
        {

            try
            {
                if (card.setingCard.path_background.ToString() == "")
                    return;
                //txt_PathImage.Text = card.setingCard.path_background;
                //MessageBox.Show(card.setingCard.path_background.ToString());
                //string sourcePath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\CardsBack";
                string sourcePath = utils.Get_CardsBack_Directory() ;
                string SourcePath_File = System.IO.Path.Combine(sourcePath, card.setingCard.path_background.ToString());
                //txt_PathImage.Text = "tempCards\\cardsBack" + card.setingCard.path_background.ToString();
                FileInfo file = new FileInfo(SourcePath_File);
                double sizeInBytes = file.Length;
                try
                {
                    Bitmap img = new Bitmap(SourcePath_File);
                    //txt_PathImage.Text = openFileDialog1.FileName;
                    //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(txt_PathImage.Text);
                    pictureBox1.Image = System.Drawing.Image.FromFile(SourcePath_File);
                    //BackgroundImgCard_Chbox.Checked = true;
                }
                catch
                {
                    pictureBox1.Image = null;
                    //BackgroundImgCard_Chbox.Checked = false;
                    MessageBox.Show("خطأ في ملف الخلفية");
                }
                //BackgroundImgCard_Chbox.Checked = true;
            }
            catch (Exception ex)
            {
                pictureBox1.Image = null;
                //BackgroundImgCard_Chbox.Checked = false;
                //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "خطأ في مسار الصوره");
                //MessageBox.Show("خطأ في مسار الصوره \n" + ex.Message.ToString());
            }

        }
        void ChangPositonControl_IMAGE_In_Imag(Control Control_lable, PropertyItemImage loc)
        {
            try
            {

                Point point = new Point();
                point = Control_lable.Location;
                int X = (int)(float.Parse(loc.x.ToString()) * (float)6.0);
                int Y = (int)(float.Parse(loc.y.ToString()) * (float)6.0);

                Control_lable.Location = new Point(X, Y);

                //loc_itemtemp.x = X/5;
                //loc_itemtemp.y = Y/5;
                //Loc_item_Save_in_struct();

            }
            catch (Exception ex) { /*MessageBox.Show("ChangPositonControl_In_Imag  " + loc);*/ }
            //loc_itemtemp.x = Publi_lbl_Use.Location.X ;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y ;
            //loc_itemtemp.x = Publi_lbl_Use.Location.X / 5;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y / 5;

        }

        void setControlToPict()
        {
            pictureBox1.Controls.Add(Login_Lbl);
            pictureBox1.Controls.Add(Password_Lbl);
            pictureBox1.Controls.Add(lbl_Squ_Nuber);
            pictureBox1.Controls.Add(Lbl_SizeTransfer);
            pictureBox1.Controls.Add(Lbl_Time);
            pictureBox1.Controls.Add(Lbl_validity);
            pictureBox1.Controls.Add(Lbl_Price);
            pictureBox1.Controls.Add(Lbl_OtherText1);
            pictureBox1.Controls.Add(Lbl_Date_Print);
            pictureBox1.Controls.Add(Lbl_Number_Print);
            pictureBox1.Controls.Add(pictureBox_QR);
            pictureBox1.Controls.Add(pictureBox_logo);
            pictureBox1.Controls.Add(Lbl_SP);

            //lbl_Abaad.Visible = false;
            //groupBox_Abaad.Visible = false;
            //groupBox_Abaad.Location = new Point(395, 163);
        }

        private void UC_PreviewTemplateCards_Resize(object sender, EventArgs e)
        {
            Refresh_desgin();
        }
        void Refresh_desgin()
        {
            if (card == null) return;
            pnl_x = pictureBox1.Width / card.setingCard.card_width;
            pnl_y = pictureBox1.Height / card.setingCard.card_height;

            pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(card.setingCard.card_width)) * pnl_x);
            //pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(card.setingCard.card_width)) * (decimal)4.5);
            //pictureBox1.Width = (int)pnl_x;
            pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(card.setingCard.card_height)) * pnl_y);
            set_value_For_item();
        }


        public void show_Profile_info(string templateID, string _Name, string price, string transferLimit, string uptimeLimit, string Validity, string numberBrint, string SP_id)
        {
            if (card==null) return;
            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            //string profileName = profile.Name;
            //string price = profile.Price.ToString();
            //string Validity = profile.Validity.ToString();
            //string time = profile.uptimeLimit.ToString();  // or  time="5h";
            ////string time = "720:00:00";  // or  time="5h";
            ////time=utils.GetString_Time_in_Hour(time).ToString();
            //string sizeTransfer = profile.transferLimit.ToString();
            //string sp = "";
            //string numberPrint = "";
            //string DatePrint = "";
            //string Note_On_Pages_text = "";


            string profileName = _Name;
            //string price = price.ToString();
            if (Validity == "")
                Validity = "0";
            //string Validity = Validity.ToString();
            string time = uptimeLimit.ToString();  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            if (transferLimit == "")
                transferLimit = "0";

            if (transferLimit != "")
                transferLimit = "0";
            string sizeTransfer = transferLimit.ToString();

            if (price =="")
                price = "0";
            //string sp = sp;
            //string numberPrint = "";
            string DatePrint = "";
            string Note_On_Pages_text = "";



            if (card.cardsItems.Price.Enable)
            {
                if (card.cardsItems.Price.unit_show)
                {
                    price = price + " " + card.setingCard.currency.ToString();
                }
                if (card.cardsItems.Price.title_show)
                {
                    //price = price + " " + card.setingCard.currency.ToString();
                    price = card.cardsItems.Price.title_text + " " + price;
                }
                Lbl_Price.Text = price;

            }
            if (card.cardsItems.Validity.Enable)
            {
                if (card.cardsItems.Validity.unit_show)
                {
                    if (Validity != "" && Validity != "مفتوح" && Validity != "0")
                    {
                        Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                    }
                }
                if (card.cardsItems.Validity.title_show)
                {
                    Validity = card.cardsItems.Validity.title_text + " " + Validity;

                }
                Lbl_validity.Text = Validity;

            }
            if (card.cardsItems.Time.Enable)
            {
                if (time != "" && time != "مفتوح" && time != "0" && time != "00:00:00")
                {
                    time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                }
                if (card.cardsItems.Time.title_show)
                {
                    time = card.cardsItems.Time.title_text + " " + time;
                }
                Lbl_Time.Text = time;

            }
            if (card.cardsItems.Size.Enable)
            {
                if (sizeTransfer != "" && sizeTransfer.ToLower() != "0b" && sizeTransfer != "0")
                {
                    sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                    if (card.cardsItems.Size.title_show)
                    {
                        sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                    }

                }
                Lbl_SizeTransfer.Text = sizeTransfer;

            }
            if (card.cardsItems.SP.Enable)
            {
                string SellingP = "";
                if (SP_id != "" && SP_id != "0" && SP_id != "-1")
                {
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                        SellingPoint ssp = smart_DataAccess.Get_SellingPoint_Code(SP_id);
                        if (ssp != null)
                        {
                            if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                                SellingP = ssp.Code;
                            else
                                SellingP = ssp.UserName;
                            if (card.cardsItems.SP.title_show)
                            {
                                SellingP = card.cardsItems.SP.title_text + " " + SellingP;
                            }
                        }
                    }
                    Lbl_SP.Text = SellingP;

                }

            }
            if (card.cardsItems.Number_Print.Enable)
            {
                if (numberPrint != "")
                {
                    if (card.cardsItems.Number_Print.title_show)
                    {
                        numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                    }
                }
                Lbl_Number_Print.Text = numberPrint;

            }
            if (card.cardsItems.Date_Print.Enable)
            {
                string format = card.cardsItems.Date_Print.format;
                DateTime now = DateTime.Now;
                DatePrint = now.ToString("dd-MM-yyyy");
                try
                {
                    DatePrint = (now.ToString(format));
                }
                catch (Exception ex) { MessageBox.Show("صيغة تاريخ الطباعة التي ادخلته غير صحيح\n" + format + "\n" + ex.Message); }
                if (card.cardsItems.Date_Print.title_show)
                {
                    DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;

                }
                Lbl_Date_Print.Text = DatePrint;
            }
            if (card.cardsItems.Number_Print.Enable)
            {
                try
                {
                    int batchNumber = SqlDataAccess.Get_lastID_Batch_cards() + 1;
                    Lbl_Number_Print.Text = (batchNumber).ToString();
                    if (card.cardsItems.Number_Print.title_show)
                    {
                        Lbl_Number_Print.Text = card.cardsItems.Number_Print.title_text + " " + batchNumber;
                    }
                }
                catch { }
            }
            if (card.cardsItems.SP.Enable)
            {
                if (SP_id != "" && SP_id != "0" && SP_id != "-1")
                {
                    try
                    {
                        string _sp = "";
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                        
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(SP_id);
                        if (Show_sp != null)
                        {
                            if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            {
                                Lbl_SP.Text = (Show_sp.Code).ToString();
                                _sp = (Show_sp.Code).ToString();
                            }
                            else
                            {
                                Lbl_SP.Text = (Show_sp.UserName).ToString();
                                _sp = (Show_sp.UserName).ToString();
                            }
                        }
                        if (card.cardsItems.SP.title_show)
                        {
                            Lbl_SP.Text = card.cardsItems.SP.title_text + " " + _sp;
                        }
                    }
                    catch { }
                }
            }

        }
        private void Set_Font()
        {
            foreach (var contrl in this.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(System.Windows.Forms.Label))
                    {
                        System.Windows.Forms.Label textbox = (System.Windows.Forms.Label)contrl;
                        //textbox.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 8f, false, GraphicsUnit.Point, 0);
                        textbox.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8 * utils.ScaleFactor, FontStyle.Regular);
                    }
                }
                catch { }
            }
            Login_Lbl.Font = Password_Lbl.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10*utils.ScaleFactor, FontStyle.Regular);

            //Login_Lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);
            //Password_Lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);

        }

        private void UC_PreviewTemplateCards_Load(object sender, EventArgs e)
        {
            //    //Refresh_desgin();
            //foreach (var contrl in this.Controls)
            //{
            //    try
            //    {
            //        if (contrl.GetType() == typeof(System.Windows.Forms.Label))
            //        {
            //            System.Windows.Forms.Label textbox = (System.Windows.Forms.Label)contrl;
            //            textbox.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 8f, false, GraphicsUnit.Point, 0);
            //        }
            //    }
            //    catch { }
            //}
            ////Login_Lbl.Font =  CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);
            ////Password_Lbl.Font =  CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);



            //setControlToPict();
            //Get_TemplateCardsItemsGraphics(TemplateCards_name);
            //SetValuToCardToGraphics();

            //show_Profile_info(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);


        }
    }
}
