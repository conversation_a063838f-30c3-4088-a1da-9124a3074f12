using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نسخة آمنة من RJTabControl - بناء تدريجي لإيجاد المشكلة
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class SafeRJTabControl : Panel
    {
        #region Fields - الحقول الأساسية فقط
        internal List<RJTabPage> tabs; // internal للوصول من SimpleTabCollection
        private RJPanel tabsPanel;
        private RJPanel contentPanel;
        private RJTabPage activeTab;
        private int tabHeight = 35;
        private int tabSpacing = 2;
        private int tabPadding = 15;
        private SimpleTabCollection _tabsCollection;
        #endregion

        #region Constructor - مبسط جداً
        public SafeRJTabControl()
        {
            // تهيئة فورية
            tabs = new List<RJTabPage>();
            _tabsCollection = new SimpleTabCollection(this);

            // إنشاء المكونات
            InitializeComponent();

            // تاب افتراضي للـ Designer
            if (DesignMode)
            {
                try
                {
                    AddDefaultTab();
                }
                catch
                {
                    // تجاهل الأخطاء في DesignMode
                }
            }
        }
        #endregion

        #region InitializeComponent - مبسط
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إنشاء panel التابات
            this.tabsPanel = new RJPanel();
            this.tabsPanel.Dock = DockStyle.Top;
            this.tabsPanel.Height = tabHeight;
            this.tabsPanel.BackColor = Color.FromArgb(55, 55, 58);
            this.tabsPanel.BorderSize = 0;

            // إنشاء panel المحتوى
            this.contentPanel = new RJPanel();
            this.contentPanel.Dock = DockStyle.Fill;
            this.contentPanel.BackColor = Color.FromArgb(37, 37, 38);
            this.contentPanel.BorderSize = 1;
            this.contentPanel.BorderColor = Color.FromArgb(0, 122, 204);

            // إعدادات الكنترول الرئيسي
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Size = new Size(400, 300);

            // إضافة المكونات
            this.Controls.Add(this.contentPanel);
            this.Controls.Add(this.tabsPanel);

            this.ResumeLayout(false);
        }
        #endregion

        #region Properties - خصائص أساسية آمنة فقط
        /// <summary>
        /// ارتفاع التابات
        /// </summary>
        [Category("Safe Tab Control")]
        [Description("Height of tabs")]
        [DefaultValue(35)]
        public int TabHeight
        {
            get { return tabHeight; }
            set
            {
                tabHeight = value;
                if (tabsPanel != null)
                {
                    tabsPanel.Height = tabHeight;
                    ArrangeTabs();
                }
            }
        }

        /// <summary>
        /// المسافة بين التابات
        /// </summary>
        [Category("Safe Tab Control")]
        [Description("Spacing between tabs")]
        [DefaultValue(2)]
        public int TabSpacing
        {
            get { return tabSpacing; }
            set
            {
                tabSpacing = value;
                ArrangeTabs();
            }
        }

        /// <summary>
        /// الحشو الداخلي للتابات
        /// </summary>
        [Category("Safe Tab Control")]
        [Description("Padding inside tabs")]
        [DefaultValue(15)]
        public int TabPadding
        {
            get { return tabPadding; }
            set
            {
                tabPadding = value;
                ArrangeTabs();
            }
        }

        /// <summary>
        /// عدد التابات - للقراءة فقط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int TabCount
        {
            get { return tabs?.Count ?? 0; }
        }

        /// <summary>
        /// فهرس التاب النشط
        /// </summary>
        [Category("Safe Tab Control")]
        [Description("Index of selected tab")]
        [DefaultValue(-1)]
        public int SelectedIndex
        {
            get 
            { 
                if (tabs == null || activeTab == null) return -1;
                return tabs.IndexOf(activeTab);
            }
            set
            {
                if (tabs == null || value < 0 || value >= tabs.Count) return;
                ActivateTab(tabs[value]);
            }
        }

        /// <summary>
        /// مجموعة التابات - نسخة مبسطة
        /// </summary>
        [Category("Safe Tab Control")]
        [Description("Collection of tabs - simplified version")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public SimpleTabCollection Tabs
        {
            get { return _tabsCollection; }
        }
        #endregion

        #region Methods - طرق أساسية آمنة
        /// <summary>
        /// إضافة تاب افتراضي للـ Designer
        /// </summary>
        private void AddDefaultTab()
        {
            if (tabsPanel == null || contentPanel == null) return;

            var defaultTab = new RJTabPage("TabPage1");
            defaultTab.BackColor = Color.FromArgb(0, 122, 204);
            defaultTab.ForeColor = Color.White;
            defaultTab.Height = tabHeight - 4;
            defaultTab.Click += Tab_Click;

            // إضافة للقائمة
            tabs.Add(defaultTab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(defaultTab);
            contentPanel.Controls.Add(defaultTab.ContentPanel);

            // ترتيب وتفعيل
            ArrangeTabs();
            ActivateTab(defaultTab);
        }

        /// <summary>
        /// إضافة تاب جديد
        /// </summary>
        public RJTabPage AddTab(string text)
        {
            if (string.IsNullOrEmpty(text) || tabsPanel == null || contentPanel == null)
                return null;

            var tab = new RJTabPage(text);
            tab.BackColor = Color.FromArgb(70, 70, 70);
            tab.ForeColor = Color.White;
            tab.Height = tabHeight - 4;
            tab.Click += Tab_Click;

            // إضافة للقائمة
            tabs.Add(tab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(tab);
            contentPanel.Controls.Add(tab.ContentPanel);

            // ترتيب وتفعيل
            ArrangeTabs();
            if (tabs.Count == 1)
                ActivateTab(tab);

            return tab;
        }

        /// <summary>
        /// تفعيل تاب
        /// </summary>
        private void ActivateTab(RJTabPage tab)
        {
            if (tab == null || tabs == null || !tabs.Contains(tab)) return;

            // إلغاء تفعيل التاب السابق
            if (activeTab != null)
            {
                activeTab.BackColor = Color.FromArgb(70, 70, 70);
                activeTab.ContentPanel.Visible = false;
            }

            // تفعيل التاب الجديد
            activeTab = tab;
            activeTab.BackColor = Color.FromArgb(0, 122, 204);
            activeTab.ContentPanel.Visible = true;
            activeTab.ContentPanel.BringToFront();
        }

        /// <summary>
        /// ترتيب التابات
        /// </summary>
        private void ArrangeTabs()
        {
            if (tabs == null || tabs.Count == 0) return;

            int x = tabSpacing;
            foreach (var tab in tabs)
            {
                if (tab == null) continue;

                // حساب عرض التاب
                int tabWidth = CalculateTabWidth(tab);

                // تحديد موقع التاب
                tab.Location = new Point(x, 2);
                tab.Size = new Size(tabWidth, tabHeight - 4);

                x += tabWidth + tabSpacing;
            }
        }

        /// <summary>
        /// حساب عرض التاب
        /// </summary>
        private int CalculateTabWidth(RJTabPage tab)
        {
            if (tab == null) return 100;

            using (Graphics g = this.CreateGraphics())
            {
                SizeF textSize = g.MeasureString(tab.Text, tab.Font);
                return (int)textSize.Width + (tabPadding * 2) + 10; // 10 للهامش
            }
        }

        /// <summary>
        /// معالج النقر على التاب
        /// </summary>
        private void Tab_Click(object sender, EventArgs e)
        {
            if (sender is RJTabPage tab)
            {
                ActivateTab(tab);
            }
        }
        #endregion

        #region Public Methods - طرق عامة
        /// <summary>
        /// مسح جميع التابات
        /// </summary>
        public void ClearTabs()
        {
            if (tabs == null) return;

            foreach (var tab in tabs)
            {
                if (tab != null)
                {
                    tab.Click -= Tab_Click;
                    tabsPanel?.Controls.Remove(tab);
                    contentPanel?.Controls.Remove(tab.ContentPanel);
                }
            }

            tabs.Clear();
            activeTab = null;
        }

        /// <summary>
        /// إزالة تاب
        /// </summary>
        public void RemoveTab(RJTabPage tab)
        {
            if (tab == null || tabs == null || !tabs.Contains(tab)) return;

            // إلغاء ربط الأحداث
            tab.Click -= Tab_Click;

            // إزالة من القائمة والواجهة
            tabs.Remove(tab);
            tabsPanel?.Controls.Remove(tab);
            contentPanel?.Controls.Remove(tab.ContentPanel);

            // تفعيل تاب آخر إذا كان هذا نشط
            if (activeTab == tab)
            {
                if (tabs.Count > 0)
                    ActivateTab(tabs[0]);
                else
                    activeTab = null;
            }

            // ترتيب التابات
            ArrangeTabs();
        }

        /// <summary>
        /// إضافة تاب داخلياً - للـ Collection
        /// </summary>
        internal void AddTabInternal(RJTabPage tab)
        {
            if (tab == null || tabsPanel == null || contentPanel == null) return;

            // إعداد التاب
            tab.Height = tabHeight - 4;
            tab.Click += Tab_Click;

            // إضافة للقائمة
            if (!tabs.Contains(tab))
                tabs.Add(tab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(tab);
            contentPanel.Controls.Add(tab.ContentPanel);

            // ترتيب وتفعيل
            ArrangeTabs();
            if (tabs.Count == 1)
                ActivateTab(tab);
        }
        #endregion
    }

    /// <summary>
    /// مجموعة تابات مبسطة للاختبار
    /// </summary>
    public class SimpleTabCollection
    {
        private SafeRJTabControl owner;

        public SimpleTabCollection(SafeRJTabControl owner)
        {
            this.owner = owner ?? throw new ArgumentNullException(nameof(owner));
        }

        /// <summary>
        /// عدد التابات
        /// </summary>
        public int Count
        {
            get { return owner?.tabs?.Count ?? 0; }
        }

        /// <summary>
        /// الوصول للتاب بالفهرس
        /// </summary>
        public RJTabPage this[int index]
        {
            get
            {
                if (owner?.tabs == null || index < 0 || index >= owner.tabs.Count)
                    return null;
                return owner.tabs[index];
            }
        }

        /// <summary>
        /// إضافة تاب
        /// </summary>
        public void Add(RJTabPage tab)
        {
            if (tab != null && owner != null)
            {
                owner.AddTabInternal(tab);
            }
        }

        /// <summary>
        /// إزالة تاب
        /// </summary>
        public void Remove(RJTabPage tab)
        {
            if (tab != null && owner != null)
            {
                owner.RemoveTab(tab);
            }
        }

        /// <summary>
        /// مسح جميع التابات
        /// </summary>
        public void Clear()
        {
            owner?.ClearTabs();
        }
    }
}
