﻿namespace SmartCreator.Forms.Acive_Host
{
    partial class Form_Acive_Host_Bar
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.panel_Tab_Container = new System.Windows.Forms.Panel();
            this.tableLayoutPanel_Top_Btn = new System.Windows.Forms.TableLayoutPanel();
            this.btn_Host = new SmartCreator.RJControls.RJButton();
            this.btn_Acive = new SmartCreator.RJControls.RJButton();
            this.btn_WalledGarden = new SmartCreator.RJControls.RJButton();
            this.btn_IpBinding = new SmartCreator.RJControls.RJButton();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnlClientArea.SuspendLayout();
            this.tableLayoutPanel_Top_Btn.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.panel_Tab_Container);
            this.pnlClientArea.Controls.Add(this.tableLayoutPanel_Top_Btn);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 609);
            this.pnlClientArea.Resize += new System.EventHandler(this.pnlClientArea_Resize);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(151, 22);
            this.lblCaption.Text = "Form_Acive_Host_Bar";
            // 
            // panel_Tab_Container
            // 
            this.panel_Tab_Container.BackColor = System.Drawing.Color.Transparent;
            this.panel_Tab_Container.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel_Tab_Container.Location = new System.Drawing.Point(0, 38);
            this.panel_Tab_Container.Name = "panel_Tab_Container";
            this.panel_Tab_Container.Size = new System.Drawing.Size(990, 571);
            this.panel_Tab_Container.TabIndex = 58;
            // 
            // tableLayoutPanel_Top_Btn
            // 
            this.tableLayoutPanel_Top_Btn.ColumnCount = 7;
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 100F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 44F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 17F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 239F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 198F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 227F));
            this.tableLayoutPanel_Top_Btn.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Absolute, 241F));
            this.tableLayoutPanel_Top_Btn.Controls.Add(this.btn_Host, 5, 0);
            this.tableLayoutPanel_Top_Btn.Controls.Add(this.btn_Acive, 6, 0);
            this.tableLayoutPanel_Top_Btn.Controls.Add(this.btn_WalledGarden, 3, 0);
            this.tableLayoutPanel_Top_Btn.Controls.Add(this.btn_IpBinding, 4, 0);
            this.tableLayoutPanel_Top_Btn.Dock = System.Windows.Forms.DockStyle.Top;
            this.tableLayoutPanel_Top_Btn.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel_Top_Btn.Margin = new System.Windows.Forms.Padding(0);
            this.tableLayoutPanel_Top_Btn.Name = "tableLayoutPanel_Top_Btn";
            this.tableLayoutPanel_Top_Btn.RowCount = 1;
            this.tableLayoutPanel_Top_Btn.RowStyles.Add(new System.Windows.Forms.RowStyle());
            this.tableLayoutPanel_Top_Btn.Size = new System.Drawing.Size(990, 38);
            this.tableLayoutPanel_Top_Btn.TabIndex = 57;
            // 
            // btn_Host
            // 
            this.btn_Host.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_Host.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Host.BorderRadius = 0;
            this.btn_Host.BorderSize = 1;
            this.btn_Host.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Host.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_Host.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_Host.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_Host.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Host.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F, System.Drawing.FontStyle.Bold);
            this.btn_Host.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Host.IconChar = FontAwesome.Sharp.IconChar.Magento;
            this.btn_Host.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Host.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Host.IconSize = 24;
            this.btn_Host.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Host.Location = new System.Drawing.Point(522, 0);
            this.btn_Host.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Host.Name = "btn_Host";
            this.btn_Host.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Host.Size = new System.Drawing.Size(227, 38);
            this.btn_Host.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Host.TabIndex = 5;
            this.btn_Host.Text = "جميع المستخدمين Host";
            this.btn_Host.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Host.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Host.UseVisualStyleBackColor = false;
            this.btn_Host.Click += new System.EventHandler(this.btn_Host_Click);
            // 
            // btn_Acive
            // 
            this.btn_Acive.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Acive.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Acive.BorderRadius = 0;
            this.btn_Acive.BorderSize = 1;
            this.btn_Acive.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Acive.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_Acive.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Acive.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Acive.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Acive.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F, System.Drawing.FontStyle.Bold);
            this.btn_Acive.ForeColor = System.Drawing.Color.White;
            this.btn_Acive.IconChar = FontAwesome.Sharp.IconChar.Magento;
            this.btn_Acive.IconColor = System.Drawing.Color.White;
            this.btn_Acive.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Acive.IconSize = 24;
            this.btn_Acive.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Acive.Location = new System.Drawing.Point(749, 0);
            this.btn_Acive.Margin = new System.Windows.Forms.Padding(0, 0, 8, 0);
            this.btn_Acive.Name = "btn_Acive";
            this.btn_Acive.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Acive.Size = new System.Drawing.Size(233, 38);
            this.btn_Acive.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Acive.TabIndex = 4;
            this.btn_Acive.Text = "المستخدمين النشطين (Acive)";
            this.btn_Acive.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Acive.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Acive.UseVisualStyleBackColor = false;
            this.btn_Acive.Click += new System.EventHandler(this.btn_Acive_Click);
            // 
            // btn_WalledGarden
            // 
            this.btn_WalledGarden.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_WalledGarden.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_WalledGarden.BorderRadius = 0;
            this.btn_WalledGarden.BorderSize = 1;
            this.btn_WalledGarden.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_WalledGarden.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_WalledGarden.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_WalledGarden.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_WalledGarden.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_WalledGarden.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F, System.Drawing.FontStyle.Bold);
            this.btn_WalledGarden.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_WalledGarden.IconChar = FontAwesome.Sharp.IconChar.Magento;
            this.btn_WalledGarden.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_WalledGarden.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_WalledGarden.IconSize = 24;
            this.btn_WalledGarden.Location = new System.Drawing.Point(85, 0);
            this.btn_WalledGarden.Margin = new System.Windows.Forms.Padding(0);
            this.btn_WalledGarden.Name = "btn_WalledGarden";
            this.btn_WalledGarden.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_WalledGarden.Size = new System.Drawing.Size(239, 38);
            this.btn_WalledGarden.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_WalledGarden.TabIndex = 5;
            this.btn_WalledGarden.Text = "مواقع مجانية Walled-Garden";
            this.btn_WalledGarden.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_WalledGarden.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_WalledGarden.UseVisualStyleBackColor = false;
            this.btn_WalledGarden.Visible = false;
            this.btn_WalledGarden.Click += new System.EventHandler(this.btn_WalledGarden_Click);
            // 
            // btn_IpBinding
            // 
            this.btn_IpBinding.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_IpBinding.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_IpBinding.BorderRadius = 0;
            this.btn_IpBinding.BorderSize = 1;
            this.btn_IpBinding.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_IpBinding.Dock = System.Windows.Forms.DockStyle.Fill;
            this.btn_IpBinding.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_IpBinding.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_IpBinding.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_IpBinding.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F, System.Drawing.FontStyle.Bold);
            this.btn_IpBinding.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_IpBinding.IconChar = FontAwesome.Sharp.IconChar.Magento;
            this.btn_IpBinding.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_IpBinding.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_IpBinding.IconSize = 24;
            this.btn_IpBinding.Location = new System.Drawing.Point(324, 0);
            this.btn_IpBinding.Margin = new System.Windows.Forms.Padding(0);
            this.btn_IpBinding.Name = "btn_IpBinding";
            this.btn_IpBinding.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_IpBinding.Size = new System.Drawing.Size(198, 38);
            this.btn_IpBinding.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_IpBinding.TabIndex = 5;
            this.btn_IpBinding.Text = "الامتيازات (Ip Binding)";
            this.btn_IpBinding.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_IpBinding.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_IpBinding.UseVisualStyleBackColor = false;
            this.btn_IpBinding.Visible = false;
            this.btn_IpBinding.Click += new System.EventHandler(this.btn_IpBinding_Click);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // Form_Acive_Host_Bar
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.BorderSize = 5;
            this.Caption = "Form_Acive_Host_Bar";
            this.ClientSize = new System.Drawing.Size(1000, 659);
            this.Name = "Form_Acive_Host_Bar";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_Acive_Host_Bar";
            this.Load += new System.EventHandler(this.Form_Acive_Host_Bar_Load);
            this.pnlClientArea.ResumeLayout(false);
            this.tableLayoutPanel_Top_Btn.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel_Tab_Container;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel_Top_Btn;
        private RJControls.RJButton btn_Host;
        private RJControls.RJButton btn_Acive;
        private RJControls.RJButton btn_WalledGarden;
        private RJControls.RJButton btn_IpBinding;
        private System.Windows.Forms.Timer timer1;
    }
}