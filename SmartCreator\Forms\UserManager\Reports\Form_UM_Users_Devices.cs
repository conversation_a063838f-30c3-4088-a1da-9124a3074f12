﻿using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using SmartCreator.ViewModels;
using System.Threading;

namespace SmartCreator.Forms.UserManager.Reports
{
    public partial class Form_UM_Users_Devices : RJChildForm
    {
        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;
        bool firstLoad = true;
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";
        string Server_Type = "UM";

        private DateTime dateFrom_detail = DateTime.Now;
        private DateTime dateTo_detail = DateTime.Now;

        public Form_UM_Users_Devices(string server_Type="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv2.RightToLeft = RightToLeft.No;
            }

            if (server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            CBox_SearchBy.SelectedIndex = 0;
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            this.Text = "تقارير اجهزة المستخدمين";
            Spanel.Width = 0;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel1.Customizable = false;
                rjPanel2.Customizable = false;
                rjPanel3.Customizable = false;
                rjPanel4.Customizable = false;
                rjPanel5.Customizable = false;
                pnl_side_sn.Customizable = false;

            }


            if (!UIAppearance.Language_ar)
            {
                this.Text = "Reports Users Devices";
                this.dgv.RightToLeft = RightToLeft.No;
            }

            set_font();

            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00").AddDays(-1);
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");

            dateFrom_detail = rjDateTime_From.Value;
            dateTo_detail = rjDateTime_To.Value;


            CheckBox_To_Date.Check = true;

            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
            CBox_SN_Compar.SelectedIndex = 3;
            Cbox_View.SelectedIndex = 0;
            Cbox_View.label.RightToLeft = RightToLeft.Yes;
            Cbox_View.label.TextAlign = ContentAlignment.MiddleLeft;
        }
        private void set_font()
        {
            //return;

            dgv.AllowUserToOrderColumns = true;
            dgv2.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv2.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv2.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = utils.Control_Mesur_DPI(35); 
            dgv2.ColumnHeadersHeight = utils.Control_Mesur_DPI(35);
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;

            rjLabel25.Font=
            rjCheckBox1.Font=
            CheckBox_To_Date.Font=
            check_with_Commi.Font=
            CheckBox_FromSession.Font=
            rjLabel26.Font=
            rjLabel6.Font=
            rjLabel23.Font=
            lbl_Sub_title.Font= 
                
                Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            Radio_By_Cards.Font=Radio_By_IP.Font=Radio_By_Mac.Font
                = ToggleButton_Detail.Font=ToggleButton_Monthly.Font=jToggleButton_Year.Font
                = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
           
            btn_.Font = btn_apply.Font = btn_Filter.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10 , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.dgv_textSize(dgv2);
            utils.item_Contrlol_textSize(dmAll_Cards);
        }
        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }

        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }

        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width > 50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = utils.Control_Mesur_DPI(260);
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();


            get_report();
            try
            {
                if (dgv.Rows.Count > 0)
                {

                    //Sub_LocadData(dgv.Rows[0].Cells["رقم الدفعة"].Value.ToString());
                }
            }
            catch { }
            firstLoad = false;

        }

        private void Form_UM_Users_Devices_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }
        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }
        private DataTable dt_ByDetails()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الماك", typeof(string));
            dt.Columns.Add("الايبي", typeof(string));
            dt.Columns.Add("عدد الكروت", typeof(double));
            dt.Columns.Add("اجمالي المبلغ", typeof(string));
            dt.Columns.Add("الوقت المستخدم", typeof(string));
            dt.Columns.Add("الاستهلاك المستخدم", typeof(string));
            dt.Columns.Add("عدد الجلسات", typeof(double));
            dt.Columns.Add("Price", typeof(double));
            dt.Columns.Add("UpTime", typeof(double));
            dt.Columns.Add("Up_Down", typeof(double));

            //dt.Columns.Add("CountSession", typeof(double));

            //dt.Columns.Add("UptimeLimt", typeof(double));
            //dt.Columns.Add("DownloadLimit", typeof(double));
            //dt.Columns.Add("count", typeof(double));
            return dt;
        }
        private void get_report()
        {


            string GroupBy = " GROUP BY s.CallingStationId ";
            if (Radio_By_IP.Checked)
                GroupBy = " GROUP BY s.IpUser ";
            dgv.DataSource = null;
            double sum = 0;
            double sum_download = 0;
            double sum_uptime = 0;
            double count_cards = 0;
            double total_Session_cards = 0;
            string Price = "Price";

            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            //txt_avg.Text = "0"; 
            txt_count_Cards.Text = "0";
            //txt_Total_Session.Text = "0";
            txt_Download.Text = "0";
            txt_sum_Sales.Text = "0";
            txt_uptime.Text = "00:00:00";

            string Query_firstUse = condition_detail_firstUse();


            string Qury1 = $@"SELECT u.Sn_Name,s.CallingStationId as CallerMac	,u.Price ,u.ProfileName,u.DownloadUsed,u.UploadUsed,u.UptimeUsed
			                        FROM {TableUser} u INNER JOIN {TableSession} s ON u.Sn_Name = s.Fk_Sn_Name   
			                        {Query_firstUse} 
			                        {GroupBy},s.Fk_Sn_Name 
                                ";
            List<UmUser> Users = Local_DA.Load<UmUser>(Qury1);
            string Qury2 = @$" SELECT s.CallingStationId, sum(s.BytesDownload) BytesDownload , sum(s.BytesUpload) BytesUpload , sum(s.UpTime) UpTime FROM {TableSession} s 
                                {condition_detail_firstUse(true)}  
                                Group BY s.CallingStationId ";


            if (Server_Type == "UM")
            {
                List<UmSession> Session = Local_DA.Load<UmSession>(Qury2);
                var result = (from u in Users
                              join s in Session
                              on u.CallerMac equals s.CallingStationId
                              group new { u, s } by u.CallerMac into g
                              select new
                              {
                                  CallingStationId = g.Key,
                                  IpUser = g.First().s.IpUser,
                                  //UpTime = g.First().s.UpTime,
                                  UpTime = CheckBox_FromSession.Check ? g.First().u.UptimeUsed : g.First().u.UptimeUsed,
                                  //UpTime = g.First().u.UptimeUsed,
                                  //Up_Down = g.Sum(x => x.u.DownloadUsed + x.u.UploadUsed),
                                  Up_Down = CheckBox_FromSession.Check ? g.Sum(x => x.u.DownloadUsed + x.u.UploadUsed) : g.Sum(x => x.s.BytesUpload + x.s.BytesDownload),
                                  //Up_Down = g.Sum(x => x.s.BytesUpload + x.s.BytesDownload),
                                  count = g.Distinct().Count(),
                                  CountSession = g.Count(),
                                  Price = g.Sum(x => x.u.Price),

                              }).ToList();
                //dgv.DataSource = result;


                DataTable dt = dt_ByDetails();
                foreach (var itm in result)
                {
                    DataRow row = dt.NewRow();
                    row[0] = itm.CallingStationId;
                    row[1] = itm.IpUser;
                    row[2] = itm.count;
                    row[3] = String.Format("{0:n0}", itm.Price);
                    row[4] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm.UpTime));
                    row[5] = utils.ConvertSize_Get_InArabic(itm.Up_Down.ToString());
                    row["عدد الجلسات"] = itm.CountSession;
                    row[7] = itm.Price;
                    row[8] = itm.UpTime;
                    row[9] = itm.Up_Down;

                    dt.Rows.Add(row);
                    sum_uptime += Convert.ToDouble(itm.UpTime);
                    sum_download += Convert.ToDouble(itm.Up_Down);
                    total_Session_cards += Convert.ToDouble(itm.count);
                }

                dgv.DataSource = dt;
            }
            if(Server_Type=="HS")
            {
                List<HsSession> Session = Local_DA.Load<HsSession>(Qury2);
                var result = (from u in Users
                              join s in Session
                              on u.CallerMac equals s.CallingStationId
                              group new { u, s } by u.CallerMac into g
                              select new
                              {
                                  CallingStationId = g.Key,
                                  IpUser = g.First().s.IpUser,
                                  //UpTime = g.First().s.UpTime,
                                  UpTime = CheckBox_FromSession.Check ? g.First().u.UptimeUsed : g.First().u.UptimeUsed,
                                  //UpTime = g.First().u.UptimeUsed,
                                  //Up_Down = g.Sum(x => x.u.DownloadUsed + x.u.UploadUsed),
                                  Up_Down = CheckBox_FromSession.Check ? g.Sum(x => x.u.DownloadUsed + x.u.UploadUsed) : g.Sum(x => x.s.BytesUpload + x.s.BytesDownload),
                                  //Up_Down = g.Sum(x => x.s.BytesUpload + x.s.BytesDownload),
                                  count = g.Distinct().Count(),
                                  CountSession = g.Count(),
                                  Price = g.Sum(x => x.u.Price),

                              }).ToList();
                //dgv.DataSource = result;


                DataTable dt = dt_ByDetails();
                foreach (var itm in result)
                {
                    DataRow row = dt.NewRow();
                    row[0] = itm.CallingStationId;
                    row[1] = itm.IpUser;
                    row[2] = itm.count;
                    row[3] = String.Format("{0:n0}", itm.Price);
                    row[4] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm.UpTime));
                    row[5] = utils.ConvertSize_Get_InArabic(itm.Up_Down.ToString());
                    row["عدد الجلسات"] = itm.CountSession;
                    row[7] = itm.Price;
                    row[8] = itm.UpTime;
                    row[9] = itm.Up_Down;

                    dt.Rows.Add(row);
                    sum_uptime += Convert.ToDouble(itm.UpTime);
                    sum_download += Convert.ToDouble(itm.Up_Down);
                    total_Session_cards += Convert.ToDouble(itm.count);
                }

                dgv.DataSource = dt;
            }
            
            
            //return;

            //string Qury = $@"WITH Users AS (
            //                    SELECT u.Sn_Name
            //                    ,sum(u.{Price}) Price
            //                    FROM UmUser u INNER JOIN UmSession s ON u.Sn_Name = s.Fk_Sn_Name 
            //                    {Query_firstUse} 
            //                    GROUP BY s.CallingStationId,s.Fk_Sn_Name
            //                    )
            //                    SELECT s.CallingStationId
            //                    ,s.IpUser
            //                    ,sum(s.UpTime) UpTime
            //                    ,sum(s.BytesDownload+BytesUpload) Up_Down
            //                    ,count(DISTINCT u.Sn_Name) count
            //                    ,count(s.Fk_Sn_Name) CountSession
            //                    ,sum(u.{Price}) Price
            //                    FROM Users u INNER JOIN UmSession s ON u.Sn_Name = s.Fk_Sn_Name 
            //                    {condition_detail_firstUse(true)}  
            //                    {GroupBy} 
            //                     ORDER by count DESC
            //                    ";

            //return;
            try
            {
                //    //DataTable users = Local_DA.RunSqlCommandAsDatatable(Qury);
                //    //dgv.DataSource = users;
                //    //loadDgvState();

                //DataTable dt = dt_ByDetails();
                //DataTable users = Local_DA.RunSqlCommandAsDatatable(Qury);
                //foreach (DataRow itm in users.Rows)
                //{
                //    DataRow row = dt.NewRow();
                //    row[0] = itm["CallingStationId"].ToString();
                //    row[1] = itm["IpUser"].ToString();
                //    row[2] = itm["count"];
                //    row[3] = String.Format("{0:n0}", itm["Price"]);
                //    row[4] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm["UpTime"]));
                //    row[5] = utils.ConvertSize_Get_InArabic(itm["Up_Down"].ToString());
                //    row["عدد الجلسات"] = itm["CountSession"];
                //    row[7] = itm["Price"];
                //    row[8] = itm["UpTime"];
                //    row[9] = itm["Up_Down"];

                //    dt.Rows.Add(row);
                //    sum_uptime += Convert.ToDouble(itm["UpTime"]);
                //    sum_download += Convert.ToDouble(itm["Up_Down"]);
                //    total_Session_cards += Convert.ToDouble(itm["count"]);
                //}
                //dgv.DataSource = dt;

                txt_count_Cards.Text = dgv.Rows.Count.ToString();
                txt_Download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(sum_uptime));

                //txt_Total_Session.Text = total_Session_cards.ToString();

                //var sum_download22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("down+up"));
                //var sum_uptime22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("uptime"));

                //loadDgvState();
                dgv.Columns["Price"].Visible = false;
                dgv.Columns["UpTime"].Visible = false;
                dgv.Columns["Up_Down"].Visible = false;
                dgv.Columns["الايبي"].Visible = false;
                dgv.Columns["الماك"].Visible = false;
                if (Radio_By_IP.Checked)
                {
                    dgv.Columns["الايبي"].Visible = true;
                    dgv.Columns["الماك"].Visible = false;
                }
                else
                {
                    dgv.Columns["الايبي"].Visible = false;
                    dgv.Columns["الماك"].Visible = true;
                }

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }

            //update_select_DGV();

            return;


            //else
            //{
            //    string Query_conditon = condition_Session_By_Days_for_firstUse();
            //    string fitler = "'%Y-%m-%d'";
            //    if (jToggleButton_Year.Checked)
            //        fitler = "'%Y-%m'";

            //    List<class_Report_monthly_or_Dayliy> um = new List<class_Report_monthly_or_Dayliy>();
            //    List<class_Report_monthly_or_Dayliy> totalDownload = new List<class_Report_monthly_or_Dayliy>();


            //    string Query = $"SELECT {ColumnShow} strftime({fitler}, s.FromTime) date , sum(BytesDownload + BytesUpload) as 'down+up' , sum(UpTime) as Uptime ,COUNT(DISTINCT u.Sn_Name)  Count,count(s.Fk_Sn_Name) CountSession " +
            //        $"FROM UmSession s " +
            //        $"INNER JOIN UmUser u ON u.Sn_Name  = s.Fk_Sn_Name " +
            //        $"{Query_conditon} " +
            //        $"group by strftime({fitler}, s.FromTime)";
            //    try
            //    {
            //        DataTable dt = dt_Monthly();
            //        DataTable dt_res = Local_DA.RunSqlCommandAsDatatable(Query);
            //        foreach (DataRow itm in dt_res.Rows)
            //        {
            //            DataRow row = dt.NewRow();
            //            row[0] = (itm["date"].ToString());

            //            row[1] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm["uptime"]));
            //            row[2] = utils.ConvertSize_Get_InArabic(itm["down+up"].ToString());

            //            row[3] = itm["Count"];
            //            row[4] = itm["CountSession"];
            //            row[5] = itm["uptime"];
            //            row[6] = itm["down+up"];

            //            dt.Rows.Add(row);

            //            sum_uptime += Convert.ToDouble(itm["uptime"]);
            //            sum_download += Convert.ToDouble(itm["down+up"]);
            //            total_Session_cards += Convert.ToDouble(itm["CountSession"]);
            //            count_cards += Convert.ToDouble(itm["Count"]);
            //        }
            //        dgv.DataSource = dt;

            //        txt_count_Cards.Text = count_cards.ToString();
            //        txt_Total_Session.Text = total_Session_cards.ToString();
            //        txt_uptime.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
            //        txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));

            //        //var sum_download22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("down+up"));
            //        //var sum_uptime22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("uptime"));

            //        dgv.Columns["uptime"].Visible = false;
            //        dgv.Columns["down+up"].Visible = false;
            //        //loadDgvState();
            //    }
            //    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            //    //dgv.DataSource = Local_DA.Load<class_Report_Size_And_Times>(Query);
            //    //update_select_DGV();
            //    return;
            //}

        }

        private string condition_detail_firstUse(bool BySession=false)
        {
            string conditon_date = "";
            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
          
            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";
            if(BySession)
                conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";
            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    conditon_date = " ";
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    {
                        if (BySession)
                            nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  ";
                    }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    {
                        if (BySession)
                            radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  ";
                    }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }
                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    //if (ToggleButton_Detail.Checked)
                    //Sub_LocadData(dgv.Rows[e.RowIndex].Cells["رقم الدفعة"].Value.ToString());
                    //else
                    Sub_LocadData();
                }
            }
            catch { }
        }
        private void Sub_LocadData(string CardId_Key = "")
        {
            try
            {
                if (dgv.Rows.Count <= 0)
                    return;

                CardId_Key = dgv.CurrentRow.Cells["الماك"].Value.ToString();
                string filter = $" and s.CallingStationId='{CardId_Key}' "; 
                if(Radio_By_IP.Checked)
                     filter = $" and s.IpUser='{CardId_Key}' ";

                dgv2.ContextMenuStrip = null;
                string Query_conditon = condition_detail_firstUse();
                
                CardId_Key = dgv.CurrentRow.Cells["الماك"].Value.ToString();
                if (Radio_By_IP.Checked)
                {
                    CardId_Key = dgv.CurrentRow.Cells["الايبي"].Value.ToString();
                    CardId_Key= dgv.CurrentRow.Cells["الايبي"].Value.ToString();
                }
                 
                if (ToggleButton_Detail.Checked)
                {
                    string Query= @$" SELECT u.* , count(s.Fk_Sn_Name) CountSession FROM {TableUser} u INNER JOIN {TableSession} s ON u.Sn_Name = s.Fk_Sn_Name {Query_conditon} {filter} GROUP BY s.Fk_Sn_Name ORDER by Sn DESC ";
                    dgv2.ContextMenuStrip = dmAll_Cards;
                    dgv2.DataSource = Local_DA.Load<UmUser>(Query);

                }
                
                try { rjTextBox1.Text = CardId_Key.ToString(); } catch { }
                try { txt_count_Cards.Text = dgv2.Rows.Count.ToString(); } catch { }
                //try { txt_Download.Text = dgv.CurrentRow.Cells["Up_Down"].Value.ToString(); } catch { }
                try { txt_sum_Sales.Text = dgv.CurrentRow.Cells["اجمالي المبلغ"].Value.ToString(); } catch { }
                //try { txt_Download.Text = dgv.CurrentRow.Cells["الوقت المستخدم"].Value.ToString(); } catch { }
                //try { txt_Download.Text = dgv.CurrentRow.Cells["الاستهلاك المستخدم"].Value.ToString(); } catch { }
                loadDgvState();
                update_select_DGV2();
            }
            catch { }
            //update_select_DGV2();
        }
        private void update_select_DGV2()
        {
            try
            {
                string ListAll = dgv2.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv2.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        DataGridViewCell ActiveCell = null;
        private void dgv2_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv2.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv2[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void Copy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void dgv2_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV2();
        }
        Dgv_Header_Proprties Dgv_State_list = null;
        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if (Cbox_View.SelectedIndex == 1)
            {
                Init_dgv_to_Default();
                return;
            }

            if (Cbox_View.SelectedIndex == 1)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Session");
            else if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_RB_Archive");
            //else
            //    DgvState = SqlDataAccess.Get_SourceSaveStateFormsVariable("DgvUserManagerPrcess");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            //dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv2.Columns[dv.Index].Visible = dv.Visable;
                    dgv2.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;
                    dgv2.Columns[dv.Index].Width = dv.Width;
                    foreach (ToolStripMenuItem control in toolStripMenuItem1.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            if (control.Tag != null)
                                if (control.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { /*RJMessageBox.Show(ex.Message);*/ }
            }

            try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv2.Columns["IdHX"].Visible = false; } catch { }
            try { dgv2.Columns["Status"].Visible = false; } catch { }
            try { dgv2.Columns["Disabled"].Visible = false; } catch { }

            try { dgv2.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv2.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv2.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["CallerMac"].Visible = false; } catch { }
            try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
            try { dgv2.Columns["CountSession"].Visible = false; } catch { }
        }
        private void Init_dgv_to_Default()
        {
            if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
            {
                try
                {
                    foreach (DataGridViewColumn column in dgv2.Columns)
                    {

                        column.Visible = false;
                    }



                    //dgv.Columns["Sn"].Visible = true;
                    dgv2.Columns["Str_Status"].Visible = true;
                    dgv2.Columns["Str_Status"].DisplayIndex = 0;
                    Status_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["UserName"].Visible = true;
                    dgv2.Columns["UserName"].DisplayIndex = 1;
                    UserName_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["ProfileName"].Visible = true;
                    dgv2.Columns["ProfileName"].DisplayIndex = 3;
                    Profile_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);
                    Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_Up_Down"].Visible = true;
                    dgv2.Columns["Str_Up_Down"].DisplayIndex = 7;
                    dgv2.Columns["Str_Up_Down"].Width = utils.Control_Mesur_DPI(190);
                    Str_Up_Down_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["MoneyTotal"].Visible = true;

                    dgv2.Columns["Str_ProfileTimeLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                    dgv2.Columns["Str_ProfileTimeLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTransferLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                    dgv2.Columns["Str_ProfileTransferLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTillTime"].Visible = true;
                    dgv2.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                    dgv2.Columns["Str_ProfileTillTime"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["LastSynDb"].Visible = false;
                    dgv2.Columns["LastSynDb"].DisplayIndex = 11;
                    dgv2.Columns["LastSynDb"].Width = utils.Control_Mesur_DPI(150);
                    //dgv.Columns["SpName"].Visible = true;

                    //dgv.Columns["Comment"].Visible = true;
                    dgv2.Columns["Comment"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["CountProfile"].Visible = true;
                    try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
                    //try { dgv.Columns["Status "].Visible = false; } catch { }
                    //try { dgv.Columns["Disabled "].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Width = utils.Control_Mesur_DPI(150); } catch { }
                    try { dgv2.Columns["CountSession"].Visible = false; } catch { }
                    try { dgv2.Columns["CountSession"].Width = utils.Control_Mesur_DPI(150); } catch { }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Init_dgv_to_Default"); }
            }

            else
            {
                try
                {
                    //foreach (DataGridViewColumn column in dgv.Columns)
                    //{
                    //    column.Visible = false;
                    //}

                    dgv2.Columns["UserName"].Visible = false;
                    dgv2.Columns["UserName"].DisplayIndex = 1;

                    dgv2.Columns["FromTime"].Visible = true;
                    dgv2.Columns["FromTime"].DisplayIndex = 2;
                    dgv2.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["TillTime"].Visible = true;
                    dgv2.Columns["TillTime"].DisplayIndex = 3;
                    dgv2.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                    dgv2.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                    dgv2.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["CallingStationId"].Visible = true;
                    dgv2.Columns["CallingStationId"].DisplayIndex = 7;
                    dgv2.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                    dgv2.Columns["IpUser"].Visible = true;
                    dgv2.Columns["IpUser"].DisplayIndex = 8;
                    dgv2.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["NasPortId"].Visible = true;
                    dgv2.Columns["NasPortId"].DisplayIndex = 9;

                    dgv2.Columns["IpRouter"].Visible = true;
                    dgv2.Columns["IpRouter"].DisplayIndex = 10;
                    dgv2.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                    dgv2.Columns["Sn_Name"].Visible = false;
                    dgv2.Columns["Fk_Sn_Name"].Visible = false;
                    dgv2.Columns["IdHX"].Visible = false;

                }
                catch { }

            }
        }
        bool sort_Uptime = true;
        bool sort_download = true;
        bool sort_Upload = true;
        bool up_down_inByte = true;
        bool sort_Validay = true;
        public void ordercolumn(DataGridView dg, DataGridViewCellMouseEventArgs e)
        {
            //return;
             
            try
            {
                int currentColumnIndex = e.ColumnIndex;
                string columnName = dg.Columns[currentColumnIndex].Name;

                if (columnName == "الوقت المستخدم")
                {
                    try
                    {
                        if (sort_Uptime)
                        {
                            sort_Uptime = false;
                            dg.Sort(dg.Columns["UpTime"], ListSortDirection.Ascending);
                        }
                        else
                        {
                            sort_Uptime = true;
                            dg.Sort(dg.Columns["UpTime"], ListSortDirection.Descending);
                        }
                    }
                    catch
                    {
                        //if (sort_Uptime)
                        //{
                        //    sort_Uptime = false;
                        //    dg.Sort(dg.Columns["اجمالي الوقت"], ListSortDirection.Ascending);
                        //}
                        //else
                        //{
                        //    sort_Uptime = true;
                        //    dg.Sort(dg.Columns["اجمالي الوقت"], ListSortDirection.Descending);
                        //}
                    }
                }
                if (columnName == "الاستهلاك المستخدم")
                    if (sort_download)
                    {
                        sort_download = false;
                        dg.Sort(dg.Columns["Up_Down"], ListSortDirection.Ascending);
                    }
                    else
                    {
                        sort_download = true;
                        dg.Sort(dg.Columns["Up_Down"], ListSortDirection.Descending);
                    }
                if (columnName == "اجمالي المبلغ")
                    if (sort_Upload)
                    {
                        sort_Upload = false;
                        dg.Sort(dg.Columns["Price"], ListSortDirection.Ascending);
                    }
                    else
                    {
                        sort_Upload = true;
                        dg.Sort(dg.Columns["Price"], ListSortDirection.Descending);
                    }
                //if (columnName == "الرفع" || columnName == "اجمالي الرفع")
                //    if (sort_Upload)
                //    {
                //        sort_Upload = false;
                //        dg.Sort(dg.Columns["upload_inByte"], ListSortDirection.Ascending);
                //    }
                //    else
                //    {
                //        sort_Upload = true;
                //        dg.Sort(dg.Columns["upload_inByte"], ListSortDirection.Descending);
                //    }
                //if (columnName == "الصلاحية المتبقية" || columnName == "الصلاحية")
                //    if (sort_Validay)
                //    {
                //        sort_Validay = false;
                //        dg.Sort(dg.Columns["validy_In_Houre"], ListSortDirection.Ascending);
                //    }
                //    else
                //    {
                //        sort_Validay = true;
                //        dg.Sort(dg.Columns["validy_In_Houre"], ListSortDirection.Descending);
                //    }
            }
            catch { }
        }

        private void dgv_ColumnHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            ordercolumn(dgv, e);
        }

        private void Radio_By_Mac_CheckedChanged(object sender, EventArgs e)
        {
            if(firstLoad) 
                return;
            get_report();
        }

        private void Radio_By_IP_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            get_report();

        }

        private void Radio_By_Cards_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            get_report();
        }

        private void dgv2_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
                get_card_detail();
        }

        private void get_card_detail()
        {
            try
            {
                foreach (DataGridViewRow dr in dgv2.SelectedRows)
                {
                    if (Server_Type == "UM")
                    {
                        UmUser user = dr.DataBoundItem as UmUser;
                        Form_CardsDetails form_CardsDetails = new Form_CardsDetails(user, "From_RB_Archive");
                        form_CardsDetails.ShowDialog();
                    }
                    else if (Server_Type == "HS")
                    {
                        HSUser user = dr.DataBoundItem as HSUser;
                        Form_CardsDetailsHS form_CardsDetails = new Form_CardsDetailsHS(user, "From_RB_Archive");
                        form_CardsDetails.ShowDialog();
                    }
                    return;
                }
            }
            catch { }
        }

        private void toolStripMenuItem4_Click(object sender, EventArgs e)
        {
            if (dgv2.SelectedRows.Count > 0)
                get_card_detail();
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            try
            {
                BindingSource bs = new BindingSource();
                bs.DataSource = dgv.DataSource;
                bs.Filter = string.Format(" [الماك]  LIKE '%{0}%'", txt_search.Text);
                dgv.DataSource = bs;
            }
            catch { }
        }

        private void rjButton3_Click(object sender, EventArgs e)
        {
            btnPdf_Click2();
        }

        double avg = 0; double sum = 0; double sum_download = 0; double sum_uptime = 0; double count_cards = 0; double CountFirstLogin = 0; double CountSession = 0;

        private void Pdf_By_Days(DataGridView _dgv = null)
        {
            if (_dgv == null)
            {
                _dgv = dgv;
            }
            string _name = dgv.CurrentRow.Cells["الماك"].Value.ToString();
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            if (_dgv.Name == "dgv")
            {
                if (ToggleButton_Detail.Checked)
                    dateHeader = "تقرير العملاء - من تاريخ  : " + start + "  الى  " + end;
                if (ToggleButton_Monthly.Checked)
                    dateHeader = "تقرير يومي للعملاء لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    dateHeader = "تقرير شهري للعملاء لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);
            }
            else
            {
                if (ToggleButton_Detail.Checked)
                    dateHeader = $"تقرير تفصيلي للعميل ({_name}) من تاريخ  : " + start + "  الى  " + end;
                if (ToggleButton_Monthly.Checked)
                    dateHeader = $"تقرير يومي للعميل ({_name}) لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    dateHeader = $"تقرير شهري للعميل ({_name}) لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            }
            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + "- العملاء" + ".pdf";
                if (_dgv.Name == "dgv")
                    sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + $"- عميل {_name}" + ".pdf";

                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);

                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in _dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in _dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in _dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);

                            PdfPTable footer = new PdfPTable(5);
                            //======================================================================
                            if (ToggleButton_Monthly.Checked || jToggleButton_Year.Checked)
                            {
                                footer.TotalWidth = 580f;
                                footer.LockedWidth = true;
                                footer.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                                footer.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                footer.HorizontalAlignment = Element.ALIGN_CENTER;
                                footer.SpacingBefore = 2f;
                                footer.SpacingAfter = 2f;
                                footer.DefaultCell.Padding = 3;


                                //footer.AddCell(new Phrase("اجمالي الجلسات", arabicFont_fotter));
                                footer.AddCell(new Phrase("اجمالي الكروت", arabicFont_fotter));
                                //footer.AddCell(new Phrase("كروت اول دخول", arabicFont_fotter));
                                footer.AddCell(new Phrase("المبلغ", arabicFont_fotter));

                                footer.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                                footer.AddCell(new Phrase("اجمالي الاستهلاك", arabicFont_fotter));

                                if (ToggleButton_Monthly.Checked)
                                    footer.AddCell(new Phrase("متوسط المبيعات اليومي", arabicFont_fotter));
                                if (jToggleButton_Year.Checked)
                                    footer.AddCell(new Phrase("متوسط المبيعات الشهري", arabicFont_fotter));

                                //======================================================================

                                //footer.AddCell(new Phrase(CountSession.ToString(), arabicFont));
                                footer.AddCell(new Phrase(count_cards.ToString(), arabicFont));
                                //footer.AddCell(new Phrase(CountFirstLogin.ToString(), arabicFont));
                                footer.AddCell(new Phrase(String.Format("{0:n0}", sum), arabicFont));
                                footer.AddCell(new Phrase(utils.Get_Seconds_By_clock_Mode((sum_uptime)), arabicFont));
                                footer.AddCell(new Phrase(utils.ConvertSize_Get_InArabic(sum_download.ToString()), arabicFont));
                                footer.AddCell(new Phrase(String.Format("{0:n0}", avg), arabicFont));
                            }
                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Add(footer);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            //RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }


        private void btnPdf_Click2()
        {
            
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            //if (ToggleButton_Detail.Checked)
            dateHeader = "تقرير اجهزة العملاء - من تاريخ  : " + start + "  الى  " + end;
            //if (ToggleButton_Monthly.Checked)
            //    dateHeader = "تقرير يومي للاجهزة لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
            //if (jToggleButton_Year.Checked)
            //    dateHeader = "تقرير شهري للاجهزة لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + "- العملاء" + ".pdf";
                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);

                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);
                            //======================================================================
                            //table_out.AddCell(new Phrase("اجمالي الجلسات", arabicFont_fotter));

                            //table_out.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("اجمالي الاستهلاك", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("اجمالي الكروت", arabicFont_fotter));

                            //======================================================================

                            //table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            //table_out.AddCell(new Phrase(txt_uptime.Text, arabicFont));
                            //table_out.AddCell(new Phrase("", arabicFont));
                            //table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            //table_out.AddCell(new Phrase(txt_count_Cards.Text, arabicFont));

                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            //RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            Pdf_By_Days(dgv2);
        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (ToggleButton_Detail.Checked)
            {
                dgv.ContextMenuStrip = dmAll_Cards;
                firstLoad = true;
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;
                firstLoad = false;

                //pnl_size_time_count.Visible = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                {
                    firstLoad = true;
                    ToggleButton_Detail.Checked = true;
                    firstLoad = false;
                }
            }
            try
            {
                string today = DateTime.Now.ToString("MM-dd-yyyy");
                dateFrom_detail = Convert.ToDateTime(today + "  00:00:00");
            }
            catch { }

            try { Sub_LocadData(dgv.CurrentRow.Cells["الماك"].Value.ToString()); } catch { }

        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            dgv.ContextMenuStrip = null;
            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {

                firstLoad = true;
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                //pnl_size_time_count.Visible = true;
                firstLoad = false;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                {
                    firstLoad = true;
                    ToggleButton_Monthly.Checked = true;
                    firstLoad = false;
                }
            }
            try
            {
                DateTime firstDayOfMonth;
                DateTime lastDayOfMonth;
                utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                string first = firstDayOfMonth.ToString("MM-dd-yyyy");
                string last = lastDayOfMonth.ToString("MM-dd-yyyy");

                dateFrom_detail = Convert.ToDateTime(first + "  00:00:00");
                dateTo_detail = Convert.ToDateTime(last + "  23:59:59");

                try { Sub_LocadData(dgv.CurrentRow.Cells["الماك"].Value.ToString()); } catch { }
            }
            catch { }
        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;


            if (jToggleButton_Year.Checked)
            {

                firstLoad = true;
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                //pnl_size_time_count.Visible = true;
                firstLoad = false;

            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                {
                    firstLoad = true;
                    jToggleButton_Year.Checked = true;
                    firstLoad = false;
                }
            }
            try
            {
                dateFrom_detail = new DateTime(DateTime.Now.Year, 1, 1);
                dateTo_detail = new DateTime(DateTime.Now.Year, 12, 31);
                //get_report();
                try { Sub_LocadData(dgv.CurrentRow.Cells["الماك"].Value.ToString()); } catch { }
            }
            catch { }
        }

        private void btn_Refresh2_Click(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;

            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => Refersh_mikrotik());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }



        }

        [Obsolete]
        private void Refersh_mikrotik()
        {
            Global_Variable.StartThreadProcessFromMK = true;
            bool Syn_Users_FromFasrDB = false;
            bool Syn_Pyment_FromFasrDB = false;
            bool Syn_Session_FromFasrDB = false;

            if (Global_Variable.load_by_DownloadDB && Global_Variable.Mk_resources.version <= 6)
            {
                int count_process = 7;
                Fast_Load_From_Mikrotik fast = new Fast_Load_From_Mikrotik();
                Global_Variable.Update_Um_StatusBar_Prograss("تنزيل قاعدة بيانات اليوزمنجر  -  لتستفيد من الميزه افتح السرعه للكمبيوتر", Convert.ToInt32(1 * (100.0 / count_process)));
                if (fast.Download_Sql_From_Mikrotik())
                {

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر", Convert.ToInt32(2 * (100.0 / count_process)));
                    //Thread.Sleep(1000);
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه الكروت يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                    Syn_Users_FromFasrDB = fast.Syn_UmUser_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه المبيعات والحسابات يوزمنجر", Convert.ToInt32(4 * (100.0 / count_process)));
                    Syn_Pyment_FromFasrDB = fast.Syn_Pyments_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه  الجلسات يوزمنجر", Convert.ToInt32(5 * (100.0 / count_process)));
                    Syn_Session_FromFasrDB = fast.Syn_Session_From_FastDB();

                    //---======================================================================================================

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب ومزامنه بيانات اليوزمنجر من الروتر", Convert.ToInt32(0 * (100.0 / count_process)));

                    fast.Create_Indexs();
                }
                try
                {
                    string Downloadfile = utils.Get_Database_Directory() + "\\" + "dbs\\temp.db";
                    string Downloadfile2 = utils.Get_Database_Directory() + "\\" + "dbs\\temp2.db";
                    string Downloadfile3 = Directory.GetCurrentDirectory() + "\\sql.bat";
                    if (File.Exists(Downloadfile))
                        File.Delete(Downloadfile);
                    if (File.Exists(Downloadfile2))
                        File.Delete(Downloadfile2);
                    if (File.Exists(Downloadfile3))
                        File.Delete(Downloadfile3);
                }
                catch { }

            }

            if (Syn_Session_FromFasrDB == false)
            //if (  ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false))   )
            {
                int count_process = 4;
                UserManagerProcess u = new UserManagerProcess();
                //if (Global_Variable.Ddiable_LoadSession == false)
                //{
                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(1 * (100.0 / count_process)));

                Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                //RJMessageBox.Show("يتم finsh get session");

                Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(2 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StartSyn();


                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(3 * (100.0 / count_process)));

                if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                    u.Syn_Session_to_LocalDB();
                Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(0 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StopSyn();
                //}
            }


            Global_Variable.StartThreadProcessFromMK = false;
        }

    }
}
