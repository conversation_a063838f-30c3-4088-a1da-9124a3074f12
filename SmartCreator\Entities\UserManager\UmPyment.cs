﻿using SmartCreator.Data;
using System;
using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
//using ServiceStack.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.UserManager
{

    //CREATE INDEX UmPyment_idx_c6d9b548 ON UmPyment(AddedDate);
    //CREATE INDEX UmPyment_idx_811fb391 ON UmPyment(Fk_Sn_Name);
    //CREATE INDEX UmPyment_idx_ddd70e93 ON UmPyment(DeleteFromServer, Fk_Sn_Name);
    //CREATE INDEX UmPyment_idx_6cd2fe2d ON UmPyment(Fk_Sn_Name, Sn DESC);

    [CompositeIndex("DeleteFromServer", "Fk_Sn_Name")]
    [CompositeIndex("Fk_Sn_Name", "Sn DESC")]

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class UmPyment: BasePyment
    {
        //public UmPyment() { }
                
        //[PrimaryKey, AutoIncrement,Unique,Required]
        //public int? Id { get; set; }

        [StringLength(100)]
        
        public string IdHX { get; set; }

        
        public long Sn { get; set; }

        //[Required]
        //[StringLength(200)]
        //[Index(Unique = true)]
        //[Unique]
        [PrimaryKey, Unique, Required]
        public string Sn_Name { get; set; }
        
       
        [Default(0)]
        public int DeleteFromServer { get; set; } = 0;

        [ForeignKey(typeof(UmUser), OnDelete = "CASCADE", ColumnName = "Sn_Name")]
        //[ForeignKey(typeof(UmUser), OnDelete = "CASCADE"),
        [Index]
        public string Fk_Sn_Name { get; set; }


        //[ForeignKey("UmUser")]
        //[ForeignKey(typeof(UmUser), OnDelete = "CASCADE"), Index]
        //public int UmUserId { get; set; }

        //[Computed]
        //public  UmUser UmUser { get; set; }


    }

}
