﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Data
{
    public class SmartDBAttribute : Attribute
    {
        //public SmartDBAttribute() : base()
        //{

        //}


    }

    [AttributeUsage(AttributeTargets.Property)]
    public class ComputedAttribute : AttributeBase
    {
    }

    public class AttributeBase : Attribute
    {
        public AttributeBase()
        {
            this.typeId = Guid.NewGuid();
        }

        protected readonly Guid typeId; //Hack required to give Attributes unique identity
    }

    public class MetadataAttributeBase : AttributeBase
    {
        /// <summary>
        /// Don't include default bool or nullable int default values
        /// </summary>
        public virtual bool ShouldInclude(PropertyInfo pi, string value)
        {
            if (pi.PropertyType == typeof(int) && value == "-2147483648") //int.MinValue
                return false;
            if (pi.PropertyType == typeof(bool) && value == "false")
                return false;

            return true;
        }
    }
    
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public class DefaultAttribute : AttributeBase
    {
        public int IntValue { get; set; }
        public double DoubleValue { get; set; }

        public Type DefaultType { get; set; }
        public string DefaultValue { get; set; }
        public string WithConstraint { get; set; }

        public bool OnUpdate { get; set; }

        public DefaultAttribute(int intValue)
        {
            this.IntValue = intValue;
            this.DefaultType = typeof(int);
            this.DefaultValue = this.IntValue.ToString();
        }

        public DefaultAttribute(double doubleValue)
        {
            this.DoubleValue = doubleValue;
            this.DefaultType = typeof(double);
            this.DefaultValue = doubleValue.ToString();
        }

        public DefaultAttribute(string defaultValue)
        {
            this.DefaultType = typeof(string);
            this.DefaultValue = defaultValue;
        }

        public DefaultAttribute(Type defaultType, string defaultValue)
        {
            this.DefaultValue = defaultValue;
            this.DefaultType = defaultType;
        }
    }

    [AttributeUsage(AttributeTargets.Property)]
    public class UniqueAttribute : AttributeBase { }

    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field)]
    public class AutoIncrementAttribute : AttributeBase
    {
    }
    
    [AttributeUsage(AttributeTargets.Property)]
    public class PrimaryKeyAttribute : AttributeBase
    {
    }
    public class RequiredAttribute : AttributeBase
    {
    }

    
    
    //===================Smart  attribut on class and property
    
    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = true)]
    public class UniqueConstraintAttribute : AttributeBase
    {
        public List<string> FieldNames { get; set; }

        public string Name { get; set; }

        public UniqueConstraintAttribute()
        {
            FieldNames = new List<string>();
        }

        public UniqueConstraintAttribute(params string[] fieldNames)
        {
            FieldNames = new List<string>(fieldNames);
        }
    }

    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct | AttributeTargets.Property | AttributeTargets.Field)]
    public class IndexAttribute : AttributeBase
    {
        public string Name { get; set; }

        public bool Unique { get; set; }

        public bool Clustered { get; set; }

        public bool NonClustered { get; set; }

        public IndexAttribute()
        {
        }

        public IndexAttribute(bool unique)
        {
            Unique = unique;
        }
    }

    public class StringLengthAttribute : AttributeBase
    {
        public const int MaxText = int.MaxValue;

        public int MinimumLength { get; set; }

        public int MaximumLength { get; set; }

        public StringLengthAttribute(int maximumLength)
        {
            MaximumLength = maximumLength;
        }

        public StringLengthAttribute(int minimumLength, int maximumLength)
        {
            MinimumLength = minimumLength;
            MaximumLength = maximumLength;
        }
    }


    [AttributeUsage(AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = true)]
    public class CompositeIndexAttribute : AttributeBase
    {
        public CompositeIndexAttribute()
        {
            this.FieldNames = new List<string>();
        }

        public CompositeIndexAttribute(params string[] fieldNames)
        {
            this.FieldNames = new List<string>(fieldNames);
        }

        public CompositeIndexAttribute(bool unique, params string[] fieldNames)
        {
            this.Unique = unique;
            this.FieldNames = new List<string>(fieldNames);
        }

        public List<string> FieldNames { get; set; }

        public bool Unique { get; set; }

        public string Name { get; set; }
    }

    //==============
    [AttributeUsage(AttributeTargets.Property | AttributeTargets.Field | AttributeTargets.Class | AttributeTargets.Struct, AllowMultiple = true)]
    public class ReferencesAttribute(Type type) : AttributeBase
    {
        public Type Type { get; set; } = type;
    }

    [AttributeUsage(AttributeTargets.Property)]
    public class ForeignKeyAttribute : ReferencesAttribute
    {
        public ForeignKeyAttribute(Type type)
            : base(type)
        {
        }

        public string OnDelete { get; set; }
        public string OnUpdate { get; set; }
        /// <summary>
        /// Explicit foreign key name. If it's null, or empty, the FK name will be autogenerated as before.
        /// </summary>
        public string ForeignKeyName { get; set; }
        public string ColumnName { get; set; }

    }
}
