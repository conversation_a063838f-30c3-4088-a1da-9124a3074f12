﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Runtime.ConstrainedExecution;
using System.Text;
using System.Threading;
using System.Windows.Forms;
//using static Org.BouncyCastle.Asn1.Cmp.Challenge;

namespace SmartCreator.Forms.Hotspot
{
    public partial class FormAllCardsHotspot : RJChildForm
    {
        DataGridViewCell ActiveCell = null;

        private int PageSize = 10000;
        private int currentPageindex = 1;
        private int totalPages = 0;
        private int totalRows = 0;


        int PW;
        bool Hided;
        Dictionary<int, Dgv_Header_Values> dvalue;

        private bool firstLoad = true;
        private bool Dgv_Click_ToChangIndex = false;
        Dgv_Header_Proprties Dgv_State_list = null;
        private string fillter_type = "From_Server";
        Smart_DataAccess Smart_DA;
        Sql_DataAccess Local_DA;

        private BatchCard Filter_BatchCards = null;
        private NumberPrintCard Filter_NumberPrintCard = null;
        private string Filter_SpCode = null;
        private string Filter_ProfileName = null;

        public FormAllCardsHotspot(string _fillter = "From_Server")
        {

            fillter_type = _fillter;
            InitializeComponent();
            InitializeFirstLoad();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }


        }
        public FormAllCardsHotspot(BatchCard batchCards = null, NumberPrintCard numberPrintCard = null, string filter_SpCode = null, string filter_ProfileName = null)
        {


            InitializeComponent();
            Filter_BatchCards = batchCards;
            Filter_NumberPrintCard = numberPrintCard;
            Filter_ProfileName = filter_ProfileName;
            Filter_SpCode = filter_SpCode;
            fillter_type = "From_RB_Archive";
            InitializeFirstLoad();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }


        }
        private void InitializeFirstLoad()
        {

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (fillter_type == "From_RB_Archive")
            {
                this.Text = "كروت الروتر والارشيف - هوتسبوت";
                this.lblCaption.Text = "كروت  الروتر مع الارشيف - هوتسبوت";
            }
            else if (fillter_type == "From_Finsh_Cards")
            {
                this.Text = "الكروت المنتهية - هوتسبوت";
                this.lblCaption.Text = "الكروت المنتهية - هوتسبوت";
                btn_RemoveFinsh.Visible = true;
                pnl_side_Finsh_Cards.Visible = true;
                pnl_side_Count_Session.Visible = false;


            }
            else if (fillter_type == "From_Server")
            {
                this.Text = "جميع الكروت من الروتر - هوتسبوت";
                this.lblCaption.Text = "جميع الكروت من الروتر - هوتسبوت";
            }




            CBox_OrderBy.label.TextAlign = ContentAlignment.MiddleLeft;
            CBox_SearchBy.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_SellingPoint.label.TextAlign = ContentAlignment.MiddleLeft;


            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            Date_From.Value = DateTime.Now.AddDays(-30);
            Date_To.Value = DateTime.Now;


            if (UIAppearance.Language_ar == false)
            {
                this.Text = "All Cards From Router";
                if (fillter_type == "From_RB_Archive")
                    this.Text = "All Cards From Router And Archive";
                if (fillter_type == "From_Finsh_Cards")
                    this.Text = "All Finsh Cards";

                this.dgv.RightToLeft = RightToLeft.No;
                this.dmAll_Cards.RightToLeft = RightToLeft.No;
            }
            else
            {
                if (fillter_type == "From_RB_Archive")
                    this.Text = "كروت الروتر والارشيف - هوتسبوت";
                else if (fillter_type == "From_Finsh_Cards")
                    this.Text = "الكروت المنتهية - هوتسبوت";
                else if (fillter_type == "From_Server")
                    this.Text = "جميع الكروت من الروتر - هوتسبوت";
            }

            if (fillter_type == "From_Finsh_Cards")
            {
                pnl_side_Finsh_Cards.Visible = true;
            }
            else
                pnl_side_Count_Session.Visible = true;

            if (fillter_type == "From_RB_Archive")
            {
                fpnl_showArchive.Visible = true;
                fpnl_showServer.Visible = true;
            }
           
            //dbFactory = Sql_DataAccess.Get_dbFactory();
            Spanel.Width = 0;
            PW = 280; ;
            Hided = true;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                pnl_side_sn.Customizable = false;
                pnl_side_datePrint.Customizable = false;
            }

            fill_Combo_orderBy();
            CBox_OrderBy.Text = "الرقم التسلسلي";
            CBox_SearchBy.SelectedIndex = 0;
            CBox_OrderBy.SelectedIndex = 0;
            CBox_SN_Compar.SelectedIndex = 0;
            CBox_PageCount.SelectedIndex = 6;

            Set_Font();


            //System.Drawing.Font menu_font = Program.GetCustomFont(Resources.Cairo_Medium, 8  , FontStyle.Regular);
            //dmAll_Cards.Font = menu_font;

            //rjPanel11.BackColor = UIAppearance.FormBorderColor;
            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            //rjPanel3.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;

            //this.Refresh();
            //dgv.AllowUserToOrderColumns = true;
            //System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.Cairo_Medium, 8, FontStyle.Bold);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = utils.Control_Mesur_DPI(41);

            //Date_From.Value = DateTime.Now.AddDays(-30); ;
            //Date_To.Value = DateTime.Now ;

            //lbl_byProfile.Font=rjLabel1.Font=rjLabel10.Font=rjLabel11.Font=rjLabel12.Font=rjLabel13.Font=rjLabel14.Font=rjLabel15.Font=
            //    rjLabel16.Font=rjLabel17.Font=rjLabel19.Font=rjLabel2.Font=rjLabel20.Font=rjLabel3.Font=rjLabel4.Font=rjLabel9.Font=

            //    CBox_SellingPoint.Font= CBox_Staus.Font=
            //    Program.GetCustomFont(Resources.Cairo_Regular,8,FontStyle.Regular);

            //rjLabel25Title.Font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);
            //btn_apply.Font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Bold);
            //lbl_Filter.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 8.0f, FontStyle.Regular);



        }
        private void Set_Font()
        {
            CBox_Batch.label.TextAlign = CBox_SN_Compar.label.TextAlign = ContentAlignment.MiddleRight;

            Date_From.RightToLeft =
            Date_To.RightToLeft =
            txtAllCountRows.RightToLeft =
            txtCurrentPageindex.RightToLeft =
            txtTotalPages.RightToLeft =
            txt_search.RightToLeft =
            txt_SN_End.RightToLeft =
            txt_SN_Start.RightToLeft =
            CBox_Profile.RightToLeft =
            CBox_Batch.RightToLeft =
            CBox_PageCount.RightToLeft =
            RightToLeft.No;
            
            Date_From.dateText.TextAlign = Date_To.dateText.TextAlign=
            CBox_PageCount.label.TextAlign=
            ContentAlignment.MiddleRight;

            rjLabel25Title.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12 , FontStyle.Bold);
            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;


            
            //System.Drawing.Font menu_font = Program.GetCustomFont(Resources.Cairo_Medium, 8, FontStyle.Regular);
            ////Font menu_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 9f, false);
            //dmAll_Cards.Font = menu_font;

            //System.Drawing.Font dgvHeader_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 9f, true);
           
            //btn_Filter.Font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 10f, true);
            btn_Filter.Font = btnRefresh_DB.Font = btn_RemoveFinsh.Font
                = btn_RemoveFinsh_All.Font
                = btn_RemoveFinsh_Download.Font
                = btn_RemoveFinsh_Validaty.Font
                = btn_RemoveFinsh_Uptime.Font
                = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f , FontStyle.Bold);
            rjButton1.Font = btn_apply.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f , FontStyle.Bold);

            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            //Font fnt = Program.GetCustomFont(Resources.Cairo_Regular, 8f, FontStyle.Regular);
            //Font fnt = CustomFonts.Get_Custom_Font("Cairo_Regular", 9f, false);

            rjLabel9.Font = rjLabel7.Font =
            lbl_Filter.Font = rjLabel14.Font = rjLabel15.Font = rjLabel3.Font =
            rjLabel2.Font = rjLabel17.Font =
            rjLabel20.Font = rjLabel1.Font =
            rjLabel16.Font = rjLabel12.Font =
            rjLabel25.Font = rjLabel12.Font =

            CheckBox_orderBy.Font = CheckBox_byDatePrint.Font = CheckBox_SN.Font =

             fnt;

            rjLabel1.Font = rjLabel13.Font = rjLabel4.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);

            CBox_OrderBy.Font = CBox_SearchBy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8f , FontStyle.Regular);


            lbl_note.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8f, FontStyle.Regular);
            lbl_note.ForeColor = Color.Red;
            if (UIAppearance.Theme == UITheme.Dark)
                lbl_note.ForeColor = utils.Dgv_DarkColor;

            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);

            utils.tollstrip_textSize(View_Hide_toolStripMenuItem);
            utils.tollstrip_textSize(تصديرالىملفنصيToolStripMenuItem);
            return;


            //    fnt;
            //btn_Batch_Cards_Title.Font = fnt;
            //btn_Finsh_Cards_Title.Font = fnt;
            //btn_Sessions_Cards_Title.Font = fnt;
            //btn_All_Cards_From_RB_Archive_Title.Font = fnt;




            //==========================================
            dgv.AllowUserToOrderColumns = true;
            dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = utils.Control_Mesur_DPI(40);

            dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);

            //Control_Loop(pnlClientArea);
            //foreach (Control ctr in this.Controls)
            //{
            //    if (ctr.GetType() == typeof(RJPanel) || ctr.GetType() == typeof(Panel))
            //    {
            //        Control_Loop(ctr);
            //    }
            //    else
            //    {
            //        RJLabel lbl = ctr as RJLabel;
            //        lbl.Font = new Font(lbl.Font.FontFamily, lbl.Font.Size , lbl.Font.Style);
            //    }

            //}
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }
        private void fill_Combo_orderBy()
        {
            try
            {
                //Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{

                //    comboSource.Add(column.Name, column.Name);
                //}
                //CBox_OrderBy.DataSource = new BindingSource(comboSource, null);
                //CBox_OrderBy.DisplayMember = "Value";
                //CBox_OrderBy.ValueMember = "Key";
                //CBox_OrderBy.SelectedIndex = 0;
                ////CBox_SearchBy.Text = "";

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("SN", "الرقم التسلسلي");
                comboSource.Add("UserName", "الاسم");
                comboSource.Add("Password", "كلمة المرور");
                comboSource.Add("BatchCardId", "الدفعة");
                comboSource.Add("ProfileName", "الباقة");
                comboSource.Add("SpCode", "نقطة البيع");
                comboSource.Add("UptimeLimit", "الوقت المسموح");
                comboSource.Add("TransferLimit", "التنزيل المسموح");
                comboSource.Add("UptimeUsed", "الوقت المستخدم");
                comboSource.Add("DownloadUsed+UploadUsed", "تحميل+رفع المستخدم");
                comboSource.Add("DownloadUsed", "التحميل المستخدم");
                comboSource.Add("UploadUsed", "الرفع المستخدم");
                comboSource.Add("ProfileTimeLeft", "الوقت المتبقي");
                comboSource.Add("ProfileTransferLeft", "التحميل المتبقي");
                comboSource.Add("Status", "الحالة");
                comboSource.Add("Price", "السعر");
                comboSource.Add("RegDate", "تاريخ الاضافة");
                comboSource.Add("CustomerName", "مستخدم يوزمنجر");
                comboSource.Add("Comment", "تعليق");
                comboSource.Add("CountSession", "عدد الجلسات");
                comboSource.Add("CountProfile", "عدد الباقات");

                CBox_OrderBy.DataSource = new BindingSource(comboSource, null);
                CBox_OrderBy.DisplayMember = "Value";
                CBox_OrderBy.ValueMember = "Key";
                CBox_OrderBy.SelectedIndex = 0;
                //CBox_OrderBy.Text = "";

            }
            catch { }

        }

        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if (fillter_type == "From_Finsh_Cards")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvHS_From_Finsh_Cards");
            else if (fillter_type == "From_Session")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvHS_From_Session");
            else if (fillter_type == "From_RB_Archive")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvHS_From_RB_Archive");
            else
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvUserHotspotPrcess");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                SaveFromState();
                return;
            }
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                SaveFromState();
                return;
            }
            dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv.Columns[dv.Index].Visible = dv.Visable;
                    dgv.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;//toolStripSeparator5
                    dgv.Columns[dv.Index].Width = dv.Width;
                    foreach (var control in View_Hide_toolStripMenuItem.DropDownItems)
                    //foreach (ToolStripMenuItem control in View_Hide_toolStripMenuItem.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            ToolStripMenuItem control1 = (ToolStripMenuItem)control;
                            if (control1.Tag != null)
                                if (control1.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control1.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { /*RJMessageBox.Show(ex.Message); */}
            }


            try { dgv.Columns["Status"].Visible = false; } catch { }
            //try { dgv.Columns["Id"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv.Columns["Disabled"].Visible = false; } catch { }
            //try { dgv.Columns["is_spPercentage"].Visible = false; } catch { }
            //try { dgv.Columns["spPercentage"].Visible = false; } catch { }
            //try { dgv.Columns["profilePercentage"].Visible = false; } catch { }

            //try { dgv.Columns["NasPortId"].Visible = false; } catch { }
            //try { dgv.Columns["SmartValidatiy_Add"].Visible = false; } catch { }
            //try { dgv.Columns["SmartValidatiy_ByDayOrHour"].Visible = false; } catch { }
            //try { dgv.Columns["SmartValidatiy_sizeSave"].Visible = false; } catch { }
            //try { dgv.Columns["SmartValidatiy_timeSave"].Visible = false; } catch { }
            //try { dgv.Columns["SmartValidatiy_sessionSave"].Visible = false; } catch { }
            //try { dgv.Columns["macAddress"].Visible = false; } catch { }
            //try { dgv.Columns["First_mac"].Visible = false; } catch { }
            try { dgv.Columns["CountProfile"].Visible = false; } catch { }
            try { dgv.Columns["CountSession"].Visible = false; } catch { }
            try { dgv.Columns["LastSynDb"].Visible = false; } catch { }
        }
        
        public void SaveFromState()
        {

            try
            {
                Dgv_State_list = new Dgv_Header_Proprties();
                dvalue = new Dictionary<int, Dgv_Header_Values>();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    Dgv_Header_Values dgv_Header_Values = new Dgv_Header_Values();
                    dgv_Header_Values.Visable = column.Visible;
                    dgv_Header_Values.HeaderText = column.HeaderText;
                    dgv_Header_Values.Name = column.Name;
                    dgv_Header_Values.DisplayIndex = column.DisplayIndex;
                    dgv_Header_Values.Index = column.Index;
                    dgv_Header_Values.Width = column.Width;

                    dvalue[column.Index] = dgv_Header_Values;
                }
                Dgv_State_list.items = dvalue;

                string formSetting = JsonConvert.SerializeObject(Dgv_State_list);

                if (fillter_type == "From_Finsh_Cards")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("DgvHS_From_Finsh_Cards", "SaveControlState", formSetting);
                else if (fillter_type == "Dgv_From_Session")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("DgvHS_From_Session", "SaveControlState", formSetting);
                else if (fillter_type == "From_RB_Archive")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("DgvHS_From_RB_Archive", "SaveControlState", formSetting);
                else
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("DgvUserHotspotPrcess", "SaveControlState", formSetting);


            }
            catch { }
        }
        List<CardsUserHotspot_Display_FromDB> users;


        public void loadData()
        {

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    LoadDataGridviewData();
                });
        }
        public void LoadDataGridviewData()
        {
            //From_Finsh_Cards
            if (fillter_type == "From_Finsh_Cards")
            {
                Get_Finsh_Cards();
                return;
            }
            LoadDataGridviewData2();
        }

        public void LoadDataGridviewData2()
        {
            List<HSUser> users = null;

            try
            {

                string sql_where = "";
                string sql_where_session = "";
                string sql_where_Pyment = "";
                string sql_select = "";
                string orderby = " ORDER BY ";

                if (fillter_type == "From_Server")
                {
                    sql_where = " where HSUser.DeleteFromServer = 0 ";
                    sql_where_session = " and  HsSession.DeleteFromServer = 0 ";
                    sql_where_Pyment = " and HsPyment.DeleteFromServer = 0 ";
                }
                if (fillter_type == "From_RB_Archive")
                {
                    sql_where = "  ";

                    if (ToggleButton_Show_Archive.Checked)
                    {
                        sql_where = " where HSUser.DeleteFromServer = 1 ";

                        sql_where_session = " and  HsSession.DeleteFromServer = 1 ";
                        sql_where_Pyment = " and HsPyment.DeleteFromServer = 1 ";
                    }
                    if (ToggleButton_Show_onlyServer.Checked)
                    {
                        sql_where = " where HSUser.DeleteFromServer = 0 ";

                        sql_where_session = " and  HsSession.DeleteFromServer = 0 ";
                        sql_where_Pyment = " and HsPyment.DeleteFromServer = 0 ";
                    }


                    //sql_where_session = "  ";
                    //sql_where_Pyment = "  ";
                }
                orderby = " ORDER BY SN DESC ";

                if (CBox_OrderBy.Text != "")
                {
                    orderby = $" ORDER BY {CBox_OrderBy.SelectedValue} ";

                    if (ToggleButton_ByCountSession.Checked)
                    {
                        firstLoad = true;
                        CBox_OrderBy.SelectedIndex = 19;
                        orderby = " ORDER BY count(HsSession.Fk_Sn_Name)  ";
                        firstLoad = false;
                    }
                    if (ToggleButton_ByCountProfile.Checked)
                    {
                        firstLoad = true;
                        CBox_OrderBy.SelectedIndex = 20;
                        orderby = " ORDER BY count(HsPyment.Fk_Sn_Name)  ";
                        firstLoad = false;
                    }

                    if (CheckBox_orderBy.Check)
                        orderby = orderby + " DESC ";
                }

                sql_where = Get_Fillter(sql_where);
              
                //sql_where_session = Get_Fillter(sql_where_session);
                //sql_where_Pyment = Get_Fillter(sql_where_Pyment);

                if (ToggleButton_ByCountSession.Checked)
                {
                    sql_select = $"select HSUser.*,count(HsSession.Fk_Sn_Name) as CountSession from HSUser  INNER JOIN HsSession  ON HSUser.Sn_Name = HsSession.Fk_Sn_Name  {sql_where} {sql_where_session} GROUP by HSUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    string c = $"WITH Get_Count AS( select HSUser.UserName,count(HsSession.Fk_Sn_Name) as CountSession from HSUser  INNER JOIN HsSession  ON HSUser.Sn_Name = HsSession.Fk_Sn_Name  {sql_where} {sql_where_session}  GROUP by HSUser.Sn_Name) SELECT count( UserName) FROM Get_Count ";
                    totalRows = (int)Local_DA.Get_int_FromDB(c);
                    //totalRows = db.Scalar<int>($"WITH Get_Count AS( select HSUser.UserName,count(HsSession.Fk_Sn_Name) as CountSession from HSUser  INNER JOIN HsSession  ON HSUser.Sn_Name = HsSession.Fk_Sn_Name  {sql_where} {sql_where_session}  GROUP by HSUser.Sn_Name) SELECT count( UserName) FROM Get_Count ");
                    //totalRows = db.Scalar<int>($"select count(HSUser.Sn_Name),count(HsSession.Fk_Sn_Name) as CountSession from HSUser  INNER JOIN HsSession  ON HSUser.Sn_Name = HsSession.Fk_Sn_Name  {sql_where}  GROUP by HSUser.Sn_Name {orderby} ");
                }
                else if (ToggleButton_ByCountProfile.Checked)
                {
                    sql_select = $"select HSUser.*,count(HsPyment.Fk_Sn_Name) as  CountProfile from HSUser INNER JOIN HsPyment ON HSUser.Sn_Name = HsPyment.Fk_Sn_Name  {sql_where} {sql_where_Pyment} GROUP by HSUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    //totalRows = db.Scalar<int>($"select count(HSUser.Sn_Name),count(HsPyment.Fk_Sn_Name) as CountProfile from HSUser  INNER JOIN HsPyment  ON HSUser.Fk_Sn_Name = HsPyment.Fk_Sn_Name  {sql_where}  GROUP by HSUser.Sn_Name  ");
                    string c = $"WITH Get_Count AS( select HSUser.UserName,count(HsPyment.Fk_Sn_Name) as CountProfile from HSUser  INNER JOIN HsPyment  ON HSUser.Sn_Name = HsPyment.Fk_Sn_Name {sql_where} {sql_where_Pyment}  GROUP by HSUser.Sn_Name) SELECT count( UserName) FROM Get_Count ";
                    totalRows = (int)Local_DA.Get_int_FromDB(c);

                }
                else if (ToggleButton_Show_Archive.Checked)
                {
                    sql_select = $"select HSUser.*,count(HsPyment.Fk_Sn_Name) as  CountProfile from HSUser INNER JOIN HsPyment ON HSUser.Sn_Name = HsPyment.Fk_Sn_Name  {sql_where} {sql_where_Pyment} GROUP by HSUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    //totalRows = db.Scalar<int>($"select count(HSUser.Sn_Name),count(HsPyment.Fk_Sn_Name) as CountProfile from HSUser  INNER JOIN HsPyment  ON HSUser.Fk_Sn_Name = HsPyment.Fk_Sn_Name  {sql_where}  GROUP by HSUser.Sn_Name  ");
                    string c = $"WITH Get_Count AS( select HSUser.UserName,count(HsPyment.Fk_Sn_Name) as CountProfile from HSUser  INNER JOIN HsPyment  ON HSUser.Sn_Name = HsPyment.Fk_Sn_Name {sql_where} {sql_where_Pyment}  GROUP by HSUser.Sn_Name) SELECT count( UserName) FROM Get_Count ";
                    totalRows = (int)Local_DA.Get_int_FromDB(c);

                }
                //select HSUser.*, count(HsPyment.Fk_Sn_Name) as CountProfile from HSUser LEFT JOIN HsPyment ON HSUser.Sn_Name = HsPyment.Fk_Sn_Name  where HSUser.DeleteFromServer = 0  GROUP by HsPyment.Fk_Sn_Name ORDER BY CountProfile DESC LIMIT 100 OFFSET 0;

                else
                {
                    sql_select = $"select * from HSUser  {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    string c = "select count(*) from HSUser  " + sql_where;
                    totalRows = (int)Local_DA.Get_int_FromDB(c);
                }

                //sql_select = $"select * from UmUser  {sql_where}  ORDER BY SN DESC LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";

                totalPages = (int)Math.Ceiling((double)totalRows / PageSize);
                txtTotalPages.Text = totalPages.ToString();
                txtCurrentPageindex.Text = currentPageindex.ToString();
                txtAllCountRows.Text = totalRows.ToString();
                try
                {
                    var umS = Local_DA.Load<HSUser>(sql_select);
                    //var umS = db.SqlList<UmUser>(sql_select);
                    users = umS;
                    //dgv.RightToLeft = RightToLeft.Yes;
                    //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }
                catch { }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }


            dgv.DataSource = users;

            loadDgvState();

            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }

            //dgv.Columns["CountSession"].Visible = false;


            if (ToggleButton_ByCountSession.Checked)
            {
                try { dgv.Columns["CountSession"].Width = 120; } catch { }
                try { dgv.Columns["CountSession"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["CountSession"].Visible = true; } catch { }
                try { dgv.Columns["CountProfile"].Visible = false; } catch { }


                CountSession_ToolStripMenuItem.Checked = true;
                //update_select_DGV();
            }
            else if (ToggleButton_ByCountProfile.Checked)
            {
                try { dgv.Columns["CountProfile"].Width = 120; } catch { }
                try { dgv.Columns["CountProfile"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["CountProfile"].Visible = true; } catch { }
                try { dgv.Columns["CountSession"].Visible = false; } catch { }

                Count_profile_ToolStripMenuItem.Checked = true;


            }
            else
            {
                //try
                //{
                //    if (dgv.Columns["CountSession"].Visible)
                //    {
                //        dgv.Columns["CountSession"].Visible = false;
                //        CountSession_ToolStripMenuItem.Checked = false;
                //    }
                //}
                //catch { }

                //try
                //{
                //    if (dgv.Columns["CountProfile"].Visible)
                //    {
                //        dgv.Columns["CountProfile"].Visible = false;
                //        Count_profile_ToolStripMenuItem.Checked = false;
                //    }
                //}
                //catch { }
                //CountSession_ToolStripMenuItem.Checked = false;
                //Count_profile_ToolStripMenuItem.Checked = false;

                ////dgv.Columns["CountSession"].Visible = false;
                //try { dgv.Columns["CountProfile"].Visible = false; } catch { }
                ////loadData();
            }

            update_select_DGV();

            disable_colum();
        }

        void disable_colum()
        {
            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv.Columns["Status"].Visible = false; } catch { }
            try { dgv.Columns["Disabled"].Visible = false; } catch { }
            try { dgv.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv.Columns["CallerMac"].Visible = false; } catch { }
            try { dgv.Columns["NasPortId"].Visible = false; } catch { }
            //try { dgv.Columns["Radius"].Visible = false; } catch { }

        }
        private string Get_Fillter(string sql_sorce = "")
        {
            string sql = sql_sorce;


            //string sql = "";
            if (txt_search.Text.Trim() != "")
            {
                //WHERE SALARY LIKE '%200%'
                if (CBox_SearchBy.SelectedIndex == 0)
                    sql += "and HSUser.UserName LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 1)
                    sql += "and HSUser.Password LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 2)
                    sql += "and HSUser.SN LIKE '%" + txt_search.Text + "%'  or  HSUser.SN LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 3)
                    sql += "and HSUser.SpName LIKE '%" + txt_search.Text + "%'  or  HSUser.SpCode LIKE '%" + txt_search.Text + "%' ";

            }

            if (CheckBox_UMProfile.Checked==false)
            {
                if (CBox_Profile.Text != "" && CBox_Profile.SelectedIndex != 0)
                {
                    sql += "and HSUser.ProfileName ='" + CBox_Profile.Text + "' ";
                    lbl_Filter.Text += "- الباقة ";
                    lbl_Filter.Visible = true;
                }
            }
            else
            {
                if (CBox_Profile_HotspotLocal.Text != "" && CBox_Profile_HotspotLocal.SelectedIndex != 0)
                {
                    sql += "and HSUser.ProfileName ='" + CBox_Profile_HotspotLocal.Text + "' ";
                    lbl_Filter.Text += "- الباقة ";
                    lbl_Filter.Visible = true;
                }
            }


            if (CBox_profile_Source_hotspot.Text != "" && CBox_profile_Source_hotspot.SelectedIndex != 0)
            {
                sql += "and HSUser.ProfileHotspot ='" + CBox_profile_Source_hotspot.Text + "' ";
                lbl_Filter.Text += "-بروفايل الهوتسبوت";
                lbl_Filter.Visible = true;
            }
            if (CBox_Server_hotspot.Text != "" && CBox_Server_hotspot.SelectedIndex != 0)
            {
                sql += "and HSUser.Server ='" + CBox_Server_hotspot.Text + "' ";
                lbl_Filter.Text += "-السيرفر";
                lbl_Filter.Visible = true;


            }
            //if (CBox_Profile.Text != "" && CBox_Profile.SelectedIndex != 0)
            //{
            //    sql += "and HSUser.ProfileName ='" + CBox_Profile.Text + "' ";
            //    lbl_Filter.Text += "- الباقة ";
            //    //lbl_Filter.Visible = true;
            //}
            if (CBox_SellingPoint.Text != "" && CBox_SellingPoint.SelectedIndex != 0)
            {
                sql += "and HSUser.SpCode ='" + CBox_SellingPoint.SelectedValue.ToString() + "' ";
                lbl_Filter.Text += "-نقطة البيع";
                //lbl_Filter.Visible = true;
            }
            if (CBox_Batch.Text != "" && CBox_Batch.SelectedIndex != 0)
            {
                sql += "and HSUser.BatchCardId =" + CBox_Batch.SelectedValue.ToString() + " ";
                lbl_Filter.Text += "-الدفعة";
                //lbl_Filter.Visible = true;
            }
            if (CBox_NumberPrint.Text != "" && CBox_NumberPrint.SelectedIndex != 0)
            {
                sql += "and HSUser.NumberPrint =" + CBox_NumberPrint.SelectedValue.ToString() + " ";
                lbl_Filter.Text += "-الطبعة";
                //lbl_Filter.Visible = true;
            }

            if (CheckBox_SN.Check && CBox_SN_Compar.Text != "")
            {
                //lbl_Filter.Visible = true;
                lbl_Filter.Text += "-التسلسلي";

                if (CBox_SN_Compar.Text.ToString() == "بين")
                {
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text) && utils.check_Filed_Intiger(txt_SN_End.Text))
                        sql += "and  (HSUser.Sn BETWEEN " + txt_SN_Start.Text + " and  " + txt_SN_End.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                }

                if (CBox_SN_Compar.Text.ToString() == "=")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (HSUser.Sn =" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                if (CBox_SN_Compar.Text.ToString() == ">")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (HSUser.Sn >" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                if (CBox_SN_Compar.Text.ToString() == "<")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (HSUser.Sn <" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
            }
            if (CBox_Staus.Text != "" && CBox_Staus.SelectedIndex != 0 && fillter_type != "From_Finsh_Cards")
            {
                sql += "and HSUser.Status =" + CBox_Staus.SelectedValue + " ";
                lbl_Filter.Text += "-الحالة";
                //lbl_Filter.Visible = true;
            }
            if (CBox_Didabled.Text != "" && CBox_Didabled.SelectedIndex != 0)
            {
                sql += "and HSUser.Disabled =" + CBox_Didabled.SelectedValue + " ";
                lbl_Filter.Text += "-الحالة";
                //lbl_Filter.Visible = true;
            }
            if (CheckBox_byDatePrint.Check)
            {
                //lbl_Filter.Visible = true;
                lbl_Filter.Text += " -تاريخ الطباعة";
                //WHERE date BETWEEN '2022-01-10 13:45:00' AND '2023-01-10 15:50:00';
                sql += "and HSUser.RegDate >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND HSUser.RegDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                //sql += " and RegDate BETWEEN '" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND '"+ Date_To.Value.ToString("yyyy-MM-dd")+"'  ";
            }
            //if (fillter_type != "From_Finsh_Cards")
            //{
            //sql += " ORDER BY SN DESC ";
            //}

            char[] charsToTrim1 = { 'a', 'n', 'd' };
            sql = sql.TrimStart();
            sql = sql.TrimStart(charsToTrim1);

            if (sql_sorce.Trim() == "" && sql.Trim() != "")
                sql = " where " + sql + " ";
            return sql;

        }

        private void Get_Finsh_Cards()
        {
            //0=waiting,1=active,2=all_finsh,3=open
            dgv.DataSource = null;
            List<HSUser_Finsh_Cards> users = null;
            string sql_where = " HSUser.DeleteFromServer = 0 ";
            string sql_select = " ";
            string orderby = "  ORDER BY SN DESC ";
            if (CBox_OrderBy.Text != "")
            {
                orderby = $" ORDER BY {CBox_OrderBy.SelectedValue} ";
                if (CheckBox_orderBy.Check)
                    orderby = orderby + " DESC ";
            }
            //string cond = $" (Status == 2  " +
            //              $"or  ((LimitUptime > 0 and UptimeUsed > 0) and ((LimitUptime - UptimeUsed <= 0)))" +
            //              $"or   ((Limitbytestotal > 0 and  DownloadUsed > 0) and ((Limitbytestotal - (UploadUsed + DownloadUsed )) <= 0)  ))";

            string cond = $" Status == 2 ";



            if (CBox_Staus.Text != "" && CBox_Staus.SelectedIndex != 0)
            {
                if (CBox_Staus.SelectedIndex == 1)
                    cond = "(Disabled == 1  ) ";
                else if (CBox_Staus.SelectedIndex == 2)
                    cond = " (  (LimitUptime > 0 and UptimeUsed > 0) and ((LimitUptime - UptimeUsed <= 0)) and Disabled = 0  ) ";
                else if (CBox_Staus.SelectedIndex == 3)
                    cond = " ( (Limitbytestotal > 0 and  DownloadUsed > 0) and ((Limitbytestotal - (UploadUsed + DownloadUsed )) <= 0) and Disabled = 0 )";
            }
            sql_where = Get_Fillter(sql_where);

          
            sql_select = $"select * from HSUser WHERE {cond} and {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
            string sql_select_count = $"select count(Sn_Name) from HSUser WHERE {cond} and {sql_where}  ";

            try
            {
                users = Local_DA.Load<HSUser_Finsh_Cards>(sql_select);

              
                //users = db.SqlList<UMUser_Finsh_Cards>(sql_select);
            }
            catch { }


            totalRows = (int)Local_DA.Get_int_FromDB(sql_select_count);
            totalPages = (int)Math.Ceiling((double)totalRows / PageSize);
            txtTotalPages.Text = totalPages.ToString();
            txtCurrentPageindex.Text = currentPageindex.ToString();
            txtAllCountRows.Text = totalRows.ToString();

            //users = db.SqlList<UMUser_Finsh_Cards>($"SELECT *  FROM UmUser  WHERE ( " +
            //    $"Status == 2  " +
            //    $"or  ((UptimeLimit > 0 and UptimeUsed > 0) and ((UptimeLimit - UptimeUsed <= 0)))" +
            //    $"or   ((TransferLimit > 0 and  DownloadUsed > 0) and ((TransferLimit - (UploadUsed + DownloadUsed )) <= 0)  )" +
            //         $")  and " +
            //         $" {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize}");
            //}
            
            try
            {

                dgv.DataSource = users;
                loadDgvState();
                dgv.Columns["Str_Status"].Visible = true;
                dgv.Columns["Str_Status"].Width = 180;
                dgv.Columns["Str_Status"].DisplayIndex = 0;
                //loadDgvState();

                update_select_DGV();
            }
            catch { }
            disable_colum();

        }

        private void update_header_DGV()
        {

            //dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

            loadDgvState();

            if (UIAppearance.Language_ar == false)
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    try
                    {
                        if (column.HeaderText == "SN") { column.HeaderText = "SN"; }
                        else if (column.HeaderText == "Str_Status") { column.HeaderText = "Status"; }

                        else if (column.HeaderText == "UserName") { column.HeaderText = "UserName"; }
                        else if (column.HeaderText == "Password") { column.HeaderText = "Password"; }
                        else if (column.HeaderText == "ProfileName") { column.HeaderText = "Profile"; }

                        else if (column.HeaderText == "Str_UptimeUsed") { column.HeaderText = "Uptime Used"; }
                        else if (column.HeaderText == "Str_Up_Down") { column.HeaderText = "Up Down"; }
                        else if (column.HeaderText == "Str_DownloadUsed") { column.HeaderText = "Download Used"; }
                        else if (column.HeaderText == "Str_UploadUsed") { column.HeaderText = "Upload Used"; }

                        else if (column.HeaderText == "price") { column.HeaderText = "Price"; }
                        else if (column.HeaderText == "Price_Display") { column.HeaderText = "Price Display"; }
                        else if (column.HeaderText == "Str_UptimeLimit") { column.HeaderText = "Uptime Limit"; }
                        else if (column.HeaderText == "Str_TransferLimit") { column.HeaderText = "Transfer Limit"; }

                        else if (column.HeaderText == "Str_ProfileTimeLeft") { column.HeaderText = "Profile TimeLeft"; }
                        else if (column.HeaderText == "ProfileTransferLeft") { column.HeaderText = "Profile TransferLeft"; }
                        else if (column.HeaderText == "Str_ProfileTillTime") { column.HeaderText = "تاريخ الانتهاء"; }

                        //else if (column.HeaderText == "CountProfile") { column.HeaderText = "Count Profile"; }
                        else if (column.HeaderText == "SpName") { column.HeaderText = "Selling Point"; }
                        else if (column.HeaderText == "BatchCardId") { column.HeaderText = "Bach No"; }
                        //else if (column.HeaderText == "CustomerName") { column.HeaderText = "Customer"; }
                        else if (column.HeaderText == "Comment") { column.HeaderText = "Comment"; }
                    }
                    catch { }
                }
            }
        }

        private void Get_Finsh_Cards22()
        {
            dgv.DataSource = null;
            List<HSUser> users = null;

            var um = Local_DA.Load<HSUser>("select * from HSUser where DeleteFromServer = 0");
            users = um;

            dgv.DataSource = users;
            update_select_DGV();
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();

            
            Get_Cbox_Profile_Hotspot_local();
            Get_Cbox_Profile_Source_hotspot();
            Get_SellingPoint();
            Get_Server_Hotspot();
            Get_Cbox_Profile();
            Get_Batch_cards();
            Get_Status();
            Get_Status_disabled();

            
            if (Filter_BatchCards != null)
            {
                try
                {
                    for (int i = 0; i < CBox_Batch.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_Batch.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_BatchCards.BatchNumber.ToString() == Value)
                        {
                            CBox_Batch.SelectedIndex = i;
                            CBox_Batch.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }
            if (Filter_NumberPrintCard != null)
            {
                try
                {
                    Get_NumberPrint(CBox_Batch.SelectedValue.ToString());
                    for (int i = 0; i < CBox_NumberPrint.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_NumberPrint.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_NumberPrintCard.NumberPrint.ToString() == Value)
                        {
                            CBox_NumberPrint.SelectedIndex = i;
                            CBox_NumberPrint.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }
            if (Filter_ProfileName != null)
            {
                try
                {
                    bool found=false;
                    CheckBox_UMProfile.Checked = true;
                    for (int i = 0; i < CBox_Profile.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_Profile.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_ProfileName == Value)
                        {
                            CBox_Profile.SelectedIndex = i;
                            CBox_Profile.Enabled = false;
                            CheckBox_UMProfile.Checked = false;
                            found = true;
                            break;
                        }
                    }
                    if (!found)
                    {
                        for (int i = 0; i < CBox_Profile_HotspotLocal.Items.Count; ++i)
                        {
                            var selectedItem = (KeyValuePair<string, string>)CBox_Profile_HotspotLocal.Items[i];
                            string text = selectedItem.Key;
                            string Value = selectedItem.Value;
                            if (Filter_ProfileName == Value)
                            {
                                CBox_Profile_HotspotLocal.SelectedIndex = i;
                                CBox_Profile_HotspotLocal.Enabled = false;
                                CheckBox_UMProfile.Checked = true;
                                found = true;
                                break;
                            }
                        }
                    }
                }
                catch { }
            }
            if (Filter_SpCode != null)
            {
                try
                {
                    for (int i = 0; i < CBox_SellingPoint.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_SellingPoint.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_SpCode == Value)
                        {
                            CBox_SellingPoint.SelectedIndex = i;
                            CBox_SellingPoint.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }


            LoadDataGridviewData();
            //loadDgvState();

            firstLoad = false;
        }

        private void FormAllCardsHotspot_Load(object sender, EventArgs e)
        {
            timer1.Start();


        }

        private void timer_SideBar_Tick(object sender, EventArgs e)
        {
            if (Hided)
            {
                Spanel.Width = Spanel.Width + 60;
                if (Spanel.Width >= PW)
                {
                    Spanel.Width = utils.Control_Mesur_DPI(280);
                    //Spanel.Width = 280;
                    timer_SideBar.Stop();
                    Hided = false;
                    this.Refresh();
                }

            }
            else
            {
                Spanel.Width = Spanel.Width - 60;
                if (Spanel.Width <= 0)
                {
                    timer_SideBar.Stop();
                    Hided = true;
                    this.Refresh();
                }
            }
        }

        private void btn_Collaps_Click(object sender, EventArgs e)
        {
            Spanel.Visible = !Spanel.Visible;
            if (Hided)
            {
                btn_Collaps.Text = "H\nI\nD\nD\nE";
            }
            else
            {
                btn_Collaps.Text = "S\nH\nO\nW";
            }
            timer_SideBar.Start();
        }

        private void FormAllCardsHotspot_FormClosing(object sender, FormClosingEventArgs e)
        {
            Global_Variable.StartThreadProcessFromMK = false;
            //SaveFromState();
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {

            if (e.RowIndex > -1) //not click  on header
            {

            }


        }
        private void DGV_User_Color()
        {
            return;
            //Thread.Sleep(2000);
            foreach (DataGridViewRow row in dgv.Rows)
            {
                try
                {
                    string Status = row.Cells["Status"].Value.ToString();
                    string disabled = row.Cells["disabled"].Value.ToString();

                    switch (Status)
                    {
                        case "0":
                            //row.DefaultCellStyle.ForeColor = Color.Black;
                            //row.DefaultCellStyle.BackColor = Color.White;
                            break;
                        case "1":
                            row.Cells["Str_Status"].Style.BackColor = Color.Red;
                            row.Cells["Str_Status"].Style.ForeColor = Color.White;
                            //row.DefaultCellStyle.BackColor = Color.Red;
                            //row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                        case "2":
                            row.Cells["Str_Status"].Style.BackColor = Color.Yellow;
                            row.Cells["Str_Status"].Style.ForeColor = Color.Black;

                            //row.DefaultCellStyle.BackColor = Color.Yellow;
                            //row.DefaultCellStyle.ForeColor = Color.Black;
                            break;
                        case "3":
                            row.Cells["Str_Status"].Style.BackColor = Color.Black;
                            row.Cells["Str_Status"].Style.ForeColor = Color.White;


                            //row.DefaultCellStyle.BackColor = Color.Black;
                            //row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                    }
                    if (disabled == "1")
                    {
                        row.Cells["Str_Status"].Style.BackColor = Color.Silver;
                        row.Cells["Str_Status"].Style.ForeColor = Color.White;

                    }
                }
                catch { }
            }
        }
        private void Get_Cbox_Profile_Hotspot_local()
        {
            try
            {
                var umProfil = new List<HSLocalProfile>();
                umProfil.Add(new HSLocalProfile { Id = 0, Name = "" });
                HSLocalProfile hotspot = new HSLocalProfile();
                umProfil.AddRange(hotspot.Ge_Local_Hotspot());

                CBox_Profile_HotspotLocal.DataSource = umProfil;
                CBox_Profile_HotspotLocal.DisplayMember = "Name";
                CBox_Profile_HotspotLocal.ValueMember = "Name";
                CBox_Profile_HotspotLocal.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }
        }
        private void Get_Cbox_Profile_Source_hotspot()
        {
            try
            {

                Hotspot_Source_Profile hotspot = new Hotspot_Source_Profile();
                List<Hotspot_Source_Profile> p = Global_Variable.Source_HS_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");

                if (p.Count > 0)
                    foreach (Hotspot_Source_Profile s in p)
                        comboSource.Add(s.Name, s.Name);

                CBox_profile_Source_hotspot.DataSource = new BindingSource(comboSource, null);
                CBox_profile_Source_hotspot.ValueMember = "Value";
                CBox_profile_Source_hotspot.DisplayMember = "Key";
                CBox_profile_Source_hotspot.Text = "";
            }
            catch { }
        }
        private void Get_Server_Hotspot()
        {
            try
            {
                CBox_Server_hotspot.Items.Clear();
                CBox_Server_hotspot.Items.Add("");
                CBox_Server_hotspot.Items.Add("all");
                CBox_Server_hotspot.SelectedIndex = 0;
                return;


            }
            catch { }

        }
        private void Get_Batch_cards()
        {
            try
            {

                try
                {
                    CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch(1);
                    CBox_Batch.ValueMember = "Value";
                    CBox_Batch.DisplayMember = "Key";
                    CBox_Batch.SelectedIndex = -1;
                    CBox_Batch.Text = "";
                    CBox_Batch.label.RightToLeft = RightToLeft.No;
                    CBox_Batch.label.RightToLeft = RightToLeft.No;
                    CBox_Batch.RightToLeft = RightToLeft.No;

                }
                catch { }


                ////CBox_Batch.Items.Clear();
                ////using (var db=dbFactory.Open())
                ////{
                ////Dictionary<int, string> Dict_batch = db.Dictionary<int, string>(db.From<BatchCard>().Select());
                ////var Dict_batch = db.Select<BatchCard>(a => a.Server == 1);
                //var Dict_batch = Smart_DA.Load<BatchCard>($"select * from BatchCard where Server=1 and Rb='{Global_Variable.Mk_resources.RB_code}'");
                //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
                //    comboSource.Add(0, "");


                //    foreach (BatchCard s in Dict_batch)
                //        comboSource.Add(s.BatchNumber, s.Str_Name);
                //    try
                //    {
                //        CBox_Batch.DataSource = new BindingSource(comboSource, null);
                //        CBox_Batch.ValueMember = "Key";
                //        CBox_Batch.DisplayMember = "Value";
                //        CBox_Batch.Text = "";
                //    }
                //    catch { }
                //    CBox_Batch.Text = "";
                ////}
                //return;
            }
            catch { }

        }
        private void Get_Cbox_Profile()
        {

            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }


        }

        private void Get_SellingPoint()
        {
            try
            {

                Smart_DataAccess da = new Smart_DataAccess();
                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";

            }
            catch { }
        }
        private void Get_Status()
        {
            try
            {
                //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();

                if (fillter_type == "From_Finsh_Cards")
                {
                    comboSource.Add("-1", "");
                    comboSource.Add("0", "منتهي الايام");
                    comboSource.Add("1", "منتهي الوقت");
                    comboSource.Add("2", "منتهي التحميل");
                }
                else
                {
                    comboSource.Add("-1", "");
                    comboSource.Add("0", "انتظار");
                    comboSource.Add("1", "نشط");
                    comboSource.Add("2", "منتهي");
                    //comboSource.Add("4", "معطل");
                    //comboSource.Add("5", "مفعل");
                    comboSource.Add("3", "مفتوح");
                    //comboSource.Add("4", "مؤرشف");
                }

                CBox_Staus.DataSource = new BindingSource(comboSource, null);
                CBox_Staus.DisplayMember = "Value";
                CBox_Staus.ValueMember = "Key";
                CBox_Staus.SelectedIndex = 0;
                CBox_Staus.Text = "";
            }
            catch { }


            //try
            //{

            //    //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    comboSource.Add(-1, "");
            //    comboSource.Add(0, "انتظار");
            //    comboSource.Add(1, "نشط");
            //    comboSource.Add(2, "منتهي");
            //    comboSource.Add(6, "معطل");
            //    comboSource.Add(3, "مفتوح");

            //    CBox_Staus.DataSource = new BindingSource(comboSource, null);
            //    CBox_Staus.DisplayMember = "Value";
            //    CBox_Staus.ValueMember = "Key";
            //    CBox_Staus.SelectedIndex = 0;
            //    CBox_Staus.Text = "";
            //}
            //catch { }
        }

        private void dgv_RowPrePaint(object sender, DataGridViewRowPrePaintEventArgs e)
        {

        }
        private void dgv_Users_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV();
        }
        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        private void Init_dgv_to_Default()
        {
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {

                    column.Visible = false;
                }
                //dgv.Columns["Sn"].Visible = true;
                dgv.Columns["Str_Status"].Visible = true;
                dgv.Columns["Str_Status"].DisplayIndex = 0;
                Status_ToolStripMenuItem.Checked = true;

                dgv.Columns["UserName"].Visible = true;
                dgv.Columns["UserName"].DisplayIndex = 1;
                dgv.Columns["UserName"].Width = utils.Control_Mesur_DPI(120);

                UserName_ToolStripMenuItem.Checked = true;

                dgv.Columns["Password"].Visible = true;
                dgv.Columns["Password"].DisplayIndex = 2;
                Password_ToolStripMenuItem.Checked = true;

                dgv.Columns["ProfileName"].Visible = true;
                dgv.Columns["ProfileName"].DisplayIndex = 3;
                dgv.Columns["ProfileName"].Width = utils.Control_Mesur_DPI(120);

                Profile_ToolStripMenuItem.Checked = true;


                dgv.Columns["Str_limitUptime"].Visible = true;
                dgv.Columns["Str_limitUptime"].DisplayIndex = 4;
                dgv.Columns["Str_limitUptime"].Width = utils.Control_Mesur_DPI(150);
                Str_limitUptime_ToolStripMenuItem.Checked = true;

                //dgv.Columns["Str_UptimeLimit"].Visible = true;
                //dgv.Columns["Str_UptimeLimit"].DisplayIndex = 4;
                //dgv.Columns["Str_UptimeLimit"].Width = 150;
                //Str_UptimeLimit_ToolStripMenuItem.Checked = true;

                dgv.Columns["Str_limitbytestotal"].Visible = true;
                dgv.Columns["Str_limitbytestotal"].DisplayIndex = 5;
                dgv.Columns["Str_limitbytestotal"].Width = utils.Control_Mesur_DPI(150);
                Str_limitbytestotal_ToolStripMenuItem.Checked = true;
                
                //dgv.Columns["Str_TransferLimit"].Visible = true;
                //dgv.Columns["Str_TransferLimit"].DisplayIndex = 5;
                //dgv.Columns["Str_TransferLimit"].Width = 150;
                //Str_TransferLimit_ToolStripMenuItem.Checked = true;


                dgv.Columns["Str_UptimeUsed"].Visible = true;
                dgv.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                dgv.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);
                Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                //dgv.Columns["Str_DownloadUsed"].Visible = true;
                dgv.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                //dgv.Columns["Str_UploadUsed"].Visible = true;
                dgv.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["Str_Up_Down"].Visible = true;
                dgv.Columns["Str_Up_Down"].DisplayIndex = 7;
                dgv.Columns["Str_Up_Down"].Width = utils.Control_Mesur_DPI(190);
                Str_Up_Down_ToolStripMenuItem.Checked = true;

                //dgv.Columns["MoneyTotal"].Visible = true;

                dgv.Columns["Str_ProfileTimeLeft"].Visible = true;
                dgv.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                dgv.Columns["Str_ProfileTimeLeft"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                dgv.Columns["Str_ProfileTransferLeft"].Visible = true;
                dgv.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                dgv.Columns["Str_ProfileTransferLeft"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                dgv.Columns["Str_ProfileTillTime"].Visible = true;
                dgv.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                dgv.Columns["Str_ProfileTillTime"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                dgv.Columns["LastSynDb"].Visible = false;
                dgv.Columns["LastSynDb"].DisplayIndex = 11;
                dgv.Columns["LastSynDb"].Width = utils.Control_Mesur_DPI(150);
                //dgv.Columns["SpName"].Visible = true;

                dgv.Columns["Descr"].Visible = false;
                dgv.Columns["Descr"].Width = utils.Control_Mesur_DPI(150);
                dgv.Columns["Descr"].DisplayIndex = 12;
                Descr_ToolStripMenuItem.Checked = false;

                dgv.Columns["Str_SmartValidatiy_Add"].Visible = true;
                dgv.Columns["Str_SmartValidatiy_Add"].Width = utils.Control_Mesur_DPI(150);
                dgv.Columns["Str_SmartValidatiy_Add"].DisplayIndex = 20;
                Str_SmartValidatiy_Add_ToolStripMenuItem.Checked = true;

                //dgv.Columns["CountProfile"].Visible = true;
                //try { dgv.Columns["Id"].Visible = false; } catch { }
                //try { dgv.Columns["Status "].Visible = false; } catch { }
                //try { dgv.Columns["Disabled "].Visible = false; } catch { }
                try { dgv.Columns["CountProfile"].Visible = false; } catch { }
                try { dgv.Columns["CountProfile"].Width = utils.Control_Mesur_DPI(150); } catch { }

                try { dgv.Columns["CountSession"].Visible = false; } catch { }
                try { dgv.Columns["CountSession"].Width = utils.Control_Mesur_DPI(150); } catch { }

            }
            catch { }

            //try
            //{
            //    foreach (DataGridViewColumn column in dgv.Columns)
            //    {

            //        column.Visible = false;
            //    }
            //    dgv.Columns["SN"].Visible = true;
            //    dgv.Columns["Str_Status"].Visible = true;
            //    dgv.Columns["UserName"].Visible = true;
            //    dgv.Columns["Password"].Visible = true;
            //    dgv.Columns["ProfileName"].Visible = true;
            //    //dgv.Columns["Str_price"].Visible = true;
            //    dgv.Columns["TotalPrice"].Visible = true;
            //    //dgv.Columns["Str_priceDisplay"].Visible = true;
            //    //dgv.Columns["ProfileHotspot"].Visible = true;
            //    dgv.Columns["SpName"].Visible = true;

            //    //dgv.Columns["NumberPrintedId"].Visible = true;

            //    dgv.Columns["Str_UptimeLimit"].Visible = true;
            //    dgv.Columns["Str_UptimeLimit"].Width = 150;
            //    dgv.Columns["Str_TransferLimit"].Visible = true;
            //    dgv.Columns["Str_TransferLimit"].Width = 150;
            //    //dgv.Columns[""].Visible = true;

            //    //dgv.Columns["Str_limitUptime"].Visible = true;
            //    dgv.Columns["Str_limitUptime"].Width = 150;
            //    //dgv.Columns["Str_limitbytestotal"].Visible = true;
            //    dgv.Columns["Str_limitbytestotal"].Width = 150;

            //    //dgv.Columns["Str_UptimeUsed"].Visible = true;
            //    //dgv.Columns["Str_DownloadUsed"].Visible = true;

            //    //dgv.Columns["Str_UploadUsed"].Visible = true;
            //    dgv.Columns["Str_Up_Down"].Visible = true;
            //    dgv.Columns["Str_Up_Down"].Width = 190;


            //    dgv.Columns["Str_ProfileTimeLeft"].Visible = true;
            //    dgv.Columns["Str_ProfileTimeLeft"].Width = 150;
            //    dgv.Columns["Str_ProfileTransferLeft"].Visible = true;
            //    dgv.Columns["Str_ProfileTransferLeft"].Width = 150;
            //    dgv.Columns["Str_ProfileTillTime"].Visible = true;
            //    dgv.Columns["Str_ProfileTillTime"].Width = 150;

            //    //dgv.Columns["comment"].Visible = true;
            //    //dgv.Columns["dt_RegDate"].Visible = true;
            //    //dgv.Columns["dt_FirstUse"].Visible = true;
            //    //dgv.Columns["dt_LastSeenAt"].Visible = true;
            //    //dgv.Columns["Str_ProfileTillTime"].Visible = true;
            //    dgv.Columns["descr"].Visible = true;
            //    //dgv.Columns["CountProfile"].Visible = true;
            //}
            //catch { }

        }

        private void btnRefresh_DB_Click(object sender, EventArgs e)
        {
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        [Obsolete]
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            //ThreadStart theprogress1 = new ThreadStart(() => Update_UserMan_Session());
            //Thread startprogress1 = new Thread(theprogress1);
            //startprogress1.Name = "Update Swssion";
            //startprogress1.Start();

            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سوف يقوم بجلب الكروت من الروتر قد ياخذ وقت في الجلب بحسب السرعه وكميه الكروت", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;


            Thread thread = new Thread(Refresh_formMK);
            Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();
        }

        [Obsolete]
        private void Refresh_formMK()
        {
            try
            {
                Global_Variable.Update_Um_StatusBar(false, true, 40, "", "يقوم الان بجلب كروت الهوتسبوت من النظام");
                Global_Variable.Source_Users_HotSpot = SourceCardsHotspot_fromMK.Get_HS_user();

                HsPyment hsPyment = new HsPyment();
                hsPyment.Syn_HS_Pyments_to_FirstUsers();

                Global_Variable.Update_Um_StatusBar(false, true, 70, "", "يتم  مزامنه كروت الهوتسبوت ");
                SourceCardsHotspot_fromMK hotSpotProcess = new SourceCardsHotspot_fromMK();
                hotSpotProcess.Syn_HS_Users_to_LocalDB();


                Global_Variable.Update_Um_StatusBar(false, true, 85, "", "يقوم الان بجلب ومزامنة جلسات الهوتسبوت من النظام");
                SourceCardsHotspot_fromMK.Get_HS_Session("", true, true);

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                  (MethodInvoker)delegate ()
                  {
                      LoadDataGridviewData();
                  });

                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب بيانات الهوتسبوت من النظام");
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false; }

            Global_Variable.StartThreadProcessFromMK = false;
        }


        private void Change_Items_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());

        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv.Columns[columnName].Visible = elemnt.Checked;
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
                SaveFromState();
            }
            catch { }
        }

        private void btn_Menu_Click(object sender, EventArgs e)
        {
            //dmAll_Cards.Show();
        }

        [Obsolete]
        private void btn_add_ScriptSmart_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله ");
                return;
            }
            Thread thread = new Thread(add_SmartScripts_To_users);
            Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();
        }

        [Obsolete]
        private void add_SmartScripts_To_users()
        {
            try
            {
                if (Global_Variable.Source_Users_HotSpot == null)
                {

                    return;
                }
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", "يتم الان تجهيز الكروت  ذات الصلاحيه القديمة");

                List<SourceCardsHotspot_fromMK> user = new List<SourceCardsHotspot_fromMK>();

                foreach (SourceCardsHotspot_fromMK hotspt in Global_Variable.Source_Users_HotSpot)
                {
                    SourceCardsHotspot_fromMK hs = hotspt;
                    string profile = hotspt.profileHotspot;
                    int Validity = 0;
                    int Price_Sales = 0;
                    int sp = 0;
                    int NumberPrint = 0;
                    string dateprint = "0";
                    string dateprint_comment_after = "0";
                    string byDayHour = "false";
                    string firstUse = "false";
                    string timeSave = "true";
                    string sizeSave = "true";
                    string sessionSave = "true";

                    string firstLogin = "0";
                    if (hotspt.comment.Contains("#") && hotspt.comment.Contains("%"))
                    {
                        try
                        {
                            string tmp = hotspt.comment.Replace("%", ";");
                            tmp = tmp.Replace("#", " ");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            dateprint = Convert.ToDateTime(split[0]).ToString("yyyy-MM-dd-hh-mm");
                            firstUse = split[1];
                        }
                        catch { }
                        try
                        {
                            if (hotspt.email.Contains("!"))
                            {

                                string tmp = hotspt.email.Replace("!", ";");
                                tmp = tmp.Replace("%", ";");
                                tmp = tmp.Replace("@", ";");
                                string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                                try { Validity = Convert.ToInt32(split[0]); } catch { };
                                try { Price_Sales = Convert.ToInt32(split[2]); } catch { };
                                profile = split[1];
                                //Price_Sales = split[2];
                                if (split[3] == "d.npdf")
                                    byDayHour = "true";
                            }
                        }
                        catch { }

                        string email =
                        profile + "'" +
                        Validity + "'" +
                        Price_Sales + "'" +
                        sp + "'" +
                        NumberPrint + "'" +
                        dateprint + "'"
                        + byDayHour + "'" +
                        firstUse + "'" +
                        timeSave + "'" +
                        sizeSave + "'" +
                        sessionSave + "'@smart.befor";

                        hs.email = email;
                        hs.comment = "";
                        user.Add(hs);
                    }


                    if (hotspt.comment.Contains("^") && hotspt.comment.Contains("*"))
                    {
                        try
                        {
                            string tmp = hotspt.comment.Replace("%", ";");
                            tmp = tmp.Replace("!", " ");
                            tmp = tmp.Replace("%", ";");
                            tmp = tmp.Replace("^", ";");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            firstLogin = utils.StringDatetimeToUnixTimeStamp(split[0]).ToString();
                            dateprint_comment_after = split[1];
                            dateprint = Convert.ToDateTime(split[1]).ToString("yyyy-MM-dd-hh-mm");
                        }
                        catch { }
                        try
                        {
                            string tmp = hotspt.email.Replace("!", ";");
                            tmp = tmp.Replace("%", ";");
                            tmp = tmp.Replace("@", ";");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            try { Validity = Convert.ToInt32(split[0]); } catch { };
                            try { Price_Sales = Convert.ToInt32(split[2]); } catch { };
                            profile = split[1];

                            if (split[3] == "d.npdf")
                                byDayHour = "true";
                        }
                        catch { }

                        string email =
                        profile + "'" +
                        Validity + "'" +
                        Price_Sales + "'" +
                        sp + "'" +
                        NumberPrint + "'" +
                        dateprint + "'"
                        + byDayHour + "'" +
                        firstUse + "'" +
                        timeSave + "'" +
                        sizeSave + "'" +
                        sessionSave + "'@smart.after";


                        hs.email = email;

                        if (Global_Variable.Mk_resources.version <= 6)
                            hs.comment = dateprint_comment_after;
                        else
                            hs.comment = Convert.ToDateTime(dateprint_comment_after).ToString("yyyy-MM-dd hh:mm:ss");


                        user.Add(hs);
                    }

                }
                if (user.Count <= 0)
                {
                    RJMessageBox.Show("لا يوجد كروت عليها اصدار الصلاحيات من النسخه السابقة");
                    return;
                }
                string result = Mk_DataAccess.Add_To_UserHotspot_SmartScript(user);
                Global_Variable.StartThreadProcessFromMK = false;
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", "تم العميلة");

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false;
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", " حدث مشكلة اثناء المعالجة");

            }

        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            return;
            if (txt_search.Text == "")
                LoadDataGridviewData();

            List<CardsUserHotspot_Display_FromDB> _filter = users.FindAll(x => x.UserName.Contains(txt_search.Text));

            if (users == null || users.Count == 0)
                dgv.DataSource = Global_Variable.Source_Users_HotSpot;
            else
            {
                dgv.DataSource = _filter;


                update_header_DGV();
                update_select_DGV();

            }

            this.Refresh();



        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            btn_Collaps_Click(sender, e);
            LoadDataGridviewData();
        }

        private string condition_detail_firstUse()
        {
            string byDatePrint = " ";
            string profile = " ";
            string addScriptSmart = " ";
            string sp = "";
            string batch = "";
            string numberPrint = "";
            string SN = "";
            string status = "";
            try
            {
                if (CheckBox_byDatePrint.Check || CBox_Profile_HotspotLocal.Text != "" || CBox_Staus.Text != "" || CBox_SellingPoint.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CheckBox_NoSmartScript.Check)
                {
                    //bool add_Where = false;
                    //conditon_date = " WHERE u.regDate >=" + From_DT + " AND u.regDate<=" + To_DT + "  ";

                    if (CheckBox_byDatePrint.Checked)
                    {
                        double From_DT = utils.DateTimeToUnixTimeStamp(Date_From.Value.Date.ToUniversalTime());
                        double To_DT = utils.DateTimeToUnixTimeStamp(Date_To.Value.Date.ToUniversalTime());
                        byDatePrint = "AND RegDate >=" + From_DT + " AND RegDate<=" + To_DT + "  ";
                        //add_Where=true;
                    }

                    if (CheckBox_NoSmartScript.Check)
                    {
                        //if (add_Where)
                        addScriptSmart = " AND  SmartValidatiy_Add=0";
                        //else
                        //    addScriptSmart = " WHERE  smartValidatiy_Add=0";
                        //add_Where = true;
                    }
                    if (CBox_Staus.SelectedIndex != 0 && CBox_Staus.SelectedIndex != -1 && CBox_Staus.Text != "")
                    {
                        addScriptSmart = " AND  Status=" + CBox_Staus.SelectedIndex + 1;
                    }

                    if (CBox_Profile_HotspotLocal.SelectedIndex != 0 && CBox_Profile_HotspotLocal.SelectedIndex != -1 && CBox_Profile_HotspotLocal.Text != "")
                    {
                        //if (add_Where)
                        profile = "AND ProfileName='" + CBox_Profile_HotspotLocal.Text.ToString() + "'  ";
                        //else
                        //    profile = "WHERE actualProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                        //add_Where = true;

                    }
                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    {
                        //if (add_Where)
                        sp = "AND SpId=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";
                        //else
                        //    sp = "WHERE spId=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";
                        //add_Where = true;

                    }
                    if (CBox_NumberPrint.Text != "" && CBox_NumberPrint.SelectedIndex != 0)
                    {
                        numberPrint += "and HsUser.NumberPrint =" + CBox_NumberPrint.SelectedValue.ToString() + " ";
                        lbl_Filter.Text += "-الطبعة";
                        //lbl_Filter.Visible = true;
                    }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    {
                        //if (add_Where) 
                        batch = "AND BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";
                        //else
                        //    batch = "WHERE numberPrintedId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                        //add_Where = true;

                    }
                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (check_Filed_Intiger(txt_SN_Start.Text) && check_Filed_Intiger(txt_SN_End.Text))
                                SN = " AND SN BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + " ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = " AND SN=" + txt_SN_Start.Text + " ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = " AND SN >=" + txt_SN_Start.Text + " ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (check_Filed_Intiger(txt_SN_Start.Text))
                                SN = " AND SN <=" + txt_SN_Start.Text + " ";
                    }


                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = byDatePrint + addScriptSmart + profile + sp + batch + numberPrint + SN + status;

            return conditon;
        }

        private bool check_Filed_Intiger(string ctrl)
        {
            double numberChik;
            //string nm=ctrl.Text;
            if (!(double.TryParse(ctrl, out numberChik)))
            {
                RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                return false;
            }
            return true;
        }

        private void dgv_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {


        }
        private static Random random = new Random();

        //Color myColor = ColorTranslator.FromHtml("#55ff55");
        private void dgv_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            int status = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Status"].Value);
            int disabled = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Disabled"].Value);
            int DeleteFromServer = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["DeleteFromServer"].Value);

            if (DeleteFromServer == 1)
            {
                //dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkRed;
                //dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.Red;
                //dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = myColor;
                dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
            }

            else if (this.dgv.Columns[e.ColumnIndex].Name == "Str_Status")
            {
                if (status == 1)
                {
                    e.CellStyle.BackColor = Color.Red;
                    e.CellStyle.ForeColor = Color.White;
                }
                else if (status == 2)
                {
                    e.CellStyle.BackColor = Color.Yellow;
                    e.CellStyle.ForeColor = Color.Black;
                    //row.DefaultCellStyle.ForeColor = Color.Yellow;

                }
                else if (status == 3)
                {
                    e.CellStyle.BackColor = Color.Black;
                    e.CellStyle.ForeColor = Color.White;
                    //row.DefaultCellStyle.ForeColor = Color.Black;

                }
                if (disabled == 1)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.Silver;

                    e.CellStyle.BackColor = Color.Silver;
                    e.CellStyle.ForeColor = Color.White;
                    //e.DefaultCellStyle.ForeColor = Color.Silver;
                }

            }
            return;

        }

        private void dgv_RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {

        }

        private void CheckBox_UMProfile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (CheckBox_UMProfile.Checked == false)
            {
                CheckBox_UMProfile.Text = "باقة يوزمنجر";
                //lbl_byProfile.Text = "باقة يوزمنجر";
                CBox_Profile.Visible = true;
                CBox_Profile_HotspotLocal.Visible = false;

            }
            else
            {
                CheckBox_UMProfile.Text = "باقه سمارت";
                //lbl_byProfile.Text = "باقه سمارت";
                CBox_Profile.Visible = false;
                CBox_Profile_HotspotLocal.Visible = true;


            }
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            btn_Collaps_Click(sender, e);
            //Spanel.Visible = !Spanel.Visible;
            //if (Hided)
            //{
            //    btn_Collaps.Text = "H\nI\nD\nD\nE";
            //}
            //else
            //{
            //    btn_Collaps.Text = "S\nH\nO\nW";
            //}
            //timer_SideBar.Start();
        }

        private void Restor_ColumnToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Init_dgv_to_Default();
        }

        private void نسخCtrlcToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }

        }

        private void تصديرالىملفاكسلToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CreateExcel();
        }
        void CreateExcel()
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "csv (*.csv)|*.csv";
            sfd.FileName = $"Hotspot_{DateTime.Now.ToString("yyyyMMddhhmmss")}.csv";
            //sfd.FileName = "users.txt";
            string FileName = sfd.FileName;

            if (sfd.ShowDialog() == DialogResult.OK)
            {
                FileName = sfd.FileName;
                if (File.Exists(FileName))
                {
                    try
                    {
                        File.Delete(FileName);
                    }
                    catch (IOException ex)
                    {
                        MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);

                        return;
                    }
                }
                FileStream fs = File.Create(FileName);
                fs.Close();

                string file_ExceltName = FileName;
                //file_ExceltName = FileName;
                try
                {
                    string[] outputCsv = new string[dgv.SelectedRows.Count];
                    //for (int i = 0; i < NewUser.Length; i++)
                    //{
                    //    outputCsv[i] += "=\"" + NewUser[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + Newpassword[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + profile + "\",";
                    //    outputCsv[i] += "=\"" + sp + "\",";
                    //}
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        HSUser um = (HSUser)dr.DataBoundItem;

                        string UserName = "";
                        string Password = "";
                        string ProfileName = "";
                        string SpCode = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            UserName = um.UserName.ToString();
                        if (!string.IsNullOrEmpty(um.Password))
                            Password = um.Password.ToString();
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            ProfileName = um.ProfileName.ToString();
                        if (!string.IsNullOrEmpty(um.SpCode))
                            SpCode = um.SpCode.ToString();




                        //outputCsv[i] += UserName + ",";
                        //outputCsv[i] += Password + ",";
                        //outputCsv[i] += ProfileName + ",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }


                        outputCsv[i] += $"=\"{UserName ?? ""}\",=\"{Password ?? ""}\",=\"{ProfileName ?? ""}\",=\"{SpCode ?? ""}\"";

                        //outputCsv[i] +="=\""+ UserName
                        //           + ",=\"" + Password
                        //           + "\"," + ProfileName
                        //           + "," + SpCode;
                        //           //+ "," + cm.OrigDocAmt.ToString()
                        //           //+ "," + cm.CreateDate.ToShortDateString();


                        //outputCsv[i] += $"\"{UserName ?? ""}\",";
                        //outputCsv[i] += $"\"{Password ?? ""}\",";
                        //outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        //outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] += $"\"{UserName ?? ""}\",";
                        //outputCsv[i] += $"\"{Password ?? ""}\",";
                        //outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        //outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] +=$" \"{UserName}\",";
                        //outputCsv[i] += $" \"{Password}\",";
                        //outputCsv[i] += $" \"{ProfileName}\",";
                        //outputCsv[i] += $" \"{SpCode}\",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        i = i + 1;
                    }

                    //for (int i = 1; (i - 1) < dgvUserManager.Rows.Count; i++)
                    //{
                    //    for (int j = 0; j < columnCount; j++)
                    //    {
                    //        outputCsv[i] += dgvUserManager.Rows[i - 1].Cells[j].Value.ToString() + ",";
                    //    }
                    //}

                    File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                    //MessageBox.Show("Data Exported Successfully !!!", "Info");

                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                    }
                    catch { }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("export_execl :" + ex.Message);
                }



            }
        }

        void CreateExcel1()
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "csv (*.csv)|*.csv";
            string FileName = sfd.FileName = $"Hotspot_{DateTime.Now.ToString("yyyyMMddhhmmss")}.csv";
            if (sfd.ShowDialog() == DialogResult.OK)
            {
                if (File.Exists(FileName))
                {
                    try
                    {
                        File.Delete(FileName);
                    }
                    catch (IOException ex)
                    {
                        MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);
                        return;
                    }
                }
                FileStream fs = File.Create(FileName);
                fs.Close();

                string file_ExceltName = FileName;



                var sb = new StringBuilder();

                var headers = dgv.Columns.Cast<DataGridViewColumn>();
                sb.AppendLine(string.Join(",", headers.Select(column => "\"" + column.HeaderText + "\"").ToArray()));

                foreach (DataGridViewRow row in dgv.Rows)
                {
                    var cells = row.Cells.Cast<DataGridViewCell>();
                    sb.AppendLine(string.Join(",", cells.Select(cell => "\"" + cell.Value + "\"").ToArray()));
                }
                string textBoxExport = sb.ToString();

                System.IO.File.WriteAllText(file_ExceltName, textBoxExport, Encoding.UTF8);

                try
                {
                    System.Diagnostics.Process.Start(file_ExceltName);
                }
                catch { }
                return;







                //file_ExceltName = FileName;
                try
                {
                    string[] outputCsv = new string[dgv.SelectedRows.Count];
                    //for (int i = 0; i < NewUser.Length; i++)
                    //{
                    //    outputCsv[i] += "=\"" + NewUser[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + Newpassword[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + profile + "\",";
                    //    outputCsv[i] += "=\"" + sp + "\",";
                    //}
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        HSUser um = (HSUser)dr.DataBoundItem;

                        string UserName = "";
                        string Password = "";
                        string ProfileName = "";
                        string SpCode = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            UserName = um.UserName.ToString();
                        if (!string.IsNullOrEmpty(um.Password))
                            Password = um.Password.ToString();
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            ProfileName = um.ProfileName.ToString();
                        if (!string.IsNullOrEmpty(um.SpCode))
                            SpCode = um.SpCode.ToString();




                        //outputCsv[i] += UserName + ",";
                        //outputCsv[i] += Password + ",";
                        //outputCsv[i] += ProfileName + ",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        outputCsv[i] += $"\"{UserName ?? ""}\",";
                        outputCsv[i] += $"\"{Password ?? ""}\",";
                        outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] +=$" \"{UserName}\",";
                        //outputCsv[i] += $" \"{Password}\",";
                        //outputCsv[i] += $" \"{ProfileName}\",";
                        //outputCsv[i] += $" \"{SpCode}\",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        i = i + 1;
                    }

                    //for (int i = 1; (i - 1) < dgvUserManager.Rows.Count; i++)
                    //{
                    //    for (int j = 0; j < columnCount; j++)
                    //    {
                    //        outputCsv[i] += dgvUserManager.Rows[i - 1].Cells[j].Value.ToString() + ",";
                    //    }
                    //}

                    File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                    //MessageBox.Show("Data Exported Successfully !!!", "Info");

                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                    }
                    catch { }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("export_execl :" + ex.Message);
                }



            }
        }

        private void تصديرالاسمفقطToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name");
        }

        private void تصديرالاسمكلمةالمرورToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass");
        }

        private void تصديرالاسمكلمةالمرورالباقةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass_profile");
        }

        private void تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass_profile_sp");
        }

        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }
        void export_to_text(string choice)
        {
            if (dgv.SelectedRows.Count <= 0)
            {
                MessageBox.Show("لم تقم بتحديد كروت لتصديرها");
                return;
            }
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "txt (*.txt)|*.txt";
                //string FileName = "";
                sfd.FileName = $"Hotspot_{DateTime.Now.ToString("yyyyMMddhhmmss")}.txt";
                //sfd.FileName = "users.txt";
                string FileName = sfd.FileName;
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    FileName = sfd.FileName;
                    if (File.Exists(FileName))
                    {
                        try
                        {
                            File.Delete(FileName);
                        }
                        catch (IOException ex)
                        {
                            MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);
                        }
                    }
                    FileStream fs = File.Create(FileName);
                    fs.Close();
                    TextWriter writeFile = new StreamWriter(FileName, false, Encoding.UTF8);
                    writeFile.Close();
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        HSUser um = (HSUser)dr.DataBoundItem;
                        string text = "";
                        //string name = UM.UserName + " ";
                        //string pass = UM.Password + " ";
                        //string profile = UM.ProfileName + " ";
                        //string sp = UM.SpCode+ " ";

                        string name = "";
                        string pass = "";
                        string profile = "";
                        string sp = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            name = um.UserName.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.Password))
                            pass = um.Password.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            profile = um.ProfileName.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.SpCode))
                            sp = um.SpCode.ToString() + " ";


                        if (choice == "name")
                            text += name;
                        if (choice == "name_pass")
                            text += name + pass;
                        if (choice == "name_pass_profile")
                            text += name + pass + profile;
                        if (choice == "name_pass_profile_sp")
                            text += name + pass + profile + sp;

                        //text += text + "\n";
                        //try { text += dgv.Rows[i].Cells["location"].Value.ToString() + " "; } catch { }

                        File.AppendAllText(FileName, text + "\n", Encoding.UTF8);

                        i = i + 1;
                    }
                    MessageBox.Show("تم التصدير بنجاح");
                    try
                    {
                        System.Diagnostics.Process.Start(FileName);
                    }
                    catch { }
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show(ex.Message.ToString());
            }
        }
        public bool isProcessRun = false;
        string typeProcess = "DeleteFromServer";
        bool is_Delete_FromArchive = false;

        [Obsolete]
        public void ProccessCards_ByID()
        {
            isProcessRun = true;
            string msg = "";
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<HSUser> dbUser = new HashSet<HSUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((HSUser)row.DataBoundItem);
                        _DataGridViewRow.Add(row);
                    }
                }

                if (dbUser.Count <= 0)
                {
                    isProcessRun = false;
                    is_Delete_FromArchive = false;
                    RJMessageBox.Show("حدد كروت الروتر");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");

                    return;
                }

                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<HSUser> ResUsers = mk_DataAccess.Process_Hotspot_ByID(dbUser, typeProcess);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " حدث خطا عن تنفيذ العملية");
                    isProcessRun = false;
                    is_Delete_FromArchive = false;
                    //RJMessageBox.Show("تمت الم");
                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count)
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في تنفيذ العملية علي بعض الكروت قم بتحديث الكروت من الروتر");

                    //else
                    //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Local_DA.Process_Cards_OnDB("HSUser", dbUser, typeProcess);

                    foreach (var itm in _DataGridViewRow)
                    {
                        if (typeProcess == "Disabled")
                        {
                            msg = "تعطيل";
                            itm.Cells["Disabled"].Value = 1;
                        }
                        else if (typeProcess == "Enabled")
                        {
                            msg = "تفعيل";
                            itm.Cells["Disabled"].Value = 0;
                        }
                        else if (typeProcess == "DeleteFromServer")
                        {
                            msg = "حذف";
                            itm.Cells["DeleteFromServer"].Value = 1;
                        }
                        else if (typeProcess == "RestCards")
                        {
                            itm.Cells["UptimeUsed"].Value = 0;
                            itm.Cells["UploadUsed"].Value = 0;
                            itm.Cells["DownloadUsed"].Value = 0;
                            itm.Cells["Status"].Value = 0;
                        }
                        else if (typeProcess == "caller_bind")
                        {
                            msg = "ربط ماك ";
                            itm.Cells["CallerMac"].Value = "bind";
                        }
                        else if (typeProcess == "Remove_caller_bind")
                        {
                            msg = "الغاء ربط ماك";
                            itm.Cells["CallerMac"].Value = "";
                        }
                    }


                    if (is_Delete_FromArchive)
                    {
                        lock (Sql_DataAccess.Lock_localDB)
                        {
                            int delete = Local_DA.Remove_UmUser_FormDB(dbUser, "HSUser");
                        }
                    }

                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                       (System.Windows.Forms.MethodInvoker)delegate ()
                       {
                           dgv.Refresh();
                       });
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم {msg} {ResUsers.Count}  كرت من الهوتسبوت");
                    //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");
                    //loadData();
                }

                isProcessRun = false;
                is_Delete_FromArchive = false;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; }

        }

        [Obsolete]
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }

            DialogResult result = RJMessageBox.Show("هل متاكد من حذف الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            typeProcess = "DeleteFromServer";
            //isProcessRun = true;
            ThreadStart theprogress;
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف الكروت المحدده من الهوتسبوت");
            //if (dgv.SelectedRows.Count <= 20)
            theprogress = new ThreadStart(() => ProccessCards_ByID());
            //else
            //    theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        [Obsolete]
        private void btnDisable_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تعطيل الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "Disabled";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتعطيل الكروت المحدده من الهوتسبوت");
            ThreadStart theprogress;
            theprogress = new ThreadStart(() => ProccessCards_ByID());
            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        [Obsolete]
        private void btnEnable_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تفعيل الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }

            typeProcess = "Enabled";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتفعيل الكروت المحدده من الهوتسبوت");
            ThreadStart theprogress;
            theprogress = new ThreadStart(() => ProccessCards_ByID());
            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        [Obsolete]
        private void حذفالكروتالمحددةمنالارشيفToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("  لن تظهر تقارير واحصائيات الكروت المحذوه من الارشيف والتي ليست موجوده في الروتر \nهل متاكد من حذف الكروت المحدده من الارشيف", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            HashSet<HSUser> dbUser = new HashSet<HSUser>();
            List<DataGridViewRow> rw = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                dbUser.Add((HSUser)row.DataBoundItem);
                rw.Add(row);
            }
            int delete = 0;
            lock (Sql_DataAccess.Lock_localDB)
            {
                delete = Local_DA.Remove_UmUser_FormDB(dbUser, "HSUser");
            }
            if (delete > 0)
            {
                RJMessageBox.Show("تم حذف الكروت من الارشيف");
                LoadDataGridviewData();
            }
        }

        [Obsolete]
        private void حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem_Click(object sender, EventArgs e)
        {
            is_Delete_FromArchive = true;
            btnDelete_Click(sender, e);
        }

        private void حذفجلساتالكروتالمحددةToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void تصفيرعدادالكروتالمحددةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تصفير عداد الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "RestCards";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتصفير عداد الكروت المحدده من الهوتسبوت");
            ThreadStart theprogress;
            theprogress = new ThreadStart(() => ProccessCards_ByID());
            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        [Obsolete]
        private void الغاءربطالكروتباولجهازاستخدامToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من  الغاء ربط الماك من  الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "Remove_caller_bind";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان الغاء ربط الماك من الكروت المحدده من الهوتسبوت");
            ThreadStart theprogress;
            theprogress = new ThreadStart(() => ProccessCards_ByID());
            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        private void طباعةالكروتالمحددةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Form_PrintForm frm = new Form_PrintForm();
            frm.ServerType = "HSUser";
            frm.dgv = (dgv.SelectedRows);
            frm.ShowDialog();
        }

        private void اعادةشحنالكروتالمحددةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            AddProfile(sender, e);
            //if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            //{
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يتم اضافة رصيد  الي الكروت المحدده");


            //    //using (Form_WaitForm frm = new Form_WaitForm(AddProfile))
            //    //{
            //    //    frm.ShowDialog();
            //    //}
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم اضافة الرصيد الي الكروت المحدده  ");
            //}
        }
        private void AddProfile(object sender, EventArgs e)
        {
            HashSet<string> list_user = new HashSet<string>();
            HashSet<HSUser> dbUser = new HashSet<HSUser>();
            List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                {
                    list_user.Add(row.Cells["IdHX"].Value.ToString());
                    dbUser.Add((HSUser)row.DataBoundItem);
                    _DataGridViewRow.Add(row);
                }
            }
            if (list_user.Count == 0)
            {
                RJMessageBox.Show("حدد كروت الروتر");
                return;
            }
            Form_Add_Profile_Balance_HS frm = new Form_Add_Profile_Balance_HS(dbUser);
            frm.ShowDialog();

            if (frm.success == true)
            {

                foreach (var itm in _DataGridViewRow)
                {
                    itm.Cells["ProfileName"].Value = frm.CBox_Profile_UserMan.Text;

                    if (Convert.ToInt16(itm.Cells["Status"].Value) == 2)
                    {
                        itm.Cells["Status"].Value = 1;
                    }
                    else if (Convert.ToInt16(itm.Cells["Status"].Value) == 3)
                    {
                        itm.Cells["Status"].Value = 0;
                    }
                }
                dgv.Refresh();

                if (frm.Sccess_WthiRefersh)
                {
                    btnRefresh_DB_Click(sender, e);
                    LoadDataGridviewData();
                    //return;
                }

            }

        }



        private void تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //DialogResult result = RJMessageBox.Show("هل متاكد من تغير نقطة بيع الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            //if (result == DialogResult.No)
            //    return;


            if (fillter_type == "From_RB_Archive")
            {
                RJMessageBox.Show("سيتم تجاهل الكروت المأرشفة");
            }


            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتعديل نقطة البيع للكروت المحدده");

            Change_SellingPoint();
            //using (Form_WaitForm frm = new Form_WaitForm(Change_SellingPoint))
            //{
            //    frm.ShowDialog();
            //}
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم تعديل نقطة البيع للكروت المحدده ");
        }

        private void Change_SellingPoint()
        {
            HashSet<string> list_user = new HashSet<string>();
            HashSet<HSUser> dbUser = new HashSet<HSUser>();
            List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                {
                    list_user.Add(row.Cells["IdHX"].Value.ToString());
                    dbUser.Add((HSUser)row.DataBoundItem);
                    _DataGridViewRow.Add(row);
                }
            }

            Form_Change_Cards_SellingPoint frm = new Form_Change_Cards_SellingPoint();
            frm.HSUsers = dbUser;

            frm.ShowDialog();

            if (frm.is_success == true)
            {
                foreach (var itm in _DataGridViewRow)
                {
                    //itm.Cells["SpCode"].Value = frm.CBox_SellingPoint.SelectedValue.ToString();
                    itm.Cells["SpName"].Value = frm.CBox_SellingPoint.Text;
                }

            }
            dgv.Refresh();
        }

        private void Remove_SP_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("هل متاكد من حذف نقطة البيع من الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;


            HashSet<string> list_user = new HashSet<string>();
            HashSet<HSUser> dbUser = new HashSet<HSUser>();
            List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                {
                    list_user.Add(row.Cells["IdHX"].Value.ToString());
                    var um = (HSUser)row.DataBoundItem;
                    um.SpCode = null;
                    um.SpName = null;
                    dbUser.Add(um);
                    _DataGridViewRow.Add(row);
                }
            }

            lock (Smart_DataAccess.Lock_object)
            {
                Smart_DataAccess smart_dataAccess = new Smart_DataAccess();
                if (smart_dataAccess.Change_Selling_Point<HSUser>("HSUser",dbUser, "", "", true))
                {
                    foreach (var itm in _DataGridViewRow)
                    {
                        itm.Cells["SpName"].Value = null;
                        //itm.Cells["SpCode"].Value = null;
                    }

                }
            }
            dgv.Refresh();
        }

        [Obsolete]
        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
                get_card_detail();
        }
        [Obsolete]
        private void get_card_detail()
        {
            foreach (DataGridViewRow dr in dgv.SelectedRows)
            {
                HSUser user = dr.DataBoundItem as HSUser;
                Form_CardsDetailsHS form_CardsDetails = new Form_CardsDetailsHS(user, fillter_type);
                form_CardsDetails.ShowDialog();

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                       (System.Windows.Forms.MethodInvoker)delegate ()
                       {
                           dgv.Refresh();
                       });
                return;
            }
        }

        private void btn_RemoveFinsh_Validaty_Click(object sender, EventArgs e)
        {
            btn_Collaps_Click(sender, e);
        }

        private void CBox_OrderBy_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void CheckBox_orderBy_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            Cursor.Current = Cursors.WaitCursor;
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
            Spanel.Visible = false;
            Hided = true;
            //btn_Collaps_Click(sender, e);

            ////Spanel.Visible = false;
            //Spanel.Width = 0;
            //Hided=true;
            //btn_Collaps.Text = "S\nH\nO\nW";
            ////if (Hided)
            ////{
            ////    btn_Collaps.Text = "H\nI\nD\nD\nE";
            ////}
            ////else
            ////{
            ////    btn_Collaps.Text = "S\nH\nO\nW";
            ////}
            ////timer_SideBar.Start();

            Cursor.Current = Cursors.Default;
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            currentPageindex = totalPages;
            loadData();
            txtCurrentPageindex.Text = currentPageindex.ToString();
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if (currentPageindex < totalPages)
            {
                currentPageindex++;
                loadData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (currentPageindex > 1)
            {
                currentPageindex--;
                loadData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            currentPageindex = 1;
            loadData();
            txtCurrentPageindex.Text = currentPageindex.ToString();

        }

        private void CBox_PageCount_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            //if(CBox_PageCount.SelectedIndex != 0)
            PageSize = Convert.ToInt32(CBox_PageCount.Text);
            //if (PageSize > totalRows)
            //{
            //    PageSize = totalRows;
            //    currentPageindex = 1;
            //    totalPages = 0;
            //    totalRows = 0;
            //}
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
        }

        private void ToggleButton_ByCountProfile_CheckedChanged(object sender, EventArgs e)
        {

            if (firstLoad)
                return;

            if (ToggleButton_ByCountProfile.Checked == false && ToggleButton_ByCountSession.Checked == false)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                firstLoad = false;
            }

            if ((ToggleButton_ByCountProfile.Checked))
            {
                if (ToggleButton_ByCountSession.Checked)
                {
                    firstLoad = true;
                    ToggleButton_ByCountSession.Checked = false;



                    firstLoad = false;
                }


                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }
            else
            {

                
                try
                {
                    if (dgv.Columns["CountProfile"].Visible)
                    {
                        dgv.Columns["CountProfile"].Visible = false;
                        Count_profile_ToolStripMenuItem.Checked = false;
                    }
                }
                catch { }

                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();

            }

            btn_Collaps_Click(sender, e);
        }

        private void ToggleButton_ByCountSession_CheckedChanged(object sender, EventArgs e)
        {

            if (firstLoad)
                return;
            if (ToggleButton_ByCountProfile.Checked == false && ToggleButton_ByCountSession.Checked == false)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                firstLoad = false;
            }

            if ((ToggleButton_ByCountSession.Checked))
            {
                if (ToggleButton_ByCountProfile.Checked)
                {
                    firstLoad = true;
                    ToggleButton_ByCountProfile.Checked = false;
                    firstLoad = false;
                }

                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }
            else
            {
                try
                {
                    if (dgv.Columns["CountSession"].Visible)
                    {
                        dgv.Columns["CountSession"].Visible = false;
                        CountSession_ToolStripMenuItem.Checked = false;
                    }
                }
                catch { }


                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }

            btn_Collaps_Click(sender, e);

        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            firstLoad = true;
            CBox_Profile.SelectedIndex = -1;
            CBox_Profile.Text = "";

            CBox_Staus.SelectedIndex = -1;
            CBox_Staus.Text = "";

            CBox_SearchBy.SelectedIndex = -1;
            txt_search.Text = "";

            CBox_Batch.SelectedIndex = -1;
            CBox_Batch.Text = "";

            CBox_NumberPrint.SelectedIndex = -1;
            CBox_NumberPrint.Text = "";

            CBox_Profile_HotspotLocal.SelectedIndex = -1;
            CBox_Profile_HotspotLocal.Text = "";

            CBox_Server_hotspot.SelectedIndex = -1;
            CBox_Server_hotspot.Text = "";

            CBox_Didabled.SelectedIndex = -1;
            CBox_Didabled.Text = "";

            CBox_SellingPoint.SelectedIndex = -1;
            CBox_SellingPoint.Text = "";

            CheckBox_byDatePrint.Check = false;
            CheckBox_SN.Check = false;

            if (ToggleButton_ByCountProfile.Checked)
            {
                firstLoad = true;
                ToggleButton_ByCountProfile.Checked = false;
                firstLoad = false;
            }
            if (ToggleButton_ByCountSession.Checked)
            {
                firstLoad = true;
                ToggleButton_ByCountSession.Checked = false;
                firstLoad = false;
            }
            if (ToggleButton_Show_Archive.Checked)
            {
                firstLoad = true;
                ToggleButton_Show_Archive.Checked = false;
                firstLoad = false;
            }


            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
            btn_Collaps_Click(sender, e);

            firstLoad = false;
        }

        private void CBox_Batch_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            Get_NumberPrint(CBox_Batch.SelectedValue.ToString());
            loadData();


            Cursor.Current = Cursors.Default;
        }
        private void Get_Status_disabled()
        {
            try
            {

                //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("-1", "");
                comboSource.Add("1", "معطل");
                comboSource.Add("0", "مفعل");

                CBox_Didabled.DataSource = new BindingSource(comboSource, null);
                CBox_Didabled.DisplayMember = "Value";
                CBox_Didabled.ValueMember = "Key";
                CBox_Didabled.SelectedIndex = 0;
                CBox_Didabled.Text = "";
            }
            catch { }
        }
        private void Get_NumberPrint(string BatchNumber)
        {
            try
            {
                CBox_NumberPrint.DataSource = Smart_DA.Get_BindingSource_Number_Print(1, BatchNumber);
                CBox_NumberPrint.ValueMember = "Value";
                CBox_NumberPrint.DisplayMember = "Key";
                CBox_NumberPrint.SelectedIndex = -1;
                CBox_NumberPrint.Text = "";
                CBox_NumberPrint.label.RightToLeft = RightToLeft.No;
                CBox_NumberPrint.label.RightToLeft = RightToLeft.No;
                CBox_NumberPrint.RightToLeft = RightToLeft.No;

            }
            catch { }
        }

        [Obsolete]
        private void btn_RemoveFinsh_All_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Users_HotSpot == null || Global_Variable.Source_Users_HotSpot.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب الكروت من الروتر قم بجلب الكروت من الروتر كي يتم مزامنتها وعرض تقارير عنها", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
                return;
            }
            RJMessageBox.Show("سوف يتم حذف جميع الكروت المنتهية من الروتر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinsh_All_Cards))
                    fRM.ShowDialog();
            }
            return;
        }

        [Obsolete]
        private void btn_RemoveFinsh_Uptime_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Users_HotSpot == null || Global_Variable.Source_Users_HotSpot.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب الكروت من الروتر قم بجلب الكروت من الروتر كي يتم مزامنتها وعرض تقارير عنها", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
                return;
            }
            RJMessageBox.Show("سوف يتم حذف جميع الكروت منتيهة الوقت المحدد لها من الروتر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinsh_Uptime_Cards))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        private void btn_RemoveFinsh_Download_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Users_HotSpot == null || Global_Variable.Source_Users_HotSpot.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب الكروت من الروتر قم بجلب الكروت من الروتر كي يتم مزامنتها وعرض تقارير عنها", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
                return;
            }
            RJMessageBox.Show("سوف يتم حذف جميع الكروت منتيهة التحميل والاستهلاك المحدد لها من الروتر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinsh_Download_Cards))
                    fRM.ShowDialog();

            }
        }

        [Obsolete]
        private void btn_RemoveFinsh_Validaty_Click_1(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Users_HotSpot == null || Global_Variable.Source_Users_HotSpot.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب الكروت من الروتر قم بجلب الكروت من الروتر كي يتم مزامنتها وعرض تقارير عنها", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
                return;
            }
            RJMessageBox.Show("سوف يتم حذف جميع الكروت منتيهة الصلاحية المحدد لها او المعطلات من الروتر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinsh_Validaty_Cards))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        void RemoveFinsh_All_Cards()
        {
            string script = @"/ip hotspot user remove [find where disabled=yes || uptime >= $""limit-uptime"" || $""bytes-in""+$""bytes-out"" >= $""limit-bytes-total""  ]; " + "\n";

            if (Global_Variable.Mk_resources.version >= 7)
                script = @"/ip/hotspot/user remove [find where disabled=yes || uptime >= $""limit-uptime"" || $""bytes-in""+$""bytes-out"" >= $""limit-bytes-total""  ]; " + "\n";
            
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
            if (res["status"] == "false")
            {
                RJMessageBox.Show("Erorr\n" + res["result"]);
                return;
            }
            RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات");
        }

        [Obsolete]
        void RemoveFinsh_Uptime_Cards()
        {
            string script = @"{:foreach i in [ /ip hotspot user find ] do={:local limitUptime  [ /ip hotspot user get $i  limit-uptime ]; :local uptime  [/ip hotspot user get $i  uptime]; :if (  $uptime   >=  $limitUptime ) do {[/ip hotspot user remove $i];}}} " + "\n";

            if (Global_Variable.Mk_resources.version >= 7)
                script = @"{:foreach i in [ /ip/hotspot/user find ] do={:local limitUptime  [ /ip/hotspot/user get $i  limit-uptime ];:local uptime  [/ip/hotspot/user get $i  uptime]; :if (  $uptime   >=  $limitUptime ) do {[/ip/hotspot/user remove $i];}}} " + "\n";
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
            if (res["status"] == "false")
            {
                RJMessageBox.Show("Erorr\n" + res["result"]);
                return;
            }
            RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات");
        }

        [Obsolete]
        void RemoveFinsh_Download_Cards()
        {
            string script = @"{:foreach i in=[ /ip hotspot user find] do={:local bin [ /ip hotspot user get $i bytes-in];:local bout [ /ip hotspot user get $i bytes-out];:local bt [ /ip hotspot user get $i limit-bytes-total];:if ( ($bin + $bout) >= $bt ) do={ [/ip hotspot user remove $i];}}} " + "\n";
            if (Global_Variable.Mk_resources.version >= 7)
                script = @"{:foreach i in=[ /ip/hotspot/user find] do={:local bin [ /ip/hotspot/user get $i bytes-in];:local bout [ /ip/hotspot/user get $i bytes-out];:local bt [ /ip/hotspot/user get $i limit-bytes-total];:if ( ($bin + $bout) >= $bt ) do={ [/ip/hotspot/user remove $i];}}} " + "\n";

            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
            if (res["status"] == "false")
            {
                RJMessageBox.Show("Erorr\n" + res["result"]);
                return;
            }
            RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات");
        }

        [Obsolete]
        void RemoveFinsh_Validaty_Cards()
        {
            string script = @"/ip hotspot user remove [find where disabled=yes ]; " + "\n";
            if (Global_Variable.Mk_resources.version >= 7)
                script = @"/ip/hotspot/user remove [find where disabled=yes ]; " + "\n";
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
            if (res["status"] == "false")
            {
                RJMessageBox.Show("Erorr\n" + res["result"]);
                return;
            }
            RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات");
        }

        private void SaveDGVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveFromState();
        }

        private void ToggleButton_Show_Archive_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (ToggleButton_Show_Archive.Checked == true )
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                ToggleButton_Show_onlyServer.Checked = false;
                firstLoad = false;
            }

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
            btn_Collaps_Click(sender, e);
        }

        [Obsolete]
        private void toolStripMenuItem2_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count > 0)
                get_card_detail();

        }

        private void rjButton3_Click(object sender, EventArgs e)
        {
            btn_Collaps_Click(sender, e);
        }

        private void ToggleButton_Show_onlyServer_CheckedChanged(object sender, EventArgs e)
        {

            if (firstLoad)
                return;
            if (ToggleButton_Show_onlyServer.Checked == true)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                ToggleButton_Show_Archive.Checked = false;
                firstLoad = false;
            }

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
            btn_Collaps_Click(sender, e);
        }
    }
}
