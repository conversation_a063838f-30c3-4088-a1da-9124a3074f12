﻿using Dapper;
using SmartCreator.Data;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SQLite;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using tik4net;
using SmartCreator.ViewModels;

namespace SmartCreator.Models
{
    public class SourceCardsUserManager_fromDB
    { 
         
        #region fileds
        public int id { get; set; }
        //[DisplayName("التسلسل")]

        public double sn { get; set; }
        public string idHX { get; set; }
        public double custId { get; set; }
        public string cusName { get; set; }
        public string userName { get; set; }
        public string password { get; set; } = "";
        public int    disabled { get; set; } = 0;
        public double? regDate { get; set; }
        public double firstUse { get; set; } // اول تسجيل دخول
        public string firstName { get; set; } = "";
        public string lastName { get; set; } = "";
        public string descr { get; set; } = string.Empty; //comment
        public string phone { get; set; } = "";
        public string location { get; set; } = "";
        public string email { get; set; } = "";
        public string callerId { get; set; } = "";
        public double uptimeLimit { get; set; } = 0;//from profile الوقت المحدد للكرت
        public double downloadLimit { get; set; } = 0; 
        public double uploadLimit { get; set; } = 0; 
        public double transferLimit { get; set; } = 0;
        public double uptimeUsed { get; set; } = 0;
        //public string uptimeUsed_str { get; set; } 
        public double downloadUsed { get; set; } = 0;
        public string downloadUsed_str { get; set; } 
        public double uploadUsed { get; set; } = 0;
        //public string uploadUsed_str { get; set; } = "0";
        public double lastSeenAt { get; set; } = 0; 
        public string activeSessions { get; set; } 
        public string active { get; set; } 
        public string sharedUsers { get; set; } 
        public int moneyPaid { get; set; }=0; 
        public int moneyUsed { get; set; }=0; 
        public double moneyPercentage { get; set; }=0;
        public double moneyTotal { get; set; }=0; 
        public double discount { get; set; }=0; 
        public double actualLimDownload { get; set; }=0; 
        public double actualLimUpload { get; set; }=0; 
        public double actualLimTransfer { get; set; }=0; 
        public double actualLimUptime { get; set; }=0; 
        public int actualProfileId { get; set; }=0; 
        public double actualProfileStart { get; set; }
        public double actualProfileEnd { get; set; }
        public string actualProfileName { get; set; } = "";
        public double profileTillTime { get; set; }
        public double profileTimeLeft { get; set; }
        public double profileTransferLeft { get; set; }
        public int countProfile { get; set; }
        public int? spId { get; set; }   
        public int is_spPercentage { get; set; }   
        public string spName { get; set; } = "";
        public double spPercentage { get; set; } = 0;
        public int? numberPrintedId { get; set; }
        public string sn_userName { get; set; }
        public int Delet_fromServer { get; set; } = 0;
        public string mkId { get; set; } 
        public string radius { get; set; } 
        public string nasPortId { get; set; } 
        public double? date_added_Localdb { get; set; }
        public int status { get; set; }

        //public static List<SourceCardsUserManager_fromDB> Get_UsersManager_from_DB()
        //{
        //   List<SourceCardsUserManager_fromDB> user= SqlDataAccess.GetUsersManager_By_PageFilter();
        //}
        #endregion
    }

    public class CardsUserManagerFromDB
    {
        private int id;
        private string idHX;
        private double sn;
        private string cusName;
        private string userName;
        private string password = "";
        private int disabled = 0;
        private double regDate;
        private DateTime? _regDate;
        private string descr;
        private string callerId = "";
        private double uptimeLimit = 0;
 
        private double transferLimit = 0;
        //private string str_transferLimit = "";
        private double uptimeUsed = 0;
        //private string str_uptimeUsed = "";
        private double downloadUsed = 0;
        //private string str_downloadUsed = "";
        private double uploadUsed = 0;
        //private string str_uploadUsed = "";
        //private string str_Up_Down = "";
        private double lastSeenAt = 0;
        //private DateTime? _lastSeenAt;
        private int activeSessions;
        private double moneyTotal = 0;
        private string actualProfileName = "";
        private double profileTillTime;
        //private DateTime? _profileTillTime;
        private double profileTimeLeft;
        private double profileTransferLeft;
        private int countProfile;
        private int? spId;
        private string spName = "";
        private int numberPrintedId;
        private string sn_userName;
        private int delet_fromServer = 0;
        private int status;

        private double firstUse = 0;
        private double profileValiday = 0;
        private string radius;
        private string nasPortId;


        #region fileds

        [DisplayName("التسلسل")]
        public double Sn { get => sn; set => sn = value; }
        [DisplayName("الحالة")]
        public string Str_Status
        {
            get
            {
                if (activeSessions == 1)
                    return "نشط + اونلاين";
                string s = (status == 0 ? "انتظار" : (status == 1 ? "نشط" : (status == 2 ? "منتهي" : (status == 3 ? "خطأ في الباقة" : ""))));
               if (disabled==1)
                    return s +(" + معطل");
                return s;
            }
        }
        [DisplayName("الاسم")]
        public string UserName { get => userName; set => userName = value; }
        [DisplayName("كلمة المرور")]
        public string Password { get => password; set => password = value; }
        [DisplayName("المبلغ")]
        public string Str_MoneyTotal
        {
            get
            {
                //try { row.Cells["moneyTotal"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["moneyTotal"].Value)); } catch { row.Cells["moneyTotal"].Value = 0; };

                return String.Format("{0:n0}", moneyTotal);
            }
        }
        [DisplayName("السعر")]
        public double MoneyTotal { get => moneyTotal; set => moneyTotal = value; }
        [DisplayName("الباقة")]
        public string ActualProfileName { get => actualProfileName; set => actualProfileName = value; }
        [DisplayName("عدد الباقات")]
        public int CountProfile { get => countProfile; set => countProfile = value; }
        public int? SpId { get => spId; set => spId = value; }
        [DisplayName("نقطة البيع")]
        public string SpName { get => spName; set => spName = value; }
        [DisplayName("الدفعه")]
        public int NumberPrintedId { get => numberPrintedId; set => numberPrintedId = value; }
        [DisplayName("الوقت المسموح")]
        public string Str_UptimeLimit
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptimeLimit);
            }

        }
        [DisplayName("التنزيل المسموح")]
        public string Str_TransferLimit
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(transferLimit.ToString());
                else
                    return utils.ConvertSize_Get_En(transferLimit.ToString());
            }
        }
        [DisplayName("الوقت المستخدم")]
        public string Str_UptimeUsed
        {
            get
            { 
                return utils.Get_Seconds_By_clock_Mode(uptimeUsed);
            }
        }
        [DisplayName("التحميل المستخدم")]
        public string Str_DownloadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(downloadUsed.ToString());
                else return utils.ConvertSize_Get_En(downloadUsed.ToString());

            }
        }
        [DisplayName("الرقع المستخدم")]
        public string Str_UploadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(uploadUsed.ToString());
                else return utils.ConvertSize_Get_En(uploadUsed.ToString());

            }
        }
        //[DisplayName("تحميل+رفع")]
        [DisplayName("التحميل+الرفع المستخدم")]

        public string Str_Up_Down
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic((uploadUsed + downloadUsed).ToString());
                else return utils.ConvertSize_Get_En((uploadUsed + downloadUsed).ToString());

            }
        }
        [DisplayName("تاريخ الاضافة")]
        public DateTime? dt_RegDate
        {
            get
            {
                if (RegDate > 0)
                    return utils.UnixTimeStampToDateTime(regDate);
                else return null;
            }
        }
        [DisplayName("اخر ضهور")]
        public DateTime? dt_LastSeenAt
        {
            get
            {
                if (lastSeenAt > 0)
                    return utils.UnixTimeStampToDateTime(lastSeenAt);
                else return null;
            }
        }
        [DisplayName("اول دخول")]
        public DateTime? dt_FirstUse
        {
            get
            {
                if (firstUse > 0)
                    return utils.UnixTimeStampToDateTime(firstUse);
                else return null;
            }
        }
        [DisplayName("تاريخ الانتهاء")]
        public DateTime? Str_ProfileTillTime
        {
            get
            {
                if (firstUse > 0 && profileValiday > 0 )
                    return utils.UnixTimeStampToDateTime(firstUse + profileValiday);
                else return null;
            }
        }
        [DisplayName("الوقت المتبقي")]
        public string Str_ProfileTimeLeft
        {
            get
            {
                if (uptimeLimit == 0) return "غير معروف";
                double time = Math.Max(0, (uptimeLimit - uptimeUsed));
                return utils.Get_Seconds_By_clock_Mode(time);
            }
        }
        [DisplayName("التحميل المتبقي")]
        public string Str_ProfileTransferLeft
        {
            get
            {if (transferLimit==0) return "غير معروف";
                double size = Math.Max(0, (transferLimit - (uploadUsed + downloadUsed)));
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic_with_ZeroByte(size.ToString());
                else return utils.ConvertSize_Get_En_with_ZeroByte(size.ToString());
            }
        }
        [DisplayName("تعليق")]
        public string Descr { get => descr; set => descr = value; } //comment
        [DisplayName("عميل اليوزمنجر")]
        public string CusName { get => cusName; set => cusName = value; }
        public int Status { get => status; set => status = value; }

        public int Id { get => id; set => id = value; }
        public string IdHX { get => idHX; set => idHX = value; }
        public string Sn_userName { get => sn_userName; set => sn_userName = value; }
        public int Delet_fromServer { get => delet_fromServer; set => delet_fromServer = value; }
        public double ProfileTillTime { get => profileTillTime; set => profileTillTime = value; }
        public double ProfileTimeLeft { get => profileTimeLeft; set => profileTimeLeft = value; }
        public double ProfileTransferLeft { get => profileTransferLeft; set => profileTransferLeft = value; }
        public double ProfileValiday { get => profileValiday; set => profileValiday = value; }
        public double FirstUse { get => firstUse; set => firstUse = value; }
        public string CallerId { get => callerId; set => callerId = value; }
        public double UptimeLimit { get => uptimeLimit; set => uptimeLimit = value; }
        public double TransferLimit { get => transferLimit; set => transferLimit = value; }
        public double UptimeUsed { get => uptimeUsed; set => uptimeUsed = value; } //public string uptimeUsed_str { get; set; } 
        public double DownloadUsed { get => downloadUsed; set => downloadUsed = value; }
        public double UploadUsed { get => uploadUsed; set => uploadUsed = value; } //public string uploadUsed_str { get; set; } = "0";
        public double LastSeenAt { get => lastSeenAt; set => lastSeenAt = value; }
        public int Disabled { get => disabled; set => disabled = value; }
        public double RegDate { get => regDate; set => regDate = value; }
        public int ActiveSessions { get => activeSessions; set => activeSessions = value; }
        [DisplayName("الراديوس")]

        public string Radius { get => radius; set => radius = value; }
        [DisplayName("الجهاز")]

        public string NasPortId { get => nasPortId; set => nasPortId = value; }


        //public double custId { get; set; }
        //public string FirstName { get; set; } = "";
        //public string lastName { get; set; } = "";
        //public string phone { get; set; } = "";
        //public string location { get; set; } = "";
        //public string email { get; set; } = "";
        //public double downloadLimit { get; set; } = 0;
        //public double uploadLimit { get; set; } = 0;
        //public string downloadUsed_str { get; set; }

        //public string active { get; set; }
        //public string sharedUsers { get; set; }

        //public int moneyPaid { get; set; } = 0;
        //public int moneyUsed { get; set; } = 0;
        //public double moneyPercentage { get; set; } = 0;
        //public double discount { get; set; } = 0;
        //public double actualLimDownload { get; set; } = 0;
        //public double actualLimUpload { get; set; } = 0;
        //public double actualLimTransfer { get; set; } = 0;
        //public double actualLimUptime { get; set; } = 0;
        //public int actualProfileId { get; set; } = 0;
        //public double actualProfileStart { get; set; }
        //public double actualProfileEnd { get; set; }
        //public int is_spPercentage { get; set; }
        //public double spPercentage { get; set; } = 0;
        //public string mkId { get; set; }
        //public double date_added_Localdb { get; set; }


        //private string status2 { get; set; }

        //public string Status2
        //{
        //    get { return "ooooo"; }
        //    set { status2 = value; }
        //}

 
        public List<CardsUserManagerFromDB> Get_UsersManager_from_DB()
        {
            List<CardsUserManagerFromDB> user = SqlDataAccess.GetUsersManager_By_PageFilter2();
            return user;
        }

 
        #endregion
    }
    public class UsermanagerOnly_Name_NotDeleteFromServer
    {
        public string Name { get; set; }

    }
  
    public class Dgv_Header_Values
    {
        public string HeaderText { get; set; } = "id";
        public string Name { get; set; } = "id";
        public int Index { get ; set ; }= 0;
        public int DisplayIndex { get ; set ; }= 0;
        public int Width { get ; set ; } = 100;
        public bool Visable { get ; set ; } = false;
        //public int ColumnWidth { get ; set ; }
    }
     
    public class Dgv_Header_Proprties
    {
        //public List<Dgv_Header_Values> items { get; set; }=new List<Dgv_Header_Values>();
        public Dictionary<int, Dgv_Header_Values> items { get; set; } = new Dictionary<int, Dgv_Header_Values>();
    }

    public class SourceCardsUserManager_fromMK
    {
        private string password1 = null;

        #region fileds
        public string id { get; set; }
        public string cusName { get; set; }
        public string userName { get; set; }
        public string password { get => password1; set => password1 = value; }
        public string disabled { get; set; } 
        public string RegDate { get; set; }
        public string firstName { get; set; }
        public string lastName { get; set; }
        public string descr { get; set; } //comment
        public string phone { get; set; }
        public string location { get; set; }
        public string email { get; set; } = "";
        public string callerId { get; set; } = "";
        public string uptimeUsed { get; set; }
        public string downloadUsed { get; set; } = "0";
        public string uploadUsed { get; set; } = "0";
        public string lastSeenAt { get; set; } 
        public string activeSessions { get; set; }= "0";
        public string active { get; set; }
        public string sharedUsers { get; set; }
        public string group { get; set; } = "default";
        public string attributes { get; set; }
        public string actualProfileName { get; set; } = "";
        public string radius { get; set; }
        public string nasPortId { get; set; }

        #endregion
        ITikConnection connection { get; set; }

        [Obsolete]
        public static List<SourceCardsUserManager_fromMK> Get_UM_user(string name="", bool searchByID = true, bool is_syn = false)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = new List<SourceCardsUserManager_fromMK>();
            Global_Variable.Source_Users_UserManager_ForPrint = new HashSet<string>();
            try
            {

                string code = Properties.Settings.Default.userman_print;
                string KeyName = "username";

                if (Global_Variable.Mk_resources.version >= 7)
                {
                    KeyName = "name";
                    code = Properties.Settings.Default.userman7_print;
                }

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                    {
                        if (searchByID)
                            loadCmd = connection.CreateCommandAndParameters(code, ".id", name);
                        else
                            loadCmd = connection.CreateCommandAndParameters(code, KeyName, name);
                    }
                 
                    var response = loadCmd.ExecuteList();
                    users = Get_Response_UmUsers(response);

                    stopwatch.Stop();
                    //string ss =
                    //   (
                    //     (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString()) +
                    //      " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                    //     " : " + (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString())
                    //   );
                    string ss =
                       (
                         (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                          " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                         " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                       );

                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب كروت اليوزمنجر", "");

                    //int totalSeconds = (int)stopwatch.Elapsed.TotalSeconds;
                    //int seconds = totalSeconds % 60;
                    //int minutes = totalSeconds / 60;
                    //string time = minutes + ":" + seconds;


                    if (users.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_UM_Users_to_LocalDB(users);
                        }
                    }
                    return users;
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده البيانات او تاكد من مساحة ذاكرة الروتر");
                //else
                //    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;
        }
        [Obsolete]
        public  List<SourceCardsUserManager_fromMK> Get_UM_user2(string name = "", bool searchByID = true, bool is_syn = false)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = new List<SourceCardsUserManager_fromMK>();
            Global_Variable.Source_Users_UserManager_ForPrint = new HashSet<string>();
            try
            {
                string code = Properties.Settings.Default.userman_print;
                string KeyName = "username";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    KeyName = "name";
                    code = Properties.Settings.Default.userman7_print;
                }

                using ( connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                    {
                        if (searchByID)
                            loadCmd = connection.CreateCommandAndParameters(code, ".id", name);
                        else
                            loadCmd = connection.CreateCommandAndParameters(code, KeyName, name);
                    }

                    var response = loadCmd.ExecuteList();
                    users = Get_Response_UmUsers(response);

                    stopwatch.Stop();
                    string ss =
                        (


                          (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                           " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                          " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                        );
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب كروت اليوزمنجر", "");

                    //int totalSeconds = (int)stopwatch.Elapsed.TotalSeconds;
                    //int seconds = totalSeconds % 60;
                    //int minutes = totalSeconds / 60;
                    //string time = minutes + ":" + seconds;


                    if (users.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_UM_Users_to_LocalDB(users);
                        }
                    }
                    return users;
                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                //else
                //    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;
        }

        public void MkConnClose()
        {
            Mk_DataAccess.Mk_Conn_Close(this.connection);
        }
        [Obsolete]
        public static List<SourceCardsUserManager_fromMK> Get_UM_user22()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = null;
            Global_Variable.Source_Users_UserManager_ForPrint = new HashSet<string>();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/tool/user-manager/user/print";
                if (Global_Variable.Mk_resources.version >= 7)
                    code = "/user-manager/user/print";

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    users = new List<SourceCardsUserManager_fromMK>();

                    //var loadCmd = connection.CreateCommandAndParameters(code, "nextid", "*956D");
                    var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", null);
                            card.userName = item.GetResponseFieldOrDefault("username", "");
                            card.password = item.GetResponseFieldOrDefault("password", null);
                            card.actualProfileName = item.GetResponseFieldOrDefault("actual-profile", null);
                            card.uptimeUsed = item.GetResponseFieldOrDefault("uptime-used", "0");
                            card.downloadUsed = (item.GetResponseFieldOrDefault("download-used", "0"));
                            card.uploadUsed = (item.GetResponseFieldOrDefault("upload-used", "0"));
                            card.cusName = item.GetResponseFieldOrDefault("customer", "");
                            card.lastSeenAt = item.GetResponseFieldOrDefault("last-seen", null);
                            card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "1");
                            card.location = item.GetResponseFieldOrDefault("location", null);
                            card.lastName = item.GetResponseFieldOrDefault("last-name", null);
                            card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                            //card.caller_id_bind_on_first_use = item.GetResponseFieldOrDefault("caller-id-bind-on-first-use", "");
                            card.descr = item.GetResponseFieldOrDefault("comment", null);
                            card.email = item.GetResponseFieldOrDefault("email", null);
                            card.phone = item.GetResponseFieldOrDefault("phone", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.activeSessions = (item.GetResponseFieldOrDefault("active-sessions", "0"));
                            card.active = (item.GetResponseFieldOrDefault("active", ""));
                            users.Add(card);
                            Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);
                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    return users;
                }

            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                else
                    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;

        }

        private static List<SourceCardsUserManager_fromMK> Get_Response_UmUsers(IEnumerable<ITikReSentence> response)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
               return Get_Response_UmUsers_V7(response);
            }
            List<SourceCardsUserManager_fromMK> users = users = new List<SourceCardsUserManager_fromMK>();
            foreach (var item in response)
            {
              try
                {
                    SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                    card.id = item.GetResponseFieldOrDefault(".id", null);
                    card.userName = item.GetResponseFieldOrDefault("username", "");
                    //if (card.userName == "497124751")
                    //    MessageBox.Show("");
                    card.password = item.GetResponseFieldOrDefault("password", null);
                    card.actualProfileName = item.GetResponseFieldOrDefault("actual-profile", null);
                    card.uptimeUsed = item.GetResponseFieldOrDefault("uptime-used", "0");
                    card.downloadUsed = (item.GetResponseFieldOrDefault("download-used", "0"));
                    card.uploadUsed = (item.GetResponseFieldOrDefault("upload-used", "0"));
                    card.cusName = item.GetResponseFieldOrDefault("customer", "");
                    card.lastSeenAt = item.GetResponseFieldOrDefault("last-seen", null);
                    card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "1");
                    card.location = item.GetResponseFieldOrDefault("location", null);
                    card.lastName = item.GetResponseFieldOrDefault("last-name", null);
                    card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                    //card.caller_id_bind_on_first_use = item.GetResponseFieldOrDefault("caller-id-bind-on-first-use", "");
                    card.descr = item.GetResponseFieldOrDefault("comment", null);
                    card.email = item.GetResponseFieldOrDefault("email", null);
                    card.phone = item.GetResponseFieldOrDefault("phone", null);
                    card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                    card.activeSessions = (item.GetResponseFieldOrDefault("active-sessions", "0"));
                    card.active = (item.GetResponseFieldOrDefault("active", ""));
                    users.Add(card);
                    Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);
                }
                catch { }
            }
            return users;
        }
        private static List<SourceCardsUserManager_fromMK> Get_Response_UmUsers_V7(IEnumerable<ITikReSentence> response)
        {
            string KeyName = "name";
            //{ApiReSentence:.id=*1|name=58382097|password=12323|group=default|shared-users=unlimited|caller-id=bind|attributes=Framed-IP-Address:kkkk|disabled=false|comment=cccc}
            List<SourceCardsUserManager_fromMK> users = users = new List<SourceCardsUserManager_fromMK>();
            foreach (var item in response)
            {
                try
                {
                    SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                    card.id = item.GetResponseFieldOrDefault(".id", null);
                    card.userName = item.GetResponseFieldOrDefault(KeyName, "");
                   
                    card.password = item.GetResponseFieldOrDefault("password", null);
                    card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "1");
                    card.group = item.GetResponseFieldOrDefault("group", null);
                    card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                    card.attributes = item.GetResponseFieldOrDefault("attributes", null);
                    card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                    card.descr = item.GetResponseFieldOrDefault("comment", null);
                    users.Add(card);
                    Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);
                }
                catch { }
            }
            return users;
        }

        [Obsolete]
        public static List<SourceCardsUserManager_fromMK> Get_one_UM_User(string name, bool is_syn=true)
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return Get_one_UM_User_v7(name,is_syn);
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = null;
           
            try
            {

                string code = Properties.Settings.Default.userman_print;
                string KeyName = "username";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    KeyName = "name";
                    code = Properties.Settings.Default.userman7_print;
                }

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    users = new List<SourceCardsUserManager_fromMK>();

                    var loadCmd = connection.CreateCommandAndParameters(code, "username",name);
                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", "");
                            card.userName = item.GetResponseFieldOrDefault("username", "");
                            card.password = item.GetResponseFieldOrDefault("password", null);
                            card.actualProfileName = item.GetResponseFieldOrDefault("actual-profile", null);
                            card.uptimeUsed = item.GetResponseFieldOrDefault("uptime-used", "0");
                            card.downloadUsed = (item.GetResponseFieldOrDefault("download-used", "0"));
                            card.uploadUsed = (item.GetResponseFieldOrDefault("upload-used", "0"));
                            card.cusName = item.GetResponseFieldOrDefault("customer", "");
                            card.lastSeenAt = item.GetResponseFieldOrDefault("last-seen", "");
                            card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "");
                            card.location = item.GetResponseFieldOrDefault("location", null);
                            card.lastName = item.GetResponseFieldOrDefault("last-name", null);
                            card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                            //card.caller_id_bind_on_first_use = item.GetResponseFieldOrDefault("caller-id-bind-on-first-use", "");
                            card.descr = item.GetResponseFieldOrDefault("comment", null);
                            card.email = item.GetResponseFieldOrDefault("email", null);
                            card.phone = item.GetResponseFieldOrDefault("phone", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.activeSessions = (item.GetResponseFieldOrDefault("active-sessions", "0"));
                            card.active = (item.GetResponseFieldOrDefault("active", ""));
                            users.Add(card);
                           
                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    if (users.Count > 0)
                    {
                        UserManagerProcess u=new UserManagerProcess();
                        if (is_syn)
                            u.Syn_UM_Users_to_LocalDB(users);
                    }
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    return users;
                }

            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                else
                    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;

        }
        [Obsolete]
        public static List<SourceCardsUserManager_fromMK> Get_one_UM_User_v7(string name, bool is_syn = true)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = null;

            try
            {

                string code = Properties.Settings.Default.userman_print;
                if (Global_Variable.Mk_resources.version >= 7)
                    code = Properties.Settings.Default.userman7_print;
             
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    users = new List<SourceCardsUserManager_fromMK>();

                    var loadCmd = connection.CreateCommandAndParameters(code, "name", name);
                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", null);
                            card.userName = item.GetResponseFieldOrDefault("name", "");

                            card.password = item.GetResponseFieldOrDefault("password", null);
                            card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "1");
                            card.group = item.GetResponseFieldOrDefault("group", null);
                            card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                            card.attributes = item.GetResponseFieldOrDefault("attributes", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.descr = item.GetResponseFieldOrDefault("comment", null);
                            users.Add(card);


                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    if (users.Count > 0)
                    {
                        UserManagerProcess u = new UserManagerProcess();
                        if (is_syn)
                            u.Syn_UM_Users_to_LocalDB(users);
                    }
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    return users;
                }

            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                else
                    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;

        }

        [Obsolete]
        public static List<SourceCardsUserManager_fromMK> Get_UM_user_FromSN_ToSN(double SN_Start , double SN_To)
        {
            Stopwatch stopwatch = new Stopwatch();stopwatch.Start();
            List<SourceCardsUserManager_fromMK> users = new List<SourceCardsUserManager_fromMK>();
            try
            {
                string code = Properties.Settings.Default.userman_print;
                if (Global_Variable.Mk_resources.version >= 7)
                    code = Properties.Settings.Default.userman7_print;


                 
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    for (double i = SN_Start; i <= SN_To; i++)
                    {
                        SourceCardsUserManager_fromMK us = new SourceCardsUserManager_fromMK();
                        string idx = "*" + Convert.ToInt64(i).ToString("X");
                        var loadCmd = connection.CreateCommandAndParameters(code, TikSpecialProperties.Id, idx);


                        //var loadCmd = connection.CreateCommandAndParameters(code, ".id", "*" + i.ToString("X"));
                        var response = loadCmd.ExecuteSingleRow();
                        if (response != null)
                        {
                            us = Get_SingleRow_UmUsers(response);
                            users.Add(us);
                        }
                    }

                    //response.GetResponseFieldOrDefault(".id", null);
                    //if (users.Count > 0)
                    //{
                    //    if (is_syn)
                    //    {
                    //        UserManagerProcess u = new UserManagerProcess();
                    //        u.Syn_UM_Users_to_LocalDB(users);
                    //    }
                    //}
                    return users;

                }
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                else
                    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;
        }

        private static SourceCardsUserManager_fromMK Get_SingleRow_UmUsers(ITikReSentence item)
        {
 
            SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
            try
            {
                card.id = item.GetResponseFieldOrDefault(".id", null);
                card.userName = item.GetResponseFieldOrDefault("username", "");
                card.password = item.GetResponseFieldOrDefault("password", null);
                card.actualProfileName = item.GetResponseFieldOrDefault("actual-profile", null);
                card.uptimeUsed = item.GetResponseFieldOrDefault("uptime-used", "0");
                card.downloadUsed = (item.GetResponseFieldOrDefault("download-used", "0"));
                card.uploadUsed = (item.GetResponseFieldOrDefault("upload-used", "0"));
                card.cusName = item.GetResponseFieldOrDefault("customer", "");
                card.lastSeenAt = item.GetResponseFieldOrDefault("last-seen", null);
                card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "1");
                card.location = item.GetResponseFieldOrDefault("location", null);
                card.lastName = item.GetResponseFieldOrDefault("last-name", null);
                card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                //card.caller_id_bind_on_first_use = item.GetResponseFieldOrDefault("caller-id-bind-on-first-use", "");
                card.descr = item.GetResponseFieldOrDefault("comment", null);
                card.email = item.GetResponseFieldOrDefault("email", null);
                card.phone = item.GetResponseFieldOrDefault("phone", null);
                card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                card.activeSessions = (item.GetResponseFieldOrDefault("active-sessions", "0"));
                card.active = (item.GetResponseFieldOrDefault("active", ""));
                //users.Add(card);
                //Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);


            }
            catch { } 
            return card;
        }

        [Obsolete]
        public static SourceCardsUserManager_fromMK Get_one_UM_User2(string name)
        {
            //Stopwatch stopwatch = new Stopwatch();
            //stopwatch.Start();
            //Global_Variable.Sorce_Users_UserManager_ForPrint = new HashSet<string>();
            SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
            try
            {
                string code = Properties.Settings.Default.userman_print;
                string valueName = "username";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    code = Properties.Settings.Default.userman7_print;
                    valueName = "name";
                }
                 
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters(code, valueName, name);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                     foreach (var item in response)
                    {
                        try
                        {
                            //SourceCardsUserManager_fromMK card = new SourceCardsUserManager_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", "");
                            card.userName = item.GetResponseFieldOrDefault("username", "");
                            card.password = item.GetResponseFieldOrDefault("password", null);
                            card.actualProfileName = item.GetResponseFieldOrDefault("actual-profile", null);
                            card.uptimeUsed = item.GetResponseFieldOrDefault("uptime-used", "0");
                            card.downloadUsed = (item.GetResponseFieldOrDefault("download-used", "0"));
                            card.uploadUsed = (item.GetResponseFieldOrDefault("upload-used", "0"));
                            card.cusName = item.GetResponseFieldOrDefault("customer", "");
                            card.lastSeenAt = item.GetResponseFieldOrDefault("last-seen", "");
                            card.sharedUsers = item.GetResponseFieldOrDefault("shared-users", "");
                            card.location = item.GetResponseFieldOrDefault("location", null);
                            card.lastName = item.GetResponseFieldOrDefault("last-name", null);
                            card.callerId = item.GetResponseFieldOrDefault("caller-id", null);
                            //card.caller_id_bind_on_first_use = item.GetResponseFieldOrDefault("caller-id-bind-on-first-use", "");
                            card.descr = item.GetResponseFieldOrDefault("comment", null);
                            card.email = item.GetResponseFieldOrDefault("email", null);
                            card.phone = item.GetResponseFieldOrDefault("phone", null);
                            card.disabled =  ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.activeSessions = (item.GetResponseFieldOrDefault("active-sessions", "0"));
                            card.active = (item.GetResponseFieldOrDefault("active", ""));
                            //users.Add(card);
                            Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);      
                        }
                        catch { }
                    }
                   
                    
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    return card;
                }

            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("database is not acceseble, yet"))
                    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                else
                    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return card;

        }

    }

    public class Class_Batch_cards
    {
        //private string server;
        //private int batchNumber; 
        //private int date;
       

        [DisplayName("رقم الدفعه")]
        public int batchNumber { get; set; }
        [DisplayName("التاريخ")]
        public DateTime? Str_date
        {
            get
            {
                //return (date).Date;
                return utils.UnixTimeStampToDateTime((double)date).Date;
            }
        }
        [DisplayName("العدد")]
        public int count { get; set; }
        [DisplayName("من رقم")]
        public double sn_from { get; set; }
        [DisplayName("الي رقم")]
        public double sn_to { get; set; }
        [DisplayName("الباقة")]
        public string actualProfileName { get; set; }
        [DisplayName("نقطة البيع")]
        public int? spId { get; set; }
        [DisplayName("الاسم")]
        [Browsable(false)]
        public string Str_name
        {
            get
            {
                return batchNumber.ToString("000") + " - " + utils.UnixTimeStampToDateTime(date).ToShortDateString();
                //return batchNumber.ToString("000") + " - " + utils.UnixTimeStampToDateTime(date).ToShortDateString();
            }
        }
      
        [Browsable(false)]
        public string server { get; set; }
        [Browsable(false)]
        //public DateTime date { get; set; }= DateTime.Now;
        public double date { get; set; }
        [Browsable(false)]
        public int? id { get; set; }
        [Browsable(false)]
        public string rb { get; set; }

    }

    public class Batch_cards_FromDB :Class_Batch_cards
    {
        private List<Get_Batch_Status_Cards> Status { get; set; }

        [DisplayName("الكروت النشطة")]
        public int? count_active
        {
            get
            {
                var st = ( from status in Status
                        where status.numberPrintedId == batchNumber && status.status == 1
                          select status.count ).FirstOrDefault() ;
                return st;
            }
        }
        [DisplayName("الانتظار")]
        public int count_deactive
        {
            get
            {
                var st = (from status in Status
                          where status.numberPrintedId == batchNumber && status.status == 0
                          select status.count).FirstOrDefault();
                return st;
            }
        }
        [DisplayName("المنتهية")]

        public int count_Finshed
        {
            get
            {
                var st = (from status in Status
                          where status.numberPrintedId == batchNumber && status.status == 2
                          select status.count).FirstOrDefault();
                return st;
            }
        }

        //[DisplayName("تم حذفه من السيرفر")]
        //public int count_DeleteFromServer
        //{
        //    get
        //    {
        //        var st = (from status in Status
        //                  where status.numberPrintedId == batchNumber && status.DeleteFromServer == 1
        //                  select status.count).FirstOrDefault();
        //        return st;
        //    }
        //}

        public List<Batch_cards_FromDB> Get_Batch_Cards(List<Get_Batch_Status_Cards> _Status, bool by_sp, bool by_profile, bool by_numberPrint)
        {
            string grbBy = "";
            string countColums = "Count,Sn_from,Sn_to,";

            if (by_sp)
            {
                grbBy += " SpId ";
              
            }
            if (by_profile)
            {
                if (grbBy != "")
                    grbBy += ",";
                grbBy += "ProfileName ";
            }
            if (by_numberPrint)
            {
                if (grbBy != "")
                    grbBy += ",";
                grbBy += "BatchNumber ";
                countColums = " sum(Count) as Count , min(Sn_from) as Sn_from, max(Sn_to) as Sn_to , ";
            }
            if (grbBy != "")
                grbBy = " group by " + grbBy;
           
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string Qury = "SELECT " +
                     "Id," +
                     "BatchNumber," +
                     "AddedDate," +
                     countColums +
                     //"sn_from," +
                     //"sn_to, " +
                     "Server, " +
                     "Rb, " +
                     "ProfileName, " +
                     "SpId  " + 
                    
                    " FROM BatchCard where Server=0  " +
                     grbBy+ 
                    " ORDER BY date DESC; ";
                var output = cnn.Query<Batch_cards_FromDB>(Qury, new DynamicParameters());
                
                foreach (var row in output) 
                    row.Status = _Status;

                return output.ToList();
            }
        }
    }

    public class Get_Batch_Status_Cards
    {
        //public static List<Get_Batch_Status_Cards> Batch_Status_Cards_list;
        public int count { get; set; }
        public int status { get; set; }
        public int numberPrintedId { get; set; }

        public List<Get_Batch_Status_Cards> Get_Batch_Cards(bool by_sp, bool by_profile,bool by_numberPrint,bool by_status)
        {
            //string grbBy = "group by ";
            string grbBy = "";

            if (by_sp)
                grbBy += " SpId";
           
            if (by_profile)
            {
                if (grbBy != "")
                    grbBy += ",";
                grbBy += "ProfileName ";
            }
            if (by_numberPrint)
            {
                if (grbBy != "")
                    grbBy += ",";
                grbBy += "BatchNumber ";
            }
            if (by_status)
            {
                if (grbBy != "")
                    grbBy += ",";
                grbBy += "Status ";
            }

            if (grbBy == "")
                grbBy += " group by Status ,BatchNumber";
            else
                grbBy = " group by "+grbBy;


            string _localDB_path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            List<Get_Batch_Status_Cards> s = new List<Get_Batch_Status_Cards>();
            using (IDbConnection cnn = new SQLiteConnection(_localDB_path))
            {
                string qury = "  SELECT  Count(id) as Count,Status ,BatchNumber FROM UmUser  WHERE  BatchNumber > 0  " + grbBy;
                //string qury = "  SELECT  count(id) as count,status ,numberPrintedId FROM user  WHERE Delet_fromServer=0 and numberPrintedId IS NOT NULL and NOT numberPrintedId=0  " + grbBy;
                //string Qury = "SELECT *  FROM Batch_cards where server='usermanager' ORDER BY date DESC;";
                var output = cnn.Query<Get_Batch_Status_Cards>(qury, new DynamicParameters());
                return output.ToList();
            }
        }
        
    }

}
