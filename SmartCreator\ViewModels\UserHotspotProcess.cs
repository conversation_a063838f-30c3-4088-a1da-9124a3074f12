﻿using Dapper;
using Newtonsoft.Json;
using Org.BouncyCastle.Utilities.IO;
using Renci.SshNet;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.CardsDesigen;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.RJForms;
using SmartCreator.Utils;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.ViewModels
{
    public class UserHotspotProcess
    {
        public bool startPrint = false;
        public bool is_Add_One_Card = false;
        public bool is_add_batch_cards = false;
        public bool is_add_batch_cards_to_Archive = false;
        public bool is_rest_sn = false;
        public int inext = 0;

        public FormAddHotspotCards frm = null;
        public Form_PrintHotSpotState Frm_State = null;
        Dictionary<string, object> info_print2;
        Clss_InfoPrint clss_InfoPrint;
        Random rndU;

        private string Public_file_Name = "";
        private string pathfile = "";
        private Sql_DataAccess LDB_DA = null;
        private Smart_DataAccess Smart_DA = null;

        public UserHotspotProcess() { LDB_DA = new Sql_DataAccess(); Smart_DA = new Smart_DataAccess(); }
        public UserHotspotProcess(FormAddHotspotCards _frm, Form_PrintHotSpotState _Frm_State)
        {
            frm = _frm;
            Frm_State = _Frm_State;
            is_add_batch_cards = _Frm_State.is_add_batch_cards;
            is_add_batch_cards_to_Archive = Frm_State.is_add_batch_cards_to_Archive;
            is_Add_One_Card = _Frm_State.is_Add_One_Card;
            LDB_DA = new Sql_DataAccess(); Smart_DA = new Smart_DataAccess();
        }

        void update_lastCode_toSN()
        {
            try
            {
                lock (Smart_DataAccess.Lock_object)
                    using (var conn = Smart_DataAccess.GetConnSmart())
                    {
                        try { var update_last_sn = conn.ExecuteScalar<My_Sequence>($"update My_Sequence set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var CardsTemplate = conn.ExecuteScalar($"update CardsTemplate set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Hotspot_Profile_Hotspot_local = conn.ExecuteScalar($"update Hotspot_Profile_Hotspot_local set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Hotspot_Source_Profile = conn.ExecuteScalar($"update Hotspot_Source_Profile set  rb=@Rb where rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Comm_SellingPoint = conn.ExecuteScalar($"update Comm_SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var HSLocalProfile = conn.ExecuteScalar($"update HSLocalProfile set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var NumberPrintCard = conn.ExecuteScalar($"update NumberPrintCard set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var BatchCard = conn.ExecuteScalar($"update BatchCard set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var Alert_SellingPoint = conn.ExecuteScalar($"update Alert_SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var SellingPoint = conn.ExecuteScalar($"update SellingPoint set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmLimitation = conn.ExecuteScalar($"update UmLimitation set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmProfile = conn.ExecuteScalar($"update UmProfile set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UmProfile_Limtition = conn.ExecuteScalar($"update UmProfile_Limtition set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Customer = conn.ExecuteScalar($"update UserManager_Customer set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_SourceProfile_UserManager = conn.ExecuteScalar($"update UserManager_SourceProfile_UserManager set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Source_Profile_Limtition = conn.ExecuteScalar($"update UserManager_Source_Profile_Limtition set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                        try { var UserManager_Source_limitation = conn.ExecuteScalar($"update UserManager_Source_limitation set  Rb=@Rb where Rb='{Global_Variable.Mk_resources.RB_code}' ;", new { Rb = Global_Variable.Mk_resources.RB_SN }); } catch { }
                    }
            }
            catch { }   
        }

        [Obsolete]
        public bool AddCardMk_Usermanager(FormAddHotspotCards _frm, Form_PrintHotSpotState _Frm_State, string username = "", string password = "")
        {

            update_lastCode_toSN();

            ////////


            frm = _frm;
            Frm_State = _Frm_State;


            if (startPrint == true)
            {
                RJMessageBox.Show("الرجاء الانتضار حتى اكتمال العملية السابقة");
                return false;
            }

            if (check_fields() == false)
                return false;

            clss_InfoPrint = get_data_from_interface2();

            if (clss_InfoPrint.Save_To_PDF)
                if (init_file_pdf() == false)
                    return false;


            //Dictionary<string, object> info_print = get_data_from_interface();

            DialogResult result = RJMessageBox.Show("  هل انت متأكد من انشاء الكروت ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 3, "", "يرجى الانتضار حتى تجميع البيانات");
                frm.btnAdd.Enabled = false;
                frm.tableLayoutPanel1.Enabled = false;
                try
                {
                    if (Frm_State.is_Add_One_Card)
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserUser_one(username, password));
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                    }
                    else if (Frm_State.is_add_batch_cards)
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserHotspot_batch_cards());
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                    }
                    else
                    {
                        ThreadStart theprogress = new ThreadStart(() => AddUserHotspotScript());
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                    }
                }

                catch { startPrint = false; }
            }
            else
            {
                //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الفاء عملية الاضافة");
                startPrint = false;
                return false;
            }
            startPrint = false;
            return true;
        }
        private bool check_fields()
        {
            int numberChik;
            if (startPrint == true)
            {
                RJMessageBox.Show("الرجاء الانتضار حتى اكتمال العملية السابقة");
                return false;
            }

            if (frm.pnl_profile_HS_local.Visible == true)
                if (frm.CBox_Profile_HotspotLocal.SelectedIndex <=0 || frm.CBox_Profile_HotspotLocal.Text == "")
                {
                    RJMessageBox.Show("حدد البروفايل");
                    return false;
                }

            if (frm.pnl_usermanger.Visible == true)
                if (frm.CBox_Profile_UserMan.SelectedIndex == -1 || frm.CBox_Profile_UserMan.Text == "")
                {
                    RJMessageBox.Show("حدد البروفايل");
                    return false;
                }

            if (frm.pnl_Menul_profile.Visible == true)
            {

                if (!(int.TryParse(frm.txt_houre.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل عدد الساعات بشكل صحيح ");
                    return false;
                }
                if (!(int.TryParse(frm.txt_validatiy.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل عدد صلاحية الايام بشكل صحيح ");
                    return false;
                }
                if (!(int.TryParse(frm.txt_price.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل السعر بشكل صحيح ");
                    return false;
                }
                if (!(int.TryParse(frm.txt_download.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل كمية التحميل بشكل صحيح ");
                    return false;
                }
                if (Convert.ToInt32(frm.txt_download.Text) > 0)
                    if (frm.CBox_SizeDownload.SelectedIndex == -1 || frm.CBox_SizeDownload.Text == "")
                    {
                        RJMessageBox.Show("حدد وحده التحميل");
                        return false;
                    }

            }


            if (Frm_State.is_Add_One_Card == false)
            {
                if (!(int.TryParse(frm.txtNumberCard.Text, out numberChik)))
                {
                    RJMessageBox.Show(" ادخل عدد الكروت بشكل صحيح ");
                    return false;
                }
                if (Convert.ToInt32(frm.txtNumberCard.Text) < 2)
                {
                    RJMessageBox.Show("عند اضافة كروت عشوائي يجب ان يكون اقل عدد للكروت 2 كروت");
                    return false;
                }
            }
            if (!(int.TryParse(frm.txt_longUsers.Text, out numberChik)) && is_Add_One_Card == false)
            {
                RJMessageBox.Show(" ادخل عدد صحيح الى طول اسم المستخدم");
                return false;
            }
            if ((Convert.ToInt16(frm.txt_longUsers.Text) + frm.txt_StartCard.Text.Length + frm.txt_EndCard.Text.Length) < 3)
            {
                RJMessageBox.Show("يجب ان يكون طول رقم الكرت مع البادئة والاحقة اكبر من 4");
                //return false;
            }
            if (!(int.TryParse(frm.txt_longPassword.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح في طول كلمة السر ");
                return false;
            }
            if (frm.cbox_User_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم المستخدم");
                return false;
            }
            if (frm.cbox_Pass_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم كلمة السر");
                return false;
            }
            if (frm.cbox_UserPassword_Pattern.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد صيغة الكرت ");
                return false;
            }
            
            
            if (frm.CBox_profile_Source_hotspot.Text == "" && frm.CBox_profile_Source_hotspot.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد بروفايل الهوتسبوت الافتراضي ");
                return false;

            }
            if (frm.CBox_Server_hotspot.Text == "" && frm.CBox_Server_hotspot.SelectedIndex == -1)
            {
                RJMessageBox.Show(" اختر سيرفر الهوتسبوت الافتراضي ");
                return false;

            }
            if (frm.CBox_TemplateCards.SelectedIndex == -1 && frm.CBox_TemplateCards.Text == "")
            {
                //RJMessageBox.Show("لم تختر اي قالب للطباعة");
                DialogResult result2 = RJMessageBox.Show("  لم تقم باختيار قالب للطباعة \n هل تريد المتابعة بدون اخراج الكروت الي ملف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result2 == DialogResult.Yes)
                {
                    frm.checkBoxSaveTo_PDF.Checked = false;
                    frm.checkBoxOpenAfterPrint.Checked = false;
                }
                else
                {
                    startPrint = false;
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                    return false;
                }

            }


            if (frm.checkBox_RegisterAs_LastBatch.Checked)
            {
                if (!(int.TryParse(frm.txt_last_batchNumber.Text, out numberChik)) && is_Add_One_Card == false)
                {
                    RJMessageBox.Show(" ادخل رقم خانة الدفعة  السابقه بشكل صحيح");
                    return false;
                }


                var found = Smart_DA.Get_Batch_byBatchNumber_And_Server(Convert.ToInt32(frm.txt_last_batchNumber.Text), 1);
                //var found = LDB_DA.Get_Batch_byBatchNumber_And_Server(Convert.ToInt32(frm.txt_last_batchNumber.Text)  , 1);
                if (found == null || found.Count == 0)
                {
                    RJMessageBox.Show("رقم الطبعة السابقة التي ادخلتها غير موجود");
                    return false;
                }

                ////Batch_cards_FromDB b = SqlDataAccess.Search_Number_Batch_cards(frm.txt_last_batchNumber.Text);
                //if (SqlDataAccess.Search_Number_Batch_cards(frm.txt_last_batchNumber.Text) == null)
                //{
                //    RJMessageBox.Show("رقم الطبعة السابقة التي ادخلتها غير موجود");
                //    return false;
                //}
            }
            return true;
        }

        private Clss_InfoPrint get_data_from_interface2()
        {
            clss_InfoPrint = new Clss_InfoPrint();
            clss_InfoPrint.is_add_batch_cards = Frm_State.is_add_batch_cards;
            clss_InfoPrint.is_add_batch_cards_to_Archive = Frm_State.is_add_batch_cards_to_Archive;
            clss_InfoPrint.is_Add_One_Card = Frm_State.is_Add_One_Card;
            

            HSLocalProfile Profile_Hotspot = new HSLocalProfile();


            if (frm.pnl_usermanger.Visible)
            {
                UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == frm.CBox_Profile_UserMan.SelectedValue.ToString());
                Profile_Hotspot.Name = profile.Name;
                Profile_Hotspot.TransferLimit = profile.TransferLimit;
                Profile_Hotspot.UptimeLimit = profile.UptimeLimit;
                Profile_Hotspot.Price_Display = profile.Price_Disply;
                Profile_Hotspot.Price = profile.Price;
                Profile_Hotspot.Validity = profile.Validity;

                 
            }
            else if (frm.pnl_profile_HS_local.Visible)
            {
                HSLocalProfile cprofile = new HSLocalProfile(); 
                HSLocalProfile profile = cprofile.Ge_Local_Hotspot_ByName(frm.CBox_Profile_HotspotLocal.Text);
                Profile_Hotspot.Name = profile.Name;
                Profile_Hotspot.TransferLimit = profile.TransferLimit;
                Profile_Hotspot.UptimeLimit = profile.UptimeLimit;
                Profile_Hotspot.Validity = profile.Validity;
                Profile_Hotspot.Price_Display = profile.Price_Display;
                Profile_Hotspot.Price = profile.Price;



                //Profile_Hotspot.Price_Display = profile.Price_Display;
            }
            else 
            {
                Profile_Hotspot.Name = frm.CBox_profile_Source_hotspot.Text;
                Profile_Hotspot.UptimeLimit = (Convert.ToDouble(frm.txt_houre.Text) * 60 * 60);
                Profile_Hotspot.Price_Display=(frm.txt_price.Text);
                Profile_Hotspot.Price =Convert.ToInt32( frm.txt_price.Text);
                Profile_Hotspot.Validity=Convert.ToDouble( frm.txt_validatiy.Text);
               
                if (frm.txt_download.Text != "0")
                {
                    if (frm.CBox_SizeDownload.SelectedIndex == 0)
                        Profile_Hotspot.TransferLimit = (Convert.ToDouble(frm.txt_download.Text) * 1024 * 1024);
                    if (frm.CBox_SizeDownload.SelectedIndex == 1)
                        Profile_Hotspot.TransferLimit = (Convert.ToDouble(frm.txt_download.Text) * 1024 * 1024 * 1024);
                }
            }


            clss_InfoPrint.profile_HS = Profile_Hotspot;

            UmProfile prof=new UmProfile();
            prof.Name=Profile_Hotspot.Name; 
            prof.UptimeLimit= Profile_Hotspot.UptimeLimit;
            prof.TransferLimit= Profile_Hotspot.TransferLimit;
            prof.Price= Profile_Hotspot.Price;
            prof.Price_Disply=Profile_Hotspot.Price_Display;
            prof.Percentage= Profile_Hotspot.Percentage;
            prof.PercentageType= Profile_Hotspot.PercentageType;
            prof.Validity= Profile_Hotspot.Validity;

            clss_InfoPrint.profile=prof;

            if (Frm_State.is_Add_One_Card==false)
            {
                int NumberPrint = 0;
                int BatchNumber = 0;
                if (frm.checkBox_RegisterAsBatch.Checked)
                {
                    BatchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence("BatchCards");
                    BatchNumber += 1;
                    clss_InfoPrint.BatchNumber = BatchNumber;
                }
                if (frm.checkBox_RegisterAs_LastBatch.Checked)
                {
                    BatchNumber = Convert.ToInt32(frm.txt_last_batchNumber.Text);
                    clss_InfoPrint.BatchNumber = BatchNumber;
                    clss_InfoPrint.is_RegisterAs_LastBatch = true;

                }

                NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint");
                NumberPrint += 1;
                clss_InfoPrint.NumberPrint = NumberPrint;

            }

            if (frm.CBox_profile_Source_hotspot.Text != "")
                clss_InfoPrint.profile_Source_hotspot = frm.CBox_profile_Source_hotspot.Text.ToString();
            
            if (frm.CBox_Server_hotspot.Text != "")
                clss_InfoPrint.Server_hotspot = frm.CBox_Server_hotspot.Text.ToString();


            clss_InfoPrint.Selected_template_item = frm.CBox_TemplateCards.SelectedIndex;
            clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(frm.txtNumberCard.Text);
            clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(frm.txtNumberCard.Text);
            //clss_InfoPrint.Profile_Name = frm.CBox_Profile.SelectedValue.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter = frm.cbox_User_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter_Value = frm.cbox_User_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.User_Long = (Convert.ToInt32(frm.txt_longUsers.Text));
            clss_InfoPrint.Mode_Password_NumberORcharcter = frm.cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_Password_NumberORcharcter_Value = frm.cbox_Pass_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.Password_Long = Convert.ToInt32(frm.txt_longPassword.Text);
            clss_InfoPrint.UserPassword_Pattern = frm.cbox_UserPassword_Pattern.SelectedIndex;
            
            if (frm.CBox_SellingPoint.Text != "" && frm.CBox_SellingPoint.SelectedValue!=null)
            {
                 
                try
                {
                    clss_InfoPrint.SellingPoint_Name = frm.CBox_SellingPoint.Text;
                    clss_InfoPrint.SellingPoint_Value =  frm.CBox_SellingPoint.SelectedValue.ToString();
                    clss_InfoPrint.SellingPoint_Value_str = frm.CBox_SellingPoint.SelectedValue.ToString();
                    //clss_InfoPrint.SellingPoint = Smart_DA.LoadSingleById<SellingPoint>(frm.CBox_SellingPoint.SelectedValue.ToString());
                    clss_InfoPrint.SellingPoint = Smart_DA.Get_SellingPoint_Code(frm.CBox_SellingPoint.SelectedValue.ToString());
                }
                catch { }

            }
            clss_InfoPrint.StartCard = frm.txt_StartCard.Text.Trim();
            clss_InfoPrint.EndCard = frm.txt_EndCard.Text.Trim();
            //clss_InfoPrint.ShardUser = frm.txt_ShardUser.Text.Trim();
            clss_InfoPrint.FirstUse = frm.checkBoxFirstUse.Checked;
            clss_InfoPrint.is_comment = frm.checkBox_note.Checked;
            clss_InfoPrint.pathfile = pathfile;

            if (frm.checkBox_note.Checked)
                clss_InfoPrint.Comment = frm.txt_note.Text.Trim().ToString();

            //if (Global_Variable.Mk_resources.version <= 6)
            //    clss_InfoPrint.Custumer_UserMan = frm.CBox_CustomerUserMan.Text.ToString();

            clss_InfoPrint.Smart_Validatiy_Add = frm.checkBox_Add_Smart_Validatiy.Checked;
            clss_InfoPrint.Smart_Validatiy_timeSave = frm.CheckBox_Save_time.Checked;
            clss_InfoPrint.Smart_Validatiy_sizeSave = frm.CheckBox_Save_download.Checked;
            clss_InfoPrint.Smart_Validatiy_sessionSave = frm.CheckBox_Save_session.Checked;
            clss_InfoPrint.Smart_Validatiy_byDayHour = frm.CheckBox_byDayOrHour.Checked;

            clss_InfoPrint.Save_To_PDF = frm.checkBoxSaveTo_PDF.Checked;
            clss_InfoPrint.Open_PDF_file = frm.checkBoxOpenAfterPrint.Checked;
            clss_InfoPrint.SaveTo_excel = frm.checkBoxSaveTo_excel.Checked;
            clss_InfoPrint.SaveTo_script_File = frm.checkBoxSaveTo_script_File.Checked;
            clss_InfoPrint.SaveTo_text_File = frm.checkBoxSaveTo_text_File.Checked;
            clss_InfoPrint.RegisterAsBatch = frm.checkBox_RegisterAsBatch.Checked;
            clss_InfoPrint.RegisterAs_LasBatch = frm.checkBox_RegisterAs_LastBatch.Checked;
            clss_InfoPrint.With_Archive_uniqe = frm.checkBox_With_Archive_uniqe.Checked;
            clss_InfoPrint.TemplateId = frm.CBox_TemplateCards.SelectedValue.ToString();
            clss_InfoPrint.TemplateName = frm.CBox_TemplateCards.Text.ToString();

            return clss_InfoPrint;
        }

        private void Refesh_DGV_User()
        {
            try
            {

                Form_Cards_Hotspot Openform2 = (Form_Cards_Hotspot)RJMainForm.listChildForms.Find(x => x.Name == "Form_Cards_Hotspot");
                if (Openform2 != null)
                {
                    Thread thread = new Thread(Openform2.formAllCardsUserHotspot.loadData);
                    thread.Start();
                }
            }
            catch { }

        }
        private bool init_file_pdf()
        {
            SaveFileDialog saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Title = "حدد مكان حفظ الملف";
            try
            {
                if (Frm_State.PathFolderPrint != "")
                    saveFileDialog1.InitialDirectory = Frm_State.PathFolderPrint;
                else
                    saveFileDialog1.InitialDirectory = $"{utils.Get_TempCards_Pdf_Directory()}\\Hotspot";
                //saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\pdf";
            }
            catch
            {
                saveFileDialog1.InitialDirectory = $"{utils.Get_TempCards_Pdf_Directory()}\\Hotspot";
                Frm_State.PathFolderPrint = $"{utils.Get_TempCards_Pdf_Directory()}\\Hotspot";
            }
            try
            {
                if (!Directory.Exists(Frm_State.path_saved_file))
                {
                    Directory.CreateDirectory(Frm_State.path_saved_file);
                }
            }
            catch
            {
                Frm_State.path_saved_file = $"{utils.Get_TempCards_Pdf_Directory()}\\Hotspot";
            }
            Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + frm.txtNumberCard.Text + "Cards)" + "_(" + clss_InfoPrint.profile_HS.Name + ")";
            pathfile = Frm_State.path_saved_file + "\\" + "Cards_" + Public_file_Name + ".pdf";

            saveFileDialog1.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialog1.FileName = "Cards_" + Public_file_Name;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialog1.FileName;
                Frm_State.PathFolderPrint = Path.GetDirectoryName(saveFileDialog1.FileName);
                Frm_State.path_saved_file = pathfile;

            }
            else
            {
                startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                return false;
            }


            clss_InfoPrint.pathfile = pathfile;
            return true;
        }

        private New_Generate_Cards new_Generate_Cards;
        private string strProfile = "";
         [Obsolete]
        public void AddUserHotspotScript()
        {
            startPrint = true;
            try
            {
                HashSet<string> card_copy = new HashSet<string>();
                if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
                {
                    //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    //    card_copy = db.ColumnDistinct<string>(db.From<HSUser>().Where(x => x.DeleteFromServer == 0).Select(x => x.UserName));
                    //}
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_Hotspot_from_if_RunOffline());
                    Sql_DataAccess Local_DA=new Sql_DataAccess();
                    card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM HSUser WHERE DeleteFromServer=0;"));

                }
                else
                {
                    if (Global_Variable.Source_Users_Hotspot_ForPrint != null)
                        card_copy = new HashSet<string>(Global_Variable.Source_Users_Hotspot_ForPrint);
                }
                if (clss_InfoPrint.With_Archive_uniqe)
                {
                    //=========  get cards from archive  and copy  to hashset card_copy
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
                }

                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

                string mesgBtn = "يتم الان اضافة الكروت الى الهوتسبوت";
                if (is_add_batch_cards_to_Archive)
                    mesgBtn = "يتم الان اضافة الكروت الى الارشيف";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

                // ====== توليد الكروت العشوائية ============================
                CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
                New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Public_Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }
                //========= تجهيز سكربت الاضافة الي المايكروتك =================
                Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
                
                //=========== فحص اذا طريقة الاضافة سكربت واحد لليوزر والبروفايل او فصل سكربت اضافة البروفايل وحده =========
                //bool _check_if_use_2Scritp_add = check_if_use_2Scritp_add();
                //======= الاضافة الي المايكروتك ========================
                Dictionary<string, string> res  = GenerateBachScriptUser(variableScript);

                if (res["status"] == "false")
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }

                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                string[] Res_split = res["result"].Split(new string[] { ";" }, StringSplitOptions.None);
                string[] user_split = Res_split[2].Split(new string[] { "|" }, StringSplitOptions.None);  //====== check if error add user ========
                if (user_split.Length > 1)
                {
                    for (int i = 1; i < user_split.Length; i++)
                        new_Generate_Cards.dicUser.Remove(user_split[i]);
                    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                    new_Generate_Cards.dicUser = GenerateIfLastErorr(new_Generate_Cards.dicUser, user_split.Length - 1, cLS_Genrate_Cards);
                }

                //===== الاستعلام عن الكرت الاول ومعرفه الرقم التسلسلي تبعه عشان التسلسل للبقية =======
                if (new_Generate_Cards.dicUser.Count == 0)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return; 
                } 
                  

                List<HSUser> dbUser = add_sn_to_local_dbUser(new_Generate_Cards);
                
                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي اليوزمنجر");
              
                add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    Add_to_NumberPrint_cards_toDB(dbUser);
                }
                //if (clss_InfoPrint.SaveTo_excel) { }
                //if (clss_InfoPrint.SaveTo_script_File) { }
                //if (clss_InfoPrint.SaveTo_text_File) { }


                if (clss_InfoPrint.SaveTo_excel) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف اكسل"); CreateExcel(dbUser); }
                if (clss_InfoPrint.SaveTo_script_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف سكربت"); Create_Script_File(dbUser,variableScript); }
                if (clss_InfoPrint.SaveTo_text_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف نصي"); Create_Text_File(dbUser); }


                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (MethodInvoker)delegate ()
                 {
                     frm.LoadDatagridviewData();
                 });
                
                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الاضافة بنجاح");
                Refesh_DGV_User();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message + "\n\n" /*+ ex.ToString()*/); is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                startPrint = false;
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
            }
            startPrint = false;
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
        }

        private Dictionary<string, string> Get_VariableGenerateBachScriptUser(string strUser, string strPass)
        {
            Dictionary<string, string> result = new Dictionary<string, string>();
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(new char[] { ';' });
            strUser = strUser.TrimEnd(new char[] { ' ' });
            strUser = strUser.TrimEnd(',');
            strPass = strPass.TrimEnd(',');

            result["strUser"] = strUser;
            result["strPass"] = strPass;
            result["note"] = "";

            if (clss_InfoPrint.is_comment)
            {
                if (clss_InfoPrint.Comment != null)
                    if (clss_InfoPrint.Comment != "")
                        if (clss_InfoPrint.is_Add_One_Card)
                        {
                            result["note"] = clss_InfoPrint.Comment.ToString().Trim();
                        }
                        else
                        {
                            result["note"] = " comment=\"" + clss_InfoPrint.Comment.ToString().Trim() + "\"";

                        }
            }
            if (clss_InfoPrint.is_Add_One_Card)
            {
                result["limtUptime"] =  clss_InfoPrint.profile_HS.UptimeLimit.ToString();
                result["limtTotal"] =  clss_InfoPrint.profile_HS.TransferLimit.ToString();
                result["server"] = clss_InfoPrint.Server_hotspot ;
                result["Profile"] = clss_InfoPrint.profile_Source_hotspot ;

                result["email"] = "";
                

            }
            else
            {
                result["limtUptime"] = " limit-uptime=" + clss_InfoPrint.profile_HS.UptimeLimit;
                result["limtTotal"] = " limit-bytes-total=" + clss_InfoPrint.profile_HS.TransferLimit;
                result["server"] = " server=" + "\"" + clss_InfoPrint.Server_hotspot + "\"";
                result["Profile"] = " profile=" + "\"" + clss_InfoPrint.profile_Source_hotspot + "\"";

                result["email"] = "";
                
            }


            CultureInfo English = new CultureInfo("en-US");
            string dateprint= DateTime.Now.ToString("yyyy-MM-dd-HH-mm", English);

            //profil'day'price'sp'numPrint'datetimePrint'byDayHour'mac'timeSave'sizeSave'sessionSave'@smart.befor
            if (clss_InfoPrint.Smart_Validatiy_Add)
            {
                string sp = "0";
                string datetimePrint = dateprint;
                string firstUse = clss_InfoPrint.FirstUse.ToString();
                string byDayHour = clss_InfoPrint.Smart_Validatiy_byDayHour.ToString();
                string timeSave = clss_InfoPrint.Smart_Validatiy_timeSave.ToString();
                string sizeSave = clss_InfoPrint.Smart_Validatiy_sizeSave.ToString();
                string sessionSave = clss_InfoPrint.Smart_Validatiy_sessionSave.ToString();

                if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value != null)
                     sp =  clss_InfoPrint.SellingPoint_Value.ToString();

                

                string email = 
                    clss_InfoPrint.profile_HS.Name+"'"+
                    clss_InfoPrint.profile_HS.Validity+"'"+
                    clss_InfoPrint.profile_HS.Price+"'"+
                    sp+"'"+
                    //clss_InfoPrint.BatchNumber+"'"+
                    clss_InfoPrint.BatchNumber+"!"+clss_InfoPrint.NumberPrint+ "'" +
                    dateprint+"'"
                    +byDayHour+"'"+
                    firstUse+"'"+
                    timeSave+"'"+
                    sizeSave+"'"+
                    sessionSave+ "'@smart.befor";
                if (clss_InfoPrint.is_Add_One_Card)
                result["email"] =  email.ToLower();
                else
                    result["email"] = " email=" + "\"" + email.ToLower() + "\"";

                clss_InfoPrint.email=email;
            }

            return result;
        }

        [Obsolete]
        private Dictionary<string, string> GenerateBachScriptUser(Dictionary<string, string> varible)
        {
            string script = "";
            if (clss_InfoPrint.UserPassword_Pattern == 0)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;" +
                ":for i from=0 to=([:len $usr]-1) do={:do {[/ip hotspot user add " + "name=[:pick $usr $i] " + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + "] ; " +
                " } on-error={:set us ($us.\"|\".[:pick $usr $i]); :put $us;}}}";
            }

            if (clss_InfoPrint.UserPassword_Pattern == 1)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;" +
                    ":for i from=0 to=([:len $usr]-1) do={:do {[/ip hotspot user add " + "name=[:pick $usr $i] " + "password=[:pick $usr $i] " + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + "] ; " +
                    " } on-error={:set us ($us.\"|\".[:pick $usr $i]); :put $us;}}}";
            }
            if (clss_InfoPrint.UserPassword_Pattern == 2)
            {
                script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local Pwd [:toarray (" + varible["strPass"] + ")];:local us ;" +
                        ":for i from=0 to=([:len $usr]-1) do={:do {[/ip hotspot user add " + "name=[:pick $usr $i] " + "password=[:pick $Pwd $i] " + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + "] ; " +
                        " } on-error={:set us ($us.\"|\".[:pick $usr $i]); :put $us;}}}";
            }
            Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script);
            return res;
        }
        private int CountTry = 5;
        [Obsolete]
        private Dictionary<string, NewUserToAdd> GenerateIfLastErorr(Dictionary<string, NewUserToAdd> dicUser, int number_user, CLS_Generate_Random_Cards cLS_Genrate_Cards, int CountTry = 5)
        {
            if (CountTry < 1)
                return dicUser;
            CountTry = CountTry - 1;

            Dictionary<string, NewUserToAdd> _dicUser = dicUser;
            New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(number_user, "", false);
            if (new_Generate_Cards == null)
                return _dicUser;

            Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
            Dictionary<string, string> res = GenerateBachScriptUser(variableScript);
            if (res["status"] == "false")
                return _dicUser;

            string[] Res_split = res["result"].Split(new string[] { ";" }, StringSplitOptions.None);
            string[] user_split = Res_split[2].Split(new string[] { "|" }, StringSplitOptions.None);  //====== check if error add user ========

            if (user_split.Length > 1)
            {
                for (int i = 1; i < user_split.Length; i++)
                    new_Generate_Cards.dicUser.Remove(user_split[i]);

                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                GenerateIfLastErorr(_dicUser, user_split.Length - 1, cLS_Genrate_Cards);
            }
            else
            {
                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                return _dicUser;
            }


            return _dicUser;
        }

        //[Obsolete]
        [Obsolete]
        private List<HSUser> add_sn_to_local_dbUser(New_Generate_Cards new_Generate_Cards, bool formArchive = false)
        {
            strProfile = "";
            //===== الاستعلام عن الكرت الاول ومعرفه الرقم التسلسلي تبعه عشان التسلسل للبقية =======
            SourceCardsHotspot_fromMK firstUser = new SourceCardsHotspot_fromMK();
            SourceCardsHotspot_fromMK first =firstUser.Get_one_HS_User(new_Generate_Cards.dicUser.ElementAt(0).Key, false).First();
            double sn = 0;
            if (first.id != null)
                sn = Int32.Parse(first.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
            #region تجهيز كلاس اليوزر عشان الاضافة الي قاعده البيانات المحلية واضافة اول كرت قبل الدخول للدوارة
            List<HSUser> dbUser = new List<HSUser>();


            //============= حساب النسبه للباقة او البقالة ===============================-==-

            //float totalPrice = clss_InfoPrint.profile.Price;
            float totalPrice = clss_InfoPrint.profile_HS.Price;
            string Price_Disply = clss_InfoPrint.profile_HS.Price_Display;
            float price = clss_InfoPrint.profile_HS.Price;
            //float price = clss_InfoPrint.profile.Price;
            //string Price_Disply = clss_InfoPrint.profile.Price_Disply;
            float percentage = 0;
            int percentage_type = 0;

            if (clss_InfoPrint.SellingPoint != null)
            {
                if (clss_InfoPrint.SellingPoint.Is_percentage == 1)
                {
                    percentage_type = clss_InfoPrint.SellingPoint.PercentageType;
                    percentage = clss_InfoPrint.SellingPoint.Percentage;
                    if (percentage_type == 0)
                    {
                        float percentage_value = (price * percentage) / 100;
                        totalPrice = price - percentage_value;
                    }
                    else
                    {
                        totalPrice = price - percentage;
                    }
                }
                else if (clss_InfoPrint.profile_HS.Is_percentage == 1)
                {
                    percentage_type = clss_InfoPrint.profile_HS.PercentageType;
                    percentage = clss_InfoPrint.profile_HS.Percentage;
                    if (percentage_type == 0)
                    {
                        float percentage_value = (price * percentage) / 100;
                        totalPrice = price - percentage_value;
                    }
                    else
                    {
                        totalPrice = price - percentage;
                    }
                }
            }
            else if (clss_InfoPrint.profile_HS.Is_percentage == 1)
            {
                percentage_type = clss_InfoPrint.profile_HS.PercentageType;
                percentage = clss_InfoPrint.profile_HS.Percentage;
                if (percentage_type == 0)
                {
                    float percentage_value = (price * percentage) / 100;
                    totalPrice = price - percentage_value;
                }
                else
                {
                    totalPrice = price - percentage;
                }
            }


            if (clss_InfoPrint.SellingPoint != null)
            {
                if (Convert.ToBoolean(clss_InfoPrint.SellingPoint.Is_percentage))
                {
                    percentage_type = clss_InfoPrint.SellingPoint.PercentageType;
                    percentage = clss_InfoPrint.SellingPoint.Percentage;
                    if (clss_InfoPrint.SellingPoint.PercentageType == 0)
                    {
                        percentage = (clss_InfoPrint.SellingPoint.Percentage * clss_InfoPrint.profile_HS.Price) / 100;
                        totalPrice = totalPrice - percentage;
                    }
                    else
                    {
                        totalPrice = totalPrice - percentage;
                    }
                }
            }
            //==============================



            int? Number_Card_In_Page = Calclate_Number_Card_In_Page();

            for (int i = 0; i < new_Generate_Cards.dicUser.Count; i++)
            {
                //sn = sn + 1;
                int? PageNumber = null;
                new_Generate_Cards.dicUser.ElementAt(i).Value.SN = sn;
                Global_Variable.Source_Users_UserManager_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(i).Key);
                string id_user = "*" + Convert.ToInt32(sn).ToString("X");
                strProfile += "\"" + id_user + "\"" + ",";

                HSUser db = new HSUser();
                db.IdHX = id_user;
                db.SN = (long)sn;
                db.UserName = new_Generate_Cards.dicUser.ElementAt(i).Value.Name;
                db.Sn_Name = sn + "-" + new_Generate_Cards.dicUser.ElementAt(i).Value.Name;
                db.Password = new_Generate_Cards.dicUser.ElementAt(i).Value.Password;
                db.ProfileHotspot = clss_InfoPrint.profile_Source_hotspot;
                db.ProfileName = clss_InfoPrint.profile_HS.Name;
                db.LimitUptime = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.UptimeLimit = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.TransferLimit = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.Limitbytestotal = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.Price = price;
                //db.TotalPrice = clss_InfoPrint.profile_HS.Price;

                db.TotalPrice = totalPrice;
                db.Price = price;
                db.Price_Disply = clss_InfoPrint.profile_HS.Price_Display;
                db.Percentage = percentage;
                db.PercentageType = percentage_type;

                //db.TotalPrice = totalPrice;
                //db.Price_Disply = Price_Disply;
                db.ProfileValidity = (long)(clss_InfoPrint.profile_HS.Validity * 24 * 60 * 60);
                db.ValidityLimit = (long)(clss_InfoPrint.profile_HS.Validity * 24 * 60 * 60);
                
                db.Descr = clss_InfoPrint.Comment;
                db.Email = clss_InfoPrint.email;
                db.Server = clss_InfoPrint.Server_hotspot;
                db.RegDate = clss_InfoPrint.regDate;
                db.LastSynDb = clss_InfoPrint.regDate;
                db.AddedDb = clss_InfoPrint.regDate;

                db.ProfileTransferLeft = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.ProfileTimeLeft = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.CountProfile = 1;
                db.SpCode = clss_InfoPrint.SellingPoint_Value;
                db.SpName = clss_InfoPrint.SellingPoint_Name;
                db.NumberPrint = clss_InfoPrint.NumberPrint;
                db.BatchCardId = clss_InfoPrint.BatchNumber;
                db.SmartValidatiy_Add = Convert.ToInt32(clss_InfoPrint.Smart_Validatiy_Add);
                db.SmartValidatiy_sessionSave = Convert.ToInt32(clss_InfoPrint.Smart_Validatiy_timeSave);
                db.SmartValidatiy_sizeSave = Convert.ToInt32(clss_InfoPrint.Smart_Validatiy_sizeSave);
                db.SmartValidatiy_timeSave = Convert.ToInt32(clss_InfoPrint.Smart_Validatiy_timeSave);
                db.SmartValidatiy_ByDayOrHour = Convert.ToInt32(clss_InfoPrint.Smart_Validatiy_byDayHour);
                 
                db.DeleteFromServer = 0;

                if (clss_InfoPrint.FirstUse)
                {
                    db.Caller_id_yes_no = "yes";
                    db.CallerMac = "bind";
                }


                //db.date_added_Localdb = clss_InfoPrint.regDate;
                db.Status = 0;
               
                if (formArchive)
                    db.PageNumber = new_Generate_Cards.dicUser.ElementAt(i).Value.PageNumber;
                else
                {
                    try
                    {
                        PageNumber = (int?)((i) / Number_Card_In_Page) + 1;
                    }
                    catch { }
                    db.PageNumber = PageNumber;
                }
                db.Sn_Archive = new_Generate_Cards.dicUser.ElementAt(i).Value.SN_Archive;



                dbUser.Add(db);
                sn = sn + 1;
            }
            return dbUser;
            #endregion

        }
        
        private void add_to_db(List<HSUser> dbUser)
        {
            SourceCardsHotspot_fromMK s=new SourceCardsHotspot_fromMK();
            s.Add_HSUser_toDB(dbUser, true, true); 
            HsPyment hsPyment = new HsPyment();
            hsPyment.Add_HS_Pyments_after_print(dbUser.ToHashSet(),true);

            //SqlDataAccess.Add_UM_Pyement_to_LocalDB(List < SourcePymentUserManager_fromDB > UM_Pyement, bool is_insert = true)
        }
        
        
        void print_pdf(Dictionary<string, NewUserToAdd> dicUsers)
        {
            CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            CardsTemplate card = new CardsTemplate();
            string TemplateId = clss_InfoPrint.TemplateId.ToString();
            string TemplateName = clss_InfoPrint.TemplateName.ToString();
            SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);
            //Form_PDF_Prview pdf = new Form_PDF_Prview();


            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);
                //pdf.print_pdf(dicUsers, clss_InfoPrint, card, null);
                CLS_Print.print_pdf(dicUsers, clss_InfoPrint, card, null);
            }
            else if (Sourcecard.type == "table_Desigen1")
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                //pdf.print_pdf(dicUsers, clss_InfoPrint, null, cardTable1);

                CLS_Print.print_pdf(dicUsers, clss_InfoPrint, null, cardTable1);
            }
            return;
            //CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            //CardsTemplate card = new CardsTemplate();
            //string TemplateId = clss_InfoPrint.TemplateId.ToString();
            //string TemplateName = clss_InfoPrint.TemplateName.ToString();
            //SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);

            HSLocalProfile profile = clss_InfoPrint.profile_HS;
            //UserManager_Profile_UserManager profile = Global_Variable.UM_Profile.Find(x => x.Name == info_print["Public_Profile_Name"].ToString());

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = profile.Name;
            string price = profile.Price_Display.ToString();
            string Validity = profile.Validity.ToString();
            string time = profile.UptimeLimit.ToString();  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            string sizeTransfer = profile.TransferLimit.ToString();
            string SP = "";
            string numberPrint = "";
            string BatchNumber = "";
            string DatePrint = "";
            string Note_On_Pages_text = "";


            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);

                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    if (card.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                        }
                    }
                    if (card.cardsItems.Validity.title_show)
                    {
                        Validity = card.cardsItems.Validity.title_text + " " + Validity;
                    }
                }
                if (card.cardsItems.Time.Enable)
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                        if (card.cardsItems.Time.title_show)
                        {
                            time = card.cardsItems.Time.title_text + " " + time;
                        }

                    }
                }
                if (card.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                        if (card.cardsItems.Size.title_show)
                        {
                            sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                        }

                    }
                }

                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrint != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = (int)LDB_DA.get_BatchCards_my_sequence();

                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (card.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                        
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (Show_sp.Code).ToString();
                        else
                            SP = (Show_sp.UserName).ToString();
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                if (cardTable1.cardsItems.Price.Enable)
                {
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = price + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    if (cardTable1.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                        }
                    }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    try
                    {
                        if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        }
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                    }
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();

                    //int batchNumber = SqlDataAccess.get_BatchCards_my_sequence();
                    //int batchNumber = (int)LDB_DA.get_BatchCards_my_sequence();
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (cardTable1.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != "")
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                         
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                        if (Show_sp != null)
                        {
                            if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                                SP = (Show_sp.Code).ToString();
                            else
                                SP = (Show_sp.UserName).ToString();
                        }
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("numberPrint", numberPrint);
            Cardsdata.Add("BatchNumber", BatchNumber);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (Sourcecard.type == "design")
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);
            else if (Sourcecard.type == "table_Desigen1")
            {
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            }


            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            //MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            //try
            //{
            //    System.Diagnostics.Process.Start(pathfile);
            //}
            //catch { }
        }
        void Add_to_Batch_cards_toDB(List<HSUser> dbUser)
        {
            long sn_from = (long)dbUser.First().SN;
            long sn_to = (long)dbUser.Last().SN;

            BatchCard data = new BatchCard();
           
            data.Id = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);
            data.Sn_from = sn_from;
            data.Sn_to = sn_to;
            //data.BatchNumber = clss_InfoPrint.BatchNumber;
            data.BatchNumber = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);

            data.ProfileName = clss_InfoPrint.profile_HS.Name;
            
            data.AddedDate = clss_InfoPrint.regDate;
            data.Count = clss_InfoPrint.Number_Cards_ToAdd;
            data.Rb = Global_Variable.Mk_resources.RB_SN;
            data.SpCode = clss_InfoPrint.SellingPoint_Value;
            //data.Server = "hotspot"; 
            data.Server = 1;//hotspot;
            data.BatchType = 0;//print;
            Smart_DA.Add_Batch_Cards(data,data.Server,clss_InfoPrint.is_RegisterAs_LastBatch);

        }
        void Add_to_NumberPrint_cards_toDB(List<HSUser> dbUser)
        {
            long sn_from = (long)dbUser.First().SN;
            long sn_to = (long)dbUser.Last().SN;

            NumberPrintCard data = new NumberPrintCard();
            data.Id = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);
            //data.Id = (int)clss_InfoPrint.NumberPrint;
            data.Sn_from = sn_from;
            data.Sn_to = sn_to;
            data.BatchNumber = (int)(clss_InfoPrint.BatchNumber > 0 ? clss_InfoPrint.BatchNumber : 0);
            data.NumberPrint = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);
            //data.BatchNumber = clss_InfoPrint.BatchNumber;
            //data.NumberPrint = clss_InfoPrint.NumberPrint;
            data.ProfileName = clss_InfoPrint.profile_HS.Name;

            data.AddedDate = clss_InfoPrint.regDate;
            data.Count = clss_InfoPrint.Number_Cards_ToAdd;
            data.Rb = Global_Variable.Mk_resources.RB_SN;
            data.SpCode = clss_InfoPrint.SellingPoint_Value;
            data.Server = 1;//hotspot;
            data.BatchType = 0;//print;
            Smart_DA.Add_NumberPrint_Cards(data, data.Server, clss_InfoPrint.is_RegisterAs_LastBatch);

        }

        [Obsolete]
        public void AddUserHotspot_batch_cards()
        {
            startPrint = true;
            try
            {
                HashSet<string> card_copy = new HashSet<string>();
                if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
                {
                    //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    //    card_copy = db.ColumnDistinct<string>(db.From<HSUser>().Where(x => x.DeleteFromServer == 0).Select(x => x.UserName));
                    //}
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_Hotspot_from_if_RunOffline());
                     Sql_DataAccess Local_DA = new Sql_DataAccess();
                    card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM HSUser WHERE DeleteFromServer=0;"));

                }
                else
                {
                    if (Global_Variable.Source_Users_Hotspot_ForPrint != null)
                        card_copy = new HashSet<string>(Global_Variable.Source_Users_Hotspot_ForPrint);
                }
                if (clss_InfoPrint.With_Archive_uniqe)
                {
                    //=========  get cards from archive  and copy  to hashset card_copy
                    //card_copy = new HashSet<string>(SqlDataAccess.Get_UsersManager_from_Archive());
                }

                int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;
                string mesgBtn = "يتم الان اضافة الكروت الى اليوزرمنجر";
                if (is_add_batch_cards_to_Archive)
                    mesgBtn = "يتم الان اضافة الكروت الى الارشيف";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);
                // ====== توليد الكروت العشوائية ============================
                CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
                new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Public_Number_Cards_ToAdd, mesgBtn, true);
                if (new_Generate_Cards == null)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }
                //========= تجهيز سكربت الاضافة الي المايكروتك =================
                Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
                //======= الاضافة الي المايكروتك ========================
                Dictionary<string, string> res = null;
                res = GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
                if (res["status"] == "false")
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
                    return;
                }
                // ======= نفحص الناتج من المايكروتك  اذا في خطاء في ضافة اليوزر او اضافة البروفايل =====
                //string path = @"tempCards\script\batch\";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\Hotspot\\batch\\";
                string path_SmartErorrCards = path + "SmartErorrCards.rsc";
                //string path_SmartErorrProfile = Directory.GetCurrentDirectory() + "\\" + path + "SmartErorrProfile.rsc";
                var Users_lines = File.ReadAllLines(path_SmartErorrCards);
                List<string> user_erorr = new List<string>();
                for (var i = 0; i < Users_lines.Length; i += 1)
                {
                    var line = Users_lines[i];
                    user_erorr.Add(line.Trim());
                }
                //var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
                List<string> profile_erorr = new List<string>();

                //for (var i = 0; i < Profiles_lines.Length; i += 1)
                //{
                //    var line = Profiles_lines[i];
                //    profile_erorr.Add(line.Trim());
                //}

                string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
                //string[] profile_split = profile_erorr.ToArray();

                if (user_split.Length > 0)
                {
                    for (int i = 0; i < user_split.Length; i++)
                        new_Generate_Cards.dicUser.Remove(user_split[i]);
                    //========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                    CountTry = 5;
                    new_Generate_Cards.dicUser = GenerateIfLastErorr_batch_cards(new_Generate_Cards.dicUser, user_split.Length, cLS_Genrate_Cards);
                }
                ////====== check if error add profile   or  _check_if_use_2Scritp_add ================== 
                //if (profile_split.Length > 1)
                //{
                //    AddProfile_ErorreCards(profile_split.ToList());
                //}

                try { File.Delete(path_SmartErorrCards); } catch { }
                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي الهوتسبوت");

                List<HSUser> dbUser = add_sn_to_local_dbUser(new_Generate_Cards);
                add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { Add_to_Batch_cards_toDB(dbUser); }
                if (clss_InfoPrint.is_Add_One_Card == false)
                {
                    Add_to_NumberPrint_cards_toDB(dbUser);
                }
                if (clss_InfoPrint.SaveTo_excel) { }
                if (clss_InfoPrint.SaveTo_script_File) { }
                if (clss_InfoPrint.SaveTo_text_File) { }

                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (MethodInvoker)delegate ()
                {
                    frm.LoadDatagridviewData();
                });

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الطباعة بنجاح");
                Refesh_DGV_User();  

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\n" /*+ ex.ToString()*/); is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                startPrint = false;
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
            }
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
            startPrint = false;
            
        }

        [Obsolete]
        private Dictionary<string, string> GenerateBachScriptUser_batch_cards(New_Generate_Cards users, Dictionary<string, string> varible)
        {
            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false"; res["result"] = "";
            string mk_File_Name = DateTime.Now.ToString("ddMMyyHHmmss") + ".rsc";
            //string path = @"tempCards\script\batch\";

            string path = $"{utils.Get_TempCards_Script_Directory()}\\Hotspot\\batch";

            string file_Path_PC = path + "\\" + mk_File_Name;
            string SmartErorrCards_Path_PC = path + "\\" + "SmartErorrCards.rsc";
            //string SmartErorrProfile_Path_PC = path + "\\" + "SmartErorrProfile.rsc";
            try { if (!Directory.Exists(path)) { Directory.CreateDirectory(path); } } catch (Exception ex) { MessageBox.Show(" tempCards\\script\\batch\\ خطا في مسار حفظ الملف \n" + ex.Message.ToString()); }
            //string script = "";
            //if (Global_Variable.Mk_resources.version >= 7)
            //{

            //}
            //else
            //{
                string row_user = "";
                for (int i = 0; i < users.dicUser.Count; i++)
                {
                    if (clss_InfoPrint.UserPassword_Pattern == 0)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /ip hotspot user add  name=" + userName + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + " ;"
                        + "} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\")}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 1)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        row_user = ":do { /ip hotspot user add  name=" + userName + " password="+ userName + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + " ;"
                       + "} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\")}; ";
                    }
                    if (clss_InfoPrint.UserPassword_Pattern == 2)
                    {
                        string userName = users.dicUser.ElementAt(i).Value.Name;
                        string password = users.dicUser.ElementAt(i).Value.Password;
                        row_user = ":do { /ip hotspot user add  name=" + userName + " password=" + password + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + " ;"
                       + "} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + userName + "\\n\")}; ";
                    }
                    File.AppendAllText(file_Path_PC, row_user + "\n", Encoding.ASCII);
                }
            //}

            bool added_file = add_batch_to_Mikrotik(file_Path_PC);
            if (added_file == false)
            {
                RJMessageBox.Show("خطا في ملف الاضافة");
                return res;
            }
            //========= add files =================
            using (File.Create(SmartErorrCards_Path_PC))
            {

            }
            add_batch_to_Mikrotik(SmartErorrCards_Path_PC);

            Mk_DataAccess_old mk_DataAccess_Old = new Mk_DataAccess_old();
            Dictionary<string, string> res_mik = mk_DataAccess_Old.run_import_file(mk_File_Name);

            if (res_mik["status"] == "false")
            {
                frm.rest_port_mk_after();
                remove_file_import_after_print(mk_File_Name);
                remove_file_import_after_print("SmartErorrCards.rsc");
                return res;
            }

            string path_SmartErorrCards = path + "\\" + "SmartErorrCards.rsc";
            string path_SmartErorrProfile = path + "\\" + "SmartErorrProfile.rsc";

            //string path_SmartErorrCards = Directory.GetCurrentDirectory() + "\\" + "SmartErorrCards.rsc";
            download_files_from_Mikrotik("SmartErorrCards.rsc");



            res["status"] = "true";
            frm.rest_port_mk_after();
            remove_file_import_after_print(mk_File_Name);
            remove_file_import_after_print("SmartErorrCards.rsc");

            try { File.Delete(file_Path_PC); } catch { }

            return res;
        }

        bool add_batch_to_Mikrotik(string file_ScriptName)
        {
            bool add = false;
            string CurrentDirectory = Directory.GetCurrentDirectory();
            //string path_html = CurrentDirectory + "\\" + "test";
            //string path_html = CurrentDirectory + "\\" + file_ScriptName;
            string path_html = file_ScriptName;
            //Process.Start(path_html); 
            string uploadfile = path_html;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(uploadfile, FileMode.Open))
                    {
                        client.BufferSize = 4 * 1024; // bypass Payload error large files
                        client.UploadFile(fileStream, Path.GetFileName(uploadfile));
                    }
                    add = true;
                }
            }
            catch { }
            return add;
        }
        bool download_files_from_Mikrotik(string file_ScriptName)
        {
            bool add = false;
            //string CurrentDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\script\\batch\\";
            string CurrentDirectory = $"{utils.Get_TempCards_Script_Directory()}\\Hotspot\\batch\\";
            string path_html = CurrentDirectory + file_ScriptName;
            string uploadfile = path_html;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(uploadfile, FileMode.Create))
                    {
                        client.BufferSize = 4 * 1024 * 1024; // bypass Payload error large files
                        //client.UploadFile(fileStream, Path.GetFileName(uploadfile));
                        client.DownloadFile(file_ScriptName, fileStream);
                    }
                    add = true;
                }
            }
            catch { }
            return add;
        }
        public void Download(string fileToDownload, string pathToDownload)
        {
            try
            {
                using (var sftpClient = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                using (var fs = new FileStream(Path.GetFileName(pathToDownload), FileMode.OpenOrCreate))
                {
                    sftpClient.Connect();

                    sftpClient.DownloadFile(fileToDownload, fs, downloaded =>
                    {
                        //MessageBox.Show($"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                        //Global_Variable.Update_Um_StatusBar(false, true, 0, $"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                        Console.WriteLine($"Downloaded {(double)downloaded / fs.Length * 100}% of the file.");
                    });

                    sftpClient.Disconnect();
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }

        }
        [Obsolete]
        public void remove_file_import_after_print(string fileName)
        {
            Mk_DataAccess da = new Mk_DataAccess();
            string result2 = da.remove_file_import_after_print(fileName);
        }
        [Obsolete]
        private Dictionary<string, NewUserToAdd> GenerateIfLastErorr_batch_cards(Dictionary<string, NewUserToAdd> dicUser, int number_user, CLS_Generate_Random_Cards cLS_Genrate_Cards)
        {
            if (CountTry < 1)
                return dicUser;
            CountTry = CountTry - 1;

            Dictionary<string, NewUserToAdd> _dicUser = dicUser;
            New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(number_user, "", false);
            if (new_Generate_Cards == null)
                return _dicUser;

            Dictionary<string, string> variableScript = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);
            Dictionary<string, string> res = GenerateBachScriptUser_batch_cards(new_Generate_Cards, variableScript);
            if (res["status"] == "false")
                return _dicUser;

            string path = $"{utils.Get_TempCards_Script_Directory()}\\Hotspot\\batch\\";
            string path_SmartErorrCards = Directory.GetCurrentDirectory() + "\\" + path + "SmartErorrCards.rsc";
            //string path_SmartErorrProfile = Directory.GetCurrentDirectory() + "\\" + path + "SmartErorrProfile.rsc";
            var Users_lines = File.ReadAllLines(path_SmartErorrCards);
            List<string> user_erorr = new List<string>();
            for (var i = 0; i < Users_lines.Length; i += 1)
            {
                var line = Users_lines[i];
                user_erorr.Add(line.Trim());
            }

            //var Profiles_lines = File.ReadAllLines(path_SmartErorrProfile);
            List<string> profile_erorr = new List<string>();

            //for (var i = 0; i < Profiles_lines.Length; i += 1)
            //{
            //    var line = Profiles_lines[i];
            //    profile_erorr.Add(line.Trim());
            //}

            string[] user_split = user_erorr.ToArray();  //====== check if error add user ========
            //string[] profile_split = profile_erorr.ToArray();

            if (user_split.Length > 1)
            {
                for (int i = 0; i < user_split.Length; i++)
                    new_Generate_Cards.dicUser.Remove(user_split[i]);

                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                GenerateIfLastErorr_batch_cards(_dicUser, user_split.Length - 1, cLS_Genrate_Cards);
            }
            else
            {
                _dicUser = _dicUser.Concat(new_Generate_Cards.dicUser).ToDictionary(x => x.Key, x => x.Value);
                return _dicUser;
            }


            return _dicUser;
        }

        [Obsolete]
        public void AddUserUser_one(string username , string password )
        {
            startPrint = true;
            try
            {
                int Public_Number_Cards_ToAdd = 1;
                clss_InfoPrint.Number_Cards_ToAdd = 1;
                string mesgBtn = "يتم   اضافة الكرت الى الهوتسبوت";
                Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

                Dictionary<string, NewUserToAdd> dicUser = new Dictionary<string, NewUserToAdd>();
                dicUser.Add(username, new NewUserToAdd { Name = username, Password = password, SN = 0 });
                New_Generate_Cards new_Generate_Cards = new New_Generate_Cards { dicUser = dicUser, strUser = "", strPass = "", status = true };

                Dictionary<string, string> varible = Get_VariableGenerateBachScriptUser(new_Generate_Cards.strUser, new_Generate_Cards.strPass);

                Dictionary<string, string> User = new Dictionary<string, string>();


                User["username"] = username;
                User["password"] = password;
                User["limtUptime"] = varible["limtUptime"];
                User["limtTotal"] = varible["limtTotal"];
                User["email"] = varible["email"];
                User["server"] = varible["server"];
                User["Profile"] = varible["Profile"];
                User["comment"] = varible["note"];

                

                string id_user = Mk_DataAccess.add_one_user_hotspot(User);
                
                
                double sn = 0;
                if (id_user == null || id_user == "")
                {
                    RJMessageBox.Show(" خطاء قد يكون الاسم مكرر او خطاء في بروفايل الهوسبوت او السيرفر");
                    Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "خطاء في اضافة اليوزر");
                    startPrint=false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return;
                }
                sn = Int32.Parse(id_user.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);

               
                new_Generate_Cards.dicUser.ElementAt(0).Value.SN = sn;
                Global_Variable.Source_Users_Hotspot_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(0).Key);
                if (new_Generate_Cards.dicUser.Count == 0)
                {
                    startPrint = false;
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
                    return;
                }
               
                #region تجهيز كلاس اليوزر عشان الاضافة الي قاعده البيانات المحلية واضافة اول كرت قبل الدخول للدوارة
                List<HSUser> dbUser = new List<HSUser>();
                HSUser db = new HSUser();
    
                Global_Variable.Source_Users_UserManager_ForPrint.Add(new_Generate_Cards.dicUser.ElementAt(0).Key);
                
                strProfile += "\"" + id_user + "\"" + ",";
            
                db.IdHX = id_user;
                db.SN = (long)sn;
                db.UserName = new_Generate_Cards.dicUser.ElementAt(0).Value.Name;
                db.Sn_Name = sn + "-" + new_Generate_Cards.dicUser.ElementAt(0).Value.Name;
                db.Password = new_Generate_Cards.dicUser.ElementAt(0).Value.Password;
                db.ProfileHotspot = clss_InfoPrint.profile_Source_hotspot;
                db.ProfileName = clss_InfoPrint.profile_HS.Name;
                db.LimitUptime = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.UptimeLimit = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.TransferLimit = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.Limitbytestotal = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.Price = clss_InfoPrint.profile_HS.Price;
                db.TotalPrice = clss_InfoPrint.profile_HS.Price ;
                db.Price_Disply = clss_InfoPrint.profile_HS.Price_Display;
                db.ProfileValidity = (long)(clss_InfoPrint.profile_HS.Validity * 24 * 60 * 60);
                db.ValidityLimit = (long)(clss_InfoPrint.profile_HS.Validity * 24 * 60 * 60);

                db.Descr = clss_InfoPrint.Comment;
                db.Email = clss_InfoPrint.email;
                db.Server = clss_InfoPrint.Server_hotspot;
                db.RegDate = clss_InfoPrint.regDate;
                //db.LastSynDb = clss_InfoPrint.regDate;
                db.AddedDb = clss_InfoPrint.regDate;

                db.ProfileTransferLeft = (long)clss_InfoPrint.profile_HS.TransferLimit;
                db.ProfileTimeLeft = (long)clss_InfoPrint.profile_HS.UptimeLimit;
                db.CountProfile = 1;
                db.SpCode = clss_InfoPrint.SellingPoint_Value;
                db.SpName = clss_InfoPrint.SellingPoint_Name;
                //db.BatchCardId = clss_InfoPrint.BatchNumber;
                //db.NumberPrint = clss_InfoPrint.NumberPrint;

                db.DeleteFromServer = 0;
                if (clss_InfoPrint.FirstUse)
                {
                    db.Caller_id_yes_no = "yes";
                    db.CallerMac = "bind";
                }


                //db.date_added_Localdb = clss_InfoPrint.regDate;
                db.Status = 0;

            
                dbUser.Add(db);
                #endregion

                Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي الهوتسبوت");
                add_to_db(dbUser);
                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file);
                        }
                        catch { }
                    }
                }
                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { add_to_Batch_cards_toDB(dbUser); }
                //if (clss_InfoPrint.SaveTo_excel) { }
                //if (clss_InfoPrint.SaveTo_script_File) { }
                //if (clss_InfoPrint.SaveTo_text_File) { }

                //==== refresh datagridview batch Number
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (MethodInvoker)delegate ()
                {
                    frm.LoadDatagridviewData();
                });

                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + inext + " )", " تم انشاء  " + Public_Number_Cards_ToAdd + " كروت الى اليوزرمنجر");
                RJMessageBox.Show("تمت الاضافة بنجاح");
                Refesh_DGV_User();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message + "\n\n" /*+ ex.ToString()*/); is_Add_One_Card = false; startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                startPrint = false;
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
              (MethodInvoker)delegate ()
              {
                  frm.btnAdd.Enabled = true;
                  frm.tableLayoutPanel1.Enabled = true;
              });
            }
            startPrint = false;
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (MethodInvoker)delegate ()
               {
                   frm.btnAdd.Enabled = true;
                   frm.tableLayoutPanel1.Enabled = true;
               });
        }



        private int? Calclate_Number_Card_In_Page()
        {
            if (clss_InfoPrint.TemplateId == null)
                return null;

            CardsTemplate tcard = new CardsTemplate();
            SourceCardsTemplate sorceTemplates = SqlDataAccess.Get_template_cards_By_id(clss_InfoPrint.TemplateId);
            if (sorceTemplates == null)
                return null;
            if (sorceTemplates.type == "design")
            {
                CardsTemplate cardsTemplate = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplates.values);
                if (cardsTemplate == null)
                {
                    return null;
                }
                tcard = cardsTemplate;
            }
            else
                return null;

            //==========================================================================================  
            float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.space_horizontal_margin.ToString()));
            float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.Space_vertical_margin.ToString()));
            float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
            float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_height.ToString()));


            float Pictur_width_orginal = Pictur_width;
            float Pictur_height__orginal = Pictur_height;

            float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
            float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);

            //int NuberCards = 51;
            int NumberCard_for_OneColum = 0;
            int NumberCard_in_Page = 0;
            //double NumberPages = 0;

            int ColumNumber = 0;
            //float CardNumber = 0;

            ColumNumber = (int)(595 / (Pictur_width + Space_Y));
            if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                ColumNumber = ColumNumber - 1;

            NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
            if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                NumberCard_for_OneColum = NumberCard_for_OneColum - 1;


            NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

            //txt_NumberCard.Text = NumberCard_in_Page.ToString();
            //txt_NumberCulum.Text = ColumNumber.ToString();

            iTextSharp.text.Image jpg = null;

            return NumberCard_in_Page;


        }

        //===========================================================
        public void CreateExcel(List<HSUser> dbUser)
        {
            //string path = @"tempCards\Excel";
            string path = $"{utils.Get_TempCards_Excel_Directory()}\\Hotspot";

            string sp = "";
            string file_ExceltName = "";
            if (clss_InfoPrint.SellingPoint != null)
                sp = clss_InfoPrint.SellingPoint.Code;

            //if (is_add_batch_cards_to_Archive)
            //    path = Cards_setting.path_saved_file;


            //string pathC = Directory.GetCurrentDirectory() + "\\tempCards\\Excel";
            //string time= DateTime.Now.Day.ToString()+ DateTime.Now.Month.ToString()+ DateTime.Now.Year.ToString();
            //file_ExceltName = path + $"\\Excel_{clss_InfoPrint.profile.Name}_{DateTime.Now.Day}.csv";
            file_ExceltName = path + "\\" + "Excel_" + Public_file_Name + ".csv";
            try
            {
                //int columnCount = dgvUserManager.Columns.Count;
                //string columnNames = "";

                string[] outputCsv = new string[dbUser.Count];
                int row = 0;
                foreach (HSUser user in dbUser)
                {
                    string UserName = "";
                    string Password = "";
                    string ProfileName = "";
                    string SpCode = "";

                    if (!string.IsNullOrEmpty(user.UserName))
                        UserName = user.UserName.ToString();
                    if (!string.IsNullOrEmpty(user.Password))
                        Password = user.Password.ToString();
                    if (!string.IsNullOrEmpty(user.ProfileName))
                        ProfileName = user.ProfileName.ToString();
                    if (!string.IsNullOrEmpty(user.SpCode))
                        SpCode = user.SpCode.ToString();




                    //outputCsv[i] += UserName + ",";
                    //outputCsv[i] += Password + ",";
                    //outputCsv[i] += ProfileName + ",";
                    //try { outputCsv[i] += SpCode + ","; } catch { }


                    outputCsv[row] += $"=\"{UserName ?? ""}\",=\"{Password ?? ""}\",=\"{ProfileName ?? ""}\",=\"{SpCode ?? ""}\"";


                    //outputCsv[row] += user.UserName.ToString() + ",";
                    //outputCsv[row] += user.Password.ToString() + ",";
                    //outputCsv[row] += clss_InfoPrint.profile.Name + ",";
                    //outputCsv[row] +=  sp + ",";

                    //outputCsv[row] += "=\"" + user.UserName.ToString() + "\",";
                    //outputCsv[row] += "=\"" + user.Password.ToString() + "\",";
                    //outputCsv[row] += "=\"" + clss_InfoPrint.profile.Name + "\",";
                    //outputCsv[row] += "=\"" + sp + "\",";

                    row++;
                }

                File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                //MessageBox.Show("Data Exported Successfully !!!", "Info");

                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                        System.Diagnostics.Process.Start(path);
                    }
                    catch { }
            }
            catch (Exception ex)
            {
                MessageBox.Show("export_execl :" + ex.Message);
            }



        }
        public void Create_Script_File(List<HSUser> dbUser, Dictionary<string, string> varible)
        {
            try
            {
                string valPassword = "";
                //string path = @"tempCards\script\Hotspot";
                string path = $"{utils.Get_TempCards_Script_Directory()}\\Hotspot";
              
                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "Script_" + Public_file_Name + ".rsc";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show("  خطا في مسار حفظ الملف النصي \n" + ex.Message.ToString()); }
                //MessageBox.Show(file_ScriptName);
                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);
                fs.Close();
                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();
                int row = 0;
                foreach (HSUser user in dbUser)
                {

                    if (clss_InfoPrint.Mode_Password_NumberORcharcter == "1")
                        valPassword = " password=" + dbUser[row].UserName + " ";
                    if (clss_InfoPrint.Mode_Password_NumberORcharcter == "2")
                        valPassword = " password=" + dbUser[row].Password + " ";


                    string row_user = ":do { /ip hotspot user add  name=" + dbUser[row].UserName + valPassword + varible["limtUptime"] + varible["limtTotal"] + varible["email"] + varible["note"] + varible["server"] + varible["Profile"] + " ;"
                    + "} on-error={/file set SmartErorrCards.rsc contents=([/file get SmartErorrCards.rsc contents] . \"" + dbUser[row].UserName + "\\n\")}; ";
                    File.AppendAllText(file_ScriptName, row_user + "\n", Encoding.ASCII);
                    row++;
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show("Create_Script_File" + ex.Message.ToString());
            }
        }
        public void Create_Text_File(List<HSUser> dbUser)
        {
            try
            {
                //string path = @"tempCards\text\Hotspot";
                string path = $"{utils.Get_TempCards_Text_Directory()}\\Hotspot";
                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "text_" + Public_file_Name + ".txt";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show(" خطا في مسار حفظ الملف النصي\n" + ex.Message.ToString()); }


                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);

                fs.Close();

                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();
                foreach (HSUser user in dbUser)
                {
                    File.AppendAllText(file_ScriptName, user.UserName + " " + user.Password + " " + clss_InfoPrint.profile_HS.Name + "\n", Encoding.ASCII);
                }
                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ScriptName);
                    }
                    catch { }
            }
            catch (IOException ex)
            {
                MessageBox.Show(ex.Message.ToString());
            }
            //=========================================================================== :do {/interface bridge add name=loopback; } on-error={:put "erorr"}
            //}
        }

    }
}
