﻿using SmartCreator.Properties;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Utils
{
    public class CustomFonts
    {
        public static readonly PrivateFontCollection pfc = new PrivateFontCollection();
        public static readonly PrivateFontCollection pfc_DroidNask = new PrivateFontCollection();
        public static readonly PrivateFontCollection pfc_DroidSansArabic = new PrivateFontCollection();
        public static readonly PrivateFontCollection pfc_DroidKufi_Regular = new PrivateFontCollection();

        public CustomFonts() { }
        private void loadFont()
        {
            //var pfc = new PrivateFontCollection();
            //pfc.AddFontFile(@"fonts\DroidKufi-Bold.ttf");
            //btnLogin.Font = new Font(pfc.Families[0], 14, FontStyle.Regular);

            try
            {
                string DroidKufi_file = @"fonts\DroidKufi-Bold.ttf";
                string DroidSansArabic_file = @"fonts\DroidSansArabic.ttf";
                string DroidKufi_Regular_file = @"fonts\DroidKufi_Regular.ttf";
                string DroidNaskhBold_file = @"fonts\DroidNaskh-Bold.ttf";
                string Cairo_Medium = @"fonts\Cairo-Medium.ttf";
                string Cairo_Regular = @"fonts\Cairo-Regular.ttf";

                //File.WriteAllBytes(DroidKufi_file, Resources.DroidKufi_Bold);
                //File.WriteAllBytes(DroidSansArabic_file, Resources.DroidSansArabic);
                //File.WriteAllBytes(DroidNaskhBold_file, Resources.DroidNaskh_Bold);
                //File.WriteAllBytes(DroidKufi_Regular_file, Resources.DroidKufi_Regular);

                pfc.AddFontFile(DroidKufi_file);
                pfc_DroidSansArabic.AddFontFile(DroidSansArabic_file);
                pfc_DroidNask.AddFontFile(DroidNaskhBold_file);
                pfc_DroidKufi_Regular.AddFontFile(DroidKufi_Regular_file);

                //radioButtonByIP.Font = new Font(pfc_DroidSansArabic.Families[0], 8, FontStyle.Bold);
                //radioButtonDomain.Font = new Font(pfc.Families[0], 8, FontStyle.Bold);
                //checkBoxSaveUsername.Font = new Font(pfc.Families[0], 7, FontStyle.Bold);
                //checkBoxSavePassword.Font = new Font(pfc.Families[0], 7, FontStyle.Bold);
                //checkBox_Port.Font = new Font(pfc.Families[0], 7, FontStyle.Bold);
                //checkBox_ssh.Font = new Font(pfc.Families[0], 7, FontStyle.Bold);
                //checkBox_DisableLoadSession.Font = new Font(pfc.Families[0], 7, FontStyle.Bold);
                //btnLogin.Font = new Font(pfc.Families[0], 8, FontStyle.Bold);
                //lbl_virtion_name.Font = new Font(pfc.Families[0], 8, FontStyle.Bold);

                //dgvMicrotikSaved.ColumnHeadersDefaultCellStyle.Font = new Font(pfc.Families[0], 6.5f, FontStyle.Bold);

            }
            catch (Exception ex) { /*MessageBox.Show(ex.ToString());*/ }

        }

        public static Font Get_Custom_Font(string fontName = "Cairo_Medium", float fontSize = 9.0F, bool fontBlod = false, GraphicsUnit unit = GraphicsUnit.Point, byte grd = 0)
        {
            string Cairo_Medium = @"fonts\Cairo-Medium.ttf";
            string Cairo_Regular = @"fonts\Cairo-Regular.ttf";
            string Cairo_SemiBold = @"fonts\Cairo-SemiBold.ttf";
            string DroidKufi_Regular = @"fonts\DroidKufi_Regular.ttf";
            string DroidSansArabic = @"fonts\DroidSansArabic.ttf";
            pfc.AddFontFile(Cairo_Medium);

            if (fontName == "Cairo_Medium")
                pfc.AddFontFile(Cairo_Medium);
            if (fontName == "DroidKufi_Regular")
                pfc.AddFontFile(DroidKufi_Regular);
            if (fontName == "DroidSansArabic")
                pfc.AddFontFile(DroidSansArabic);
            if (fontName == "Cairo_Regular")
                pfc.AddFontFile(Cairo_SemiBold);
            if (fontName == "Cairo_SemiBold")
                pfc.AddFontFile(Cairo_Regular);

            FontStyle fs = FontStyle.Regular;
            if(fontBlod)
                 fs = FontStyle.Bold;

            return new Font(pfc.Families[0], fontSize, fs, unit, grd);
        }
    }
}
