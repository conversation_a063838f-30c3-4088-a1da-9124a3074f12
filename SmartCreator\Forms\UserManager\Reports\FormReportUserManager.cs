﻿using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Windows.Forms;
using SmartCreator.Forms.UserManager.Reports;
using SmartCreator.Forms.SellingPoints;
using SmartCreator.Properties;
using System.Drawing;
using SmartCreator.Entities.EnumType;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormReportUserManager : RJChildForm
    {
        Form_UM_Sales form_UM_Sales;
        Form_UM_Sales_Size_Times form_UM_Sales_Size_Times;
        Form_UM_Sales_Device form_UM_Sales_Device;
        Form_UM_Report_ByPrint form_UM_Report_Print;
        Form_UM_Report_ByBatch form_UM_Report_Batch;
        Form_Sales_SellingPoint form_Sales_SP;
        Form_UM_Users_Devices form_UM_Report_Users;

        bool First_Form_UM_Sales = true;
        bool First_Form_UM_Sales_Size_Times = true;
        bool First_Form_UM_Sales_Device = true;
        bool First_Form_UM_Report_Print = true;
        bool First_Form_UM_Report_Batch = true;
        bool First_Form_UM_Report_SP = true;
        bool First_Form_UM_Report_Users = true;
        string ServerType = "UM";
        int PW;
        bool Hided;
        System.Drawing.Image bgImage;
        //OrmLiteConnectionFactory dbFactory = null;
        public FormReportUserManager(string _ServerType="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            ServerType = _ServerType;
            
            //dbFactory = Sql_DataAccess.Get_dbFactory();
            //bgImage = System.Drawing.Image.FromFile(@"D:\MyProjects\SmartCreator\SmartCreatorDesktop\SmartCreator\bin\Debug\tempCards\cardsBack\1.jpg");

            this.Text = "تقارير اليوزمنجر";
            if (ServerType=="HS")
                this.Text = "تقارير الهوتسبوت";

            if (UIAppearance.Language_ar)
            {
                rjPanel_btns.RightToLeftLayout = false;
                rjPanel_btns.RightToLeft = RightToLeft.No;
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
                //tableLayoutPanel_Contains.RightToLeft = RightToLeft.No;
                //tableLayoutPanel3.RightToLeft = RightToLeft.No;
            }
            else
            {
                this.Text = "Reports UserManager";
                if (ServerType == "HS")
                    this.Text = "Reports HotSpot";

                rjPanel_btns.RightToLeftLayout = true;
                rjPanel_btns.RightToLeft = RightToLeft.Yes;
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
                //tableLayoutPanel_Contains.RightToLeft = RightToLeft.Yes;
                //tableLayoutPanel3.RightToLeft = RightToLeft.Yes;
               
            }
            btn_ReportBysSizeTitle.Font =
               btn_ReportDevice.Font =
               btn_Report_Batch.Font =
               btn_Report_Print.Font = btn_Report_SP.Font =
               btn_Report_Users.Font = btn_SalesTitle.Font =
               Program.GetCustomFont(Resources.DroidKufi_Bold, 8 * utils.ScaleFactor, FontStyle.Bold);

        }
  
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_SalesTitle_Click(object sender, EventArgs e)
        {
            //this.Text = "مبيعات اليوزمنجر";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_SalesTitle);



            if (First_Form_UM_Sales)
            {
                First_Form_UM_Sales = false;
                form_UM_Sales = new Form_UM_Sales(ServerType);
                form_UM_Sales.TopLevel = false;
                form_UM_Sales.IsChildForm = true;
                form_UM_Sales.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Sales);
                this.panel_Tab_Container.Tag = form_UM_Sales;
                form_UM_Sales.Show(); //show on desktop panel  
                form_UM_Sales.BringToFront();
                form_UM_Sales.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Sales.BringToFront();
                form_UM_Sales.Show();
                form_UM_Sales.Focus();
            }
        }

        private void btn_ReportBysSizeTitle_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير الاستهلاك يوزمنجر";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_ReportBysSizeTitle);



            if (First_Form_UM_Sales_Size_Times)
            {
                First_Form_UM_Sales_Size_Times = false;
                form_UM_Sales_Size_Times = new Form_UM_Sales_Size_Times("UM");
                form_UM_Sales_Size_Times.TopLevel = false;
                form_UM_Sales_Size_Times.IsChildForm = true;
                form_UM_Sales_Size_Times.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Sales_Size_Times);
                this.panel_Tab_Container.Tag = form_UM_Sales_Size_Times;
                form_UM_Sales_Size_Times.Show(); //show on desktop panel  
                form_UM_Sales_Size_Times.BringToFront();
                form_UM_Sales_Size_Times.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Sales_Size_Times.BringToFront();
                form_UM_Sales_Size_Times.Show();
                form_UM_Sales_Size_Times.Focus();
            }
        }

        private void btn_ReportDevice_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير الاجهزة والمنافذ";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_ReportDevice);



            if (First_Form_UM_Sales_Device)
            {
                First_Form_UM_Sales_Device = false;
                form_UM_Sales_Device = new Form_UM_Sales_Device();
                form_UM_Sales_Device.TopLevel = false;
                form_UM_Sales_Device.IsChildForm = true;
                form_UM_Sales_Device.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Sales_Device);
                this.panel_Tab_Container.Tag = form_UM_Sales_Device;
                form_UM_Sales_Device.Show(); //show on desktop panel  
                form_UM_Sales_Device.BringToFront();
                form_UM_Sales_Device.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Sales_Device.BringToFront();
                form_UM_Sales_Device.Show();
                form_UM_Sales_Device.Focus();
            }
        }



        private void pnlClientArea_Resize(object sender, EventArgs e)
        {

        }

        private void rjTime_From_MouseDown(object sender, MouseEventArgs e)
        {
        }

        private void rjDate_From_OnValueChanged(object sender, EventArgs e)
        {
        }

        private void btn_SideFilter_Click(object sender, EventArgs e)
        {
        }
       
        private void btn__Click(object sender, EventArgs e)
        {
        }

       

        private void FormReportUserManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            tableLayoutPanel_Top_Btn.Refresh();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            btn_SalesTitle_Click(sender, e);
        }

        private void btn_Report_Print_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير الطباعة";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_Report_Print);



            if (First_Form_UM_Report_Print)
            {
                First_Form_UM_Report_Print = false;
                form_UM_Report_Print = new Form_UM_Report_ByPrint();
                form_UM_Report_Print.TopLevel = false;
                form_UM_Report_Print.IsChildForm = true;
                form_UM_Report_Print.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Report_Print);
                this.panel_Tab_Container.Tag = form_UM_Report_Print;
                form_UM_Report_Print.Show(); //show on desktop panel  
                form_UM_Report_Print.BringToFront();
                form_UM_Report_Print.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Report_Print.BringToFront();
                form_UM_Report_Print.Show();
                form_UM_Report_Print.Focus();
            }

        }

        private void btn_Report_Batch_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير الدفعات";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_Report_Batch);



            if (First_Form_UM_Report_Batch)
            {
                First_Form_UM_Report_Batch = false;
                form_UM_Report_Batch = new Form_UM_Report_ByBatch();
                form_UM_Report_Batch.TopLevel = false;
                form_UM_Report_Batch.IsChildForm = true;
                form_UM_Report_Batch.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Report_Batch);
                this.panel_Tab_Container.Tag = form_UM_Report_Batch;
                form_UM_Report_Batch.Show(); //show on desktop panel  
                form_UM_Report_Batch.BringToFront();
                form_UM_Report_Batch.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Report_Batch.BringToFront();
                form_UM_Report_Batch.Show();
                form_UM_Report_Batch.Focus();
            }
        }

        private void btn_Report_Users_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير اجهزة المستخدمين ";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_Report_Users);



            if (First_Form_UM_Report_Users)
            {
                First_Form_UM_Report_Users = false;
                form_UM_Report_Users = new Form_UM_Users_Devices();
                form_UM_Report_Users.TopLevel = false;
                form_UM_Report_Users.IsChildForm = true;
                form_UM_Report_Users.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_UM_Report_Users);
                this.panel_Tab_Container.Tag = form_UM_Report_Users;
                form_UM_Report_Users.Show(); //show on desktop panel  
                form_UM_Report_Users.BringToFront();
                form_UM_Report_Users.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_UM_Report_Users.BringToFront();
                form_UM_Report_Users.Show();
                form_UM_Report_Users.Focus();
            }
        }

        private void btn_Report_SP_Click(object sender, EventArgs e)
        {
            this.Text = "تقارير نقاط البيع ";
            Btn_DeActive();
            //Btn_DeActive();
            Btn_Active(btn_Report_SP);



            if (First_Form_UM_Report_SP)
            {
                First_Form_UM_Report_SP = false;
                form_Sales_SP = new Form_Sales_SellingPoint();
                form_Sales_SP.TopLevel = false;
                form_Sales_SP.IsChildForm = true;
                form_Sales_SP.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Sales_SP);
                this.panel_Tab_Container.Tag = form_Sales_SP;
                form_Sales_SP.Show(); //show on desktop panel  
                form_Sales_SP.BringToFront();
                form_Sales_SP.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_Sales_SP.BringToFront();
                form_Sales_SP.Show();
                form_Sales_SP.Focus();
            }
        }
    }
}
