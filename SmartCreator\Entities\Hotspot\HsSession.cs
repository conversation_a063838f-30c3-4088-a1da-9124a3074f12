﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Hotspot
{
    [UniqueConstraint("TillTime_inSecond", "UserName")]

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class HsSession : BaseSession
    {
        //[PrimaryKey, Required, Unique, StringLength(250)/*,Browsable(false)*/]
        //public string Sn_Name { get; set; }

        [PrimaryKey, Required, Unique, AutoIncrement/*,Browsable(false)*/]
        public long Id { get; set; }

        [Browsable(false)]
        public int Status { get; set; } = 0;

        [ForeignKey(typeof(HSUser), OnDelete = "CASCADE", ColumnName = "Sn_Name"), Index]
        public string Fk_Sn_Name { get; set; }
        public string contents { get; set; }

        public long TillTime_inSecond { get; set; }

        [Obsolete]
        public void Syn_HS_Session(List<HsSession> _HsSession = null,bool WithremoveSession=true,string username=null)
        {
            Sql_DataAccess Local_DA = new Sql_DataAccess();
            if (_HsSession == null)
                return;
            if (_HsSession.Count == 0)
                return;

            //List<HsSession> _HSession = Local_DA.Get_Not_Delet_fromServer<HsSession>("HsSession");
            //if (_HsSession == null)
            //    _HsSession = Local_DA.Get_Not_Delet_fromServer<HsSession>("HSUser");
            ////=============== set deleteFromServer === that not have user ================
            //var DeleteFromServer = (from user in _HSPyment
            //                        where !_HSUser.Any(f => f.UserName == user.UserName)
            //                        select user as HsPyment).ToHashSet();
            //if (DeleteFromServer != null)
            //{
            //    if (DeleteFromServer.Count > 0)
            //        Local_DA.Set_Delet_fromServer<HsPyment>("HsPyment", DeleteFromServer, "Id");
            //}
            ////======================================================================
            //_HSPyment = Local_DA.Get_Not_Delet_fromServer<HsPyment>("HsPyment");
            //$uptime."!&".$"bytes-in"."!&".$"bytes-out"."!&".$"bytes-total"."!&".$interface."!&".$address."!&".$"mac-address"."!&"."$user"); 


            List<HSUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<HSUser>("HSUser");
            var Session_Users = (from user in _HsSession
                            join dbUser in sourceCardsUsers on user.UserName equals dbUser.UserName
                          
                            select new HsSession
                            {
                                UserName = user.UserName,
                                UpTime = (user.UpTime),
                                BytesUpload = (user.BytesUpload),
                                BytesDownload = user.BytesDownload,
                                //AddedDate = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                Fk_Sn_Name = dbUser.Sn_Name,
                                DeleteFromServer = 0,
                                NasPortId = user.NasPortId,
                                IpUser = user.IpUser,
                                CallingStationId = user.CallingStationId,
                                FromTime = user.FromTime,
                                TillTime = user.TillTime,
                                TillTime_inSecond = user.TillTime_inSecond,
                                MkId = Global_Variable.Mk_resources.RB_SN,
                            }).ToList();


            if (Session_Users.Count == 0 || Session_Users == null)
                return;

            if (Local_DA.Add_HSession_ToDB(Session_Users))
            {

                //============== update users table  ============
                List<HsSession> sourceHsSession = Local_DA.Get_Not_Delet_fromServer<HsSession>("HsSession");

                var SessionLQ = (from r in Session_Users
                                 group r by r.UserName into g
                                 select new
                                 {
                                     UserName = g.Key,
                                     //Sn_Name = g.First().Sn_Name,
                                     Fk_Sn_Name = g.First().Fk_Sn_Name,
                                     //FromTime = g.OrderBy(x => x.FromTime).First().FromTime,
                                     //UptimeUsed = g.Sum(a => a.UpTime),
                                     //DownloadUsed = g.Sum(r => r.BytesDownload),
                                     //UploadUsed = g.Sum(r => r.BytesUpload),
                                     //Radius = g.First().IpRouter,
                                     NasPortId = g.First().NasPortId,
                                     CountSession = g.Count(),
                                 });

                var Users_update = (from ses in SessionLQ
                                    join u in sourceCardsUsers on ses.Fk_Sn_Name equals u.Sn_Name
                                    //where u.FirsLogin == null
                                    select new HSUser
                                    {
                                        UserName = u.UserName,
                                        Sn_Name = u.Sn_Name,
                                        NasPortId = string.IsNullOrEmpty(u.NasPortId) ? ses.NasPortId : u.NasPortId,
                                        //CountSession = ses.CountSession > u.CountSession ? ses.CountSession : u.CountSession,
                                        CountSession = ses.CountSession + u.CountSession, // نعمل جمع لانه دائما يحذف جلسات الهوتسبوت عند جلبها
                                    }).ToList();

                string query = "update HSUser set [CountSession]=@CountSession,[NasPortId]=@NasPortId where Sn_Name=@Sn_Name";
               
               int efect= Local_DA.Execute<HSUser>(query,Users_update.ToHashSet());
              
                //=========== remove last session from rb ==============

                SourceCardsHotspot_fromMK mk=new SourceCardsHotspot_fromMK();
                if (mk.Remove_files_Session(WithremoveSession,username))
                {

                }

            }

        }

    }
}
