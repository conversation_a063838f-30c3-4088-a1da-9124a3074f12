using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// مشغل اختبارات آمن - يعمل داخل التطبيق الموجود
    /// </summary>
    public static class SafeTestRunner
    {
        /// <summary>
        /// تشغيل واجهة الاختبارات الآمنة
        /// </summary>
        public static void ShowTestMenu()
        {
            var form = new Form
            {
                Text = "🧪 RJTabControl - قائمة الاختبارات",
                Size = new Size(500, 600),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(45, 45, 48),
                FormBorderStyle = FormBorderStyle.FixedDialog,
                MaximizeBox = false,
                MinimizeBox = false
            };

            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            // العنوان
            var titleLabel = new Label
            {
                Text = "🎉 RJTabControl جاهز!",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(460, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الوصف
            var descLabel = new Label
            {
                Text = "اختر نوع الاختبار:",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(20, 70),
                Size = new Size(460, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // الأزرار
            var buttonY = 120;
            var buttonSpacing = 60;

            // زر الاختبار البسيط
            var simpleButton = CreateTestButton("اختبار بسيط", IconChar.Play, 
                Color.FromArgb(0, 122, 204), new Point(150, buttonY));
            simpleButton.Click += (s, e) => {
                form.Hide();
                try { SimpleTabTestForm.RunTest(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر الاختبار السريع
            buttonY += buttonSpacing;
            var quickButton = CreateTestButton("اختبار سريع", IconChar.Bolt, 
                Color.FromArgb(255, 152, 0), new Point(150, buttonY));
            quickButton.Click += (s, e) => {
                form.Hide();
                try { TestRJTabControl.QuickTest(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر الاختبار المتقدم
            buttonY += buttonSpacing;
            var advancedButton = CreateTestButton("اختبار متقدم", IconChar.Cogs,
                Color.FromArgb(76, 175, 80), new Point(150, buttonY));
            advancedButton.Click += (s, e) => {
                form.Hide();
                try { FinalTestForm.RunTest(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر اختبار Constructors
            buttonY += buttonSpacing;
            var constructorButton = CreateTestButton("اختبار Constructors", IconChar.Code,
                Color.FromArgb(156, 39, 176), new Point(150, buttonY));
            constructorButton.Click += (s, e) => {
                form.Hide();
                try { ConstructorTestForm.RunTest(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر عرض طرق AddTab
            buttonY += buttonSpacing;
            var addTabButton = CreateTestButton("طرق AddTab", IconChar.Plus,
                Color.FromArgb(255, 193, 7), new Point(150, buttonY));
            addTabButton.Click += (s, e) => {
                form.Hide();
                try { AddTabMethodsDemo.RunDemo(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر اختبار Designer
            buttonY += buttonSpacing;
            var designerButton = CreateTestButton("دعم Designer", IconChar.PaintBrush,
                Color.FromArgb(233, 30, 99), new Point(150, buttonY));
            designerButton.Click += (s, e) => {
                form.Hide();
                try { DesignerTestForm.RunTest(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر الاختبار النهائي
            buttonY += buttonSpacing;
            var validationButton = CreateTestButton("اختبار نهائي", IconChar.CheckCircle,
                Color.FromArgb(139, 195, 74), new Point(150, buttonY));
            validationButton.Click += (s, e) => {
                form.Hide();
                try { FinalValidationTest.RunValidation(); }
                catch (Exception ex) { ShowError(ex); }
                form.Show();
            };

            // زر الخروج
            buttonY += buttonSpacing;
            var exitButton = CreateTestButton("إغلاق", IconChar.Times,
                Color.FromArgb(244, 67, 54), new Point(150, buttonY));
            exitButton.Click += (s, e) => form.Close();

            // معلومات الحالة
            var statusLabel = new Label
            {
                Text = "✅ جميع الأخطاء مصلحة | ✅ جاهز للاستخدام",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(150, 150, 150),
                Location = new Point(20, 530),
                Size = new Size(460, 20),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // إضافة العناصر
            panel.Controls.Add(titleLabel);
            panel.Controls.Add(descLabel);
            panel.Controls.Add(simpleButton);
            panel.Controls.Add(quickButton);
            panel.Controls.Add(advancedButton);
            panel.Controls.Add(constructorButton);
            panel.Controls.Add(addTabButton);
            panel.Controls.Add(designerButton);
            panel.Controls.Add(validationButton);
            panel.Controls.Add(exitButton);
            panel.Controls.Add(statusLabel);

            form.Controls.Add(panel);
            form.ShowDialog();
        }

        /// <summary>
        /// إنشاء زر اختبار
        /// </summary>
        private static RJButton CreateTestButton(string text, IconChar icon, Color backColor, Point location)
        {
            return new RJButton
            {
                Text = text,
                IconChar = icon,
                Location = location,
                Size = new Size(200, 45),
                BackColor = backColor,
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
        }

        /// <summary>
        /// عرض رسالة خطأ
        /// </summary>
        private static void ShowError(Exception ex)
        {
            MessageBox.Show($"خطأ في تشغيل الاختبار:\n\n{ex.Message}", 
                "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }

        /// <summary>
        /// اختبار سريع للتأكد من عمل الكنترولات
        /// </summary>
        public static void QuickValidationTest()
        {
            QuickErrorTest.RunQuickTest();
        }

        /// <summary>
        /// عرض معلومات سريعة عن RJTabControl
        /// </summary>
        public static void ShowInfo()
        {
            MessageBox.Show("🎉 RJTabControl - التحديث النهائي\n\n" +
                           "✅ الميزات الجديدة:\n" +
                           "• RJTabPage ترث من RJButton\n" +
                           "• tabsPanel و contentPanel الآن RJPanel\n" +
                           "• RJPanel يدعم BorderSize و BorderColor\n" +
                           "• RJTextBox يدعم ReadOnly\n" +
                           "• الألوان الافتراضية محفوظة\n\n" +
                           "✅ جميع الأخطاء مصلحة:\n" +
                           "• GetRoundedPath → GetRoundedGPath\n" +
                           "• IconChar.Border → IconChar.BorderAll\n" +
                           "• IconChar.BorderTop → IconChar.BorderStyle\n" +
                           "• مشاكل Designer محلولة\n\n" +
                           "🚀 جاهز للاستخدام!",
                           "معلومات RJTabControl", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        /// <summary>
        /// اختبار إنشاء TabControl بسيط
        /// </summary>
        public static void CreateSimpleTabControl()
        {
            var form = new Form
            {
                Text = "RJTabControl - مثال بسيط",
                Size = new Size(600, 400),
                StartPosition = FormStartPosition.CenterScreen
            };

            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 8
            };

            // تاب الرئيسية
            var homeTab = tabControl.AddTab("الرئيسية", IconChar.Home);
            var homeLabel = new Label
            {
                Text = "🏠 مرحباً بك!\n\nهذا مثال بسيط على RJTabControl الجديد",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            homeTab.AddControl(homeLabel);

            // تاب النص
            var textTab = tabControl.AddTab("نص", IconChar.Edit);
            var textBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 هذا RJTextBox مع ReadOnly = true\n\nلا يمكن تعديل هذا النص!",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 1,
                BorderColor = Color.FromArgb(200, 200, 200),
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };
            textTab.AddControl(textBox);

            form.Controls.Add(tabControl);
            form.ShowDialog();
        }
    }
}
