﻿using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounts
{
    public class Product
    {
        [PrimaryKey,Required]
        public int Id { get; set; }
        [DisplayName("الكود")]
        public string Code { get; set; }
        [DisplayName("اسم المنتج")]
        public string Name { get; set; }
        [DisplayName("الاسم الضاهر"), Browsable(false)]
        public string Name_eng { get; set; }
        [DisplayName("الوصف")]
        public string Description { get; set; }
        [DisplayName("صورة المنتج")]
        public Byte[] Image { get; set; } 
        [DisplayName("السعر")]
        public float Price { get; set; } = 0;
        [DisplayName("وحدة القياس")]
        public int? Product_UomId { get; set; } = null;

        [DisplayName("ح/المبيعات")]
        public int? Account_IncomeId { get; set; }= null;
        [DisplayName("ح/مردود المبيعات")]
        public int? Account_ExpenseId { get; set; }= null;

        public int Active { get; set; } = 1;
        [DisplayName("مفعل")]
        public bool Str_Active {
            get
            {
                return Convert.ToBoolean(Active);
            }
        }

        [DisplayName("متاح في نقاط البيع"),Browsable(false)]
        public int Available_In_Pos { get; set; } = 0;

        public string Rb {  get; set; }

    }

    public class ProductUoM
    {
        [Required, PrimaryKey,DisplayName("الرقم")]
        public int Id { get; set; }

        [DisplayName("الكود")]
        public string Code { get; set; }

        [DisplayName("وحدة القياس")]
        public string Name { get; set; }
        public string Rb { get; set; }
    }

}
