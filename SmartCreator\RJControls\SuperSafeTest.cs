using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار النسخة الآمنة جداً - بدون تاب افتراضي
    /// </summary>
    public partial class SuperSafeTest : Form
    {
        private SuperSafeRJTabControl superSafeControl;

        public SuperSafeTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // superSafeControl
            // 
            this.superSafeControl = new SuperSafeRJTabControl();
            this.superSafeControl.Dock = DockStyle.Fill;
            this.superSafeControl.TabHeight = 40;
            this.superSafeControl.TabSpacing = 3;
            this.superSafeControl.TabPadding = 20;

            // 
            // SuperSafeTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.superSafeControl);
            this.Name = "SuperSafeTest";
            this.Text = "🚀 اختبار SuperSafeRJTabControl - بدون تاب افتراضي";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += SuperSafeTest_Load;
        }

        private void SuperSafeTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية برمجياً
                AddSuperSafeTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ يعمل بمثالية!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل SuperSafeRJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل!";
            }
        }

        private void AddSuperSafeTabs()
        {
            // تاب الترحيب
            var welcomeTab = this.superSafeControl.AddTab("🚀 مرحباً", IconChar.Home);
            if (welcomeTab != null)
            {
                var welcomeLabel = new Label
                {
                    Text = "🚀 SuperSafeRJTabControl يعمل!\n\n" +
                           "✅ بدون تاب افتراضي في Constructor\n" +
                           "✅ بدون مشاكل NullReference\n" +
                           "✅ آمن 100% للـ Designer\n" +
                           "✅ يبدأ فارغ تماماً\n\n" +
                           "🎯 الآن جرب سحب SuperSafeRJTabControl\n" +
                           "من Toolbox في Visual Studio!\n\n" +
                           "يجب أن يظهر فارغ بدون أي تابات\n" +
                           "ويمكنك إضافة التابات برمجياً",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(0, 122, 204),
                    Padding = new Padding(20)
                };
                welcomeTab.AddControl(welcomeLabel);
            }

            // تاب الاختبار
            var testTab = this.superSafeControl.AddTab("اختبار", IconChar.Play);
            if (testTab != null)
            {
                var testPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

                var addTabButton = new RJButton
                {
                    Text = "إضافة تاب",
                    IconChar = IconChar.Plus,
                    Location = new Point(20, 20),
                    Size = new Size(150, 40),
                    BackColor = Color.FromArgb(76, 175, 80),
                    ForeColor = Color.White,
                    BorderRadius = 8,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold)
                };
                addTabButton.Click += (s, e) => {
                    try
                    {
                        var newTab = this.superSafeControl.AddTab($"تاب {this.superSafeControl.TabCount + 1}", IconChar.Star);
                        if (newTab != null)
                        {
                            var label = new Label
                            {
                                Text = $"🌟 تاب جديد!\n\nرقم: {this.superSafeControl.TabCount}\nوقت الإنشاء: {DateTime.Now:HH:mm:ss}",
                                Dock = DockStyle.Fill,
                                TextAlign = ContentAlignment.MiddleCenter,
                                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                                ForeColor = Color.FromArgb(156, 39, 176)
                            };
                            newTab.AddControl(label);
                            this.superSafeControl.SelectedTab = newTab;
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                var clearTabsButton = new RJButton
                {
                    Text = "مسح جميع التابات",
                    IconChar = IconChar.Trash,
                    Location = new Point(190, 20),
                    Size = new Size(150, 40),
                    BackColor = Color.FromArgb(244, 67, 54),
                    ForeColor = Color.White,
                    BorderRadius = 8,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold)
                };
                clearTabsButton.Click += (s, e) => {
                    try
                    {
                        this.superSafeControl.ClearAllTabs();
                        MessageBox.Show("✅ تم مسح جميع التابات!", "تم المسح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                var testPropsButton = new RJButton
                {
                    Text = "اختبار الخصائص",
                    IconChar = IconChar.Cogs,
                    Location = new Point(360, 20),
                    Size = new Size(150, 40),
                    BackColor = Color.FromArgb(255, 152, 0),
                    ForeColor = Color.White,
                    BorderRadius = 8,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold)
                };
                testPropsButton.Click += (s, e) => {
                    try
                    {
                        var results = $"🧪 نتائج اختبار الخصائص:\n\n" +
                                     $"TabCount: {this.superSafeControl.TabCount}\n" +
                                     $"SelectedIndex: {this.superSafeControl.SelectedIndex}\n" +
                                     $"TabHeight: {this.superSafeControl.TabHeight}\n" +
                                     $"TabSpacing: {this.superSafeControl.TabSpacing}\n" +
                                     $"TabPadding: {this.superSafeControl.TabPadding}\n\n" +
                                     "✅ جميع الخصائص تعمل!";

                        MessageBox.Show(results, "نتائج الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                };

                var infoLabel = new Label
                {
                    Text = "ℹ️ معلومات SuperSafeRJTabControl:\n\n" +
                           "🎯 الهدف: كنترول آمن 100% بدون تاب افتراضي\n\n" +
                           "🚀 الميزات:\n" +
                           "• يبدأ فارغ تماماً\n" +
                           "• لا يضيف تابات افتراضية\n" +
                           "• آمن تماماً في Designer\n" +
                           "• يمكن إضافة التابات برمجياً\n" +
                           "• معالجة شاملة للأخطاء\n\n" +
                           "🧪 في Designer:\n" +
                           "سيظهر فارغ بدون أي تابات\n" +
                           "يمكنك إضافة التابات في الكود\n\n" +
                           "✅ هذا يحل مشكلة التابات المضاعفة!",
                    Location = new Point(20, 80),
                    Size = new Size(500, 200),
                    Font = new Font("Segoe UI", 10),
                    ForeColor = Color.FromArgb(70, 70, 70)
                };

                testPanel.Controls.Add(addTabButton);
                testPanel.Controls.Add(clearTabsButton);
                testPanel.Controls.Add(testPropsButton);
                testPanel.Controls.Add(infoLabel);
                testTab.AddControl(testPanel);
            }

            // تفعيل التاب الأول
            this.superSafeControl.SelectedIndex = 0;
        }

        /// <summary>
        /// تشغيل اختبار النسخة الآمنة جداً
        /// </summary>
        public static void RunSuperSafeTest()
        {
            try
            {
                var form = new SuperSafeTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار SuperSafeRJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء SuperSafeRJTabControl فقط
        /// </summary>
        public static void TestSuperSafeCreation()
        {
            try
            {
                var control = new SuperSafeRJTabControl();
                
                // اختبار الخصائص
                var tabCount = control.TabCount;
                var selectedIndex = control.SelectedIndex;
                var tabHeight = control.TabHeight;
                
                MessageBox.Show($"✅ SuperSafeRJTabControl تم إنشاؤه بنجاح!\n\n" +
                               $"TabCount: {control.TabCount} (يجب أن يكون 0)\n" +
                               $"SelectedIndex: {control.SelectedIndex} (يجب أن يكون -1)\n" +
                               $"TabHeight: {control.TabHeight}\n\n" +
                               "🎯 الآن جرب سحبه من Toolbox في Designer!\n" +
                               "يجب أن يظهر فارغ بدون أي تابات",
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء SuperSafeRJTabControl:\n\n{ex.Message}\n\n{ex.StackTrace}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
