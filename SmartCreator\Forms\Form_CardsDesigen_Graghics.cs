﻿using DevComponents.DotNetBar.Metro;
using iTextSharp.text;
using iTextSharp.text.pdf;
using iTextSharp.text.pdf.qrcode;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.CardsDesigen;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.RJForms.Private;
using SmartCreator.Settings;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Drawing.Text;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
 
using Newtonsoft.Json.Serialization;

//using static System.Windows.Forms.VisualStyles.VisualStyleElement;
//using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;

namespace SmartCreator.Forms
{
    public partial class Form_CardsDesigen_Graghics : RJChildForm
    {
        private Point MouseDownLocation;
        private bool firstLoad = true;
        private bool saveToClass = true;
        private bool selectTemplateFromDrowpDown = true;
        private bool selectTemplateFromCheckBox = true;
        public static string pathfile = "";
        bool mouseClicked = false;
        public DataTable dt_templateCards;
        public CardsTemplate card;
        bool addDeletTemplate = false;
        decimal numvalue = 0;
        public Form_CardsDesigen_Graghics()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);



            TCResize resizablePnel = new TCResize(this.pictureBox1);
            TCResize resizablePne5 = new TCResize(this.pictureBox_logo);
            TCResize resizablePne3 = new TCResize(this.pictureBox_QR);

            this.Text = "استديو التصميم";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Desigen studio";
            }
            if (UIAppearance.Theme == UITheme.Dark)
            {
            rjLabel8.ForeColor =utils.Dgv_DarkColor;
                //rjPanel1.Customizable = false;
            }
            else
            {
                rjLabel8.ForeColor = Color.Red;
            }

            set_fonts();
            //txt_dimension_w.BackColor = txt_dimension_H.BackColor = txt_Space_X.BackColor = txt_Space_Y.BackColor=txt_Number_Page_X.BackColor = txt_Number_Page_Y.BackColor = txt_Number_Page_Size.BackColor = UIAppearance.BackgroundColor;
            //txt_Note_Page_Y.BackColor = TextCard_Y.BackColor = TextCard_W.BackColor = UIAppearance.BackgroundColor;
            //txt_SizeBorder.BackColor = txt_Note_Page_Size.BackColor = txt_Note_Page_X.BackColor = UIAppearance.BackgroundColor;
            //txt_Element_W.BackColor = txt_Element_Y.BackColor = UIAppearance.BackgroundColor;

            //System.Drawing.Font fnm = new System.Drawing.Font(UIAppearance.TextFamilyName, UIAppearance.TextSize);

            //txt_dimension_w.Font = txt_dimension_H.Font = txt_Space_X.Font = txt_Space_Y.Font= txt_Number_Page_X.Font = txt_Number_Page_Y.Font = txt_Number_Page_Size.Font = fnm;
            //txt_Note_Page_Y.Font = TextCard_Y.Font = TextCard_W.Font = fnm;
            //txt_SizeBorder.Font = txt_Note_Page_Size.Font = txt_Note_Page_X.Font = fnm;
            //txt_Element_W.Font = txt_Element_Y.Font = fnm;

            //txt_dimension_w.ForeColor = txt_dimension_H.ForeColor = txt_Space_Y.ForeColor= txt_Space_X.ForeColor = txt_Number_Page_X.ForeColor = txt_Number_Page_Y.ForeColor = txt_Number_Page_Size.ForeColor = UIAppearance.TextColor;
            //txt_Note_Page_Y.ForeColor = TextCard_Y.ForeColor = TextCard_W.ForeColor = UIAppearance.TextColor;
            //txt_SizeBorder.ForeColor = txt_Note_Page_Size.ForeColor = txt_Note_Page_X.ForeColor = UIAppearance.TextColor;
            //txt_Element_W.ForeColor = txt_Element_Y.ForeColor = UIAppearance.TextColor;

        }

        #region Function Util
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel) || C.GetType() != typeof(Form) || C.GetType() != typeof(MetroForm))
                            C.Font = new System.Drawing.Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        //if (C.Controls.Count > 0)
                        //    Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }
        private void Get_Cbox_Profile()
        {

            //if (selectTemplateFromDrowpDown || firstLoad)
            //    return;
            //CBox_Profile.DataSource = Global_Variable.UM_Profile;
            //CBox_Profile.DisplayMember = "Name";
            //CBox_Profile.ValueMember = "Name";
            //CBox_Profile.SelectedIndex = -1;
            //CBox_Profile.Text = "";

            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                }
            }
            catch { }


        }
        void get_Profile_Hotspot()
        {
            //try
            //{
            //    CBox_Profile_HS.DataSource = Global_Variable.Sorce_HS_Profile;
            //    CBox_Profile_HS.DisplayMember = "Name";
            //    CBox_Profile_HS.ValueMember = "Name";

            //    CBox_Profile_HS.SelectedIndex = -1;
            //    CBox_Profile_HS.Text = "";
            //}
            //catch { }
            try
            {
                List<Hotspot_Source_Profile> sp = Global_Variable.Source_HS_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (Hotspot_Source_Profile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile_HS.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile_HS.DisplayMember = "Value";
                    CBox_Profile_HS.ValueMember = "Key";
                }
            }
            catch { }
        }
        private void Get_TemplateCardsFromDB()
        {
            try
            {
                CBox_TemplateCards.Items.Clear();
                //CBox_TemplateCards = null;
            }
            catch { }
            try
            {
                List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("design");
                CBox_TemplateCards.DataSource = sourceCardsTemplate;
                CBox_TemplateCards.DisplayMember = "Name";
                CBox_TemplateCards.ValueMember = "id";
                CBox_TemplateCards.SelectedIndex = -1;
                if (sourceCardsTemplate.Count <= 0 || sourceCardsTemplate == null)
                {
                    CreateDefultTemplate();
                    //SetValuToCardToGraphics();
                }
            }
            catch { }
            //try
            //{
            //    if (dt_templateCards.Rows.Count <= 0)
            //    {
            //        CreateDefultTemplate();
            //        //SetValuToCardToGraphics();
            //    }
            //}
            //catch { }

        }
        private void set_fonts()
        {
            if(UIAppearance.Theme==UITheme.Dark)
            {
                //rjPanel13.Customizable = false;
                rjLabel29.ForeColor=UIAppearance.TextColor;
                rjLabel35.ForeColor=UIAppearance.TextColor;
                rjLabel19.ForeColor=UIAppearance.TextColor;
                rjLabel23.ForeColor=UIAppearance.TextColor;
                rjLabel42.ForeColor=UIAppearance.TextColor;
                checkBoxBorderCard.ForeColor=UIAppearance.TextColor;
                check_Number_Pages.ForeColor=UIAppearance.TextColor;
                checkShowAddresItem.ForeColor=UIAppearance.TextColor;
                checkShowUnit_Item.ForeColor=UIAppearance.TextColor;
            }
            //return;



            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            btnPreviewTemplate.Font = title_font;
            //rjLabel27Title.Font = title_font;
            //rjLabel28Title.Font = title_font;
            //rjLabel11Title.Font = title_font;

            System.Drawing.Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            BackgroundImgCard_Chbox.Font=fnt;
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                  else  if (contrl.GetType() == typeof(RJControls.RJPanel))
                    {
                        RJPanel pnl = (RJPanel)contrl;

                        foreach (var contrl2 in pnl.Controls)
                        {
                            try
                            {
                                if (contrl2.GetType() == typeof(RJControls.RJLabel))
                                {
                                    RJLabel lbl = (RJLabel)contrl2;
                                    lbl.Font = fnt;
                                }
                               else if ( contrl2.GetType() == typeof(RJControls.RJToggleButton))
                                {
                                    RJToggleButton lbl = (RJToggleButton)contrl2;
                                    lbl.Font = fnt;
                                }
                            }
                            catch { }
                        }

                    }




                }
                catch { }
            }

            utils.Control_textSize(pnlClientArea);
            return;

            //Control_Loop(pnlClientArea);
        }
        public void CreateDefultTemplate()
        {

            SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
            if (sourceCardsTemplate.CreateDefaultTemplate(true))
            {

            }

            card = new CardsTemplate();
            card.setingCard.name = "default1";
            card.setingCard.type = "design";
            return;

            //string stringjson = JsonConvert.SerializeObject(card);
        }

        void setControlToPict()
        {
            pictureBox1.Controls.Add(Login_Lbl);
            pictureBox1.Controls.Add(Password_Lbl);
            pictureBox1.Controls.Add(lbl_Squ_Nuber);
            pictureBox1.Controls.Add(Lbl_SizeTransfer);
            pictureBox1.Controls.Add(Lbl_Time);
            pictureBox1.Controls.Add(Lbl_validity);
            pictureBox1.Controls.Add(Lbl_Price);
            pictureBox1.Controls.Add(Lbl_OtherText1);
            pictureBox1.Controls.Add(Lbl_Date_Print);
            pictureBox1.Controls.Add(Lbl_Number_Print);
            pictureBox1.Controls.Add(Lbl_Batch_Print);
            pictureBox1.Controls.Add(pictureBox_QR);
            pictureBox1.Controls.Add(pictureBox_logo);
            pictureBox1.Controls.Add(Lbl_SP);

            //lbl_Abaad.Visible = false;
            //groupBox_Abaad.Visible = false;
            groupBox_Abaad.Enabled = false;
            //groupBox_Abaad.Location = new Point(395, 163);
        }
        private void disableAll_Lable()
        {
            foreach (Control lb in pictureBox1.Controls)
            {
                lb.Visible = false;
            }
            foreach (Control lb in panel2.Controls)
            {
                if (lb.GetType() == typeof(System.Windows.Forms.CheckBox))
                {
                    RJCheckBox ch = new RJCheckBox();
                    ch = (RJCheckBox)lb;
                    ch.Checked = false;
                }
            }
        }
        private void Get_TemplateCardsItemsGraphics()
        {
            try
            {
                if (addDeletTemplate) return;
                SourceCardsTemplate sorceTemplate = SqlDataAccess.Get_template_cards_By_Name(CBox_TemplateCards.Text.ToString());
                if (sorceTemplate == null)
                    return;
                if (sorceTemplate.type == "design")
                {
                    //card = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values);

                    ////string json = JsonConvert.SerializeObject(person, Formatting.Indented);

                    //card.rb = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values).rb;
                    //card.setingCard = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values).setingCard;
                    //card.id = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values).id;
                    //card.name = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values).name;
                    //card.type = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values).type;

                    //try
                    //{
                    //    JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values, new JsonSerializerSettings
                    //    {
                    //        MissingMemberHandling = MissingMemberHandling.Ignore
                    //    });
                    //}
                    //catch (JsonSerializationException ex)
                    //{
                    //    Console.WriteLine(ex.Message);
                    //    // Could not find member 'DeletedDate' on object of type 'Account'. Path 'DeletedDate', line 4, position 23.
                    //}

                    //var settings = new JsonSerializerSettings
                    //{
                    //    Error = OnError,
                    //    MissingMemberHandling = MissingMemberHandling.Error
                    //};

                    //var deserialized = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplate.values, settings);


                }
            } catch (Exception ex) { MessageBox.Show(ex.Message);}
            //try
            //{
            //    card = SqlDataAccess.Get_template_cards_By_Name(CBox_TemplateCards.SelectedValue.ToString());
            //}
            //catch (Exception ex) { MessageBox.Show("Get_TemplateCardsItemsGraphics   " + ex.Message); }

        }
        //void OnError(object sender, ErrorEventArgs args)
        //{
        //    //Console.WriteLine("Unable to find member '{0}' on object of type {1}", args.ErrorContext.Member, args.ErrorContext.OriginalObject.GetType().Name);

        //    // set the current error as handled
        //    args.ErrorContext.Handled = true;
        //}
        void SetValuToCardToGraphics()
        {
            if (card == null) return;
            pictureBox1.Image = null;

            BackgroundImgCard_Chbox.Checked = card.setingCard.enable_background;
            txt_Pathfile.Text = card.setingCard.path_saved_file.ToString();
            //txt_PathImage.Text = card.setingCard.path_background.ToString();
            if (BackgroundImgCard_Chbox.Checked) set_BackroundFromPath(true);

            comboBox_quilty_image.SelectedIndex = card.setingCard.quilty_image;

            CBox_Curncey.Text = card.setingCard.currency;
            TextCard_W.Value = card.setingCard.card_width;
            TextCard_Y.Value = card.setingCard.card_height;
            txt_Space_X.Value = card.setingCard.space_horizontal_margin;
            txt_Space_Y.Value = card.setingCard.Space_vertical_margin;
            checkBoxBorderCard.Checked = card.setingCard.card_border_enable;
            txt_SizeBorder.Value = (decimal)card.setingCard.card_border_Size;
            btn_BorderColor.BackColor = System.Drawing.ColorTranslator.FromHtml(card.setingCard.card_border_Color);

            check_Number_Pages.Checked = card.setingCard.Number_Pages;
            txt_Number_Page_Size.Value = card.setingCard.Number_Pages_Size;
            txt_Number_Page_X.Value = card.setingCard.Number_Pages_X;
            txt_Number_Page_Y.Value = card.setingCard.Number_Pages_Y;

            checkNoteOnPage.Checked = card.setingCard.Note_On_Pages;
            txt_Note_Page_Size.Value = card.setingCard.Note_On_Pages_Size;
            txt_Note_Page_X.Value = card.setingCard.Note_On_Pages_X;
            txt_Note_Page_Y.Value = card.setingCard.Note_On_Pages_Y;
            txt_Note_onPage.Text = card.setingCard.Note_On_Pages_text;
            CBox_NoteType_onPage.SelectedIndex = card.setingCard.NoteType_onPage;

            pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(TextCard_W.Text)) * (decimal)7.0);
            pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(TextCard_Y.Text)) * (decimal)7.0);
            try { CBox_Profile.Text = card.setingCard.proile_link; } catch { }
            try { CBox_Profile_HotspotLocal.Text = card.setingCard.proile_HS_Local_link; } catch { }
            //try { CBox_Profile.Text = card.setingCard.proile_link; } catch { }
            try { CBox_Profile_HS.Text = card.setingCard.proile_HS_link; } catch { }
            //try { CBox_Profile.SelectedItem = card.setingCard.proile_link; } catch { }
            //try { CBox_Profile_HS.SelectedItem = card.setingCard.proile_HS_link; } catch { }

            txt_LogoImage.Text = card.cardsItems.logo.Path;
            set_value_For_item();

            Calclate_Number_Card_In_Page();
        }
        void set_value_For_item()
        {
            //============login=================
            Set_Proprties_For_Item(Login_Lbl, card.cardsItems.login);
            ChangPositonControl_In_Imag(Login_Lbl, card.cardsItems.login);
            Change_Font_Size_Bold_Color_Control_In_Imag(Login_Lbl, card.cardsItems.login);
            Login_Chbox.Checked = card.cardsItems.login.Enable;

            //checkShowAddresItem.Checked = card.cardsItems.login.title_show;
            //txt_AddresItem.Text = card.cardsItems.login.title_text;


            //============password=================
            Set_Proprties_For_Item(Password_Lbl, card.cardsItems.Password);
            ChangPositonControl_In_Imag(Password_Lbl, card.cardsItems.Password);
            Change_Font_Size_Bold_Color_Control_In_Imag(Password_Lbl, card.cardsItems.Password);
            Password_Chbox.Checked = card.cardsItems.Password.Enable;

            //checkShowAddresItem.Checked = card.cardsItems.Password.title_show;
            //txt_AddresItem.Text = card.cardsItems.Password.title_text;

            //============Price=================
            Set_Proprties_For_Item(Lbl_Price, card.cardsItems.Price);
            ChangPositonControl_In_Imag(Lbl_Price, card.cardsItems.Price);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Price, card.cardsItems.Price);
            Price_Chbox.Checked = card.cardsItems.Price.Enable;
            //============Lbl_Time=================
            Set_Proprties_For_Item(Lbl_Time, card.cardsItems.Time);
            ChangPositonControl_In_Imag(Lbl_Time, card.cardsItems.Time);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Time, card.cardsItems.Time);
            Time_Chbox.Checked = card.cardsItems.Time.Enable;
            CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;

            //============Lbl_SizeTransfer=================
            Set_Proprties_For_Item(Lbl_SizeTransfer, card.cardsItems.Size);
            ChangPositonControl_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            Size_Chbox.Checked = card.cardsItems.Size.Enable;
            //CBox_UniteTransfer_format.Visible = true;
            CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;

            //============Lbl_validity=================
            Set_Proprties_For_Item(Lbl_validity, card.cardsItems.Validity);
            ChangPositonControl_In_Imag(Lbl_validity, card.cardsItems.Validity);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_validity, card.cardsItems.Validity);
            validity_Chbox.Checked = card.cardsItems.Validity.Enable;
            CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;

            //============lbl_Squ_Nuber=================
            Set_Proprties_For_Item(lbl_Squ_Nuber, card.cardsItems.SN);
            ChangPositonControl_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            Change_Font_Size_Bold_Color_Control_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            Squnce_Number_Chbox.Checked = card.cardsItems.SN.Enable;
            //============SP=================
            Set_Proprties_For_Item(Lbl_SP, card.cardsItems.SP);
            ChangPositonControl_In_Imag(Lbl_SP, card.cardsItems.SP);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SP, card.cardsItems.SP);
            SP_Chbox.Checked = card.cardsItems.SP.Enable;
            try
            {
                CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);

            }
            catch { }

            //============Lbl_Number_Print=================
            Set_Proprties_For_Item(Lbl_Number_Print, card.cardsItems.Number_Print);
            ChangPositonControl_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            Number_Print_Chbox.Checked = card.cardsItems.Number_Print.Enable;

            //============Lbl_Batch_Print=================
            Set_Proprties_For_Item(Lbl_Batch_Print, card.cardsItems.BatchNumber);
            ChangPositonControl_In_Imag(Lbl_Batch_Print, card.cardsItems.BatchNumber);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Batch_Print, card.cardsItems.BatchNumber);
            Number_Batch_Chbox.Checked = card.cardsItems.BatchNumber.Enable;

            //============Lbl_OtherText1=================
            Set_Proprties_For_Item(Lbl_OtherText1, card.cardsItems.Other_Text1);
            ChangPositonControl_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            OtherText1_Chbox.Checked = card.cardsItems.Other_Text1.Enable;
            txt_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            Lbl_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            //Lbl_OtherText1.Text=card.cardsItems.Other_Text1.title_text;
            //checkShowAddresItem.Checked = false;
            //txt_AddresItem.Text = "";

            ////============Lbl_OtherText2=================
            //Set_Proprties_For_Item(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //ChangPositonControl_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            //OtherText1_Chbox.Checked = card.cardsItems.Other_Text2.Enable;
            //============Lbl_Date_Print=================
            Set_Proprties_For_Item(Lbl_Date_Print, card.cardsItems.Date_Print);
            ChangPositonControl_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            Date_Print_Chbox.Checked = card.cardsItems.Date_Print.Enable;
            //card.cardsItems.SP.Show_ByNumber_OR_Name = !Convert.ToBoolean(CBox_SellingPoint.SelectedIndex);
            CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;
            //============QR=================
            Set_Proprties_For_Item_img(pictureBox_QR, card.cardsItems.QR);
            ChangPositonControl_IMAGE_In_Imag(pictureBox_QR, card.cardsItems.QR);
            QR_Chbox.Checked = card.cardsItems.QR.Enable;
            //============logo=================
            Set_Proprties_For_Item_img(pictureBox_logo, card.cardsItems.logo);
            ChangPositonControl_IMAGE_In_Imag(pictureBox_logo, card.cardsItems.logo);
            Logo_Chbox.Checked = card.cardsItems.logo.Enable;
            try
            {
                Bitmap img = new Bitmap(card.cardsItems.logo.Path);
                pictureBox_logo.Image = System.Drawing.Image.FromFile(card.cardsItems.logo.Path);
            }
            catch { /*MessageBox.Show("خطأ في صوره الشعار"); */}


        }
        void Set_Proprties_For_Item(Label lbl, PropertyItemText loc)
        {
            try
            {
                lbl.Visible = loc.Enable;

                txt_Element_W.Value = loc.x;
                txt_Element_Y.Value = loc.y;

                Color color = ColorTranslator.FromHtml(loc.Color);
                btn_ElementColor.BackColor = color;
                lbl.ForeColor = color;

                CB_ElementSize.Text = loc.font_size.ToString();
                txt_font.Text = loc.Font;
                CB_Fonts.Text = loc.Font;
                //CB_Fonts.SelectedItem = loc.Font;

                checkShowAddresItem.Checked = loc.title_show;
                txt_AddresItem.Text = loc.title_text;
                //txt_AddresItem.Text = loc.title_text;
                //checkShowAddresItem.Checked = loc.title_show;

                //checkShowUnit_Item.Checked = loc.unit_show;
                checkBoxISBlod.Checked = loc.Blod;
                checkBoxIsItalic.Checked = loc.italic;

                CBox_UniteTime_format.Visible = false;
                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteValidaty_format.Visible = false;
                //checkShowUnit_Item


            }
            catch { }

            //if(lbl.Name == "Lbl_Date_Print")
            //{
            //    CBox_Date_print.SelectedItem = loc.address_text;
            //}
            ////ChangPositonControl_In_Imag(lbl);
        }
        void Set_Proprties_For_Item_img(Control item, PropertyItemImage property)
        {
            try
            {
                item.Visible = property.Enable;

                txt_Element_W.Value = Convert.ToDecimal(property.x);
                txt_Element_Y.Value = Convert.ToDecimal(property.y);

                txt_dimension_w.Value = Convert.ToDecimal(property.item_dimension_w);
                txt_dimension_H.Value = Convert.ToDecimal(property.item_dimension_y);


                //Color color = ColorTranslator.FromHtml(property.Color);
                //btn_Element_panelColor.BackColor = color;

                item.Width = Convert.ToInt16((Convert.ToDecimal(property.item_dimension_w)) * (decimal)7.0);
                item.Height = Convert.ToInt16((Convert.ToDecimal(property.item_dimension_w)) * (decimal)7.0);

                item.BringToFront();

                //if (item.GetType() == typeof(Panel))
                //{
                //    item.BackColor = color;
                //}
                //if (item.GetType() == typeof(PictureBox))
                //{
                //    item.BackColor = color;
                //}


                //CB_ElementSize.Text = loc.Size_text.ToString();
                //txt_font.Text = loc.Font;
                //CB_Fonts.SelectedItem = loc.Font;

                //checkShowAddresItem.Checked = loc.address_show;
                //txt_AddresItem.Text = loc.address_text;


                //checkShowUnit_Item.Checked = loc.unit_show;
                //checkBoxISBlod.Checked = loc.Blod;
                //checkBoxIsItalic.Checked = loc.italic;
            }
            catch (Exception e) { MessageBox.Show("Set_Proprties_For_Item_img erroooor"); }
            //ChangPositonControl_In_Imag(lbl);
        }
        void ChangPositonControl_In_Imag(Control Control_lable, PropertyItemText loc)
        {
            try
            {
                if (!firstLoad)
                {
                    Point point = new Point();
                    point = Control_lable.Location;
                    int X = (int)(float.Parse(loc.x.ToString()) * (float)7.0);
                    int Y = (int)(float.Parse(loc.y.ToString()) * (float)7.0);

                    Control_lable.Location = new Point(X, Y);

                    //loc_itemtemp.x = X/5;
                    //loc_itemtemp.y = Y/5;
                    //Loc_item_Save_in_struct();
                }
            }
            catch (Exception ex) { MessageBox.Show("ChangPositonControl_In_Imag  " + loc); }
            //loc_itemtemp.x = Publi_lbl_Use.Location.X ;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y ;
            //loc_itemtemp.x = Publi_lbl_Use.Location.X / 5;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y / 5;

        }
        void ChangPositonControl_In_Imag(Control Control_lable)
        {

            if (!firstLoad)
            {
                Point point = new Point();
                point = Control_lable.Location;
                int X = (int)(float.Parse(txt_Element_W.Text) * (float)7.0);
                int Y = (int)(float.Parse(txt_Element_Y.Text) * (float)7.0);
                if (X < 0)
                    X = 0;
                if (Y < 0)
                    Y = 0;
                Control_lable.Location = new Point(X, Y);

                //loc_itemtemp.x = X/5;
                //loc_itemtemp.y = Y/5;
                //Loc_item_Save_in_struct();
            }

            //loc_itemtemp.x = Publi_lbl_Use.Location.X ;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y ;
            //loc_itemtemp.x = Publi_lbl_Use.Location.X / 5;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y / 5;
        }
       void Change_Dimension_ControlImag(Control Control_lable)
        {

            if (!firstLoad)
            {
                Control_lable.Width = (int)(txt_dimension_w.Value * (decimal)7.0);
                Control_lable.Height = (int)(txt_dimension_H.Value * (decimal)7.0);
            }
        }
       
        void ChangPositonControl_IMAGE_In_Imag(Control Control_lable, PropertyItemImage loc)
        {
            try
            {
                if (!firstLoad)
                {
                    Point point = new Point();
                    point = Control_lable.Location;
                    int X = (int)(float.Parse(loc.x.ToString()) * (float)7.0);
                    int Y = (int)(float.Parse(loc.y.ToString()) * (float)7.0);

                    Control_lable.Location = new Point(X, Y);

                    //loc_itemtemp.x = X/5;
                    //loc_itemtemp.y = Y/5;
                    //Loc_item_Save_in_struct();
                }
            }
            catch (Exception ex) { MessageBox.Show("ChangPositonControl_In_Imag  " + loc); }
            //loc_itemtemp.x = Publi_lbl_Use.Location.X ;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y ;
            //loc_itemtemp.x = Publi_lbl_Use.Location.X / 5;
            //loc_itemtemp.y = Publi_lbl_Use.Location.Y / 5;

        }
        void Change_Font_Size_Bold_Color_Control_In_Imag(Label lbl, PropertyItemText loc)
        {
            //loc.Font = txt_font.Text;
            //loc.Size_text = Convert.ToInt32(CB_ElementSize.Text);
            //loc.Blod = checkBoxISBlod.Checked;
            //loc.italic = checkBoxISBlod.Checked;

            if (loc.Blod)
            {
                if (loc.italic)
                    lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size + 5, FontStyle.Italic | FontStyle.Bold);
                else lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size + 5, FontStyle.Bold);
            }
            else
            {
                if (loc.italic) lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size + 5, FontStyle.Italic | FontStyle.Regular);
                else lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size + 5, FontStyle.Regular);
            }
            Color color = ColorTranslator.FromHtml(loc.Color);
            //String Color = System.Drawing.ColorTranslator.ToHtml(color);            
            lbl.ForeColor = color;
            btn_ElementColor.BackColor = color;

            //#############
            if (loc.title_show)
            {

            }
        }
        void check_Location_control_isOut(Control contr)
        {
            try
            {
                Point point = new Point();
                point = contr.Location;
                decimal X = (point.X / (decimal)7.0);
                decimal Y = (point.Y / (decimal)7.0);


                if (X < 0)
                {
                    contr.Left = 0;
                    //contr.Top = point.Y;
                }
                if (X > TextCard_W.Value - 2)
                {
                    contr.Left = Convert.ToInt32((TextCard_W.Value * 6) - 40);
                    //contr.Top = point.Y;
                }

                if (Y < 0)
                {
                    //contr.Left = point.X;
                    contr.Top = 0;
                }
                if (Y > TextCard_Y.Value - 1)
                {
                    //Login_Lbl.Left = point.X;
                    contr.Top = Convert.ToInt32((TextCard_Y.Value * 6) - 30);
                }

                //txt_Element_W.Text = X.ToString("0.0");
                //txt_Element_Y.Text = Y.ToString("0.0");
                //card.cardsItems.login.x = X;
                //card.cardsItems.login.y = Y;


                //control.Left = e.X + control.Left - MouseDownLocation.X;
                //control.Top = e.Y + control.Top - MouseDownLocation.Y;

            }
            catch { }
        }
        public void Loc_item_Set_To_Use()
        {
            if (saveToClass == false)
                return;

            if (Login_Lbl.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.login.Font = txt_font.Text;
                card.cardsItems.login.Blod = checkBoxISBlod.Checked;
                card.cardsItems.login.italic = checkBoxIsItalic.Checked;
                card.cardsItems.login.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.login.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.login.y = txt_Element_Y.Value;
                card.cardsItems.login.x = txt_Element_W.Value;
                card.cardsItems.login.title_show = checkShowAddresItem.Checked;
                card.cardsItems.login.title_text = txt_AddresItem.Text;
                card.cardsItems.login.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                Login_Lbl.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Login_Lbl, card.cardsItems.login);
                ChangPositonControl_In_Imag(Login_Lbl, card.cardsItems.login);

            }

            else if (Password_Lbl.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Password.Font = txt_font.Text;
                card.cardsItems.Password.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Password.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Password.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Password.Color = System.Drawing.ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                Password_Lbl.ForeColor = btn_ElementColor.BackColor;


                card.cardsItems.Password.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Password.title_text = txt_AddresItem.Text;

                Change_Font_Size_Bold_Color_Control_In_Imag(Password_Lbl, card.cardsItems.Password);

                card.cardsItems.Password.y = txt_Element_Y.Value;
                card.cardsItems.Password.x = txt_Element_W.Value;
                card.cardsItems.Password.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);


                //ChangPositonControl_In_Imag(Password_Lbl, card.cardsItems.Password);
            }

            else if (Lbl_Price.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Price.Font = txt_font.Text;
                card.cardsItems.Price.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Price.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Price.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Price.Color = System.Drawing.ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                Lbl_Price.ForeColor = btn_ElementColor.BackColor;
                card.cardsItems.Price.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);

                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Price, card.cardsItems.Price);

                card.cardsItems.Price.y = txt_Element_Y.Value;
                card.cardsItems.Price.x = txt_Element_W.Value;

                card.cardsItems.Price.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Price.title_text = txt_AddresItem.Text;

                card.cardsItems.Price.unit_show = checkShowUnit_Item.Checked;
            }

            else if (Lbl_Time.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Time.Font = txt_font.Text;
                card.cardsItems.Time.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Time.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Time.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Time.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Time.y = txt_Element_Y.Value;
                card.cardsItems.Time.x = txt_Element_W.Value;
                card.cardsItems.Time.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Time.title_text = txt_AddresItem.Text;
                Lbl_Time.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Time, card.cardsItems.Time);
                //ChangPositonControl_In_Imag(Lbl_Time, card.cardsItems.Time);
                card.cardsItems.Time.unit_show = checkShowUnit_Item.Checked;
                card.cardsItems.Time.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);


            }

            else if (Lbl_validity.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Validity.Font = txt_font.Text;
                card.cardsItems.Validity.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Validity.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Validity.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Validity.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Validity.y = txt_Element_Y.Value;
                card.cardsItems.Validity.x = txt_Element_W.Value;
                card.cardsItems.Validity.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Validity.title_text = txt_AddresItem.Text;
                Lbl_validity.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_validity, card.cardsItems.Validity);
                //ChangPositonControl_In_Imag(Lbl_validity, card.cardsItems.Validity);
                card.cardsItems.Validity.unit_show = checkShowUnit_Item.Checked;
                card.cardsItems.login.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);

            }

            else if (lbl_Squ_Nuber.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.SN.Font = txt_font.Text;
                card.cardsItems.SN.Blod = checkBoxISBlod.Checked;
                card.cardsItems.SN.italic = checkBoxIsItalic.Checked;
                card.cardsItems.SN.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.SN.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.SN.y = txt_Element_Y.Value;
                card.cardsItems.SN.x = txt_Element_W.Value;
                card.cardsItems.SN.title_show = checkShowAddresItem.Checked;
                card.cardsItems.SN.title_text = txt_AddresItem.Text;
                lbl_Squ_Nuber.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
                //ChangPositonControl_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
                //card.cardsItems.Price.unit_show = checkShowUnit_Item.Checked;
            }

            else if (Lbl_OtherText1.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Other_Text1.Font = txt_font.Text;
                card.cardsItems.Other_Text1.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Other_Text1.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Other_Text1.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Other_Text1.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Other_Text1.y = txt_Element_Y.Value;
                card.cardsItems.Other_Text1.x = txt_Element_W.Value;
                card.cardsItems.Other_Text1.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Other_Text1.title_text = txt_AddresItem.Text;

                Lbl_OtherText1.Text = txt_AddresItem.Text;
                

                Lbl_OtherText1.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
                //ChangPositonControl_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            }

            else if (Lbl_SizeTransfer.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Size.Font = txt_font.Text;
                card.cardsItems.Size.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Size.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Size.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Size.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Size.y = txt_Element_Y.Value;
                card.cardsItems.Size.x = txt_Element_W.Value;
                card.cardsItems.Size.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Size.title_text = txt_AddresItem.Text;
                Lbl_SizeTransfer.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
                //ChangPositonControl_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);

                card.cardsItems.Size.unit_format = CBox_UniteTransfer_format.SelectedIndex;
                card.cardsItems.Size.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);

                card.cardsItems.Size.unit_show = checkShowUnit_Item.Checked;

            }

            else if (Lbl_Date_Print.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Date_Print.Font = txt_font.Text;
                card.cardsItems.Date_Print.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Date_Print.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Date_Print.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Date_Print.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Date_Print.y = txt_Element_Y.Value;
                card.cardsItems.Date_Print.x = txt_Element_W.Value;
                card.cardsItems.Date_Print.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Date_Print.title_text = txt_AddresItem.Text;
                Lbl_Date_Print.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
                //ChangPositonControl_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
                card.cardsItems.Date_Print.format = CBox_Date_print_format.SelectedItem.ToString();
                card.cardsItems.Date_Print.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
            }

            else if (Lbl_Number_Print.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.Number_Print.Font = txt_font.Text;
                card.cardsItems.Number_Print.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Number_Print.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Number_Print.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Number_Print.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.Number_Print.y = txt_Element_Y.Value;
                card.cardsItems.Number_Print.x = txt_Element_W.Value;
                card.cardsItems.Number_Print.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Number_Print.title_text = txt_AddresItem.Text;
                Lbl_Number_Print.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
                //ChangPositonControl_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
                card.cardsItems.Number_Print.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                card.cardsItems.Number_Print.unit_format = CBox_Date_print_format.SelectedIndex;

            }

             else if (Lbl_Batch_Print.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.BatchNumber.Font = txt_font.Text;
                card.cardsItems.BatchNumber.Blod = checkBoxISBlod.Checked;
                card.cardsItems.BatchNumber.italic = checkBoxIsItalic.Checked;
                card.cardsItems.BatchNumber.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.BatchNumber.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.BatchNumber.y = txt_Element_Y.Value;
                card.cardsItems.BatchNumber.x = txt_Element_W.Value;
                card.cardsItems.BatchNumber.title_show = checkShowAddresItem.Checked;
                card.cardsItems.BatchNumber.title_text = txt_AddresItem.Text;
                Lbl_Batch_Print.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Batch_Print, card.cardsItems.BatchNumber);
                //ChangPositonControl_In_Imag(Lbl_batc, card.cardsItems.Number_batc);
                card.cardsItems.BatchNumber.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                card.cardsItems.BatchNumber.unit_format = CBox_Date_print_format.SelectedIndex;

            }


            else if (Lbl_SP.BorderStyle == BorderStyle.FixedSingle)
            {
                card.cardsItems.SP.Font = txt_font.Text;
                card.cardsItems.SP.Blod = checkBoxISBlod.Checked;
                card.cardsItems.SP.italic = checkBoxIsItalic.Checked;
                card.cardsItems.SP.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.SP.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                card.cardsItems.SP.y = txt_Element_Y.Value;
                card.cardsItems.SP.x = txt_Element_W.Value;
                card.cardsItems.SP.title_show = checkShowAddresItem.Checked;
                card.cardsItems.SP.title_text = txt_AddresItem.Text;
                Lbl_SP.ForeColor = btn_ElementColor.BackColor;
                Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SP, card.cardsItems.SP);
                //ChangPositonControl_In_Imag(Lbl_SP, card.cardsItems.SP);
                card.cardsItems.SP.Show_ByNumber_OR_Name = Convert.ToBoolean(CBox_SellingPoint.SelectedIndex);
                card.cardsItems.SP.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                //card.cardsItems.Number_Print.unit_format = CBox_SellingPoint.SelectedIndex;



            }
            else if (pictureBox_logo.BorderStyle == BorderStyle.FixedSingle)
            {

            }

            else if (pictureBox_QR.BorderStyle == BorderStyle.FixedSingle)
            {
                //MessageBox.Show("pictureBox_QR active");
                //loc_itemtemp = loc_QR;
                //card.cardsItems.QR.Enable = true;
            }
        }

        private Control getSelectControl_pic_lbl()
        {
            Control contrl = new Control();
            //Label lbl = new Label();
            //PictureBox pic = new PictureBox();
            Control ctr_selected = new Control();

            foreach (Control control in pictureBox1.Controls)
            {
                if (control.GetType() == typeof(PictureBox))
                {
                    PictureBox pic = (PictureBox)control;
                    if (pic.BorderStyle == BorderStyle.FixedSingle)
                    {
                        ctr_selected = pic;
                    }
                }
                else
                {
                    if (control.GetType() == typeof(Label))
                    {
                        Label lbl = (Label)control;
                        if (lbl.BorderStyle == BorderStyle.FixedSingle)
                        {
                            ctr_selected = lbl;
                        }
                    }
                }
            }
            return ctr_selected;
        }

        private void set_BackroundFromPath(bool fromDB)
        {
            if (!fromDB)
            {
                try
                {
                    DialogResult res = openFileDialog1.ShowDialog();
                    openFileDialog1.Filter = "jpeg|*.jpg|bmp|*.bmp|all files|*.*";
                    openFileDialog1.RestoreDirectory = true;
                    if (Properties.Settings.Default.PathFolderPrint.ToString() != "")
                        openFileDialog1.InitialDirectory = Properties.Settings.Default.PathFolderBckgroundCards;
                    else
                        openFileDialog1.InitialDirectory = utils.Get_CardsBack_Directory();
                        //openFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";

                    //openFileDialog1.InitialDirectory = Properties.Settings.Default.PathFolderBckgroundCards;

                    if (res == DialogResult.OK)
                    {
                        FileInfo file = new FileInfo(openFileDialog1.FileName);
                        double sizeInBytes = file.Length;
                        try
                        {
                            //Bitmap img = new Bitmap(openFileDialog1.FileName);
                            //txt_PathImage.Text = openFileDialog1.FileName;
                            //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            //pictureBox1.Image = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            //BackgroundImgCard_Chbox.Checked = true;
                            //Cards_setting.path_background = openFileDialog1.FileName;

                            string sourceFileName = Path.GetFileName(openFileDialog1.FileName);
                            SaveFileImageToAppFolder(sourceFileName, openFileDialog1.FileName);

                            //Properties.Settings.Default.PathFolderBckgroundCards = Path.GetDirectoryName(openFileDialog1.FileName);
                            //Properties.Settings.Default.Save();
                            //card.setingCard.path_background = openFileDialog1.FileName; 
                        }
                        catch (Exception ex)
                        {
                            pictureBox1.Image = null;
                            BackgroundImgCard_Chbox.Checked = false;
                            MessageBox.Show("خطأ في  الصوره" + ex.Message.ToString());
                        }
                    }
                    //BackgroundImgCard_Chbox.Checked = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
            }
            else
            {
                try
                {
                    if (card.setingCard.path_background.ToString() == "")
                        return;
                    //txt_PathImage.Text = card.setingCard.path_background;
                    //MessageBox.Show(card.setingCard.path_background.ToString());
                    string sourcePath = utils.Get_CardsBack_Directory();
                    //string sourcePath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";
                    string SourcePath_File = System.IO.Path.Combine(sourcePath, card.setingCard.path_background.ToString());
                    txt_PathImage.Text = utils.Get_CardsBack_Directory()+"\\" + card.setingCard.path_background.ToString();
                    //txt_PathImage.Text = "tempCards\\cardsBack" + card.setingCard.path_background.ToString();
                    FileInfo file = new FileInfo(SourcePath_File);
                    double sizeInBytes = file.Length;
                    try
                    {
                        Bitmap img = new Bitmap(SourcePath_File);
                        //txt_PathImage.Text = openFileDialog1.FileName;
                        //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(txt_PathImage.Text);
                        pictureBox1.Image = System.Drawing.Image.FromFile(SourcePath_File);
                        BackgroundImgCard_Chbox.Checked = true;
                    }
                    catch
                    {
                        pictureBox1.Image = null;
                        BackgroundImgCard_Chbox.Checked = false;
                        MessageBox.Show("خطأ في ملف الخلفية");
                    }
                    //BackgroundImgCard_Chbox.Checked = true;
                }
                catch (Exception ex)
                {
                    pictureBox1.Image = null;
                    BackgroundImgCard_Chbox.Checked = false;
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "خطأ في مسار الصوره");
                    MessageBox.Show("خطأ في مسار الصوره \n" + ex.Message.ToString());
                }
            }
        }
       
        void DisableBorder_All(Control elment)
        {
            Label ll = new Label();
            PictureBox pp = new PictureBox();
            Panel pn = new Panel();
            Label elm = new Label();

            //CBox_Date_print_format.Visible = false;

            foreach (Control lbl in pictureBox1.Controls)
            {
                if (lbl.GetType() == typeof(Label))
                {
                    ll = (Label)lbl;
                    ll.BorderStyle = BorderStyle.None;
                }
                else if (lbl.GetType() == typeof(PictureBox))
                {

                    pp = (PictureBox)lbl;
                    pp.BorderStyle = BorderStyle.None;
                }
                else if (lbl.GetType() == typeof(Panel))
                {

                    pn = (Panel)lbl;
                    pn.BorderStyle = BorderStyle.None;
                }
            }


            if (elment.GetType() == typeof(Label))
            {
                ll = (Label)elment;
                ll.BorderStyle = BorderStyle.FixedSingle;
                //groupBox_Abaad.Visible = false;
                groupBox_Abaad.Enabled = false;
                groupBox_FontAndSize.Visible = true;
                groupBox_FontAndSize.Enabled = true;
                //panel_ColorBack.Visible = false;


            }
            else if (elment.GetType() == typeof(PictureBox))
            {
                pp = (PictureBox)elment;
                pp.BorderStyle = BorderStyle.FixedSingle;
                groupBox_Abaad.Visible = true;
                groupBox_Abaad.Enabled = true;

                //groupBox_FontAndSize.Visible = false;
                groupBox_FontAndSize.Enabled = false;
                //panel_ColorBack.Visible = false;

                // MessageBox.Show("img");


            }
            else if (elment.GetType() == typeof(Panel))
            {
                pn = (Panel)elment;
                pn.BorderStyle = BorderStyle.FixedSingle;
                groupBox_Abaad.Visible = true;
                //groupBox_FontAndSize.Visible = false;
                groupBox_FontAndSize.Enabled = false;
                //panel_ColorBack.Visible = true;
            }
            elment.BringToFront();
            //checkShowUnit_Item.Enabled = false;
            //elment.BringToFront();
        }

        private void control_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        void move_qr()
        {
            try
            {
                if (selectTemplateFromDrowpDown)
                    return;

                Point point = new Point();
                point = pictureBox_QR.Location;

                decimal X = (point.X / (decimal)7.0);
                decimal Y = (point.Y / (decimal)7.0);

                txt_Element_W.Text = X.ToString("0.0");
                txt_Element_Y.Text = Y.ToString("0.0");


                card.cardsItems.QR.x = X;
                card.cardsItems.QR.y = Y;

            }
            catch { }
        }

        private void set_logoFromPath(bool fromDB)
        {
            if (!fromDB)
            {
                try
                {
                    DialogResult res = openFileDialog1.ShowDialog();
                    openFileDialog1.Filter = "jpeg|*.jpg|bmp|*.bmp|all files|*.*";

                    if (res == DialogResult.OK)
                    {
                        FileInfo file = new FileInfo(openFileDialog1.FileName);
                        double sizeInBytes = file.Length;
                        try
                        {
                            Bitmap img = new Bitmap(openFileDialog1.FileName);
                            txt_LogoImage.Text = openFileDialog1.FileName;

                            pictureBox_logo.Image = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            Logo_Chbox.Checked = true;
                            card.cardsItems.logo.Path = openFileDialog1.FileName;
                        }
                        catch
                        {
                            //BackgroundImgCard_Chbox.Checked = false;
                            Logo_Chbox.Checked = false;
                            //MessageBox.Show("خطأ في الصوره");
                        }


                    }
                    Logo_Chbox.Checked = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
            }
        }

        #endregion

        #region  all Event
        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            rjPanel1.Refresh();
            CBox_TemplateCards.Refresh();
            txt_OtherText1.Refresh();
            rjPanel16.Refresh();
            CB_Fonts.Refresh();
            groupBox_FontAndSize.Refresh();
            rjPanel13.Refresh();
            txt_Note_onPage.Refresh();
            groub_Design.Refresh();
            groupBox_Abaad.Refresh();

            //rjCheckBox1
            //rjTextBox1 
        }

        private void rjPictureBox1_Resize(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                pictureBox1.Width = Convert.ToInt16((Convert.ToDecimal(TextCard_W.Text)) * (decimal)7.0);
                pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(TextCard_Y.Text)) * (decimal)7.0);

                Calclate_Number_Card_In_Page();
                //CalcluteColumAndCard();
            }

            //pictureBox1.Location = new Point((groub_Design.Width / 2) - pictureBox1.Width / 2, (groub_Design.Height / 2) - pictureBox1.Height / 2);



        }

        private void Form_CardsDesigen_Graghics_Load(object sender, EventArgs e)
        {
            firstLoad = true;
            saveToClass = true;
            selectTemplateFromDrowpDown = true;
            selectTemplateFromCheckBox = true;
            CBox_UniteTime_format.Visible = true;

            timer1.Start();
        }
        void setFont()
        {
            //try
            //{
            //    this.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);


            //    groupBox3.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Bold);
            //    radioTempletCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
            //    radioTableCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
            //    BackgroundImgCard_Chbox.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 7, FontStyle.Regular);
            //    label1.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label2.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 7, FontStyle.Regular);
            //    label4.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label16.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label3.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label5.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 7, FontStyle.Regular);


            //}
            //catch (Exception ex) { }
            //try
            //{
            //    using (InstalledFontCollection col = new InstalledFontCollection())
            //    {
            //        foreach (FontFamily fa in col.Families)
            //        {
            //            CB_Fonts.Items.Add(fa.Name);
            //        }
            //    }
            //}
            //catch { }
            //return;
            try
            {
                int totalfonts = FontFactory.RegisterDirectory("C:\\WINDOWS\\Fonts");
                StringBuilder sb = new StringBuilder();
                foreach (string fontname in FontFactory.RegisteredFonts)
                {
                    //sb.Append(fontname + "\n");
                    CB_Fonts.Items.Add(fontname);
                }
                //CB_Fonts.SelectedItem = loc_login.Font;
            }
            catch { }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            firstLoad = true;
            selectTemplateFromDrowpDown = true;

            setFont();
            setControlToPict();
            Get_Cbox_Profile();
            get_Profile_Hotspot();
            Get_TemplateCardsFromDB();
            Get_Cbox_Profile_Hotspot_local();
            try
            {
                //comboBox_quilty_image.SelectedIndex = 0;
                CBox_NoteType_onPage.SelectedIndex = 0;
                CBox_UniteTime_format.SelectedIndex = 0;
                CBox_UniteTransfer_format.SelectedIndex = 0;
                CBox_UniteValidaty_format.SelectedIndex = 0;
                CBox_Date_print_format.SelectedIndex = 0;
                CBox_SellingPoint.SelectedIndex = 0;
            }
            catch { /*MessageBox.Show(ex.Message);*/ }
            firstLoad = false;
            selectTemplateFromDrowpDown = false;
            //CBox_TemplateCards.SelectedIndex = 0;
            CBox_TemplateCards_OnSelectedIndexChanged(sender, EventArgs.Empty);
        }

        private void Get_Cbox_Profile_Hotspot_local()
        {
            var umProfil = new List<HSLocalProfile>();
            HSLocalProfile hotspot = new HSLocalProfile();
            umProfil.Add(new HSLocalProfile { Id = 0, Name = "" });

            umProfil.AddRange(hotspot.Ge_Local_Hotspot());

            try
            {
                CBox_Profile_HotspotLocal.DataSource = umProfil;
                CBox_Profile_HotspotLocal.DisplayMember = "Name";
                CBox_Profile_HotspotLocal.ValueMember = "Name";
                //CBox_Profile.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }
        }
        private void groub_Design_Resize(object sender, EventArgs e)
        {
            //if (!firstLoad)
            //{
            pictureBox1.Location = new Point((groub_Design.Width / 2) - pictureBox1.Width / 2, (groub_Design.Height / 2) - pictureBox1.Height / 2);

            //}
        }

        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            
            if (!firstLoad)
            {
                CBox_Profile.SelectedIndex = -1;
                selectTemplateFromDrowpDown = true;

                disableAll_Lable();
                Get_TemplateCardsItemsGraphics();
                SetValuToCardToGraphics();
                //firstLoad = false;
                selectTemplateFromDrowpDown = false;

            }
            
        }

       
        public void SaveFileImageToAppFolder(string sourceFileName, string sourcePathFile)
        {
            //string AppPath = @"\\tempCards\\cardsBack";
            string destinationPath =utils.Get_CardsBack_Directory();
            //string destinationPath = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";
            string destinationFileName = DateTime.Now.ToString("yyyyMMddhhmmss-") + sourceFileName; // Don't mind this. I did this because I needed to name the copied files with respect to time.
            string destinationFile = System.IO.Path.Combine(destinationPath, destinationFileName);

            if (!System.IO.Directory.Exists(destinationPath))
            {
                System.IO.Directory.CreateDirectory(destinationPath);
            }
            System.IO.File.Copy(sourcePathFile, destinationFile, true);
            //string destAppPath_File = System.IO.Path.Combine(AppPath, destinationFileName);

            try
            {
                Bitmap img = new Bitmap(destinationFile);
                pictureBox1.Image = System.Drawing.Image.FromFile(destinationFile);
                BackgroundImgCard_Chbox.Checked = true;

                Properties.Settings.Default.PathFolderBckgroundCards = Path.GetDirectoryName(sourceFileName);
                Properties.Settings.Default.Save();

                card.setingCard.path_background = destinationFileName;
                txt_PathImage.Text = destinationFileName;
            }
            catch (Exception ex)
            {
                pictureBox1.Image = null;
                BackgroundImgCard_Chbox.Checked = false;
                MessageBox.Show("خطأ في ملف الصوره" + ex.Message.ToString());
            }
        }
        private void btnAddTemplate_Click(object sender, EventArgs e)
        {
            addDeletTemplate = true;
            try
            {
                frm_Input_Dailog_New_Template frm = new frm_Input_Dailog_New_Template();
                frm.ShowDialog();
                if (frm.add)
                {
                    SourceCardsTemplate tmplate = new SourceCardsTemplate();
                    tmplate.name = frm.txt_Name.Text;
                    tmplate.type = "design";
                    card.setingCard.name = frm.txt_Name.Text;
                    card.setingCard.type = "design";

                    card.setingCard.proile_HS_link = "";
                    card.setingCard.proile_link = "";
                    tmplate.values = JsonConvert.SerializeObject(card);
                    if (SqlDataAccess.Add_New_Template(tmplate))
                    {
                        RJMessageBox.Show("تم انشاء القالب بنجاح", "ok");
                        Get_TemplateCardsFromDB();
                        addDeletTemplate = false;
                        CBox_TemplateCards.SelectedIndex = CBox_TemplateCards.Items.Count - 1;
                        CBox_Profile.SelectedIndex = -1;
                    }
                    else
                        RJMessageBox.Show("خطاء بالاضافة", "ok");
                }
            }
            catch (Exception ex) { addDeletTemplate = false; MessageBox.Show(ex.Message); }
             
        }
        private void btnSaveTemplate_Click(object sender, EventArgs e)
        {
            if (CBox_TemplateCards.Text == "")
            {
                RJMessageBox.Show("لا يوجد قالب لحفظه اختار القالب");
                return;
            }

            save_state_Profile_UM();
            save_state_Profile_HS();
            save_state_Profile_Local_HS();
            try
            {
                SourceCardsTemplate sourceCardTemplate = new SourceCardsTemplate();
                sourceCardTemplate.values = JsonConvert.SerializeObject(card);
                sourceCardTemplate.id = (int)CBox_TemplateCards.SelectedValue;

                if (SqlDataAccess.Add_New_Template(sourceCardTemplate, false))
                {
                    RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                }
                else
                    RJMessageBox.Show("خطاء في الحفظ", "ok");
            }
            catch (Exception ex) { MessageBox.Show("save_template_To_DB" + "\n" + ex.Message.ToString()); }
            Get_TemplateCardsItemsGraphics();
        }

        private void save_state_Profile_UM()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile.Text == "")
                return;

            string profileName = CBox_Profile.Text; 
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_link == profileName)
                        {
                            card_test.setingCard.proile_link = "";
                            s.values= JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_link == profileName)
                        {
                            card_test.setingCard.proile_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx ++;
            }

        }
        private void save_state_Profile_HS()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile_HS.Text == "")
                return;

            string profileName = CBox_Profile_HS.Text;
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_link == profileName)
                        {
                            card_test.setingCard.proile_HS_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_link == profileName)
                        {
                            card_test.setingCard.proile_HS_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx++;
            }

        }
       
         private void save_state_Profile_Local_HS()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile_HotspotLocal.Text == "")
                return;

            string profileName = CBox_Profile_HotspotLocal.Text;
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_Local_link == profileName)
                        {
                            card_test.setingCard.proile_HS_Local_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_Local_link == profileName)
                        {
                            card_test.setingCard.proile_HS_Local_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx++;
            }

        }
       
        
        
        private void btnPreviewTemplate_Click(object sender, EventArgs e)
        {
            if(card==null)
            {
                RJMessageBox.Show("اختار قالب");
                return;
            }
            //Form_PrviewPdf prviewPdf = new Form_PrviewPdf();
            Form_PDF_Prview prviewPdf = new Form_PDF_Prview(card.name);

            prviewPdf.card = card;
            prviewPdf.type_template = card.type;
            prviewPdf.pathfile = pathfile;
            prviewPdf.CBox_TemplateCards.Text = CBox_TemplateCards.Text;
            prviewPdf.txtNumberCard.Text = txt_NumberCard.Text;

            //prviewPdf.CBox_Profile.Text = "test";
            prviewPdf.ShowDialog();
            return;

            //preview_print();
        }
        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من  حذف الباقة ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;
            if (SqlDataAccess.delete_template((int)CBox_TemplateCards.SelectedValue))
            {
                try
                {
                    Get_TemplateCardsFromDB();
                    addDeletTemplate = false;
                    CBox_TemplateCards.SelectedIndex = 0;
                    RJMessageBox.Show("تم الحذف بنجاح ");
                }
                catch {  }
                }
            else
                RJMessageBox.Show("خطاء اثناء الحذف");

            //try
            //{
            //    addDeletTemplate = true;
            //    if (CBox_TemplateCards.Text == "default")
            //    {
            //        RJMessageBox.Show("لايمكن حذف القالب الافتراضي");
            //        return;
            //    }
            //    string Qury = "DELETE FROM CardsTemplate WHERE [id] = @id";
            //    var conn = new SQLiteConnection(CLS_DBAcess_V2.source);
            //    SQLiteCommand cmd2 = new SQLiteCommand(Qury, conn);
            //    cmd2.Parameters.AddWithValue("@id", CBox_TemplateCards.SelectedValue);
            //    conn.Open();
            //    int RowsAffected = cmd2.ExecuteNonQuery();
            //    if (RowsAffected > 0)
            //    {
            //        Get_TemplateCardsFromDB();
            //        addDeletTemplate = false;
            //        CBox_TemplateCards.SelectedIndex = 0;
            //        MessageBox.Show("تم الحذف بنجاح ");
            //    }
            //    conn.Close();
            //}
            //catch { addDeletTemplate = false; }
            addDeletTemplate = false;
        }

        private void CBox_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_link = CBox_Profile.SelectedValue.ToString();
            }
            catch { }
        }

        private void CBox_Profile_HS_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_HS_link = CBox_Profile_HS.SelectedValue.ToString();
                //card.setingCard.proile_HS_link = CBox_Profile_HS.Text.ToString();
            }
            catch { }
        }

        private void CBox_Curncey_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.currency = CBox_Curncey.SelectedItem.ToString();
        }

        private void BackgroundImgCard_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {
                    if (BackgroundImgCard_Chbox.Checked == true)
                    {

                        //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(txt_PathImage.Text);
                        string destinationPath = utils.Get_CardsBack_Directory();
                        //string destinationPath = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";

                        string destinationFile = System.IO.Path.Combine(destinationPath, card.setingCard.path_background.ToString());

                        pictureBox1.Image = System.Drawing.Image.FromFile(destinationFile);
                        //// txt_PathImage.Text = openFileDialog1.FileName;
                        //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;

                    }
                    else
                    {
                        //ImgTemplet_Panel.BackgroundImage = null;
                        pictureBox1.Image = null;
                        //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;

                        ////ImgTemplet_Panel.BackgroundImage = Panel_hight;
                        //// txt_PathImage.Text = openFileDialog1.FileName;
                    }
                }
                catch { }
                card.setingCard.enable_background = BackgroundImgCard_Chbox.Checked;
                //card.setingCard.path_background = txt_PathImage.Text;
                //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;
                //saveProperties();
            }

        }

        private void btnAddImag_Click(object sender, EventArgs e)
        {
            set_BackroundFromPath(false);
        }
        private void pictureBox1_Paint(object sender, PaintEventArgs e)
        {
            if (!firstLoad)
            {
                Pen pen55 = new Pen(btn_BorderColor.BackColor, (float)txt_SizeBorder.Value);
                //pen55.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
                e.Graphics.DrawRectangle(pen55, 0, 0, pictureBox1.Width - 1, pictureBox1.Height - 1);
            }
        }
        private void pictureBox1_SizeChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                pictureBox1.Left = (groub_Design.ClientSize.Width - pictureBox1.Width) / 2;
                pictureBox1.Top = (groub_Design.ClientSize.Height - pictureBox1.Height) / 2;

                card.setingCard.card_width = TextCard_W.Value;
                card.setingCard.card_height = TextCard_Y.Value;
            }
            //Calclate_Number_Card_In_Page();

        }
        void Calclate_Number_Card_In_Page()
        {


            //==========================================================================================  

            float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(txt_Space_X.Text));
            float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(txt_Space_Y.Text));

            float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(TextCard_W.Text));
            float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(TextCard_Y.Text));


            float Pictur_width_orginal = Pictur_width;
            float Pictur_height__orginal = Pictur_height;




            float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
            float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);

            //int NuberCards = 51;
            int NumberCard_for_OneColum = 0;
            int NumberCard_in_Page = 0;
            double NumberPages = 0;

            int ColumNumber = 0; float CardNumber = 0;

            ColumNumber = (int)(595 / (Pictur_width + Space_Y));
            if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                ColumNumber = ColumNumber - 1;

            NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
            if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                NumberCard_for_OneColum = NumberCard_for_OneColum - 1;


            NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

            txt_NumberCard.Text = NumberCard_in_Page.ToString();
            txt_NumberCulum.Text = ColumNumber.ToString();

            iTextSharp.text.Image jpg = null;


        }


        
        #endregion

        #region Login Fun
        private void Login_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Login_Chbox.Checked == true)
            {
                card.cardsItems.login.Enable = true;
                Login_Lbl.Visible = card.cardsItems.login.title_show;
                txt_AddresItem.Text = card.cardsItems.login.title_text;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Login_Lbl);
                Set_Proprties_For_Item(Login_Lbl, card.cardsItems.login);
                check_Location_control_isOut(Login_Lbl);

            }
            else
            {
                Login_Lbl.Visible = false;
                //Login_Lbl.Visible = false;
                card.cardsItems.login.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Login_Lbl_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown)
                return;
            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";

            DisableBorder_All(Login_Lbl);
            Set_Proprties_For_Item(Login_Lbl, card.cardsItems.login);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;
        }

        private void Login_Lbl_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }
        }

        private void Login_Lbl_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Login_Lbl);
        }

        private void Login_Lbl_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Login_Lbl.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.login.x = X;
            card.cardsItems.login.y = Y;
        }

        #endregion

        #region Password Fun
        private void Password_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Password_Chbox.Checked == true)
            {
                card.cardsItems.Password.Enable = true;
                Password_Lbl.Visible = card.cardsItems.Password.title_show;
                txt_AddresItem.Text = card.cardsItems.Password.title_text;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Password_Lbl);
                Set_Proprties_For_Item(Password_Lbl, card.cardsItems.Password);
                check_Location_control_isOut(Password_Lbl);

            }
            else
            {
                Password_Lbl.Visible = false;
                //Password_Lbl_title.Visible = false;
                card.cardsItems.Password.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;
        }

        private void Password_Lbl_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown)
                return;
            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";

            DisableBorder_All(Password_Lbl);
            Set_Proprties_For_Item(Password_Lbl, card.cardsItems.Password);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void Password_Lbl_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }
        }

        private void Password_Lbl_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Password_Lbl);
        }

        private void Password_Lbl_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Password_Lbl.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Password.x = X;
            card.cardsItems.Password.y = Y;
        }
        #endregion

        #region Time
        private void Time_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Time_Chbox.Checked == true)
            {
                card.cardsItems.Time.Enable = true;
                Lbl_Time.Visible = card.cardsItems.Time.title_show;
                txt_AddresItem.Text = card.cardsItems.Time.title_text;
                checkShowUnit_Item.Enabled = true;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_Time);
                Set_Proprties_For_Item(Lbl_Time, card.cardsItems.Time);
                check_Location_control_isOut(Lbl_Time);

                CBox_UniteTime_format.Visible = true;
                CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;
                //CBox_UniteTime_format.Location = new Point(19, 7);

                CBox_Curncey.Visible = false;
                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteValidaty_format.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_SellingPoint.Visible = false;


            }
            else
            {
                Lbl_Time.Visible = false;
                Lbl_Time.Visible = false;
                card.cardsItems.Time.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;
        }

        private void Lbl_Time_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = true;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";

            DisableBorder_All(Lbl_Time);
            Set_Proprties_For_Item(Lbl_Time, card.cardsItems.Time);
            checkShowUnit_Item.Checked = card.cardsItems.Time.unit_show;


            groupBox_UintShow.Enabled = true;
            CBox_UniteTime_format.Visible = true;
            CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;
            //CBox_UniteTime_format.Location = new Point(19, 7);

            CBox_Curncey.Visible = false;

            CBox_UniteTransfer_format.Visible = false;
            CBox_UniteValidaty_format.Visible = false;
            CBox_Date_print_format.Visible = false;
            CBox_SellingPoint.Visible = false;


            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;
        }

        private void Lbl_Time_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }
        }

        private void Lbl_Time_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_Time);

        }

        private void Lbl_Time_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_Time.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Time.x = X;
            card.cardsItems.Time.y = Y;

        }
        #endregion

        #region SizeTransfer
        private void Size_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Size_Chbox.Checked == true)
            {
                card.cardsItems.Size.Enable = true;
                Lbl_SizeTransfer.Visible = card.cardsItems.Size.title_show;
                txt_AddresItem.Text = card.cardsItems.Size.title_text;
                checkShowUnit_Item.Enabled = true;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_SizeTransfer);
                Set_Proprties_For_Item(Lbl_SizeTransfer, card.cardsItems.Size);
                check_Location_control_isOut(Lbl_SizeTransfer);
                CBox_UniteTransfer_format.Visible = true;
                CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;

                CBox_Curncey.Visible = false;
                CBox_UniteValidaty_format.Visible = false;
                CBox_UniteTime_format.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_SellingPoint.Visible = false;

                //CBox_UniteTransfer_format.Location = new Point(19, 7);

            }
            else
            {
                Lbl_SizeTransfer.Visible = false;
                //Lbl_SizeTransfer_title.Visible = false;
                card.cardsItems.Size.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_SizeTransfer_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = true;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";

            DisableBorder_All(Lbl_SizeTransfer);
            Set_Proprties_For_Item(Lbl_SizeTransfer, card.cardsItems.Size);
            checkShowUnit_Item.Checked = card.cardsItems.Size.unit_show;
            CBox_UniteTransfer_format.Visible = true;

            groupBox_UintShow.Enabled = true;
            CBox_UniteTransfer_format.Visible = true;
            CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;
            //CBox_UniteTransfer_format.Location = new Point(19, 7);

            CBox_Curncey.Visible = false;
            CBox_UniteValidaty_format.Visible = false;
            CBox_UniteTime_format.Visible = false;
            CBox_Date_print_format.Visible = false;
            CBox_SellingPoint.Visible = false;


            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void Lbl_SizeTransfer_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }
        }
        private void Lbl_SizeTransfer_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_SizeTransfer);

        }

        private void Lbl_SizeTransfer_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_SizeTransfer.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Size.x = X;
            card.cardsItems.Size.y = Y;

        }
        #endregion

        #region validity
        private void validity_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (validity_Chbox.Checked == true)
            {
                card.cardsItems.Validity.Enable = true;
                Lbl_validity.Visible = card.cardsItems.Validity.title_show;
                txt_AddresItem.Text = card.cardsItems.Validity.title_text;
                checkShowUnit_Item.Enabled = true;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_validity);
                Set_Proprties_For_Item(Lbl_validity, card.cardsItems.Validity);
                check_Location_control_isOut(Lbl_validity);

                CBox_UniteValidaty_format.Visible = true;
                CBox_UniteValidaty_format.BringToFront();
                CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;
                CBox_Curncey.Visible = false;

                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteTime_format.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_SellingPoint.Visible = false;
                //CBox_UniteValidaty_format.Location = new Point(19, 7);


            }
            else
            {
                Lbl_validity.Visible = false;
                //Lbl_validity_title.Visible = false;
                card.cardsItems.Validity.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_validity_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = true;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";

            DisableBorder_All(Lbl_validity);
            Set_Proprties_For_Item(Lbl_validity, card.cardsItems.Validity);
            checkShowUnit_Item.Checked = card.cardsItems.Validity.unit_show;

            groupBox_UintShow.Enabled = true;
            CBox_UniteValidaty_format.Visible = true;
            CBox_UniteValidaty_format.BringToFront();
            CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;
            //CBox_UniteValidaty_format.Location = new Point(19, 7);

            CBox_Curncey.Visible = false;

            CBox_UniteTransfer_format.Visible = false;
            CBox_UniteTime_format.Visible = false;
            CBox_Date_print_format.Visible = false;
            CBox_SellingPoint.Visible = false;

            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }
        private void Lbl_validity_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }


        }
        private void Lbl_validity_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_validity);

        }

        private void Lbl_validity_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_validity.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Validity.x = X;
            card.cardsItems.Validity.y = Y;

        }
        #endregion

        #region Price
        private void Price_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Price_Chbox.Checked == true)
            {
                card.cardsItems.Price.Enable = true;
                Lbl_Price.Visible = card.cardsItems.Price.title_show;
                txt_AddresItem.Text = card.cardsItems.Price.title_text;
                checkShowUnit_Item.Enabled = true;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_Price);
                Set_Proprties_For_Item(Lbl_Price, card.cardsItems.Price);
                check_Location_control_isOut(Lbl_Price);


                CBox_Curncey.Visible = true;
                CBox_Curncey.BringToFront();
                CBox_Curncey.Text = card.setingCard.currency;
                
               

                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteTime_format.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_SellingPoint.Visible = false;
                CBox_UniteValidaty_format.Visible = false;
                //CBox_Curncey.Location = new Point(19, 7);

            }
            else
            {
                Lbl_Price.Visible = false;
                //Lbl_Price_title.Visible = false;
                card.cardsItems.Price.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_Price_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            checkShowUnit_Item.Enabled = true;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";


            DisableBorder_All(Lbl_Price);
            Set_Proprties_For_Item(Lbl_Price, card.cardsItems.Price);
            checkShowUnit_Item.Checked = card.cardsItems.Price.unit_show;
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;


            CBox_Curncey.Visible = true;
            CBox_Curncey.BringToFront();
            CBox_Curncey.Text = card.setingCard.currency;



            CBox_UniteTransfer_format.Visible = false;
            CBox_UniteTime_format.Visible = false;
            CBox_Date_print_format.Visible = false;
            CBox_SellingPoint.Visible = false;
            CBox_UniteValidaty_format.Visible = false;
            //CBox_Curncey.Location = new Point(19, 7);

        }

        private void Lbl_Price_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void Lbl_Price_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_Price);

        }

        private void Lbl_Price_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_Price.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Price.x = X;
            card.cardsItems.Price.y = Y;

        }

        #endregion

        #region Squ_Nuber
        private void Squnce_Number_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Squnce_Number_Chbox.Checked == true)
            {
                card.cardsItems.SN.Enable = true;
                lbl_Squ_Nuber.Visible = card.cardsItems.SN.title_show;
                txt_AddresItem.Text = card.cardsItems.SN.title_text;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(lbl_Squ_Nuber);
                Set_Proprties_For_Item(lbl_Squ_Nuber, card.cardsItems.SN);
                check_Location_control_isOut(lbl_Squ_Nuber);

            }
            else
            {
                lbl_Squ_Nuber.Visible = false;
                //lbl_Squ_Nuber_title.Visible = false;
                card.cardsItems.SN.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }
        private void lbl_Squ_Nuber_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;
            CBox_Date_print_format.Text = "";
            DisableBorder_All(lbl_Squ_Nuber);
            Set_Proprties_For_Item(lbl_Squ_Nuber, card.cardsItems.SN);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void lbl_Squ_Nuber_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void lbl_Squ_Nuber_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(lbl_Squ_Nuber);

        }

        private void lbl_Squ_Nuber_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = lbl_Squ_Nuber.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.SN.x = X;
            card.cardsItems.SN.y = Y;

        }

        #endregion

        #region Number_Print
        private void Number_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Number_Print_Chbox.Checked == true)
            {
                card.cardsItems.Number_Print.Enable = true;
                Lbl_Number_Print.Visible = card.cardsItems.Number_Print.title_show;
                txt_AddresItem.Text = card.cardsItems.Number_Print.title_text;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_Number_Print);
                Set_Proprties_For_Item(Lbl_Number_Print, card.cardsItems.Number_Print);
                check_Location_control_isOut(Lbl_Number_Print);

            }
            else
            {
                Lbl_Number_Print.Visible = false;
                //Lbl_Number_Print_title.Visible = false;
                card.cardsItems.Number_Print.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }
        private void Number_Batch_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Number_Batch_Chbox.Checked == true)
            {
                card.cardsItems.BatchNumber.Enable = true;
                Lbl_Batch_Print.Visible = card.cardsItems.BatchNumber.title_show;
                txt_AddresItem.Text = card.cardsItems.BatchNumber.title_text;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(Lbl_Batch_Print   );
                Set_Proprties_For_Item(Lbl_Batch_Print, card.cardsItems.BatchNumber);
                check_Location_control_isOut(Lbl_Batch_Print);

            }
            else
            {
                Lbl_Batch_Print.Visible = false;
                //Lbl_batc_title.Visible = false;
                card.cardsItems.BatchNumber.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_Number_Print_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;


            DisableBorder_All(Lbl_Number_Print);
            Set_Proprties_For_Item(Lbl_Number_Print, card.cardsItems.Number_Print);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }
         private void Lbl_Batch_Print_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;


            DisableBorder_All(Lbl_Batch_Print);
            Set_Proprties_For_Item(Lbl_Batch_Print, card.cardsItems.BatchNumber);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void Lbl_Number_Print_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }
         private void Lbl_Batch_Print_MouseMove(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void Lbl_Number_Print_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_Number_Print);

        }        
        private void Lbl_Batch_Print_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_Batch_Print);

        }

        private void Lbl_Number_Print_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_Number_Print.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Number_Print.x = X;
            card.cardsItems.Number_Print.y = Y;

        }
       private void Lbl_Batch_Print_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_Batch_Print.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.BatchNumber.x = X;
            card.cardsItems.BatchNumber.y = Y;

        }
        #endregion

        #region QR
        private void QR_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;
            try
            {
                if (QR_Chbox.Checked == true)
                {
                    pictureBox_QR.Visible = true;
                    card.cardsItems.QR.Enable = true;

                    //txt_AddresItem.Text = loc_QR.address_text;
                    //txt_LogoImage.Text = loc_QR.address_text;
                    DisableBorder_All(pictureBox_QR);
                    Set_Proprties_For_Item_img(pictureBox_QR, card.cardsItems.QR);

                    check_Location_control_isOut(pictureBox_QR);


                }
                else
                {
                    pictureBox_QR.Visible = false;
                    card.cardsItems.QR.Enable = false;
                }
            }
            catch { }

        }

        private void pictureBox_QR_MouseDown(object sender, MouseEventArgs e)
        {
            DisableBorder_All(pictureBox_QR);
            //Set_Proprties_For_Item_img(pictureBox_QR, loc_QR);
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
        }

        private void pictureBox_QR_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left)
            {
                pictureBox_QR.Left = e.X + pictureBox_QR.Left - MouseDownLocation.X;
                pictureBox_QR.Top = e.Y + pictureBox_QR.Top - MouseDownLocation.Y;
            }
            try
            {
                if (selectTemplateFromDrowpDown)
                    return;

                Point point = new Point();
                point = pictureBox_QR.Location;

                decimal X = (point.X / (decimal)7.0);
                decimal Y = (point.Y / (decimal)7.0);

                txt_Element_W.Text = X.ToString("0.0");
                txt_Element_Y.Text = Y.ToString("0.0");


                card.cardsItems.QR.x = X;
                card.cardsItems.QR.y = Y;

            }
            catch { }

        }

        private void pictureBox_QR_Resize(object sender, EventArgs e)
        {
            try
            {
                if (!firstLoad)
                {
                    pictureBox_QR.Height = (pictureBox_QR.Width);
                    txt_dimension_w.Value = Convert.ToDecimal(pictureBox_QR.Width) / (decimal)7.0;
                    txt_dimension_H.Value = Convert.ToDecimal(pictureBox_QR.Height) / (decimal)7.0;
                    ////===========================
                    //    txt_dimension_w.Value = Convert.ToDecimal(pictureBox_QR.Width) / (decimal)7.0;
                    //    txt_dimension_H.Value = Convert.ToDecimal(pictureBox_QR.Height) / (decimal)7.0;

                    card.cardsItems.QR.item_dimension_w = txt_dimension_w.Value;
                    card.cardsItems.QR.item_dimension_y = txt_dimension_H.Value;

                    //move_qr();
                }
            }
            catch { }

        }

        private void pictureBox_QR_SizeChanged(object sender, EventArgs e)
        {
            try
            {
                if (!firstLoad)
                {
                    txt_dimension_w.Value = Convert.ToDecimal(pictureBox_QR.Width) / (decimal)7.0;
                    txt_dimension_H.Value = Convert.ToDecimal(pictureBox_QR.Height) / (decimal)7.0;

                    card.cardsItems.QR.item_dimension_w = txt_dimension_w.Value;
                    card.cardsItems.QR.item_dimension_y = txt_dimension_H.Value;

                    move_qr();

                }
            }
            catch { }

        }

        #endregion

        #region SP
        private void CBox_SellingPoint_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (card == null)
                return;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (saveToClass == false)
                return;

            if (CBox_SellingPoint.SelectedIndex == 0)
                card.cardsItems.SP.Show_ByNumber_OR_Name = false;
            else
                card.cardsItems.SP.Show_ByNumber_OR_Name = true;


        }

        private void SP_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            try
            {
                if (selectTemplateFromDrowpDown || firstLoad)
                    return;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                if (SP_Chbox.Checked == true)
                {
                    card.cardsItems.SP.Enable = true;
                    //Lbl_SP_title.Visible = card.cardsItems.SP.title_show;
                    txt_AddresItem.Text = card.cardsItems.SP.title_text;

                    checkShowUnit_Item.Enabled = true;

                    DisableBorder_All(Lbl_SP);
                    Set_Proprties_For_Item(Lbl_SP, card.cardsItems.SP);
                    check_Location_control_isOut(Lbl_SP);

                    if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                        CBox_SellingPoint.SelectedIndex = 0;
                    else
                        CBox_SellingPoint.SelectedIndex = 1;


                    CBox_SellingPoint.Visible = true;
                    //CBox_SellingPoint.Location = new Point(19, 7);
                    CBox_Curncey.Visible = false;

                    CBox_UniteValidaty_format.Visible = false;
                    CBox_UniteTime_format.Visible = false;
                    CBox_Date_print_format.Visible = false;
                    CBox_UniteTransfer_format.Visible = false;


                }
                else
                {
                    Lbl_SP.Visible = false;
                    //Lbl_SP_title.Visible = false;
                    card.cardsItems.SP.Enable = false;
                    //loc_login_title.Enable = false;
                    return;
                }
            }
            catch { }
            saveToClass = true;

        }

        private void Lbl_SP_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            try
            {
                if (selectTemplateFromDrowpDown)
                    return;

                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;
                CBox_Date_print_format.Text = "";

                //CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);

                if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                    CBox_SellingPoint.SelectedIndex = 0;
                else
                    CBox_SellingPoint.SelectedIndex = 1;

                DisableBorder_All(Lbl_SP);
                Set_Proprties_For_Item(Lbl_SP, card.cardsItems.SP);

                CBox_Curncey.Visible = false;

                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteTime_format.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_SellingPoint.Visible = true;

                if (e.Button == MouseButtons.Left)
                {
                    MouseDownLocation = e.Location;
                }
            }catch { }
            saveToClass = true;

        }

        private void Lbl_SP_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void Lbl_SP_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_SP);

        }

        private void Lbl_SP_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_SP.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.SP.x = X;
            card.cardsItems.SP.y = Y;

        }

        #endregion

        #region Date_Print
        private void Date_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (Date_Print_Chbox.Checked == true)
            {
                card.cardsItems.Date_Print.Enable = true;
                Lbl_Date_Print.Visible = card.cardsItems.Date_Print.title_show;
                txt_AddresItem.Text = card.cardsItems.Date_Print.title_text;
                
                //groupBox_Date_print_format.Enabled = true;
                checkShowUnit_Item.Enabled=true;
                DisableBorder_All(Lbl_Date_Print);
                Set_Proprties_For_Item(Lbl_Date_Print, card.cardsItems.Date_Print);
                check_Location_control_isOut(Lbl_Date_Print);

                CBox_Date_print_format.Visible = true;
                //CBox_SellingPoint.Visible = true;
                //CBox_Date_print_format.Location = new Point(19, 7);
                CBox_Curncey.Visible = false;

                CBox_UniteValidaty_format.Visible = false;
                CBox_UniteTime_format.Visible = false;
                CBox_SellingPoint.Visible = false;
                CBox_UniteTransfer_format.Visible = false;

            }
            else
            {
                Lbl_Date_Print.Visible = false;
                //Lbl_Date_Print_title.Visible = false;
                card.cardsItems.Date_Print.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_Date_Print_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = true;
            //groupBox_Date_print_format.Enabled = true;

            DisableBorder_All(Lbl_Date_Print);
            Set_Proprties_For_Item(Lbl_Date_Print, card.cardsItems.Date_Print);

            CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;

            CBox_Curncey.Visible = false;
            CBox_UniteTransfer_format.Visible = false;
            CBox_UniteTime_format.Visible = false;
            CBox_Date_print_format.Visible = true;
            CBox_SellingPoint.Visible = false;

            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void Lbl_Date_Print_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void Lbl_Date_Print_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_Date_Print);

        }
        private void Lbl_Date_Print_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_Date_Print.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Date_Print.x = X;
            card.cardsItems.Date_Print.y = Y;

        }

        private void CBox_Date_print_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (card == null)
                return;
            if (saveToClass == false)
                return;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.cardsItems.Date_Print.format = CBox_Date_print_format.SelectedItem.ToString();
            Loc_item_Set_To_Use();

        }

        #endregion

        #region OtherText1
        private void OtherText1_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card==null) return;
            if (OtherText1_Chbox.Checked == true)
            {
                card.cardsItems.Other_Text1.Enable = true;
                Lbl_OtherText1.Visible = card.cardsItems.Other_Text1.Enable;
                checkShowUnit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;
                DisableBorder_All(Lbl_OtherText1);
                Set_Proprties_For_Item(Lbl_OtherText1, card.cardsItems.Other_Text1);
                check_Location_control_isOut(Lbl_OtherText1);

                txt_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
                Lbl_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
                txt_AddresItem.Text = card.cardsItems.Other_Text1.title_text;

                checkShowAddresItem.Checked = false;
            }
            else
            {
                Lbl_OtherText1.Visible = false;
                //Lbl_OtherText1.Visible = false;
                card.cardsItems.Other_Text1.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void Lbl_OtherText1_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;


            DisableBorder_All(Lbl_OtherText1);
            Set_Proprties_For_Item(Lbl_OtherText1, card.cardsItems.Other_Text1);
            if (e.Button == MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }
            saveToClass = true;

        }

        private void Lbl_OtherText1_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown)
                return;

            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (sender is Label)
                {
                    Control control = (Label)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;

                }
                else if (sender is Panel)
                {
                    Control control = (Panel)sender;
                    control.Left = e.X + control.Left - MouseDownLocation.X;
                    control.Top = e.Y + control.Top - MouseDownLocation.Y;
                }
            }

        }

        private void Lbl_OtherText1_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            check_Location_control_isOut(Lbl_OtherText1);

        }

        private void Lbl_OtherText1_Move(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Point point = new Point();
            point = Lbl_OtherText1.Location;
            decimal X = (point.X / (decimal)7.0);
            decimal Y = (point.Y / (decimal)7.0);
            txt_Element_W.Text = X.ToString("0.0");
            txt_Element_Y.Text = Y.ToString("0.0");
            card.cardsItems.Other_Text1.x = X;
            card.cardsItems.Other_Text1.y = Y;

        }

        private void txt_OtherText1_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Lbl_OtherText1.Text = txt_OtherText1.Text;
            card.cardsItems.Other_Text1.title_text = txt_OtherText1.Text;
            //Loc_item_Set_To_Use();

        }
        #endregion

        #region
        private void Logo_Chbox_CheckedChanged(object sender, EventArgs e)
        {
         
            if (selectTemplateFromDrowpDown || firstLoad)
                return;  
            if (card==null) return;

            checkShowUnit_Item.Enabled = false;
            //groupBox_Date_print_format.Enabled = false;
            try
            {
                if (Logo_Chbox.Checked == true)
                {
                    pictureBox_logo.Visible = true;
                    card.cardsItems.logo.Enable = true;
                    //txt_AddresItem.Text = loc_QR.address_text;
                    txt_LogoImage.Text = card.cardsItems.logo.Path;
                    DisableBorder_All(pictureBox_logo);
                    Set_Proprties_For_Item_img(pictureBox_logo, card.cardsItems.logo);
                    check_Location_control_isOut(pictureBox_logo);


                }
                else
                {
                    pictureBox_logo.Visible = false;
                    card.cardsItems.logo.Enable = false;
                }
            }
            catch { }

        }

        private void pictureBox_logo_MouseDown(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            DisableBorder_All(pictureBox_logo);
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                MouseDownLocation = e.Location;
            }

        }

        private void pictureBox_logo_MouseMove(object sender, MouseEventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (e.Button == MouseButtons.Left)
            {
                pictureBox_logo.Left = e.X + pictureBox_logo.Left - MouseDownLocation.X;
                pictureBox_logo.Top = e.Y + pictureBox_logo.Top - MouseDownLocation.Y;
            }

        }

        private void pictureBox_logo_Move(object sender, EventArgs e)
        {

            try
            {
                if (selectTemplateFromDrowpDown)
                    return;

                Point point = new Point();
                point = pictureBox_logo.Location;

                decimal X = (point.X / (decimal)7.0);
                decimal Y = (point.Y / (decimal)7.0);

                txt_Element_W.Text = X.ToString("0.0");
                txt_Element_Y.Text = Y.ToString("0.0");


                card.cardsItems.logo.x = X;
                card.cardsItems.logo.y = Y;

            }
            catch { }

        }

        private void pictureBox_logo_Resize(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;
            try
            {
                if (!firstLoad)
                {

                    pictureBox_logo.Height = (pictureBox_logo.Width);
                    txt_dimension_w.Value = Convert.ToDecimal(pictureBox_logo.Width) / (decimal)7.0;
                    txt_dimension_H.Value = Convert.ToDecimal(pictureBox_logo.Height) / (decimal)7.0;
                    ////===========================
                    //    txt_dimension_w.Value = Convert.ToDecimal(pictureBox_QR.Width) / (decimal)7.0;
                    //    txt_dimension_H.Value = Convert.ToDecimal(pictureBox_QR.Height) / (decimal)7.0;

                    card.cardsItems.logo.item_dimension_w = txt_dimension_w.Value;
                    card.cardsItems.logo.item_dimension_y = txt_dimension_H.Value;

                    //move_qr();
                }
            }
            catch { }

        }

        private void rjButton6_Click(object sender, EventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            set_logoFromPath(false);

        }
        #endregion

        private void txt_Element_W_KeyDown(object sender, KeyEventArgs e)
        {
            //if (selectTemplateFromDrowpDown || firstLoad)
            //    return;

            if (e.KeyCode == Keys.Enter)
            {
                Loc_item_Set_To_Use();
                Control lbl = getSelectControl_pic_lbl();
                ChangPositonControl_In_Imag(lbl);
                //loc_itemtemp.x = lbl.Location.X / 5;

                //Loc_item_Save_in_struct();
            }
            Loc_item_Set_To_Use();
        }

        private void txt_Element_W_Leave(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;

            Loc_item_Set_To_Use();
            Control lbl = getSelectControl_pic_lbl();
            ChangPositonControl_In_Imag(lbl);
            //loc_itemtemp.y = txt_Element_Y.Value;
            //Loc_item_Save_in_struct();
            Loc_item_Set_To_Use();
        }

        private void txt_Element_W_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && numvalue != txt_Element_W.Value)
            {
                Control lbl = getSelectControl_pic_lbl();
                ChangPositonControl_In_Imag(lbl);
                //loc_itemtemp.x = txt_Element_W.Value;
                //Loc_item_Save_in_struct();

                //MessageBox.Show(txt_Element_W.Value.ToString());
            }

            numvalue = txt_Element_W.Value;
            Loc_item_Set_To_Use();
        }

        private void txt_Element_Y_KeyDown(object sender, KeyEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (e.KeyCode == Keys.Enter)
            {
                //Loc_item_Set_To_Use();
                Control lbl = getSelectControl_pic_lbl();
                ChangPositonControl_In_Imag(lbl);
                //loc_itemtemp.y = lbl.Location.Y / 5;
                //Loc_item_Save_in_struct();
            }
            Loc_item_Set_To_Use();
        }

        private void txt_Element_Y_Leave(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown)
                return;
            Loc_item_Set_To_Use();
            Control lbl = getSelectControl_pic_lbl();
            ChangPositonControl_In_Imag(lbl);
            //loc_itemtemp.y = txt_Element_Y.Value;
            //Loc_item_Save_in_struct();
        }

        private void txt_Element_Y_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && numvalue != txt_Element_Y.Value)
            {
                Loc_item_Set_To_Use();
                Control lbl = getSelectControl_pic_lbl();
                ChangPositonControl_In_Imag(lbl);
            }

            numvalue = txt_Element_W.Value;
        }

        private void txt_dimension_w_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            Control pic = getSelectControl_pic_lbl();
            //ChangPositonControl_In_Imag(pic);
            Change_Dimension_ControlImag(pic);
            Loc_item_Set_To_Use();
        }

        private void txt_dimension_H_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            Control pic = getSelectControl_pic_lbl();
            //ChangPositonControl_In_Imag(pic);
            Change_Dimension_ControlImag(pic);
            Loc_item_Set_To_Use();
        }

        private void CB_ElementSize_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Convert.ToInt16(CB_ElementSize.Text) <= 4)
                CB_ElementSize.Text = 6.ToString();

            Loc_item_Set_To_Use();

            //CB_Fonts
        }

        private void CB_ElementSize_Leave(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Convert.ToInt16(CB_ElementSize.Text) <= 4)
                CB_ElementSize.Text = 6.ToString();

            Loc_item_Set_To_Use();

        }

        private void btn_ElementColor_Click(object sender, EventArgs e)
        {
            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_ElementColor.BackColor = colorDialog1.Color;
                Loc_item_Set_To_Use();
            }
        }

        private void checkBoxISBlod_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Loc_item_Set_To_Use();
        }

        private void checkBoxIsItalic_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Loc_item_Set_To_Use();
        }

        private void CB_Fonts_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Control lbl = getSelectControl_pic_lbl();
            try
            {
                iTextSharp.text.Font palatino = palatino = FontFactory.GetFont(CB_Fonts.SelectedItem.ToString(), BaseFont.CP1252, BaseFont.EMBEDDED, Convert.ToInt16(CB_ElementSize.Text), iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
            }
            catch
            {
                MessageBox.Show("الخط غير مدعوم");
                return;
            }
            txt_font.Text = CB_Fonts.SelectedItem.ToString();
            //loc_itemtemp.Font = CB_Fonts.SelectedItem.ToString();

            if (txt_font.Text.ToLower().Contains("blod"))
            {
                checkBoxISBlod.Checked = true;
            }
            Loc_item_Set_To_Use();

        }

        private void CB_ElementSize_KeyUp(object sender, KeyEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Convert.ToInt16(CB_ElementSize.Text) <= 4)
                CB_ElementSize.Text = 6.ToString();

            Loc_item_Set_To_Use();

        }

        private void TextCard_Y_KeyDown(object sender, KeyEventArgs e)
        {
            if (!firstLoad)
            {
                try
                {
                    if (e.KeyCode == Keys.Enter)
                    {
                        pictureBox1.Height = Convert.ToInt16((Convert.ToDouble(TextCard_Y.Text)) * (float)7.0);
                    }
                }
                catch { }
            }
        }

        private void TextCard_W_Leave(object sender, EventArgs e)
        {
            try
            {
                pictureBox1.Width = Convert.ToInt16((Convert.ToDouble(TextCard_W.Text)) * (float)7.0);
            }
            catch { }
        }

        private void TextCard_W_KeyDown(object sender, KeyEventArgs e)
        {
            if (!firstLoad)
            {
                try
                {
                    if (e.KeyCode == Keys.Enter)
                    {
                        pictureBox1.Width = Convert.ToInt16((Convert.ToDouble(TextCard_W.Text)) * (float)7.0);
                    }
                }
                catch { }
            }
        }

        private void TextCard_W_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && numvalue != TextCard_W.Value)
            {
                pictureBox1.Width = Convert.ToInt16((TextCard_W.Value) * (decimal)7.0);
                card.setingCard.card_width = TextCard_W.Value;

            }
            numvalue = txt_Element_W.Value;
 
            Calclate_Number_Card_In_Page();
        }

        private void TextCard_W_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            pictureBox1.Width = Convert.ToInt16((TextCard_W.Value) * (decimal)7.0);
            card.setingCard.card_width = TextCard_W.Value;

            Calclate_Number_Card_In_Page();

        }

        private void TextCard_Y_Leave(object sender, EventArgs e)
        {
            pictureBox1.Height = Convert.ToInt16((Convert.ToDouble(TextCard_Y.Text)) * (float)7.0);

        }

        private void TextCard_Y_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && numvalue != TextCard_Y.Value)
            {
                pictureBox1.Height = Convert.ToInt16((Convert.ToDouble(TextCard_Y.Text)) * (float)7.0);
            }
            numvalue = txt_Element_Y.Value;
            Calclate_Number_Card_In_Page();
        }

        private void TextCard_Y_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            pictureBox1.Height = Convert.ToInt16((TextCard_Y.Value) * (decimal)7.0);
            card.setingCard.card_height = TextCard_Y.Value;

            Calclate_Number_Card_In_Page();

            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }
        }

        private void txt_Space_Y_Leave(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Calclate_Number_Card_In_Page();
            card.setingCard.Space_vertical_margin = Convert.ToDecimal(txt_Space_Y.Value);
        }

        private void txt_Space_Y_MouseUp(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Calclate_Number_Card_In_Page();
        }

        private void txt_Space_Y_ValueChanged(object sender, EventArgs e)
        {
           
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Calclate_Number_Card_In_Page();
            
            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }

            card.setingCard.Space_vertical_margin = Convert.ToDecimal(txt_Space_Y.Value);
          
        }

        private void txt_Space_X_Leave(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Calclate_Number_Card_In_Page();
            card.setingCard.space_horizontal_margin = Convert.ToDecimal(txt_Space_X.Value);
        }

        private void txt_Space_X_MouseUp(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Left && numvalue != txt_Element_W.Value)
            {
                Calclate_Number_Card_In_Page();
                //CalcluteColumAndCard();
            }
        }

        private void txt_Space_X_ValueChanged(object sender, EventArgs e)
        {
            Calclate_Number_Card_In_Page();
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }
            card.setingCard.space_horizontal_margin = Convert.ToDecimal(txt_Space_X.Value);
            Calclate_Number_Card_In_Page();
        }

        private void checkBoxBorderCard_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.card_border_enable = checkBoxBorderCard.Checked;

        }

        private void txt_SizeBorder_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;


            pictureBox1.Update();
            Refresh();
            card.setingCard.card_border_Size = (float)(txt_SizeBorder.Value);
        }

        private void btn_BorderColor_Click(object sender, EventArgs e)
        {

            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_BorderColor.BackColor = colorDialog1.Color;
                card.setingCard.card_border_Color = System.Drawing.ColorTranslator.ToHtml(colorDialog1.Color);
            }
            pictureBox1.Update();
            Refresh();
        }

        private void checkShowAddresItem_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void txt_AddresItem_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Loc_item_Set_To_Use();
        }

        private void btn_title_ElementColor_Click(object sender, EventArgs e)
        {
            RJMessageBox.Show("تغير لون عنوان المتغير غير متاح حاليا");
            return;
            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_title_ElementColor.BackColor = colorDialog1.Color;
                Loc_item_Set_To_Use();
            }
        }

        private void checkShowUnit_Item_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            Loc_item_Set_To_Use();
        }

        private void CBox_UniteTransfer_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.cardsItems.Size.unit_format = CBox_UniteTransfer_format.SelectedIndex;
        }

        private void CBox_UniteValidaty_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            card.cardsItems.Validity.unit_format = CBox_UniteValidaty_format.SelectedIndex;
        }

        private void CBox_UniteTime_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.cardsItems.Time.unit_format = CBox_UniteTime_format.SelectedIndex;

        }

        private void check_Number_Pages_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.Number_Pages = check_Number_Pages.Checked;
            //save_Numbers_Page_info();
        }

        private void txt_Number_Page_X_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_X = txt_Number_Page_X.Value;

                }
                catch { }
            }
        }

        private void txt_Number_Page_Y_ValueChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_Y = txt_Number_Page_Y.Value;

                }
                catch { }
            }
        }

        private void txt_Number_Page_Size_ValueChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_Size = txt_Number_Page_Size.Value;

                }
                catch { }
            }
        }

        private void checkNoteOnPage_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.Note_On_Pages = checkNoteOnPage.Checked;
            card.setingCard.Note_On_Pages_text = txt_Note_onPage.Text;
            card.setingCard.Note_On_Pages_X = txt_Note_Page_X.Value;
            card.setingCard.Note_On_Pages_Y = txt_Note_Page_Y.Value;
            card.setingCard.Note_On_Pages_Size = txt_Note_Page_Size.Value;
            card.setingCard.NoteType_onPage = CBox_NoteType_onPage.SelectedIndex;
        }

        private void CBox_NoteType_onPage_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.NoteType_onPage = CBox_NoteType_onPage.SelectedIndex;


                }
                catch { }
            }
        }

        private void txt_Note_onPage_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.Note_On_Pages_text = txt_Note_onPage.Text;
        }

        private void txt_Note_Page_X_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_X = txt_Note_Page_X.Value;

                }
                catch { }
            }
        }

        private void txt_Note_Page_Y_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_Y = txt_Note_Page_Y.Value;

                }
                catch { }
            }
        }

        private void txt_Note_Page_Size_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_Size = txt_Note_Page_Size.Value;

                }
                catch { }
            }
        }

        private void txt_Element_W_ValueChanged(object sender, EventArgs e)
        {

        }

        private void txt_Element_Y_ValueChanged(object sender, EventArgs e)
        {

        }

        private void CBox_Profile_HotspotLocal_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_HS_Local_link = CBox_Profile_HotspotLocal.SelectedValue.ToString();
            }
            catch { }
        }

        private void btn_Items_Click(object sender, EventArgs e)
        {
            Form_CardsDesigen_Custom_items items = new Form_CardsDesigen_Custom_items(card,this);
            items.Login_Chbox.Checked = Login_Chbox.Checked;
            items.Password_Chbox.Checked = Password_Chbox.Checked;
            items.Size_Chbox.Checked = Size_Chbox.Checked;
            items.Squnce_Number_Chbox.Checked = Squnce_Number_Chbox.Checked;
            items.validity_Chbox.Checked = validity_Chbox.Checked;
            items.Price_Chbox.Checked = Price_Chbox.Checked;
            items.Time_Chbox.Checked = Time_Chbox.Checked;
            items.Date_Print_Chbox.Checked = Date_Print_Chbox.Checked;
            items.QR_Chbox.Checked = QR_Chbox.Checked;
            //items.txt_Page_Url.Text = txt_Page_Url;
            items.SP_Chbox.Checked = SP_Chbox.Checked;
            items.Number_Print_Chbox.Checked = Number_Print_Chbox.Checked;
            items.Number_Batch.Checked = Number_Batch_Chbox.Checked;
            items.OtherText1_Chbox.Checked = OtherText1_Chbox.Checked;
            items.Logo_Chbox.Checked = Logo_Chbox.Checked;
            items.txt_LogoImage.Text=txt_LogoImage.Text;
            items.txt_Page_Url.Text= card.cardsItems.QR.url;

            //items.Logo_Chbox.Checked = Logo_Chbox.Checked;

            //items.StartPosition = FormStartPosition.CenterParent;
            //items.Location=new Point(0,0);

            //var locationInForm = myControl.Location;
            //var locationOnScreen = this.PointToScreen(locationInForm);

            //using (var model = new Form_CardsDesigen_Custom_items())
            //{
            //    model.Location = new Point(locationOnScreen.X, locationOnScreen.Y + myControl.Height + 3);
            //    model.ShowDialog();
            //}

            items.ShowDialog(this);
            //if (items.StartPosition == FormStartPosition.CenterParent)
            //{
            //    //var x = Location.X - (Width + items.Width) ;
            //    var x = Location.X + (this.Width - items.Width)/2 ;
            //    //var x = Location.X + (Width - items.Width) / 2;
            //    var y = Location.Y + this.Height;
            //    //var y = Location.Y + (Height - items.Height) / 2;
            //    items.Location = new Point(Math.Max(x, 0), Math.Max(y, 0));
            //}

            Login_Chbox.Checked = items.Login_Chbox.Checked;
            Password_Chbox.Checked = items.Password_Chbox.Checked;
            Size_Chbox.Checked = items.Size_Chbox.Checked;
            Squnce_Number_Chbox.Checked = items.Squnce_Number_Chbox.Checked;
            validity_Chbox.Checked = items.validity_Chbox.Checked;
            Price_Chbox.Checked = items.Price_Chbox.Checked;
            Time_Chbox.Checked = items.Time_Chbox.Checked;
            Date_Print_Chbox.Checked = items.Date_Print_Chbox.Checked;
            QR_Chbox.Checked = items.QR_Chbox.Checked;
            //items.txt_Page_Url.Text = txt_Page_Url.;
            SP_Chbox.Checked = items.SP_Chbox.Checked;
            Number_Print_Chbox.Checked = items.Number_Print_Chbox.Checked;
            Number_Batch_Chbox.Checked = items.Number_Batch.Checked;
            OtherText1_Chbox.Checked = items.OtherText1_Chbox.Checked;
            Logo_Chbox.Check = items.Logo_Chbox.Checked;
            txt_LogoImage.Text = items.txt_LogoImage.Text;
            txt_Page_Url.Text = items.txt_Page_Url.Text;

            card = items.card;
        }

        private void pictureBox1_SizeChanged_1(object sender, EventArgs e)
        {
            pictureBox1.Location = new Point((groub_Design.Width / 2) - pictureBox1.Width / 2, (groub_Design.Height / 2) - pictureBox1.Height / 2);
            Calclate_Number_Card_In_Page();
        }

        
        
    }
}
