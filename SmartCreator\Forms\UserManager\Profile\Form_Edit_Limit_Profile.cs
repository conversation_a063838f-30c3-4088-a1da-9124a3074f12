﻿using CefSharp.DevTools.Profiler;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using tik4net;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_Edit_Limit_Profile : RJForms.RJChildForm
    {
        UmProfile profile;
        UmLimitation limitation;
        UmProfile_Limtition ProfileLimtitions;
        public bool succes = false;
        public bool add = true;

        public Form_Edit_Limit_Profile()
        {
            btnSave.BackColor = RJColors.Confirm;
        }
        public Form_Edit_Limit_Profile(UmProfile _profile)
        {
            InitializeComponent();
            Set_Font();
            btnSave.BackColor = UIAppearance.StyleColor;
            this.profile = _profile;

            txt_from.Text = "00:00:00";
            txt_to.Text = "23:59:59";

            lblTitle.Text = "Add new limit to profile";
            this.Text = "Add new limit";
            btnSave.Text = "Add";
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = profile.Name + "  اضافة تحديد جديد للباقة  ";
                this.Text = " اضافة تحديد للباقة";
                btnSave.Text = "اضافة";
               
            }
            txt_Download.Text = "0";
            txt_SpeedUpload.Text = "0";
            txt_SpeedDown.Text = "0";
            txt_uptime_Hour.Text = "0";
            CBox_SizeDownload.SelectedIndex = 0;
            CBox_speedDownlad.SelectedIndex = 0;
            CBox_speedUpload.SelectedIndex = 0;
            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";

                CBox_profile_hotspot.SelectedIndex = 0;
                CBox_profile_hotspot.Text = "";
            }
            catch { }

          
        }
        public Form_Edit_Limit_Profile(UmLimitation _limitation,UmProfile _profile,UmProfile_Limtition _ProfileLimtitions)
        {
            InitializeComponent();
            Set_Font();
            this.limitation = _limitation;
            this.profile = _profile;
            this.ProfileLimtitions=_ProfileLimtitions;


            txt_from.Text = "00:00:00";
            txt_to.Text = "23:59:59";

            //profile_eidt = profile;
            lblTitle.Text = "Edit profile";
            this.Text = "Edit profile";
            btnSave.Text = "edit";

            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = _profile.Name + "  تعديل تحديد الباقة  ";
                this.Text = "تعديل محدد الباقة";
                btnSave.Text = "تعديل";
                System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12, FontStyle.Regular);
                lblTitle.Font = title_font;
                btnSave.Font = title_font;
            }
            txt_profileName.Text = limitation.Name;
            txt_profileName.Enabled = false;
            txt_profileName._Customizable = false;
            txt_profileName.BackColor = Color.FromArgb(240, 245, 249);


            //profile.IdHX_limt = profile.Limits[indexLimit].IdHX;
            //profile.IdHX_prfileLimt = profile.Profile_Limtitions[indexLimit].IdHX;

            //===========================
            string transfer = utils.ConvertSize_Get_En(limitation.TransferLimit.ToString());
            //if (transfer.Contains("GB"))
            //    CBox_SizeDownload.SelectedIndex = 1;
            //else
            if (transfer.Contains("."))
            {
                CBox_SizeDownload.SelectedIndex = 0;
                txt_Download.Text = utils.ConvertSize_Get_InMB_without_Uint(limitation.TransferLimit.ToString());
            }
            else
            {
                if (transfer.Contains("GB"))
                    CBox_SizeDownload.SelectedIndex = 1;
                else
                    CBox_SizeDownload.SelectedIndex = 0;

                txt_Download.Text = utils.ConvertSize_Get_Without_Uint(limitation.TransferLimit.ToString());

            }
            //========================================
            string upload_rx = utils.ConvertSize_Get_En(limitation.Upload_rx.ToString());
            if (upload_rx.Contains("MB"))
                CBox_speedUpload.SelectedIndex = 1;
            else
                CBox_speedUpload.SelectedIndex = 0;
            txt_SpeedUpload.Text = utils.ConvertSize_Get_Without_Uint(limitation.Upload_rx.ToString());
            //===================================================
            string download_tx = utils.ConvertSize_Get_En(limitation.Download_tx.ToString());
            if (download_tx.Contains("MB"))
                CBox_speedDownlad.SelectedIndex = 1;
            else
                CBox_speedDownlad.SelectedIndex = 0;
            txt_SpeedDown.Text = utils.ConvertSize_Get_Without_Uint(limitation.Download_tx.ToString());

            if (!string.IsNullOrEmpty(txt_SpeedDown.Text) && txt_SpeedDown.Text != "0")
                rjCheckBox_Speed.Checked = true;
            if (!string.IsNullOrEmpty(txt_SpeedUpload.Text) && txt_SpeedUpload.Text != "0")
                rjCheckBox_Speed.Checked = true;
            //===================================================

            int hour_uptime = ((Int32)((Convert.ToInt32(limitation.UptimeLimit)) / 3600));
            txt_uptime_Hour.Text = hour_uptime.ToString();

            int minut_uptime = ((int)(Convert.ToInt32(limitation.UptimeLimit) / 60 % 60));
            if (minut_uptime > 0)
                txt_uptime_Hour.Text = (hour_uptime + "." + minut_uptime).ToString();

            //txt_uptime_Hour.Text = ((Int32)((Convert.ToInt32(profile.uptimeLimit)) / 3600)).ToString();
            //txt_uptime_Minut.Text = ((int)(Convert.ToInt32(profile.uptimeLimit) / 60 % 60)).ToString();

            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";
                CBox_profile_hotspot.Text = "";
            }
            catch { }

            CBox_profile_hotspot.Text = limitation.GroupName.ToString();

            if (limitation.GroupName.ToString() != "")
                rjCheckBox_group.Checked = true;

            if (ProfileLimtitions.Weekdays.Contains(","))
            {
                string[] split = ProfileLimtitions.Weekdays.Split(new string[] { "," }, StringSplitOptions.None);
                if (split.Length > 0)
                {
                    //"sunday,monday,tuesday,wednesday,thursday,friday,saturday"
                    for (int i = 0; i < split.Length; i++)
                    {
                        if (split[i].ToString() == "saturday")
                            check_saturday.Checked = true;
                        else if (split[i].ToString() == "sunday")
                            check_sunday.Checked = true;
                        else if (split[i].ToString() == "monday")
                            check_monday.Checked = true;
                        else if (split[i].ToString() == "tuesday")
                            check_tuesday.Checked = true;
                        else if (split[i].ToString() == "wednesday")
                            check_wednesday.Checked = true;
                        else if (split[i].ToString() == "thursday")
                            check_thursday.Checked = true;
                        else if (split[i].ToString() == "friday")
                            check_friday.Checked = true;
                    }
                }

                if (ProfileLimtitions.Weekdays != "sunday,monday,tuesday,wednesday,thursday,friday,saturday")
                    check_weekdays.Check = true;
            }
            txt_from.Text = utils.GetTimeCard_By_clock_Mode(ProfileLimtitions.From_time.ToString());
            txt_to.Text = utils.GetTimeCard_By_clock_Mode(ProfileLimtitions.Till_time.ToString());
        }

        private void Set_Font()
        {

            utils utils = new utils();
            utils.Control_textSize1(this);


            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in groupBox1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in groupBox2.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }

            CBox_SizeDownload.Font = fnt;
            CBox_speedDownlad.Font = fnt;
            CBox_speedUpload.Font = fnt;

            lblTitle.Font = btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12 , FontStyle.Bold);

            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 12 , FontStyle.Regular);


            utils.Control_textSize(pnlClientArea);
            return;

            Control_Loop(pnlClientArea);
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            //UmProfile prof= new UmProfile();
            //prof.Name = txt_from.Text;
            //prof.IdHX_prfileLimt =profile.IdHX_prfileLimt;
            //prof.UptimeLimit = profile.UptimeLimit;

            if (check() == false)
                return;
            succes = true;
            this.Close();

        }

        [Obsolete]
        bool check()
        {
            if (txt_profileName.Text == "")
            {
                RJMessageBox.Show("اكتب اسم التحديد");
                return false;
            }
            var item = Global_Variable.Source_limtition.SingleOrDefault(x => x.Name == txt_profileName.Text.Trim());
            if (item != null && add)
            {
                RJMessageBox.Show("اسم قد يكون مكرر");
                return false;
            }

            int number2;
           
            if (!(float.TryParse(txt_uptime_Hour.Text, out float Hnumber)))
            {
                RJMessageBox.Show(" الوقت ساعات يجب ان تكون رقم صحيح او كسر عشري");
                return false;
            }
            if (!(float.TryParse(txt_Download.Text, out float downl)))
            {
                RJMessageBox.Show("كمية التحميل  يجب ان تكون رقم صحيح او كسر عشري");
                return false;
            }
           
            if (!(int.TryParse(txt_SpeedUpload.Text, out number2)))
            {
                RJMessageBox.Show("سرعة الرفع يجب ان تكون رقم");
                return false;
            }
            if (!(int.TryParse(txt_SpeedDown.Text, out number2)))
            {
                RJMessageBox.Show("سرعة التحميل يجب ان تكون رقم");
                return false;
            }
             
            
            double downloadSize = 0;
            double downloadSpeed = 0;
            double UploadSpeed = 0;

            //=====================================
            if (CBox_SizeDownload.SelectedIndex == 0)
                downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024);
            if (CBox_SizeDownload.SelectedIndex == 1)
                downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024 * 1024);
            if (CBox_SizeDownload.SelectedIndex == -1 && txt_Download.Text != "0")
            {
                RJMessageBox.Show("اختر وحدة الحجم");
                return false;
            }
            //=====================================
            if (rjCheckBox_Speed.Check)
            {
                if (CBox_speedDownlad.SelectedIndex == 0)
                    downloadSpeed = (Convert.ToDouble(txt_SpeedDown.Text) * 1024);
                if (CBox_speedDownlad.SelectedIndex == 1)
                    downloadSpeed = (Convert.ToDouble(txt_SpeedDown.Text) * 1024 * 1024);
                if (CBox_speedDownlad.SelectedIndex == -1 && txt_SpeedDown.Text != "0")
                {
                    RJMessageBox.Show("اختر وحدة لسرعة الباقة");
                    return false;
                }
                //==============================
                if (CBox_speedUpload.SelectedIndex == 0)
                    UploadSpeed = (Convert.ToDouble(txt_SpeedUpload.Text) * 1024);
                if (CBox_speedUpload.SelectedIndex == 1)
                    UploadSpeed = (Convert.ToDouble(txt_SpeedUpload.Text) * 1024 * 1024);
                if (CBox_speedUpload.SelectedIndex == -1 && txt_SpeedUpload.Text != "0")
                {
                    RJMessageBox.Show("اختر وحدة لسرعة الباقة");
                    return false;
                }
            }
            //======================================

            //=========================================
            UmProfile prf = new UmProfile();
            prf.Name = txt_profileName.Text.Trim();
            //============================
            prf.TransferLimit = downloadSize;
            //prf.uptimeLimit_str = (txt_uptime_Hour.Text + "h" + txt_uptime_Minut.Text + "m");
    
            if (txt_uptime_Hour.Text.Contains("."))
            {
                string[] split = txt_uptime_Hour.Text.Split(new string[] { "." }, StringSplitOptions.None);
                if (split.Length > 0)
                {
                    prf.UptimeLimit = (Convert.ToDouble(split[0]) * 60 * 60) + (Convert.ToInt32(split[1]) * 60);
                }
            }
            else
                prf.UptimeLimit = (Convert.ToDouble(txt_uptime_Hour.Text) * 60 * 60);

            prf.Str_uptimeLimit = prf.UptimeLimit.ToString();
            prf.Upload_rx = UploadSpeed.ToString();
            prf.Download_tx = downloadSpeed.ToString();
            prf.Owner = prf.Owner;
            if (rjCheckBox_group.Checked)
                prf.GroupName = CBox_profile_hotspot.Text;

            string weekDays = "sunday,monday,tuesday,wednesday,thursday,friday,saturday";
            string fromTime = "00h00m00s";
            string toTime = "23h59m59s";
            if (check_weekdays.Checked)
            {
                string[] Spilt_fromTime = txt_from.Text.Split(new string[] { ":" }, StringSplitOptions.None);
                fromTime = Spilt_fromTime[0] + "h" + Spilt_fromTime[1] + "m" + Spilt_fromTime[2] + "s";
                string[] Spilt_ToTime = txt_to.Text.Split(new string[] { ":" }, StringSplitOptions.None);
                toTime = Spilt_ToTime[0] + "h" + Spilt_ToTime[1] + "m" + Spilt_ToTime[2] + "s";
                weekDays = "";
                if (check_saturday.Check)
                    weekDays += ",saturday";
                if (check_sunday.Check)
                    weekDays += ",sunday";
                if (check_monday.Check)
                    weekDays += ",monday";
                if (check_tuesday.Check)
                    weekDays += ",tuesday";
                if (check_wednesday.Check)
                    weekDays += ",wednesday";
                if (check_thursday.Check)
                    weekDays += ",thursday";
                if (check_friday.Check)
                    weekDays += ",friday";
            }
            prf.Weekdays = weekDays.TrimStart(',');
            prf.From_time = fromTime;
            prf.Till_time = toTime;




            Mk_DataAccess dataAccess = new Mk_DataAccess();

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_DataAccess.Mk_Conn(connection) == false)
                    return false;
                if (add)
                {
                    prf.Name_limt = txt_profileName.Text;
                    prf.Name = profile.Name;

                    UmLimitation ul = Global_Variable.Source_limtition.Find(x => x.Name == txt_profileName.Text);
                    if(ul!=null)
                    {
                        RJMessageBox.Show("حدث مشكله قد يكون الاسم مكرر");
                        return false;
                    }

                    string id_profile = dataAccess._Add_UserManager_Limition(connection, prf);
                    if (string.IsNullOrEmpty(id_profile))
                    {
                        RJMessageBox.Show("حدث مشكله قد يكون الاسم مكرر");
                        return false;
                    }

                    
                    string id_pl = dataAccess._Add_UserManager_Profile_Limition(connection, prf);
                    if (string.IsNullOrEmpty(id_pl))

                        RJMessageBox.Show("حدث مشكله");
                }
                else
                {
                    prf.IdHX = profile.IdHX;
                    prf.IdHX_limt = limitation.IdHX;
                    prf.IdHX_prfileLimt = ProfileLimtitions.IdHX;

                    //prf.IdHX = profile.IdHX;
                    //prf.Sn_Name = profile.Sn_Name;
                    //prf.IdHX_limt = profile.IdHX_limt;
                    //prf.IdHX_prfileLimt = profile.IdHX_prfileLimt;


                    if (dataAccess._Edit_UserManager_Limition(connection, prf))
                    {
                        if (dataAccess._Edit_UserManager_Profile_Limition(connection, prf))

                            RJMessageBox.Show("تمت العملية بنجاح");
                    }

                }
            }
            //======================================================================================
            succes = true;
            this.Close();
            return true;

        }

        private void Form_Edit_Limit_Profile_Load(object sender, EventArgs e)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                //lbl_group.Visible = false;
                rjCheckBox_group.Visible = false;
                CBox_profile_hotspot.Visible = false;
            }
        }
    }
}
