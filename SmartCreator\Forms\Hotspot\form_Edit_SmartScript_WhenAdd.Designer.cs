﻿namespace SmartCreator.Forms.Hotspot
{
    partial class form_Edit_SmartScript_WhenAdd
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.lbl_DayOrHour = new SmartCreator.RJControls.RJLabel();
            this.lbl_Save_session = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_byDayOrHour = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_session = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_download = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_time = new SmartCreator.RJControls.RJCheckBox();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.pnlClientArea.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.btnSave);
            this.pnlClientArea.Controls.Add(this.tableLayoutPanel1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(473, 196);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(137, 22);
            this.lblCaption.Text = "تخصيص صلاحيات الكروت";
            // 
            // lbl_DayOrHour
            // 
            this.lbl_DayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_DayOrHour.AutoSize = true;
            this.lbl_DayOrHour.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_DayOrHour.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_DayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_DayOrHour.LinkLabel = false;
            this.lbl_DayOrHour.Location = new System.Drawing.Point(91, 90);
            this.lbl_DayOrHour.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_DayOrHour.Name = "lbl_DayOrHour";
            this.lbl_DayOrHour.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.lbl_DayOrHour.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_DayOrHour.Size = new System.Drawing.Size(335, 25);
            this.lbl_DayOrHour.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_DayOrHour.TabIndex = 119;
            this.lbl_DayOrHour.Text = "حساب صلاحيات الايام فقط  بدون الوقت (بالايام للاشتراكات فقط)";
            this.lbl_DayOrHour.UseCompatibleTextRendering = true;
            // 
            // lbl_Save_session
            // 
            this.lbl_Save_session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Save_session.AutoSize = true;
            this.lbl_Save_session.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Save_session.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Save_session.LinkLabel = false;
            this.lbl_Save_session.Location = new System.Drawing.Point(246, 60);
            this.lbl_Save_session.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_Save_session.Name = "lbl_Save_session";
            this.lbl_Save_session.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.lbl_Save_session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Save_session.Size = new System.Drawing.Size(180, 25);
            this.lbl_Save_session.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Save_session.TabIndex = 120;
            this.lbl_Save_session.Text = "تسجيل وحفظ  جلسات للهوتسبوت ";
            this.lbl_Save_session.UseCompatibleTextRendering = true;
            // 
            // CheckBox_byDayOrHour
            // 
            this.CheckBox_byDayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_byDayOrHour.AutoSize = true;
            this.CheckBox_byDayOrHour.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.BorderSize = 1;
            this.CheckBox_byDayOrHour.Check = false;
            this.CheckBox_byDayOrHour.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDayOrHour.Customizable = false;
            this.CheckBox_byDayOrHour.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_byDayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDayOrHour.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.Location = new System.Drawing.Point(434, 93);
            this.CheckBox_byDayOrHour.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDayOrHour.Name = "CheckBox_byDayOrHour";
            this.CheckBox_byDayOrHour.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_byDayOrHour.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_byDayOrHour.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDayOrHour.TabIndex = 123;
            this.CheckBox_byDayOrHour.UseVisualStyleBackColor = true;
            this.CheckBox_byDayOrHour.CheckedChanged += new System.EventHandler(this.CheckBox_byDayOrHour_CheckedChanged);
            // 
            // CheckBox_Save_session
            // 
            this.CheckBox_Save_session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_session.AutoSize = true;
            this.CheckBox_Save_session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.BorderSize = 1;
            this.CheckBox_Save_session.Check = true;
            this.CheckBox_Save_session.Checked = true;
            this.CheckBox_Save_session.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_session.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_session.Customizable = false;
            this.CheckBox_Save_session.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_session.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.Location = new System.Drawing.Point(434, 63);
            this.CheckBox_Save_session.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_session.Name = "CheckBox_Save_session";
            this.CheckBox_Save_session.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_session.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_session.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_session.TabIndex = 124;
            this.CheckBox_Save_session.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_download
            // 
            this.CheckBox_Save_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_download.AutoSize = true;
            this.CheckBox_Save_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.BorderSize = 1;
            this.CheckBox_Save_download.Check = true;
            this.CheckBox_Save_download.Checked = true;
            this.CheckBox_Save_download.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_download.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_download.Customizable = false;
            this.CheckBox_Save_download.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.Location = new System.Drawing.Point(434, 33);
            this.CheckBox_Save_download.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_download.Name = "CheckBox_Save_download";
            this.CheckBox_Save_download.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_download.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_download.TabIndex = 125;
            this.CheckBox_Save_download.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_time
            // 
            this.CheckBox_Save_time.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_time.AutoSize = true;
            this.CheckBox_Save_time.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.BorderSize = 1;
            this.CheckBox_Save_time.Check = true;
            this.CheckBox_Save_time.Checked = true;
            this.CheckBox_Save_time.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_time.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_time.Customizable = false;
            this.CheckBox_Save_time.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.CheckBox_Save_time.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_time.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.Location = new System.Drawing.Point(434, 3);
            this.CheckBox_Save_time.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_time.Name = "CheckBox_Save_time";
            this.CheckBox_Save_time.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_Save_time.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_Save_time.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_time.TabIndex = 126;
            this.CheckBox_Save_time.UseVisualStyleBackColor = true;
            this.CheckBox_Save_time.CheckedChanged += new System.EventHandler(this.CheckBox_Save_time_CheckedChanged);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 10;
            this.btnSave.BorderSize = 2;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnSave.IconColor = System.Drawing.Color.White;
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 25;
            this.btnSave.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnSave.Location = new System.Drawing.Point(182, 157);
            this.btnSave.Name = "btnSave";
            this.btnSave.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnSave.Size = new System.Drawing.Size(119, 36);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnSave.TabIndex = 127;
            this.btnSave.Text = "حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(190, 0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(233, 25);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 128;
            this.rjLabel1.Text = "حفظ الوقت في حال الانطفاء المفاجئ للروتر";
            this.rjLabel1.UseCompatibleTextRendering = true;
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(121, 30);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(302, 25);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 129;
            this.rjLabel2.Text = "حفظ بيانات الاستهلاك والتنزيل في حالة الانظفاء المفاجى";
            this.rjLabel2.UseCompatibleTextRendering = true;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 93.88235F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 6.117647F));
            this.tableLayoutPanel1.Controls.Add(this.CheckBox_Save_time, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.rjLabel2, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.lbl_DayOrHour, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.rjLabel1, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.CheckBox_byDayOrHour, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.lbl_Save_session, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.CheckBox_Save_download, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.CheckBox_Save_session, 1, 2);
            this.tableLayoutPanel1.Location = new System.Drawing.Point(3, 15);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 4;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 30F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(454, 126);
            this.tableLayoutPanel1.TabIndex = 130;
            // 
            // form_Edit_SmartScript_WhenAdd
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "تخصيص صلاحيات الكروت";
            this.ClientSize = new System.Drawing.Size(483, 246);
            this.ControlBox = false;
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.HelpButton = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "form_Edit_SmartScript_WhenAdd";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Resizable = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "تخصيص صلاحيات الكروت";
            this.pnlClientArea.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private RJControls.RJLabel lbl_DayOrHour;
        private RJControls.RJLabel lbl_Save_session;
        private RJControls.RJButton btnSave;
        public RJControls.RJCheckBox CheckBox_byDayOrHour;
        public RJControls.RJCheckBox CheckBox_Save_session;
        public RJControls.RJCheckBox CheckBox_Save_download;
        public RJControls.RJCheckBox CheckBox_Save_time;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel2;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
    }
}