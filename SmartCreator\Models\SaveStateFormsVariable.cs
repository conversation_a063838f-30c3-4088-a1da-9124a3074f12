﻿using iTextSharp.text.xml;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Models
{
    public class SourceSaveStateFormsVariable
    {
        public int id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public string values { get; set; }
    }
    public class SourceMk_login_saved
    {
        public int id { get; set; }
        public string active { get; set; }
        public string values { get; set; }
    }
    public class Form_LoingState
    {
        public int Id { get; set; }
        public string Name { get; set; } = "FormLogin";
        public string Type { get; set; } = "SaveFromState";
        public bool Login_By_Domain { get; set; } = false;
        public bool Login_By_IP { get; set; } = true;
        public bool Login_By_SmartCloud { get; set; } = false;

        public string Mk_IP { get; set; } = "********";
        public string Mk_Domain { get; set; } = "";
        public string Mk_SmartCloud { get; set; } = "";
        public bool Is_Use_Port { get; set; } = false;
        public bool Is_Use_Port_Domain { get; set; } = false;
        public bool Is_Use_Port_SmartCloud { get; set; } = false;
        public int  Mk_Port_api { get; set; } = 8728;
        public int  Mk_Port_Domain { get; set; } = 8728;
        public int  Mk_Port_SmartCloud { get; set; } = 8728;
        public string Mk_UserName { get; set; } = "admin";
        public string Mk_password { get; set; } = "YWRtaW4=";
        public bool UserName_Rem { get; set; } = true;
        public bool Password_Rem { get; set; } = true;
        public string Note { get; set; } = "";
        public bool load_by_DownloadDB { get; set; } = false;
        public bool load_by_Disable_LoadSession { get; set; } = false;
        public bool load_by_Custom_Login { get; set; } = false;


        public bool DisableLoad_HSSession { get; set; } = false;
        public bool DisableLoad_HSUsers { get; set; } = false;
        public bool DisableLoad_UmPyment { get; set; } = false;
        public bool DisableLoad_UmSession { get; set; } = false;
        public bool DisableLoad_UmUsers { get; set; } = false;

        public int Mk_Port_ssh { get; set; } = 8729;
        //public bool is_check_Port_api { get; set; } = true;
        //public bool is_check_Port_ssh { get; set; } = false;
        
        public bool LogIn_Without_mk { get; set; } = false;
        public bool LogIn_with_disable_loadAllData { get; set; } = false;
        public bool last_open { get; set; } = true;
      
        
        public string localDB_Path { get; set; } = "db\\localDB1.db";
        public string localDB_Name { get; set; } = "localDB1.db";
        public int routerId { get; set; }
        public string rb_code { get; set; }

    }
    public class Form_PrintUserManagerState
    {
        public string Name { get; set; } = "FormUserManagerPrint";
        public string Type { get; set; } = "SaveFromState";
        public string txtNumberCard { get; set; } = "4080";
        public string txt_StartCard { get; set; } = "";
        public string txt_EndCard { get; set; } = "";
        public string CBox_CustomerUserMan { get; set; } = "admin";
        public int cbox_UserPassword_Pattern { get; set; } = 0;
        public int cbox_User_NumberORcharcter { get; set; } = 0;
        public int cbox_Pass_NumberORcharcter { get; set; } = 0;
        public string txt_longUsers { get; set; } = "8";
        public string txt_longPassword { get; set; } = "4";
        public string txt_ShardUser { get; set; } = "1";
        public string CBox_group { get; set; } = "default";
        public int CBox_Attribute { get; set; } = -1;
        public bool checkBoxFirstUse { get; set; } = false;
        public bool checkBoxSaveTo_PDF { get; set; } = true;
        public bool checkBoxSaveTo_excel { get; set; } = true;
        public bool checkBoxSaveTo_text_File { get; set; } = true;
        public bool checkBoxOpenAfterPrint { get; set; } = true;
        public bool checkBoxSaveTo_script_File { get; set; } = true;
        public bool checkBox_Create_without_Add_ToMicrotik { get; set; } = false;
        public bool checkBox_note { get; set; } = false;
        public string txt_note { get; set; } = "";

        public string path_saved_file { get; set; } = ""; 
        public string path_saved_file_Archive { get; set; } = ""; 
        public string PathFolderPrint { get; set; } = "";
        public string PathFolderPrint_Archive { get; set; } = "";


        public bool is_add_batch_cards { get; set; }=false;
        public bool is_add_batch_cards_to_Archive { get; set; }=false ;
        public bool is_Add_One_Card { get; set; }=false ;

        //==========archive====
        public bool checkBox_Remove_FromArchive { get; set; }=false ;
        public bool Archive_Radio_SN { get; set; }=true ;
        public bool Archive_Radio_Page { get; set; }=false ;
    }
    public class Form_PrintHotSpotState:Form_PrintUserManagerState
    {
        public bool checkBox_Add_Smart_Validatiy { get; set; } = true;
        public string txt_houre { get; set; } = "0";
        public string txt_validatiy { get; set; } = "0";
        public string txt_price { get; set; } = "0";
        public string txt_download { get; set; } = "0";
        public int CBox_SizeDownload { get; set; } = 0;


    }
}
