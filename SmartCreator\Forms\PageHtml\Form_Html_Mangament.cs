﻿using SmartCreator.Forms.Devices;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.PageHtml
{
    public partial class Form_Html_Mangament : RJChildForm
    {
        Form_Page_Editor form_Page_Editor;
        Form_ads_Pages form_ads_Pages;
        bool First_Page_Editor = true;
        bool First_ads_Pages = true;


        public Form_Html_Mangament()
        {
            InitializeComponent();
            this.Text = "ادارة صفحات الـ html";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "html Management";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
        }

        private void btn_PageEditor_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_PageEditor_Title);

            if (First_Page_Editor)
            {
                First_Page_Editor = false;
                form_Page_Editor = new Form_Page_Editor();
                form_Page_Editor.TopLevel = false;
                form_Page_Editor.IsChildForm = true;
                form_Page_Editor.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Page_Editor);
                this.panel_Tab_Container.Tag = form_Page_Editor;
                form_Page_Editor.Show(); //show on desktop panel  
                form_Page_Editor.BringToFront();
                form_Page_Editor.Focus();
                //form_OpentWrt_Manage.LoadDataGridviewData();

            }
            else
            {
                form_Page_Editor.BringToFront();
                form_Page_Editor.Show();
                form_Page_Editor.Focus();
            }
        }

        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_Ads_Manage_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Ads_Manage_Title);

            if (First_ads_Pages)
            {
                First_ads_Pages = false;
                form_ads_Pages = new Form_ads_Pages();
                form_ads_Pages.TopLevel = false;
                form_ads_Pages.IsChildForm = true;
                form_ads_Pages.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_ads_Pages);
                this.panel_Tab_Container.Tag = form_ads_Pages;
                form_ads_Pages.Show(); //show on desktop panel  
                form_ads_Pages.BringToFront();
                form_ads_Pages.Focus();
                //form_OpentWrt_Manage.LoadDataGridviewData();

            }
            else
            {
                form_ads_Pages.BringToFront();
                form_ads_Pages.Show();
                form_ads_Pages.Focus();
            }
        }
    }
}
