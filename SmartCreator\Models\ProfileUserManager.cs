﻿using SmartCreator.Data;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SQLite;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Dapper;
using SmartCreator.Entities.UserManager;

namespace SmartCreator.Models
{
    public class UserManager_Profile_UserManager
    {
        public int Id { get; set; }
        public string IdHX { get; set; }

        //[DisplayName("التسلسل")]
        public string Name { get; set; }
        public string owner { get; set; } = "admin";
        public double uptimeLimit { get; set; } = 0;//in second
        public string uptimeLimit_str { get; set; }
        public double Validity { get; set; } = 0; // in days
        public string Validity_str { get; set; }  
        public double transferLimit { get; set; } = 0; //in byte
        public string transferLimit_str { get; set; }
        public float Price { get; set; } = 0;
        public string Price_Disply { get; set; } = "";
        //[DisplayName("سعر العرض")]

        //public int Price_display { get; set; } = 0;
        public string SharedUsers { get; set; } = "off";
        public string groupName { get; set; } = "";
        public string NameForUser { get; set; }
        public string rateLimit { get; set; }
        public string download_tx { get; set; } = "0";
        public string upload_rx { get; set; } = "0";
        public double uptimePrice { get; set; }
        public double transferPrice { get; set; }
        public double transferLimit_MB { get; set; }
        public string Speed { get; set; }
        public string IdHX_limt { get; set; }
        public string IdHX_prfileLimt { get; set; }
        public string name_Limt { get; set; }
        public int count_limit { get; set; } = 0;
        public string from_time { get; set; } = "00:00:00";
        public string till_time { get; set; } = "23:59:59";
        public string weekdays { get; set; } = "sunday,monday,tuesday,wednesday,thursday,friday,saturday";

        public List<UmProfile_Limtition> profile_limit { get; set; } = new List<UmProfile_Limtition>();
        public List<UmLimitation> limit { get; set; } = new List<UmLimitation>();

        //public string Speed()
        //{
        //    string up = utils.ConvertSize_Get_En(upload_rx);
        //    string dow = utils.ConvertSize_Get_En(download_tx);
        //    return (up+"/"+dow);
        //}


        //public List<UserManager_Profile_UserManager> GetProfileUserManager()
        //{
        //    List<UserManager_SourceProfile_UserManager> profile = Global_Variable.Source_profile;
        //    List<UserManager_Source_Profile_Limtition> limt_profile = Global_Variable.Source_profile_limtition;
        //    List<UserManager_Source_limitation> limts = Global_Variable.Source_limtition; 

        //    List<UserManager_Profile_UserManager> finalProfile = new List<UserManager_Profile_UserManager>();
        //    try
        //    {
        //        foreach (var p in profile)
        //        {
        //            UserManager_Profile_UserManager fprofile = new UserManager_Profile_UserManager();
        //            fprofile.Id = p.id;
        //            fprofile.IdHX = p.idHX;
        //            fprofile.Name = p.Name;
        //            fprofile.Validity = p.Validity;
        //            fprofile.NameForUser = p.NameForUser;
        //            fprofile.Price = p.Price;
        //            fprofile.SharedUsers = p.SharedUsers;

        //            int count_limit = 0;
        //            foreach (var pl in limt_profile)

        //                if (p.Name == pl.profile)
        //                {
        //                    fprofile.IdHX_prfileLimt = pl.idHX;
        //                    fprofile.count_limit = count_limit + 1;
        //                    count_limit = count_limit + 1;
        //                    foreach (var l in limts)
        //                        if (l.Name == pl.limitation)
        //                        {
                                    
        //                            fprofile.from_time = pl.from_time;
        //                            fprofile.till_time = pl.till_time;
        //                            fprofile.weekdays = pl.weekdays;
        //                            fprofile.IdHX_limt = l.idHX;
        //                            fprofile.name_Limt = l.Name; 
        //                            fprofile.uptimeLimit += l.uptimeLimit;
        //                            fprofile.uptimeLimit_str = utils.Get_Seconds_By_clock_Mode(fprofile.uptimeLimit);
        //                            //fprofile.uptimeLimit_str = utils.Get_Seconds_By_clock_Mode(l.uptimeLimit);

        //                            fprofile.transferLimit += l.transferLimit;
        //                            if (UIAppearance.Language_ar)
        //                                fprofile.transferLimit_str = utils.ConvertSize_Get_InArabic(fprofile.transferLimit.ToString());
        //                                //fprofile.transferLimit_str = utils.ConvertSize_Get_InArabic(l.transferLimit.ToString());
        //                            else
        //                                fprofile.transferLimit_str = utils.ConvertSize_Get_En(fprofile.transferLimit.ToString());
        //                                //fprofile.transferLimit_str = utils.ConvertSize_Get_En(l.transferLimit.ToString());


        //                            fprofile.groupName = l.groupName;
        //                            fprofile.download_tx = l.download_tx;
        //                            fprofile.upload_rx = l.upload_rx;

        //                            if ((l.upload_rx != "0") && (l.download_tx != "0"))
        //                            {
        //                                if (UIAppearance.Language_ar)
        //                                    fprofile.Speed = utils.ConvertSize_Get_InArabic_short(l.upload_rx) + "/" + utils.ConvertSize_Get_InArabic_short(l.download_tx);
        //                                else
        //                                    fprofile.Speed = utils.ConvertSize_Get_En(l.upload_rx) + "/" + utils.ConvertSize_Get_En(l.download_tx);
        //                            }
        //                        }
        //                }
        //            finalProfile.Add(fprofile);
        //        }
        //    }
        //    catch { }
            
        //    Global_Variable.UM_Profile = finalProfile;
        //    return finalProfile;
        //}
   
    
    
    }
    public class UserManager_SourceProfile_UserManager
    {
        public int id { get; set; }
        public string idHX { get; set; }
        public string Name { get; set; }    
        public double Validity { get; set; }    
        public int Price { get; set; }    
        public string NameForUser { get; set; }    
        public string SharedUsers { get; set; }    
        public string rb { get; set; }    
        public int Delet_fromServer { get; set; }
        
        public void Syn_Profile_UserManager_to_LocalDB()
        {
            //Add_Profile_to_LocalDB(Global_Variable.Source_profile);
            ////List<UserManager_SourceProfile_UserManager> sourceProfile = Ge_Profile_FromDB(); 
            //Set_Profile_disable_LocalDB();
            ////Add_Profile_to_LocalDB(sourceProfile,false);
            //Add_Profile_to_LocalDB(Global_Variable.Source_profile, false);
        }
        public string Add_Profile_to_LocalDB(List<UserManager_SourceProfile_UserManager> UM_Profile, bool is_insert = true)
        {
            string rb=Global_Variable.Mk_resources.RB_SN;
            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    query = "INSERT OR IGNORE into UserManager_SourceProfile_UserManager (" +
                            "[idHX], " +
                            "[Name], " +
                            "[Validity], " +
                            "[Price], " +
                            "[NameForUser], " +
                            "[SharedUsers], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@Validity, " +
                            "@Price," +
                            "@NameForUser, " +
                            "@SharedUsers, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "update UserManager_SourceProfile_UserManager set " +
                     "[Validity]=@Validity , " +
                     "[Price]=@Price , " +
                     "[NameForUser]=@NameForUser , " +
                     "[SharedUsers]=@SharedUsers , " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                     " WHERE Name = @Name and rb='"+rb+"';";
                     //" WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.Execute(query, UM_Profile, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public  List<UserManager_SourceProfile_UserManager> Ge_Profile_FromDB()
        {
            List <UserManager_SourceProfile_UserManager> profiles= new List<UserManager_SourceProfile_UserManager>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<UserManager_SourceProfile_UserManager>("select * from UserManager_SourceProfile_UserManager where Delet_fromServer=0 and rb='" + rb+"' ; ", new DynamicParameters());
                    profiles= output.ToList();
                }
            }
            catch(Exception ex) { RJMessageBox.Show(ex.Message); }
            return profiles;
        }
        public  void Set_Profile_disable_LocalDB()
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE UserManager_SourceProfile_UserManager SET Delet_fromServer = 1 and rb='"+rb+"'  WHERE Delet_fromServer = 0;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }

    }
    public class UserManager_Source_limitation
    {
        public int id { get; set; }
        public string idHX { get; set; }
        public string Name { get; set; }
        public double downloadLimit { get; set; }
        public double uploadLimit { get; set; }
        public double transferLimit { get; set; }
        public double uptimeLimit { get; set; }
        public string rateLimit { get; set; }
        public string download_tx { get; set; }
        public string upload_rx { get; set; }
        public string groupName { get; set; }
        public double downloadPrice { get; set; }
        public double uploadPrice { get; set; }
        public double uptimePrice { get; set; }

        public string rb { get; set; }
        public int Delet_fromServer { get; set; }

        public void Syn_limitation_to_LocalDB()
        {
            //Add_limitation_to_LocalDB(Global_Variable.Source_limtition);
            ////List<UserManager_Source_limitation> sourcelimitation = Ge_limitation_FromDB();
            //Set_limitation_disable_LocalDB();
            ////Add_limitation_to_LocalDB(sourcelimitation, false);
            //Add_limitation_to_LocalDB(Global_Variable.Source_limtition, false);
        }
        public string Add_limitation_to_LocalDB(List<UserManager_Source_limitation> UM_limitation, bool is_insert = true)
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    //foreach (UserManager_Source_limitation p in UM_limitation)
                    //    p.rb = Global_Variable.Mk_resources.RB_SN;

                    query = "INSERT OR IGNORE into UserManager_Source_limitation (" +
                            "[idHX], " +
                            "[Name], " +
                            "[downloadLimit], " +
                            "[uploadLimit], " +
                            "[transferLimit], " +
                            "[uptimeLimit], " +
                            "[rateLimit], " +
                            "[download_tx], " +
                            "[upload_rx], " +
                            "[groupName], " +
                            "[downloadPrice], " +
                            "[uploadPrice], " +
                            "[uptimePrice], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@downloadLimit, " +
                            "@uploadLimit," +
                            "@transferLimit, " +
                            "@uptimeLimit, " +
                            "@rateLimit, " +
                            "@download_tx, " +
                            "@upload_rx, " +
                            "@groupName, " +
                            "@downloadPrice, " +
                            "@uploadPrice, " +
                            "@uptimePrice, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "update UserManager_Source_limitation set " +
                     "[downloadLimit]=@downloadLimit , " +
                     "[uploadLimit]=@uploadLimit , " +
                     "[transferLimit]=@transferLimit , " +
                     "[uptimeLimit]=@uptimeLimit , " +
                     "[rateLimit]=@rateLimit , " +
                     "[download_tx]=@download_tx , " +
                     "[upload_rx]=@upload_rx , " +
                     "[groupName]=@groupName , " +
                     "[downloadPrice]=@downloadPrice , " +
                     "[uploadPrice]=@uploadPrice , " +
                     "[uptimePrice]=@uptimePrice , " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                      " WHERE Name = @Name and rb='" + rb + "';";
                    //" WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.Execute(query, UM_limitation, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public  List<UserManager_Source_limitation> Ge_limitation_FromDB()
        {
            List<UserManager_Source_limitation> limitation = new List<UserManager_Source_limitation>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<UserManager_Source_limitation>("select * from UserManager_Source_limitation where Delet_fromServer=0 and rb='" + rb + "' ; ", new DynamicParameters());
                    limitation = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return limitation;
        }
        public  void Set_limitation_disable_LocalDB()
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE UserManager_Source_limitation SET Delet_fromServer = 1 WHERE Delet_fromServer = 0 and rb='" + rb + "' ;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }


    }
    public class UserManager_Source_Profile_Limtition
    {
        public int id { get; set; }
        public string idHX { get; set; }
        public string profile { get; set; }
        public string limitation { get; set; }
        public string from_time { get; set; }
        public string till_time { get; set; }
        public string weekdays { get; set; }
        public string rb { get; set; }
        public int Delet_fromServer { get; set; }

        public void Syn_ProfileLimtition_to_LocalDB()
        {
            //Add_ProfileLimtition_to_LocalDB(Global_Variable.Source_profile_limtition);
            ////List<UserManager_Source_Profile_Limtition> sourcelimitation = Ge_ProfileLimtition_FromDB();
            //Set_SourceProfileLimtition_disable_LocalDB();
            //Add_ProfileLimtition_to_LocalDB(Global_Variable.Source_profile_limtition, false);
            ////Add_ProfileLimtition_to_LocalDB(sourcelimitation, false);
        }
        public string Add_ProfileLimtition_to_LocalDB(List<UserManager_Source_Profile_Limtition> UM_ProfileLimtition, bool is_insert = true)
        {
            string rb = Global_Variable.Mk_resources.RB_SN;
            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    //foreach (UserManager_Source_Profile_Limtition p in UM_ProfileLimtition)
                    //    p.rb = Global_Variable.Mk_resources.RB_SN;

                    query = "INSERT OR IGNORE into UserManager_Source_Profile_Limtition (" +
                            "[idHX], " +
                            "[profile], " +
                            "[limitation], " +
                            "[from_time], " +
                            "[till_time], " +
                            "[weekdays], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@profile, " +
                            "@limitation, " +
                            "@from_time," +
                            "@till_time, " +
                            "@weekdays, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }
                else
                {
                    query = "update UserManager_Source_Profile_Limtition set " +
                     "[from_time]=@from_time , " +
                     "[till_time]=@till_time , " +
                     "[weekdays]=@weekdays , " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                       " WHERE profile = @profile and limitation=@limitation and rb='" + rb + "';";
                    //" WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.Execute(query, UM_ProfileLimtition, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public  List<UserManager_Source_Profile_Limtition> Ge_ProfileLimtition_FromDB()
        {
            List<UserManager_Source_Profile_Limtition> ProfileLimtition = new List<UserManager_Source_Profile_Limtition>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<UserManager_Source_Profile_Limtition>("select * from UserManager_Source_Profile_Limtition where Delet_fromServer=0 and rb='" + rb + "' ; ", new DynamicParameters());
                    ProfileLimtition = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return ProfileLimtition;
        }
        public  void Set_SourceProfileLimtition_disable_LocalDB()
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE UserManager_Source_Profile_Limtition SET Delet_fromServer = 1 WHERE Delet_fromServer = 0 and rb='" + rb + "' ;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
    }
    public class UserManager_Customer
    {
        public int id { get; set; }
        public string Name { get; set; }
        public string idHX { get; set; }
        public string rb { get; set; }
        public int Delet_fromServer { get; set; }


        public void Syn_Customer_to_LocalDB()
        {
            if (Global_Variable.Mk_resources.version > 6)
                return;
            if (Global_Variable.UM_Customer == null)
                return;

            Add_Customer_to_LocalDB(Global_Variable.UM_Customer);
            //List<UserManager_Customer> sourceCustomer = Ge_Customer_FromDB();
            Set_Customer_disable_LocalDB();
            Add_Customer_to_LocalDB(Global_Variable.UM_Customer, false);
        }
        public string Add_Customer_to_LocalDB(List<UserManager_Customer> UM_Customer, bool is_insert = true)
        {
            string status = "true";
            string rb = Global_Variable.Mk_resources.RB_SN;
            try
            {
                string query = "";
                if (is_insert)
                {
                    //foreach (CustomerUserManager p in UM_Customer)
                    //    p.rb = Global_Variable.Mk_resources.RB_SN;

                    query = "INSERT OR IGNORE into UserManager_Customer (" +
                            "[idHX], " +
                            "[Name], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "UPDATE UserManager_Customer SET Delet_fromServer = @Delet_fromServer  WHERE Name=@Name and rb='"+rb+"' ;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.Execute(query, UM_Customer, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public  List<UserManager_Customer> Ge_Customer_FromDB()
        {
            List<UserManager_Customer> Customer = new List<UserManager_Customer>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<UserManager_Customer>("select * from UserManager_Customer where Delet_fromServer=0 and rb='" + rb + "' ; ", new DynamicParameters());
                    Customer = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return Customer;
        }
        public  void Set_Customer_disable_LocalDB()
        {
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE UserManager_Customer SET Delet_fromServer = 1 WHERE Delet_fromServer = 0 and rb='"+rb+"' ;";
                var i= cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }

    }

}
