﻿using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevComponents.AdvTree;
using System.Threading;
using SmartCreator.ViewModels;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_UM_Sales : RJChildForm
    {
        Smart_DataAccess Smart_DA=null;
        Sql_DataAccess Local_DA=null;
        string Server_Type = "UM";
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";
        bool FirstLoad = true;
        public Form_UM_Sales(string _Server_Type="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            Server_Type = _Server_Type;

            
            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();

            if(Server_Type=="UM")
            this.Text = "مبيعات اليوزمنجر ";
            else
                this.Text = "مبيعات الهوتسبوت ";
            Spanel.Width = 0;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel1.Customizable = false;
                rjPanel12.Customizable = false;
                 
                pnl_side_sn.Customizable = false;

            }

            if (!UIAppearance.Language_ar)
            {
                if (Server_Type == "UM")
                    this.Text = "Sales UserManager ";
                else
                    this.Text = "Sales HotsPot";

                this.dgv.RightToLeft = RightToLeft.No;
            }
            
            set_font();
            
            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");
          
            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
            //Spanel.Visible = !Spanel.Visible;
        }
        private void set_font()
        {
            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            dgv.DefaultCellStyle.Font = new System.Drawing.Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);

            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            CardsArtchive cardsArtchive = new CardsArtchive();
            dgv.DataSource = cardsArtchive;

            //rjDateTime_From.Font=rjDateTime_To.Font=
                rjLabel3.Font= rjCheckBox2.Font= rjLabel6.Font =  rjLabel2.Font  =check_with_Commi.Font=
                rjLabel9.Font= rjLabel4.Font= rjLabel15.Font= rjLabel16.Font= rjLabel14.Font = rjLabel17.Font =
                ToggleButton_Detail.Font =ToggleButton_Monthly.Font = jToggleButton_Year.Font=
                Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel19.Font= rjLabel5.Font= rjLabel2.Font= rjLabel18.Font=lbl_avg.Font= Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Regular);
            rjLabel25Title.Font=btn_apply.Font = btn_Show.Font= btn_Filter.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

            lbl_note.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
            lbl_note.ForeColor = Color.Red;
            if (UIAppearance.Theme == UITheme.Dark)
                lbl_note.ForeColor = utils.Dgv_DarkColor;



            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);
            utils.tollstrip_textSize(View_Hide_toolStripMenuItem);


            return;
            Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new System.Drawing.Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if(Global_Variable.Mk_resources.version>=7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }
 
        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft =  RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;
               


            }
            catch { }
        }
        
        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width>50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = utils.Control_Mesur_DPI(260);
            }
        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }
        private void loadDgvState()
        {
            //return;
            if (ToggleButton_Detail.Checked)
            {
                try
                {
                    SourceSaveStateFormsVariable DgvState = null;

                    if (Server_Type == "UM")
                        DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_UM_Sales2");
                    else
                        DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_HS_Sales2");

                    if (DgvState == null)
                    {
                        init_first_load_null();
                        //Init_dgv_to_Default();
                        SaveFromState();
                        return;
                    }
                    Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
                    if (Dgv_State_list == null)
                    {
                        init_first_load_null();
                        //Init_dgv_to_Default();
                        SaveFromState();
                        return;
                    }
                    dvalue = Dgv_State_list.items;
                    foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
                    {
                        try
                        {
                            dgv.Columns[dv.Index].Visible = dv.Visable;
                            dgv.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;//toolStripSeparator5
                            dgv.Columns[dv.Index].Width = dv.Width;
                            foreach (var control in View_Hide_toolStripMenuItem.DropDownItems)
                            //foreach (ToolStripMenuItem control in View_Hide_toolStripMenuItem.DropDownItems)
                            {
                                //if (control.HasDropDownItems)
                                if (control.GetType() == typeof(ToolStripMenuItem))
                                {
                                    ToolStripMenuItem control1 = (ToolStripMenuItem)control;
                                    if (control1.Tag != null)
                                        if (control1.Tag.ToString().ToLower() == dv.Name.ToLower())
                                        {
                                            control1.Checked = dv.Visable;
                                        }
                                }
                            }
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    }


                }
                catch { }


            }

            else
                Init_dgv_to_Default();
        }
        Dgv_Header_Proprties Dgv_State_list = null;
        Dictionary<int, Dgv_Header_Values> dvalue;
       
        private void Init_dgv_to_Default()
        {
            try
            {
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{
                //    column.Visible = false;
                //}
                //try
                //{
                //    foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                //    { m.Checked = false; }
                //}
                //catch { }

                if (ToggleButton_Detail.Checked)
                {
                    //try
                    //{
                    //    dgv.Columns["UserName"].Visible = true;
                    //    //dgv.Columns["Str_TotalPrice"].Visible = true;
                    //    //dgv.Columns["TotalPrice"].Visible = true;
                    //    dgv.Columns["FirsLogin"].Visible = true;
                    //    UserName_ToolStripMenuItem.Checked = true;
                    //    Price_ToolStripMenuItem.Checked = true;
                    //    dt_FirstUse_ToolStripMenuItem.Checked = true;

                    //    if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked)
                    //    {
                    //        if (CheckBox_SN.Check)
                    //            dgv.Columns["Sn"].Visible = true;
                            
                    //        if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                    //            dgv.Columns["ProfileName"].Visible = true;
                            
                    //        if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    //            dgv.Columns["SpName"].Visible = true;
                            
                    //        if (/*CBox_Batch.SelectedIndex != 0 || CBox_Batch.SelectedIndex != -1 ||*/ CBox_Batch.Text != "")
                    //            dgv.Columns["BatchCardId"].Visible = true;
                            
                    //        if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    //            dgv.Columns["CustomerName"].Visible = true;
                            
                    //        if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    //            try { dgv.Columns["NasPortId"].Visible = true; } catch { }
                            
                    //        if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    //            dgv.Columns["Radius"].Visible = true;
                    //    }
                    //    dgv.Columns["Sn"].DisplayIndex = 0;
                    //    dgv.Columns["UserName"].DisplayIndex = 1;
                    //    dgv.Columns["TotalPrice"].DisplayIndex = 2;
                    //    dgv.Columns["Price"].DisplayIndex = 3;
                    //    dgv.Columns["FirsLogin"].DisplayIndex = 4;
                    //    dgv.Columns["ProfileName"].DisplayIndex = 5;
                    //    //dgv.Columns["Str_Status"].Visible = true;
                    //    //dgv.Columns["Str_UptimeUsed"].Visible = true;
                    //    //dgv.Columns["Str_Up_Down"].Visible = true;


                    //}
                    //catch (Exception ex) { MessageBox.Show(ex.Message); }
               
                }
                
                else
                {
                    try
                    {
                        foreach (DataGridViewColumn column in dgv.Columns)
                        {
                            column.Visible = false;
                        }
                        try
                        {
                            foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                            { m.Checked = false; }
                        }
                        catch { }

                        dgv.Columns["Str_TotalPrice"].Visible = true;
                        //dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["date"].Visible = true;
                        dgv.Columns["Str_UptimeUsed"].Visible = true;
                        dgv.Columns["Str_Up_Down"].Visible = true;
                        dgv.Columns["count"].Visible = true;

                        DateToolStripMenuItem.Checked = true;
                        Price_ToolStripMenuItem.Checked = true;
                        Str_UptimeUsed_ToolStripMenuItem.Checked = true;
                        Str_Up_Down_ToolStripMenuItem.Checked = true;
                        count_ToolStripMenuItem.Checked = true;

                        dgv.Columns["date"].DisplayIndex = 0;
                        dgv.Columns["TotalPrice"].DisplayIndex = 1;
                        //dgv.Columns["MoneyTotal"].DisplayIndex = 2;
                        dgv.Columns["Str_UptimeUsed"].DisplayIndex = 3;
                        dgv.Columns["Str_Up_Down"].DisplayIndex = 4;
                        dgv.Columns["count"].DisplayIndex = 5;
                    }
                    catch { }
                }
            }
            catch { }

        }

        private void init_first_load_null()
        {
            if (ToggleButton_Detail.Checked)
            {
                try
                {
                    foreach (DataGridViewColumn column in dgv.Columns)
                    {
                        column.Visible = false;
                    }
                    try
                    {
                        foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                        { m.Checked = false; }
                    }
                    catch { }

                    dgv.Columns["UserName"].Visible = true;
                    dgv.Columns["ProfileName"].Visible = true;
                    //dgv.Columns["Str_TotalPrice"].Visible = true;
                    //dgv.Columns["Str_Price"].Visible = true;
                    dgv.Columns["FirsLogin"].Visible = true;
                    dgv.Columns["Str_Price"].Visible = true;

                    UserName_ToolStripMenuItem.Checked = true;
                    Price_ToolStripMenuItem.Checked = true;
                    dt_FirstUse_ToolStripMenuItem.Checked = true;

                    if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked)
                    {
                        if (CheckBox_SN.Check)
                            dgv.Columns["Sn"].Visible = true;

                        if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                            dgv.Columns["ProfileName"].Visible = true;

                        if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                            dgv.Columns["SpName"].Visible = true;

                        if (/*CBox_Batch.SelectedIndex != 0 || CBox_Batch.SelectedIndex != -1 ||*/ CBox_Batch.Text != "")
                            dgv.Columns["BatchCardId"].Visible = true;

                        if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                            dgv.Columns["CustomerName"].Visible = true;

                        if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                            try { dgv.Columns["NasPortId"].Visible = true; } catch { }

                        if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                            dgv.Columns["Radius"].Visible = true;
                    }
                    dgv.Columns["Sn"].DisplayIndex = 0;
                    dgv.Columns["UserName"].DisplayIndex = 1;
                    //dgv.Columns["UserName"].Width = 120;
                    dgv.Columns["ProfileName"].DisplayIndex = 2;
                    //dgv.Columns["ProfileName"].Width = 120;

                    dgv.Columns["TotalPrice"].DisplayIndex = 3;
                    //dgv.Columns["TotalPrice"].Width = 120;
                    dgv.Columns["Price"].DisplayIndex = 4;
                    dgv.Columns["FirsLogin"].DisplayIndex = 5;
                    //dgv.Columns["FirsLogin"].Width = 120;
                    //dgv.Columns["Str_Status"].Visible = true;
                    //dgv.Columns["Str_UptimeUsed"].Visible = true;
                    //dgv.Columns["Str_Up_Down"].Visible = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
            }

        }
        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            if (ToggleButton_Detail.Checked)
            {
                FirstLoad = true;
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;

                if(rjCheckBox2.Checked==false)
                    pnl_size_time_count.Visible = false;
                FirstLoad = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Detail.Checked = true;
                    FirstLoad = false;
                }
            }
            try
            {
                lbl_avg.Visible = false;
                txt_avg.Visible = false;

                rjDateTime_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
                rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";

                string today = DateTime.Now.ToString("MM-dd-yyyy");
                rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
            }
            catch { }
            get_report();
        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {
                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                pnl_size_time_count.Visible = true;
                FirstLoad = false;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Monthly.Checked = true;
                    FirstLoad = false;
                }
            }
            try
            {
                lbl_avg.Visible = true;
                txt_avg.Visible = true;
                lbl_avg.Text = "المتوسط اليومي";

                rjDateTime_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                rjDateTime_From.CustomFormat = "MM/yyyy";
                //rjDateTime_To.CustomFormat = "MM/yyyy";
                DateTime firstDayOfMonth;
                DateTime lastDayOfMonth;
                utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                string first = firstDayOfMonth.ToString("MM-dd-yyyy");
                rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");


                string last = lastDayOfMonth.AddDays(-1).ToString("MM-dd-yyyy");
                rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
            }
            catch { }
            get_report();
        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            CheckBox_To_Date.Check = true;
            if (jToggleButton_Year.Checked)
            {
                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                pnl_size_time_count.Visible = true;
                FirstLoad = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                {
                    FirstLoad = true;
                    jToggleButton_Year.Checked = true;
                    FirstLoad = false;
                }
            }
            lbl_avg.Visible = true;
            txt_avg.Visible = true;

            lbl_avg.Text = "المتوسط الشهري";

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "yyyy";
            //rjDateTime_To.CustomFormat = "yyyy";

            //rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
            //rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
            rjDateTime_From.Value = new DateTime(DateTime.Now.Year, 1, 1);
            rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31);
            //rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31,23,59,59);

            ////rjDateTime_From.Value = new DateTime(1,DateTime.Now.Year,1,0,0,0);
            //rjDateTime_To.Value = new DateTime(12,DateTime.Now.Year,31,23,59,59);

            get_report();
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }
        private void get_report()
        {
            try
            {
                dgv.DataSource = null;
                double sum = 0; double sum_download = 0; double sum_uptime = 0; double count_cards = 0;
                txt_avg.Text = "0"; txt_count_Cards.Text = "0"; txt_download.Text = "0"; txt_sum_Sales.Text = "0"; txt_uptime.Text = "00:00:00";
                string ColumPrice = "Price";
               

                string Query_firstUse = condition_detail_firstUse();
                if (ToggleButton_Detail.Checked)
                {


                    if (Server_Type == "HS")
                    {
                        string Qury = "SELECT u.*,p.Price as Price ,p.TotalPrice as TotalPrice  FROM HSUser u LEFT JOIN HsPyment p ON u.Sn_Name = p.Fk_Sn_Name " + Query_firstUse;
                        List<HSUser> Reports = Local_DA.LoadAsync<HSUser>(Qury);
                        //List<HSUser> Reports = Local_DA.Load<HSUser>(Qury);
                        dgv.DataSource = Reports;
                    }
                    else
                    {
                        string Qury = "SELECT u.*,p.Price as Price ,p.TotalPrice as TotalPrice  FROM UmUser u LEFT JOIN UmPyment p ON u.Sn_Name = p.Fk_Sn_Name " + Query_firstUse;
                        List<UmUser> Reports = Local_DA.LoadAsync<UmUser>(Qury);
                        //List<UmUser> Reports = Local_DA.Load<UmUser>(Qury);
                        dgv.DataSource = Reports;

                    }



                   
                    dgv.Columns["Price"].Visible = true;
                    if (check_with_Commi.Checked)
                    {
                        ColumPrice = "TotalPrice";
                        dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["Price"].Visible = false;
                    }

                    foreach (DataGridViewRow row in dgv.Rows)
                    {
                        if (check_with_Commi.Checked)
                        {
                            sum += Convert.ToDouble(row.Cells["TotalPrice"].Value);
                            //row.Cells["Str_TotalPrice"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["TotalPrice"].Value));
                        }
                        else
                        {
                            sum += Convert.ToDouble(row.Cells["Price"].Value);
                        }

                        sum_download += Convert.ToDouble(row.Cells["DownloadUsed"].Value)+ Convert.ToDouble(row.Cells["UploadUsed"].Value);
                        sum_uptime += Convert.ToDouble(row.Cells["UptimeUsed"].Value);
                    }
                    txt_sum_Sales.Text = String.Format("{0:n0}", sum);
                    txt_count_Cards.Text = dgv.Rows.Count.ToString();

                    if (Server_Type == "HS")
                    {
                        txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                        txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));
                    }
                    if (Server_Type == "UM")
                    {
                        sum_download = 0;
                        sum_uptime = 0;
                        if (rjCheckBox2.Checked)
                        {
                            string Query_conditon_Session = condition_Session_By_Days_for_firstUse();
                            string Qury_str = "SELECT " +
                                   "sum(BytesDownload + BytesUpload) as DownloadUsed ," +
                                   "sum(UpTime) as UptimeUsed " +
                                   "FROM UmSession s " +
                                   " INNER JOIN UmUser u ON u.Sn_Name  = s.Fk_Sn_Name  " +
                                   Query_conditon_Session + " " +
                                   " ;";
                                   //" group by s.Fk_Sn_Name;";

                            //List<UmUser> users = Local_DA.Load<UmUser>(Qury_str);

                            List<UmUser> users = Local_DA.LoadAsync<UmUser>(Qury_str);

                            foreach (UmUser user in users)
                            {
                                sum_download += user.DownloadUsed;
                                sum_uptime += user.UptimeUsed;
                            }

                            txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                            txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));
                        }
                    }



                    loadDgvState();
                }

                else
                {
                    string Query_conditon_Session = condition_Session_By_Days_for_firstUse();
                    string fitler = "'%Y-%m-%d'";
                    if (jToggleButton_Year.Checked)
                        fitler = "'%Y-%m'";

                    List<class_Report_monthly_or_Dayliy> um = new List<class_Report_monthly_or_Dayliy>();
                    List<class_Report_monthly_or_Dayliy> totalDownload = new List<class_Report_monthly_or_Dayliy>();
                    ColumPrice = "Price";
                    if (check_with_Commi.Checked)
                    {
                        ColumPrice = "TotalPrice";
                    }
                    try
                    {

                        //using (var db = dbFactory.Open())
                        //{
                        string Qury = "SELECT " +
                              "strftime(" + fitler + ", u.FirsLogin) Date," +
                              $"sum(c.{ColumPrice}) as TotalPrice ," +
                              "count(u.Sn_Name) as count " +
                              "FROM UmUser u LEFT JOIN UmPyment c ON u.Sn_Name = c.Fk_Sn_Name " +
                              Query_firstUse + " " +
                              " group by strftime(" + fitler + ", u.FirsLogin);";

                        if (Server_Type == "HS")
                        {
                            Qury = "SELECT " +
                              "strftime(" + fitler + ", u.FirsLogin) Date," +
                              $"sum(u.{ColumPrice}) as TotalPrice ," +
                              "count(u.Sn_Name) as count " +
                              "FROM HSUser u LEFT JOIN HsPyment c ON u.Sn_Name = c.Fk_Sn_Name " +
                              Query_firstUse + " " +
                              " group by strftime(" + fitler + ", u.FirsLogin);";
                        }
                        um = Local_DA.LoadAsync<class_Report_monthly_or_Dayliy>(Qury);
                        //um = Local_DA.Load<class_Report_monthly_or_Dayliy>(Qury);

                        string Qury_str = "";
                        if (Server_Type == "UM")
                        {
                            Qury_str = "SELECT " +
                               "strftime(" + fitler + ", s.FromTime) date," +
                               "sum(BytesDownload + BytesUpload) as Download ," +
                               "sum(UpTime) as Uptime " +
                               "FROM UmSession s " +
                               " INNER JOIN UmUser u ON u.Sn_Name  = s.Fk_Sn_Name  " +
                               Query_conditon_Session + " " +
                               " group by strftime(" + fitler + ",   s.FromTime);";

                            totalDownload = Local_DA.LoadAsync<class_Report_monthly_or_Dayliy>(Qury_str);
                            //totalDownload = Local_DA.Load<class_Report_monthly_or_Dayliy>(Qury_str);
                            var all = (from u in um
                                       join s in totalDownload on u.Date equals s.Date
                                       select new class_Report_monthly_or_Dayliy
                                       {
                                           Date = u.Date,
                                           TotalPrice = u.TotalPrice,
                                           count = u.count,
                                           Uptime = s.Uptime,
                                           Download = s.Download,
                                       }).ToList();
                            dgv.DataSource = all;

                        }
                        else if (Server_Type == "HS")
                        {
                            Qury_str = $"SELECT " +
                                       $"strftime(" + fitler + ", u.FirsLogin) Date," +
                                       $"sum(u.{ColumPrice}) as TotalPrice ," +
                                       "sum(u.DownloadUsed + u.UploadUsed) as Download ," +
                                       "sum(u.UptimeUsed) as Uptime , " +
                                       "count(u.Sn_Name) as count " +
                                       "FROM HSUser u  " +
                                       Query_firstUse + " " +
                                       " group by strftime(" + fitler + ", u.FirsLogin);";
                            totalDownload = Local_DA.LoadAsync<class_Report_monthly_or_Dayliy>(Qury_str);
                            //totalDownload = Local_DA.Load<class_Report_monthly_or_Dayliy>(Qury_str);
                            dgv.DataSource = totalDownload;

                        }

                        //totalDownload = Local_DA.Load<class_Report_monthly_or_Dayliy>(Qury_str);
                        //var all = (from u in um
                        //           join s in totalDownload on u.Date equals s.Date
                        //           select new class_Report_monthly_or_Dayliy
                        //           {
                        //               Date = u.Date,
                        //               TotalPrice = u.TotalPrice,
                        //               count = u.count,
                        //               Uptime = s.Uptime,
                        //               Download = s.Download,
                        //           }).ToList();
                        //dgv.DataSource = all;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    //dgv.DataSource = um;

                   
                    foreach (DataGridViewRow row in dgv.Rows)
                    {
                        sum += Convert.ToDouble(row.Cells["TotalPrice"].Value);
                        row.Cells["Str_TotalPrice"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["TotalPrice"].Value));

                        sum_download += Convert.ToDouble(row.Cells["Download"].Value);
                        sum_uptime += Convert.ToDouble(row.Cells["Uptime"].Value);
                        count_cards += Convert.ToDouble(row.Cells["count"].Value);
                    }
                    txt_sum_Sales.Text = String.Format("{0:n0}", sum);
                    txt_count_Cards.Text = count_cards.ToString();
                    double avg = sum / dgv.Rows.Count; 
                    txt_avg.Text = String.Format("{0:n0}", avg); 
                    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString()); 
                    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode((sum_uptime));

                    loadDgvState();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        private string condition_detail_firstUse()
        {

            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            //string str_to_Date = (rjDateTime_From.Value.AddDays(1)).ToString("yyyy-MM-dd hh:mm:ss", CultureInfo.InvariantCulture);

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            //string str_from_Date = (rjDateTime_From.Value).ToString("MM-dd-yyyy", CultureInfo.InvariantCulture);
            //DateTime dt_to = Convert.ToDateTime(str_from_Date + " " + "23:59:59").ToUniversalTime();
            //Int32 From_DT = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(rjDateTime_From.Value).ToUniversalTime());
            //Int32 To_DT = utils.DateTimeToUnixTimeStamp(dt_to);

            //if (CheckBox_To_Date.Checked)
            //    To_DT = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(rjDateTime_To.Value).ToUniversalTime());
            //if (ToggleButton_Monthly.Checked)
            //{
            //    DateTime firstDayOfMonth;
            //    DateTime lastDayOfMonth;
            //    utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    From_DT = utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}

            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin <='" + str_to_Date + "'  ";
            //conditon_date = " WHERE u.firstUse >=" + From_DT + " AND u.firstUse<=" + To_DT + "  ";
            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                        radius = " AND u.Radius='" + CBox_Radius.Text.ToString() + "'  ";

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                        customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }

                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }
        private string condition_Session_By_Days_for_firstUse()
        {

            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            //string str_to_Date = (rjDateTime_From.Value.AddDays(1)).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture);
            //string str_to_Date = Convert.ToDateTime(rjDateTime_From.Value.Date + " " + "23:59:59").ToString("yyyy-MM-dd hh:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            
            conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";
            //conditon_date = $" WHERE s.FromTime BETWEEN  '{str_from_Date}' AND '{str_to_Date}'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                        radius = " AND u.Radius='" + CBox_Radius.Text.ToString() + "'  ";

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                        customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }

        private void Form_UM_Sales_Load(object sender, EventArgs e)
        {
          timer1.Start();
            //Get_Batch_cards();
        }

        private void Form_UM_Sales_SizeChanged(object sender, EventArgs e)
        {
            panel1_side.Refresh();
            rjPanel_back_side.Refresh();
            rjPanel12.Refresh();
            rjPanel_topFilter.Refresh();
            dgv.Refresh();
            txt_search.Refresh();

            lbl_avg.Location = new Point(pnlClientArea.Width / 2 - (lbl_avg.Width / 2), lbl_avg.Location.Y);
            txt_avg.Location = new Point(pnlClientArea.Width / 2 - (txt_avg.Width / 2), txt_avg.Location.Y);
        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv.Columns[columnName].Visible = elemnt.Checked;
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
            }
            catch { }
        }
        private void UserName_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ToggleButton_Detail.Checked)
            {
                ToolStripMenuItem elm = sender as ToolStripMenuItem;
                Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());

                //SaveFromState();
            }
        }

        private void CardsDetailsToolStripMenuItem_Click(object sender, EventArgs e)
        {

            if (dgv.SelectedRows.Count > 0)
                get_card_detail();
        }
        private void get_card_detail()
        {
            foreach (DataGridViewRow dr in dgv.SelectedRows)
            {
                if (Server_Type == "UM")
                {
                    UmUser user = dr.DataBoundItem as UmUser;
                    Form_CardsDetails form_CardsDetails = new Form_CardsDetails(user, "From_RB_Archive");
                    form_CardsDetails.ShowDialog();
                }
                else if(Server_Type =="HS")
                {
                    HSUser user = dr.DataBoundItem as HSUser;
                    Form_CardsDetailsHS form_CardsDetails = new Form_CardsDetailsHS(user, "From_RB_Archive");
                    form_CardsDetails.ShowDialog();
                }
                return;
            }
        }

        DataGridViewCell ActiveCell = null;
        private void نسخCtrlcToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }

        }

        private void تصديرالىملفاكسلToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void تصديرالىملفنصيToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();

            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();


            get_report();

            loadDgvState();

            FirstLoad = false;
        }

  

        public void SaveFromState()
        {

            try
            {
                if (ToggleButton_Detail.Checked == false)
                    return;

                Dgv_State_list = new Dgv_Header_Proprties();
                dvalue = new Dictionary<int, Dgv_Header_Values>();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    Dgv_Header_Values dgv_Header_Values = new Dgv_Header_Values();
                    dgv_Header_Values.Visable = column.Visible;
                    dgv_Header_Values.HeaderText = column.HeaderText;
                    dgv_Header_Values.Name = column.Name;
                    dgv_Header_Values.DisplayIndex = column.DisplayIndex;
                    dgv_Header_Values.Index = column.Index;
                    dgv_Header_Values.Width = column.Width;

                    dvalue[column.Index] = dgv_Header_Values;
                }
                Dgv_State_list.items = dvalue;

                string formSetting = JsonConvert.SerializeObject(Dgv_State_list);

                if (Server_Type == "UM")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_HS_Sales2", "SaveControlState", formSetting);
                 else
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_HS_Sales2", "SaveControlState", formSetting);



                //string formSetting = JsonConvert.SerializeObject(Dgv_State_list);

                //if (Server_Type == "UM")
                //   Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_UM_Sales");
                //else
                //  Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_HS_Sales");

            }
            catch { }
        }

        private void txt_sum_Sales_onTextChanged(object sender, EventArgs e)
        {
            txt_sum_Sales.Text = txt_sum_Sales.Text;
            return;
        }

        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void rjCheckBox2_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            if (rjCheckBox2.Checked)
            {
                pnl_size_time_count.Visible = true;
                get_report();
            }
            else
                pnl_size_time_count.Visible = false;

        }

        private void check_with_Commi_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            get_report();
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if(ToggleButton_Detail.Checked)
            if (e.RowIndex > -1)
                get_card_detail();
        }

        private void rjDateTime_To_OnValueChanged(object sender, EventArgs e)
        {

        }

        private void btnDisable_Click(object sender, EventArgs e)
        {
            Spanel.Width = 0;
        }

        private void iconMenuItem1_Click(object sender, EventArgs e)
        {
            if (ToggleButton_Detail.Checked)
            {
                SaveFromState();
                RJMessageBox.Show("تم الحفظ");
            }
        }

        private void btn_print_Click(object sender, EventArgs e)
        {
            btnPdf_Click2();
        }
        private void btnPdf_Click2()
        {
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            if (ToggleButton_Detail.Checked)
                dateHeader = "تقرير المبيعات - تفصيلي - من تاريخ  : " + start + "  الى  " + end;
            if (ToggleButton_Monthly.Checked)
                dateHeader = "تقرير يومي للمبيعات لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
            if (jToggleButton_Year.Checked)
                dateHeader = "تقرير شهري للمبيعات لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName =  System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture)+ "- المبيعات"  + ".pdf";
                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;
                 
               if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            
                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if(column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);
                            //======================================================================
                            if (ToggleButton_Detail.Checked)
                                table_out.AddCell(new Phrase("", arabicFont_fotter));
                            else
                                table_out.AddCell(new Phrase("المتوسط", arabicFont_fotter));

                            table_out.AddCell(new Phrase("الاجمالي", arabicFont_fotter));
                           
                            table_out.AddCell(new Phrase("اجمالي التحميل", arabicFont_fotter));

                            table_out.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                            table_out.AddCell(new Phrase("عدد الكروت", arabicFont_fotter));

                            //======================================================================
                            if (ToggleButton_Detail.Checked)
                                table_out.AddCell(new Phrase("", arabicFont));
                            else
                            table_out.AddCell(new Phrase(txt_avg.Text, arabicFont));
                            table_out.AddCell(new Phrase(txt_sum_Sales.Text, arabicFont));

                           
                             

                            table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            table_out.AddCell(new Phrase(txt_uptime.Text, arabicFont));

                            table_out.AddCell(new Phrase(txt_count_Cards.Text, arabicFont));

                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            //RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;

            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => Refersh_mikrotik());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }

           

        }

        [Obsolete]
        private void Refersh_mikrotik()
        {
            Global_Variable.StartThreadProcessFromMK=true;
            bool Syn_Users_FromFasrDB = false;
            bool Syn_Pyment_FromFasrDB = false;
            bool Syn_Session_FromFasrDB = false;

            if (Global_Variable.load_by_DownloadDB && Global_Variable.Mk_resources.version <= 6)
            {
                int count_process = 7;
                Fast_Load_From_Mikrotik fast = new Fast_Load_From_Mikrotik();
                Global_Variable.Update_Um_StatusBar_Prograss("تنزيل قاعدة بيانات اليوزمنجر  -  لتستفيد من الميزه افتح السرعه للكمبيوتر", Convert.ToInt32(1 * (100.0 / count_process)));
                if (fast.Download_Sql_From_Mikrotik())
                {

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر", Convert.ToInt32(2 * (100.0 / count_process)));
                    //Thread.Sleep(1000);
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه الكروت يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                    Syn_Users_FromFasrDB = fast.Syn_UmUser_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه المبيعات والحسابات يوزمنجر", Convert.ToInt32(4 * (100.0 / count_process)));
                    Syn_Pyment_FromFasrDB = fast.Syn_Pyments_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه  الجلسات يوزمنجر", Convert.ToInt32(5 * (100.0 / count_process)));
                    Syn_Session_FromFasrDB = fast.Syn_Session_From_FastDB();

                    //---======================================================================================================

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب ومزامنه بيانات اليوزمنجر من الروتر", Convert.ToInt32(0 * (100.0 / count_process)));

                    fast.Create_Indexs();
                }
                try
                {
                    string Downloadfile = utils.Get_Database_Directory() + "\\" + "dbs\\temp.db";
                    string Downloadfile2 = utils.Get_Database_Directory() + "\\" + "dbs\\temp2.db";
                    string Downloadfile3 = Directory.GetCurrentDirectory() + "\\sql.bat";
                    if (File.Exists(Downloadfile))
                        File.Delete(Downloadfile);
                    if (File.Exists(Downloadfile2))
                        File.Delete(Downloadfile2);
                    if (File.Exists(Downloadfile3))
                        File.Delete(Downloadfile3);
                }
                catch { }

            }

            if (Syn_Session_FromFasrDB == false)
                //if (  ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false))   )
                {
                    int count_process = 4;
                    UserManagerProcess u = new UserManagerProcess();
                    //if (Global_Variable.Ddiable_LoadSession == false)
                    //{
                        Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(1 * (100.0 / count_process)));

                        Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                        //RJMessageBox.Show("يتم finsh get session");

                        Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(2 * (100.0 / count_process)));

                        Global_Variable.Update_StatusBar_StartSyn();


                        Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(3 * (100.0 / count_process)));

                        if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                            u.Syn_Session_to_LocalDB();
                        Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(0 * (100.0 / count_process)));

                        Global_Variable.Update_StatusBar_StopSyn();
                    //}
                }


            Global_Variable.StartThreadProcessFromMK=false;
        }

        private void CheckBox_To_Date_CheckedChanged(object sender, EventArgs e)
        {

        }
    }
}
