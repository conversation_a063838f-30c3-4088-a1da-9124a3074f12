﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    /// <summary>
    /// الكلاس الأساسي لجميع الكيانات في النظام
    /// </summary>
    public abstract class BaseEntity
    {
        [Key]
        public int Id { get; set; }

        public DateTime CreatedDate { get; set; } = DateTime.Now;

        public DateTime? UpdatedDate { get; set; }

        public string CreatedBy { get; set; } = Environment.UserName;

        public string? UpdatedBy { get; set; }

        public bool IsDeleted { get; set; } = false;

        public DateTime? DeletedDate { get; set; }

        public string? DeletedBy { get; set; }

        /// <summary>
        /// تحديث معلومات التعديل
        /// </summary>
        public virtual void UpdateModificationInfo()
        {
            UpdatedDate = DateTime.Now;
            UpdatedBy = Environment.UserName;
        }

        /// <summary>
        /// تحديث معلومات الحذف الناعم
        /// </summary>
        public virtual void MarkAsDeleted()
        {
            IsDeleted = true;
            DeletedDate = DateTime.Now;
            DeletedBy = Environment.UserName;
        }

        /// <summary>
        /// استرجاع من الحذف الناعم
        /// </summary>
        public virtual void RestoreFromDeleted()
        {
            IsDeleted = false;
            DeletedDate = null;
            DeletedBy = null;
            UpdateModificationInfo();
        }
    }
}
