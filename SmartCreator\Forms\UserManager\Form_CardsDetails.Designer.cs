﻿namespace SmartCreator.Forms.UserManager
{
    partial class Form_CardsDetails
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.txt_TransferLeft = new SmartCreator.RJControls.RJTextBox();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.txt_TimeLeft = new SmartCreator.RJControls.RJTextBox();
            this.Toggle_Bind_Mac = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.txt_mac = new SmartCreator.RJControls.RJTextBox();
            this.txt_TransferLimit = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.txt_uptimeLimit = new SmartCreator.RJControls.RJTextBox();
            this.Toggle_Status = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.lbl_limituptime = new SmartCreator.RJControls.RJLabel();
            this.txt_Till_Date = new SmartCreator.RJControls.RJTextBox();
            this.txt_RegDate = new SmartCreator.RJControls.RJTextBox();
            this.txt_Password = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.txt_username = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.lbl_printDate = new SmartCreator.RJControls.RJLabel();
            this.lbl_username = new SmartCreator.RJControls.RJLabel();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.dgv_Sessions = new SmartCreator.RJControls.RJDataGridView();
            this.dm_Session = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.ترتيبالاعمدةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبتاريخبدايةالجلسةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبتاريخنهايةالجلسةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالوقتالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالتحميلالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبالرفعالمستخدمToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حسبايبيالجلسهIPToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالماكToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالبورتالجهازToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حذفجميعجلساتالكرتToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تحديثالجلساتمنالروترToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخالسطركاملToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.dgv_profiles = new SmartCreator.RJControls.RJDataGridView();
            this.dm_profile = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.اضافةباقةجديدةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.RemoveProfile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.btnAddProfile = new SmartCreator.RJControls.RJButton();
            this.txt_TotalPrice = new SmartCreator.RJControls.RJTextBox();
            this.txt_CountProfile = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.txt_TotalDownload = new SmartCreator.RJControls.RJTextBox();
            this.txt_TotalUptime = new SmartCreator.RJControls.RJTextBox();
            this.txt_CountSession = new SmartCreator.RJControls.RJTextBox();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.Radio_WithSession = new SmartCreator.RJControls.RJRadioButton();
            this.Radio_Baisc = new SmartCreator.RJControls.RJRadioButton();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Sessions)).BeginInit();
            this.dm_Session.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_profiles)).BeginInit();
            this.dm_profile.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.lbl_note);
            this.pnlClientArea.Controls.Add(this.Radio_Baisc);
            this.pnlClientArea.Controls.Add(this.Radio_WithSession);
            this.pnlClientArea.Controls.Add(this.txt_CountSession);
            this.pnlClientArea.Controls.Add(this.btn_Refresh);
            this.pnlClientArea.Controls.Add(this.txt_TotalUptime);
            this.pnlClientArea.Controls.Add(this.txt_TotalDownload);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.rjLabel7);
            this.pnlClientArea.Controls.Add(this.rjLabel6);
            this.pnlClientArea.Controls.Add(this.rjLabel5);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(951, 529);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(130, 22);
            this.lblCaption.Text = "Form_CardsDetails";
            // 
            // rjPanel1
            // 
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 10;
            this.rjPanel1.Controls.Add(this.txt_TransferLeft);
            this.rjPanel1.Controls.Add(this.btnSave);
            this.rjPanel1.Controls.Add(this.txt_TimeLeft);
            this.rjPanel1.Controls.Add(this.Toggle_Bind_Mac);
            this.rjPanel1.Controls.Add(this.rjLabel4);
            this.rjPanel1.Controls.Add(this.txt_mac);
            this.rjPanel1.Controls.Add(this.txt_TransferLimit);
            this.rjPanel1.Controls.Add(this.rjLabel16);
            this.rjPanel1.Controls.Add(this.rjLabel15);
            this.rjPanel1.Controls.Add(this.rjLabel2);
            this.rjPanel1.Controls.Add(this.txt_uptimeLimit);
            this.rjPanel1.Controls.Add(this.Toggle_Status);
            this.rjPanel1.Controls.Add(this.rjLabel14);
            this.rjPanel1.Controls.Add(this.rjLabel13);
            this.rjPanel1.Controls.Add(this.lbl_limituptime);
            this.rjPanel1.Controls.Add(this.txt_Till_Date);
            this.rjPanel1.Controls.Add(this.txt_RegDate);
            this.rjPanel1.Controls.Add(this.txt_Password);
            this.rjPanel1.Controls.Add(this.rjLabel12);
            this.rjPanel1.Controls.Add(this.txt_username);
            this.rjPanel1.Controls.Add(this.rjLabel11);
            this.rjPanel1.Controls.Add(this.lbl_printDate);
            this.rjPanel1.Controls.Add(this.lbl_username);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(393, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(549, 236);
            this.rjPanel1.TabIndex = 0;
            // 
            // txt_TransferLeft
            // 
            this.txt_TransferLeft._Customizable = false;
            this.txt_TransferLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TransferLeft.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TransferLeft.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TransferLeft.BorderRadius = 5;
            this.txt_TransferLeft.BorderSize = 1;
            this.txt_TransferLeft.Enabled = false;
            this.txt_TransferLeft.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TransferLeft.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TransferLeft.Location = new System.Drawing.Point(3, 115);
            this.txt_TransferLeft.MultiLine = false;
            this.txt_TransferLeft.Name = "txt_TransferLeft";
            this.txt_TransferLeft.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TransferLeft.PasswordChar = false;
            this.txt_TransferLeft.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TransferLeft.PlaceHolderText = null;
            this.txt_TransferLeft.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TransferLeft.Size = new System.Drawing.Size(168, 29);
            this.txt_TransferLeft.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TransferLeft.TabIndex = 91;
            this.txt_TransferLeft.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btnSave
            // 
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 8;
            this.btnSave.BorderSize = 1;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnSave.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.btnSave.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 25;
            this.btnSave.Location = new System.Drawing.Point(14, 181);
            this.btnSave.Name = "btnSave";
            this.btnSave.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnSave.Size = new System.Drawing.Size(167, 40);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btnSave.TabIndex = 49;
            this.btnSave.Text = "حفظ التغيرات";
            this.btnSave.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnSave.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // txt_TimeLeft
            // 
            this.txt_TimeLeft._Customizable = false;
            this.txt_TimeLeft.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TimeLeft.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TimeLeft.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TimeLeft.BorderRadius = 5;
            this.txt_TimeLeft.BorderSize = 1;
            this.txt_TimeLeft.Enabled = false;
            this.txt_TimeLeft.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_TimeLeft.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TimeLeft.Location = new System.Drawing.Point(3, 82);
            this.txt_TimeLeft.MultiLine = false;
            this.txt_TimeLeft.Name = "txt_TimeLeft";
            this.txt_TimeLeft.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TimeLeft.PasswordChar = false;
            this.txt_TimeLeft.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TimeLeft.PlaceHolderText = null;
            this.txt_TimeLeft.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TimeLeft.Size = new System.Drawing.Size(168, 27);
            this.txt_TimeLeft.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TimeLeft.TabIndex = 91;
            this.txt_TimeLeft.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // Toggle_Bind_Mac
            // 
            this.Toggle_Bind_Mac.Activated = false;
            this.Toggle_Bind_Mac.CheckAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.Toggle_Bind_Mac.Customizable = false;
            this.Toggle_Bind_Mac.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_Bind_Mac.Location = new System.Drawing.Point(269, 160);
            this.Toggle_Bind_Mac.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.Toggle_Bind_Mac.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Bind_Mac.Name = "Toggle_Bind_Mac";
            this.Toggle_Bind_Mac.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Bind_Mac.OFF_Text = "ربط بالماك";
            this.Toggle_Bind_Mac.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Bind_Mac.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Bind_Mac.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Bind_Mac.ON_Text = "ربط بالماك";
            this.Toggle_Bind_Mac.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Bind_Mac.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Bind_Mac.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Toggle_Bind_Mac.Size = new System.Drawing.Size(163, 29);
            this.Toggle_Bind_Mac.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Bind_Mac.TabIndex = 89;
            this.Toggle_Bind_Mac.Tag = "تفصيلي";
            this.Toggle_Bind_Mac.Text = "#";
            this.Toggle_Bind_Mac.ThreeState = true;
            this.Toggle_Bind_Mac.UseVisualStyleBackColor = true;
            this.Toggle_Bind_Mac.CheckedChanged += new System.EventHandler(this.Toggle_Bind_Mac_CheckedChanged);
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(447, 164);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(82, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 90;
            this.rjLabel4.Text = "ربــــــــط بالمــاك";
            // 
            // txt_mac
            // 
            this.txt_mac._Customizable = false;
            this.txt_mac.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_mac.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_mac.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_mac.BorderRadius = 5;
            this.txt_mac.BorderSize = 1;
            this.txt_mac.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_mac.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_mac.Location = new System.Drawing.Point(3, 148);
            this.txt_mac.MultiLine = false;
            this.txt_mac.Name = "txt_mac";
            this.txt_mac.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_mac.PasswordChar = false;
            this.txt_mac.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_mac.PlaceHolderText = null;
            this.txt_mac.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_mac.Size = new System.Drawing.Size(168, 27);
            this.txt_mac.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_mac.TabIndex = 91;
            this.txt_mac.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.toolTip1.SetToolTip(this.txt_mac, "ماك الجهاز المرتبط بالكرت");
            // 
            // txt_TransferLimit
            // 
            this.txt_TransferLimit._Customizable = false;
            this.txt_TransferLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TransferLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TransferLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TransferLimit.BorderRadius = 5;
            this.txt_TransferLimit.BorderSize = 1;
            this.txt_TransferLimit.Enabled = false;
            this.txt_TransferLimit.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TransferLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TransferLimit.Location = new System.Drawing.Point(269, 116);
            this.txt_TransferLimit.MultiLine = false;
            this.txt_TransferLimit.Name = "txt_TransferLimit";
            this.txt_TransferLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TransferLimit.PasswordChar = false;
            this.txt_TransferLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TransferLimit.PlaceHolderText = null;
            this.txt_TransferLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TransferLimit.Size = new System.Drawing.Size(163, 29);
            this.txt_TransferLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TransferLimit.TabIndex = 91;
            this.txt_TransferLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(185, 153);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel16.Size = new System.Drawing.Size(76, 17);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 90;
            this.rjLabel16.Text = "الماك المرتبط";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(179, 122);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel15.Size = new System.Drawing.Size(82, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 90;
            this.rjLabel15.Text = "الحجم المتبقي";
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(444, 194);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(85, 23);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 47;
            this.rjLabel2.Text = "الحــــــــــــــــالة";
            // 
            // txt_uptimeLimit
            // 
            this.txt_uptimeLimit._Customizable = false;
            this.txt_uptimeLimit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptimeLimit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_uptimeLimit.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptimeLimit.BorderRadius = 5;
            this.txt_uptimeLimit.BorderSize = 1;
            this.txt_uptimeLimit.Enabled = false;
            this.txt_uptimeLimit.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_uptimeLimit.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptimeLimit.Location = new System.Drawing.Point(269, 83);
            this.txt_uptimeLimit.MultiLine = false;
            this.txt_uptimeLimit.Name = "txt_uptimeLimit";
            this.txt_uptimeLimit.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptimeLimit.PasswordChar = false;
            this.txt_uptimeLimit.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptimeLimit.PlaceHolderText = null;
            this.txt_uptimeLimit.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptimeLimit.Size = new System.Drawing.Size(163, 27);
            this.txt_uptimeLimit.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_uptimeLimit.TabIndex = 91;
            this.txt_uptimeLimit.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // Toggle_Status
            // 
            this.Toggle_Status.Activated = true;
            this.Toggle_Status.Checked = true;
            this.Toggle_Status.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Toggle_Status.Customizable = false;
            this.Toggle_Status.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_Status.Location = new System.Drawing.Point(269, 198);
            this.Toggle_Status.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_Status.Name = "Toggle_Status";
            this.Toggle_Status.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Status.OFF_Text = "";
            this.Toggle_Status.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_Status.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_Status.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Status.ON_Text = "";
            this.Toggle_Status.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_Status.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_Status.Size = new System.Drawing.Size(158, 29);
            this.Toggle_Status.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_Status.TabIndex = 89;
            this.Toggle_Status.Tag = "تفصيلي";
            this.Toggle_Status.Text = "#";
            this.Toggle_Status.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.Toggle_Status.UseVisualStyleBackColor = true;
            this.Toggle_Status.CheckedChanged += new System.EventHandler(this.Toggle_Status_CheckedChanged);
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(442, 123);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(87, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 90;
            this.rjLabel14.Text = "الحجم المسموح";
            // 
            // rjLabel13
            // 
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(177, 89);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel13.Size = new System.Drawing.Size(84, 17);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 90;
            this.rjLabel13.Text = "الوقت المتبقي";
            // 
            // lbl_limituptime
            // 
            this.lbl_limituptime.AutoSize = true;
            this.lbl_limituptime.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_limituptime.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_limituptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_limituptime.LinkLabel = false;
            this.lbl_limituptime.Location = new System.Drawing.Point(440, 90);
            this.lbl_limituptime.Name = "lbl_limituptime";
            this.lbl_limituptime.Size = new System.Drawing.Size(89, 17);
            this.lbl_limituptime.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_limituptime.TabIndex = 90;
            this.lbl_limituptime.Text = "الوقت المسموح";
            // 
            // txt_Till_Date
            // 
            this.txt_Till_Date._Customizable = false;
            this.txt_Till_Date.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Till_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Till_Date.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Till_Date.BorderRadius = 5;
            this.txt_Till_Date.BorderSize = 1;
            this.txt_Till_Date.Enabled = false;
            this.txt_Till_Date.Font = new System.Drawing.Font("Arial", 9.75F);
            this.txt_Till_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Till_Date.Location = new System.Drawing.Point(3, 46);
            this.txt_Till_Date.MultiLine = false;
            this.txt_Till_Date.Name = "txt_Till_Date";
            this.txt_Till_Date.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Till_Date.PasswordChar = false;
            this.txt_Till_Date.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Till_Date.PlaceHolderText = null;
            this.txt_Till_Date.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Till_Date.Size = new System.Drawing.Size(168, 27);
            this.txt_Till_Date.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Till_Date.TabIndex = 48;
            this.txt_Till_Date.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_RegDate
            // 
            this.txt_RegDate._Customizable = false;
            this.txt_RegDate.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_RegDate.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_RegDate.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_RegDate.BorderRadius = 5;
            this.txt_RegDate.BorderSize = 1;
            this.txt_RegDate.Enabled = false;
            this.txt_RegDate.Font = new System.Drawing.Font("Arial", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_RegDate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_RegDate.Location = new System.Drawing.Point(269, 47);
            this.txt_RegDate.MultiLine = false;
            this.txt_RegDate.Name = "txt_RegDate";
            this.txt_RegDate.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_RegDate.PasswordChar = false;
            this.txt_RegDate.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_RegDate.PlaceHolderText = null;
            this.txt_RegDate.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_RegDate.Size = new System.Drawing.Size(163, 27);
            this.txt_RegDate.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_RegDate.TabIndex = 48;
            this.txt_RegDate.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Password
            // 
            this.txt_Password._Customizable = false;
            this.txt_Password.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Password.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Password.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Password.BorderRadius = 5;
            this.txt_Password.BorderSize = 1;
            this.txt_Password.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_Password.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Password.Location = new System.Drawing.Point(3, 10);
            this.txt_Password.MultiLine = false;
            this.txt_Password.Name = "txt_Password";
            this.txt_Password.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Password.PasswordChar = false;
            this.txt_Password.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Password.PlaceHolderText = null;
            this.txt_Password.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Password.Size = new System.Drawing.Size(168, 27);
            this.txt_Password.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Password.TabIndex = 0;
            this.txt_Password.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel12
            // 
            this.rjLabel12.AutoSize = true;
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(188, 53);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel12.Size = new System.Drawing.Size(73, 17);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel12.TabIndex = 47;
            this.rjLabel12.Text = "تاريخ الانتهاء";
            // 
            // txt_username
            // 
            this.txt_username._Customizable = false;
            this.txt_username.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_username.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_username.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_username.BorderRadius = 5;
            this.txt_username.BorderSize = 1;
            this.txt_username.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_username.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_username.Location = new System.Drawing.Point(269, 11);
            this.txt_username.MultiLine = false;
            this.txt_username.Name = "txt_username";
            this.txt_username.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_username.PasswordChar = false;
            this.txt_username.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_username.PlaceHolderText = null;
            this.txt_username.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_username.Size = new System.Drawing.Size(163, 27);
            this.txt_username.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_username.TabIndex = 0;
            this.txt_username.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_username.onTextChanged += new System.EventHandler(this.txt_username_onTextChanged);
            // 
            // rjLabel11
            // 
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(188, 12);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(73, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 47;
            this.rjLabel11.Text = "كلمـــة المرور";
            // 
            // lbl_printDate
            // 
            this.lbl_printDate.AutoSize = true;
            this.lbl_printDate.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_printDate.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_printDate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_printDate.LinkLabel = false;
            this.lbl_printDate.Location = new System.Drawing.Point(449, 53);
            this.lbl_printDate.Name = "lbl_printDate";
            this.lbl_printDate.Size = new System.Drawing.Size(80, 17);
            this.lbl_printDate.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_printDate.TabIndex = 47;
            this.lbl_printDate.Text = "تــــاريخ الطباعة";
            // 
            // lbl_username
            // 
            this.lbl_username.AutoSize = true;
            this.lbl_username.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_username.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_username.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_username.LinkLabel = false;
            this.lbl_username.Location = new System.Drawing.Point(458, 13);
            this.lbl_username.Name = "lbl_username";
            this.lbl_username.Size = new System.Drawing.Size(71, 17);
            this.lbl_username.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_username.TabIndex = 47;
            this.lbl_username.Text = "الاســـــــــــــــــم";
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.dgv_Sessions);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(8, 310);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(934, 189);
            this.rjPanel2.TabIndex = 1;
            // 
            // dgv_Sessions
            // 
            this.dgv_Sessions.AllowUserToAddRows = false;
            this.dgv_Sessions.AllowUserToDeleteRows = false;
            this.dgv_Sessions.AllowUserToOrderColumns = true;
            this.dgv_Sessions.AllowUserToResizeRows = false;
            this.dgv_Sessions.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_Sessions.AlternatingRowsColorApply = false;
            this.dgv_Sessions.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_Sessions.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_Sessions.BorderRadius = 13;
            this.dgv_Sessions.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_Sessions.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_Sessions.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_Sessions.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_Sessions.ColumnHeaderHeight = 40;
            this.dgv_Sessions.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv_Sessions.ColumnHeadersHeight = 40;
            this.dgv_Sessions.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_Sessions.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_Sessions.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv_Sessions.ContextMenuStrip = this.dm_Session;
            this.dgv_Sessions.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv_Sessions.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_Sessions.EnableHeadersVisualStyles = false;
            this.dgv_Sessions.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_Sessions.Location = new System.Drawing.Point(14, 7);
            this.dgv_Sessions.Name = "dgv_Sessions";
            this.dgv_Sessions.ReadOnly = true;
            this.dgv_Sessions.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_Sessions.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_Sessions.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Sessions.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv_Sessions.RowHeadersVisible = false;
            this.dgv_Sessions.RowHeadersWidth = 35;
            this.dgv_Sessions.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_Sessions.RowHeight = 35;
            this.dgv_Sessions.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv_Sessions.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.RowTemplate.Height = 35;
            this.dgv_Sessions.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_Sessions.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_Sessions.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_Sessions.Size = new System.Drawing.Size(907, 179);
            this.dgv_Sessions.TabIndex = 52;
            this.dgv_Sessions.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dgv_Sessions_CellFormatting);
            this.dgv_Sessions.CellPainting += new System.Windows.Forms.DataGridViewCellPaintingEventHandler(this.dgv_Sessions_CellPainting);
            this.dgv_Sessions.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv_Sessions_MouseDown);
            // 
            // dm_Session
            // 
            this.dm_Session.ActiveMenuItem = false;
            this.dm_Session.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dm_Session.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dm_Session.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ترتيبالاعمدةToolStripMenuItem,
            this.حذفجميعجلساتالكرتToolStripMenuItem,
            this.تحديثالجلساتمنالروترToolStripMenuItem,
            this.نسخToolStripMenuItem,
            this.نسخالسطركاملToolStripMenuItem});
            this.dm_Session.Name = "dm_Session";
            this.dm_Session.OwnerIsMenuButton = false;
            this.dm_Session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dm_Session.Size = new System.Drawing.Size(199, 114);
            // 
            // ترتيبالاعمدةToolStripMenuItem
            // 
            this.ترتيبالاعمدةToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.حسبتاريخبدايةالجلسةToolStripMenuItem,
            this.حسبتاريخنهايةالجلسةToolStripMenuItem,
            this.حسبالوقتالمستخدمToolStripMenuItem,
            this.حسبالتحميلالمستخدمToolStripMenuItem,
            this.حسبالرفعالمستخدمToolStripMenuItem,
            this.حسبايبيالجلسهIPToolStripMenuItem,
            this.ترتيببحسبالماكToolStripMenuItem,
            this.ترتيببحسبالبورتالجهازToolStripMenuItem,
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem});
            this.ترتيبالاعمدةToolStripMenuItem.Name = "ترتيبالاعمدةToolStripMenuItem";
            this.ترتيبالاعمدةToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.ترتيبالاعمدةToolStripMenuItem.Text = "ترتيب الجلسات ";
            // 
            // حسبتاريخبدايةالجلسةToolStripMenuItem
            // 
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Name = "حسبتاريخبدايةالجلسةToolStripMenuItem";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Tag = "FromTime";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Text = "حسب تاريخ بداية الجلسة";
            this.حسبتاريخبدايةالجلسةToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حسبتاريخنهايةالجلسةToolStripMenuItem
            // 
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Name = "حسبتاريخنهايةالجلسةToolStripMenuItem";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Tag = "TillTime";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Text = "حسب تاريخ نهاية الجلسة";
            this.حسبتاريخنهايةالجلسةToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حسبالوقتالمستخدمToolStripMenuItem
            // 
            this.حسبالوقتالمستخدمToolStripMenuItem.Name = "حسبالوقتالمستخدمToolStripMenuItem";
            this.حسبالوقتالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالوقتالمستخدمToolStripMenuItem.Tag = "UpTime";
            this.حسبالوقتالمستخدمToolStripMenuItem.Text = "حسب الوقت المستخدم";
            this.حسبالوقتالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حسبالتحميلالمستخدمToolStripMenuItem
            // 
            this.حسبالتحميلالمستخدمToolStripMenuItem.Name = "حسبالتحميلالمستخدمToolStripMenuItem";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالتحميلالمستخدمToolStripMenuItem.Tag = "BytesDownload";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Text = "حسب التحميل المستخدم";
            this.حسبالتحميلالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حسبالرفعالمستخدمToolStripMenuItem
            // 
            this.حسبالرفعالمستخدمToolStripMenuItem.Name = "حسبالرفعالمستخدمToolStripMenuItem";
            this.حسبالرفعالمستخدمToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبالرفعالمستخدمToolStripMenuItem.Tag = "BytesUpload";
            this.حسبالرفعالمستخدمToolStripMenuItem.Text = "حسب الرفع المستخدم";
            this.حسبالرفعالمستخدمToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حسبايبيالجلسهIPToolStripMenuItem
            // 
            this.حسبايبيالجلسهIPToolStripMenuItem.Name = "حسبايبيالجلسهIPToolStripMenuItem";
            this.حسبايبيالجلسهIPToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.حسبايبيالجلسهIPToolStripMenuItem.Tag = "IpUser";
            this.حسبايبيالجلسهIPToolStripMenuItem.Text = "حسب اي بي الجلسه(IP)";
            this.حسبايبيالجلسهIPToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // ترتيببحسبالماكToolStripMenuItem
            // 
            this.ترتيببحسبالماكToolStripMenuItem.Name = "ترتيببحسبالماكToolStripMenuItem";
            this.ترتيببحسبالماكToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالماكToolStripMenuItem.Tag = "CallingStationId";
            this.ترتيببحسبالماكToolStripMenuItem.Text = "ترتيب بحسب الماك";
            this.ترتيببحسبالماكToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // ترتيببحسبالبورتالجهازToolStripMenuItem
            // 
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Name = "ترتيببحسبالبورتالجهازToolStripMenuItem";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Tag = "NasPortId";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Text = "ترتيب بحسب البورت(الجهاز)";
            this.ترتيببحسبالبورتالجهازToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // ترتيببحسبالسيرفرراديوسToolStripMenuItem
            // 
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Name = "ترتيببحسبالسيرفرراديوسToolStripMenuItem";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Size = new System.Drawing.Size(220, 22);
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Tag = "IpRouter";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Text = "ترتيب بحسب السيرفر(راديوس)";
            this.ترتيببحسبالسيرفرراديوسToolStripMenuItem.Click += new System.EventHandler(this.OrderBy_ToolStripMenuItem_Click);
            // 
            // حذفجميعجلساتالكرتToolStripMenuItem
            // 
            this.حذفجميعجلساتالكرتToolStripMenuItem.Name = "حذفجميعجلساتالكرتToolStripMenuItem";
            this.حذفجميعجلساتالكرتToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.حذفجميعجلساتالكرتToolStripMenuItem.Text = "حذف جميع جلسات الكرت";
            this.حذفجميعجلساتالكرتToolStripMenuItem.Click += new System.EventHandler(this.حذفجميعجلساتالكرتToolStripMenuItem_Click);
            // 
            // تحديثالجلساتمنالروترToolStripMenuItem
            // 
            this.تحديثالجلساتمنالروترToolStripMenuItem.Name = "تحديثالجلساتمنالروترToolStripMenuItem";
            this.تحديثالجلساتمنالروترToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.تحديثالجلساتمنالروترToolStripMenuItem.Text = "تحديث الجلسات من الروتر";
            this.تحديثالجلساتمنالروترToolStripMenuItem.Click += new System.EventHandler(this.تحديثالجلساتمنالروترToolStripMenuItem_Click);
            // 
            // نسخToolStripMenuItem
            // 
            this.نسخToolStripMenuItem.Name = "نسخToolStripMenuItem";
            this.نسخToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.نسخToolStripMenuItem.Text = "نسخ الخلية المحدده";
            this.نسخToolStripMenuItem.Click += new System.EventHandler(this.نسخToolStripMenuItem_Click);
            // 
            // نسخالسطركاملToolStripMenuItem
            // 
            this.نسخالسطركاملToolStripMenuItem.Name = "نسخالسطركاملToolStripMenuItem";
            this.نسخالسطركاملToolStripMenuItem.Size = new System.Drawing.Size(198, 22);
            this.نسخالسطركاملToolStripMenuItem.Text = "نسخ السطر كامل";
            this.نسخالسطركاملToolStripMenuItem.Click += new System.EventHandler(this.نسخالسطركاملToolStripMenuItem_Click);
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 10;
            this.rjPanel3.Controls.Add(this.dgv_profiles);
            this.rjPanel3.Controls.Add(this.btnAddProfile);
            this.rjPanel3.Controls.Add(this.txt_TotalPrice);
            this.rjPanel3.Controls.Add(this.txt_CountProfile);
            this.rjPanel3.Controls.Add(this.rjLabel10);
            this.rjPanel3.Controls.Add(this.rjLabel8);
            this.rjPanel3.Controls.Add(this.rjLabel9);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(11, 6);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjPanel3.Size = new System.Drawing.Size(376, 236);
            this.rjPanel3.TabIndex = 0;
            // 
            // dgv_profiles
            // 
            this.dgv_profiles.AllowUserToAddRows = false;
            this.dgv_profiles.AllowUserToDeleteRows = false;
            this.dgv_profiles.AllowUserToOrderColumns = true;
            this.dgv_profiles.AllowUserToResizeRows = false;
            this.dgv_profiles.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_profiles.AlternatingRowsColorApply = false;
            this.dgv_profiles.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_profiles.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_profiles.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_profiles.BorderRadius = 13;
            this.dgv_profiles.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_profiles.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_profiles.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_profiles.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_profiles.ColumnHeaderHeight = 40;
            this.dgv_profiles.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv_profiles.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv_profiles.ColumnHeadersHeight = 40;
            this.dgv_profiles.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_profiles.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_profiles.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_profiles.ContextMenuStrip = this.dm_profile;
            this.dgv_profiles.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_profiles.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv_profiles.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_profiles.EnableHeadersVisualStyles = false;
            this.dgv_profiles.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_profiles.Location = new System.Drawing.Point(11, 27);
            this.dgv_profiles.Name = "dgv_profiles";
            this.dgv_profiles.ReadOnly = true;
            this.dgv_profiles.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_profiles.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_profiles.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_profiles.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv_profiles.RowHeadersVisible = false;
            this.dgv_profiles.RowHeadersWidth = 35;
            this.dgv_profiles.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_profiles.RowHeight = 35;
            this.dgv_profiles.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_profiles.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv_profiles.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_profiles.RowTemplate.Height = 35;
            this.dgv_profiles.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_profiles.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_profiles.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_profiles.Size = new System.Drawing.Size(355, 141);
            this.dgv_profiles.TabIndex = 51;
            this.dgv_profiles.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dgv_profiles_CellFormatting);
            this.dgv_profiles.CellPainting += new System.Windows.Forms.DataGridViewCellPaintingEventHandler(this.dgv_profiles_CellPainting);
            // 
            // dm_profile
            // 
            this.dm_profile.ActiveMenuItem = false;
            this.dm_profile.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dm_profile.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dm_profile.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.اضافةباقةجديدةToolStripMenuItem,
            this.RemoveProfile_ToolStripMenuItem,
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem});
            this.dm_profile.Name = "dm_profile";
            this.dm_profile.OwnerIsMenuButton = false;
            this.dm_profile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dm_profile.Size = new System.Drawing.Size(215, 70);
            // 
            // اضافةباقةجديدةToolStripMenuItem
            // 
            this.اضافةباقةجديدةToolStripMenuItem.Name = "اضافةباقةجديدةToolStripMenuItem";
            this.اضافةباقةجديدةToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.اضافةباقةجديدةToolStripMenuItem.Text = "اضافة باقة جديدة";
            this.اضافةباقةجديدةToolStripMenuItem.Click += new System.EventHandler(this.اضافةباقةجديدةToolStripMenuItem_Click);
            // 
            // RemoveProfile_ToolStripMenuItem
            // 
            this.RemoveProfile_ToolStripMenuItem.Name = "RemoveProfile_ToolStripMenuItem";
            this.RemoveProfile_ToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.RemoveProfile_ToolStripMenuItem.Text = "حذف الباقة المحدده";
            this.RemoveProfile_ToolStripMenuItem.Visible = false;
            this.RemoveProfile_ToolStripMenuItem.Click += new System.EventHandler(this.حذفالباقةالمحددهToolStripMenuItem_Click);
            // 
            // تحديثباقاتالكرتمنالروترToolStripMenuItem
            // 
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Name = "تحديثباقاتالكرتمنالروترToolStripMenuItem";
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Size = new System.Drawing.Size(214, 22);
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Text = "تحديث باقات الكرت من الروتر";
            this.تحديثباقاتالكرتمنالروترToolStripMenuItem.Click += new System.EventHandler(this.تحديثباقاتالكرتمنالروترToolStripMenuItem_Click);
            // 
            // btnAddProfile
            // 
            this.btnAddProfile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btnAddProfile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.BorderRadius = 8;
            this.btnAddProfile.BorderSize = 1;
            this.btnAddProfile.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAddProfile.FlatAppearance.BorderSize = 0;
            this.btnAddProfile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btnAddProfile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btnAddProfile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddProfile.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnAddProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAddProfile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAddProfile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddProfile.IconSize = 17;
            this.btnAddProfile.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAddProfile.Location = new System.Drawing.Point(12, 185);
            this.btnAddProfile.Name = "btnAddProfile";
            this.btnAddProfile.Size = new System.Drawing.Size(128, 42);
            this.btnAddProfile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btnAddProfile.TabIndex = 49;
            this.btnAddProfile.Text = "اضافة باقة";
            this.btnAddProfile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAddProfile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAddProfile.UseVisualStyleBackColor = false;
            this.btnAddProfile.Click += new System.EventHandler(this.btnAddProfile_Click);
            // 
            // txt_TotalPrice
            // 
            this.txt_TotalPrice._Customizable = false;
            this.txt_TotalPrice.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalPrice.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalPrice.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalPrice.BorderRadius = 8;
            this.txt_TotalPrice.BorderSize = 1;
            this.txt_TotalPrice.Enabled = false;
            this.txt_TotalPrice.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalPrice.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalPrice.Location = new System.Drawing.Point(147, 194);
            this.txt_TotalPrice.MultiLine = false;
            this.txt_TotalPrice.Name = "txt_TotalPrice";
            this.txt_TotalPrice.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalPrice.PasswordChar = false;
            this.txt_TotalPrice.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalPrice.PlaceHolderText = null;
            this.txt_TotalPrice.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.txt_TotalPrice.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalPrice.Size = new System.Drawing.Size(105, 29);
            this.txt_TotalPrice.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalPrice.TabIndex = 49;
            this.txt_TotalPrice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_CountProfile
            // 
            this.txt_CountProfile._Customizable = false;
            this.txt_CountProfile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_CountProfile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_CountProfile.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_CountProfile.BorderRadius = 8;
            this.txt_CountProfile.BorderSize = 1;
            this.txt_CountProfile.Enabled = false;
            this.txt_CountProfile.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_CountProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_CountProfile.Location = new System.Drawing.Point(264, 195);
            this.txt_CountProfile.MultiLine = false;
            this.txt_CountProfile.Name = "txt_CountProfile";
            this.txt_CountProfile.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_CountProfile.PasswordChar = false;
            this.txt_CountProfile.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_CountProfile.PlaceHolderText = null;
            this.txt_CountProfile.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_CountProfile.Size = new System.Drawing.Size(94, 29);
            this.txt_CountProfile.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_CountProfile.TabIndex = 49;
            this.txt_CountProfile.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(164, 170);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel10.Size = new System.Drawing.Size(77, 23);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 47;
            this.rjLabel10.Text = "اجمالي المبلغ";
            // 
            // rjLabel8
            // 
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(119, 7);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(123, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 47;
            this.rjLabel8.Text = "جميع الباقات المضافه :";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(283, 170);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(66, 23);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 47;
            this.rjLabel9.Text = "عدد الباقات";
            // 
            // rjLabel5
            // 
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(794, 250);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(90, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 47;
            this.rjLabel5.Text = "اجمالي الجلسات";
            // 
            // rjLabel6
            // 
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(529, 250);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(134, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 47;
            this.rjLabel6.Text = "اجمالي الوقت المستخدم";
            // 
            // rjLabel7
            // 
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(279, 250);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(165, 17);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 47;
            this.rjLabel7.Text = "اجمالي تحميل+الرقع المستخدم";
            // 
            // txt_TotalDownload
            // 
            this.txt_TotalDownload._Customizable = false;
            this.txt_TotalDownload.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalDownload.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalDownload.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalDownload.BorderRadius = 8;
            this.txt_TotalDownload.BorderSize = 1;
            this.txt_TotalDownload.Enabled = false;
            this.txt_TotalDownload.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalDownload.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalDownload.Location = new System.Drawing.Point(287, 275);
            this.txt_TotalDownload.MultiLine = false;
            this.txt_TotalDownload.Name = "txt_TotalDownload";
            this.txt_TotalDownload.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalDownload.PasswordChar = false;
            this.txt_TotalDownload.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalDownload.PlaceHolderText = null;
            this.txt_TotalDownload.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalDownload.Size = new System.Drawing.Size(170, 29);
            this.txt_TotalDownload.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalDownload.TabIndex = 49;
            this.txt_TotalDownload.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_TotalUptime
            // 
            this.txt_TotalUptime._Customizable = false;
            this.txt_TotalUptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_TotalUptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_TotalUptime.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_TotalUptime.BorderRadius = 8;
            this.txt_TotalUptime.BorderSize = 1;
            this.txt_TotalUptime.Enabled = false;
            this.txt_TotalUptime.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_TotalUptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_TotalUptime.Location = new System.Drawing.Point(504, 276);
            this.txt_TotalUptime.MultiLine = false;
            this.txt_TotalUptime.Name = "txt_TotalUptime";
            this.txt_TotalUptime.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_TotalUptime.PasswordChar = false;
            this.txt_TotalUptime.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_TotalUptime.PlaceHolderText = null;
            this.txt_TotalUptime.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_TotalUptime.Size = new System.Drawing.Size(189, 29);
            this.txt_TotalUptime.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_TotalUptime.TabIndex = 49;
            this.txt_TotalUptime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_CountSession
            // 
            this.txt_CountSession._Customizable = false;
            this.txt_CountSession.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_CountSession.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_CountSession.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_CountSession.BorderRadius = 8;
            this.txt_CountSession.BorderSize = 1;
            this.txt_CountSession.Enabled = false;
            this.txt_CountSession.Font = new System.Drawing.Font("Tahoma", 11F);
            this.txt_CountSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_CountSession.Location = new System.Drawing.Point(753, 275);
            this.txt_CountSession.MultiLine = false;
            this.txt_CountSession.Name = "txt_CountSession";
            this.txt_CountSession.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_CountSession.PasswordChar = false;
            this.txt_CountSession.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_CountSession.PlaceHolderText = null;
            this.txt_CountSession.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_CountSession.Size = new System.Drawing.Size(182, 29);
            this.txt_CountSession.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_CountSession.TabIndex = 49;
            this.txt_CountSession.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 8;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.RotateForward;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 20;
            this.btn_Refresh.Location = new System.Drawing.Point(19, 246);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Size = new System.Drawing.Size(161, 32);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 49;
            this.btn_Refresh.Text = "تحديث من الروتر";
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // Radio_WithSession
            // 
            this.Radio_WithSession.AutoSize = true;
            this.Radio_WithSession.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_WithSession.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_WithSession.Customizable = false;
            this.Radio_WithSession.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_WithSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_WithSession.Location = new System.Drawing.Point(17, 283);
            this.Radio_WithSession.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_WithSession.Name = "Radio_WithSession";
            this.Radio_WithSession.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_WithSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Radio_WithSession.Size = new System.Drawing.Size(93, 21);
            this.Radio_WithSession.TabIndex = 53;
            this.Radio_WithSession.Text = "مع الجلسات";
            this.Radio_WithSession.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_WithSession.UseVisualStyleBackColor = true;
            // 
            // Radio_Baisc
            // 
            this.Radio_Baisc.AutoSize = true;
            this.Radio_Baisc.Checked = true;
            this.Radio_Baisc.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_Baisc.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_Baisc.Customizable = false;
            this.Radio_Baisc.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_Baisc.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_Baisc.Location = new System.Drawing.Point(127, 283);
            this.Radio_Baisc.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_Baisc.Name = "Radio_Baisc";
            this.Radio_Baisc.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_Baisc.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Radio_Baisc.Size = new System.Drawing.Size(156, 21);
            this.Radio_Baisc.TabIndex = 54;
            this.Radio_Baisc.TabStop = true;
            this.Radio_Baisc.Text = "تحديث البيانات الاساسية";
            this.Radio_Baisc.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_Baisc.UseVisualStyleBackColor = true;
            // 
            // lbl_note
            // 
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(24, 505);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(333, 17);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 47;
            this.lbl_note.Text = "* اضغط بزر الماوس الايمن علي اي جدول لاضهار الخيارات المتاحة";
            // 
            // Form_CardsDetails
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "Form_CardsDetails";
            this.ClientSize = new System.Drawing.Size(961, 579);
            this.ControlBox = false;
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.DoubleBuffered = false;
            this.FormIcon = FontAwesome.Sharp.IconChar.DesktopAlt;
            this.HelpButton = true;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.Name = "Form_CardsDetails";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Resizable = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "Form_CardsDetails";
            this.Load += new System.EventHandler(this.Form_CardsDetails_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Sessions)).EndInit();
            this.dm_Session.ResumeLayout(false);
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_profiles)).EndInit();
            this.dm_profile.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel1;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJPanel rjPanel3;
        public RJControls.RJTextBox txt_RegDate;
        public RJControls.RJTextBox txt_username;
        private RJControls.RJLabel lbl_printDate;
        private RJControls.RJLabel lbl_username;
        private RJControls.RJToggleButton Toggle_Status;
        private RJControls.RJLabel rjLabel2;
        public RJControls.RJTextBox txt_uptimeLimit;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel lbl_limituptime;
        private RJControls.RJToggleButton Toggle_Bind_Mac;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJTextBox txt_TotalDownload;
        private RJControls.RJTextBox txt_CountSession;
        private RJControls.RJTextBox txt_TotalUptime;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJTextBox txt_TotalPrice;
        private RJControls.RJTextBox txt_CountProfile;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJLabel rjLabel9;
        public RJControls.RJButton btnAddProfile;
        public RJControls.RJButton btnSave;
        public RJControls.RJTextBox txt_TimeLeft;
        private RJControls.RJLabel rjLabel13;
        public RJControls.RJTextBox txt_Till_Date;
        public RJControls.RJTextBox txt_Password;
        private RJControls.RJLabel rjLabel12;
        private RJControls.RJLabel rjLabel11;
        public RJControls.RJTextBox txt_TransferLeft;
        public RJControls.RJTextBox txt_TransferLimit;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJLabel rjLabel14;
        public RJControls.RJTextBox txt_mac;
        private RJControls.RJLabel rjLabel16;
        public RJControls.RJButton btn_Refresh;
        private RJControls.RJDataGridView dgv_profiles;
        private RJControls.RJDataGridView dgv_Sessions;
        private RJControls.RJRadioButton Radio_WithSession;
        private RJControls.RJRadioButton Radio_Baisc;
        private RJControls.RJDropdownMenu dm_profile;
        private RJControls.RJDropdownMenu dm_Session;
        private System.Windows.Forms.ToolStripMenuItem نسخToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخالسطركاملToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem اضافةباقةجديدةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem RemoveProfile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تحديثباقاتالكرتمنالروترToolStripMenuItem;
        private RJControls.RJLabel lbl_note;
        private System.Windows.Forms.ToolStripMenuItem ترتيبالاعمدةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبتاريخبدايةالجلسةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبتاريخنهايةالجلسةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالوقتالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالتحميلالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبالرفعالمستخدمToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حسبايبيالجلسهIPToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالماكToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالبورتالجهازToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ترتيببحسبالسيرفرراديوسToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حذفجميعجلساتالكرتToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تحديثالجلساتمنالروترToolStripMenuItem;
        private System.Windows.Forms.ToolTip toolTip1;
    }
}