using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار RJTabControl المبسط بدون Collection معقد
    /// </summary>
    public partial class SimplifiedRJTabControlTest : Form
    {
        private RJTabControl simplifiedTabControl;

        public SimplifiedRJTabControlTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // simplifiedTabControl
            // 
            this.simplifiedTabControl = new RJTabControl();
            this.simplifiedTabControl.Dock = DockStyle.Fill;
            this.simplifiedTabControl.TabHeight = 45;
            this.simplifiedTabControl.TabSpacing = 3;
            this.simplifiedTabControl.TabPadding = 20;

            // 
            // SimplifiedRJTabControlTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Controls.Add(this.simplifiedTabControl);
            this.Name = "SimplifiedRJTabControlTest";
            this.Text = "🔧 اختبار RJTabControl المبسط - بدون Collection معقد";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += SimplifiedRJTabControlTest_Load;
        }

        private void SimplifiedRJTabControlTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية
                AddSimplifiedTestTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ تم التحميل بنجاح!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل RJTabControl المبسط:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل التحميل!";
            }
        }

        private void AddSimplifiedTestTabs()
        {
            // تاب التعليمات
            var instructionsTab = this.simplifiedTabControl.AddTab("التعليمات", IconChar.Info);
            instructionsTab.BackColor = Color.FromArgb(0, 122, 204);
            instructionsTab.ForeColor = Color.White;

            var instructionsLabel = new Label
            {
                Text = "🔧 RJTabControl المبسط:\n\n" +
                       "✅ تم إخفاء خاصية Tabs Collection مؤقتاً\n" +
                       "✅ [Browsable(false)] و [DesignerSerializationVisibility.Hidden]\n" +
                       "✅ هذا يجب أن يحل مشكلة Designer\n\n" +
                       "🧪 خطوات الاختبار:\n" +
                       "1. اسحب RJTabControl من Toolbox\n" +
                       "2. يجب أن يعمل بدون أخطاء الآن\n" +
                       "3. لن تجد خاصية Tabs في Properties\n" +
                       "4. لكن يمكن إضافة التابات برمجياً\n\n" +
                       "🎯 إذا عمل هذا:\n" +
                       "فالمشكلة في RJTabPageCollection أو Editor\n" +
                       "وليس في RJTabControl نفسه\n\n" +
                       "📋 الميزات المتاحة:\n" +
                       "• إضافة التابات برمجياً ✅\n" +
                       "• جميع خصائص RJTabControl ✅\n" +
                       "• التنقل بين التابات ✅\n" +
                       "• الأحداث والتفاعل ✅",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                Padding = new Padding(20)
            };
            instructionsTab.AddControl(instructionsLabel);

            // تاب الاختبار
            var testTab = this.simplifiedTabControl.AddTab("اختبار الوظائف", IconChar.Play);
            testTab.BackColor = Color.FromArgb(76, 175, 80);
            testTab.ForeColor = Color.White;

            var testPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var infoLabel = new Label
            {
                Text = "🎮 اختبار وظائف RJTabControl:\n\n" +
                       $"عدد التابات: {this.simplifiedTabControl.TabCount}\n" +
                       $"التاب النشط: {this.simplifiedTabControl.SelectedIndex}\n" +
                       $"ارتفاع التابات: {this.simplifiedTabControl.TabHeight}\n\n" +
                       "جرب الأزرار أدناه:",
                Location = new Point(0, 0),
                Size = new Size(800, 120),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            var addTabButton = new RJButton
            {
                Text = "إضافة تاب جديد",
                IconChar = IconChar.Plus,
                Location = new Point(20, 140),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            addTabButton.Click += AddNewTab_Click;

            var changePropertiesButton = new RJButton
            {
                Text = "تغيير الخصائص",
                IconChar = IconChar.Cogs,
                Location = new Point(240, 140),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            changePropertiesButton.Click += ChangeProperties_Click;

            var testAllButton = new RJButton
            {
                Text = "اختبار شامل",
                IconChar = IconChar.CheckCircle,
                Location = new Point(460, 140),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            testAllButton.Click += TestAll_Click;

            testPanel.Controls.Add(infoLabel);
            testPanel.Controls.Add(addTabButton);
            testPanel.Controls.Add(changePropertiesButton);
            testPanel.Controls.Add(testAllButton);
            testTab.AddControl(testPanel);

            // تاب النتائج
            var resultsTab = this.simplifiedTabControl.AddTab("النتائج", IconChar.ChartBar);
            resultsTab.BackColor = Color.FromArgb(63, 81, 181);
            resultsTab.ForeColor = Color.White;

            var resultsTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "📊 نتائج الاختبار المبسط:\n\n" +
                       "🎯 الهدف:\n" +
                       "تحديد ما إذا كانت المشكلة في RJTabControl نفسه\n" +
                       "أم في RJTabPageCollection/Editor\n\n" +
                       "🔧 التعديلات المطبقة:\n" +
                       "• إخفاء خاصية Tabs من Designer\n" +
                       "• [Browsable(false)]\n" +
                       "• [DesignerSerializationVisibility.Hidden]\n" +
                       "• الاحتفاظ بجميع الوظائف الأخرى\n\n" +
                       "✅ إذا عمل RJTabControl في Designer الآن:\n" +
                       "• المشكلة في RJTabPageCollection\n" +
                       "• أو في RJTabPageCollectionEditor\n" +
                       "• أو في تفاعل Collection مع Designer\n" +
                       "• RJTabControl نفسه سليم\n\n" +
                       "❌ إذا لم يعمل RJTabControl:\n" +
                       "• المشكلة في RJTabControl نفسه\n" +
                       "• أو في خاصية أخرى غير Collection\n" +
                       "• نحتاج لتبسيط أكثر\n\n" +
                       "🚀 الخطوة التالية:\n" +
                       "بناءً على النتيجة، سنعرف الاتجاه الصحيح\n" +
                       "لحل المشكلة نهائياً\n\n" +
                       "⚠️ ملاحظة:\n" +
                       "هذا اختبار تشخيصي فقط\n" +
                       "الهدف تحديد مصدر المشكلة بدقة",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(63, 81, 181),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            resultsTab.AddControl(resultsTextBox);

            // تفعيل التاب الأول
            this.simplifiedTabControl.SelectedIndex = 0;
        }

        private void AddNewTab_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = this.simplifiedTabControl.AddTab($"تاب {this.simplifiedTabControl.TabCount + 1}", IconChar.Star);
                newTab.BackColor = Color.FromArgb(233, 30, 99);
                newTab.ForeColor = Color.White;

                var label = new Label
                {
                    Text = $"🌟 تاب جديد #{this.simplifiedTabControl.TabCount}\n\n" +
                           $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ إضافة التابات تعمل بمثالية!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                newTab.AddControl(label);

                // تفعيل التاب الجديد
                this.simplifiedTabControl.SelectedTab = newTab;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ChangeProperties_Click(object sender, EventArgs e)
        {
            try
            {
                // تغيير خصائص عشوائية
                var random = new Random();
                this.simplifiedTabControl.TabHeight = 30 + random.Next(20);
                this.simplifiedTabControl.TabSpacing = 1 + random.Next(5);
                this.simplifiedTabControl.TabPadding = 10 + random.Next(20);

                MessageBox.Show($"✅ تم تغيير الخصائص:\n\n" +
                               $"TabHeight: {this.simplifiedTabControl.TabHeight}\n" +
                               $"TabSpacing: {this.simplifiedTabControl.TabSpacing}\n" +
                               $"TabPadding: {this.simplifiedTabControl.TabPadding}",
                               "تم التغيير", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تغيير الخصائص:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void TestAll_Click(object sender, EventArgs e)
        {
            try
            {
                var results = "🧪 نتائج الاختبار الشامل:\n\n";

                // اختبار الخصائص
                results += $"TabCount: {this.simplifiedTabControl.TabCount} ✅\n";
                results += $"SelectedIndex: {this.simplifiedTabControl.SelectedIndex} ✅\n";
                results += $"TabHeight: {this.simplifiedTabControl.TabHeight} ✅\n";
                results += $"TabSpacing: {this.simplifiedTabControl.TabSpacing} ✅\n";
                results += $"TabPadding: {this.simplifiedTabControl.TabPadding} ✅\n";

                // اختبار التاب النشط
                var activeTab = this.simplifiedTabControl.SelectedTab;
                results += $"ActiveTab: {activeTab?.Text ?? "null"} ✅\n";

                results += "\n🎉 جميع الوظائف تعمل بمثالية!";

                MessageBox.Show(results, "نتائج الاختبار الشامل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الاختبار الشامل:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار RJTabControl المبسط
        /// </summary>
        public static void RunSimplifiedTest()
        {
            try
            {
                var form = new SimplifiedRJTabControlTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار RJTabControl المبسط:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
