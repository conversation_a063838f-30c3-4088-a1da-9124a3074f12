using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار التحقق النهائي من جميع الميزات
    /// </summary>
    public class FinalValidationTest : Form
    {
        public FinalValidationTest()
        {
            InitializeForm();
            RunValidationTests();
        }

        private void InitializeForm()
        {
            this.Text = "✅ اختبار التحقق النهائي - RJTabControl";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void RunValidationTests()
        {
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 45,
                TabSpacing = 5,
                TabPadding = 25,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 8
            };

            // اختبار 1: إنشاء التابات بالطرق المختلفة
            TestTabCreationMethods(tabControl);

            // اختبار 2: خصائص RJButton
            TestRJButtonProperties(tabControl);

            // اختبار 3: خصائص Designer
            TestDesignerProperties(tabControl);

            // اختبار 4: RJPanel و RJTextBox
            TestUpdatedControls(tabControl);

            // اختبار 5: الأحداث
            TestEvents(tabControl);

            this.Controls.Add(tabControl);
        }

        private void TestTabCreationMethods(RJTabControl tabControl)
        {
            // الطريقة الأولى: AddTab(string)
            var tab1 = tabControl.AddTab("الطريقة الأولى");
            tab1.BackColor = Color.FromArgb(0, 122, 204);
            tab1.ForeColor = Color.White;
            tab1.IconChar = IconChar.Play;

            var label1 = new Label
            {
                Text = "✅ اختبار الطريقة الأولى نجح!\n\n" +
                       "AddTab(string text)\n" +
                       "تم إضافة الأيقونة يدوياً",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            tab1.AddControl(label1);

            // الطريقة الثانية: AddTab(string, IconChar)
            var tab2 = tabControl.AddTab("الطريقة الثانية", IconChar.Cogs);
            tab2.BackColor = Color.FromArgb(76, 175, 80);
            tab2.ForeColor = Color.White;

            var label2 = new Label
            {
                Text = "✅ اختبار الطريقة الثانية نجح!\n\n" +
                       "AddTab(string text, IconChar icon)\n" +
                       "النص والأيقونة معاً",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White
            };
            tab2.AddControl(label2);

            // الطريقة الثالثة: AddTab(RJTabPage)
            var customTab = new RJTabPage("الطريقة الثالثة", IconChar.Star);
            customTab.BackColor = Color.FromArgb(156, 39, 176);
            customTab.ForeColor = Color.White;
            customTab.BorderRadius = 12;
            customTab.Style = ControlStyle.Glass;

            var label3 = new Label
            {
                Text = "✅ اختبار الطريقة الثالثة نجح!\n\n" +
                       "AddTab(RJTabPage tab)\n" +
                       "تخصيص كامل قبل الإضافة",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White
            };
            customTab.AddControl(label3);
            tabControl.AddTab(customTab);
        }

        private void TestRJButtonProperties(RJTabControl tabControl)
        {
            var tab = tabControl.AddTab("خصائص RJButton", IconChar.Palette);
            
            // تطبيق جميع خصائص RJButton
            tab.BackColor = Color.FromArgb(244, 67, 54);
            tab.ForeColor = Color.White;
            tab.IconSize = 24;
            tab.BorderRadius = 15;
            tab.Style = ControlStyle.Glass;
            tab.Font = new Font("Segoe UI", 12, FontStyle.Bold);

            var panel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 3,
                BorderColor = Color.FromArgb(244, 67, 54),
                BorderRadius = 12,
                Padding = new Padding(20)
            };

            var label = new Label
            {
                Text = "✅ خصائص RJButton تعمل بمثالية!\n\n" +
                       "الخصائص المطبقة على هذا التاب:\n" +
                       "• BackColor = أحمر\n" +
                       "• IconSize = 24\n" +
                       "• BorderRadius = 15\n" +
                       "• Style = Glass\n" +
                       "• Font = Segoe UI, 12pt, Bold\n\n" +
                       "🎨 جميع خصائص RJButton متاحة!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(244, 67, 54)
            };
            panel.Controls.Add(label);
            tab.AddControl(panel);
        }

        private void TestDesignerProperties(RJTabControl tabControl)
        {
            var tab = tabControl.AddTab("خصائص Designer", IconChar.PaintBrush);
            tab.BackColor = Color.FromArgb(255, 152, 0);
            tab.ForeColor = Color.White;

            var textBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "✅ خصائص Designer تعمل بمثالية!\n\n" +
                       "الخصائص المتاحة:\n\n" +
                       "🎯 Tabs Collection:\n" +
                       "   • إضافة/تعديل/حذف التابات\n" +
                       "   • Collection Editor مخصص\n\n" +
                       "🎯 SelectedTab:\n" +
                       "   • التاب النشط حالياً\n" +
                       "   • يمكن تعيينه من Properties\n\n" +
                       "🎯 SelectedIndex:\n" +
                       "   • فهرس التاب النشط\n" +
                       "   • Current Index: " + tabControl.SelectedIndex + "\n\n" +
                       "🎯 TabCount:\n" +
                       "   • عدد التابات الحالي\n" +
                       "   • Current Count: " + tabControl.TabCount + "\n\n" +
                       "🚀 جميع خصائص Designer متاحة!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(255, 152, 0),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            tab.AddControl(textBox);
        }

        private void TestUpdatedControls(RJTabControl tabControl)
        {
            var tab = tabControl.AddTab("الكنترولات المحدثة", IconChar.Wrench);
            tab.BackColor = Color.FromArgb(103, 58, 183);
            tab.ForeColor = Color.White;

            var mainPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(10) };

            // اختبار RJPanel مع الحدود الجديدة
            var testPanel = new RJPanel
            {
                Location = new Point(10, 10),
                Size = new Size(400, 150),
                BorderSize = 4,
                BorderColor = Color.FromArgb(103, 58, 183),
                BorderRadius = 15,
                Padding = new Padding(15)
            };

            var panelLabel = new Label
            {
                Text = "✅ RJPanel محدث!\n\nBorderSize = 4\nBorderColor = بنفسجي\nBorderRadius = 15",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(103, 58, 183)
            };
            testPanel.Controls.Add(panelLabel);

            // اختبار RJTextBox مع ReadOnly
            var testTextBox = new RJTextBox
            {
                Location = new Point(420, 10),
                Size = new Size(400, 150),
                MultiLine = true,
                ReadOnly = true,
                Text = "✅ RJTextBox محدث!\n\nReadOnly = true\nهذا النص للقراءة فقط\nلا يمكن تعديله\n\nجرب الكتابة - لن يحدث شيء! 🔒",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(103, 58, 183),
                BorderRadius = 10,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Center
            };

            var statusLabel = new Label
            {
                Text = "🎉 جميع التحديثات تعمل بمثالية!\n\n" +
                       "• RJPanel يدعم BorderSize و BorderColor\n" +
                       "• RJTextBox يدعم ReadOnly\n" +
                       "• tabsPanel و contentPanel الآن RJPanel\n" +
                       "• الألوان الافتراضية محفوظة",
                Location = new Point(10, 180),
                Size = new Size(810, 100),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(70, 70, 70),
                TextAlign = ContentAlignment.MiddleCenter
            };

            mainPanel.Controls.Add(testPanel);
            mainPanel.Controls.Add(testTextBox);
            mainPanel.Controls.Add(statusLabel);
            tab.AddControl(mainPanel);
        }

        private void TestEvents(RJTabControl tabControl)
        {
            var tab = tabControl.AddTab("الأحداث", IconChar.Bolt);
            tab.BackColor = Color.FromArgb(0, 150, 136);
            tab.ForeColor = Color.White;

            var eventLog = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "✅ اختبار الأحداث:\n\n",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(0, 150, 136),
                BorderRadius = 8,
                Font = new Font("Consolas", 10),
                TextAlign = HorizontalAlignment.Left
            };

            // ربط الأحداث
            tabControl.TabChanged += (sender, e) => {
                eventLog.Text += $"🔄 TabChanged: {e.PreviousTab?.Text} → {e.CurrentTab?.Text}\n";
            };

            tabControl.TabAdded += (sender, e) => {
                eventLog.Text += $"➕ TabAdded: {e.Tab.Text}\n";
            };

            tabControl.TabRemoved += (sender, e) => {
                eventLog.Text += $"➖ TabRemoved: {e.Tab.Text}\n";
            };

            eventLog.Text += "الأحداث مربوطة ومستعدة للعمل!\n\n";
            eventLog.Text += "جرب التنقل بين التابات لرؤية الأحداث...\n\n";

            tab.AddControl(eventLog);
        }

        /// <summary>
        /// تشغيل اختبار التحقق النهائي
        /// </summary>
        public static void RunValidation()
        {
            try
            {
                var form = new FinalValidationTest();
                form.ShowDialog();
                
                MessageBox.Show("🎉 جميع الاختبارات نجحت!\n\n" +
                               "✅ طرق إنشاء التابات الثلاث\n" +
                               "✅ جميع خصائص RJButton\n" +
                               "✅ خصائص Designer\n" +
                               "✅ RJPanel و RJTextBox المحدثة\n" +
                               "✅ جميع الأحداث\n\n" +
                               "🚀 RJTabControl جاهز للاستخدام!",
                               "نتيجة الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الاختبار:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
