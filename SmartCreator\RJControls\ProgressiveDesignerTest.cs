using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار تدريجي لإيجاد مشكلة Designer
    /// </summary>
    public partial class ProgressiveDesignerTest : Form
    {
        private SafeRJTabControl progressiveTabControl;

        public ProgressiveDesignerTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // progressiveTabControl
            // 
            this.progressiveTabControl = new SafeRJTabControl();
            this.progressiveTabControl.Dock = DockStyle.Fill;
            this.progressiveTabControl.TabHeight = 45;
            this.progressiveTabControl.TabSpacing = 3;
            this.progressiveTabControl.TabPadding = 20;

            // 
            // ProgressiveDesignerTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(900, 700);
            this.Controls.Add(this.progressiveTabControl);
            this.Name = "ProgressiveDesignerTest";
            this.Text = "🔍 اختبار تدريجي - SafeRJTabControl مع Collection";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += ProgressiveDesignerTest_Load;
        }

        private void ProgressiveDesignerTest_Load(object sender, EventArgs e)
        {
            try
            {
                // اختبار Collection
                TestCollection();

                // إضافة تابات تجريبية
                AddProgressiveTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ Collection يعمل!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ Collection فشل!";
            }
        }

        private void TestCollection()
        {
            // اختبار خصائص Collection
            var collection = this.progressiveTabControl.Tabs;
            var count = collection.Count;

            // اختبار إضافة تاب عبر Collection
            var testTab = new RJTabPage("تاب Collection");
            testTab.BackColor = Color.FromArgb(255, 152, 0);
            testTab.ForeColor = Color.White;

            var testLabel = new Label
            {
                Text = "🧪 تم إنشاء هذا التاب عبر Collection!\n\n" +
                       "✅ SimpleTabCollection يعمل\n" +
                       "✅ يمكن إضافة التابات عبر Collection\n" +
                       "✅ الخصائص متاحة في Properties\n\n" +
                       "🎯 الآن جرب سحب SafeRJTabControl\n" +
                       "من Toolbox مرة أخرى!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White
            };
            testTab.AddControl(testLabel);

            // إضافة عبر Collection
            collection.Add(testTab);
        }

        private void AddProgressiveTabs()
        {
            // تاب معلومات Collection
            var infoTab = this.progressiveTabControl.AddTab("معلومات Collection");
            infoTab.BackColor = Color.FromArgb(63, 81, 181);
            infoTab.ForeColor = Color.White;

            var infoPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var collectionInfoLabel = new Label
            {
                Text = "📊 معلومات SimpleTabCollection:\n\n" +
                       $"• عدد التابات: {this.progressiveTabControl.Tabs.Count}\n" +
                       $"• التاب النشط: {this.progressiveTabControl.SelectedIndex}\n" +
                       $"• ارتفاع التابات: {this.progressiveTabControl.TabHeight}\n" +
                       $"• المسافة بين التابات: {this.progressiveTabControl.TabSpacing}\n\n" +
                       "🔍 الميزات المضافة:\n" +
                       "• SimpleTabCollection class\n" +
                       "• خاصية Tabs مرئية في Properties\n" +
                       "• DesignerSerializationVisibility.Content\n" +
                       "• طرق Add/Remove/Clear\n\n" +
                       "🎯 إذا عمل هذا في Designer:\n" +
                       "فالمشكلة ليست في Collection البسيط\n" +
                       "بل في Collection Editor المعقد",
                Location = new Point(0, 0),
                Size = new Size(800, 300),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            var testCollectionButton = new RJButton
            {
                Text = "اختبار Collection",
                IconChar = IconChar.List,
                Location = new Point(20, 320),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            testCollectionButton.Click += TestCollectionButton_Click;

            var addViaCollectionButton = new RJButton
            {
                Text = "إضافة عبر Collection",
                IconChar = IconChar.Plus,
                Location = new Point(240, 320),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            addViaCollectionButton.Click += AddViaCollectionButton_Click;

            var clearCollectionButton = new RJButton
            {
                Text = "مسح Collection",
                IconChar = IconChar.Trash,
                Location = new Point(460, 320),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            clearCollectionButton.Click += (s, e) => {
                this.progressiveTabControl.Tabs.Clear();
                AddProgressiveTabs(); // إعادة إضافة التابات الأساسية
            };

            infoPanel.Controls.Add(collectionInfoLabel);
            infoPanel.Controls.Add(testCollectionButton);
            infoPanel.Controls.Add(addViaCollectionButton);
            infoPanel.Controls.Add(clearCollectionButton);
            infoTab.AddControl(infoPanel);

            // تاب التعليمات
            var instructionsTab = this.progressiveTabControl.AddTab("التعليمات");
            instructionsTab.BackColor = Color.FromArgb(0, 150, 136);
            instructionsTab.ForeColor = Color.White;

            var instructionsTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "📋 تعليمات الاختبار التدريجي:\n\n" +
                       "🎯 الهدف:\n" +
                       "تحديد الميزة المحددة التي تسبب مشكلة Designer\n\n" +
                       "✅ تم إضافة حتى الآن:\n" +
                       "• SimpleTabCollection class\n" +
                       "• خاصية Tabs مع DesignerSerializationVisibility\n" +
                       "• طرق Add/Remove/Clear للـ Collection\n" +
                       "• AddTabInternal method\n\n" +
                       "🧪 خطوات الاختبار:\n" +
                       "1. جرب سحب SafeRJTabControl من Toolbox\n" +
                       "2. تحقق من Properties Panel\n" +
                       "3. ابحث عن خاصية Tabs\n" +
                       "4. جرب النقر على [...] بجانب Tabs\n\n" +
                       "📊 النتائج المتوقعة:\n" +
                       "• إذا عمل: المشكلة في Collection Editor\n" +
                       "• إذا لم يعمل: المشكلة في Collection نفسه\n\n" +
                       "🔍 الخطوة التالية:\n" +
                       "بناءً على النتيجة، سنضيف أو نزيل ميزات\n" +
                       "حتى نجد السبب الدقيق للمشكلة\n\n" +
                       "⚠️ ملاحظة مهمة:\n" +
                       "إذا فشل SafeRJTabControl الآن في Designer\n" +
                       "فالمشكلة في SimpleTabCollection أو\n" +
                       "في DesignerSerializationVisibility.Content",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(0, 150, 136),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            instructionsTab.AddControl(instructionsTextBox);

            // تفعيل التاب الأول
            this.progressiveTabControl.SelectedIndex = 0;
        }

        private void TestCollectionButton_Click(object sender, EventArgs e)
        {
            try
            {
                var collection = this.progressiveTabControl.Tabs;
                var results = $"🧪 نتائج اختبار Collection:\n\n" +
                             $"Count: {collection.Count}\n" +
                             $"First Tab: {collection[0]?.Text ?? "null"}\n" +
                             $"Last Tab: {collection[collection.Count - 1]?.Text ?? "null"}\n\n" +
                             "✅ جميع طرق Collection تعمل بمثالية!";

                MessageBox.Show(results, "نتائج الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void AddViaCollectionButton_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = new RJTabPage($"Collection Tab {this.progressiveTabControl.Tabs.Count + 1}");
                newTab.BackColor = Color.FromArgb(233, 30, 99);
                newTab.ForeColor = Color.White;

                var label = new Label
                {
                    Text = $"🌟 تاب جديد عبر Collection!\n\n" +
                           $"رقم التاب: {this.progressiveTabControl.Tabs.Count + 1}\n" +
                           $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ إضافة التابات عبر Collection تعمل!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                newTab.AddControl(label);

                // إضافة عبر Collection
                this.progressiveTabControl.Tabs.Add(newTab);

                // تفعيل التاب الجديد
                this.progressiveTabControl.SelectedIndex = this.progressiveTabControl.Tabs.Count - 1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب عبر Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل الاختبار التدريجي
        /// </summary>
        public static void RunProgressiveTest()
        {
            try
            {
                var form = new ProgressiveDesignerTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل الاختبار التدريجي:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
