using System;
using System.ComponentModel;
using System.Drawing;

namespace SmartCreator.RJControls.Design
{
    /// <summary>
    /// أنماط التابات المختلفة
    /// </summary>
    public enum TabStyleType
    {
        /// <summary>
        /// النمط الكلاسيكي - مثل Windows
        /// </summary>
        Classic,

        /// <summary>
        /// النمط الحديث - مسطح
        /// </summary>
        Modern,

        /// <summary>
        /// نمط Chrome - مدور
        /// </summary>
        Chrome,

        /// <summary>
        /// نمط VS Code - مربع
        /// </summary>
        VSCode,

        /// <summary>
        /// نمط مخصص
        /// </summary>
        Custom
    }

    /// <summary>
    /// موقع التابات
    /// </summary>
    public enum TabPosition
    {
        /// <summary>
        /// في الأعلى
        /// </summary>
        Top,

        /// <summary>
        /// في الأسفل
        /// </summary>
        Bottom,

        /// <summary>
        /// على اليسار
        /// </summary>
        Left,

        /// <summary>
        /// على اليمين
        /// </summary>
        Right
    }

    /// <summary>
    /// كلاس إعدادات نمط التابات
    /// </summary>
    [TypeConverter(typeof(ExpandableObjectConverter))]
    public class TabStyle
    {
        #region Fields
        private TabStyleType styleType = TabStyleType.Modern;
        private TabPosition position = TabPosition.Top;
        private Color activeBackColor = Color.FromArgb(0, 122, 204);
        private Color inactiveBackColor = Color.FromArgb(240, 240, 240);
        private Color activeForeColor = Color.White;
        private Color inactiveForeColor = Color.FromArgb(70, 70, 70);
        private Color borderColor = Color.FromArgb(200, 200, 200);
        private Color hoverBackColor = Color.FromArgb(230, 230, 230);
        private int borderRadius = 8;
        private int borderSize = 1;
        private bool showBorder = true;
        private bool showShadow = false;
        private Font font = new Font("Segoe UI", 9F);
        #endregion

        #region Properties
        /// <summary>
        /// نوع النمط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tab style type")]
        [DefaultValue(TabStyleType.Modern)]
        public TabStyleType StyleType
        {
            get { return styleType; }
            set { styleType = value; }
        }

        /// <summary>
        /// موقع التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tab position")]
        [DefaultValue(TabPosition.Top)]
        public TabPosition Position
        {
            get { return position; }
            set { position = value; }
        }

        /// <summary>
        /// لون خلفية التاب النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the active tab background color")]
        public Color ActiveBackColor
        {
            get { return activeBackColor; }
            set { activeBackColor = value; }
        }

        /// <summary>
        /// لون خلفية التاب غير النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the inactive tab background color")]
        public Color InactiveBackColor
        {
            get { return inactiveBackColor; }
            set { inactiveBackColor = value; }
        }

        /// <summary>
        /// لون نص التاب النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the active tab text color")]
        public Color ActiveForeColor
        {
            get { return activeForeColor; }
            set { activeForeColor = value; }
        }

        /// <summary>
        /// لون نص التاب غير النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the inactive tab text color")]
        public Color InactiveForeColor
        {
            get { return inactiveForeColor; }
            set { inactiveForeColor = value; }
        }

        /// <summary>
        /// لون الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the border color")]
        public Color BorderColor
        {
            get { return borderColor; }
            set { borderColor = value; }
        }

        /// <summary>
        /// لون الخلفية عند التمرير
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the hover background color")]
        public Color HoverBackColor
        {
            get { return hoverBackColor; }
            set { hoverBackColor = value; }
        }

        /// <summary>
        /// نصف قطر الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the border radius")]
        [DefaultValue(8)]
        public int BorderRadius
        {
            get { return borderRadius; }
            set { borderRadius = value; }
        }

        /// <summary>
        /// سمك الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the border size")]
        [DefaultValue(1)]
        public int BorderSize
        {
            get { return borderSize; }
            set { borderSize = value; }
        }

        /// <summary>
        /// عرض الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether to show border")]
        [DefaultValue(true)]
        public bool ShowBorder
        {
            get { return showBorder; }
            set { showBorder = value; }
        }

        /// <summary>
        /// عرض الظل
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether to show shadow")]
        [DefaultValue(false)]
        public bool ShowShadow
        {
            get { return showShadow; }
            set { showShadow = value; }
        }

        /// <summary>
        /// خط النص
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the font")]
        public Font Font
        {
            get { return font; }
            set { font = value; }
        }
        #endregion

        #region Methods
        /// <summary>
        /// تطبيق النمط على RJButton (التاب)
        /// </summary>
        public void ApplyToTab(RJButton tab, bool isActive)
        {
            if (tab == null) return;

            // تطبيق الألوان
            tab.BackColor = isActive ? activeBackColor : inactiveBackColor;
            tab.ForeColor = isActive ? activeForeColor : inactiveForeColor;

            // تطبيق الحدود
            if (showBorder)
            {
                tab.BorderColor = borderColor;
                tab.BorderSize = borderSize;
                tab.BorderRadius = borderRadius;
            }
            else
            {
                tab.BorderSize = 0;
            }

            // تطبيق الخط
            tab.Font = font;

            // تطبيق النمط حسب النوع
            switch (styleType)
            {
                case TabStyleType.Classic:
                    ApplyClassicStyle(tab, isActive);
                    break;
                case TabStyleType.Modern:
                    ApplyModernStyle(tab, isActive);
                    break;
                case TabStyleType.Chrome:
                    ApplyChromeStyle(tab, isActive);
                    break;
                case TabStyleType.VSCode:
                    ApplyVSCodeStyle(tab, isActive);
                    break;
            }
        }

        /// <summary>
        /// تطبيق النمط الكلاسيكي
        /// </summary>
        private void ApplyClassicStyle(RJButton tab, bool isActive)
        {
            tab.Style = isActive ? ControlStyle.Solid : ControlStyle.Glass;
            tab.BorderRadius = 4;
        }

        /// <summary>
        /// تطبيق النمط الحديث
        /// </summary>
        private void ApplyModernStyle(RJButton tab, bool isActive)
        {
            tab.Style = isActive ? ControlStyle.Solid : ControlStyle.Glass;
            tab.BorderRadius = borderRadius;
        }

        /// <summary>
        /// تطبيق نمط Chrome
        /// </summary>
        private void ApplyChromeStyle(RJButton tab, bool isActive)
        {
            tab.Style = ControlStyle.Solid;
            tab.BorderRadius = 12;
            tab.BorderSize = 0;
        }

        /// <summary>
        /// تطبيق نمط VS Code
        /// </summary>
        private void ApplyVSCodeStyle(RJButton tab, bool isActive)
        {
            tab.Style = ControlStyle.Solid;
            tab.BorderRadius = 0;
            tab.BorderSize = isActive ? 2 : 0;
        }

        /// <summary>
        /// إنشاء نسخة من النمط
        /// </summary>
        public TabStyle Clone()
        {
            return new TabStyle
            {
                StyleType = this.styleType,
                Position = this.position,
                ActiveBackColor = this.activeBackColor,
                InactiveBackColor = this.inactiveBackColor,
                ActiveForeColor = this.activeForeColor,
                InactiveForeColor = this.inactiveForeColor,
                BorderColor = this.borderColor,
                HoverBackColor = this.hoverBackColor,
                BorderRadius = this.borderRadius,
                BorderSize = this.borderSize,
                ShowBorder = this.showBorder,
                ShowShadow = this.showShadow,
                Font = new Font(this.font, this.font.Style)
            };
        }
        #endregion

        #region Predefined Styles
        /// <summary>
        /// النمط الافتراضي
        /// </summary>
        public static TabStyle Default => new TabStyle();

        /// <summary>
        /// نمط أزرق
        /// </summary>
        public static TabStyle Blue => new TabStyle
        {
            ActiveBackColor = Color.FromArgb(0, 122, 204),
            ActiveForeColor = Color.White,
            InactiveBackColor = Color.FromArgb(240, 240, 240),
            InactiveForeColor = Color.FromArgb(70, 70, 70)
        };

        /// <summary>
        /// نمط أخضر
        /// </summary>
        public static TabStyle Green => new TabStyle
        {
            ActiveBackColor = Color.FromArgb(76, 175, 80),
            ActiveForeColor = Color.White,
            InactiveBackColor = Color.FromArgb(240, 240, 240),
            InactiveForeColor = Color.FromArgb(70, 70, 70)
        };

        /// <summary>
        /// نمط داكن
        /// </summary>
        public static TabStyle Dark => new TabStyle
        {
            ActiveBackColor = Color.FromArgb(45, 45, 48),
            ActiveForeColor = Color.White,
            InactiveBackColor = Color.FromArgb(60, 60, 60),
            InactiveForeColor = Color.FromArgb(200, 200, 200),
            BorderColor = Color.FromArgb(80, 80, 80)
        };
        #endregion
    }
}
