﻿namespace SmartCreator.Forms.UserManager.Reports
{
    partial class Form_UM_Users_Devices
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle21 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle22 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle23 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle24 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle17 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle18 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle19 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle20 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel_topFilter = new SmartCreator.RJControls.RJPanel();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.rjCheckBox1 = new SmartCreator.RJControls.RJCheckBox();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.CBox_SearchBy = new SmartCreator.RJControls.RJComboBox();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.check_with_Commi = new SmartCreator.RJControls.RJCheckBox();
            this.rjDateTime_From = new SmartCreator.RJControls.RJDatePicker();
            this.rjDateTime_To = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_To_Date = new SmartCreator.RJControls.RJCheckBox();
            this.btn_ = new SmartCreator.RJControls.RJButton();
            this.rjButton3 = new SmartCreator.RJControls.RJButton();
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.rjPanel5 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel26 = new SmartCreator.RJControls.RJLabel();
            this.Radio_By_Mac = new SmartCreator.RJControls.RJToggleButton();
            this.Radio_By_IP = new SmartCreator.RJControls.RJToggleButton();
            this.Radio_By_Cards = new SmartCreator.RJControls.RJToggleButton();
            this.rjPanel4 = new SmartCreator.RJControls.RJPanel();
            this.txt_Download = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel22 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel25 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel20 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.txt_uptime = new SmartCreator.RJControls.RJTextBox();
            this.txt_sum_Session = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel21 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel24 = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_FromSession = new SmartCreator.RJControls.RJCheckBox();
            this.Spanel = new SmartCreator.RJControls.RJPanel();
            this.panel1_side = new System.Windows.Forms.Panel();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.CBox_Port = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.panel3_side = new System.Windows.Forms.Panel();
            this.panel2_side = new System.Windows.Forms.Panel();
            this.pnl_side_sn = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.CBox_Customer = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Radius = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.rjButton4 = new SmartCreator.RJControls.RJButton();
            this.btn_Fix = new SmartCreator.RJControls.RJButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dgv2 = new SmartCreator.RJControls.RJDataGridView();
            this.rjPanel12 = new SmartCreator.RJControls.RJPanel();
            this.txt_sum_Sales = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel23 = new SmartCreator.RJControls.RJLabel();
            this.rjToggleButton1 = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Detail = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Monthly = new SmartCreator.RJControls.RJToggleButton();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.txt_count_Cards = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.rjTextBox1 = new SmartCreator.RJControls.RJTextBox();
            this.lbl_Sub_title = new SmartCreator.RJControls.RJLabel();
            this.Cbox_View = new SmartCreator.RJControls.RJComboBox();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.PageNumber_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.LastSynDb_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CusName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Descr_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTransferLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTimeLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTillTime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_FirstUse_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_LastSeenAt_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_RegDate_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_Up_Down_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UploadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_DownloadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_TransferLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BachCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SellingPoint_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.Password_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.UserName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SN_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Status_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Restor_ColumnToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_AllRowToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel_topFilter.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel5.SuspendLayout();
            this.rjPanel4.SuspendLayout();
            this.Spanel.SuspendLayout();
            this.pnl_side_sn.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).BeginInit();
            this.rjPanel12.SuspendLayout();
            this.dmAll_Cards.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel4);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.Spanel);
            this.pnlClientArea.Controls.Add(this.Cbox_View);
            this.pnlClientArea.Controls.Add(this.rjPanel_topFilter);
            this.pnlClientArea.Controls.Add(this.dgv2);
            this.pnlClientArea.Controls.Add(this.rjPanel12);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 700);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(153, 17);
            this.lblCaption.Text = "Form_UM_Users_Devices";
            // 
            // rjPanel_topFilter
            // 
            this.rjPanel_topFilter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_topFilter.BorderRadius = 10;
            this.rjPanel_topFilter.Controls.Add(this.btn_Refresh);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel2);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel1);
            this.rjPanel_topFilter.Controls.Add(this.btn_);
            this.rjPanel_topFilter.Controls.Add(this.rjButton3);
            this.rjPanel_topFilter.Controls.Add(this.btn_Filter);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel5);
            this.rjPanel_topFilter.Customizable = false;
            this.rjPanel_topFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjPanel_topFilter.Location = new System.Drawing.Point(0, 0);
            this.rjPanel_topFilter.Margin = new System.Windows.Forms.Padding(1, 0, 0, 0);
            this.rjPanel_topFilter.Name = "rjPanel_topFilter";
            this.rjPanel_topFilter.Size = new System.Drawing.Size(990, 143);
            this.rjPanel_topFilter.TabIndex = 82;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(6, 8);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(35, 38);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 50;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel2.BorderRadius = 0;
            this.rjPanel2.Controls.Add(this.rjCheckBox1);
            this.rjPanel2.Controls.Add(this.btn_search);
            this.rjPanel2.Controls.Add(this.CBox_SearchBy);
            this.rjPanel2.Controls.Add(this.txt_search);
            this.rjPanel2.Customizable = true;
            this.rjPanel2.Location = new System.Drawing.Point(645, 55);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(326, 80);
            this.rjPanel2.TabIndex = 89;
            // 
            // rjCheckBox1
            // 
            this.rjCheckBox1.AutoSize = true;
            this.rjCheckBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox1.BorderSize = 1;
            this.rjCheckBox1.Check = false;
            this.rjCheckBox1.CheckAlign = System.Drawing.ContentAlignment.BottomRight;
            this.rjCheckBox1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjCheckBox1.Customizable = false;
            this.rjCheckBox1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjCheckBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjCheckBox1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjCheckBox1.Location = new System.Drawing.Point(90, 49);
            this.rjCheckBox1.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjCheckBox1.Name = "rjCheckBox1";
            this.rjCheckBox1.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.rjCheckBox1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjCheckBox1.Size = new System.Drawing.Size(124, 21);
            this.rjCheckBox1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjCheckBox1.TabIndex = 52;
            this.rjCheckBox1.Text = "من بداية الفترة";
            this.rjCheckBox1.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjCheckBox1.UseVisualStyleBackColor = true;
            this.rjCheckBox1.Visible = false;
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 5;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(152, 8);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 28);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 95;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            // 
            // CBox_SearchBy
            // 
            this.CBox_SearchBy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SearchBy.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SearchBy.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SearchBy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SearchBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.BorderRadius = 5;
            this.CBox_SearchBy.BorderSize = 1;
            this.CBox_SearchBy.Customizable = false;
            this.CBox_SearchBy.DataSource = null;
            this.CBox_SearchBy.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SearchBy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SearchBy.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.Enabled = false;
            this.CBox_SearchBy.Font = new System.Drawing.Font("Droid Sans Arabic", 7.8F);
            this.CBox_SearchBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SearchBy.Items.AddRange(new object[] {
            "ماك الجهاز",
            "ايبي الجهاز",
            "الاسم"});
            this.CBox_SearchBy.Location = new System.Drawing.Point(3, 7);
            this.CBox_SearchBy.Name = "CBox_SearchBy";
            this.CBox_SearchBy.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SearchBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SearchBy.SelectedIndex = -1;
            this.CBox_SearchBy.Size = new System.Drawing.Size(143, 32);
            this.CBox_SearchBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SearchBy.TabIndex = 96;
            this.CBox_SearchBy.Texts = "";
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(177, 8);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(146, 28);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 94;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel1.BorderRadius = 5;
            this.rjPanel1.Controls.Add(this.rjLabel2);
            this.rjPanel1.Controls.Add(this.check_with_Commi);
            this.rjPanel1.Controls.Add(this.rjDateTime_From);
            this.rjPanel1.Controls.Add(this.rjDateTime_To);
            this.rjPanel1.Controls.Add(this.CheckBox_To_Date);
            this.rjPanel1.Customizable = true;
            this.rjPanel1.Location = new System.Drawing.Point(247, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(730, 45);
            this.rjPanel1.TabIndex = 72;
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(685, 12);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(23, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 103;
            this.rjLabel2.Text = "من";
            // 
            // check_with_Commi
            // 
            this.check_with_Commi.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.check_with_Commi.AutoSize = true;
            this.check_with_Commi.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.BorderSize = 1;
            this.check_with_Commi.Check = false;
            this.check_with_Commi.Cursor = System.Windows.Forms.Cursors.Hand;
            this.check_with_Commi.Customizable = false;
            this.check_with_Commi.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.check_with_Commi.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.check_with_Commi.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.Location = new System.Drawing.Point(22, 8);
            this.check_with_Commi.MinimumSize = new System.Drawing.Size(0, 21);
            this.check_with_Commi.Name = "check_with_Commi";
            this.check_with_Commi.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.check_with_Commi.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.check_with_Commi.Size = new System.Drawing.Size(116, 24);
            this.check_with_Commi.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.check_with_Commi.TabIndex = 95;
            this.check_with_Commi.Text = "خصم العمولات";
            this.check_with_Commi.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.check_with_Commi.UseVisualStyleBackColor = true;
            // 
            // rjDateTime_From
            // 
            this.rjDateTime_From.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjDateTime_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjDateTime_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjDateTime_From.BorderRadius = 7;
            this.rjDateTime_From.BorderSize = 1;
            this.rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_From.Customizable = false;
            this.rjDateTime_From.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjDateTime_From.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_From.Location = new System.Drawing.Point(454, 5);
            this.rjDateTime_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_From.Name = "rjDateTime_From";
            this.rjDateTime_From.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_From.Size = new System.Drawing.Size(270, 34);
            this.rjDateTime_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjDateTime_From.TabIndex = 79;
            this.rjDateTime_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            // 
            // rjDateTime_To
            // 
            this.rjDateTime_To.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjDateTime_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjDateTime_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjDateTime_To.BorderRadius = 7;
            this.rjDateTime_To.BorderSize = 1;
            this.rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_To.Customizable = false;
            this.rjDateTime_To.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjDateTime_To.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_To.Location = new System.Drawing.Point(148, 5);
            this.rjDateTime_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_To.Name = "rjDateTime_To";
            this.rjDateTime_To.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_To.Size = new System.Drawing.Size(247, 34);
            this.rjDateTime_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjDateTime_To.TabIndex = 80;
            this.rjDateTime_To.Value = new System.DateTime(2024, 9, 27, 23, 59, 59, 0);
            // 
            // CheckBox_To_Date
            // 
            this.CheckBox_To_Date.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_To_Date.AutoSize = true;
            this.CheckBox_To_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.BorderSize = 1;
            this.CheckBox_To_Date.Check = true;
            this.CheckBox_To_Date.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.Checked = true;
            this.CheckBox_To_Date.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_To_Date.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_To_Date.Customizable = false;
            this.CheckBox_To_Date.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.CheckBox_To_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_To_Date.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.Location = new System.Drawing.Point(382, 7);
            this.CheckBox_To_Date.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_To_Date.Name = "CheckBox_To_Date";
            this.CheckBox_To_Date.Padding = new System.Windows.Forms.Padding(0, 0, 18, 0);
            this.CheckBox_To_Date.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_To_Date.Size = new System.Drawing.Size(66, 26);
            this.CheckBox_To_Date.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_To_Date.TabIndex = 102;
            this.CheckBox_To_Date.Text = "الى";
            this.CheckBox_To_Date.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.UseVisualStyleBackColor = true;
            // 
            // btn_
            // 
            this.btn_.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.BorderRadius = 4;
            this.btn_.BorderSize = 1;
            this.btn_.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_.FlatAppearance.BorderSize = 0;
            this.btn_.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_.IconSize = 2;
            this.btn_.Location = new System.Drawing.Point(156, 8);
            this.btn_.Margin = new System.Windows.Forms.Padding(0);
            this.btn_.Name = "btn_";
            this.btn_.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_.Size = new System.Drawing.Size(83, 38);
            this.btn_.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_.TabIndex = 56;
            this.btn_.Text = "عــرض";
            this.btn_.UseVisualStyleBackColor = false;
            this.btn_.Click += new System.EventHandler(this.btn__Click);
            // 
            // rjButton3
            // 
            this.rjButton3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderRadius = 4;
            this.rjButton3.BorderSize = 1;
            this.rjButton3.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton3.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton3.FlatAppearance.BorderSize = 0;
            this.rjButton3.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton3.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton3.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton3.IconSize = 25;
            this.rjButton3.Location = new System.Drawing.Point(42, 8);
            this.rjButton3.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton3.Name = "rjButton3";
            this.rjButton3.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton3.Size = new System.Drawing.Size(35, 38);
            this.rjButton3.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton3.TabIndex = 49;
            this.rjButton3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton3.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton3.UseVisualStyleBackColor = false;
            this.rjButton3.Click += new System.EventHandler(this.rjButton3_Click);
            // 
            // btn_Filter
            // 
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 17;
            this.btn_Filter.Location = new System.Drawing.Point(78, 8);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Filter.Size = new System.Drawing.Size(86, 38);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Filter.TabIndex = 73;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // rjPanel5
            // 
            this.rjPanel5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel5.BorderRadius = 5;
            this.rjPanel5.Controls.Add(this.rjLabel26);
            this.rjPanel5.Controls.Add(this.Radio_By_Mac);
            this.rjPanel5.Controls.Add(this.Radio_By_IP);
            this.rjPanel5.Controls.Add(this.Radio_By_Cards);
            this.rjPanel5.Customizable = true;
            this.rjPanel5.Location = new System.Drawing.Point(9, 55);
            this.rjPanel5.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel5.Name = "rjPanel5";
            this.rjPanel5.Size = new System.Drawing.Size(262, 80);
            this.rjPanel5.TabIndex = 101;
            // 
            // rjLabel26
            // 
            this.rjLabel26.AutoSize = true;
            this.rjLabel26.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel26.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel26.LinkLabel = false;
            this.rjLabel26.Location = new System.Drawing.Point(157, 9);
            this.rjLabel26.Name = "rjLabel26";
            this.rjLabel26.Size = new System.Drawing.Size(76, 17);
            this.rjLabel26.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel26.TabIndex = 53;
            this.rjLabel26.Text = "عرض بواسطة";
            // 
            // Radio_By_Mac
            // 
            this.Radio_By_Mac.Activated = true;
            this.Radio_By_Mac.Checked = true;
            this.Radio_By_Mac.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Radio_By_Mac.Customizable = false;
            this.Radio_By_Mac.Enabled = false;
            this.Radio_By_Mac.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_By_Mac.Location = new System.Drawing.Point(6, 6);
            this.Radio_By_Mac.MinimumSize = new System.Drawing.Size(50, 25);
            this.Radio_By_Mac.Name = "Radio_By_Mac";
            this.Radio_By_Mac.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_Mac.OFF_Text = "ماك الجهاز";
            this.Radio_By_Mac.OFF_TextColor = System.Drawing.Color.Gray;
            this.Radio_By_Mac.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_Mac.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_Mac.ON_Text = "ماك الجهاز";
            this.Radio_By_Mac.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_By_Mac.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_Mac.Size = new System.Drawing.Size(123, 25);
            this.Radio_By_Mac.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Radio_By_Mac.TabIndex = 52;
            this.Radio_By_Mac.Tag = "تفصيلي";
            this.Radio_By_Mac.Text = "#";
            this.Radio_By_Mac.UseVisualStyleBackColor = true;
            this.Radio_By_Mac.CheckedChanged += new System.EventHandler(this.Radio_By_Mac_CheckedChanged);
            // 
            // Radio_By_IP
            // 
            this.Radio_By_IP.Activated = false;
            this.Radio_By_IP.Customizable = false;
            this.Radio_By_IP.Enabled = false;
            this.Radio_By_IP.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_By_IP.Location = new System.Drawing.Point(135, 37);
            this.Radio_By_IP.MinimumSize = new System.Drawing.Size(50, 25);
            this.Radio_By_IP.Name = "Radio_By_IP";
            this.Radio_By_IP.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_IP.OFF_Text = "ايبي الجهاز";
            this.Radio_By_IP.OFF_TextColor = System.Drawing.Color.Gray;
            this.Radio_By_IP.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_IP.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_IP.ON_Text = "ايبي الجهاز";
            this.Radio_By_IP.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_By_IP.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_IP.Size = new System.Drawing.Size(123, 25);
            this.Radio_By_IP.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Radio_By_IP.TabIndex = 52;
            this.Radio_By_IP.Tag = "تفصيلي";
            this.Radio_By_IP.Text = "#";
            this.Radio_By_IP.UseVisualStyleBackColor = true;
            this.Radio_By_IP.CheckedChanged += new System.EventHandler(this.Radio_By_IP_CheckedChanged);
            // 
            // Radio_By_Cards
            // 
            this.Radio_By_Cards.Activated = false;
            this.Radio_By_Cards.Customizable = false;
            this.Radio_By_Cards.Enabled = false;
            this.Radio_By_Cards.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_By_Cards.Location = new System.Drawing.Point(6, 37);
            this.Radio_By_Cards.MinimumSize = new System.Drawing.Size(50, 25);
            this.Radio_By_Cards.Name = "Radio_By_Cards";
            this.Radio_By_Cards.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_Cards.OFF_Text = "كرت العميل";
            this.Radio_By_Cards.OFF_TextColor = System.Drawing.Color.Gray;
            this.Radio_By_Cards.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Radio_By_Cards.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_Cards.ON_Text = "كرت العميل";
            this.Radio_By_Cards.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_By_Cards.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_By_Cards.Size = new System.Drawing.Size(123, 25);
            this.Radio_By_Cards.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Radio_By_Cards.TabIndex = 52;
            this.Radio_By_Cards.Tag = "تفصيلي";
            this.Radio_By_Cards.Text = "#";
            this.Radio_By_Cards.UseVisualStyleBackColor = true;
            this.Radio_By_Cards.CheckedChanged += new System.EventHandler(this.Radio_By_Cards_CheckedChanged);
            // 
            // rjPanel4
            // 
            this.rjPanel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel4.BorderRadius = 0;
            this.rjPanel4.Controls.Add(this.txt_Download);
            this.rjPanel4.Controls.Add(this.rjLabel22);
            this.rjPanel4.Controls.Add(this.rjLabel25);
            this.rjPanel4.Controls.Add(this.rjLabel20);
            this.rjPanel4.Controls.Add(this.rjLabel7);
            this.rjPanel4.Controls.Add(this.txt_uptime);
            this.rjPanel4.Controls.Add(this.txt_sum_Session);
            this.rjPanel4.Controls.Add(this.rjLabel18);
            this.rjPanel4.Controls.Add(this.rjLabel21);
            this.rjPanel4.Controls.Add(this.rjLabel3);
            this.rjPanel4.Controls.Add(this.rjLabel24);
            this.rjPanel4.Customizable = true;
            this.rjPanel4.Location = new System.Drawing.Point(93, 225);
            this.rjPanel4.Name = "rjPanel4";
            this.rjPanel4.Size = new System.Drawing.Size(383, 128);
            this.rjPanel4.TabIndex = 98;
            this.rjPanel4.Visible = false;
            // 
            // txt_Download
            // 
            this.txt_Download._Customizable = false;
            this.txt_Download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_Download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Download.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Download.BorderRadius = 3;
            this.txt_Download.BorderSize = 1;
            this.txt_Download.Enabled = false;
            this.txt_Download.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_Download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Download.Location = new System.Drawing.Point(129, 89);
            this.txt_Download.MultiLine = false;
            this.txt_Download.Name = "txt_Download";
            this.txt_Download.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Download.PasswordChar = false;
            this.txt_Download.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Download.PlaceHolderText = null;
            this.txt_Download.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Download.Size = new System.Drawing.Size(123, 25);
            this.txt_Download.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_Download.TabIndex = 91;
            this.txt_Download.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_Download.Visible = false;
            // 
            // rjLabel22
            // 
            this.rjLabel22.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel22.AutoSize = true;
            this.rjLabel22.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel22.Enabled = false;
            this.rjLabel22.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel22.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel22.LinkLabel = false;
            this.rjLabel22.Location = new System.Drawing.Point(129, 69);
            this.rjLabel22.Name = "rjLabel22";
            this.rjLabel22.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel22.Size = new System.Drawing.Size(101, 17);
            this.rjLabel22.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel22.TabIndex = 93;
            this.rjLabel22.Text = "اجمالي الاستهلاك";
            this.rjLabel22.Visible = false;
            // 
            // rjLabel25
            // 
            this.rjLabel25.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25.AutoSize = true;
            this.rjLabel25.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel25.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel25.LinkLabel = false;
            this.rjLabel25.Location = new System.Drawing.Point(30, 26);
            this.rjLabel25.Name = "rjLabel25";
            this.rjLabel25.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel25.Size = new System.Drawing.Size(250, 17);
            this.rjLabel25.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel25.TabIndex = 95;
            this.rjLabel25.Text = " عرض اجمالي الاستهلاك والوقت بحسب الجلسات";
            // 
            // rjLabel20
            // 
            this.rjLabel20.AutoSize = true;
            this.rjLabel20.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel20.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel20.LinkLabel = false;
            this.rjLabel20.Location = new System.Drawing.Point(7, 3);
            this.rjLabel20.Name = "rjLabel20";
            this.rjLabel20.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel20.Size = new System.Drawing.Size(77, 23);
            this.rjLabel20.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel20.TabIndex = 95;
            this.rjLabel20.Text = "طريقة العرض";
            // 
            // rjLabel7
            // 
            this.rjLabel7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(262, 4);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(29, 22);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 50;
            this.rjLabel7.Text = "الي";
            // 
            // txt_uptime
            // 
            this.txt_uptime._Customizable = false;
            this.txt_uptime.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_uptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_uptime.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptime.BorderRadius = 3;
            this.txt_uptime.BorderSize = 1;
            this.txt_uptime.Enabled = false;
            this.txt_uptime.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptime.Location = new System.Drawing.Point(236, 89);
            this.txt_uptime.MultiLine = false;
            this.txt_uptime.Name = "txt_uptime";
            this.txt_uptime.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptime.PasswordChar = false;
            this.txt_uptime.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptime.PlaceHolderText = null;
            this.txt_uptime.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptime.Size = new System.Drawing.Size(135, 25);
            this.txt_uptime.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_uptime.TabIndex = 91;
            this.txt_uptime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_uptime.Visible = false;
            // 
            // txt_sum_Session
            // 
            this.txt_sum_Session._Customizable = false;
            this.txt_sum_Session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_sum_Session.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_sum_Session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_sum_Session.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_sum_Session.BorderRadius = 3;
            this.txt_sum_Session.BorderSize = 1;
            this.txt_sum_Session.Enabled = false;
            this.txt_sum_Session.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_sum_Session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_sum_Session.Location = new System.Drawing.Point(9, 89);
            this.txt_sum_Session.MultiLine = false;
            this.txt_sum_Session.Name = "txt_sum_Session";
            this.txt_sum_Session.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_sum_Session.PasswordChar = false;
            this.txt_sum_Session.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_sum_Session.PlaceHolderText = null;
            this.txt_sum_Session.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_sum_Session.Size = new System.Drawing.Size(112, 25);
            this.txt_sum_Session.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_sum_Session.TabIndex = 91;
            this.txt_sum_Session.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_sum_Session.Visible = false;
            // 
            // rjLabel18
            // 
            this.rjLabel18.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(175, 10);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel18.Size = new System.Drawing.Size(56, 21);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.rjLabel18.TabIndex = 97;
            this.rjLabel18.Text = "بواسطة :";
            this.rjLabel18.Visible = false;
            // 
            // rjLabel21
            // 
            this.rjLabel21.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel21.AutoSize = true;
            this.rjLabel21.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel21.Enabled = false;
            this.rjLabel21.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel21.LinkLabel = false;
            this.rjLabel21.Location = new System.Drawing.Point(287, 69);
            this.rjLabel21.Name = "rjLabel21";
            this.rjLabel21.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel21.Size = new System.Drawing.Size(80, 17);
            this.rjLabel21.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel21.TabIndex = 93;
            this.rjLabel21.Text = "اجمالي الوقت";
            this.rjLabel21.Visible = false;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(179, 4);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(88, 22);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 49;
            this.rjLabel3.Text = "من بداية الفترة";
            // 
            // rjLabel24
            // 
            this.rjLabel24.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel24.AutoSize = true;
            this.rjLabel24.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel24.Enabled = false;
            this.rjLabel24.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel24.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel24.LinkLabel = false;
            this.rjLabel24.Location = new System.Drawing.Point(39, 69);
            this.rjLabel24.Name = "rjLabel24";
            this.rjLabel24.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel24.Size = new System.Drawing.Size(71, 17);
            this.rjLabel24.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel24.TabIndex = 93;
            this.rjLabel24.Text = "عدد الجلسات";
            this.rjLabel24.Visible = false;
            // 
            // CheckBox_FromSession
            // 
            this.CheckBox_FromSession.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_FromSession.AutoSize = true;
            this.CheckBox_FromSession.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_FromSession.BorderSize = 1;
            this.CheckBox_FromSession.Check = true;
            this.CheckBox_FromSession.Checked = true;
            this.CheckBox_FromSession.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_FromSession.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_FromSession.Customizable = false;
            this.CheckBox_FromSession.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_FromSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_FromSession.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_FromSession.Location = new System.Drawing.Point(30, 28);
            this.CheckBox_FromSession.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_FromSession.Name = "CheckBox_FromSession";
            this.CheckBox_FromSession.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.CheckBox_FromSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_FromSession.Size = new System.Drawing.Size(291, 21);
            this.CheckBox_FromSession.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_FromSession.TabIndex = 52;
            this.CheckBox_FromSession.Text = " عرض اجمالي الاستهلاك والوقت بحسب الجلسات";
            this.CheckBox_FromSession.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.toolTip1.SetToolTip(this.CheckBox_FromSession, "يتم عمليه التجميع اما حسب بيانات الكرت او بحسب بيانات جلسات الماك اوا لجهاز المحد" +
        "د\r\nلان الكرت قد يستخدمه اكثر من جهاز او اكثر من ماك");
            this.CheckBox_FromSession.UseVisualStyleBackColor = true;
            this.CheckBox_FromSession.Visible = false;
            // 
            // Spanel
            // 
            this.Spanel.AutoScroll = true;
            this.Spanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Spanel.BorderRadius = 0;
            this.Spanel.Controls.Add(this.panel1_side);
            this.Spanel.Controls.Add(this.rjLabel25Title);
            this.Spanel.Controls.Add(this.CBox_Port);
            this.Spanel.Controls.Add(this.rjLabel16);
            this.Spanel.Controls.Add(this.panel3_side);
            this.Spanel.Controls.Add(this.panel2_side);
            this.Spanel.Controls.Add(this.pnl_side_sn);
            this.Spanel.Controls.Add(this.CBox_Customer);
            this.Spanel.Controls.Add(this.rjLabel17);
            this.Spanel.Controls.Add(this.CBox_Radius);
            this.Spanel.Controls.Add(this.rjLabel14);
            this.Spanel.Controls.Add(this.CBox_SellingPoint);
            this.Spanel.Controls.Add(this.rjLabel15);
            this.Spanel.Controls.Add(this.CBox_Batch);
            this.Spanel.Controls.Add(this.rjButton4);
            this.Spanel.Controls.Add(this.btn_Fix);
            this.Spanel.Controls.Add(this.rjLabel4);
            this.Spanel.Controls.Add(this.CBox_Profile);
            this.Spanel.Controls.Add(this.rjLabel9);
            this.Spanel.Controls.Add(this.btn_apply);
            this.Spanel.Customizable = false;
            this.Spanel.Dock = System.Windows.Forms.DockStyle.Left;
            this.Spanel.Location = new System.Drawing.Point(0, 143);
            this.Spanel.Name = "Spanel";
            this.Spanel.Size = new System.Drawing.Size(41, 557);
            this.Spanel.TabIndex = 83;
            // 
            // panel1_side
            // 
            this.panel1_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel1_side.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1_side.Location = new System.Drawing.Point(0, 530);
            this.panel1_side.Name = "panel1_side";
            this.panel1_side.Size = new System.Drawing.Size(238, 10);
            this.panel1_side.TabIndex = 54;
            this.panel1_side.Visible = false;
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Cairo Medium", 12F);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(10702, 395);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(92, 30);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 31;
            this.rjLabel25Title.Text = "فلتره بحسب";
            this.rjLabel25Title.Visible = false;
            // 
            // CBox_Port
            // 
            this.CBox_Port.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Port.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems;
            this.CBox_Port.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Port.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.BorderRadius = 5;
            this.CBox_Port.BorderSize = 1;
            this.CBox_Port.Customizable = false;
            this.CBox_Port.DataSource = null;
            this.CBox_Port.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Port.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Port.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Port.Location = new System.Drawing.Point(20, 6);
            this.CBox_Port.Name = "CBox_Port";
            this.CBox_Port.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Port.SelectedIndex = -1;
            this.CBox_Port.Size = new System.Drawing.Size(155, 32);
            this.CBox_Port.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Port.TabIndex = 33;
            this.CBox_Port.Texts = "";
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(191, 11);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(38, 23);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 35;
            this.rjLabel16.Text = "الجهاز";
            this.rjLabel16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel3_side
            // 
            this.panel3_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.panel3_side.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3_side.Location = new System.Drawing.Point(238, 1);
            this.panel3_side.Name = "panel3_side";
            this.panel3_side.Size = new System.Drawing.Size(1, 539);
            this.panel3_side.TabIndex = 52;
            // 
            // panel2_side
            // 
            this.panel2_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel2_side.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2_side.Location = new System.Drawing.Point(0, 0);
            this.panel2_side.Name = "panel2_side";
            this.panel2_side.Size = new System.Drawing.Size(239, 1);
            this.panel2_side.TabIndex = 55;
            // 
            // pnl_side_sn
            // 
            this.pnl_side_sn.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_sn.BorderRadius = 10;
            this.pnl_side_sn.Controls.Add(this.CheckBox_SN);
            this.pnl_side_sn.Controls.Add(this.rjLabel10);
            this.pnl_side_sn.Controls.Add(this.CBox_SN_Compar);
            this.pnl_side_sn.Controls.Add(this.txt_SN_Start);
            this.pnl_side_sn.Controls.Add(this.rjLabel1);
            this.pnl_side_sn.Controls.Add(this.txt_SN_End);
            this.pnl_side_sn.Customizable = true;
            this.pnl_side_sn.Location = new System.Drawing.Point(9, 225);
            this.pnl_side_sn.Name = "pnl_side_sn";
            this.pnl_side_sn.Size = new System.Drawing.Size(229, 90);
            this.pnl_side_sn.TabIndex = 53;
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.Location = new System.Drawing.Point(187, 11);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 42;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(81, 15);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(10, 40);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(155, 32);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 31;
            this.CBox_SN_Compar.Texts = "";
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(96, 9);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 43;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(169, 43);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(57, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 35;
            this.rjLabel1.Text = "التسلسلي";
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(12, 9);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 43;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // CBox_Customer
            // 
            this.CBox_Customer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Customer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Customer.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.BorderRadius = 5;
            this.CBox_Customer.BorderSize = 1;
            this.CBox_Customer.Customizable = false;
            this.CBox_Customer.DataSource = null;
            this.CBox_Customer.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Customer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Customer.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Customer.Location = new System.Drawing.Point(21, 187);
            this.CBox_Customer.Name = "CBox_Customer";
            this.CBox_Customer.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Customer.SelectedIndex = -1;
            this.CBox_Customer.Size = new System.Drawing.Size(155, 32);
            this.CBox_Customer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Customer.TabIndex = 33;
            this.CBox_Customer.Texts = "";
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(186, 190);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(44, 23);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 35;
            this.rjLabel17.Text = "العميل";
            this.rjLabel17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Radius
            // 
            this.CBox_Radius.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Radius.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Radius.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Radius.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.BorderRadius = 5;
            this.CBox_Radius.BorderSize = 1;
            this.CBox_Radius.Customizable = false;
            this.CBox_Radius.DataSource = null;
            this.CBox_Radius.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Radius.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Radius.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Radius.Location = new System.Drawing.Point(21, 151);
            this.CBox_Radius.Name = "CBox_Radius";
            this.CBox_Radius.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Radius.SelectedIndex = -1;
            this.CBox_Radius.Size = new System.Drawing.Size(155, 32);
            this.CBox_Radius.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Radius.TabIndex = 33;
            this.CBox_Radius.Texts = "";
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(185, 155);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(47, 23);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 35;
            this.rjLabel14.Text = "راديوس";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(21, 114);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(155, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(180, 118);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(56, 23);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "001-12-09-2024",
            "002-15-09-2024",
            "003-05-09-2024"});
            this.CBox_Batch.Location = new System.Drawing.Point(21, 78);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(155, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 33;
            this.CBox_Batch.Texts = "";
            // 
            // rjButton4
            // 
            this.rjButton4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton4.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.BorderRadius = 4;
            this.rjButton4.BorderSize = 1;
            this.rjButton4.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton4.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton4.FlatAppearance.BorderSize = 0;
            this.rjButton4.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton4.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton4.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton4.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconChar = FontAwesome.Sharp.IconChar.CreativeCommonsSampling;
            this.rjButton4.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton4.IconSize = 20;
            this.rjButton4.Location = new System.Drawing.Point(97, 379);
            this.rjButton4.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton4.Name = "rjButton4";
            this.rjButton4.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton4.Size = new System.Drawing.Size(25, 31);
            this.rjButton4.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton4.TabIndex = 49;
            this.rjButton4.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton4.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton4.UseVisualStyleBackColor = false;
            this.rjButton4.Visible = false;
            // 
            // btn_Fix
            // 
            this.btn_Fix.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Fix.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.BorderRadius = 4;
            this.btn_Fix.BorderSize = 1;
            this.btn_Fix.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Fix.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Fix.FlatAppearance.BorderSize = 0;
            this.btn_Fix.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Fix.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Fix.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Fix.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Fix.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconChar = FontAwesome.Sharp.IconChar.Unlock;
            this.btn_Fix.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Fix.IconSize = 20;
            this.btn_Fix.Location = new System.Drawing.Point(123, 379);
            this.btn_Fix.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Fix.Name = "btn_Fix";
            this.btn_Fix.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Fix.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Fix.Size = new System.Drawing.Size(25, 31);
            this.btn_Fix.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Fix.TabIndex = 49;
            this.btn_Fix.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Fix.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Fix.UseVisualStyleBackColor = false;
            this.btn_Fix.Visible = false;
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(186, 85);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(44, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 35;
            this.rjLabel4.Text = "الدفعه";
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(21, 42);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(155, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(189, 47);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.Size = new System.Drawing.Size(38, 23);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 15;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Cairo Medium", 12F, System.Drawing.FontStyle.Bold);
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(63, 321);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.Size = new System.Drawing.Size(101, 40);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 51;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_apply.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle21.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle21.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle21.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle21.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle21.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle21;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            dataGridViewCellStyle22.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle22.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle22.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle22.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle22.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle22.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle22.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle22;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(13, 149);
            this.dgv.MultiSelect = false;
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle23.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle23.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle23.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle23.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle23.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle23.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle23.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle23;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 30;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle24.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle24.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle24.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle24.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle24.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle24.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle24;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 30;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(968, 298);
            this.dgv.TabIndex = 86;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellClick);
            this.dgv.ColumnHeaderMouseClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dgv_ColumnHeaderMouseClick);
            // 
            // dgv2
            // 
            this.dgv2.AllowUserToAddRows = false;
            this.dgv2.AllowUserToDeleteRows = false;
            this.dgv2.AllowUserToOrderColumns = true;
            this.dgv2.AllowUserToResizeRows = false;
            this.dgv2.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv2.AlternatingRowsColorApply = false;
            this.dgv2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv2.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.BorderRadius = 10;
            this.dgv2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv2.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv2.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv2.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv2.ColumnHeaderHeight = 40;
            this.dgv2.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle17.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle17.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle17.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle17.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle17.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle17;
            this.dgv2.ColumnHeadersHeight = 40;
            this.dgv2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv2.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv2.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv2.Customizable = false;
            dataGridViewCellStyle18.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle18.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle18.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle18.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle18.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle18.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle18.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.DefaultCellStyle = dataGridViewCellStyle18;
            this.dgv2.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.EnableHeadersVisualStyles = false;
            this.dgv2.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv2.Location = new System.Drawing.Point(15, 536);
            this.dgv2.Name = "dgv2";
            this.dgv2.ReadOnly = true;
            this.dgv2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv2.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv2.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle19.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle19.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle19.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle19.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle19.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle19.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle19.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.RowHeadersDefaultCellStyle = dataGridViewCellStyle19;
            this.dgv2.RowHeadersVisible = false;
            this.dgv2.RowHeadersWidth = 30;
            this.dgv2.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv2.RowHeight = 30;
            this.dgv2.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle20.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle20.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle20.Font = new System.Drawing.Font("Tahoma", 9F);
            dataGridViewCellStyle20.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle20.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle20.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv2.RowsDefaultCellStyle = dataGridViewCellStyle20;
            this.dgv2.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv2.RowTemplate.Height = 30;
            this.dgv2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv2.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv2.Size = new System.Drawing.Size(968, 150);
            this.dgv2.TabIndex = 88;
            this.dgv2.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv2_CellDoubleClick);
            this.dgv2.SelectionChanged += new System.EventHandler(this.dgv2_SelectionChanged);
            // 
            // rjPanel12
            // 
            this.rjPanel12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel12.BorderRadius = 5;
            this.rjPanel12.Controls.Add(this.txt_sum_Sales);
            this.rjPanel12.Controls.Add(this.rjLabel23);
            this.rjPanel12.Controls.Add(this.rjToggleButton1);
            this.rjPanel12.Controls.Add(this.ToggleButton_Detail);
            this.rjPanel12.Controls.Add(this.ToggleButton_Monthly);
            this.rjPanel12.Controls.Add(this.rjButton1);
            this.rjPanel12.Controls.Add(this.jToggleButton_Year);
            this.rjPanel12.Controls.Add(this.txt_count_Cards);
            this.rjPanel12.Controls.Add(this.rjLabel6);
            this.rjPanel12.Controls.Add(this.rjTextBox1);
            this.rjPanel12.Controls.Add(this.lbl_Sub_title);
            this.rjPanel12.Customizable = false;
            this.rjPanel12.Location = new System.Drawing.Point(15, 453);
            this.rjPanel12.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel12.Name = "rjPanel12";
            this.rjPanel12.Size = new System.Drawing.Size(968, 78);
            this.rjPanel12.TabIndex = 87;
            // 
            // txt_sum_Sales
            // 
            this.txt_sum_Sales._Customizable = false;
            this.txt_sum_Sales.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_sum_Sales.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_sum_Sales.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_sum_Sales.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_sum_Sales.BorderRadius = 3;
            this.txt_sum_Sales.BorderSize = 1;
            this.txt_sum_Sales.Enabled = false;
            this.txt_sum_Sales.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_sum_Sales.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_sum_Sales.Location = new System.Drawing.Point(392, 38);
            this.txt_sum_Sales.MultiLine = false;
            this.txt_sum_Sales.Name = "txt_sum_Sales";
            this.txt_sum_Sales.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_sum_Sales.PasswordChar = false;
            this.txt_sum_Sales.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_sum_Sales.PlaceHolderText = null;
            this.txt_sum_Sales.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_sum_Sales.Size = new System.Drawing.Size(154, 25);
            this.txt_sum_Sales.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_sum_Sales.TabIndex = 91;
            this.txt_sum_Sales.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel23
            // 
            this.rjLabel23.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel23.AutoSize = true;
            this.rjLabel23.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel23.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel23.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel23.LinkLabel = false;
            this.rjLabel23.Location = new System.Drawing.Point(446, 12);
            this.rjLabel23.Name = "rjLabel23";
            this.rjLabel23.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel23.Size = new System.Drawing.Size(78, 17);
            this.rjLabel23.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel23.TabIndex = 93;
            this.rjLabel23.Text = "اجمالي المبلغ";
            // 
            // rjToggleButton1
            // 
            this.rjToggleButton1.Activated = false;
            this.rjToggleButton1.Customizable = false;
            this.rjToggleButton1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjToggleButton1.Location = new System.Drawing.Point(75, 46);
            this.rjToggleButton1.MinimumSize = new System.Drawing.Size(50, 25);
            this.rjToggleButton1.Name = "rjToggleButton1";
            this.rjToggleButton1.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton1.OFF_Text = "تجميع بحسب الباقة";
            this.rjToggleButton1.OFF_TextColor = System.Drawing.Color.Gray;
            this.rjToggleButton1.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton1.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton1.ON_Text = "تجميع بحسب الباقة";
            this.rjToggleButton1.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjToggleButton1.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton1.Size = new System.Drawing.Size(184, 25);
            this.rjToggleButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjToggleButton1.TabIndex = 52;
            this.rjToggleButton1.Tag = "تفصيلي";
            this.rjToggleButton1.Text = "#";
            this.rjToggleButton1.UseVisualStyleBackColor = true;
            this.rjToggleButton1.Visible = false;
            this.rjToggleButton1.CheckedChanged += new System.EventHandler(this.Radio_By_IP_CheckedChanged);
            // 
            // ToggleButton_Detail
            // 
            this.ToggleButton_Detail.Activated = true;
            this.ToggleButton_Detail.Checked = true;
            this.ToggleButton_Detail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToggleButton_Detail.Customizable = false;
            this.ToggleButton_Detail.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Detail.Location = new System.Drawing.Point(226, 17);
            this.ToggleButton_Detail.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Detail.Name = "ToggleButton_Detail";
            this.ToggleButton_Detail.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.OFF_Text = "تفصيلي";
            this.ToggleButton_Detail.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Detail.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.ON_Text = "تفصيلي";
            this.ToggleButton_Detail.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Detail.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Detail.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Detail.TabIndex = 99;
            this.ToggleButton_Detail.Tag = "تفصيلي";
            this.ToggleButton_Detail.Text = "#";
            this.ToggleButton_Detail.UseVisualStyleBackColor = true;
            this.ToggleButton_Detail.CheckedChanged += new System.EventHandler(this.ToggleButton_Detail_CheckedChanged);
            // 
            // ToggleButton_Monthly
            // 
            this.ToggleButton_Monthly.Activated = false;
            this.ToggleButton_Monthly.Customizable = false;
            this.ToggleButton_Monthly.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Monthly.Location = new System.Drawing.Point(133, 15);
            this.ToggleButton_Monthly.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Monthly.Name = "ToggleButton_Monthly";
            this.ToggleButton_Monthly.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.OFF_Text = "يومي";
            this.ToggleButton_Monthly.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Monthly.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.ON_Text = "يومي";
            this.ToggleButton_Monthly.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Monthly.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Monthly.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Monthly.TabIndex = 100;
            this.ToggleButton_Monthly.Tag = "تفصيلي";
            this.ToggleButton_Monthly.Text = "#";
            this.ToggleButton_Monthly.UseVisualStyleBackColor = true;
            this.ToggleButton_Monthly.Visible = false;
            this.ToggleButton_Monthly.CheckedChanged += new System.EventHandler(this.ToggleButton_Monthly_CheckedChanged);
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 4;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 25;
            this.rjButton1.Location = new System.Drawing.Point(-1, 11);
            this.rjButton1.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(39, 30);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton1.TabIndex = 49;
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton1.UseVisualStyleBackColor = false;
            this.rjButton1.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.jToggleButton_Year.Location = new System.Drawing.Point(39, 15);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = "شهري";
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = "شهري";
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(91, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 101;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            this.jToggleButton_Year.Visible = false;
            this.jToggleButton_Year.CheckedChanged += new System.EventHandler(this.jToggleButton_Year_CheckedChanged);
            // 
            // txt_count_Cards
            // 
            this.txt_count_Cards._Customizable = false;
            this.txt_count_Cards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_count_Cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Cards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Cards.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Cards.BorderRadius = 3;
            this.txt_count_Cards.BorderSize = 1;
            this.txt_count_Cards.Enabled = false;
            this.txt_count_Cards.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_count_Cards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Cards.Location = new System.Drawing.Point(593, 38);
            this.txt_count_Cards.MultiLine = false;
            this.txt_count_Cards.Name = "txt_count_Cards";
            this.txt_count_Cards.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Cards.PasswordChar = false;
            this.txt_count_Cards.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Cards.PlaceHolderText = null;
            this.txt_count_Cards.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Cards.Size = new System.Drawing.Size(154, 25);
            this.txt_count_Cards.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_count_Cards.TabIndex = 91;
            this.txt_count_Cards.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel6
            // 
            this.rjLabel6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(641, 12);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(64, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 93;
            this.rjLabel6.Text = "عدد الكروت";
            // 
            // rjTextBox1
            // 
            this.rjTextBox1._Customizable = false;
            this.rjTextBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjTextBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjTextBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjTextBox1.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.rjTextBox1.BorderRadius = 3;
            this.rjTextBox1.BorderSize = 1;
            this.rjTextBox1.Enabled = false;
            this.rjTextBox1.Font = new System.Drawing.Font("Tahoma", 9F);
            this.rjTextBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox1.Location = new System.Drawing.Point(791, 38);
            this.rjTextBox1.MultiLine = false;
            this.rjTextBox1.Name = "rjTextBox1";
            this.rjTextBox1.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.rjTextBox1.PasswordChar = false;
            this.rjTextBox1.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.rjTextBox1.PlaceHolderText = null;
            this.rjTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.rjTextBox1.Size = new System.Drawing.Size(146, 25);
            this.rjTextBox1.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.rjTextBox1.TabIndex = 91;
            this.rjTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_Sub_title
            // 
            this.lbl_Sub_title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Sub_title.AutoSize = true;
            this.lbl_Sub_title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Sub_title.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Sub_title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Sub_title.LinkLabel = false;
            this.lbl_Sub_title.Location = new System.Drawing.Point(825, 12);
            this.lbl_Sub_title.Name = "lbl_Sub_title";
            this.lbl_Sub_title.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Sub_title.Size = new System.Drawing.Size(72, 17);
            this.lbl_Sub_title.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Sub_title.TabIndex = 93;
            this.lbl_Sub_title.Text = "بيانات الجهاز";
            // 
            // Cbox_View
            // 
            this.Cbox_View.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.Cbox_View.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.Cbox_View.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Cbox_View.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_View.BorderRadius = 10;
            this.Cbox_View.BorderSize = 1;
            this.Cbox_View.Customizable = false;
            this.Cbox_View.DataSource = null;
            this.Cbox_View.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Cbox_View.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Cbox_View.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_View.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Cbox_View.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Cbox_View.Items.AddRange(new object[] {
            "عرض جميع الكروت",
            "عرض جميع الجلسات تفصيلي",
            "عرض جميع الجلسات تجميعي"});
            this.Cbox_View.Location = new System.Drawing.Point(63, 400);
            this.Cbox_View.Name = "Cbox_View";
            this.Cbox_View.Padding = new System.Windows.Forms.Padding(2);
            this.Cbox_View.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Cbox_View.SelectedIndex = -1;
            this.Cbox_View.Size = new System.Drawing.Size(301, 32);
            this.Cbox_View.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Cbox_View.TabIndex = 94;
            this.Cbox_View.Texts = "";
            this.Cbox_View.Visible = false;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.Restor_ColumnToolStripMenuItem,
            this.toolStripMenuItem4,
            this.Copy_ToolStripMenuItem,
            this.Copy_AllRowToolStripMenuItem,
            this.ExportExcelToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(222, 136);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Checked = true;
            this.toolStripMenuItem1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.toolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.PageNumber_ToolStripMenuItem,
            this.LastSynDb_ToolStripMenuItem,
            this.toolStripMenuItem3,
            this.Count_profile_ToolStripMenuItem,
            this.CusName_ToolStripMenuItem,
            this.Descr_ToolStripMenuItem,
            this.Str_ProfileTransferLeft_ToolStripMenuItem,
            this.Str_ProfileTimeLeft_ToolStripMenuItem,
            this.Str_ProfileTillTime_ToolStripMenuItem,
            this.dt_FirstUse_ToolStripMenuItem,
            this.dt_LastSeenAt_ToolStripMenuItem,
            this.dt_RegDate_ToolStripMenuItem,
            this.Str_Up_Down_ToolStripMenuItem,
            this.Str_UploadUsed_ToolStripMenuItem,
            this.Str_DownloadUsed_ToolStripMenuItem,
            this.Str_UptimeUsed_ToolStripMenuItem,
            this.Str_TransferLimit_ToolStripMenuItem,
            this.Str_UptimeLimit_ToolStripMenuItem,
            this.BachCards_ToolStripMenuItem,
            this.SellingPoint_ToolStripMenuItem,
            this.Profile_ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.Password_ToolStripMenuItem,
            this.UserName_ToolStripMenuItem,
            this.SN_ToolStripMenuItem,
            this.Status_ToolStripMenuItem});
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(221, 22);
            this.toolStripMenuItem1.Text = "عرض واخفاء الاعمدة";
            // 
            // PageNumber_ToolStripMenuItem
            // 
            this.PageNumber_ToolStripMenuItem.Name = "PageNumber_ToolStripMenuItem";
            this.PageNumber_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.PageNumber_ToolStripMenuItem.Tag = "PageNumber";
            this.PageNumber_ToolStripMenuItem.Text = "رقم الصفحة";
            // 
            // LastSynDb_ToolStripMenuItem
            // 
            this.LastSynDb_ToolStripMenuItem.Name = "LastSynDb_ToolStripMenuItem";
            this.LastSynDb_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.LastSynDb_ToolStripMenuItem.Tag = "LastSynDb";
            this.LastSynDb_ToolStripMenuItem.Text = "اخر تحديث او مزامنه للكرت";
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(202, 22);
            this.toolStripMenuItem3.Tag = "CountSession";
            this.toolStripMenuItem3.Text = "عدد جلسات الكرت";
            // 
            // Count_profile_ToolStripMenuItem
            // 
            this.Count_profile_ToolStripMenuItem.Name = "Count_profile_ToolStripMenuItem";
            this.Count_profile_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Count_profile_ToolStripMenuItem.Tag = "CountProfile";
            this.Count_profile_ToolStripMenuItem.Text = "عدد الباقات";
            // 
            // CusName_ToolStripMenuItem
            // 
            this.CusName_ToolStripMenuItem.Name = "CusName_ToolStripMenuItem";
            this.CusName_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.CusName_ToolStripMenuItem.Tag = "CustomerName";
            this.CusName_ToolStripMenuItem.Text = "عميل يوزمنجر";
            // 
            // Descr_ToolStripMenuItem
            // 
            this.Descr_ToolStripMenuItem.Name = "Descr_ToolStripMenuItem";
            this.Descr_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Descr_ToolStripMenuItem.Tag = "Comment";
            this.Descr_ToolStripMenuItem.Text = "تعلـــــــيق";
            // 
            // Str_ProfileTransferLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Name = "Str_ProfileTransferLeft_ToolStripMenuItem";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Tag = "Str_ProfileTransferLeft";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Text = "التحميل المتبقي";
            // 
            // Str_ProfileTimeLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Name = "Str_ProfileTimeLeft_ToolStripMenuItem";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Tag = "Str_ProfileTimeLeft";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Text = "الوقت المتبقي";
            // 
            // Str_ProfileTillTime_ToolStripMenuItem
            // 
            this.Str_ProfileTillTime_ToolStripMenuItem.Name = "Str_ProfileTillTime_ToolStripMenuItem";
            this.Str_ProfileTillTime_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTillTime_ToolStripMenuItem.Tag = "Str_ProfileTillTime";
            this.Str_ProfileTillTime_ToolStripMenuItem.Text = "تــــاريخ الانتــــهاء";
            // 
            // dt_FirstUse_ToolStripMenuItem
            // 
            this.dt_FirstUse_ToolStripMenuItem.Name = "dt_FirstUse_ToolStripMenuItem";
            this.dt_FirstUse_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_FirstUse_ToolStripMenuItem.Tag = "FirsLogin";
            this.dt_FirstUse_ToolStripMenuItem.Text = "اول دخـــــــــول";
            // 
            // dt_LastSeenAt_ToolStripMenuItem
            // 
            this.dt_LastSeenAt_ToolStripMenuItem.Name = "dt_LastSeenAt_ToolStripMenuItem";
            this.dt_LastSeenAt_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_LastSeenAt_ToolStripMenuItem.Tag = "LastSeenAt";
            this.dt_LastSeenAt_ToolStripMenuItem.Text = "اخــــــر ظهــــور";
            // 
            // dt_RegDate_ToolStripMenuItem
            // 
            this.dt_RegDate_ToolStripMenuItem.Name = "dt_RegDate_ToolStripMenuItem";
            this.dt_RegDate_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_RegDate_ToolStripMenuItem.Tag = "RegDate";
            this.dt_RegDate_ToolStripMenuItem.Text = "تـــاريخ الاضــــافة";
            // 
            // Str_Up_Down_ToolStripMenuItem
            // 
            this.Str_Up_Down_ToolStripMenuItem.Name = "Str_Up_Down_ToolStripMenuItem";
            this.Str_Up_Down_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_Up_Down_ToolStripMenuItem.Tag = "Str_Up_Down";
            this.Str_Up_Down_ToolStripMenuItem.Text = "تحميـــــل+رفــــع";
            // 
            // Str_UploadUsed_ToolStripMenuItem
            // 
            this.Str_UploadUsed_ToolStripMenuItem.Name = "Str_UploadUsed_ToolStripMenuItem";
            this.Str_UploadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UploadUsed_ToolStripMenuItem.Tag = "Str_UploadUsed";
            this.Str_UploadUsed_ToolStripMenuItem.Text = "الرقع المستخدم";
            // 
            // Str_DownloadUsed_ToolStripMenuItem
            // 
            this.Str_DownloadUsed_ToolStripMenuItem.Name = "Str_DownloadUsed_ToolStripMenuItem";
            this.Str_DownloadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_DownloadUsed_ToolStripMenuItem.Tag = "Str_DownloadUsed";
            this.Str_DownloadUsed_ToolStripMenuItem.Text = "التحميل المستخدم";
            // 
            // Str_UptimeUsed_ToolStripMenuItem
            // 
            this.Str_UptimeUsed_ToolStripMenuItem.Name = "Str_UptimeUsed_ToolStripMenuItem";
            this.Str_UptimeUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UptimeUsed_ToolStripMenuItem.Tag = "Str_UptimeUsed";
            this.Str_UptimeUsed_ToolStripMenuItem.Text = "الوقت المستخدم";
            // 
            // Str_TransferLimit_ToolStripMenuItem
            // 
            this.Str_TransferLimit_ToolStripMenuItem.Name = "Str_TransferLimit_ToolStripMenuItem";
            this.Str_TransferLimit_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_TransferLimit_ToolStripMenuItem.Tag = "Str_TransferLimit";
            this.Str_TransferLimit_ToolStripMenuItem.Text = "التنزيل المسموح";
            // 
            // Str_UptimeLimit_ToolStripMenuItem
            // 
            this.Str_UptimeLimit_ToolStripMenuItem.Name = "Str_UptimeLimit_ToolStripMenuItem";
            this.Str_UptimeLimit_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UptimeLimit_ToolStripMenuItem.Tag = "Str_UptimeLimit";
            this.Str_UptimeLimit_ToolStripMenuItem.Text = "الوقت المسموح";
            // 
            // BachCards_ToolStripMenuItem
            // 
            this.BachCards_ToolStripMenuItem.Name = "BachCards_ToolStripMenuItem";
            this.BachCards_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.BachCards_ToolStripMenuItem.Tag = "BatchCardId";
            this.BachCards_ToolStripMenuItem.Text = "الـــــدفعــــــه";
            // 
            // SellingPoint_ToolStripMenuItem
            // 
            this.SellingPoint_ToolStripMenuItem.Name = "SellingPoint_ToolStripMenuItem";
            this.SellingPoint_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.SellingPoint_ToolStripMenuItem.Tag = "SpName";
            this.SellingPoint_ToolStripMenuItem.Text = "نقـــــطة البيع";
            // 
            // Profile_ToolStripMenuItem
            // 
            this.Profile_ToolStripMenuItem.Name = "Profile_ToolStripMenuItem";
            this.Profile_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Profile_ToolStripMenuItem.Tag = "ProfileName";
            this.Profile_ToolStripMenuItem.Text = "البــــــــاقة";
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(202, 22);
            this.toolStripMenuItem2.Tag = "Str_TotalPrice";
            this.toolStripMenuItem2.Text = "الســـــــعر";
            // 
            // Password_ToolStripMenuItem
            // 
            this.Password_ToolStripMenuItem.Checked = true;
            this.Password_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Password_ToolStripMenuItem.Name = "Password_ToolStripMenuItem";
            this.Password_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Password_ToolStripMenuItem.Tag = "Password";
            this.Password_ToolStripMenuItem.Text = "كلمة المرور";
            // 
            // UserName_ToolStripMenuItem
            // 
            this.UserName_ToolStripMenuItem.Checked = true;
            this.UserName_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.UserName_ToolStripMenuItem.Name = "UserName_ToolStripMenuItem";
            this.UserName_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.UserName_ToolStripMenuItem.Tag = "UserName";
            this.UserName_ToolStripMenuItem.Text = "الاســـــــم";
            // 
            // SN_ToolStripMenuItem
            // 
            this.SN_ToolStripMenuItem.Name = "SN_ToolStripMenuItem";
            this.SN_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.SN_ToolStripMenuItem.Tag = "Sn";
            this.SN_ToolStripMenuItem.Text = "الرقم التسلسلي";
            // 
            // Status_ToolStripMenuItem
            // 
            this.Status_ToolStripMenuItem.Checked = true;
            this.Status_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Status_ToolStripMenuItem.Name = "Status_ToolStripMenuItem";
            this.Status_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Status_ToolStripMenuItem.Tag = "Str_Status";
            this.Status_ToolStripMenuItem.Text = "الحــــالــــة";
            // 
            // Restor_ColumnToolStripMenuItem
            // 
            this.Restor_ColumnToolStripMenuItem.Name = "Restor_ColumnToolStripMenuItem";
            this.Restor_ColumnToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Restor_ColumnToolStripMenuItem.Text = "استعادة الاعمده الي الافتراضي";
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(221, 22);
            this.toolStripMenuItem4.Text = "عرض معلومات الكرت المحدد";
            this.toolStripMenuItem4.Click += new System.EventHandler(this.toolStripMenuItem4_Click);
            // 
            // Copy_ToolStripMenuItem
            // 
            this.Copy_ToolStripMenuItem.Name = "Copy_ToolStripMenuItem";
            this.Copy_ToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Copy_ToolStripMenuItem.Text = "نسخ                 ctrl+c";
            this.Copy_ToolStripMenuItem.Click += new System.EventHandler(this.Copy_ToolStripMenuItem_Click);
            // 
            // Copy_AllRowToolStripMenuItem
            // 
            this.Copy_AllRowToolStripMenuItem.Name = "Copy_AllRowToolStripMenuItem";
            this.Copy_AllRowToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Copy_AllRowToolStripMenuItem.Text = "نسخ السطر كامل ";
            // 
            // ExportExcelToolStripMenuItem
            // 
            this.ExportExcelToolStripMenuItem.Name = "ExportExcelToolStripMenuItem";
            this.ExportExcelToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.ExportExcelToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel3.BorderRadius = 0;
            this.rjPanel3.Controls.Add(this.CheckBox_FromSession);
            this.rjPanel3.Customizable = true;
            this.rjPanel3.Location = new System.Drawing.Point(274, 55);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(365, 80);
            this.rjPanel3.TabIndex = 90;
            // 
            // Form_UM_Users_Devices
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "Form_UM_Users_Devices";
            this.ClientSize = new System.Drawing.Size(1000, 750);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_UM_Users_Devices";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_UM_Users_Devices";
            this.Load += new System.EventHandler(this.Form_UM_Users_Devices_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel_topFilter.ResumeLayout(false);
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel5.ResumeLayout(false);
            this.rjPanel5.PerformLayout();
            this.rjPanel4.ResumeLayout(false);
            this.rjPanel4.PerformLayout();
            this.Spanel.ResumeLayout(false);
            this.Spanel.PerformLayout();
            this.pnl_side_sn.ResumeLayout(false);
            this.pnl_side_sn.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).EndInit();
            this.rjPanel12.ResumeLayout(false);
            this.rjPanel12.PerformLayout();
            this.dmAll_Cards.ResumeLayout(false);
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel_topFilter;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJDatePicker rjDateTime_To;
        private RJControls.RJDatePicker rjDateTime_From;
        private RJControls.RJButton btn_;
        private RJControls.RJButton rjButton3;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJPanel Spanel;
        private System.Windows.Forms.Panel panel1_side;
        private RJControls.RJLabel rjLabel25Title;
        private RJControls.RJComboBox CBox_Port;
        private RJControls.RJLabel rjLabel16;
        private System.Windows.Forms.Panel panel3_side;
        private System.Windows.Forms.Panel panel2_side;
        private RJControls.RJPanel pnl_side_sn;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJComboBox CBox_Customer;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJComboBox CBox_Radius;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJComboBox CBox_Batch;
        private RJControls.RJButton rjButton4;
        private RJControls.RJButton btn_Fix;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJButton btn_apply;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJDataGridView dgv2;
        private RJControls.RJPanel rjPanel12;
        private RJControls.RJComboBox Cbox_View;
        private RJControls.RJButton rjButton1;
        private RJControls.RJTextBox rjTextBox1;
        private RJControls.RJLabel lbl_Sub_title;
        private RJControls.RJComboBox CBox_SearchBy;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJButton btn_search;
        private RJControls.RJLabel rjLabel18;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJLabel rjLabel20;
        private RJControls.RJTextBox txt_count_Cards;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJTextBox txt_uptime;
        private RJControls.RJLabel rjLabel21;
        private RJControls.RJTextBox txt_Download;
        private RJControls.RJLabel rjLabel22;
        private RJControls.RJTextBox txt_sum_Sales;
        private RJControls.RJLabel rjLabel23;
        private RJControls.RJTextBox txt_sum_Session;
        private RJControls.RJLabel rjLabel24;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem PageNumber_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem LastSynDb_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem Count_profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CusName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Descr_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTransferLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTimeLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTillTime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_FirstUse_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_LastSeenAt_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_RegDate_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_Up_Down_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UploadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_DownloadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_TransferLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BachCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SellingPoint_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem Password_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem UserName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SN_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Status_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Restor_ColumnToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem Copy_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Copy_AllRowToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportExcelToolStripMenuItem;
        private RJControls.RJCheckBox rjCheckBox1;
        private RJControls.RJLabel rjLabel25;
        private RJControls.RJCheckBox CheckBox_FromSession;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJPanel rjPanel4;
        private System.Windows.Forms.ToolTip toolTip1;
        private RJControls.RJPanel rjPanel5;
        private RJControls.RJToggleButton Radio_By_Mac;
        private RJControls.RJToggleButton Radio_By_IP;
        private RJControls.RJLabel rjLabel26;
        private RJControls.RJToggleButton Radio_By_Cards;
        private RJControls.RJCheckBox check_with_Commi;
        private RJControls.RJCheckBox CheckBox_To_Date;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJToggleButton ToggleButton_Detail;
        private RJControls.RJToggleButton ToggleButton_Monthly;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJToggleButton rjToggleButton1;
    }
}