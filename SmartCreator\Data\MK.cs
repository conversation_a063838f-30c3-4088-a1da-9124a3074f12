﻿using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Sockets;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Data
{
    public partial class MK
    {
        Stream connection;
        TcpClient con;
        public static bool True_IP = false;
        public static bool True_UserPass = false;
        public MK()
        {
            try
            {
                con = new TcpClient();
                con.Connect(Global_Variable.Server_IP, Global_Variable.Server_Port);
                //con.Connect(ip, MyDataClass.Server_Port);
                connection = (Stream)con.GetStream();
                True_IP = true;
            }
            catch
            {
                True_IP = false;
                //MessageBox.Show(" غير مفعل (API) لا يوجد سيرفر بهذا العنوان او منفذ  ");
                //return; 
            }
        }
        public MK(string ip)
        {
            try
            {
                con = new TcpClient();
                con.Connect(Global_Variable.Server_IP, Global_Variable.Server_Port);
                //con.Connect(ip, MyDataClass.Server_Port);
                connection = (Stream)con.GetStream();
                True_IP = true;
            }
            catch
            {
                True_IP = false;
                //MessageBox.Show(" غير مفعل (API) لا يوجد سيرفر بهذا العنوان او منفذ  ");
                //return; 
            }
        }
        public void Close()
        {
            try
            {
                connection.Close();
                con.Close();
            }
            catch { }
        }
        public bool Login_old(string username, string password)
        {
            bool choice = false;
            try
            {
                Send("/login", true);
                string hash = Read()[0].Split(new string[] { "ret=" }, StringSplitOptions.None)[1];
                Send("/login");
                Send("=name=" + username);
                Send("=response=00" + EncodePassword(password, hash), true);
                if (Read()[0] == "!done")
                {
                    choice = true;
                    // return true;
                }
                else
                {
                    True_UserPass = false;
                    //return false;
                    choice = false;
                }
            }
            catch
            {
                True_UserPass = false;
                //MessageBox.Show("اسم المستخدم او كلمة المرور غير صحيح");


            }
            True_UserPass = choice;
            //if (!choice)
            //    choice = Login_new(username, password);

            return choice;
        }

        public bool Login(string username, string password)
        {
            bool choice = false;
            try
            {
                //Send("/login", true);
                //string hash = Read()[0].Split(new string[] { "ret=" }, StringSplitOptions.None)[1];
                Send("/login");
                Send("=name=" + username);
                Send("=password=" + password, true);
                //Send("=response=00" + EncodePassword(password, hash), true);
                string QuryString = "";
                foreach (string h in Read())
                {
                    QuryString += h;
                }
                //mk.Read();
                //MessageBox.Show(QuryString);

                if (QuryString == "!done")
                {
                    //MessageBox.Show(QuryString);
                    //MessageBox.Show(Read()[0].ToString());
                    choice = true;
                    // return true;
                }
                else
                {
                    True_UserPass = false;
                    //return false;
                    choice = false;
                }
            }
            catch
            {
                True_UserPass = false;
                //MessageBox.Show("اسم المستخدم او كلمة المرور غير صحيح");


            }
            True_UserPass = choice;
            if (!choice)
                choice = Login_old(username, password);

            return choice;
        }
        public void Send(string co)
        {
            try
            {
                byte[] bajty = Encoding.UTF8.GetBytes(co.ToCharArray());
                //byte[] bajty = Encoding.ASCII.GetBytes(co.ToCharArray());
                byte[] velikost = EncodeLength(bajty.Length);

                connection.Write(velikost, 0, velikost.Length);
                connection.Write(bajty, 0, bajty.Length);
            }
            catch { }
        }
        public void Send(string co, bool endsentence)
        {
            try
            {
                //byte[] bajty = Encoding.Default.GetBytes(co.ToCharArray());
                byte[] bajty = Encoding.UTF8.GetBytes(co.ToCharArray());
                byte[] velikost = EncodeLength(bajty.Length);
                connection.Write(velikost, 0, velikost.Length);
                connection.Write(bajty, 0, bajty.Length);
                connection.WriteByte(0);
            }
            catch
            {
                return;
            }
        }
        public List<string> Read()
        {
            List<string> output = new List<string>();
            string o = "";
            byte[] tmp = new byte[4];
            long count;
            try
            {
                while (true)
                {
                    tmp[3] = (byte)connection.ReadByte();
                    // if(tmp[3] == 220) tmp[3] = (byte)connection.ReadByte();// it sometimes happend to me that 
                    //mikrotik send 220 as some kind of "bonus" between words, this fixed things, not sure about it though
                    if (tmp[3] == 0)
                    {
                        //=============
                        //var utf8 = Encoding.UTF8;
                        //byte[] utfBytes = utf8.GetBytes(myString);
                        //var myReturnedString = utf8.GetString(utfBytes);

                        //===========
                        output.Add(o);
                        if (o.Substring(0, 5) == "!done")
                        {
                            break;
                        }
                        else
                        {
                            o = "";
                            continue;
                        }
                    }
                    else
                    {
                        if (tmp[3] < 0x80)
                        {
                            count = tmp[3];
                        }
                        else
                        {
                            if (tmp[3] < 0xC0)
                            {
                                int tmpi = BitConverter.ToInt32(new byte[] { (byte)connection.ReadByte(), tmp[3], 0, 0 }, 0);
                                count = tmpi ^ 0x8000;
                            }
                            else
                            {
                                if (tmp[3] < 0xE0)
                                {
                                    tmp[2] = (byte)connection.ReadByte();
                                    int tmpi = BitConverter.ToInt32(new byte[] { (byte)connection.ReadByte(), tmp[2], tmp[3], 0 }, 0);
                                    count = tmpi ^ 0xC00000;
                                }
                                else
                                {
                                    if (tmp[3] < 0xF0)
                                    {
                                        tmp[2] = (byte)connection.ReadByte();
                                        tmp[1] = (byte)connection.ReadByte();
                                        int tmpi = BitConverter.ToInt32(new byte[] { (byte)connection.ReadByte(), tmp[1], tmp[2], tmp[3] }, 0);
                                        count = tmpi ^ 0xE0000000;
                                    }
                                    else
                                    {
                                        if (tmp[3] == 0xF0)
                                        {
                                            tmp[3] = (byte)connection.ReadByte();
                                            tmp[2] = (byte)connection.ReadByte();
                                            tmp[1] = (byte)connection.ReadByte();
                                            tmp[0] = (byte)connection.ReadByte();
                                            count = BitConverter.ToInt32(tmp, 0);
                                        }
                                        else
                                        {
                                            //Error in packet reception, unknown length
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    for (int i = 0; i < count; i++)
                    {
                        //var bytes = Encoding.UTF8.GetBytes(connection.ReadByte());
                        //var cBack = Encoding.UTF8.GetString(bytes);
                        //o = Encoding.UTF8.GetString(myChar);
                        //o = Encoding.UTF8.GetString(connection.ReadByte().ToString);

                        //char myChar = (Char)connection.ReadByte();
                        //MessageBox.Show(connection.ReadByte().ToString());
                        //MessageBox.Show(myChar.ToString());
                        //returnValue = new string(myChar);

                        o += (Char)connection.ReadByte();
                        //o += (Char)connection.ReadByte();
                    }
                    //MessageBox.Show(o.ToString());
                }
            }
            catch { }
            //MessageBox.Show(output.ToString());
            return output;
        }

        byte[] EncodeLength(int delka)
        {
            if (delka < 0x80)
            {
                byte[] tmp = BitConverter.GetBytes(delka);
                return new byte[1] { tmp[0] };
            }
            if (delka < 0x4000)
            {
                byte[] tmp = BitConverter.GetBytes(delka | 0x8000);
                return new byte[2] { tmp[1], tmp[0] };
            }
            if (delka < 0x200000)
            {
                byte[] tmp = BitConverter.GetBytes(delka | 0xC00000);
                return new byte[3] { tmp[2], tmp[1], tmp[0] };
            }
            if (delka < 0x10000000)
            {
                byte[] tmp = BitConverter.GetBytes(delka | 0xE0000000);
                return new byte[4] { tmp[3], tmp[2], tmp[1], tmp[0] };
            }
            else
            {
                byte[] tmp = BitConverter.GetBytes(delka);
                return new byte[5] { 0xF0, tmp[3], tmp[2], tmp[1], tmp[0] };
            }
        }

        public string EncodePassword(string Password, string hash)
        {
            byte[] hash_byte = new byte[hash.Length / 2];
            for (int i = 0; i <= hash.Length - 2; i += 2)
            {
                hash_byte[i / 2] = Byte.Parse(hash.Substring(i, 2), System.Globalization.NumberStyles.HexNumber);
            }
            byte[] heslo = new byte[1 + Password.Length + hash_byte.Length];
            heslo[0] = 0;
            Encoding.UTF8.GetBytes(Password.ToCharArray()).CopyTo(heslo, 1);
            //Encoding.ASCII.GetBytes(Password.ToCharArray()).CopyTo(heslo, 1);
            hash_byte.CopyTo(heslo, 1 + Password.Length);

            Byte[] hotovo;
            System.Security.Cryptography.MD5 md5;

            md5 = new System.Security.Cryptography.MD5CryptoServiceProvider();

            hotovo = md5.ComputeHash(heslo);

            //Convert encoded bytes back to a 'readable' string
            string navrat = "";
            foreach (byte h in hotovo)
            {
                navrat += h.ToString("x2");
            }
            return navrat;
        }
    }


}
