﻿using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Data
{
    public class Class_Reports
    {
    }
    public class class_Report_monthly_or_Dayliy
    {
        private double uptime = 0;
        //private string str_uptimeUsed = "";
        private double download = 0;
        //private double moneyTotal;
        private string date;

        [DisplayName("التاريخ")]
        public string Date
        {
            get
            {
                return date;
            }
            //get => date; 
            set => date = value;
        }

        [DisplayName("المبلغ")]
        public string Str_TotalPrice
        {
            get
            {
                //try { row.Cells["moneyTotal"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["moneyTotal"].Value)); } catch { row.Cells["moneyTotal"].Value = 0; };

                return String.Format("{0:n0}", TotalPrice);
            }
        }
        [DisplayName("اجمالي الوقت")]
        public string Str_UptimeUsed
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptime);
            }
        }
        [DisplayName("اجمالي الاستهلاك")]
        public string Str_Up_Down
        {
            get
            {
                if (UIAppearance.Language_ar)
                    return utils.ConvertSize_Get_InArabic(download.ToString());
                else
                    return utils.ConvertSize_Get_En(download.ToString());

            }
        }
        [DisplayName("عدد الكروت")]
        public int count { get; set; } = 0;
        public double Uptime { get => uptime; set => uptime = value; }
        public double Download { get => download; set => download = value; }
        //public double MoneyTotal { get => moneyTotal; set => moneyTotal = value; }
        public double TotalPrice { get; set; }
        public double Price { get; set; }

    }

    public class class_Report_Size_And_Times
    {
        private double uptime = 0;
        private double download = 0;
        private string date;

        [DisplayName("التاريخ")]
        public string Date
        {
            get
            {
                return date;
            }
            //get => date; 
            set => date = value;
        }

        [DisplayName("الاسم")]
        public string UserName {  get; set; }
        
        [DisplayName("اجمالي الوقت")]
        public string Str_UptimeUsed
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptime);
            }
        }
        [DisplayName("اجمالي الاستهلاك")]
        public string Str_Up_Down
        {
            get
            {
                if (UIAppearance.Language_ar)
                    return utils.ConvertSize_Get_InArabic(download.ToString());
                else
                    return utils.ConvertSize_Get_En(download.ToString());

            }
        }
        [DisplayName("عدد الجلسات")]
        public int CountSession { get; set; }
        public double Uptime { get => uptime; set => uptime = value; }
        public double Download { get => download; set => download = value; }

    }

}
