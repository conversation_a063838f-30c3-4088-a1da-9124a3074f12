﻿using DevComponents.DotNetBar.Metro;
using SmartCreator.Models;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class MessageForm : MetroForm
    {
        public bool isOK = false;
        public MessageForm()
        {
            InitializeComponent();
            utils.Control_textSize(this);
        }
        private void MessageForm_Load(object sender, EventArgs e)
        {
            this.Focus();
            checkBoxX1.Checked = Properties.Settings.Default.Show_updaeForm;
        }

        private void btn_Save_Click(object sender, EventArgs e)
        {
            if (checkBoxX1.Checked==false)
            {
                Properties.Settings.Default.Show_updaeForm = checkBoxX1.Checked;
                Properties.Settings.Default.Show_updaeForm_V = Global_Variable.Response_api.Next_AppVerion;
                Properties.Settings.Default.Show_updaeForm_AppBuilder = Global_Variable.Response_api.Next_AppBuilder;

                Properties.Settings.Default.Save();
            }
            isOK = true;
            this.Close();
        }

        private void buttonX2_Click(object sender, EventArgs e)
        {
            if (checkBoxX1.Checked == false)
            {
                Properties.Settings.Default.Show_updaeForm = checkBoxX1.Checked;
                Properties.Settings.Default.Show_updaeForm_V = Global_Variable.Response_api.Next_AppVerion;
                Properties.Settings.Default.Show_updaeForm_AppBuilder = Global_Variable.Response_api.Next_AppBuilder;

                Properties.Settings.Default.Save();
            }
            this.Close();
        }


    }
}
