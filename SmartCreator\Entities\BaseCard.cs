﻿
//using ServiceStack.OrmLite.Sqlite;
//using ServiceStack.OrmLite.SqlServer;

//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.UserManager;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;

//using System.ComponentModel.DataAnnotations.Schema;
using System.Data;

//using System.Data.SqlClient;
//using System.ComponentModel.DataAnnotations;

//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class BaseCard
    {
        //public BaseCard() { }
        //public int UmUserId { get; set; }  nvarchar(50)

        
        [/*Index,*/ Required,DisplayName("التسلسل")]
        public long SN { get; set; }

        [Required, StringLength(250), DisplayName("اسم المستخدم")]
        public string UserName { get; set; }

        [DisplayName("كلمة المرور"), StringLength(200)]
        public string Password { get; set; }

        [DisplayName("الباقة")]
        public string ProfileName { get; set; }

        [Default(0), DisplayName("السعر")]
        public float TotalPrice { get; set; } = 0;

        [Default(0), DisplayName("سعر العرض"), Browsable(false)]
        public string Price_Disply { get; set; } = "";

        [DisplayName("السعر"),Computed, Browsable(false)] 
        public string Str_TotalPrice
        {
            get
            {
                //try { row.Cells["moneyTotal"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["moneyTotal"].Value)); } catch { row.Cells["moneyTotal"].Value = 0; };

                return String.Format("{0:n0}", TotalPrice);
            }
        }
        [DisplayName("السعر"),Computed] 
        public string Str_Price
        {
            get
            {
                return String.Format("{0:n0}", Price);
            }
        }

        
        //[ForeignKey(typeof(BatchCard), OnDelete = "CASCADE"), DisplayName("الدفعة")]
        [DisplayName("الدفعة")]
        public int? BatchCardId { get; set; }
        [Default(0), Browsable(false)]
        public int BatchType { get; set; } = 0;  // 0=print  1=import   2=import_archive
        [DisplayName("رقم الطبعة")]
        public int? NumberPrint { get; set; }   // 0=print  1=import   2=import_archive

        
        [StringLength(200), DisplayName("نقطة البيع")]
        public string SpName { get; set; }
        [Computed, DisplayName("وقت الباقة المسموح")]
        public string Str_UptimeLimit
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(UptimeLimit);
            }

        }
        [Computed, DisplayName("تنزيل الباقة المسموح")]
        public string Str_TransferLimit
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(TransferLimit.ToString());
                else
                    return utils.ConvertSize_Get_En(TransferLimit.ToString());
            }
        }
        [Computed, DisplayName("الوقت المستخدم")]
        public string Str_UptimeUsed
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(UptimeUsed);
            }
        }
        [Computed, DisplayName("التحميل المستخدم")]
        public string Str_DownloadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(DownloadUsed.ToString());
                else return utils.ConvertSize_Get_En(DownloadUsed.ToString());

            }
        }
        [Computed, DisplayName("الرقع المستخدم")]
        public string Str_UploadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(UploadUsed.ToString());
                else return utils.ConvertSize_Get_En(UploadUsed.ToString());

            }
        }
        [Computed, DisplayName("التحميل+الرفع المستخدم")]
        public string Str_Up_Down
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic((UploadUsed + DownloadUsed).ToString());
                else return utils.ConvertSize_Get_En((UploadUsed + DownloadUsed).ToString());

            }
        }

        [Computed, DisplayName("تاريخ الانتهاء")]
        public DateTime? Str_ProfileTillTime
        {
            get
            {
                //return null;

                if (FirsLogin != null && ValidityLimit > 0)
                {
                    if (FirsLogin.Value.Year > 1990)
                        return (FirsLogin.Value.AddSeconds(ValidityLimit));
                    else return null;
                }
                else return null;
            }
        }
        [Computed, DisplayName("الايام المتبقية")]
        public int? Str_DaysLeft
        {
            get
            {
                //return null;

                if (FirsLogin != null && ValidityLimit > 0)
                {
                    if (FirsLogin.Value.Year > 1990)
                        //if( FirsLogin.Value.AddSeconds(ValidityLimit) > DateTime.Now )
                        return (FirsLogin.Value.AddSeconds(ValidityLimit) - DateTime.Now ).Days;
                    //else return 0;
                    else return null;
                }
                else return null;
            }
        }
        [Computed, DisplayName("الوقت المتبقي")]
        public string Str_ProfileTimeLeft
        {
            get
            {
                //return null;
                if (UptimeLimit == 0) return "غير معروف";
                double time = Math.Max(0, (UptimeLimit - UptimeUsed));
                return utils.Get_Seconds_By_clock_Mode(time);
            }
        }
        [Computed, DisplayName("التحميل المتبقي")]
        public string Str_ProfileTransferLeft
        {
            get
            {
                //return null;

                if (TransferLimit == 0) return "غير معروف";
                double size = Math.Max(0, (TransferLimit - (UploadUsed + DownloadUsed)));
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic_with_ZeroByte(size.ToString());
                else return utils.ConvertSize_Get_En_with_ZeroByte(size.ToString());
            }
        }

        [ DisplayName("تاريخ الاضافة")]
        public DateTime? RegDate { get; set; }

        [ DisplayName("اول دخول"),Index]
        //[CustomSelect("FORMAT(FirsLogin, 'yyyy-MM-dd')")]
        public DateTime? FirsLogin { get; set; }

        [StringLength(500), DisplayName("ملاحظة")]
        public string Comment { get; set; }
        
        [DisplayName("اخر تحديث")]
        public DateTime? LastSynDb { get; set; }

        //[Computed]
        [DisplayName("عدد الباقات"), Default(0)]
        public int CountProfile { get; set; } = 0;

        [DisplayName("عدد الجلسات"), Default(0)]
        public int CountSession { get; set; } = 0;
        [DisplayName("رقم الصفحة")]
        public int? PageNumber { get; set; }

        [ Unique, Required, StringLength(200), PrimaryKey]
        //[Index(Unique = true), Unique, Required, StringLength(200), PrimaryKey]
        public string Sn_Name { get; set; }
        
        [ Default(0)]
        public int DeleteFromServer { get; set; } = 0;

        [Default(0), Browsable(false)]
        public long ValidityLimit { get; set; } = 0;  //مجموع ايام الباقات المضافة
       
        [Default(0), Browsable(false)]
        public long UptimeLimit { get; set; } = 0; //مجموع الوقت للباقات المضافة
        
        [Default(0), Browsable(false)]
        public long TransferLimit { get; set; } = 0;
        
        [Default(0)/*, Browsable(false)*/]
        public long UptimeUsed { get; set; } = 0;
       
        [Default(0)/*, Browsable(false)*/]
        public long DownloadUsed { get; set; } = 0;
        
        [/*Browsable(false),*/ Default(0)]
        public long UploadUsed { get; set; } = 0;
       
        [Browsable(false)]
        public DateTime? ProfileTillTime { get; set; }
        
        [Browsable(false), Default(0)]
        public long ProfileTimeLeft { get; set; } = 0;
        
        [Browsable(false),Default(0)]
        public long ProfileTransferLeft { get; set; } = 0;
        
        [Default(0), Browsable(false)]
        public long ProfileValidity { get; set; } = 0;
        
        [Default(0), DisplayName("السعر")]
        public float Price { get; set; } = 0;
        
        [Browsable(false), Default(0)]
        public float Percentage { get; set; }= 0;
        
        [Browsable(false), Default(0)]
        public int PercentageType { get; set; } = 0;
        
        [ Default(0)]
        public int Disabled { get; set; }=0;
        
        [ Default(0)]
        public int Status { get; set; } = 0;
        
        [Browsable(false),Index]
        //public int? SpId { get; set; }
        public string SpCode { get; set; }
                
        [StringLength(100), Browsable(false)]
        public string Email { get; set; }
        
        [StringLength(100), Index, DisplayName("الجهاز")]
        public string NasPortId { get; set; }
        
        [StringLength(100), Browsable(false)]
        public string MkId { get; set; }

        [Browsable(false)]
        public DateTime? AddedDb { get; set; }

        [StringLength(150), /* Browsable(false)*/]
        public string IdHX { get; set; }
        [/*Browsable(false),*/ StringLength(100)]
        public string CallerMac { get; set; }
        [Browsable(false), StringLength(100), Computed]
        public string Caller_id_yes_no { get; set; } = "no";

        [Browsable(false),Computed]
        public int Get_info { get; set; }


        [StringLength(200), Browsable(false)]
        public string Group { get; set; } = "default";
        [StringLength(200), Browsable(false)]
        public string Attributes { get; set; }

        [DisplayName("تسلسل الارشيف")]
        public long? Sn_Archive { get; set; }




    }
}
