﻿namespace SmartCreator.Forms.UserManager
{
    partial class FormAllBatchsCards_Archive
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            this.btnAddNew = new SmartCreator.RJControls.RJButton();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.btn_All_cards = new SmartCreator.RJControls.RJButton();
            this.btn_Add_To_Mikrotik = new SmartCreator.RJControls.RJButton();
            this.btnDelete = new SmartCreator.RJControls.RJButton();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.btn_import_FromFile = new SmartCreator.RJControls.RJButton();
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.btn_DBCheck = new SmartCreator.RJControls.RJButton();
            this.rjPanel_back_side = new SmartCreator.RJControls.RJPanel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.rjCheckBox1 = new SmartCreator.RJControls.RJCheckBox();
            this.Date_To = new SmartCreator.RJControls.RJDatePicker();
            this.Date_From = new SmartCreator.RJControls.RJDatePicker();
            this.lbl_to = new SmartCreator.RJControls.RJLabel();
            this.rjToggleButton1 = new SmartCreator.RJControls.RJToggleButton();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.rjToggleButton2 = new SmartCreator.RJControls.RJToggleButton();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_datePrint = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_byDatePrint = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel20 = new SmartCreator.RJControls.RJLabel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.ViewCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.PrintToMK_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.اعادةطباعةملفPDFToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Delete_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.toolTip2 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel_back_side.SuspendLayout();
            this.pnl_side_datePrint.SuspendLayout();
            this.panel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.dmAll_Cards.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.panel1);
            this.pnlClientArea.Controls.Add(this.rjPanel_back_side);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.rjLabel4);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 558);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(192, 22);
            this.lblCaption.Text = "FormAllBatchsCards_Archive";
            // 
            // btnAddNew
            // 
            this.btnAddNew.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddNew.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddNew.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAddNew.BorderRadius = 8;
            this.btnAddNew.BorderSize = 1;
            this.btnAddNew.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnAddNew.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddNew.FlatAppearance.BorderSize = 0;
            this.btnAddNew.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnAddNew.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnAddNew.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddNew.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnAddNew.ForeColor = System.Drawing.Color.White;
            this.btnAddNew.IconChar = FontAwesome.Sharp.IconChar.PlusSquare;
            this.btnAddNew.IconColor = System.Drawing.Color.White;
            this.btnAddNew.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddNew.IconSize = 24;
            this.btnAddNew.Location = new System.Drawing.Point(666, 10);
            this.btnAddNew.Name = "btnAddNew";
            this.btnAddNew.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnAddNew.Size = new System.Drawing.Size(227, 34);
            this.btnAddNew.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddNew.TabIndex = 70;
            this.btnAddNew.Text = "اضافة دفعة الي الارشيف";
            this.btnAddNew.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAddNew.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAddNew.UseVisualStyleBackColor = false;
            this.btnAddNew.Click += new System.EventHandler(this.btnAddNew_Click);
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 1;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(448, 25);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 25);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 68;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Visible = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Tahoma", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(478, 24);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث - الباقة - الدفعة - التاريخ";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(197, 27);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 66;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.Visible = false;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 13;
            this.rjPanel1.Controls.Add(this.btn_All_cards);
            this.rjPanel1.Controls.Add(this.btn_Add_To_Mikrotik);
            this.rjPanel1.Controls.Add(this.btnAddNew);
            this.rjPanel1.Controls.Add(this.btnDelete);
            this.rjPanel1.Controls.Add(this.btnRefresh);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(14, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(962, 54);
            this.rjPanel1.TabIndex = 89;
            // 
            // btn_All_cards
            // 
            this.btn_All_cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_All_cards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_All_cards.BorderRadius = 8;
            this.btn_All_cards.BorderSize = 1;
            this.btn_All_cards.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_All_cards.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_All_cards.FlatAppearance.BorderSize = 0;
            this.btn_All_cards.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_All_cards.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_All_cards.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_All_cards.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_All_cards.ForeColor = System.Drawing.Color.White;
            this.btn_All_cards.IconChar = FontAwesome.Sharp.IconChar.Archive;
            this.btn_All_cards.IconColor = System.Drawing.Color.White;
            this.btn_All_cards.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_All_cards.IconSize = 24;
            this.btn_All_cards.Location = new System.Drawing.Point(358, 10);
            this.btn_All_cards.Name = "btn_All_cards";
            this.btn_All_cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_All_cards.Size = new System.Drawing.Size(249, 34);
            this.btn_All_cards.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_All_cards.TabIndex = 70;
            this.btn_All_cards.Text = "جميع كروت ارشيف المعلقات";
            this.btn_All_cards.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_All_cards.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_All_cards.UseVisualStyleBackColor = false;
            this.btn_All_cards.Click += new System.EventHandler(this.btn_All_cards_Click);
            // 
            // btn_Add_To_Mikrotik
            // 
            this.btn_Add_To_Mikrotik.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_To_Mikrotik.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_To_Mikrotik.BorderRadius = 8;
            this.btn_Add_To_Mikrotik.BorderSize = 1;
            this.btn_Add_To_Mikrotik.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Add_To_Mikrotik.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_Add_To_Mikrotik.FlatAppearance.BorderSize = 0;
            this.btn_Add_To_Mikrotik.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Add_To_Mikrotik.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Add_To_Mikrotik.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Add_To_Mikrotik.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Add_To_Mikrotik.ForeColor = System.Drawing.Color.White;
            this.btn_Add_To_Mikrotik.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.btn_Add_To_Mikrotik.IconColor = System.Drawing.Color.White;
            this.btn_Add_To_Mikrotik.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Add_To_Mikrotik.IconSize = 24;
            this.btn_Add_To_Mikrotik.Location = new System.Drawing.Point(15, 10);
            this.btn_Add_To_Mikrotik.Name = "btn_Add_To_Mikrotik";
            this.btn_Add_To_Mikrotik.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Add_To_Mikrotik.Size = new System.Drawing.Size(297, 34);
            this.btn_Add_To_Mikrotik.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Add_To_Mikrotik.TabIndex = 70;
            this.btn_Add_To_Mikrotik.Text = "استيراد كروت الارشيف الي الروتر";
            this.btn_Add_To_Mikrotik.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Add_To_Mikrotik.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Add_To_Mikrotik.UseVisualStyleBackColor = false;
            this.btn_Add_To_Mikrotik.Click += new System.EventHandler(this.btn_Add_To_Mikrotik_Click);
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderRadius = 5;
            this.btnDelete.BorderSize = 1;
            this.btnDelete.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnDelete.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btnDelete.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btnDelete.IconColor = System.Drawing.Color.White;
            this.btnDelete.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDelete.IconSize = 20;
            this.btnDelete.Location = new System.Drawing.Point(897, 9);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnDelete.Size = new System.Drawing.Size(29, 34);
            this.btnDelete.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDelete.TabIndex = 86;
            this.btnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDelete.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.toolTip1.SetToolTip(this.btnDelete, "حذف الكروت المحدده من الارشيف");
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click_1);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 1;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRefresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 18;
            this.btnRefresh.Location = new System.Drawing.Point(927, 9);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(29, 34);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 60;
            this.btnRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh, "تحديث");
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.rjButton2_Click);
            // 
            // btn_import_FromFile
            // 
            this.btn_import_FromFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_import_FromFile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_import_FromFile.BorderRadius = 8;
            this.btn_import_FromFile.BorderSize = 1;
            this.btn_import_FromFile.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_import_FromFile.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_import_FromFile.FlatAppearance.BorderSize = 0;
            this.btn_import_FromFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_import_FromFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_import_FromFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_import_FromFile.Font = new System.Drawing.Font("Droid Arabic Kufi", 8F);
            this.btn_import_FromFile.ForeColor = System.Drawing.Color.White;
            this.btn_import_FromFile.IconChar = FontAwesome.Sharp.IconChar.Download;
            this.btn_import_FromFile.IconColor = System.Drawing.Color.White;
            this.btn_import_FromFile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_import_FromFile.IconSize = 24;
            this.btn_import_FromFile.Location = new System.Drawing.Point(91, 16);
            this.btn_import_FromFile.Name = "btn_import_FromFile";
            this.btn_import_FromFile.Size = new System.Drawing.Size(274, 34);
            this.btn_import_FromFile.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_import_FromFile.TabIndex = 87;
            this.btn_import_FromFile.Text = "استيراد كروت من ملف الي الارشيف";
            this.btn_import_FromFile.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_import_FromFile.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_import_FromFile.UseVisualStyleBackColor = false;
            this.btn_import_FromFile.Visible = false;
            // 
            // btn_Filter
            // 
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.White;
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.White;
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 16;
            this.btn_Filter.Location = new System.Drawing.Point(8, 3);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_Filter.Size = new System.Drawing.Size(77, 34);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Filter.TabIndex = 87;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Visible = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // btn_DBCheck
            // 
            this.btn_DBCheck.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_DBCheck.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_DBCheck.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_DBCheck.BorderRadius = 5;
            this.btn_DBCheck.BorderSize = 1;
            this.btn_DBCheck.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_DBCheck.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_DBCheck.FlatAppearance.BorderSize = 0;
            this.btn_DBCheck.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_DBCheck.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_DBCheck.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_DBCheck.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_DBCheck.ForeColor = System.Drawing.Color.White;
            this.btn_DBCheck.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_DBCheck.IconColor = System.Drawing.Color.White;
            this.btn_DBCheck.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_DBCheck.IconSize = 18;
            this.btn_DBCheck.Location = new System.Drawing.Point(48, 43);
            this.btn_DBCheck.Name = "btn_DBCheck";
            this.btn_DBCheck.Size = new System.Drawing.Size(121, 34);
            this.btn_DBCheck.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_DBCheck.TabIndex = 60;
            this.btn_DBCheck.Text = "فحص قاعدة البيانات";
            this.btn_DBCheck.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_DBCheck.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_DBCheck.UseVisualStyleBackColor = false;
            this.btn_DBCheck.Visible = false;
            this.btn_DBCheck.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // rjPanel_back_side
            // 
            this.rjPanel_back_side.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel_back_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_back_side.BorderRadius = 13;
            this.rjPanel_back_side.Controls.Add(this.txt_SN_End);
            this.rjPanel_back_side.Controls.Add(this.rjLabel1);
            this.rjPanel_back_side.Controls.Add(this.txt_SN_Start);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_SN);
            this.rjPanel_back_side.Controls.Add(this.CBox_SN_Compar);
            this.rjPanel_back_side.Controls.Add(this.rjLabel11);
            this.rjPanel_back_side.Controls.Add(this.rjCheckBox1);
            this.rjPanel_back_side.Controls.Add(this.Date_To);
            this.rjPanel_back_side.Controls.Add(this.Date_From);
            this.rjPanel_back_side.Controls.Add(this.lbl_to);
            this.rjPanel_back_side.Controls.Add(this.rjToggleButton1);
            this.rjPanel_back_side.Controls.Add(this.btn_apply);
            this.rjPanel_back_side.Controls.Add(this.rjLabel25Title);
            this.rjPanel_back_side.Controls.Add(this.rjToggleButton2);
            this.rjPanel_back_side.Controls.Add(this.CBox_SellingPoint);
            this.rjPanel_back_side.Controls.Add(this.rjLabel2);
            this.rjPanel_back_side.Controls.Add(this.CBox_Profile);
            this.rjPanel_back_side.Controls.Add(this.rjLabel3);
            this.rjPanel_back_side.Controls.Add(this.pnl_side_datePrint);
            this.rjPanel_back_side.Customizable = false;
            this.rjPanel_back_side.Location = new System.Drawing.Point(725, 66);
            this.rjPanel_back_side.Margin = new System.Windows.Forms.Padding(0, 3, 3, 3);
            this.rjPanel_back_side.Name = "rjPanel_back_side";
            this.rjPanel_back_side.Size = new System.Drawing.Size(260, 461);
            this.rjPanel_back_side.TabIndex = 95;
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(8, 275);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(117, 28);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 112;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_SN_End.Visible = false;
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(201, 183);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(47, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 108;
            this.rjLabel1.Text = "الطباعة";
            this.rjLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.rjLabel1.Visible = false;
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(138, 275);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(112, 28);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 113;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_SN_Start.Visible = false;
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.Location = new System.Drawing.Point(175, 249);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 110;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            this.CheckBox_SN.Visible = false;
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(8, 246);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(117, 28);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 107;
            this.CBox_SN_Compar.Texts = "";
            this.CBox_SN_Compar.Visible = false;
            // 
            // rjLabel11
            // 
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(198, 246);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.Size = new System.Drawing.Size(52, 23);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 109;
            this.rjLabel11.Text = "التسلسل";
            this.rjLabel11.Visible = false;
            // 
            // rjCheckBox1
            // 
            this.rjCheckBox1.AutoSize = true;
            this.rjCheckBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox1.BorderSize = 1;
            this.rjCheckBox1.Check = false;
            this.rjCheckBox1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjCheckBox1.Customizable = false;
            this.rjCheckBox1.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjCheckBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjCheckBox1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox1.Location = new System.Drawing.Point(181, 183);
            this.rjCheckBox1.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjCheckBox1.Name = "rjCheckBox1";
            this.rjCheckBox1.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.rjCheckBox1.Size = new System.Drawing.Size(17, 21);
            this.rjCheckBox1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjCheckBox1.TabIndex = 111;
            this.rjCheckBox1.UseVisualStyleBackColor = true;
            this.rjCheckBox1.Visible = false;
            // 
            // Date_To
            // 
            this.Date_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_To.BorderRadius = 3;
            this.Date_To.BorderSize = 1;
            this.Date_To.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_To.Customizable = false;
            this.Date_To.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_To.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_To.Location = new System.Drawing.Point(8, 207);
            this.Date_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_To.Name = "Date_To";
            this.Date_To.Padding = new System.Windows.Forms.Padding(2);
            this.Date_To.Size = new System.Drawing.Size(120, 32);
            this.Date_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_To.TabIndex = 114;
            this.Date_To.Value = new System.DateTime(2024, 9, 24, 20, 3, 55, 357);
            this.Date_To.Visible = false;
            // 
            // Date_From
            // 
            this.Date_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_From.BorderRadius = 3;
            this.Date_From.BorderSize = 1;
            this.Date_From.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_From.Customizable = false;
            this.Date_From.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_From.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_From.Location = new System.Drawing.Point(134, 207);
            this.Date_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_From.Name = "Date_From";
            this.Date_From.Padding = new System.Windows.Forms.Padding(2);
            this.Date_From.Size = new System.Drawing.Size(120, 32);
            this.Date_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_From.TabIndex = 115;
            this.Date_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            this.Date_From.Visible = false;
            // 
            // lbl_to
            // 
            this.lbl_to.AutoSize = true;
            this.lbl_to.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_to.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_to.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_to.LinkLabel = false;
            this.lbl_to.Location = new System.Drawing.Point(92, 186);
            this.lbl_to.Name = "lbl_to";
            this.lbl_to.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_to.Size = new System.Drawing.Size(29, 22);
            this.lbl_to.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_to.TabIndex = 116;
            this.lbl_to.Text = "الي";
            this.lbl_to.Visible = false;
            // 
            // rjToggleButton1
            // 
            this.rjToggleButton1.Activated = false;
            this.rjToggleButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjToggleButton1.AutoSize = true;
            this.rjToggleButton1.Customizable = false;
            this.rjToggleButton1.Location = new System.Drawing.Point(185, 55);
            this.rjToggleButton1.MinimumSize = new System.Drawing.Size(50, 25);
            this.rjToggleButton1.Name = "rjToggleButton1";
            this.rjToggleButton1.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton1.OFF_Text = null;
            this.rjToggleButton1.OFF_TextColor = System.Drawing.Color.Gray;
            this.rjToggleButton1.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton1.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton1.ON_Text = null;
            this.rjToggleButton1.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjToggleButton1.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton1.Size = new System.Drawing.Size(50, 25);
            this.rjToggleButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjToggleButton1.TabIndex = 88;
            this.rjToggleButton1.Tag = "تفصيلي";
            this.rjToggleButton1.Text = "#";
            this.rjToggleButton1.UseVisualStyleBackColor = true;
            this.rjToggleButton1.Visible = false;
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 15;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Cairo Medium", 12F, System.Drawing.FontStyle.Bold);
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(34, 376);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.Size = new System.Drawing.Size(165, 40);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 106;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_apply.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Visible = false;
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Cairo Medium", 12F);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(70, 24);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(100, 30);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 105;
            this.rjLabel25Title.Text = "فلنره وتجميع";
            // 
            // rjToggleButton2
            // 
            this.rjToggleButton2.Activated = false;
            this.rjToggleButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjToggleButton2.AutoSize = true;
            this.rjToggleButton2.Customizable = false;
            this.rjToggleButton2.Location = new System.Drawing.Point(186, 119);
            this.rjToggleButton2.MinimumSize = new System.Drawing.Size(50, 25);
            this.rjToggleButton2.Name = "rjToggleButton2";
            this.rjToggleButton2.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton2.OFF_Text = null;
            this.rjToggleButton2.OFF_TextColor = System.Drawing.Color.Gray;
            this.rjToggleButton2.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.rjToggleButton2.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton2.ON_Text = null;
            this.rjToggleButton2.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjToggleButton2.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjToggleButton2.Size = new System.Drawing.Size(50, 25);
            this.rjToggleButton2.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjToggleButton2.TabIndex = 63;
            this.rjToggleButton2.Tag = "تفصيلي";
            this.rjToggleButton2.Text = "#";
            this.rjToggleButton2.UseVisualStyleBackColor = true;
            this.rjToggleButton2.Visible = false;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(11, 148);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(202, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            this.CBox_SellingPoint.Visible = false;
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(88, 124);
            this.rjLabel2.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(56, 23);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 35;
            this.rjLabel2.Text = "نقطع بيع";
            this.rjLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.rjLabel2.Visible = false;
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(11, 82);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(202, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            this.CBox_Profile.Visible = false;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(88, 60);
            this.rjLabel3.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.Size = new System.Drawing.Size(38, 23);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 35;
            this.rjLabel3.Text = "الباقه";
            this.rjLabel3.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.rjLabel3.Visible = false;
            // 
            // pnl_side_datePrint
            // 
            this.pnl_side_datePrint.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_side_datePrint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_datePrint.BorderRadius = 10;
            this.pnl_side_datePrint.Controls.Add(this.CheckBox_byDatePrint);
            this.pnl_side_datePrint.Controls.Add(this.rjLabel20);
            this.pnl_side_datePrint.Customizable = true;
            this.pnl_side_datePrint.Location = new System.Drawing.Point(33, 323);
            this.pnl_side_datePrint.Name = "pnl_side_datePrint";
            this.pnl_side_datePrint.Size = new System.Drawing.Size(202, 119);
            this.pnl_side_datePrint.TabIndex = 92;
            this.pnl_side_datePrint.Visible = false;
            // 
            // CheckBox_byDatePrint
            // 
            this.CheckBox_byDatePrint.AutoSize = true;
            this.CheckBox_byDatePrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.BorderSize = 1;
            this.CheckBox_byDatePrint.Check = false;
            this.CheckBox_byDatePrint.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDatePrint.Customizable = false;
            this.CheckBox_byDatePrint.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_byDatePrint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDatePrint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.Location = new System.Drawing.Point(177, 3);
            this.CheckBox_byDatePrint.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDatePrint.Name = "CheckBox_byDatePrint";
            this.CheckBox_byDatePrint.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_byDatePrint.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_byDatePrint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDatePrint.TabIndex = 42;
            this.CheckBox_byDatePrint.UseVisualStyleBackColor = true;
            // 
            // rjLabel20
            // 
            this.rjLabel20.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel20.AutoSize = true;
            this.rjLabel20.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel20.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel20.LinkLabel = false;
            this.rjLabel20.Location = new System.Drawing.Point(125, 29);
            this.rjLabel20.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel20.Name = "rjLabel20";
            this.rjLabel20.Size = new System.Drawing.Size(69, 23);
            this.rjLabel20.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel20.TabIndex = 35;
            this.rjLabel20.Text = "تاريخ الدفعة";
            this.rjLabel20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.btn_import_FromFile);
            this.panel1.Controls.Add(this.btn_search);
            this.panel1.Controls.Add(this.txt_search);
            this.panel1.Controls.Add(this.btn_Filter);
            this.panel1.Controls.Add(this.btn_DBCheck);
            this.panel1.Controls.Add(this.dgv);
            this.panel1.Location = new System.Drawing.Point(9, 66);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(712, 464);
            this.panel1.TabIndex = 96;
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv.ContextMenuStrip = this.dmAll_Cards;
            this.dgv.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(8, 3);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 35;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 35;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 35;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(701, 458);
            this.dgv.TabIndex = 29;
            this.dgv.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellDoubleClick);
            this.dgv.SelectionChanged += new System.EventHandler(this.dgv_SelectionChanged);
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.ViewCards_ToolStripMenuItem,
            this.PrintToMK_ToolStripMenuItem,
            this.اعادةطباعةملفPDFToolStripMenuItem,
            this.Delete_ToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(196, 92);
            // 
            // ViewCards_ToolStripMenuItem
            // 
            this.ViewCards_ToolStripMenuItem.Name = "ViewCards_ToolStripMenuItem";
            this.ViewCards_ToolStripMenuItem.Size = new System.Drawing.Size(195, 22);
            this.ViewCards_ToolStripMenuItem.Text = "عرض كروت الدفعه";
            this.ViewCards_ToolStripMenuItem.Click += new System.EventHandler(this.ViewCards_ToolStripMenuItem_Click);
            // 
            // PrintToMK_ToolStripMenuItem
            // 
            this.PrintToMK_ToolStripMenuItem.Name = "PrintToMK_ToolStripMenuItem";
            this.PrintToMK_ToolStripMenuItem.Size = new System.Drawing.Size(195, 22);
            this.PrintToMK_ToolStripMenuItem.Text = "طباعة الي المايكروتك";
            this.PrintToMK_ToolStripMenuItem.Click += new System.EventHandler(this.PrintToMK_ToolStripMenuItem_Click);
            // 
            // اعادةطباعةملفPDFToolStripMenuItem
            // 
            this.اعادةطباعةملفPDFToolStripMenuItem.Name = "اعادةطباعةملفPDFToolStripMenuItem";
            this.اعادةطباعةملفPDFToolStripMenuItem.Size = new System.Drawing.Size(195, 22);
            this.اعادةطباعةملفPDFToolStripMenuItem.Text = "اعادة طباعة ملف PDF";
            // 
            // Delete_ToolStripMenuItem
            // 
            this.Delete_ToolStripMenuItem.Name = "Delete_ToolStripMenuItem";
            this.Delete_ToolStripMenuItem.Size = new System.Drawing.Size(195, 22);
            this.Delete_ToolStripMenuItem.Text = "حذف الدفعه من الارشيف";
            this.Delete_ToolStripMenuItem.Click += new System.EventHandler(this.Delete_ToolStripMenuItem_Click);
            // 
            // rjLabel4
            // 
            this.rjLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(15, 530);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(284, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 35;
            this.rjLabel4.Text = "* اضغط بزر الماوس الايمن علي الجدول لمزيد من الخيارات";
            this.rjLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // FormAllBatchsCards_Archive
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "FormAllBatchsCards_Archive";
            this.ClientSize = new System.Drawing.Size(1000, 608);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "FormAllBatchsCards_Archive";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "FormAllBatchsCards_Archive";
            this.Load += new System.EventHandler(this.FormAllBatchsCards_Archive_Load);
            this.SizeChanged += new System.EventHandler(this.FormAllBatchsCards_Archive_SizeChanged);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel_back_side.ResumeLayout(false);
            this.rjPanel_back_side.PerformLayout();
            this.pnl_side_datePrint.ResumeLayout(false);
            this.pnl_side_datePrint.PerformLayout();
            this.panel1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.dmAll_Cards.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion
        private RJControls.RJButton btnAddNew;
        private RJControls.RJButton btn_search;
        private RJControls.RJTextBox txt_search;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJButton btnDelete;
        private RJControls.RJButton btnRefresh;
        private RJControls.RJPanel rjPanel_back_side;
        private RJControls.RJToggleButton rjToggleButton1;
        private RJControls.RJButton btn_apply;
        private RJControls.RJLabel rjLabel25Title;
        private RJControls.RJPanel pnl_side_datePrint;
        private RJControls.RJCheckBox CheckBox_byDatePrint;
        private RJControls.RJToggleButton rjToggleButton2;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel20;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel3;
        private System.Windows.Forms.Panel panel1;
        private RJControls.RJButton btn_DBCheck;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJCheckBox rjCheckBox1;
        private RJControls.RJDatePicker Date_To;
        private RJControls.RJDatePicker Date_From;
        private RJControls.RJLabel lbl_to;
        private RJControls.RJButton btn_Add_To_Mikrotik;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem PrintToMK_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Delete_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ViewCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem اعادةطباعةملفPDFToolStripMenuItem;
        private System.Windows.Forms.ToolTip toolTip1;
        private RJControls.RJButton btn_import_FromFile;
        private RJControls.RJLabel rjLabel4;
        private System.Windows.Forms.ToolTip toolTip2;
        private RJControls.RJButton btn_All_cards;
    }
}