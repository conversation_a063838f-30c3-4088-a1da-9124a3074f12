﻿using DevComponents.DotNetBar.Controls;
using iTextSharp.text;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.RJForms.Private;
using SmartCreator.Settings;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormProfileUserManager : RJChildForm
    {
        Sql_DataAccess Local_DB;
        public FormProfileUserManager()
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            Init_dgv();
            //loadData();
            this.Text = "باقات اليوزمنجر";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "UserManager Profiles";
            }
            Local_DB = new Sql_DataAccess();

            System.Drawing.Font menu_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10 , FontStyle.Bold);
            btnAddNew.Font=btnDelete.Font=btnEdit.Font= btnRefresh.Font = menu_font;

            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = (int)(45 );

            //dgv.DefaultCellStyle.Font = new System.Drawing.Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);
            
            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);

            utils utils = new utils();
            utils.Control_textSize1(this);



        }
        private void loadData()
        {

            dgv.DataSource = Global_Variable.UM_Profile;
            foreach (DataGridViewRow r in dgv.Rows)
            {
                string uptime = r.Cells["UptimeLimit"].Value.ToString();
                string validay = r.Cells["Validity"].Value.ToString();
                string download = r.Cells["TransferLimit"].Value.ToString();
                string name = r.Cells["Name"].Value.ToString();

                if ((uptime == "0" || uptime == "" || uptime == "مفتوح") && (download == "0" || download == "" || download == "مفتوح") && (validay == "0" || validay == "" || validay == "مفتوح") && (download == "0" || download == "" || download == "مفتوح")  /*download == "مفتوح"*/)
                {
                    r.DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                    //r.DefaultCellStyle.ForeColor = Color.Red;
                }
                try
                {
                    //Local_DB.Get_int_FromDB($"select count(Sn) form UmUser where ProfileName='{name}' and DeleteFromServer=0");
                    r.Cells["CountCards"].Value = Local_DB.Get_int_FromDB($"select count(Sn) from UmUser where ProfileName='{name}' and DeleteFromServer=0").ToString();
                }
                catch { }

            }
        }

        private void Init_dgv()
        {
            
            dgv.AutoGenerateColumns = false;

            dgv.Columns.Add("Id", "Id");
            dgv.Columns.Add("IdHX", "IdHX");
            dgv.Columns.Add("Name", "الاسم");
            dgv.Columns.Add("Str_UptimeLimit", "الوقت");
            dgv.Columns.Add("Validity", "الصلاحية");
            dgv.Columns.Add("Str_transferLimit", "التحميل");
            dgv.Columns.Add("Price", "السعر");
            dgv.Columns.Add("CountCards", "عدد الكروت المرتبطه");
            dgv.Columns.Add("SharedUsers", "المستخدمين");
            dgv.Columns.Add("GroupName", "المجموعه");
            dgv.Columns.Add("CountLimit", "عدد التحديدات");
            dgv.Columns.Add("Speed", "السرعة");
            dgv.Columns.Add("Sn_Name", "Sn_Name");

            dgv.Columns.Add("UptimeLimit", "UptimeLimit");
            dgv.Columns.Add("TransferLimit", "TransferLimit");


            
            //DataGridViewButtonColumn EditButton = new DataGridViewButtonColumn();
            ////EditButton.UseColumnTextForButtonValue = true;
            //EditButton.DataPropertyName = "btnRemove";
            //EditButton.Text = "حذف";
            //EditButton.FlatStyle= FlatStyle.Flat;

            //dgv.Columns.Add(EditButton);

            

            dgv.Columns["Id"].DataPropertyName = "Id";
            dgv.Columns["IdHX"].DataPropertyName = "IdHX";
            dgv.Columns["Name"].DataPropertyName = "Name";
            dgv.Columns["Str_UptimeLimit"].DataPropertyName = "Str_UptimeLimit";
            dgv.Columns["Validity"].DataPropertyName = "Validity";
            dgv.Columns["Str_transferLimit"].DataPropertyName = "Str_transferLimit";
            dgv.Columns["Price"].DataPropertyName = "Price";
            dgv.Columns["SharedUsers"].DataPropertyName = "SharedUsers";
            dgv.Columns["GroupName"].DataPropertyName = "GroupName";
            dgv.Columns["Speed"].DataPropertyName = "Speed";
            dgv.Columns["UptimeLimit"].DataPropertyName = "UptimeLimit";
            dgv.Columns["TransferLimit"].DataPropertyName = "TransferLimit";
            dgv.Columns["CountLimit"].DataPropertyName = "CountLimit";
            dgv.Columns["Sn_Name"].DataPropertyName = "Sn_Name";
            dgv.Columns["CountCards"].DataPropertyName = "CountCards";

            dgv.Columns["UptimeLimit"].Visible = false;
            dgv.Columns["TransferLimit"].Visible = false;
            dgv.Columns["CountLimit"].Width = 180;
            dgv.Columns["CountCards"].Width = 200;
            //dgv.Columns["Sn_Name"].Visible = false;

            dgv.Columns["Id"].Visible = false;
            dgv.Columns["IdHX"].Visible = false;
            dgv.Columns["Sn_Name"].Visible = false;
            dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);

            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersHeight = 40;
           
        }

        [Obsolete]
        private void btnRemove_Click(object sender, EventArgs e)
        {
            double CountCards = 0;
            try
            {
                CountCards = Convert.ToDouble(dgv.CurrentRow.Cells["CountCards"].Value.ToString());
            }
            catch { }
            //return;

            DialogResult = RJMessageBox.Show("اذا تم حذف الباقة وهناك كروت بهذه الباقه لن تعمل الكروت", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (DialogResult == DialogResult.No)
                return;

            if (CountCards > 0)
            {
                DialogResult = RJMessageBox.Show($"هذه الباقة مرتبط بها {CountCards} كرت . اذا تم حذفها لن تعمل الكروت ", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                DialogResult = RJMessageBox.Show("هل تريد حذف الباقة", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (DialogResult == DialogResult.No)
                    return;
            }
            //DialogResult = RJMessageBox.Show("هل تريد حذف الباقة", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);



            string IdHX = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.IdHX == IdHX);

            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            if (mk_DataAccess.DeletProfileUsermanager(profile))
            {
                mk_DataAccess.DeletLimition_Usermanager(profile);
                //RJMessageBox.Show("تم");
                refersh_From_MK();
                loadData();

            }
            else
                RJMessageBox.Show("خطاء");


        }

        [Obsolete]
        private void btnAddNew_Click(object sender, EventArgs e)
        {
            var frmProfile = new FormAdd_Edit_Profile();
            frmProfile.ShowDialog();
            
            if(frmProfile.succes)
            {
                refersh_From_MK();
                loadData();
            }
        }
        [Obsolete]
        void refersh_From_MK()
        {
            try
            {
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                Global_Variable.Source_limtition = mk_DataAccess.GetSource_UserManager_Limit();
                Global_Variable.Source_profile = mk_DataAccess.GetSource_UserManager_Profile();
                Global_Variable.Source_profile_limtition = mk_DataAccess.GetSource_UserManager_Profile_Limtition();
                //Global_Variable.Source_profile_limtition = Mk_DataAccess.GetSource_UserManager_Profile_Limtition();

                UmProfile umProfile = new UmProfile();
                umProfile.Get_UMProfile();
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            refersh_From_MK();
            loadData();
        }

        [Obsolete]
        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                //return;
                if (dgv.Rows.Count <= 0)
                    return;
                DialogResult = RJMessageBox.Show("اذا تم حذف الباقة وهناك كروت بهذه الباقه لن تعمل الكروت", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (DialogResult == DialogResult.No)
                    return;

                DialogResult = RJMessageBox.Show("هل تريد حذف الباقة", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (DialogResult == DialogResult.No)
                    return;

                string IdHX = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
                UmProfile profile = Global_Variable.UM_Profile.Find(x => x.IdHX == IdHX);

                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                if (mk_DataAccess.DeletProfileUsermanager(profile))
                {
                    mk_DataAccess.DeletLimition_Usermanager(profile);
                    mk_DataAccess.Delet_ProfileLimition_Usermanager(profile);
                    //RJMessageBox.Show("تم");
                    refersh_From_MK();
                    loadData();

                }
                else
                    RJMessageBox.Show("خطاء");
            }
            catch { }

            
        }
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            loadData();
        }

        private void FormProfileUserManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }
        [Obsolete]

        public void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                if (dgv.Rows.Count == 0)
                    return;

                int id = Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString());
                string Sn_Name = (dgv.CurrentRow.Cells["Sn_Name"].Value.ToString());
                string rb_code = Global_Variable.Mk_resources.RB_code;
                string rb = Global_Variable.Mk_resources.RB_SN;

                UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Sn_Name == Sn_Name && (x.Rb == rb || x.Rb == rb_code));
                //UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Id == id);
                if (profile.CountLimit > 1)
                {
                    var frm = new FormAdd_Edit_Profile_Multi_Limit(profile);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        refersh_From_MK();
                        loadData();
                    }
                }
                else
                {
                    var frm = new FormAdd_Edit_Profile(profile);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        refersh_From_MK();
                        loadData();
                    }
                }
            }
            catch { }

        }

        [Obsolete]
        void openForm()
        {
            int id = Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString());
            string Sn_Name = (dgv.CurrentRow.Cells["Sn_Name"].Value.ToString());
            string rb_code = Global_Variable.Mk_resources.RB_code;
            string rb = Global_Variable.Mk_resources.RB_SN;

            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Sn_Name == Sn_Name && (x.Rb == rb || x.Rb == rb_code));
            //UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Id == id);
            if (profile.CountLimit > 1)
            {
                var frm = new FormAdd_Edit_Profile_Multi_Limit(profile);
                frm.add = false;
                frm.ShowDialog();
                if (frm.succes)
                {
                    refersh_From_MK();
                    loadData();
                }
            }
            else
            {
                var frm = new FormAdd_Edit_Profile(profile);
                frm.add = false;
                frm.ShowDialog();
                if (frm.succes)
                {
                    refersh_From_MK();
                    loadData();
                }
            }

        }

        [Obsolete]
        private void dgv_CellMouseDoubleClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                string idd = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
                int id = Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString());
                string Sn_Name = (dgv.CurrentRow.Cells["Sn_Name"].Value.ToString());
                string rb_code = Global_Variable.Mk_resources.RB_code;
                string rb = Global_Variable.Mk_resources.RB_SN;

                UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Sn_Name == Sn_Name && (x.Rb == rb || x.Rb == rb_code));
                //UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Id == id);
                if (profile.CountLimit > 1)
                {
                    var frm = new FormAdd_Edit_Profile_Multi_Limit(profile);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        refersh_From_MK();
                        loadData();
                    }
                }
                else
                {
                    var frm = new FormAdd_Edit_Profile(profile);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        refersh_From_MK();
                        loadData();
                    }
                }
            }

        }
    }
}
