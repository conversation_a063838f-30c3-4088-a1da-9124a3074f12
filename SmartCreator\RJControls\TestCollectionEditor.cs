using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Drawing.Design;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار Collection Editor مبسط
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class TestCollectionEditor : Panel
    {
        private List<RJTabPage> _testTabs;
        private TestTabCollection _collection;

        public TestCollectionEditor()
        {
            _testTabs = new List<RJTabPage>();
            _collection = new TestTabCollection(this);
            
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.Size = new Size(400, 300);
        }

        /// <summary>
        /// مجموعة التابات للاختبار
        /// </summary>
        [Category("Test Collection")]
        [Description("Test collection for tabs")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Editor(typeof(TestTabCollectionEditor), typeof(UITypeEditor))]
        public TestTabCollection TestTabs
        {
            get { return _collection; }
        }

        internal void AddTestTab(RJTabPage tab)
        {
            if (tab != null && !_testTabs.Contains(tab))
            {
                _testTabs.Add(tab);
                // يمكن إضافة منطق إضافي هنا
            }
        }

        internal void RemoveTestTab(RJTabPage tab)
        {
            if (tab != null)
            {
                _testTabs.Remove(tab);
                // يمكن إضافة منطق إضافي هنا
            }
        }
    }

    /// <summary>
    /// مجموعة اختبار مبسطة
    /// </summary>
    public class TestTabCollection : IList<RJTabPage>
    {
        private List<RJTabPage> _items;
        private TestCollectionEditor _parent;

        public TestTabCollection(TestCollectionEditor parent)
        {
            _items = new List<RJTabPage>();
            _parent = parent;
        }

        public int Count => _items.Count;
        public bool IsReadOnly => false;

        public RJTabPage this[int index]
        {
            get => _items[index];
            set
            {
                var oldTab = _items[index];
                _items[index] = value;
                _parent?.RemoveTestTab(oldTab);
                _parent?.AddTestTab(value);
            }
        }

        public void Add(RJTabPage item)
        {
            if (item != null)
            {
                _items.Add(item);
                _parent?.AddTestTab(item);
            }
        }

        public void Insert(int index, RJTabPage item)
        {
            if (item != null)
            {
                _items.Insert(index, item);
                _parent?.AddTestTab(item);
            }
        }

        public bool Remove(RJTabPage item)
        {
            var removed = _items.Remove(item);
            if (removed) _parent?.RemoveTestTab(item);
            return removed;
        }

        public void RemoveAt(int index)
        {
            var item = _items[index];
            _items.RemoveAt(index);
            _parent?.RemoveTestTab(item);
        }

        public void Clear()
        {
            var itemsCopy = new List<RJTabPage>(_items);
            _items.Clear();
            foreach (var item in itemsCopy)
                _parent?.RemoveTestTab(item);
        }

        public bool Contains(RJTabPage item) => _items.Contains(item);
        public void CopyTo(RJTabPage[] array, int arrayIndex) => _items.CopyTo(array, arrayIndex);
        public int IndexOf(RJTabPage item) => _items.IndexOf(item);

        public IEnumerator<RJTabPage> GetEnumerator() => _items.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    /// <summary>
    /// محرر مجموعة اختبار مبسط
    /// </summary>
    public class TestTabCollectionEditor : CollectionEditor
    {
        public TestTabCollectionEditor(Type type) : base(type)
        {
        }

        protected override Type CreateCollectionItemType()
        {
            return typeof(RJTabPage);
        }

        protected override Type[] CreateNewItemTypes()
        {
            return new Type[] { typeof(RJTabPage) };
        }

        protected override object CreateInstance(Type itemType)
        {
            if (itemType == typeof(RJTabPage))
            {
                try
                {
                    // إنشاء تاب بسيط جداً
                    var tab = new RJTabPage("New Tab");
                    tab.BackColor = Color.FromArgb(70, 70, 70);
                    tab.ForeColor = Color.White;
                    return tab;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"Error creating tab: {ex.Message}", "Debug", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return new RJTabPage("Default Tab");
                }
            }
            return base.CreateInstance(itemType);
        }

        protected override bool CanCreateInstance(Type itemType)
        {
            return itemType == typeof(RJTabPage);
        }

        protected override string GetDisplayText(object value)
        {
            if (value is RJTabPage tab)
                return !string.IsNullOrEmpty(tab.Text) ? tab.Text : "Unnamed Tab";
            return base.GetDisplayText(value);
        }

        protected override bool CanRemoveInstance(object value)
        {
            return true;
        }

        protected override bool CanSelectMultipleInstances()
        {
            return false;
        }
    }
}
