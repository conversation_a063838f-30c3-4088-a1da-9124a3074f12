﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_AllSession_UserManager : RJChildForm
    {
        bool firstLoad = true;
        string ServerType = "UM";
        string TableSession = "UmSession";
        string OrderKey = "sn";
        Dictionary<int, Dgv_Header_Values> dvalue;
        Dgv_Header_Proprties Dgv_State_list = null;

        private  int PageSize = 50000;
        private  int currentPageindex = 1;
        private  int totalPages = 0;
        private  int totalRows = 0;
        Sql_DataAccess Local_DA;

        private bool Dgv_Click_ToChangIndex = false;
        public Form_AllSession_UserManager(string serverType = "UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            if (serverType == "HS")
            {
                TableSession = "HsSession";
                ServerType = "HS";
                OrderKey = "Id";
            }
            Local_DA = new Sql_DataAccess();
            sideMenu();
            Date_To.Value = DateTime.Now;
            Date_From.Value = DateTime.Now.AddMonths(-4);
            Smart_DataAccess sd = new Smart_DataAccess();

            //dbFactory = Sql_DataAccess.Get_dbFactory();
            this.Text = "جلسات اليوزمنجر";

            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Sessions UserManager";
            }

            //dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            CBox_PageCount.SelectedIndex = 9;
            lbl_Filter.Font =rjLabel1.Font = rjLabel10.Font = rjLabel13.Font = rjLabel20.Font = rjLabel3.Font = rjLabel4.Font = rjLabel5.Font = rjLabel6.Font = rjLabel7.Font = rjLabel8.Font =
                 rjLabel9.Font = rjPanel1.Font = rjPanel3.Font = CBox_SearchBy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //dm_Session.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            rjLabel25Title.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12 , FontStyle.Bold);

            CBox_SearchBy.SelectedIndex = 0;
            CBox_PageCount.label.TextAlign = ContentAlignment.MiddleRight;



            Date_From.RightToLeft =
            Date_To.RightToLeft =
            txtAllCountRows.RightToLeft =
            txtCurrentPageindex.RightToLeft =
            txtTotalPages.RightToLeft =
            txt_search.RightToLeft =
            CBox_PageCount.RightToLeft =
                RightToLeft.No;

            Date_From.dateText.TextAlign = Date_To.dateText.TextAlign = CBox_PageCount.label.TextAlign = ContentAlignment.MiddleRight;
            utils.item_Contrlol_textSize(dm_Session);
            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);


            pnl_side_datePrint.BackColor = Color.White;
            if (UIAppearance.Theme == UITheme.Dark)
            {
                pnl_side_datePrint.Customizable = false;
            }


        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            sideMenu();
        }
        void sideMenu()
        {
            //return;
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);
            }
            else
            {
                rjPanel_back_side.Width = utils.Control_Mesur_DPI(230);
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 13, panel1.Location.Y - 1);
                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Height-(panel1.Height - dgv.Location.Y));
            }
            rjPanel3.Width = dgv.Width;
            panel1.Refresh();
            this.Refresh();
        }
       
        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;
            DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Sessions");

            if (ServerType=="HS")
            DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Sessions_HS");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                SaveFromState();
                return;
            }
            
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
                return;
            dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv.Columns[dv.Index].Visible = dv.Visable;
                    dgv.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;
                    dgv.Columns[dv.Index].Width = dv.Width;

                    //foreach (ToolStripMenuItem control in View_Hide_toolStripMenuItem.DropDownItems)
                    //{
                    //    //if (control.HasDropDownItems)
                    //    if (control.GetType() == typeof(ToolStripMenuItem))
                    //    {
                    //        if (control.Tag != null)
                    //            if (control.Tag.ToString().ToLower() == dv.Name.ToLower())
                    //            {
                    //                control.Checked = dv.Visable;
                    //            }
                    //    }

                    //}
                }
                catch { }
            }

            try { dgv.Columns["Id"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["Status"].Visible = false; } catch { }
            try { dgv.Columns["Disabled"].Visible = false; } catch { }
            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }
        }

        public void LoadDataGridviewData()
        {

            //lbl_Filter.Text = "";
            //lbl_Filter.Visible = false;
            //"الاسم\r\nالماك\r\nالايبي\r\nالروتر-(راديوس)\r\nالجهاز (بورت)"
            //List<UmSession> users = null;
            string search = "";
            string sql_where = "";
            string sql_select = "";

            if (Toggle_FromServer.Checked) sql_where = " where DeleteFromServer = 0 ";
            if (Toggle_FromArchiveOnly.Checked) sql_where = "where DeleteFromServer = 1 ";


            try
            {

                if (txt_search.Text.Trim() != "")
                {
                    if (CBox_SearchBy.SelectedIndex == 0)
                        search += " UserName LIKE '%" + txt_search.Text + "%' ";
                    else if (CBox_SearchBy.SelectedIndex == 1)
                        search += " CallingStationId LIKE '%" + txt_search.Text + "%' ";

                    else if (CBox_SearchBy.SelectedIndex == 2)
                        search += " IpUser LIKE '%" + txt_search.Text + "%' ";
                    else if (CBox_SearchBy.SelectedIndex == 3)
                        search += " IpRouter LIKE '%" + txt_search.Text + "%' ";

                    else if (CBox_SearchBy.SelectedIndex == 4)
                        search += " NasPortId LIKE '%" + txt_search.Text + "%' ";

                    if (sql_where == "")
                        sql_where = $" where {search}";
                    else
                        sql_where += $" and {search}";

                }




                if (CheckBox_byDatePrint.Check)
                {
                    if (sql_where != "")
                        sql_where += "and FromTime >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND FromTime <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                    else
                        sql_where += "where FromTime >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND FromTime <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";

                }

                sql_select = $"select * from {TableSession} {sql_where}  LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                //sql_select = $"select * from {TableSession} {sql_where} ORDER BY {OrderKey} DESC LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";

                if (Toggle_FromArchiveOnly.Checked || Toggle_FromServer.Checked)
                    sql_select = $"select * from {TableSession}  {sql_where}  LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    //sql_select = $"select * from {TableSession}  {sql_where}  ORDER BY {OrderKey} DESC LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";

                string co = $"select count(*) from {TableSession}  " + sql_where;
                totalRows = (int)Local_DA.Get_int_FromDB(co);
                totalPages = (int)Math.Ceiling((double)totalRows / PageSize);

                //sql = $"select * from UmSession LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";

                txtTotalPages.Text = totalPages.ToString();
                txtCurrentPageindex.Text = currentPageindex.ToString();
                txtAllCountRows.Text = totalRows.ToString();
                
                if (ServerType == "UM")
                    dgv.DataSource = Local_DA.Load<UmSession>(sql_select);
                else
                    dgv.DataSource = Local_DA.Load<HsSession>(sql_select);


            }
            catch { }
             
            update_select_DGV();
            Init_dgv_to_Default();
            //try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            //try { dgv.Columns["Id"].Visible = false; } catch { }
            //try { dgv.Columns["Fk_Sn_Name"].Visible = false; } catch { }
            //try { dgv.Columns["IdHX"].Visible = false; } catch { }
            ////try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            //try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }

        }


        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            //Get_Cbox_Profile();
            //Get_SellingPoint();

            LoadDataGridviewData();
            Init_dgv_to_Default();
            //loadDgvState();
            firstLoad = false;
        }
        private void Init_dgv_to_Default()
        {
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.Visible = false;
                }

                dgv.Columns["UserName"].Visible = true;
                dgv.Columns["UserName"].DisplayIndex = 1;

                dgv.Columns["FromTime"].Visible = true;
                dgv.Columns["FromTime"].DisplayIndex = 2;
                dgv.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["TillTime"].Visible = true;
                dgv.Columns["TillTime"].DisplayIndex = 3;
                dgv.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["Str_UptimeUsed"].Visible = true;
                dgv.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                dgv.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["Str_DownloadUsed"].Visible = true;
                dgv.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                dgv.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["Str_UploadUsed"].Visible = true;
                dgv.Columns["Str_UploadUsed"].DisplayIndex = 6;
                dgv.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["CallingStationId"].Visible = true;
                dgv.Columns["CallingStationId"].DisplayIndex = 7;
                dgv.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                dgv.Columns["IpUser"].Visible = true;
                dgv.Columns["IpUser"].DisplayIndex = 8;
                dgv.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["NasPortId"].Visible = true;
                dgv.Columns["NasPortId"].DisplayIndex = 9;

                dgv.Columns["IpRouter"].Visible = true;
                dgv.Columns["IpRouter"].DisplayIndex = 10;
                dgv.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
                try { dgv.Columns["IdHX"].Visible = false;} catch { }

            }
            catch { }


        }
        
        
        private void Get_UMCustomer()
        {
            //try
            //{
            //    List<UserManager_Customer> sp = Global_Variable.UM_Customer;
            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("0", "");
            //    foreach (UserManager_Customer s in sp)
            //        comboSource.Add(s.Name, s.Name);

            //    CBox_Customer.DataSource = new BindingSource(comboSource, null);
            //    CBox_Customer.DisplayMember = "Value";
            //    CBox_Customer.ValueMember = "Key";
            //    CBox_Customer.SelectedIndex = 0;
            //    CBox_Customer.Text = "";
            //}
            //catch { }
        }
        private void Get_Batch_cards()
        {
            //try
            //{
            //    List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    comboSource.Add(0, "");
            //    foreach (Class_Batch_cards s in sp)
            //        comboSource.Add((int)s.batchNumber, s.Str_name);

            //    CBox_Batch.DataSource = new BindingSource(comboSource, null);
            //    CBox_Batch.DisplayMember = "Value";
            //    CBox_Batch.ValueMember = "Key";
            //    CBox_Batch.SelectedIndex = 0;
            //    CBox_Batch.Text = "";
            //}
            //catch { }
        }
        private void Get_Status()
        {
            //try
            //{

            //    //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    comboSource.Add(-1, "");
            //    comboSource.Add(0, "انتظار");
            //    comboSource.Add(1, "نشط");
            //    comboSource.Add(2, "منتهي");
            //    comboSource.Add(5, "معطل");
            //    comboSource.Add(3, "خطاء في الباقة");

            //    CBox_Staus.DataSource = new BindingSource(comboSource, null);
            //    CBox_Staus.DisplayMember = "Value";
            //    CBox_Staus.ValueMember = "Key";
            //    CBox_Staus.SelectedIndex = 0;
            //    CBox_Staus.Text = "";
            //}
            //catch { }
        }
         public void SaveFromState()
        {
            try
            {
                Dgv_State_list = new Dgv_Header_Proprties();
                dvalue = new Dictionary<int, Dgv_Header_Values>();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    Dgv_Header_Values dgv_Header_Values = new Dgv_Header_Values();
                    dgv_Header_Values.Visable = column.Visible;
                    dgv_Header_Values.HeaderText = column.HeaderText;
                    dgv_Header_Values.Name = column.Name;
                    dgv_Header_Values.DisplayIndex = column.DisplayIndex;
                    dgv_Header_Values.Index = column.Index;
                    dgv_Header_Values.Width = column.Width;

                    dvalue[column.Index] = dgv_Header_Values;
                }
                Dgv_State_list.items = dvalue;

                string formSetting = JsonConvert.SerializeObject(Dgv_State_list);


                if (ServerType == "HS")

                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_Sessions_HS", "SaveControlState", formSetting);
                else
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_Sessions", "SaveControlState", formSetting);


            }
            catch { }
        }
        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
       
        //private void btnRefresh_Click(object sender, EventArgs e)
        //{

        //    LoadDataGridviewData();
        //}
        [Obsolete]
        private void btnRefresh_Click(object sender, EventArgs e)
        {

            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;


            //RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر");
            Thread thread = new Thread(Refresh_formMK);
            Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();
        }

        [Obsolete]
        private void Refresh_formMK()
        {
            try
            {

                if (ServerType == "UM")
                {
                    int count_process = 4;
                    UserManagerProcess u = new UserManagerProcess();

                    //Global_Variable.Update_Um_StatusBar(false, true, 60, "", "يقوم الان بجلب جيمع جلسات اليوزمنجر من الروتر");

                    //Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions("", true);

                    Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(2 * (100.0 / count_process)));

                    Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                    //RJMessageBox.Show("يتم finsh get session");

                    Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(3 * (100.0 / count_process)));

                    Global_Variable.Update_StatusBar_StartSyn();


                    Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(4 * (100.0 / count_process)));

                    if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                        u.Syn_Session_to_LocalDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(0 * (100.0 / count_process)));

                    Global_Variable.Update_StatusBar_StopSyn();


                }
                else if (ServerType == "HS")
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 60, "", "يقوم الان بجلب جيمع جلسات الهوتسبوت من الروتر");

                    SourceCardsHotspot_fromMK.Get_HS_Session("", true, true);
                }
                //Global_Variable.Update_Um_StatusBar(false, true, 90, "", "يتم الان مزامنه البيانات ");
                //UserManagerProcess SessionManagerProcess = new UserManagerProcess();
                //SessionManagerProcess.Syn_UM_Users_to_LocalDB();
                //SessionManagerProcess.Syn_Pyments_to_LocalDB();

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                  (System.Windows.Forms.MethodInvoker)delegate ()
                  {
                      LoadDataGridviewData();
                  });
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب الجلسات  من الروتر");
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false; }

            Global_Variable.StartThreadProcessFromMK = false;
        }


        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV();
        }

        private void Form_AllSession_UserManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void Toggle_FromServer_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            //LoadDataGridviewData();
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;


            if (Toggle_FromServer.Checked)
            {
                firstLoad = true;
                Toggle_FromArchiveOnly.Checked = false;
                Toggle_FromServerArchtive.Checked = false;
               LoadDataGridviewData();
                firstLoad = false;
            }
            
             
        }

        private void Toggle_FromServerArchtive_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            if (Toggle_FromServerArchtive.Checked)
            {
                firstLoad = true;
                Toggle_FromArchiveOnly.Checked = false;
                Toggle_FromServer.Checked = false;
                LoadDataGridviewData();
                firstLoad = false;
            }
        }

        private void Toggle_FromArchiveOnly_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            if (Toggle_FromArchiveOnly.Checked)
            {
                firstLoad = true;
                Toggle_FromServer.Checked = false;
                Toggle_FromServerArchtive.Checked = false;
                LoadDataGridviewData();
                firstLoad = false;
            }
        }

        private void CBox_PageCount_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if(firstLoad)
                return;

            //if(CBox_PageCount.SelectedIndex != 0)
            PageSize = Convert.ToInt32(CBox_PageCount.Text);
            if (PageSize> totalRows)
                PageSize= totalRows;

            LoadDataGridviewData();
        }

        private void dgv_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {

            //int status = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Status"].Value);
            //int disabled = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Disabled"].Value);
            int DeleteFromServer = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["DeleteFromServer"].Value);

            if (DeleteFromServer == 1)
            {
                dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
            }

            //else if (this.dgv.Columns[e.ColumnIndex].Name == "Str_Status")
            //{
            //    if (status == 1)
            //    {
            //        e.CellStyle.BackColor = Color.Red;
            //        e.CellStyle.ForeColor = Color.White;
            //    }
            //    else if (status == 2)
            //    {
            //        e.CellStyle.BackColor = Color.Yellow;
            //        e.CellStyle.ForeColor = Color.Black;
            //        //row.DefaultCellStyle.ForeColor = Color.Yellow;

            //    }
            //    else if (status == 3)
            //    {
            //        e.CellStyle.BackColor = Color.Black;
            //        e.CellStyle.ForeColor = Color.White;
            //        //row.DefaultCellStyle.ForeColor = Color.Black;

            //    }
            //    if (disabled == 1)
            //    {
            //        dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.Silver;

            //        e.CellStyle.BackColor = Color.Silver;
            //        e.CellStyle.ForeColor = Color.White;
            //        //e.DefaultCellStyle.ForeColor = Color.Silver;
            //    }

            //}
            //return;

        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            if(currentPageindex < totalPages)
            {
                currentPageindex++;
                LoadDataGridviewData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (currentPageindex > 1 )
            {
                currentPageindex--;
                LoadDataGridviewData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            currentPageindex = 1;
            LoadDataGridviewData();
            txtCurrentPageindex.Text= currentPageindex.ToString();
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            currentPageindex = totalPages;
            LoadDataGridviewData();
            txtCurrentPageindex.Text = currentPageindex.ToString();

        }

        private void btnRefresh_DB_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
        }
        DataGridViewCell ActiveCell = null;
        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void نسخToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.Rows.Count <= 0)
                return;

            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.Rows.Count <= 0)
                return;
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
        }

        private void dgv_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            //if (e.RowIndex >= 0)
            //{
            //    //if (e.Value.GetType()
            //    e.PaintBackground(e.CellBounds, true);
            //    TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.VerticalCenter);
            //    //TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.Default | TextFormatFlags.VerticalCenter );
            //    e.Handled = true;

            //}
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {

        }
    }
}
