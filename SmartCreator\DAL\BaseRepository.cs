﻿using Dapper;
//using SmartCreator.Data;
using SmartCreator.Helpers;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace SmartCreator.DAL
{
    public abstract class BaseRepository<T> where T : class
    {
        protected readonly DatabaseHelper _databaseHelper;
        protected readonly string _tableName;

        protected BaseRepository(string tableName)
        {
            _databaseHelper = new DatabaseHelper();
            _tableName = tableName;
        }

        protected SQLiteConnection GetConnection()
        {
            return new SQLiteConnection(_databaseHelper.ConnectionString);
        }

        public virtual async Task<IEnumerable<T>> GetAllAsync()
        {
            using (var connection = GetConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE IsDeleted = 0 ORDER BY Id";
                return await connection.QueryAsync<T>(sql);
            }
        }

        public virtual async Task<T> GetByIdAsync(int id)
        {
            using (var connection = GetConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE Id = @Id AND IsDeleted = 0";
                return await connection.QueryFirstOrDefaultAsync<T>(sql, new { Id = id });
            }
        }

        public virtual async Task<int> InsertAsync(T entity)
        {
            using (var connection = GetConnection())
            {
                var properties = GetInsertProperties();
                var columns = string.Join(", ", properties);
                var values = string.Join(", ", properties.Select(p => "@" + p));

                var sql = $"INSERT INTO {_tableName} ({columns}) VALUES ({values}); SELECT last_insert_rowid();";
                return await connection.QuerySingleAsync<int>(sql, entity);
            }
        }

        public virtual async Task<bool> UpdateAsync(T entity)
        {
            using (var connection = GetConnection())
            {
                var properties = GetUpdateProperties();
                var setClause = string.Join(", ", properties.Select(p => $"{p} = @{p}"));

                var sql = $"UPDATE {_tableName} SET {setClause}, UpdatedAt = @UpdatedAt WHERE Id = @Id";

                // إضافة UpdatedAt تلقائياً
                var parameters = new DynamicParameters(entity);
                parameters.Add("UpdatedAt", DateTime.Now);

                var rowsAffected = await connection.ExecuteAsync(sql, parameters);
                return rowsAffected > 0;
            }
        }

        public virtual async Task<bool> DeleteAsync(int id)
        {
            using (var connection = GetConnection())
            {
                var sql = $"UPDATE {_tableName} SET IsDeleted = 1, UpdatedAt = @UpdatedAt WHERE Id = @Id";
                var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id, UpdatedAt = DateTime.Now });
                return rowsAffected > 0;
            }
        }

        public virtual async Task<bool> HardDeleteAsync(int id)
        {
            using (var connection = GetConnection())
            {
                var sql = $"DELETE FROM {_tableName} WHERE Id = @Id";
                var rowsAffected = await connection.ExecuteAsync(sql, new { Id = id });
                return rowsAffected > 0;
            }
        }

        public virtual async Task<IEnumerable<T>> GetByRouterIdAsync(int routerId)
        {
            using (var connection = GetConnection())
            {
                var sql = $"SELECT * FROM {_tableName} WHERE RouterId = @RouterId AND IsDeleted = 0 ORDER BY Id";
                return await connection.QueryAsync<T>(sql, new { RouterId = routerId });
            }
        }

        public virtual async Task<int> GetCountAsync()
        {
            using (var connection = GetConnection())
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE IsDeleted = 0";
                return await connection.QuerySingleAsync<int>(sql);
            }
        }

        public virtual async Task<int> GetCountByRouterIdAsync(int routerId)
        {
            using (var connection = GetConnection())
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE RouterId = @RouterId AND IsDeleted = 0";
                return await connection.QuerySingleAsync<int>(sql, new { RouterId = routerId });
            }
        }

        protected abstract List<string> GetInsertProperties();
        protected abstract List<string> GetUpdateProperties();
    }
}
