﻿using SmartCreator.Models;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class frm_Input_Dailog_New_User : RJForms.RJChildForm
    {
        public bool add = false;

        int Userlength = 8;
        int Passwordlength = 6;
        int Usermode = 0;
        int Passwordmode = 0;
        public frm_Input_Dailog_New_User(int _Userlength = 8, int _Passwordlength = 6, int _Usermode = 0, int _Passwordmode = 0)
        {
            InitializeComponent();
            this.Userlength = _Userlength;
            this.Passwordlength = _Passwordlength;
            this.Usermode = _Usermode;
            this.Passwordmode = _Passwordmode;



            this.Text = "ادخل البيانات ";
            if (UIAppearance.Language_ar == false)
                this.Text = "enter new data";
            utils.Control_textSize(pnlClientArea);
            txt_Name.Focus();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txt_Name.Text.Trim() == "")
            {
                RJMessageBox.Show("ادخل الاسم ");
                return;
            }
            add = true;
            this.Close();
        }
        Random random = new Random();
        CLS_Generate_Random_Cards GeneUser = new CLS_Generate_Random_Cards();
        private void btnRandomUser_Click(object sender, EventArgs e)
        {
            string rand = GeneUser.GenerateUser(Userlength, random, Usermode);
            txt_Name.Text = rand;
        }

        private void btnRandomPassword_Click(object sender, EventArgs e)
        {
            string rand = GeneUser.GenerateUser(Passwordlength, random, Passwordmode);
            txt_password.Text = rand;
        }
    }
}
