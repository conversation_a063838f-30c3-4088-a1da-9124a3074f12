﻿using Org.BouncyCastle.Utilities;
using SmartCreator.db;
using SmartCreator.Forms.Settings;
using SmartCreator.RJForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.SettingsForms
{
    public partial class Form_RestorLastBackup : RJChildForm
    {
        Old_DatabaseInfo old_DatabaseInfo;
        string DB_type = "SmartDB";
        FormConnection fc = new FormConnection();
        Form_Backup fb = new Form_Backup();
        //string Rb_Sn;

        public Form_RestorLastBackup()
        {
            InitializeComponent();
            rjLabel6.ForeColor = System.Drawing.Color.Blue;
        }

        [Obsolete]
        public Form_RestorLastBackup(Old_DatabaseInfo _old_DatabaseInfo, string _DB_type = "SmartDB")
        {
            InitializeComponent();
            rjLabel6.ForeColor = System.Drawing.Color.Blue;

            old_DatabaseInfo = _old_DatabaseInfo;
            DB_type = _DB_type;

            if(_DB_type=="SmartDB")
            {
                using (Form_WaitForm fRM = new Form_WaitForm(LoadData))
                    fRM.ShowDialog();
                this.Hide();
            }

            this.Hide();
        }

        [Obsolete]
        private void btn_RestorSmartDB_Click(object sender, EventArgs e)
        {
            switch (RJMessageBox.Show(" قد يستغرق وقت طويل في عمليه المزامنة والاستعادة لا تقم باغلاق البرنامج حتى يكتمل ", "سمارت كريتور"))
            {
                case System.Windows.Forms.DialogResult.OK:
                    using (Form_WaitForm fRM = new Form_WaitForm(LoadData))
                    {
                        fRM.ShowDialog();
                        //this.Close();
                       
                    }

                    //using (Form_WaitForm frm = new Form_WaitForm(LoadData))
                    //{
                    //    //LoadData();
                    //}

                    break;
                case System.Windows.Forms.DialogResult.Cancel:

                    break;

            }
            this.Close();
        }

        [Obsolete]
        public void LoadData()
        {
            if (DB_type == "SmartDB")
            {
                //string connection_str = $@"Data Source={old_DatabaseInfo.Path.Trim()}\db\SmartDB.db;";
                fc.Check_SmartDB_File(); 
                fb.Restor_SmartDB_firstLoad(old_DatabaseInfo);


                //foreach (var db in old_DatabaseInfo.Routers)
                //    fb.Restor_SmartDB(connection_str,db.Rb_sn);
            }
            else if (DB_type == "localDB")
            {
                string connection_str = $@"Data Source={old_DatabaseInfo.Path.Trim()}\db\localDB.db;";
                fc.Get_Path_Database3();
                //fc.Get_Path_Database2();
                fb.LoadLocal_Old_DB2(connection_str);
            }

            //this.Close();
        }

    }
}
