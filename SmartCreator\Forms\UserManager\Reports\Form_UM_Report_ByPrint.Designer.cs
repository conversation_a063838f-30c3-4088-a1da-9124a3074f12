﻿namespace SmartCreator.Forms.UserManager.Reports
{
    partial class Form_UM_Report_ByPrint
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            this.panel1 = new System.Windows.Forms.Panel();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjPanel4 = new SmartCreator.RJControls.RJPanel();
            this.txt_count_Cards = new SmartCreator.RJControls.RJTextBox();
            this.Toggle_By_Profile = new SmartCreator.RJControls.RJToggleButton();
            this.txt_sum_Sales = new SmartCreator.RJControls.RJTextBox();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.Toggle_By_SP = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dgv_Detail = new SmartCreator.RJControls.RJDataGridView();
            this.panel2 = new System.Windows.Forms.Panel();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.btn_ = new SmartCreator.RJControls.RJButton();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.ToggleButton_Detail = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Monthly = new SmartCreator.RJControls.RJToggleButton();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.btn_more = new SmartCreator.RJControls.RJButton();
            this.rjButton3 = new SmartCreator.RJControls.RJButton();
            this.check_with_Commi = new SmartCreator.RJControls.RJCheckBox();
            this.rjPanel12 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.Date_From = new SmartCreator.RJControls.RJDatePicker();
            this.Date_To = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_To_Date = new SmartCreator.RJControls.RJCheckBox();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.pnl_size_time_count = new System.Windows.Forms.Panel();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.txt_uptime = new SmartCreator.RJControls.RJTextBox();
            this.txt_download = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel19 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel_back_side = new SmartCreator.RJControls.RJPanel();
            this.CBox_Customer = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_sn = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnlClientArea.SuspendLayout();
            this.panel1.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel4.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Detail)).BeginInit();
            this.rjPanel2.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.rjPanel12.SuspendLayout();
            this.pnl_size_time_count.SuspendLayout();
            this.rjPanel_back_side.SuspendLayout();
            this.pnl_side_sn.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.panel1);
            this.pnlClientArea.Controls.Add(this.rjPanel_back_side);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 584);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(175, 22);
            this.lblCaption.Text = "Form_UM_Report_ByPrint";
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.rjPanel1);
            this.panel1.Controls.Add(this.rjPanel2);
            this.panel1.Location = new System.Drawing.Point(250, 10);
            this.panel1.Margin = new System.Windows.Forms.Padding(0, 0, 3, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(728, 559);
            this.panel1.TabIndex = 78;
            // 
            // rjPanel1
            // 
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 0;
            this.rjPanel1.Controls.Add(this.rjPanel4);
            this.rjPanel1.Controls.Add(this.dgv);
            this.rjPanel1.Controls.Add(this.dgv_Detail);
            this.rjPanel1.Controls.Add(this.panel2);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.rjPanel1.Location = new System.Drawing.Point(0, 107);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(728, 452);
            this.rjPanel1.TabIndex = 64;
            // 
            // rjPanel4
            // 
            this.rjPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel4.BorderRadius = 5;
            this.rjPanel4.Controls.Add(this.txt_count_Cards);
            this.rjPanel4.Controls.Add(this.Toggle_By_Profile);
            this.rjPanel4.Controls.Add(this.txt_sum_Sales);
            this.rjPanel4.Controls.Add(this.rjButton1);
            this.rjPanel4.Controls.Add(this.Toggle_By_SP);
            this.rjPanel4.Controls.Add(this.rjLabel5);
            this.rjPanel4.Controls.Add(this.rjLabel2);
            this.rjPanel4.Customizable = false;
            this.rjPanel4.Location = new System.Drawing.Point(4, 205);
            this.rjPanel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel4.Name = "rjPanel4";
            this.rjPanel4.Size = new System.Drawing.Size(718, 72);
            this.rjPanel4.TabIndex = 85;
            // 
            // txt_count_Cards
            // 
            this.txt_count_Cards._Customizable = false;
            this.txt_count_Cards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_count_Cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Cards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Cards.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Cards.BorderRadius = 0;
            this.txt_count_Cards.BorderSize = 1;
            this.txt_count_Cards.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            this.txt_count_Cards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Cards.Location = new System.Drawing.Point(401, 31);
            this.txt_count_Cards.MultiLine = false;
            this.txt_count_Cards.Name = "txt_count_Cards";
            this.txt_count_Cards.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Cards.PasswordChar = false;
            this.txt_count_Cards.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Cards.PlaceHolderText = null;
            this.txt_count_Cards.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Cards.Size = new System.Drawing.Size(155, 30);
            this.txt_count_Cards.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_count_Cards.TabIndex = 58;
            this.txt_count_Cards.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // Toggle_By_Profile
            // 
            this.Toggle_By_Profile.Activated = false;
            this.Toggle_By_Profile.Customizable = false;
            this.Toggle_By_Profile.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_By_Profile.Location = new System.Drawing.Point(45, 20);
            this.Toggle_By_Profile.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_By_Profile.Name = "Toggle_By_Profile";
            this.Toggle_By_Profile.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_Profile.OFF_Text = "تجميع بحسب الباقة";
            this.Toggle_By_Profile.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_By_Profile.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_Profile.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_Profile.ON_Text = "تجميع بحسب الباقة";
            this.Toggle_By_Profile.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_By_Profile.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_Profile.Size = new System.Drawing.Size(169, 32);
            this.Toggle_By_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_By_Profile.TabIndex = 94;
            this.Toggle_By_Profile.Tag = "تفصيلي";
            this.Toggle_By_Profile.Text = "#";
            this.Toggle_By_Profile.UseVisualStyleBackColor = true;
            this.Toggle_By_Profile.CheckedChanged += new System.EventHandler(this.Toggle_By_Profile_CheckedChanged);
            // 
            // txt_sum_Sales
            // 
            this.txt_sum_Sales._Customizable = false;
            this.txt_sum_Sales.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_sum_Sales.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_sum_Sales.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_sum_Sales.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_sum_Sales.BorderRadius = 0;
            this.txt_sum_Sales.BorderSize = 1;
            this.txt_sum_Sales.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            this.txt_sum_Sales.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_sum_Sales.Location = new System.Drawing.Point(562, 31);
            this.txt_sum_Sales.MultiLine = false;
            this.txt_sum_Sales.Name = "txt_sum_Sales";
            this.txt_sum_Sales.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_sum_Sales.PasswordChar = false;
            this.txt_sum_Sales.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_sum_Sales.PlaceHolderText = null;
            this.txt_sum_Sales.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_sum_Sales.Size = new System.Drawing.Size(148, 30);
            this.txt_sum_Sales.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_sum_Sales.TabIndex = 59;
            this.txt_sum_Sales.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 4;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 25;
            this.rjButton1.Location = new System.Drawing.Point(7, 18);
            this.rjButton1.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(35, 32);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton1.TabIndex = 49;
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton1.UseVisualStyleBackColor = false;
            // 
            // Toggle_By_SP
            // 
            this.Toggle_By_SP.Activated = false;
            this.Toggle_By_SP.Customizable = false;
            this.Toggle_By_SP.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Toggle_By_SP.Location = new System.Drawing.Point(220, 20);
            this.Toggle_By_SP.MinimumSize = new System.Drawing.Size(50, 25);
            this.Toggle_By_SP.Name = "Toggle_By_SP";
            this.Toggle_By_SP.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_SP.OFF_Text = "تجميع بحسب نقطة البيع";
            this.Toggle_By_SP.OFF_TextColor = System.Drawing.Color.Gray;
            this.Toggle_By_SP.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.Toggle_By_SP.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_SP.ON_Text = "تجميع بحسب نقطة البيع";
            this.Toggle_By_SP.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Toggle_By_SP.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Toggle_By_SP.Size = new System.Drawing.Size(169, 32);
            this.Toggle_By_SP.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Toggle_By_SP.TabIndex = 95;
            this.Toggle_By_SP.Tag = "تفصيلي";
            this.Toggle_By_SP.Text = "#";
            this.Toggle_By_SP.UseVisualStyleBackColor = true;
            this.Toggle_By_SP.Visible = false;
            // 
            // rjLabel5
            // 
            this.rjLabel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(614, 8);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.Size = new System.Drawing.Size(54, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 61;
            this.rjLabel5.Text = "الاجمالي";
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(451, 9);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(70, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 60;
            this.rjLabel2.Text = "عدد الكـــروت";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(192)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(9, 6);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 30;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 30;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(710, 189);
            this.dgv.TabIndex = 76;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellClick);
            // 
            // dgv_Detail
            // 
            this.dgv_Detail.AllowUserToAddRows = false;
            this.dgv_Detail.AllowUserToDeleteRows = false;
            this.dgv_Detail.AllowUserToOrderColumns = true;
            this.dgv_Detail.AllowUserToResizeRows = false;
            this.dgv_Detail.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_Detail.AlternatingRowsColorApply = false;
            this.dgv_Detail.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_Detail.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_Detail.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.dgv_Detail.BorderRadius = 13;
            this.dgv_Detail.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_Detail.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_Detail.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_Detail.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_Detail.ColumnHeaderHeight = 40;
            this.dgv_Detail.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Detail.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv_Detail.ColumnHeadersHeight = 40;
            this.dgv_Detail.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_Detail.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_Detail.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_Detail.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Detail.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv_Detail.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(255)))), ((int)(((byte)(192)))));
            this.dgv_Detail.EnableHeadersVisualStyles = false;
            this.dgv_Detail.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_Detail.Location = new System.Drawing.Point(9, 287);
            this.dgv_Detail.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            this.dgv_Detail.Name = "dgv_Detail";
            this.dgv_Detail.ReadOnly = true;
            this.dgv_Detail.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_Detail.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_Detail.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_Detail.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv_Detail.RowHeadersVisible = false;
            this.dgv_Detail.RowHeadersWidth = 30;
            this.dgv_Detail.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_Detail.RowHeight = 30;
            this.dgv_Detail.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_Detail.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv_Detail.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_Detail.RowTemplate.Height = 30;
            this.dgv_Detail.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_Detail.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_Detail.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_Detail.Size = new System.Drawing.Size(709, 155);
            this.dgv_Detail.TabIndex = 75;
            this.dgv_Detail.SelectionChanged += new System.EventHandler(this.dgv_Detail_SelectionChanged);
            // 
            // panel2
            // 
            this.panel2.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel2.Location = new System.Drawing.Point(0, 390);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(728, 62);
            this.panel2.TabIndex = 74;
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.btn_search);
            this.rjPanel2.Controls.Add(this.btn_);
            this.rjPanel2.Controls.Add(this.rjPanel3);
            this.rjPanel2.Controls.Add(this.btn_more);
            this.rjPanel2.Controls.Add(this.rjButton3);
            this.rjPanel2.Controls.Add(this.check_with_Commi);
            this.rjPanel2.Controls.Add(this.rjPanel12);
            this.rjPanel2.Controls.Add(this.txt_search);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjPanel2.Location = new System.Drawing.Point(0, 0);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(728, 107);
            this.rjPanel2.TabIndex = 1;
            // 
            // btn_search
            // 
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 1;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(23, 71);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(35, 25);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 92;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            // 
            // btn_
            // 
            this.btn_.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.BorderRadius = 4;
            this.btn_.BorderSize = 1;
            this.btn_.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_.FlatAppearance.BorderSize = 0;
            this.btn_.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_.IconSize = 2;
            this.btn_.Location = new System.Drawing.Point(93, 9);
            this.btn_.Margin = new System.Windows.Forms.Padding(0);
            this.btn_.Name = "btn_";
            this.btn_.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_.Size = new System.Drawing.Size(60, 34);
            this.btn_.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_.TabIndex = 57;
            this.btn_.Text = "عــرض";
            this.btn_.UseVisualStyleBackColor = false;
            this.btn_.Click += new System.EventHandler(this.btn__Click);
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel3.BorderRadius = 5;
            this.rjPanel3.Controls.Add(this.ToggleButton_Detail);
            this.rjPanel3.Controls.Add(this.ToggleButton_Monthly);
            this.rjPanel3.Controls.Add(this.jToggleButton_Year);
            this.rjPanel3.Controls.Add(this.rjLabel11);
            this.rjPanel3.Customizable = true;
            this.rjPanel3.Location = new System.Drawing.Point(347, 65);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(374, 32);
            this.rjPanel3.TabIndex = 73;
            // 
            // ToggleButton_Detail
            // 
            this.ToggleButton_Detail.Activated = false;
            this.ToggleButton_Detail.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Detail.Customizable = false;
            this.ToggleButton_Detail.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Detail.Location = new System.Drawing.Point(198, 3);
            this.ToggleButton_Detail.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Detail.Name = "ToggleButton_Detail";
            this.ToggleButton_Detail.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.OFF_Text = "تفصيلي";
            this.ToggleButton_Detail.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Detail.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.ON_Text = "تفصيلي";
            this.ToggleButton_Detail.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Detail.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Detail.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Detail.TabIndex = 52;
            this.ToggleButton_Detail.Tag = "تفصيلي";
            this.ToggleButton_Detail.Text = "#";
            this.ToggleButton_Detail.UseVisualStyleBackColor = true;
            this.ToggleButton_Detail.CheckedChanged += new System.EventHandler(this.ToggleButton_Detail_CheckedChanged);
            // 
            // ToggleButton_Monthly
            // 
            this.ToggleButton_Monthly.Activated = true;
            this.ToggleButton_Monthly.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Monthly.Checked = true;
            this.ToggleButton_Monthly.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToggleButton_Monthly.Customizable = false;
            this.ToggleButton_Monthly.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Monthly.Location = new System.Drawing.Point(101, 3);
            this.ToggleButton_Monthly.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Monthly.Name = "ToggleButton_Monthly";
            this.ToggleButton_Monthly.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.OFF_Text = "يومي";
            this.ToggleButton_Monthly.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Monthly.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.ON_Text = "يومي";
            this.ToggleButton_Monthly.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Monthly.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Monthly.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Monthly.TabIndex = 52;
            this.ToggleButton_Monthly.Tag = "تفصيلي";
            this.ToggleButton_Monthly.Text = "#";
            this.ToggleButton_Monthly.UseVisualStyleBackColor = true;
            this.ToggleButton_Monthly.CheckedChanged += new System.EventHandler(this.ToggleButton_Monthly_CheckedChanged);
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.jToggleButton_Year.Location = new System.Drawing.Point(6, 3);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = "شهري";
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = "شهري";
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(91, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 52;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            this.jToggleButton_Year.CheckedChanged += new System.EventHandler(this.jToggleButton_Year_CheckedChanged);
            // 
            // rjLabel11
            // 
            this.rjLabel11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(292, 7);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.Size = new System.Drawing.Size(75, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 53;
            this.rjLabel11.Text = "طريقة العرض";
            this.rjLabel11.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // btn_more
            // 
            this.btn_more.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_more.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_more.BorderRadius = 5;
            this.btn_more.BorderSize = 1;
            this.btn_more.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_more.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_more.FlatAppearance.BorderSize = 0;
            this.btn_more.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_more.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_more.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_more.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.btn_more.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_more.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_more.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_more.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_more.IconSize = 1;
            this.btn_more.Location = new System.Drawing.Point(4, 9);
            this.btn_more.Margin = new System.Windows.Forms.Padding(0);
            this.btn_more.Name = "btn_more";
            this.btn_more.Size = new System.Drawing.Size(53, 34);
            this.btn_more.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_more.TabIndex = 25;
            this.btn_more.Text = "فلتره";
            this.btn_more.UseVisualStyleBackColor = false;
            this.btn_more.Click += new System.EventHandler(this.btn_more_Click);
            // 
            // rjButton3
            // 
            this.rjButton3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderRadius = 4;
            this.rjButton3.BorderSize = 1;
            this.rjButton3.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton3.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton3.FlatAppearance.BorderSize = 0;
            this.rjButton3.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton3.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.rjButton3.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton3.IconSize = 25;
            this.rjButton3.Location = new System.Drawing.Point(58, 9);
            this.rjButton3.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton3.Name = "rjButton3";
            this.rjButton3.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton3.Size = new System.Drawing.Size(35, 34);
            this.rjButton3.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton3.TabIndex = 65;
            this.rjButton3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton3.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton3.UseVisualStyleBackColor = false;
            // 
            // check_with_Commi
            // 
            this.check_with_Commi.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.check_with_Commi.AutoSize = true;
            this.check_with_Commi.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.BorderSize = 1;
            this.check_with_Commi.Check = false;
            this.check_with_Commi.Cursor = System.Windows.Forms.Cursors.Hand;
            this.check_with_Commi.Customizable = false;
            this.check_with_Commi.Font = new System.Drawing.Font("Droid Sans Arabic", 8F);
            this.check_with_Commi.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.check_with_Commi.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.Location = new System.Drawing.Point(31, 45);
            this.check_with_Commi.MinimumSize = new System.Drawing.Size(0, 21);
            this.check_with_Commi.Name = "check_with_Commi";
            this.check_with_Commi.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.check_with_Commi.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.check_with_Commi.Size = new System.Drawing.Size(114, 21);
            this.check_with_Commi.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.check_with_Commi.TabIndex = 86;
            this.check_with_Commi.Text = "خصم العمولات";
            this.check_with_Commi.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.check_with_Commi.UseVisualStyleBackColor = true;
            this.check_with_Commi.CheckedChanged += new System.EventHandler(this.check_with_Commi_CheckedChanged);
            // 
            // rjPanel12
            // 
            this.rjPanel12.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel12.BorderRadius = 5;
            this.rjPanel12.Controls.Add(this.rjLabel3);
            this.rjPanel12.Controls.Add(this.Date_From);
            this.rjPanel12.Controls.Add(this.Date_To);
            this.rjPanel12.Controls.Add(this.CheckBox_To_Date);
            this.rjPanel12.Customizable = false;
            this.rjPanel12.Location = new System.Drawing.Point(156, 7);
            this.rjPanel12.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel12.Name = "rjPanel12";
            this.rjPanel12.Size = new System.Drawing.Size(565, 55);
            this.rjPanel12.TabIndex = 74;
            // 
            // rjLabel3
            // 
            this.rjLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(529, 13);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(25, 22);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 49;
            this.rjLabel3.Text = "من";
            // 
            // Date_From
            // 
            this.Date_From.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Date_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_From.BorderRadius = 7;
            this.Date_From.BorderSize = 1;
            this.Date_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.Date_From.Customizable = false;
            this.Date_From.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Date_From.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.Date_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_From.Location = new System.Drawing.Point(312, 6);
            this.Date_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_From.Name = "Date_From";
            this.Date_From.Padding = new System.Windows.Forms.Padding(2);
            this.Date_From.Size = new System.Drawing.Size(249, 39);
            this.Date_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_From.TabIndex = 79;
            this.Date_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            // 
            // Date_To
            // 
            this.Date_To.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Date_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_To.BorderRadius = 7;
            this.Date_To.BorderSize = 1;
            this.Date_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.Date_To.Customizable = false;
            this.Date_To.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Date_To.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.Date_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_To.Location = new System.Drawing.Point(5, 6);
            this.Date_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_To.Name = "Date_To";
            this.Date_To.Padding = new System.Windows.Forms.Padding(2);
            this.Date_To.Size = new System.Drawing.Size(250, 39);
            this.Date_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_To.TabIndex = 80;
            this.Date_To.Value = new System.DateTime(2024, 9, 27, 23, 59, 59, 0);
            // 
            // CheckBox_To_Date
            // 
            this.CheckBox_To_Date.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_To_Date.AutoSize = true;
            this.CheckBox_To_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.BorderSize = 1;
            this.CheckBox_To_Date.Check = false;
            this.CheckBox_To_Date.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_To_Date.Customizable = false;
            this.CheckBox_To_Date.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_To_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_To_Date.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.Location = new System.Drawing.Point(238, 13);
            this.CheckBox_To_Date.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_To_Date.Name = "CheckBox_To_Date";
            this.CheckBox_To_Date.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.CheckBox_To_Date.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_To_Date.Size = new System.Drawing.Size(68, 21);
            this.CheckBox_To_Date.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_To_Date.TabIndex = 81;
            this.CheckBox_To_Date.Text = "الي";
            this.CheckBox_To_Date.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.UseVisualStyleBackColor = true;
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Verdana", 12F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(58, 69);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(283, 30);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 64;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // pnl_size_time_count
            // 
            this.pnl_size_time_count.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_size_time_count.Controls.Add(this.btn_Refresh);
            this.pnl_size_time_count.Controls.Add(this.txt_uptime);
            this.pnl_size_time_count.Controls.Add(this.txt_download);
            this.pnl_size_time_count.Controls.Add(this.rjLabel19);
            this.pnl_size_time_count.Controls.Add(this.rjLabel7);
            this.pnl_size_time_count.Controls.Add(this.rjLabel18);
            this.pnl_size_time_count.Location = new System.Drawing.Point(31, 468);
            this.pnl_size_time_count.Name = "pnl_size_time_count";
            this.pnl_size_time_count.Size = new System.Drawing.Size(402, 67);
            this.pnl_size_time_count.TabIndex = 73;
            this.pnl_size_time_count.Visible = false;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(106, 9);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(35, 34);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 66;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Visible = false;
            // 
            // txt_uptime
            // 
            this.txt_uptime._Customizable = false;
            this.txt_uptime.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.txt_uptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_uptime.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptime.BorderRadius = 0;
            this.txt_uptime.BorderSize = 1;
            this.txt_uptime.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            this.txt_uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptime.Location = new System.Drawing.Point(201, 29);
            this.txt_uptime.MultiLine = false;
            this.txt_uptime.Name = "txt_uptime";
            this.txt_uptime.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptime.PasswordChar = false;
            this.txt_uptime.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptime.PlaceHolderText = null;
            this.txt_uptime.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptime.Size = new System.Drawing.Size(184, 30);
            this.txt_uptime.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_uptime.TabIndex = 3;
            this.txt_uptime.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_download
            // 
            this.txt_download._Customizable = false;
            this.txt_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.txt_download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_download.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_download.BorderRadius = 0;
            this.txt_download.BorderSize = 1;
            this.txt_download.Font = new System.Drawing.Font("Tahoma", 12F, System.Drawing.FontStyle.Bold);
            this.txt_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_download.Location = new System.Drawing.Point(9, 29);
            this.txt_download.MultiLine = false;
            this.txt_download.Name = "txt_download";
            this.txt_download.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_download.PasswordChar = false;
            this.txt_download.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_download.PlaceHolderText = null;
            this.txt_download.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_download.Size = new System.Drawing.Size(186, 30);
            this.txt_download.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_download.TabIndex = 3;
            this.txt_download.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel19
            // 
            this.rjLabel19.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.rjLabel19.AutoSize = true;
            this.rjLabel19.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel19.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.rjLabel19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.rjLabel19.LinkLabel = false;
            this.rjLabel19.Location = new System.Drawing.Point(50, 22);
            this.rjLabel19.Name = "rjLabel19";
            this.rjLabel19.Size = new System.Drawing.Size(145, 22);
            this.rjLabel19.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.rjLabel19.TabIndex = 57;
            this.rjLabel19.Text = "اجمالي رصيد الباقات";
            // 
            // rjLabel7
            // 
            this.rjLabel7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(16, 16);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(56, 22);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 50;
            this.rjLabel7.Text = "الي تاريخ";
            // 
            // rjLabel18
            // 
            this.rjLabel18.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(231, 9);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.Size = new System.Drawing.Size(145, 22);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.rjLabel18.TabIndex = 57;
            this.rjLabel18.Text = "اجمالي وقت الباقات";
            // 
            // rjPanel_back_side
            // 
            this.rjPanel_back_side.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.rjPanel_back_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_back_side.BorderRadius = 10;
            this.rjPanel_back_side.Controls.Add(this.pnl_size_time_count);
            this.rjPanel_back_side.Controls.Add(this.CBox_Customer);
            this.rjPanel_back_side.Controls.Add(this.rjLabel14);
            this.rjPanel_back_side.Controls.Add(this.pnl_side_sn);
            this.rjPanel_back_side.Controls.Add(this.rjLabel25Title);
            this.rjPanel_back_side.Controls.Add(this.CBox_SellingPoint);
            this.rjPanel_back_side.Controls.Add(this.CBox_Batch);
            this.rjPanel_back_side.Controls.Add(this.btn_apply);
            this.rjPanel_back_side.Controls.Add(this.CBox_Profile);
            this.rjPanel_back_side.Controls.Add(this.rjLabel9);
            this.rjPanel_back_side.Controls.Add(this.rjLabel4);
            this.rjPanel_back_side.Controls.Add(this.rjLabel15);
            this.rjPanel_back_side.Customizable = false;
            this.rjPanel_back_side.Location = new System.Drawing.Point(13, 5);
            this.rjPanel_back_side.Margin = new System.Windows.Forms.Padding(0, 3, 3, 3);
            this.rjPanel_back_side.Name = "rjPanel_back_side";
            this.rjPanel_back_side.Size = new System.Drawing.Size(230, 564);
            this.rjPanel_back_side.TabIndex = 77;
            // 
            // CBox_Customer
            // 
            this.CBox_Customer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Customer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Customer.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.BorderRadius = 5;
            this.CBox_Customer.BorderSize = 1;
            this.CBox_Customer.Customizable = false;
            this.CBox_Customer.DataSource = null;
            this.CBox_Customer.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Customer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Customer.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Customer.Location = new System.Drawing.Point(11, 257);
            this.CBox_Customer.Name = "CBox_Customer";
            this.CBox_Customer.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Customer.SelectedIndex = -1;
            this.CBox_Customer.Size = new System.Drawing.Size(178, 32);
            this.CBox_Customer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Customer.TabIndex = 108;
            this.CBox_Customer.Texts = "";
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(73, 235);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(55, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 109;
            this.rjLabel14.Text = "المسوول";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnl_side_sn
            // 
            this.pnl_side_sn.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_sn.BorderRadius = 10;
            this.pnl_side_sn.Controls.Add(this.CheckBox_SN);
            this.pnl_side_sn.Controls.Add(this.rjLabel10);
            this.pnl_side_sn.Controls.Add(this.txt_SN_End);
            this.pnl_side_sn.Controls.Add(this.CBox_SN_Compar);
            this.pnl_side_sn.Controls.Add(this.txt_SN_Start);
            this.pnl_side_sn.Controls.Add(this.rjLabel1);
            this.pnl_side_sn.Customizable = true;
            this.pnl_side_sn.Location = new System.Drawing.Point(6, 296);
            this.pnl_side_sn.Name = "pnl_side_sn";
            this.pnl_side_sn.Size = new System.Drawing.Size(218, 90);
            this.pnl_side_sn.TabIndex = 107;
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.Location = new System.Drawing.Point(187, 11);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 42;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(81, 15);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(11, 9);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 43;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(10, 40);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(155, 32);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 31;
            this.CBox_SN_Compar.Texts = "";
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(96, 9);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 43;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(162, 43);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(59, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 35;
            this.rjLabel1.Text = "التسلسلي";
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(65, 9);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(61, 22);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 105;
            this.rjLabel25Title.Text = "تخصيص";
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(10, 194);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(179, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Location = new System.Drawing.Point(10, 129);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(179, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 36;
            this.CBox_Batch.Texts = "";
            // 
            // btn_apply
            // 
            this.btn_apply.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 15;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Cairo Medium", 12F, System.Drawing.FontStyle.Bold);
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(53, 388);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.Size = new System.Drawing.Size(114, 40);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 51;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_apply.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.SuggestAppend;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(10, 69);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(179, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(87, 51);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.Size = new System.Drawing.Size(37, 17);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(78, 111);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(43, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 71;
            this.rjLabel4.Text = "الدفعه";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(75, 171);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(52, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // Form_UM_Report_ByPrint
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "Form_UM_Report_ByPrint";
            this.ClientSize = new System.Drawing.Size(1000, 634);
            this.Name = "Form_UM_Report_ByPrint";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_UM_Report_ByPrint";
            this.Load += new System.EventHandler(this.Form_UM_Report_ByPrint_Load);
            this.pnlClientArea.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel4.ResumeLayout(false);
            this.rjPanel4.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_Detail)).EndInit();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            this.rjPanel12.ResumeLayout(false);
            this.rjPanel12.PerformLayout();
            this.pnl_size_time_count.ResumeLayout(false);
            this.pnl_size_time_count.PerformLayout();
            this.rjPanel_back_side.ResumeLayout(false);
            this.rjPanel_back_side.PerformLayout();
            this.pnl_side_sn.ResumeLayout(false);
            this.pnl_side_sn.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private RJControls.RJPanel rjPanel1;
        private System.Windows.Forms.Panel pnl_size_time_count;
        private RJControls.RJLabel rjLabel18;
        private RJControls.RJTextBox txt_uptime;
        private RJControls.RJTextBox txt_download;
        private RJControls.RJLabel rjLabel19;
        private RJControls.RJTextBox txt_count_Cards;
        private RJControls.RJTextBox txt_sum_Sales;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJPanel rjPanel12;
        private RJControls.RJButton btn_more;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJButton rjButton3;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJToggleButton ToggleButton_Detail;
        private RJControls.RJToggleButton ToggleButton_Monthly;
        private RJControls.RJDatePicker Date_To;
        private RJControls.RJDatePicker Date_From;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJPanel rjPanel_back_side;
        private RJControls.RJPanel pnl_side_sn;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel25Title;
        private RJControls.RJComboBox CBox_SellingPoint;
        public RJControls.RJComboBox CBox_Batch;
        private RJControls.RJButton btn_apply;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel rjLabel15;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJComboBox CBox_Customer;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJCheckBox check_with_Commi;
        private RJControls.RJButton btn_;
        private RJControls.RJCheckBox CheckBox_To_Date;
        private System.Windows.Forms.Panel panel2;
        private RJControls.RJDataGridView dgv_Detail;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJButton btn_search;
        private RJControls.RJPanel rjPanel4;
        private RJControls.RJToggleButton Toggle_By_Profile;
        private RJControls.RJButton rjButton1;
        private RJControls.RJToggleButton Toggle_By_SP;
    }
}