﻿using iTextSharp.text;
using iTextSharp.text.pdf;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;
using System.Drawing.Text;
using SmartCreator.Models.hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Utils;
using DevComponents.DotNetBar.Metro;
using SmartCreator.Forms.CardsDesigen;

namespace SmartCreator.Forms
{
    public partial class Form_TemplateTable1 : RJChildForm
    {
        public DataTable dt_emplateCards;
        private Point MouseDownLocation;
        private bool firstLoad = true;
        private bool saveToClass = true;
        private bool selectTemplateFromDrowpDown = true;
        private bool selectTemplateFromCheckBox = true;
        public static string pathfile = "";
        bool addDeletTemplate = false;

        bool mouseClicked = false;
        public DataTable dt_templateCards;

        public CardsTableDesg1 card;

        public Form_TemplateTable1()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            this.Text = "استديو التصميم";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Desigen studio";
            }
            setFont();
           
            //var line = new Control();
            //line.Size = new Size(lblDescription.Width - 10, 1);
            //line.BackColor = Color.LightGray;
            //line.Location = new Point(lblDescription.Left + 5, lblTitle.Bottom + 15);
            //icoBanner.Controls.Add(line);

            txt_Space_X.BackColor = txt_Space_Y.BackColor = txt_Number_Page_X.BackColor = txt_Number_Page_Y.BackColor = txt_Number_Page_Size.BackColor = UIAppearance.BackgroundColor;
            txt_Note_Page_Y.BackColor = txt_Padding_Cell.BackColor = txt_SizeBorder_midell.BackColor = TextCard_W.BackColor = UIAppearance.BackgroundColor;
            txt_SizeBorder.BackColor = txt_Note_Page_Size.BackColor = txt_Note_Page_X.BackColor = UIAppearance.BackgroundColor;


            System.Drawing.Font fnm = new System.Drawing.Font(UIAppearance.TextFamilyName, UIAppearance.TextSize);

            txt_Space_X.Font = txt_Space_Y.Font = txt_Number_Page_X.Font = txt_Number_Page_Y.Font = txt_Number_Page_Size.Font = fnm;
            txt_Note_Page_Y.Font = txt_SizeBorder_midell.Font = txt_Padding_Cell.Font = TextCard_W.Font = fnm;
            txt_SizeBorder.Font = txt_Note_Page_Size.Font = txt_Note_Page_X.Font = fnm;


            txt_Space_Y.ForeColor = txt_Space_X.ForeColor = txt_Number_Page_X.ForeColor = txt_Number_Page_Y.ForeColor = txt_Number_Page_Size.ForeColor = UIAppearance.TextColor;
            txt_Note_Page_Y.ForeColor = txt_SizeBorder_midell.ForeColor = txt_Padding_Cell.ForeColor = TextCard_W.ForeColor = UIAppearance.TextColor;
            txt_SizeBorder.ForeColor = txt_Note_Page_Size.ForeColor = txt_Note_Page_X.ForeColor = UIAppearance.TextColor;

            TXT_USerName.Text = "########";
            TXT_Password.Text = "########";
            TXT_Uptime.Text = "########";
            TXT_SizeTransfer.Text = "########";
            TXT_Validate.Text = "########";
            TXT_Price.Text = "########";
            TXT_SP.Text = "########";
            txt_lbl_username.Text = "اسم المستخدم";
            txt_lbl_password.Text = "كلمة المرور";
            txt_lbl_time.Text = "مــدة  الكرت";
            txt_lbl_validy.Text = "الصلاحية ايام";
            txt_lbl_download.Text = "التنزيل";
            txt_lbl_price.Text = "السعر";
            txt_lbl_SP.Text = "نقطة البيع";
            txt_lbl_SN.Text = "الرقم التسلسلي";
            txt_lbl_DatePrint.Text = "تاريخ الطباعة";
            txt_lbl_NumberPrint.Text = "رقم الطبعة";


            groupBox_FontAndSize.BackColor = Color.White;
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel) || C.GetType() != typeof(Form) || C.GetType() != typeof(MetroForm))
                            C.Font = new System.Drawing.Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_Cbox_Profile()
        {

            //if (selectTemplateFromDrowpDown || firstLoad)
            //    return;
            //CBox_Profile.DataSource = Global_Variable.UM_Profile;
            //CBox_Profile.DisplayMember = "Name";
            //CBox_Profile.ValueMember = "Name";
            //CBox_Profile.SelectedIndex = -1;
            //CBox_Profile.Text = "";

            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                }
            }
            catch { }


        }
        void get_Profile_Hotspot()
        {
            //try
            //{
            //    CBox_Profile_HS.DataSource = Global_Variable.Sorce_HS_Profile;
            //    CBox_Profile_HS.DisplayMember = "Name";
            //    CBox_Profile_HS.ValueMember = "Name";

            //    CBox_Profile_HS.SelectedIndex = -1;
            //    CBox_Profile_HS.Text = "";
            //}
            //catch { }
            try
            {
                List<Hotspot_Source_Profile> sp = Global_Variable.Source_HS_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (Hotspot_Source_Profile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile_HS.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile_HS.DisplayMember = "Value";
                    CBox_Profile_HS.ValueMember = "Key";
                }
            }
            catch { }
        }
        private void Get_TemplateCardsFromDB()
        {
            try
            {
                CBox_TemplateCards.Items.Clear();
                //CBox_TemplateCards = null;
            }
            catch { }
            try
            {
                List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("table_Desigen1");
                CBox_TemplateCards.DataSource = sourceCardsTemplate;
                CBox_TemplateCards.DisplayMember = "Name";
                CBox_TemplateCards.ValueMember = "id";
                CBox_TemplateCards.SelectedIndex = -1;
                if (sourceCardsTemplate.Count <= 0 || sourceCardsTemplate == null)
                {
                    CreateDefultTemplate();
                }
            }
            catch { }
            //try
            //{
            //    if (dt_templateCards.Rows.Count <= 0)
            //    {
            //        CreateDefultTemplate();
            //        //SetValuToCardToGraphics();
            //    }
            //}
            //catch { }

        }
        public void CreateDefultTemplate()
        {
            SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
            if (sourceCardsTemplate.CreateDefaultTemplate(true))
            {

            }

            card = new CardsTableDesg1();
            card.setingCard.name = "Table_Default1";
            card.setingCard.type = "table_Desigen1";
            return;


            //SourceCardsTemplate sourceCardsTemplate2 = new SourceCardsTemplate();
            //if (sourceCardsTemplate2.CreateDefaultTemplate())
            //{
            //    try
            //    {
            //        List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("table_Desigen1");
            //        CBox_TemplateCards.DataSource = sourceCardsTemplate;
            //        CBox_TemplateCards.DisplayMember = "Name";
            //        CBox_TemplateCards.ValueMember = "id";
            //        CBox_TemplateCards.SelectedIndex = -1;
            //        if (sourceCardsTemplate.Count <= 0 || sourceCardsTemplate == null)
            //        {
            //            card = new CardsTableDesg1();
            //            card.setingCard.name = "Table_Default";
            //            card.setingCard.type = "table_Desigen1";
            //            return;
            //        }
            //    }
            //    catch { }
            //}

           
            //string stringjson = JsonConvert.SerializeObject(card);
        }

        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            rjPanel1.Refresh();
            tableLayoutPanel1.Refresh();
        }

        private void Form_TemplateTable1_Load(object sender, EventArgs e)
        {
            firstLoad = true;
            saveToClass = false;
            selectTemplateFromDrowpDown = true;
            selectTemplateFromCheckBox = true;
            CBox_UniteTime_format.Visible = true;
            timer1.Start();
        }
        private void Get_Cbox_Profile_Hotspot_local()
        {
            var umProfil = new List<HSLocalProfile>();
            umProfil.Add(new HSLocalProfile { Id = 0, Name = "" });
            HSLocalProfile hotspot = new HSLocalProfile();
            umProfil.AddRange(hotspot.Ge_Local_Hotspot());

            try
            {
                CBox_Profile_HotspotLocal.DataSource = umProfil;
                CBox_Profile_HotspotLocal.DisplayMember = "Name";
                CBox_Profile_HotspotLocal.ValueMember = "Name";
                //CBox_Profile.Text = "";
                //Cbox_Profile_Select_Typ.SelectedIndex = 0;

            }
            catch { }
        }
            private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            //setFont();
            Get_Cbox_Profile();
            get_Profile_Hotspot();
            Get_Cbox_Profile_Hotspot_local();
            Get_TemplateCardsFromDB();

            disableAll_Lable();
            try
            {
                //comboBox_quilty_image.SelectedIndex = 0;
                CBox_NoteType_onPage.SelectedIndex = 0;
                CBox_UniteTime_format.SelectedIndex = 1;
                CBox_UniteTransfer_format.SelectedIndex = 0;
                CBox_UniteValidaty_format.SelectedIndex = 0;
                CBox_Date_print_format.SelectedIndex = 0;
                CBox_SellingPoint.SelectedIndex = 0;
            }
            catch { /*MessageBox.Show(ex.Message);*/ }
            
            firstLoad = false;
            selectTemplateFromDrowpDown = false;
            saveToClass = true;
            CBox_TemplateCards_OnSelectedIndexChanged(sender, EventArgs.Empty);
            //SetValuToCardToGraphics();

            txt_NumberCulum.Enabled = true;

        }
        void setFont()
        {
            try
            {
                //this.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);

                System.Drawing.Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

                ////groupBox3.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Bold);
                ////radioTempletCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
                ////radioTableCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
                //BackgroundImgCard_Chbox.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 7, FontStyle.Regular);

                lbl_info1.Font = lbl_login.Font = lbl_info2.Font = lbl_info4.Font = fnt;

                BackgroundImgCard_Chbox.Font = fnt;
                foreach (var contrl in pnlClientArea.Controls)
                {
                    try
                    {
                        if (contrl.GetType() == typeof(RJControls.RJLabel))
                        {
                            RJLabel lbl = (RJLabel)contrl;
                            lbl.Font = fnt;
                        }
                        else if (contrl.GetType() == typeof(RJControls.RJPanel))
                        {
                            RJPanel pnl = (RJPanel)contrl;

                            foreach (var contrl2 in pnl.Controls)
                            {
                                try
                                {
                                    if (contrl2.GetType() == typeof(RJControls.RJLabel))
                                    {
                                        RJLabel lbl = (RJLabel)contrl2;
                                        lbl.Font = fnt;
                                    }
                                    else if (contrl2.GetType() == typeof(RJControls.RJCheckBox))
                                    {
                                        RJCheckBox lbl = (RJCheckBox)contrl2;
                                        lbl.Font = fnt;
                                    }
                                }
                                catch { }
                            }

                        }
                        else if (contrl.GetType() == typeof(TableLayoutPanel))
                        {
                            TableLayoutPanel pnl = (TableLayoutPanel)contrl;

                            foreach (var contrl2 in pnl.Controls)
                            {
                                try
                                {
                                    if (contrl2.GetType() == typeof(RJControls.RJLabel))
                                    {
                                        RJLabel lbl = (RJLabel)contrl;
                                        lbl.Font = fnt;
                                    }
                                }
                                catch { }
                            }

                        }


                    }
                    catch { }
                }


            }
            catch (Exception ex) { }

            try
            {
                int totalfonts = FontFactory.RegisterDirectory("C:\\WINDOWS\\Fonts");
                StringBuilder sb = new StringBuilder();
                foreach (string fontname in FontFactory.RegisteredFonts)
                {
                    //sb.Append(fontname + "\n");
                    CB_Fonts.Items.Add(fontname);
                }
                //CB_Fonts.SelectedItem = loc_login.Font;
            }
            catch { }

            utils.Control_textSize(pnlClientArea);
            return;
            Control_Loop(pnlClientArea);
        }
        private void disableAll_Lable()
        {

            foreach (Control lb in tableLayoutPanel1.Controls)
            {
                if (lb.GetType() == typeof(RJCheckBox))
                {
                    RJCheckBox ch = new RJCheckBox();
                    //System.Windows.Forms.CheckBox ch = new System.Windows.Forms.CheckBox();
                    ch = (RJCheckBox)lb;
                    ch.Checked = false;
                }
            }
        }
        public void SetValuToCardToGraphics()
        {
            if (card == null) return;
            firstLoad = true;
            selectTemplateFromDrowpDown = true;
            saveToClass = false;
            DisableBorder_All();

            BackgroundImgCard_Chbox.Checked = card.setingCard.enable_background;
            //txt_Pathfile.Text = card.setingCard.path_saved_file.ToString();
            txt_PathImage.Text = card.setingCard.path_background.ToString();
            //if (BackgroundImgCard_Chbox.Checked) set_BackroundFromPath(true);

            //comboBox_quilty_image.SelectedIndex = card.setingCard.quilty_image;

            //CBox_Curncey.Text = card.setingCard.currency;
            TextCard_W.Value = card.setingCard.card_width;
            //txt_SizeBorder_midell.Value = (decimal)card.setingCard.size_Border_Midell;
            txt_Padding_Cell.Value = (decimal)card.setingCard.Padding_Cell;
            txt_Space_X.Value = card.setingCard.space_horizontal_margin;
            txt_Space_Y.Value = card.setingCard.Space_vertical_margin;
            checkBoxBorderCard.Checked = card.setingCard.card_border_enable;
            txt_SizeBorder.Value = (decimal)card.setingCard.card_border_Size;
            btn_BorderColor.BackColor = System.Drawing.ColorTranslator.FromHtml(card.setingCard.card_border_Color);

            check_Number_Pages.Checked = card.setingCard.Number_Pages;
            txt_Number_Page_Size.Value = card.setingCard.Number_Pages_Size;
            txt_Number_Page_X.Value = card.setingCard.Number_Pages_X;
            txt_Number_Page_Y.Value = card.setingCard.Number_Pages_Y;

            checkNoteOnPage.Checked = card.setingCard.Note_On_Pages;
            txt_Note_Page_Size.Value = card.setingCard.Note_On_Pages_Size;
            txt_Note_Page_X.Value = card.setingCard.Note_On_Pages_X;
            txt_Note_Page_Y.Value = card.setingCard.Note_On_Pages_Y;
            txt_Note_onPage.Text = card.setingCard.Note_On_Pages_text;
            CBox_NoteType_onPage.SelectedIndex = card.setingCard.NoteType_onPage;

            checkBox_Border_Colum.Checked = card.setingCard.Show_Border_Coulum;
            checkBox_Border_Row.Checked = card.setingCard.Show_Border_Row;
            checkBox_Border_midell_careds.Checked = card.setingCard.Show_border_Midell;
            txt_SizeBorder_midell.Value = (decimal)card.setingCard.size_Border_Midell;

            checkBox_Width_Card.Checked = card.setingCard.Fixed_Width_Card;
            txt_NumberCulum.Text = card.setingCard.NumberCulum.ToString();

            if (card.setingCard.Fixed_Width_Card)
            {
                //txt_NumberCulum.ReadOnly = true;
                txt_NumberCulum.Enabled = true;
                TextCard_W.ReadOnly = false;
            }
            else
            {
                //txt_NumberCulum.ReadOnly = false;
                txt_NumberCulum.Enabled = false;
                TextCard_W.ReadOnly = true;
            }

            //pictureBox1.Width = Convert.ToInt32((Convert.ToDecimal(TextCard_W.Text)) * (decimal)6.0);
            //pictureBox1.Height = Convert.ToInt16((Convert.ToDecimal(TextCard_Y.Text)) * (decimal)6.0);
            try { CBox_Profile.Text = card.setingCard.proile_link; } catch { }
            try { CBox_Profile_HS.Text = card.setingCard.proile_HS_link; } catch { }
            try { CBox_Profile_HotspotLocal.Text = card.setingCard.proile_HS_Local_link; } catch { }
            try { CBox_Curncey.Text = card.setingCard.currency; } catch { }

            //txt_LogoImage.Text = card.cardsItems.logo.Path;
            set_value_For_item();
            selectTemplateFromDrowpDown = false;



        }
        void DisableBorder_All()
        {
            //if (UIAppearance.Theme == UITheme.Dark)
            //    return;
            Label ll = new Label();
            foreach (Control lbl in tableLayoutPanel1.Controls)
            {
                if (lbl.GetType() == typeof(Label))
                {
                    ll = (Label)lbl;
                    ll.BackColor = Color.Transparent;
                    //ll.BackColor = Color.White;
                }
            }
        }
        void DisableBorder_All(Control elment)
        {
            Label ll = new Label();
            RJLabel elm = new RJLabel();
            foreach (Control lbl in tableLayoutPanel1.Controls)
            {
                if (lbl.GetType() == typeof(RJLabel))
                {
                    ll = (RJLabel)lbl;
                    //ll.BorderStyle = BorderStyle.None;
                    //ll.BackColor = Color.White;

                    //ll.BackColor = Color.FromArgb(250, 252, 253);
                    //ll.ForeColor = Color.FromArgb(132, 129, 132);
                    ll.BackColor = Color.Transparent;
                    ll.ForeColor = UIAppearance.TextColor;
                }
            }

            if (elment.GetType() == typeof(RJLabel))
            {
                ll = (RJLabel)elment;
                ll.BackColor = Color.Gold;
                ll.ForeColor = Color.Black;
            }
            elment.BringToFront();
        }
        void set_value_For_item()
        {
            //============info1=================
            Info1_Chbox.Checked = card.cardsItems.info1.Enable;
            txt_AddresItem.Text = card.cardsItems.info1.title_text;
            TXT_info1.Text = card.cardsItems.info1.title_text;
            Set_Proprties_For_Item(TXT_info1, card.cardsItems.info1);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info1, card.cardsItems.info1, null);
            //check_Show_AddresItem.Checked = true;
            check_Show_Unit_Item.Enabled = false;
            //============info2=================
            Info2_Chbox.Checked = card.cardsItems.info2.Enable;
            TXT_info2.Text = card.cardsItems.info2.title_text;
            txt_AddresItem.Text = card.cardsItems.info2.title_text;
            Set_Proprties_For_Item(TXT_info2, card.cardsItems.info2);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info2, card.cardsItems.info2, null);

            //============login=================
            Login_Chbox.Checked = card.cardsItems.login.Enable;
            txt_AddresItem.Text = card.cardsItems.login.title_text;
            txt_lbl_username.Text = card.cardsItems.login.title_text;
            Set_Proprties_For_Item(TXT_USerName, card.cardsItems.login);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_USerName, card.cardsItems.login, txt_lbl_username);

            //============password=================
            Password_Chbox.Checked = card.cardsItems.Password.Enable;
            txt_AddresItem.Text = card.cardsItems.Password.title_text;
            txt_lbl_password.Text = card.cardsItems.Password.title_text;
            Set_Proprties_For_Item(TXT_Password, card.cardsItems.Password);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Password, card.cardsItems.Password, txt_lbl_password);

            //============Price=================
            Price_Chbox.Checked = card.cardsItems.Price.Enable;
            txt_AddresItem.Text = card.cardsItems.Price.title_text;
            txt_lbl_price.Text = card.cardsItems.Price.title_text;
            Set_Proprties_For_Item(TXT_Price, card.cardsItems.Price);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Price, card.cardsItems.Price, txt_lbl_price);

            //============Lbl_Time=================
            Time_Chbox.Checked = card.cardsItems.Time.Enable;
            txt_AddresItem.Text = card.cardsItems.Time.title_text;
            txt_lbl_time.Text = card.cardsItems.Time.title_text;
            Set_Proprties_For_Item(TXT_Uptime, card.cardsItems.Time);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Uptime, card.cardsItems.Time, txt_lbl_time);

            //============Lbl_SizeTransfer=================
            SizeTransfer_Chbox.Checked = card.cardsItems.Size.Enable;
            txt_AddresItem.Text = card.cardsItems.Size.title_text;
            txt_lbl_download.Text = card.cardsItems.Size.title_text;
            Set_Proprties_For_Item(TXT_SizeTransfer, card.cardsItems.Size);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SizeTransfer, card.cardsItems.Size, txt_lbl_download);

            //============Lbl_validity=================
            Vaildate_Chbox.Checked = card.cardsItems.Validity.Enable;
            txt_AddresItem.Text = card.cardsItems.Validity.title_text;
            txt_lbl_validy.Text = card.cardsItems.Validity.title_text;
            Set_Proprties_For_Item(TXT_Validate, card.cardsItems.Validity);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Validate, card.cardsItems.Validity, txt_lbl_validy);

            //============lbl_Squ_Nuber=================
            SN_Chbox.Checked = card.cardsItems.SN.Enable;
            txt_AddresItem.Text = card.cardsItems.SN.title_text;
            txt_lbl_SN.Text = card.cardsItems.SN.title_text;
            Set_Proprties_For_Item(TXT_SQ, card.cardsItems.SN);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SQ, card.cardsItems.SN, txt_lbl_SN);

            //============SP=================
            SP_Chbox.Checked = card.cardsItems.SP.Enable;
            txt_AddresItem.Text = card.cardsItems.SP.title_text;
            txt_lbl_SP.Text = card.cardsItems.SP.title_text;
            Set_Proprties_For_Item(TXT_SP, card.cardsItems.SP);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SP, card.cardsItems.SP, txt_lbl_SP);
            CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);

            //============Lbl_Number_Print=================
            Number_Print_Chbox.Checked = card.cardsItems.Number_Print.Enable;
            txt_AddresItem.Text = card.cardsItems.Number_Print.title_text;
            txt_lbl_NumberPrint.Text = card.cardsItems.Number_Print.title_text;
            Set_Proprties_For_Item(TXT_NumberPrint, card.cardsItems.Number_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_NumberPrint, card.cardsItems.Number_Print, txt_lbl_NumberPrint);

            //============TXT_DatePrint=================
            Date_Print_Chbox.Checked = card.cardsItems.Date_Print.Enable;
            txt_AddresItem.Text = card.cardsItems.Date_Print.title_text;
            txt_lbl_DatePrint.Text = card.cardsItems.Date_Print.title_text;
            Set_Proprties_For_Item(TXT_DatePrint, card.cardsItems.Date_Print);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_DatePrint, card.cardsItems.Date_Print, txt_lbl_DatePrint);
            txt_lbl_DatePrint.Text = card.cardsItems.Date_Print.title_text;
            CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;

            //============info3=================
            Info3_Chbox.Checked = card.cardsItems.info3.Enable;
            txt_AddresItem.Text = card.cardsItems.info3.title_text;
            TXT_info3.Text = card.cardsItems.info3.title_text;
            Set_Proprties_For_Item(TXT_info3, card.cardsItems.info3);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info3, card.cardsItems.info3, null);

            //============info4=================
            Info4_Chbox.Checked = card.cardsItems.info4.Enable;
            txt_AddresItem.Text = card.cardsItems.info4.title_text;
            TXT_info4.Text = card.cardsItems.info4.title_text;
            Set_Proprties_For_Item(TXT_info4, card.cardsItems.info4);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info4, card.cardsItems.info4, null);


            //============info5=================
            Info5_Chbox.Checked = card.cardsItems.info5.Enable;
            txt_AddresItem.Text = card.cardsItems.info5.title_text;
            TXT_info5.Text = card.cardsItems.info5.title_text;
            Set_Proprties_For_Item(TXT_info5, card.cardsItems.info5);
            Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info5, card.cardsItems.info5, null);


            firstLoad = false;
            selectTemplateFromDrowpDown = false;
            saveToClass = true;
            ////============password=================
            //Set_Proprties_For_Item(Password_Lbl, card.cardsItems.Password);
            //ChangPositonControl_In_Imag(Password_Lbl, card.cardsItems.Password);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Password_Lbl, card.cardsItems.Password);
            //Password_Chbox.Checked = card.cardsItems.Password.Enable;

            ////checkShowAddresItem.Checked = card.cardsItems.Password.title_show;
            ////txt_AddresItem.Text = card.cardsItems.Password.title_text;

            ////============Price=================
            //Set_Proprties_For_Item(Lbl_Price, card.cardsItems.Price);
            //ChangPositonControl_In_Imag(Lbl_Price, card.cardsItems.Price);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Price, card.cardsItems.Price);
            //Price_Chbox.Checked = card.cardsItems.Price.Enable;
            ////============Lbl_Time=================
            //Set_Proprties_For_Item(Lbl_Time, card.cardsItems.Time);
            //ChangPositonControl_In_Imag(Lbl_Time, card.cardsItems.Time);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Time, card.cardsItems.Time);
            //Time_Chbox.Checked = card.cardsItems.Time.Enable;
            //CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;

            ////============Lbl_SizeTransfer=================
            //Set_Proprties_For_Item(Lbl_SizeTransfer, card.cardsItems.Size);
            //ChangPositonControl_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SizeTransfer, card.cardsItems.Size);
            //Size_Chbox.Checked = card.cardsItems.Size.Enable;
            ////CBox_UniteTransfer_format.Visible = true;
            //CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;

            ////============Lbl_validity=================
            //Set_Proprties_For_Item(Lbl_validity, card.cardsItems.Validity);
            //ChangPositonControl_In_Imag(Lbl_validity, card.cardsItems.Validity);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_validity, card.cardsItems.Validity);
            //validity_Chbox.Checked = card.cardsItems.Validity.Enable;
            //CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;

            ////============lbl_Squ_Nuber=================
            //Set_Proprties_For_Item(lbl_Squ_Nuber, card.cardsItems.SN);
            //ChangPositonControl_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            //Change_Font_Size_Bold_Color_Control_In_Imag(lbl_Squ_Nuber, card.cardsItems.SN);
            //Squnce_Number_Chbox.Checked = card.cardsItems.SN.Enable;
            ////============SP=================
            //Set_Proprties_For_Item(Lbl_SP, card.cardsItems.SP);
            //ChangPositonControl_In_Imag(Lbl_SP, card.cardsItems.SP);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_SP, card.cardsItems.SP);
            //SP_Chbox.Checked = card.cardsItems.SP.Enable;
            //CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);

            ////============Lbl_Number_Print=================
            //Set_Proprties_For_Item(Lbl_Number_Print, card.cardsItems.Number_Print);
            //ChangPositonControl_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Number_Print, card.cardsItems.Number_Print);
            //Number_Print_Chbox.Checked = card.cardsItems.Number_Print.Enable;
            ////============Lbl_OtherText1=================
            //Set_Proprties_For_Item(Lbl_OtherText1, card.cardsItems.Other_Text1);
            //ChangPositonControl_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText1, card.cardsItems.Other_Text1);
            //OtherText1_Chbox.Checked = card.cardsItems.Other_Text1.Enable;
            //txt_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            //Lbl_OtherText1.Text = card.cardsItems.Other_Text1.title_text;
            ////Lbl_OtherText1.Text=card.cardsItems.Other_Text1.title_text;
            ////checkShowAddresItem.Checked = false;
            ////txt_AddresItem.Text = "";

            //////============Lbl_OtherText2=================
            ////Set_Proprties_For_Item(Lbl_OtherText2, card.cardsItems.Other_Text2);
            ////ChangPositonControl_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            ////Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_OtherText2, card.cardsItems.Other_Text2);
            ////OtherText1_Chbox.Checked = card.cardsItems.Other_Text2.Enable;
            ////============Lbl_Date_Print=================
            //Set_Proprties_For_Item(Lbl_Date_Print, card.cardsItems.Date_Print);
            //ChangPositonControl_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            //Change_Font_Size_Bold_Color_Control_In_Imag(Lbl_Date_Print, card.cardsItems.Date_Print);
            //Date_Print_Chbox.Checked = card.cardsItems.Date_Print.Enable;
            ////card.cardsItems.SP.Show_ByNumber_OR_Name = !Convert.ToBoolean(CBox_SellingPoint.SelectedIndex);
            //CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;
            ////============QR=================
            //Set_Proprties_For_Item_img(pictureBox_QR, card.cardsItems.QR);
            //ChangPositonControl_IMAGE_In_Imag(pictureBox_QR, card.cardsItems.QR);
            //QR_Chbox.Checked = card.cardsItems.QR.Enable;
            ////============logo=================
            //Set_Proprties_For_Item_img(pictureBox_logo, card.cardsItems.logo);
            //ChangPositonControl_IMAGE_In_Imag(pictureBox_logo, card.cardsItems.logo);
            //Logo_Chbox.Checked = card.cardsItems.logo.Enable;
            //try
            //{
            //    Bitmap img = new Bitmap(card.cardsItems.logo.Path);
            //    pictureBox_logo.Image = System.Drawing.Image.FromFile(card.cardsItems.logo.Path);
            //}
            //catch { /*MessageBox.Show("خطأ في صوره الشعار"); */}


        }
        void Set_Proprties_For_Item(Control lbl, PropertyItemText loc)
        {
            try
            {
                //lbl.Visible = loc.Enable;

                //txt_Element_W.Value = loc.x;
                //txt_Element_Y.Value = loc.y;

                Color color = ColorTranslator.FromHtml(loc.Color);
                btn_ElementColor.BackColor = color;
                lbl.ForeColor = color;

                CB_ElementSize.Text = loc.font_size.ToString();
                txt_font.Text = loc.Font;
                CB_Fonts.Text = loc.Font;

                checkShowAddresItem.Checked = loc.title_show;
                txt_AddresItem.Text = loc.title_text;

                check_Show_Unit_Item.Checked = loc.unit_show;

                checkBoxISBlod.Checked = loc.Blod;
                checkBoxIsItalic.Checked = loc.italic;

                Color colorTitle = ColorTranslator.FromHtml(loc.title_Color);
                btn_title_ElementColor.BackColor = colorTitle;

                CBox_UniteTime_format.Visible = false;
                CBox_UniteTransfer_format.Visible = false;
                CBox_UniteValidaty_format.Visible = false;
                CBox_SellingPoint.Visible = false;
                CBox_Date_print_format.Visible = false;
                CBox_Curncey.Visible = false;
                //checkShowUnit_Item

                check_Show_Unit_Item.Enabled = false;
                //check_Show_Unit_Item.Checked = false;
                //CBox_Date_print_format.Text = "";
            }
            catch (Exception e) { MessageBox.Show(e.Message); }

            //if(lbl.Name == "Lbl_Date_Print")
            //{
            //    CBox_Date_print.SelectedItem = loc.address_text;
            //}
            ////ChangPositonControl_In_Imag(lbl);
        }
        void Change_Font_Size_Bold_Color_Control_In_Imag(Control lbl, PropertyItemText loc, Control txt_title)
        {
            //return;
            //if (selectTemplateFromDrowpDown || firstLoad)
            //    return;

            Color color = ColorTranslator.FromHtml(loc.Color);
            lbl.ForeColor = color;
            //btn_ElementColor.BackColor = color;
            //lbl.Font = lbl.Font;

            if (txt_title == null)
                return;
                //if (loc.title_show)
                //{
                    Color colorTitle = ColorTranslator.FromHtml(loc.title_Color);
                    txt_title.ForeColor = colorTitle;
            //btn_title_ElementColor.BackColor = colorTitle;
            //txt_title.Font = lbl.Font;
            //}

            return;
            if (loc.Blod)
            {

                if (loc.italic)
                    lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size, FontStyle.Italic | FontStyle.Bold);
                else lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size, FontStyle.Bold);
            }
            else
            {
                if (loc.italic) lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size, FontStyle.Italic | FontStyle.Regular);
                else lbl.Font = new System.Drawing.Font(loc.Font, loc.font_size, FontStyle.Regular);
            }
            Color color2 = ColorTranslator.FromHtml(loc.Color);
            lbl.ForeColor = color;
            btn_ElementColor.BackColor = color;

            //#############
            try
            {
                if (txt_title != null)
                    if (loc.title_show)
                    {
                        Color colorTitle2 = ColorTranslator.FromHtml(loc.title_Color);
                        //String Color = System.Drawing.ColorTranslator.ToHtml(color);            
                        txt_title.ForeColor = colorTitle;
                        btn_title_ElementColor.BackColor = colorTitle;
                        txt_title.Font = lbl.Font;
                    }
            }
            catch  { }
        }
        private void Get_TemplateCardsItemsGraphics()
        {
            if (addDeletTemplate) return;

            SourceCardsTemplate sorcCard = new SourceCardsTemplate();

            try
            {
                sorcCard = SqlDataAccess.Get_template_cards_By_Name(CBox_TemplateCards.Text.ToString());
                //card = SqlDataAccess.Get_template_cards_By_id(CBox_TemplateCards.SelectedItem.ToString());
                if (sorcCard == null)
                {
                    card.setingCard.NumberCulum = 3;
                    card.setingCard.card_border_Size = .5f;
                    card.setingCard.Fixed_Width_Card = false;
                    card.setingCard.Show_border_Midell = true;

                    card = new CardsTableDesg1();
                    card.cardsItems.info1.title_text = "شبكة سمارت اللاسليكة";
                    card.cardsItems.info1.Enable = true;

                    card.cardsItems.login.title_text = "اسم الدخول";
                    card.cardsItems.login.title_show = true;
                    card.cardsItems.login.Enable = true;
                    
                    card.cardsItems.Password.title_text = "كلمة المرور";
                    card.cardsItems.Password.title_show = true;
                    card.cardsItems.Password.Enable = true;

                    card.cardsItems.Time.title_text = "الوقت";
                    card.cardsItems.Time.title_show = true;
                    card.cardsItems.Time.Enable = true;

                    card.cardsItems.Price.title_text = "السعر";
                    card.cardsItems.Price.title_show = true;
                    card.cardsItems.Price.Enable = true;

                    card.cardsItems.Validity.title_text = "الصلاحية";
                    card.cardsItems.Validity.title_show = true;
                    card.cardsItems.Validity.Enable = false;

                    card.cardsItems.Size.title_text = "كمية التحميل";
                    card.cardsItems.Size.title_show = true;
                    card.cardsItems.Size.Enable = false;

                    card.cardsItems.SP.title_text = "نقطة البيع";
                    card.cardsItems.SP.title_show = true;
                    card.cardsItems.SP.Enable = false;

                    card.cardsItems.SN.title_text = "التسلسل";
                    card.cardsItems.SN.title_show = true;
                    card.cardsItems.SN.Enable = false;

                    card.cardsItems.Date_Print.title_text = "تاريخ الطباعة";
                    card.cardsItems.Date_Print.title_show = true;
                    card.cardsItems.Date_Print.Enable = false;

                    card.cardsItems.Number_Print.title_text = "رقم الدفعة";
                    card.cardsItems.Number_Print.title_show = true;
                    card.cardsItems.Number_Print.Enable = false;

                    card.cardsItems.info3.title_text = "للتواصل والاستفسار الاتصال علي:7777777";
                    card.cardsItems.info3.title_show = true;
                    card.cardsItems.info3.Enable = true;

                    card.cardsItems.info4.title_text = "اعلان 1";
                    card.cardsItems.info4.title_show = true;
                    card.cardsItems.info4.Enable = false;
                }
                else
                    card = JsonConvert.DeserializeObject<CardsTableDesg1>(sorcCard.values);
            }
            catch (Exception ex) { /*MessageBox.Show("Get_TemplateCardsItemsGraphics   " + ex.Message);*/ }

        }
        void Calclate_Number_Card_In_Page()
        {


            //==========================================================================================  

            float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(txt_Space_X.Text));
            float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(txt_Space_Y.Text));

            float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(TextCard_W.Text));
            float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(TextCard_Y.Text));


            float Pictur_width_orginal = Pictur_width;
            float Pictur_height__orginal = Pictur_height;




            float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
            float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);

            //int NuberCards = 51;
            int NumberCard_for_OneColum = 0;
            int NumberCard_in_Page = 0;
            double NumberPages = 0;

            int ColumNumber = 0; float CardNumber = 0;

            ColumNumber = (int)(595 / (Pictur_width + Space_Y));
            if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                ColumNumber = ColumNumber - 1;

            NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
            if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                NumberCard_for_OneColum = NumberCard_for_OneColum - 1;


            NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

            txt_NumberCard.Text = NumberCard_in_Page.ToString();
            txt_NumberCulum.Text = ColumNumber.ToString();

            //iTextSharp.text.Image jpg = null;


        }
        public void Loc_item_Set_To_Use()
        {
            if (saveToClass == false)
                return;

            if (lbl_info1.BackColor == Color.Gold)
            {
                card.cardsItems.info1.Font = txt_font.Text;
                card.cardsItems.info1.Blod = checkBoxISBlod.Checked;
                card.cardsItems.info1.italic = checkBoxIsItalic.Checked;
                card.cardsItems.info1.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.info1.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.info1.title_show = checkShowAddresItem.Checked;
                card.cardsItems.info1.title_text = txt_AddresItem.Text;
                TXT_info1.Text = card.cardsItems.info1.title_text;
                card.cardsItems.info1.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_info1.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info1, card.cardsItems.info1, TXT_info1);

            }

            if (lbl_info2.BackColor == Color.Gold)
            {
                card.cardsItems.info2.Font = txt_font.Text;
                card.cardsItems.info2.Blod = checkBoxISBlod.Checked;
                card.cardsItems.info2.italic = checkBoxIsItalic.Checked;
                card.cardsItems.info2.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.info2.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.info2.title_show = checkShowAddresItem.Checked;
                card.cardsItems.info2.title_text = txt_AddresItem.Text;
                TXT_info2.Text = card.cardsItems.info2.title_text;
                card.cardsItems.info2.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_info2.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info2, card.cardsItems.info2, TXT_info2);

            }
            if (lbl_login.BackColor == Color.Gold)
            {
                card.cardsItems.login.Font = txt_font.Text;
                card.cardsItems.login.Blod = checkBoxISBlod.Checked;
                card.cardsItems.login.italic = checkBoxIsItalic.Checked;
                card.cardsItems.login.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.login.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);
                //card.cardsItems.login.y = txt_Element_Y.Value;
                //card.cardsItems.login.x = txt_Element_W.Value;
                card.cardsItems.login.title_show = checkShowAddresItem.Checked;
                card.cardsItems.login.title_text = txt_AddresItem.Text;
                txt_lbl_username.Text = card.cardsItems.login.title_text;
                card.cardsItems.login.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_USerName.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_USerName, card.cardsItems.login, txt_lbl_username);
                //ChangPositonControl_In_Imag(Login_Lbl, card.cardsItems.login);

            }
            if (lbl_Password.BackColor == Color.Gold)
            {
                card.cardsItems.Password.Font = txt_font.Text;
                card.cardsItems.Password.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Password.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Password.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Password.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Password.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Password.title_text = txt_AddresItem.Text;
                txt_lbl_password.Text = card.cardsItems.Password.title_text;
                card.cardsItems.Password.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_Password.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Password, card.cardsItems.Password, txt_lbl_password);
            }
            if (lbl_time.BackColor == Color.Gold)
            {
                card.cardsItems.Time.Font = txt_font.Text;
                card.cardsItems.Time.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Time.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Time.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Time.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Time.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Time.title_text = txt_AddresItem.Text;
                txt_lbl_time.Text = card.cardsItems.Time.title_text;
                card.cardsItems.Time.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_Uptime.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Uptime, card.cardsItems.Time, txt_lbl_time);

                card.cardsItems.Time.unit_show = check_Show_Unit_Item.Checked;
                card.cardsItems.Time.unit_format = CBox_UniteTime_format.SelectedIndex;


            }
            if (lbl_validay.BackColor == Color.Gold)
            {
                card.cardsItems.Validity.Font = txt_font.Text;
                card.cardsItems.Validity.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Validity.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Validity.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Validity.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Validity.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Validity.title_text = txt_AddresItem.Text;
                txt_lbl_validy.Text = card.cardsItems.Validity.title_text;
                card.cardsItems.Validity.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_Validate.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Validate, card.cardsItems.Validity, txt_lbl_validy);
                card.cardsItems.Validity.unit_show = check_Show_Unit_Item.Checked;
                card.cardsItems.Validity.unit_format = CBox_UniteValidaty_format.SelectedIndex;
            }

            if (lbl_transfer.BackColor == Color.Gold)
            {
                card.cardsItems.Size.Font = txt_font.Text;
                card.cardsItems.Size.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Size.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Size.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Size.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Size.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Size.title_text = txt_AddresItem.Text;
                txt_lbl_download.Text = card.cardsItems.Size.title_text;
                card.cardsItems.Size.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_SizeTransfer.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SizeTransfer, card.cardsItems.Size, txt_lbl_download);
                card.cardsItems.Size.unit_show = check_Show_Unit_Item.Checked;
                card.cardsItems.Size.unit_format = CBox_UniteTransfer_format.SelectedIndex;
            }
            if (lbl_price.BackColor == Color.Gold)
            {
                card.cardsItems.Price.Font = txt_font.Text;
                card.cardsItems.Price.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Price.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Price.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Price.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Price.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Price.title_text = txt_AddresItem.Text;
                txt_lbl_price.Text = card.cardsItems.Price.title_text;
                card.cardsItems.Price.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_Price.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Price, card.cardsItems.Price, txt_lbl_price);
                card.cardsItems.Price.unit_show = check_Show_Unit_Item.Checked;

            }
            if (lbl_sn.BackColor == Color.Gold)
            {
                card.cardsItems.SN.Font = txt_font.Text;
                card.cardsItems.SN.Blod = checkBoxISBlod.Checked;
                card.cardsItems.SN.italic = checkBoxIsItalic.Checked;
                card.cardsItems.SN.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.SN.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.SN.title_show = checkShowAddresItem.Checked;
                card.cardsItems.SN.title_text = txt_AddresItem.Text;
                txt_lbl_SN.Text = card.cardsItems.SN.title_text;
                card.cardsItems.SN.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_SQ.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SQ, card.cardsItems.SN, txt_lbl_SN);
            }
            if (lbl_sp.BackColor == Color.Gold)
            {
                card.cardsItems.SP.Font = txt_font.Text;
                card.cardsItems.SP.Blod = checkBoxISBlod.Checked;
                card.cardsItems.SP.italic = checkBoxIsItalic.Checked;
                card.cardsItems.SP.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.SP.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.SP.title_show = checkShowAddresItem.Checked;
                card.cardsItems.SP.title_text = txt_AddresItem.Text;
                txt_lbl_SP.Text = card.cardsItems.SP.title_text;
                card.cardsItems.SP.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                //TXT_SP.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SP, card.cardsItems.SP, txt_lbl_SP);
                try { card.cardsItems.SP.Show_ByNumber_OR_Name = Convert.ToBoolean(CBox_SellingPoint.SelectedIndex); } catch { }

                //CBox_SellingPoint.SelectedIndex = Convert.ToInt16(card.cardsItems.SP.Show_ByNumber_OR_Name);

            }
            if (lbl_dataPrint.BackColor == Color.Gold)
            {
                card.cardsItems.Date_Print.Font = txt_font.Text;
                card.cardsItems.Date_Print.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Date_Print.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Date_Print.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Date_Print.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Date_Print.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Date_Print.title_text = txt_AddresItem.Text;
                txt_lbl_DatePrint.Text = card.cardsItems.Date_Print.title_text;
                card.cardsItems.Date_Print.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_DatePrint.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_DatePrint, card.cardsItems.Date_Print, txt_lbl_DatePrint);
                card.cardsItems.Date_Print.format = CBox_Date_print_format.Text;
            }
            if (lbl_numberPrint.BackColor == Color.Gold)
            {
                card.cardsItems.Number_Print.Font = txt_font.Text;
                card.cardsItems.Number_Print.Blod = checkBoxISBlod.Checked;
                card.cardsItems.Number_Print.italic = checkBoxIsItalic.Checked;
                card.cardsItems.Number_Print.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.Number_Print.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.Number_Print.title_show = checkShowAddresItem.Checked;
                card.cardsItems.Number_Print.title_text = txt_AddresItem.Text;
                txt_lbl_NumberPrint.Text = card.cardsItems.Number_Print.title_text;
                card.cardsItems.Number_Print.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                txt_NumberCard.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(txt_NumberCard, card.cardsItems.Number_Print, txt_lbl_NumberPrint);
            }
            if (lbl_info3.BackColor == Color.Gold)
            {
                card.cardsItems.info3.Font = txt_font.Text;
                card.cardsItems.info3.Blod = checkBoxISBlod.Checked;
                card.cardsItems.info3.italic = checkBoxIsItalic.Checked;
                card.cardsItems.info3.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.info3.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.info3.title_show = checkShowAddresItem.Checked;
                card.cardsItems.info3.title_text = txt_AddresItem.Text;
                TXT_info3.Text = card.cardsItems.info3.title_text;
                card.cardsItems.info3.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_info3.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info3, card.cardsItems.info3, TXT_info3);

            }
            if (lbl_info4.BackColor == Color.Gold)
            {
                card.cardsItems.info4.Font = txt_font.Text;
                card.cardsItems.info4.Blod = checkBoxISBlod.Checked;
                card.cardsItems.info4.italic = checkBoxIsItalic.Checked;
                card.cardsItems.info4.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.info4.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.info4.title_show = checkShowAddresItem.Checked;
                card.cardsItems.info4.title_text = txt_AddresItem.Text;
                TXT_info4.Text = card.cardsItems.info4.title_text;
                card.cardsItems.info4.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_info4.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info4, card.cardsItems.info4, TXT_info4);

            }
            if (lbl_info5.BackColor == Color.Gold)
            {
                card.cardsItems.info5.Font = txt_font.Text;
                card.cardsItems.info5.Blod = checkBoxISBlod.Checked;
                card.cardsItems.info5.italic = checkBoxIsItalic.Checked;
                card.cardsItems.info5.font_size = Convert.ToInt16(CB_ElementSize.Text);
                card.cardsItems.info5.Color = ColorTranslator.ToHtml(btn_ElementColor.BackColor);

                card.cardsItems.info5.title_show = checkShowAddresItem.Checked;
                card.cardsItems.info5.title_text = txt_AddresItem.Text;
                TXT_info5.Text = card.cardsItems.info5.title_text;
                card.cardsItems.info5.title_Color = ColorTranslator.ToHtml(btn_title_ElementColor.BackColor);
                TXT_info5.ForeColor = btn_ElementColor.BackColor;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info5, card.cardsItems.info5, TXT_info5);

            }

        }
        private Control getSelectControl_pic_lbl()
        {
            Control contrl = new Control();
            //Label lbl = new Label();
            //PictureBox pic = new PictureBox();
            Control ctr_selected = new Control();

            foreach (Control control in tableLayoutPanel1.Controls)
            {
                if (control.GetType() == typeof(PictureBox))
                {
                    //PictureBox pic = (PictureBox)control;
                    //if (pic.BorderStyle == BorderStyle.FixedSingle)
                    //{
                    //    ctr_selected = pic;
                    //}
                }
                else
                {
                    if (control.GetType() == typeof(Label))
                    {
                        Label lbl = (Label)control;
                        if (lbl.ForeColor == Color.Gold)
                        {
                            ctr_selected = lbl;
                        }
                    }
                }
            }
            return ctr_selected;
        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            rjPanel1.Refresh();
            tableLayoutPanel1.Refresh();
        }

        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            CBox_Profile.SelectedIndex = -1;
            CBox_Profile_HS.SelectedIndex = -1;
            CBox_Profile.Text = "";
            CBox_Profile_HS.Text = "";
            selectTemplateFromDrowpDown = true;
            if (!firstLoad)
            {
                disableAll_Lable();
                Get_TemplateCardsItemsGraphics();
                SetValuToCardToGraphics();
                CBox_Curncey.Visible = true;

            }
            //firstLoad = false;
            selectTemplateFromDrowpDown = false;
        }

        private void btnAddTemplate_Click(object sender, EventArgs e)
        {
            addDeletTemplate = true;

            if (card == null)
            {
                card = new CardsTableDesg1();
                return;
            }

            try
            {
                frm_Input_Dailog_New_Template frm = new frm_Input_Dailog_New_Template();
                frm.ShowDialog();
                
                if (frm.add)
                {
                    SourceCardsTemplate tmplate = new SourceCardsTemplate();
                    tmplate.name = frm.txt_Name.Text;
                    tmplate.type = "table_Desigen1";

                    card.setingCard.name = frm.txt_Name.Text;
                    card.setingCard.type = "table_Desigen1";
                    card.setingCard.proile_HS_link = "";
                    card.setingCard.proile_link = "";

                    tmplate.values = JsonConvert.SerializeObject(card);
                    if (SqlDataAccess.Add_New_Template(tmplate))
                    {
                        RJMessageBox.Show("تم انشاء القالب بنجاح", "ok");
                        Get_TemplateCardsFromDB();
                        addDeletTemplate = false;
                        CBox_TemplateCards.SelectedIndex = CBox_TemplateCards.Items.Count - 1;
                        CBox_Profile.SelectedIndex = -1;
                    }
                    else
                        RJMessageBox.Show("خطاء بالاضافة", "ok");
                }
            }
            catch (Exception ex) { addDeletTemplate = false; MessageBox.Show(ex.Message); }

        }

        private void btnSaveTemplate_Click(object sender, EventArgs e)
        {
            try
            {
                if (CBox_TemplateCards.Text=="")
                {
                    RJMessageBox.Show("لا يوجد قالب لحفظه اختار القالب");
                    return;
                }
                save_state_Profile_UM();
                save_state_Profile_HS();
                save_state_Profile_Local_HS();

                SourceCardsTemplate sourceCardTemplate = new SourceCardsTemplate();
                sourceCardTemplate.values = JsonConvert.SerializeObject(card);

                sourceCardTemplate.id = (int)CBox_TemplateCards.SelectedValue;

                if (SqlDataAccess.Add_New_Template(sourceCardTemplate, false))
                {
                    RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                }
                else
                    RJMessageBox.Show("خطاء في الحفظ", "ok");
            }
            catch (Exception ex) { MessageBox.Show("save_template_To_DB" + "\n" + ex.Message.ToString()); }
            Get_TemplateCardsItemsGraphics();
        }
        private void save_state_Profile_UM()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile.Text == "")
                return;

            string profileName = CBox_Profile.Text;
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_link == profileName)
                        {
                            card_test.setingCard.proile_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_link == profileName)
                        {
                            card_test.setingCard.proile_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx++;
            }

        }
        private void save_state_Profile_HS()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile_HS.Text == "")
                return;

            string profileName = CBox_Profile_HS.Text;
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_link == profileName)
                        {
                            card_test.setingCard.proile_HS_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_link == profileName)
                        {
                            card_test.setingCard.proile_HS_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx++;
            }

        }


        private void save_state_Profile_Local_HS()
        {
            //if(CBox_Profile.SelectedIndex == -1 || CBox_Profile.SelectedIndex == 0 || CBox_Profile.Text=="")
            //    return;
            if (CBox_Profile_HotspotLocal.Text == "")
                return;

            string profileName = CBox_Profile_HotspotLocal.Text;
            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
            int idx = 0;
            foreach (SourceCardsTemplate s in sorceTemplates)
            {
                if (s.type == "design")
                {
                    CardsTemplate card_test = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_Local_link == profileName)
                        {
                            card_test.setingCard.proile_HS_Local_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card_test = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card_test != null)
                    {
                        if (card_test.setingCard.proile_HS_Local_link == profileName)
                        {
                            card_test.setingCard.proile_HS_Local_link = "";
                            s.values = JsonConvert.SerializeObject(card_test);
                            if (SqlDataAccess.Add_New_Template(s, false))
                            {
                                //RJMessageBox.Show("تم حفظ التغيرات بنجاح", "ok");
                            }
                            //else
                            //    RJMessageBox.Show("خطاء في الحفظ", "ok");

                        }
                    }
                }
                idx++;
            }

        }


        private void btnRemoveTemplate_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من  حذف الباقة ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            if (SqlDataAccess.delete_template((int)CBox_TemplateCards.SelectedValue))
            {
                Get_TemplateCardsFromDB();
                addDeletTemplate = false;
                CBox_TemplateCards.SelectedIndex = 0;
                RJMessageBox.Show("تم الحذف بنجاح ");
            }
            else
                RJMessageBox.Show("خطاء اثناء الحذف");

            addDeletTemplate = false;
        }

        private void btnPreviewTemplate_Click(object sender, EventArgs e)
        {
            if (card == null)
            {
                RJMessageBox.Show("اختار قالب");
                return;
            }
            //Form_PrviewPdf prviewPdf = new Form_PrviewPdf();
            Form_PDF_Prview prviewPdf = new Form_PDF_Prview(card.name);
           
            prviewPdf.type_template = card.type;
            prviewPdf.pathfile = pathfile;
            prviewPdf.CBox_TemplateCards.Text = CBox_TemplateCards.Text;
            prviewPdf.cardTable1 = card;
            //prviewPdf.CBox_Profile.Text = "test";
            prviewPdf.ShowDialog();
            return;
        }

        private void CBox_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_link = CBox_Profile.SelectedValue.ToString();
            }
            catch { }
        }

        private void CBox_Profile_HS_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_HS_link = CBox_Profile_HS.SelectedValue.ToString();
                //card.setingCard.proile_HS_link = CBox_Profile_HS.Text.ToString();
            }
            catch { }
        }

        private void CBox_Curncey_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.currency = CBox_Curncey.SelectedItem.ToString();
        }

        private void checkBox_Width_Card_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (checkBox_Width_Card.Checked == true)
            {
                txt_NumberCulum.Enabled = false;
                //txt_NumberCulum.ReadOnly = true;
                TextCard_W.ReadOnly = false;
                Calclate_Number_Card_In_Page();

            }
            else
            {
                txt_NumberCulum.Enabled = true;
                //txt_NumberCulum.ReadOnly = false;
                TextCard_W.ReadOnly = true;
            }

            if (saveToClass == false) return;
            card.setingCard.Fixed_Width_Card = checkBox_Width_Card.Checked;
            card.setingCard.NumberCulum = Convert.ToInt16(txt_NumberCulum.Text);


        }

        private void TextCard_W_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            //pictureBox1.Width = Convert.ToInt16((TextCard_W.Value) * (decimal)6.0);
            card.setingCard.card_width = TextCard_W.Value;
            Calclate_Number_Card_In_Page();
        }

        private void txt_NumberCulum_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            try { card.setingCard.NumberCulum = Convert.ToUInt16(txt_NumberCulum.Text); } catch { }

        }

        private void BackgroundImgCard_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {
                    if (BackgroundImgCard_Chbox.Checked == true)
                    {

                        //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(txt_PathImage.Text);
                        string destinationPath = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";

                        string destinationFile = System.IO.Path.Combine(destinationPath, card.setingCard.path_background.ToString());

                        //pictureBox1.Image = System.Drawing.Image.FromFile(destinationFile);
                        //// txt_PathImage.Text = openFileDialog1.FileName;
                        //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;

                    }
                    else
                    {
                        //ImgTemplet_Panel.BackgroundImage = null;
                        //pictureBox1.Image = null;
                        //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;

                        ////ImgTemplet_Panel.BackgroundImage = Panel_hight;
                        //// txt_PathImage.Text = openFileDialog1.FileName;
                    }
                }
                catch { }
                card.setingCard.enable_background = BackgroundImgCard_Chbox.Checked;
                //card.setingCard.path_background = txt_PathImage.Text;
                //dt_TempletFromXML.Rows[0]["Enable_background"] = BackgroundImgCard_Chbox.Checked;
                //saveProperties();
            }

        }

        private void btnAddImag_Click(object sender, EventArgs e)
        {
            set_BackroundFromPath(false);
        }
        public void SaveFileImageToAppFolder(string sourceFileName, string sourcePathFile)
        {
            //string AppPath = @"\\tempCards\\cardsBack";
            string destinationPath = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";
            string destinationFileName = DateTime.Now.ToString("yyyyMMddhhmmss-") + sourceFileName; // Don't mind this. I did this because I needed to name the copied files with respect to time.
            string destinationFile = System.IO.Path.Combine(destinationPath, destinationFileName);

            if (!System.IO.Directory.Exists(destinationPath))
            {
                System.IO.Directory.CreateDirectory(destinationPath);
            }
            System.IO.File.Copy(sourcePathFile, destinationFile, true);
            //string destAppPath_File = System.IO.Path.Combine(AppPath, destinationFileName);

            try
            {
                Bitmap img = new Bitmap(destinationFile);
                //pictureBox1.Image = System.Drawing.Image.FromFile(destinationFile);
                BackgroundImgCard_Chbox.Checked = true;

                Properties.Settings.Default.PathFolderBckgroundCards = Path.GetDirectoryName(sourceFileName);
                Properties.Settings.Default.Save();

                card.setingCard.path_background = destinationFileName;
                txt_PathImage.Text = destinationFileName;
            }
            catch (Exception ex)
            {
                //pictureBox1.Image = null;
                BackgroundImgCard_Chbox.Checked = false;
                MessageBox.Show("خطأ في ملف الصوره" + ex.Message.ToString());

            }

        }

        private void set_BackroundFromPath(bool fromDB)
        {
            if (!fromDB)
            {
                try
                {
                    DialogResult res = openFileDialog1.ShowDialog();
                    openFileDialog1.Filter = "jpeg|*.jpg|bmp|*.bmp|all files|*.*";
                    openFileDialog1.RestoreDirectory = true;
                    if (Properties.Settings.Default.PathFolderPrint.ToString() != "")
                        openFileDialog1.InitialDirectory = Properties.Settings.Default.PathFolderBckgroundCards;
                    else
                        openFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";

                    //openFileDialog1.InitialDirectory = Properties.Settings.Default.PathFolderBckgroundCards;

                    if (res == DialogResult.OK)
                    {
                        FileInfo file = new FileInfo(openFileDialog1.FileName);
                        double sizeInBytes = file.Length;
                        try
                        {
                            //Bitmap img = new Bitmap(openFileDialog1.FileName);
                            //txt_PathImage.Text = openFileDialog1.FileName;
                            //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            //pictureBox1.Image = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            //BackgroundImgCard_Chbox.Checked = true;
                            //Cards_setting.path_background = openFileDialog1.FileName;

                            string sourceFileName = Path.GetFileName(openFileDialog1.FileName);
                            SaveFileImageToAppFolder(sourceFileName, openFileDialog1.FileName);

                            //Properties.Settings.Default.PathFolderBckgroundCards = Path.GetDirectoryName(openFileDialog1.FileName);
                            //Properties.Settings.Default.Save();
                            //card.setingCard.path_background = openFileDialog1.FileName; 
                        }
                        catch (Exception ex)
                        {
                            //pictureBox1.Image = null;
                            BackgroundImgCard_Chbox.Checked = false;
                            MessageBox.Show("خطأ في  الصوره" + ex.Message.ToString());

                        }

                    }
                    //BackgroundImgCard_Chbox.Checked = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
            }
            else
            {
                try
                {
                    if (card.setingCard.path_background.ToString() == "")
                        return;
                    //txt_PathImage.Text = card.setingCard.path_background;
                    //MessageBox.Show(card.setingCard.path_background.ToString());
                    string sourcePath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";
                    string SourcePath_File = System.IO.Path.Combine(sourcePath, card.setingCard.path_background.ToString());
                    txt_PathImage.Text = "tempCards\\cardsBack" + card.setingCard.path_background.ToString();
                    FileInfo file = new FileInfo(SourcePath_File);
                    double sizeInBytes = file.Length;
                    try
                    {
                        Bitmap img = new Bitmap(SourcePath_File);
                        //txt_PathImage.Text = openFileDialog1.FileName;
                        //ImgTemplet_Panel.BackgroundImage = System.Drawing.Image.FromFile(txt_PathImage.Text);
                        //pictureBox1.Image = System.Drawing.Image.FromFile(SourcePath_File);
                        BackgroundImgCard_Chbox.Checked = true;
                    }
                    catch
                    {
                        //pictureBox1.Image = null;
                        BackgroundImgCard_Chbox.Checked = false;
                        MessageBox.Show("خطأ في ملف الخلفية");
                    }
                    //BackgroundImgCard_Chbox.Checked = true;
                }
                catch (Exception ex)
                {
                    //pictureBox1.Image = null;
                    BackgroundImgCard_Chbox.Checked = false;
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "خطأ في مسار الصوره");
                    //Update_Um_StatusBar(false, true, 0, "", "خطأ في مسار الصوره");
                    MessageBox.Show("خطأ في مسار الصوره \n" + ex.Message.ToString());
                }
            }
        }

        private void checkBox_Border_midell_careds_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (checkBox_Border_midell_careds.Checked == true)
            {
                checkBox_Border_Colum.Checked = false;
                checkBox_Border_Row.Checked = false;
            }
            if (saveToClass == false) return;

            card.setingCard.Show_border_Midell = checkBox_Border_midell_careds.Checked;
            card.setingCard.Show_Border_Coulum = checkBox_Border_Colum.Checked;
            card.setingCard.Show_Border_Row = checkBox_Border_Row.Checked;

        }

        private void checkBox_Border_Colum_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (checkBox_Border_Colum.Checked == true)
            {
                checkBox_Border_midell_careds.Checked = false;
                checkBox_Border_Row.Checked = false;
            }
            if (saveToClass == false) return;

            card.setingCard.Show_border_Midell = checkBox_Border_midell_careds.Checked;
            card.setingCard.Show_Border_Coulum = checkBox_Border_Colum.Checked;
            card.setingCard.Show_Border_Row = checkBox_Border_Row.Checked;

        }

        private void checkBox_Border_Row_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (checkBox_Border_Row.Checked == true)
            {
                checkBox_Border_midell_careds.Checked = false;
                checkBox_Border_Colum.Checked = false;
            }
            if (saveToClass == false) return;

            card.setingCard.Show_border_Midell = checkBox_Border_midell_careds.Checked;
            card.setingCard.Show_Border_Coulum = checkBox_Border_Colum.Checked;
            card.setingCard.Show_Border_Row = checkBox_Border_Row.Checked;

        }

        private void txt_SizeBorder_midell_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.setingCard.size_Border_Midell = (float)(txt_SizeBorder_midell.Value);

        }

        private void checkBoxBorderCard_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.setingCard.card_border_enable = checkBoxBorderCard.Checked;

        }

        private void txt_SizeBorder_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            //pictureBox1.Update();
            //Refresh();
            card.setingCard.card_border_Size = (float)(txt_SizeBorder.Value);

        }

        private void btn_BorderColor_Click(object sender, EventArgs e)
        {
            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_BorderColor.BackColor = colorDialog1.Color;
                card.setingCard.card_border_Color = System.Drawing.ColorTranslator.ToHtml(colorDialog1.Color);
            }
            //pictureBox1.Update();
            Refresh();
        }

        private void txt_Space_Y_ValueChanged(object sender, EventArgs e)
        {
            Calclate_Number_Card_In_Page();
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }
            card.setingCard.Space_vertical_margin = Convert.ToDecimal(txt_Space_Y.Value);

        }

        private void txt_Space_X_ValueChanged(object sender, EventArgs e)
        {
            Calclate_Number_Card_In_Page();
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if ((Control.MouseButtons & MouseButtons.Left) == MouseButtons.Left)
            {
                return;
            }
            card.setingCard.space_horizontal_margin = Convert.ToDecimal(txt_Space_X.Value);
            Calclate_Number_Card_In_Page();
        }

        private void CB_ElementSize_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (Convert.ToInt16(CB_ElementSize.Text) <= 1)
                CB_ElementSize.Text = 1.ToString();
            Loc_item_Set_To_Use();
        }

        private void btn_ElementColor_Click(object sender, EventArgs e)
        {
            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_ElementColor.BackColor = colorDialog1.Color;
                Loc_item_Set_To_Use();
            }
        }

        private void checkBoxISBlod_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void checkBoxIsItalic_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void CB_Fonts_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Control lbl = getSelectControl_pic_lbl();
            try
            {
                iTextSharp.text.Font palatino = palatino = FontFactory.GetFont(CB_Fonts.SelectedItem.ToString(), BaseFont.CP1252, BaseFont.EMBEDDED, Convert.ToInt16(CB_ElementSize.Text), iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
            }
            catch
            {
                MessageBox.Show("الخط غير مدعوم");
                return;
            }
            txt_font.Text = CB_Fonts.SelectedItem.ToString();
            //loc_itemtemp.Font = CB_Fonts.SelectedItem.ToString();

            if (txt_font.Text.ToLower().Contains("blod"))
            {
                checkBoxISBlod.Checked = true;
            }
            Loc_item_Set_To_Use();
        }

        private void check_Show_AddresItem_CheckedChanged(object sender, EventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void txt_AddresItem_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void btn_title_ElementColor_Click(object sender, EventArgs e)
        {
            //MessageBox.Show("تغير لون عنوان المتغير غير متاح حاليا");
            //return;
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            DialogResult result = colorDialog1.ShowDialog();
            if (result == DialogResult.OK)
            {
                btn_title_ElementColor.BackColor = colorDialog1.Color;
                Loc_item_Set_To_Use();
            }
        }

        private void check_Show_Unit_Item_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            Loc_item_Set_To_Use();
        }

        private void CBox_UniteValidaty_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            card.cardsItems.Validity.unit_format = CBox_UniteValidaty_format.SelectedIndex;

        }

        private void CBox_UniteTransfer_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.cardsItems.Size.unit_format = CBox_UniteTransfer_format.SelectedIndex;

        }

        private void CBox_UniteTime_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.cardsItems.Time.unit_format = CBox_UniteTime_format.SelectedIndex;

        }

        private void CBox_Date_print_format_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.cardsItems.Date_Print.format = CBox_Date_print_format.SelectedItem.ToString();
            Loc_item_Set_To_Use();
        }

        private void CBox_SellingPoint_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (CBox_SellingPoint.SelectedIndex == 0)
                card.cardsItems.SP.Show_ByNumber_OR_Name = false;
            else
                card.cardsItems.SP.Show_ByNumber_OR_Name = true;

        }

        private void check_Number_Pages_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            card.setingCard.Number_Pages = check_Number_Pages.Checked;
            //save_Numbers_Page_info();
        }

        private void txt_Number_Page_X_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_X = txt_Number_Page_X.Value;

                }
                catch { }
            }
        }

        private void txt_Number_Page_Y_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_Y = txt_Number_Page_Y.Value;

                }
                catch { }
            }
        }

        private void txt_Number_Page_Size_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Number_Pages_Size = txt_Number_Page_Size.Value;

                }
                catch { }
            }
        }

        private void checkNoteOnPage_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.setingCard.Note_On_Pages = checkNoteOnPage.Checked;
            card.setingCard.Note_On_Pages_text = txt_Note_onPage.Text;
            card.setingCard.Note_On_Pages_X = txt_Note_Page_X.Value;
            card.setingCard.Note_On_Pages_Y = txt_Note_Page_Y.Value;
            card.setingCard.Note_On_Pages_Size = txt_Note_Page_Size.Value;
            card.setingCard.NoteType_onPage = CBox_NoteType_onPage.SelectedIndex;

        }

        private void CBox_NoteType_onPage_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.NoteType_onPage = CBox_NoteType_onPage.SelectedIndex;


                }
                catch { }
            }
        }

        private void txt_Note_Page_Size_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_Size = txt_Note_Page_Size.Value;

                }
                catch { }
            }
        }

        private void txt_Note_onPage_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.NoteType_onPage = CBox_NoteType_onPage.SelectedIndex;


                }
                catch { }
            }
        }

        private void txt_Note_Page_X_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;

            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_X = txt_Note_Page_X.Value;

                }
                catch { }
            }
        }

        private void txt_Note_Page_Y_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            if (!firstLoad)
            {
                try
                {

                    card.setingCard.Note_On_Pages_Y = txt_Note_Page_Y.Value;

                }
                catch { }
            }
        } 

        private void Info1_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Info1_Chbox.Checked == true)
            {
                card.cardsItems.info1.Enable = true;
                txt_AddresItem.Text = card.cardsItems.info1.title_text;
                TXT_info1.Text = card.cardsItems.info1.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_info1);
                Set_Proprties_For_Item(TXT_info1, card.cardsItems.info1);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info1, card.cardsItems.info1, null);

            }
            else
            {
                card.cardsItems.info1.Enable = false;
                return;
            }
            saveToClass = true;

        } 

        private void info1_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown)
                return;

            DisableBorder_All(lbl_info1);
            Set_Proprties_For_Item(TXT_info1, card.cardsItems.info1);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info1, card.cardsItems.info1, null);

            saveToClass = true;


        }

        private void TXT_info1_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.info1.title_text = TXT_info1.Text;
            txt_AddresItem.Text = TXT_info1.Text;

        }
        private void lbl_info2_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown)
                return;

            DisableBorder_All(lbl_info2);
            Set_Proprties_For_Item(TXT_info2, card.cardsItems.info2);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info2, card.cardsItems.info2, null);

            saveToClass = true;
        }

        private void Info2_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Info2_Chbox.Checked == true)
            {
                card.cardsItems.info2.Enable = true;
                txt_AddresItem.Text = card.cardsItems.info2.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_info2);
                Set_Proprties_For_Item(TXT_info2, card.cardsItems.info2);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info2, card.cardsItems.info2, null);

            }
            else
            {
                card.cardsItems.info2.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void TXT_info2_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.info2.title_text = TXT_info2.Text;
            txt_AddresItem.Text = TXT_info2.Text;

        }

        private void lbl_login_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown)
                return;

            DisableBorder_All(lbl_login);
            Set_Proprties_For_Item(TXT_USerName, card.cardsItems.login);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_USerName, card.cardsItems.login, txt_lbl_username);

            saveToClass = true;
        }

        private void Login_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;

            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Login_Chbox.Checked == true)
            {
                card.cardsItems.login.Enable = true;
                //Login_Lbl.Visible = card.cardsItems.login.title_show;
                txt_AddresItem.Text = card.cardsItems.login.title_text;
                txt_lbl_username.Text = card.cardsItems.login.title_text;
                check_Show_Unit_Item.Enabled = false;
                //groupBox_Date_print_format.Enabled = false;

                DisableBorder_All(lbl_login);
                Set_Proprties_For_Item(TXT_USerName, card.cardsItems.login);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_USerName, card.cardsItems.login, txt_lbl_username);

                //check_Location_control_isOut(Login_Lbl);

            }
            else
            {
                //Login_Lbl.Visible = false;
                //Login_Lbl.Visible = false;
                card.cardsItems.login.Enable = false;
                //loc_login_title.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_username_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.login.title_text = txt_lbl_username.Text;
            txt_AddresItem.Text = card.cardsItems.login.title_text;

        }

        private void lbl_Password_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_Password);
            Set_Proprties_For_Item(TXT_Password, card.cardsItems.Password);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Password, card.cardsItems.Password, txt_lbl_password);

            saveToClass = true;

        }

        private void txt_lbl_password_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Password.title_text = txt_lbl_password.Text;
            txt_AddresItem.Text = card.cardsItems.Password.title_text;

        }

        private void Password_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Password_Chbox.Checked == true)
            {
                card.cardsItems.Password.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Password.title_text;
                txt_lbl_password.Text = card.cardsItems.Password.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_Password);
                Set_Proprties_For_Item(TXT_Password, card.cardsItems.Password);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Password, card.cardsItems.Password, txt_lbl_password);

            }
            else
            {
                card.cardsItems.Password.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void lbl_time_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_time);
            Set_Proprties_For_Item(TXT_Uptime, card.cardsItems.Time);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Uptime, card.cardsItems.Time, txt_lbl_time);

            //check_Show_Unit_Item.Enabled = card.cardsItems.Time.title_show;
            CBox_UniteTime_format.Visible = true;
            CBox_UniteTime_format.SelectedIndex = card.cardsItems.Time.unit_format;

            check_Show_Unit_Item.Enabled = true;
            saveToClass = true;

        }

        private void Time_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Time_Chbox.Checked == true)
            {
                card.cardsItems.Time.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Time.title_text;
                txt_lbl_time.Text = card.cardsItems.Time.title_text;
                DisableBorder_All(lbl_time);
                Set_Proprties_For_Item(TXT_Uptime, card.cardsItems.Time);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Uptime, card.cardsItems.Time, txt_lbl_time);

                check_Show_Unit_Item.Enabled = true;
                CBox_UniteTime_format.Visible = true;
            }
            else
            {
                card.cardsItems.Time.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_time_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Time.title_text = txt_lbl_time.Text;
            txt_AddresItem.Text = card.cardsItems.Time.title_text;

        }

        private void lbl_validay_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_validay);
            Set_Proprties_For_Item(TXT_Validate, card.cardsItems.Validity);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Validate, card.cardsItems.Validity, txt_lbl_validy);

            CBox_UniteValidaty_format.Visible = true;
            CBox_UniteValidaty_format.SelectedIndex = card.cardsItems.Validity.unit_format;
            //check_Show_Unit_Item.Enabled = card.cardsItems.Validity.title_show;
            check_Show_Unit_Item.Enabled = true;

            saveToClass = true;

        }

        private void Vaildate_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Vaildate_Chbox.Checked == true)
            {
                card.cardsItems.Validity.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Validity.title_text;
                txt_lbl_validy.Text = card.cardsItems.Validity.title_text;

                DisableBorder_All(lbl_validay);
                Set_Proprties_For_Item(TXT_Validate, card.cardsItems.Validity);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Validate, card.cardsItems.Validity, txt_lbl_validy);

                check_Show_Unit_Item.Enabled = true;
                CBox_UniteValidaty_format.Visible = true;

            }
            else
            {
                card.cardsItems.Validity.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_validy_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Validity.title_text = txt_lbl_validy.Text;
            txt_AddresItem.Text = card.cardsItems.Validity.title_text;

        }

        private void lbl_transfer_MouseDoubleClick(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_transfer);
            Set_Proprties_For_Item(TXT_SizeTransfer, card.cardsItems.Size);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SizeTransfer, card.cardsItems.Size, txt_lbl_download);
            CBox_UniteTransfer_format.SelectedIndex = card.cardsItems.Size.unit_format;
            CBox_UniteTransfer_format.Visible = true;
            check_Show_Unit_Item.Enabled = true;

            saveToClass = true;

        }

        private void txt_lbl_download_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Size.title_text = txt_lbl_download.Text;
            txt_AddresItem.Text = card.cardsItems.Size.title_text;

        }

        private void SizeTransfer_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (SizeTransfer_Chbox.Checked == true)
            {
                card.cardsItems.Size.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Size.title_text;
                txt_lbl_download.Text = card.cardsItems.Size.title_text;
                DisableBorder_All(txt_lbl_download);
                Set_Proprties_For_Item(TXT_SizeTransfer, card.cardsItems.Size);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SizeTransfer, card.cardsItems.Size, txt_lbl_download);

                CBox_UniteTransfer_format.Visible = true;
                check_Show_Unit_Item.Enabled = true;


            }
            else
            {
                card.cardsItems.Size.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void lbl_price_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_price);
            Set_Proprties_For_Item(TXT_Price, card.cardsItems.Price);
            check_Show_Unit_Item.Enabled = true;
            CBox_Curncey.Visible = true;
            //check_Show_Unit_Item.Enabled = card.cardsItems.Price.title_show; ;
            saveToClass = true;

        }

        private void Price_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Price_Chbox.Checked == true)
            {
                card.cardsItems.Price.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Price.title_text;
                txt_lbl_price.Text = card.cardsItems.Price.title_text;
                check_Show_Unit_Item.Enabled = true;
                DisableBorder_All(lbl_price);
                Set_Proprties_For_Item(TXT_Price, card.cardsItems.Price);
                CBox_Curncey.Visible = true;
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Price, card.cardsItems.Price, txt_lbl_price);

            }
            else
            {
                card.cardsItems.Price.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_price_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            
                card.cardsItems.Price.title_text = txt_lbl_price.Text;
                txt_AddresItem.Text = card.cardsItems.Price.title_text;
             
        }

        private void lbl_sp_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;

            DisableBorder_All(lbl_sp);
            Set_Proprties_For_Item(TXT_SP, card.cardsItems.SP);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SP, card.cardsItems.SP, txt_lbl_SP);

            CBox_SellingPoint.Visible = true;
            //CBox_SellingPoint.SelectedIndex = card.cardsItems.SP.unit_format;
            CBox_SellingPoint.SelectedIndex = Convert.ToInt32(card.cardsItems.SP.Show_ByNumber_OR_Name);


            //check_Show_Unit_Item.Enabled = true;
            saveToClass = true;

        }

        private void SP_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (SP_Chbox.Checked == true)
            {
                card.cardsItems.SP.Enable = true;
                txt_AddresItem.Text = card.cardsItems.SP.title_text;
                txt_lbl_SP.Text = card.cardsItems.SP.title_text;
                DisableBorder_All(lbl_sp);
                Set_Proprties_For_Item(TXT_SP, card.cardsItems.SP);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SP, card.cardsItems.SP, txt_lbl_SP);

                CBox_SellingPoint.Visible = true;
                check_Show_Unit_Item.Enabled = true;

                CBox_SellingPoint.SelectedIndex = Convert.ToInt32( card.cardsItems.SP.Show_ByNumber_OR_Name);

            }
            else
            {
                card.cardsItems.SP.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_SP_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.SP.title_text = txt_lbl_SP.Text;
            txt_AddresItem.Text = card.cardsItems.SP.title_text;

        }

        private void lbl_sn_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_sn);
            Set_Proprties_For_Item(TXT_SQ, card.cardsItems.SN);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SQ, card.cardsItems.SN, txt_lbl_SN);

            saveToClass = true;

        }

        private void SN_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (SN_Chbox.Checked == true)
            {
                card.cardsItems.SN.Enable = true;
                txt_AddresItem.Text = card.cardsItems.SN.title_text;
                txt_lbl_SN.Text = card.cardsItems.SN.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_sn);
                Set_Proprties_For_Item(TXT_SQ, card.cardsItems.SN);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SQ, card.cardsItems.SN, txt_lbl_SN);

            }
            else
            {
                card.cardsItems.SN.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_SN_onTextChanged(object sender, EventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.SN.title_text = txt_lbl_SN.Text;
            txt_AddresItem.Text = card.cardsItems.SN.title_text;

        }

        private void lbl_dataPrint_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_dataPrint);
            Set_Proprties_For_Item(TXT_DatePrint, card.cardsItems.Date_Print);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_DatePrint, card.cardsItems.Date_Print, txt_lbl_DatePrint);

            //CBox_Date_print_format.Visible = true;
            CBox_Date_print_format.Text = card.cardsItems.Date_Print.format;
            CBox_Date_print_format.Visible = true;
            check_Show_Unit_Item.Enabled = true;

            saveToClass = true;

        }

        private void Date_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Date_Print_Chbox.Checked == true)
            {
                card.cardsItems.Date_Print.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Date_Print.title_text;
                txt_lbl_DatePrint.Text = card.cardsItems.Date_Print.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_dataPrint);
                Set_Proprties_For_Item(TXT_DatePrint, card.cardsItems.Date_Print);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_DatePrint, card.cardsItems.Date_Print, txt_lbl_DatePrint);

                CBox_UniteTime_format.Visible = true;

            }
            else
            {
                card.cardsItems.Date_Print.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_DatePrint_onTextChanged(object sender, EventArgs e)
        {

            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Date_Print.title_text = txt_lbl_DatePrint.Text;
            txt_AddresItem.Text = card.cardsItems.Date_Print.title_text;

        }

        private void lbl_numberPrint_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_numberPrint);
            Set_Proprties_For_Item(txt_NumberCard, card.cardsItems.Number_Print);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_NumberPrint, card.cardsItems.Number_Print, txt_lbl_NumberPrint);

            saveToClass = true;

        }

        private void Number_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Number_Print_Chbox.Checked == true)
            {
                card.cardsItems.Number_Print.Enable = true;
                txt_AddresItem.Text = card.cardsItems.Number_Print.title_text;
                txt_lbl_NumberPrint.Text = card.cardsItems.Number_Print.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_numberPrint);
                Set_Proprties_For_Item(TXT_NumberPrint, card.cardsItems.Number_Print);
                //Change_Font_Size_Bold_Color_Control_In_Imag(txt_NumberCard, card.cardsItems.Number_Print, txt_lbl_NumberPrint);

            }
            else
            {
                card.cardsItems.Number_Print.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void txt_lbl_NumberPrint_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.Number_Print.title_text = txt_lbl_NumberPrint.Text;
            txt_AddresItem.Text = card.cardsItems.Number_Print.title_text;

        }

        private void lbl_info3_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_info3);
            Set_Proprties_For_Item(TXT_info3, card.cardsItems.info3);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info3, card.cardsItems.info3, null);

            saveToClass = true;

        }

        private void Info3_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Info3_Chbox.Checked == true)
            {
                card.cardsItems.info3.Enable = true;
                txt_AddresItem.Text = card.cardsItems.info3.title_text;
                TXT_info3.Text = card.cardsItems.info3.title_text;

                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_info3);
                Set_Proprties_For_Item(TXT_info3, card.cardsItems.info3);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info3, card.cardsItems.info3, null);

            }
            else
            {
                card.cardsItems.info3.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void TXT_info3_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.info3.title_text = TXT_info3.Text;
            txt_AddresItem.Text = TXT_info3.Text;

        }

        private void lbl_info4_MouseDown(object sender, MouseEventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown)
                return;
            DisableBorder_All(lbl_info4);
            Set_Proprties_For_Item(TXT_info4, card.cardsItems.info4);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info4, card.cardsItems.info4, null);

            saveToClass = true;

        }

        private void Info4_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.info4.title_text = TXT_info4.Text;
            txt_AddresItem.Text = TXT_info4.Text;


        }

        private void TXT_info4_onTextChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Info4_Chbox.Checked == true)
            {
                card.cardsItems.info1.Enable = true;
                txt_AddresItem.Text = card.cardsItems.info4.title_text;
                TXT_info4.Text = card.cardsItems.info4.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_info4);
                Set_Proprties_For_Item(TXT_info4, card.cardsItems.info4);
                //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info4, card.cardsItems.info4, null);

            }
            else
            {
                card.cardsItems.info4.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void lbl_info5_MouseDown(object sender, MouseEventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            DisableBorder_All(lbl_info5);
            Set_Proprties_For_Item(TXT_info5, card.cardsItems.info5);
            //Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info5, card.cardsItems.info5, null);

            saveToClass = true;

        }

        private void Info5_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            saveToClass = false;
            if (selectTemplateFromDrowpDown || firstLoad)
                return;

            if (Info5_Chbox.Checked == true)
            {
                card.cardsItems.info5.Enable = true;
                txt_AddresItem.Text = card.cardsItems.info5.title_text;
                TXT_info5.Text = card.cardsItems.info5.title_text;
                check_Show_Unit_Item.Enabled = false;
                DisableBorder_All(lbl_info5);
                Set_Proprties_For_Item(TXT_info5, card.cardsItems.info5);
            }
            else
            {
                card.cardsItems.info5.Enable = false;
                return;
            }
            saveToClass = true;

        }

        private void TXT_info5_onTextChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad == true)
                return;
            card.cardsItems.info5.title_text = TXT_info5.Text;
            txt_AddresItem.Text = TXT_info5.Text;

        }

        private void CBox_Profile_HotspotLocal_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad)
                return;
            if (card == null)
                return;
            try
            {
                card.setingCard.proile_HS_Local_link = CBox_Profile_HotspotLocal.SelectedValue.ToString();
            }
            catch { }
        }

        private void txt_Padding_Cell_ValueChanged(object sender, EventArgs e)
        {
            if (selectTemplateFromDrowpDown || firstLoad || saveToClass == false)
                return;
            card.setingCard.Padding_Cell = (float)(txt_Padding_Cell.Value);
        }
    }
}

