﻿using Org.BouncyCastle.Asn1.Pkcs;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager.Maintenance
{
    public partial class Form_Process_UserManager : RJChildForm
    {
        public Form_Process_UserManager()
        {
            InitializeComponent();
            //if (UIAppearance.DGV_RTL == false)
            //{
            //    dgv.RightToLeft = RightToLeft.No;
            //}

            Set_Font();
            Smart_DA=new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();


        }

        private void Set_Font()
        {
            rjLabel1.Font = rjLabel8.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9.75f, FontStyle.Regular);

            lnk_Remove_Logs.Font =
            lnk_Rebuld_DB.Font =
            lnk_Rebuld_Logs.Font =
            rjLabel5.Font =
            lnk_Reboot.Font =
            lnk_Remove_FinshCards.Font =
            Program.GetCustomFont(Resources.DroidKufi_Bold, 11, FontStyle.Bold);

            rjLabel9.Font =
            rjLabel10.Font =
            lbl_title_last_SaveDB.Font =
            lbl_title_last_RebuldDB.Font =
            Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

            rjLabel7.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);

            lbl_path_DB.Font = lbl_Last_Rebuld_DB.Font = lbl_Last_Save_DB.Font = lbl_Size_DB.Font = new Font("Verdana", 10);
            lbl_path_DB.ForeColor = lbl_Last_Rebuld_DB.ForeColor = lbl_Last_Save_DB.ForeColor = lbl_Size_DB.ForeColor = utils.Dgv_DarkColor;
                
        }

        [Obsolete]
        private void GetData()
        {

            ThreadStart theprogress = new ThreadStart(() => Get_info_Db());
            Thread startprogress = new Thread(theprogress);
            startprogress.Name = "Update ProgressBar";
            startprogress.Start();
        }

        [Obsolete]
        private void Get_info_Db()
        {
            Mk_DataAccess mk_Data = new Mk_DataAccess();
            UserManager_Database_Info db = mk_Data.Get_Info_UserManager_Database();

            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    lbl_path_DB.Text = db.db_path;
                    
                    lbl_Size_DB.Text = ConvertSize_Get_En(db.size.ToString());
                    if(Global_Variable.Mk_resources.version>=7)
                        lbl_Size_DB.Text = utils.ConvertSize_Get_En(db.size.ToString());

                    lbl_Last_Save_DB.Text = db.last_save;
                    lbl_Last_Rebuld_DB.Text = db.last_rebuild;

                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        lbl_title_last_SaveDB.Text = "مساحة القرص الفارغة";
                        lbl_Last_Save_DB.Text = utils.ConvertSize_Get_En(db.freediskspace);

                        lbl_title_last_RebuldDB.Visible = false;
                        lbl_Last_Rebuld_DB.Visible = false;
                    }

                });
            }
            catch { }
        }
        private  string ConvertSize_Get_En(string size)
        {
            //string size = "";
            try
            {
                if (size != "" && size != "0")
                {
                    string[] sizes = { " KB", " MB", " GB" };
                    double len = Convert.ToDouble(size);
                    int order = 0;
                    while (len >= 1024 && order + 1 < sizes.Length)
                    {
                        order++;
                        len = len / 1024;
                    }
                    size = String.Format("{0:0.##}{1}", len, sizes[order]);
                }
                else
                    size = "0";
            }
            catch { }
            return size;
        }
        private void Form_Process_UserManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            GetData();
        }

        private void Form_Process_UserManager_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjPanel1.Refresh();

        }

        [Obsolete]
        private void lnk_Remove_FinshCards_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Pyment_UserManager == null || Global_Variable.Source_Pyment_UserManager.Count < 0 || Global_Variable.Source_Session_UserManager == null || Global_Variable.Source_Session_UserManager.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب المبيعات والجلسات حتى يتم مزامنتها في قاعدة البيانات المحلية(الارشيف) قد لا تستطيع معرفة التقارير عنها بعد حذفها \n  يفضل ان تقوم بجلب المبيعات والجلسات من اجل ان يتم حفظها في قاعدة البيانات المحلية هل تريد العودة للقائمة السابقة", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
            }
            //RJMessageBox.Show("سوف يتم حذف الكروت المنتهيةالصلاحية مع جلساتهن من السيرفر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinshCards))
                    fRM.ShowDialog();

            }
        }

        Smart_DataAccess Smart_DA;
        Sql_DataAccess Local_DA;

        [Obsolete]
        private void RemoveFinshCards()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = "{/tool user-manager user remove [find where !actual-profile && uptime-used>0]; }\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
                RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات او عيد تشغيل البرنامج");
            }

            else
            {
                List<UmUser> users = Local_DA.Load<UmUser>("select * from Umuser where Status=2 and DeleteFromServer=0");

                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmUser> ResUsers = mk_DataAccess.Delete_UserManager_ByID(users.ToHashSet());

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                    //isProcessRun = false;

                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < users.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض العناصر");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Local_DA.Set_Delet_fromServer("UmUser", users.ToHashSet());
                    //loadData();

                }

                //isProcessRun = false;
                //is_Delete_FromArchive = false;



            }
        }

        [Obsolete]
        private void lnk_Remove_Logs_Click(object sender, EventArgs e)
        {
            //RJMessageBox.Show("سوف يتم حذف الكروت المنتهيةالصلاحية مع جلساتهن من السيرفر ");
            DialogResult result2 = RJMessageBox.Show(" هل انت متاكد من حذف تسجيلات النظام ( log ) من النظام  ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(Remove_Logs_UserManager))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        private void Remove_Logs_UserManager()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = "{/tool user-manager database clear-log;}\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
            }
            RJMessageBox.Show("تمت العملية");
        }

        [Obsolete]
        private void lnk_Rebuld_Logs_Click(object sender, EventArgs e)
        {
            DialogResult result2 = RJMessageBox.Show(" هل انت متاكد من اعادة بناء  تسجيلات النظام ( log )   ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(Rebuild_Logs_UserManager))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        private void Rebuild_Logs_UserManager()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = "{ /tool user-manager database rebuild-log ;}\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
            }
            RJMessageBox.Show("تمت العملية");
        }

        [Obsolete]
        private void lnk_Rebuld_DB_Click(object sender, EventArgs e)
        {
            DialogResult result2 = RJMessageBox.Show(" هل انت متاكد من اعادة بناء  قاعدة البيانات ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            RJMessageBox.Show("من الضروري اعادة تشغيل الروتر بعد انتهاء اعادة بناء قاعدة البيانات");
            if (result2 == DialogResult.Yes)
            {

                using (Form_WaitForm fRM = new Form_WaitForm(Rebuild_DB_UserManager))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        private void Rebuild_DB_UserManager()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = "{ /tool user-manager database rebuild ;}\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
            }
            RJMessageBox.Show("تمت العملية");
        }

        [Obsolete]
        private void lnk_Reboot_Click(object sender, EventArgs e)
        {
            DialogResult result2 = RJMessageBox.Show(" هل انت متاكد من اعادة تشغيل الروتر ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {

                //using (Form_WaitForm fRM = new Form_WaitForm(Rebuild_DB_UserManager))
                //    fRM.ShowDialog();

                string script = "{ /system reboot ;}\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
                //RJMessageBox.Show("قم باعادة تشغيل البرنامج ليتم تحديث البيانات الجديده من الروتر");
                DialogResult result3 = RJMessageBox.Show("قم باعادة تشغيل البرنامج ليتم تحديث البيانات الجديده من الروتر", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result3 == DialogResult.Yes)
                {
                    Application.Restart();
                    Environment.Exit(0);
                }
            }
        }

        [Obsolete]
        private void btn_Run_All_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Pyment_UserManager == null || Global_Variable.Source_Pyment_UserManager.Count < 0 || Global_Variable.Source_Session_UserManager == null || Global_Variable.Source_Session_UserManager.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب المبيعات والجلسات حتى يتم مزامنتها في قاعدة البيانات المحلية(الارشيف) قد لا تستطيع معرفة التقارير عنها بعد حذفها \n  يفضل ان تقوم بجلب المبيعات والجلسات من اجل ان يتم حفظها في قاعدة البيانات المحلية هل تريد العودة للقائمة السابقة", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
            }
            DialogResult result2 = RJMessageBox.Show(" هل انت متاكد من تنفيذ امر صيانه اليوزمنجر - سوف يتم اعادة تشغيل الروتر بعد انهاء الصيانة ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            //RJMessageBox.Show("من الضروري اعادة تشغيل الروتر بعد انتهاء اعادة بناء قاعدة البيانات");
            if (result2 == DialogResult.Yes)
            {
                using (Form_WaitForm fRM = new Form_WaitForm(Proccess_All))
                    fRM.ShowDialog();
            }
        }

        [Obsolete]
        private void Proccess_All()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = @"{/tool user-manager user remove [find where !actual-profile and uptime-used>0s];
/tool user-manager log remove [find ] ;
/tool user-manager database rebuild;
/delay 2s;
/tool user-manager database rebuild-log;
/delay 5s;
/system reboot;

};";
                   
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
            }
            RJMessageBox.Show("تمت العملية");
        }

        private void rjLabel5_Click(object sender, EventArgs e)
        {

        }
    }
}

