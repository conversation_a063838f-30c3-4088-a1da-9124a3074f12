﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Models
{
    public class SalesOrder
    {
        /// <summary>
        /// This class was created for testing purposes only,
        /// the data may be inaccurate and meaningless.
        /// </summary>
        /// 
       
        private string itemType;
        private DateTime orderDate;
        private string orderId;
        private int unitSold;
        private double unitPrice;
        private double totalRevenue;
        
        public int Number { get; set; }

        [DisplayName("Product")]
        public string ItemType
        {
            get { return itemType; }
            set { itemType = value; }
        }

        [DisplayName("Order date")]
        public DateTime OrderDate
        {
            get { return orderDate; }
            set { orderDate = value; }
        }

        [DisplayName("Order ID")]
        public string OrderId
        {
            get { return orderId; }
            set { orderId = value; }
        }

        [DisplayName("Unit sold")]
        public int UnitSold
        {
            get { return unitSold; }
            set { unitSold = value; }
        }

        [DisplayName("Unit price")]       
        public double UnitPrice
        {
            get { return unitPrice; }
            set { unitPrice = value; }
        }

        [DisplayName("Total revenue")]
        public double TotalRevenue
        {
            get { return totalRevenue; }
            set { totalRevenue = value; }
        }

        public List<SalesOrder> GetSalesList()
        {
            var list = new List<SalesOrder>();
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 01, 01), orderId = "329530894", unitSold = 44, unitPrice = 154.06, totalRevenue = 6730.88 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 01, 02), orderId = "515616118", unitSold = 44, unitPrice = 47.45, totalRevenue = 2073.09 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 01, 03), orderId = "386600577", unitSold = 159, unitPrice = 437.2, totalRevenue = 19101.27 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 01, 04), orderId = "256994950", unitSold = 44, unitPrice = 668.27, totalRevenue = 29196.72 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 01, 05), orderId = "967328870", unitSold = 44, unitPrice = 81.73, totalRevenue = 3570.78 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 01, 06), orderId = "480863702", unitSold = 44, unitPrice = 421.89, totalRevenue = 18432.37 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 01, 07), orderId = "131482589", unitSold = 44, unitPrice = 154.06, totalRevenue = 6730.88 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 01, 08), orderId = "546986377", unitSold = 44, unitPrice = 152.58, totalRevenue = 6666.22 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 01, 09), orderId = "812613904", unitSold = 94, unitPrice = 47.45, totalRevenue = 4444.64 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 01, 10), orderId = "936042296", unitSold = 46, unitPrice = 47.45, totalRevenue = 2168.94 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 01, 11), orderId = "403961122", unitSold = 99, unitPrice = 437.2, totalRevenue = 43405.22 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 01, 12), orderId = "521445310", unitSold = 61, unitPrice = 668.27, totalRevenue = 40831.30 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 01, 13), orderId = "921992242", unitSold = 68, unitPrice = 81.73, totalRevenue = 5596.87 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 01, 13), orderId = "607757937", unitSold = 79, unitPrice = 9.33, totalRevenue = 740.24 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 01, 13), orderId = "607521903", unitSold = 81, unitPrice = 421.89, totalRevenue = 34114.03 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 01, 13), orderId = "731972110", unitSold = 57, unitPrice = 651.21, totalRevenue = 36910.58 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 01, 14), orderId = "220003211", unitSold = 27, unitPrice = 152.58, totalRevenue = 4110.51 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 01, 15), orderId = "494525372", unitSold = 99, unitPrice = 668.27, totalRevenue = 66172.10 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 01, 16), orderId = "157542073", unitSold = 91, unitPrice = 668.27, totalRevenue = 60511.85 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 01, 17), orderId = "670613467", unitSold = 83, unitPrice = 47.45, totalRevenue = 3929.81 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 01, 18), orderId = "769205892", unitSold = 40, unitPrice = 9.33, totalRevenue = 370.59 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 01, 18), orderId = "600124156", unitSold = 89, unitPrice = 152.58, totalRevenue = 13623.87 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 01, 18), orderId = "531405103", unitSold = 34, unitPrice = 154.06, totalRevenue = 5290.42 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 01, 19), orderId = "861462724", unitSold = 48, unitPrice = 9.33, totalRevenue = 449.52 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 01, 20), orderId = "732211148", unitSold = 64, unitPrice = 152.58, totalRevenue = 9772.75 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 01, 21), orderId = "320368897", unitSold = 31, unitPrice = 651.21, totalRevenue = 20389.39 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 01, 22), orderId = "160299813", unitSold = 51, unitPrice = 205.7, totalRevenue = 10556.52 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 01, 23), orderId = "393693625", unitSold = 15, unitPrice = 47.45, totalRevenue = 734.05 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 01, 23), orderId = "989928519", unitSold = 7, unitPrice = 651.21, totalRevenue = 4571.49 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 01, 23), orderId = "126767909", unitSold = 23, unitPrice = 109.28, totalRevenue = 2509.07 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 01, 24), orderId = "221975171", unitSold = 62, unitPrice = 205.7, totalRevenue = 12837.74 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 01, 25), orderId = "161442649", unitSold = 33, unitPrice = 205.7, totalRevenue = 6833.35 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 01, 26), orderId = "907513463", unitSold = 23, unitPrice = 109.28, totalRevenue = 2465.36 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 01, 27), orderId = "611816871", unitSold = 91, unitPrice = 651.21, totalRevenue = 59019.16 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 01, 28), orderId = "559352862", unitSold = 380, unitPrice = 81.73, totalRevenue = 3103.29 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 01, 29), orderId = "151839911", unitSold = 17, unitPrice = 437.2, totalRevenue = 7253.15 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 01, 30), orderId = "852176702", unitSold = 69, unitPrice = 668.27, totalRevenue = 45963.61 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 01, 31), orderId = "534085166", unitSold = 65, unitPrice = 152.58, totalRevenue = 9954.32 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 01), orderId = "942700612", unitSold = 49, unitPrice = 47.45, totalRevenue = 2332.17 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 02, 02), orderId = "375630986", unitSold = 64, unitPrice = 255.28, totalRevenue = 16366.00 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 02, 02), orderId = "211114585", unitSold = 24, unitPrice = 651.21, totalRevenue = 15316.46 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 02, 02), orderId = "624295365", unitSold = 17, unitPrice = 9.33, totalRevenue = 161.13 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 02, 02), orderId = "181045520", unitSold = 42, unitPrice = 205.7, totalRevenue = 8736.08 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 02, 02), orderId = "409873998", unitSold = 97, unitPrice = 437.2, totalRevenue = 42316.59 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 02, 03), orderId = "109966123", unitSold = 3, unitPrice = 9.33, totalRevenue = 25.56 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 04), orderId = "411448562", unitSold = 56, unitPrice = 154.06, totalRevenue = 8670.50 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 02, 05), orderId = "453089320", unitSold = 81, unitPrice = 437.2, totalRevenue = 35207.72 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 06), orderId = "325412309", unitSold = 56, unitPrice = 152.58, totalRevenue = 8526.17 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 02, 07), orderId = "901180875", unitSold = 83, unitPrice = 205.7, totalRevenue = 17077.21 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 07), orderId = "958840644", unitSold = 11, unitPrice = 152.58, totalRevenue = 1692.11 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 07), orderId = "985092818", unitSold = 97, unitPrice = 47.45, totalRevenue = 4607.87 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 02, 08), orderId = "304750287", unitSold = 120, unitPrice = 437.2, totalRevenue = 5408.16 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 09), orderId = "620441138", unitSold = 12, unitPrice = 154.06, totalRevenue = 1810.21 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 10), orderId = "909053695", unitSold = 80, unitPrice = 152.58, totalRevenue = 12273.54 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 02, 11), orderId = "706399714", unitSold = 82, unitPrice = 109.28, totalRevenue = 8991.56 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 12), orderId = "363276517", unitSold = 4, unitPrice = 152.58, totalRevenue = 685.08 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 12), orderId = "285662829", unitSold = 28, unitPrice = 81.73, totalRevenue = 2316.23 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 12), orderId = "496897733", unitSold = 19, unitPrice = 81.73, totalRevenue = 1582.29 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 02, 13), orderId = "953293836", unitSold = 97, unitPrice = 651.21, totalRevenue = 63069.69 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 02, 14), orderId = "944635236", unitSold = 74, unitPrice = 651.21, totalRevenue = 48274.20 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 15), orderId = "977499377", unitSold = 26, unitPrice = 152.58, totalRevenue = 4032.69 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 02, 16), orderId = "448685348", unitSold = 48, unitPrice = 109.28, totalRevenue = 5267.30 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 02, 17), orderId = "450849997", unitSold = 54, unitPrice = 109.28, totalRevenue = 5888.01 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 18), orderId = "167787253", unitSold = 8, unitPrice = 154.06, totalRevenue = 1281.78 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 02, 18), orderId = "817824685", unitSold = 14, unitPrice = 255.28, totalRevenue = 3453.94 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 02, 18), orderId = "551725089", unitSold = 66, unitPrice = 421.89, totalRevenue = 27713.95 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 02, 18), orderId = "464626681", unitSold = 22, unitPrice = 668.27, totalRevenue = 14802.18 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 19), orderId = "551057326", unitSold = 90, unitPrice = 81.73, totalRevenue = 7325.46 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 20), orderId = "492689454", unitSold = 66, unitPrice = 81.73, totalRevenue = 5404.80 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 21), orderId = "976871955", unitSold = 70, unitPrice = 154.06, totalRevenue = 10745.69 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 02, 22), orderId = "207580077", unitSold = 64, unitPrice = 421.89, totalRevenue = 27055.81 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 22), orderId = "147047555", unitSold = 35, unitPrice = 81.73, totalRevenue = 2855.65 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 22), orderId = "770714795", unitSold = 5, unitPrice = 152.58, totalRevenue = 747.64 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 22), orderId = "531693494", unitSold = 88, unitPrice = 47.45, totalRevenue = 4163.74 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 23), orderId = "211201274", unitSold = 80, unitPrice = 154.06, totalRevenue = 12332.50 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 24), orderId = "363832271", unitSold = 49, unitPrice = 81.73, totalRevenue = 4012.13 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 25), orderId = "496941077", unitSold = 48, unitPrice = 81.73, totalRevenue = 3892.80 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 02, 25), orderId = "314004981", unitSold = 99, unitPrice = 9.33, totalRevenue = 924.32 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 02, 25), orderId = "658348691", unitSold = 89, unitPrice = 9.33, totalRevenue = 826.82 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 02, 26), orderId = "178377473", unitSold = 47, unitPrice = 437.2, totalRevenue = 20605.24 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 02, 26), orderId = "489148938", unitSold = 89, unitPrice = 421.89, totalRevenue = 37531.33 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 02, 26), orderId = "251466166", unitSold = 33, unitPrice = 255.28, totalRevenue = 8378.29 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 02, 27), orderId = "717110955", unitSold = 79, unitPrice = 668.27, totalRevenue = 52940.35 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 02, 27), orderId = "238714301", unitSold = 97, unitPrice = 109.28, totalRevenue = 10623.11 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 27), orderId = "683184659", unitSold = 84, unitPrice = 47.45, totalRevenue = 3974.89 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 27), orderId = "248121345", unitSold = 35, unitPrice = 154.06, totalRevenue = 5353.59 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 02, 27), orderId = "283504188", unitSold = 40, unitPrice = 152.58, totalRevenue = 6170.34 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 02, 27), orderId = "280749452", unitSold = 89, unitPrice = 255.28, totalRevenue = 22607.60 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 02, 28), orderId = "950427091", unitSold = 14, unitPrice = 651.21, totalRevenue = 8804.36 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 02, 28), orderId = "880999934", unitSold = 63, unitPrice = 109.28, totalRevenue = 6898.85 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 02, 28), orderId = "778708636", unitSold = 60, unitPrice = 81.73, totalRevenue = 4940.58 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 02, 28), orderId = "651621711", unitSold = 82, unitPrice = 437.2, totalRevenue = 35850.40 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 02, 29), orderId = "366630351", unitSold = 29, unitPrice = 651.21, totalRevenue = 19034.87 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 29), orderId = "262056386", unitSold = 72, unitPrice = 47.45, totalRevenue = 3398.84 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 02, 29), orderId = "133276879", unitSold = 84, unitPrice = 47.45, totalRevenue = 4007.15 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 02, 29), orderId = "737816321", unitSold = 51, unitPrice = 154.06, totalRevenue = 7857.06 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 01), orderId = "887180173", unitSold = 52, unitPrice = 255.28, totalRevenue = 13231.16 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 01), orderId = "151854932", unitSold = 61, unitPrice = 154.06, totalRevenue = 9403.82 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 01), orderId = "536028802", unitSold = 17, unitPrice = 9.33, totalRevenue = 157.58 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 01), orderId = "807678210", unitSold = 88, unitPrice = 152.58, totalRevenue = 13405.68 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 02), orderId = "272016179", unitSold = 45, unitPrice = 81.73, totalRevenue = 3667.23 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 02), orderId = "331604564", unitSold = 80, unitPrice = 81.73, totalRevenue = 6549.84 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 02), orderId = "164569461", unitSold = 86, unitPrice = 152.58, totalRevenue = 13144.77 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 02), orderId = "694722020", unitSold = 25, unitPrice = 154.06, totalRevenue = 3911.58 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 03), orderId = "494468724", unitSold = 31, unitPrice = 205.7, totalRevenue = 6456.92 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 03), orderId = "423821055", unitSold = 69, unitPrice = 154.06, totalRevenue = 10665.57 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 03), orderId = "494745099", unitSold = 123, unitPrice = 437.2, totalRevenue = 21527.73 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 03), orderId = "166066348", unitSold = 16, unitPrice = 255.28, totalRevenue = 4028.32 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 04), orderId = "674808442", unitSold = 97, unitPrice = 9.33, totalRevenue = 902.12 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 04), orderId = "504270160", unitSold = 36, unitPrice = 205.7, totalRevenue = 7407.26 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 04), orderId = "306889617", unitSold = 43, unitPrice = 255.28, totalRevenue = 11007.67 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 04), orderId = "861601769", unitSold = 60, unitPrice = 205.7, totalRevenue = 12270.01 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 05), orderId = "699368035", unitSold = 74, unitPrice = 109.28, totalRevenue = 8084.53 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 05), orderId = "719362294", unitSold = 41, unitPrice = 81.73, totalRevenue = 3386.89 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 05), orderId = "191256368", unitSold = 59, unitPrice = 109.28, totalRevenue = 6408.18 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 05), orderId = "444604098", unitSold = 71, unitPrice = 255.28, totalRevenue = 18094.25 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 06), orderId = "794969689", unitSold = 81, unitPrice = 421.89, totalRevenue = 33970.58 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 06), orderId = "888248336", unitSold = 13, unitPrice = 81.73, totalRevenue = 1042.87 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 06), orderId = "139540803", unitSold = 21, unitPrice = 421.89, totalRevenue = 8771.09 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 06), orderId = "430081975", unitSold = 97, unitPrice = 9.33, totalRevenue = 902.12 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 07), orderId = "940870702", unitSold = 34, unitPrice = 205.7, totalRevenue = 7002.03 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 07), orderId = "432995069", unitSold = 17, unitPrice = 109.28, totalRevenue = 1877.43 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 07), orderId = "247776305", unitSold = 14, unitPrice = 205.7, totalRevenue = 2818.09 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 07), orderId = "409774005", unitSold = 1, unitPrice = 81.73, totalRevenue = 72.74 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 08), orderId = "783596694", unitSold = 25, unitPrice = 255.28, totalRevenue = 6458.58 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 08), orderId = "267888581", unitSold = 30, unitPrice = 9.33, totalRevenue = 283.54 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 08), orderId = "841492497", unitSold = 52, unitPrice = 421.89, totalRevenue = 21875.00 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 08), orderId = "951380240", unitSold = 34, unitPrice = 154.06, totalRevenue = 5253.45 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 09), orderId = "841138446", unitSold = 4, unitPrice = 651.21, totalRevenue = 2689.50 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 09), orderId = "960905301", unitSold = 21, unitPrice = 9.33, totalRevenue = 194.72 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 09), orderId = "693743550", unitSold = 30, unitPrice = 154.06, totalRevenue = 4603.31 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 09), orderId = "547528827", unitSold = 48, unitPrice = 255.28, totalRevenue = 12258.55 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 10), orderId = "248093020", unitSold = 51, unitPrice = 421.89, totalRevenue = 21486.86 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 03, 10), orderId = "606055057", unitSold = 40, unitPrice = 668.27, totalRevenue = 26824.36 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 10), orderId = "533006703", unitSold = 74, unitPrice = 437.2, totalRevenue = 32278.48 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 10), orderId = "461768949", unitSold = 65, unitPrice = 81.73, totalRevenue = 5351.68 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 11), orderId = "729443109", unitSold = 73, unitPrice = 154.06, totalRevenue = 11217.11 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 11), orderId = "716849601", unitSold = 6, unitPrice = 109.28, totalRevenue = 636.01 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 11), orderId = "464840400", unitSold = 55, unitPrice = 152.58, totalRevenue = 8329.34 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 11), orderId = "867551982", unitSold = 68, unitPrice = 109.28, totalRevenue = 7392.79 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 12), orderId = "462085664", unitSold = 3, unitPrice = 255.28, totalRevenue = 691.81 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 12), orderId = "692566382", unitSold = 46, unitPrice = 9.33, totalRevenue = 432.73 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 12), orderId = "714135205", unitSold = 73, unitPrice = 9.33, totalRevenue = 684.08 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 12), orderId = "221530139", unitSold = 45, unitPrice = 9.33, totalRevenue = 424.14 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 13), orderId = "890695369", unitSold = 54, unitPrice = 47.45, totalRevenue = 2566.10 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 13), orderId = "759279143", unitSold = 64, unitPrice = 651.21, totalRevenue = 41846.75 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 13), orderId = "972678697", unitSold = 61, unitPrice = 421.89, totalRevenue = 25718.41 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 13), orderId = "379710948", unitSold = 38, unitPrice = 81.73, totalRevenue = 3074.68 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 14), orderId = "373335015", unitSold = 70, unitPrice = 255.28, totalRevenue = 17823.65 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 14), orderId = "960085189", unitSold = 94, unitPrice = 47.45, totalRevenue = 4458.88 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 14), orderId = "812295901", unitSold = 53, unitPrice = 651.21, totalRevenue = 34273.18 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 03, 14), orderId = "718781220", unitSold = 220, unitPrice = 668.27, totalRevenue = 14641.80 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 15), orderId = "801590669", unitSold = 73, unitPrice = 109.28, totalRevenue = 8028.80 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 15), orderId = "851299941", unitSold = 74, unitPrice = 109.28, totalRevenue = 8114.04 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 15), orderId = "693159472", unitSold = 1, unitPrice = 109.28, totalRevenue = 159.55 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 15), orderId = "644670712", unitSold = 12, unitPrice = 152.58, totalRevenue = 1899.62 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 16), orderId = "347163522", unitSold = 23, unitPrice = 81.73, totalRevenue = 1843.83 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 16), orderId = "854095017", unitSold = 46, unitPrice = 47.45, totalRevenue = 2158.98 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 16), orderId = "669978749", unitSold = 5, unitPrice = 47.45, totalRevenue = 215.90 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 16), orderId = "103617227", unitSold = 15, unitPrice = 9.33, totalRevenue = 139.48 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 17), orderId = "466970717", unitSold = 59, unitPrice = 109.28, totalRevenue = 6411.46 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 17), orderId = "127468717", unitSold = 97, unitPrice = 81.73, totalRevenue = 7912.28 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 17), orderId = "547143447", unitSold = 8, unitPrice = 651.21, totalRevenue = 4949.20 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 17), orderId = "195833718", unitSold = 4, unitPrice = 255.28, totalRevenue = 1031.33 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 18), orderId = "280571782", unitSold = 63, unitPrice = 651.21, totalRevenue = 40895.99 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 18), orderId = "736967885", unitSold = 40, unitPrice = 437.2, totalRevenue = 17614.79 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 03, 18), orderId = "778490626", unitSold = 150, unitPrice = 668.27, totalRevenue = 10231.21 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 18), orderId = "821587932", unitSold = 50, unitPrice = 9.33, totalRevenue = 464.73 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 19), orderId = "280494105", unitSold = 33, unitPrice = 154.06, totalRevenue = 5074.74 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 19), orderId = "908136594", unitSold = 67, unitPrice = 421.89, totalRevenue = 28072.56 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 19), orderId = "427811324", unitSold = 77, unitPrice = 154.06, totalRevenue = 11913.46 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 03, 19), orderId = "905381858", unitSold = 250, unitPrice = 668.27, totalRevenue = 9796.84 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 20), orderId = "339256370", unitSold = 24, unitPrice = 109.28, totalRevenue = 2572.45 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 20), orderId = "369837844", unitSold = 21, unitPrice = 152.58, totalRevenue = 3190.45 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 20), orderId = "673130881", unitSold = 32, unitPrice = 152.58, totalRevenue = 4945.12 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 20), orderId = "313044536", unitSold = 57, unitPrice = 255.28, totalRevenue = 14522.88 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 21), orderId = "115309941", unitSold = 16, unitPrice = 152.58, totalRevenue = 2485.53 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 21), orderId = "107005393", unitSold = 41, unitPrice = 47.45, totalRevenue = 1959.21 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 21), orderId = "661953580", unitSold = 56, unitPrice = 109.28, totalRevenue = 6151.37 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 21), orderId = "127589738", unitSold = 55, unitPrice = 81.73, totalRevenue = 4490.25 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 22), orderId = "834460818", unitSold = 44, unitPrice = 421.89, totalRevenue = 18373.31 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 22), orderId = "968554103", unitSold = 55, unitPrice = 109.28, totalRevenue = 6050.83 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 22), orderId = "479216182", unitSold = 96, unitPrice = 421.89, totalRevenue = 40383.31 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 03, 22), orderId = "742443025", unitSold = 42, unitPrice = 152.58, totalRevenue = 6477.02 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 23), orderId = "296438443", unitSold = 16, unitPrice = 47.45, totalRevenue = 748.76 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 23), orderId = "186196649", unitSold = 86, unitPrice = 47.45, totalRevenue = 4071.68 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 23), orderId = "534633624", unitSold = 88, unitPrice = 437.2, totalRevenue = 38582.90 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 23), orderId = "681006705", unitSold = 39, unitPrice = 9.33, totalRevenue = 361.26 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 24), orderId = "351855885", unitSold = 8, unitPrice = 81.73, totalRevenue = 678.36 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 03, 24), orderId = "860952031", unitSold = 37, unitPrice = 47.45, totalRevenue = 1752.33 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 24), orderId = "285341823", unitSold = 78, unitPrice = 651.21, totalRevenue = 51061.38 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 24), orderId = "524363124", unitSold = 96, unitPrice = 154.06, totalRevenue = 14721.97 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 25), orderId = "770508801", unitSold = 95, unitPrice = 651.21, totalRevenue = 62073.34 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 25), orderId = "830923306", unitSold = 75, unitPrice = 255.28, totalRevenue = 19212.37 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 25), orderId = "227486360", unitSold = 71, unitPrice = 9.33, totalRevenue = 664.67 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 25), orderId = "562817418", unitSold = 90, unitPrice = 437.2, totalRevenue = 39505.39 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 26), orderId = "185177838", unitSold = 71, unitPrice = 81.73, totalRevenue = 5796.29 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 26), orderId = "500160586", unitSold = 75, unitPrice = 421.89, totalRevenue = 31586.90 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 26), orderId = "881974112", unitSold = 46, unitPrice = 255.28, totalRevenue = 11727.56 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 26), orderId = "393620669", unitSold = 100, unitPrice = 255.28, totalRevenue = 25420.78 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 27), orderId = "835572326", unitSold = 33, unitPrice = 205.7, totalRevenue = 6734.62 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 27), orderId = "867374312", unitSold = 42, unitPrice = 437.2, totalRevenue = 18314.31 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 27), orderId = "392952907", unitSold = 24, unitPrice = 437.2, totalRevenue = 10282.94 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 27), orderId = "177901113", unitSold = 37, unitPrice = 255.28, totalRevenue = 9565.34 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 28), orderId = "162866580", unitSold = 47, unitPrice = 154.06, totalRevenue = 7233.12 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 28), orderId = "277083623", unitSold = 41, unitPrice = 437.2, totalRevenue = 17732.83 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 03, 28), orderId = "365560901", unitSold = 64, unitPrice = 668.27, totalRevenue = 43096.73 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 03, 28), orderId = "435146415", unitSold = 83, unitPrice = 205.7, totalRevenue = 17173.89 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 29), orderId = "590768182", unitSold = 3, unitPrice = 255.28, totalRevenue = 735.21 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 03, 29), orderId = "775278842", unitSold = 11, unitPrice = 421.89, totalRevenue = 4611.26 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 03, 29), orderId = "432037627", unitSold = 84, unitPrice = 437.2, totalRevenue = 36681.08 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 29), orderId = "304832684", unitSold = 56, unitPrice = 154.06, totalRevenue = 8658.17 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 30), orderId = "612573039", unitSold = 28, unitPrice = 109.28, totalRevenue = 3092.62 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 30), orderId = "716202867", unitSold = 92, unitPrice = 9.33, totalRevenue = 858.27 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 03, 30), orderId = "505244338", unitSold = 19, unitPrice = 154.06, totalRevenue = 2899.41 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 03, 30), orderId = "630048596", unitSold = 42, unitPrice = 651.21, totalRevenue = 27585.26 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 03, 31), orderId = "812984693", unitSold = 91, unitPrice = 109.28, totalRevenue = 9935.74 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 03, 31), orderId = "645713555", unitSold = 98, unitPrice = 9.33, totalRevenue = 918.54 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 03, 31), orderId = "254927718", unitSold = 76, unitPrice = 255.28, totalRevenue = 19482.97 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 03, 31), orderId = "147599017", unitSold = 67, unitPrice = 81.73, totalRevenue = 5462.83 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 01), orderId = "885129249", unitSold = 83, unitPrice = 81.73, totalRevenue = 6758.25 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 01), orderId = "708215034", unitSold = 54, unitPrice = 152.58, totalRevenue = 8271.36 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 01), orderId = "476436126", unitSold = 69, unitPrice = 651.21, totalRevenue = 44881.39 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 01), orderId = "183022201", unitSold = 92, unitPrice = 421.89, totalRevenue = 38775.91 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 02), orderId = "980612885", unitSold = 40, unitPrice = 47.45, totalRevenue = 1897.53 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 02), orderId = "777840888", unitSold = 93, unitPrice = 81.73, totalRevenue = 7567.38 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 02), orderId = "733563411", unitSold = 66, unitPrice = 9.33, totalRevenue = 612.89 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 02), orderId = "765955483", unitSold = 21, unitPrice = 9.33, totalRevenue = 196.30 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 03), orderId = "364818465", unitSold = 67, unitPrice = 651.21, totalRevenue = 43930.63 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 03), orderId = "460379779", unitSold = 55, unitPrice = 152.58, totalRevenue = 8333.92 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 03), orderId = "644772422", unitSold = 13, unitPrice = 47.45, totalRevenue = 637.25 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 04, 03), orderId = "773315894", unitSold = 2, unitPrice = 437.2, totalRevenue = 931.24 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 04), orderId = "806298053", unitSold = 4, unitPrice = 47.45, totalRevenue = 173.67 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 04), orderId = "887927329", unitSold = 63, unitPrice = 651.21, totalRevenue = 40915.52 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 04), orderId = "750253188", unitSold = 540, unitPrice = 668.27, totalRevenue = 36393.98 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 04), orderId = "899853074", unitSold = 54, unitPrice = 205.7, totalRevenue = 11058.43 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 05), orderId = "681298100", unitSold = 1, unitPrice = 421.89, totalRevenue = 434.55 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 05), orderId = "234073007", unitSold = 63, unitPrice = 421.89, totalRevenue = 26406.10 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 05), orderId = "957276809", unitSold = 83, unitPrice = 205.7, totalRevenue = 17145.10 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 05), orderId = "773160541", unitSold = 42, unitPrice = 205.7, totalRevenue = 8721.68 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 04, 06), orderId = "573025262", unitSold = 98, unitPrice = 437.2, totalRevenue = 42688.21 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 06), orderId = "221007430", unitSold = 99, unitPrice = 651.21, totalRevenue = 64241.87 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 06), orderId = "579913604", unitSold = 82, unitPrice = 81.73, totalRevenue = 6683.06 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 06), orderId = "594003999", unitSold = 78, unitPrice = 9.33, totalRevenue = 731.29 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 07), orderId = "489784085", unitSold = 69, unitPrice = 152.58, totalRevenue = 10451.73 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 07), orderId = "373048341", unitSold = 21, unitPrice = 47.45, totalRevenue = 1019.70 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 04, 07), orderId = "759504878", unitSold = 32, unitPrice = 437.2, totalRevenue = 14104.07 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 07), orderId = "185941302", unitSold = 30, unitPrice = 154.06, totalRevenue = 4649.53 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 08), orderId = "106753051", unitSold = 95, unitPrice = 47.45, totalRevenue = 4486.40 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 08), orderId = "731640803", unitSold = 76, unitPrice = 9.33, totalRevenue = 711.60 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 08), orderId = "414887797", unitSold = 34, unitPrice = 109.28, totalRevenue = 3687.11 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 08), orderId = "823739278", unitSold = 16, unitPrice = 81.73, totalRevenue = 1317.49 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 09), orderId = "141940200", unitSold = 21, unitPrice = 205.7, totalRevenue = 4348.50 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 09), orderId = "769464671", unitSold = 6, unitPrice = 47.45, totalRevenue = 260.98 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 09), orderId = "687875735", unitSold = 66, unitPrice = 109.28, totalRevenue = 7180.79 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 09), orderId = "257890164", unitSold = 43, unitPrice = 109.28, totalRevenue = 4682.65 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 10), orderId = "312559163", unitSold = 21, unitPrice = 47.45, totalRevenue = 976.05 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 10), orderId = "207395112", unitSold = 17, unitPrice = 421.89, totalRevenue = 7075.10 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 10), orderId = "608414113", unitSold = 21, unitPrice = 47.45, totalRevenue = 1001.67 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 10), orderId = "585823476", unitSold = 70, unitPrice = 255.28, totalRevenue = 17971.71 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 11), orderId = "792729079", unitSold = 50, unitPrice = 255.28, totalRevenue = 12779.32 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 11), orderId = "556738889", unitSold = 3, unitPrice = 255.28, totalRevenue = 673.94 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 11), orderId = "165835034", unitSold = 31, unitPrice = 154.06, totalRevenue = 4817.46 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 11), orderId = "232389438", unitSold = 81, unitPrice = 152.58, totalRevenue = 12288.79 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 12), orderId = "876286971", unitSold = 16, unitPrice = 668.27, totalRevenue = 10979.68 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 12), orderId = "433627212", unitSold = 88, unitPrice = 421.89, totalRevenue = 37054.60 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 12), orderId = "364554107", unitSold = 85, unitPrice = 152.58, totalRevenue = 12993.71 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 12), orderId = "201730287", unitSold = 53, unitPrice = 109.28, totalRevenue = 5824.62 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 13), orderId = "521396386", unitSold = 16, unitPrice = 152.58, totalRevenue = 2490.11 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 13), orderId = "670878255", unitSold = 66, unitPrice = 152.58, totalRevenue = 10129.79 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 13), orderId = "402614009", unitSold = 13, unitPrice = 9.33, totalRevenue = 120.08 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 13), orderId = "936387765", unitSold = 68, unitPrice = 421.89, totalRevenue = 28701.18 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 04, 14), orderId = "888647449", unitSold = 94, unitPrice = 437.2, totalRevenue = 41022.48 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 14), orderId = "626523101", unitSold = 10, unitPrice = 421.89, totalRevenue = 4062.80 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 14), orderId = "686583554", unitSold = 42, unitPrice = 47.45, totalRevenue = 1986.26 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 14), orderId = "469839179", unitSold = 47, unitPrice = 9.33, totalRevenue = 439.35 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 15), orderId = "945717132", unitSold = 76, unitPrice = 255.28, totalRevenue = 19324.70 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 15), orderId = "249237573", unitSold = 38, unitPrice = 255.28, totalRevenue = 9677.66 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 15), orderId = "720786225", unitSold = 55, unitPrice = 152.58, totalRevenue = 8416.31 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 15), orderId = "238414323", unitSold = 10, unitPrice = 255.28, totalRevenue = 2427.71 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 16), orderId = "576264083", unitSold = 82, unitPrice = 255.28, totalRevenue = 20940.62 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 16), orderId = "851652705", unitSold = 18, unitPrice = 109.28, totalRevenue = 1984.52 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 16), orderId = "343699395", unitSold = 71, unitPrice = 651.21, totalRevenue = 46522.44 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 16), orderId = "151868665", unitSold = 2, unitPrice = 154.06, totalRevenue = 258.82 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 17), orderId = "419306790", unitSold = 11, unitPrice = 154.06, totalRevenue = 1620.71 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 17), orderId = "248178422", unitSold = 4, unitPrice = 154.06, totalRevenue = 562.32 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 17), orderId = "276595246", unitSold = 95, unitPrice = 651.21, totalRevenue = 62092.87 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 17), orderId = "442214143", unitSold = 980, unitPrice = 668.27, totalRevenue = 65804.55 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 18), orderId = "668365561", unitSold = 31, unitPrice = 47.45, totalRevenue = 1460.04 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 18), orderId = "235702931", unitSold = 86, unitPrice = 205.7, totalRevenue = 17669.63 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 18), orderId = "526834189", unitSold = 8, unitPrice = 109.28, totalRevenue = 873.15 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 18), orderId = "410452497", unitSold = 9, unitPrice = 47.45, totalRevenue = 412.82 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 19), orderId = "745783555", unitSold = 28, unitPrice = 109.28, totalRevenue = 3040.17 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 19), orderId = "361137616", unitSold = 75, unitPrice = 651.21, totalRevenue = 48847.26 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 19), orderId = "479447925", unitSold = 82, unitPrice = 421.89, totalRevenue = 34384.04 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 19), orderId = "990708720", unitSold = 16, unitPrice = 152.58, totalRevenue = 2412.29 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 20), orderId = "221062791", unitSold = 62, unitPrice = 47.45, totalRevenue = 2964.20 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 20), orderId = "584534299", unitSold = 55, unitPrice = 152.58, totalRevenue = 8459.04 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 20), orderId = "305959212", unitSold = 90, unitPrice = 205.7, totalRevenue = 18482.15 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 20), orderId = "131209647", unitSold = 67, unitPrice = 9.33, totalRevenue = 625.58 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 21), orderId = "959853875", unitSold = 9, unitPrice = 154.06, totalRevenue = 1458.95 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 21), orderId = "369681203", unitSold = 57, unitPrice = 668.27, totalRevenue = 38345.33 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 21), orderId = "627122199", unitSold = 83, unitPrice = 9.33, totalRevenue = 769.73 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 21), orderId = "509914386", unitSold = 39, unitPrice = 152.58, totalRevenue = 5878.91 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 22), orderId = "133336961", unitSold = 62, unitPrice = 81.73, totalRevenue = 5087.69 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 04, 22), orderId = "251529252", unitSold = 20, unitPrice = 9.33, totalRevenue = 181.94 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 22), orderId = "328316819", unitSold = 51, unitPrice = 47.45, totalRevenue = 2419.00 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 22), orderId = "481065833", unitSold = 69, unitPrice = 81.73, totalRevenue = 5659.80 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 23), orderId = "567614495", unitSold = 86, unitPrice = 47.45, totalRevenue = 4079.75 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 23), orderId = "274200570", unitSold = 9, unitPrice = 47.45, totalRevenue = 425.63 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 23), orderId = "918334138", unitSold = 43, unitPrice = 421.89, totalRevenue = 18284.71 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 23), orderId = "290455615", unitSold = 11, unitPrice = 81.73, totalRevenue = 920.28 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 24), orderId = "554154527", unitSold = 55, unitPrice = 651.21, totalRevenue = 35777.48 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 24), orderId = "208630645", unitSold = 73, unitPrice = 109.28, totalRevenue = 7976.35 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 24), orderId = "366653096", unitSold = 97, unitPrice = 154.06, totalRevenue = 14872.95 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 24), orderId = "958912742", unitSold = 24, unitPrice = 255.28, totalRevenue = 6239.04 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 25), orderId = "563915622", unitSold = 40, unitPrice = 205.7, totalRevenue = 8267.08 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 25), orderId = "207922542", unitSold = 78, unitPrice = 421.89, totalRevenue = 32717.57 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 04, 25), orderId = "109724509", unitSold = 88, unitPrice = 81.73, totalRevenue = 7171.81 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 25), orderId = "261765420", unitSold = 11, unitPrice = 205.7, totalRevenue = 2180.42 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 26), orderId = "104845464", unitSold = 50, unitPrice = 205.7, totalRevenue = 10196.55 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 26), orderId = "132082116", unitSold = 49, unitPrice = 651.21, totalRevenue = 31831.14 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 26), orderId = "640942227", unitSold = 79, unitPrice = 205.7, totalRevenue = 16256.47 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 26), orderId = "555142009", unitSold = 77, unitPrice = 154.06, totalRevenue = 11881.11 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 04, 27), orderId = "384013640", unitSold = 70, unitPrice = 154.06, totalRevenue = 10822.72 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 27), orderId = "556480538", unitSold = 38, unitPrice = 109.28, totalRevenue = 4165.75 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 27), orderId = "816204202", unitSold = 18, unitPrice = 668.27, totalRevenue = 12135.78 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 27), orderId = "732568633", unitSold = 22, unitPrice = 47.45, totalRevenue = 1040.58 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 28), orderId = "927666509", unitSold = 60, unitPrice = 668.27, totalRevenue = 40029.37 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 28), orderId = "343239343", unitSold = 10, unitPrice = 651.21, totalRevenue = 6538.15 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 28), orderId = "827539861", unitSold = 63, unitPrice = 421.89, totalRevenue = 26532.66 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 04, 28), orderId = "594540441", unitSold = 66, unitPrice = 205.7, totalRevenue = 13541.23 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 04, 29), orderId = "286210000", unitSold = 48, unitPrice = 152.58, totalRevenue = 7253.65 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 04, 29), orderId = "165380990", unitSold = 880, unitPrice = 668.27, totalRevenue = 58573.87 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 29), orderId = "368547379", unitSold = 2, unitPrice = 421.89, totalRevenue = 902.84 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 04, 29), orderId = "767401731", unitSold = 1, unitPrice = 255.28, totalRevenue = 204.22 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 04, 30), orderId = "643817985", unitSold = 89, unitPrice = 47.45, totalRevenue = 4224.95 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 04, 30), orderId = "147119653", unitSold = 48, unitPrice = 109.28, totalRevenue = 5277.13 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 04, 30), orderId = "286014306", unitSold = 68, unitPrice = 421.89, totalRevenue = 28874.15 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 04, 30), orderId = "673573338", unitSold = 42, unitPrice = 651.21, totalRevenue = 27181.51 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 01), orderId = "768662583", unitSold = 32, unitPrice = 154.06, totalRevenue = 4922.22 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 01), orderId = "905392587", unitSold = 46, unitPrice = 205.7, totalRevenue = 9546.54 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 01), orderId = "499690234", unitSold = 83, unitPrice = 81.73, totalRevenue = 6782.77 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 01), orderId = "404010903", unitSold = 47, unitPrice = 109.28, totalRevenue = 5091.36 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 02), orderId = "414244067", unitSold = 29, unitPrice = 47.45, totalRevenue = 1366.56 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 02), orderId = "542669522", unitSold = 68, unitPrice = 109.28, totalRevenue = 7459.45 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 02), orderId = "356506621", unitSold = 71, unitPrice = 437.2, totalRevenue = 30979.99 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 02), orderId = "845058763", unitSold = 5, unitPrice = 437.2, totalRevenue = 2282.18 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 05, 03), orderId = "946759974", unitSold = 62, unitPrice = 651.21, totalRevenue = 40179.66 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 03), orderId = "554045522", unitSold = 32, unitPrice = 47.45, totalRevenue = 1535.96 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 03), orderId = "355602824", unitSold = 96, unitPrice = 421.89, totalRevenue = 40640.66 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 03), orderId = "614028298", unitSold = 35, unitPrice = 154.06, totalRevenue = 5350.50 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 04), orderId = "247802054", unitSold = 90, unitPrice = 668.27, totalRevenue = 60070.79 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 04), orderId = "808890140", unitSold = 74, unitPrice = 154.06, totalRevenue = 11434.33 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 04), orderId = "632093942", unitSold = 95, unitPrice = 47.45, totalRevenue = 4507.28 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 04), orderId = "645948302", unitSold = 93, unitPrice = 152.58, totalRevenue = 14208.25 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 05), orderId = "531023156", unitSold = 4, unitPrice = 152.58, totalRevenue = 621.00 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 05), orderId = "641146934", unitSold = 85, unitPrice = 154.06, totalRevenue = 13064.29 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 05), orderId = "216552817", unitSold = 16, unitPrice = 668.27, totalRevenue = 10999.72 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 05), orderId = "551371467", unitSold = 19, unitPrice = 109.28, totalRevenue = 2103.64 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 06), orderId = "517799222", unitSold = 72, unitPrice = 205.7, totalRevenue = 14709.61 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 06), orderId = "303691565", unitSold = 79, unitPrice = 152.58, totalRevenue = 12111.80 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 06), orderId = "637448060", unitSold = 39, unitPrice = 81.73, totalRevenue = 3188.29 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 06), orderId = "226077878", unitSold = 3, unitPrice = 81.73, totalRevenue = 263.99 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 07), orderId = "880444610", unitSold = 20, unitPrice = 154.06, totalRevenue = 3050.39 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 07), orderId = "251621949", unitSold = 94, unitPrice = 421.89, totalRevenue = 39577.50 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 07), orderId = "890010011", unitSold = 26, unitPrice = 81.73, totalRevenue = 2120.89 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 07), orderId = "675548303", unitSold = 86, unitPrice = 9.33, totalRevenue = 803.31 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 08), orderId = "531734263", unitSold = 80, unitPrice = 668.27, totalRevenue = 5613.47 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 08), orderId = "645597255", unitSold = 54, unitPrice = 154.06, totalRevenue = 8363.92 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 08), orderId = "795315158", unitSold = 3, unitPrice = 205.7, totalRevenue = 584.19 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 08), orderId = "418973767", unitSold = 25, unitPrice = 81.73, totalRevenue = 2045.70 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 09), orderId = "175033080", unitSold = 50, unitPrice = 152.58, totalRevenue = 7679.35 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 09), orderId = "312117135", unitSold = 13, unitPrice = 109.28, totalRevenue = 1367.09 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 09), orderId = "585931193", unitSold = 89, unitPrice = 154.06, totalRevenue = 13735.99 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 09), orderId = "849630105", unitSold = 33, unitPrice = 437.2, totalRevenue = 14357.65 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 10), orderId = "981086671", unitSold = 42, unitPrice = 47.45, totalRevenue = 1994.32 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 10), orderId = "205300843", unitSold = 19, unitPrice = 437.2, totalRevenue = 8468.56 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 10), orderId = "430390107", unitSold = 9, unitPrice = 205.7, totalRevenue = 1752.56 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 10), orderId = "478919208", unitSold = 17, unitPrice = 154.06, totalRevenue = 2605.15 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 11), orderId = "512019383", unitSold = 68, unitPrice = 81.73, totalRevenue = 5532.30 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 11), orderId = "723608338", unitSold = 6, unitPrice = 668.27, totalRevenue = 4290.29 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 11), orderId = "407681453", unitSold = 9, unitPrice = 109.28, totalRevenue = 935.44 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 11), orderId = "473527753", unitSold = 62, unitPrice = 255.28, totalRevenue = 15929.47 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 12), orderId = "529612958", unitSold = 31, unitPrice = 668.27, totalRevenue = 20703.00 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 12), orderId = "654693591", unitSold = 72, unitPrice = 109.28, totalRevenue = 7908.59 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 12), orderId = "266820847", unitSold = 71, unitPrice = 205.7, totalRevenue = 14549.16 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 12), orderId = "480456435", unitSold = 66, unitPrice = 152.58, totalRevenue = 10056.55 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 13), orderId = "735968816", unitSold = 84, unitPrice = 205.7, totalRevenue = 17241.77 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 13), orderId = "584204280", unitSold = 79, unitPrice = 109.28, totalRevenue = 8615.64 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 13), orderId = "899659097", unitSold = 80, unitPrice = 437.2, totalRevenue = 34862.33 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 13), orderId = "156619393", unitSold = 60, unitPrice = 421.89, totalRevenue = 25372.46 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 14), orderId = "642140424", unitSold = 25, unitPrice = 81.73, totalRevenue = 2023.63 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 14), orderId = "529970014", unitSold = 88, unitPrice = 154.06, totalRevenue = 13494.12 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 14), orderId = "433228528", unitSold = 8, unitPrice = 9.33, totalRevenue = 75.01 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 14), orderId = "775171554", unitSold = 93, unitPrice = 421.89, totalRevenue = 39421.40 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 15), orderId = "859909617", unitSold = 52, unitPrice = 47.45, totalRevenue = 2476.89 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 15), orderId = "832713305", unitSold = 72, unitPrice = 152.58, totalRevenue = 11026.96 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 15), orderId = "703815782", unitSold = 36, unitPrice = 421.89, totalRevenue = 15124.76 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 15), orderId = "193775498", unitSold = 13, unitPrice = 9.33, totalRevenue = 124.18 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 16), orderId = "858611428", unitSold = 11, unitPrice = 152.58, totalRevenue = 1612.77 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 16), orderId = "739474999", unitSold = 59, unitPrice = 437.2, totalRevenue = 25969.68 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 16), orderId = "262770926", unitSold = 86, unitPrice = 109.28, totalRevenue = 9410.10 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 05, 16), orderId = "256158959", unitSold = 20, unitPrice = 651.21, totalRevenue = 12913.49 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 05, 17), orderId = "810871112", unitSold = 36, unitPrice = 651.21, totalRevenue = 23678.00 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 17), orderId = "382537782", unitSold = 333, unitPrice = 668.27, totalRevenue = 1904.57 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 17), orderId = "300476777", unitSold = 66, unitPrice = 255.28, totalRevenue = 16874.01 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 17), orderId = "671898782", unitSold = 86, unitPrice = 668.27, totalRevenue = 57705.11 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 18), orderId = "576455485", unitSold = 48, unitPrice = 668.27, totalRevenue = 32364.32 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 18), orderId = "416386401", unitSold = 68, unitPrice = 154.06, totalRevenue = 10543.87 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 18), orderId = "619670808", unitSold = 32, unitPrice = 81.73, totalRevenue = 2629.25 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 18), orderId = "671939122", unitSold = 6, unitPrice = 255.28, totalRevenue = 1462.75 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 19), orderId = "576654183", unitSold = 36, unitPrice = 668.27, totalRevenue = 24338.39 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 19), orderId = "247857415", unitSold = 54, unitPrice = 437.2, totalRevenue = 23486.38 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 19), orderId = "278910958", unitSold = 48, unitPrice = 668.27, totalRevenue = 32110.37 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 05, 19), orderId = "328856265", unitSold = 47, unitPrice = 651.21, totalRevenue = 30815.26 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 20), orderId = "467986953", unitSold = 66, unitPrice = 47.45, totalRevenue = 3128.85 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 20), orderId = "428392827", unitSold = 980, unitPrice = 668.27, totalRevenue = 65570.65 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 20), orderId = "806662833", unitSold = 83, unitPrice = 421.89, totalRevenue = 35071.72 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 20), orderId = "253407227", unitSold = 77, unitPrice = 437.2, totalRevenue = 33598.82 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 21), orderId = "353061807", unitSold = 77, unitPrice = 152.58, totalRevenue = 11731.88 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 21), orderId = "707520663", unitSold = 99, unitPrice = 81.73, totalRevenue = 8125.60 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 21), orderId = "875304210", unitSold = 8, unitPrice = 47.45, totalRevenue = 390.04 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 21), orderId = "612943828", unitSold = 26, unitPrice = 421.89, totalRevenue = 10775.07 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 22), orderId = "232155120", unitSold = 87, unitPrice = 81.73, totalRevenue = 7121.95 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 22), orderId = "423159730", unitSold = 12, unitPrice = 205.7, totalRevenue = 2513.65 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 22), orderId = "447601306", unitSold = 40, unitPrice = 9.33, totalRevenue = 375.81 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 22), orderId = "559299647", unitSold = 80, unitPrice = 255.28, totalRevenue = 20547.49 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 23), orderId = "607080304", unitSold = 74, unitPrice = 668.27, totalRevenue = 49505.44 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 23), orderId = "695557582", unitSold = 98, unitPrice = 9.33, totalRevenue = 914.34 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 23), orderId = "403836238", unitSold = 30, unitPrice = 109.28, totalRevenue = 3247.80 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 05, 23), orderId = "749282443", unitSold = 82, unitPrice = 651.21, totalRevenue = 53268.98 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 24), orderId = "749912869", unitSold = 47, unitPrice = 81.73, totalRevenue = 3872.37 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 05, 24), orderId = "560600841", unitSold = 310, unitPrice = 668.27, totalRevenue = 20723.05 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 24), orderId = "693473613", unitSold = 31, unitPrice = 47.45, totalRevenue = 1474.27 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 24), orderId = "935644042", unitSold = 67, unitPrice = 205.7, totalRevenue = 13820.98 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 25), orderId = "975002133", unitSold = 37, unitPrice = 81.73, totalRevenue = 2985.60 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 25), orderId = "248335492", unitSold = 68, unitPrice = 47.45, totalRevenue = 3248.43 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 25), orderId = "265929067", unitSold = 32, unitPrice = 255.28, totalRevenue = 8105.14 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 25), orderId = "887313640", unitSold = 47, unitPrice = 152.58, totalRevenue = 7139.22 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 05, 26), orderId = "782857692", unitSold = 38, unitPrice = 109.28, totalRevenue = 4199.63 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 26), orderId = "605825459", unitSold = 61, unitPrice = 81.73, totalRevenue = 4961.83 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 26), orderId = "332877862", unitSold = 48, unitPrice = 47.45, totalRevenue = 2282.82 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 26), orderId = "243102395", unitSold = 83, unitPrice = 9.33, totalRevenue = 770.28 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 27), orderId = "978349959", unitSold = 43, unitPrice = 47.45, totalRevenue = 2063.60 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 27), orderId = "967644727", unitSold = 64, unitPrice = 9.33, totalRevenue = 600.20 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 27), orderId = "786519229", unitSold = 74, unitPrice = 152.58, totalRevenue = 11249.72 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 27), orderId = "538957345", unitSold = 83, unitPrice = 154.06, totalRevenue = 12802.39 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 05, 28), orderId = "827964293", unitSold = 19, unitPrice = 9.33, totalRevenue = 176.15 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 28), orderId = "641801393", unitSold = 21, unitPrice = 81.73, totalRevenue = 1756.38 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 28), orderId = "785261380", unitSold = 51, unitPrice = 205.7, totalRevenue = 10587.38 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 05, 28), orderId = "622791612", unitSold = 67, unitPrice = 205.7, totalRevenue = 13849.78 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 29), orderId = "166435849", unitSold = 9, unitPrice = 81.73, totalRevenue = 752.73 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 29), orderId = "351182544", unitSold = 16, unitPrice = 152.58, totalRevenue = 2401.61 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 29), orderId = "345437037", unitSold = 14, unitPrice = 154.06, totalRevenue = 2081.35 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 05, 29), orderId = "969616687", unitSold = 100, unitPrice = 421.89, totalRevenue = 42104.62 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 30), orderId = "960486018", unitSold = 80, unitPrice = 255.28, totalRevenue = 20453.03 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 05, 30), orderId = "194225251", unitSold = 44, unitPrice = 437.2, totalRevenue = 19337.36 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 05, 30), orderId = "813209140", unitSold = 55, unitPrice = 154.06, totalRevenue = 8490.25 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 05, 30), orderId = "213865458", unitSold = 64, unitPrice = 152.58, totalRevenue = 9760.54 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 05, 31), orderId = "400304734", unitSold = 79, unitPrice = 81.73, totalRevenue = 6473.83 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 31), orderId = "296320855", unitSold = 68, unitPrice = 47.45, totalRevenue = 3217.58 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 05, 31), orderId = "641018617", unitSold = 5, unitPrice = 255.28, totalRevenue = 1296.82 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 05, 31), orderId = "989691627", unitSold = 6, unitPrice = 47.45, totalRevenue = 284.70 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 01), orderId = "195840156", unitSold = 23, unitPrice = 668.27, totalRevenue = 15430.35 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 01), orderId = "887400329", unitSold = 3, unitPrice = 152.58, totalRevenue = 506.57 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 01), orderId = "585917890", unitSold = 50, unitPrice = 668.27, totalRevenue = 33273.16 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 01), orderId = "441395747", unitSold = 77, unitPrice = 152.58, totalRevenue = 11695.26 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 02), orderId = "799003732", unitSold = 18, unitPrice = 47.45, totalRevenue = 861.22 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 02), orderId = "376456248", unitSold = 6, unitPrice = 152.58, totalRevenue = 952.10 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 02), orderId = "714306008", unitSold = 79, unitPrice = 152.58, totalRevenue = 12017.20 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 02), orderId = "447970378", unitSold = 32, unitPrice = 421.89, totalRevenue = 13690.33 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 03), orderId = "333281266", unitSold = 42, unitPrice = 9.33, totalRevenue = 390.74 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 03), orderId = "714818418", unitSold = 950, unitPrice = 668.27, totalRevenue = 63545.79 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 03), orderId = "142553031", unitSold = 35, unitPrice = 152.58, totalRevenue = 5286.90 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 03), orderId = "817740142", unitSold = 63, unitPrice = 255.28, totalRevenue = 16171.99 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 04), orderId = "329110324", unitSold = 99, unitPrice = 47.45, totalRevenue = 4703.72 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 04), orderId = "306125295", unitSold = 81, unitPrice = 421.89, totalRevenue = 34308.09 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 04), orderId = "487014758", unitSold = 73, unitPrice = 437.2, totalRevenue = 32107.97 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 04), orderId = "336116683", unitSold = 33, unitPrice = 109.28, totalRevenue = 3552.69 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 05), orderId = "254291713", unitSold = 94, unitPrice = 47.45, totalRevenue = 4471.69 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 05), orderId = "445178306", unitSold = 70, unitPrice = 255.28, totalRevenue = 17890.02 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 05), orderId = "629925000", unitSold = 77, unitPrice = 437.2, totalRevenue = 33493.89 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 05), orderId = "829352176", unitSold = 26, unitPrice = 47.45, totalRevenue = 1230.85 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 06), orderId = "860287702", unitSold = 71, unitPrice = 205.7, totalRevenue = 14610.87 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 06), orderId = "434355056", unitSold = 42, unitPrice = 255.28, totalRevenue = 10640.07 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 06), orderId = "105558288", unitSold = 59, unitPrice = 651.21, totalRevenue = 38408.37 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 06), orderId = "637521445", unitSold = 56, unitPrice = 152.58, totalRevenue = 8571.94 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 07), orderId = "884493243", unitSold = 1, unitPrice = 421.89, totalRevenue = 257.35 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 07), orderId = "801093709", unitSold = 3, unitPrice = 421.89, totalRevenue = 1202.39 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 06, 07), orderId = "603123080", unitSold = 64, unitPrice = 81.73, totalRevenue = 5211.92 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 07), orderId = "663065516", unitSold = 47, unitPrice = 668.27, totalRevenue = 31248.31 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 08), orderId = "419124829", unitSold = 72, unitPrice = 47.45, totalRevenue = 3419.25 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 08), orderId = "872412145", unitSold = 50, unitPrice = 109.28, totalRevenue = 5458.54 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 08), orderId = "402084004", unitSold = 44, unitPrice = 255.28, totalRevenue = 11352.30 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 08), orderId = "171131217", unitSold = 4, unitPrice = 9.33, totalRevenue = 35.92 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 09), orderId = "257915914", unitSold = 19, unitPrice = 154.06, totalRevenue = 2934.84 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 09), orderId = "547748982", unitSold = 100, unitPrice = 152.58, totalRevenue = 15183.24 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 09), orderId = "599622905", unitSold = 6, unitPrice = 152.58, totalRevenue = 910.90 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 09), orderId = "854545199", unitSold = 78, unitPrice = 9.33, totalRevenue = 724.85 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 10), orderId = "151807725", unitSold = 21, unitPrice = 255.28, totalRevenue = 5447.68 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 10), orderId = "109694898", unitSold = 102, unitPrice = 668.27, totalRevenue = 86.88 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 10), orderId = "159050118", unitSold = 53, unitPrice = 421.89, totalRevenue = 22440.33 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 10), orderId = "718327605", unitSold = 80, unitPrice = 154.06, totalRevenue = 12257.01 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 11), orderId = "405785882", unitSold = 99, unitPrice = 651.21, totalRevenue = 64567.47 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 11), orderId = "751940190", unitSold = 47, unitPrice = 154.06, totalRevenue = 7189.98 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 06, 11), orderId = "849312102", unitSold = 92, unitPrice = 81.73, totalRevenue = 7502.81 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 11), orderId = "663857305", unitSold = 90, unitPrice = 437.2, totalRevenue = 39278.05 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 12), orderId = "769651782", unitSold = 59, unitPrice = 109.28, totalRevenue = 6470.47 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 12), orderId = "185342633", unitSold = 59, unitPrice = 47.45, totalRevenue = 2780.10 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 12), orderId = "607300031", unitSold = 24, unitPrice = 437.2, totalRevenue = 10619.59 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 12), orderId = "444358193", unitSold = 43, unitPrice = 668.27, totalRevenue = 28862.58 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 13), orderId = "957547605", unitSold = 85, unitPrice = 9.33, totalRevenue = 790.25 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 13), orderId = "285509622", unitSold = 75, unitPrice = 651.21, totalRevenue = 48821.21 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 13), orderId = "858877503", unitSold = 98, unitPrice = 47.45, totalRevenue = 4647.25 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 13), orderId = "742141759", unitSold = 51, unitPrice = 47.45, totalRevenue = 2416.63 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 14), orderId = "540431916", unitSold = 47, unitPrice = 152.58, totalRevenue = 7122.43 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 14), orderId = "879107797", unitSold = 64, unitPrice = 651.21, totalRevenue = 41599.29 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 14), orderId = "108989799", unitSold = 14, unitPrice = 205.7, totalRevenue = 2793.41 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 14), orderId = "908627116", unitSold = 18, unitPrice = 437.2, totalRevenue = 7913.32 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 06, 15), orderId = "995529830", unitSold = 83, unitPrice = 81.73, totalRevenue = 6745.99 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 15), orderId = "327585113", unitSold = 86, unitPrice = 255.28, totalRevenue = 21874.94 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 15), orderId = "167161977", unitSold = 580, unitPrice = 668.27, totalRevenue = 38746.29 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 15), orderId = "860886800", unitSold = 44, unitPrice = 152.58, totalRevenue = 6698.26 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 16), orderId = "572550618", unitSold = 93, unitPrice = 651.21, totalRevenue = 60601.60 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 16), orderId = "780282342", unitSold = 17, unitPrice = 255.28, totalRevenue = 4439.32 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 16), orderId = "109228837", unitSold = 21, unitPrice = 651.21, totalRevenue = 13642.85 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 06, 16), orderId = "865650832", unitSold = 42, unitPrice = 81.73, totalRevenue = 3410.59 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 17), orderId = "940904176", unitSold = 33, unitPrice = 651.21, totalRevenue = 21548.54 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 17), orderId = "263080346", unitSold = 2, unitPrice = 109.28, totalRevenue = 191.24 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 17), orderId = "936574876", unitSold = 22, unitPrice = 651.21, totalRevenue = 14150.79 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 17), orderId = "703693473", unitSold = 74, unitPrice = 651.21, totalRevenue = 48130.93 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 18), orderId = "367576634", unitSold = 2, unitPrice = 9.33, totalRevenue = 275.14 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 18), orderId = "957553613", unitSold = 2, unitPrice = 421.89, totalRevenue = 1046.29 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 18), orderId = "986442506", unitSold = 91, unitPrice = 205.7, totalRevenue = 18745.44 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 18), orderId = "450268065", unitSold = 32, unitPrice = 205.7, totalRevenue = 6543.32 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 19), orderId = "118598544", unitSold = 48, unitPrice = 154.06, totalRevenue = 7394.88 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 19), orderId = "989119565", unitSold = 13, unitPrice = 47.45, totalRevenue = 623.97 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 19), orderId = "734945714", unitSold = 56, unitPrice = 255.28, totalRevenue = 14356.95 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 19), orderId = "468532407", unitSold = 80, unitPrice = 668.27, totalRevenue = 53501.70 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 20), orderId = "952714908", unitSold = 84, unitPrice = 154.06, totalRevenue = 12890.20 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 20), orderId = "509819114", unitSold = 57, unitPrice = 154.06, totalRevenue = 8719.80 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 20), orderId = "858020055", unitSold = 61, unitPrice = 152.58, totalRevenue = 9240.24 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 20), orderId = "277568137", unitSold = 74, unitPrice = 421.89, totalRevenue = 31367.52 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 21), orderId = "306220996", unitSold = 65, unitPrice = 154.06, totalRevenue = 9939.95 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 21), orderId = "236911857", unitSold = 63, unitPrice = 255.28, totalRevenue = 16179.65 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 21), orderId = "564646470", unitSold = 3, unitPrice = 9.33, totalRevenue = 27.34 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 21), orderId = "371123158", unitSold = 24, unitPrice = 668.27, totalRevenue = 16339.20 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 22), orderId = "979165780", unitSold = 58, unitPrice = 651.21, totalRevenue = 37561.79 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 06, 22), orderId = "241281497", unitSold = 94, unitPrice = 154.06, totalRevenue = 14500.13 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 22), orderId = "816696012", unitSold = 74, unitPrice = 651.21, totalRevenue = 47883.47 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 22), orderId = "580823838", unitSold = 75, unitPrice = 152.58, totalRevenue = 11498.43 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 23), orderId = "713696610", unitSold = 75, unitPrice = 205.7, totalRevenue = 15513.89 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 23), orderId = "539065062", unitSold = 2, unitPrice = 668.27, totalRevenue = 1242.98 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 23), orderId = "133362710", unitSold = 38, unitPrice = 205.7, totalRevenue = 7907.11 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 23), orderId = "469283854", unitSold = 4, unitPrice = 152.58, totalRevenue = 573.70 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 24), orderId = "270723140", unitSold = 18, unitPrice = 205.7, totalRevenue = 3801.34 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 24), orderId = "160127294", unitSold = 41, unitPrice = 255.28, totalRevenue = 10412.87 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 24), orderId = "621442782", unitSold = 760, unitPrice = 668.27, totalRevenue = 50681.60 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 24), orderId = "448621833", unitSold = 600, unitPrice = 437.2, totalRevenue = 26341.30 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 25), orderId = "317323625", unitSold = 20, unitPrice = 205.7, totalRevenue = 4046.12 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 25), orderId = "377502095", unitSold = 1, unitPrice = 47.45, totalRevenue = 54.09 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 25), orderId = "761439931", unitSold = 59, unitPrice = 205.7, totalRevenue = 12035.51 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 06, 25), orderId = "251753699", unitSold = 84, unitPrice = 152.58, totalRevenue = 12769.42 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 26), orderId = "167882096", unitSold = 89, unitPrice = 651.21, totalRevenue = 57944.67 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 26), orderId = "844997823", unitSold = 25, unitPrice = 109.28, totalRevenue = 2718.89 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 26), orderId = "418593108", unitSold = 990, unitPrice = 437.2, totalRevenue = 43099.18 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 26), orderId = "964214932", unitSold = 15, unitPrice = 205.7, totalRevenue = 3044.36 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 06, 27), orderId = "386163699", unitSold = 33, unitPrice = 421.89, totalRevenue = 13816.90 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 27), orderId = "805484378", unitSold = 6, unitPrice = 9.33, totalRevenue = 894.00 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 06, 27), orderId = "346215522", unitSold = 7, unitPrice = 9.33, totalRevenue = 160.57 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 27), orderId = "406275975", unitSold = 49, unitPrice = 109.28, totalRevenue = 5402.80 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 28), orderId = "985665738", unitSold = 93, unitPrice = 109.28, totalRevenue = 10108.40 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 28), orderId = "290878760", unitSold = 639, unitPrice = 437.2, totalRevenue = 27735.97 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 28), orderId = "850038230", unitSold = 41, unitPrice = 668.27, totalRevenue = 27111.71 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 06, 28), orderId = "294499957", unitSold = 79, unitPrice = 255.28, totalRevenue = 20261.57 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 29), orderId = "331457364", unitSold = 45, unitPrice = 668.27, totalRevenue = 29771.43 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 06, 29), orderId = "811546599", unitSold = 35, unitPrice = 205.7, totalRevenue = 7257.10 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 29), orderId = "192721068", unitSold = 93, unitPrice = 668.27, totalRevenue = 62162.48 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 06, 29), orderId = "568944442", unitSold = 22, unitPrice = 109.28, totalRevenue = 2358.26 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 06, 30), orderId = "668599021", unitSold = 22, unitPrice = 651.21, totalRevenue = 14085.67 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 06, 30), orderId = "506365287", unitSold = 36, unitPrice = 437.2, totalRevenue = 15721.71 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 06, 30), orderId = "668362987", unitSold = 230, unitPrice = 668.27, totalRevenue = 15470.45 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 06, 30), orderId = "270611131", unitSold = 87, unitPrice = 47.45, totalRevenue = 4129.10 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 01), orderId = "224287021", unitSold = 4, unitPrice = 255.28, totalRevenue = 939.43 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 01), orderId = "778763139", unitSold = 22, unitPrice = 154.06, totalRevenue = 3347.72 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 01), orderId = "340827071", unitSold = 72, unitPrice = 421.89, totalRevenue = 30203.11 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 01), orderId = "219762027", unitSold = 13, unitPrice = 81.73, totalRevenue = 1080.47 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 02), orderId = "452171361", unitSold = 654, unitPrice = 437.2, totalRevenue = 28020.15 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 02), orderId = "641129338", unitSold = 33, unitPrice = 255.28, totalRevenue = 8355.31 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 02), orderId = "251800048", unitSold = 21, unitPrice = 421.89, totalRevenue = 8796.41 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 02), orderId = "479880082", unitSold = 60, unitPrice = 255.28, totalRevenue = 15406.15 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 03), orderId = "647663629", unitSold = 69, unitPrice = 154.06, totalRevenue = 10653.25 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 03), orderId = "285884702", unitSold = 85, unitPrice = 47.45, totalRevenue = 4028.98 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 03), orderId = "350274455", unitSold = 29, unitPrice = 651.21, totalRevenue = 18559.49 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 03), orderId = "133766114", unitSold = 32, unitPrice = 651.21, totalRevenue = 20975.47 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 04), orderId = "739998137", unitSold = 7, unitPrice = 154.06, totalRevenue = 1152.37 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 04), orderId = "603426492", unitSold = 62, unitPrice = 154.06, totalRevenue = 9494.72 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 04), orderId = "443121373", unitSold = 83, unitPrice = 154.06, totalRevenue = 12811.63 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 04), orderId = "277070748", unitSold = 2, unitPrice = 152.58, totalRevenue = 375.35 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 05), orderId = "444540584", unitSold = 83, unitPrice = 651.21, totalRevenue = 53998.33 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 05), orderId = "727131259", unitSold = 32, unitPrice = 154.06, totalRevenue = 4857.51 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 05), orderId = "535654580", unitSold = 9, unitPrice = 651.21, totalRevenue = 6179.98 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 05), orderId = "543723094", unitSold = 86, unitPrice = 81.73, totalRevenue = 7029.60 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 06), orderId = "744370782", unitSold = 47, unitPrice = 154.06, totalRevenue = 7257.77 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 06), orderId = "560608565", unitSold = 54, unitPrice = 651.21, totalRevenue = 35080.68 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 06), orderId = "360945355", unitSold = 6, unitPrice = 255.28, totalRevenue = 1549.55 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 06), orderId = "641489398", unitSold = 98, unitPrice = 154.06, totalRevenue = 15133.31 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 07), orderId = "370116364", unitSold = 45, unitPrice = 152.58, totalRevenue = 6884.41 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 07), orderId = "941909682", unitSold = 9, unitPrice = 205.7, totalRevenue = 1771.08 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 07), orderId = "386371409", unitSold = 47, unitPrice = 421.89, totalRevenue = 20001.80 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 07), orderId = "974655807", unitSold = 1200, unitPrice = 437.2, totalRevenue = 5102.12 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 08), orderId = "680533778", unitSold = 39, unitPrice = 255.28, totalRevenue = 10014.63 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 08), orderId = "281561410", unitSold = 910, unitPrice = 437.2, totalRevenue = 39929.48 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 07, 08), orderId = "673803794", unitSold = 2, unitPrice = 9.33, totalRevenue = 221.21 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 08), orderId = "812344396", unitSold = 96, unitPrice = 47.45, totalRevenue = 4561.84 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 09), orderId = "751929891", unitSold = 16, unitPrice = 154.06, totalRevenue = 2494.23 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 09), orderId = "389678895", unitSold = 35, unitPrice = 421.89, totalRevenue = 14761.93 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 09), orderId = "133812463", unitSold = 69, unitPrice = 205.7, totalRevenue = 14267.35 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 09), orderId = "431083619", unitSold = 61, unitPrice = 81.73, totalRevenue = 4966.73 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 10), orderId = "633895957", unitSold = 28, unitPrice = 154.06, totalRevenue = 4244.35 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 10), orderId = "180412948", unitSold = 71, unitPrice = 205.7, totalRevenue = 14512.14 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 10), orderId = "502715766", unitSold = 36, unitPrice = 81.73, totalRevenue = 2959.44 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 10), orderId = "554707705", unitSold = 92, unitPrice = 47.45, totalRevenue = 4361.60 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 07, 11), orderId = "647278249", unitSold = 29, unitPrice = 109.28, totalRevenue = 3139.61 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 11), orderId = "298015153", unitSold = 82, unitPrice = 47.45, totalRevenue = 3872.39 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 11), orderId = "175257527", unitSold = 15, unitPrice = 421.89, totalRevenue = 6125.84 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 11), orderId = "798688733", unitSold = 86, unitPrice = 255.28, totalRevenue = 21954.08 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 12), orderId = "461823451", unitSold = 27, unitPrice = 154.06, totalRevenue = 4124.19 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 12), orderId = "781253516", unitSold = 91, unitPrice = 668.27, totalRevenue = 61019.73 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 12), orderId = "315254676", unitSold = 97, unitPrice = 47.45, totalRevenue = 4611.67 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 12), orderId = "622758996", unitSold = 71, unitPrice = 421.89, totalRevenue = 29874.03 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 13), orderId = "753585135", unitSold = 14, unitPrice = 205.7, totalRevenue = 2968.25 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 13), orderId = "308170640", unitSold = 34, unitPrice = 255.28, totalRevenue = 8666.76 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 13), orderId = "106578814", unitSold = 79, unitPrice = 154.06, totalRevenue = 12161.50 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 13), orderId = "947620856", unitSold = 9, unitPrice = 421.89, totalRevenue = 3898.26 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 14), orderId = "496656548", unitSold = 6, unitPrice = 154.06, totalRevenue = 868.90 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 14), orderId = "498585164", unitSold = 130, unitPrice = 668.27, totalRevenue = 8587.27 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 14), orderId = "367050921", unitSold = 74, unitPrice = 152.58, totalRevenue = 11258.88 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 07, 14), orderId = "933924853", unitSold = 8, unitPrice = 9.33, totalRevenue = 743.88 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 15), orderId = "775076282", unitSold = 12, unitPrice = 205.7, totalRevenue = 2365.55 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 15), orderId = "749690568", unitSold = 90, unitPrice = 47.45, totalRevenue = 4248.67 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 07, 15), orderId = "821912801", unitSold = 11, unitPrice = 109.28, totalRevenue = 1220.66 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 15), orderId = "275269162", unitSold = 71, unitPrice = 47.45, totalRevenue = 3377.02 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 16), orderId = "337022197", unitSold = 12, unitPrice = 47.45, totalRevenue = 575.09 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 16), orderId = "494454562", unitSold = 89, unitPrice = 668.27, totalRevenue = 59796.80 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 07, 16), orderId = "749258840", unitSold = 12, unitPrice = 109.28, totalRevenue = 1306.99 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 16), orderId = "738199555", unitSold = 85, unitPrice = 255.28, totalRevenue = 21719.22 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 17), orderId = "382206475", unitSold = 22, unitPrice = 651.21, totalRevenue = 14613.15 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 17), orderId = "459112060", unitSold = 3, unitPrice = 651.21, totalRevenue = 2057.82 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 17), orderId = "642442548", unitSold = 19, unitPrice = 437.2, totalRevenue = 8223.73 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 07, 17), orderId = "903740775", unitSold = 8, unitPrice = 9.33, totalRevenue = 544.22 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 07, 18), orderId = "217140328", unitSold = 5, unitPrice = 9.33, totalRevenue = 513.43 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 18), orderId = "482649838", unitSold = 57, unitPrice = 255.28, totalRevenue = 14469.27 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 18), orderId = "625283706", unitSold = 42, unitPrice = 47.45, totalRevenue = 1992.43 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 18), orderId = "686800706", unitSold = 84, unitPrice = 437.2, totalRevenue = 36925.91 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 19), orderId = "410231912", unitSold = 56, unitPrice = 668.27, totalRevenue = 37383.02 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 19), orderId = "116699969", unitSold = 30, unitPrice = 154.06, totalRevenue = 4574.04 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 19), orderId = "110442054", unitSold = 11, unitPrice = 205.7, totalRevenue = 2289.44 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 19), orderId = "683927953", unitSold = 83, unitPrice = 47.45, totalRevenue = 3954.48 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 20), orderId = "581990706", unitSold = 28, unitPrice = 47.45, totalRevenue = 1346.63 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 20), orderId = "141259562", unitSold = 7, unitPrice = 47.45, totalRevenue = 331.20 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 20), orderId = "970611894", unitSold = 45, unitPrice = 437.2, totalRevenue = 19599.68 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 20), orderId = "125870978", unitSold = 69, unitPrice = 437.2, totalRevenue = 30053.13 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 21), orderId = "723680436", unitSold = 20, unitPrice = 152.58, totalRevenue = 3018.03 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 21), orderId = "723090350", unitSold = 74, unitPrice = 255.28, totalRevenue = 18783.50 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 21), orderId = "270001733", unitSold = 8400, unitPrice = 437.2, totalRevenue = 36584.90 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 21), orderId = "653939568", unitSold = 41, unitPrice = 154.06, totalRevenue = 6324.16 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 22), orderId = "683458888", unitSold = 95, unitPrice = 205.7, totalRevenue = 19599.10 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 22), orderId = "219034612", unitSold = 61, unitPrice = 421.89, totalRevenue = 25583.41 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 22), orderId = "372845780", unitSold = 22, unitPrice = 421.89, totalRevenue = 9311.11 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 22), orderId = "532324779", unitSold = 56, unitPrice = 47.45, totalRevenue = 2650.56 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 23), orderId = "351317298", unitSold = 15, unitPrice = 152.58, totalRevenue = 2212.41 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 23), orderId = "515007579", unitSold = 10, unitPrice = 47.45, totalRevenue = 494.43 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 23), orderId = "275632226", unitSold = 46, unitPrice = 47.45, totalRevenue = 2161.82 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 23), orderId = "700620734", unitSold = 81, unitPrice = 255.28, totalRevenue = 20675.13 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 07, 24), orderId = "875370299", unitSold = 4, unitPrice = 152.58, totalRevenue = 578.28 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 24), orderId = "971916091", unitSold = 4, unitPrice = 421.89, totalRevenue = 1788.81 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 24), orderId = "807281672", unitSold = 14, unitPrice = 421.89, totalRevenue = 6079.43 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 07, 24), orderId = "376547658", unitSold = 77, unitPrice = 47.45, totalRevenue = 3641.79 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 25), orderId = "511349046", unitSold = 84, unitPrice = 651.21, totalRevenue = 54708.15 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 25), orderId = "480177485", unitSold = 40, unitPrice = 421.89, totalRevenue = 17057.01 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 25), orderId = "436372077", unitSold = 110, unitPrice = 668.27, totalRevenue = 7016.84 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 25), orderId = "873522365", unitSold = 35, unitPrice = 437.2, totalRevenue = 15450.65 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 26), orderId = "864981782", unitSold = 62, unitPrice = 81.73, totalRevenue = 5055.82 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 26), orderId = "276225316", unitSold = 1, unitPrice = 154.06, totalRevenue = 98.60 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 26), orderId = "573378455", unitSold = 43, unitPrice = 421.89, totalRevenue = 18061.11 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 07, 26), orderId = "675079667", unitSold = 99, unitPrice = 109.28, totalRevenue = 10851.50 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 27), orderId = "677284657", unitSold = 24, unitPrice = 205.7, totalRevenue = 5010.85 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 07, 27), orderId = "980459678", unitSold = 87, unitPrice = 109.28, totalRevenue = 9464.74 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 07, 27), orderId = "889740073", unitSold = 2700, unitPrice = 437.2, totalRevenue = 11869.98 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 07, 27), orderId = "960269725", unitSold = 40, unitPrice = 421.89, totalRevenue = 16900.91 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 28), orderId = "138867890", unitSold = 30, unitPrice = 205.7, totalRevenue = 6068.15 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 28), orderId = "743053281", unitSold = 48, unitPrice = 154.06, totalRevenue = 7445.72 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 28), orderId = "299921452", unitSold = 23, unitPrice = 668.27, totalRevenue = 15223.19 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 28), orderId = "266467225", unitSold = 24, unitPrice = 154.06, totalRevenue = 3740.58 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 29), orderId = "841291654", unitSold = 58, unitPrice = 81.73, totalRevenue = 4700.29 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 29), orderId = "666424071", unitSold = 85, unitPrice = 651.21, totalRevenue = 55658.92 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 29), orderId = "184170186", unitSold = 89, unitPrice = 255.28, totalRevenue = 22735.24 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 29), orderId = "672327935", unitSold = 56, unitPrice = 81.73, totalRevenue = 4602.22 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 30), orderId = "977313554", unitSold = 77, unitPrice = 205.7, totalRevenue = 15742.22 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 07, 30), orderId = "850108611", unitSold = 9, unitPrice = 9.33, totalRevenue = 455.68 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 07, 30), orderId = "984673964", unitSold = 58, unitPrice = 255.28, totalRevenue = 14711.79 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 07, 30), orderId = "395563447", unitSold = 49, unitPrice = 668.27, totalRevenue = 32538.07 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 07, 31), orderId = "686458671", unitSold = 72, unitPrice = 81.73, totalRevenue = 5909.08 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 07, 31), orderId = "544219195", unitSold = 54, unitPrice = 205.7, totalRevenue = 11126.31 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 07, 31), orderId = "363329732", unitSold = 62, unitPrice = 651.21, totalRevenue = 40355.48 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 07, 31), orderId = "720307290", unitSold = 38, unitPrice = 154.06, totalRevenue = 5837.33 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 01), orderId = "634153020", unitSold = 88, unitPrice = 651.21, totalRevenue = 57475.79 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 01), orderId = "679414975", unitSold = 28, unitPrice = 205.7, totalRevenue = 5850.11 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 01), orderId = "680517470", unitSold = 91, unitPrice = 109.28, totalRevenue = 9941.20 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 01), orderId = "532846200", unitSold = 99, unitPrice = 81.73, totalRevenue = 8079.83 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 02), orderId = "775724732", unitSold = 30, unitPrice = 255.28, totalRevenue = 7763.06 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 02), orderId = "768522679", unitSold = 18, unitPrice = 152.58, totalRevenue = 2737.29 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 02), orderId = "470897471", unitSold = 790, unitPrice = 437.2, totalRevenue = 34455.73 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 02), orderId = "869397771", unitSold = 30, unitPrice = 421.89, totalRevenue = 12551.23 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 03), orderId = "652889430", unitSold = 33, unitPrice = 421.89, totalRevenue = 14116.44 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 03), orderId = "685871589", unitSold = 35, unitPrice = 154.06, totalRevenue = 5392.10 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 03), orderId = "401116263", unitSold = 81, unitPrice = 154.06, totalRevenue = 12434.18 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 03), orderId = "155128943", unitSold = 500, unitPrice = 255.28, totalRevenue = 12654.23 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 04), orderId = "925405299", unitSold = 68, unitPrice = 205.7, totalRevenue = 14084.28 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 04), orderId = "901712167", unitSold = 55, unitPrice = 651.21, totalRevenue = 35966.33 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 04), orderId = "128090989", unitSold = 380, unitPrice = 668.27, totalRevenue = 25561.33 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 04), orderId = "243882596", unitSold = 910, unitPrice = 668.27, totalRevenue = 61046.46 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 05), orderId = "358099639", unitSold = 85, unitPrice = 651.21, totalRevenue = 55326.80 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 05), orderId = "682489430", unitSold = 26, unitPrice = 47.45, totalRevenue = 1254.58 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 05), orderId = "650727784", unitSold = 307, unitPrice = 437.2, totalRevenue = 16032.12 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 05), orderId = "267066323", unitSold = 97, unitPrice = 205.7, totalRevenue = 19983.76 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 06), orderId = "763568961", unitSold = 59, unitPrice = 152.58, totalRevenue = 8970.18 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 06), orderId = "517935693", unitSold = 75, unitPrice = 81.73, totalRevenue = 6159.17 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 06), orderId = "860232770", unitSold = 8, unitPrice = 47.45, totalRevenue = 402.38 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 06), orderId = "954259860", unitSold = 56, unitPrice = 81.73, totalRevenue = 4538.47 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 07), orderId = "590198266", unitSold = 16, unitPrice = 152.58, totalRevenue = 2497.73 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 07), orderId = "360820043", unitSold = 5, unitPrice = 9.33, totalRevenue = 328.79 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 07), orderId = "795451629", unitSold = 7, unitPrice = 668.27, totalRevenue = 4464.04 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 07), orderId = "179970920", unitSold = 62, unitPrice = 47.45, totalRevenue = 2965.15 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 08), orderId = "925333631", unitSold = 6, unitPrice = 9.33, totalRevenue = 526.12 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 08), orderId = "873105657", unitSold = 2, unitPrice = 9.33, totalRevenue = 20.62 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 08), orderId = "698002040", unitSold = 32, unitPrice = 154.06, totalRevenue = 4883.70 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 08), orderId = "537578904", unitSold = 4, unitPrice = 47.45, totalRevenue = 189.33 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 09), orderId = "576700961", unitSold = 75, unitPrice = 668.27, totalRevenue = 50020.01 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 09), orderId = "636879432", unitSold = 56, unitPrice = 421.89, totalRevenue = 23760.84 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 09), orderId = "867360150", unitSold = 100, unitPrice = 109.28, totalRevenue = 10925.81 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 09), orderId = "935371100", unitSold = 5900, unitPrice = 255.28, totalRevenue = 15186.61 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 10), orderId = "953554761", unitSold = 69, unitPrice = 47.45, totalRevenue = 3273.58 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 10), orderId = "433871400", unitSold = 10, unitPrice = 154.06, totalRevenue = 1608.39 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 10), orderId = "215434443", unitSold = 7, unitPrice = 205.7, totalRevenue = 1427.56 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 10), orderId = "859686028", unitSold = 91, unitPrice = 205.7, totalRevenue = 18626.14 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 11), orderId = "505975615", unitSold = 83, unitPrice = 152.58, totalRevenue = 12638.20 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 11), orderId = "499009597", unitSold = 69, unitPrice = 651.21, totalRevenue = 44829.30 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 11), orderId = "109956681", unitSold = 75, unitPrice = 255.28, totalRevenue = 19094.94 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 11), orderId = "974337804", unitSold = 71, unitPrice = 668.27, totalRevenue = 47199.91 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 12), orderId = "847999322", unitSold = 7, unitPrice = 109.28, totalRevenue = 763.87 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 12), orderId = "597918736", unitSold = 63, unitPrice = 437.2, totalRevenue = 27526.11 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 12), orderId = "312927377", unitSold = 10, unitPrice = 651.21, totalRevenue = 6642.34 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 12), orderId = "816632068", unitSold = 84, unitPrice = 651.21, totalRevenue = 54903.52 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 13), orderId = "666678130", unitSold = 37, unitPrice = 47.45, totalRevenue = 1769.41 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 13), orderId = "105117976", unitSold = 56, unitPrice = 109.28, totalRevenue = 6119.68 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 13), orderId = "110667788", unitSold = 79, unitPrice = 152.58, totalRevenue = 12073.66 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 13), orderId = "644913613", unitSold = 31, unitPrice = 81.73, totalRevenue = 2553.25 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 14), orderId = "154119145", unitSold = 61, unitPrice = 421.89, totalRevenue = 25882.95 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 14), orderId = "669355189", unitSold = 59, unitPrice = 109.28, totalRevenue = 6480.30 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 14), orderId = "510978686", unitSold = 88, unitPrice = 255.28, totalRevenue = 22472.30 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 14), orderId = "956433522", unitSold = 88, unitPrice = 651.21, totalRevenue = 57228.33 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 15), orderId = "928647124", unitSold = 62, unitPrice = 81.73, totalRevenue = 5047.64 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 15), orderId = "262749040", unitSold = 21, unitPrice = 81.73, totalRevenue = 1744.94 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 15), orderId = "588200986", unitSold = 6, unitPrice = 651.21, totalRevenue = 3894.24 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 15), orderId = "835054767", unitSold = 1, unitPrice = 109.28, totalRevenue = 127.86 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 16), orderId = "229571187", unitSold = 41, unitPrice = 651.21, totalRevenue = 26510.76 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 16), orderId = "939787089", unitSold = 27, unitPrice = 255.28, totalRevenue = 6992.12 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 16), orderId = "218629920", unitSold = 63, unitPrice = 255.28, totalRevenue = 16100.51 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 16), orderId = "430967319", unitSold = 170, unitPrice = 668.27, totalRevenue = 11099.96 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 17), orderId = "925264966", unitSold = 53, unitPrice = 437.2, totalRevenue = 23259.04 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 17), orderId = "284870612", unitSold = 84, unitPrice = 109.28, totalRevenue = 9178.43 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 17), orderId = "212058293", unitSold = 16, unitPrice = 255.28, totalRevenue = 4125.32 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 17), orderId = "474222981", unitSold = 20, unitPrice = 255.28, totalRevenue = 5036.67 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 18), orderId = "893779695", unitSold = 81, unitPrice = 651.21, totalRevenue = 52930.35 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 18), orderId = "473555219", unitSold = 44, unitPrice = 205.7, totalRevenue = 8984.98 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 18), orderId = "414715278", unitSold = 230, unitPrice = 668.27, totalRevenue = 15510.55 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 18), orderId = "948607051", unitSold = 28, unitPrice = 154.06, totalRevenue = 4253.60 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 19), orderId = "925504004", unitSold = 61, unitPrice = 421.89, totalRevenue = 25553.88 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 19), orderId = "509214437", unitSold = 67, unitPrice = 109.28, totalRevenue = 7345.80 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 19), orderId = "837067067", unitSold = 56, unitPrice = 154.06, totalRevenue = 8630.44 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 19), orderId = "659798800", unitSold = 80, unitPrice = 651.21, totalRevenue = 51979.58 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 20), orderId = "352176463", unitSold = 57, unitPrice = 651.21, totalRevenue = 37092.92 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 20), orderId = "210409057", unitSold = 36, unitPrice = 651.21, totalRevenue = 23248.20 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 20), orderId = "166013562", unitSold = 60, unitPrice = 109.28, totalRevenue = 6509.81 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 20), orderId = "802078616", unitSold = 17, unitPrice = 437.2, totalRevenue = 7611.65 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 21), orderId = "635397565", unitSold = 71, unitPrice = 81.73, totalRevenue = 5814.27 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 21), orderId = "293258845", unitSold = 7, unitPrice = 47.45, totalRevenue = 315.07 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 21), orderId = "837855851", unitSold = 90, unitPrice = 437.2, totalRevenue = 39435.44 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 21), orderId = "618474757", unitSold = 93, unitPrice = 255.28, totalRevenue = 23687.43 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 22), orderId = "740760314", unitSold = 63, unitPrice = 47.45, totalRevenue = 2986.03 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 22), orderId = "282475936", unitSold = 8, unitPrice = 9.33, totalRevenue = 910.79 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 22), orderId = "824200189", unitSold = 80, unitPrice = 154.06, totalRevenue = 12334.04 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 22), orderId = "263506495", unitSold = 63, unitPrice = 205.7, totalRevenue = 12924.13 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 23), orderId = "310343015", unitSold = 62, unitPrice = 109.28, totalRevenue = 6828.91 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 23), orderId = "136931979", unitSold = 1, unitPrice = 651.21, totalRevenue = 455.85 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 23), orderId = "770478332", unitSold = 5, unitPrice = 109.28, totalRevenue = 562.79 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 23), orderId = "888670623", unitSold = 62, unitPrice = 651.21, totalRevenue = 40635.50 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 24), orderId = "938025844", unitSold = 15, unitPrice = 154.06, totalRevenue = 2383.31 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 24), orderId = "642683303", unitSold = 31, unitPrice = 205.7, totalRevenue = 6430.18 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 24), orderId = "567588317", unitSold = 9, unitPrice = 47.45, totalRevenue = 403.80 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 24), orderId = "808538234", unitSold = 33, unitPrice = 205.7, totalRevenue = 6759.30 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 25), orderId = "737890565", unitSold = 71, unitPrice = 154.06, totalRevenue = 10893.58 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 25), orderId = "544485270", unitSold = 41, unitPrice = 437.2, totalRevenue = 18126.31 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 25), orderId = "102928006", unitSold = 75, unitPrice = 81.73, totalRevenue = 6161.62 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 08, 25), orderId = "451010930", unitSold = 30, unitPrice = 109.28, totalRevenue = 3291.51 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 26), orderId = "118002879", unitSold = 85, unitPrice = 81.73, totalRevenue = 6970.75 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 26), orderId = "123670709", unitSold = 58, unitPrice = 255.28, totalRevenue = 14719.44 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 08, 26), orderId = "795000588", unitSold = 72, unitPrice = 81.73, totalRevenue = 5881.29 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 26), orderId = "659845149", unitSold = 17, unitPrice = 255.28, totalRevenue = 4334.65 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 27), orderId = "867222821", unitSold = 94, unitPrice = 421.89, totalRevenue = 39484.69 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 27), orderId = "453569972", unitSold = 3, unitPrice = 437.2, totalRevenue = 1263.51 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 27), orderId = "793938434", unitSold = 9, unitPrice = 9.33, totalRevenue = 268.70 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 27), orderId = "746434152", unitSold = 53, unitPrice = 668.27, totalRevenue = 35471.77 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 28), orderId = "991831386", unitSold = 38, unitPrice = 437.2, totalRevenue = 16626.72 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 08, 28), orderId = "465418040", unitSold = 64, unitPrice = 152.58, totalRevenue = 9759.02 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 08, 28), orderId = "357222878", unitSold = 90, unitPrice = 421.89, totalRevenue = 38151.51 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 28), orderId = "346045577", unitSold = 14, unitPrice = 651.21, totalRevenue = 9318.82 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 29), orderId = "289606320", unitSold = 98, unitPrice = 668.27, totalRevenue = 65497.14 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 08, 29), orderId = "561255729", unitSold = 69, unitPrice = 9.33, totalRevenue = 643.49 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 29), orderId = "242024362", unitSold = 92, unitPrice = 255.28, totalRevenue = 23592.98 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 29), orderId = "349157369", unitSold = 23, unitPrice = 255.28, totalRevenue = 5817.83 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 08, 30), orderId = "396820008", unitSold = 67, unitPrice = 154.06, totalRevenue = 10343.59 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 08, 30), orderId = "708063542", unitSold = 6, unitPrice = 205.7, totalRevenue = 1217.74 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 08, 30), orderId = "869589173", unitSold = 96, unitPrice = 437.2, totalRevenue = 42036.78 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 08, 30), orderId = "791975486", unitSold = 20, unitPrice = 47.45, totalRevenue = 949.47 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 31), orderId = "402646195", unitSold = 8, unitPrice = 651.21, totalRevenue = 5287.83 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 08, 31), orderId = "431535089", unitSold = 97, unitPrice = 255.28, totalRevenue = 24703.45 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 08, 31), orderId = "276661765", unitSold = 920, unitPrice = 668.27, totalRevenue = 61607.81 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 08, 31), orderId = "800084340", unitSold = 16, unitPrice = 651.21, totalRevenue = 10360.75 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 01), orderId = "461408460", unitSold = 99, unitPrice = 152.58, totalRevenue = 15062.70 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 01), orderId = "823699796", unitSold = 99, unitPrice = 255.28, totalRevenue = 25346.75 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 01), orderId = "234825313", unitSold = 89, unitPrice = 651.21, totalRevenue = 57846.98 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 01), orderId = "877424657", unitSold = 83, unitPrice = 437.2, totalRevenue = 36326.95 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 02), orderId = "745182311", unitSold = 49, unitPrice = 437.2, totalRevenue = 21247.92 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 02), orderId = "237660729", unitSold = 79, unitPrice = 47.45, totalRevenue = 3770.38 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 02), orderId = "694687259", unitSold = 23, unitPrice = 47.45, totalRevenue = 1068.57 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 02), orderId = "779897391", unitSold = 78, unitPrice = 152.58, totalRevenue = 11937.86 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 03), orderId = "448817956", unitSold = 41, unitPrice = 47.45, totalRevenue = 1927.42 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 03), orderId = "769822585", unitSold = 65, unitPrice = 154.06, totalRevenue = 9959.98 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 03), orderId = "405997025", unitSold = 24, unitPrice = 651.21, totalRevenue = 15609.50 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 03), orderId = "991644704", unitSold = 86, unitPrice = 668.27, totalRevenue = 57197.23 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 04), orderId = "267614781", unitSold = 20, unitPrice = 421.89, totalRevenue = 8505.30 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 04), orderId = "312404668", unitSold = 63, unitPrice = 81.73, totalRevenue = 5180.05 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 04), orderId = "711141002", unitSold = 13, unitPrice = 668.27, totalRevenue = 8553.86 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 04), orderId = "446970021", unitSold = 72, unitPrice = 154.06, totalRevenue = 11118.51 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 05), orderId = "971377074", unitSold = 9, unitPrice = 651.21, totalRevenue = 5971.60 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 05), orderId = "370222795", unitSold = 60, unitPrice = 255.28, totalRevenue = 15334.67 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 05), orderId = "724799668", unitSold = 32, unitPrice = 437.2, totalRevenue = 13916.08 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 05), orderId = "606017291", unitSold = 28, unitPrice = 81.73, totalRevenue = 2319.50 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 06), orderId = "359911954", unitSold = 48, unitPrice = 152.58, totalRevenue = 7323.84 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 06), orderId = "459212481", unitSold = 0, unitPrice = 651.21, totalRevenue = 214.90 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 06), orderId = "566935575", unitSold = 7, unitPrice = 9.33, totalRevenue = 717.48 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 06), orderId = "447917163", unitSold = 75, unitPrice = 47.45, totalRevenue = 3557.33 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 07), orderId = "681941401", unitSold = 5, unitPrice = 47.45, totalRevenue = 223.02 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 07), orderId = "902424991", unitSold = 65, unitPrice = 109.28, totalRevenue = 7062.77 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 07), orderId = "366526925", unitSold = 23, unitPrice = 152.58, totalRevenue = 3535.28 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 07), orderId = "914391076", unitSold = 75, unitPrice = 109.28, totalRevenue = 8189.44 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 08), orderId = "292180383", unitSold = 5, unitPrice = 9.33, totalRevenue = 142.10 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 08), orderId = "212874114", unitSold = 30, unitPrice = 421.89, totalRevenue = 12808.58 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 08), orderId = "722088277", unitSold = 8, unitPrice = 9.33, totalRevenue = 76.69 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 08), orderId = "837407815", unitSold = 64, unitPrice = 109.28, totalRevenue = 7033.26 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 09), orderId = "225666320", unitSold = 85, unitPrice = 437.2, totalRevenue = 37310.65 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 09), orderId = "734526431", unitSold = 15, unitPrice = 109.28, totalRevenue = 1692.75 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 09), orderId = "565798747", unitSold = 13, unitPrice = 421.89, totalRevenue = 5387.54 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 09), orderId = "784117686", unitSold = 67, unitPrice = 154.06, totalRevenue = 10326.64 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 10), orderId = "458942115", unitSold = 0, unitPrice = 81.73, totalRevenue = 20.43 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 10), orderId = "611809146", unitSold = 680, unitPrice = 668.27, totalRevenue = 45288.66 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 10), orderId = "332839667", unitSold = 35, unitPrice = 651.21, totalRevenue = 22850.96 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 10), orderId = "612911641", unitSold = 30, unitPrice = 154.06, totalRevenue = 4668.02 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 11), orderId = "105390059", unitSold = 61, unitPrice = 205.7, totalRevenue = 12578.56 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 11), orderId = "219607102", unitSold = 55, unitPrice = 47.45, totalRevenue = 2598.84 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 11), orderId = "994566810", unitSold = 33, unitPrice = 205.7, totalRevenue = 6736.68 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 11), orderId = "676121222", unitSold = 81, unitPrice = 205.7, totalRevenue = 16762.49 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 12), orderId = "613542068", unitSold = 96, unitPrice = 421.89, totalRevenue = 40446.59 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 12), orderId = "689975583", unitSold = 80, unitPrice = 255.28, totalRevenue = 20327.95 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 12), orderId = "579379737", unitSold = 2, unitPrice = 255.28, totalRevenue = 495.24 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 12), orderId = "606970441", unitSold = 49, unitPrice = 651.21, totalRevenue = 31889.75 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 13), orderId = "461463820", unitSold = 63, unitPrice = 154.06, totalRevenue = 9634.91 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 13), orderId = "328236997", unitSold = 15, unitPrice = 255.28, totalRevenue = 3767.93 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 13), orderId = "334612929", unitSold = 83, unitPrice = 81.73, totalRevenue = 6747.63 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 13), orderId = "170214545", unitSold = 91, unitPrice = 154.06, totalRevenue = 14051.81 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 14), orderId = "469414317", unitSold = 90, unitPrice = 205.7, totalRevenue = 18478.03 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 14), orderId = "421043574", unitSold = 50, unitPrice = 81.73, totalRevenue = 4090.59 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 14), orderId = "635309588", unitSold = 11, unitPrice = 421.89, totalRevenue = 4556.41 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 14), orderId = "474178349", unitSold = 88, unitPrice = 47.45, totalRevenue = 4159.47 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 15), orderId = "752716100", unitSold = 43, unitPrice = 47.45, totalRevenue = 2028.96 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 15), orderId = "196587741", unitSold = 35, unitPrice = 651.21, totalRevenue = 23026.79 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 15), orderId = "352327525", unitSold = 4, unitPrice = 668.27, totalRevenue = 2666.40 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 15), orderId = "308168065", unitSold = 26, unitPrice = 109.28, totalRevenue = 2877.34 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 16), orderId = "823444449", unitSold = 44, unitPrice = 154.06, totalRevenue = 6726.26 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 16), orderId = "654480731", unitSold = 42, unitPrice = 154.06, totalRevenue = 6542.93 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 16), orderId = "410067975", unitSold = 71, unitPrice = 205.7, totalRevenue = 14565.62 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 16), orderId = "927232635", unitSold = 76, unitPrice = 81.73, totalRevenue = 6209.03 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 17), orderId = "916881453", unitSold = 45, unitPrice = 205.7, totalRevenue = 9157.76 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 17), orderId = "941323029", unitSold = 3, unitPrice = 9.33, totalRevenue = 677.17 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 17), orderId = "252557933", unitSold = 4, unitPrice = 9.33, totalRevenue = 593.39 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 17), orderId = "855445134", unitSold = 41, unitPrice = 421.89, totalRevenue = 17213.11 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 18), orderId = "246222341", unitSold = 15, unitPrice = 255.28, totalRevenue = 3872.60 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 18), orderId = "792983996", unitSold = 4, unitPrice = 9.33, totalRevenue = 41.15 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 18), orderId = "884216010", unitSold = 80, unitPrice = 668.27, totalRevenue = 53601.94 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 18), orderId = "947779643", unitSold = 79, unitPrice = 81.73, totalRevenue = 6467.29 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 19), orderId = "584072101", unitSold = 88, unitPrice = 255.28, totalRevenue = 22385.50 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 19), orderId = "245042169", unitSold = 23, unitPrice = 9.33, totalRevenue = 212.54 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 19), orderId = "901573550", unitSold = 45, unitPrice = 255.28, totalRevenue = 11495.26 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 19), orderId = "596628272", unitSold = 44, unitPrice = 152.58, totalRevenue = 6742.51 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 20), orderId = "702186715", unitSold = 150, unitPrice = 668.27, totalRevenue = 10077.51 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 20), orderId = "107172334", unitSold = 35, unitPrice = 437.2, totalRevenue = 15433.16 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 20), orderId = "143657672", unitSold = 4, unitPrice = 651.21, totalRevenue = 2292.26 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 09, 20), orderId = "195177543", unitSold = 62, unitPrice = 9.33, totalRevenue = 580.98 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 21), orderId = "962186753", unitSold = 13, unitPrice = 668.27, totalRevenue = 8667.46 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 21), orderId = "653148210", unitSold = 99, unitPrice = 437.2, totalRevenue = 43387.73 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 21), orderId = "817192542", unitSold = 43, unitPrice = 154.06, totalRevenue = 6606.09 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 21), orderId = "446991050", unitSold = 34, unitPrice = 109.28, totalRevenue = 3759.23 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 22), orderId = "825143039", unitSold = 70, unitPrice = 437.2, totalRevenue = 30678.32 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 22), orderId = "109653699", unitSold = 78, unitPrice = 81.73, totalRevenue = 6392.10 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 22), orderId = "726708972", unitSold = 82, unitPrice = 421.89, totalRevenue = 34548.57 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 22), orderId = "173571383", unitSold = 25, unitPrice = 81.73, totalRevenue = 2030.17 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 23), orderId = "635122907", unitSold = 58, unitPrice = 255.28, totalRevenue = 14900.69 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 23), orderId = "801213872", unitSold = 58, unitPrice = 109.28, totalRevenue = 6386.32 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 23), orderId = "613830459", unitSold = 49, unitPrice = 205.7, totalRevenue = 10136.90 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 23), orderId = "206435525", unitSold = 77, unitPrice = 255.28, totalRevenue = 19692.30 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 24), orderId = "973208701", unitSold = 290, unitPrice = 668.27, totalRevenue = 19620.41 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 09, 24), orderId = "677394092", unitSold = 48, unitPrice = 255.28, totalRevenue = 12304.50 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 24), orderId = "600370490", unitSold = 18, unitPrice = 81.73, totalRevenue = 1490.76 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 24), orderId = "169754493", unitSold = 30, unitPrice = 651.21, totalRevenue = 19419.08 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 25), orderId = "326714789", unitSold = 10, unitPrice = 668.27, totalRevenue = 6823.04 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 25), orderId = "827506387", unitSold = 64, unitPrice = 437.2, totalRevenue = 27910.85 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 25), orderId = "826916301", unitSold = 18, unitPrice = 154.06, totalRevenue = 2717.62 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 25), orderId = "917834603", unitSold = 65, unitPrice = 47.45, totalRevenue = 3089.00 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 26), orderId = "214743077", unitSold = 61, unitPrice = 81.73, totalRevenue = 4987.98 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 26), orderId = "141812741", unitSold = 44, unitPrice = 651.21, totalRevenue = 28627.19 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 26), orderId = "596870315", unitSold = 60, unitPrice = 152.58, totalRevenue = 9223.46 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 26), orderId = "551136291", unitSold = 23, unitPrice = 109.28, totalRevenue = 2547.32 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 27), orderId = "586165082", unitSold = 81, unitPrice = 437.2, totalRevenue = 35535.62 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 09, 27), orderId = "607261836", unitSold = 11, unitPrice = 668.27, totalRevenue = 7531.40 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 27), orderId = "760364902", unitSold = 77, unitPrice = 421.89, totalRevenue = 32595.22 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 27), orderId = "348286616", unitSold = 46, unitPrice = 154.06, totalRevenue = 7092.92 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 28), orderId = "397877871", unitSold = 98, unitPrice = 47.45, totalRevenue = 4630.65 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 28), orderId = "605373561", unitSold = 23, unitPrice = 109.28, totalRevenue = 2561.52 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 09, 28), orderId = "276694810", unitSold = 90, unitPrice = 81.73, totalRevenue = 7354.07 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 09, 28), orderId = "877259004", unitSold = 93, unitPrice = 205.7, totalRevenue = 19107.47 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 29), orderId = "947434604", unitSold = 58, unitPrice = 154.06, totalRevenue = 8947.80 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 09, 29), orderId = "580854308", unitSold = 66, unitPrice = 421.89, totalRevenue = 27642.23 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 09, 29), orderId = "146263062", unitSold = 13, unitPrice = 651.21, totalRevenue = 8758.77 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 09, 29), orderId = "228836476", unitSold = 67, unitPrice = 152.58, totalRevenue = 10151.15 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 09, 30), orderId = "891271722", unitSold = 60, unitPrice = 109.28, totalRevenue = 6516.37 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 09, 30), orderId = "140635573", unitSold = 60, unitPrice = 154.06, totalRevenue = 9314.47 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 09, 30), orderId = "866004025", unitSold = 87, unitPrice = 47.45, totalRevenue = 4123.88 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 09, 30), orderId = "157244670", unitSold = 10, unitPrice = 437.2, totalRevenue = 4577.48 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 10, 01), orderId = "682831895", unitSold = 40, unitPrice = 47.45, totalRevenue = 1891.83 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 10, 01), orderId = "531067359", unitSold = 35, unitPrice = 421.89, totalRevenue = 14715.52 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 10, 01), orderId = "155710446", unitSold = 70, unitPrice = 154.06, totalRevenue = 10839.66 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 10, 01), orderId = "522921168", unitSold = 28, unitPrice = 154.06, totalRevenue = 4389.17 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 10, 02), orderId = "886628711", unitSold = 20, unitPrice = 47.45, totalRevenue = 945.68 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 10, 02), orderId = "738839423", unitSold = 79, unitPrice = 421.89, totalRevenue = 33156.34 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 02), orderId = "574441039", unitSold = 87, unitPrice = 437.2, totalRevenue = 38141.33 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 10, 02), orderId = "866792809", unitSold = 21, unitPrice = 152.58, totalRevenue = 3217.91 });
            list.Add(new SalesOrder { itemType = "BabyFood", orderDate = new DateTime(2020, 10, 03), orderId = "298856723", unitSold = 72, unitPrice = 255.28, totalRevenue = 18380.16 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 10, 03), orderId = "398511302", unitSold = 720, unitPrice = 668.27, totalRevenue = 48148.85 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 03), orderId = "903278148", unitSold = 89, unitPrice = 205.7, totalRevenue = 18373.12 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 10, 03), orderId = "522371423", unitSold = 2, unitPrice = 152.58, totalRevenue = 254.81 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 10, 04), orderId = "688344371", unitSold = 3, unitPrice = 9.33, totalRevenue = 489.92 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 10, 04), orderId = "177950036", unitSold = 82, unitPrice = 152.58, totalRevenue = 12549.71 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 10, 04), orderId = "141977107", unitSold = 30, unitPrice = 651.21, totalRevenue = 19770.74 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 04), orderId = "630488908", unitSold = 45, unitPrice = 205.7, totalRevenue = 9326.44 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 10, 05), orderId = "733528649", unitSold = 63, unitPrice = 152.58, totalRevenue = 9586.60 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 10, 05), orderId = "604041039", unitSold = 80, unitPrice = 81.73, totalRevenue = 6556.38 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 05), orderId = "798784863", unitSold = 70, unitPrice = 437.2, totalRevenue = 30809.48 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 10, 05), orderId = "607190167", unitSold = 99, unitPrice = 47.45, totalRevenue = 4706.57 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 10, 06), orderId = "919890248", unitSold = 48, unitPrice = 421.89, totalRevenue = 20339.32 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 10, 06), orderId = "105966842", unitSold = 8, unitPrice = 9.33, totalRevenue = 634.25 });
            list.Add(new SalesOrder { itemType = "Vegetables", orderDate = new DateTime(2020, 10, 06), orderId = "954092919", unitSold = 62, unitPrice = 154.06, totalRevenue = 9477.77 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 06), orderId = "129268586", unitSold = 23, unitPrice = 205.7, totalRevenue = 4735.21 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 07), orderId = "228097045", unitSold = 78, unitPrice = 437.2, totalRevenue = 34272.11 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 10, 07), orderId = "521787345", unitSold = 7, unitPrice = 9.33, totalRevenue = 683.42 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 10, 07), orderId = "692956054", unitSold = 100, unitPrice = 81.73, totalRevenue = 8132.14 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 10, 07), orderId = "958937633", unitSold = 98, unitPrice = 109.28, totalRevenue = 10720.37 });
            list.Add(new SalesOrder { itemType = "Snacks", orderDate = new DateTime(2020, 10, 08), orderId = "927766072", unitSold = 55, unitPrice = 152.58, totalRevenue = 8320.19 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 08), orderId = "148871457", unitSold = 32, unitPrice = 205.7, totalRevenue = 6637.94 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 08), orderId = "866053378", unitSold = 33, unitPrice = 437.2, totalRevenue = 14405.74 });
            list.Add(new SalesOrder { itemType = "Beverages", orderDate = new DateTime(2020, 10, 08), orderId = "944415509", unitSold = 24, unitPrice = 47.45, totalRevenue = 1134.53 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 09), orderId = "721767270", unitSold = 58, unitPrice = 205.7, totalRevenue = 11990.25 });
            list.Add(new SalesOrder { itemType = "Cereal", orderDate = new DateTime(2020, 10, 09), orderId = "629709136", unitSold = 38, unitPrice = 205.7, totalRevenue = 7779.57 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 10, 09), orderId = "811701095", unitSold = 92, unitPrice = 651.21, totalRevenue = 60217.39 });
            list.Add(new SalesOrder { itemType = "Clothes", orderDate = new DateTime(2020, 10, 09), orderId = "763920438", unitSold = 99, unitPrice = 109.28, totalRevenue = 10805.61 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 10, 10), orderId = "869832932", unitSold = 17, unitPrice = 668.27, totalRevenue = 11688.04 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 10, 10), orderId = "109358012", unitSold = 3, unitPrice = 81.73, totalRevenue = 262.35 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 10), orderId = "674421346", unitSold = 51, unitPrice = 437.2, totalRevenue = 22375.90 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 10, 10), orderId = "408834159", unitSold = 20, unitPrice = 651.21, totalRevenue = 12815.81 });
            list.Add(new SalesOrder { itemType = "OfficeSupplies", orderDate = new DateTime(2020, 10, 11), orderId = "256243503", unitSold = 70, unitPrice = 651.21, totalRevenue = 45597.72 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 10, 11), orderId = "377938973", unitSold = 94, unitPrice = 81.73, totalRevenue = 7679.35 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 10, 11), orderId = "701816356", unitSold = 9, unitPrice = 9.33, totalRevenue = 178.20 });
            list.Add(new SalesOrder { itemType = "PersonalCare", orderDate = new DateTime(2020, 10, 11), orderId = "803517568", unitSold = 76, unitPrice = 81.73, totalRevenue = 6177.97 });
            list.Add(new SalesOrder { itemType = "Household", orderDate = new DateTime(2020, 10, 12), orderId = "306859576", unitSold = 540, unitPrice = 668.27, totalRevenue = 36240.28 });
            list.Add(new SalesOrder { itemType = "Fruits", orderDate = new DateTime(2020, 10, 12), orderId = "987714517", unitSold = 5, unitPrice = 9.33, totalRevenue = 515.95 });
            list.Add(new SalesOrder { itemType = "Cosmetics", orderDate = new DateTime(2020, 10, 12), orderId = "461065137", unitSold = 83, unitPrice = 437.2, totalRevenue = 36178.30 });
            list.Add(new SalesOrder { itemType = "Meat", orderDate = new DateTime(2020, 10, 12), orderId = "614994323", unitSold = 93, unitPrice = 421.89, totalRevenue = 39408.74 });
            int count = 1;
            foreach (var item in list)
            {
                item.Number = count;
                count++;
            }
            return list;
        }

    }
}
