﻿using Dapper;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Models;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
//using Microsoft.Data.Sqlite;
using System.Linq;
using System.Linq.Expressions;
using System.Windows.Forms;

namespace SmartCreator.Data
{
    public class Archive_DataAccess
    {
        //public string Name { get; set; }
        public static int _dbType = 0;//sqlite
        public static string connection_string = "Data Source=db\\CardsArchive.db;";
        //Connection_string= "Data Source=db\\" + fileName + ";",
        public static string DefualtDBType = "SQLlite";
        public static string username_db = "";
        public static string password_db = "";
        public IDbConnection Connection_db;
        //private OrmLiteConnectionFactory dbFactory = null;

        public static readonly object Lock_ArchiveDB = new object();

        public Archive_DataAccess()
        {
            connection_string=utils.Get_CardsArchive_ConnectionString();
        }
        public static IDbConnection GetConnection()
        {
            return new SQLiteConnection(utils.Get_CardsArchive_ConnectionString());
        }
        public List<T> GetListAnyDB<T>(string query)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var cnn = GetConnection())
                    {
                        return cnn.Query<T>(query, new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        public T GetSingleAnyDB<T>(string query)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var cnn = GetConnection())
                    {
                        return cnn.QuerySingle<T>(query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
        //public List<BatchArtchive> Get_Batch_byBatchNumber(int id)
        //{
        //    lock (Lock_ArchiveDB)
        //    {
        //        using var db = dbFactory.Open();
        //        var table = db.Select<BatchArtchive>(a => a.BatchNumber == id);

        //        return table;
        //    }
        //}
        //public List<BatchArtchive> Get_Batch_byBatchNumber_And_Server(int id, int server = 0)
        //{
        //    lock (Lock_ArchiveDB)
        //    {
        //        using var db = dbFactory.Open();
        //        //var table = db.Select<BatchCard>(a => a.BatchNumber == id);
        //        //if (table.Count == 0)
        //        var table = db.Select<BatchArtchive>(a => a.BatchNumber == id && a.Rb==Global_Variable.Mk_resources.RB_code);

        //        return table;
        //    }
        //}
        public int Add_ArchiveCards_ToDB(List<CardsArtchive> UM_Users, bool is_insert = true, bool check_byID = true)
        {
            int rowEfect = 0;
            lock (Lock_ArchiveDB)
            {
                bool status = false;

                string query = "";
                if (is_insert)
                {
                    query =
                        "INSERT  into CardsArtchive ("
                        + "[SN], "
                        + "[UserName], "
                        + "[Password], "
                        + "[ProfileName], "
                        + "[BatchCardId], "

                        + "[RegDate], "
                        + "[PageNumber], "
                        + "[Status], "
                        //+ "[AddedDb], "
                        + "[Rb] "
                        + ") "

                        + "values ("

                        + "@SN, "
                        + "@UserName, "
                        + "@Password,"
                        + "@ProfileName, "

                        + "@BatchCardId, "
                        + "@RegDate, "
                        + "@PageNumber,"
                        + "@Status,"
                        //+ "@AddedDb,"
                        + "@Rb"

                        + "); ";
                }
                else
                {
                    string _update_check = " WHERE Id=@Id;";
                    _update_check = " " +
                        ",[CustomerName]=@CustomerName" +
                        ",[Location]=@Location" +
                        ",[SpCode]=@SpCode" +
                        ",[SpName]=@SpName" +
                        ",[Password]=@Password" +
                        ",[Status]=@Status" +
                        ",[Percentage]=@Percentage" +
                        ",[ProfileName]=@ProfileName" +
                        ",[LastSynDb]=@LastSynDb " +
                        //",[MkId]=@MkId " +
                        ",[DeleteFromServer]=@DeleteFromServer  " +
                        " WHERE Id = @Id;";

                    if (!check_byID)
                        _update_check = " ,[DeleteFromServer]=@DeleteFromServer  WHERE Sn_Name=@Sn_Name;";

                    query =
                        "update UmUser set "
                        + " [CustomerName]=@CustomerName "
                        + " ,[Disabled]=@Disabled "
                        + ",[Password]=@Password "
                        + ",[FirstName]=@FirstName "
                        + ",[LastName]=@LastName "
                        + ",[Comment]=@Comment"
                        + ",[Phone]=@Phone"
                        + ",[Email]=@Email"
                        + ",[CallerMac]=@CallerMac"
                        + ",[UptimeUsed]=@UptimeUsed"
                        + ",[DownloadUsed]=@DownloadUsed"
                        + ",[UploadUsed]=@UploadUsed"
                        + ",[LastSeenAt]=@LastSeenAt"
                        + ",[ActiveSessions]=@ActiveSessions"
                        + ",[SharedUsers]=@SharedUsers"
                         + ",[ProfileTransferLeft]=@ProfileTransferLeft"
                        //+ ",[actualLimUptime]=@actualLimUptime"
                        + ",[ProfileTillTime]=@ProfileTillTime"
                        + ",[ProfileTimeLeft]=@ProfileTimeLeft "
                        //+ ",[Location]=@Location"
                        //+ ",[SpId]=@SpId"
                        //+ ",[SpName]=@SpName"
                        //+ ",[Status]=@Status"
                        //+ ",[Percentage]=@Percentage"
                        //+ ",[ProfileName]=@ProfileName"
                        //+ ",[DeleteFromServer]=@DeleteFromServer"
                        //+ ",[DeleteFromServer]=0"


                        + _update_check;
                    //+ " WHERE UmUserId = @UmUserId;";

                    

                }

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                         rowEfect = con.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }

                }
                

                return rowEfect;
            }
        }
        public void Add_Batch_Cards2(BatchArtchive data)
        {
            //lock (Lock_ArchiveDB)
            //{
            //    try
            //    {
            //        data.Rb = Global_Variable.Mk_resources.RB_code;
            //        using var db = dbFactory.Open();

            //        //if (add_to_Last == false)
            //        //    db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == "BatchCards" && p.Rb == data.Rb);

            //        var effect = db.Insert(data);
            //    }
            //    catch (Exception ex) { RJMessageBox.Show("error add batch number to db \n" + ex.Message); }
            //}
        }
        public int Add_Batch_Cards(BatchArtchive data)
        {
            int rowEfect = 0;
           string query =
                        "INSERT into BatchArtchive ("
                        + "[BatchNumber], "
                        + "[AddedDate], "
                        + "[Count], "
                        + "[Sn_from], "
                        + "[Sn_to], "

                        + "[ProfileName], "
                        + "[Count_waiting], "
                        + "[Count_active], "
                        + "[Count_DeleteFormArchive], "
                        + "[Rb] "
                        + ") "

                        + "values ("

                        + "@BatchNumber, "
                        + "@AddedDate, "
                        + "@Count,"
                        + "@Sn_from, "
                        + "@Sn_to, "

                        + "@ProfileName, "
                        + "@Count_waiting,"
                        + "@Count_active,"
                        + "@Count_DeleteFormArchive,"
                        + "@Rb"

                        + "); ";
            lock (Lock_ArchiveDB)
            {
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                         rowEfect = con.Execute(query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        if (rowEfect > 0)
                        {
                            lock (Smart_DataAccess.Lock_object)
                            {
                                Smart_DataAccess sm = new Smart_DataAccess();
                                sm.Update_MySequence("BatchArchive", data.BatchNumber);
                            }
                            //using (var db = sm.dbFactory.Open())
                            //{
                            //    db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == "BatchArchive" && p.Rb == data.Rb);
                            //    //db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == "CardsArchive" && p.Rb == data.Rb);
                            //}
                        }

                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message);   }
                }
            }
            return rowEfect;
        }
        public BindingSource Get_BindingSource_Archive_Batch()
        {
            BindingSource bindingSource = new BindingSource();
            try
            {
               string Rb= Global_Variable.Mk_resources.RB_SN;
               string Rb_code= Global_Variable.Mk_resources.RB_code;
                List<BatchArtchive> sp = GetListAnyDB<BatchArtchive>("select * from BatchArtchive where ( Rb='" + Rb + "' or Rb='" + Rb_code + "'  ) order by Id desc");

                //List<BatchArtchive> sp = GetListAnyDB<BatchArtchive("");
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (BatchArtchive s in sp)
                    comboSource.Add(s.Str_Name, s.BatchNumber.ToString());

                bindingSource = new BindingSource(comboSource, null);
                //combo.DisplayMember = "Value";
                //combo.ValueMember = "Key";
                //combo.SelectedIndex = 0;
                //combo.Text = "";
            }
            catch { }
            return bindingSource;
        }




        public List<T> Load<T>(string query)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.Query<T>(query, new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        public List<T> Load<T>()
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.Query<T>($"select * from {typeof(T).Name}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        public List<T> Load<T>(Expression<Func<T, bool>> expression)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    Func<T, bool> func = expression.Compile();
                    Predicate<T> predicate = func.Invoke;

                    using (var con = GetConnection())
                    {
                        return con.Query<T>($"select * from {typeof(T).Name}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        public T LoadSingleById<T>(string id)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {typeof(T).Name} where Id={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        public T LoadSingle<T>(string query)
        {
            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.QueryFirstOrDefault<T>(query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }

        //public  List<string> load(string Query)
        //{
        //    lock (Lock_ArchiveDB)
        //    {
        //        try
        //        {
        //            using (var con = GetConnection())
        //            {
        //                return con.Query<string>(Query, new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }

        //    //using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
        //    //{
        //    //    string Qury = "SELECT userName FROM user WHERE Delet_fromServer=0 ;";
        //    //    var output = cnn.Query<string>(Qury, new DynamicParameters());
        //    //    return output.ToList();
        //    //}
        //}

        public bool DeleteById<T>(string id)
        {
            lock (Lock_ArchiveDB)
            {
                bool status = false;

                string tableName = GetType().Name;
                //string id = (string)table.GetId();
                //string iid = (string)table.ToId();
                //string iid = (string)table.id();

                string query = "DELETE FROM " + tableName + " where Id=" + (id) + ";  ";
                using (var con = GetConnection())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }

        public bool Delete<T>(string Query)
        {
            lock (Lock_ArchiveDB)
            {
                bool status = false;
                string tableName = GetType().Name;
                //string query = "DELETE FROM " + tableName + " where Id=" + (id) + ";  ";
                using (var con = GetConnection())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(Query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }

        public long Get_int_FromDB(string query)
        {

            lock (Lock_ArchiveDB)
            {
                try
                {
                    using (var cnn = GetConnection())
                    {
                        return cnn.ExecuteScalar<int>(query);
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return 0; }
            }
        }

        public int Execute<T>(string Query, T data)
        {
            lock (Lock_ArchiveDB)
            {
                int affectedRows = 0;
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
                return affectedRows;
            }
        }

        public int Execute<T>(string Query, List<T> data)
        {
            lock (Lock_ArchiveDB)
            {
                int affectedRows = 0;
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
                return affectedRows;
            }
        }


        public DataTable RunSqlCommandAsDatatable(string Qury)
        {
            DataTable dt = new DataTable();
            try
            {
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, utils.Get_CardsArchive_ConnectionString());
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                dt = tbFound;
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); return null; }

            return dt;
        }

    }
}
