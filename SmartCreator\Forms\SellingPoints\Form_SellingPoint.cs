﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Forms.SellingPoints
{
    public partial class Form_SellingPoint : RJForms.RJChildForm
    {
        public Form_SellingPoint()
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Selling Points";
            if (UIAppearance.Language_ar)
            {
                this.Text = "نقاط البيع";
                //btnSave.Text = "اضافة";
                System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Bold);
                //btnSave.Font = title_font;
                //rjButton1.Font = title_font;

                System.Drawing.Font DGV_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9  , FontStyle.Regular);
               
                //dgv.Font = DGV_font;
                //dgv.ColumnHeaderHeight =Utils.utils.Control_Mesur_DPI(40);

                //btnAddNew.Font = btnEdit.Font=btnDelete.Font= btnRefresh.Font= CustomFonts.Get_Custom_Font("Cairo_Medium", 10, true);
                btnAddNew.Font = btnEdit.Font=btnDelete.Font= btnRefresh.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 10    , FontStyle.Bold);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
                dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.ColumnHeadersHeight = (int)(50 );

                //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);


                //utils.Control_textSize(pnlClientArea);
                //utils.dgv_textSize(dgv);

                utils utils = new utils();
                utils.Control_textSize1(this);



                return;


            }



        }

        private void getData()
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.Load<SellingPoint>($"select * from SellingPoint where  Rb='{Global_Variable.Mk_resources.RB_code}'  or Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                dgv.DataSource = sp;

                //using (var db = dataAccess.dbFactory.Open())
                //{
                //    var sp = db.Select<SellingPoint>(x => x.Rb == Global_Variable.Mk_resources.RB_code);
                //    rjDataGridView1.DataSource = sp;
                //}
                dgv.Columns["Id"].Visible = false;
                dgv.Columns["Is_percentage_str"].Visible = false;
                dgv.Columns["PercentageType_Srt"].Visible = false;
                dgv.Columns["Phone"].Visible = false;
                dgv.Columns["UseAccounting_ٍStr"].Visible = false;
                //dgv.Columns["Is_Alert_str"].Visible = false;
            }
            catch(Exception ex) { RJMessageBox.Show(ex.Message); }
            }

        private void Form_SellingPoint_Load(object sender, EventArgs e)
        {
            getData();
        }

        private void rjDataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<SellingPoint>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "SellingPoint");
                    var frm = new Form_Add_Edit_SellingPoint(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                        //refersh_From_MK();
                        //loadData();
                    }


                    //using (var db = dataAccess.dbFactory.Open())
                    //{
                    //    var sp = db.Select<SellingPoint>(x => x.Id == Convert.ToInt32(rjDataGridView1.CurrentRow.Cells["Id"].Value.ToString())).FirstOrDefault();
                    //    if (sp != null)
                    //    {
                    //        //var frm = new Form_Add_Edit_SellingPoint(sp);
                    //        var frm = new Form_Add_Edit_SellingPoint(sp);
                    //        frm.add = false;
                    //        frm.ShowDialog();
                    //        if (frm.succes)
                    //        {
                    //            getData();
                    //            //refersh_From_MK();
                    //            //loadData();
                    //        }

                    //    }
                    //}
                    return;
                }
                catch { }

            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.LoadSingleById<SellingPoint>((dgv.CurrentRow.Cells["Id"].Value.ToString()), "SellingPoint");
                if (sp != null)
                {
                    //var frm = new Form_Add_Edit_SellingPoint(sp);
                    var frm = new Form_Add_Edit_SellingPoint(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                        //refersh_From_MK();
                        //loadData();
                    }

                }

                //using (var db = dataAccess.dbFactory.Open())
                //{
                //    var sp = db.Select<SellingPoint>(x => x.Id == Convert.ToInt32(rjDataGridView1.CurrentRow.Cells["Id"].Value.ToString())).FirstOrDefault();
                //    if (sp != null)
                //    {
                //        //var frm = new Form_Add_Edit_SellingPoint(sp);
                //        var frm = new Form_Add_Edit_SellingPoint(sp);
                //        frm.add = false;
                //        frm.ShowDialog();
                //        if (frm.succes)
                //        {
                //            getData();
                //            //refersh_From_MK();
                //            //loadData();
                //        }

                //    }
                //}
                return;
            }
            catch { }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            getData();
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            var frm = new Form_Add_Edit_SellingPoint();
            frm.add = true;
            frm.ShowDialog();
            if (frm.succes)
            {
                getData();
                //refersh_From_MK();
                //loadData();
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {

            if (RJMessageBox.Show("هل انت متاكد من حذف نقطة البيع ", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                smart_DataAccess.DeleteById<SellingPoint>(dgv.CurrentRow.Cells["Id"].Value.ToString(), "SellingPoint");
                getData();
                //    using (var db=smart_DataAccess.dbFactory.Open())
                //    {
                //          //db.DeleteById<SellingPoint>(Convert.ToInt16(rjDataGridView1.CurrentRow.Cells["Id"].Value.ToString()));
                //        getData();
                //    }
                //}
            }
        }
    }
}
