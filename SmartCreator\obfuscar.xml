﻿<?xml version="1.0" encoding="utf-8" ?>
 

<Obfuscator>
  <Var name="InPath" value="." />
  <Var name="OutPath" value=".\Obfuscator" />
  <Var name="HidePrivateApi" value="true" />
  <Var name="KeepPublicApi" value="false" />
  <Var name="KeyFile" value=".\test.snk" />

  <!--<Module file="$(InPath)\BasicExampleExe.exe">-->
    <!-- You need to ommit afuscate startup form to call it from new project with fody.costura -->
    <!--<SkipType name="BasicExampleExe.ExampleUI" />-->
  <!--</Module>-->
  
  <Module file="$(InPath)\SmartCreator.exe" />
  <!--<Module file="$(InPath)\BasicExampleLibrary.dll" />-->
</Obfuscator>