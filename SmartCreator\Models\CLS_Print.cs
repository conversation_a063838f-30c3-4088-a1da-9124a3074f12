﻿using iTextSharp.text.pdf;
using iTextSharp.text;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Utils;
using SmartCreator.Data;
using SmartCreator.Entities;

namespace SmartCreator.Models
{
    public class CLS_Print
    {
        ComboBox CB_Fonts=new ComboBox();
        
        public CLS_Print()
        {
            setFont();
        }
        public CLS_Print(string s)
        {

        }
        void setFont()
        {
            //try
            //{
            //    this.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);


            //    groupBox3.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Bold);
            //    radioTempletCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
            //    radioTableCard.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 8, FontStyle.Regular);
            //    BackgroundImgCard_Chbox.Font = new System.Drawing.Font(frm_login.pfc_DroidSansArabic.Families[0], 7, FontStyle.Regular);
            //    label1.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label2.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 7, FontStyle.Regular);
            //    label4.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label16.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label3.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 8, FontStyle.Regular);
            //    label5.Font = new System.Drawing.Font(frm_login.pfc.Families[0], 7, FontStyle.Regular);


            //}
            //catch (Exception ex) { }
            //try
            //{
            //    using (InstalledFontCollection col = new InstalledFontCollection())
            //    {
            //        foreach (FontFamily fa in col.Families)
            //        {
            //            CB_Fonts.Items.Add(fa.Name);
            //        }
            //    }
            //}
            //catch { }
            //return;
            try
            {
                int totalfonts = FontFactory.RegisterDirectory("C:\\WINDOWS\\Fonts");
                StringBuilder sb = new StringBuilder();
                foreach (string fontname in FontFactory.RegisteredFonts)
                {
                    //sb.Append(fontname + "\n");
                    CB_Fonts.Items.Add(fontname);
                }
                //CB_Fonts.SelectedItem = loc_login.Font;
            }
            catch { }
        }

        private Dictionary<string, string> get_table_info()
        {
            Dictionary<string, string> data = new Dictionary<string, string>();
            //data.Add("info1", "5h");

            try
            {

                StreamReader streamReader = new StreamReader(@"infCrd.data");
                string text = streamReader.ReadToEnd();
                streamReader.Close();
                string[] split = text.Split(new string[] { "\n" }, StringSplitOptions.None);
                data.Add("info1", split[0]);
                data.Add("info2", split[1]);
                data.Add("info3", split[2]);
                data.Add("info4", split[3]);
                data.Add("info5", split[4]);
                data.Add("txt_curncey", split[5]);
                data.Add("CBNumberCol", split[6]);
                data.Add("nuberCol", split[6]);
                data.Add("textFontSize", split[7]);
                data.Add("chek_temp", split[8]);


                //txt_curncey.Text = split[5];
                //CBNumberCol.Text = split[6];
                //nuberCol = Convert.ToInt16(CBNumberCol.Text);
                //textFontSize.Text = split[7];
                string chek_temp = split[8];
                string[] split_chek = chek_temp.Split(new string[] { "&" }, StringSplitOptions.None);
                //MessageBox.Show(chek_temp);
                //MessageBox.Show(chek_temp);


                try
                {
                    data.Add("checkBoxInfo1", Convert.ToBoolean(split_chek[0]).ToString());
                    data.Add("checkBoxInfo2", Convert.ToBoolean(split_chek[1]).ToString());
                    data.Add("checkBoxUser", Convert.ToBoolean(split_chek[2]).ToString());
                    data.Add("checkBoxPassword", Convert.ToBoolean(split_chek[3]).ToString());
                    data.Add("checkBoxTimeCard", Convert.ToBoolean(split_chek[4]).ToString());
                    data.Add("checkBoxVaildate", Convert.ToBoolean(split_chek[5]).ToString());
                    data.Add("checkBox_SizeTransfer", Convert.ToBoolean(split_chek[6]).ToString());
                    data.Add("checkBoxPrice", Convert.ToBoolean(split_chek[7]).ToString());
                    data.Add("checkBoxInfo3", Convert.ToBoolean(split_chek[8]).ToString());
                    data.Add("checkBoxInfo4", Convert.ToBoolean(split_chek[9]).ToString());
                    data.Add("checkBoxInfo5", Convert.ToBoolean(split_chek[10]).ToString());
                    data.Add("checkBox_SN", Convert.ToBoolean(split_chek[11]).ToString());



                }
                catch { }
            }
            catch { }


            return data;
        }

        private float MillimetersToPoints(float value)
        {
            value = iTextSharp.text.Utilities.MillimetersToPoints(value);
            return value;
        }
        public void Print_To_Pdf(Dictionary<string, NewUserToAdd> dicUsers, Dictionary<string, string> Cardsdata, CardsTemplate tcard, string pathfile)
        {
            try
            {
                loadFont_System();
                DataTable printed_N = new DataTable();
                //CLS_DBAcess_V2 cc = new CLS_DBAcess_V2();
                //printed_N = cc.GetAll_Printed_Number("usermanager");
                Byte[] bytes;
                Random _r = new Random();
                Document dc = new Document(PageSize.A4, 0f, 0f, 0f, 0f);
                FileStream fs = File.Create(pathfile);
                PdfWriter writer = PdfWriter.GetInstance(dc, fs);

                dc.Open();

                float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.space_horizontal_margin.ToString()));
                float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.Space_vertical_margin.ToString()));
                float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
                float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_height.ToString()));

                float UserName_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.login.x);
                float UserName_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-(tcard.cardsItems.login.y) - 4);

                float password_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Password.x);
                float password_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Password.y - 3);
                float Sq_Number_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.SN.y - 3);

                float Time_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Time.x);
                float Time_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Time.y - 3);

                float validity_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Validity.x);
                float validity_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Validity.y - 3);

                float sizeTransfer_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Size.x);
                float sizeTransfer_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Size.y - 3);


                float Price_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Price.x);
                float Price_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Price.y - 3);

                float Other1_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Other_Text1.x);
                float Other1_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Other_Text1.y - 3);

                float Date_Print_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Date_Print.x);
                float Date_Print_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Date_Print.y - 3);

                float Number_Print_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.Number_Print.x);
                float Number_Print_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.Number_Print.y - 3);

                 float BatchNumber_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.BatchNumber.x);
                float BatchNumber_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.BatchNumber.y - 3);

                float Logo_img_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.logo.x);
                float Logo_img_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.logo.y);

                float Logo_img_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.cardsItems.logo.item_dimension_w.ToString()));
                float Logo_img_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.cardsItems.logo.item_dimension_y.ToString()));



                float QR_img_width = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.QR.item_dimension_y);
                float QR_img_height = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.QR.item_dimension_y);

                float QR_img_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.QR.x);
                float QR_img_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.QR.y);

                float SP_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.SP.x);
                float SP_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.cardsItems.SP.y - 3);

                float Note_Page_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Note_On_Pages_X);
                float Note_Page_Y = iTextSharp.text.Utilities.MillimetersToPoints((float)-tcard.setingCard.Note_On_Pages_Y);


                float Pictur_width_orginal = Pictur_width;
                float Pictur_height__orginal = Pictur_height;//panel_back1 

                float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
                float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
                float NumreicPage = iTextSharp.text.Utilities.MillimetersToPoints(40);

                int NuberCards = dicUsers.Count;
                int NumberCard_for_OneColum = 0;
                int NumberCard_in_Page = 0;
                double NumberPages = 0;

                int ColumNumber = (int)(595 / (Pictur_width + Space_Y));
                if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                    ColumNumber = ColumNumber - 1;

                NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
                if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                    NumberCard_for_OneColum = NumberCard_for_OneColum - 1;
                NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

                //string destDirectory = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";
                string destDirectory = utils.Get_CardsBack_Directory();

                iTextSharp.text.Image jpg = null;
                iTextSharp.text.Image logo = null;

                if (tcard.setingCard.enable_background && tcard.setingCard.path_background != "")
                {
                    string destImage = Path.Combine(destDirectory, tcard.setingCard.path_background);
                    try
                    {
                        jpg = iTextSharp.text.Image.GetInstance(destImage);
                        jpg.ScaleAbsolute(Pictur_width, Pictur_height);

                        if (tcard.setingCard.quilty_image == 1)
                            jpg.SetDpi(300, 300);
                        if (tcard.setingCard.card_border_enable)
                        {
                            jpg.Border = iTextSharp.text.Rectangle.BOX;
                            jpg.BorderWidth = (float)(tcard.setingCard.card_border_Size);
                            Color c = System.Drawing.ColorTranslator.FromHtml(tcard.setingCard.card_border_Color.ToString());
                            jpg.BorderColor = ColorToBaseColor(c);
                        }
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                if (tcard.cardsItems.logo.Enable && tcard.cardsItems.logo.Path != "")
                {
                    string Logo_destImage = Path.Combine(destDirectory, tcard.cardsItems.logo.Path);
                    try
                    {
                        logo = iTextSharp.text.Image.GetInstance(Logo_destImage);
                        logo.ScaleAbsolute(Logo_img_width, Logo_img_height);
                        logo.Alignment = Element.ALIGN_LEFT;

                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }

                float Top_Bouttom_Hash = (dc.PageSize.Height - (((Space_X * NumberCard_for_OneColum) + (NumberCard_for_OneColum * Pictur_height)) - Space_X)) / 2;
                float Right_Left_Hash = (dc.PageSize.Width - (((Space_Y * ColumNumber) + (ColumNumber * Pictur_width)) - Space_Y)) / 2;

                float Right_Left_Hash_orginal = Right_Left_Hash;
                NumberPages = NuberCards / NumberCard_in_Page;
                if (NuberCards % NumberCard_in_Page != 0)
                {
                    NumberPages = NuberCards / NumberCard_in_Page;
                    NumberPages = Convert.ToInt16(NumberPages) + 1;
                }

                float pic_height_Element_Y = Pictur_height;
                float Right_Left_Hash_Element_Y = Right_Left_Hash;
                int nc = 0;
                float _6 = iTextSharp.text.Utilities.MillimetersToPoints(6);

                for (int n = 0; n < NumberPages; n++)
                {
                    Right_Left_Hash = Right_Left_Hash_orginal;
                    float _UserName_X = UserName_X;
                    float _password_X = password_X;

                    float Sq_Number_X = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.cardsItems.SN.x);
                    float _Sq_Number_X = Sq_Number_X;
                    float _Time_X = Time_X;
                    float _Other1_X = Other1_X;
                    float _validity_X = validity_X;
                    float _sizeTransfer_X = sizeTransfer_X;
                    float _Price_X = Price_X;
                    float _Date_Print_X = Date_Print_X;
                    float _Number_Print_X = Number_Print_X;
                    float _BatchNumber_X = BatchNumber_X;
                    float _SP_X = SP_X;
                    float _QR_img_X = QR_img_X;
                    float _Logo_img_X = Logo_img_X;


                    dc.NewPage();
                    for (int i = 0; i < ColumNumber; i++)
                    {
                        Pictur_height = Pictur_height__orginal;
                        pic_height_Element_Y = 0;
                        //UserName_Y = UserName_Y_orginal;
                        float _UserName_Y = UserName_Y;
                        float _password_Y = password_Y;
                        float _Sq_Number_Y = Sq_Number_Y;
                        float _Time_Y = Time_Y;
                        float _Other1_Y = Other1_Y;
                        float _validity_Y = validity_Y;
                        float _Price_Y = Price_Y;
                        float _Date_Print_Y = Date_Print_Y;
                        float _Number_Print_Y = Number_Print_Y;
                        float _BatchNumber_Y = BatchNumber_Y;
                        float _sizeTransfer_Y = sizeTransfer_Y;
                        float _SP_Y = SP_Y;
                        float _QR_img_Y = QR_img_Y;
                        float _Logo_img_Y = Logo_img_Y;

                        for (int j = 0; j < NumberCard_for_OneColum; j++)
                        {
                            if (tcard.setingCard.card_border_enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                cb.Rectangle(Right_Left_Hash, ((dc.PageSize.Height - Pictur_height) - Top_Bouttom_Hash), Pictur_width_orginal, Pictur_height__orginal);
                                Color c = ColorTranslator.FromHtml(tcard.setingCard.card_border_Color.ToString());
                                CMYKColor sd = utils.ConvertRgbToCmyk(c.R, c.G, c.B);
                                cb.SetColorStroke(sd);
                                //cb.SetCMYKColorStroke=sd;
                                //cb.SetCMYKColorFill(0, 255, 255, 0);
                                cb.SetLineWidth(tcard.setingCard.card_border_Size);
                                cb.Stroke();
                            }

                            try
                            {
                                if (tcard.setingCard.enable_background)
                                {
                                    jpg.SetAbsolutePosition((Right_Left_Hash), ((dc.PageSize.Height - Pictur_height) - Top_Bouttom_Hash));
                                    dc.Add(jpg);
                                }
                            }
                            catch { }
                            Pictur_height = (Pictur_height) + Pictur_height__orginal + Space_X;

                            if (tcard.cardsItems.login.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _UserName_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _UserName_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.login.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.login.font_size;

                                if (tcard.cardsItems.login.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.login.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.login.Color);
                                font.SetColor(c.R, c.G, c.B);

                                ColumnText ct = new ColumnText(cb);

                                string text = dicUsers.ElementAt(nc).Key;
                                if (tcard.cardsItems.login.title_show)
                                    text = tcard.cardsItems.login.title_text + " " + text;
                                Chunk chunk = new Chunk(text, font);

                                ct.SetSimpleColumn(x, (y + _6), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Password.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _password_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _password_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Password.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Password.font_size;

                                if (tcard.cardsItems.Password.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Password.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Password.Color);
                                font.SetColor(c.R, c.G, c.B);

                                ColumnText ct = new ColumnText(cb);
                                //string text = NewUser[nc, 1];
                                //if (loc_password.address_show)
                                //    text = loc_password_title.address_text + " " + NewUser[nc, 1];

                                string text = dicUsers.ElementAt(nc).Value.Password;
                                if (tcard.cardsItems.Password.title_show)
                                    text = tcard.cardsItems.Password.title_text + " " + text;

                                Chunk chunk = new Chunk(text, font);

                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.SN.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Sq_Number_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Sq_Number_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.SN.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.SN.font_size;

                                if (tcard.cardsItems.SN.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.SN.italic) { font.SetStyle("italic"); }

                                Color c = ColorTranslator.FromHtml(tcard.cardsItems.SN.Color);
                                font.SetColor(c.R, c.G, c.B);


                                //Chunk chunk = new Chunk(Sq_Number_Arry[nc], font);

                                ColumnText ct = new ColumnText(cb);
                                string text = dicUsers.ElementAt(nc).Value.SN.ToString();
                                if (tcard.cardsItems.SN.title_show)
                                    text = tcard.cardsItems.SN.title_text + " " + text;
                                Chunk chunk = new Chunk(text, font);


                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();

                            }

                            if (tcard.cardsItems.Time.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Time_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Time_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Time.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Time.font_size;

                                if (tcard.cardsItems.Time.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Time.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Time.Color);
                                font.SetColor(c.R, c.G, c.B);

                                //ColumnText ct = new ColumnText(cb);
                                //Chunk chunk = new Chunk(tiem, font);

                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["time"].ToString();
                                //if (tcard.cardsItems.Time.title_show)
                                //    text = tcard.cardsItems.Time.title_text + " " + text;

                                //if (tcard.cardsItems.Time.unit_show)
                                //    text = text + " " + tiem;
                                Chunk chunk = new Chunk(text, font);

                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();

                            }

                            if (tcard.cardsItems.Validity.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _validity_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _validity_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Validity.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Validity.font_size;

                                if (tcard.cardsItems.Validity.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Validity.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Validity.Color);
                                font.SetColor(c.R, c.G, c.B);
                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["Validity"].ToString();
                                Chunk chunk = new Chunk(text, font);
                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Size.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _sizeTransfer_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _sizeTransfer_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Size.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Size.font_size;

                                if (tcard.cardsItems.Size.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Size.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Size.Color);
                                font.SetColor(c.R, c.G, c.B);
                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["sizeTransfer"].ToString();
                                Chunk chunk = new Chunk(text, font);
                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Other_Text1.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Other1_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Other1_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Other_Text1.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Other_Text1.font_size;

                                if (tcard.cardsItems.Other_Text1.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Other_Text1.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Other_Text1.Color);
                                font.SetColor(c.R, c.G, c.B);

                                ColumnText ct = new ColumnText(cb);
                                Chunk chunk = new Chunk(tcard.cardsItems.Other_Text1.title_text, font);


                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Price.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Price_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Price_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Price.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Price.font_size;

                                if (tcard.cardsItems.Price.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Price.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Price.Color);
                                font.SetColor(c.R, c.G, c.B);

                                ColumnText ct = new ColumnText(cb);
                                //Chunk chunk = new Chunk(price, font);

                                string text = Cardsdata["price"].ToString();

                                Chunk chunk = new Chunk(text, font);

                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Date_Print.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Date_Print_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Date_Print_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Date_Print.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Date_Print.font_size;

                                if (tcard.cardsItems.Date_Print.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Date_Print.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Date_Print.Color);
                                font.SetColor(c.R, c.G, c.B);

                                ColumnText ct = new ColumnText(cb);
                                //Chunk chunk = new Chunk(dateprint, font);

                                string text = Cardsdata["DatePrint"].ToString();

                                Chunk chunk = new Chunk(text, font);


                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.Number_Print.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _Number_Print_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _Number_Print_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.Number_Print.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.Number_Print.font_size;

                                if (tcard.cardsItems.Number_Print.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.Number_Print.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.Number_Print.Color);
                                font.SetColor(c.R, c.G, c.B);
                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["numberPrint"].ToString();
                                Chunk chunk = new Chunk(text, font);
                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }
                            if (tcard.cardsItems.BatchNumber.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _BatchNumber_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _BatchNumber_Y) - Top_Bouttom_Hash);

                                iTextSharp.text.Font font = FontFactory.GetFont(tcard.cardsItems.BatchNumber.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                font.Size = tcard.cardsItems.BatchNumber.font_size;

                                if (tcard.cardsItems.BatchNumber.Blod) { font.SetStyle("bold"); }
                                if (tcard.cardsItems.BatchNumber.italic) { font.SetStyle("italic"); }

                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.BatchNumber.Color);
                                font.SetColor(c.R, c.G, c.B);
                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["batchNumber"].ToString();

                                Chunk chunk = new Chunk(text, font);
                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();
                            }

                            if (tcard.cardsItems.SP.Enable)
                            {
                                PdfContentByte cb = writer.DirectContent;
                                float x = (Right_Left_Hash_Element_Y + _SP_X);
                                float y = (dc.PageSize.Height - (pic_height_Element_Y - _SP_Y) - Top_Bouttom_Hash);
                                iTextSharp.text.Font fontItem = FontFactory.GetFont(tcard.cardsItems.SP.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                fontItem.Size = tcard.cardsItems.SP.font_size;
                                if (tcard.cardsItems.SP.Blod) { fontItem.SetStyle("bold"); }
                                if (tcard.cardsItems.SP.italic) { fontItem.SetStyle("italic"); }
                                Color c = System.Drawing.ColorTranslator.FromHtml(tcard.cardsItems.SP.Color);
                                fontItem.SetColor(c.R, c.G, c.B);
                                ColumnText ct = new ColumnText(cb);
                                string text = Cardsdata["sp"].ToString();

                                Chunk chunk = new Chunk(text, fontItem);
                                ct.SetSimpleColumn(x, (y + 17), x + chunk.GetWidthPoint() + 1, 0);
                                ct.AddText(chunk);
                                ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                ct.Go();

                            }

                            if (tcard.cardsItems.QR.Enable)
                            {
                                try
                                {
                                    float x = (Right_Left_Hash_Element_Y + _QR_img_X);
                                    float y = ((dc.PageSize.Height - _QR_img_Y - pic_height_Element_Y) - QR_img_height) - Top_Bouttom_Hash;

                                    PdfContentByte cb = writer.DirectContent;
                                    string user = dicUsers.ElementAt(nc).Key;
                                    string pass = dicUsers.ElementAt(nc).Value.Password;
                                    string str_qr = tcard.cardsItems.QR.url.TrimEnd('/') + $"?username={user}&password={pass}";
                                    BarcodeQRCode Qr = new BarcodeQRCode(str_qr, 2000, 2000, null);
                                    //BarcodeQRCode Qr = new BarcodeQRCode(dicUsers.ElementAt(nc).Key, 2000, 2000, null);

                                    iTextSharp.text.Image img = Qr.GetImage();
                                    img.Alignment = Element.ALIGN_LEFT;
                                    img.ScaleAbsolute(QR_img_width, QR_img_height);
                                    img.SetAbsolutePosition(x, y);
                                    dc.Add(img);
                                }
                                catch { }
                            }

                            if (tcard.cardsItems.logo.Enable)
                                try
                                {
                                    if (logo != null)
                                    {
                                        //logo = iTextSharp.text.Image.GetInstance(tcard.cardsItems.logo.Path);
                                        //logo.ScaleAbsolute(Logo_img_width, Logo_img_height);
                                        //logo.Alignment = Element.ALIGN_LEFT;

                                        float x = (Right_Left_Hash_Element_Y + _Logo_img_X);
                                        float y = ((dc.PageSize.Height - _Logo_img_Y - pic_height_Element_Y) - Logo_img_height) - Top_Bouttom_Hash;

                                        //float y = ((dc.PageSize.Height - Logo_img_Y) - Logo_img_height - pic_height_Element_Y - Top_Bouttom_Hash);


                                        //logo_jpg.SetAbsolutePosition(Logo_img_X, ((dc.PageSize.Height - Logo_img_Y) - Logo_img_height));
                                        logo.SetAbsolutePosition(x, y);
                                        dc.Add(logo);
                                    }
                                }
                                catch { }


                            pic_height_Element_Y = (pic_height_Element_Y) + Pictur_height__orginal + Space_X;
                            nc++;
                            if (nc == NuberCards)
                                goto endc;
                        }
                        _UserName_X = (_UserName_X) + Pictur_width_orginal + Space_Y;
                        _password_X = (_password_X) + Pictur_width_orginal + Space_Y;
                        _Sq_Number_X = (_Sq_Number_X) + Pictur_width_orginal + Space_Y;
                        _Time_X = (_Time_X) + Pictur_width_orginal + Space_Y;
                        _Other1_X = (_Other1_X) + Pictur_width_orginal + Space_Y;
                        _validity_X = (_validity_X) + Pictur_width_orginal + Space_Y;
                        _sizeTransfer_X = (_sizeTransfer_X) + Pictur_width_orginal + Space_Y;
                        _Price_X = (_Price_X) + Pictur_width_orginal + Space_Y;
                        _Date_Print_X = (_Date_Print_X) + Pictur_width_orginal + Space_Y;
                        _Number_Print_X = (_Number_Print_X) + Pictur_width_orginal + Space_Y;
                        _BatchNumber_X = (_BatchNumber_X) + Pictur_width_orginal + Space_Y;
                        _SP_X = (_SP_X) + Pictur_width_orginal + Space_Y;
                        _QR_img_X = (_QR_img_X) + Pictur_width_orginal + Space_Y;
                        _Logo_img_X = (_Logo_img_X) + Pictur_width_orginal + Space_Y;

                        Right_Left_Hash = Right_Left_Hash + Pictur_width_orginal + Space_Y;

                        //========================

                    }

                endc:;
                    if (tcard.setingCard.Note_On_Pages)
                    {
                        //A4: 210mm x 297mm(794px x 1123px)
                        float x = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Note_On_Pages_X);
                        float y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Note_On_Pages_Y);

                        PdfContentByte cb = writer.DirectContent;
                        //float x = (int)tcard.setingCard.Note_On_Pages_X;
                        //float y = ((int)tcard.setingCard.Note_On_Pages_Y);

                        iTextSharp.text.Font font = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                        font.Size = Convert.ToInt16(tcard.setingCard.Note_On_Pages_Size);

                        ColumnText ct = new ColumnText(cb);
                        string text = Cardsdata["Note_On_Pages_text"];
                        Chunk chunk = new Chunk(text, font);

                        //ct.SetSimpleColumn(x, y, x + chunk.GetWidthPoint() + 1, 0);
                        //ct.AddText(chunk);
                        //ct.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                        //ct.Go();

                        ColumnText ctt = new ColumnText(cb);
                        ctt.AddText(chunk);
                        ctt.SetSimpleColumn(x, y + _6, x + chunk.GetWidthPoint() + 1, 0);
                        ctt.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                        ctt.Go();

                    }
                    if (tcard.setingCard.Number_Pages)
                    {
                        PdfContentByte cb = writer.DirectContent;
                        float x = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Number_Pages_X);
                        float y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Number_Pages_Y);
                        iTextSharp.text.Font font = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                        font.Size = Convert.ToInt16(tcard.setingCard.Number_Pages_Size);

                        ColumnText ct = new ColumnText(cb);
                        string numTxt = (String.Format("{0} of {1}", n + 1, NumberPages));

                        Chunk chunk = new Chunk(numTxt, font);
                        ct.SetSimpleColumn(x, y + _6, x + chunk.GetWidthPoint() + 1, 0);
                        ct.AddText(chunk);
                        //ct.RunDirection = PdfWriter.RUN_DIRECTION_LTR;
                        ct.Alignment = Element.ALIGN_LEFT;
                        ct.Go();

                    }

                    if (nc == NuberCards)
                        goto la;

                }
            la:;
                dc.Close();

                //===========================================================
                //bytes = fs.ToArray();
                //using (var reader = new PdfReader(bytes))
                //{
                //    using (var ms = new MemoryStream())
                //    {
                //        using (var stamper = new PdfStamper(reader, ms))
                //        {
                //            int PageCount = reader.NumberOfPages;
                //            for (int i = 1; i <= PageCount; i++)
                //            {
                //                if ((tcard.setingCard.Number_Pages))
                //                {
                //                    iTextSharp.text.Font font = FontFactory.GetFont("arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                //                    font.Size = Convert.ToInt16(tcard.setingCard.Number_Pages_Size);
                //                    Phrase numTxt = new Phrase(String.Format("{0} of {1}", i, PageCount), font);
                //                    int x = Convert.ToInt16(tcard.setingCard.Number_Pages_X);
                //                    int y = Convert.ToInt16(tcard.setingCard.Number_Pages_Y);

                //                    ColumnText.ShowTextAligned(stamper.GetOverContent(i), Element.ALIGN_CENTER, numTxt, x, y, 0);
                //                }
                //            }
                //        }
                //        bytes = ms.ToArray();
                //    }
                //}

                //System.IO.File.WriteAllBytes(pathfile, bytes);
            }
            catch (Exception ex) { MessageBox.Show(ex.Message + "\npdf لم يستطع انشاء ملف الطباعة"); }

        }

        private PdfPTable CreateTble(CardsTableDesg1 tcard, KeyValuePair<string, NewUserToAdd> users, Dictionary<string, string> Cardsdata)
        {
            PdfPTable table2 = new PdfPTable(2);
            table2.SpacingBefore = 0f;
            table2.SpacingAfter = 0f;

            table2.DefaultCell.HorizontalAlignment = 1;
            table2.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
            table2.DefaultCell.Padding = 2f;
            PdfPCell infoTxt = new PdfPCell();
            if (tcard.setingCard.Fixed_Width_Card)
            {
                float tableWidth = iTextSharp.text.Utilities.MillimetersToPoints((float)(tcard.setingCard.card_width));
                table2.TotalWidth = tableWidth;
                table2.LockedWidth = true;
            }
            //================================================================================================= 
            if (tcard.cardsItems.info1.Enable)
            {
                table2 = addProprtiyTo_Cell_info(table2, infoTxt, tcard.cardsItems.info1, tcard.setingCard);


            }
            if (tcard.cardsItems.info2.Enable)
            {
                table2 = addProprtiyTo_Cell_info(table2, infoTxt, tcard.cardsItems.info2, tcard.setingCard);

                //iTextSharp.text.Font font = GetFont_Proprty_item(tcard.cardsItems.info2);

                //infoTxt = new PdfPCell(new Phrase(tcard.cardsItems.info2.title_text, font));
                //infoTxt.HorizontalAlignment = 1;
                //if (tcard.setingCard.Show_border_Midell) { infoTxt.BorderWidth = tcard.setingCard.size_Border_Midell; }
                //else if (tcard.setingCard.Show_Border_Coulum) { infoTxt.BorderWidthRight = tcard.setingCard.size_Border_Midell; infoTxt.BorderWidthBottom = 0; }
                //else if (tcard.setingCard.Show_Border_Row) { infoTxt.BorderWidthBottom = tcard.setingCard.size_Border_Midell; infoTxt.BorderWidthRight = 0; }
                //else { infoTxt.BorderWidth = 0; }

                //infoTxt.Colspan = 2;
                //table2.AddCell(infoTxt);
            }
            if (tcard.cardsItems.login.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.login, tcard.setingCard, users.Key);
            }
            if (tcard.cardsItems.Password.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Password, tcard.setingCard, users.Value.Password);
            }
            if (tcard.cardsItems.Time.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Time, tcard.setingCard, Cardsdata["time"]);
            }
            if (tcard.cardsItems.Validity.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Validity, tcard.setingCard, Cardsdata["Validity"]);
            }
            if (tcard.cardsItems.Price.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Price, tcard.setingCard, Cardsdata["price"]);
            }
            if (tcard.cardsItems.Size.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Size, tcard.setingCard, Cardsdata["sizeTransfer"]);
            }
            if (tcard.cardsItems.SP.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.SP, tcard.setingCard, Cardsdata["sp"]);
            }
            if (tcard.cardsItems.SN.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.SN, tcard.setingCard, users.Value.SN.ToString());
            }
            if (tcard.cardsItems.Date_Print.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Date_Print, tcard.setingCard, Cardsdata["DatePrint"]);
            }
            if (tcard.cardsItems.BatchNumber.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.BatchNumber, tcard.setingCard, Cardsdata["batchNumber"]);
            }
            if (tcard.cardsItems.Number_Print.Enable)
            {
                table2 = addProprtiyTo_Cell(table2, infoTxt, tcard.cardsItems.Number_Print, tcard.setingCard, Cardsdata["numberPrint"]);
            }
            if (tcard.cardsItems.info3.Enable)
            {
                table2 = addProprtiyTo_Cell_info(table2, infoTxt, tcard.cardsItems.info3, tcard.setingCard);
            }
            if (tcard.cardsItems.info4.Enable)
            {
                table2 = addProprtiyTo_Cell_info(table2, infoTxt, tcard.cardsItems.info4, tcard.setingCard);
            }
            if (tcard.cardsItems.info5.Enable)
            {
                table2 = addProprtiyTo_Cell_info(table2, infoTxt, tcard.cardsItems.info5, tcard.setingCard);
            }

            //if (tcard.setingCard.Show_border_Midell == false) { table2.DefaultCell.Border = Rectangle.NO_BORDER; table2.DefaultCell.Border = 0; infoTxt.Border = 0; }

            if (tcard.setingCard.Show_border_Midell) { table2.DefaultCell.BorderWidth = tcard.setingCard.size_Border_Midell; }
            else if (tcard.setingCard.Show_Border_Coulum) { table2.DefaultCell.BorderWidth = 0; table2.DefaultCell.BorderWidthRight = tcard.setingCard.size_Border_Midell; table2.DefaultCell.BorderWidthLeft = tcard.setingCard.size_Border_Midell; }
            else if (tcard.setingCard.Show_Border_Row) { table2.DefaultCell.BorderWidth = 0; table2.DefaultCell.BorderWidthBottom = tcard.setingCard.size_Border_Midell; }
            else { table2.DefaultCell.BorderWidth = 0; }

            return table2;
        }
        PdfPTable addProprtiyTo_Cell_info(PdfPTable table2, PdfPCell infoTxt, PropertyItemText loc, CardsSettingForTable cardsetting)
        {
            iTextSharp.text.Font font = GetFont_Proprty_item(loc);
            //iTextSharp.text.Font font_title = GetFont_Proprty_item(loc);
            infoTxt = new PdfPCell(new Phrase(loc.title_text, font));
            infoTxt.Colspan = 2;
            infoTxt.HorizontalAlignment = 1;
            infoTxt.Padding = cardsetting.Padding_Cell;
            //infoTxt.Padding = 2.5f;

            if (cardsetting.Show_border_Midell) { infoTxt.BorderWidth = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Coulum) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthRight = cardsetting.size_Border_Midell; infoTxt.BorderWidthLeft = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Row) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthBottom = cardsetting.size_Border_Midell; }
            else { infoTxt.BorderWidth = 0; }
            table2.AddCell(infoTxt);

            return table2;
        }
        PdfPTable addProprtiyTo_Cell(PdfPTable table2, PdfPCell infoTxt, PropertyItemText loc, CardsSettingForTable cardsetting, string value)
        {
            iTextSharp.text.Font font = GetFont_Proprty_item(loc);
            iTextSharp.text.Font font_title = GetFont_Proprty_item_title(loc);
            infoTxt = new PdfPCell(new Phrase(loc.title_text, font_title));
            infoTxt.HorizontalAlignment = 1;

            if (cardsetting.Show_border_Midell) { infoTxt.BorderWidth = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Coulum) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthRight = cardsetting.size_Border_Midell; infoTxt.BorderWidthLeft = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Row) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthBottom = cardsetting.size_Border_Midell; }
            else { infoTxt.BorderWidth = 0; }
            infoTxt.Padding = cardsetting.Padding_Cell;
            //infoTxt.Padding = 2.5f;
            table2.AddCell(infoTxt);

            infoTxt = new PdfPCell(new Phrase(value, font));
            infoTxt.HorizontalAlignment = 1;

            if (cardsetting.Show_border_Midell) { infoTxt.BorderWidth = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Coulum) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthRight = cardsetting.size_Border_Midell; infoTxt.BorderWidthLeft = cardsetting.size_Border_Midell; }
            else if (cardsetting.Show_Border_Row) { infoTxt.BorderWidth = 0; infoTxt.BorderWidthBottom = cardsetting.size_Border_Midell; }
            else { infoTxt.BorderWidth = 0; }
            //infoTxt.Padding = 2.5f;
            infoTxt.Padding = cardsetting.Padding_Cell;

            table2.AddCell(infoTxt);
            return table2;
        }
        public iTextSharp.text.Font GetFont_Proprty_item(PropertyItemText items)
        {
            iTextSharp.text.Font font = FontFactory.GetFont(items.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            font.Size = items.font_size;
            if (items.Blod) { font.SetStyle("bold"); }
            if (items.italic) { font.SetStyle("italic"); }
            Color c = System.Drawing.ColorTranslator.FromHtml(items.Color);
            font.SetColor(c.R, c.G, c.B);
            return font;

        }
        public iTextSharp.text.Font GetFont_Proprty_item_title(PropertyItemText items)
        {
            iTextSharp.text.Font font = FontFactory.GetFont(items.Font, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            font.Size = items.font_size;
            if (items.Blod) { font.SetStyle("bold"); }
            if (items.italic) { font.SetStyle("italic"); }
            Color c = System.Drawing.ColorTranslator.FromHtml(items.title_Color);
            font.SetColor(c.R, c.G, c.B);
            return font;

        }
        private void Fiexd_Width_TableSpacing(Dictionary<string, NewUserToAdd> dicUsers, Dictionary<string, string> Cardsdata, CardsTableDesg1 tcard, string pathfile)
        {
            try
            {
                using (FileStream fs = new FileStream(pathfile, FileMode.Create))
                {

                    Document doc = new Document(PageSize.A4, 0f, 0f, 0f, 0f);
                    PdfWriter writer = PdfWriter.GetInstance(doc, fs);
                    doc.Open();
                    float UserName_X = iTextSharp.text.Utilities.MillimetersToPoints(0);
                    float UserName_Y = iTextSharp.text.Utilities.MillimetersToPoints(0);

                    float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.space_horizontal_margin.ToString()));
                    float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.Space_vertical_margin.ToString()));
                    float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
                    PdfPTable fTable = CreateTble(tcard, dicUsers.First(), Cardsdata);
                    float Pictur_height = fTable.TotalHeight;

                    float Pictur_width_orginal = Pictur_width;
                    float Pictur_height__orginal = Pictur_height;

                    float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
                    float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
                    float NumreicPage = iTextSharp.text.Utilities.MillimetersToPoints(40);

                    int NuberCards = dicUsers.Count;
                    int NumberCard_for_OneColum = 0;
                    int NumberCard_in_Page = 0;
                    double NumberPages = 0;

                    int ColumNumber = (int)(595 / (Pictur_width + Space_Y));
                    if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                        ColumNumber = ColumNumber - 1;

                    NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
                    if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                        NumberCard_for_OneColum = NumberCard_for_OneColum - 1;
                    NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

                    //string destDirectory = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";
                    string destDirectory = utils.Get_CardsBack_Directory();
                    iTextSharp.text.Image jpg = null;
                    if (tcard.setingCard.enable_background && tcard.setingCard.path_background != "")
                    {
                        string destImage = Path.Combine(destDirectory, tcard.setingCard.path_background);
                        try
                        {
                            //using (Bitmap b = new Bitmap(System.Drawing.Image.FromFile(destDirectory)))
                            //{
                            //    b.MakeTransparent(Color.White);
                            //    jpg = iTextSharp.text.Image.GetInstance(b, System.Drawing.Imaging.ImageFormat.Png); 
                            //}

                            jpg = iTextSharp.text.Image.GetInstance(destImage);
                            jpg.ScaleAbsolute(Pictur_width, Pictur_height);

                            //if (tcard.setingCard.quilty_image == 1)
                            //    jpg.SetDpi(300, 300);
                            //if (tcard.setingCard.card_border_enable)
                            //{
                            //    jpg.Border = iTextSharp.text.Rectangle.BOX;
                            //    jpg.BorderWidth = (float)(tcard.setingCard.card_border_Size);
                            //    Color c = System.Drawing.ColorTranslator.FromHtml(tcard.setingCard.card_border_Color.ToString());
                            //    jpg.BorderColor = ColorToBaseColor(c);
                            //}
                        }
                        catch (Exception ex) { MessageBox.Show(ex.Message); }
                    }
                    float Top_Bouttom_Hash = (doc.PageSize.Height - (((Space_X * NumberCard_for_OneColum) + (NumberCard_for_OneColum * Pictur_height)) - Space_X)) / 2;
                    float Right_Left_Hash = (doc.PageSize.Width - (((Space_Y * ColumNumber) + (ColumNumber * Pictur_width)) - Space_Y)) / 2;

                    float Right_Left_Hash_orginal = Right_Left_Hash;
                    NumberPages = NuberCards / NumberCard_in_Page;
                    if (NuberCards % NumberCard_in_Page != 0)
                    {
                        NumberPages = NuberCards / NumberCard_in_Page;
                        NumberPages = Convert.ToInt16(NumberPages) + 1;
                    }
                    float pic_height_Element_Y = Pictur_height;
                    float Right_Left_Hash_Element_Y = Right_Left_Hash;
                    int nc = 0;
                    float _6 = iTextSharp.text.Utilities.MillimetersToPoints(6);

                    PdfPTable table1 = new PdfPTable(2);
                    for (int n = 0; n < NumberPages; n++)
                    {
                        Right_Left_Hash = Right_Left_Hash_orginal;
                        float _UserName_X = UserName_X;

                        doc.NewPage();
                        for (int i = 0; i < ColumNumber; i++)
                        {
                            Pictur_height = Pictur_height__orginal;
                            pic_height_Element_Y = 0;
                            float _UserName_Y = UserName_Y;



                            for (int j = 0; j < NumberCard_for_OneColum; j++)
                            {
                                try
                                {
                                    if (tcard.setingCard.enable_background)
                                    {
                                        jpg.SetAbsolutePosition((Right_Left_Hash), ((doc.PageSize.Height - Pictur_height) - Top_Bouttom_Hash));
                                        doc.Add(jpg);
                                    }
                                }
                                catch { }
                                if (tcard.setingCard.card_border_enable)
                                {

                                    PdfContentByte cb = writer.DirectContent;
                                    cb.Rectangle(Right_Left_Hash, ((doc.PageSize.Height - Pictur_height) - Top_Bouttom_Hash), Pictur_width_orginal, Pictur_height__orginal);
                                    Color c = System.Drawing.ColorTranslator.FromHtml(tcard.setingCard.card_border_Color.ToString());
                                    CMYKColor sd = utils.ConvertRgbToCmyk(c.R, c.G, c.B);
                                    cb.SetColorStroke(sd);
                                    //cb.SetCMYKColorStroke=sd;
                                    //cb.SetCMYKColorFill(0, 255, 255, 0);
                                    cb.SetLineWidth(tcard.setingCard.card_border_Size);
                                    cb.Stroke();
                                }
                                Pictur_height = (Pictur_height) + Pictur_height__orginal + Space_X;

                                if (tcard.cardsItems.login.Enable)
                                {
                                    float x = (Right_Left_Hash_Element_Y + _UserName_X);
                                    float y = (doc.PageSize.Height - (pic_height_Element_Y - _UserName_Y) - Top_Bouttom_Hash);




                                    PdfPTable table = CreateTble(tcard, dicUsers.ElementAt(i), Cardsdata);
                                    table.WriteSelectedRows(0, table.Rows.Count, x, y, writer.DirectContent);


                                }

                                pic_height_Element_Y = (pic_height_Element_Y) + Pictur_height__orginal + Space_X;
                                nc++;
                                if (nc == NuberCards)
                                    goto endc;
                            }
                            _UserName_X = (_UserName_X) + Pictur_width_orginal + Space_Y;
                            Right_Left_Hash = Right_Left_Hash + Pictur_width_orginal + Space_Y;

                            //========================

                        }

                    endc:;
                        if (tcard.setingCard.Note_On_Pages)
                        {
                            //A4: 210mm x 297mm(794px x 1123px)
                            float x = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Note_On_Pages_X);
                            float y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Note_On_Pages_Y);
                            PdfContentByte cb = writer.DirectContent;
                            iTextSharp.text.Font font = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                            font.Size = Convert.ToInt16(tcard.setingCard.Note_On_Pages_Size);
                            ColumnText ct = new ColumnText(cb);
                            string text = Cardsdata["Note_On_Pages_text"];
                            Chunk chunk = new Chunk(text, font);
                            ColumnText ctt = new ColumnText(cb);
                            ctt.AddText(chunk);
                            ctt.SetSimpleColumn(x, y + _6, x + chunk.GetWidthPoint() + 1, 0);
                            ctt.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            ctt.Go();

                        }
                        if (tcard.setingCard.Number_Pages)
                        {
                            PdfContentByte cb = writer.DirectContent;
                            float x = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Number_Pages_X);
                            float y = iTextSharp.text.Utilities.MillimetersToPoints((float)tcard.setingCard.Number_Pages_Y);
                            iTextSharp.text.Font font = FontFactory.GetFont("Arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                            font.Size = Convert.ToInt16(tcard.setingCard.Number_Pages_Size);

                            ColumnText ct = new ColumnText(cb);
                            string numTxt = (String.Format("{0} of {1}", n + 1, NumberPages));

                            Chunk chunk = new Chunk(numTxt, font);
                            ct.SetSimpleColumn(x, y + _6, x + chunk.GetWidthPoint() + 1, 0);
                            ct.AddText(chunk);
                            //ct.RunDirection = PdfWriter.RUN_DIRECTION_LTR;
                            ct.Alignment = Element.ALIGN_LEFT;
                            ct.Go();

                        }

                        if (nc == NuberCards)
                            goto la;

                    }
                la:;
                    doc.Close();

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }
        private void createTB(Document doc, PdfPTable table, int n)
        {
            Paragraph paragraphTable1 = new Paragraph();
            paragraphTable1.SpacingAfter = 1f;

            table = new PdfPTable(3);
            table.TotalWidth = 145;
            table.LockedWidth = true;
            PdfPCell cell = new PdfPCell(new Phrase("This is table " + n));
            cell.Colspan = 3;
            cell.HorizontalAlignment = 1;
            table.AddCell(cell);
            table.AddCell("Col 1 Row 1");
            table.AddCell("Col 2 Row 1");
            table.AddCell("Col 3 Row 1");
            //table.AddCell("Col 1 Row 2"); 
            //table.AddCell("Col 2 Row 2"); 
            //table.AddCell("Col 3 Row 2"); 
            paragraphTable1.Add(table);
            doc.Add(paragraphTable1);

            float h = CalculatePdfPTableHeight(table);
            MessageBox.Show(h.ToString() + "    hiheh");

        }
        public float CalculatePdfPTableHeight(PdfPTable table)
        {
            try
            {
                using (MemoryStream ms = new MemoryStream())
                {
                    using (Document doc = new Document(PageSize.TABLOID))
                    {
                        using (PdfWriter w = PdfWriter.GetInstance(doc, ms))
                        {
                            doc.Open();
                            doc.NewPage();
                            table.WriteSelectedRows(0, table.Rows.Count, 0, 0, w.DirectContent);

                            doc.Close();
                            return table.TotalHeight;
                        }
                    }
                }
            }
            catch (Exception e) { MessageBox.Show(e.Message); }
            return table.TotalHeight;
        }

        public float CalculatePdfPTableHeight2(PdfPTable table)
        {
            Document doc = new Document(iTextSharp.text.PageSize.LETTER, 10, 10, 42, 35);

            byte[] pdfBytes;
            using (var mem = new MemoryStream())
            {
                using (PdfWriter wri = PdfWriter.GetInstance(doc, mem))
                {
                    doc.Open();//Open Document to write
                    Paragraph paragraph = new Paragraph("This is my first line using Paragraph.");
                    Phrase pharse = new Phrase("This is my second line using Pharse.");
                    Chunk chunk = new Chunk(" This is my third line using Chunk.");

                    doc.Add(paragraph);

                    doc.Add(pharse);

                    doc.Add(chunk);
                    table.WriteSelectedRows(0, table.Rows.Count, 0, 0, wri.DirectContent);
                    return table.TotalHeight;
                }
                pdfBytes = mem.ToArray();
            }
        }
        float getHieht_Table(PdfPTable table)
        {
            float total_H = 0;
            // added
            table.TotalWidth = 400f;
            int i = 0;
            foreach (var r in table.GetRows(0, 10))
            {
                total_H = total_H + table.GetRowHeight(i);
                i++;
            }
            return total_H;
            // table.GetRows(0,10);
            ////
            //PdfPRow firstRow = new PdfPRow(cells1.ToArray());

            //table.Rows.Add(firstRow);
            //PdfPRow secondRow = new PdfPRow(cells2.ToArray());
            //table.Rows.Add(secondRow);

            //float h1 = table.GetRowHeight(0), h2 = table.GetRowHeight(1);

            //if (currentY - h1 - h2 < 30) document.NewPage();

            //document.Add(table);
        }

        public void Print_To_Pdf_table1(Dictionary<string, NewUserToAdd> dicUsers, Dictionary<string, string> Cardsdata, CardsTableDesg1 tcard, string pathfile)
        {
            if (tcard.setingCard.Fixed_Width_Card)
                Fiexd_Width_TableSpacing(dicUsers, Cardsdata, tcard, pathfile);
            else
                createTable_by_table_Page(dicUsers, Cardsdata, tcard, pathfile);
        }
        private void createTable_by_table_Page(Dictionary<string, NewUserToAdd> dicUsers, Dictionary<string, string> Cardsdata, CardsTableDesg1 tcard, string pathfile)
        {
            //============Table  out ====================
            Document doc = new Document(PageSize.A4, 0f, 0f, 0f, 0f);
            try
            {
                using (MemoryStream fs = new MemoryStream())
                {
                    PdfWriter writer = PdfWriter.GetInstance(doc, fs);
                    doc.Open();
                    float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
                    float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_height.ToString()));

                    float Pictur_width_orginal = Pictur_width;
                    float Pictur_height__orginal = Pictur_height;

                    //string destDirectory = Directory.GetCurrentDirectory() + "\\tempCards\\cardsBack";
                    string destDirectory = utils.Get_CardsBack_Directory();
                    iTextSharp.text.Image jpg = null;
                    if (tcard.setingCard.enable_background && tcard.setingCard.path_background != "")
                    {
                        string destImage = Path.Combine(destDirectory, tcard.setingCard.path_background);
                        try
                        {
                            jpg = iTextSharp.text.Image.GetInstance(destImage);
                            jpg.ScaleAbsolute(Pictur_width, Pictur_height);
                        }
                        catch (Exception ex) { MessageBox.Show(ex.Message); }
                    }

                    //============ Table  out ====================
                    PdfPTable table = new PdfPTable(tcard.setingCard.NumberCulum);
                    table.TotalWidth = 590f;
                    table.LockedWidth = true;
                    table.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                    table.DefaultCell.BorderWidth = 0;
                    //table.PaddingTop = 0;
                    table.SpacingAfter = 0;
                    table.SpacingBefore = 0;
                    //table.DefaultCell.Padding = 0;

                    //table.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                    //PdfPCell cell = new PdfPCell();

                    for (int i = 0; i < dicUsers.Count(); i++)
                    {
                        PdfPTable table2 = CreateTble(tcard, dicUsers.ElementAt(i), Cardsdata);
                        table2.WidthPercentage = 100f;
                        var cell = new PdfPCell();
                        cell.PaddingBottom = 5;
                        //cell.PaddingBottom = (float)tcard.setingCard.space_horizontal_margin;
                        //cell.PaddingRight = (float)tcard.setingCard.Space_vertical_margin;
                        cell.PaddingRight = 0;
                        cell.PaddingLeft = 0;
                        cell.PaddingTop = 5;
                        if (tcard.setingCard.card_border_enable) cell.BorderWidth = tcard.setingCard.card_border_Size;
                        else cell.BorderWidth = 0;
                        if (tcard.setingCard.enable_background)
                        {
                            jpg.ScaleToFit(table2.TotalWidth, table2.TotalHeight);
                            jpg.Alignment = iTextSharp.text.Image.ALIGN_JUSTIFIED;
                            cell.AddElement(jpg);

                            //cel.Image.ScaleToFit(table2.TotalWidth, table2.TotalHeight);
                            //cel.Image.ScaleToFitLineWhenOverflow = true;
                        }

                        //================================================================================================= 

                        cell.AddElement(table2);
                        //table.DefaultCell.Padding = 0;
                        //table.DefaultCell.PaddingBottom = (float)tcard.setingCard.space_horizontal_margin;
                        //table.DefaultCell.PaddingRight = (float)tcard.setingCard.Space_vertical_margin;
                        //table.AddCell(cell);
                        table.AddCell(table2);
                    }
                  
                    
                    if ((tcard.setingCard.NumberCulum) == 2 && (dicUsers.Count() % 2) != 0)
                    {

                        table.AddCell("");
                    }
                    if ((tcard.setingCard.NumberCulum) == 3 && (dicUsers.Count() % 3) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                    }
                    if ((tcard.setingCard.NumberCulum) == 4 && (dicUsers.Count() % 4) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                    }
                    if ((tcard.setingCard.NumberCulum) == 5 && (dicUsers.Count() % 5) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                    }
                    if ((tcard.setingCard.NumberCulum) == 6 && (dicUsers.Count() % 6) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");

                    }
                    if ((tcard.setingCard.NumberCulum) == 7 && (dicUsers.Count() % 7) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");

                    }
                    if ((tcard.setingCard.NumberCulum) == 8 && (dicUsers.Count() % 8) != 0)
                    {
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");
                        table.AddCell("");

                    }

                    //table.DefaultCell.Padding = 0;
                    doc.Add(table);
                    doc.Close();

                    Byte[] bytes;
                    //MemoryStream fs = new MemoryStream();
                    bytes = fs.ToArray();
                    using (var reader = new PdfReader(bytes))
                    {
                        using (var ms = new MemoryStream())
                        {
                            using (var stamper = new PdfStamper(reader, ms))
                            {
                                int PageCount = reader.NumberOfPages;
                                for (int i = 1; i <= PageCount; i++)
                                {

                                    if ((tcard.setingCard.Number_Pages))
                                    {
                                        iTextSharp.text.Font font = FontFactory.GetFont("arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                        font.Size = ((int)tcard.setingCard.Number_Pages_Size);
                                        ColumnText.ShowTextAligned(stamper.GetOverContent(i), Element.ALIGN_CENTER, new Phrase(String.Format("{0} of {1}", i, PageCount), font),
                                            Convert.ToInt16(tcard.setingCard.Number_Pages_X),
                                            Convert.ToInt16(tcard.setingCard.Number_Pages_Y), 0);
                                    }
                                    if (tcard.setingCard.Note_On_Pages)
                                    {
                                        iTextSharp.text.Font font = FontFactory.GetFont("arial", BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
                                        font.Size = ((int)tcard.setingCard.Note_On_Pages_Size);
                                        ColumnText.ShowTextAligned(stamper.GetOverContent(i), Element.ALIGN_CENTER, new Phrase(Cardsdata["Note_On_Pages_text"], font),
                                            Convert.ToInt16(tcard.setingCard.Note_On_Pages_X),
                                            Convert.ToInt16(tcard.setingCard.Note_On_Pages_Y), 0);

                                    }
                                }
                            }
                            bytes = ms.ToArray();
                        }
                    }

                    //var outputFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "t5.pdf");
                    //var outputFile = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "t5.pdf");
                    System.IO.File.WriteAllBytes(pathfile, bytes);



                }
            }
            catch (Exception ex) { MessageBox.Show("createTable_by_table_Page\n"+ex.Message); }

        }


        public static BaseColor ColorToBaseColor(Color color)
        {

            return new BaseColor(color);

        }
        void loadFont_System()
        {
            int totalfonts = FontFactory.RegisterDirectory("C:\\WINDOWS\\Fonts");
            StringBuilder sb = new StringBuilder();
            foreach (string fontname in FontFactory.RegisteredFonts)
            {
                //sb.Append(fontname + "\n");
                //CB_Fonts.Items.Add(fontname);
            }
        }




        public static void print_pdf(Dictionary<string, NewUserToAdd> dicUsers, Clss_InfoPrint clss_InfoPrint, CardsTemplate card = null, CardsTableDesg1 cardTable1 = null)
        {

            //UmProfile profile = clss_InfoPrint.profile;
            Smart_DataAccess Smart_DA = new Smart_DataAccess();

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = clss_InfoPrint.profile.Name;
            string price = clss_InfoPrint.profile.Price_Disply;
            string Validity = clss_InfoPrint.profile.Validity.ToString();
            string time = clss_InfoPrint.profile.UptimeLimit.ToString();

            string sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
            string SP = "";
            string numberPrintId = "81";
            string batchNumberId = "81";
            string DatePrint = "105";
            string Note_On_Pages_text = "";


            if (card != null)
            {
                //card = new CardsTemplate();
                //card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);
                profileName = card.name;
                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    try
                    {
                        Validity = (clss_InfoPrint.profile.Validity.ToString());
                        if (clss_InfoPrint.profile.Validity > 0)
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(clss_InfoPrint.profile.Validity.ToString(), card.cardsItems.Validity);
                        else
                            Validity = "مفتوح";
                    }
                    catch { }
                }
                if (card.cardsItems.Time.Enable)
                {
                    time = clss_InfoPrint.profile.UptimeLimit.ToString();
                    try
                    {
                        if (clss_InfoPrint.profile.UptimeLimit > 0)
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(clss_InfoPrint.profile.UptimeLimit, card.cardsItems.Time);
                        else
                            time = "مفتوح";
                    }
                    catch { }

                }
                if (card.cardsItems.Size.Enable)
                {
                    sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
                    if (clss_InfoPrint.profile.TransferLimit > 0)
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size);
                    else
                        sizeTransfer = "مفتوح";
                }

                if (card.cardsItems.BatchNumber.Enable)
                {
                    if (batchNumberId != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            batchNumberId = card.cardsItems.Number_Print.title_text + " " + batchNumberId;
                        }
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrintId != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrintId = card.cardsItems.Number_Print.title_text + " " + numberPrintId;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.get_BatchCards_my_sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();  
                    numberPrintId = (batchNumber + 1).ToString();
                }
                if (card.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (Show_sp.Code).ToString();
                        else
                            SP = (Show_sp.UserName).ToString();
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else if (cardTable1 != null)
            {
                //cardTable1 = new CardsTableDesg1();
                //cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);
                profileName = cardTable1.name;
                if (cardTable1.cardsItems.Price.Enable)
                {
                    price = clss_InfoPrint.profile.Price_Disply;
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = clss_InfoPrint.profile.Price_Disply + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    try
                    {
                        Validity = (clss_InfoPrint.profile.Validity.ToString());
                        if (clss_InfoPrint.profile.Validity > 0)
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(clss_InfoPrint.profile.Validity.ToString(), cardTable1.cardsItems.Validity);
                        else
                            Validity = "مفتوح";
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    time = clss_InfoPrint.profile.UptimeLimit.ToString();
                    try
                    {
                        if (clss_InfoPrint.profile.UptimeLimit > 0)
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(clss_InfoPrint.profile.UptimeLimit, cardTable1.cardsItems.Time);
                        }
                        else
                            time = "مفتوح";
                    }
                    catch { }

                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
                    if (clss_InfoPrint.profile.TransferLimit > 0)
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size);
                    else
                        sizeTransfer = "مفتوح";
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.BatchNumber.Enable)
                {
                    try
                    {
                        int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                        batchNumberId = (batchNumber + 1).ToString();
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    if (numberPrintId != "")
                    {
                        //if (cardTable1.cardsItems.Number_Print.title_show)
                        //{
                            //numberPrintId =  numberPrintId;
                            //numberPrintId = cardTable1.cardsItems.Number_Print.title_text + " " + numberPrintId;
                        //}
                    }
                }
                if (cardTable1.cardsItems.SP.Enable)
                {
                    try
                    {
                        if (clss_InfoPrint.SellingPoint != null)
                        //if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != null)
                        {
                            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                            //SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                            //if (Show_sp != null)
                            {
                                if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                                    SP = (clss_InfoPrint.SellingPoint.Code).ToString();
                                else
                                    SP = (clss_InfoPrint.SellingPoint.UserName).ToString();
                            }
                            if (card.cardsItems.SP.title_show)
                            {
                                SP = card.cardsItems.SP.title_text + " " + SP;
                            }
                        }
                    }
                    catch { }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (clss_InfoPrint.SellingPoint.Code).ToString();
                        else
                            SP = (clss_InfoPrint.SellingPoint.UserName).ToString();

                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("batchNumber", batchNumberId);
            Cardsdata.Add("numberPrint", numberPrintId);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", clss_InfoPrint.pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (card != null)
                print.Print_To_Pdf(dicUsers, Cardsdata, card, clss_InfoPrint.pathfile);

            else if (cardTable1 != null)
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, clss_InfoPrint.pathfile);
            try
            {
                System.Diagnostics.Process.Start(clss_InfoPrint.pathfile);
            }
            catch { }

            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            //MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            //try
            //{
            //    System.Diagnostics.Process.Start(pathfile);
            //}
            //catch { }
        }


    }

    public class NewUserToAdd
    {
        public double SN { get; set; }
        public long? SN_Archive { get; set; }
        public string Name { get; set; }
        public string Password { get; set; }
        public string Profile { get; set; }
        public int? PageNumber { get; set; }
    }
    public class variablePrint
    {
        public CardsTemplate cardsTemplate { get; set; }
        public string Number_Cards_ToAdd { get; set; }
        public string[,] Users { get; set; }
        public string ProfileName { get; set; }
        public string Price { get; set; }
        public string Uptime { get; set; }
        public string Size_download { get; set; }
        public string Validity { get; set; }
    }

}
