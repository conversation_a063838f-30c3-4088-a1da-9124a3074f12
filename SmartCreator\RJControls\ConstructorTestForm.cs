using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار الـ constructors المختلفة لـ RJTabPage
    /// </summary>
    public class ConstructorTestForm : Form
    {
        public ConstructorTestForm()
        {
            InitializeForm();
            TestConstructors();
        }

        private void InitializeForm()
        {
            this.Text = "اختبار Constructors - RJTabPage";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void TestConstructors()
        {
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 45,
                TabSpacing = 5,
                TabPadding = 25
            };

            // اختبار Constructor الأساسي
            var tab1 = new RJTabPage();
            tab1.Text = "تاب أساسي";
            tab1.BackColor = Color.FromArgb(200, 200, 200);
            tabControl.AddTab(tab1);

            var label1 = new Label
            {
                Text = "🔧 تم إنشاؤه بـ Constructor الأساسي:\n\n" +
                       "new RJTabPage()\n" +
                       "tab.Text = \"تاب أساسي\"",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70)
            };
            tab1.AddControl(label1);

            // اختبار Constructor مع النص
            var tab2 = new RJTabPage("تاب بنص");
            tab2.BackColor = Color.FromArgb(0, 122, 204);
            tab2.ForeColor = Color.White;
            tabControl.AddTab(tab2);

            var label2 = new Label
            {
                Text = "📝 تم إنشاؤه بـ Constructor النص:\n\n" +
                       "new RJTabPage(\"تاب بنص\")\n\n" +
                       "النص يتم تعيينه تلقائياً!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White
            };
            tab2.AddControl(label2);

            // اختبار Constructor مع النص والأيقونة
            var tab3 = new RJTabPage("تاب مع أيقونة", IconChar.Home);
            tab3.BackColor = Color.FromArgb(76, 175, 80);
            tab3.ForeColor = Color.White;
            tab3.IconSize = 24;
            tabControl.AddTab(tab3);

            var label3 = new Label
            {
                Text = "🏠 تم إنشاؤه بـ Constructor النص والأيقونة:\n\n" +
                       "new RJTabPage(\"تاب مع أيقونة\", IconChar.Home)\n\n" +
                       "النص والأيقونة يتم تعيينهما تلقائياً!\n" +
                       "IconSize = 24",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White
            };
            tab3.AddControl(label3);

            // اختبار Constructor متقدم
            var tab4 = new RJTabPage("تاب متقدم", IconChar.Cogs);
            tab4.BackColor = Color.FromArgb(156, 39, 176);
            tab4.ForeColor = Color.White;
            tab4.IconSize = 20;
            tab4.BorderRadius = 12;
            tab4.Style = ControlStyle.Glass;
            tabControl.AddTab(tab4);

            var panel4 = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 3,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 15,
                Padding = new Padding(20)
            };

            var label4 = new Label
            {
                Text = "⚙️ تاب متقدم مع تخصيصات:\n\n" +
                       "• Constructor: new RJTabPage(\"تاب متقدم\", IconChar.Cogs)\n" +
                       "• BackColor = بنفسجي\n" +
                       "• IconSize = 20\n" +
                       "• BorderRadius = 12\n" +
                       "• Style = Glass\n" +
                       "• RJPanel مع حدود ملونة\n\n" +
                       "جميع ميزات RJButton متاحة! 🎨",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176)
            };
            panel4.Controls.Add(label4);
            tab4.AddControl(panel4);

            // تاب المعلومات
            var infoTab = new RJTabPage("معلومات", IconChar.InfoCircle);
            infoTab.BackColor = Color.FromArgb(255, 152, 0);
            infoTab.ForeColor = Color.White;
            infoTab.IconSize = 22;
            tabControl.AddTab(infoTab);

            var infoTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "ℹ️ معلومات الـ Constructors:\n\n" +
                       "✅ تم إصلاح خطأ التكرار!\n\n" +
                       "الـ Constructors المتاحة:\n\n" +
                       "1️⃣ RJTabPage()\n" +
                       "   - Constructor أساسي\n" +
                       "   - يحتاج تعيين النص يدوياً\n\n" +
                       "2️⃣ RJTabPage(string text)\n" +
                       "   - Constructor مع النص\n" +
                       "   - النص يتم تعيينه تلقائياً\n\n" +
                       "3️⃣ RJTabPage(string text, IconChar icon)\n" +
                       "   - Constructor مع النص والأيقونة\n" +
                       "   - النص والأيقونة يتم تعيينهما تلقائياً\n\n" +
                       "🎉 جميع الـ Constructors تعمل بشكل مثالي!\n" +
                       "🚀 RJTabPage ترث من RJButton بنجاح!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(255, 152, 0),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            infoTab.AddControl(infoTextBox);

            // تاب الاختبار التفاعلي
            var interactiveTab = new RJTabPage("تفاعلي", IconChar.Play);
            interactiveTab.BackColor = Color.FromArgb(244, 67, 54);
            interactiveTab.ForeColor = Color.White;
            tabControl.AddTab(interactiveTab);

            var interactivePanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var createTabButton = new RJButton
            {
                Text = "إنشاء تاب جديد",
                IconChar = IconChar.Plus,
                Location = new Point(20, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };

            createTabButton.Click += (s, e) => {
                var tabNumber = tabControl.TabCount + 1;
                var icons = new[] { IconChar.Star, IconChar.Heart, IconChar.Lightbulb, IconChar.Rocket, IconChar.Gift };
                var colors = new[] { 
                    Color.FromArgb(233, 30, 99),
                    Color.FromArgb(103, 58, 183),
                    Color.FromArgb(63, 81, 181),
                    Color.FromArgb(0, 150, 136),
                    Color.FromArgb(139, 195, 74)
                };

                var random = new Random();
                var newTab = new RJTabPage($"تاب {tabNumber}", icons[random.Next(icons.Length)]);
                newTab.BackColor = colors[random.Next(colors.Length)];
                newTab.ForeColor = Color.White;
                newTab.IconSize = 18;

                var newLabel = new Label
                {
                    Text = $"🎉 تاب جديد رقم {tabNumber}!\n\n" +
                           "تم إنشاؤه ديناميكياً باستخدام:\n" +
                           $"new RJTabPage(\"تاب {tabNumber}\", IconChar.Random)\n\n" +
                           "جميع الـ Constructors تعمل بشكل مثالي! 🚀",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(50, 50, 50)
                };
                newTab.AddControl(newLabel);
                tabControl.AddTab(newTab);
            };

            var instructionLabel = new Label
            {
                Text = "🎮 اضغط الزر لإنشاء تاب جديد ديناميكياً!\n\n" +
                       "سيتم استخدام Constructor مع النص والأيقونة.",
                Location = new Point(20, 90),
                Size = new Size(400, 60),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            interactivePanel.Controls.Add(createTabButton);
            interactivePanel.Controls.Add(instructionLabel);
            interactiveTab.AddControl(interactivePanel);

            this.Controls.Add(tabControl);
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new ConstructorTestForm();
            form.ShowDialog();
        }
    }
}
