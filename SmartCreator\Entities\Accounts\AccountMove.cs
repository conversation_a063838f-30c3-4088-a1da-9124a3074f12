﻿using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Forms.Accounting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
//using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounts
{
//# ==============================================================================================
//#                                     JOURNAL ENTRY
//# ==============================================================================================
    public class AccountMove
    {
        //# =================== Parent fields ============ #
        [Required, PrimaryKey]
        public int Id { get; set; }
        public string Code { get; set; }
        [NotMapped]
        public AccountMove Parent { get; set; } = null;
        public int? ParentId { get; set; } = null;
        //public string Str_ParentId
        //{

        //    get
        //    {
        //        string txt = AccountTypeChoices.Where(p => p.Value == AccountType.ToString()).First().Text;
        //        return txt;
        //    }
        //}

        [DisplayName("التسلسل")]
        public double Sequence { get; set; } = 0;
        [DisplayName("التاريخ")]
        public DateTime Date { get; set; } = DateTime.Now;
        [DisplayName("المرجع/البيــان")]
        public string Ref { get; set; }

        //# ============== Accountable fields ============== #

        [DisplayName("مدين")]
        public double debit { get; set; } = 0;
        [DisplayName("دائن")]
        public double credit { get; set; } = 0;
        //# ==============================================================================================
        //#                              INVOICE
        //# ==============================================================================================
        [DisplayName("المنتج")]
        public int? ProductId {  get; set; }=null;
        [DisplayName("وحدة القياس")]
        public int? Product_uomId {  get; set; }=null;
        [DisplayName("الكمية")]
        public int Quantity { get; set; } = 0;
        //      # === Price fields === #
        public double Price_unit { get; set; } = 0;
        public double Price_total { get; set; } = 0;
        public int Discount { get; set; } = 0;
        public string Move_type { get;set; }

        [NotMapped]
        public  List<SelectListItem> Move_typeChoice = new List<SelectListItem>
        {
            new SelectListItem { Value = "out_invoice", Text = "فاتورة العميل" },
            new SelectListItem { Value = "out_refund", Text = "إشعار دائن للعميل" },
            new SelectListItem { Value = "in_refund", Text = "إشعار المورد الدائن" },
            new SelectListItem { Value = "out_receipt", Text = "إيصال المبيعات" },
            new SelectListItem { Value = "in_receipt", Text = "إيصال الشراء" }
        };

        [DisplayName("خزينة - دفع")]
        public int? PartnerId { get; set; } = null;
        public int? AccountId { get; set; }=null;

        public string Rb {  get; set; }

        public List<AccountMove> Details { get; set; }

    }
}
