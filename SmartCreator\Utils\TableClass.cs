﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Utils
{
    public class TableClass
    {
        private List<KeyValuePair<String, Type>> _fieldInfo = new List<KeyValuePair<String, Type>>();
        private string _className = String.Empty;
        private Type _typeTable = null;
        private bool _WITHOUTROWID = false;

        private Dictionary<Type, String> dataMapper
        {
            get
            {
                // Add the rest of your CLR Types to SQL Types mapping here
                Dictionary<Type, String> dataMapper = new Dictionary<Type, string>();
                //dataMapper.Add(typeof(int), "BIGINT");
                //dataMapper.Add(typeof(string), "NVARCHAR(500)");
                //dataMapper.Add(typeof(bool), "BIT");
                //dataMapper.Add(typeof(DateTime), "DATETIME");
                //dataMapper.Add(typeof(float), "FLOAT");
                //dataMapper.Add(typeof(decimal), "DECIMAL(18,0)");
                //dataMapper.Add(typeof(Guid), "UNIQUEIDENTIFIER");


                dataMapper.Add(typeof(int), "INTEGER");
                dataMapper.Add(typeof(long), "INTEGER");
                dataMapper.Add(typeof(string), "TEXT");
                dataMapper.Add(typeof(bool), "INTEGER");
                dataMapper.Add(typeof(DateTime), "TEXT");
                dataMapper.Add(typeof(float), "DOUBLE");
                dataMapper.Add(typeof(decimal), "DOUBLE");
                //dataMapper.Add(typeof(Guid), "UNIQUE");

                return dataMapper;
            }
        }

        public List<KeyValuePair<String, Type>> Fields
        {
            get { return this._fieldInfo; }
            set { this._fieldInfo = value; }
        }

        public string ClassName
        {
            get { return this._className; }
            set { this._className = value; }
        }
        public Type TypeTable
        {
            get { return this._typeTable; }
            set { this._typeTable = value; }
        }

        public bool WITHOUTROWID
        {
            get { return this._WITHOUTROWID; }
            set { this._WITHOUTROWID = value; }
        }

        public TableClass(Type t, bool WITHOUTROWID = false)
        {
            this._className = t.Name;
            this._typeTable = t;    
            this._WITHOUTROWID = WITHOUTROWID;    
            Type tt = t;
            foreach (PropertyInfo p in t.GetProperties())
            {
                bool comput = false;
                KeyValuePair<String, Type> field = new KeyValuePair<String, Type>(p.Name, p.PropertyType);
                var w = t.GetProperty(p.Name).GetCustomAttributes(false).ToDictionary(a => a.GetType().Name, a => a);
                foreach (var v in w)
                {
                    if (v.Key == "ComputedAttribute")
                        comput = true;
                    //var d = v.Value.ToObjectDictionary();
                }
                if (comput == false)
                    this.Fields.Add(field);
                comput = false;
            }
        }

        public string CreateTableScript(bool WITHOUTROWID = true)
        {

            this._WITHOUTROWID = WITHOUTROWID;

            var ordertest = this.Fields.OrderBy(x => x.Key);

            System.Text.StringBuilder script = new StringBuilder();
            System.Text.StringBuilder script2 = new StringBuilder();
            System.Text.StringBuilder IndexConstraintClass = new StringBuilder();
            System.Text.StringBuilder ForeignKeyConstraintClass = new StringBuilder();
            //List<string> IndexConstraintClass = new List<string>();
            //List<string> ForeignKeyConstraintClass = new List<string>();
            //List<string> ForeignKeyConstraintClass = new List<string>();
            
            script.AppendLine("CREATE TABLE IF NOT EXISTS " + this.ClassName);
            script.AppendLine("(");
            //script.AppendLine("\t \"Id\" INTEGER,");
            for (int i = 0; i < this.Fields.Count; i++)
            {
                KeyValuePair<String, Type> field = this.Fields[i];
                if (dataMapper.ContainsKey(field.Value))
                {
                    string colum = $"\t\"{field.Key}\" {dataMapper[field.Value]}";
                    string option = Get_attr_colum(this.TypeTable, field.Key);                  
                    script.Append(colum + " " + option);

                    //============== foregin key =============
                    string ForeignKey_option = Get_attr_colum(this.TypeTable, field.Key, true, "ForeignKey");
                    if (ForeignKey_option != "")
                    {
                        ForeignKeyConstraintClass.AppendLine(","+ForeignKey_option);
                    }
                    //=================== Index   ===============
                    //string Index_option = Get_attr_colum(this.TypeTable, field.Key, true, "Index");
                    //if (Index_option != "")
                    //    IndexConstraintClass.AppendLine(Index_option);
                }
                else
                {
                    // Complex Type? 
                    if (field.Value.ToString().Contains("System.Int32") || field.Value.ToString().Contains("System.Int64"))
                        script.Append($"\t\"{field.Key}\" INTEGER");
                    else if (field.Value.ToString().Contains("System.DateTime"))
                        script.Append($"\t\"{field.Key}\" TEXT");
                    else
                        script.Append($"\t\"{field.Key}\" INTEGER");
                }
                if (i != this.Fields.Count - 1)
                {
                    script.Append(",");
                }
                script.Append(Environment.NewLine);
            }

            //    script.Append(ForeignKeyConstraintClass);

            //if (this._WITHOUTROWID)
            //    script.Append(") WITHOUT ROWID;");
            //else
            //    script.AppendLine(");");

            //script.Append(Environment.NewLine);
            //    script.Append(IndexConstraintClass);



            ////============== foregin key =============

            //for (int i = 0; i < this.Fields.Count; i++)
            //{
            //    KeyValuePair<String, Type> field = this.Fields[i];
            //    string option = Get_attr_colum(this.TypeTable, field.Key, true, "ForeignKey");
            //    if (option != "")
            //        script.AppendLine(", "+option);

            //}
            //============== end table create ============
            //if (this._WITHOUTROWID)
            //    script.AppendLine(") WITHOUT ROWID;");
            //else
            //    script.AppendLine(");");

            ////=================== Index   ===============
            //for (int i = 0; i < this.Fields.Count; i++)
            //{
            //    KeyValuePair<String, Type> field = this.Fields[i];
            //    string option = Get_attr_colum(this.TypeTable, field.Key, true, "Index");
            //    if (option != "")
            //        script.AppendLine(option);

            //    //option = Get_attr_colum(typeof(T), field.Key, true, "PrimaryKey");
            //    //if (option != "")
            //    //    script.AppendLine(option);

            //}
            //=================== attribut  class ===============
            List<string> ClassIndexConstraintClass = new List<string>();

            DataTable dt = new DataTable();
            dt.Columns.Add("Name", typeof(string));
            dt.Columns.Add("Attribute", typeof(System.Attribute));
            var attrsa = System.Attribute.GetCustomAttributes(this.TypeTable);
            foreach(System.Attribute attribute in attrsa)
            {
                var row = dt.NewRow();
                row[0] = attribute.GetType().Name;
                row[1] = attribute;
                dt.Rows.Add(row);
            }

            var attrs33 = System.Attribute.GetCustomAttributes(this.TypeTable);

            //var attrs = System.Attribute.GetCustomAttributes(this.TypeTable).ToDictionary(a => a.GetType(), a => a);  // Reflection.
            string UniqueConstraintClass = " ";
            foreach (DataRow attr in dt.Rows)
            {
                if (attr[0].ToString() == "UniqueConstraintAttribute")
                {
                    script.Append(Environment.NewLine);
                    UniqueConstraintAttribute uniq = attr[1]  as UniqueConstraintAttribute;
                    //string a = $"CONSTRAINT \"UC_UmUser_UserName_SN\" UNIQUE(\"UserName\",\"SN\")";
                    string nameConst = $"UC_{_className}";
                    string FiledConst = "";

                    if (uniq.FieldNames.Count > 0)
                    {
                        for (int n = 0; n < uniq.FieldNames.Count; n++)
                        {
                            nameConst += $"_{uniq.FieldNames[n]}";
                            FiledConst += $"\"{uniq.FieldNames[n]}\"";

                            if (n != uniq.FieldNames.Count - 1)
                            {
                                FiledConst += ",";
                            }
                        }
                        UniqueConstraintClass = $"CONSTRAINT \"{nameConst}\" UNIQUE({FiledConst})";
                        ForeignKeyConstraintClass.AppendLine(","+UniqueConstraintClass);
                         
                    }
                    //uniq.FieldNames;
                }

                //if (attr[0].ToString() == "CompositeIndexAttribute")
                //{
                //    CompositeIndexAttribute uniq = attr[1] as CompositeIndexAttribute;
                //    string a = $"CREATE INDEX \"idx_{ClassName}_idhx\" ON \"UmSession\" (\r\n\t\"Fk_Sn_Name\",\r\n\t\"CallingStationId\"\tDESC\r\n);";
                //    string nameConst = $"Idx_{_className}";
                //    string FiledConst = "";

                //    if (uniq.FieldNames.Count > 0)
                //    {
                //        script.Append(Environment.NewLine);
                //        for (int n = 0; n < uniq.FieldNames.Count; n++)
                //        {
                //            nameConst += $"_{uniq.FieldNames[n]}";
                //            string[] ssize = uniq.FieldNames[n].Trim().Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
                //            if (ssize.Length >= 2)
                //            {
                //                FiledConst += $"\"{ssize[0]}\"  {ssize[1]}";
                //            }
                //            else
                //            {
                //                FiledConst += $"\"{ssize[0]}\"";
                //            }

                //            if (n != uniq.FieldNames.Count - 1)
                //            {
                //                FiledConst += ",";
                //            }
                //        }
                //        UniqueConstraintClass = $"CREATE INDEX IF NOT EXISTS \"idx_{ClassName}_{nameConst}\" ON \"{ClassName}\" ({FiledConst});";
                //        IndexConstraintClass.Append(UniqueConstraintClass);
                //    }
                //}
            }


            //foreach (var attr in attrs)
            //{
            //    if (attr.Key.Name == "UniqueConstraintAttribute")
            //    {
            //        script.Append(Environment.NewLine);
            //        UniqueConstraintAttribute uniq = attr.Value as UniqueConstraintAttribute;
            //        string a = $"CONSTRAINT \"UC_UmUser_UserName_SN\" UNIQUE(\"UserName\",\"SN\")";
            //        string nameConst = $"UC_{_className}";
            //        string FiledConst = "";

            //        if (uniq.FieldNames.Count > 0)
            //        {
            //            for (int n = 0; n < uniq.FieldNames.Count; n++)
            //            {
            //                nameConst += $"_{uniq.FieldNames[n]}";
            //                FiledConst += $"\"{uniq.FieldNames[n]}\"";

            //                if (n != uniq.FieldNames.Count - 1)
            //                {
            //                    FiledConst += ",";
            //                }
            //            }
            //            UniqueConstraintClass = $"CONSTRAINT \"{nameConst}\" UNIQUE({FiledConst})";
            //            ForeignKeyConstraintClass.AppendLine("," + UniqueConstraintClass);

            //        }
            //        //uniq.FieldNames;
            //    }
            //    if (attr.Key.Name == "CompositeIndexAttribute")
            //    {
            //        CompositeIndexAttribute uniq = attr.Value as CompositeIndexAttribute;
            //        string a = $"CREATE INDEX \"idx_{ClassName}_idhx\" ON \"UmSession\" (\r\n\t\"Fk_Sn_Name\",\r\n\t\"CallingStationId\"\tDESC\r\n);";
            //        string nameConst = $"Idx_{_className}";
            //        string FiledConst = "";

            //        if (uniq.FieldNames.Count > 0)
            //        {
            //            script.Append(Environment.NewLine);
            //            for (int n = 0; n < uniq.FieldNames.Count; n++)
            //            {
            //                nameConst += $"_{uniq.FieldNames[n]}";
            //                string[] ssize = uniq.FieldNames[n].Trim().Split(new char[] { ' ', '\t' }, StringSplitOptions.RemoveEmptyEntries);
            //                if (ssize.Length >= 2)
            //                {
            //                    FiledConst += $"\"{ssize[0]}\"  {ssize[1]}";
            //                }
            //                else
            //                {
            //                    FiledConst += $"\"{ssize[0]}\"";
            //                }

            //                if (n != uniq.FieldNames.Count - 1)
            //                {
            //                    FiledConst += ",";
            //                }
            //            }
            //            UniqueConstraintClass = $"CREATE INDEX IF NOT EXISTS \"idx_{ClassName}_{nameConst}\" ON \"{ClassName}\" ({FiledConst});";
            //            //script.AppendLine(UniqueConstraintClass);
            //            IndexConstraintClass.Append(UniqueConstraintClass);
            //        }
            //        //uniq.FieldNames;
            //    }
            //}

            script.Append(ForeignKeyConstraintClass);
            if (this._WITHOUTROWID)
                script.Append(") WITHOUT ROWID;");
            else
                script.AppendLine(");");
            script.Append(Environment.NewLine);
            script.Append(IndexConstraintClass);



            //foreach(var idx in IndexConstraintClass)
            //{
            //    script.AppendLine(idx);
            //}

            return script.ToString();
        }

        private string Get_attr_colum(Type t, string columNAme, bool varibleEndTable = false, string attName = "Index")
        {
            string attValue = "";
            var at = t.GetProperty(columNAme).GetCustomAttributes(false).ToDictionary(a => a.GetType().Name, a => a);

            bool Index = false;
            bool PrimaryKey = false;
            bool ForeignKey = false;
            bool AutoIncrement = false;

            bool Unique = false;
            bool Required = false;
            bool DefaultValue = false;

            foreach (var v in at)
            {
                if (varibleEndTable)
                {
                    if (v.Key == "ForeignKeyAttribute")
                        ForeignKey = true;
                    if (v.Key == "IndexAttribute")
                        Index = true;
                }
                else
                {

                    if (v.Key == "PrimaryKeyAttribute")
                        PrimaryKey = true;
                    if (v.Key == "AutoIncrementAttribute")
                        AutoIncrement = true;
                    if (v.Key == "RequiredAttribute")
                        Required = true;
                    if (v.Key == "DefaultAttribute")
                        DefaultValue = true;
                    if (v.Key == "UniqueConstraintAttribute")
                        Unique = true;
                }
            }

            if (Unique)
                attValue += " UNIQUE ";
            if (Required)
                attValue += " NOT NULL ";
            if (PrimaryKey)
                attValue += " PRIMARY KEY ";
            if (AutoIncrement)
                attValue += " AUTOINCREMENT ";

            if (DefaultValue)
            {
                foreach (var v in at)
                {
                    var d = v.Value;
                    DefaultAttribute ee = d as DefaultAttribute;
                    if (ee != null)
                    {
                        if (ee.DefaultType.Name == "Int32")
                        {
                            attValue += $" DEFAULT ({ee.DefaultValue}) ";
                        }
                        else
                        {
                            attValue += $" DEFAULT ('{ee.DefaultValue}') ";
                        }
                    }

                }
            }

            if (varibleEndTable)
            {
                if (attName == "ForeignKey")
                {
                    if (ForeignKey)
                    {
                        ForeignKeyAttribute ee = at.Values.First() as ForeignKeyAttribute;
                        if (ee != null)
                        {
                            
                            attValue += $"FOREIGN KEY(\"{columNAme}\") REFERENCES \"{ee.Type.Name}\"(\"{ee.ColumnName}\") ON  DELETE CASCADE";

                        }
                    }
                }
               
                if (attName == "Index")
                {
                    if (Index)
                    {
                        attValue = $@"CREATE INDEX IF NOT EXISTS ""idx_{this.ClassName}_{columNAme}"" ON ""{this.ClassName}"" (""{columNAme}"");";
                    }
                }
            }
            return attValue;
        }
    }
    


    public class FakeDataClass
    {
        public int AnInt
        {
            get;
            set;
        }

        public string AString
        {
            get;
            set;
        }

        public float AFloat
        {
            get;
            set;
        }

        public FKClass AFKReference
        {
            get;
            set;
        }
    }

    public class FKClass
    {
        public int AFKInt
        {
            get;
            set;
        }
    }

}
