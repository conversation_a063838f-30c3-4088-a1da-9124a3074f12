﻿namespace SmartCreator.Forms.UserManager
{
    partial class Form_UM_Sales_Device
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel_topFilter = new SmartCreator.RJControls.RJPanel();
            this.btn_Print = new SmartCreator.RJControls.RJButton();
            this.check_with_Commi = new SmartCreator.RJControls.RJCheckBox();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.txt_countDevice = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.rjDateTime_To = new SmartCreator.RJControls.RJDatePicker();
            this.rjDateTime_From = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_To_Date = new SmartCreator.RJControls.RJCheckBox();
            this.btn_ = new SmartCreator.RJControls.RJButton();
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.Radi_Month = new SmartCreator.RJControls.RJRadioButton();
            this.Radi_Days = new SmartCreator.RJControls.RJRadioButton();
            this.Radi_Details = new SmartCreator.RJControls.RJRadioButton();
            this.rjPanel12 = new SmartCreator.RJControls.RJPanel();
            this.rjTextBox1 = new SmartCreator.RJControls.RJTextBox();
            this.Cbox_View = new SmartCreator.RJControls.RJComboBox();
            this.ToggleButton_Detail = new SmartCreator.RJControls.RJToggleButton();
            this.btn_Print_details = new SmartCreator.RJControls.RJButton();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Monthly = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel21 = new SmartCreator.RJControls.RJLabel();
            this.dgv2 = new SmartCreator.RJControls.RJDataGridView();
            this.dmAll_Cards1 = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.View_Hide_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.NasPort_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Uptim_StripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Download_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Price_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_FirstLogin_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CountCard_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CountSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.PrintAll_toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.Print_DeviceCtrlcToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Excel_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.toolStripMenuItem1 = new System.Windows.Forms.ToolStripMenuItem();
            this.PageNumber_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.LastSynDb_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem3 = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CusName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Descr_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTransferLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTimeLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTillTime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_FirstUse_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_LastSeenAt_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_RegDate_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_Up_Down_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UploadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_DownloadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_TransferLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BachCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SellingPoint_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.Password_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.UserName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SN_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Status_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Restor_ColumnToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem4 = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_AllRowToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ExportExcelToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.Spanel = new SmartCreator.RJControls.RJPanel();
            this.panel1_side = new System.Windows.Forms.Panel();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.CBox_Port = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.panel3_side = new System.Windows.Forms.Panel();
            this.panel2_side = new System.Windows.Forms.Panel();
            this.pnl_side_sn = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.CBox_Customer = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Radius = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.rjButton4 = new SmartCreator.RJControls.RJButton();
            this.btn_Fix = new SmartCreator.RJControls.RJButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.timer = new System.Windows.Forms.Timer(this.components);
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.panel1 = new System.Windows.Forms.Panel();
            this.pnlClientArea.SuspendLayout();
            this.rjPanel_topFilter.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.rjPanel12.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).BeginInit();
            this.dmAll_Cards1.SuspendLayout();
            this.dmAll_Cards.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.Spanel.SuspendLayout();
            this.pnl_side_sn.SuspendLayout();
            this.panel1.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.Spanel);
            this.pnlClientArea.Controls.Add(this.rjPanel_topFilter);
            this.pnlClientArea.Controls.Add(this.panel1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 579);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(146, 17);
            this.lblCaption.Text = "Form_UM_Sales_Device";
            // 
            // rjPanel_topFilter
            // 
            this.rjPanel_topFilter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_topFilter.BorderRadius = 10;
            this.rjPanel_topFilter.Controls.Add(this.btn_Print);
            this.rjPanel_topFilter.Controls.Add(this.check_with_Commi);
            this.rjPanel_topFilter.Controls.Add(this.btn_search);
            this.rjPanel_topFilter.Controls.Add(this.btn_Refresh);
            this.rjPanel_topFilter.Controls.Add(this.txt_search);
            this.rjPanel_topFilter.Controls.Add(this.txt_countDevice);
            this.rjPanel_topFilter.Controls.Add(this.rjLabel2);
            this.rjPanel_topFilter.Controls.Add(this.rjPanel1);
            this.rjPanel_topFilter.Controls.Add(this.btn_);
            this.rjPanel_topFilter.Controls.Add(this.btn_Filter);
            this.rjPanel_topFilter.Customizable = false;
            this.rjPanel_topFilter.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjPanel_topFilter.Location = new System.Drawing.Point(0, 0);
            this.rjPanel_topFilter.Margin = new System.Windows.Forms.Padding(1, 0, 0, 0);
            this.rjPanel_topFilter.Name = "rjPanel_topFilter";
            this.rjPanel_topFilter.Size = new System.Drawing.Size(990, 89);
            this.rjPanel_topFilter.TabIndex = 80;
            // 
            // btn_Print
            // 
            this.btn_Print.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Print.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print.BorderRadius = 4;
            this.btn_Print.BorderSize = 1;
            this.btn_Print.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Print.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Print.FlatAppearance.BorderSize = 0;
            this.btn_Print.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Print.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Print.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Print.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Print.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.btn_Print.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Print.IconSize = 25;
            this.btn_Print.Location = new System.Drawing.Point(105, 11);
            this.btn_Print.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Print.Name = "btn_Print";
            this.btn_Print.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Print.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Print.Size = new System.Drawing.Size(35, 44);
            this.btn_Print.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Print.TabIndex = 49;
            this.btn_Print.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Print.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Print.UseVisualStyleBackColor = false;
            this.btn_Print.Click += new System.EventHandler(this.btn_Print_Click);
            // 
            // check_with_Commi
            // 
            this.check_with_Commi.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.check_with_Commi.AutoSize = true;
            this.check_with_Commi.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.BorderSize = 1;
            this.check_with_Commi.Check = false;
            this.check_with_Commi.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.check_with_Commi.Cursor = System.Windows.Forms.Cursors.Hand;
            this.check_with_Commi.Customizable = false;
            this.check_with_Commi.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.check_with_Commi.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.check_with_Commi.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.check_with_Commi.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.check_with_Commi.Location = new System.Drawing.Point(323, 59);
            this.check_with_Commi.MinimumSize = new System.Drawing.Size(0, 21);
            this.check_with_Commi.Name = "check_with_Commi";
            this.check_with_Commi.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.check_with_Commi.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.check_with_Commi.Size = new System.Drawing.Size(116, 21);
            this.check_with_Commi.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.check_with_Commi.TabIndex = 94;
            this.check_with_Commi.Text = "خصم العمولات";
            this.check_with_Commi.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.check_with_Commi.UseVisualStyleBackColor = true;
            this.check_with_Commi.CheckedChanged += new System.EventHandler(this.check_with_Commi_CheckedChanged);
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 1;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(805, 58);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(35, 25);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 91;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(141, 12);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(35, 44);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 50;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(843, 57);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث باسم الجهاز";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(126, 26);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 29;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // txt_countDevice
            // 
            this.txt_countDevice._Customizable = false;
            this.txt_countDevice.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_countDevice.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_countDevice.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_countDevice.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_countDevice.BorderRadius = 3;
            this.txt_countDevice.BorderSize = 1;
            this.txt_countDevice.Enabled = false;
            this.txt_countDevice.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txt_countDevice.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_countDevice.Location = new System.Drawing.Point(456, 58);
            this.txt_countDevice.MultiLine = false;
            this.txt_countDevice.Name = "txt_countDevice";
            this.txt_countDevice.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_countDevice.PasswordChar = false;
            this.txt_countDevice.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_countDevice.PlaceHolderText = null;
            this.txt_countDevice.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_countDevice.Size = new System.Drawing.Size(97, 25);
            this.txt_countDevice.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_countDevice.TabIndex = 91;
            this.txt_countDevice.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(559, 62);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(239, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 93;
            this.rjLabel2.Text = "عدد الاجهزه التي اشتغلت خلال الفتره المحدده";
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjPanel1.BorderRadius = 5;
            this.rjPanel1.Controls.Add(this.rjLabel3);
            this.rjPanel1.Controls.Add(this.rjDateTime_To);
            this.rjPanel1.Controls.Add(this.rjDateTime_From);
            this.rjPanel1.Controls.Add(this.CheckBox_To_Date);
            this.rjPanel1.Customizable = true;
            this.rjPanel1.Location = new System.Drawing.Point(346, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(639, 47);
            this.rjPanel1.TabIndex = 72;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(581, 16);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(23, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 49;
            this.rjLabel3.Text = "من";
            // 
            // rjDateTime_To
            // 
            this.rjDateTime_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_To.BorderRadius = 7;
            this.rjDateTime_To.BorderSize = 1;
            this.rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_To.Customizable = false;
            this.rjDateTime_To.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.rjDateTime_To.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_To.IconColor = System.Drawing.Color.White;
            this.rjDateTime_To.Location = new System.Drawing.Point(7, 5);
            this.rjDateTime_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_To.Name = "rjDateTime_To";
            this.rjDateTime_To.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_To.Size = new System.Drawing.Size(265, 35);
            this.rjDateTime_To.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjDateTime_To.TabIndex = 80;
            this.rjDateTime_To.Value = new System.DateTime(2024, 9, 27, 23, 59, 59, 0);
            // 
            // rjDateTime_From
            // 
            this.rjDateTime_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjDateTime_From.BorderRadius = 7;
            this.rjDateTime_From.BorderSize = 1;
            this.rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            this.rjDateTime_From.Customizable = false;
            this.rjDateTime_From.Font = new System.Drawing.Font("Segoe UI", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjDateTime_From.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.rjDateTime_From.IconColor = System.Drawing.Color.White;
            this.rjDateTime_From.Location = new System.Drawing.Point(352, 6);
            this.rjDateTime_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.rjDateTime_From.Name = "rjDateTime_From";
            this.rjDateTime_From.Padding = new System.Windows.Forms.Padding(2);
            this.rjDateTime_From.Size = new System.Drawing.Size(265, 35);
            this.rjDateTime_From.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjDateTime_From.TabIndex = 79;
            this.rjDateTime_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            // 
            // CheckBox_To_Date
            // 
            this.CheckBox_To_Date.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_To_Date.AutoSize = true;
            this.CheckBox_To_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.BorderSize = 1;
            this.CheckBox_To_Date.Check = true;
            this.CheckBox_To_Date.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.Checked = true;
            this.CheckBox_To_Date.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_To_Date.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_To_Date.Customizable = false;
            this.CheckBox_To_Date.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.CheckBox_To_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_To_Date.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.Location = new System.Drawing.Point(258, 10);
            this.CheckBox_To_Date.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_To_Date.Name = "CheckBox_To_Date";
            this.CheckBox_To_Date.Padding = new System.Windows.Forms.Padding(0, 0, 18, 0);
            this.CheckBox_To_Date.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_To_Date.Size = new System.Drawing.Size(66, 26);
            this.CheckBox_To_Date.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_To_Date.TabIndex = 95;
            this.CheckBox_To_Date.Text = "الى";
            this.CheckBox_To_Date.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.UseVisualStyleBackColor = true;
            // 
            // btn_
            // 
            this.btn_.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.BorderRadius = 4;
            this.btn_.BorderSize = 1;
            this.btn_.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_.FlatAppearance.BorderSize = 0;
            this.btn_.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_.Font = new System.Drawing.Font("Droid Arabic Kufi", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_.IconSize = 2;
            this.btn_.Location = new System.Drawing.Point(176, 12);
            this.btn_.Margin = new System.Windows.Forms.Padding(0);
            this.btn_.Name = "btn_";
            this.btn_.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_.Size = new System.Drawing.Size(66, 44);
            this.btn_.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_.TabIndex = 56;
            this.btn_.Text = "عــرض";
            this.btn_.UseVisualStyleBackColor = false;
            this.btn_.Click += new System.EventHandler(this.btn__Click);
            // 
            // btn_Filter
            // 
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 17;
            this.btn_Filter.Location = new System.Drawing.Point(16, 11);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Filter.Size = new System.Drawing.Size(86, 44);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Filter.TabIndex = 73;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 0;
            this.rjPanel2.Controls.Add(this.Radi_Month);
            this.rjPanel2.Controls.Add(this.Radi_Days);
            this.rjPanel2.Controls.Add(this.Radi_Details);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(142, 44);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(272, 35);
            this.rjPanel2.TabIndex = 95;
            this.rjPanel2.Visible = false;
            // 
            // Radi_Month
            // 
            this.Radi_Month.AutoSize = true;
            this.Radi_Month.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radi_Month.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radi_Month.Customizable = false;
            this.Radi_Month.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radi_Month.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radi_Month.Location = new System.Drawing.Point(7, 6);
            this.Radi_Month.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radi_Month.Name = "Radi_Month";
            this.Radi_Month.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radi_Month.Size = new System.Drawing.Size(72, 21);
            this.Radi_Month.TabIndex = 86;
            this.Radi_Month.Tag = "";
            this.Radi_Month.Text = "شهري";
            this.Radi_Month.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radi_Month.UseVisualStyleBackColor = true;
            // 
            // Radi_Days
            // 
            this.Radi_Days.AutoSize = true;
            this.Radi_Days.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radi_Days.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radi_Days.Customizable = false;
            this.Radi_Days.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radi_Days.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radi_Days.Location = new System.Drawing.Point(83, 6);
            this.Radi_Days.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radi_Days.Name = "Radi_Days";
            this.Radi_Days.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radi_Days.Size = new System.Drawing.Size(66, 21);
            this.Radi_Days.TabIndex = 86;
            this.Radi_Days.Tag = "";
            this.Radi_Days.Text = "يومي";
            this.Radi_Days.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radi_Days.UseVisualStyleBackColor = true;
            // 
            // Radi_Details
            // 
            this.Radi_Details.AutoSize = true;
            this.Radi_Details.Checked = true;
            this.Radi_Details.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radi_Details.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radi_Details.Customizable = false;
            this.Radi_Details.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radi_Details.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radi_Details.Location = new System.Drawing.Point(154, 7);
            this.Radi_Details.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radi_Details.Name = "Radi_Details";
            this.Radi_Details.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radi_Details.Size = new System.Drawing.Size(110, 21);
            this.Radi_Details.TabIndex = 86;
            this.Radi_Details.TabStop = true;
            this.Radi_Details.Tag = "";
            this.Radi_Details.Text = "طباعة تفصيليه";
            this.Radi_Details.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radi_Details.UseVisualStyleBackColor = true;
            // 
            // rjPanel12
            // 
            this.rjPanel12.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel12.BorderRadius = 5;
            this.rjPanel12.Controls.Add(this.rjTextBox1);
            this.rjPanel12.Controls.Add(this.Cbox_View);
            this.rjPanel12.Controls.Add(this.ToggleButton_Detail);
            this.rjPanel12.Controls.Add(this.btn_Print_details);
            this.rjPanel12.Controls.Add(this.jToggleButton_Year);
            this.rjPanel12.Controls.Add(this.ToggleButton_Monthly);
            this.rjPanel12.Customizable = false;
            this.rjPanel12.Location = new System.Drawing.Point(8, 239);
            this.rjPanel12.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel12.Name = "rjPanel12";
            this.rjPanel12.Size = new System.Drawing.Size(960, 45);
            this.rjPanel12.TabIndex = 58;
            // 
            // rjTextBox1
            // 
            this.rjTextBox1._Customizable = true;
            this.rjTextBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjTextBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjTextBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjTextBox1.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.rjTextBox1.BorderRadius = 3;
            this.rjTextBox1.BorderSize = 1;
            this.rjTextBox1.Enabled = false;
            this.rjTextBox1.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.rjTextBox1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjTextBox1.Location = new System.Drawing.Point(710, 8);
            this.rjTextBox1.MultiLine = false;
            this.rjTextBox1.Name = "rjTextBox1";
            this.rjTextBox1.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.rjTextBox1.PasswordChar = false;
            this.rjTextBox1.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.rjTextBox1.PlaceHolderText = null;
            this.rjTextBox1.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.rjTextBox1.Size = new System.Drawing.Size(240, 30);
            this.rjTextBox1.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.rjTextBox1.TabIndex = 91;
            this.rjTextBox1.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // Cbox_View
            // 
            this.Cbox_View.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Cbox_View.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.Cbox_View.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.Cbox_View.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Cbox_View.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_View.BorderRadius = 10;
            this.Cbox_View.BorderSize = 1;
            this.Cbox_View.Customizable = false;
            this.Cbox_View.DataSource = null;
            this.Cbox_View.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Cbox_View.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Cbox_View.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_View.Font = new System.Drawing.Font("Segoe UI", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Cbox_View.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Cbox_View.Items.AddRange(new object[] {
            "عرض جميع الكروت التي اشتغلت في الجهاز المحدد",
            "عرض جميع الجلسات التي دخلت في الجهاز المحدد",
            "عرض كروت اول دخول فقط (التي تسجلت كمبيعات)"});
            this.Cbox_View.Location = new System.Drawing.Point(419, 7);
            this.Cbox_View.Name = "Cbox_View";
            this.Cbox_View.Padding = new System.Windows.Forms.Padding(2);
            this.Cbox_View.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Cbox_View.SelectedIndex = -1;
            this.Cbox_View.Size = new System.Drawing.Size(280, 32);
            this.Cbox_View.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Cbox_View.TabIndex = 94;
            this.Cbox_View.Texts = "";
            this.Cbox_View.OnSelectedIndexChanged += new System.EventHandler(this.Cbox_View_OnSelectedIndexChanged);
            // 
            // ToggleButton_Detail
            // 
            this.ToggleButton_Detail.Activated = true;
            this.ToggleButton_Detail.Checked = true;
            this.ToggleButton_Detail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToggleButton_Detail.Customizable = false;
            this.ToggleButton_Detail.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Detail.Location = new System.Drawing.Point(248, 11);
            this.ToggleButton_Detail.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Detail.Name = "ToggleButton_Detail";
            this.ToggleButton_Detail.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.OFF_Text = "تفصيلي";
            this.ToggleButton_Detail.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Detail.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.ON_Text = "تفصيلي";
            this.ToggleButton_Detail.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Detail.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Detail.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Detail.TabIndex = 83;
            this.ToggleButton_Detail.Tag = "تفصيلي";
            this.ToggleButton_Detail.Text = "#";
            this.ToggleButton_Detail.UseVisualStyleBackColor = true;
            this.ToggleButton_Detail.CheckedChanged += new System.EventHandler(this.ToggleButton_Detail_CheckedChanged);
            // 
            // btn_Print_details
            // 
            this.btn_Print_details.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Print_details.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print_details.BorderRadius = 4;
            this.btn_Print_details.BorderSize = 1;
            this.btn_Print_details.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Print_details.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Print_details.FlatAppearance.BorderSize = 0;
            this.btn_Print_details.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Print_details.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Print_details.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Print_details.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Print_details.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print_details.IconChar = FontAwesome.Sharp.IconChar.Print;
            this.btn_Print_details.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Print_details.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Print_details.IconSize = 25;
            this.btn_Print_details.Location = new System.Drawing.Point(5, 4);
            this.btn_Print_details.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Print_details.Name = "btn_Print_details";
            this.btn_Print_details.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Print_details.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Print_details.Size = new System.Drawing.Size(35, 37);
            this.btn_Print_details.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Print_details.TabIndex = 49;
            this.btn_Print_details.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Print_details.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Print_details.UseVisualStyleBackColor = false;
            this.btn_Print_details.Click += new System.EventHandler(this.btn_Print_details_Click);
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.jToggleButton_Year.Location = new System.Drawing.Point(55, 11);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = "شهري";
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = "شهري";
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(91, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 85;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            this.jToggleButton_Year.CheckedChanged += new System.EventHandler(this.jToggleButton_Year_CheckedChanged);
            // 
            // ToggleButton_Monthly
            // 
            this.ToggleButton_Monthly.Activated = false;
            this.ToggleButton_Monthly.Customizable = false;
            this.ToggleButton_Monthly.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Monthly.Location = new System.Drawing.Point(152, 11);
            this.ToggleButton_Monthly.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Monthly.Name = "ToggleButton_Monthly";
            this.ToggleButton_Monthly.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.OFF_Text = "يومي";
            this.ToggleButton_Monthly.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Monthly.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.ON_Text = "يومي";
            this.ToggleButton_Monthly.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Monthly.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Monthly.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Monthly.TabIndex = 84;
            this.ToggleButton_Monthly.Tag = "تفصيلي";
            this.ToggleButton_Monthly.Text = "#";
            this.ToggleButton_Monthly.UseVisualStyleBackColor = true;
            this.ToggleButton_Monthly.CheckedChanged += new System.EventHandler(this.ToggleButton_Monthly_CheckedChanged);
            // 
            // rjLabel21
            // 
            this.rjLabel21.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel21.AutoSize = true;
            this.rjLabel21.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel21.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel21.LinkLabel = false;
            this.rjLabel21.Location = new System.Drawing.Point(718, 295);
            this.rjLabel21.Name = "rjLabel21";
            this.rjLabel21.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel21.Size = new System.Drawing.Size(226, 17);
            this.rjLabel21.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel21.TabIndex = 93;
            this.rjLabel21.Text = "عدد الكروت التي اشتغلت في الجهاز المحدد";
            this.rjLabel21.Visible = false;
            // 
            // dgv2
            // 
            this.dgv2.AllowUserToAddRows = false;
            this.dgv2.AllowUserToDeleteRows = false;
            this.dgv2.AllowUserToOrderColumns = true;
            this.dgv2.AllowUserToResizeRows = false;
            this.dgv2.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv2.AlternatingRowsColorApply = false;
            this.dgv2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv2.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.BorderRadius = 10;
            this.dgv2.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv2.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv2.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv2.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv2.ColumnHeaderHeight = 45;
            this.dgv2.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv2.ColumnHeadersHeight = 45;
            this.dgv2.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv2.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv2.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv2.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv2.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv2.EnableHeadersVisualStyles = false;
            this.dgv2.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv2.Location = new System.Drawing.Point(6, 292);
            this.dgv2.Margin = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.dgv2.MultiSelect = false;
            this.dgv2.Name = "dgv2";
            this.dgv2.ReadOnly = true;
            this.dgv2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv2.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv2.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv2.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv2.RowHeadersVisible = false;
            this.dgv2.RowHeadersWidth = 30;
            this.dgv2.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv2.RowHeight = 30;
            this.dgv2.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F);
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv2.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv2.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv2.RowTemplate.Height = 30;
            this.dgv2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv2.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv2.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv2.Size = new System.Drawing.Size(960, 181);
            this.dgv2.TabIndex = 82;
            this.dgv2.SelectionChanged += new System.EventHandler(this.dgv2_SelectionChanged);
            this.dgv2.MouseClick += new System.Windows.Forms.MouseEventHandler(this.dgv2_MouseClick);
            this.dgv2.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv2_MouseDown);
            // 
            // dmAll_Cards1
            // 
            this.dmAll_Cards1.ActiveMenuItem = false;
            this.dmAll_Cards1.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards1.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.dmAll_Cards1.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.View_Hide_toolStripMenuItem,
            this.toolStripSeparator1,
            this.PrintAll_toolStripMenuItem2,
            this.Print_DeviceCtrlcToolStripMenuItem,
            this.Excel_ToolStripMenuItem});
            this.dmAll_Cards1.Name = "dmExample";
            this.dmAll_Cards1.OwnerIsMenuButton = false;
            this.dmAll_Cards1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards1.Size = new System.Drawing.Size(204, 98);
            // 
            // View_Hide_toolStripMenuItem
            // 
            this.View_Hide_toolStripMenuItem.Checked = true;
            this.View_Hide_toolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.View_Hide_toolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.NasPort_ToolStripMenuItem,
            this.Uptim_StripMenuItem,
            this.Download_ToolStripMenuItem,
            this.Price_ToolStripMenuItem,
            this.Count_FirstLogin_ToolStripMenuItem,
            this.CountCard_ToolStripMenuItem,
            this.CountSession_ToolStripMenuItem});
            this.View_Hide_toolStripMenuItem.Name = "View_Hide_toolStripMenuItem";
            this.View_Hide_toolStripMenuItem.Size = new System.Drawing.Size(203, 22);
            this.View_Hide_toolStripMenuItem.Text = "عرض واخفاء الاعمدة";
            // 
            // NasPort_ToolStripMenuItem
            // 
            this.NasPort_ToolStripMenuItem.Checked = true;
            this.NasPort_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.NasPort_ToolStripMenuItem.Name = "NasPort_ToolStripMenuItem";
            this.NasPort_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.NasPort_ToolStripMenuItem.Tag = "Str_Status";
            this.NasPort_ToolStripMenuItem.Text = "جهاز البث";
            // 
            // Uptim_StripMenuItem
            // 
            this.Uptim_StripMenuItem.Checked = true;
            this.Uptim_StripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Uptim_StripMenuItem.Name = "Uptim_StripMenuItem";
            this.Uptim_StripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.Uptim_StripMenuItem.Tag = "Sn";
            this.Uptim_StripMenuItem.Text = "اجمالي الوقت";
            this.Uptim_StripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Download_ToolStripMenuItem
            // 
            this.Download_ToolStripMenuItem.Checked = true;
            this.Download_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Download_ToolStripMenuItem.Name = "Download_ToolStripMenuItem";
            this.Download_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.Download_ToolStripMenuItem.Tag = "UserName";
            this.Download_ToolStripMenuItem.Text = "احمالي الاستهلاك";
            // 
            // Price_ToolStripMenuItem
            // 
            this.Price_ToolStripMenuItem.Checked = true;
            this.Price_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Price_ToolStripMenuItem.Name = "Price_ToolStripMenuItem";
            this.Price_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.Price_ToolStripMenuItem.Tag = "Str_MoneyTotal";
            this.Price_ToolStripMenuItem.Text = "مبلغ اول دخول";
            // 
            // Count_FirstLogin_ToolStripMenuItem
            // 
            this.Count_FirstLogin_ToolStripMenuItem.Checked = true;
            this.Count_FirstLogin_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Count_FirstLogin_ToolStripMenuItem.Name = "Count_FirstLogin_ToolStripMenuItem";
            this.Count_FirstLogin_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.Count_FirstLogin_ToolStripMenuItem.Tag = "ProfileName";
            this.Count_FirstLogin_ToolStripMenuItem.Text = "كروت اول دخول";
            // 
            // CountCard_ToolStripMenuItem
            // 
            this.CountCard_ToolStripMenuItem.Checked = true;
            this.CountCard_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CountCard_ToolStripMenuItem.Name = "CountCard_ToolStripMenuItem";
            this.CountCard_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.CountCard_ToolStripMenuItem.Tag = "SpName";
            this.CountCard_ToolStripMenuItem.Text = "اجمالي الكروت التي دخلت";
            // 
            // CountSession_ToolStripMenuItem
            // 
            this.CountSession_ToolStripMenuItem.Name = "CountSession_ToolStripMenuItem";
            this.CountSession_ToolStripMenuItem.Size = new System.Drawing.Size(200, 22);
            this.CountSession_ToolStripMenuItem.Tag = "BatchCardId";
            this.CountSession_ToolStripMenuItem.Text = "اجمالي الجلسات";
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(200, 6);
            // 
            // PrintAll_toolStripMenuItem2
            // 
            this.PrintAll_toolStripMenuItem2.Name = "PrintAll_toolStripMenuItem2";
            this.PrintAll_toolStripMenuItem2.Size = new System.Drawing.Size(203, 22);
            this.PrintAll_toolStripMenuItem2.Text = "طباعة تقرير للاجهزة";
            // 
            // Print_DeviceCtrlcToolStripMenuItem
            // 
            this.Print_DeviceCtrlcToolStripMenuItem.Name = "Print_DeviceCtrlcToolStripMenuItem";
            this.Print_DeviceCtrlcToolStripMenuItem.Size = new System.Drawing.Size(203, 22);
            this.Print_DeviceCtrlcToolStripMenuItem.Text = "طباعة بيانات الجهاز المحدد";
            // 
            // Excel_ToolStripMenuItem
            // 
            this.Excel_ToolStripMenuItem.Name = "Excel_ToolStripMenuItem";
            this.Excel_ToolStripMenuItem.Size = new System.Drawing.Size(203, 22);
            this.Excel_ToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(20, 20);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.toolStripMenuItem1,
            this.Restor_ColumnToolStripMenuItem,
            this.toolStripMenuItem4,
            this.Copy_ToolStripMenuItem,
            this.Copy_AllRowToolStripMenuItem,
            this.ExportExcelToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(222, 136);
            // 
            // toolStripMenuItem1
            // 
            this.toolStripMenuItem1.Checked = true;
            this.toolStripMenuItem1.CheckState = System.Windows.Forms.CheckState.Checked;
            this.toolStripMenuItem1.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.PageNumber_ToolStripMenuItem,
            this.LastSynDb_ToolStripMenuItem,
            this.toolStripMenuItem3,
            this.Count_profile_ToolStripMenuItem,
            this.CusName_ToolStripMenuItem,
            this.Descr_ToolStripMenuItem,
            this.Str_ProfileTransferLeft_ToolStripMenuItem,
            this.Str_ProfileTimeLeft_ToolStripMenuItem,
            this.Str_ProfileTillTime_ToolStripMenuItem,
            this.dt_FirstUse_ToolStripMenuItem,
            this.dt_LastSeenAt_ToolStripMenuItem,
            this.dt_RegDate_ToolStripMenuItem,
            this.Str_Up_Down_ToolStripMenuItem,
            this.Str_UploadUsed_ToolStripMenuItem,
            this.Str_DownloadUsed_ToolStripMenuItem,
            this.Str_UptimeUsed_ToolStripMenuItem,
            this.Str_TransferLimit_ToolStripMenuItem,
            this.Str_UptimeLimit_ToolStripMenuItem,
            this.BachCards_ToolStripMenuItem,
            this.SellingPoint_ToolStripMenuItem,
            this.Profile_ToolStripMenuItem,
            this.toolStripMenuItem2,
            this.Password_ToolStripMenuItem,
            this.UserName_ToolStripMenuItem,
            this.SN_ToolStripMenuItem,
            this.Status_ToolStripMenuItem});
            this.toolStripMenuItem1.Name = "toolStripMenuItem1";
            this.toolStripMenuItem1.Size = new System.Drawing.Size(221, 22);
            this.toolStripMenuItem1.Text = "عرض واخفاء الاعمدة";
            // 
            // PageNumber_ToolStripMenuItem
            // 
            this.PageNumber_ToolStripMenuItem.Name = "PageNumber_ToolStripMenuItem";
            this.PageNumber_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.PageNumber_ToolStripMenuItem.Tag = "PageNumber";
            this.PageNumber_ToolStripMenuItem.Text = "رقم الصفحة";
            this.PageNumber_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // LastSynDb_ToolStripMenuItem
            // 
            this.LastSynDb_ToolStripMenuItem.Name = "LastSynDb_ToolStripMenuItem";
            this.LastSynDb_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.LastSynDb_ToolStripMenuItem.Tag = "LastSynDb";
            this.LastSynDb_ToolStripMenuItem.Text = "اخر تحديث او مزامنه للكرت";
            this.LastSynDb_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem3
            // 
            this.toolStripMenuItem3.Name = "toolStripMenuItem3";
            this.toolStripMenuItem3.Size = new System.Drawing.Size(202, 22);
            this.toolStripMenuItem3.Tag = "CountSession";
            this.toolStripMenuItem3.Text = "عدد جلسات الكرت";
            this.toolStripMenuItem3.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Count_profile_ToolStripMenuItem
            // 
            this.Count_profile_ToolStripMenuItem.Name = "Count_profile_ToolStripMenuItem";
            this.Count_profile_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Count_profile_ToolStripMenuItem.Tag = "CountProfile";
            this.Count_profile_ToolStripMenuItem.Text = "عدد الباقات";
            this.Count_profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // CusName_ToolStripMenuItem
            // 
            this.CusName_ToolStripMenuItem.Name = "CusName_ToolStripMenuItem";
            this.CusName_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.CusName_ToolStripMenuItem.Tag = "CustomerName";
            this.CusName_ToolStripMenuItem.Text = "عميل يوزمنجر";
            this.CusName_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Descr_ToolStripMenuItem
            // 
            this.Descr_ToolStripMenuItem.Name = "Descr_ToolStripMenuItem";
            this.Descr_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Descr_ToolStripMenuItem.Tag = "Comment";
            this.Descr_ToolStripMenuItem.Text = "تعلـــــــيق";
            this.Descr_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTransferLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Name = "Str_ProfileTransferLeft_ToolStripMenuItem";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Tag = "Str_ProfileTransferLeft";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Text = "التحميل المتبقي";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTimeLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Name = "Str_ProfileTimeLeft_ToolStripMenuItem";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Tag = "Str_ProfileTimeLeft";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Text = "الوقت المتبقي";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTillTime_ToolStripMenuItem
            // 
            this.Str_ProfileTillTime_ToolStripMenuItem.Name = "Str_ProfileTillTime_ToolStripMenuItem";
            this.Str_ProfileTillTime_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_ProfileTillTime_ToolStripMenuItem.Tag = "Str_ProfileTillTime";
            this.Str_ProfileTillTime_ToolStripMenuItem.Text = "تــــاريخ الانتــــهاء";
            this.Str_ProfileTillTime_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_FirstUse_ToolStripMenuItem
            // 
            this.dt_FirstUse_ToolStripMenuItem.Name = "dt_FirstUse_ToolStripMenuItem";
            this.dt_FirstUse_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_FirstUse_ToolStripMenuItem.Tag = "FirsLogin";
            this.dt_FirstUse_ToolStripMenuItem.Text = "اول دخـــــــــول";
            this.dt_FirstUse_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_LastSeenAt_ToolStripMenuItem
            // 
            this.dt_LastSeenAt_ToolStripMenuItem.Name = "dt_LastSeenAt_ToolStripMenuItem";
            this.dt_LastSeenAt_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_LastSeenAt_ToolStripMenuItem.Tag = "LastSeenAt";
            this.dt_LastSeenAt_ToolStripMenuItem.Text = "اخــــــر ظهــــور";
            this.dt_LastSeenAt_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_RegDate_ToolStripMenuItem
            // 
            this.dt_RegDate_ToolStripMenuItem.Name = "dt_RegDate_ToolStripMenuItem";
            this.dt_RegDate_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.dt_RegDate_ToolStripMenuItem.Tag = "RegDate";
            this.dt_RegDate_ToolStripMenuItem.Text = "تـــاريخ الاضــــافة";
            this.dt_RegDate_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_Up_Down_ToolStripMenuItem
            // 
            this.Str_Up_Down_ToolStripMenuItem.Name = "Str_Up_Down_ToolStripMenuItem";
            this.Str_Up_Down_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_Up_Down_ToolStripMenuItem.Tag = "Str_Up_Down";
            this.Str_Up_Down_ToolStripMenuItem.Text = "تحميـــــل+رفــــع";
            this.Str_Up_Down_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UploadUsed_ToolStripMenuItem
            // 
            this.Str_UploadUsed_ToolStripMenuItem.Name = "Str_UploadUsed_ToolStripMenuItem";
            this.Str_UploadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UploadUsed_ToolStripMenuItem.Tag = "Str_UploadUsed";
            this.Str_UploadUsed_ToolStripMenuItem.Text = "الرقع المستخدم";
            this.Str_UploadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_DownloadUsed_ToolStripMenuItem
            // 
            this.Str_DownloadUsed_ToolStripMenuItem.Name = "Str_DownloadUsed_ToolStripMenuItem";
            this.Str_DownloadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_DownloadUsed_ToolStripMenuItem.Tag = "Str_DownloadUsed";
            this.Str_DownloadUsed_ToolStripMenuItem.Text = "التحميل المستخدم";
            this.Str_DownloadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeUsed_ToolStripMenuItem
            // 
            this.Str_UptimeUsed_ToolStripMenuItem.Name = "Str_UptimeUsed_ToolStripMenuItem";
            this.Str_UptimeUsed_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UptimeUsed_ToolStripMenuItem.Tag = "Str_UptimeUsed";
            this.Str_UptimeUsed_ToolStripMenuItem.Text = "الوقت المستخدم";
            this.Str_UptimeUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_TransferLimit_ToolStripMenuItem
            // 
            this.Str_TransferLimit_ToolStripMenuItem.Name = "Str_TransferLimit_ToolStripMenuItem";
            this.Str_TransferLimit_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_TransferLimit_ToolStripMenuItem.Tag = "Str_TransferLimit";
            this.Str_TransferLimit_ToolStripMenuItem.Text = "التنزيل المسموح";
            this.Str_TransferLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeLimit_ToolStripMenuItem
            // 
            this.Str_UptimeLimit_ToolStripMenuItem.Name = "Str_UptimeLimit_ToolStripMenuItem";
            this.Str_UptimeLimit_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Str_UptimeLimit_ToolStripMenuItem.Tag = "Str_UptimeLimit";
            this.Str_UptimeLimit_ToolStripMenuItem.Text = "الوقت المسموح";
            this.Str_UptimeLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // BachCards_ToolStripMenuItem
            // 
            this.BachCards_ToolStripMenuItem.Name = "BachCards_ToolStripMenuItem";
            this.BachCards_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.BachCards_ToolStripMenuItem.Tag = "BatchCardId";
            this.BachCards_ToolStripMenuItem.Text = "الـــــدفعــــــه";
            this.BachCards_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SellingPoint_ToolStripMenuItem
            // 
            this.SellingPoint_ToolStripMenuItem.Name = "SellingPoint_ToolStripMenuItem";
            this.SellingPoint_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.SellingPoint_ToolStripMenuItem.Tag = "SpName";
            this.SellingPoint_ToolStripMenuItem.Text = "نقـــــطة البيع";
            this.SellingPoint_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Profile_ToolStripMenuItem
            // 
            this.Profile_ToolStripMenuItem.Name = "Profile_ToolStripMenuItem";
            this.Profile_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Profile_ToolStripMenuItem.Tag = "ProfileName";
            this.Profile_ToolStripMenuItem.Text = "البــــــــاقة";
            this.Profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(202, 22);
            this.toolStripMenuItem2.Tag = "Str_TotalPrice";
            this.toolStripMenuItem2.Text = "الســـــــعر";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Password_ToolStripMenuItem
            // 
            this.Password_ToolStripMenuItem.Checked = true;
            this.Password_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Password_ToolStripMenuItem.Name = "Password_ToolStripMenuItem";
            this.Password_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Password_ToolStripMenuItem.Tag = "Password";
            this.Password_ToolStripMenuItem.Text = "كلمة المرور";
            this.Password_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // UserName_ToolStripMenuItem
            // 
            this.UserName_ToolStripMenuItem.Checked = true;
            this.UserName_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.UserName_ToolStripMenuItem.Name = "UserName_ToolStripMenuItem";
            this.UserName_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.UserName_ToolStripMenuItem.Tag = "UserName";
            this.UserName_ToolStripMenuItem.Text = "الاســـــــم";
            this.UserName_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SN_ToolStripMenuItem
            // 
            this.SN_ToolStripMenuItem.Name = "SN_ToolStripMenuItem";
            this.SN_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.SN_ToolStripMenuItem.Tag = "Sn";
            this.SN_ToolStripMenuItem.Text = "الرقم التسلسلي";
            this.SN_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Status_ToolStripMenuItem
            // 
            this.Status_ToolStripMenuItem.Checked = true;
            this.Status_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Status_ToolStripMenuItem.Name = "Status_ToolStripMenuItem";
            this.Status_ToolStripMenuItem.Size = new System.Drawing.Size(202, 22);
            this.Status_ToolStripMenuItem.Tag = "Str_Status";
            this.Status_ToolStripMenuItem.Text = "الحــــالــــة";
            // 
            // Restor_ColumnToolStripMenuItem
            // 
            this.Restor_ColumnToolStripMenuItem.Name = "Restor_ColumnToolStripMenuItem";
            this.Restor_ColumnToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Restor_ColumnToolStripMenuItem.Text = "استعادة الاعمده الي الافتراضي";
            this.Restor_ColumnToolStripMenuItem.Click += new System.EventHandler(this.Restor_ColumnToolStripMenuItem_Click);
            // 
            // toolStripMenuItem4
            // 
            this.toolStripMenuItem4.Name = "toolStripMenuItem4";
            this.toolStripMenuItem4.Size = new System.Drawing.Size(221, 22);
            this.toolStripMenuItem4.Text = "عرض معلومات الكرت المحدد";
            // 
            // Copy_ToolStripMenuItem
            // 
            this.Copy_ToolStripMenuItem.Name = "Copy_ToolStripMenuItem";
            this.Copy_ToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Copy_ToolStripMenuItem.Text = "نسخ                 ctrl+c";
            this.Copy_ToolStripMenuItem.Click += new System.EventHandler(this.Copy_ToolStripMenuItem_Click);
            // 
            // Copy_AllRowToolStripMenuItem
            // 
            this.Copy_AllRowToolStripMenuItem.Name = "Copy_AllRowToolStripMenuItem";
            this.Copy_AllRowToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.Copy_AllRowToolStripMenuItem.Text = "نسخ السطر كامل ";
            // 
            // ExportExcelToolStripMenuItem
            // 
            this.ExportExcelToolStripMenuItem.Name = "ExportExcelToolStripMenuItem";
            this.ExportExcelToolStripMenuItem.Size = new System.Drawing.Size(221, 22);
            this.ExportExcelToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 10;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.ContextMenuStrip = this.dmAll_Cards1;
            this.dgv.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(6, 11);
            this.dgv.Margin = new System.Windows.Forms.Padding(0, 5, 0, 0);
            this.dgv.MultiSelect = false;
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 30;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F);
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 30;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(960, 222);
            this.dgv.TabIndex = 81;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellClick);
            this.dgv.ColumnHeaderMouseClick += new System.Windows.Forms.DataGridViewCellMouseEventHandler(this.dgv_ColumnHeaderMouseClick);
            // 
            // Spanel
            // 
            this.Spanel.AutoScroll = true;
            this.Spanel.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Spanel.BorderRadius = 0;
            this.Spanel.Controls.Add(this.panel1_side);
            this.Spanel.Controls.Add(this.rjLabel25Title);
            this.Spanel.Controls.Add(this.CBox_Port);
            this.Spanel.Controls.Add(this.rjLabel16);
            this.Spanel.Controls.Add(this.panel3_side);
            this.Spanel.Controls.Add(this.panel2_side);
            this.Spanel.Controls.Add(this.pnl_side_sn);
            this.Spanel.Controls.Add(this.CBox_Customer);
            this.Spanel.Controls.Add(this.rjLabel17);
            this.Spanel.Controls.Add(this.CBox_Radius);
            this.Spanel.Controls.Add(this.rjLabel14);
            this.Spanel.Controls.Add(this.CBox_SellingPoint);
            this.Spanel.Controls.Add(this.rjLabel15);
            this.Spanel.Controls.Add(this.CBox_Batch);
            this.Spanel.Controls.Add(this.rjButton4);
            this.Spanel.Controls.Add(this.btn_Fix);
            this.Spanel.Controls.Add(this.rjLabel4);
            this.Spanel.Controls.Add(this.CBox_Profile);
            this.Spanel.Controls.Add(this.rjLabel9);
            this.Spanel.Controls.Add(this.btn_apply);
            this.Spanel.Customizable = false;
            this.Spanel.Dock = System.Windows.Forms.DockStyle.Left;
            this.Spanel.Location = new System.Drawing.Point(0, 89);
            this.Spanel.Name = "Spanel";
            this.Spanel.Size = new System.Drawing.Size(40, 490);
            this.Spanel.TabIndex = 81;
            // 
            // panel1_side
            // 
            this.panel1_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel1_side.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1_side.Location = new System.Drawing.Point(0, 463);
            this.panel1_side.Name = "panel1_side";
            this.panel1_side.Size = new System.Drawing.Size(238, 10);
            this.panel1_side.TabIndex = 54;
            this.panel1_side.Visible = false;
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Cairo Medium", 12F);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(8087, 395);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(92, 30);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 31;
            this.rjLabel25Title.Text = "فلتره بحسب";
            this.rjLabel25Title.Visible = false;
            // 
            // CBox_Port
            // 
            this.CBox_Port.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Port.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.ListItems;
            this.CBox_Port.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Port.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.BorderRadius = 5;
            this.CBox_Port.BorderSize = 1;
            this.CBox_Port.Customizable = false;
            this.CBox_Port.DataSource = null;
            this.CBox_Port.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Port.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Port.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Port.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Port.Location = new System.Drawing.Point(20, 6);
            this.CBox_Port.Name = "CBox_Port";
            this.CBox_Port.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Port.SelectedIndex = -1;
            this.CBox_Port.Size = new System.Drawing.Size(155, 32);
            this.CBox_Port.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Port.TabIndex = 33;
            this.CBox_Port.Texts = "";
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(191, 11);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(38, 23);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 35;
            this.rjLabel16.Text = "الجهاز";
            this.rjLabel16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // panel3_side
            // 
            this.panel3_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.panel3_side.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3_side.Location = new System.Drawing.Point(238, 1);
            this.panel3_side.Name = "panel3_side";
            this.panel3_side.Size = new System.Drawing.Size(1, 472);
            this.panel3_side.TabIndex = 52;
            // 
            // panel2_side
            // 
            this.panel2_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel2_side.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2_side.Location = new System.Drawing.Point(0, 0);
            this.panel2_side.Name = "panel2_side";
            this.panel2_side.Size = new System.Drawing.Size(239, 1);
            this.panel2_side.TabIndex = 55;
            // 
            // pnl_side_sn
            // 
            this.pnl_side_sn.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_sn.BorderRadius = 10;
            this.pnl_side_sn.Controls.Add(this.CheckBox_SN);
            this.pnl_side_sn.Controls.Add(this.rjLabel10);
            this.pnl_side_sn.Controls.Add(this.CBox_SN_Compar);
            this.pnl_side_sn.Controls.Add(this.txt_SN_Start);
            this.pnl_side_sn.Controls.Add(this.rjLabel1);
            this.pnl_side_sn.Controls.Add(this.txt_SN_End);
            this.pnl_side_sn.Customizable = true;
            this.pnl_side_sn.Location = new System.Drawing.Point(9, 225);
            this.pnl_side_sn.Name = "pnl_side_sn";
            this.pnl_side_sn.Size = new System.Drawing.Size(229, 90);
            this.pnl_side_sn.TabIndex = 53;
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.Location = new System.Drawing.Point(187, 11);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_SN.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 42;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(81, 15);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(10, 40);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(155, 32);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 31;
            this.CBox_SN_Compar.Texts = "";
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(96, 9);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 43;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(169, 43);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(57, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 35;
            this.rjLabel1.Text = "التسلسلي";
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 8F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(12, 9);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(70, 24);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 43;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // CBox_Customer
            // 
            this.CBox_Customer.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Customer.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Customer.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.BorderRadius = 5;
            this.CBox_Customer.BorderSize = 1;
            this.CBox_Customer.Customizable = false;
            this.CBox_Customer.DataSource = null;
            this.CBox_Customer.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Customer.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Customer.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Customer.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Customer.Location = new System.Drawing.Point(21, 187);
            this.CBox_Customer.Name = "CBox_Customer";
            this.CBox_Customer.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Customer.SelectedIndex = -1;
            this.CBox_Customer.Size = new System.Drawing.Size(155, 32);
            this.CBox_Customer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Customer.TabIndex = 33;
            this.CBox_Customer.Texts = "";
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(186, 190);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(44, 23);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 35;
            this.rjLabel17.Text = "العميل";
            this.rjLabel17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Radius
            // 
            this.CBox_Radius.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Radius.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Radius.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Radius.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.BorderRadius = 5;
            this.CBox_Radius.BorderSize = 1;
            this.CBox_Radius.Customizable = false;
            this.CBox_Radius.DataSource = null;
            this.CBox_Radius.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Radius.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Radius.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Radius.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Radius.Location = new System.Drawing.Point(21, 151);
            this.CBox_Radius.Name = "CBox_Radius";
            this.CBox_Radius.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Radius.SelectedIndex = -1;
            this.CBox_Radius.Size = new System.Drawing.Size(155, 32);
            this.CBox_Radius.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Radius.TabIndex = 33;
            this.CBox_Radius.Texts = "";
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(185, 155);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(47, 23);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 35;
            this.rjLabel14.Text = "راديوس";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(21, 114);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(155, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(180, 118);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(56, 23);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Items.AddRange(new object[] {
            "1",
            "2",
            "3",
            "4",
            "5",
            "001-12-09-2024",
            "002-15-09-2024",
            "003-05-09-2024"});
            this.CBox_Batch.Location = new System.Drawing.Point(21, 78);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(155, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 33;
            this.CBox_Batch.Texts = "";
            // 
            // rjButton4
            // 
            this.rjButton4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton4.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.BorderRadius = 4;
            this.rjButton4.BorderSize = 1;
            this.rjButton4.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton4.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton4.FlatAppearance.BorderSize = 0;
            this.rjButton4.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton4.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton4.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton4.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconChar = FontAwesome.Sharp.IconChar.CreativeCommonsSampling;
            this.rjButton4.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton4.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton4.IconSize = 20;
            this.rjButton4.Location = new System.Drawing.Point(97, 379);
            this.rjButton4.Margin = new System.Windows.Forms.Padding(0);
            this.rjButton4.Name = "rjButton4";
            this.rjButton4.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.rjButton4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton4.Size = new System.Drawing.Size(25, 31);
            this.rjButton4.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton4.TabIndex = 49;
            this.rjButton4.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton4.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton4.UseVisualStyleBackColor = false;
            this.rjButton4.Visible = false;
            // 
            // btn_Fix
            // 
            this.btn_Fix.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Fix.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.BorderRadius = 4;
            this.btn_Fix.BorderSize = 1;
            this.btn_Fix.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Fix.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Fix.FlatAppearance.BorderSize = 0;
            this.btn_Fix.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Fix.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Fix.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Fix.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Fix.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconChar = FontAwesome.Sharp.IconChar.Unlock;
            this.btn_Fix.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Fix.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Fix.IconSize = 20;
            this.btn_Fix.Location = new System.Drawing.Point(123, 379);
            this.btn_Fix.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Fix.Name = "btn_Fix";
            this.btn_Fix.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Fix.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Fix.Size = new System.Drawing.Size(25, 31);
            this.btn_Fix.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Fix.TabIndex = 49;
            this.btn_Fix.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Fix.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Fix.UseVisualStyleBackColor = false;
            this.btn_Fix.Visible = false;
            this.btn_Fix.Click += new System.EventHandler(this.btn_Fix_Click);
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(186, 85);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(44, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 35;
            this.rjLabel4.Text = "الدفعه";
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(21, 42);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(155, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(189, 47);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.Size = new System.Drawing.Size(38, 23);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 15;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Droid Arabic Kufi", 10.2F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(42, 321);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.Size = new System.Drawing.Size(122, 40);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 51;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_apply.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // timer
            // 
            this.timer.Interval = 15;
            this.timer.Tick += new System.EventHandler(this.timer_Tick);
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.rjPanel2);
            this.panel1.Controls.Add(this.dgv);
            this.panel1.Controls.Add(this.rjLabel21);
            this.panel1.Controls.Add(this.dgv2);
            this.panel1.Controls.Add(this.rjPanel12);
            this.panel1.Location = new System.Drawing.Point(9, 92);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(976, 484);
            this.panel1.TabIndex = 83;
            // 
            // Form_UM_Sales_Device
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "Form_UM_Sales_Device";
            this.ClientSize = new System.Drawing.Size(1000, 629);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_UM_Sales_Device";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_UM_Sales_Device";
            this.Load += new System.EventHandler(this.Form_UM_Sales_Device_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel_topFilter.ResumeLayout(false);
            this.rjPanel_topFilter.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.rjPanel12.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv2)).EndInit();
            this.dmAll_Cards1.ResumeLayout(false);
            this.dmAll_Cards.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.Spanel.ResumeLayout(false);
            this.Spanel.PerformLayout();
            this.pnl_side_sn.ResumeLayout(false);
            this.pnl_side_sn.PerformLayout();
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel_topFilter;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJDatePicker rjDateTime_To;
        private RJControls.RJDatePicker rjDateTime_From;
        private RJControls.RJPanel rjPanel12;
        private RJControls.RJButton btn_;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJButton btn_Print;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJTextBox rjTextBox1;
        private RJControls.RJLabel rjLabel21;
        private RJControls.RJButton btn_search;
        private RJControls.RJButton btn_Print_details;
        private RJControls.RJPanel Spanel;
        private RJControls.RJComboBox CBox_Customer;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJComboBox CBox_Radius;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJComboBox CBox_Port;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJComboBox CBox_Batch;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJComboBox CBox_Profile;
        private RJControls.RJLabel rjLabel9;
        private System.Windows.Forms.Panel panel3_side;
        private RJControls.RJButton btn_apply;
        private RJControls.RJLabel rjLabel25Title;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJDataGridView dgv2;
        private RJControls.RJPanel pnl_side_sn;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJDropdownMenu dmAll_Cards1;
        private System.Windows.Forms.ToolStripMenuItem View_Hide_toolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem NasPort_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Uptim_StripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Download_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Price_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Count_FirstLogin_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CountCard_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CountSession_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem PrintAll_toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem Print_DeviceCtrlcToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Excel_ToolStripMenuItem;
        private System.Windows.Forms.Timer timer;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Panel panel1_side;
        private System.Windows.Forms.Panel panel2_side;
        private RJControls.RJTextBox txt_countDevice;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJButton btn_Fix;
        private RJControls.RJButton rjButton4;
        private System.Windows.Forms.Panel panel1;
        private RJControls.RJComboBox Cbox_View;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem1;
        private System.Windows.Forms.ToolStripMenuItem Status_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SN_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem UserName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Password_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripMenuItem Profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SellingPoint_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BachCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_TransferLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_DownloadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UploadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_Up_Down_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_RegDate_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_LastSeenAt_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_FirstUse_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTillTime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTimeLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTransferLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Descr_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CusName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Count_profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem3;
        private System.Windows.Forms.ToolStripMenuItem LastSynDb_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem PageNumber_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Restor_ColumnToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem4;
        private System.Windows.Forms.ToolStripMenuItem Copy_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Copy_AllRowToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ExportExcelToolStripMenuItem;
        private RJControls.RJCheckBox check_with_Commi;
        private RJControls.RJToggleButton ToggleButton_Detail;
        private RJControls.RJToggleButton ToggleButton_Monthly;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJCheckBox CheckBox_To_Date;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJRadioButton Radi_Details;
        private RJControls.RJRadioButton Radi_Month;
        private RJControls.RJRadioButton Radi_Days;
    }
}