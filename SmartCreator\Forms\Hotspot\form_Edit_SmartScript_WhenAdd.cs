﻿using iTextSharp.text;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.Forms.Hotspot
{
    public partial class form_Edit_SmartScript_WhenAdd : RJForms.RJChildForm
    {
        public bool is_save=false;
        public form_Edit_SmartScript_WhenAdd()
        {
            InitializeComponent();
            utils utils = new utils();
            utils.Control_textSize1(this);

            Set_Font();

        }
        public form_Edit_SmartScript_WhenAdd(bool save_time,bool save_download,bool save_session,bool dayOrhour)
        {
            InitializeComponent();
            //Set_Font();
            utils.Control_textSize(pnlClientArea);

            this.btnMaximize.Visible = false;
            this.btnMinimize.Visible = false;
            this.miExitSnap.Visible = false;
            this.miSnapLeft.Visible = false;
            this.miSnapRight.Visible = false;
            this.Resizable = false;
            //this.RightToLeft=RightToLeft.Yes;
            //this.RightToLeftLayout=true;
            
            //this.SuspendLayout();
            //CheckBox_Save_time.RightToLeft = RightToLeft.Yes;
            //CheckBox_Save_download.RightToLeft = RightToLeft.Yes;
            //CheckBox_Save_session.RightToLeft = RightToLeft.Yes;
            //CheckBox_byDayOrHour.RightToLeft = RightToLeft.Yes;
            //CheckBox_Save_time.Anchor = AnchorStyles.Right | AnchorStyles.Top;
            //CheckBox_Save_download.Anchor = AnchorStyles.Right | AnchorStyles.Top;
            //CheckBox_Save_session.Anchor = AnchorStyles.Right | AnchorStyles.Top;
            //CheckBox_byDayOrHour.Anchor = AnchorStyles.Right | AnchorStyles.Top;


            //CheckBox_Save_download.RightToLeft = RightToLeft.Yes;
            //CheckBox_Save_session.RightToLeft = RightToLeft.Yes;
            //CheckBox_byDayOrHour.RightToLeft = RightToLeft.Yes;
            CheckBox_Save_time.Checked = save_time;
            CheckBox_Save_download.Checked = save_download;
            CheckBox_Save_session.Checked = save_session;
            CheckBox_byDayOrHour.Checked = dayOrhour;

            
        }

        private void Set_Font()
        {
            System.Drawing.Font fnt = Program.GetCustomFont(Resources.DroidKufi_Regular, 9 , FontStyle.Regular);
            //System.Drawing.Font fnt = Program.GetCustomFont(Resources.DroidKufi_Regular, 8 * utils.ScaleFactor, FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        //lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 8f, false, GraphicsUnit.Point, 0);
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
         
            //System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10 * utils.ScaleFactor , FontStyle.Bold);
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10  , FontStyle.Bold);
          
            btnSave.Font = title_font;
            utils.Control_textSize(pnlClientArea);

        }
        private void btnSave_Click(object sender, EventArgs e)
        {
            is_save = true;
            this.Close();
        }

        private void CheckBox_Save_time_CheckedChanged(object sender, EventArgs e)
        {
            if(CheckBox_Save_time.Checked) 
            CheckBox_byDayOrHour.Checked=false;
        }

        private void CheckBox_byDayOrHour_CheckedChanged(object sender, EventArgs e)
        {
            if (CheckBox_byDayOrHour.Checked)
                CheckBox_Save_time.Checked = false;
        }
    }
}
