﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Threading;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_Add_Profile_Balance : RJChildForm
    {
        bool firstUser=true;
        public bool success = false;
        HashSet<UmUser> Users = new HashSet<UmUser>();
        [Obsolete]
        Mk_DataAccess mk_DataAccess;

        public Form_Add_Profile_Balance(HashSet<UmUser> users)
        {
            InitializeComponent();
            this.Text = "اضافة باقه جديد";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Add New Profile";
            }
            //btnSave.BackColor = UIAppearance.StyleColor;
            lblTitle.Select();//Not focus on text boxes when starting the form.
            Users = users;


            
            lblTitle.Font=btnSave.Font = Program.GetCustomFont(Resources.DroidSansArabic, 12 , FontStyle.Bold);
            rjLabel1.Font=rjLabel4.Font=rjLabel5.Font=rjLabel9.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10 , FontStyle.Regular);

            utils.Control_textSize(pnlClientArea);
            return;


            Control_Loop(pnlClientArea);
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        string profile = "";

        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (CBox_Profile.SelectedIndex == -1 || CBox_Profile.Text == "")
            {
                MessageBox.Show("اختر البروفايل");
                return;
            }
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من  تعبئة الرصيد او اضافة الباقة المحددة", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

             profile = CBox_Profile.Text;

            if (Toggle_AddBalance.Checked)
            {
                using (Form_WaitForm frm = new Form_WaitForm(add_balance))
                {
                    Global_Variable.StartThreadProcessFromMK = true;
                    frm.ShowDialog();
                    if (frm.IsCancel)
                    {
                        if (mk_DataAccess != null) mk_DataAccess.MkConnClose();
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية    ");
                    }
                }
            }
            else if (Toggle_Clear_Profile.Checked)
            {
                using (Form_WaitForm frm = new Form_WaitForm(clear_add_Balance))
                {
                    Global_Variable.StartThreadProcessFromMK = true;
                    frm.ShowDialog();
                    if (frm.IsCancel)
                    {
                        if (mk_DataAccess != null) mk_DataAccess.MkConnClose();
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية    ");
                    }
                }
            }
            else if (Toggle_RemoveAndAddBalacne.Checked)
            {
                using (Form_WaitForm frm = new Form_WaitForm(RemoveAndAddBalacne))
                {
                    Global_Variable.StartThreadProcessFromMK = true;
                    frm.ShowDialog();
                    if (frm.IsCancel)
                    {
                        if (mk_DataAccess != null) mk_DataAccess.MkConnClose();
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية    ");
                    }
                }
            }
            //ThreadStart theprogress = new ThreadStart(() => add_balance(CBox_Profile.Text));
            //Thread startprogress = new Thread(theprogress);
            //startprogress.Name = "Update ProgressBar";
            //startprogress.Start();

        }

        [Obsolete]
        private void add_balance()
        {

            //Mk_DataAccess MKDA=new Mk_DataAccess();
            //if (Toggle_AddBalance.Checked)
            //{
            //using (Mk_DataAccess mk = new Mk_DataAccess())
            //{
            //}
            mk_DataAccess=new Mk_DataAccess();
                HashSet<UmUser> res = mk_DataAccess.Add_Profile_To_Users(Users, profile);
                if (res.Count > 0)
                {
                    success = true;
                //RJMessageBox.Show("تمت العملية");
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تمت اضافة الباقة بنجاح");

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                           (System.Windows.Forms.MethodInvoker)delegate ()
                           {
                               this.Close();
                           });
                
                }
            //}
            //else if (Toggle_Clear_Profile.Checked)
            //{
            //    HashSet<UmUser> res = mk_DataAccess.Process_UserManager_ByID(Users, "clearProfile");
            //    if (res.Count == Users.Count)
            //    {
            //        HashSet<UmUser> resProfile = mk_DataAccess.Add_Profile_To_Users(Users, profile);
            //        if (resProfile.Count > 0 && resProfile.Count < Users.Count)
            //        {
            //            HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, profile);
            //        }
            //        success = true;
            //        this.Close();
            //    }
            //    else if (res.Count > 0 && res.Count < Users.Count)
            //    {
            //        HashSet<UmUser> res2 = mk_DataAccess.Process_UserManager_ByID(res, "clearProfile");
            //        HashSet<UmUser> resProfile = mk_DataAccess.Add_Profile_To_Users(Users, profile);

            //        if (resProfile.Count > 0 && resProfile.Count < Users.Count)
            //        {
            //            HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, profile);
            //        }
            //        success = true;
            //        this.Close();
            //    }
            //    else
            //    {
            //        RJMessageBox.Show("حدث خطاء");
            //        return;
            //    }
            //    RJMessageBox.Show("تمت العملية");
            //}
            //else if (Toggle_RemoveAndAddBalacne.Checked)
            //{
            //    HashSet<UmUser> res = mk_DataAccess.Process_UserManager_ByID(Users, "DeleteFromServer");
            //    if (res.Count == Users.Count)
            //    {
            //        HashSet<UmUser> resProfile = mk_DataAccess.Add_Group_UserManager_WithProfile(Users, CBox_Profile.Text);
            //        //if (resProfile.Count > 0 && resProfile.Count < Users.Count)
            //        //{
            //        //    HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, CBox_Profile.Text);
            //        //}
            //        success = true;
            //        this.Close();
            //    }

            //    RJMessageBox.Show("تمت العملية");

            //}
            Global_Variable.StartThreadProcessFromMK = false;
        }

        [Obsolete]
        void clear_add_Balance()
        {
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            HashSet<UmUser> res = mk_DataAccess.Process_UserManager_ByID(Users, "clearProfile");
            if (res.Count == Users.Count)
            {
                HashSet<UmUser> resProfile = mk_DataAccess.Add_Profile_To_Users(Users, profile);
                if (resProfile.Count > 0 && resProfile.Count < Users.Count)
                {
                    HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, profile);
                }
                success = true;
                //RJMessageBox.Show("تمت العملية");
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تمت اضافة الباقة بنجاح");

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                           (System.Windows.Forms.MethodInvoker)delegate ()
                           {
                               this.Close();
                           });


            //this.Close();
            }
            else if (res.Count > 0 && res.Count < Users.Count)
            {
                HashSet<UmUser> res2 = mk_DataAccess.Process_UserManager_ByID(res, "clearProfile");
                HashSet<UmUser> resProfile = mk_DataAccess.Add_Profile_To_Users(Users, profile);

                if (resProfile.Count > 0 && resProfile.Count < Users.Count)
                {
                    HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, profile);
                }
                success = true;
                RJMessageBox.Show("تمت العملية");
                this.Close();
            }
            else
            {
                RJMessageBox.Show("حدث خطاء");
                return;
            }
            //RJMessageBox.Show("تمت العملية");
            Global_Variable.StartThreadProcessFromMK = true;
           
        }

        [Obsolete]
        void RemoveAndAddBalacne()
        {
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            HashSet<UmUser> res = mk_DataAccess.Process_UserManager_ByID(Users, "DeleteFromServer");
            if (res.Count == Users.Count)
            {
                //============================
                Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
                var delectfromDB = sql_DataAccess.Execute<UmUser>($"update UmUser set DeleteFromServer=1 where Sn_Name=@Sn_Name", Users);
                //==========================================
                
                HashSet<UmUser> resProfile = mk_DataAccess.Add_Group_UserManager_WithProfile(Users, CBox_Profile.Text);

                if (resProfile.Count > 0 && resProfile.Count < Users.Count)
                {
                    HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, CBox_Profile.Text);
                }
                success = true;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تمت العملية بنجاح");
                Global_Variable.Uc_StatusBar.lblDescription.Invoke((System.Windows.Forms.MethodInvoker)delegate ()
                           {
                               this.Close();
                           });

            }
            else if (res.Count > 0 && res.Count < Users.Count)
            {
                HashSet<UmUser> res2 = mk_DataAccess.Process_UserManager_ByID(res, "clearProfile");
                HashSet<UmUser> resProfile = mk_DataAccess.Add_Profile_To_Users(Users, profile);

                if (resProfile.Count > 0 && resProfile.Count < Users.Count)
                {
                    HashSet<UmUser> resProfile2 = mk_DataAccess.Add_Profile_To_Users(resProfile, profile);
                }
                success = true;
                RJMessageBox.Show("تمت العملية");
                this.Close();
            }
            else
            {
                RJMessageBox.Show("حدث خطاء");
                return;
            }
            //RJMessageBox.Show("تمت العملية");
            Global_Variable.StartThreadProcessFromMK = true;


            RJMessageBox.Show("تمت العملية");
            Global_Variable.StartThreadProcessFromMK = false;

        }
        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            //try
            //{
            //    List<UserManager_Customer> sp = Global_Variable.UM_Customer;
            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    //comboSource.Add("0", "");
            //    foreach (UserManager_Customer s in sp)
            //        comboSource.Add(s.Name, s.Name);

            //    CBox_Customer.DataSource = new BindingSource(comboSource, null);
            //    CBox_Customer.DisplayMember = "Value";
            //    CBox_Customer.ValueMember = "Key";
            //    CBox_Customer.SelectedIndex = 0;
            //    //CBox_Customer.Text = "";
            //}
            //catch { }
        }

        private void Form_Add_Profile_Balance_Load(object sender, EventArgs e)
        {
            Get_Cbox_Profile();
            Get_UMCustomer();
            firstUser = false;
        }

        private void Toggle_AddBalance_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if(Toggle_AddBalance.Checked)
            {
                firstUser = true;

                Toggle_RemoveAndAddBalacne.Checked=false;
            Toggle_Clear_Profile.Checked=false;
                firstUser = false;

            }
        }

        private void Toggle_Remove_Profile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (Toggle_Clear_Profile.Checked)
            {
                firstUser=true;
                Toggle_RemoveAndAddBalacne.Checked = false;
                Toggle_AddBalance.Checked = false;
                firstUser = false;
            }
        }

        private void Toggle_RemoveAndAddBalacne_CheckedChanged(object sender, EventArgs e)
        {
            if (firstUser)
                return;
            if (Toggle_RemoveAndAddBalacne.Checked)
            {
                firstUser = true;

                Toggle_Clear_Profile.Checked = false;
                Toggle_AddBalance.Checked = false;
                firstUser = false;
            }
        }
    }
}
