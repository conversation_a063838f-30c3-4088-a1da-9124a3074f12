﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_PrintForm : RJChildForm
    {
        //private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA = null;


        public Form_PrintForm(string _ServerType = "UmUser")
        {
            InitializeComponent();
            ServerType = _ServerType;
            //dicUsers = _dicUsers;

            rjLabel9.Font = lbl_TemplateCards.Font = rjLabel4.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10f * utils.ScaleFactor, FontStyle.Regular);
            lbl_desc_profile.Font  = Program.GetCustomFont(Resources.DroidSansArabic, 9f * utils.ScaleFactor, FontStyle.Regular);
            rjLabel2.Font  = Program.GetCustomFont(Resources.DroidKufi_Bold, 14f * utils.ScaleFactor, FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);

            Smart_DA = new Smart_DataAccess();
            Get_TemplateCardsFromDB();
            Get_Cbox_Profile();


            this.Text = "اخراج الي ملف Pdf";

            if (UIAppearance.Theme == UITheme.Light)
                lbl_desc_profile.ForeColor = Color.Red;

            if (UIAppearance.Language_ar == false)
                this.Text = "Create Pdf File ";
        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> p = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile s in p)
                    comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

                CBox_Profile.DataSource = new BindingSource(comboSource, null);
                CBox_Profile.DisplayMember = "Value";
                CBox_Profile.ValueMember = "Key";
                CBox_Profile.Text = "";
            }
            catch { }
        }

        private void Get_TemplateCardsFromDB()
        {
            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
                CBox_TemplateCards.Text = "";
            }
            catch { }
        }

        public DataGridViewSelectedRowCollection dgv;
        public string ServerType = "UmUser";
        private void btnPrint_Click(object sender, EventArgs e)
        {

            if(CBox_Profile.Text==""||CBox_Profile.SelectedIndex<=0)
            {
                RJMessageBox.Show("اختار الباقة");
                return; 
            }
            if (CBox_TemplateCards.Text == "" || CBox_TemplateCards.SelectedIndex <= 0)
            {
                RJMessageBox.Show("حدد قالب الطباعة");
                return;
            }
            try
            {
                Dictionary<string, NewUserToAdd> dicUsers = new Dictionary<string, NewUserToAdd>();

                if (ServerType == "UmUser")
                {
                    foreach (DataGridViewRow row in dgv)
                    {
                        if (row.Cells["ProfileName"].Value.ToString() == CBox_Profile.SelectedValue.ToString() && row.Cells["DeleteFromServer"].Value.ToString() == "0")
                        {
                            UmUser um = ((UmUser)row.DataBoundItem);
                            NewUserToAdd new_usr = new NewUserToAdd();

                            new_usr.Name = row.Cells["Username"].Value.ToString();
                            new_usr.Password = (string)row.Cells["Password"].Value;
                            new_usr.SN = Convert.ToDouble(row.Cells["SN"].Value.ToString());

                            dicUsers[new_usr.Name] = new_usr;

                        }
                    }
                }
                if (ServerType == "HSUser")
                {
                    foreach (DataGridViewRow row in dgv)
                    {
                        if (row.Cells["ProfileName"].Value.ToString() == CBox_Profile.SelectedValue.ToString() && row.Cells["DeleteFromServer"].Value.ToString() == "0")
                        {
                            HSUser um = ((HSUser)row.DataBoundItem);
                            NewUserToAdd new_usr = new NewUserToAdd();

                            new_usr.Name = row.Cells["Username"].Value.ToString();
                            new_usr.Password = (string)row.Cells["Password"].Value;
                            new_usr.SN = Convert.ToDouble(row.Cells["SN"].Value.ToString());

                            dicUsers[new_usr.Name] = new_usr;

                        }
                    }
                }

                if (dicUsers.Count > 0)
                    print_pdf(dicUsers);
                else
                    RJMessageBox.Show("لا يوجد كروت للطباعة");
                
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void print_pdf(Dictionary<string, NewUserToAdd> dicUsers)
        {
            string pathfile = "";
            CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            CardsTemplate card = new CardsTemplate();
            string profilename = CBox_Profile.SelectedValue.ToString();
            string TemplateName = CBox_TemplateCards.Text.ToString();
            SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);

            UmProfile profile = new UmProfile() ;
            if (ServerType == "HSUser")
            {
                var HsProfil = new List<HSLocalProfile>();
                HSLocalProfile hotspot = new HSLocalProfile();
                HsProfil = hotspot.Ge_Local_Hotspot();

                HSLocalProfile HSprofile = HsProfil.Find(x => x.Name == profilename);
                if (HSprofile != null)
                {
                    profile.Name = HSprofile.Name;
                    profile.Is_percentage = HSprofile.Is_percentage;
                    profile.Percentage = HSprofile.Percentage;
                    profile.PercentageType = HSprofile.PercentageType;

                    profile.UptimeLimit=HSprofile.UptimeLimit;
                    profile.TransferLimit=HSprofile.TransferLimit;
                    profile.Price=HSprofile.Price;
                    profile.Price_Disply=HSprofile.Price_Display;
                    profile.Validity=HSprofile.Validity;
                  
                }
                else
                {
                    profile.Name = profilename;
                }
            }
            else if (ServerType == "UmUser")
            {
                profile = Global_Variable.UM_Profile.Find(x => x.Name == profilename);
            }
                

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();



            SaveFileDialog saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Title = "حدد مكان حفظ الملف";
            string Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + dicUsers.Count + "Cards)" + "_(" + profilename + ")";
            saveFileDialog1.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialog1.FileName = "Cards_" + Public_file_Name;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialog1.FileName;
            }
            else
            {
                return;
            }

            string profileName = profile.Name;
            string price = profile.Price.ToString();
            string Validity = profile.Validity.ToString();
            string time = profile.UptimeLimit.ToString();  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            string sizeTransfer = profile.TransferLimit.ToString();
            string SP = "";
            string numberPrint = "";
            string DatePrint = "";
            string Note_On_Pages_text = "";


            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);

                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    if (card.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                        }
                    }
                    if (card.cardsItems.Validity.title_show)
                    {
                        Validity = card.cardsItems.Validity.title_text + " " + Validity;
                    }
                }
                if (card.cardsItems.Time.Enable)
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                        if (card.cardsItems.Time.title_show)
                        {
                            time = card.cardsItems.Time.title_text + " " + time;
                        }

                    }
                }
                if (card.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                        if (card.cardsItems.Size.title_show)
                        {
                            sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                        }

                    }
                }

                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrint != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.get_BatchCards_my_sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();  
                    numberPrint = (batchNumber + 1).ToString();
                }
                //if (card.cardsItems.SP.Enable)
                //{
                //    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != null)
                //    {
                //        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                //        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                //        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                //            SP = (Show_sp.Code).ToString();
                //        else
                //            SP = (Show_sp.UserName).ToString();
                //        if (card.cardsItems.SP.title_show)
                //        {
                //            SP = card.cardsItems.SP.title_text + " " + SP;
                //        }
                //    }
                //}

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                if (cardTable1.cardsItems.Price.Enable)
                {
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = price + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    if (cardTable1.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                        }
                    }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    try
                    {
                        if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        }
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                    }
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();
                    numberPrint = (batchNumber + 1).ToString();
                }
               
                //if (cardTable1.cardsItems.SP.Enable)
                //{
                //    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != null)
                //    {
                //        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                //        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                //        if (Show_sp != null)
                //        {
                //            if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                //                SP = (Show_sp.Code).ToString();
                //            else
                //                SP = (Show_sp.UserName).ToString();
                //        }
                //        if (card.cardsItems.SP.title_show)
                //        {
                //            SP = card.cardsItems.SP.title_text + " " + SP;
                //        }
                //    }
                //}

                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("numberPrint", numberPrint);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (Sourcecard.type == "design")
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);
            else if (Sourcecard.type == "table_Desigen1")
            {
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            }
            try
            {
                System.Diagnostics.Process.Start(pathfile);
                System.Diagnostics.Process.Start(Path.GetDirectoryName(saveFileDialog1.FileName));
            }
            catch { }
        }
    }
}
