﻿using SmartCreator.Forms.Hotspot;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Utils;
using SmartCreator.Properties;
using SmartCreator.RJControls;

namespace SmartCreator.Forms.UserManager.Maintenance
{
    public partial class Form_Maintenances_UserManager : RJChildForm
    {
        Form_Process_UserManager form_Process_UserManager;
        Form_Backup_Files_UserManager form_Backup_Files_UserManager;
        Form_Scheduler_UserManager form_Scheduler_UserManager;

        bool _first_Form_Process_UserManager = true;
        bool _form_Backup_Files_UserManager = true;
        bool _first_Scheduler_UserManager = true;

        public Form_Maintenances_UserManager()
        {
            InitializeComponent();
            utils utils = new utils();
            utils.Control_textSize1(this);

            this.Text = "ادارة صيانة اليوزمنجر";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Maintenances User-Manage";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
            Set_Font();

        }
        private void Set_Font()
        {
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 8 * utils.ScaleFactor, FontStyle.Bold);
            //Font fnt = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 8f, true);
            btn_Process.Font = fnt;
            btn_Scheduler.Font = fnt;
            //btn_Finsh_Cards_Title.Font = fnt;
            //btn_Sessions_Cards_Title.Font = fnt;
            btn_Backup_Files.Font = fnt;
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_Process_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Process);

            if (_first_Form_Process_UserManager)
            {
                _first_Form_Process_UserManager = false;
                form_Process_UserManager = new Form_Process_UserManager();
                form_Process_UserManager.TopLevel = false;
                form_Process_UserManager.IsChildForm = true;
                form_Process_UserManager.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Process_UserManager);
                this.panel_Tab_Container.Tag = form_Process_UserManager;
                form_Process_UserManager.Show(); //show on desktop panel  
                form_Process_UserManager.BringToFront();
                form_Process_UserManager.Focus();
                //formAllCardsUserHotspot.LoadDataGridviewData();

            }
            else
            {
                form_Process_UserManager.BringToFront();
                form_Process_UserManager.Show();
                form_Process_UserManager.Focus();
            }

        }

        private void btn_Backup_Files_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Backup_Files);

            if (_form_Backup_Files_UserManager)
            {
                _form_Backup_Files_UserManager = false;
                form_Backup_Files_UserManager = new Form_Backup_Files_UserManager();
                form_Backup_Files_UserManager.TopLevel = false;
                form_Backup_Files_UserManager.IsChildForm = true;
                form_Backup_Files_UserManager.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Backup_Files_UserManager);
                this.panel_Tab_Container.Tag = form_Backup_Files_UserManager;
                form_Backup_Files_UserManager.Show(); //show on desktop panel  
                form_Backup_Files_UserManager.BringToFront();
                form_Backup_Files_UserManager.Focus();
            }
            else
            {
                form_Backup_Files_UserManager.BringToFront();
                form_Backup_Files_UserManager.Show();
                form_Backup_Files_UserManager.Focus();
            }
            this.Refresh();
        }

        private void btn_Sechulr_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Scheduler);

            if (_first_Scheduler_UserManager)
            {
                _first_Scheduler_UserManager = false;
                form_Scheduler_UserManager = new Form_Scheduler_UserManager();
                form_Scheduler_UserManager.TopLevel = false;
                form_Scheduler_UserManager.IsChildForm = true;
                form_Scheduler_UserManager.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Scheduler_UserManager);
                this.panel_Tab_Container.Tag = form_Scheduler_UserManager;

                form_Scheduler_UserManager.Show(); //show on desktop panel  
                form_Scheduler_UserManager.BringToFront();
                form_Scheduler_UserManager.Focus();
            }
            else
            {
                form_Scheduler_UserManager.BringToFront();
                form_Scheduler_UserManager.Show();
                form_Scheduler_UserManager.Focus();
            }
            this.Refresh();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            
            btn_Process_Click(sender, e);

        }

        private void Form_Maintenances_UserManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }
    }
}
