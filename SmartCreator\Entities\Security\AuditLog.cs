﻿//using SmartCreator.Data.CustomORM;
//using SmartCreator.Data.DirectORM;
using System;
using DataAnnotations = System.ComponentModel.DataAnnotations;
//using SmartCreator.Entities.Security;
using SmartCreator.Models;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;
namespace SmartCreator.Entities
{

    /// <summary>
    /// نموذج سجل الأحداث والمراقبة
    /// </summary>
    [Table("AuditLogs")]
    public class AuditLog : BaseEntity
    {
        [Column("UserId")]
        //[ForeignKey("Users", "Id")]
        public int? UserId { get; set; }

        [Column("Username")]
        [DataAnnotations.MaxLength(50)]
        public string? Username { get; set; }

        [Column("Action")]
        [DataAnnotations.Required]
        [DataAnnotations.MaxLength(100)]
        public string Action { get; set; } = string.Empty;

        [Column("EntityType")]
        [DataAnnotations.Required]
        [DataAnnotations.MaxLength(100)]
        public string EntityType { get; set; } = string.Empty;

        [Column("EntityId")]
        public int? EntityId { get; set; }

        [Column("EntityName")]
        [DataAnnotations.MaxLength(200)]
        public string? EntityName { get; set; }

        [Column("OldValues")]
        public string? OldValues { get; set; }

        [Column("NewValues")]
        public string? NewValues { get; set; }

        [Column("Changes")]
        public string? Changes { get; set; }

        [Column("IPAddress")]
        [DataAnnotations.MaxLength(45)]
        public string? IPAddress { get; set; }

        [Column("UserAgent")]
        [DataAnnotations.MaxLength(500)]
        public string? UserAgent { get; set; }

        [Column("SessionId")]
        [DataAnnotations.MaxLength(100)]
        public string? SessionId { get; set; }

        [Column("Module")]
        [DataAnnotations.Required]
        [DataAnnotations.MaxLength(100)]
        public string Module { get; set; } = string.Empty;

        [Column("Severity")]
        [DataAnnotations.Required]
        public AuditSeverity Severity { get; set; } = AuditSeverity.Information;

        [Column("Success")]
        [DefaultValue(true)]
        public bool Success { get; set; } = true;

        [Column("ErrorMessage")]
        [DataAnnotations.MaxLength(1000)]
        public string? ErrorMessage { get; set; }

        [Column("Duration")]
        public long? Duration { get; set; }

        [Column("AdditionalData")]
        public string? AdditionalData { get; set; }

        [Column("Timestamp")]
        [DefaultValue("CURRENT_TIMESTAMP")]
        public DateTime Timestamp { get; set; } = DateTime.Now;

        // خصائص التنقل
        [NotMapped]
        public User? User { get; set; }

        // خصائص محسوبة
        [NotMapped]
        public string SeverityText => Severity switch
        {
            AuditSeverity.Critical => "حرج",
            AuditSeverity.Error => "خطأ",
            AuditSeverity.Warning => "تحذير",
            AuditSeverity.Information => "معلومات",
            AuditSeverity.Debug => "تصحيح",
            _ => "غير محدد"
        };

        [NotMapped]
        public string ActionText => Action switch
        {
            "Create" => "إنشاء",
            "Update" => "تحديث",
            "Delete" => "حذف",
            "View" => "عرض",
            "Login" => "تسجيل دخول",
            "Logout" => "تسجيل خروج",
            "Export" => "تصدير",
            "Print" => "طباعة",
            "Approve" => "موافقة",
            "Reject" => "رفض",
            "Cancel" => "إلغاء",
            "Post" => "ترحيل",
            "Unpost" => "إلغاء ترحيل",
            _ => Action
        };

        [NotMapped]
        public string DurationText => Duration.HasValue ? $"{Duration.Value} مللي ثانية" : "";
    }

    /// <summary>
    /// مستويات خطورة الأحداث
    /// </summary>
    public enum AuditSeverity
    {
        /// <summary>
        /// تصحيح
        /// </summary>
        Debug = 0,

        /// <summary>
        /// معلومات
        /// </summary>
        Information = 1,

        /// <summary>
        /// تحذير
        /// </summary>
        Warning = 2,

        /// <summary>
        /// خطأ
        /// </summary>
        Error = 3,

        /// <summary>
        /// حرج
        /// </summary>
        Critical = 4
    }

    /// <summary>
    /// نموذج جلسة المستخدم
    /// </summary>
    [Table("UserSessions")]
    public class UserSession : BaseEntity
    {
        [Column("UserId")]
        [DataAnnotations.Required]
        //[ForeignKey("Users", "Id")]
        public int UserId { get; set; }

        [Column("SessionId")]
        [DataAnnotations.Required]
        [DataAnnotations.MaxLength(100)]
        //[Index(Name = "IX_UserSession_SessionId", IsUnique = true)]
        public string SessionId { get; set; } = string.Empty;

        [Column("IPAddress")]
        [DataAnnotations.MaxLength(45)]
        public string? IPAddress { get; set; }

        [Column("UserAgent")]
        [DataAnnotations.MaxLength(500)]
        public string? UserAgent { get; set; }

        [Column("LoginTime")]
        [DefaultValue("CURRENT_TIMESTAMP")]
        public DateTime LoginTime { get; set; } = DateTime.Now;

        [Column("LastActivityTime")]
        public DateTime LastActivityTime { get; set; } = DateTime.Now;

        [Column("LogoutTime")]
        public DateTime? LogoutTime { get; set; }

        [Column("IsActive")]
        [DefaultValue(true)]
        public bool IsActive { get; set; } = true;

        [Column("LogoutReason")]
        [DataAnnotations.MaxLength(100)]
        public string? LogoutReason { get; set; }

        [Column("DeviceInfo")]
        [DataAnnotations.MaxLength(500)]
        public string? DeviceInfo { get; set; }

        [Column("Location")]
        [DataAnnotations.MaxLength(200)]
        public string? Location { get; set; }

        // خصائص التنقل
        [NotMapped]
        public User? User { get; set; }

        // خصائص محسوبة
        [NotMapped]
        public TimeSpan? SessionDuration => LogoutTime.HasValue ?
            LogoutTime.Value - LoginTime :
            DateTime.Now - LoginTime;

        [NotMapped]
        public string SessionDurationText => SessionDuration.HasValue ?
            $"{SessionDuration.Value.Hours:D2}:{SessionDuration.Value.Minutes:D2}:{SessionDuration.Value.Seconds:D2}" :
            "جلسة نشطة";

        [NotMapped]
        public bool IsExpired => !IsActive || (LogoutTime.HasValue && LogoutTime.Value < DateTime.Now);

        [NotMapped]
        public string StatusText => IsActive && !LogoutTime.HasValue ? "نشط" : "منتهي";
    }

    /// <summary>
    /// أنواع الأحداث المحددة مسبقاً
    /// </summary>
    public static class AuditActions
    {
        // أحداث المصادقة
        public const string Login = "Login";
        public const string Logout = "Logout";
        public const string LoginFailed = "LoginFailed";
        public const string PasswordChanged = "PasswordChanged";
        public const string PasswordReset = "PasswordReset";
        public const string AccountLocked = "AccountLocked";
        public const string AccountUnlocked = "AccountUnlocked";

        // أحداث CRUD
        public const string Create = "Create";
        public const string Read = "Read";
        public const string Update = "Update";
        public const string Delete = "Delete";
        public const string View = "View";

        // أحداث العمليات
        public const string Export = "Export";
        public const string Print = "Print";
        public const string Approve = "Approve";
        public const string Reject = "Reject";
        public const string Cancel = "Cancel";
        public const string Post = "Post";
        public const string Unpost = "Unpost";
        public const string Close = "Close";
        public const string Reopen = "Reopen";

        // أحداث النظام
        public const string SystemStart = "SystemStart";
        public const string SystemShutdown = "SystemShutdown";
        public const string BackupCreated = "BackupCreated";
        public const string BackupRestored = "BackupRestored";
        public const string SettingsChanged = "SettingsChanged";

        // أحداث الأمان
        public const string PermissionGranted = "PermissionGranted";
        public const string PermissionRevoked = "PermissionRevoked";
        public const string RoleAssigned = "RoleAssigned";
        public const string RoleRemoved = "RoleRemoved";
        public const string RoleRevoked = "RoleRevoked";
        public const string UnauthorizedAccess = "UnauthorizedAccess";
        public const string SecurityViolation = "SecurityViolation";
    }
}
