﻿using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator
{
    public partial class FormCustomLogin : RJForms.RJBaseForm
    {
        public FormCustomLogin()
        {
            InitializeComponent();
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;

            AddControlBox();
            ApplyAppearanceSettings();

        }
        private void AddControlBox()
        {//Add control buttons (Maximize, close and minimize)
            try
            {
                //pnlTopTitle.Controls.Add(this.btnClose);
                //pnlTopTitle.Controls.Add(this.btnMinimize);
                this.btnClose.Height = 20;
                this.btnMinimize.Height = 20;

                if (UIAppearance.Language_ar)
                {
                    this.btnClose.Location = new Point(6, 5);
                    this.btnMinimize.Location = new Point(this.btnClose.Location.X + btnMinimize.Width, 5);
                    //lbl_Viriion.Location = new Point(pnlTopBorder.Width - 100, 7);
                }
                else
                {
                    //this.btnClose.Location = new Point(pnlTopBorder.Width - btnClose.Width - 6, 5);
                    this.btnMinimize.Location = new Point(this.btnClose.Location.X - btnMinimize.Width, 5);
                    //lbl_Viriion.Location = new Point(11, 7);

                }
            }
            catch { }
        }
        private void ApplyAppearanceSettings()
        {// Apply the appearance properties of the configuration.
            try
            {
                this.PrimaryForm = true; // Set as primary form.
                this.Resizable = false; // Set that the form cannot be resized from the border.
                                        //this.BorderSize = 0; // Remove the border.
                this.BackColor = UIAppearance.BackgroundColor; // Set the back color.
                                                               //this.BorderSize = UIAppearance.FormBorderSize;//The form Border Width will be equal to the border of the user settings

                //pnlBootom.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlBootom.Height = UIAppearance.FormBorderSize;

                if (UIAppearance.Theme == UITheme.Light)//if the theme is LIGHT, set the maximize, minimize and close buttons to black.
                {
                    this.btnClose.Image = Properties.Resources.CloseDark;
                    this.btnMaximize.Image = Properties.Resources.MaximizeDark;
                    this.btnMinimize.Image = Properties.Resources.MinimizeDark;
                }


                //pnlTopBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlTopBorder.Height = 2;

                //pnlLeftBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlLeftBorder.Width = 2;

                //pnlRightBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlRightBorder.Width = 2;

                //pnlBootom.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlBootom.Height = 2;
            }
            catch { }

        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
