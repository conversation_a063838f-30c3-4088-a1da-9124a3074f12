using System;
using System.ComponentModel;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.Windows.Forms;
using SmartCreator.Settings;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// RJGroupBox - GroupBox مخصص مع تحسينات متقدمة
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class RJGroupBox : GroupBox
    {
        #region Fields
        private int _borderSize = 2;
        private Color _borderColor = Settings.UIAppearance.FormBorderColor; // لون حدود من ثيم البرنامج
        private int _borderRadius = 8;
        private Color _titleBackColor = Settings.UIAppearance.BackgroundColor; // خلفية من ثيم البرنامج
        private Color _titleForeColor = Settings.UIAppearance.TextColor; // نص من ثيم البرنامج
        private Font _titleFont = new Font("Segoe UI", 10, FontStyle.Bold);
        private int _titleHeight = 30;
        private bool _showTitle = true;
        private ContentAlignment _titleAlignment = ContentAlignment.MiddleCenter;
        private Padding _titlePadding = new Padding(10, 0, 10, 0);
        private Color _contentBackColor = Settings.UIAppearance.ItemBackgroundColor; // مشابه لـ RJPanel
        //private Color _contentBackColor = Settings.UIAppearance.ItemBackgroundColor; // مشابه لـ RJPanel
        private bool _enableShadow = true;
        private Color _shadowColor = Color.FromArgb(50, 0, 0, 0);
        private int _shadowOffset = 3;
        private ControlStyle _style = ControlStyle.Solid; // نمط الكنترول مثل RJButton
        #endregion

        #region Constructor
        public RJGroupBox()
        {
            // إعدادات افتراضية
            this.SetStyle(ControlStyles.UserPaint |
                         ControlStyles.ResizeRedraw |
                         ControlStyles.AllPaintingInWmPaint |
                         ControlStyles.OptimizedDoubleBuffer, true);

            this.Size = new Size(200, 150);
            this.BackColor = Color.Transparent;
            this.ForeColor = Color.FromArgb(64, 64, 64);
            this.Font = new Font("Segoe UI", 9, FontStyle.Regular);
            this.Text = "RJGroupBox";

            // تطبيق ثيم النظام الافتراضي
            UpdateThemeColors();

            // تحديث المظهر
            UpdateStyles();
        }
        #endregion

        #region Properties - Border
        /// <summary>
        /// حجم الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Size of the border")]
        [DefaultValue(2)]
        public int BorderSize
        {
            get { return _borderSize; }
            set
            {
                _borderSize = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// لون الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Color of the border")]
        public Color BorderColor
        {
            get { return _borderColor; }
            set
            {
                _borderColor = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// انحناء الحدود
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Radius of the border corners")]
        [DefaultValue(8)]
        public int BorderRadius
        {
            get { return _borderRadius; }
            set
            {
                _borderRadius = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// نمط الكنترول - Solid أو Glass
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Style of the control - Solid or Glass")]
        [DefaultValue(ControlStyle.Solid)]
        public ControlStyle Style
        {
            get { return _style; }
            set
            {
                _style = value;
                UpdateThemeColors();
                this.Invalidate();
            }
        }
        #endregion

        #region Properties - Title
        /// <summary>
        /// إظهار العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Show or hide the title bar")]
        [DefaultValue(true)]
        public bool ShowTitle
        {
            get { return _showTitle; }
            set
            {
                _showTitle = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// لون خلفية العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Background color of the title bar")]
        public Color TitleBackColor
        {
            get { return _titleBackColor; }
            set
            {
                _titleBackColor = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// لون نص العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Text color of the title")]
        public Color TitleForeColor
        {
            get { return _titleForeColor; }
            set
            {
                _titleForeColor = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// خط العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Font of the title text")]
        public Font TitleFont
        {
            get { return _titleFont; }
            set
            {
                _titleFont = value ?? new Font("Segoe UI", 10, FontStyle.Bold);
                this.Invalidate();
            }
        }

        /// <summary>
        /// ارتفاع العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Height of the title bar")]
        [DefaultValue(30)]
        public int TitleHeight
        {
            get { return _titleHeight; }
            set
            {
                _titleHeight = Math.Max(20, value);
                this.Invalidate();
            }
        }

        /// <summary>
        /// محاذاة العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Alignment of the title text")]
        [DefaultValue(ContentAlignment.MiddleLeft)]
        public ContentAlignment TitleAlignment
        {
            get { return _titleAlignment; }
            set
            {
                _titleAlignment = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// حشو العنوان
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Padding around the title text")]
        public Padding TitlePadding
        {
            get { return _titlePadding; }
            set
            {
                _titlePadding = value;
                this.Invalidate();
            }
        }
        #endregion

        #region Properties - Content
        /// <summary>
        /// لون خلفية المحتوى
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Background color of the content area")]
        public Color ContentBackColor
        {
            get { return _contentBackColor; }
            set
            {
                _contentBackColor = value;
                this.Invalidate();
            }
        }
        #endregion

        #region Properties - Shadow
        /// <summary>
        /// تفعيل الظل
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Enable or disable shadow effect")]
        [DefaultValue(true)]
        public bool EnableShadow
        {
            get { return _enableShadow; }
            set
            {
                _enableShadow = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// لون الظل
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Color of the shadow")]
        public Color ShadowColor
        {
            get { return _shadowColor; }
            set
            {
                _shadowColor = value;
                this.Invalidate();
            }
        }

        /// <summary>
        /// إزاحة الظل
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Offset of the shadow")]
        [DefaultValue(3)]
        public int ShadowOffset
        {
            get { return _shadowOffset; }
            set
            {
                _shadowOffset = Math.Max(0, value);
                this.Invalidate();
            }
        }
        #endregion

        #region Helper Methods
        /// <summary>
        /// إنشاء مسار مستطيل مع زوايا منحنية
        /// </summary>
        private GraphicsPath GetRoundedRectanglePath(Rectangle rect, int radius)
        {
            GraphicsPath path = new GraphicsPath();
            
            if (radius <= 0)
            {
                path.AddRectangle(rect);
                return path;
            }

            int diameter = radius * 2;
            Size size = new Size(diameter, diameter);
            Rectangle arc = new Rectangle(rect.Location, size);

            // الزاوية العلوية اليسرى
            path.AddArc(arc, 180, 90);

            // الزاوية العلوية اليمنى
            arc.X = rect.Right - diameter;
            path.AddArc(arc, 270, 90);

            // الزاوية السفلية اليمنى
            arc.Y = rect.Bottom - diameter;
            path.AddArc(arc, 0, 90);

            // الزاوية السفلية اليسرى
            arc.X = rect.Left;
            path.AddArc(arc, 90, 90);

            path.CloseFigure();
            return path;
        }

        /// <summary>
        /// حساب مستطيل العنوان
        /// </summary>
        private Rectangle GetTitleRectangle()
        {
            if (!_showTitle) return Rectangle.Empty;
            
            return new Rectangle(
                _borderSize,
                _borderSize,
                this.Width - (_borderSize * 2),
                _titleHeight
            );
        }

        /// <summary>
        /// حساب مستطيل المحتوى
        /// </summary>
        private Rectangle GetContentRectangle()
        {
            int topOffset = _showTitle ? _titleHeight + _borderSize : _borderSize;
            
            return new Rectangle(
                _borderSize,
                topOffset,
                this.Width - (_borderSize * 2),
                this.Height - topOffset - _borderSize
            );
        }

        /// <summary>
        /// رسم الظل
        /// </summary>
        private void DrawShadow(Graphics graphics, Rectangle rect)
        {
            if (!_enableShadow || _shadowOffset <= 0) return;

            Rectangle shadowRect = new Rectangle(
                rect.X + _shadowOffset,
                rect.Y + _shadowOffset,
                rect.Width,
                rect.Height
            );

            using (GraphicsPath shadowPath = GetRoundedRectanglePath(shadowRect, _borderRadius))
            using (SolidBrush shadowBrush = new SolidBrush(_shadowColor))
            {
                graphics.FillPath(shadowBrush, shadowPath);
            }
        }
        #endregion

        #region Paint Methods
        protected override void OnPaint(PaintEventArgs e)
        {
            // لا نستدعي base.OnPaint لأننا نرسم كل شيء بأنفسنا
            Graphics graphics = e.Graphics;
            graphics.SmoothingMode = SmoothingMode.AntiAlias;

            // حساب المستطيلات
            Rectangle mainRect = new Rectangle(0, 0, this.Width - _shadowOffset, this.Height - _shadowOffset);
            Rectangle titleRect = GetTitleRectangle();
            Rectangle contentRect = GetContentRectangle();

            // رسم الظل
            DrawShadow(graphics, mainRect);

            // رسم الخلفية الرئيسية والحدود
            DrawMainBackground(graphics, mainRect);

            // رسم العنوان
            if (_showTitle && !string.IsNullOrEmpty(this.Text))
            {
                DrawTitle(graphics, titleRect);
            }

            // رسم منطقة المحتوى
            DrawContentArea(graphics, contentRect);
        }

        /// <summary>
        /// رسم الخلفية الرئيسية والحدود
        /// </summary>
        private void DrawMainBackground(Graphics graphics, Rectangle rect)
        {
            using (GraphicsPath path = GetRoundedRectanglePath(rect, _borderRadius))
            {
                // رسم الحدود
                if (_borderSize > 0)
                {
                    using (Pen borderPen = new Pen(_borderColor, _borderSize))
                    {
                        graphics.DrawPath(borderPen, path);
                    }
                }

                // قص المنطقة للرسم داخل الحدود
                graphics.SetClip(path);
            }
        }

        /// <summary>
        /// رسم العنوان
        /// </summary>
        private void DrawTitle(Graphics graphics, Rectangle titleRect)
        {
            if (titleRect.IsEmpty) return;

            // رسم خلفية العنوان
            using (SolidBrush titleBrush = new SolidBrush(_titleBackColor))
            {
                // إنشاء مسار للعنوان مع انحناء في الأعلى فقط
                using (GraphicsPath titlePath = GetTitlePath(titleRect))
                {
                    graphics.FillPath(titleBrush, titlePath);
                }
            }

            // رسم نص العنوان
            DrawTitleText(graphics, titleRect);
        }

        /// <summary>
        /// إنشاء مسار العنوان
        /// </summary>
        private GraphicsPath GetTitlePath(Rectangle titleRect)
        {
            GraphicsPath path = new GraphicsPath();

            if (_borderRadius <= 0)
            {
                path.AddRectangle(titleRect);
                return path;
            }

            int radius = Math.Min(_borderRadius, titleRect.Height / 2);
            int diameter = radius * 2;

            // الزاوية العلوية اليسرى
            path.AddArc(titleRect.X, titleRect.Y, diameter, diameter, 180, 90);

            // الخط العلوي
            path.AddLine(titleRect.X + radius, titleRect.Y, titleRect.Right - radius, titleRect.Y);

            // الزاوية العلوية اليمنى
            path.AddArc(titleRect.Right - diameter, titleRect.Y, diameter, diameter, 270, 90);

            // الخط الأيمن
            path.AddLine(titleRect.Right, titleRect.Y + radius, titleRect.Right, titleRect.Bottom);

            // الخط السفلي
            path.AddLine(titleRect.Right, titleRect.Bottom, titleRect.X, titleRect.Bottom);

            // الخط الأيسر
            path.AddLine(titleRect.X, titleRect.Bottom, titleRect.X, titleRect.Y + radius);

            path.CloseFigure();
            return path;
        }

        /// <summary>
        /// رسم نص العنوان
        /// </summary>
        private void DrawTitleText(Graphics graphics, Rectangle titleRect)
        {
            if (string.IsNullOrEmpty(this.Text)) return;

            // تطبيق الحشو
            Rectangle textRect = new Rectangle(
                titleRect.X + _titlePadding.Left,
                titleRect.Y + _titlePadding.Top,
                titleRect.Width - _titlePadding.Horizontal,
                titleRect.Height - _titlePadding.Vertical
            );

            // تحديد تنسيق النص
            StringFormat stringFormat = new StringFormat();

            // محاذاة أفقية
            switch (_titleAlignment)
            {
                case ContentAlignment.TopLeft:
                case ContentAlignment.MiddleLeft:
                case ContentAlignment.BottomLeft:
                    stringFormat.Alignment = StringAlignment.Near;
                    break;
                case ContentAlignment.TopCenter:
                case ContentAlignment.MiddleCenter:
                case ContentAlignment.BottomCenter:
                    stringFormat.Alignment = StringAlignment.Center;
                    break;
                case ContentAlignment.TopRight:
                case ContentAlignment.MiddleRight:
                case ContentAlignment.BottomRight:
                    stringFormat.Alignment = StringAlignment.Far;
                    break;
            }

            // محاذاة عمودية
            switch (_titleAlignment)
            {
                case ContentAlignment.TopLeft:
                case ContentAlignment.TopCenter:
                case ContentAlignment.TopRight:
                    stringFormat.LineAlignment = StringAlignment.Near;
                    break;
                case ContentAlignment.MiddleLeft:
                case ContentAlignment.MiddleCenter:
                case ContentAlignment.MiddleRight:
                    stringFormat.LineAlignment = StringAlignment.Center;
                    break;
                case ContentAlignment.BottomLeft:
                case ContentAlignment.BottomCenter:
                case ContentAlignment.BottomRight:
                    stringFormat.LineAlignment = StringAlignment.Far;
                    break;
            }

            // رسم النص
            using (SolidBrush textBrush = new SolidBrush(_titleForeColor))
            {
                graphics.DrawString(this.Text, _titleFont, textBrush, textRect, stringFormat);
            }
        }

        /// <summary>
        /// رسم منطقة المحتوى
        /// </summary>
        private void DrawContentArea(Graphics graphics, Rectangle contentRect)
        {
            if (contentRect.IsEmpty) return;

            using (SolidBrush contentBrush = new SolidBrush(_contentBackColor))
            {
                graphics.FillRectangle(contentBrush, contentRect);
            }
        }
        #endregion

        #region Override Methods
        protected override void OnResize(EventArgs e)
        {
            base.OnResize(e);
            this.Invalidate();
        }

        protected override void OnTextChanged(EventArgs e)
        {
            base.OnTextChanged(e);
            this.Invalidate();
        }

        protected override void OnFontChanged(EventArgs e)
        {
            base.OnFontChanged(e);
            this.Invalidate();
        }

        protected override void OnEnabledChanged(EventArgs e)
        {
            base.OnEnabledChanged(e);
            this.Invalidate();
        }

        /// <summary>
        /// تحديد منطقة الكنترولات الفرعية
        /// </summary>
        public override Rectangle DisplayRectangle
        {
            get
            {
                Rectangle contentRect = GetContentRectangle();

                // إضافة هامش داخلي
                return new Rectangle(
                    contentRect.X + 5,
                    contentRect.Y + 5,
                    Math.Max(0, contentRect.Width - 10),
                    Math.Max(0, contentRect.Height - 10)
                );
            }
        }
        #endregion

        #region Theme Methods
        /// <summary>
        /// تحديث ألوان الثيم حسب النمط
        /// </summary>
        private void UpdateThemeColors()
        {
            try
            {
                if (_style == ControlStyle.Solid)
                {
                    // نمط Solid - ألوان ثابتة من ثيم البرنامج
                    _borderColor = Settings.UIAppearance.FormBorderColor;
                    _titleBackColor = Settings.UIAppearance.BackgroundColor;
                    _titleForeColor = Settings.UIAppearance.TextColor;
                    _contentBackColor = Settings.UIAppearance.ItemBackgroundColor;
                }
                else // Glass
                {
                    // نمط Glass - ألوان شفافة مبنية على ثيم البرنامج
                    var baseColor = Settings.UIAppearance.FormBorderColor;
                    _borderColor = Color.FromArgb(150, baseColor.R, baseColor.G, baseColor.B);

                    var titleColor = Settings.UIAppearance.BackgroundColor;
                    _titleBackColor = Color.FromArgb(200, titleColor.R, titleColor.G, titleColor.B);

                    _titleForeColor = Settings.UIAppearance.TextColor;

                    var contentColor = Settings.UIAppearance.ItemBackgroundColor;
                    _contentBackColor = Color.FromArgb(200, contentColor.R, contentColor.G, contentColor.B);
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم الألوان الافتراضية من Settings
                _borderColor = Settings.UIAppearance.FormBorderColor;
                _titleBackColor = Settings.UIAppearance.BackgroundColor;
                _titleForeColor = Settings.UIAppearance.TextColor;
                _contentBackColor = Settings.UIAppearance.ItemBackgroundColor;
            }
        }

        /// <summary>
        /// تطبيق ثيم النظام الأساسي
        /// </summary>
        public void ApplySystemTheme()
        {
            try
            {
                // تطبيق ألوان النظام مثل RJPanel
                if (SystemInformation.HighContrast)
                {
                    // وضع التباين العالي
                    _borderColor = SystemColors.WindowFrame;
                    _titleBackColor = SystemColors.Control;
                    _titleForeColor = SystemColors.ControlText;
                    _contentBackColor = SystemColors.Window;
                }
                else
                {
                    // الوضع العادي - استخدم ثيم البرنامج
                    _borderColor = Settings.UIAppearance.FormBorderColor;
                    _titleBackColor = Settings.UIAppearance.BackgroundColor;
                    _titleForeColor = Settings.UIAppearance.TextColor;
                    _contentBackColor = Settings.UIAppearance.ItemBackgroundColor;
                }

                this.Invalidate();
            }
            catch
            {
                // في حالة الخطأ، استخدم UpdateThemeColors
                UpdateThemeColors();
            }
        }

        /// <summary>
        /// تطبيق ثيم مخصص
        /// </summary>
        public void ApplyCustomTheme(Color borderColor, Color titleBackColor, Color titleForeColor, Color contentBackColor)
        {
            try
            {
                _borderColor = borderColor;
                _titleBackColor = titleBackColor;
                _titleForeColor = titleForeColor;
                _contentBackColor = contentBackColor;
                this.Invalidate();
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// تحديث الثيم من إعدادات البرنامج - يُستدعى عند تغيير الثيم
        /// </summary>
        public void RefreshTheme()
        {
            try
            {
                // إعادة تحميل الألوان من Settings
                _borderColor = Settings.UIAppearance.FormBorderColor;
                _titleBackColor = Settings.UIAppearance.BackgroundColor;
                _titleForeColor = Settings.UIAppearance.TextColor;
                _contentBackColor = Settings.UIAppearance.ItemBackgroundColor;

                // تطبيق النمط الحالي
                UpdateThemeColors();

                this.Invalidate();
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// تحديث الثيم لجميع RJGroupBox في الكنترول الأب
        /// </summary>
        public static void RefreshAllThemes(Control parentControl)
        {
            try
            {
                if (parentControl == null) return;

                foreach (Control control in parentControl.Controls)
                {
                    if (control is RJGroupBox groupBox)
                    {
                        groupBox.RefreshTheme();
                    }

                    // البحث في الكنترولات الفرعية
                    if (control.HasChildren)
                    {
                        RefreshAllThemes(control);
                    }
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion

        #region Dispose
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                _titleFont?.Dispose();
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
