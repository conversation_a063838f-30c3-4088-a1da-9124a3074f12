using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار RJGroupBox
    /// </summary>
    public partial class RJGroupBoxTest : Form
    {
        private RJGroupBox groupBox1;
        private RJGroupBox groupBox2;
        private RJGroupBox groupBox3;
        private RJGroupBox groupBox4;

        public RJGroupBoxTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            //
            // groupBox1 - ثيم البرنامج (غير مخصص)
            //
            this.groupBox1 = new RJGroupBox();
            this.groupBox1.Text = "المعلومات الشخصية - ثيم البرنامج";
            this.groupBox1.Location = new Point(20, 20);
            this.groupBox1.Size = new Size(300, 200);
            this.groupBox1.Customizable = false; // يتبع ثيم البرنامج

            // إضافة كنترولات للمجموعة الأولى
            AddControlsToGroup1();

            //
            // groupBox2 - مخصص أخضر
            //
            this.groupBox2 = new RJGroupBox();
            this.groupBox2.Text = "الإعدادات - مخصص";
            this.groupBox2.Location = new Point(340, 20);
            this.groupBox2.Size = new Size(300, 200);
            this.groupBox2.Customizable = true; // تفعيل التخصيص
            this.groupBox2.BorderColor = Color.FromArgb(76, 175, 80);
            this.groupBox2.TitleBackColor = Color.FromArgb(76, 175, 80);
            this.groupBox2.TitleForeColor = Color.White;
            this.groupBox2.ContentBackColor = Color.FromArgb(248, 255, 248);
            this.groupBox2.BorderRadius = 12;
            this.groupBox2.TitleAlignment = ContentAlignment.MiddleCenter;

            // إضافة كنترولات للمجموعة الثانية
            AddControlsToGroup2();

            //
            // groupBox3 - ثيم مخصص أحمر
            //
            this.groupBox3 = new RJGroupBox();
            this.groupBox3.Text = "التنبيهات - ثيم مخصص";
            this.groupBox3.Location = new Point(20, 240);
            this.groupBox3.Size = new Size(300, 200);
            this.groupBox3.BorderRadius = 15;
            this.groupBox3.TitleHeight = 35;
            this.groupBox3.EnableShadow = true;
            this.groupBox3.ShadowOffset = 5;
            // تطبيق ثيم مخصص أحمر (سيفعل Customizable تلقائياً)
            this.groupBox3.ApplyCustomTheme(
                Color.FromArgb(244, 67, 54),    // حدود حمراء
                Color.FromArgb(244, 67, 54),    // عنوان أحمر
                Color.White,                    // نص أبيض
                Color.FromArgb(255, 248, 248)   // محتوى وردي فاتح
            );

            // إضافة كنترولات للمجموعة الثالثة
            AddControlsToGroup3();

            //
            // groupBox4 - ثيم البرنامج مع تخصيص شكل
            //
            this.groupBox4 = new RJGroupBox();
            this.groupBox4.Text = "إحصائيات - ثيم البرنامج";
            this.groupBox4.Location = new Point(340, 240);
            this.groupBox4.Size = new Size(300, 200);
            this.groupBox4.Customizable = false; // يتبع ثيم البرنامج
            this.groupBox4.BorderRadius = 20;
            this.groupBox4.TitleAlignment = ContentAlignment.MiddleRight;
            this.groupBox4.TitlePadding = new Padding(15, 5, 15, 5);
            this.groupBox4.BorderSize = 3;

            // إضافة كنترولات للمجموعة الرابعة
            AddControlsToGroup4();

            // 
            // RJGroupBoxTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(680, 480);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.groupBox2);
            this.Controls.Add(this.groupBox3);
            this.Controls.Add(this.groupBox4);
            this.Name = "RJGroupBoxTest";
            this.Text = "🎨 اختبار RJGroupBox - أنماط متنوعة";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 240, 240);

            this.ResumeLayout(false);
        }

        private void AddControlsToGroup1()
        {
            // Label
            var nameLabel = new Label
            {
                Text = "الاسم:",
                Location = new Point(10, 15),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // TextBox
            var nameTextBox = new RJTextBox
            {
                Location = new Point(70, 12),
                Size = new Size(200, 25),
                PlaceHolderText = "أدخل الاسم",
                BorderColor = Color.FromArgb(0, 122, 204),
                BorderRadius = 5
            };

            // Label
            var emailLabel = new Label
            {
                Text = "البريد:",
                Location = new Point(10, 50),
                Size = new Size(50, 20),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // TextBox
            var emailTextBox = new RJTextBox
            {
                Location = new Point(70, 47),
                Size = new Size(200, 25),
                PlaceHolderText = "أدخل البريد الإلكتروني",
                BorderColor = Color.FromArgb(0, 122, 204),
                BorderRadius = 5
            };

            // Button
            var saveButton = new RJButton
            {
                Text = "حفظ",
                Location = new Point(70, 85),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 8,
                IconChar = IconChar.Save,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            this.groupBox1.Controls.Add(nameLabel);
            this.groupBox1.Controls.Add(nameTextBox);
            this.groupBox1.Controls.Add(emailLabel);
            this.groupBox1.Controls.Add(emailTextBox);
            this.groupBox1.Controls.Add(saveButton);
        }

        private void AddControlsToGroup2()
        {
            // CheckBox
            var enableNotifications = new CheckBox
            {
                Text = "تفعيل التنبيهات",
                Location = new Point(10, 15),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(64, 64, 64),
                Checked = true
            };

            // CheckBox
            var autoSave = new CheckBox
            {
                Text = "الحفظ التلقائي",
                Location = new Point(10, 45),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            // ComboBox
            var themeCombo = new ComboBox
            {
                Location = new Point(10, 75),
                Size = new Size(150, 25),
                Font = new Font("Segoe UI", 9),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            themeCombo.Items.AddRange(new[] { "فاتح", "داكن", "تلقائي" });
            themeCombo.SelectedIndex = 0;

            // Button
            var applyButton = new RJButton
            {
                Text = "تطبيق",
                Location = new Point(10, 110),
                Size = new Size(100, 35),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 8,
                IconChar = IconChar.Check,
                Font = new Font("Segoe UI", 9, FontStyle.Bold)
            };

            this.groupBox2.Controls.Add(enableNotifications);
            this.groupBox2.Controls.Add(autoSave);
            this.groupBox2.Controls.Add(themeCombo);
            this.groupBox2.Controls.Add(applyButton);
        }

        private void AddControlsToGroup3()
        {
            // Label تحذيري
            var warningLabel = new Label
            {
                Text = "⚠️ تحذير: يوجد 3 رسائل غير مقروءة",
                Location = new Point(10, 15),
                Size = new Size(250, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(244, 67, 54)
            };

            // ListBox
            var alertsList = new ListBox
            {
                Location = new Point(10, 45),
                Size = new Size(250, 80),
                Font = new Font("Segoe UI", 8),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle
            };
            alertsList.Items.AddRange(new[] { 
                "خطأ في الاتصال بالخادم",
                "انتهت صلاحية كلمة المرور", 
                "مساحة التخزين ممتلئة" 
            });

            // Button
            var clearButton = new RJButton
            {
                Text = "مسح الكل",
                Location = new Point(10, 135),
                Size = new Size(100, 30),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 6,
                IconChar = IconChar.Trash,
                Font = new Font("Segoe UI", 8, FontStyle.Bold)
            };

            this.groupBox3.Controls.Add(warningLabel);
            this.groupBox3.Controls.Add(alertsList);
            this.groupBox3.Controls.Add(clearButton);
        }

        private void AddControlsToGroup4()
        {
            // إحصائيات
            var usersLabel = new Label
            {
                Text = "المستخدمين: 1,234",
                Location = new Point(10, 15),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176)
            };

            var ordersLabel = new Label
            {
                Text = "الطلبات: 5,678",
                Location = new Point(10, 40),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176)
            };

            var revenueLabel = new Label
            {
                Text = "الإيرادات: $12,345",
                Location = new Point(10, 65),
                Size = new Size(150, 20),
                Font = new Font("Segoe UI", 9, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176)
            };

            // Progress Bar
            var progressBar = new ProgressBar
            {
                Location = new Point(10, 95),
                Size = new Size(200, 20),
                Value = 75,
                Style = ProgressBarStyle.Continuous
            };

            var progressLabel = new Label
            {
                Text = "التقدم: 75%",
                Location = new Point(10, 125),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 8),
                ForeColor = Color.FromArgb(64, 64, 64)
            };

            this.groupBox4.Controls.Add(usersLabel);
            this.groupBox4.Controls.Add(ordersLabel);
            this.groupBox4.Controls.Add(revenueLabel);
            this.groupBox4.Controls.Add(progressBar);
            this.groupBox4.Controls.Add(progressLabel);
        }

        /// <summary>
        /// تشغيل نموذج الاختبار
        /// </summary>
        public static void ShowTest()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var form = new RJGroupBoxTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار RJGroupBox:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
