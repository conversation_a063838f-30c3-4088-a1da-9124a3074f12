using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Drawing.Design;
using System.Linq;
using System.Windows.Forms;
using System.Windows.Forms.Design;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// TabControl مخصص يستخدم RJButton للتابات
    /// بساطة وقوة - كل تاب هو RJButton!
    /// </summary>
    [DefaultEvent("TabChanged")]
    [DefaultProperty("Tabs")]
    [ToolboxBitmap(typeof(TabControl))]
    [Designer(typeof(RJTabControlDesigner))]
    public class RJTabControl : Panel
    {
        #region Fields
        private RJPanel tabsPanel;
        private RJPanel contentPanel;
        private List<RJTabPage> tabs;
        private RJTabPage activeTab;
        private int tabHeight = 35;
        private int tabSpacing = 2;
        private int tabPadding = 15;
        private bool showCloseButtons = false;
        private TabStyle tabStyle = TabStyle.Default;
        #endregion

        #region Constructor
        public RJTabControl()
        {
            // تهيئة فورية لجميع الحقول لتجنب null
            tabs = new List<RJTabPage>();
            activeTab = null;
            tabHeight = 35;
            tabSpacing = 2;
            tabPadding = 15;
            showCloseButtons = false;
            tabStyle = TabStyle.Default;

            // تهيئة Collection فوراً
            _tabs = new RJTabPageCollection(this);

            // إنشاء المكونات
            InitializeComponent();

            // إضافة تاب افتراضي للـ Designer بحذر شديد
            if (DesignMode)
            {
                try
                {
                    // التأكد من أن كل شيء جاهز
                    if (IsInitialized)
                    {
                        var defaultTab = new RJTabPage("TabPage1");
                        AddTabInternal(defaultTab);
                    }
                }
                catch
                {
                    // تجاهل الأخطاء في DesignMode تماماً
                }
            }
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // RJPanel للتابات (في الأعلى) - بخلفية افتراضية
            tabsPanel = new RJPanel
            {
                Dock = DockStyle.Top,
                Height = tabHeight,
                BorderRadius = 0,
                BorderSize = 0
            };

            // RJPanel للمحتوى (الباقي) - بخلفية افتراضية
            contentPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderRadius = 0,
                BorderSize = 1,
                BorderColor = Color.FromArgb(200, 200, 200)
            };

            // إضافة للكنترول
            this.Controls.Add(contentPanel);
            this.Controls.Add(tabsPanel);

            this.ResumeLayout(false);
        }
        #endregion

        #region Properties


        /// <summary>
        /// التاب النشط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public RJTabPage ActiveTab
        {
            get { return activeTab; }
        }

        /// <summary>
        /// ارتفاع التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the height of tabs")]
        [DefaultValue(35)]
        public int TabHeight
        {
            get { return tabHeight; }
            set
            {
                tabHeight = value;
                if (tabsPanel != null)
                    tabsPanel.Height = tabHeight;
                ArrangeTabs();
            }
        }

        /// <summary>
        /// المسافة بين التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the spacing between tabs")]
        [DefaultValue(2)]
        public int TabSpacing
        {
            get { return tabSpacing; }
            set
            {
                tabSpacing = value;
                ArrangeTabs();
            }
        }

        /// <summary>
        /// الحشو الداخلي للتابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the padding inside tabs")]
        [DefaultValue(15)]
        public int TabPadding
        {
            get { return tabPadding; }
            set
            {
                tabPadding = value;
                ArrangeTabs();
            }
        }

        /// <summary>
        /// عرض أزرار الإغلاق
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether to show close buttons")]
        [DefaultValue(false)]
        public bool ShowCloseButtons
        {
            get { return showCloseButtons; }
            set
            {
                showCloseButtons = value;
                UpdateCloseButtons();
            }
        }

        /// <summary>
        /// عدد التابات
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int TabCount
        {
            get { return tabs?.Count ?? 0; }
        }

        /// <summary>
        /// نمط التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tab style")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        public TabStyle TabStyle
        {
            get { return tabStyle; }
            set
            {
                tabStyle = value ?? TabStyle.Default;
                ApplyStyleToAllTabs();
            }
        }

        /// <summary>
        /// لون حدود منطقة المحتوى
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the content panel border color")]
        public Color ContentBorderColor
        {
            get { return contentPanel?.BorderColor ?? Color.Transparent; }
            set { if (contentPanel != null) contentPanel.BorderColor = value; }
        }

        /// <summary>
        /// سمك حدود منطقة المحتوى
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the content panel border size")]
        [DefaultValue(1)]
        public int ContentBorderSize
        {
            get { return contentPanel?.BorderSize ?? 1; }
            set { if (contentPanel != null) contentPanel.BorderSize = value; }
        }

        /// <summary>
        /// نصف قطر حدود منطقة المحتوى
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the content panel border radius")]
        [DefaultValue(0)]
        public int ContentBorderRadius
        {
            get { return contentPanel?.BorderRadius ?? 0; }
            set { if (contentPanel != null) contentPanel.BorderRadius = value; }
        }

        /// <summary>
        /// لون خلفية منطقة المحتوى
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the content panel background color")]
        public Color ContentBackColor
        {
            get { return contentPanel?.BackColor ?? Color.White; }
            set { if (contentPanel != null) contentPanel.BackColor = value; }
        }

        /// <summary>
        /// لون حدود منطقة التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tabs panel border color")]
        public Color TabsPanelBorderColor
        {
            get { return tabsPanel?.BorderColor ?? Color.Transparent; }
            set { if (tabsPanel != null) tabsPanel.BorderColor = value; }
        }

        /// <summary>
        /// سمك حدود منطقة التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tabs panel border size")]
        [DefaultValue(0)]
        public int TabsPanelBorderSize
        {
            get { return tabsPanel?.BorderSize ?? 0; }
            set { if (tabsPanel != null) tabsPanel.BorderSize = value; }
        }

        /// <summary>
        /// نصف قطر حدود منطقة التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tabs panel border radius")]
        [DefaultValue(0)]
        public int TabsPanelBorderRadius
        {
            get { return tabsPanel?.BorderRadius ?? 0; }
            set { if (tabsPanel != null) tabsPanel.BorderRadius = value; }
        }

        /// <summary>
        /// لون خلفية منطقة التابات
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the tabs panel background color")]
        public Color TabsPanelBackColor
        {
            get { return tabsPanel?.BackColor ?? Color.Transparent; }
            set { if (tabsPanel != null) tabsPanel.BackColor = value; }
        }

        /// <summary>
        /// مجموعة التابات - مخفية مؤقتاً لحل مشكلة Designer
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public RJTabPageCollection Tabs
        {
            get
            {
                try
                {
                    return _tabs ?? (_tabs = new RJTabPageCollection(this));
                }
                catch
                {
                    return _tabs = new RJTabPageCollection(this);
                }
            }
            private set { _tabs = value; }
        }
        private RJTabPageCollection _tabs;

        /// <summary>
        /// حماية للـ Designer - تتحقق من حالة التهيئة
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public bool IsInitialized
        {
            get { return tabsPanel != null && contentPanel != null && tabs != null; }
        }

        /// <summary>
        /// التاب النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the selected tab")]
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public RJTabPage SelectedTab
        {
            get { return activeTab; }
            set { if (IsInitialized) ActivateTab(value); }
        }

        /// <summary>
        /// فهرس التاب النشط
        /// </summary>
        [Category("RJ Code Advance")]
        [Description("Gets or sets the selected tab index")]
        [DefaultValue(-1)]
        public int SelectedIndex
        {
            get { return IsInitialized && activeTab != null ? tabs.IndexOf(activeTab) : -1; }
            set
            {
                if (!IsInitialized) return;

                if (value >= 0 && value < tabs.Count)
                    ActivateTab(tabs[value]);
                else if (value == -1)
                    ActivateTab(null);
            }
        }

        #endregion

        #region Events
        /// <summary>
        /// حدث تغيير التاب
        /// </summary>
        public event EventHandler<TabChangedEventArgs> TabChanged;

        /// <summary>
        /// حدث إضافة تاب
        /// </summary>
        public event EventHandler<TabEventArgs> TabAdded;

        /// <summary>
        /// حدث إزالة تاب
        /// </summary>
        public event EventHandler<TabEventArgs> TabRemoved;

        /// <summary>
        /// حدث طلب إغلاق تاب
        /// </summary>
        public event EventHandler<TabCloseEventArgs> TabClosing;

        /// <summary>
        /// حدث إغلاق تاب
        /// </summary>
        public event EventHandler<TabEventArgs> TabClosed;
        #endregion

        #region Methods
        /// <summary>
        /// إضافة تاب جديد
        /// </summary>
        public RJTabPage AddTab(string text)
        {
            return AddTab(text, IconChar.None);
        }

        /// <summary>
        /// إضافة تاب جديد مع أيقونة
        /// </summary>
        public RJTabPage AddTab(string text, IconChar icon)
        {
            var tab = new RJTabPage(text, icon)
            {
                Height = tabHeight - 4
            };

            // تطبيق النمط
            tabStyle.ApplyToTab(tab, false);

            // ربط الأحداث
            tab.TabClosing += Tab_TabClosing;
            tab.TabClosed += Tab_TabClosed;
            tab.Click += Tab_Click;

            // إضافة للقائمة
            tabs.Add(tab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(tab);
            contentPanel.Controls.Add(tab.ContentPanel);

            // ترتيب التابات
            ArrangeTabs();

            // تفعيل التاب إذا كان الأول
            if (tabs.Count == 1)
            {
                ActivateTab(tab);
            }

            // إثارة الحدث
            TabAdded?.Invoke(this, new TabEventArgs(tab));

            return tab;
        }

        /// <summary>
        /// إضافة تاب موجود مسبقاً
        /// </summary>
        public RJTabPage AddTab(RJTabPage tab)
        {
            if (tab == null) return null;

            // تطبيق إعدادات TabControl
            tab.Height = tabHeight - 4;

            // تطبيق النمط
            tabStyle.ApplyToTab(tab, false);

            // ربط الأحداث
            tab.TabClosing += Tab_TabClosing;
            tab.TabClosed += Tab_TabClosed;
            tab.Click += Tab_Click;

            // إضافة للقائمة
            tabs.Add(tab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(tab);
            contentPanel.Controls.Add(tab.ContentPanel);

            // ترتيب التابات
            ArrangeTabs();

            // تفعيل التاب إذا كان الأول
            if (tabs.Count == 1)
            {
                ActivateTab(tab);
            }

            // إثارة الحدث
            TabAdded?.Invoke(this, new TabEventArgs(tab));

            return tab;
        }

        /// <summary>
        /// إزالة تاب
        /// </summary>
        public void RemoveTab(RJTabPage tab)
        {
            if (tab == null || !tabs.Contains(tab)) return;

            // إزالة من القائمة
            tabs.Remove(tab);

            // إزالة من الواجهة
            tabsPanel.Controls.Remove(tab);
            contentPanel.Controls.Remove(tab.ContentPanel);

            // إذا كان التاب المحذوف نشط، فعّل تاب آخر
            if (activeTab == tab)
            {
                activeTab = null;
                if (tabs.Count > 0)
                {
                    ActivateTab(tabs[0]);
                }
            }

            // ترتيب التابات
            ArrangeTabs();

            // إثارة الحدث
            TabRemoved?.Invoke(this, new TabEventArgs(tab));

            // تحرير الموارد
            tab.Dispose();
        }

        /// <summary>
        /// تفعيل تاب
        /// </summary>
        public void ActivateTab(RJTabPage tab)
        {
            if (tab == null || !tabs.Contains(tab) || tab == activeTab) return;

            var previousTab = activeTab;

            // إلغاء تفعيل التاب السابق
            if (activeTab != null)
            {
                activeTab.Deactivate();
                tabStyle.ApplyToTab(activeTab, false);
            }

            // تفعيل التاب الجديد
            activeTab = tab;
            activeTab.Activate();
            tabStyle.ApplyToTab(activeTab, true);

            // إثارة الحدث
            TabChanged?.Invoke(this, new TabChangedEventArgs(previousTab, activeTab));
        }

        /// <summary>
        /// ترتيب التابات
        /// </summary>
        private void ArrangeTabs()
        {
            if (tabs == null || tabs.Count == 0) return;

            int x = tabSpacing;

            foreach (var tab in tabs)
            {
                if (tab == null) continue;

                // حساب عرض التاب
                int tabWidth = CalculateTabWidth(tab);

                // تحديد موقع التاب
                tab.Location = new Point(x, 2);
                tab.Size = new Size(tabWidth, tabHeight - 4);

                x += tabWidth + tabSpacing;
            }
        }

        /// <summary>
        /// حساب عرض التاب
        /// </summary>
        private int CalculateTabWidth(RJTabPage tab)
        {
            using (var g = this.CreateGraphics())
            {
                var textSize = g.MeasureString(tab.Text, tab.Font);
                int width = (int)textSize.Width + (tabPadding * 2);

                // إضافة مساحة للأيقونة
                if (tab.IconChar != IconChar.None)
                {
                    width += tab.IconSize + 5;
                }

                // إضافة مساحة لزر الإغلاق
                if (showCloseButtons && tab.CanClose)
                {
                    width += 20;
                }

                return Math.Max(width, 80); // حد أدنى للعرض
            }
        }

        /// <summary>
        /// تحديث أزرار الإغلاق
        /// </summary>
        private void UpdateCloseButtons()
        {
            if (tabs == null) return;

            // TODO: إضافة أزرار الإغلاق إذا لزم الأمر
            ArrangeTabs();
        }

        /// <summary>
        /// مسح جميع التابات
        /// </summary>
        public void ClearTabs()
        {
            while (tabs.Count > 0)
            {
                RemoveTab(tabs[0]);
            }
        }

        /// <summary>
        /// تطبيق النمط على جميع التابات
        /// </summary>
        private void ApplyStyleToAllTabs()
        {
            if (tabs == null || tabStyle == null) return;

            foreach (var tab in tabs)
            {
                if (tab == null) continue;
                bool isActive = tab == activeTab;
                tabStyle.ApplyToTab(tab, isActive);
            }
            ArrangeTabs();
        }
        #endregion

        #region Event Handlers
        private void Tab_Click(object sender, EventArgs e)
        {
            if (sender is RJTabPage tab)
            {
                ActivateTab(tab);
            }
        }

        private void Tab_TabClosing(object sender, TabCloseEventArgs e)
        {
            TabClosing?.Invoke(this, e);
        }

        private void Tab_TabClosed(object sender, EventArgs e)
        {
            if (sender is RJTabPage tab)
            {
                RemoveTab(tab);
                TabClosed?.Invoke(this, new TabEventArgs(tab));
            }
        }

        #region Designer Support Methods
        /// <summary>
        /// إضافة تاب داخلياً - للـ Designer
        /// </summary>
        internal void AddTabInternal(RJTabPage tab)
        {
            if (tab == null || tabs == null || tabsPanel == null || contentPanel == null)
                return;

            // تطبيق إعدادات TabControl
            tab.Height = tabHeight - 4;

            // تطبيق النمط
            tabStyle?.ApplyToTab(tab, false);

            // ربط الأحداث
            tab.Click += Tab_Click;

            // إضافة للقائمة الداخلية
            if (!tabs.Contains(tab))
                tabs.Add(tab);

            // إضافة للواجهة
            tabsPanel.Controls.Add(tab);
            contentPanel.Controls.Add(tab.ContentPanel);

            // ترتيب التابات
            ArrangeTabs();

            // تفعيل التاب إذا كان الأول
            if (tabs.Count == 1)
            {
                ActivateTab(tab);
            }

            // إثارة الحدث
            TabAdded?.Invoke(this, new TabEventArgs(tab));
        }

        /// <summary>
        /// إزالة تاب داخلياً - للـ Designer
        /// </summary>
        internal void RemoveTabInternal(RJTabPage tab)
        {
            if (tab == null) return;

            // إلغاء ربط الأحداث
            tab.Click -= Tab_Click;

            // إزالة من القائمة
            tabs.Remove(tab);

            // إزالة من الواجهة
            tabsPanel.Controls.Remove(tab);
            contentPanel.Controls.Remove(tab.ContentPanel);

            // تفعيل تاب آخر إذا كان هذا نشط
            if (activeTab == tab)
            {
                if (tabs.Count > 0)
                    ActivateTab(tabs[0]);
                else
                    activeTab = null;
            }

            // ترتيب التابات
            ArrangeTabs();

            // إثارة الحدث
            TabRemoved?.Invoke(this, new TabEventArgs(tab));
        }

        /// <summary>
        /// إشعار تغيير التاب - للـ Designer
        /// </summary>
        internal void OnTabPageChanged(RJTabPage oldTab, RJTabPage newTab, int index)
        {
            if (oldTab != null)
            {
                RemoveTabInternal(oldTab);
            }

            if (newTab != null)
            {
                AddTabInternal(newTab);
            }
        }
        #endregion
        #endregion
    }

    #region Event Args Classes
    /// <summary>
    /// معاملات حدث تغيير التاب
    /// </summary>
    public class TabChangedEventArgs : EventArgs
    {
        public RJTabPage PreviousTab { get; }
        public RJTabPage CurrentTab { get; }

        public TabChangedEventArgs(RJTabPage previousTab, RJTabPage currentTab)
        {
            PreviousTab = previousTab;
            CurrentTab = currentTab;
        }
    }

    /// <summary>
    /// معاملات أحداث التاب العامة
    /// </summary>
    public class TabEventArgs : EventArgs
    {
        public RJTabPage Tab { get; }

        public TabEventArgs(RJTabPage tab)
        {
            Tab = tab;
        }
    }
    #endregion
}