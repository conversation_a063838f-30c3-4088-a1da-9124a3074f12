﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Policy;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using static System.Windows.Forms.AxHost;

namespace SmartCreator.TestAndDemo
{
    public partial class FormDashboard : RJForms.RJChildForm
    {
        SalesAnalysis analysis = new SalesAnalysis();
        //Fields
        private Dashboard model;
        private bool firtLoad=true;
        API_Server server = new API_Server();
        private bool SaveSesstion=false;
        public FormDashboard(bool SaveSess=false)
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            this.StartPosition = FormStartPosition.Manual;
            this.Top = (Screen.PrimaryScreen.Bounds.Height - this.Height) / 2;
            this.Left = (Screen.PrimaryScreen.Bounds.Width - this.Width) / 2;
            CboxPeriod.RightToLeft = RightToLeft.No;
            SaveSesstion = SaveSess;

            //var categories = new List<SalesAnalysis.SalesByCategory>();
            //categories.AddRange(analysis.SalesByCategoryList);
            //categories.Add(new SalesAnalysis.SalesByCategory { Item = "All" });

            //cbCategory.DataSource = categories;
            //cbCategory.DisplayMember = "Item";
            //cbCategory.SelectedIndex = categories.Count - 1;

            Set_Font();
            //Default - Last 7 days
            //dtpStartDate.Value = DateTime.Today.AddDays(-30);
            dtpStartDate.Value = DateTime.Today.AddDays(-7);
            dtpEndDate.Value = DateTime.Now;
            //btnLast7Days.Select();

            

            cbCategory.SelectedIndex = 0;
            CboxPeriod.SelectedIndex = 1;

            Dictionary<string, string> comboSource = new Dictionary<string, string>();
            comboSource.Add("UM", "يوزمنجر");
            comboSource.Add("HS", "هوتسبوت");
            comboSource.Add("PPP", "برودباند");

            CboxServerType.DataSource = new BindingSource(comboSource, null);
            CboxServerType.DisplayMember = "Value";
            CboxServerType.ValueMember = "Key";
            CboxServerType.SelectedIndex = 0;
            CboxServerType.Text = "يوزمنجر";

            Cbox_By_profile.SelectedIndex = 0;
            Cbox_By_Port.SelectedIndex = 0;
            Cbox_By_Port.label.TextAlign = ContentAlignment.MiddleLeft;
            Cbox_By_profile.label.TextAlign = ContentAlignment.MiddleLeft;
            CboxPeriod.label.TextAlign = ContentAlignment.MiddleRight;
            CboxServerType.label.TextAlign = ContentAlignment.MiddleRight;



            ToggleButton_Comm.Checked = false;

            CboxServerType.SelectedIndex = Properties.Settings.Default.DashBoardServerType;
            ToggleButton_Comm.Checked = Properties.Settings.Default.DashBoardCommi;


            model = new Dashboard(CboxServerType.SelectedIndex, ToggleButton_Comm.Checked);

            //LoadData(); 

            if (UIAppearance.Language_ar)
                this.Text = "لوحة المعلومات";
        }

        [Obsolete]
        private void FormDashboard_Load(object sender, EventArgs e)
        {
            rjLabel4.Refresh();
            //rjLabel3.UseCompatibleTextRendering = true;



            timer1.Start();
        }
        private void Set_Font()
        {
            //return;
            try
            {
                lblCountHotspot.Font = lblNumberSessionUM.Font =
                lblNumberUserManager.Font = lblRevenue.Font =
                    new System.Drawing.Font("Verdana", 12f * utils.ScaleFactor, FontStyle.Regular);


                Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 10*utils.ScaleFactor, FontStyle.Regular);
                //Font title_font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 11f, false, GraphicsUnit.Point, 0);


                
              CboxPeriod.Font=CboxServerType.Font= Cbox_By_profile.Font = Cbox_By_Port.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8f * utils.ScaleFactor, FontStyle.Regular);
                
                Font frm_font = Program.GetCustomFont(Resources.DroidSansArabic, 9f*utils.ScaleFactor, FontStyle.Regular);
                //Font frm_font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 9.75f, false, GraphicsUnit.Point, 0);
                rjLabel13.Font =  rjLabel6.Font = rjLabel4.Font = rjLabel7.Font = rjLabel9.Font = rjLabel11.Font = rjLabel2.Font = rjLabel3.Font =
                CboxPeriod.Font=
                CboxServerType.Font =
                frm_font;
                //btnUserManager.Font = frm_font;
                //btnHotspot.Font = frm_font;
                //btnHotspot.Font = frm_font;
                //btnDevice.Font = frm_font;
                //btnCardsDesgin.Font = frm_font;
                //btnReport.Font = frm_font;
                //btn_Pages_Editor.Font = frm_font;
                //btnSetting.Font = frm_font;
                lblNumberUserManager.RightToLeft =
                lblCountHotspot.RightToLeft =
                lblNumberSessionUM.RightToLeft =
                lblRevenue.RightToLeft =
                lbActiveCards.RightToLeft =
                CboxPeriod.RightToLeft =
                lbCountHost.RightToLeft =
                lblCountNeghibor.RightToLeft =
                lbl_Rb_model.RightToLeft =
                lbl_Rb_Version.RightToLeft =
                lbl_Uptime.RightToLeft =
                
                 
                    RightToLeft.No;


                chartRevenue.Titles[0].Font = title_font;
                chartTop5Products.Titles[0].Font = title_font;
                chartSalesByProfile.Titles[0].Font = title_font;

                chartNumberSales.Titles[0].Font = title_font;


            }
            catch { }

        }

        [Obsolete]
        public void LoadData_fromMK()
        {
            //if(Global_Variable.StartThreadProcessFromMK)
            //{
            //    RJMessageBox.Show("هناك عمليه علي الروتر قيد التنفيذ");
            //    return;
            //}
            string NumberSessionUM = "";
            string NumberUserManager = "";
            string ActiveCards = "";
            string HostCards = "";
            string CountHotspot = "";
            string CountNeghibor = "";
            string RbUptime = "";
            string RbModel = "";
            string Rb_verison = "";
            Mk_DataAccess mk = new Mk_DataAccess();
             mk.GetResources();
            RbUptime = Global_Variable.Mk_resources.uptime;
            RbModel = Global_Variable.Mk_resources.boardName;
            Rb_verison = Global_Variable.Mk_resources.version_str;

            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    lbl_Uptime.Text = RbUptime;
                    lbl_Rb_Version.Text = Rb_verison;
                    lbl_Rb_model.Text = RbModel;
                });
            }
            catch { }


            Mk_DataAccess_old cl = new Mk_DataAccess_old(); 
            NumberSessionUM = cl.Get_usermanager_session_count().ToString();
            NumberUserManager = cl.Get_usermanager_count().ToString();
            ActiveCards = cl.Get_Active_count().ToString();
            CountHotspot = cl.Get_Hotsopot_count().ToString();
            CountNeghibor = cl.Get_neighbor_count().ToString();
            HostCards = cl.Get_Host_count().ToString();
           
            //model.NumUserManager_users = Convert.ToInt32( NumberUserManager);
            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    lblNumberSessionUM.Text = NumberSessionUM;
                    lblNumberUserManager.Text = NumberUserManager;
                    lbActiveCards.Text = ActiveCards;
                    lblCountHotspot.Text = CountHotspot;
                    lblCountNeghibor.Text = CountNeghibor;
                    lbCountHost.Text = HostCards;
                });
            }
            catch { }
            

        }
        public void LoadData(bool forceGet = false)
        {

            model.Dashboard_Server(CboxServerType.SelectedIndex, ToggleButton_Comm.Checked);

            model.LoadData(dtpStartDate.Value, dtpEndDate.Value, forceGet);
            model.GetProfileAnalisys(Cbox_By_profile.SelectedIndex);
            model.GetTop5DeviceAnalisys(Cbox_By_Port.SelectedIndex);
            //if (refreshData == true)
            //{

            try
            {
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
               (System.Windows.Forms.MethodInvoker)delegate ()
               {
                   lblRevenue.Text = "0";
                   lblRevenue.Text = String.Format("{0:n0}", model.TotalRevenue);

                   chartRevenue.DataSource = model.GrossRevenueList;
                   chartRevenue.Series[0].XValueMember = "Date";
                   chartRevenue.Series[0].YValueMembers = "TotalAmount";
                   chartRevenue.DataBind();

                   chartSalesByProfile.DataSource = model.TopProductsList;
                   chartSalesByProfile.Series[0].XValueMember = "Key";
                   chartSalesByProfile.Series[0].YValueMembers = "Value";
                   chartSalesByProfile.DataBind();


                   chartTop5Products.Series[0].Points.DataBind(model.Top5DeviceList, "Item", "UnitSold", "");
               });
                //Console.WriteLine("Loaded view :)");
            }
            catch { }
            //}
            //else Console.WriteLine("View not loaded, same query");
        }


        [Obsolete]
        private void LoadDashboardData()
        {
            //chartNumberSales.Series[0].Points.DataBind(analysis.NumberSalesList, "DateOrTime", "UnitSold", "");
            //chartNumberSales.Series[1].Points.DataBind(analysis.NumberSalesList, "DateOrTime", "UnitSold", "");
            //chartTop5Products.Series[0].Points.DataBind(analysis.Top5ProductsList, "Item", "UnitSold", "");

        }


        void Get_inf()
        {
            //DataAccess.CLS_DataAccess cl = new CLS_DataAccess();
            //lbl_active.Text = cl.Get_Active_count();
            //lbl_users_usereman.Text = cl.Get_usermanager_count();
            //lbl_user_hotspot.Text = cl.Get_Hotsopot_count();
            //lbl_count_session.Text = cl.Get_usermanager_session_count();
            //lbl_count_neghibor.Text = cl.Get_neighbor_count();

            //lblUptime.Text = MyDataClass.Resources.Rows[0]["uptime"].ToString();

        }

        private void dtpEndDate_ValueChanged(object sender, EventArgs e)
        {
            return;
            if (firtLoad)
                return;
            //LoadData();

            //Thread thread = new Thread(LoadData);
            //thread.Start();
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            //LoadAll();

            Thread thread = new Thread(LoadData_fromMK);
            thread.Start();

            
            LoadData();
            firtLoad = false;
            //Thread thread2 = new Thread(LoadData);
            //thread2.Start();
            //Font title_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 11, FontStyle.Regular);
            //chartSalesByProfile.Titles[0].Font = title_font;
            //chartRevenue.Titles[0].Font = title_font;
            //chartTop5Products.Titles[0].Font = title_font;
            //chartNumberSales.Titles[0].Font = title_font;

            if(SaveSesstion)
            {
                ThreadStart therGetSession = new ThreadStart(() => Create_Session());
                Thread startSession = new Thread(therGetSession);
                startSession.Start();
            }

        }

        [Obsolete]
        public void LoadAll()
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                //RJMessageBox.Show("هناك عمليه علي الروتر قيد التنفيذ");
                return;
            }

            LoadData();
            LoadData_fromMK();
            //Thread thread = new Thread(LoadData_fromMK);
            //thread.Start();

            //firtLoad = false;
            ////LoadData();
            //Thread thread2 = new Thread(LoadData);
            //thread2.Start();
        }
        public  void Create_Session()
        {
            try
            {
                var data = new Dictionary<string, string>
                    {
                        {"active", lbActiveCards.Text},
                        
                        {"count_user_hotspot", lblCountHotspot.Text },
                        {"count_user_manager", lblNumberUserManager.Text },
                        {"count_session_userManager", lblNumberSessionUM.Text},
                        {"count_neghibor.",  lblCountNeghibor.Text},
                        {"Revenue", model.TotalRevenue.ToString()},
                        {"GrossRevenue",JsonConvert.SerializeObject(model.GrossRevenueList) },
                        {"TopDeviceList", JsonConvert.SerializeObject( model.Top5DeviceList)},
                        
                        //{"APPID", Global_Variable.App_Info.APPId},
                        {"APPID", Global_Variable.App_Info.Name},
                        {"AppBuilder", Global_Variable.App_Info.AppBuilder.ToString()},
                        {"identity.",   Global_Variable.Mk_resources.identity},
                        {"version", Global_Variable.Mk_resources.version_str.ToString()},
                        {"uptime", Global_Variable.Mk_resources.uptime.ToString()},
                        {"cpu",  Global_Variable.Mk_resources.cpu.ToString()},
                        {"board_name", Global_Variable.Mk_resources.boardName.ToString()},

                        {"RB_SN",  Global_Variable.Mk_resources.RB_SN},
                        {"ether1_MAC",  Global_Variable.Mk_resources.ether1_MAC},
                        {"licenseCode",  Global_Variable.Mk_resources.licenseCode},
                        {"pc_code", (Global_Variable.Pc_Code)},

                    };

                if (server.create_session(data))
                {
                    //MessageBox.Show("");  
                    //this.Close();
                }
                else
                {
                    //MessageBox.Show("خطاء في عمليه الطلب");
                }
            }
            catch { }


        }

        private void Cbox_By_profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (firtLoad)
                    return;
                model.GetProfileAnalisys(Cbox_By_profile.SelectedIndex);
                chartSalesByProfile.DataSource = model.TopProductsList;
                chartSalesByProfile.Series[0].XValueMember = "Key";
                chartSalesByProfile.Series[0].YValueMembers = "Value";
                chartSalesByProfile.DataBind();
            }
            catch { }

        }

        private void Cbox_By_Port_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (firtLoad)
                    return;

                model.GetTop5DeviceAnalisys(Cbox_By_Port.SelectedIndex);
                chartTop5Products.Series[0].Points.DataBind(model.Top5DeviceList, "Item", "UnitSold", "");
            }
            catch { }
        }

        private void cbPeriod_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if(firtLoad)
                return;

            var g = @"اليوم
                    اخر 7 ايام
                    هذا الشهر
                    اخر 30 يوم
                    هذا العام
                    مخصص";
            if (CboxPeriod.SelectedIndex == 0)
            {
                dtpStartDate.Value = DateTime.Now.AddDays(-1);
                dtpEndDate.Value = DateTime.Now;
                LoadData();
            }
            else if (CboxPeriod.SelectedIndex == 1)
            {
                dtpStartDate.Value = DateTime.Now.AddDays(-7);
                dtpEndDate.Value = DateTime.Now;
                LoadData();

            }
            else if (CboxPeriod.SelectedIndex == 2)
            {
                DateTime dateTime = DateTime.Now;
                dtpStartDate.Value = new DateTime(dateTime.Year,dateTime.Month,1);
                dtpEndDate.Value = DateTime.Now;
                LoadData();

            }
            else if (CboxPeriod.SelectedIndex == 3)
                {
                dtpStartDate.Value = DateTime.Now.AddDays(-30);
                dtpEndDate.Value = DateTime.Now;
                LoadData();

            }
            else if (CboxPeriod.SelectedIndex == 4)
            {
                DateTime dateTime = DateTime.Now;
                dtpStartDate.Value = new DateTime(dateTime.Year,1, 1);
                dtpEndDate.Value = DateTime.Now;
                LoadData();

            }
        }
        private void CboxServerType_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firtLoad)
                return;

            Properties.Settings.Default.DashBoardServerType = CboxServerType.SelectedIndex;
            Properties.Settings.Default.Save();

            LoadData(true);

        }

        private void dtpStartDate_ValueChanged(object sender, EventArgs e)
        {

        }

        private void ToggleButton_Comm_CheckedChanged(object sender, EventArgs e)
        {
            if(firtLoad)
                return;

            Properties.Settings.Default.DashBoardCommi = ToggleButton_Comm.Checked;
            Properties.Settings.Default.Save();

            LoadData();


        }

        private void FormDashboard_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjPanel7.Refresh();
            rjPanel8.Refresh();
            chartRevenue.Refresh();
            chartNumberSales.Refresh();
        }

        private void rjLabel3_Click(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            if (RJMessageBox.Show("سيقوم بجلب وتحديث البيانات من الروتر بنفس الخيارات عند تسجيل الدخول", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;


            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => GetData.FirstLoadDataFromMK());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }


        }

        [Obsolete]
        private void btn_Dashbard_Refersh_Click(object sender, EventArgs e)
        {
            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => LoadAll());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }
            //timer1.Start();
        }
    }
}
