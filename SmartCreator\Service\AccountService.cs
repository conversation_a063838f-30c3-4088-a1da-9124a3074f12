﻿using Dapper;
using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
//using SmartCreator.Entities.Accounts;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Service
{
    /// <summary>
    /// خدمة إدارة شجرة الحسابات
    /// </summary>
    public class AccountService
    {
        private readonly DatabaseHelper _dbHelper;

        public AccountService(DatabaseHelper dbHelper)
        {
            _dbHelper = dbHelper;
        }

        /// <summary>
        /// جلب جميع الحسابات
        /// </summary>
        public List<Account> GetAll(bool activeOnly = true)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT * FROM Accounts
                WHERE IsActive = 1
                ORDER BY Code";

            var accounts = connection.Query<Account>(sql);
            return accounts.ToList();
            //return BuildAccountHierarchy(accounts.ToList());

            //List<Account> accounts = new List<Account>();
            //try
            //{
            //    string query = @"
            //    SELECT a.*, g.GroupName, t.TypeName,
            //           p.AccountName as ParentName
            //    FROM Accounts a
            //    LEFT JOIN AccountGroups g ON a.GroupID = g.GroupID
            //    LEFT JOIN AccountTypes t ON g.TypeID = t.TypeID
            //    LEFT JOIN Accounts p ON a.ParentID = p.AccountID";

            //    if (activeOnly)
            //    {
            //        query += " WHERE a.IsActive = 1";
            //    }

            //    query += " ORDER BY a.AccountCode";

            //    DataTable result = _dbHelper.ExecuteQuery(query);

            //    foreach (DataRow row in result.Rows)
            //    {
            //        accounts.Add(MapRowToAccount(row));
            //    }
            //}
            //catch (Exception ex)
            //{
            //    System.Windows.Forms.MessageBox.Show("خطأ في جلب الحسابات: " + ex.Message, "خطأ", System.Windows.Forms.MessageBoxButtons.OK, System.Windows.Forms.MessageBoxIcon.Error);
            //}
            //return accounts;
        }

        /// <summary>
        /// بناء شجرة الحسابات متعددة المستويات
        /// </summary>

        /// <summary>
        /// بناء شجرة الحسابات متعددة المستويات
        /// </summary>
        public List<Account> BuildAccountTree()
        {
            List<Account> allAccounts = GetAll();
            List<Account> rootAccounts = new List<Account>();
            Dictionary<int, Account> accountsDict = new Dictionary<int, Account>();

            // بناء قاموس للحسابات للوصول السريع
            foreach (Account account in allAccounts)
            {
                accountsDict[account.Id] = account;
            }

            // بناء الشجرة
            foreach (Account account in allAccounts)
            {
                if (!account.ParentId.HasValue)
                {
                    // حساب جذر
                    rootAccounts.Add(account);
                }
                else if (accountsDict.ContainsKey(account.ParentId.Value))
                {
                    // إضافة الحساب كابن للحساب الأب
                    accountsDict[account.ParentId.Value].Children.Add(account);
                }
            }

            return rootAccounts;
        }


        ///////////////////////////////
        /// <summary>
        /// جلب جميع الحسابات
        /// </summary>
        public async Task<List<Account>> GetAllAccountsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT * FROM Accounts
                WHERE IsActive = 1
                ORDER BY Code";

            var accounts = await connection.QueryAsync<Account>(sql);
            return BuildAccountHierarchy(accounts.ToList());
        }

        /// <summary>
        /// تحويل صف بيانات إلى كائن حساب
        /// </summary>
        private static Account MapRowToAccount(DataRow row)
        {
            Account account = new Account
            {
                Id = Convert.ToInt32(row["Id"]),
                Code = row["Code"].ToString(),
                Name = row["Name"].ToString(),
                //GroupID = Convert.ToInt32(row["GroupID"]),
                ParentId = row["ParentID"] != DBNull.Value ? Convert.ToInt32(row["ParentId"]) : (int?)null,
                Level = Convert.ToInt32(row["Level"]),
                IsActive = Convert.ToBoolean(row["IsActive"]),
                Balance = Convert.ToDecimal(row["Balance"]),
                Description = row["Description"] != DBNull.Value ? row["Description"].ToString() : null,
                CreatedDate = Convert.ToDateTime(row["CreatedDate"]),
                //CreatedBy = Convert.ToInt32(row["CreatedBy"]),
                //GroupName = row["Type"].ToString(),
                //Type = row["TypeName"].ToString(),
                //ParentName = row["ParentName"] != DBNull.Value ? row["ParentName"].ToString() : null
                IsParent = Convert.ToBoolean(row["IsParent"]),
                //Parent = new Account(),


            };

            return account;
        }



        /// <summary>
        /// جلب الحسابات الرئيسية فقط
        /// </summary>
        public async Task<List<Account>> GetMainAccountsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT * FROM Accounts
                WHERE ParentId IS NULL AND IsActive = 1
                ORDER BY Code";

            var accounts = await connection.QueryAsync<Account>(sql);
            return accounts.ToList();
        }

        /// <summary>
        /// جلب الحسابات الفرعية لحساب معين
        /// </summary>
        public async Task<List<Account>> GetChildAccountsAsync(int parentId)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT * FROM Accounts
                WHERE ParentId = @ParentId AND IsActive = 1
                ORDER BY Code";

            var accounts = await connection.QueryAsync<Account>(sql, new { ParentId = parentId });
            return accounts.ToList();
        }

        /// <summary>
        /// جلب الحسابات النهائية (غير الأب)
        /// </summary>
        public async Task<List<Account>> GetLeafAccountsAsync()
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT * FROM Accounts
                WHERE IsParent = 0 AND IsActive = 1
                ORDER BY Code";

            var accounts = await connection.QueryAsync<Account>(sql);
            return accounts.ToList();
        }

        /// <summary>
        /// جلب حساب بالمعرف
        /// </summary>
        public async Task<Account?> GetAccountByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT * FROM Accounts WHERE Id = @Id";
            return await connection.QueryFirstOrDefaultAsync<Account>(sql, new { Id = id, Rb = Global_Variable.Mk_resources.RB_SN });
        }

        /// <summary>
        /// جلب حساب بالكود
        /// </summary>
        public async Task<Entities.Accounting.Account?> GetAccountByCodeAsync(string code)
        {
            Account account = null;
            try
            {
                using var connection = _dbHelper.GetConnection();
                var sql = "SELECT * FROM Accounts WHERE Code = @Code AND IsActive = 1 and Rb=@Rb";
                account= await connection.QueryFirstOrDefaultAsync<Account>(sql, new { Code = code, Rb = Global_Variable.Mk_resources.RB_SN });
            }catch(Exception ex) { RJMessageBox.Show(ex.Message); }
            return account;
        }

        /// <summary>
        /// إضافة حساب جديد
        /// </summary>
        public async Task<int> AddAccountAsync(Account account)
        {
            using var connection = _dbHelper.GetConnection();
            connection.Open();
            using var transaction = connection.BeginTransaction();
            //connection.Open();

            try
            {
                account.CreatedDate = DateTime.Now;

                // التحقق من عدم تكرار الكود
                var existingAccount = await GetAccountByCodeAsync(account.Code);
                if (existingAccount != null)
                {
                    throw new InvalidOperationException($"كود الحساب '{account.Code}' موجود مسبقاً");
                }

                // تحديد المستوى والتحقق من الحساب الأب
                if (account.ParentId.HasValue && account.ParentId.Value > 0)
                {
                    var parent = await GetAccountByIdAsync(account.ParentId.Value);
                    if (parent == null)
                    {
                        throw new InvalidOperationException("الحساب الأب غير موجود");
                    }

                    // التحقق من أن نوع الحساب متوافق مع الحساب الأب
                    if (parent.Type != account.Type)
                    {
                        throw new InvalidOperationException("نوع الحساب الفرعي يجب أن يكون نفس نوع الحساب الأب");
                    }

                    // التحقق من أن الكود يبدأ بكود الحساب الأب
                    if (!account.Code.StartsWith(parent.Code))
                    {
                        throw new InvalidOperationException($"كود الحساب الفرعي يجب أن يبدأ بكود الحساب الأب: {parent.Code}");
                    }

                    account.Level = parent.Level + 1;

                    // تحديث الحساب الأب ليصبح حساب أب
                    await connection.ExecuteAsync("UPDATE Accounts SET IsParent = 1 WHERE Id = @Id", new { Id = account.ParentId.Value });
                }
                else
                {
                    account.Level = 1;
                    account.ParentId = null; // تأكد من أن ParentId هو null للحسابات الرئيسية
                }

                // التحقق من أن المستوى لا يتجاوز الحد الأقصى (4 مستويات)
                if (account.Level > 4)
                {
                    throw new InvalidOperationException("لا يمكن إنشاء أكثر من 4 مستويات في الشجرة المحاسبية");
                }

                var sql = @"
                    INSERT INTO Accounts (Code, Name, NameEnglish, Type, Nature, ParentId, Level, IsParent, IsActive, Balance, Description, CreatedDate)
                    VALUES (@Code, @Name, @NameEnglish, @Type, @Nature, @ParentId, @Level, @IsParent, @IsActive, @Balance, @Description, @CreatedDate);
                    SELECT last_insert_rowid();";

                var newId = await connection.QuerySingleAsync<int>(sql, new
                {
                    account.Code,
                    account.Name,
                    account.NameEnglish,
                    Type = (int)account.Type,
                    Nature = (int)account.Nature,
                    account.ParentId,
                    account.Level,
                    account.IsParent,
                    account.IsActive,
                    account.Balance,
                    account.Description,
                    account.CreatedDate
                });

                transaction.Commit();
                return newId;
            }
            catch
            {
                transaction.Rollback();
                throw;
            }
        }

        /// <summary>
        /// تحديث حساب
        /// </summary>
        public async Task<bool> UpdateAccountAsync(Account account)
        {
            using var connection = _dbHelper.GetConnection();
            account.UpdatedDate = DateTime.Now;

            var sql = @"
                UPDATE Accounts
                SET Name = @Name, NameEnglish = @NameEnglish, Type = @Type, Nature = @Nature,
                    Description = @Description, UpdatedDate = @UpdatedDate
                WHERE Id = @Id";

            var rowsAffected = await connection.ExecuteAsync(sql, account);
            return rowsAffected > 0;
        }

        /// <summary>
        /// حذف حساب (إلغاء تفعيل)
        /// </summary>
        public bool  DeleteAccountAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();

            // التحقق من وجود حسابات فرعية
            var hasChildren =  HasChildAccountsAsync(id);
            if (hasChildren)
            {
                throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على حسابات فرعية");
            }

            // التحقق من وجود حركات على الحساب
            var hasTransactions =  HasTransactionsAsync(id);
            if (hasTransactions)
            {
                throw new InvalidOperationException("لا يمكن حذف حساب يحتوي على حركات");
            }

            var sql = "UPDATE Accounts SET IsActive = 0 WHERE Id = @Id";
            var rowsAffected =  connection.ExecuteAsync(sql, new { Id = id }).Result;
            return rowsAffected > 0;
        }

        /// <summary>
        /// تحديث رصيد حساب
        /// </summary>
        public async Task<bool> UpdateAccountBalanceAsync(int accountId, decimal amount, bool isDebit)
        {
            using var connection = _dbHelper.GetConnection();

            var account = await GetAccountByIdAsync(accountId);
            if (account == null) return false;

            // حساب الرصيد الجديد حسب طبيعة الحساب
            decimal newBalance = account.Balance;
            if (account.Nature == AccountNature.Debit)
            {
                newBalance += isDebit ? amount : -amount;
            }
            else
            {
                newBalance += isDebit ? -amount : amount;
            }

            var sql = "UPDATE Accounts SET Balance = @Balance WHERE Id = @Id";
            var rowsAffected = await connection.ExecuteAsync(sql, new { Balance = newBalance, Id = accountId });
            return rowsAffected > 0;
        }

        /// <summary>
        /// إنشاء شجرة الحسابات الافتراضية
        /// </summary>
        public async Task CreateDefaultChartOfAccountsAsync()
        {
            try
            {
                var accounts = GetDefaultAccounts();
                var accountIdMap = new Dictionary<string, int>();

                // إدراج الحسابات مرتبة حسب المستوى
                var sortedAccounts = accounts.OrderBy(a => a.Level).ThenBy(a => a.Code).ToList();

                using var connection = _dbHelper.GetConnection();

                foreach (var account in sortedAccounts)
                {
                    var existing = await GetAccountByCodeAsync(account.Code);
                    if (existing == null)
                    {
                        // تحديد الحساب الأب إذا كان موجوداً
                        if (account.Level > 1)
                        {
                            var parentCode = GetParentCode(account.Code);
                            if (accountIdMap.ContainsKey(parentCode))
                            {
                                account.ParentId = accountIdMap[parentCode];
                            }
                            else
                            {
                                // البحث عن الحساب الأب في قاعدة البيانات
                                var parentAccount = await GetAccountByCodeAsync(parentCode);
                                if (parentAccount != null)
                                {
                                    account.ParentId = parentAccount.Id;
                                    accountIdMap[parentCode] = parentAccount.Id;
                                }
                            }
                        }

                        // إدراج الحساب
                        const string sql = @"
                            INSERT INTO Accounts (Code, Name, NameEnglish, Type, Nature, ParentId, Level, IsParent, IsActive, Description, CreatedDate)
                            VALUES (@Code, @Name, @NameEnglish, @Type, @Nature, @ParentId, @Level, @IsParent, @IsActive, @Description, @CreatedDate);
                            SELECT last_insert_rowid();";

                        var newId = await connection.QuerySingleAsync<int>(sql, new
                        {
                            account.Code,
                            account.Name,
                            account.NameEnglish,
                            Type = (int)account.Type,
                            Nature = (int)account.Nature,
                            account.ParentId,
                            account.Level,
                            account.IsParent,
                            account.IsActive,
                            account.Description,
                            CreatedDate = DateTime.Now
                        });

                        accountIdMap[account.Code] = newId;
                    }
                    else
                    {
                        accountIdMap[account.Code] = existing.Id;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إنشاء شجرة الحسابات: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// إدخال بيانات تجريبية للحسابات
        /// </summary>
        public async Task InsertSampleDataAsync()
        {
            try
            {
                // إدخال بعض الأرصدة الافتتاحية
                var sampleBalances = new[]
                {
                    new { Code = "111", Balance = 50000m }, // النقدية بالصندوق
                    new { Code = "1121", Balance = 250000m }, // البنك الأهلي
                    new { Code = "1122", Balance = 180000m }, // بنك الراجحي
                    new { Code = "1131", Balance = 75000m }, // عملاء محليون
                    new { Code = "1141", Balance = 120000m }, // مخزون المواد الخام
                    new { Code = "2111", Balance = 45000m }, // موردون محليون
                    new { Code = "31", Balance = 500000m }, // رأس المال
                    new { Code = "411", Balance = 150000m }, // مبيعات البضائع
                    new { Code = "521", Balance = 25000m }, // الرواتب والأجور
                    new { Code = "522", Balance = 8000m }, // إيجار المكتب
                };

                using var connection = _dbHelper.GetConnection();

                int updatedCount = 0;
                int notFoundCount = 0;

                foreach (var balance in sampleBalances)
                {
                    try
                    {
                        // التحقق من وجود الحساب أولاً
                        const string checkSql = "SELECT COUNT(*) FROM Accounts WHERE Code = @Code";
                        var exists = await connection.QuerySingleAsync<int>(checkSql, new { balance.Code });

                        if (exists > 0)
                        {
                            const string updateSql = @"
                                UPDATE Accounts
                                SET Balance = @Balance
                                WHERE Code = @Code";

                            await connection.ExecuteAsync(updateSql, balance);
                            updatedCount++;
                        }
                        else
                        {
                            notFoundCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception($"خطأ في تحديث الحساب {balance.Code}: {ex.Message}", ex);
                    }
                }

                if (notFoundCount > 0)
                {
                    throw new Exception($"لم يتم العثور على {notFoundCount} حساب من أصل {sampleBalances.Length}. تأكد من إنشاء شجرة الحسابات الافتراضية أولاً.");
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"خطأ في إدخال البيانات التجريبية: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// الحصول على كود الحساب الأب
        /// </summary>
        private string GetParentCode(string accountCode)
        {
            if (accountCode.Length <= 1) return string.Empty;

            // إزالة آخر رقم للحصول على كود الأب
            return accountCode.Substring(0, accountCode.Length - 1);
        }

        /// <summary>
        /// بناء هيكل شجرة الحسابات
        /// </summary>
        private List<Account> BuildAccountHierarchy(List<Account> accounts)
        {
            var accountDict = accounts.ToDictionary(a => a.Id);
            var rootAccounts = new List<Account>();

            foreach (var account in accounts)
            {
                if (account.ParentId.HasValue && accountDict.ContainsKey(account.ParentId.Value))
                {
                    var parent = accountDict[account.ParentId.Value];
                    parent.Children.Add(account);
                    account.Parent = parent;
                    rootAccounts.Add(account);
                }
                else
                {
                    rootAccounts.Add(account);
                }
            }

            return rootAccounts;
        }

        /// <summary>
        /// تحديث حالة الحساب الأب
        /// </summary>
        private async Task UpdateParentStatusAsync(int accountId, bool isParent)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "UPDATE Accounts SET IsParent = @IsParent WHERE Id = @Id";
            await connection.ExecuteAsync(sql, new { IsParent = isParent, Id = accountId });
        }

        /// <summary>
        /// تحديث الحساب الأب لحساب معين
        /// </summary>
        public async Task UpdateAccountParentAsync(int accountId, int parentId)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "UPDATE Accounts SET ParentId = @ParentId WHERE Id = @Id";
            await connection.ExecuteAsync(sql, new { ParentId = parentId, Id = accountId });

            // تحديث حالة الحساب الأب
            await UpdateParentStatusAsync(parentId, true);
        }

        /// <summary>
        /// إنشاء كود تلقائي للحساب الفرعي
        /// </summary>
        public async Task<string> GenerateChildAccountCodeAsync(int parentId)
        {
            var parent = await GetAccountByIdAsync(parentId);
            if (parent == null)
                throw new ArgumentException("الحساب الأب غير موجود");

            var childAccounts = await GetChildAccountsAsync(parentId);

            // البحث عن أعلى رقم مستخدم
            int maxNumber = 0;
            foreach (var child in childAccounts)
            {
                if (child.Code.StartsWith(parent.Code) && child.Code.Length == parent.Code.Length + 1)
                {
                    string lastDigit = child.Code.Substring(parent.Code.Length);
                    if (int.TryParse(lastDigit, out int number))
                    {
                        maxNumber = Math.Max(maxNumber, number);
                    }
                }
            }

            return parent.Code + (maxNumber + 1).ToString();
        }

        /// <summary>
        /// التحقق من إمكانية إضافة حساب فرعي
        /// </summary>
        public async Task<bool> CanAddChildAccountAsync(int parentId)
        {
            var parent = await GetAccountByIdAsync(parentId);
            if (parent == null) return false;

            // التحقق من أن المستوى لا يتجاوز الحد الأقصى
            return parent.Level < 4;
        }

        /// <summary>
        /// الحصول على مسار الحساب الكامل
        /// </summary>
        public async Task<string> GetAccountFullPathAsync(int accountId)
        {
            var account = await GetAccountByIdAsync(accountId);
            if (account == null) return string.Empty;

            var path = new List<string> { account.Name };
            var currentAccount = account;

            while (currentAccount.ParentId.HasValue)
            {
                var parent = await GetAccountByIdAsync(currentAccount.ParentId.Value);
                if (parent == null) break;

                path.Insert(0, parent.Name);
                currentAccount = parent;
            }

            return string.Join(" > ", path);
        }

        /// <summary>
        /// التحقق من وجود حسابات فرعية
        /// </summary>
        private bool HasChildAccountsAsync(int accountId)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM Accounts WHERE ParentId = @AccountId AND IsActive = 1";
            int count = connection.QuerySingleAsync<int>(sql, new { AccountId = accountId }).Result;
            return count > 0;
        }

        /// <summary>
        /// التحقق من وجود حركات على الحساب
        /// </summary>
        //private async Task<bool> HasTransactionsAsync(int accountId)
        //{
        //    using var connection = _dbHelper.GetConnection();
        //    var sql = "SELECT COUNT(*) FROM JournalEntryDetails WHERE AccountId = @AccountId";
        //    var count = await connection.QuerySingleAsync<int>(sql, new { AccountId = accountId });
        //    return count > 0;
        //}
        private bool HasTransactionsAsync(int accountId)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = "SELECT COUNT(*) FROM JournalEntryDetails WHERE AccountId = @AccountId";
            var count =  connection.QuerySingleAsync<int>(sql, new { AccountId = accountId }).Result;
            return count > 0;
        }

        /// <summary>
        /// الحصول على شجرة الحسابات الافتراضية
        /// </summary>
        private List<Account> GetDefaultAccounts()
        {
            var accounts = new List<Account>();

            // المستوى الأول - الحسابات الرئيسية
            accounts.AddRange(new[]
            {
                new Account { Code = "1", Name = "الأصول", NameEnglish = "Assets", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 1, IsActive = true, Description = "جميع الأصول المملوكة للشركة" },
                new Account { Code = "2", Name = "الخصوم", NameEnglish = "Liabilities", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = true, Level = 1, IsActive = true, Description = "جميع الالتزامات والديون على الشركة" },
                new Account { Code = "3", Name = "حقوق الملكية", NameEnglish = "Equity", Type = AccountType.Equity, Nature = AccountNature.Credit, IsParent = true, Level = 1, IsActive = true, Description = "حقوق أصحاب الشركة" },
                new Account { Code = "4", Name = "الإيرادات", NameEnglish = "Revenue", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = true, Level = 1, IsActive = true, Description = "جميع الإيرادات والدخل" },
                new Account { Code = "5", Name = "المصروفات", NameEnglish = "Expenses", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = true, Level = 1, IsActive = true, Description = "جميع المصروفات والتكاليف" }
            });

            // المستوى الثاني - الحسابات الفرعية الرئيسية
            accounts.AddRange(new[]
            {
                // الأصول المتداولة
                new Account { Code = "11", Name = "الأصول المتداولة", NameEnglish = "Current Assets", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 2, IsActive = true },
                new Account { Code = "12", Name = "الأصول الثابتة", NameEnglish = "Fixed Assets", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 2, IsActive = true },

                // الخصوم المتداولة
                new Account { Code = "21", Name = "الخصوم المتداولة", NameEnglish = "Current Liabilities", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = true, Level = 2, IsActive = true },
                new Account { Code = "22", Name = "الخصوم طويلة الأجل", NameEnglish = "Long-term Liabilities", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = true, Level = 2, IsActive = true },

                // حقوق الملكية
                new Account { Code = "31", Name = "رأس المال", NameEnglish = "Capital", Type = AccountType.Equity, Nature = AccountNature.Credit, IsParent = false, Level = 2, IsActive = true },
                new Account { Code = "32", Name = "الأرباح المحتجزة", NameEnglish = "Retained Earnings", Type = AccountType.Equity, Nature = AccountNature.Credit, IsParent = false, Level = 2, IsActive = true },
                new Account { Code = "33", Name = "أرباح العام الجاري", NameEnglish = "Current Year Earnings", Type = AccountType.Equity, Nature = AccountNature.Credit, IsParent = false, Level = 2, IsActive = true },

                // الإيرادات
                new Account { Code = "41", Name = "إيرادات المبيعات", NameEnglish = "Sales Revenue", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = true, Level = 2, IsActive = true },
                new Account { Code = "42", Name = "إيرادات أخرى", NameEnglish = "Other Revenue", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = true, Level = 2, IsActive = true },

                // المصروفات
                new Account { Code = "51", Name = "تكلفة البضاعة المباعة", NameEnglish = "Cost of Goods Sold", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 2, IsActive = true },
                new Account { Code = "52", Name = "المصروفات الإدارية", NameEnglish = "Administrative Expenses", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = true, Level = 2, IsActive = true },
                new Account { Code = "53", Name = "مصروفات التسويق", NameEnglish = "Marketing Expenses", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = true, Level = 2, IsActive = true },
                new Account { Code = "54", Name = "المصروفات المالية", NameEnglish = "Financial Expenses", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = true, Level = 2, IsActive = true }
            });

            // المستوى الثالث - الحسابات التفصيلية
            accounts.AddRange(new[]
            {
                // الأصول المتداولة - التفصيل
                new Account { Code = "111", Name = "النقدية بالصندوق", NameEnglish = "Cash on Hand", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "النقدية الموجودة في صندوق الشركة" },
                new Account { Code = "112", Name = "البنوك", NameEnglish = "Banks", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 3, IsActive = true, Description = "الحسابات البنكية للشركة" },
                new Account { Code = "113", Name = "العملاء", NameEnglish = "Accounts Receivable", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 3, IsActive = true, Description = "المبالغ المستحقة من العملاء" },
                new Account { Code = "114", Name = "المخزون", NameEnglish = "Inventory", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = true, Level = 3, IsActive = true, Description = "البضائع والمواد الخام" },
                new Account { Code = "115", Name = "مصروفات مدفوعة مقدماً", NameEnglish = "Prepaid Expenses", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "المصروفات المدفوعة مسبقاً" },
                new Account { Code = "116", Name = "أوراق القبض", NameEnglish = "Notes Receivable", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الكمبيالات والسندات المستحقة القبض" },
                new Account { Code = "117", Name = "الاستثمارات قصيرة الأجل", NameEnglish = "Short-term Investments", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الاستثمارات المؤقتة" },

                // الأصول الثابتة - التفصيل
                new Account { Code = "121", Name = "الأراضي", NameEnglish = "Land", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الأراضي المملوكة للشركة" },
                new Account { Code = "122", Name = "المباني", NameEnglish = "Buildings", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "المباني والمنشآت" },
                new Account { Code = "123", Name = "الأثاث والمعدات", NameEnglish = "Furniture & Equipment", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الأثاث والمعدات المكتبية" },
                new Account { Code = "124", Name = "السيارات", NameEnglish = "Vehicles", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "المركبات والسيارات" },
                new Account { Code = "125", Name = "الآلات والمعدات", NameEnglish = "Machinery & Equipment", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الآلات والمعدات الإنتاجية" },
                new Account { Code = "126", Name = "مجمع إهلاك المباني", NameEnglish = "Accumulated Depreciation - Buildings", Type = AccountType.Assets, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "مجمع إهلاك المباني" },
                new Account { Code = "127", Name = "مجمع إهلاك الأثاث", NameEnglish = "Accumulated Depreciation - Furniture", Type = AccountType.Assets, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "مجمع إهلاك الأثاث والمعدات" },
                new Account { Code = "128", Name = "مجمع إهلاك السيارات", NameEnglish = "Accumulated Depreciation - Vehicles", Type = AccountType.Assets, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "مجمع إهلاك السيارات" },

                // الخصوم المتداولة - التفصيل
                new Account { Code = "211", Name = "الموردون", NameEnglish = "Accounts Payable", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = true, Level = 3, IsActive = true, Description = "المبالغ المستحقة للموردين" },
                new Account { Code = "212", Name = "مصروفات مستحقة", NameEnglish = "Accrued Expenses", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "المصروفات المستحقة غير المدفوعة" },
                new Account { Code = "213", Name = "ضرائب مستحقة", NameEnglish = "Accrued Taxes", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "الضرائب المستحقة للحكومة" },
                new Account { Code = "214", Name = "رواتب مستحقة", NameEnglish = "Accrued Salaries", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "الرواتب المستحقة للموظفين" },
                new Account { Code = "215", Name = "أوراق الدفع", NameEnglish = "Notes Payable", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "الكمبيالات والسندات المستحقة الدفع" },
                new Account { Code = "216", Name = "قروض قصيرة الأجل", NameEnglish = "Short-term Loans", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "القروض قصيرة الأجل" },
                new Account { Code = "217", Name = "إيرادات مقبوضة مقدماً", NameEnglish = "Unearned Revenue", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "الإيرادات المقبوضة مسبقاً" },

                // الخصوم طويلة الأجل - التفصيل
                new Account { Code = "221", Name = "قروض طويلة الأجل", NameEnglish = "Long-term Loans", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "القروض طويلة الأجل" },
                new Account { Code = "222", Name = "سندات مستحقة الدفع", NameEnglish = "Bonds Payable", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "السندات المصدرة من الشركة" },
                new Account { Code = "223", Name = "مخصص نهاية الخدمة", NameEnglish = "End of Service Provision", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "مخصص مكافآت نهاية الخدمة" },

                // إيرادات المبيعات - التفصيل
                new Account { Code = "411", Name = "مبيعات البضائع", NameEnglish = "Merchandise Sales", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "إيرادات بيع البضائع" },
                new Account { Code = "412", Name = "مبيعات الخدمات", NameEnglish = "Service Sales", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "إيرادات تقديم الخدمات" },
                new Account { Code = "413", Name = "خصومات مسموحة", NameEnglish = "Sales Discounts", Type = AccountType.Revenue, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الخصومات الممنوحة للعملاء" },
                new Account { Code = "414", Name = "مردودات المبيعات", NameEnglish = "Sales Returns", Type = AccountType.Revenue, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مردودات وإرجاعات المبيعات" },

                // إيرادات أخرى - التفصيل
                new Account { Code = "421", Name = "إيرادات الفوائد", NameEnglish = "Interest Income", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "الفوائد المكتسبة من الودائع" },
                new Account { Code = "422", Name = "إيرادات متنوعة", NameEnglish = "Miscellaneous Income", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "إيرادات متنوعة أخرى" },
                new Account { Code = "423", Name = "أرباح بيع الأصول", NameEnglish = "Gain on Sale of Assets", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "أرباح بيع الأصول الثابتة" },
                new Account { Code = "424", Name = "إيرادات الإيجار", NameEnglish = "Rental Income", Type = AccountType.Revenue, Nature = AccountNature.Credit, IsParent = false, Level = 3, IsActive = true, Description = "إيرادات تأجير العقارات" },

                // المصروفات الإدارية - التفصيل
                new Account { Code = "521", Name = "الرواتب والأجور", NameEnglish = "Salaries & Wages", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "رواتب وأجور الموظفين" },
                new Account { Code = "522", Name = "إيجار المكتب", NameEnglish = "Office Rent", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "إيجار المكاتب والمباني" },
                new Account { Code = "523", Name = "الكهرباء والماء", NameEnglish = "Utilities", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "فواتير الكهرباء والماء والغاز" },
                new Account { Code = "524", Name = "الهاتف والإنترنت", NameEnglish = "Phone & Internet", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "فواتير الهاتف والإنترنت" },
                new Account { Code = "525", Name = "القرطاسية", NameEnglish = "Stationery", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات القرطاسية والمكتبية" },
                new Account { Code = "526", Name = "صيانة وإصلاح", NameEnglish = "Maintenance & Repairs", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات الصيانة والإصلاح" },
                new Account { Code = "527", Name = "التأمين", NameEnglish = "Insurance", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "أقساط التأمين" },
                new Account { Code = "528", Name = "الاستشارات المهنية", NameEnglish = "Professional Fees", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "أتعاب المحاسبين والمحامين" },
                new Account { Code = "529", Name = "مصروفات السفر", NameEnglish = "Travel Expenses", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات السفر والانتقال" },

                // مصروفات التسويق - التفصيل
                new Account { Code = "531", Name = "الإعلان والدعاية", NameEnglish = "Advertising", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات الإعلان والتسويق" },
                new Account { Code = "532", Name = "عمولات المبيعات", NameEnglish = "Sales Commissions", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "عمولات مندوبي المبيعات" },
                new Account { Code = "533", Name = "معارض وفعاليات", NameEnglish = "Exhibitions & Events", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات المعارض والفعاليات" },
                new Account { Code = "534", Name = "هدايا وضيافة", NameEnglish = "Gifts & Entertainment", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "مصروفات الهدايا والضيافة" },

                // المصروفات المالية - التفصيل
                new Account { Code = "541", Name = "فوائد القروض", NameEnglish = "Interest Expense", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "فوائد القروض والتسهيلات" },
                new Account { Code = "542", Name = "رسوم بنكية", NameEnglish = "Bank Charges", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "الرسوم والعمولات البنكية" },
                new Account { Code = "543", Name = "خسائر أسعار الصرف", NameEnglish = "Foreign Exchange Loss", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "خسائر تقلبات أسعار الصرف" },
                new Account { Code = "544", Name = "خسائر بيع الأصول", NameEnglish = "Loss on Sale of Assets", Type = AccountType.Expenses, Nature = AccountNature.Debit, IsParent = false, Level = 3, IsActive = true, Description = "خسائر بيع الأصول الثابتة" }
            });

            // المستوى الرابع - الحسابات الفرعية التفصيلية
            accounts.AddRange(new[]
            {
                // البنوك - التفصيل
                new Account { Code = "1121", Name = "البنك الأهلي التجاري", NameEnglish = "National Commercial Bank", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "حساب البنك الأهلي التجاري" },
                new Account { Code = "1122", Name = "بنك الراجحي", NameEnglish = "Al Rajhi Bank", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "حساب بنك الراجحي" },
                new Account { Code = "1123", Name = "البنك السعودي للاستثمار", NameEnglish = "Saudi Investment Bank", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "حساب البنك السعودي للاستثمار" },
                new Account { Code = "1124", Name = "بنك الرياض", NameEnglish = "Riyad Bank", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "حساب بنك الرياض" },

                // العملاء - التفصيل
                new Account { Code = "1131", Name = "عملاء محليون", NameEnglish = "Local Customers", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "العملاء المحليون" },
                new Account { Code = "1132", Name = "عملاء خارجيون", NameEnglish = "Foreign Customers", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "العملاء الخارجيون" },
                new Account { Code = "1133", Name = "عملاء حكوميون", NameEnglish = "Government Customers", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "العملاء الحكوميون" },
                new Account { Code = "1134", Name = "مخصص ديون مشكوك فيها", NameEnglish = "Allowance for Doubtful Debts", Type = AccountType.Assets, Nature = AccountNature.Credit, IsParent = false, Level = 4, IsActive = true, Description = "مخصص الديون المشكوك في تحصيلها" },

                // المخزون - التفصيل
                new Account { Code = "1141", Name = "مخزون المواد الخام", NameEnglish = "Raw Materials Inventory", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "مخزون المواد الخام" },
                new Account { Code = "1142", Name = "مخزون البضائع الجاهزة", NameEnglish = "Finished Goods Inventory", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "مخزون البضائع الجاهزة للبيع" },
                new Account { Code = "1143", Name = "مخزون قطع الغيار", NameEnglish = "Spare Parts Inventory", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "مخزون قطع الغيار والصيانة" },
                new Account { Code = "1144", Name = "مخزون تحت التشغيل", NameEnglish = "Work in Process Inventory", Type = AccountType.Assets, Nature = AccountNature.Debit, IsParent = false, Level = 4, IsActive = true, Description = "مخزون البضائع تحت التشغيل" },

                // الموردون - التفصيل
                new Account { Code = "2111", Name = "موردون محليون", NameEnglish = "Local Suppliers", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 4, IsActive = true, Description = "الموردون المحليون" },
                new Account { Code = "2112", Name = "موردون خارجيون", NameEnglish = "Foreign Suppliers", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 4, IsActive = true, Description = "الموردون الخارجيون" },
                new Account { Code = "2113", Name = "موردو الخدمات", NameEnglish = "Service Providers", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 4, IsActive = true, Description = "موردو الخدمات" },
                new Account { Code = "2114", Name = "مقاولون", NameEnglish = "Contractors", Type = AccountType.Liabilities, Nature = AccountNature.Credit, IsParent = false, Level = 4, IsActive = true, Description = "المقاولون والمتعهدون" }
            });

            // تحديد العلاقات الهرمية
            SetParentChildRelationships(accounts);

            return accounts;
        }

        /// <summary>
        /// تحديد العلاقات الهرمية بين الحسابات
        /// </summary>
        private void SetParentChildRelationships(List<Account> accounts)
        {
            // لا نحتاج لتعيين ParentId هنا لأنه سيتم تحديده في CreateDefaultChartOfAccountsAsync
            // فقط نحدد IsParent للحسابات الأب
            foreach (var account in accounts)
            {
                if (account.Level > 1)
                {
                    // البحث عن الحساب الأب بناءً على الكود
                    var parentCode = account.Code.Substring(0, account.Code.Length - 1);
                    var parent = accounts.FirstOrDefault(a => a.Code == parentCode);
                    if (parent != null)
                    {
                        parent.IsParent = true;
                    }
                }
            }
        }
    }

}
