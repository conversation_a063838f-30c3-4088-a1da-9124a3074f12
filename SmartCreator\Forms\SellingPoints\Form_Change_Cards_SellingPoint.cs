﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_Change_Cards_SellingPoint : RJChildForm
    {
        private Smart_DataAccess Smart_DA = null;
        public HashSet<UmUser> Users = null;
        public HashSet<HSUser> HSUsers = null;
        public bool is_success = false;

        public Form_Change_Cards_SellingPoint()
        {
            InitializeComponent();
            btnSave.BackColor=UIAppearance.StyleColor;

            Smart_DA = new Smart_DataAccess();
            Get_SellingPoint();

            this.Text = "تغير نقطة البيع";


            if (UIAppearance.Language_ar == false)
                this.Text = "Change Selling Point ";


            //return;


            rjLabel2.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            Check_Change_Percent.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            utils.Control_textSize(pnlClientArea);

        }

        private void Get_SellingPoint()
        {

            Smart_DataAccess da = new Smart_DataAccess();
            CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";
        }


        private void btnSave_Click(object sender, EventArgs e)
        {

            //if (Users.Count <= 0)
            //    return;

            if (CBox_SellingPoint.Text == "")
            {
                RJMessageBox.Show("الرجاء اختيار نقطة بيع");
                return;
            }
            SellingPoint sp = Smart_DA.Get_SellingPoint_Code(CBox_SellingPoint.SelectedValue.ToString());
            if (Check_Change_Percent.Check)
            {
                if (Users != null)
                {
                    foreach (UmUser user in Users)
                    {
                        float totalPrice = user.TotalPrice;
                        float price = user.Price;
                        if (price == 0)
                            price = totalPrice;
                        float percentage = 0;
                        int percentage_type = 0;

                        if (sp.Is_percentage == 1)
                        {
                            percentage_type = sp.PercentageType;
                            percentage = sp.Percentage;
                            if (percentage_type == 0)
                            {
                                float percentage_value = (price * percentage) / 100;
                                totalPrice = price - percentage_value;
                            }
                            else
                            {
                                totalPrice = price - percentage;
                            }
                        }
                        user.TotalPrice = totalPrice;
                        user.Percentage = sp.Percentage;
                        user.PercentageType = sp.PercentageType;
                    }
                }
            }

            if (HSUsers != null)
            {
                foreach (HSUser user in HSUsers)
                {
                    float totalPrice = user.TotalPrice;
                    float price = user.Price;
                    if (price == 0)
                        price = totalPrice;
                    float percentage = 0;
                    int percentage_type = 0;

                    if (sp.Is_percentage == 1)
                    {
                        percentage_type = sp.PercentageType;
                        percentage = sp.Percentage;
                        if (percentage_type == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                    }
                    user.TotalPrice = totalPrice;
                    user.Percentage = sp.Percentage;
                    user.PercentageType = sp.PercentageType;
                }
            }
        
           
            lock (Sql_DataAccess.Lock_localDB)
            {
                Smart_DataAccess smart_dataAccess = new Smart_DataAccess();
                if (Users != null)
                {
                    if (smart_dataAccess.Change_Selling_Point<UmUser>("UmUser", Users, CBox_SellingPoint.SelectedValue.ToString(), CBox_SellingPoint.Text, false, Check_Change_Percent.Check))
                    {
                        RJMessageBox.Show("تم تغير نقطة البيع للكروت المحدده");
                        is_success = true;
                        this.Close();
                    }
                }
                if (HSUsers != null)
                {
                    if (smart_dataAccess.Change_Selling_Point<HSUser>("HSUser", HSUsers, CBox_SellingPoint.SelectedValue.ToString(), CBox_SellingPoint.Text, false, Check_Change_Percent.Check))
                    {
                        RJMessageBox.Show("تم تغير نقطة البيع للكروت المحدده");
                        is_success = true;
                        this.Close();
                    }
                }
            }
        }
        private void Form_Change_Cards_SellingPoint_Load(object sender, EventArgs e)
        {
            Get_SellingPoint();
        }
    }
}
