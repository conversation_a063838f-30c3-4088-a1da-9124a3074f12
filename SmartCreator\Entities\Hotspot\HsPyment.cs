﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Hotspot
{
    [UniqueConstraint("UserName", "Price", "ProfileName", "AddedDb", "MkId")]

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class HsPyment : BasePyment
    {
        public HsPyment() { }

        [PrimaryKey, Required, Unique, AutoIncrement]
        public long Id { get; set; }

        //public long Sn { get; set; }

        //[PrimaryKey, Unique, Required]
        //public string Sn_Name { get; set; }

        [Default(0)]
        public int DeleteFromServer { get; set; } = 0;

        [ForeignKey(typeof(HSUser), OnDelete = "CASCADE", ColumnName = "Sn_Name")]
        [Index]
        public string Fk_Sn_Name { get; set; }
        public bool MainProfile { get; set; } = false;// رصيد اساسي مدمج اول مره وليس اضافه من البرنامج باقه جديد
        [DisplayName("نوع الرصيد"),Computed]
        //public string Str_MainProfile { get; set; }
        public string Str_MainProfile
        {
            get
            {
                return MainProfile ? "اساسي" : "اضافة";
            }

        }

        public void Syn_HS_Pyments_to_FirstUsers(List<HSUser> _HSUser = null)
        {
            Sql_DataAccess Local_DA = new Sql_DataAccess();
            List<SellingPoint> sp = new List<SellingPoint>();
            List<HsPyment> _HSPyment = Local_DA.Get_Not_Delet_fromServer<HsPyment>("HsPyment");

            if (_HSUser == null)
                _HSUser = Local_DA.Get_Not_Delet_fromServer<HSUser>("HSUser");

            //=============== set deleteFromServer === that not have user ================
            var DeleteFromServer = (from user in _HSPyment
                                    where !_HSUser.Any(f => f.UserName == user.UserName && user.DeleteFromServer==0)
                                    select user as HsPyment).ToHashSet();
            if (DeleteFromServer != null)
            {
                if (DeleteFromServer.Count > 0 && _HSUser.Count>1)
                    Local_DA.Set_Delet_fromServer<HsPyment>("HsPyment", DeleteFromServer,"Id");
            }
            //======================================================================
            _HSPyment = Local_DA.Get_Not_Delet_fromServer<HsPyment>("HsPyment");
            var PY_Users = (from user in _HSUser
                            where !_HSPyment.Any(f => f.UserName == user.UserName)
                            select new HsPyment
                            {
                                UserName = user.UserName,
                                Price = (user.Price),
                                TotalPrice = (user.TotalPrice),
                                AddedDate = user.RegDate,
                                //AddedDate = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                Fk_Sn_Name = user.Sn_Name,
                                DeleteFromServer = 0,
                                ProfileName = user.ProfileName,
                                ProfileTransferLimit = user.TransferLimit,
                                ProfileUptimeLimit = user.UptimeLimit,
                                ProfileValidity = user.ValidityLimit,
                                Percentage = user.Percentage,
                                PercentageType = user.PercentageType,
                                MainProfile = true,
                                MkId = Global_Variable.Mk_resources.RB_SN,

                            }).ToList();

            if (PY_Users.Count == 0)
                return;

            if (Local_DA.Add_HSPyement_ToDB(PY_Users))
            {

            }

        }

        public void Add_HS_Pyments_after_print(HashSet<HSUser> _HSUser,bool MainProfile=false)
        {
            Sql_DataAccess Local_DA = new Sql_DataAccess();
            if (_HSUser == null)
                return;

            var PY_Users = (from user in _HSUser
                            //where !_HSPyment.Any(f => f.UserName == user.UserName)
                            select new HsPyment
                            {
                                UserName = user.UserName,
                                Price = (user.Price),
                                TotalPrice = (user.Price),
                                AddedDate = user.RegDate,
                                //AddedDate = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                Fk_Sn_Name = user.Sn_Name,
                                DeleteFromServer = 0,
                                ProfileName = user.ProfileName,
                                ProfileTransferLimit = user.TransferLimit,
                                ProfileUptimeLimit = user.UptimeLimit,
                                ProfileValidity = user.ValidityLimit,
                                Percentage = user.Percentage,
                                PercentageType = user.PercentageType,
                                MainProfile = MainProfile,
                                MkId = Global_Variable.Mk_resources.RB_SN,
                              

                            }).ToList();

            if (PY_Users.Count == 0)
                return;

            if (Local_DA.Add_HSPyement_ToDB(PY_Users))
            {

            }

        }

    }
}
