﻿using SmartCreator.Data;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using tik4net;

namespace SmartCreator.Models
{
    //	"date_added_Localdb"	INTEGER DEFAULT 0,
    public class SourceSessionUserManager_FromDB
    {
        public SourceSessionUserManager_FromDB() { }
        public int id { get; set; }
        public double sn { get; set; }
        public string idHX { get; set; }
        public string userName { get; set; }
        public int custId { get; set; }
        public double userId { get; set; }
        public string nasPortId { get; set; }
        public string callingStationId { get; set; }
        public string ipUser { get; set; }
        public string ipRouter { get; set; }
        public string acctSessionId { get; set; }// hex
        public int status { get; set; }
        public string status_str { get; set; }
        public int active { get; set; }
        public double fromTime { get; set; }
        public double tillTime { get; set; }
        public double upTime { get; set; }
        public double bytesDownload { get; set; }
        public double bytesUpload { get; set; }
        public string sn_userName { get; set; }
        public string fk_sn_userName_User { get; set; }
        public int fk_User_localDB_id { get; set; }
        public int Delet_fromServer { get; set; }
        public double date_added_Localdb { get; set; }

    }

    public class SourceSessionUserManager_fromMK
    {
        //public SourceSessionUserManager_fromMK() { }
        public string id { get; set; }
        public string idHX { get; set; }
        public string userName { get; set; }
        //public double userId { get; set; }
        public string nasPortId { get; set; }
        public string callingStationId { get; set; }
        public string ipUser { get; set; }
        public string ipRouter { get; set; }
        //public string acctSessionId { get; set; }// hex
        public string status { get; set; }
        public string active { get; set; }
        public string fromTime { get; set; }
        public string tillTime { get; set; }
        public string upTime { get; set; }
        public string bytesDownload { get; set; }
        public string bytesUpload { get; set; }
        public string sn_userName { get; set; }
        //public string fk_sn_userName_User { get; set; }
        //public int fk_User_localDB_id { get; set; }
        //public int Delet_fromServer { get; set; }
        //public double date_added_Localdb { get; set; }

        [Obsolete]
        public static List<SourceSessionUserManager_fromMK> Get_UM_Sessions2()
        {
            List<SourceSessionUserManager_fromMK> sessions = new List<SourceSessionUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    string code = "/tool/user-manager/session/print";
                    var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                  
                    //RJMessageBox.Show("start  get session");
                    //RJMessageBox.Show("start get session" + sessions.Count);


                    foreach (var item in response)
                    {
                        try
                        {
                            SourceSessionUserManager_fromMK card = new SourceSessionUserManager_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", "0");
                            card.idHX = item.GetResponseFieldOrDefault(".id", "0");
                            card.userName = item.GetResponseFieldOrDefault("user", "");
                            card.fromTime = item.GetResponseFieldOrDefault("from-time", "");
                            card.tillTime = item.GetResponseFieldOrDefault("till-time", "");
                            card.upTime = item.GetResponseFieldOrDefault("uptime", "");
                            card.bytesDownload = item.GetResponseFieldOrDefault("download", "0");
                            card.bytesUpload = item.GetResponseFieldOrDefault("upload", "0");
                            card.active = item.GetResponseFieldOrDefault("active", null);
                            card.nasPortId = item.GetResponseFieldOrDefault("nas-port-id", "");
                            card.ipRouter = item.GetResponseFieldOrDefault("host-ip", "");
                            card.callingStationId = item.GetResponseFieldOrDefault("calling-station-id", "");
                            card.ipUser = item.GetResponseFieldOrDefault("user-ip", "");
                            card.status = item.GetResponseFieldOrDefault("status", "");
                            sessions.Add(card);
                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    return sessions;

                    //RJMessageBox.Show("done get session" );
                    //RJMessageBox.Show("done get session" +sessions.Count);

                }
            }
            catch(Exception e) 
            {
                RJMessageBox.Show(e.Message);
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }

            return sessions;

        }

        [Obsolete]
        public static List<SourceSessionUserManager_fromMK> Get_UM_Sessions(string name = "", bool is_syn = false)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                return Get_UM_Sessions_V7(name, is_syn);
            }
            

            List<SourceSessionUserManager_fromMK> session = new List<SourceSessionUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/tool/user-manager/session/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                        loadCmd = connection.CreateCommandAndParameters(code, "user", name);


                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceSessionUserManager_fromMK card = new SourceSessionUserManager_fromMK();
                            string id = item.GetResponseFieldOrDefault(".id", "");
                            card.id = id;
                            card.idHX = id;

                            //card.idHX = item.GetResponseFieldOrDefault(".id", "0");
                            card.userName = item.GetResponseFieldOrDefault("user", "");
                            card.fromTime = item.GetResponseFieldOrDefault("from-time", "");
                            card.tillTime = item.GetResponseFieldOrDefault("till-time", "");
                            card.upTime = item.GetResponseFieldOrDefault("uptime", "");
                            card.bytesDownload = item.GetResponseFieldOrDefault("download", "0");
                            card.bytesUpload = item.GetResponseFieldOrDefault("upload", "0");
                            card.active = item.GetResponseFieldOrDefault("active", null);
                            card.nasPortId = item.GetResponseFieldOrDefault("nas-port-id", "");
                            card.ipRouter = item.GetResponseFieldOrDefault("host-ip", "");
                            card.callingStationId = item.GetResponseFieldOrDefault("calling-station-id", "");
                            card.ipUser = item.GetResponseFieldOrDefault("user-ip", "");
                            card.status = item.GetResponseFieldOrDefault("status", "");
                            session.Add(card);
                        }
                        catch { }
                    }

                    stopwatch.Stop();
                    string ss =
                     (
                       (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                        " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                       " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                     );

                    
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب كروت اليوزمنجر", "");


                    if (session.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_Session_to_LocalDB(session);
                        }
                    }

                    stopwatch.Stop();

                    return session;
                }

            }
            catch
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return session;
        }

        [Obsolete]
        public static List<SourceSessionUserManager_fromMK> Get_UM_Sessions_V7(string name = "", bool is_syn = false)
        {
            List<SourceSessionUserManager_fromMK> session = new List<SourceSessionUserManager_fromMK>();
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/user-manager/session/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                        loadCmd = connection.CreateCommandAndParameters(code, "user", name);


                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceSessionUserManager_fromMK card = new SourceSessionUserManager_fromMK();
                            string id = item.GetResponseFieldOrDefault(".id", "");
                            card.id = id;
                            card.idHX = id;

                            //card.idHX = item.GetResponseFieldOrDefault(".id", "0");
                            card.userName = item.GetResponseFieldOrDefault("user", "");
                            card.fromTime = item.GetResponseFieldOrDefault("started", "");
                            card.tillTime = item.GetResponseFieldOrDefault("ended", "");
                            card.upTime = item.GetResponseFieldOrDefault("uptime", "");
                            card.bytesDownload = item.GetResponseFieldOrDefault("download", "0");
                            card.bytesUpload = item.GetResponseFieldOrDefault("upload", "0");
                            card.active = item.GetResponseFieldOrDefault("active", null);
                            card.nasPortId = item.GetResponseFieldOrDefault("nas-port-id", "");
                            card.ipRouter = item.GetResponseFieldOrDefault("nas-ip-address", "");
                            card.callingStationId = item.GetResponseFieldOrDefault("calling-station-id", "");
                            card.ipUser = item.GetResponseFieldOrDefault("user-address", "");
                            card.status = item.GetResponseFieldOrDefault("status", "");
                            session.Add(card);
                        }
                        catch { }
                    }

                    stopwatch.Stop();
                    string ss =
                      (
                        (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                         " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                        " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                      );
 
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب كروت اليوزمنجر", "");
                    if (session.Count > 0)
                    {
                        if (is_syn)
                        {
                            UserManagerProcess u = new UserManagerProcess();
                            u.Syn_Session_to_LocalDB(session);
                        }
                    }
                    stopwatch.Stop();
                    return session;
                }
            }
            catch
            {
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //    RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                ////else
                ////    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return session;
        }

       
      
    }

}


