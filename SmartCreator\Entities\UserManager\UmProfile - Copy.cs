﻿using iTextSharp.text;
//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;

//using System.ComponentModel;
//using System.ComponentModel.DataAnnotations;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using RequiredAttribute = ServiceStack.DataAnnotations.RequiredAttribute;

namespace SmartCreator.Entities.UserManager
{
    public class UmProfile : BaseProfile
    {
        public string NameForUser { get; set; }
        [Computed, DisplayName("سرعة التنزيل")]
        public string Download_tx { get; set; } = "0";

        [Computed, DisplayName("سرعة الرفع")]
        public string Upload_rx
        {
            get; set;
        } = "0";

        [Computed, Default(0)/*, DisplayName("التحميل")*/]// موجود في التحديد
        public double TransferLimit
        {
            get; set;
        }

        [Computed, Default(0), DisplayName("التحميل")]
        public string Str_transferLimit
        {
            get; set;
        }

        [Computed, Default(0)/*, DisplayName("الوقت")*/]
        public double UptimeLimit
        {
            get; set;
        }
        [Computed, Default(0), DisplayName("الوقت")]
        public string Str_uptimeLimit
        {
            get; set;
        }
        [Computed, Default(0), DisplayName("عدد التحديدات")]
        public int CountLimit { get; set; } = 0;
        [Computed, DisplayName("المجموعه")]
        public string GroupName { get; set; } = "";
        [Computed]
        public string From_time { get; set; } = "00:00:00";
        [Computed]
        public string Till_time { get; set; } = "23:59:59";
        [Computed]
        public string Weekdays { get; set; } = "sunday,monday,tuesday,wednesday,thursday,friday,saturday";



        [Computed, DisplayName("السرعة")]
        public string Speed
        {
            get;
            set;
            //get
            //{
            //    return utils.ConvertSize_Get_En(Limits.Select(x => x.Upload_rx).Last() + " / " + utils.ConvertSize_Get_En(Limits.Select(x => x.Upload_rx).Last()));
            //}
        }

        [Computed, DefaultValue("admin")]
        public string Owner { get; set; } = "admin";

        [Computed, Browsable(false)]
        public string IdHX_limt { get; set; }

        [Computed, Browsable(false)]
        public string Name_limt { get; set; }

        [Computed, Browsable(false)]
        public string IdHX_prfileLimt { get; set; }


        [/*Reference,*/ Computed]
        public List<UmProfile_Limtition> Profile_Limtitions { get; set; } = new List<UmProfile_Limtition>();

        [/*Reference,*/ Computed]
        public List<UmLimitation> Limits { get; set; } = new List<UmLimitation>();


        //[Reference,Computed]
        public List<UmProfile> Get_UMProfile()
        {
            List<UmProfile> finalProfile = new List<UmProfile>();

            try
            {
                Sql_DataAccess Local_DA = new Sql_DataAccess();
                Smart_DataAccess Smart_DA = new Smart_DataAccess();

                List<UmProfile> profile = Global_Variable.Source_profile;
                List<UmProfile_Limtition> limt_profile = Global_Variable.Source_profile_limtition;
                List<UmLimitation> limts = Global_Variable.Source_limtition;
                //Smart_DataAccess sm =new Smart_DataAccess();


                //نجلبهمن من قاعده البيانات المحليه عشان الايدي للتعديل في قاعده البيانات او الاستعلام
                //lock (Smart_DataAccess.Lock_object)
                //{
                //using (var db = Smart_DA.dbFactory.Open())
                //{
                //Global_Variable.Source_profile = db.Select<UmProfile>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                //Global_Variable.Source_limtition = db.Select<UmLimitation>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                //Global_Variable.Source_profile_limtition = db.Select<UmProfile_Limtition>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);


                //Global_Variable.Source_profile = db.Select<UmProfile>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                Global_Variable.Source_profile = Smart_DA.Load<UmProfile>($"select * from UmProfile where DeleteFromServer=0 and ( Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ");

                //Global_Variable.Source_limtition = db.Select<UmLimitation>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                Global_Variable.Source_limtition = Smart_DA.Load<UmLimitation>($"select * from UmLimitation where DeleteFromServer=0 and ( Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ");

                //Global_Variable.Source_profile_limtition = db.Select<UmProfile_Limtition>(x => x.DeleteFromServer == 0 && x.Rb == Global_Variable.Mk_resources.RB_code);
                Global_Variable.Source_profile_limtition = Smart_DA.Load<UmProfile_Limtition>($"select * from UmProfile_Limtition where DeleteFromServer=0 and ( Rb='{Global_Variable.Mk_resources.RB_SN}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ");

                //}
                //}
                List<UmProfile> AllPercent = Global_Variable.Source_profile;
                try
                {
                    foreach (var p in profile)
                    {
                        try
                        {
                            UmProfile fprofile = new UmProfile();
                            fprofile.Id = p.Id;
                            fprofile.IdHX = p.IdHX;
                            fprofile.Name = p.Name;
                            fprofile.Sn_Name = p.Sn_Name;
                            fprofile.Validity = p.Validity;
                            fprofile.NameForUser = p.NameForUser;
                            fprofile.Price = p.Price;
                            fprofile.Price_Disply = p.Price_Disply;
                            fprofile.SharedUsers = p.SharedUsers;
                            fprofile.Is_percentage = (from s in AllPercent
                                                      where (s.Sn_Name == p.Sn_Name && s.Rb == Global_Variable.Mk_resources.RB_SN)
                                                      select s.Is_percentage).FirstOrDefault();
                            //fprofile.Is_percentage = p.Is_percentage;
                            fprofile.PercentageType = (from s in AllPercent
                                                       where (s.Sn_Name == p.Sn_Name && s.Rb == Global_Variable.Mk_resources.RB_SN)
                                                       select s.PercentageType).FirstOrDefault();
                            fprofile.Percentage = (from s in AllPercent
                                                   where (s.Sn_Name == p.Sn_Name && s.Rb == Global_Variable.Mk_resources.RB_SN)
                                                   select s.Percentage).FirstOrDefault();

                            fprofile.DeleteFromServer = p.DeleteFromServer;
                            fprofile.Rb = p.Rb;
                            fprofile.Sn = p.Sn;
                            fprofile.Sn_Name = p.Sn_Name;

                            int count_limit = 0;
                            foreach (var pl in limt_profile)
                                if (p.Name == pl.Profile)
                                {
                                    fprofile.Profile_Limtitions.Add(pl);
                                    fprofile.IdHX_prfileLimt = pl.IdHX;

                                    fprofile.CountLimit = count_limit + 1;
                                    count_limit = count_limit + 1;
                                    foreach (var l in limts)
                                        if (l.Name == pl.Limitation)
                                        {

                                            fprofile.Limits.Add(l);
                                            fprofile.From_time = pl.From_time;
                                            fprofile.Till_time = pl.Till_time;
                                            fprofile.Weekdays = pl.Weekdays;
                                            fprofile.IdHX_limt = l.IdHX;
                                            fprofile.Name_limt = l.Name;

                                            fprofile.UptimeLimit += l.UptimeLimit;
                                            fprofile.Str_uptimeLimit = utils.Get_Seconds_By_clock_Mode(fprofile.UptimeLimit);
                                            //fprofile.uptimeLimit_str = utils.Get_Seconds_By_clock_Mode(l.uptimeLimit);

                                            fprofile.TransferLimit += l.TransferLimit;
                                            if (UIAppearance.Language_ar)
                                                fprofile.Str_transferLimit = utils.ConvertSize_Get_InArabic(fprofile.TransferLimit.ToString());
                                            else
                                                fprofile.Str_transferLimit = utils.ConvertSize_Get_En(fprofile.TransferLimit.ToString());
                                            //fprofile.transferLimit_str = utils.ConvertSize_Get_En(l.transferLimit.ToString());


                                            fprofile.GroupName = l.GroupName;
                                            fprofile.Download_tx = l.Download_tx;
                                            fprofile.Upload_rx = l.Upload_rx;

                                            if ((l.Upload_rx != "0") && (l.Download_tx != "0"))
                                            {
                                                if (UIAppearance.Language_ar)
                                                    fprofile.Speed = utils.ConvertSize_Get_InArabic_short(l.Upload_rx) + "/" + utils.ConvertSize_Get_InArabic_short(l.Download_tx);
                                                else
                                                    fprofile.Speed = utils.ConvertSize_Get_En(l.Upload_rx) + "/" + utils.ConvertSize_Get_En(l.Download_tx);
                                            }
                                        }
                                }
                            finalProfile.Add(fprofile);

                        }
                        catch (Exception ex) { System.Windows.Forms.MessageBox.Show("Get_UMProfile\n in  profile "+ p.ToString()+"\n" + ex.Message); }

                    }
                }
                catch (Exception ex) { System.Windows.Forms.MessageBox.Show("Get_UMProfile\n" + ex.Message); }


                Global_Variable.UM_Profile = finalProfile;
            }
            catch (Exception ex) { System.Windows.Forms.MessageBox.Show("Get_UMProfile\n" + ex.Message); }
            return finalProfile;

        }
    }

    [UniqueConstraint("Sn_Name", "Rb")]
    public class UmLimitation
    {
        [Unique, AutoIncrement, PrimaryKey, Required]
        public int Id { get; set; }
        [Required]
        public string IdHX { get; set; }
        [Required, Browsable(false)]
        public double Sn { get; set; }

        [Required, DisplayName("اسم التحديد")]
        public string Name { get; set; }

        [Required]
        public string Sn_Name { get; set; }

        [DisplayName("التحميل"), Default(0), Browsable(false)]
        public double DownloadLimit { get; set; } = 0;

        [DisplayName("الرفع"), Default(0), Browsable(false)]
        public double UploadLimit { get; set; } = 0;

        [Default(0), Browsable(false)]
        public double TransferLimit { get; set; } = 0;
        [Computed, Default(0), DisplayName("التنزيل")]
        public string Str_transferLimit
        {

            get
            {
                if (UIAppearance.Language_ar)
                    return utils.ConvertSize_Get_InArabic(TransferLimit.ToString());
                else
                    return utils.ConvertSize_Get_En(TransferLimit.ToString());

            }

        }

        [Default(0), Browsable(false)]
        public double UptimeLimit { get; set; } = 0;

        [Default(0), DisplayName("الوقت"), Computed]
        public string Str_uptimeLimit
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(UptimeLimit);
            }
        }

        [Browsable(false)]
        public string RateLimit { get; set; } = "";

        [DefaultValue("0"), Browsable(false)]
        public string Download_tx { get; set; } = "0";

        [DefaultValue("0"), Browsable(false)]
        public string Upload_rx { get; set; } = "0";

        [Computed, DisplayName("السرعة")]
        public string Speed
        {
            //get;
            //set { }
            get
            {
                if (Upload_rx == "0" || Download_tx == "0")
                    return "";

                if (UIAppearance.Language_ar)
                    return utils.ConvertSize_Get_InArabic_short(Upload_rx) + "/" + utils.ConvertSize_Get_InArabic_short(Download_tx);
                else
                    return utils.ConvertSize_Get_En(Upload_rx) + "/" + utils.ConvertSize_Get_En(Download_tx);


                //return (utils.ConvertSize_Get_En(Upload_rx) + " / " + utils.ConvertSize_Get_En(Download_tx));
            }
        }

        [DisplayName("المجموعه")]
        public string GroupName { get; set; } = "";
        [Default(0), Browsable(false)]
        public double DownloadPrice { get; set; } = 0;
        [Default(0), Browsable(false)]

        public double UploadPrice { get; set; } = 0;
        [Default(0), Browsable(false)]

        public double UptimePrice { get; set; } = 0;
        [Required, Browsable(false)]
        public string Rb { get; set; }
        [Browsable(false)]
        public int DeleteFromServer { get; set; } = 0;

      

    }

    //[UniqueConstraint("Profile", "Limitation", "Rb")]
    [UniqueConstraint("Sn_Profile_Limitation", "Rb")]
    public class UmProfile_Limtition
    {
        [Required, Unique,AutoIncrement,PrimaryKey]
        public int Id { get; set; }
        [Required]
        public string IdHX { get; set; }
        [Required]
        public double Sn { get; set; }
        [Required]
        public string Sn_Profile_Limitation { get; set; }
        //[Required]
        public string Profile { get; set; }
        public string Limitation { get; set; }
        [DefaultValue("00:00:00")]
        public string From_time { get; set; }
        [DefaultValue("23:59:59")]

        public string Till_time { get; set; }
        [DefaultValue("sunday,monday,tuesday,wednesday,thursday,friday,saturday")]
        public string Weekdays { get; set; }
        public string Rb { get; set; }
        [Default(0)]
        public int DeleteFromServer { get; set; } = 0;

    }

}
