﻿using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Forms.Hotspot
{
    public partial class FormProfileHotspotLocal : RJChildForm
    {
        Sql_DataAccess Local_DB;
        public FormProfileHotspotLocal()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            Local_DB = new Sql_DataAccess();
            //Init_dgv();

            this.Text = "باقات الهوتسبوت";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Hotspot Profiles";
            }



            System.Drawing.Font menu_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Bold);
            btnAddNew.Font = btnDelete.Font = btnEdit.Font = btnRefresh.Font = menu_font;
            btn_importUsermanager.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 35;

            //Control_Loop(pnlClientArea);
            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);


        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void loadData()
        {
            try
            {
                HSLocalProfile Photspot = new HSLocalProfile();

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                lock (Smart_DataAccess.Lock_object)
                {

                    var profiles = smart_DataAccess.Load<HSLocalProfile>($"select * from HSLocalProfile where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                    dgv.DataSource = profiles;

                }
                foreach (DataGridViewRow r in dgv.Rows)
                {
                    string uptime = r.Cells["UptimeLimit"].Value.ToString();
                    string validay = r.Cells["Validity"].Value.ToString();
                    string download = r.Cells["TransferLimit"].Value.ToString();
                    string name = r.Cells["Name"].Value.ToString();

                    if ((uptime == "0" || uptime == "" || uptime == "مفتوح") && (download == "0" || download == "" || download == "مفتوح") && (validay == "0" || validay == "" || validay == "مفتوح") && (download == "0" || download == "" || download == "مفتوح")  /*download == "مفتوح"*/)
                    {
                        r.DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                        //r.DefaultCellStyle.ForeColor = Color.Red;
                    }
                    try
                    {
                        //Local_DB.Get_int_FromDB($"select count(Sn) form UmUser where ProfileName='{name}' and DeleteFromServer=0");
                        r.Cells["CountCards"].Value = Local_DB.Get_int_FromDB($"select count(Sn) from HSUser where ProfileName='{name}' and DeleteFromServer=0").ToString();
                    }
                    catch { }

                }
                Init_dgv();
                //foreach (DataGridViewRow r in dgv.Rows)
                //{
                //    string uptime = r.Cells["UptimeLimit"].Value.ToString();
                //    string validay = r.Cells["Validity"].Value.ToString();
                //    string download = r.Cells["TransferLimit"].Value.ToString();

                //    //r.Cells["Validity"].Value = utils.get_number_Days_form_validity_profile(r.Cells["Validity"].Value.ToString());
                //    if ((uptime == "0" || uptime == "" || uptime == "مفتوح") && (download == "0" || download == "" || download == "مفتوح") && (validay == "0" || validay == "" || validay == "مفتوح") && (download == "0" || download == "" || download == "مفتوح")  /*download == "مفتوح"*/)
                //    {
                //        r.DefaultCellStyle.ForeColor = Color.Red;
                //    }

                //}
            }
            catch { }
        }

        private void Init_dgv()
        {
            try
            {
                dgv.Columns["UptimeLimit"].Visible = false;
                dgv.Columns["TransferLimit"].Visible = false;
                //dgv.Columns["Validity"].Visible = false;
                //dgv.Columns["Add_Smart_Scripts"].Visible = false;
                //dgv.Columns["Save_time"].Visible = false;
                //dgv.Columns["Save_download"].Visible = false;
                //dgv.Columns["Save_session"].Visible = false;
                //dgv.Columns["ByDayOrHour"].Visible = false;
                //dgv.Columns["ByDayOrHour"].Visible = false;

                dgv.Columns["Id"].Visible = false;
                //dgv.Columns["Rb"].Visible = false;
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
                System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

                dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
                dgv.ColumnHeadersHeight = 50;
            }
            catch { }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            loadData();
        }

        private void FormProfileHotspotLocal_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            loadData();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            try
            {
                string id = dgv.CurrentRow.Cells["Id"].Value.ToString();
                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                HSLocalProfile profile = new HSLocalProfile();
                lock (Smart_DataAccess.Lock_object)
                {
                    //using (var db = smart_DataAccess.dbFactory.Open())
                    //{
                    //  profile=  db.Single<HSLocalProfile>(x => x.Id == Convert.ToInt32( id));
                    //}
                    profile = smart_DataAccess.LoadSingleById<HSLocalProfile>(id, "HSLocalProfile");
                }

                var frm = new FormAdd_Edit_Profile_Hotspot(profile);
                frm.add = false;
                frm.ShowDialog();
                if (frm.succes)
                {
                    //refersh_From_MK();
                    loadData();
                }
            }
            catch (Exception ex) {/* RJMessageBox.Show(ex.ToString());*/ loadData(); }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            try
            {
                double CountCards = 0;
                try
                {
                    CountCards = Convert.ToDouble(dgv.CurrentRow.Cells["CountCards"].Value.ToString());
                }
                catch { }
                //return;

                DialogResult = RJMessageBox.Show("هل تريد حذف الباقة", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (DialogResult == DialogResult.No)
                    return;

                if (CountCards > 0)
                {
                    DialogResult = RJMessageBox.Show($"هذه الباقه لديها {CountCards} كرت مرتبط ", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Question);
                    DialogResult = RJMessageBox.Show("هل تريد حذف الباقة", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (DialogResult == DialogResult.No)
                        return;
                }



                if (DialogResult == DialogResult.No)
                    return;

                string id = dgv.CurrentRow.Cells["Id"].Value.ToString();
                //Hotspot_Profile_hotspot profile = Global_Variable.UM_Profile.Find(x => x.IdHX == IdHX);

                //if (SqlDataAccess.delete_Profile_Hotspot(id))
                //    RJMessageBox.Show("تم");
                //else
                //    RJMessageBox.Show("خطاء");

                //refersh_From_MK();



                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                HSLocalProfile profile;
                lock (Smart_DataAccess.Lock_object)
                {
                    if (smart_DataAccess.DeleteById<HSLocalProfile>(id, "HSLocalProfile"))
                    {
                        RJMessageBox.Show("تم حذف الباقة");
                        loadData();

                    }
                    //using (var db = smart_DataAccess.dbFactory.Open())
                    //{
                    //    //var profile1 = db.SelectByIds<HSLocalProfile>();
                    //    var ff = db.Delete<HSLocalProfile>(x => x.Id == Convert.ToInt32(id));
                    //}
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            var frmProfile = new FormAdd_Edit_Profile_Hotspot();
            frmProfile.ShowDialog();

            if (frmProfile.succes)
            {
                //refersh_From_MK();
                loadData();
            }
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            btnEdit_Click(sender, e);
        }

        private void btn_importUsermanager_Click(object sender, EventArgs e)
        {
            Smart_DataAccess Smart_DA = new Smart_DataAccess();
            foreach (var profile in Global_Variable.UM_Profile)
            {
                try
                {
                    HSLocalProfile Localprf = new HSLocalProfile();

                    Localprf.Name = profile.Name;
                    Localprf.Price = profile.Price;
                    Localprf.Price_Display = profile.Price_Disply;
                    Localprf.TransferLimit = profile.TransferLimit;
                    Localprf.UptimeLimit = profile.UptimeLimit;
                    Localprf.Validity = profile.Validity;
                    Localprf.Is_percentage = profile.Is_percentage;
                    Localprf.Percentage = profile.Percentage;
                    Localprf.PercentageType = profile.PercentageType;
                    Localprf.Rb = Global_Variable.Mk_resources.RB_SN;

                    //Localprf.Link_hotspot_profile =profile
                    //Localprf.Add_Smart_Scripts = Convert.ToInt16(CheckBox_SmartScript.Checked);
                    //prf.Validity_str = txt_Validity.Text + "d";
                    //Localprf.Save_time = Convert.ToInt16(CheckBox_Save_time.Check);
                    //Localprf.Save_download = Convert.ToInt16(CheckBox_Save_download.Check);
                    //Localprf.Save_session = Convert.ToInt16(CheckBox_Save_session.Check);
                    //Localprf.ByDayOrHour = Convert.ToInt16(CheckBox_byDayOrHour.Check);
                    //if (rjCheckBox_group.Checked)

                    lock (Smart_DataAccess.Lock_object)
                    {
                        var check = Smart_DA.Load<HSLocalProfile>($"select * from HSLocalProfile where Rb='{Global_Variable.Mk_resources.RB_SN}' and Name='{profile.Name}' ; ");
                        if (check.Count > 0)
                        {
                            //RJMessageBox.Show("الاسم موجود من قبل");
                            continue;
                        }
                        List<string> Fields = new List<string>();
                        string[] aFields = {
                                        "Validity", "UptimeLimit", "TransferLimit", "Price",
                                        "Price_Display", "Link_hotspot_profile",
                                         "Add_Smart_Scripts", "Save_session", "Save_download",
                                        "ByDayOrHour", "Save_time", "Is_percentage",
                                        "Percentage", "PercentageType","Rb","Name"
                                    }; Fields.AddRange(aFields);

                        
                        //lock (Smart_DataAccess.Lock_object)
                        //{
                            int count = Smart_DA.InsertTable<HSLocalProfile>(Fields, Localprf, "HSLocalProfile");
                        //}

                    }
                }
                catch { }
            }

            loadData();
        }
    }
}
