using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// كلاس سريع لاختبار RJTabControl الجديد
    /// </summary>
    public static class TestRJTabControl
    {
        /// <summary>
        /// اختبار سريع للـ TabControl
        /// </summary>
        public static void QuickTest()
        {
            // إنشاء نموذج
            var form = new Form
            {
                Text = "RJTabControl - اختبار سريع",
                Size = new Size(800, 600),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(45, 45, 48)
            };

            // إنشاء TabControl
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                TabSpacing = 3,
                TabPadding = 20
            };

            // إضافة تابات
            var homeTab = tabControl.AddTab("الرئيسية", IconChar.Home);
            homeTab.AddControl(new Label 
            { 
                Text = "مرحباً! هذا تاب يرث من RJButton 🎉", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.White
            });

            var settingsTab = tabControl.AddTab("الإعدادات", IconChar.Cog);
            settingsTab.AddControl(new Label 
            { 
                Text = "صفحة الإعدادات ⚙️", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.White
            });

            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            infoTab.AddControl(new Label 
            { 
                Text = "• كل تاب هو RJButton\n• لا تكرار في الكود\n• استخدام مباشر لـ RJControls", 
                Dock = DockStyle.Fill, 
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White
            });

            // تطبيق نمط أزرق
            tabControl.TabStyle = TabStyle.Blue;

            // إضافة للنموذج
            form.Controls.Add(tabControl);

            // عرض النموذج
            form.ShowDialog();
        }

        /// <summary>
        /// اختبار الأنماط المختلفة
        /// </summary>
        public static void TestStyles()
        {
            var form = new Form
            {
                Text = "RJTabControl - اختبار الأنماط",
                Size = new Size(900, 700),
                StartPosition = FormStartPosition.CenterScreen
            };

            var panel = new Panel { Dock = DockStyle.Fill };

            // نمط أزرق
            var blueTab = new RJTabControl
            {
                Location = new Point(10, 10),
                Size = new Size(400, 300),
                TabStyle = TabStyle.Blue
            };
            blueTab.AddTab("أزرق", IconChar.Home);
            blueTab.AddTab("تاب 2", IconChar.File);

            // نمط أخضر
            var greenTab = new RJTabControl
            {
                Location = new Point(450, 10),
                Size = new Size(400, 300),
                TabStyle = TabStyle.Green
            };
            greenTab.AddTab("أخضر", IconChar.Leaf);
            greenTab.AddTab("تاب 2", IconChar.Tree);

            // نمط داكن
            var darkTab = new RJTabControl
            {
                Location = new Point(10, 350),
                Size = new Size(400, 300),
                TabStyle = TabStyle.Dark
            };
            darkTab.AddTab("داكن", IconChar.Moon);
            darkTab.AddTab("تاب 2", IconChar.Star);

            // نمط Chrome
            var chromeTab = new RJTabControl
            {
                Location = new Point(450, 350),
                Size = new Size(400, 300),
                TabStyle = new TabStyle { StyleType = TabStyleType.Chrome }
            };
            chromeTab.AddTab("Chrome", IconChar.Chrome);
            chromeTab.AddTab("تاب 2", IconChar.Globe);

            panel.Controls.Add(blueTab);
            panel.Controls.Add(greenTab);
            panel.Controls.Add(darkTab);
            panel.Controls.Add(chromeTab);

            form.Controls.Add(panel);
            form.ShowDialog();
        }

        /// <summary>
        /// اختبار شامل
        /// </summary>
        public static void FullTest()
        {
            SimpleTabTestForm.RunTest();
        }
    }
}
