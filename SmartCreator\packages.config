﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="AltoHttp" version="1.5.2" targetFramework="net48" />
  <package id="BouncyCastle.Cryptography" version="2.5.0" targetFramework="net48" />
  <package id="CefSharp.Common" version="136.1.40" targetFramework="net48" />
  <package id="CefSharp.WinForms" version="136.1.40" targetFramework="net48" />
  <package id="chromiumembeddedframework.runtime.win-x64" version="136.1.4" targetFramework="net48" />
  <package id="chromiumembeddedframework.runtime.win-x86" version="136.1.4" targetFramework="net48" />
  <package id="CircularProgressBar" version="2.8.0.16" targetFramework="net45" />
  <package id="Dapper" version="2.1.35" targetFramework="net462" />
  <package id="FontAwesome.Sharp" version="6.6.0" targetFramework="net48" />
  <package id="HtmlAgilityPack" version="1.11.72" targetFramework="net48" />
  <package id="iTextSharp" version="5.5.13.4" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.6" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net45" />
  <package id="Obfuscar" version="2.2.46" targetFramework="net48" developmentDependency="true" />
  <package id="SSH.NET" version="2024.2.0" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net48" />
  <package id="System.Formats.Asn1" version="9.0.1" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.6" targetFramework="net48" />
  <package id="System.Memory" version="4.6.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.6" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.6" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
  <package id="WinFormAnimation" version="*******" targetFramework="net45" />
</packages>