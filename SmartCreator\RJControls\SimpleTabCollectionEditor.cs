using System;
using System.ComponentModel;
using System.ComponentModel.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// محرر مجموعة مبسط للاختبار
    /// </summary>
    public class SimpleTabCollectionEditor : CollectionEditor
    {
        public SimpleTabCollectionEditor(Type type) : base(type)
        {
        }

        /// <summary>
        /// نوع العناصر في المجموعة
        /// </summary>
        protected override Type CreateCollectionItemType()
        {
            return typeof(RJTabPage);
        }

        /// <summary>
        /// أنواع العناصر التي يمكن إنشاؤها
        /// </summary>
        protected override Type[] CreateNewItemTypes()
        {
            return new Type[] { typeof(RJTabPage) };
        }

        /// <summary>
        /// إنشاء عنصر جديد
        /// </summary>
        protected override object CreateInstance(Type itemType)
        {
            if (itemType == typeof(RJTabPage))
            {
                // إنشاء تاب بسيط
                var tab = new RJTabPage($"TabPage{GetItemCount() + 1}");
                tab.BackColor = System.Drawing.Color.FromArgb(70, 70, 70);
                tab.ForeColor = System.Drawing.Color.White;
                return tab;
            }
            return base.CreateInstance(itemType);
        }

        /// <summary>
        /// الحصول على عدد العناصر الحالي
        /// </summary>
        private int GetItemCount()
        {
            try
            {
                if (Context?.Instance is SafeRJTabControl tabControl)
                {
                    return tabControl.Tabs?.Count ?? 0;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// عرض اسم العنصر في المحرر
        /// </summary>
        protected override string GetDisplayText(object value)
        {
            if (value is RJTabPage tab)
            {
                return !string.IsNullOrEmpty(tab.Text) ? tab.Text : $"TabPage{tab.GetHashCode()}";
            }
            return base.GetDisplayText(value);
        }

        /// <summary>
        /// تحديد ما إذا كان يمكن إزالة العناصر
        /// </summary>
        protected override bool CanRemoveInstance(object value)
        {
            return true; // يمكن إزالة أي تاب
        }

        /// <summary>
        /// تحديد ما إذا كان يمكن تحديد عناصر متعددة
        /// </summary>
        protected override bool CanSelectMultipleInstances()
        {
            return false; // تحديد تاب واحد فقط
        }
    }
}
