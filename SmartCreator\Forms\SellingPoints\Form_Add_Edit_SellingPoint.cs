﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.SellingPoints
{
    public partial class Form_Add_Edit_SellingPoint : RJForms.RJChildForm
    {
        SellingPoint SellingPoint;
        public bool succes = false;
        public bool add = true;

        public Form_Add_Edit_SellingPoint()
        {
            InitializeComponent();
            this.Text = "Add Selling Points";
            btnSave.Text = "Add";
            txt_Alert_Soon.Text = txt_Alert_Finsh.Text=txt_Percentage.Text= "0";
            CBOX_PercentageType.SelectedIndex = 0;
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "اضافة نقطة بيع جديده ";
                this.Text = "اضافة نقطه بيع";
                btnSave.Text = "اضافة";
                
            }
            txt_Percentage.Text = "0";
            Set_Font();

            txt_code.Focus();


        }
        private void Set_Font()
        {

           
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 11, FontStyle.Bold);
            //System.Drawing.Font title_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, false);
            btnSave.Font = title_font;
            //rjButton1.Font = title_font;
            lblTitle.Font = title_font;


            //System.Drawing.Font DGV_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, false);
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
            btnSave.Font = btn_cancel.Font= btnEdit_Commi.Font=btnEdit_Alert.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel10.Font= rjLabel5.Font = btn_cancel.Font = rjLabel1.Font = rjLabel2.Font = rjLabel3.Font = rjLabel4.Font = rjLabel5.Font = rjLabel6.Font = rjLabel7.Font=
            rjLabel8.Font = rjLabel9.Font =
            txt_code.Font = txt_address.Font = txt_name.Font = txt_Percentage.Font = txt_phone.Font = txt_Suffixes.Font = txt_Prefixes.Font = CBOX_PercentageType.Font =
            lbl_font;
            this.Focus();

            utils utils = new utils();
            utils.Control_textSize1(this);


            return;


            Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size    , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }
        public Form_Add_Edit_SellingPoint(SellingPoint sp)
        {
            InitializeComponent();

            SellingPoint = sp;
            this.Text = "Add Selling Points";
            btnSave.Text = "Add";

            CBOX_PercentageType.SelectedIndex = 0;
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "اضافة نقطة بيع جديده ";
                this.Text = "اضافة نقطه بيع";
                btnSave.Text = "اضافة";
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12 * utils.ScaleFactor, FontStyle.Regular);
                //btnSave.Font = title_font;
                ////rjButton1.Font = title_font;
                //lblTitle.Font = title_font;


                //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 10 * utils.ScaleFactor , FontStyle.Regular);
                //btnSave.Font = DGV_font;
                //CBOX_PercentageType.Font = DGV_font;
            }

            txt_code.Text = SellingPoint.Code;
            txt_code.Enabled = false;
            txt_name.Enabled = false;
            txt_name.Text = SellingPoint.UserName;

            //txt_Prefixes.Text = !string.IsNullOrEmpty(txt_Prefixes.Text) ? rjDataGridView1.CurrentRow.Cells["Prefixes"].Value.ToString() : "";
            txt_Prefixes.Text = SellingPoint.Prefixes;

            txt_Suffixes.Text = SellingPoint.Suffixes;
            txt_address.Text = SellingPoint.Address;
            txt_phone.Text = SellingPoint.Phone;
            txt_Percentage.Text = SellingPoint.Percentage + "";
            CBOX_PercentageType.SelectedIndex = Convert.ToInt32(SellingPoint.PercentageType);
            CheckBox_Is_percentage.Checked = Convert.ToBoolean(SellingPoint.Is_percentage);
            Toggle_Custom_Commi.Checked = Convert.ToBoolean(SellingPoint.Is_percentage_Custom);
            if(Toggle_Custom_Commi.Checked)
            {
                panel1.Visible = false;
                btnEdit_Commi.Visible = true;
            }

            CheckBox_Is_Alert.Checked = Convert.ToBoolean(SellingPoint.Is_Alert);
            Toggle_Custom_Alert.Checked = Convert.ToBoolean(SellingPoint.Is_Alert_Custom);
            if (Toggle_Custom_Alert.Checked)
            {
                panel2.Visible = false;
                btnEdit_Alert.Visible = true;
            }
            txt_Alert_Soon.Text = SellingPoint.Count_Soon.ToString();
            txt_Alert_Finsh.Text=SellingPoint.Count_Finsh.ToString();
            
            CheckBox_UseAccounting.Checked = Convert.ToBoolean(SellingPoint.UseAccounting);
            btnSave.Text = "تعديل";

            Set_Font();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            if (add)
            {

                long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM SellingPoint where Code='{txt_code.Text}'  and  ( Rb='{Global_Variable.Mk_resources.RB_code}'  or Rb='{Global_Variable.Mk_resources.RB_SN}' ) ;");
                if (txt_name.Text == "")
                {
                    RJMessageBox.Show("اسم نقطة البيع مطلوب");
                    return;
                }
                if (foundsp > 0)
                {
                    RJMessageBox.Show("رقم نقطه البيع موجوده مسبقا");
                    return;
                }
                if (!float.TryParse(txt_Percentage.Text, out float value))
                {
                    RJMessageBox.Show("النسبه يجب ان تكون رقم صحيح او رقم عشري");
                    return;
                }
                if (!float.TryParse(txt_Alert_Soon.Text, out float value2))
                {
                    RJMessageBox.Show("رقم نسبة التنبية يجب ان تكون رقم صحيح او رقم عشري");
                    return;
                }

                SellingPoint sp = new SellingPoint();
                sp.Code = txt_code.Text;
                sp.UserName = txt_name.Text;
                sp.Address = txt_address.Text;
                sp.Suffixes = txt_Suffixes.Text;
                sp.Prefixes = txt_Prefixes.Text;
                sp.Is_percentage = Convert.ToInt16(CheckBox_Is_percentage.Checked);
                sp.Percentage = Convert.ToInt16(txt_Percentage.Text);
                sp.PercentageType = CBOX_PercentageType.SelectedIndex;
                sp.Is_percentage_Custom = Convert.ToInt16(Toggle_Custom_Commi.Checked);

                sp.UseAccounting = Convert.ToInt16(CheckBox_UseAccounting.Checked);
                sp.Rb = Global_Variable.Mk_resources.RB_SN;
                sp.Rb_Sn = Global_Variable.Mk_resources.RB_SN;
                sp.Phone = txt_phone.Text;

                sp.Is_Alert = Convert.ToInt16(CheckBox_Is_Alert.Checked);
                sp.Is_Alert_Custom = Convert.ToInt16(Toggle_Custom_Alert.Checked);
                sp.Count_Soon = Convert.ToInt16(txt_Alert_Soon.Text);
                sp.Count_Finsh = Convert.ToInt16(txt_Alert_Finsh.Text);



                lock (Smart_DataAccess.Lock_object)
                {
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Code", "UserName", "Address", "Suffixes", "Prefixes", "Percentage", "Is_percentage", "PercentageType", "UseAccounting", "Is_percentage_Custom", "Rb", "Rb_Sn", "Phone"
                                         ,"Is_Alert"
                                         ,"Is_Alert_Custom"
                                         ,"Count_Soon"
                                         ,"Count_Finsh"
                    };
                    Fields.AddRange(aFields);


                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "SellingPoint");
                    if (new_sp > 0)
                    {
                        //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                        RJMessageBox.Show("تمت عمليه الاضافة");
                        succes = true;
                        this.Close();
                        return;
                    }
                    //var new_sp = db.Insert<SellingPoint>(sp);
                    //    if (new_sp > 0)
                    //    {
                    //        RJMessageBox.Show("تمت عمليه الاضافة");
                    //        succes = true;
                    //        this.Close();
                    //        return;
                    //    }
                    RJMessageBox.Show("خطاء");


                }

                //}
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = {  "Suffixes", "Prefixes", "Address", "Phone"
                        , "Percentage", "Is_percentage", "PercentageType","Is_percentage_Custom"
                        , "Is_Alert", "Is_Alert_Custom", "Count_Soon","Count_Finsh"

                        , "UseAccounting" };
                Fields.AddRange(aFields);
                lock (Smart_DataAccess.Lock_object)
                {
                    var data = new SellingPoint
                    {
                        Id = SellingPoint.Id,
                        UserName = txt_name.Text,
                        Suffixes = txt_Suffixes.Text,
                        Prefixes = txt_Prefixes.Text,
                        Address = txt_address.Text,
                        Phone = txt_phone.Text,
                        Percentage = (float)Convert.ToDouble(txt_Percentage.Text),
                        Is_percentage = Convert.ToInt32(CheckBox_Is_percentage.Checked),
                        PercentageType = CBOX_PercentageType.SelectedIndex,
                        Is_percentage_Custom = Convert.ToInt16(Toggle_Custom_Commi.Checked),
                        UseAccounting = Convert.ToInt32(CheckBox_UseAccounting.Checked),
                        Is_Alert = Convert.ToInt16(CheckBox_Is_Alert.Checked),
                        Is_Alert_Custom = Convert.ToInt16(Toggle_Custom_Alert.Checked),
                        Count_Soon = Convert.ToInt16(txt_Alert_Soon.Text),
                        Count_Finsh = Convert.ToInt16(txt_Alert_Finsh.Text),


                    };
                    string sqlquery = UtilsSql.GetUpdateSql<SellingPoint>("SellingPoint",Fields, $" where Id=@Id and  ( Rb='{Global_Variable.Mk_resources.RB_code}'  or Rb='{Global_Variable.Mk_resources.RB_SN}' )");
                    int r = sql_DataAccess.UpateTable(data, sqlquery);
                }

                //    lock (Smart_DataAccess.Lock_object)
                //        using (var db = smart_DataAccess.dbFactory.Open())
                //        {
                //            db.UpdateOnly(() => new SellingPoint
                //            {
                //                UserName=txt_name.Text,
                //                Suffixes=txt_Suffixes.Text,
                //                Prefixes=txt_Prefixes.Text,
                //                Address=txt_address.Text,
                //                Phone=txt_phone.Text,
                //                Percentage= (float)Convert.ToDouble(txt_Percentage.Text),
                //                Is_percentage = Convert.ToInt32(CheckBox_Is_percentage.Checked),
                //                PercentageType = CBOX_PercentageType.SelectedIndex,
                //                UseAccounting = Convert.ToInt32(CheckBox_UseAccounting.Checked),

                //            }, where: x => x.Id == SellingPoint.Id && x.Rb == Global_Variable.Mk_resources.RB_code);


                //        }

            }
            succes = true;
            this.Close();
        }

        private void rjButton13_Click(object sender, EventArgs e)
        {
            this.Close();
        }

        private void pnlClientArea_Paint(object sender, PaintEventArgs e)
        {

        }
        bool FirstLoad=true;
        private void Toggle_Custom_Commi_CheckedChanged(object sender, EventArgs e)
        {
            if(FirstLoad)
                return; 

            if(Toggle_Custom_Commi.Checked)
            {
                panel1.Visible = false;
                btnEdit_Commi.Visible=true;
            }
            else
            {
                panel1.Visible = true;
                btnEdit_Commi.Visible = false;
            }
        }

        private void Toggle_Custom_Alert_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            if (Toggle_Custom_Alert.Checked)
            {
                panel2.Visible = false;
                btnEdit_Alert.Visible = true;
            }
            else
            {
                panel2.Visible = true;
                btnEdit_Alert.Visible = false;
            }
        }

        private void Form_Add_Edit_SellingPoint_Load(object sender, EventArgs e)
        {
            FirstLoad = false;
        }

        private void btnEdit_Commi_Click(object sender, EventArgs e)
        {
            if (SellingPoint == null)
            {
                RJMessageBox.Show("قم باضافة نقطه البيع اولا ثم تخصيص العموله للباقات");
                return;
            }

            Form_SelleingPoint_Commissions frm = new Form_SelleingPoint_Commissions(SellingPoint);
            frm.ShowDialog();
        }

        private void btnEdit_Alert_Click(object sender, EventArgs e)
        {
            if (SellingPoint == null)
            {
                RJMessageBox.Show("قم باضافة نقطه البيع اولا ثم تخصيص الاشعارات للباقات");
                return;
            }

            Form_SellingPoint_Alert frm = new Form_SellingPoint_Alert(SellingPoint);
            frm.ShowDialog();
        }
    }
}
