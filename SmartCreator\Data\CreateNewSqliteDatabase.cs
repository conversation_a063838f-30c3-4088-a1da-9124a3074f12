﻿//using ServiceStack;
//using ServiceStack.OrmLite;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using System;
using System.Data;
using System.Data.SQLite;
using System.Linq;
using System.IO;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Utils;
using System.Collections.Generic;
using System.Collections;
using Dapper;
using SmartCreator.Entities;
using SmartCreator.ViewModels;
using System.Windows.Forms;
using SmartCreator.Entities.Accounts;

namespace SmartCreator.Data
{
    public class CreateNewSqliteDatabase
    {
        public CreateNewSqliteDatabase() { }

        public CreateNewSqliteDatabase(string name, string path, bool WithSchema=true) {

            SQLiteConnection sqlite_conn;
            sqlite_conn = CreateConnection(name, path);

            if (WithSchema)
            {
                CreateTable(sqlite_conn);
                InsertData(sqlite_conn);
            }
            //ReadData(sqlite_conn);
        }

        static SQLiteConnection CreateConnection(string name, string path)
        {
            string fn = @"db\"+name;
            SQLiteConnection sqlite_conn;
            // Create a new database connection:
            sqlite_conn = new SQLiteConnection("Data Source=" + fn + "; Version = 3; New = True; Compress = True; ");
            //sqlite_conn = new SQLiteConnection("Data Source=database.db; Version = 3; New = True; Compress = True; ");
            // Open the connection:
            //try
            //{
            //    //sqlite_conn.SetPassword("******");
            //    //sqlite_conn = new SQLiteConnection("Data Source=" + fn + "; Version = 3; New = True; Compress = True; Password = ******; ");

            //}
            //catch (Exception ex) { RJMessageBox.Show(ex.Message); }

            try
            {
                sqlite_conn.Open();
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message);
            }
            
            return sqlite_conn;
        }

        static void CreateTable(SQLiteConnection conn)
        {

            SQLiteCommand sqlite_cmd;
            
            //string routers = "CREATE TABLE \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT,\r\n\t\"mk_sn\"\tTEXT,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);";
            //string user = "CREATE TABLE \"user\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"firstName\"\tTEXT,\r\n\t\"lastName\"\tTEXT,\r\n\t\"descr\"\tTEXT,\r\n\t\"phone\"\tTEXT,\r\n\t\"location\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"callerId\"\tTEXT,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"downloadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uploadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeUsed\"\tINTEGER DEFAULT 0,\r\n\t\"downloadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"uploadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"activeSessions\"\tINTEGER DEFAULT 0,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\t\"sharedUsers\"\tTEXT DEFAULT 1,\r\n\t\"moneyPaid\"\tINTEGER DEFAULT 0,\r\n\t\"moneyUsed\"\tINTEGER DEFAULT 0,\r\n\t\"moneyPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"moneyTotal\"\tNUMERIC DEFAULT 0,\r\n\t\"discount\"\tNUMERIC DEFAULT 0,\r\n\t\"actualLimDownload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUpload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileId\"\tINTEGER,\r\n\t\"actualProfileStart\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileEnd\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);";
            //string userprofile = "CREATE TABLE \"userprofile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"userId\"\tINTEGER,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"profileId\"\tINTEGER,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"validUntil\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"activated\"\tINTEGER DEFAULT 0,\r\n\t\"state\"\tINTEGER,\r\n\t\"endTime\"\tINTEGER DEFAULT 0,\r\n\t\"paused\"\tINTEGER,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);";
            //string session = "CREATE TABLE \"session\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER,\r\n\t\"custId\"\tINTEGER,\r\n\t\"userId\"\tINTEGER,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\tUNIQUE(\"acctSessionId\",\"ipRouter\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);";
            //sqlite_cmd = conn.CreateCommand();
            //sqlite_cmd.CommandText = routers;
            //sqlite_cmd.ExecuteNonQuery();
            //sqlite_cmd.CommandText = user;
            //sqlite_cmd.ExecuteNonQuery();
            
            //sqlite_cmd.CommandText = userprofile;
            //sqlite_cmd.ExecuteNonQuery();
            //sqlite_cmd.CommandText = session;
            //sqlite_cmd.ExecuteNonQuery();



            //string Create_Database = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"BatchCards\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"batchNumber\"\tINTEGER NOT NULL,\r\n\t\"date\"\tINTEGER NOT NULL,\r\n\t\"count\"\tINTEGER,\r\n\t\"sn_from\"\tINTEGER NOT NULL,\r\n\t\"sn_to\"\tINTEGER NOT NULL,\r\n\t\"server\"\tTEXT NOT NULL DEFAULT 'usermanager',\r\n\t\"rb\"\tTEXT,\r\n\t\"actualProfileName\"\tTEXT NOT NULL,\r\n\t\"spId\"\tINTEGER,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT,\r\n\t\"mk_sn\"\tTEXT,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"session\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER,\r\n\t\"custId\"\tINTEGER,\r\n\t\"userId\"\tINTEGER,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"user\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"firstName\"\tTEXT,\r\n\t\"lastName\"\tTEXT,\r\n\t\"descr\"\tTEXT,\r\n\t\"phone\"\tTEXT,\r\n\t\"location\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"callerId\"\tTEXT,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"downloadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uploadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeUsed\"\tINTEGER DEFAULT 0,\r\n\t\"downloadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"uploadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"activeSessions\"\tINTEGER DEFAULT 0,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\t\"sharedUsers\"\tTEXT DEFAULT 1,\r\n\t\"moneyPaid\"\tINTEGER DEFAULT 0,\r\n\t\"moneyUsed\"\tINTEGER DEFAULT 0,\r\n\t\"moneyPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"moneyTotal\"\tNUMERIC DEFAULT 0,\r\n\t\"discount\"\tNUMERIC DEFAULT 0,\r\n\t\"actualLimDownload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUpload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileId\"\tINTEGER,\r\n\t\"actualProfileStart\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileEnd\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"userId\"\tINTEGER,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"profileId\"\tINTEGER,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"validUntil\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"activated\"\tINTEGER DEFAULT 0,\r\n\t\"state\"\tINTEGER,\r\n\t\"endTime\"\tINTEGER DEFAULT 0,\r\n\t\"paused\"\tINTEGER,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE INDEX \"session_Delet_fromServer\" ON \"session\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"session_acctSessionId\" ON \"session\" (\r\n\t\"acctSessionId\"\r\n);\r\nCREATE INDEX \"session_callingStationId\" ON \"session\" (\r\n\t\"callingStationId\"\r\n);\r\nCREATE INDEX \"session_fk_User_localDB_id\" ON \"session\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"session_fromTime\" ON \"session\" (\r\n\t\"fromTime\"\r\n);\r\nCREATE INDEX \"session_id\" ON \"session\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"session_idHX\" ON \"session\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"session_ipRouter\" ON \"session\" (\r\n\t\"ipRouter\"\r\n);\r\nCREATE INDEX \"session_ipUser\" ON \"session\" (\r\n\t\"ipUser\"\r\n);\r\nCREATE INDEX \"session_nasPortId\" ON \"session\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"session_sn\" ON \"session\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"session_sn_userName\" ON \"session\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"user_Delet_fromServer\" ON \"user\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"user_activeSessions\" ON \"user\" (\r\n\t\"activeSessions\"\r\n);\r\nCREATE INDEX user_actprofend_index ON user (\r\n    actualProfileEnd\r\n);\r\nCREATE INDEX \"user_all_filed_search\" ON \"user\" (\r\n\t\"cusName\",\r\n\t\"actualProfileName\",\r\n\t\"spId\",\r\n\t\"numberPrintedId\",\r\n\t\"firstUse\",\r\n\t\"radius\",\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_cusName\" ON \"user\" (\r\n\t\"cusName\"\r\n);\r\nCREATE INDEX \"user_firstUse\" ON \"user\" (\r\n\t\"firstUse\"\r\n);\r\nCREATE INDEX \"user_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"user_idHX\" ON \"user\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"user_lastSeenAt\" ON \"user\" (\r\n\t\"lastSeenAt\"\r\n);\r\nCREATE INDEX \"user_nasPortId\" ON \"user\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_numberPrintedId\" ON \"user\" (\r\n\t\"numberPrintedId\"\r\n);\r\nCREATE INDEX \"user_radius\" ON \"user\" (\r\n\t\"radius\"\r\n);\r\nCREATE INDEX \"user_regDate\" ON \"user\" (\r\n\t\"regDate\"\r\n);\r\nCREATE INDEX \"user_sn_indx\" ON \"user\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"user_sn_userName\" ON \"user\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"user_spId\" ON \"user\" (\r\n\t\"spId\"\r\n);\r\nCREATE INDEX \"userprofile_Delet_fromServer\" ON \"userprofile\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userprofile_added\" ON \"userprofile\" (\r\n\t\"added\"\r\n);\r\nCREATE INDEX \"userprofile_fk_User_localDB_id\" ON \"userprofile\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"userprofile_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userprofile_idHX\" ON \"userprofile\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"userprofile_profileName\" ON \"userprofile\" (\r\n\t\"profileName\"\r\n);\r\nCREATE INDEX \"userprofile_sn\" ON \"userprofile\" (\r\n\t\"sn\"\r\n);\r\nCOMMIT;\r\n";
            //string Create_Database = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"BatchCards\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"batchNumber\"\tINTEGER NOT NULL,\r\n\t\"date\"\tINTEGER NOT NULL,\r\n\t\"count\"\tINTEGER,\r\n\t\"sn_from\"\tINTEGER NOT NULL,\r\n\t\"sn_to\"\tINTEGER NOT NULL,\r\n\t\"server\"\tTEXT NOT NULL DEFAULT 'usermanager',\r\n\t\"rb\"\tTEXT,\r\n\t\"actualProfileName\"\tTEXT NOT NULL,\r\n\t\"spId\"\tINTEGER,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT,\r\n\t\"mk_sn\"\tTEXT,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"session\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER,\r\n\t\"custId\"\tINTEGER,\r\n\t\"userId\"\tINTEGER,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"sn_userName\",\"fk_User_localDB_id\"),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"sessionHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\tUNIQUE(\"acctSessionId\",\"ipRouter\"),\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"user\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"firstName\"\tTEXT,\r\n\t\"lastName\"\tTEXT,\r\n\t\"descr\"\tTEXT,\r\n\t\"phone\"\tTEXT,\r\n\t\"location\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"callerId\"\tTEXT,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"downloadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uploadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeUsed\"\tINTEGER DEFAULT 0,\r\n\t\"downloadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"uploadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"activeSessions\"\tINTEGER DEFAULT 0,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\t\"sharedUsers\"\tTEXT DEFAULT 1,\r\n\t\"moneyPaid\"\tINTEGER DEFAULT 0,\r\n\t\"moneyUsed\"\tINTEGER DEFAULT 0,\r\n\t\"moneyPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"moneyTotal\"\tNUMERIC DEFAULT 0,\r\n\t\"discount\"\tNUMERIC DEFAULT 0,\r\n\t\"actualLimDownload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUpload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileId\"\tINTEGER,\r\n\t\"actualProfileStart\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileEnd\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"profileHotspot\"\tTEXT,\r\n\t\"limitUptime\"\tINTEGER DEFAULT 0,\r\n\t\"uptime\"\tINTEGER DEFAULT 0,\r\n\t\"bytesOut\"\tINTEGER DEFAULT 0,\r\n\t\"bytesIn\"\tINTEGER DEFAULT 0,\r\n\t\"limitbytestotal\"\tINTEGER DEFAULT 0,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"comment\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"server\"\tTEXT,\r\n\t\"address\"\tTEXT,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"pricePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price\"\tNUMERIC DEFAULT 0,\r\n\t\"priceDisplay\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"descr\"\tTEXT,\r\n\t\"smartValidatiy_Add\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_byDayHour\"\tINTEGER DEFAULT 0,\r\n\t\"smartValidatiy_timeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sizeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sessionSave\"\tINTEGER DEFAULT 1,\r\n\t\"macAddress\"\tTEXT,\r\n\t\"first_mac\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"userId\"\tINTEGER,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"profileId\"\tINTEGER,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"validUntil\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"activated\"\tINTEGER DEFAULT 0,\r\n\t\"state\"\tINTEGER,\r\n\t\"endTime\"\tINTEGER DEFAULT 0,\r\n\t\"paused\"\tINTEGER,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofileHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"price_display\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE INDEX \"session_Delet_fromServer\" ON \"session\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"session_acctSessionId\" ON \"session\" (\r\n\t\"acctSessionId\"\r\n);\r\nCREATE INDEX \"session_callingStationId\" ON \"session\" (\r\n\t\"callingStationId\"\r\n);\r\nCREATE INDEX \"session_fk_User_localDB_id\" ON \"session\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"session_fromTime\" ON \"session\" (\r\n\t\"fromTime\"\r\n);\r\nCREATE INDEX \"session_id\" ON \"session\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"session_idHX\" ON \"session\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"session_ipRouter\" ON \"session\" (\r\n\t\"ipRouter\"\r\n);\r\nCREATE INDEX \"session_ipUser\" ON \"session\" (\r\n\t\"ipUser\"\r\n);\r\nCREATE INDEX \"session_nasPortId\" ON \"session\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"session_sn\" ON \"session\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"session_sn_userName\" ON \"session\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"userHS_Delet_fromServer\" ON \"userHS\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_id\" ON \"userHS\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userHS_sn\" ON \"userHS\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"userHS_user_delete_fromServer\" ON \"userHS\" (\r\n\t\"userName\",\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_username\" ON \"userHS\" (\r\n\t\"userName\"\r\n);\r\nCREATE INDEX \"user_Delet_fromServer\" ON \"user\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"user_activeSessions\" ON \"user\" (\r\n\t\"activeSessions\"\r\n);\r\nCREATE INDEX user_actprofend_index ON user (\r\n    actualProfileEnd\r\n);\r\nCREATE INDEX \"user_all_filed_search\" ON \"user\" (\r\n\t\"cusName\",\r\n\t\"actualProfileName\",\r\n\t\"spId\",\r\n\t\"numberPrintedId\",\r\n\t\"firstUse\",\r\n\t\"radius\",\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_cusName\" ON \"user\" (\r\n\t\"cusName\"\r\n);\r\nCREATE INDEX \"user_firstUse\" ON \"user\" (\r\n\t\"firstUse\"\r\n);\r\nCREATE INDEX \"user_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"user_idHX\" ON \"user\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"user_lastSeenAt\" ON \"user\" (\r\n\t\"lastSeenAt\"\r\n);\r\nCREATE INDEX \"user_nasPortId\" ON \"user\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_numberPrintedId\" ON \"user\" (\r\n\t\"numberPrintedId\"\r\n);\r\nCREATE INDEX \"user_radius\" ON \"user\" (\r\n\t\"radius\"\r\n);\r\nCREATE INDEX \"user_regDate\" ON \"user\" (\r\n\t\"regDate\"\r\n);\r\nCREATE INDEX \"user_sn_indx\" ON \"user\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"user_sn_userName\" ON \"user\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"user_spId\" ON \"user\" (\r\n\t\"spId\"\r\n);\r\nCREATE INDEX \"userprofile_Delet_fromServer\" ON \"userprofile\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userprofile_added\" ON \"userprofile\" (\r\n\t\"added\"\r\n);\r\nCREATE INDEX \"userprofile_fk_User_localDB_id\" ON \"userprofile\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"userprofile_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userprofile_idHX\" ON \"userprofile\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"userprofile_profileName\" ON \"userprofile\" (\r\n\t\"profileName\"\r\n);\r\nCREATE INDEX \"userprofile_sn\" ON \"userprofile\" (\r\n\t\"sn\"\r\n);\r\nCOMMIT;\r\n";
            string Create_Database = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"BatchCards\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"batchNumber\"\tINTEGER NOT NULL,\r\n\t\"date\"\tINTEGER NOT NULL,\r\n\t\"count\"\tINTEGER,\r\n\t\"sn_from\"\tINTEGER NOT NULL,\r\n\t\"sn_to\"\tINTEGER NOT NULL,\r\n\t\"server\"\tTEXT NOT NULL DEFAULT 'usermanager',\r\n\t\"rb\"\tTEXT,\r\n\t\"actualProfileName\"\tTEXT NOT NULL,\r\n\t\"spId\"\tINTEGER,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"my_sequence\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"name\"\tTEXT NOT NULL,\r\n\t\"seq\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT,\r\n\t\"mk_sn\"\tTEXT,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"session\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER,\r\n\t\"custId\"\tINTEGER,\r\n\t\"userId\"\tINTEGER,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"sn_userName\",\"fk_User_localDB_id\"),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"sessionHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"sn_userName\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"user\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"firstName\"\tTEXT,\r\n\t\"lastName\"\tTEXT,\r\n\t\"descr\"\tTEXT,\r\n\t\"phone\"\tTEXT,\r\n\t\"location\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"callerId\"\tTEXT,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"downloadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uploadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeUsed\"\tINTEGER DEFAULT 0,\r\n\t\"downloadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"uploadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"activeSessions\"\tINTEGER DEFAULT 0,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\t\"sharedUsers\"\tTEXT DEFAULT 1,\r\n\t\"moneyPaid\"\tINTEGER DEFAULT 0,\r\n\t\"moneyUsed\"\tINTEGER DEFAULT 0,\r\n\t\"moneyPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"moneyTotal\"\tNUMERIC DEFAULT 0,\r\n\t\"discount\"\tNUMERIC DEFAULT 0,\r\n\t\"actualLimDownload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUpload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileId\"\tINTEGER,\r\n\t\"actualProfileStart\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileEnd\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"sn_Archive\"\tINTEGER DEFAULT 0,\r\n\t\"page_number\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"profileHotspot\"\tTEXT,\r\n\t\"limitUptime\"\tINTEGER DEFAULT 0,\r\n\t\"uptime\"\tINTEGER DEFAULT 0,\r\n\t\"bytesOut\"\tINTEGER DEFAULT 0,\r\n\t\"bytesIn\"\tINTEGER DEFAULT 0,\r\n\t\"limitbytestotal\"\tINTEGER DEFAULT 0,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"comment\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"server\"\tTEXT,\r\n\t\"address\"\tTEXT,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"pricePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price\"\tNUMERIC DEFAULT 0,\r\n\t\"priceDisplay\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"descr\"\tTEXT,\r\n\t\"smartValidatiy_Add\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_byDayHour\"\tINTEGER DEFAULT 0,\r\n\t\"smartValidatiy_timeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sizeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sessionSave\"\tINTEGER DEFAULT 1,\r\n\t\"macAddress\"\tTEXT,\r\n\t\"first_mac\"\tINTEGER DEFAULT 0,\r\n\t\"sn_Archive\"\tINTEGER DEFAULT 0,\r\n\t\"page_number\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"userId\"\tINTEGER,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"profileId\"\tINTEGER,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"validUntil\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"activated\"\tINTEGER DEFAULT 0,\r\n\t\"state\"\tINTEGER,\r\n\t\"endTime\"\tINTEGER DEFAULT 0,\r\n\t\"paused\"\tINTEGER,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"sn_userName\",\"fk_User_localDB_id\"),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofileHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"price_display\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE INDEX \"session_Delet_fromServer\" ON \"session\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"session_acctSessionId\" ON \"session\" (\r\n\t\"acctSessionId\"\r\n);\r\nCREATE INDEX \"session_callingStationId\" ON \"session\" (\r\n\t\"callingStationId\"\r\n);\r\nCREATE INDEX \"session_fk_User_localDB_id\" ON \"session\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"session_fromTime\" ON \"session\" (\r\n\t\"fromTime\"\r\n);\r\nCREATE INDEX \"session_id\" ON \"session\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"session_idHX\" ON \"session\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"session_ipRouter\" ON \"session\" (\r\n\t\"ipRouter\"\r\n);\r\nCREATE INDEX \"session_ipUser\" ON \"session\" (\r\n\t\"ipUser\"\r\n);\r\nCREATE INDEX \"session_nasPortId\" ON \"session\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"session_sn\" ON \"session\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"session_sn_userName\" ON \"session\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"userHS_Delet_fromServer\" ON \"userHS\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_id\" ON \"userHS\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userHS_sn\" ON \"userHS\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"userHS_user_delete_fromServer\" ON \"userHS\" (\r\n\t\"userName\",\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_username\" ON \"userHS\" (\r\n\t\"userName\"\r\n);\r\nCREATE INDEX \"user_Delet_fromServer\" ON \"user\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"user_activeSessions\" ON \"user\" (\r\n\t\"activeSessions\"\r\n);\r\nCREATE INDEX user_actprofend_index ON user (\r\n    actualProfileEnd\r\n);\r\nCREATE INDEX \"user_all_filed_search\" ON \"user\" (\r\n\t\"cusName\",\r\n\t\"actualProfileName\",\r\n\t\"spId\",\r\n\t\"numberPrintedId\",\r\n\t\"firstUse\",\r\n\t\"radius\",\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_cusName\" ON \"user\" (\r\n\t\"cusName\"\r\n);\r\nCREATE INDEX \"user_firstUse\" ON \"user\" (\r\n\t\"firstUse\"\r\n);\r\nCREATE INDEX \"user_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"user_idHX\" ON \"user\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"user_lastSeenAt\" ON \"user\" (\r\n\t\"lastSeenAt\"\r\n);\r\nCREATE INDEX \"user_nasPortId\" ON \"user\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_numberPrintedId\" ON \"user\" (\r\n\t\"numberPrintedId\"\r\n);\r\nCREATE INDEX \"user_radius\" ON \"user\" (\r\n\t\"radius\"\r\n);\r\nCREATE INDEX \"user_regDate\" ON \"user\" (\r\n\t\"regDate\"\r\n);\r\nCREATE INDEX \"user_sn_indx\" ON \"user\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"user_sn_userName\" ON \"user\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"user_spId\" ON \"user\" (\r\n\t\"spId\"\r\n);\r\nCREATE INDEX \"userprofile_Delet_fromServer\" ON \"userprofile\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userprofile_added\" ON \"userprofile\" (\r\n\t\"added\"\r\n);\r\nCREATE INDEX \"userprofile_fk_User_localDB_id\" ON \"userprofile\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"userprofile_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userprofile_idHX\" ON \"userprofile\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"userprofile_profileName\" ON \"userprofile\" (\r\n\t\"profileName\"\r\n);\r\nCREATE INDEX \"userprofile_sn\" ON \"userprofile\" (\r\n\t\"sn\"\r\n);\r\nCOMMIT;\r\n";
            sqlite_cmd = conn.CreateCommand();
            sqlite_cmd.CommandText = Create_Database;
            sqlite_cmd.ExecuteNonQuery();
        }

        static void InsertData(SQLiteConnection conn)
        {
            try
            {

                string cmd = "insert into routers ([soft_id],[mk_sn],[mk_code],[comment]) values (@soft_id,@mk_sn,@mk_code,@comment)";
                //var conn = new SQLiteConnection(source);
                SQLiteCommand cmd2 = new SQLiteCommand(cmd, conn);
                cmd2.Parameters.AddWithValue("@soft_id", Global_Variable.Mk_resources.RB_Soft_id);
                cmd2.Parameters.AddWithValue("@mk_sn", Global_Variable.Mk_resources.RB_SN);
                cmd2.Parameters.AddWithValue("@mk_code", Global_Variable.Mk_resources.RB_code);
                cmd2.Parameters.AddWithValue("@comment", "");
                cmd2.ExecuteNonQuery();


            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message);  }

            //SQLiteCommand sqlite_cmd;
            //sqlite_cmd = conn.CreateCommand();
            //sqlite_cmd.CommandText = "INSERT INTO SampleTable (Col1, Col2) VALUES('Test Text ', 1); ";
            //sqlite_cmd.ExecuteNonQuery();
            //sqlite_cmd.CommandText = "INSERT INTO SampleTable (Col1, Col2) VALUES('Test1 Text1 ', 2); ";
            //sqlite_cmd.ExecuteNonQuery();
            //sqlite_cmd.CommandText = "INSERT INTO SampleTable (Col1, Col2) VALUES('Test2 Text2 ', 3); ";
            //sqlite_cmd.ExecuteNonQuery();

            //sqlite_cmd.CommandText = "INSERT INTO SampleTable1 (Col1, Col2) VALUES('Test3 Text3 ', 3); ";
            //sqlite_cmd.ExecuteNonQuery();

        }

        static void ReadData(SQLiteConnection conn)
        {
            SQLiteDataReader sqlite_datareader;
            SQLiteCommand sqlite_cmd;
            sqlite_cmd = conn.CreateCommand();
            sqlite_cmd.CommandText = "SELECT * FROM SampleTable";

            sqlite_datareader = sqlite_cmd.ExecuteReader();
            while (sqlite_datareader.Read())
            {
                string myreader = sqlite_datareader.GetString(0);
                Console.WriteLine(myreader);
            }
            conn.Close();
        }
    }

    public class clss_CreateNewDatabase
    {
        public clss_CreateNewDatabase(){ }

        public bool create_default_db2(Connections_Db conn)
        {
            bool status=false;
            //try
            //{
            //    IOrmLiteDialectProvider provider = conn.provider;
            //    //IOrmLiteDialectProvider provider = Global_Variable.ProviderList[conn.Type];
            //    //======= Create File database ===================
            //    if (conn.Type == "SQLlite")
            //    {
            //        if (!File.Exists(conn.LocalDB_path))
            //        {
            //            SQLiteConnection.CreateFile(conn.LocalDB_path);
            //        }
            //        provider = SqliteDialect.Provider;
            //        OrmLiteConfig.OnModelDefinitionInit = modelDef =>
            //        {
            //            modelDef.FieldDefinitions
            //                .Where(x => x.ColumnType == typeof(long) || x.ColumnType == typeof(double) || x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string) || x.Name == "PercentageType" || x.Name == "Server")
            //                .Each(x =>
            //                {
            //                    if (x.ColumnType == typeof(long) || x.ColumnType == typeof(double))
            //                    {
            //                        x.CustomFieldDefinition = "INTEGER";
            //                    }
            //                    if (x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string))
            //                    {
            //                        x.CustomFieldDefinition = "TEXT";
            //                    }
            //                    //if (x.Name == "PercentageType")
            //                    //{
            //                    //    x.DefaultValue = "'ByPercentage'";
            //                    //}
            //                    //if (x.Name == "Server")
            //                    //{
            //                    //    x.DefaultValue = "'UserManager'";
            //                    //}

            //                });
            //        };
            //    }

            //    provider.GetStringConverter().UseUnicode = true;
            //    provider.GetStringConverter().StringLength = 255;

            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);
            //    using (var db = dbFactory.Open())
            //    {
            //        //db.CreateTableIfNotExists<My_Sequence>();
            //        //db.CreateTableIfNotExists<BatchCard>();
            //        //db.CreateTableIfNotExists<SellingPoint>();
            //        //var efff = db.CreateTableIfNotExists<My_Sequence>();
            //        //var eff = db.CreateTableIfNotExists<BatchArtchive>();
            //        //var effect = db.CreateTableIfNotExists<CardsArtchive>();

            //        var efsf= db.CreateTableIfNotExists<UmUser>();
            //        db.CreateTableIfNotExists<UmPyment>();
            //        db.CreateTableIfNotExists<UmSession>();
            //        db.CreateTableIfNotExists<HSUser>();
            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //    }
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }
           
            return status;
        }
        public bool create_default_db(Connections_Db conn)
        {
            bool status = false;
            //string LocalDB_Dir = $"{Directory.GetParent(Application.StartupPath)}\\dbs";
            //    if (!System.IO.Directory.Exists(LocalDB_Dir))
            //        System.IO.Directory.CreateDirectory(LocalDB_Dir);
            //string LocalDB_path = $"{Directory.GetParent(Application.StartupPath)}\\dbs\\{conn.FileName}";

            //if (!File.Exists(LocalDB_path))
            //    SQLiteConnection.CreateFile(LocalDB_path);

            ////if (!File.Exists(conn.LocalDB_path))
            ////    SQLiteConnection.CreateFile(conn.LocalDB_path);
            //conn.Connection_string = $"Data Source={Directory.GetParent(Application.StartupPath)}\\dbs\\{conn.FileName};";
            conn.Connection_string = utils.Get_LocalDB_ConnectionString(conn.FileName);
            conn.LocalDB_path = $"{utils.Get_Database_Directory()}\\{conn.FileName}";
             
            List<TableClass> tables = new List<TableClass>();
            tables.Add(new TableClass(typeof(UmUser), true));
            tables.Add(new TableClass(typeof(UmPyment), true));
            tables.Add(new TableClass(typeof(UmSession), true));
            tables.Add(new TableClass(typeof(HSUser), true));

            tables.Add(new TableClass(typeof(HsPyment), false));
            tables.Add(new TableClass(typeof(HsSession), false));

            //tables.Add(new TableClass(typeof(NumberPrintCard), false));
            //tables.Add(new TableClass(typeof(Comm_SellingPoint), false));
            //tables.Add(new TableClass(typeof(Alert_SellingPoint), false));

            
            // Create SQL for each table
            foreach (TableClass table in tables)
            {
                string script = table.CreateTableScript(table.WITHOUTROWID).ToString();
                
                lock (Sql_DataAccess.Lock_localDB)
                {
                    using (var con = Sql_DataAccess.GetConnection(conn.Connection_string))
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            int effectRows = con.Execute(script, sqLiteTransaction);
                            sqLiteTransaction.Commit();

                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show("\n"+ex.Message); }
                    }
                }
            }

            lock (Sql_DataAccess.Lock_localDB)
            {
                using (var con = Sql_DataAccess.GetConnection(conn.Connection_string))
                {
                    try
                    {
                        Fast_Load_From_Mikrotik f = new Fast_Load_From_Mikrotik();
                        f.Create_Indexs();
                    }
                    catch { }
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute("PRAGMA foreign_keys = ON;\nPRAGMA optimize;", sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        //int effectRows2 = con.Execute("VACUUM;");


                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }

                   
                }
            }




            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);
            //    using (var db = dbFactory.Open())
            //    {
            //        //db.CreateTableIfNotExists<My_Sequence>();
            //        //db.CreateTableIfNotExists<BatchCard>();
            //        //db.CreateTableIfNotExists<SellingPoint>();
            //        //var efff = db.CreateTableIfNotExists<My_Sequence>();
            //        //var eff = db.CreateTableIfNotExists<BatchArtchive>();
            //        //var effect = db.CreateTableIfNotExists<CardsArtchive>();

            //        var efsf= db.CreateTableIfNotExists<UmUser>();
            //        db.CreateTableIfNotExists<UmPyment>();
            //        db.CreateTableIfNotExists<UmSession>();
            //        db.CreateTableIfNotExists<HSUser>();
            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //    }
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }

            return status;
        }
        public bool create_Archive_db(Connections_Db conn)
        {
            bool status = false;
            try
            {
                if (!File.Exists(conn.ArchiveDB_path))
                    SQLiteConnection.CreateFile(conn.ArchiveDB_path);

                List<TableClass> tables = new List<TableClass>();
                tables.Add(new TableClass(typeof(BatchArtchive), false));
                tables.Add(new TableClass(typeof(CardsArtchive), false));


                // Create SQL for each table
                foreach (TableClass table in tables)
                {
                    string script = table.CreateTableScript(false).ToString();

                    lock (Archive_DataAccess.Lock_ArchiveDB)
                    {
                        using (var con = Archive_DataAccess.GetConnection(/*conn.Connection_string*/))
                        {
                            try
                            {
                                con.Open();
                                var sqLiteTransaction = con.BeginTransaction();
                                int effectRows = con.Execute(script, sqLiteTransaction);
                                sqLiteTransaction.Commit();

                                status = true;
                            }
                            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                        }
                    }
                }

                lock (Sql_DataAccess.Lock_localDB)
                {
                    using (var con = Archive_DataAccess.GetConnection())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            int effectRows = con.Execute("PRAGMA foreign_keys = ON;", sqLiteTransaction);
                            sqLiteTransaction.Commit();

                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    }
                }


            }
            catch(Exception ex) { RJMessageBox.Show(ex.Message); }

            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);
            //    using (var db = dbFactory.Open())
            //    {
            //        //db.CreateTableIfNotExists<My_Sequence>();
            //        //db.CreateTableIfNotExists<BatchCard>();
            //        //db.CreateTableIfNotExists<SellingPoint>();
            //        //var efff = db.CreateTableIfNotExists<My_Sequence>();
            //        //var eff = db.CreateTableIfNotExists<BatchArtchive>();
            //        //var effect = db.CreateTableIfNotExists<CardsArtchive>();

            //        var efsf= db.CreateTableIfNotExists<UmUser>();
            //        db.CreateTableIfNotExists<UmPyment>();
            //        db.CreateTableIfNotExists<UmSession>();
            //        db.CreateTableIfNotExists<HSUser>();
            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //    }
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }

            return status;
        }
        public bool Create_Smart_db(Connections_Db conn)
        {
            //string Smartfile = Directory.GetCurrentDirectory() + "\\Smart.db";
            //string Smartfile = utils.Get_Database_Directory() + "\\Smart_No_Pass.db";
            string Smartfile = conn.LocalDB_path + "\\"+conn.Name;
            bool status = false;
            
            if (!File.Exists(Smartfile))
                SQLiteConnection.CreateFile(Smartfile);



            List<TableClass> tables = new List<TableClass>();
            //tables.Add(new TableClass(typeof(CardsTemplate), true));
            tables.Add(new TableClass(typeof(Account), false));
            tables.Add(new TableClass(typeof(AccountMove), false));
            tables.Add(new TableClass(typeof(Company), false));
            tables.Add(new TableClass(typeof(Partner), false));
            tables.Add(new TableClass(typeof(ProductUoM), false));
            tables.Add(new TableClass(typeof(Entities.Accounts.Product), false));





            // Create SQL for each table
            foreach (TableClass table in tables)
            {
                string script = table.CreateTableScript().ToString();

                lock (Smart_DataAccess.Lock_object)
                {
                    using (var con = Smart_DataAccess.GetConnSmart(conn.Connection_string))
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            int effectRows = con.Execute(script, sqLiteTransaction);
                            sqLiteTransaction.Commit();

                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    }
                }
            }

            lock (Smart_DataAccess.Lock_object)
            {
                using (var con = Smart_DataAccess.GetConnSmart(conn.Connection_string))
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        int effectRows = con.Execute("PRAGMA foreign_keys = ON;", sqLiteTransaction);
                        sqLiteTransaction.Commit();

                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
            }


            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);
            //    using (var db = dbFactory.Open())
            //    {
            //        //db.CreateTableIfNotExists<My_Sequence>();
            //        //db.CreateTableIfNotExists<BatchCard>();
            //        //db.CreateTableIfNotExists<SellingPoint>();
            //        //var efff = db.CreateTableIfNotExists<My_Sequence>();
            //        //var eff = db.CreateTableIfNotExists<BatchArtchive>();
            //        //var effect = db.CreateTableIfNotExists<CardsArtchive>();

            //        var efsf= db.CreateTableIfNotExists<UmUser>();
            //        db.CreateTableIfNotExists<UmPyment>();
            //        db.CreateTableIfNotExists<UmSession>();
            //        db.CreateTableIfNotExists<HSUser>();
            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //    }
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }

            return status;
        }

        public bool Create_default_ArchiveDB(Connections_Db conn)
        {
            bool status = false;
            //try
            //{
            //    IOrmLiteDialectProvider provider = conn.provider;
            //    //IOrmLiteDialectProvider provider = Global_Variable.ProviderList[conn.Type];
            //    //======= Create File database ===================
            //    if (conn.Type == "SQLlite")
            //    {
            //        if (!File.Exists(conn.ArchiveDB_path))
            //        {
            //            SQLiteConnection.CreateFile(conn.ArchiveDB_path);
            //        }
            //        provider = SqliteDialect.Provider;
            //        OrmLiteConfig.OnModelDefinitionInit = modelDef =>
            //        {
            //            modelDef.FieldDefinitions
            //                .Where(x => x.ColumnType == typeof(long) || x.ColumnType == typeof(double) || x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string) || x.Name == "PercentageType" || x.Name == "Server")
            //                .Each(x =>
            //                {
            //                    if (x.ColumnType == typeof(long) || x.ColumnType == typeof(double))
            //                    {
            //                        x.CustomFieldDefinition = "INTEGER";
            //                    }
            //                    if (x.ColumnType == typeof(DateTime) || x.ColumnType == typeof(string))
            //                    {
            //                        x.CustomFieldDefinition = "TEXT";
            //                    }
            //                    //if (x.Name == "PercentageType")
            //                    //{
            //                    //    x.DefaultValue = "'ByPercentage'";
            //                    //}
            //                    //if (x.Name == "Server")
            //                    //{
            //                    //    x.DefaultValue = "'UserManager'";
            //                    //}

            //                });
            //        };
            //    }

            //    provider.GetStringConverter().UseUnicode = true;
            //    provider.GetStringConverter().StringLength = 255;

            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);
            //    using (var db = dbFactory.Open())
            //    {
            //        //var efff = db.CreateTableIfNotExists<My_Sequence>();
            //        var eff = db.CreateTableIfNotExists<BatchArtchive>();
            //        var effect = db.CreateTableIfNotExists<CardsArtchive>();

            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //        var tab = db.Select<CardsArtchive>().ToList();
            //    }
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }
          
            return status;
        }
        
        void  CreateConnection(string name, string path)
        {
            string fn = @"db\" + name;
            SQLiteConnection sqlite_conn;
            sqlite_conn = new SQLiteConnection("Data Source=" + fn + "; Version = 3; New = True; Compress = True; ");
            try
            {
                sqlite_conn.Open();
                sqlite_conn.Close();    
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message);
            }
        }
    
        static void CreateTable(SQLiteConnection conn)
        {
            SQLiteCommand sqlite_cmd;
            string Create_Database = "BEGIN TRANSACTION;\r\nCREATE TABLE IF NOT EXISTS \"BatchCards\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"batchNumber\"\tINTEGER NOT NULL,\r\n\t\"date\"\tINTEGER NOT NULL,\r\n\t\"count\"\tINTEGER,\r\n\t\"sn_from\"\tINTEGER NOT NULL,\r\n\t\"sn_to\"\tINTEGER NOT NULL,\r\n\t\"server\"\tTEXT NOT NULL DEFAULT 'usermanager',\r\n\t\"rb\"\tTEXT,\r\n\t\"actualProfileName\"\tTEXT NOT NULL,\r\n\t\"spId\"\tINTEGER,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"my_sequence\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"name\"\tTEXT NOT NULL,\r\n\t\"seq\"\tINTEGER NOT NULL DEFAULT 0,\r\n\t\"rb\"\tTEXT NOT NULL,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"name\",\"rb\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"routers\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"soft_id\"\tTEXT,\r\n\t\"mk_sn\"\tTEXT,\r\n\t\"mk_code\"\tTEXT NOT NULL UNIQUE,\r\n\t\"comment\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT)\r\n);\r\nCREATE TABLE IF NOT EXISTS \"session\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER,\r\n\t\"custId\"\tINTEGER,\r\n\t\"userId\"\tINTEGER,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"sn_userName\",\"fk_User_localDB_id\"),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"sessionHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"callingStationId\"\tTEXT,\r\n\t\"ipUser\"\tTEXT,\r\n\t\"ipRouter\"\tTEXT,\r\n\t\"acctSessionId\"\tTEXT,\r\n\t\"status\"\tINTEGER,\r\n\t\"status_str\"\tTEXT,\r\n\t\"active\"\tINTEGER,\r\n\t\"fromTime\"\tINTEGER,\r\n\t\"tillTime\"\tINTEGER,\r\n\t\"upTime\"\tINTEGER,\r\n\t\"bytesDownload\"\tINTEGER,\r\n\t\"bytesUpload\"\tINTEGER,\r\n\t\"userName\"\tTEXT,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"sn_userName\"\tTEXT,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"user\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"firstName\"\tTEXT,\r\n\t\"lastName\"\tTEXT,\r\n\t\"descr\"\tTEXT,\r\n\t\"phone\"\tTEXT,\r\n\t\"location\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"callerId\"\tTEXT,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"downloadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uploadLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeUsed\"\tINTEGER DEFAULT 0,\r\n\t\"downloadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"uploadUsed\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"activeSessions\"\tINTEGER DEFAULT 0,\r\n\t\"active\"\tINTEGER DEFAULT 0,\r\n\t\"sharedUsers\"\tTEXT DEFAULT 1,\r\n\t\"moneyPaid\"\tINTEGER DEFAULT 0,\r\n\t\"moneyUsed\"\tINTEGER DEFAULT 0,\r\n\t\"moneyPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"moneyTotal\"\tNUMERIC DEFAULT 0,\r\n\t\"discount\"\tNUMERIC DEFAULT 0,\r\n\t\"actualLimDownload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUpload\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileId\"\tINTEGER,\r\n\t\"actualProfileStart\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileEnd\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"sn_Archive\"\tINTEGER DEFAULT 0,\r\n\t\"page_number\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT,\r\n\t\"password\"\tTEXT,\r\n\t\"profileHotspot\"\tTEXT,\r\n\t\"limitUptime\"\tINTEGER DEFAULT 0,\r\n\t\"uptime\"\tINTEGER DEFAULT 0,\r\n\t\"bytesOut\"\tINTEGER DEFAULT 0,\r\n\t\"bytesIn\"\tINTEGER DEFAULT 0,\r\n\t\"limitbytestotal\"\tINTEGER DEFAULT 0,\r\n\t\"disabled\"\tINTEGER DEFAULT 0,\r\n\t\"comment\"\tTEXT,\r\n\t\"email\"\tTEXT,\r\n\t\"server\"\tTEXT,\r\n\t\"address\"\tTEXT,\r\n\t\"regDate\"\tINTEGER DEFAULT 0,\r\n\t\"uptimeLimit\"\tINTEGER DEFAULT 0,\r\n\t\"transferLimit\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"lastSeenAt\"\tINTEGER DEFAULT 0,\r\n\t\"pricePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price\"\tNUMERIC DEFAULT 0,\r\n\t\"priceDisplay\"\tINTEGER DEFAULT 0,\r\n\t\"actualProfileName\"\tTEXT,\r\n\t\"profileTillTime\"\tINTEGER DEFAULT 0,\r\n\t\"profileTransferLeft\"\tINTEGER DEFAULT 0,\r\n\t\"profileTimeLeft\"\tINTEGER DEFAULT 0,\r\n\t\"spId\"\tINTEGER,\r\n\t\"spName\"\tTEXT,\r\n\t\"is_spPercentage\"\tINTEGER DEFAULT 0,\r\n\t\"spPercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"profilePercentage\"\tNUMERIC DEFAULT 0,\r\n\t\"numberPrintedId\"\tINTEGER,\r\n\t\"numberPrintedName\"\tTEXT,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 1,\r\n\t\"mkId\"\tINTEGER,\r\n\t\"date_added_Localdb\"\tINTEGER,\r\n\t\"status\"\tINTEGER DEFAULT 0,\r\n\t\"countProfile\"\tINTEGER DEFAULT 1,\r\n\t\"idHX\"\tTEXT,\r\n\t\"radius\"\tTEXT,\r\n\t\"nasPortId\"\tTEXT,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\t\"descr\"\tTEXT,\r\n\t\"smartValidatiy_Add\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_byDayHour\"\tINTEGER DEFAULT 0,\r\n\t\"smartValidatiy_timeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sizeSave\"\tINTEGER DEFAULT 1,\r\n\t\"smartValidatiy_sessionSave\"\tINTEGER DEFAULT 1,\r\n\t\"macAddress\"\tTEXT,\r\n\t\"first_mac\"\tINTEGER DEFAULT 0,\r\n\t\"sn_Archive\"\tINTEGER DEFAULT 0,\r\n\t\"page_number\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"id\",\"sn_userName\"),\r\n\tUNIQUE(\"sn\",\"userName\")\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofile\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"sn\"\tINTEGER NOT NULL,\r\n\t\"userId\"\tINTEGER,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"sn_userName\"\tTEXT NOT NULL UNIQUE,\r\n\t\"custId\"\tINTEGER,\r\n\t\"cusName\"\tTEXT,\r\n\t\"profileId\"\tINTEGER,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"validUntil\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"activated\"\tINTEGER DEFAULT 0,\r\n\t\"state\"\tINTEGER,\r\n\t\"endTime\"\tINTEGER DEFAULT 0,\r\n\t\"paused\"\tINTEGER,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimUptime\"\tINTEGER DEFAULT 0,\r\n\t\"actualLimTransfer\"\tINTEGER DEFAULT 0,\r\n\t\"idHX\"\tTEXT,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tUNIQUE(\"sn_userName\",\"fk_User_localDB_id\"),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"user\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE TABLE IF NOT EXISTS \"userprofileHS\" (\r\n\t\"id\"\tINTEGER NOT NULL UNIQUE,\r\n\t\"userName\"\tTEXT NOT NULL,\r\n\t\"profileName\"\tTEXT,\r\n\t\"price\"\tINTEGER DEFAULT 0,\r\n\t\"price_display\"\tINTEGER DEFAULT 0,\r\n\t\"added\"\tINTEGER DEFAULT 0,\r\n\t\"fk_sn_userName_User\"\tTEXT,\r\n\t\"fk_User_localDB_id\"\tINTEGER,\r\n\t\"percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"price_percentage\"\tNUMERIC DEFAULT 0,\r\n\t\"Delet_fromServer\"\tINTEGER DEFAULT 0,\r\n\t\"date_added_Localdb\"\tINTEGER DEFAULT 0,\r\n\t\"profileValiday\"\tINTEGER DEFAULT 0,\r\n\t\"firstUse\"\tINTEGER DEFAULT 0,\r\n\tPRIMARY KEY(\"id\" AUTOINCREMENT),\r\n\tFOREIGN KEY(\"fk_User_localDB_id\") REFERENCES \"userHS\"(\"id\") ON DELETE CASCADE\r\n);\r\nCREATE INDEX \"session_Delet_fromServer\" ON \"session\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"session_acctSessionId\" ON \"session\" (\r\n\t\"acctSessionId\"\r\n);\r\nCREATE INDEX \"session_callingStationId\" ON \"session\" (\r\n\t\"callingStationId\"\r\n);\r\nCREATE INDEX \"session_fk_User_localDB_id\" ON \"session\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"session_fromTime\" ON \"session\" (\r\n\t\"fromTime\"\r\n);\r\nCREATE INDEX \"session_id\" ON \"session\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"session_idHX\" ON \"session\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"session_ipRouter\" ON \"session\" (\r\n\t\"ipRouter\"\r\n);\r\nCREATE INDEX \"session_ipUser\" ON \"session\" (\r\n\t\"ipUser\"\r\n);\r\nCREATE INDEX \"session_nasPortId\" ON \"session\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"session_sn\" ON \"session\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"session_sn_userName\" ON \"session\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"userHS_Delet_fromServer\" ON \"userHS\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_id\" ON \"userHS\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userHS_sn\" ON \"userHS\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"userHS_user_delete_fromServer\" ON \"userHS\" (\r\n\t\"userName\",\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userHS_username\" ON \"userHS\" (\r\n\t\"userName\"\r\n);\r\nCREATE INDEX \"user_Delet_fromServer\" ON \"user\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"user_activeSessions\" ON \"user\" (\r\n\t\"activeSessions\"\r\n);\r\nCREATE INDEX user_actprofend_index ON user (\r\n    actualProfileEnd\r\n);\r\nCREATE INDEX \"user_all_filed_search\" ON \"user\" (\r\n\t\"cusName\",\r\n\t\"actualProfileName\",\r\n\t\"spId\",\r\n\t\"numberPrintedId\",\r\n\t\"firstUse\",\r\n\t\"radius\",\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_cusName\" ON \"user\" (\r\n\t\"cusName\"\r\n);\r\nCREATE INDEX \"user_firstUse\" ON \"user\" (\r\n\t\"firstUse\"\r\n);\r\nCREATE INDEX \"user_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"user_idHX\" ON \"user\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"user_lastSeenAt\" ON \"user\" (\r\n\t\"lastSeenAt\"\r\n);\r\nCREATE INDEX \"user_nasPortId\" ON \"user\" (\r\n\t\"nasPortId\"\r\n);\r\nCREATE INDEX \"user_numberPrintedId\" ON \"user\" (\r\n\t\"numberPrintedId\"\r\n);\r\nCREATE INDEX \"user_radius\" ON \"user\" (\r\n\t\"radius\"\r\n);\r\nCREATE INDEX \"user_regDate\" ON \"user\" (\r\n\t\"regDate\"\r\n);\r\nCREATE INDEX \"user_sn_indx\" ON \"user\" (\r\n\t\"sn\"\r\n);\r\nCREATE INDEX \"user_sn_userName\" ON \"user\" (\r\n\t\"sn_userName\"\r\n);\r\nCREATE INDEX \"user_spId\" ON \"user\" (\r\n\t\"spId\"\r\n);\r\nCREATE INDEX \"userprofile_Delet_fromServer\" ON \"userprofile\" (\r\n\t\"Delet_fromServer\"\r\n);\r\nCREATE INDEX \"userprofile_added\" ON \"userprofile\" (\r\n\t\"added\"\r\n);\r\nCREATE INDEX \"userprofile_fk_User_localDB_id\" ON \"userprofile\" (\r\n\t\"fk_User_localDB_id\"\r\n);\r\nCREATE INDEX \"userprofile_id\" ON \"user\" (\r\n\t\"id\"\r\n);\r\nCREATE INDEX \"userprofile_idHX\" ON \"userprofile\" (\r\n\t\"idHX\"\r\n);\r\nCREATE INDEX \"userprofile_profileName\" ON \"userprofile\" (\r\n\t\"profileName\"\r\n);\r\nCREATE INDEX \"userprofile_sn\" ON \"userprofile\" (\r\n\t\"sn\"\r\n);\r\nCOMMIT;\r\n";
            sqlite_cmd = conn.CreateCommand();
            sqlite_cmd.CommandText = Create_Database;
            sqlite_cmd.ExecuteNonQuery();
        }

        static void Inser_Default_tData(SQLiteConnection conn,Connections_Db _conns)
        {
            try
            {
                //Sql_DataAccess.AddNew_Connection_string(Global_Variable.LocalPathDB,fileName,conn);
                //string cmd = "insert into routers ([soft_id],[mk_sn],[mk_code],[comment]) values (@soft_id,@mk_sn,@mk_code,@comment)";
                ////var conn = new SQLiteConnection(source);
                //SQLiteCommand cmd2 = new SQLiteCommand(cmd, conn);
                //cmd2.Parameters.AddWithValue("@soft_id", Global_Variable.Mk_resources.RB_Soft_id);
                //cmd2.Parameters.AddWithValue("@mk_sn", Global_Variable.Mk_resources.RB_SN);
                //cmd2.Parameters.AddWithValue("@mk_code", Global_Variable.Mk_resources.RB_code);
                //cmd2.Parameters.AddWithValue("@comment", "");
                //cmd2.ExecuteNonQuery();
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        public bool create_Schema_Table_db(Connections_Db conn)
        {
            bool status = false;
            //try
            //{
            //    IOrmLiteDialectProvider provider = SqliteDialect.Provider;
            //    var dbFactory = new OrmLiteConnectionFactory(conn.Connection_string, provider);

            //    provider.GetStringConverter().UseUnicode = true;
            //    provider.GetStringConverter().StringLength = 255;
            //    using (var db = dbFactory.Open())
            //    {
            //        //db.CreateTableIfNotExists<My_Sequence>();
            //        //db.CreateTableIfNotExists<BatchCard>();
            //        //db.CreateTableIfNotExists<SellingPoint>();
            //        db.CreateTableIfNotExists<UmUser>();
            //        db.CreateTableIfNotExists<UmPyment>();
            //        db.CreateTableIfNotExists<UmSession>();
            //        db.CreateTableIfNotExists<HSUser>();
            //        db.ExecuteSql("PRAGMA foreign_keys = ON;");
            //        status = true;
            //    }
            //} catch (Exception ex) {RJMessageBox.Show(ex.Message); }
           
            return status;
        }

    }
}

