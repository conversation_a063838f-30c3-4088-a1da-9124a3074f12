﻿using SmartCreator.Forms.Hotspot;
using SmartCreator.Forms.UserManager;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Devices
{
    public partial class Form_Device_Management : RJChildForm
    {
        Form_OpentWrt_Manage form_OpentWrt_Manage;
        Form_Ubiquiti_Manage form_Ubiquiti_Manage;
        bool First_OpentWrt_Manage = true;
        bool First_Ubiquiti_Manage = true;


        public Form_Device_Management()
        {
            InitializeComponent();
            this.Text = "ادارة اجهزة البث";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Device Manage";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_OpneWrt_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_OpneWrt_Title);

            if (First_OpentWrt_Manage)
            {
                First_OpentWrt_Manage = false;
                form_OpentWrt_Manage = new Form_OpentWrt_Manage();
                form_OpentWrt_Manage.TopLevel = false;
                form_OpentWrt_Manage.IsChildForm = true;
                form_OpentWrt_Manage.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_OpentWrt_Manage);
                this.panel_Tab_Container.Tag = form_OpentWrt_Manage;
                form_OpentWrt_Manage.Show(); //show on desktop panel  
                form_OpentWrt_Manage.BringToFront();
                form_OpentWrt_Manage.Focus();
                //form_OpentWrt_Manage.LoadDataGridviewData();

            }
            else
            {
                form_OpentWrt_Manage.BringToFront();
                form_OpentWrt_Manage.Show();
                form_OpentWrt_Manage.Focus();
            }
        }

        private void btn_Ubiquiti_Title_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Ubiquiti_Title);

            if (First_Ubiquiti_Manage)
            {
                First_Ubiquiti_Manage = false;
                form_Ubiquiti_Manage = new Form_Ubiquiti_Manage();
                form_Ubiquiti_Manage.TopLevel = false;
                form_Ubiquiti_Manage.IsChildForm = true;
                form_Ubiquiti_Manage.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_Ubiquiti_Manage);
                this.panel_Tab_Container.Tag = First_Ubiquiti_Manage;
                form_Ubiquiti_Manage.Show(); //show on desktop panel  
                form_Ubiquiti_Manage.BringToFront();
                form_Ubiquiti_Manage.Focus();
                //form_Ubiquiti_Manage.LoadDataGridviewData();

            }
            else
            {
                form_Ubiquiti_Manage.BringToFront();
                form_Ubiquiti_Manage.Show();
                form_Ubiquiti_Manage.Focus();
            }
        }
    }
}
