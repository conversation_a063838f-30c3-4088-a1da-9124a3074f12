using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Drawing.Design;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نسخة آمنة جداً من RJTabControl - بدون تاب افتراضي
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class SuperSafeRJTabControl : Panel
    {
        #region Fields - أساسية فقط
        private List<RJTabPage> _tabs;
        private RJPanel _tabsPanel;
        private RJPanel _contentPanel;
        private RJTabPage _activeTab;
        private int _tabHeight = 35;
        private int _tabSpacing = 2;
        private int _tabPadding = 15;
        #endregion

        #region Constructor - مبسط جداً بدون تاب افتراضي
        public SuperSafeRJTabControl()
        {
            try
            {
                // تهيئة فورية وآمنة
                _tabs = new List<RJTabPage>();
                _tabsCollection = new SuperSafeTabCollection(this);

                // إنشاء المكونات فقط
                InitializeComponents();

                // لا نضيف أي تابات افتراضية لتجنب مشاكل Designer
            }
            catch
            {
                // تجاهل جميع الأخطاء في Constructor
            }
        }
        #endregion

        #region Properties - آمنة تماماً
        /// <summary>
        /// ارتفاع التابات
        /// </summary>
        [Category("Super Safe Tab Control")]
        [Description("Height of tabs")]
        [DefaultValue(35)]
        public int TabHeight
        {
            get { return _tabHeight; }
            set
            {
                try
                {
                    _tabHeight = value;
                    if (_tabsPanel != null)
                    {
                        _tabsPanel.Height = _tabHeight;
                        ArrangeTabsSafely();
                    }
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// المسافة بين التابات
        /// </summary>
        [Category("Super Safe Tab Control")]
        [Description("Spacing between tabs")]
        [DefaultValue(2)]
        public int TabSpacing
        {
            get { return _tabSpacing; }
            set
            {
                try
                {
                    _tabSpacing = value;
                    ArrangeTabsSafely();
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// الحشو الداخلي للتابات
        /// </summary>
        [Category("Super Safe Tab Control")]
        [Description("Padding inside tabs")]
        [DefaultValue(15)]
        public int TabPadding
        {
            get { return _tabPadding; }
            set
            {
                try
                {
                    _tabPadding = value;
                    ArrangeTabsSafely();
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// عدد التابات - للقراءة فقط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public int TabCount
        {
            get 
            { 
                try
                {
                    return _tabs?.Count ?? 0;
                }
                catch
                {
                    return 0;
                }
            }
        }

        /// <summary>
        /// فهرس التاب النشط
        /// </summary>
        [Category("Super Safe Tab Control")]
        [Description("Index of selected tab")]
        [DefaultValue(-1)]
        public int SelectedIndex
        {
            get 
            { 
                try
                {
                    if (_tabs == null || _activeTab == null) return -1;
                    return _tabs.IndexOf(_activeTab);
                }
                catch
                {
                    return -1;
                }
            }
            set
            {
                try
                {
                    if (_tabs == null || value < 0 || value >= _tabs.Count) return;
                    ActivateTabSafely(_tabs[value]);
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// التاب النشط
        /// </summary>
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public RJTabPage SelectedTab
        {
            get
            {
                try
                {
                    return _activeTab;
                }
                catch
                {
                    return null;
                }
            }
            set
            {
                try
                {
                    ActivateTabSafely(value);
                }
                catch
                {
                    // تجاهل الأخطاء
                }
            }
        }

        /// <summary>
        /// مجموعة التابات - آمنة للـ Designer
        /// </summary>
        [Category("Super Safe Tab Control")]
        [Description("Collection of tabs - safe for designer")]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]
        [Editor(typeof(SuperSafeTabCollectionEditor), typeof(UITypeEditor))]
        public SuperSafeTabCollection Tabs
        {
            get
            {
                try
                {
                    return _tabsCollection ?? (_tabsCollection = new SuperSafeTabCollection(this));
                }
                catch
                {
                    return _tabsCollection = new SuperSafeTabCollection(this);
                }
            }
        }
        private SuperSafeTabCollection _tabsCollection;
        #endregion

        #region Methods - آمنة تماماً
        /// <summary>
        /// تهيئة المكونات بطريقة آمنة
        /// </summary>
        private void InitializeComponents()
        {
            try
            {
                this.SuspendLayout();

                // إنشاء panel التابات
                _tabsPanel = new RJPanel();
                _tabsPanel.Dock = DockStyle.Top;
                _tabsPanel.Height = _tabHeight;
                _tabsPanel.BackColor = Color.FromArgb(55, 55, 58);
                _tabsPanel.BorderSize = 0;

                // إنشاء panel المحتوى
                _contentPanel = new RJPanel();
                _contentPanel.Dock = DockStyle.Fill;
                _contentPanel.BackColor = Color.FromArgb(37, 37, 38);
                _contentPanel.BorderSize = 1;
                _contentPanel.BorderColor = Color.FromArgb(0, 122, 204);

                // إعدادات الكنترول الرئيسي
                this.BackColor = Color.FromArgb(45, 45, 48);
                this.Size = new Size(400, 300);

                // إضافة المكونات
                this.Controls.Add(_contentPanel);
                this.Controls.Add(_tabsPanel);

                this.ResumeLayout(false);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إضافة تاب جديد بطريقة آمنة
        /// </summary>
        public RJTabPage AddTab(string text)
        {
            try
            {
                if (string.IsNullOrEmpty(text) || _tabsPanel == null || _contentPanel == null)
                    return null;

                var tab = new RJTabPage(text);
                tab.BackColor = Color.FromArgb(70, 70, 70);
                tab.ForeColor = Color.White;
                tab.Height = _tabHeight - 4;
                tab.Click += TabClickHandler;

                // إضافة للقائمة
                _tabs.Add(tab);

                // إضافة للواجهة
                _tabsPanel.Controls.Add(tab);
                _contentPanel.Controls.Add(tab.ContentPanel);

                // ترتيب وتفعيل
                ArrangeTabsSafely();
                if (_tabs.Count == 1)
                    ActivateTabSafely(tab);

                return tab;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// إضافة تاب مع أيقونة
        /// </summary>
        public RJTabPage AddTab(string text, IconChar icon)
        {
            try
            {
                var tab = AddTab(text);
                if (tab != null)
                {
                    tab.IconChar = icon;
                    tab.IconSize = 16;
                }
                return tab;
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// تفعيل تاب بطريقة آمنة
        /// </summary>
        private void ActivateTabSafely(RJTabPage tab)
        {
            try
            {
                if (tab == null || _tabs == null || !_tabs.Contains(tab)) return;

                // إلغاء تفعيل التاب السابق
                if (_activeTab != null)
                {
                    _activeTab.BackColor = Color.FromArgb(70, 70, 70);
                    if (_activeTab.ContentPanel != null)
                        _activeTab.ContentPanel.Visible = false;
                }

                // تفعيل التاب الجديد
                _activeTab = tab;
                _activeTab.BackColor = Color.FromArgb(0, 122, 204);
                if (_activeTab.ContentPanel != null)
                {
                    _activeTab.ContentPanel.Visible = true;
                    _activeTab.ContentPanel.BringToFront();
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// ترتيب التابات بطريقة آمنة
        /// </summary>
        private void ArrangeTabsSafely()
        {
            try
            {
                if (_tabs == null || _tabs.Count == 0) return;

                int x = _tabSpacing;
                foreach (var tab in _tabs)
                {
                    if (tab == null) continue;

                    // حساب عرض التاب
                    int tabWidth = CalculateTabWidthSafely(tab);

                    // تحديد موقع التاب
                    tab.Location = new Point(x, 2);
                    tab.Size = new Size(tabWidth, _tabHeight - 4);

                    x += tabWidth + _tabSpacing;
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// حساب عرض التاب بطريقة آمنة
        /// </summary>
        private int CalculateTabWidthSafely(RJTabPage tab)
        {
            try
            {
                if (tab == null) return 100;

                using (Graphics g = this.CreateGraphics())
                {
                    SizeF textSize = g.MeasureString(tab.Text, tab.Font);
                    return (int)textSize.Width + (_tabPadding * 2) + 10;
                }
            }
            catch
            {
                return 100; // عرض افتراضي
            }
        }

        /// <summary>
        /// معالج النقر على التاب
        /// </summary>
        private void TabClickHandler(object sender, EventArgs e)
        {
            try
            {
                if (sender is RJTabPage tab)
                {
                    ActivateTabSafely(tab);
                }
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// مسح جميع التابات
        /// </summary>
        public void ClearAllTabs()
        {
            try
            {
                if (_tabs == null) return;

                // إزالة من الواجهة
                if (_tabsPanel != null)
                    _tabsPanel.Controls.Clear();
                if (_contentPanel != null)
                    _contentPanel.Controls.Clear();

                // مسح القائمة
                _tabs.Clear();
                _activeTab = null;
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إضافة تاب داخلياً - للـ Collection
        /// </summary>
        internal void AddTabInternal(RJTabPage tab)
        {
            try
            {
                if (tab == null || _tabsPanel == null || _contentPanel == null) return;

                // إعداد التاب
                tab.Height = _tabHeight - 4;
                tab.Click += TabClickHandler;

                // إضافة للقائمة إذا لم يكن موجود
                if (!_tabs.Contains(tab))
                    _tabs.Add(tab);

                // إضافة للواجهة
                if (!_tabsPanel.Controls.Contains(tab))
                    _tabsPanel.Controls.Add(tab);
                if (!_contentPanel.Controls.Contains(tab.ContentPanel))
                    _contentPanel.Controls.Add(tab.ContentPanel);

                // ترتيب وتفعيل
                ArrangeTabsSafely();
                if (_tabs.Count == 1)
                    ActivateTabSafely(tab);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }

        /// <summary>
        /// إزالة تاب داخلياً - للـ Collection
        /// </summary>
        internal void RemoveTabInternal(RJTabPage tab)
        {
            try
            {
                if (tab == null || _tabs == null) return;

                // إلغاء ربط الأحداث
                tab.Click -= TabClickHandler;

                // إزالة من القائمة والواجهة
                _tabs.Remove(tab);
                _tabsPanel?.Controls.Remove(tab);
                _contentPanel?.Controls.Remove(tab.ContentPanel);

                // تفعيل تاب آخر إذا كان هذا نشط
                if (_activeTab == tab)
                {
                    if (_tabs.Count > 0)
                        ActivateTabSafely(_tabs[0]);
                    else
                        _activeTab = null;
                }

                // ترتيب التابات
                ArrangeTabsSafely();
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion

        #region Override Methods - آمنة
        protected override void Dispose(bool disposing)
        {
            try
            {
                if (disposing)
                {
                    // تنظيف الموارد
                    if (_tabs != null)
                    {
                        foreach (var tab in _tabs)
                        {
                            if (tab != null)
                                tab.Click -= TabClickHandler;
                        }
                        _tabs.Clear();
                    }
                }
                base.Dispose(disposing);
            }
            catch
            {
                // تجاهل الأخطاء
            }
        }
        #endregion
    }

    /// <summary>
    /// مجموعة تابات آمنة للـ Designer
    /// </summary>
    public class SuperSafeTabCollection : IList<RJTabPage>, ICollection<RJTabPage>, IEnumerable<RJTabPage>
    {
        private readonly List<RJTabPage> _items;
        private SuperSafeRJTabControl _parent;

        public SuperSafeTabCollection(SuperSafeRJTabControl parent)
        {
            _items = new List<RJTabPage>();
            _parent = parent;
        }

        public int Count => _items?.Count ?? 0;
        public bool IsReadOnly => false;

        public RJTabPage this[int index]
        {
            get
            {
                try
                {
                    if (index < 0 || index >= _items.Count) return null;
                    return _items[index];
                }
                catch { return null; }
            }
            set
            {
                try
                {
                    if (value == null || index < 0 || index >= _items.Count) return;
                    var oldTab = _items[index];
                    _items[index] = value;
                    if (_parent != null)
                    {
                        _parent.RemoveTabInternal(oldTab);
                        _parent.AddTabInternal(value);
                    }
                }
                catch { }
            }
        }

        public void Add(RJTabPage item)
        {
            try
            {
                if (item == null) return;
                _items.Add(item);
                _parent?.AddTabInternal(item);
            }
            catch { }
        }

        public void Insert(int index, RJTabPage item)
        {
            try
            {
                if (item == null || index < 0 || index > _items.Count) return;
                _items.Insert(index, item);
                _parent?.AddTabInternal(item);
            }
            catch { }
        }

        public bool Remove(RJTabPage item)
        {
            try
            {
                if (item == null) return false;
                var removed = _items.Remove(item);
                if (removed) _parent?.RemoveTabInternal(item);
                return removed;
            }
            catch { return false; }
        }

        public void RemoveAt(int index)
        {
            try
            {
                if (index < 0 || index >= _items.Count) return;
                var item = _items[index];
                _items.RemoveAt(index);
                _parent?.RemoveTabInternal(item);
            }
            catch { }
        }

        public void Clear()
        {
            try
            {
                var itemsCopy = new List<RJTabPage>(_items);
                _items.Clear();
                foreach (var item in itemsCopy)
                    _parent?.RemoveTabInternal(item);
            }
            catch { }
        }

        public bool Contains(RJTabPage item) => _items?.Contains(item) ?? false;
        public void CopyTo(RJTabPage[] array, int arrayIndex) => _items?.CopyTo(array, arrayIndex);
        public int IndexOf(RJTabPage item) => _items?.IndexOf(item) ?? -1;

        public IEnumerator<RJTabPage> GetEnumerator() => _items?.GetEnumerator() ?? new List<RJTabPage>().GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
    }

    /// <summary>
    /// محرر مجموعة آمن للـ Designer
    /// </summary>
    public class SuperSafeTabCollectionEditor : CollectionEditor
    {
        public SuperSafeTabCollectionEditor(Type type) : base(type) { }

        protected override Type CreateCollectionItemType() => typeof(RJTabPage);
        protected override Type[] CreateNewItemTypes() => new Type[] { typeof(RJTabPage) };

        protected override object CreateInstance(Type itemType)
        {
            try
            {
                if (itemType == typeof(RJTabPage))
                {
                    var tab = new RJTabPage($"TabPage{GetSafeItemCount() + 1}")
                    {
                        BackColor = System.Drawing.Color.FromArgb(70, 70, 70),
                        ForeColor = System.Drawing.Color.White,
                        IconChar = IconChar.None
                    };
                    return tab;
                }
                return base.CreateInstance(itemType);
            }
            catch
            {
                return new RJTabPage("TabPage");
            }
        }

        private int GetSafeItemCount()
        {
            try
            {
                if (Context?.Instance is SuperSafeRJTabControl tabControl)
                    return tabControl.TabCount;
                return 0;
            }
            catch { return 0; }
        }

        protected override string GetDisplayText(object value)
        {
            try
            {
                if (value is RJTabPage tab)
                    return !string.IsNullOrEmpty(tab.Text) ? tab.Text : $"TabPage{tab.GetHashCode()}";
                return base.GetDisplayText(value);
            }
            catch
            {
                return "TabPage";
            }
        }

        protected override bool CanRemoveInstance(object value) => true;
        protected override bool CanSelectMultipleInstances() => false;
    }
}
