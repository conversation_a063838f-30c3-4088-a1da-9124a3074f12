using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Translates Lambda expressions to SQL
    /// </summary>
    public static class ExpressionTranslator
    {
        /// <summary>
        /// Translate WHERE expression to SQL
        /// </summary>
        public static string Translate(Expression expression, string? tablePrefix = null)
        {
            return TranslateExpression(expression, tablePrefix);
        }

        /// <summary>
        /// Translate SELECT expression to SQL
        /// </summary>
        public static string TranslateSelect(Expression expression)
        {
            if (expression is LambdaExpression lambda)
            {
                if (lambda.Body is NewExpression newExpr)
                {
                    var members = new List<string>();
                    for (int i = 0; i < newExpr.Arguments.Count; i++)
                    {
                        var arg = newExpr.Arguments[i];
                        var member = newExpr.Members?[i];
                        var columnName = TranslateExpression(arg);
                        var alias = member?.Name ?? $"Column{i}";
                        members.Add($"{columnName} AS {alias}");
                    }
                    return string.Join(", ", members);
                }
                else if (lambda.Body is MemberExpression memberExpr)
                {
                    return GetColumnName(memberExpr);
                }
                else if (lambda.Body is MethodCallExpression methodCall)
                {
                    return TranslateMethodCall(methodCall);
                }
            }

            return "*";
        }

        /// <summary>
        /// Translate ORDER BY expression to SQL
        /// </summary>
        public static string TranslateOrderBy(Expression expression)
        {
            if (expression is LambdaExpression lambda)
            {
                if (lambda.Body is MemberExpression memberExpr)
                {
                    return GetColumnName(memberExpr);
                }
                else if (lambda.Body is UnaryExpression unaryExpr && unaryExpr.Operand is MemberExpression member)
                {
                    return GetColumnName(member);
                }
            }

            throw new NotSupportedException($"Expression type {expression.GetType()} not supported in ORDER BY");
        }

        /// <summary>
        /// Translate GROUP BY expression to SQL
        /// </summary>
        public static string TranslateGroupBy(Expression expression)
        {
            if (expression is LambdaExpression lambda)
            {
                if (lambda.Body is MemberExpression memberExpr)
                {
                    return GetColumnName(memberExpr);
                }
                else if (lambda.Body is UnaryExpression unaryExpr && unaryExpr.Operand is MemberExpression member)
                {
                    return GetColumnName(member);
                }
            }

            throw new NotSupportedException($"Expression type {expression.GetType()} not supported in GROUP BY");
        }

        /// <summary>
        /// Main expression translation method
        /// </summary>
        private static string TranslateExpression(Expression expression, string? tablePrefix = null)
        {
            switch (expression)
            {
                case LambdaExpression lambda:
                    return TranslateExpression(lambda.Body, tablePrefix);

                case BinaryExpression binary:
                    return TranslateBinaryExpression(binary, tablePrefix);

                case MemberExpression member:
                    return GetColumnName(member, tablePrefix);

                case ConstantExpression constant:
                    return FormatValue(constant.Value);

                case MethodCallExpression methodCall:
                    return TranslateMethodCall(methodCall, tablePrefix);

                case UnaryExpression unary:
                    return TranslateUnaryExpression(unary, tablePrefix);

                default:
                    throw new NotSupportedException($"Expression type {expression.GetType()} not supported");
            }
        }

        /// <summary>
        /// Translate binary expressions (==, !=, >, <, etc.)
        /// </summary>
        private static string TranslateBinaryExpression(BinaryExpression binary, string? tablePrefix = null)
        {
            var left = TranslateExpression(binary.Left, tablePrefix);
            var right = TranslateExpression(binary.Right, tablePrefix);

            var op = binary.NodeType switch
            {
                ExpressionType.Equal => "=",
                ExpressionType.NotEqual => "!=",
                ExpressionType.GreaterThan => ">",
                ExpressionType.GreaterThanOrEqual => ">=",
                ExpressionType.LessThan => "<",
                ExpressionType.LessThanOrEqual => "<=",
                ExpressionType.AndAlso => "AND",
                ExpressionType.OrElse => "OR",
                ExpressionType.Add => "+",
                ExpressionType.Subtract => "-",
                ExpressionType.Multiply => "*",
                ExpressionType.Divide => "/",
                _ => throw new NotSupportedException($"Binary operator {binary.NodeType} not supported")
            };

            return $"({left} {op} {right})";
        }

        /// <summary>
        /// Translate method calls (StartsWith, EndsWith, Contains, etc.)
        /// </summary>
        private static string TranslateMethodCall(MethodCallExpression methodCall, string? tablePrefix = null)
        {
            var methodName = methodCall.Method.Name;
            var objectExpr = methodCall.Object;

            switch (methodName)
            {
                case "StartsWith":
                    var column1 = TranslateExpression(objectExpr!, tablePrefix);
                    var value1 = TranslateExpression(methodCall.Arguments[0], tablePrefix);
                    return $"{column1} LIKE {value1.TrimEnd('\'')}%'";

                case "EndsWith":
                    var column2 = TranslateExpression(objectExpr!, tablePrefix);
                    var value2 = TranslateExpression(methodCall.Arguments[0], tablePrefix);
                    return $"{column2} LIKE '%{value2.Trim('\'')}\'";

                case "Contains" when objectExpr != null:
                    var column3 = TranslateExpression(objectExpr, tablePrefix);
                    var value3 = TranslateExpression(methodCall.Arguments[0], tablePrefix);
                    return $"{column3} LIKE '%{value3.Trim('\'')}%'";

                case "Contains" when objectExpr == null: // Collection.Contains
                    var member = TranslateExpression(methodCall.Arguments[0], tablePrefix);
                    var collection = methodCall.Object;
                    if (collection is ConstantExpression constExpr && constExpr.Value is System.Collections.IEnumerable enumerable)
                    {
                        var values = new List<string>();
                        foreach (var item in enumerable)
                        {
                            values.Add(FormatValue(item));
                        }
                        return $"{member} IN ({string.Join(", ", values)})";
                    }
                    break;

                case "ToString":
                    var column4 = TranslateExpression(objectExpr!, tablePrefix);
                    return $"CAST({column4} AS TEXT)";

                default:
                    // Handle Sql static methods
                    if (methodCall.Method.DeclaringType?.Name == "Sql")
                    {
                        return TranslateSqlMethod(methodCall, tablePrefix);
                    }
                    break;
            }

            throw new NotSupportedException($"Method {methodName} not supported");
        }

        /// <summary>
        /// Translate Sql static methods (Count, Max, Min, etc.)
        /// </summary>
        private static string TranslateSqlMethod(MethodCallExpression methodCall, string? tablePrefix = null)
        {
            var methodName = methodCall.Method.Name;

            switch (methodName)
            {
                case "Count":
                    var arg = methodCall.Arguments.FirstOrDefault();
                    if (arg is ConstantExpression constExpr && constExpr.Value?.ToString() == "*")
                        return "COUNT(*)";
                    return $"COUNT({TranslateExpression(arg!, tablePrefix)})";

                case "CountDistinct":
                    return $"COUNT(DISTINCT {TranslateExpression(methodCall.Arguments[0], tablePrefix)})";

                case "Max":
                    return $"MAX({TranslateExpression(methodCall.Arguments[0], tablePrefix)})";

                case "Min":
                    return $"MIN({TranslateExpression(methodCall.Arguments[0], tablePrefix)})";

                case "Sum":
                    return $"SUM({TranslateExpression(methodCall.Arguments[0], tablePrefix)})";

                case "Avg":
                    return $"AVG({TranslateExpression(methodCall.Arguments[0], tablePrefix)})";

                case "In":
                    var column = TranslateExpression(methodCall.Arguments[0], tablePrefix);
                    var values = new List<string>();
                    for (int i = 1; i < methodCall.Arguments.Count; i++)
                    {
                        values.Add(TranslateExpression(methodCall.Arguments[i], tablePrefix));
                    }
                    return $"{column} IN ({string.Join(", ", values)})";

                default:
                    throw new NotSupportedException($"Sql method {methodName} not supported");
            }
        }

        /// <summary>
        /// Translate unary expressions (NOT, etc.)
        /// </summary>
        private static string TranslateUnaryExpression(UnaryExpression unary, string? tablePrefix = null)
        {
            switch (unary.NodeType)
            {
                case ExpressionType.Not:
                    return $"NOT ({TranslateExpression(unary.Operand, tablePrefix)})";

                case ExpressionType.Convert:
                    return TranslateExpression(unary.Operand, tablePrefix);

                default:
                    throw new NotSupportedException($"Unary operator {unary.NodeType} not supported");
            }
        }

        /// <summary>
        /// Get column name from member expression
        /// </summary>
        private static string GetColumnName(MemberExpression member, string? tablePrefix = null)
        {
            var columnName = member.Member.Name;

            if (!string.IsNullOrEmpty(tablePrefix))
                return $"{tablePrefix}.{columnName}";

            return columnName;
        }

        /// <summary>
        /// Format value for SQL
        /// </summary>
        private static string FormatValue(object? value)
        {
            if (value == null)
                return "NULL";

            if (value is string str)
                return $"'{str.Replace("'", "''")}'";

            if (value is DateTime dt)
                return $"'{dt:yyyy-MM-dd HH:mm:ss}'";

            if (value is bool b)
                return b ? "1" : "0";

            return value.ToString() ?? "NULL";
        }
    }

    /// <summary>
    /// Static Sql helper methods for use in expressions
    /// </summary>
    public static class Sql
    {
        public static int Count(string expression) => throw new NotImplementedException("Use in expressions only");
        public static int CountDistinct<T>(T column) => throw new NotImplementedException("Use in expressions only");
        public static T Max<T>(T column) => throw new NotImplementedException("Use in expressions only");
        public static T Min<T>(T column) => throw new NotImplementedException("Use in expressions only");
        public static T Sum<T>(T column) => throw new NotImplementedException("Use in expressions only");
        public static T Avg<T>(T column) => throw new NotImplementedException("Use in expressions only");
        public static bool In<T>(T column, params T[] values) => throw new NotImplementedException("Use in expressions only");
    }
}
