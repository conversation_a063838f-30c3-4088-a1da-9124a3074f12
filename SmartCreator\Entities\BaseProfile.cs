﻿using SmartCreator.Data;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    [UniqueConstraint("Sn_Name", "Rb")]
    public class BaseProfile
    {

        [Required,Unique,AutoIncrement,PrimaryKey]
        public int Id { get; set; }

        //[Required]
        public string IdHX { get; set; }

        public double Sn { get; set; }

        [Required]
        public string Sn_Name { get; set; }

        [Required, DisplayName("الاسم")]
        public string Name { get; set; }
  
        [Default(0), DisplayName("الصلاحية")] 
        public double Validity { get; set; } = 0; // in days
        
        [Computed, DefaultValue("0d")] 
        public string Str_Validity { get; set; } = "0d"; // in days

        //public string Validity_str { get; set; }

       
        [Default(0), DisplayName("سعر البيع")]
        public float Price { get; set; } = 0;
        
        [DefaultValue("0"), DisplayName("السعر علي الكرت")]
        public string Price_Disply { get; set; } = "";

        [Default(0)]
        public int Is_percentage { get; set; }= 0;

        [Default(0),DisplayName("النسبة")]
        public float Percentage { get; set; } = 0;

        [Default(0), DisplayName("طريقة حساب النسبة")]
        public int PercentageType { get; set; } = 0;
        
        [DisplayName("عدد المستخدمين")]
        public string SharedUsers { get; set; } = "1";

        [DisplayName("عدد الكروت المرتبطه")]
        public double CountCards { get; set; } = 0;

        [Default(0)]
        public int DeleteFromServer { get; set; } = 0;
        
        [Required]
        public string Rb { get; set; }

    }
}
