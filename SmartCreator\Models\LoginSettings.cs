﻿using System;

namespace SmartCreator.Models
{
    /// <summary>
    /// نموذج إعدادات تسجيل الدخول
    /// </summary>
    public class LoginSettings
    {
        /// <summary>
        /// عنوان IP للراوتر
        /// </summary>
        public string IpAddress { get; set; } = "***********";

        /// <summary>
        /// منفذ الاتصال
        /// </summary>
        public int Port { get; set; } = 8728;

        /// <summary>
        /// اسم المستخدم
        /// </summary>
        public string Username { get; set; } = "admin";

        /// <summary>
        /// كلمة المرور (لا تُحفظ لأسباب أمنية)
        /// </summary>
        public string Password { get; set; } = "";

        /// <summary>
        /// كلمة المرور المشفرة بـ Base64 (للحفظ الآمن)
        /// </summary>
        public string PasswordBase64 { get; set; } = "";

        /// <summary>
        /// تذكر بيانات الاعتماد
        /// </summary>
        public bool RememberCredentials { get; set; } = false;

        /// <summary>
        /// استخدام SSL
        /// </summary>
        public bool UseSSL { get; set; } = false;

        /// <summary>
        /// مهلة الاتصال بالمللي ثانية
        /// </summary>
        public int ConnectionTimeout { get; set; } = 5000;

        /// <summary>
        /// آخر وقت تسجيل دخول ناجح
        /// </summary>
        public DateTime LastLoginTime { get; set; } = DateTime.MinValue;

        /// <summary>
        /// عدد محاولات تسجيل الدخول
        /// </summary>
        public int LoginAttempts { get; set; } = 0;

        /// <summary>
        /// حفظ كلمة المرور (مشفرة)
        /// </summary>
        public bool SavePassword { get; set; } = false;

        /// <summary>
        /// تسجيل دخول تلقائي
        /// </summary>
        public bool AutoLogin { get; set; } = false;

        /// <summary>
        /// إنشاء نسخة من الإعدادات
        /// </summary>
        public LoginSettings Clone()
        {
            return new LoginSettings
            {
                IpAddress = this.IpAddress,
                Port = this.Port,
                Username = this.Username,
                Password = this.Password,
                RememberCredentials = this.RememberCredentials,
                UseSSL = this.UseSSL,
                ConnectionTimeout = this.ConnectionTimeout,
                LastLoginTime = this.LastLoginTime,
                LoginAttempts = this.LoginAttempts,
                SavePassword = this.SavePassword,
                AutoLogin = this.AutoLogin
            };
        }

        /// <summary>
        /// التحقق من صحة الإعدادات
        /// </summary>
        public bool IsValid()
        {
            return !string.IsNullOrWhiteSpace(IpAddress) &&
                   Port > 0 && Port <= 65535 &&
                   !string.IsNullOrWhiteSpace(Username) &&
                   ConnectionTimeout > 0;
        }

        /// <summary>
        /// الحصول على عنوان الاتصال الكامل
        /// </summary>
        public string GetConnectionString()
        {
            var protocol = UseSSL ? "ssl" : "api";
            return $"{protocol}://{IpAddress}:{Port}";
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        public void Reset()
        {
            IpAddress = "*************";
            Port = 8728;
            Username = "admin";
            Password = "";
            RememberCredentials = false;
            UseSSL = false;
            ConnectionTimeout = 5000;
            LastLoginTime = DateTime.MinValue;
            LoginAttempts = 0;
            SavePassword = false;
            AutoLogin = false;
        }

        /// <summary>
        /// تحديث آخر وقت تسجيل دخول
        /// </summary>
        public void UpdateLastLoginTime()
        {
            LastLoginTime = DateTime.Now;
        }

        /// <summary>
        /// زيادة عدد محاولات تسجيل الدخول
        /// </summary>
        public void IncrementLoginAttempts()
        {
            LoginAttempts++;
        }

        /// <summary>
        /// إعادة تعيين عدد محاولات تسجيل الدخول
        /// </summary>
        public void ResetLoginAttempts()
        {
            LoginAttempts = 0;
        }

        /// <summary>
        /// تحويل إلى نص للعرض
        /// </summary>
        public override string ToString()
        {
            return $"{Username}@{IpAddress}:{Port} (SSL: {UseSSL})";
        }

        #region Password Encryption Methods

        /// <summary>
        /// تشفير كلمة المرور إلى Base64
        /// </summary>
        /// <param name="password">كلمة المرور الأصلية</param>
        public void SetPasswordBase64(string password)
        {
            if (string.IsNullOrEmpty(password))
            {
                this.PasswordBase64 = "";
                return;
            }

            try
            {
                var bytes = System.Text.Encoding.UTF8.GetBytes(password);
                this.PasswordBase64 = Convert.ToBase64String(bytes);
            }
            catch
            {
                this.PasswordBase64 = "";
            }
        }

        /// <summary>
        /// فك تشفير كلمة المرور من Base64
        /// </summary>
        /// <returns>كلمة المرور الأصلية</returns>
        public string GetPasswordFromBase64()
        {
            if (string.IsNullOrEmpty(this.PasswordBase64))
                return "";

            try
            {
                var bytes = Convert.FromBase64String(this.PasswordBase64);
                return System.Text.Encoding.UTF8.GetString(bytes);
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// تحديث كلمة المرور وتشفيرها
        /// </summary>
        /// <param name="password">كلمة المرور الجديدة</param>
        public void UpdatePassword(string password)
        {
            this.Password = password; // للاستخدام المؤقت
            SetPasswordBase64(password); // للحفظ الآمن
        }

        /// <summary>
        /// تحميل كلمة المرور من التشفير
        /// </summary>
        public void LoadPasswordFromBase64()
        {
            this.Password = GetPasswordFromBase64();
        }

        /// <summary>
        /// مسح كلمة المرور من الذاكرة
        /// </summary>
        public void ClearPassword()
        {
            this.Password = "";
            this.PasswordBase64 = "";
        }

        #endregion

    }

    /// <summary>
    /// نموذج إعدادات الاتصال المتقدمة
    /// </summary>
    public class AdvancedConnectionSettings
    {
        /// <summary>
        /// عدد محاولات إعادة الاتصال
        /// </summary>
        public int RetryAttempts { get; set; } = 3;

        /// <summary>
        /// فترة الانتظار بين المحاولات (بالثواني)
        /// </summary>
        public int RetryDelay { get; set; } = 2;

        /// <summary>
        /// تفعيل Keep Alive
        /// </summary>
        public bool EnableKeepAlive { get; set; } = true;

        /// <summary>
        /// فترة Keep Alive (بالثواني)
        /// </summary>
        public int KeepAliveInterval { get; set; } = 30;

        /// <summary>
        /// تفعيل ضغط البيانات
        /// </summary>
        public bool EnableCompression { get; set; } = false;

        /// <summary>
        /// حجم Buffer للقراءة
        /// </summary>
        public int ReadBufferSize { get; set; } = 8192;

        /// <summary>
        /// حجم Buffer للكتابة
        /// </summary>
        public int WriteBufferSize { get; set; } = 8192;

        /// <summary>
        /// تفعيل التشفير المتقدم
        /// </summary>
        public bool EnableAdvancedEncryption { get; set; } = false;

        /// <summary>
        /// مستوى السجل
        /// </summary>
        public string LogLevel { get; set; } = "Info";

        /// <summary>
        /// تفعيل حفظ السجل في ملف
        /// </summary>
        public bool EnableFileLogging { get; set; } = false;

        /// <summary>
        /// مسار ملف السجل
        /// </summary>
        public string LogFilePath { get; set; } = "logs/connection.log";
    }

    /// <summary>
    /// نموذج إعدادات الواجهة
    /// </summary>
    public class UISettings
    {
        /// <summary>
        /// اللغة المحددة
        /// </summary>
        public string Language { get; set; } = "ar";

        /// <summary>
        /// السمة المحددة
        /// </summary>
        public string Theme { get; set; } = "Light";

        /// <summary>
        /// حجم الخط
        /// </summary>
        public int FontSize { get; set; } = 9;

        /// <summary>
        /// نوع الخط
        /// </summary>
        public string FontFamily { get; set; } = "Segoe UI";

        /// <summary>
        /// تفعيل الرسوم المتحركة
        /// </summary>
        public bool EnableAnimations { get; set; } = true;

        /// <summary>
        /// تفعيل الأصوات
        /// </summary>
        public bool EnableSounds { get; set; } = true;

        /// <summary>
        /// إظهار نصائح الأدوات
        /// </summary>
        public bool ShowTooltips { get; set; } = true;

        /// <summary>
        /// حفظ موقع النوافذ
        /// </summary>
        public bool SaveWindowPositions { get; set; } = true;

        /// <summary>
        /// تفعيل الإشعارات
        /// </summary>
        public bool EnableNotifications { get; set; } = true;

        /// <summary>
        /// تفعيل التحديث التلقائي
        /// </summary>
        public bool EnableAutoRefresh { get; set; } = true;

        /// <summary>
        /// فترة التحديث التلقائي (بالثواني)
        /// </summary>
        public int AutoRefreshInterval { get; set; } = 30;

    }
}
