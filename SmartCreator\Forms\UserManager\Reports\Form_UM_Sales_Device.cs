﻿//using DevComponents.DotNetBar.Controls;
//using DevExpress.Utils.Filtering.Internal;
using iTextSharp.text.pdf;
using iTextSharp.text;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.ComponentModel.Design.ObjectSelectorEditor;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Tab;
using System.Windows.Documents;
using SmartCreator.ViewModels;
using System.Threading;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_UM_Sales_Device : RJChildForm
    {
        string Server_Type = "UM";
        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;

        private DateTime dateFrom_detail = DateTime.Now;
        private DateTime dateTo_detail = DateTime.Now;
        public Form_UM_Sales_Device(string server_Type="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv2.RightToLeft = RightToLeft.No;
            }

            Server_Type = server_Type;
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            this.Text = "تقارير الاجهزة يوزمنجر";
            if (server_Type == "HS")
                this.Text = "تقارير الاجهزة هوتسبوت";

            Spanel.Width = 0;
            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel1.Customizable = false;
                pnl_side_sn.Customizable = false;
            }

            if (!UIAppearance.Language_ar)
            {
                this.Text = "Reports UserManager Device";
                this.dgv.RightToLeft = RightToLeft.No;
                if (server_Type == "HS")
                {
                    this.Text = "Reports Hotspot Device";

                }

            }

            set_font();

            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00").AddDays(-1);
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");

            dateFrom_detail = rjDateTime_From.Value;
            dateTo_detail = rjDateTime_To.Value;


            CheckBox_To_Date.Check = true;

            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
            CBox_SN_Compar.SelectedIndex = 3;
            Cbox_View.SelectedIndex = 0;
            Cbox_View.label.RightToLeft = RightToLeft.Yes;
            Cbox_View.label.TextAlign=ContentAlignment.MiddleLeft;
            //dgv.BorderStyle = BorderStyle.FixedSingle;
            //dgv2.BorderStyle = BorderStyle.FixedSingle;
            rjTextBox1.ForeColor = utils.Dgv_DarkColor;
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
        }
        private void set_font()
        {
            //return;
            dgv.AllowUserToOrderColumns = true;
            dgv2.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv2.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv2.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            //dgv2.ColumnHeadersHeight = 45;

            Cbox_View.Font= Program.GetCustomFont(Resources.DroidSansArabic, 8, FontStyle.Regular);

            rjLabel3.Font=  rjLabel2.Font= rjLabel21.Font=
                //rjDateTime_From.Font= 
                //rjDateTime_To.Font=
                ToggleButton_Detail.Font=
                ToggleButton_Monthly.Font=
                jToggleButton_Year.Font= 
                check_with_Commi.Font=
               rjLabel16.Font= rjLabel9.Font=
               rjLabel15.Font= rjLabel4.Font=
               rjLabel14.Font= 
               rjLabel1.Font= 
                Program.GetCustomFont(Resources.DroidSansArabic, 9  , FontStyle.Regular);
            btn_.Font=btn_apply.Font=btn_Filter.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 10 , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.dgv_textSize(dgv2);
            utils.item_Contrlol_textSize(dmAll_Cards);
        }
        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }

        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }

        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width > 50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = utils.Control_Mesur_DPI(260);
            }
        }
        void sideMenuFix()
        {
            //if (Spanel.Width >= 200)
            //{
            //    Spanel.Width = 0;
               
            //    panel1.Location = new Point(10, Spanel.Location.Y);
            //    //dgv.Location = new Point(10, Spanel.Location.Y);
            //    //dgv2.Location = new Point(10, Spanel.Location.Y);
            //    //rjPanel12.Location = new Point(10, Spanel.Location.Y);


            //    panel1.Width = pnlClientArea.Width - 30;
            //    //dgv.Width = pnlClientArea.Width - 30;
            //    //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

            //    //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width-pnlClientArea.Padding.Left-pnlClientArea.Padding.Right-10;
            //    //rjPanel_back_side.Location = new Point(pnlClientArea.Width - rjPanel_back_side.Width - 10, panel1.Location.Y);

            //}
            //else
            //{
                Spanel.Width = 260;
                panel1.Width = pnlClientArea.Width - Spanel.Width - 10;
                panel1.Location = new Point(Spanel.Width + 10, panel1.Location.Y-2);
                //panel1.Location = new Point(Spanel.Width + 20, Spanel.Location.Y);

                //panel1.Width =  705;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width;
                //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);

            //}
            Spanel.Refresh();
            panel1.Refresh();
            //rjPanel2.Refresh();
            //rjPanel_btns.Refresh();

        }


        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            if (ToggleButton_Detail.Checked)
            {
                dgv.ContextMenuStrip = dmAll_Cards;
                FirstLoad = true;
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;
                FirstLoad = false;

                //pnl_size_time_count.Visible = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Detail.Checked = true;
                    FirstLoad = false;
                }
            }
            string today = DateTime.Now.ToString("MM-dd-yyyy");
            dateFrom_detail = Convert.ToDateTime(today + "  00:00:00");

            try { Sub_LocadData(dgv.CurrentRow.Cells["جهاز البث"].Value.ToString()); } catch { }
        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            dgv.ContextMenuStrip = null;
            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {

                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                //pnl_size_time_count.Visible = true;
                FirstLoad = false;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Monthly.Checked = true;
                    FirstLoad = false;
                }
            }

            try
            {
                DateTime firstDayOfMonth;
                DateTime lastDayOfMonth;
                utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                string first = firstDayOfMonth.ToString("MM-dd-yyyy");
                string last = lastDayOfMonth.ToString("MM-dd-yyyy");

                dateFrom_detail = Convert.ToDateTime(first + "  00:00:00");
                dateTo_detail = Convert.ToDateTime(last + "  23:59:59");

                try { Sub_LocadData(dgv.CurrentRow.Cells["جهاز البث"].Value.ToString()); } catch { }
            }
            catch { }
        }


        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            if (jToggleButton_Year.Checked)
            {

                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                //pnl_size_time_count.Visible = true;
                FirstLoad = false;

            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                {
                    FirstLoad = true;
                    jToggleButton_Year.Checked = true;
                    FirstLoad = false;
                }
            }

            dateFrom_detail = new DateTime(DateTime.Now.Year, 1, 1);
            dateTo_detail = new DateTime(DateTime.Now.Year, 12, 31);
            //get_report();
            try { Sub_LocadData(dgv.CurrentRow.Cells["جهاز البث"].Value.ToString()); } catch { }

        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private DataTable dt_ByDetails()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("جهاز البث", typeof(string));
            dt.Columns.Add("اجمالي الوقت", typeof(string));
            dt.Columns.Add("اجمالي الاستهلاك", typeof(string));
            dt.Columns.Add("مبلغ اول دخول", typeof(string));
            dt.Columns.Add("كروت اول دخول", typeof(double));
            dt.Columns.Add("اجمالي الكروت", typeof(double));
            dt.Columns.Add("اجمالي الجلسات", typeof(double));

            dt.Columns.Add("UptimeUsed", typeof(double));
            dt.Columns.Add("DownloadUsed", typeof(double));
            dt.Columns.Add("Price", typeof(double));
            //dt.Columns.Add("count", typeof(double));
            return dt;
        }
        private DataTable dt_Monthly()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("التاريخ", typeof(string));
            //dt.Columns.Add("جهاز البث", typeof(string));
            dt.Columns.Add("اجمالي الوقت", typeof(string));
            dt.Columns.Add("اجمالي الاستهلاك", typeof(string));
            dt.Columns.Add("مبلغ اول دخول", typeof(string));
            dt.Columns.Add("كروت اول دخول", typeof(double));
            dt.Columns.Add("اجمالي الكروت", typeof(double));
            dt.Columns.Add("اجمالي الجلسات", typeof(double));

            dt.Columns.Add("UptimeUsed", typeof(double));
            dt.Columns.Add("DownloadUsed", typeof(double));
            dt.Columns.Add("Price", typeof(double));

            return dt;
        }
        private string ColumnShow = "";
        private void get_report()
        {
            dgv.DataSource = null;
            string Query_firstUse = condition_detail_firstUse();
            string Query_firstUse_ForUser = condition_detail_firstUse_For_UmUser();


            string Price = "Price";
            string TableUser = "UmUser";
            string TablePyment = "UmPyment";
            string TableSession = "UmSession";

            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }


            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            
                string FromUser = "";
                string Qury = $"{FromUser}  SELECT " +
                    $"s.NasPortId NasPortId" +
                    $", sum(s.UpTime) UptimeUsed" +
                    $",sum(s.BytesDownload + s.BytesUpload) DownloadUsed  " +
                    $",COUNT(DISTINCT s.Fk_Sn_Name)  CardCount" +
                    $",count(s.Fk_Sn_Name) CountSession " +
                    $"FROM {TableSession} s" +
                    $" {Query_firstUse} GROUP BY s.NasPortId ORDER by NasPortId";

                string Qury2 = $"SELECT u.NasPortId , sum(p.{Price}) Price ,count(u.Sn_Name) CountFirstLogin FROM {TableUser} u " +
                    $"LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name  " +
                    $" {Query_firstUse_ForUser} " +
                    $"GROUP BY u.NasPortId ORDER by u.NasPortId";
               
                try
                {

                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();

                    DataTable dt = dt_ByDetails();
                    DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);
                    DataTable dt_first_use = Local_DA.RunSqlCommandAsDatatable(Qury2);

                    stopwatch.Stop();

                    foreach (DataRow itm in tbFound.Rows)
                    {
                        DataRow row = dt.NewRow();
                        row["جهاز البث"] = itm["NasPortId"].ToString();
                        row["اجمالي الوقت"] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm["UptimeUsed"]));
                        row["اجمالي الاستهلاك"] = utils.ConvertSize_Get_InArabic(itm["DownloadUsed"].ToString());
                        row["مبلغ اول دخول"] = "0";
                        row["كروت اول دخول"] = "0";
                        row["اجمالي الكروت"] = itm["CardCount"];
                        row["اجمالي الجلسات"] = itm["CountSession"];
                        row["UptimeUsed"] = itm["UptimeUsed"];
                        row["DownloadUsed"] = itm["DownloadUsed"];
                        row["Price"] = "0";
                        foreach (DataRow ItmFirst in dt_first_use.Rows)
                        {
                            if (itm["NasPortId"].ToString() == ItmFirst["NasPortId"].ToString())
                            {
                                row["مبلغ اول دخول"] = String.Format("{0:n0}", double.TryParse(ItmFirst["Price"].ToString(), out double xx) ? Convert.ToDouble(ItmFirst["Price"].ToString()) : 0);
                                row["كروت اول دخول"] = ItmFirst["CountFirstLogin"];
                                row["Price"] = ItmFirst["Price"];
                                continue;
                            }
                        }
                        dt.Rows.Add(row);
                    }
                    dgv.DataSource = dt;
                    dgv.Columns["UptimeUsed"].Visible = false;
                    dgv.Columns["DownloadUsed"].Visible = false;
                    dgv.Columns["Price"].Visible = false;
                    txt_countDevice.Text = dgv.Rows.Count.ToString();
                    //loadDgvState();

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                //update_select_DGV();

            

        }

        private void update_select_DGV2()
        {
            try
            {
                string ListAll = dgv2.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv2.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        private string condition_detail_firstUse(bool forDetail=false)
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if(forDetail)
            {
                str_from_Date = (dateFrom_detail).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                str_to_Date = (dateTo_detail.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

                //if (CheckBox_To_Date.Checked)
                    str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            }
            conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";


     
            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    { nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",s.NasPortId"; }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    { radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",s.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }
        private string condition_Session_By_Days_for_firstUse(bool SubQuery = false)
        {
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if (SubQuery)
            {
                DateTime d = Convert.ToDateTime(dgv.CurrentRow.Cells["التاريخ"].Value);

                str_from_Date = d.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                str_to_Date = d.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    str_to_Date = d.AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            }
            //if (ToggleButton_Monthly.Checked)
            //{
            //    //DateTime firstDayOfMonth;
            //    //DateTime lastDayOfMonth;
            //    //utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    //From_DT=utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    //To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    //From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    //To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}
            conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                        radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  ";

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                        customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }

        private string condition_detail_firstUse_For_UmUser(bool forDetail = false)
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if (forDetail)
            {
                str_from_Date = (dateFrom_detail).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                str_to_Date = (dateTo_detail.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

                //if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            }

            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    { nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",u.NasPortId"; }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    { radius = " AND u.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",u.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }

        bool FirstLoad = true;
        private void timer_Tick(object sender, EventArgs e)
        {
            timer.Stop();

            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();

            get_report();
            try
            {
                if (dgv.Rows.Count > 0)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[0].Cells["جهاز البث"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
            //try
            //{
            //    dgv.Rows[0].Selected = true;
            //}
            //catch { }
            FirstLoad = false;
        }

        private void Form_UM_Sales_Device_Load(object sender, EventArgs e)
        {
            timer.Start();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void btn_Fix_Click(object sender, EventArgs e)
        {
            sideMenuFix();
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[e.RowIndex].Cells["جهاز البث"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
        }
        private void Sub_LocadData(string _Sn_Name = "")
        {
            if (string.IsNullOrEmpty(_Sn_Name))
            {
                _Sn_Name = dgv.CurrentRow.Cells["جهاز البث"].Value.ToString();
            }
            dgv2.ColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            rjTextBox1.Text = $"حركة مبيعات الجهاز {_Sn_Name} ";
            if (ToggleButton_Monthly.Checked || jToggleButton_Year.Checked)
            {
                if (Cbox_View.SelectedIndex == 0)
                {
                    Sub_LocadData3(_Sn_Name);
                    return;
                } 
                 
                ToggleButton_Detail.Checked = true; 
            }
             
            try
            {
                dgv2.ContextMenuStrip = null;
                string ColumPrice = "Price";
                string TableUser = "UmUser";
                string TablePyment = "UmPyment";
                string TableSession = "UmSession";

                if (Server_Type == "HS")
                {
                    TableUser = "HSUser";
                    TablePyment = "HsPyment";
                    TableSession = "HsSession";
                }
                if (check_with_Commi.Checked)
                {
                    ColumPrice = "TotalPrice";
                    //dgv.Columns["TotalPrice"].Visible = true;
                    //dgv.Columns["Price"].Visible = false;
                }
                string GroupBy = "";
                string Query_conditon = condition_detail_firstUse();
                string Qury = "";
                string Q_ByName = $"and s.NasPortId='{_Sn_Name}'";


               
                if (Cbox_View.SelectedIndex == 0)
                {
                    if(ToggleButton_Detail.Checked)
                        Qury = $"SELECT DISTINCT u.* FROM {TableUser} u INNER JOIN {TableSession} s ON u.Sn_Name=s.Fk_Sn_Name  {Query_conditon} {Q_ByName} {GroupBy} ";

                    dgv2.DataSource = Local_DA.Load<UmUser>(Qury);
                    dgv2.ContextMenuStrip = dmAll_Cards;
                    dgv2.Refresh();

                }
                else if (Cbox_View.SelectedIndex == 1)
                {
                    Qury = $"SELECT  s.* FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
                    dgv2.DataSource = Local_DA.Load<UmSession>(Qury); 

                }
                else if (Cbox_View.SelectedIndex == 2 )
                {
                    Qury = $"SELECT  u.* FROM {TableUser} u   {condition_detail_firstUse_For_UmUser()} and u.NasPortId='{_Sn_Name}' {GroupBy} ";
                    dgv2.DataSource = Local_DA.Load<UmUser>(Qury);
                    dgv2.ContextMenuStrip = dmAll_Cards;
                    dgv2.Refresh();

                }
                //if (Toggle_By_Group.Checked)
                //    Qury = $"SELECT s.*, sum(s.Uptime) Uptime,sum(s.BytesDownload) BytesDownload ,sum(s.BytesUpload) BytesUpload " +
                //$" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy}";

                //Qury = $"SELECT  s.* FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
                //Qury = $"SELECT u.*, sum(s.Uptime) UptimeUsed,sum(s.BytesDownload) DownloadUsed ,sum(s.BytesUpload) UploadUsed,count(s.Fk_Sn_Name) CountSession   " +
                //   $" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {GroupBy}";

                //List<UmUser> umUser = Local_DA.Load<UmUser>(Qury);
                //dgv2.DataSource = umUser;

                loadDgvState();
            }
            catch { }
            //update_select_DGV2();
        }

        double avg = 0; double sum = 0; double sum_download = 0; double sum_uptime = 0; double count_cards = 0; double CountFirstLogin = 0; double CountSession = 0;
        private void Sub_LocadData3(string _Sn_Name = "")
        {
            dgv2.DataSource = null;
            string Query_firstUse = condition_detail_firstUse(true);
            string Query_firstUse_ForUser = condition_detail_firstUse_For_UmUser(true);


            string Price = "Price";
            string TableUser = "UmUser";
            string TablePyment = "UmPyment";
            string TableSession = "UmSession";
            string Q_ByName = $"and s.NasPortId='{_Sn_Name}'";
            string Q_ByName2 = $"and u.NasPortId='{_Sn_Name}'";
            string GroupBy = "";
            string Qury = "";

            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            string fitler = "'%Y-%m-%d'";
            if (jToggleButton_Year.Checked)
                fitler = "'%Y-%m'";
         
              Qury = $"SELECT " +
                $"strftime(" + fitler + ", s.FromTime) Date" +
                $",s.NasPortId NasPortId" +
                $",sum(s.UpTime) UptimeUsed" +
                $",sum(s.BytesDownload + s.BytesUpload) DownloadUsed  " +
                $",COUNT(DISTINCT s.Fk_Sn_Name)  CardCount" +
                $",count(s.Fk_Sn_Name) CountSession " +
                $"FROM {TableSession} s" +
                $" {Query_firstUse} {Q_ByName} GROUP BY strftime(" + fitler + ", s.FromTime), s.NasPortId ORDER by NasPortId";

            string Qury2 = $"SELECT " +
                $"strftime(" + fitler + ", u.FirsLogin) Date" +
                $",u.NasPortId as NasPortId " +
                $",sum(p.{Price}) Price ,count(u.Sn_Name) CountFirstLogin FROM {TableUser} u " +
                $"LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name  " +
                $" {Query_firstUse_ForUser} {Q_ByName2} " +
                 $" GROUP BY strftime(" + fitler + ", u.FirsLogin), u.NasPortId ORDER by NasPortId";

            try
            {
                dgv2.ColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                DataTable dt = dt_Monthly();
                DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);
                DataTable dt_first_use = Local_DA.RunSqlCommandAsDatatable(Qury2);

                stopwatch.Stop();
                avg = 0;sum = 0; sum_download = 0; sum_uptime = 0; count_cards = 0; CountSession = 0; CountFirstLogin = 0;

                foreach (DataRow itm in tbFound.Rows)
                {
                    DataRow row = dt.NewRow();
                    //row["جهاز البث"] = itm["NasPortId"].ToString();
                    row["التاريخ"] = itm["Date"].ToString();
                    row["اجمالي الوقت"] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm["UptimeUsed"]));
                    row["اجمالي الاستهلاك"] = utils.ConvertSize_Get_InArabic(itm["DownloadUsed"].ToString());
                    row["مبلغ اول دخول"] = "0";
                    row["كروت اول دخول"] = "0";
                    row["اجمالي الكروت"] = itm["CardCount"];
                    row["اجمالي الجلسات"] = itm["CountSession"];
                    row["UptimeUsed"] = itm["UptimeUsed"];
                    row["DownloadUsed"] = itm["DownloadUsed"];
                    row["Price"] = "0";

                    sum_download += Convert.ToDouble(itm["DownloadUsed"]);
                    sum_uptime += Convert.ToDouble(itm["UptimeUsed"]);
                    count_cards += Convert.ToDouble(itm["CardCount"]);
                    CountSession += Convert.ToDouble(itm["CountSession"]);

                    foreach (DataRow ItmFirst in dt_first_use.Rows)
                    {
                        if (itm["NasPortId"].ToString() == ItmFirst["NasPortId"].ToString() && itm["Date"].ToString() == ItmFirst["Date"].ToString())
                        {
                            row["مبلغ اول دخول"] = String.Format("{0:n0}", double.TryParse(ItmFirst["Price"].ToString(), out double xx) ? Convert.ToDouble(ItmFirst["Price"].ToString()) : 0);
                            row["كروت اول دخول"] = ItmFirst["CountFirstLogin"];
                            row["Price"] = ItmFirst["Price"];

                            sum += Convert.ToDouble(row["Price"]);
                            CountFirstLogin += Convert.ToDouble(ItmFirst["CountFirstLogin"]);
                            continue;
                        }
                    }
                    dt.Rows.Add(row);
                }
                avg = sum / dgv.Rows.Count;
               //string txt_avg= String.Format("{0:n0}", avg);
                dgv2.DataSource = dt;
                dgv2.Columns["UptimeUsed"].Visible = false;
                dgv2.Columns["DownloadUsed"].Visible = false;
                dgv2.Columns["Price"].Visible = false;
                //txt_countDevice.Text = dgv.Rows.Count.ToString();
                //loadDgvState();

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            //update_select_DGV();



        }

        private void Sub_LocadData_Groupby_date(string _Sn_Name = "")
        {
            try
            {
                dgv2.ContextMenuStrip = null;
                string ColumPrice = "Price";
                string TableUser = "UmUser";
                string TablePyment = "UmPyment";
                string TableSession = "UmSession";

                if (Server_Type == "HS")
                {
                    TableUser = "HSUser";
                    TablePyment = "HsPyment";
                    TableSession = "HsSession";
                }
                if (check_with_Commi.Checked)
                {
                    ColumPrice = "TotalPrice";
                    //dgv.Columns["TotalPrice"].Visible = true;
                    //dgv.Columns["Price"].Visible = false;
                }
                string GroupBy = "";
                string Query_conditon = condition_detail_firstUse();
                string Qury = "";
                string Q_ByName = $"and s.NasPortId='{_Sn_Name}'";


                string fitler = "'%Y-%m-%d'";
                if (jToggleButton_Year.Checked)
                    fitler = "'%Y-%m'";

                if (Cbox_View.SelectedIndex == 0)
                {
 

                    if (ToggleButton_Monthly.Checked)
                    {
                        GroupBy = " group by strftime(" + fitler + ", u.FirsLogin);";
                        
                        Qury = $"SELECT DISTINCT u.* " +
                            $" ,strftime(" + fitler + ", u.FirsLogin) Date " +
                            $"  FROM {TableUser} u INNER JOIN {TableSession} s ON u.Sn_Name=s.Fk_Sn_Name " +
                            $" {Query_conditon} {Q_ByName} {GroupBy} ";
                       
                        Qury = "SELECT " +
                                  "strftime(" + fitler + ", u.FirsLogin) Date," +
                                  $"sum(c.{ColumPrice}) as TotalPrice ," +
                                  "count(u.Sn_Name) as count " +
                                  $"FROM {TableUser} u LEFT JOIN UmPyment c ON u.Sn_Name = c.Fk_Sn_Name " +
                                  //Query_firstUse + " " +
                                  " group by strftime(" + fitler + ", u.FirsLogin);";

                    }
                    dgv2.DataSource = Local_DA.Load<UmUser>(Qury);
                    dgv2.ContextMenuStrip = dmAll_Cards;
                    dgv2.Refresh();

                }
                else if (Cbox_View.SelectedIndex == 1)
                {
                    Qury = $"SELECT  s.* FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
                    dgv2.DataSource = Local_DA.Load<UmSession>(Qury);

                }
                else if (Cbox_View.SelectedIndex == 2)
                {
                    Qury = $"SELECT  u.* FROM {TableUser} u   {condition_detail_firstUse_For_UmUser()} and u.NasPortId='{_Sn_Name}' {GroupBy} ";
                    dgv2.DataSource = Local_DA.Load<UmUser>(Qury);
                    dgv2.ContextMenuStrip = dmAll_Cards;
                    dgv2.Refresh();

                }
                //if (Toggle_By_Group.Checked)
                //    Qury = $"SELECT s.*, sum(s.Uptime) Uptime,sum(s.BytesDownload) BytesDownload ,sum(s.BytesUpload) BytesUpload " +
                //$" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy}";

                //Qury = $"SELECT  s.* FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
                //Qury = $"SELECT u.*, sum(s.Uptime) UptimeUsed,sum(s.BytesDownload) DownloadUsed ,sum(s.BytesUpload) UploadUsed,count(s.Fk_Sn_Name) CountSession   " +
                //   $" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {GroupBy}";

                //List<UmUser> umUser = Local_DA.Load<UmUser>(Qury);
                //dgv2.DataSource = umUser;

                loadDgvState();
            }
            catch { }
            //update_select_DGV2();
        }

        Dgv_Header_Proprties Dgv_State_list = null;
        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if(Cbox_View.SelectedIndex == 1)
            {
                Init_dgv_to_Default();
                return;
            }
            
            if (Cbox_View.SelectedIndex == 1)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Session");
            else if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_RB_Archive");
            //else
            //    DgvState = SqlDataAccess.Get_SourceSaveStateFormsVariable("DgvUserManagerPrcess");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            //dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv2.Columns[dv.Index].Visible = dv.Visable;
                    dgv2.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;
                    dgv2.Columns[dv.Index].Width = dv.Width;
                    foreach (ToolStripMenuItem control in toolStripMenuItem1.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            if (control.Tag != null)
                                if (control.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            }

            try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv2.Columns["IdHX"].Visible = false; } catch { }
            try { dgv2.Columns["Status"].Visible = false; } catch { }
            try { dgv2.Columns["Disabled"].Visible = false; } catch { }

            try { dgv2.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv2.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv2.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["CallerMac"].Visible = false; } catch { }
            try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
            try { dgv2.Columns["CountSession"].Visible = false; } catch { }
        }
        private void Init_dgv_to_Default()
        {
            if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
            {
                try
                {
                    foreach (DataGridViewColumn column in dgv2.Columns)
                    {

                        column.Visible = false;
                    }

                  

                    //dgv.Columns["Sn"].Visible = true;
                    dgv2.Columns["Str_Status"].Visible = true;
                    dgv2.Columns["Str_Status"].DisplayIndex = 0;
                    Status_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["UserName"].Visible = true;
                    dgv.Columns["UserName"].DisplayIndex = 1;
                    UserName_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["ProfileName"].Visible = true;
                    dgv2.Columns["ProfileName"].DisplayIndex = 3;
                    Profile_ToolStripMenuItem.Checked = true;
 
                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);
                    Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_Up_Down"].Visible = true;
                    dgv2.Columns["Str_Up_Down"].DisplayIndex = 7;
                    dgv2.Columns["Str_Up_Down"].Width = utils.Control_Mesur_DPI(190);
                    Str_Up_Down_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["MoneyTotal"].Visible = true;

                    dgv2.Columns["Str_ProfileTimeLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                    dgv2.Columns["Str_ProfileTimeLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTransferLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                    dgv2.Columns["Str_ProfileTransferLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTillTime"].Visible = true;
                    dgv2.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                    dgv2.Columns["Str_ProfileTillTime"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["LastSynDb"].Visible = false;
                    dgv2.Columns["LastSynDb"].DisplayIndex = 11;
                    dgv2.Columns["LastSynDb"].Width = utils.Control_Mesur_DPI(150);
                    //dgv.Columns["SpName"].Visible = true;

                    //dgv.Columns["Comment"].Visible = true;
                    dgv2.Columns["Comment"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["CountProfile"].Visible = true;
                    try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
                    //try { dgv.Columns["Status "].Visible = false; } catch { }
                    //try { dgv.Columns["Disabled "].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Width = 150; } catch { }
                    try { dgv2.Columns["CountSession"].Visible = false; } catch { }
                    try { dgv2.Columns["CountSession"].Width = 150; } catch { }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Init_dgv_to_Default"); }
            }

            else
            {
                try
                {
                    //foreach (DataGridViewColumn column in dgv.Columns)
                    //{
                    //    column.Visible = false;
                    //}

                    dgv2.Columns["UserName"].Visible = false;
                    dgv2.Columns["UserName"].DisplayIndex = 1;

                    dgv2.Columns["FromTime"].Visible = true;
                    dgv2.Columns["FromTime"].DisplayIndex = 2;
                    dgv2.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["TillTime"].Visible = true;
                    dgv2.Columns["TillTime"].DisplayIndex = 3;
                    dgv2.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                    dgv2.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                    dgv2.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["CallingStationId"].Visible = true;
                    dgv2.Columns["CallingStationId"].DisplayIndex = 7;
                    dgv2.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                    dgv2.Columns["IpUser"].Visible = true;
                    dgv2.Columns["IpUser"].DisplayIndex = 8;
                    dgv2.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150);

                    dgv2.Columns["NasPortId"].Visible = true;
                    dgv2.Columns["NasPortId"].DisplayIndex = 9;

                    dgv2.Columns["IpRouter"].Visible = true;
                    dgv2.Columns["IpRouter"].DisplayIndex = 10;
                    dgv2.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                    dgv2.Columns["Sn_Name"].Visible = false;
                    dgv2.Columns["Fk_Sn_Name"].Visible = false;
                    dgv2.Columns["IdHX"].Visible = false;

                }
                catch { }

            }
        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv2.Columns[columnName].Visible = elemnt.Checked;
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
            }
            catch { }
        }
        private void Change_Items_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());

        }

        private void Restor_ColumnToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Init_dgv_to_Default();
        }
        DataGridViewCell ActiveCell = null;
        private void Copy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void dgv2_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv2.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv2[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void dgv2_MouseClick(object sender, MouseEventArgs e)
        {
           
        }

        private void Cbox_View_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (dgv.Rows.Count > 0)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.CurrentRow.Cells["جهاز البث"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
        }

        private void dgv2_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV2();
        }
         bool sort_Uptime = false;
         bool sort_download = false;
         bool sort_Upload = false;
         bool up_down_inByte = false;
         bool sort_Validay = false;
        public void ordercolumn(DataGridView dg, DataGridViewCellMouseEventArgs e)
        {
            //return;
            //dt.Columns.Add("جهاز البث", typeof(string));
            //dt.Columns.Add("اجمالي الوقت", typeof(string));
            //dt.Columns.Add("اجمالي الاستهلاك", typeof(string));
            //dt.Columns.Add("مبلغ اول دخول", typeof(string));
            //dt.Columns.Add("كروت اول دخول", typeof(double));
            //dt.Columns.Add("اجمالي الكروت", typeof(double));
            //dt.Columns.Add("اجمالي الجلسات", typeof(double));

            //dt.Columns.Add("UptimeUsed", typeof(double));
            //dt.Columns.Add("DownloadUsed", typeof(double));
            //dt.Columns.Add("Price", typeof(double));
            try
            {
                int currentColumnIndex = e.ColumnIndex;
                string columnName = dg.Columns[currentColumnIndex].Name;

                if (columnName == "اجمالي الوقت")
                {
                    try
                    {
                        if (sort_Uptime)
                        {
                            sort_Uptime = false;
                            dg.Sort(dg.Columns["UptimeUsed"], ListSortDirection.Ascending);
                        }
                        else
                        {
                            sort_Uptime = true;
                            dg.Sort(dg.Columns["UptimeUsed"], ListSortDirection.Descending);
                        }
                    }
                    catch
                    {
                        //if (sort_Uptime)
                        //{
                        //    sort_Uptime = false;
                        //    dg.Sort(dg.Columns["اجمالي الوقت"], ListSortDirection.Ascending);
                        //}
                        //else
                        //{
                        //    sort_Uptime = true;
                        //    dg.Sort(dg.Columns["اجمالي الوقت"], ListSortDirection.Descending);
                        //}
                    }
                }
                if (columnName == "اجمالي الاستهلاك")
                    if (sort_download)
                    {
                        sort_download = false;
                        dg.Sort(dg.Columns["DownloadUsed"], ListSortDirection.Ascending);
                    }
                    else
                    {
                        sort_download = true;
                        dg.Sort(dg.Columns["DownloadUsed"], ListSortDirection.Descending);
                    }
                if (columnName == "مبلغ اول دخول")
                    if (sort_Upload)
                    {
                        sort_Upload = false;
                        dg.Sort(dg.Columns["Price"], ListSortDirection.Ascending);
                    }
                    else
                    {
                        sort_Upload = true;
                        dg.Sort(dg.Columns["Price"], ListSortDirection.Descending);
                    }
                //if (columnName == "الرفع" || columnName == "اجمالي الرفع")
                //    if (sort_Upload)
                //    {
                //        sort_Upload = false;
                //        dg.Sort(dg.Columns["upload_inByte"], ListSortDirection.Ascending);
                //    }
                //    else
                //    {
                //        sort_Upload = true;
                //        dg.Sort(dg.Columns["upload_inByte"], ListSortDirection.Descending);
                //    }
                //if (columnName == "الصلاحية المتبقية" || columnName == "الصلاحية")
                //    if (sort_Validay)
                //    {
                //        sort_Validay = false;
                //        dg.Sort(dg.Columns["validy_In_Houre"], ListSortDirection.Ascending);
                //    }
                //    else
                //    {
                //        sort_Validay = true;
                //        dg.Sort(dg.Columns["validy_In_Houre"], ListSortDirection.Descending);
                //    }
            }
            catch { }
        }

        private void dgv_ColumnHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            ordercolumn(dgv, e);
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            try
            {
                BindingSource bs = new BindingSource();
                bs.DataSource = dgv.DataSource;
                bs.Filter = string.Format(" [جهاز البث]  LIKE '%{0}%'", txt_search.Text);
                dgv.DataSource = bs;               
            }
            catch { }
        }

        private void btn_Print_Click(object sender, EventArgs e)
        {
            btnPdf_Click2();
        }

        private void btnPdf_Click2()
        {
            if (Radi_Days.Checked)
            {
                Pdf_By_Days();
                return;
            }
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            //if (ToggleButton_Detail.Checked)
                dateHeader = "تقرير الاجهزة - تفصيلي - من تاريخ  : " + start + "  الى  " + end;
            //if (ToggleButton_Monthly.Checked)
            //    dateHeader = "تقرير يومي للاجهزة لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
            //if (jToggleButton_Year.Checked)
            //    dateHeader = "تقرير شهري للاجهزة لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + "- الاجهزة" + ".pdf";
                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);

                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);
                            //======================================================================
                            //table_out.AddCell(new Phrase("اجمالي الجلسات", arabicFont_fotter));

                            //table_out.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("اجمالي الاستهلاك", arabicFont_fotter));
                            //table_out.AddCell(new Phrase("اجمالي الكروت", arabicFont_fotter));

                            //======================================================================

                            //table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            //table_out.AddCell(new Phrase(txt_uptime.Text, arabicFont));
                            //table_out.AddCell(new Phrase("", arabicFont));
                            //table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            //table_out.AddCell(new Phrase(txt_count_Cards.Text, arabicFont));

                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            //RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }
        private void Pdf_By_Days(DataGridView _dgv=null)
        {
            if(_dgv==null) 
                {
                _dgv = dgv;
                }
            string _name = dgv.CurrentRow.Cells["جهاز البث"].Value.ToString();
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            if (_dgv.Name == "dgv")
            {
                if (ToggleButton_Detail.Checked)
                    dateHeader = "تقرير الاجهزة - تفصيلي - من تاريخ  : " + start + "  الى  " + end;
                if (ToggleButton_Monthly.Checked)
                    dateHeader = "تقرير يومي للاجهزة لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    dateHeader = "تقرير شهري للاجهزة لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);
            }
            else
            {
                if (ToggleButton_Detail.Checked)
                    dateHeader = $"تقرير تفصيلي للجهاز ({_name}) من تاريخ  : " + start + "  الى  " + end;
                if (ToggleButton_Monthly.Checked)
                    dateHeader = $"تقرير يومي للجهاز ({_name}) لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    dateHeader = $"تقرير شهري للجهاز ({_name}) لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            }
            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + "- الاجهزة" + ".pdf";
                if (_dgv.Name == "dgv")
                    sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + $"- جهاز {_name}" + ".pdf";

                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);

                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in _dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in _dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in _dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);

                            PdfPTable footer = new PdfPTable(7);
                            //======================================================================
                            if (ToggleButton_Monthly.Checked || jToggleButton_Year.Checked)
                            {
                                footer.TotalWidth = 580f;
                                footer.LockedWidth = true;
                                footer.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                                footer.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                                footer.HorizontalAlignment = Element.ALIGN_CENTER;
                                footer.SpacingBefore = 2f;
                                footer.SpacingAfter = 2f;
                                footer.DefaultCell.Padding = 3;


                                footer.AddCell(new Phrase("اجمالي الجلسات", arabicFont_fotter));
                                footer.AddCell(new Phrase("اجمالي الكروت", arabicFont_fotter));
                                footer.AddCell(new Phrase("كروت اول دخول", arabicFont_fotter));
                                footer.AddCell(new Phrase("مبيعات اول دخول", arabicFont_fotter));

                                footer.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                                footer.AddCell(new Phrase("اجمالي الاستهلاك", arabicFont_fotter));

                                if(ToggleButton_Monthly.Checked)
                                footer.AddCell(new Phrase("متوسط المبيعات اليومي", arabicFont_fotter));
                                if (jToggleButton_Year.Checked)
                                    footer.AddCell(new Phrase("متوسط المبيعات الشهري", arabicFont_fotter));

                                //======================================================================

                                footer.AddCell(new Phrase(CountSession.ToString(), arabicFont));
                                footer.AddCell(new Phrase(count_cards.ToString(), arabicFont));
                                footer.AddCell(new Phrase(CountFirstLogin.ToString(), arabicFont));
                                footer.AddCell(new Phrase(String.Format("{0:n0}", sum), arabicFont));
                                footer.AddCell(new Phrase(utils.Get_Seconds_By_clock_Mode((sum_uptime)), arabicFont));
                                footer.AddCell(new Phrase(utils.ConvertSize_Get_InArabic(sum_download.ToString()), arabicFont));
                                footer.AddCell(new Phrase(String.Format("{0:n0}", avg), arabicFont));
                            }
                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Add(footer);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            //RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }

        private void btn_Print_details_Click(object sender, EventArgs e)
        {
            Pdf_By_Days(dgv2);
        }

        private void check_with_Commi_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            get_report();
        }

        private void btn_Refresh_Click2(object sender, EventArgs e)
        {

        }
        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;

            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => Refersh_mikrotik());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }



        }

        [Obsolete]
        private void Refersh_mikrotik()
        {
            Global_Variable.StartThreadProcessFromMK = true;
            bool Syn_Users_FromFasrDB = false;
            bool Syn_Pyment_FromFasrDB = false;
            bool Syn_Session_FromFasrDB = false;

            if (Global_Variable.load_by_DownloadDB && Global_Variable.Mk_resources.version <= 6)
            {
                int count_process = 7;
                Fast_Load_From_Mikrotik fast = new Fast_Load_From_Mikrotik();
                Global_Variable.Update_Um_StatusBar_Prograss("تنزيل قاعدة بيانات اليوزمنجر  -  لتستفيد من الميزه افتح السرعه للكمبيوتر", Convert.ToInt32(1 * (100.0 / count_process)));
                if (fast.Download_Sql_From_Mikrotik())
                {

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر", Convert.ToInt32(2 * (100.0 / count_process)));
                    //Thread.Sleep(1000);
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه الكروت يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                    Syn_Users_FromFasrDB = fast.Syn_UmUser_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه المبيعات والحسابات يوزمنجر", Convert.ToInt32(4 * (100.0 / count_process)));
                    Syn_Pyment_FromFasrDB = fast.Syn_Pyments_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه  الجلسات يوزمنجر", Convert.ToInt32(5 * (100.0 / count_process)));
                    Syn_Session_FromFasrDB = fast.Syn_Session_From_FastDB();

                    //---======================================================================================================

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب ومزامنه بيانات اليوزمنجر من الروتر", Convert.ToInt32(0 * (100.0 / count_process)));

                    fast.Create_Indexs();
                }
                try
                {
                    string Downloadfile = utils.Get_Database_Directory() + "\\" + "dbs\\temp.db";
                    string Downloadfile2 = utils.Get_Database_Directory() + "\\" + "dbs\\temp2.db";
                    string Downloadfile3 = Directory.GetCurrentDirectory() + "\\sql.bat";
                    if (File.Exists(Downloadfile))
                        File.Delete(Downloadfile);
                    if (File.Exists(Downloadfile2))
                        File.Delete(Downloadfile2);
                    if (File.Exists(Downloadfile3))
                        File.Delete(Downloadfile3);
                }
                catch { }

            }

            if (Syn_Session_FromFasrDB == false)
            //if (  ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false))   )
            {
                int count_process = 4;
                UserManagerProcess u = new UserManagerProcess();
                //if (Global_Variable.Ddiable_LoadSession == false)
                //{
                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(1 * (100.0 / count_process)));

                Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                //RJMessageBox.Show("يتم finsh get session");

                Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(2 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StartSyn();


                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(3 * (100.0 / count_process)));

                if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                    u.Syn_Session_to_LocalDB();
                Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(0 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StopSyn();
                //}
            }


            Global_Variable.StartThreadProcessFromMK = false;
        }

    }
}
