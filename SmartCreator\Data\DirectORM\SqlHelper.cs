using System;
using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Linq;
using System.Reflection;
using SmartCreator.Data.DirectORM;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Helper methods for SQL operations
    /// </summary>
    public static class SqlHelper
    {
        /// <summary>
        /// Map data reader to object
        /// </summary>
        public static T MapFromReader<T>(IDataReader reader) where T : new()
        {
            var obj = new T();
            var type = typeof(T);
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                if (!property.CanWrite) continue;

                try
                {
                    var columnName = property.Name;
                    var ordinal = GetOrdinal(reader, columnName);

                    if (ordinal >= 0 && !reader.IsDBNull(ordinal))
                    {
                        var value = reader.GetValue(ordinal);
                        var convertedValue = ConvertValue(value, property.PropertyType);
                        property.SetValue(obj, convertedValue);
                    }
                }
                catch
                {
                    // Skip properties that can't be mapped
                }
            }

            return obj;
        }

        /// <summary>
        /// Add parameters to command
        /// </summary>
        public static void AddParameters(IDbCommand command, object parameters)
        {
            if (parameters == null) return;

            var type = parameters.GetType();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var parameter = command.CreateParameter();
                parameter.ParameterName = $"@{property.Name}";
                parameter.Value = property.GetValue(parameters) ?? DBNull.Value;
                command.Parameters.Add(parameter);
            }
        }

        /// <summary>
        /// Get column ordinal safely
        /// </summary>
        private static int GetOrdinal(IDataReader reader, string columnName)
        {
            try
            {
                return reader.GetOrdinal(columnName);
            }
            catch
            {
                return -1;
            }
        }

        /// <summary>
        /// Convert value to target type
        /// </summary>
        private static object? ConvertValue(object value, Type targetType)
        {
            if (value == null || value == DBNull.Value)
                return null;

            // Handle nullable types
            if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
            {
                targetType = Nullable.GetUnderlyingType(targetType)!;
            }

            // Handle enums
            if (targetType.IsEnum)
            {
                if (value is string stringValue)
                    return Enum.Parse(targetType, stringValue);
                else
                    return Enum.ToObject(targetType, value);
            }

            // Handle DateTime
            if (targetType == typeof(DateTime) && value is string dateString)
            {
                if (DateTime.TryParse(dateString, out var dateTime))
                    return dateTime;
            }

            // Handle boolean
            if (targetType == typeof(bool))
            {
                if (value is string boolString)
                    return bool.Parse(boolString);
                if (value is int intValue)
                    return intValue != 0;
            }

            // Handle decimal/double/float
            if (targetType == typeof(decimal) || targetType == typeof(double) || targetType == typeof(float))
            {
                return Convert.ChangeType(value, targetType);
            }

            // Default conversion
            try
            {
                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                return value;
            }
        }
    }

    /// <summary>
    /// SQL Generator for basic CRUD operations
    /// </summary>
    public static class SqlGenerator
    {
        /// <summary>
        /// Get table name from type (with Table attribute support)
        /// </summary>
        public static string GetTableName<T>() where T : class
        {
            var type = typeof(T);
            var tableAttribute = type.GetCustomAttribute<TableAttribute>();
            return tableAttribute?.Name ?? type.Name;
        }

        /// <summary>
        /// Check if property has NotMapped attribute
        /// </summary>
        private static bool HasNotMappedAttribute(PropertyInfo property)
        {
            return property.GetCustomAttribute<NotMappedAttribute>() != null;
        }

        /// <summary>
        /// Get column name from property (with Column attribute support)
        /// </summary>
        private static string GetColumnName(PropertyInfo property)
        {
            var columnAttribute = property.GetCustomAttribute<ColumnAttribute>();
            return columnAttribute?.Name ?? property.Name;
        }

        /// <summary>
        /// Generate INSERT statement
        /// </summary>
        public static string GenerateInsert<T>() where T : class
        {
            var type = typeof(T);
            var tableName = GetTableName<T>();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite && p.Name != "Id") // Exclude Id for auto-increment
                .Where(p => !HasNotMappedAttribute(p)) // Exclude NotMapped properties
                .ToList();

            var columns = string.Join(", ", properties.Select(p => GetColumnName(p)));
            var values = string.Join(", ", properties.Select(p => $"@{p.Name}"));

            return $"INSERT INTO {tableName} ({columns}) VALUES ({values})";
        }

        /// <summary>
        /// Generate UPDATE statement
        /// </summary>
        public static string GenerateUpdate<T>() where T : class
        {
            var type = typeof(T);
            var tableName = GetTableName<T>();
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite && p.Name != "Id") // Exclude Id from SET clause
                .Where(p => !HasNotMappedAttribute(p)) // Exclude NotMapped properties
                .ToList();

            var setClause = string.Join(", ", properties.Select(p => $"{GetColumnName(p)} = @{p.Name}"));

            return $"UPDATE {tableName} SET {setClause} WHERE Id = @Id";
        }

        /// <summary>
        /// Generate DELETE statement
        /// </summary>
        public static string GenerateDelete<T>() where T : class
        {
            var type = typeof(T);
            var tableName = GetTableName<T>();

            return $"DELETE FROM {tableName} WHERE Id = @Id";
        }

        /// <summary>
        /// Generate SELECT statement
        /// </summary>
        public static string GenerateSelect<T>() where T : class
        {
            var type = typeof(T);
            var tableName = GetTableName<T>();

            return $"SELECT * FROM {tableName}";
        }

        /// <summary>
        /// Generate SELECT BY ID statement
        /// </summary>
        public static string GenerateSelectById<T>() where T : class
        {
            var type = typeof(T);
            var tableName = GetTableName<T>();

            return $"SELECT * FROM {tableName} WHERE Id = @Id";
        }
    }
}
