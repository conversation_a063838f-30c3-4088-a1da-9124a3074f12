﻿using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using System;
using System.ComponentModel;

//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class BasePyment
    {
        public BasePyment() { }

        [Required]
        [StringLength(200)]
        //[Index]
        public string UserName { get; set; }
        
        //[Index]
        [StringLength(200),DisplayName("اسم الباقة")]
        public string ProfileName { get; set; }

        [Default(0), DisplayName("السعر")]
        public float Price { get; set; } = 0;
        
        [Default(0)]
        public float Percentage { get; set; } = 0;

        //[StringLength(100)]
        //[DefaultValue("'ByPercentage'")]
        [Default(0)]
        public int PercentageType { get; set; } = 0;

        [Default(0)]
        public float TotalPrice { get; set; } = 0;
        //public float Price_Disply { get; set; } = 0;

        [Default(0)]
        public long ProfileValidity { get; set; } = 0;
        
        [Default(0)]
        public long ProfileUptimeLimit { get; set; } = 0;

        [Default(0)]
        public long ProfileTransferLimit { get; set; } = 0;

        [Index, DisplayName("تاريخ الاضافة")]
        public DateTime? AddedDate { get; set; }

        public DateTime? AddedDb { get; set; } 

        public DateTime? LastSynDb { get; set; }

        [StringLength(100), Browsable(false)]
        public string MkId { get; set; }

        public string state { get; set; }

    }

}
