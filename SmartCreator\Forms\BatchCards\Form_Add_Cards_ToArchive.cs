﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Windows.Forms;

namespace SmartCreator.Forms.BatchCards
{
    public partial class Form_Add_Cards_ToArchive : RJChildForm
    {
        private bool firstLoad = true;
        private bool isFirstLoadForm = true;

        private bool startPrint = false;
        private bool is_Add_One_Card = false;
        private bool is_add_batch_cards = false;
        private bool is_add_batch_cards_to_Archive = false;
        private bool is_rest_sn = false;
        private int inext = 0;
        private New_Generate_Cards new_Generate_Cards;

        private FormAddUsersManager frm = null;
        private Form_PrintUserManagerState Frm_State = null;
        private Clss_InfoPrint clss_InfoPrint;
        Random rndU;

        private string Public_file_Name = "";
        public string pathfile = "";
        private Archive_DataAccess Archive_DA;
        private Sql_DataAccess Local_DA = null;
        private Smart_DataAccess Smart_DA = null;

        private UserManagerProcess ump;

        public Form_Add_Cards_ToArchive()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            this.Text = "اضافة كروت الي الارشيف";
            Archive_DA = new Archive_DataAccess();
            Local_DA = new Sql_DataAccess(); 
            Smart_DA = new Smart_DataAccess();
           
            loadFromState();

            set_fonts();
            //utils.Control_textSize(pnlClientArea);
        }
        private void set_fonts()
        {
            Font title_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 10 , FontStyle.Bold);
            lblTitle.Font = title_font;

            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidKufi_Regular, 8.25f , FontStyle.Regular);
            



            lbl_gide.Font = dgvHeader_font;
            
            if (UIAppearance.Theme == UITheme.Light)
                lbl_gide.ForeColor = System.Drawing.Color.Blue;
            

            Font lbl1 = Program.GetCustomFont(Resources.DroidKufi_Regular, 9, FontStyle.Regular);
            lbl_count.Font = lbl_startCard.Font = lbl_endCards.Font = lbl_UserPassword_Pattern.Font
            = lbl_profile.Font = lbl_TemplateCards.Font
            = lbl_User_NumberORcharcter.Font = lbl_Pass_NumberORcharcter.Font = lbl_User_NumberORcharcter.Font
            = lbl1;

            btnAdd.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10f, FontStyle.Bold);
            cbox_User_NumberORcharcter.Font=cbox_Pass_NumberORcharcter.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 7.25f, FontStyle.Regular);
            Font lbl2 = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            lbl_excel.Font =
            lbl_Save_PDF.Font =
            lbl_OpenAfterPrint.Font =
            lbl_text_File.Font =
            lbl_RegisterAsBatch.Font =
            rjLabel6.Font =
           
            lbl2;
            //Control_Loop(pnlClientArea);
            utils.Control_textSize(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> p = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile s in p)
                    comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

                CBox_Profile.DataSource = new BindingSource(comboSource, null);
                CBox_Profile.DisplayMember = "Value";
                CBox_Profile.ValueMember = "Key";
            }
            catch { }
        }

        private void Get_TemplateCardsFromDB()
        {
            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
            }
            catch { }
        }
        private void loadFromState()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormArchivePrint");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());

            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            Frm_State.is_add_batch_cards = false;
            Frm_State.is_add_batch_cards_to_Archive = false;
            Frm_State.is_Add_One_Card = false;
            txtNumberCard.Text = Frm_State.txtNumberCard;
            txt_StartCard.Text = Frm_State.txt_StartCard;
            txt_EndCard.Text = Frm_State.txt_EndCard;
            txt_longUsers.Text = Frm_State.txt_longUsers;
            txt_longPassword.Text = Frm_State.txt_longPassword;

            cbox_UserPassword_Pattern.SelectedIndex = Frm_State.cbox_UserPassword_Pattern;
            cbox_User_NumberORcharcter.SelectedIndex = Frm_State.cbox_User_NumberORcharcter;
            cbox_Pass_NumberORcharcter.SelectedIndex = Frm_State.cbox_Pass_NumberORcharcter;

            //checkBoxSaveTo_PDF.Check = Frm_State.checkBoxSaveTo_PDF;
            checkBoxSaveTo_excel.Check = Frm_State.checkBoxSaveTo_excel;
            //checkBoxOpenAfterPrint.Check = Frm_State.checkBoxOpenAfterPrint;
            checkBoxSaveTo_script_File.Check = Frm_State.checkBoxSaveTo_script_File;
            //checkBox_With_Archive_uniqe.Check = Frm_State.checkBox_Create_without_Add_ToMicrotik;
            checkBoxSaveTo_text_File.Check = Frm_State.checkBoxSaveTo_text_File;
        }
        private void SaveFromState()
        {
            Frm_State.txtNumberCard = txtNumberCard.Text;
            Frm_State.txt_StartCard = txt_StartCard.Text;
            Frm_State.txt_EndCard = txt_EndCard.Text;

            Frm_State.txt_longUsers = txt_longUsers.Text;
            Frm_State.txt_longPassword = txt_longPassword.Text;

            Frm_State.cbox_UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;
            Frm_State.cbox_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedIndex;
            Frm_State.cbox_Pass_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedIndex;

            //Frm_State.checkBoxSaveTo_PDF = checkBoxSaveTo_PDF.Check;
            Frm_State.checkBoxSaveTo_excel = checkBoxSaveTo_excel.Check;
            //Frm_State.checkBoxOpenAfterPrint = checkBoxOpenAfterPrint.Check;
            Frm_State.checkBoxSaveTo_script_File = checkBoxSaveTo_script_File.Check;
            //Frm_State.checkBox_Create_without_Add_ToMicrotik = checkBox_With_Archive_uniqe.Check;
            Frm_State.checkBoxSaveTo_text_File = checkBoxSaveTo_text_File.Check;
            //Frm_State.PathFolderPrint_Archive = checkBoxSaveTo_text_File.Check;


            string formSetting = JsonConvert.SerializeObject(Frm_State);
            Smart_DataAccess.Setting_SaveState_Forms_Variables("FormArchivePrint", "SaveFromState", formSetting);

        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
          if (!check_fields())
                return;

            clss_InfoPrint = get_data_from_interface2();

            //Generate_pathfile(clss_InfoPrint.Profile_Name);
            //Generate_FileName(clss_InfoPrint.Number_Cards_ToAdd.ToString(), clss_InfoPrint.Profile_Name);

            if (clss_InfoPrint.Save_To_PDF)
            {
                if(CBox_TemplateCards.Text=="")
                {
                    RJMessageBox.Show("لقد حددت حفظ الي ملف بي دي اف بدون ان تختار القالب الرجاء حدد قالب للطباعة");
                    return;
                }
                if (init_file_pdf() == false)
                    return;
            }

            DialogResult result = RJMessageBox.Show("  هل انت متأكد من تخزين الكروت في الارشيف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 3, "", "يرجى الانتضار حتى تجميع البيانات");
                try
                {
                   
                        ThreadStart theprogress = new ThreadStart(() => AddUserUserd_batch_cards());
                        Thread startprogress = new Thread(theprogress);
                        startprogress.Name = "Update ProgressBar";
                        startprogress.Start();
                   
                }

                catch { startPrint = false; }
            }
            else
            {
                //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الفاء عملية الاضافة");
                startPrint = false;
                return ;
            }
            startPrint = false;
            SaveFromState();



            //AddUserUserd_batch_cards();
        }
        private bool check_fields()
        {
            if (startPrint == true)
            {
                RJMessageBox.Show("الرجاء الانتضار حتى اكتمال العملية السابقة");
                return false;
            }
            if (CBox_Profile.SelectedIndex == -1 || CBox_Profile.Text == "")
            {
                RJMessageBox.Show("حدد البروفايل");
                return false;
            }
            
            
            int numberChik;
           
            if (!(int.TryParse(txt_longUsers.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح الى طول اسم المستخدم");
                return false;
            }
            if ((Convert.ToInt16(txt_longUsers.Text) + txt_StartCard.Text.Length + txt_EndCard.Text.Length) < 6)
            {
                RJMessageBox.Show("يجب ان يكون طول رقم الكرت مع البادئة والاحقة اكبر من 5");
                return false;
            }
            if (!(int.TryParse(txt_longPassword.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح في طول كلمة السر ");
                return false;
            }
            if (cbox_User_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم المستخدم");
                return false;
            }
            if (cbox_Pass_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم كلمة السر");
                return false;
            }
            if (cbox_UserPassword_Pattern.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد صيغة الكرت ");
                return false;
            }
            //if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.Text == "")
            //{
            //    //RJMessageBox.Show("لم تختر اي قالب للطباعة");
            //    DialogResult result2 = RJMessageBox.Show("  لم تقم باختيار قالب للطباعة \n هل تريد المتابعة بدون اخراج الكروت الي ملف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            //    if (result2 == DialogResult.Yes)
            //    {
            //        checkBoxSaveTo_PDF.Checked = false;
            //        checkBoxOpenAfterPrint.Checked = false;
            //    }
            //    else
            //    {
            //        startPrint = false;
            //        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
            //        return false;
            //    }

            //}


            if (checkBox_Rest_SN.Checked)
            {
                //var found = Archive_DA.Get_Batch_byBatchNumber_And_Server(Convert.ToInt32(txt_last_batchNumber.Text));
                //if (found == null || found.Count == 0)
                //{
                //    RJMessageBox.Show("رقم الطبعة السابقة التي ادخلتها غير موجود");
                //    return false;
                //}
            }
            return true;

        }
       
        
        public bool init_file_pdf()
        {
            SaveFileDialog saveFileDialog1 = new SaveFileDialog();
            saveFileDialog1.Title = "حدد مكان حفظ الملف";
            try
            {
                if (!Directory.Exists(Frm_State.PathFolderPrint_Archive))
                {
                    Directory.CreateDirectory(Frm_State.PathFolderPrint_Archive);
                }
            }
            catch
            {
                Frm_State.path_saved_file_Archive = Directory.GetCurrentDirectory() + "\\" + "tempCards\\Archive\\pdf";
            }

            try
            {
                if (Frm_State.PathFolderPrint_Archive != "")
                    saveFileDialog1.InitialDirectory = Frm_State.PathFolderPrint_Archive;
                else
                    saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\Archive\\pdf";
            }
            catch
            {
                saveFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "tempCards\\Archive\\pdf";
                Frm_State.PathFolderPrint_Archive = Directory.GetCurrentDirectory() + "\\" + "tempCards\\Archive\\pdf";
            }
           

            //Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + frm.txtNumberCard.Text + "Cards)" + "_(" + frm.CBox_Profile.Text + ")";
            //pathfile = Frm_State.path_saved_file + "\\" + "Cards_" + Public_file_Name + ".pdf";

            Public_file_Name = Generate_FileName(txtNumberCard.Text, CBox_Profile.SelectedValue.ToString());
            pathfile = Generate_pathfile(CBox_Profile.SelectedValue.ToString());

            saveFileDialog1.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialog1.FileName = "ArchiveCards_" + Public_file_Name;
            if (saveFileDialog1.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialog1.FileName;
                Frm_State.PathFolderPrint_Archive = Path.GetDirectoryName(saveFileDialog1.FileName);
                Frm_State.path_saved_file_Archive = pathfile;

            }
            else
            {
                startPrint = false;
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");
                return false;
            }
            return true;
        }
     
        
        
        private string Generate_FileName(string txtNumberCard,string Profile_Name)
        {
            string file_Name = "";

            Public_file_Name = DateTime.Now.ToString("dd-MM-yyyy") + "_" + DateTime.Now.ToString("HH-mm-ss") + "_(" + txtNumberCard + "Cards)" + "_(" + Profile_Name + ")";
            pathfile = Frm_State.path_saved_file_Archive + "\\" + "ArchiveCards_Cards_" + Profile_Name + ".pdf";

            //try
            //{
            //    if (!Directory.Exists(Frm_State.PathFolderPrint_Archive))
            //    {
            //        Directory.CreateDirectory(Frm_State.PathFolderPrint_Archive);
            //    }
            //}
            //catch
            //{
            //    Frm_State.path_saved_file_Archive = Directory.GetCurrentDirectory() + "\\" + "tempCards\\Archive\\pdf";
            //}



            return Public_file_Name;

        }
        private string Generate_pathfile( string Profile_Name)
        {
            return  Frm_State.path_saved_file_Archive + "\\" + "Cards_" + Profile_Name + ".pdf";
        }

        private void AddUserUserd_batch_cards()
        {
            HashSet<string> card_copy = new HashSet<string>();
            HashSet<string> card_Archive = new HashSet<string>();

            if (Global_Variable.Mk_Login_data.LogIn_Without_mk)
            {
                card_copy = new HashSet<string>(Local_DA.Load<string>("SELECT UserName FROM UmUser WHERE DeleteFromServer=0;"));
            }
            else
            {
                if (Global_Variable.Source_Users_UserManager_ForPrint != null)
                    card_copy = new HashSet<string>(Global_Variable.Source_Users_UserManager_ForPrint);
            }

            var ArchiveCards= Archive_DA.Load<string>($"SELECT UserName FROM CardsArtchive WHERE Status=0;");
            card_copy.UnionWith(ArchiveCards);
            //if (clss_InfoPrint.With_Archive_uniqe)
            //{
            //    ////=========  get cards from archive  and copy  to hashset card_copy
            //    //using (var db = Archive_DA.Get_dbFactory().Open())
            //    //{
            //    //    HashSet<string>  card_copy2 = db.ColumnDistinct<string>(db.From<CardsArtchive>().Where(x => x.Status == 0).Select(x => x.UserName));
            //    //    card_copy.UnionWith(card_copy2);
            //    //}
            //}

            int Public_Number_Cards_ToAdd = clss_InfoPrint.Number_Cards_ToAdd;

            string mesgBtn = "يتم الان اضافة الكروت الى الارشيف";
            Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", mesgBtn);

            // ====== توليد الكروت العشوائية ============================
            CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, card_copy);
             new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Public_Number_Cards_ToAdd, mesgBtn, true);
            if (new_Generate_Cards == null)
            {
                startPrint = false;
                return;
            }
                  
            List<CardsArtchive> dbUser = Add_sn_to_local_dbUser(new_Generate_Cards);

            //Add_to_db(dbUser);

            if (Archive_DA.Add_ArchiveCards_ToDB(dbUser, true) > 0)
            {
                Add_to_Batch_cards_toDB(dbUser);
                Smart_DA.Update_MySequence("CardsArtchive", dbUser.Last().SN);

                //if (clss_InfoPrint.RegisterAsBatch || clss_InfoPrint.RegisterAs_LasBatch) { add_to_Batch_cards_toDB(dbUser); }

                if (clss_InfoPrint.Save_To_PDF)
                {
                    Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف PDF");
                    print_pdf(new_Generate_Cards.dicUser);
                    if (clss_InfoPrint.Open_PDF_file)
                    {
                        try
                        {
                            System.Diagnostics.Process.Start(Frm_State.path_saved_file_Archive);
                        }
                        catch { }
                    }
                }

                if (clss_InfoPrint.SaveTo_excel) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف اكسل"); CreateExcel(dbUser); }
                if (clss_InfoPrint.SaveTo_script_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف سكربت"); Create_Script_File(dbUser); }
                if (clss_InfoPrint.SaveTo_text_File) { Global_Variable.Update_Um_StatusBar(true, true, -1, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "يتم الان اخراج الكروت الي ملف نصي"); Create_Text_File(dbUser); }


                Global_Variable.Update_Um_StatusBar(true, true, 0, "( " + Public_Number_Cards_ToAdd + "  /  " + (0) + " )", "تمت الاضافة الي الارشيف");
                try
                {
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                               (MethodInvoker)delegate ()
                               {
                                   this.Close();
                               });
                }
                catch { }
            }
            else
                RJMessageBox.Show("حدث مشكله اثناء الاضافة للارشيف");
        }

        public List<CardsArtchive> Add_sn_to_local_dbUser(New_Generate_Cards new_Generate_Cards)
        {
            long sn = 0;
            try
            {
                 sn = Smart_DA.Get_MySequence("CardsArtchive");
                 //sn = Archive_DA.GetSingleNumberGetSingleNumber("select seq from sqlite_sequence where name='CardsArtchive';");
            }
            catch { }
            
            #region تجهيز كلاس اليوزر عشان الاضافة الي قاعده البيانات المحلية واضافة اول كرت قبل الدخول للدوارة
            List<CardsArtchive> dbUser = new List<CardsArtchive>();

            //============= حساب النسبه للباقة او البقالة ==================
                int? Number_Card_In_Page = Calclate_Number_Card_In_Page();
            for (int i = 0; i < new_Generate_Cards.dicUser.Count; i++)
            {
                int? PageNumber=null;
                try
                {
                    PageNumber = (int?)((i) / Number_Card_In_Page) + 1;
                }
                catch { }

                CardsArtchive db = new CardsArtchive();
                sn = sn + 1;
                db.SN = sn ;
                new_Generate_Cards.dicUser.ElementAt(i).Value.SN = sn;
                db.UserName = new_Generate_Cards.dicUser.ElementAt(i).Value.Name;
                db.Password = new_Generate_Cards.dicUser.ElementAt(i).Value.Password;
                db.ProfileName = clss_InfoPrint.profile.Name;
                db.BatchCardId = clss_InfoPrint.NumberPrint;
                db.PageNumber = PageNumber;

                //db.AddedDb = clss_InfoPrint.regDate;
                db.Rb = Global_Variable.Mk_resources.RB_SN;
                //db.LastSynDb = clss_InfoPrint.regDate;

                db.Status = 0;
                //db.DeleteFromServer = 0;

                dbUser.Add(db);
                //sn = sn + 1;
            }
            return dbUser;
            #endregion


        }
        public void Add_to_db(List<CardsArtchive> dbUser)
        {
            Archive_DA.Add_ArchiveCards_ToDB(dbUser, true);

            //using (var db = Archive_DA.Get_dbFactory().Open())
            //{
            //    var insert = db.Insert(dbUser);
            //}
        }
        private bool init_Virable()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormArchivePrint");
            if (sourceSaveState == null)
                Frm_State = new Form_PrintUserManagerState();
            else
                Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());
            if (Frm_State == null)
                Frm_State = new Form_PrintUserManagerState();

            //clss_InfoPrint = get_data_from_interface2();

            //ump = new UserManagerProcess();
            //ump.clss_InfoPrint = clss_InfoPrint;
            //ump.Frm_State = Frm_State;
            //frm = new FormAddUsersManager();
            //ump.frm = frm;

            //if (clss_InfoPrint.Save_To_PDF)
            //{
            //    ump.frm.txtNumberCard.Text = clss_InfoPrint.Number_Cards_ToAdd.ToString();
            //    ump.frm.CBox_Profile.Text = clss_InfoPrint.profile.Name;
            //    if (ump.init_file_pdf() == false)
            //        return false;

            //}



            return true;
        }
        private Clss_InfoPrint get_data_from_interface2()
        {
            clss_InfoPrint = new Clss_InfoPrint();
            clss_InfoPrint.is_add_batch_cards = true;
            clss_InfoPrint.is_add_batch_cards_to_Archive = false;
            clss_InfoPrint.is_Add_One_Card = false;

            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile.SelectedValue.ToString());
            clss_InfoPrint.profile = profile;
            int NumberPrint = 1;
           
            if (checkBox_Rest_SN.Checked==false)
            {
                //NumberPrint = SqlDataAccess.Get_lastID_Batch_cards();
                NumberPrint = (int)Smart_DA.Get_BatchCards_My_Sequence("BatchArchive"); 
                NumberPrint += 1;
                clss_InfoPrint.NumberPrint = NumberPrint;
            }
            
           
            clss_InfoPrint.Selected_template_item = CBox_TemplateCards.SelectedIndex;
            clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(txtNumberCard.Text);
            clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(txtNumberCard.Text);
            clss_InfoPrint.Profile_Name = CBox_Profile.SelectedValue.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter_Value = cbox_User_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.User_Long = (Convert.ToInt32(txt_longUsers.Text));
            clss_InfoPrint.Mode_Password_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_Password_NumberORcharcter_Value = cbox_Pass_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.Password_Long = Convert.ToInt32(txt_longPassword.Text);
            clss_InfoPrint.UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;

            clss_InfoPrint.StartCard = txt_StartCard.Text.Trim();
            clss_InfoPrint.EndCard = txt_EndCard.Text.Trim();
            clss_InfoPrint.pathfile = Frm_State.path_saved_file_Archive;

            clss_InfoPrint.Save_To_PDF = checkBoxSaveTo_PDF.Checked;
            clss_InfoPrint.Open_PDF_file = checkBoxOpenAfterPrint.Checked;
            clss_InfoPrint.SaveTo_excel = checkBoxSaveTo_excel.Checked;
            clss_InfoPrint.SaveTo_script_File = checkBoxSaveTo_script_File.Checked;
            clss_InfoPrint.SaveTo_text_File = checkBoxSaveTo_text_File.Checked;
            
            clss_InfoPrint.RegisterAsBatch = checkBox_Rest_SN.Checked;

            //clss_InfoPrint.RegisterAs_LasBatch = checkBox_RegisterAs_LastBatch.Checked;
            //clss_InfoPrint.With_Archive_uniqe = checkBox_With_Archive_uniqe.Checked;
            clss_InfoPrint.TemplateId = CBox_TemplateCards.SelectedValue.ToString();
            clss_InfoPrint.TemplateName = CBox_TemplateCards.Text.ToString();

            return clss_InfoPrint;
        }
         
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            Get_Cbox_Profile();
            Get_TemplateCardsFromDB();
            firstLoad = false;
        }

        private void Form_Add_Cards_ToArchive_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        public void Add_to_Batch_cards_toDB(List<CardsArtchive> dbUser)
        {
            long sn_from = dbUser.First().SN;
            //long sn_to = clss_InfoPrint.Number_Cards_ToAdd + sn_from ;
            long sn_to = dbUser.Last().SN;

            //Class_Batch_cards data=new Class_Batch_cards();
            BatchArtchive data = new BatchArtchive();
            data.Id = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);
            data.Sn_from = sn_from;
            data.Sn_to = sn_to;
            data.BatchNumber = (int)(clss_InfoPrint.NumberPrint > 0 ? clss_InfoPrint.NumberPrint : 0);

            data.ProfileName = clss_InfoPrint.profile.Name;
            data.AddedDate = clss_InfoPrint.regDate;
            data.Count = clss_InfoPrint.Number_Cards_ToAdd;
            //data.Rb = Global_Variable.Mk_Router.mk_code;
            data.Rb = Global_Variable.Mk_Router.mk_sn;
            Archive_DA.Add_Batch_Cards(data);
            //SqlDataAccess.Add_Batch_Cards(data,0, clss_InfoPrint.is_RegisterAs_LastBatch);

        }
        private int? Calclate_Number_Card_In_Page()
        {
            CardsTemplate tcard = new CardsTemplate();
            SourceCardsTemplate sorceTemplates = SqlDataAccess.Get_template_cards_By_id(clss_InfoPrint.TemplateId);
            if(sorceTemplates==null)
                return null;
            if (sorceTemplates.type == "design")
            {
                CardsTemplate cardsTemplate = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplates.values);
                if (cardsTemplate == null)
                {
                    return null;
                }
                tcard = cardsTemplate;
            }
            else
                return null;

            //==========================================================================================  
            float Space_X = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.space_horizontal_margin.ToString()));
            float Space_Y = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.Space_vertical_margin.ToString()));
            float Pictur_width = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_width.ToString()));
            float Pictur_height = iTextSharp.text.Utilities.MillimetersToPoints(float.Parse(tcard.setingCard.card_height.ToString()));


            float Pictur_width_orginal = Pictur_width;
            float Pictur_height__orginal = Pictur_height;

            float ColumBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);
            float CardsBetwenSpace = iTextSharp.text.Utilities.MillimetersToPoints(0);

            //int NuberCards = 51;
            int NumberCard_for_OneColum = 0;
            int NumberCard_in_Page = 0;
            //double NumberPages = 0;

            int ColumNumber = 0; 
            //float CardNumber = 0;

            ColumNumber = (int)(595 / (Pictur_width + Space_Y));
            if ((ColumNumber * (Pictur_width + Space_Y) > 595))
                ColumNumber = ColumNumber - 1;

            NumberCard_for_OneColum = (int)((842) / (Pictur_height + Space_X));
            if ((NumberCard_for_OneColum * (Pictur_height + Space_X) > 842))
                NumberCard_for_OneColum = NumberCard_for_OneColum - 1;


            NumberCard_in_Page = (NumberCard_for_OneColum * ColumNumber);

            //txt_NumberCard.Text = NumberCard_in_Page.ToString();
            //txt_NumberCulum.Text = ColumNumber.ToString();

            iTextSharp.text.Image jpg = null;

            return NumberCard_in_Page;


        }

        //===========================================================
        private void CreateExcel(List<CardsArtchive> dbUser)
        {
            string path = @"tempCards\Excel\Archive";
            string sp = "";
            string file_ExceltName = "";
            if (clss_InfoPrint.SellingPoint != null)
                sp = clss_InfoPrint.SellingPoint.Code;

            try
            {
                if (!Directory.Exists(path))
                {
                    Directory.CreateDirectory(path);
                }
            }
            catch (Exception ex) { MessageBox.Show("  خطا في مسار حفظ الملف النصي \n" + ex.Message.ToString()); }


            string pathC = Directory.GetCurrentDirectory() + "\\tempCards\\Excel\\Archive";
            //string time= DateTime.Now.Day.ToString()+ DateTime.Now.Month.ToString()+ DateTime.Now.Year.ToString();
            //file_ExceltName = path + $"\\Excel_{clss_InfoPrint.profile.Name}_{DateTime.Now.Day}.csv";
            file_ExceltName = path + "\\" + "ArchiveExcel_" + Public_file_Name + ".csv";
            try
            {
                //int columnCount = dgvUserManager.Columns.Count;
                //string columnNames = "";

                string[] outputCsv = new string[dbUser.Count];
                int row = 0;
                foreach (CardsArtchive user in dbUser)
                {
                    outputCsv[row] += user.UserName.ToString() + ",";
                    outputCsv[row] += user.Password.ToString() + ",";
                    outputCsv[row] += clss_InfoPrint.profile.Name + ",";
                    outputCsv[row] += sp + ",";

                    //outputCsv[row] += "=\"" + user.UserName.ToString() + "\",";
                    //outputCsv[row] += "=\"" + user.Password.ToString() + "\",";
                    //outputCsv[row] += "=\"" + clss_InfoPrint.profile.Name + "\",";
                    //outputCsv[row] += "=\"" + sp + "\",";

                    row++;
                }

                File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                //MessageBox.Show("Data Exported Successfully !!!", "Info");

                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                        System.Diagnostics.Process.Start(path);
                    }
                    catch { }
            }
            catch (Exception ex)
            {
                MessageBox.Show("export_execl :" + ex.Message);
            }



        }
        private void Create_Script_File(List<CardsArtchive> dbUser)
        {
            try
            {
                string valName = "username";
                string valPassword = "";
                string last_name = "";
                string location = "";

                if (Global_Variable.Mk_resources.version <= 5)
                    valName = "name";
                if (Global_Variable.Mk_resources.version == 6 && Global_Variable.Mk_resources.verisonAfter_Float <= 30)
                    valName = "name";

                //if (checkBox_Disable_Printed_OR_notPrit.Checked)
                //    last_name = " last-name=0";
                if (clss_InfoPrint.SellingPoint != null)
                    location = " location=" + clss_InfoPrint.SellingPoint.Code;

                //string pathC = Directory.GetCurrentDirectory() + "\\tempCards\\Excel\\Archive";

                string path = @"tempCards\script\Archive";

                //if (is_add_batch_cards_to_Archive)
                //    path = Cards_setting.path_saved_file;

                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "ArchiveScript_" + Public_file_Name + ".rsc";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show("  خطا في مسار حفظ الملف النصي \n" + ex.Message.ToString()); }

                //MessageBox.Show(file_ScriptName);
                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);

                fs.Close();

                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();

                int row = 0;
                foreach (CardsArtchive user in dbUser)
                {

                    if (clss_InfoPrint.Mode_Password_NumberORcharcter == "1")
                        valPassword = " password=" + dbUser[row].UserName;
                    if (clss_InfoPrint.Mode_Password_NumberORcharcter == "2")
                        valPassword = " password=" + dbUser[row].Password;

                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        string row_user = ":do { /tool user-manager user add " + valName + "=" + dbUser[row].UserName + valPassword + " customer=" + clss_InfoPrint.Custumer_UserMan + last_name + location + ";";
                        string row_prof = " /tool user-manager user create-and-activate-profile customer=" + clss_InfoPrint.Custumer_UserMan + " profile=\"" + clss_InfoPrint.profile.Name + "\"" + " numbers=\"" + dbUser[row].UserName + "\";} on-error={:put \"erorr add " + dbUser[row].UserName + "\"}; ";
                        File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);
                    }
                    else
                    {
                        // script = "{:local usr [:toarray (" + varible["strUser"] + ")];:local us ;:local ps;" +
                        //":for i from=0 to=([:len $usr]-1) do={:do {[/user-manager/user/add  " + varible["valName"] + "=[:pick $usr $i] " + varible["note"] + varible["Public_Attribut"] + varible["Public_Group"] + "] ; " +
                        //":do {[/user-manager/user-profile/add user=[:pick $usr $i] profile=\"" + varible["Profile"] + "\"" + " ];} on-error={:set ps ($ps.\"|\".[:pick $usr $i]); :put $ps;}} on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}}";


                        string row_user = ":do { /user-manager/user/add " + valName + "=" + dbUser[row].UserName + valPassword + " ;";
                        string row_prof = " /user-manager/user-profile/add user=" + dbUser[row].UserName + "  profile=" + "\\" + clss_InfoPrint.profile.Name + "\\" + " ; on-error={:put \"erorr add " + dbUser[row].UserName + "\"}; ";
                        File.AppendAllText(file_ScriptName, row_user + row_prof + "\n", Encoding.ASCII);

                    }

                }
            }
            catch (IOException ex)
            {
                MessageBox.Show("Create_Script_File" + ex.Message.ToString());
            }
        }
        private void Create_Text_File(List<CardsArtchive> dbUser)
        {
            try
            {
                string path = @"tempCards\text\Archive";
                //if (is_add_batch_cards_to_Archive)
                //path = Cards_setting.path_saved_file;

                //if (is_add_batch_cards_to_Archive)
                //{
                //    path = @"tempCards\script\batch\";
                //}
                string file_ScriptName = "";
                file_ScriptName = path + "\\" + "Archivetext_" + Public_file_Name + ".txt";
                try
                {
                    if (!Directory.Exists(path))
                    {
                        Directory.CreateDirectory(path);
                    }
                }
                catch (Exception ex) { MessageBox.Show(" خطا في مسار حفظ الملف النصي\n" + ex.Message.ToString()); }


                if (File.Exists(file_ScriptName)) { File.Delete(file_ScriptName); }
                FileStream fs = File.Create(file_ScriptName);

                fs.Close();

                TextWriter writeFile = new StreamWriter(file_ScriptName, false, Encoding.ASCII);
                writeFile.Close();
                foreach (CardsArtchive user in dbUser)
                {
                    File.AppendAllText(file_ScriptName, user.UserName + " " + user.Password + " " + clss_InfoPrint.profile.Name + "\n", Encoding.ASCII);
                }
                if (is_add_batch_cards_to_Archive)
                    try
                    {
                        System.Diagnostics.Process.Start(file_ScriptName);
                    }
                    catch { }
            }
            catch (IOException ex)
            {
                MessageBox.Show(ex.Message.ToString());
            }
            //=========================================================================== :do {/interface bridge add name=loopback; } on-error={:put "erorr"}
            //}
        }

        public void print_pdf(Dictionary<string, NewUserToAdd> dicUsers)
        {
            CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            CardsTemplate card = new CardsTemplate();
            string TemplateId = clss_InfoPrint.TemplateId.ToString();
            string TemplateName = clss_InfoPrint.TemplateName.ToString();
            SourceCardsTemplate Sourcecard = SqlDataAccess.Get_template_cards_By_Name(TemplateName);

            UmProfile profile = clss_InfoPrint.profile;
            //UserManager_Profile_UserManager profile = Global_Variable.UM_Profile.Find(x => x.Name == info_print["Public_Profile_Name"].ToString());

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = profile.Name;
            string price = profile.Price.ToString();
            string Validity = profile.Validity.ToString();
            string time = profile.UptimeLimit.ToString();  // or  time="5h";
            //string time = "720:00:00";  // or  time="5h";
            //time=utils.GetString_Time_in_Hour(time).ToString();
            string sizeTransfer = profile.TransferLimit.ToString();
            string SP = "";
            string numberPrint = "";
            string DatePrint = "";
            string Note_On_Pages_text = "";


            if (Sourcecard.type == "design")
            {
                card = new CardsTemplate();
                card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);

                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    if (card.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, card.cardsItems.Validity.unit_format);
                        }
                    }
                    if (card.cardsItems.Validity.title_show)
                    {
                        Validity = card.cardsItems.Validity.title_text + " " + Validity;
                    }
                }
                if (card.cardsItems.Time.Enable)
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, card.cardsItems.Time.unit_format, card.cardsItems.Time.unit_show);
                        if (card.cardsItems.Time.title_show)
                        {
                            time = card.cardsItems.Time.title_text + " " + time;
                        }

                    }
                }
                if (card.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size.unit_format, card.cardsItems.Size.unit_show);

                        if (card.cardsItems.Size.title_show)
                        {
                            sizeTransfer = card.cardsItems.Size.title_text + " " + sizeTransfer;
                        }

                    }
                }

                if (card.cardsItems.Number_Print.Enable)
                {
                    if (numberPrint != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrint = card.cardsItems.Number_Print.title_text + " " + numberPrint;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.get_BatchCards_my_sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();  
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (card.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (Show_sp.Code).ToString();
                        else
                            SP = (Show_sp.UserName).ToString();
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else
            {
                cardTable1 = new CardsTableDesg1();
                cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);

                if (cardTable1.cardsItems.Price.Enable)
                {
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = price + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    if (cardTable1.cardsItems.Validity.unit_show)
                    {
                        if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                        {
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                        }
                    }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    try
                    {
                        if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        }
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                    {
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                    }
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                    //int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();
                    numberPrint = (batchNumber + 1).ToString();
                }
                if (cardTable1.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                        if (Show_sp != null)
                        {
                            if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                                SP = (Show_sp.Code).ToString();
                            else
                                SP = (Show_sp.UserName).ToString();
                        }
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("numberPrint", numberPrint);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);

            CLS_Print print = new CLS_Print();

            if (Sourcecard.type == "design")
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);
            else if (Sourcecard.type == "table_Desigen1")
            {
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            }


            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            //MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            //try
            //{
            //    System.Diagnostics.Process.Start(pathfile);
            //}
            //catch { }
        }




    }
}
