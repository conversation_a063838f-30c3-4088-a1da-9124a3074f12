﻿using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Configuration;
using System.Data;
 
using System.Data.SQLite;
using Dapper;
using Newtonsoft.Json;
using System.Globalization;
using System.Windows.Forms;
using SmartCreator.Models.hotspot;
using SmartCreator.Entities;
using SmartCreator.Utils;


namespace SmartCreator.Data
{
    public class SqlDataAccess
    {
        #region connection database
        public string source222 = @"Data Source=db\localDB.db";
        public static string connection_string = "Data Source=" + AppContext.BaseDirectory + "\\db\\Smart.db;";
        public SqlDataAccess() { }
        //public string _localDB_path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
        //private static string _localDB_path3 = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path+ ";Version=3;";
        //Data Source =.\Smart.db;Version=3;

        public static string LoadConnectionString(string id = "Smartdb")
        {
            connection_string= utils.Get_SmartDB_ConnectionString();
            return utils.Get_SmartDB_ConnectionString();
            //return connection_string;
            //return ConfigurationManager.ConnectionStrings[id].ConnectionString;
        }
        public static string Load_MySqlConnectionString(string id = "Smartdb")
        {
            return ConfigurationManager.ConnectionStrings[id].ConnectionString;
        }
        public static string LoadLocalDB_ConnectionString(string id = "Smartdb")
        {
            return utils.Get_LocalDB_ConnectionString(Global_Variable.Mk_Router.localDB_path);
          string _localDB_path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            return _localDB_path;
        }

      

        #endregion

        #region All Forms Remeber state variable 
        public static List<Form_LoingState> GeUser_Login()
        {
            List<Form_LoingState> ilst = new List<Form_LoingState>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var output = cnn.Query("select * from setting_Mk_Login_Saved;", new DynamicParameters());
                    foreach (var item in output)
                    {
                        Form_LoingState f = JsonConvert.DeserializeObject<Form_LoingState>(item.values.ToString());
                        ilst.Add(f);
                    }

                    return ilst;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        }
       
        public static List<SourceMk_login_saved> Ge_SourceMk_login_saved()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                List<SourceMk_login_saved> ilst = null;
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var output = cnn.Query<SourceMk_login_saved>("select * from setting_Mk_Login_Saved;", new DynamicParameters());
                        ilst = output.ToList();
                        return ilst;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            }
        }
        public static SourceMk_login_saved Ge_SourceMk_login_saved(int id)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var output = cnn.QueryFirstOrDefault<SourceMk_login_saved>("select * from setting_Mk_Login_Saved where id=" + id + ";", new DynamicParameters());
                        return output;

                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            }
        }

        public static bool AddUser_Login(string value,bool insert=true,int id=-1)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        int affectedRows;
                        if (insert)
                         affectedRows = cnn.Execute("insert into setting_Mk_Login_Saved ([values]) values (@values)", new { values = value });
                        else
                            affectedRows = cnn.Execute("update  setting_Mk_Login_Saved set  [values]=@values where id=@id", new { values = value,id=id });

                        if (affectedRows > 0)
                            return true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return false;
            }
        }
        public static bool delete_User_Login(int id)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string query = "DELETE FROM setting_Mk_Login_Saved where id=" + (id) + ";  ";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var affectedRows = cnn.Execute(query);
                        if (affectedRows > 0)
                            return true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return false;
            }
        } 
        public static bool AddNewRouter(string localDB_path, string fileName, string comment, string Resources)
        {

            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    cnn.Execute("insert into routers ([soft_id],[mk_sn],[mk_code],[comment],[localDB_path],[fileName],[Resources]) values (@soft_id,@mk_sn,@mk_code,@comment,@localDB_path,@fileName,@Resources)",
                                new { soft_id = Global_Variable.Mk_resources.RB_Soft_id, mk_sn = Global_Variable.Mk_resources.RB_SN, mk_code = Global_Variable.Mk_resources.RB_code,
                                    comment = comment, localDB_path = localDB_path, fileName = fileName, Resources = Resources });
                    //RJMessageBox.Show("تمت العملية بنجاح");
                    return true;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
        }
        public static List<Mk_Routers> GetRouters(string id = "all")
        {
            lock (Smart_DataAccess.Lock_object)
            {
                List<Mk_Routers> ilst = new List<Mk_Routers>();
                string query = "select * from routers;";
                if (id != "all")
                    query = "select * from routers where mk_code='" + id + "';";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var output = cnn.Query(query, new DynamicParameters());
                        foreach (var item in output)
                        {

                            Mk_Routers f = new Mk_Routers();
                            f.soft_id = item.soft_id;
                            f.localDB_path = item.localDB_path;
                            f.localDB_fileName = item.fileName;
                            f.mk_code = item.mk_code;
                            f.mk_sn = item.mk_sn;
                            f.comment = item.comment;
                            f.Resources = JsonConvert.DeserializeObject<Base_Resources>(item.Resources.ToString());
                            ilst.Add(f);
                        }

                        return ilst;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            }
        }

        //public static  List<T>  Get_default_Connections_Db<T>()
        //{
        //    string query = "select * from Connections_Db where Default=1;";
        //    try
        //    {
        //        using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
        //        {
        //            return cnn.Query<T>(query, new DynamicParameters()).ToList();
                    
        //        }
        //    }
        //    catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        //}

        public static SourceSaveStateFormsVariable Get_SourceSaveStateFormsVariable2(string FormName )
        {
            lock (Smart_DataAccess.Lock_object)
            {
                SourceSaveStateFormsVariable formState = null;
                try
                {
                    string rb = Global_Variable.Mk_resources.RB_code;
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        if (FormName == "FormLogin")
                            formState = cnn.QueryFirstOrDefault<SourceSaveStateFormsVariable>("select * from Setting_SaveState_Forms_Variables where name='" + FormName + "' ;");
                        else
                            formState = cnn.QueryFirstOrDefault<SourceSaveStateFormsVariable>("select * from Setting_SaveState_Forms_Variables where name='" + FormName + "' and rb='" + rb + "' ;");

                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
                return formState;
            }
        }
        
        
        public static void Setting_SaveState_Forms_Variables2(string name, string type, string value)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                SourceSaveStateFormsVariable form_stave = Get_SourceSaveStateFormsVariable2(name);
                try
                {
                    string rb = Global_Variable.Mk_resources.RB_code;
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        if (form_stave != null)
                        {
                            if (name == "FormLogin")
                            {
                                var affectedRows = cnn.Execute("UPDATE Setting_SaveState_Forms_Variables SET [values] ='" + value + "' WHERE [name] ='" + name + "' ;");
                            }
                            else
                            {
                                var affectedRows = cnn.Execute("UPDATE Setting_SaveState_Forms_Variables SET [values] ='" + value + "' WHERE [name] ='" + name + "' and rb='" + rb + "' ;");
                                if (affectedRows > 0)
                                    return;
                            }
                        }
                        else
                        {
                            var affectedRows = cnn.Execute("insert into Setting_SaveState_Forms_Variables ([name],[type],[values],[rb]) values (@name,@type,@values,@rb)", new { name = name, type = type, values = value, rb = rb });
                        }
                        //RJMessageBox.Show("تمت العملية بنجاح");
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            }
        }

        #endregion
         
        #region All User Manager Function
        public static List<SourceCardsUserManager_fromDB> GetUsersManager()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";

            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                var output = cnn.Query<SourceCardsUserManager_fromDB>("select * from user", new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<SourceCardsUserManager_fromDB> GetUsersManager_NotDeleteFromServer()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";

                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    var output = cnn.Query<SourceCardsUserManager_fromDB>("select * from user where Delet_fromServer=0; ", new DynamicParameters());
                    return output.ToList();
                }
            }
        }
        public static List<SourcePymentUserManager_fromDB> GetPayment_NotDeleteFromServer()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                var output = cnn.Query<SourcePymentUserManager_fromDB>("select * from userprofile where Delet_fromServer=0; ", new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<SourceSessionUserManager_FromDB> GetSession_NotDeleteFromServer()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    var output = cnn.Query<SourceSessionUserManager_FromDB>("select * from session where Delet_fromServer=0; ", new DynamicParameters());
                    return output.ToList();
                }
            }
        }
        public static void SaveCardsUserManager(SourceCardsUserManager_fromDB users)
        {
            using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
            {
                cnn.Execute("insert into user (sn,userName) values (@sn,@userName", users);
            }
        }
        public static void Set_Users_disable_LocalDB()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            string query = "UPDATE user SET Delet_fromServer = 1 , activeSessions = 0 WHERE Delet_fromServer = 0;";

            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
        public static void Set_Pyment_disable_LocalDB()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            string query = "UPDATE userprofile SET Delet_fromServer = 1 WHERE Delet_fromServer = 0;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
        public static void Set_Session_disable_LocalDB()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";

            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE session SET Delet_fromServer = 1 WHERE Delet_fromServer = 0;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
        public static string Add_UM_user_to_LocalDB_sqlite2(List<SourceCardsUserManager_fromDB> UM_Users, bool is_insert = true, bool AddAfterPrint = false)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                string status = "true";
                string query = "";

                if (AddAfterPrint)
                {
                    query =
                        "INSERT OR IGNORE into user ("
                        + "[sn], "
                        + "[idHX], "
                        + "[cusName], "
                        + "[userName], "
                        + "[password], "
                        + "[regDate], "
                        + "[firstName], "
                        + "[lastName], "
                        + "[descr],"
                        + "[phone],"
                        + "[location],"
                        + "[email],"
                        + "[callerId],"
                        + "[uptimeLimit],"
                        + "[transferLimit],"


                        + "[sharedUsers],"
                        + "[moneyPercentage],"
                        + "[moneyTotal],"

                        + "[actualLimTransfer],"
                        + "[actualLimUptime],"
                        + "[actualProfileName],"

                        + "[spId],"
                        + "[is_spPercentage],"
                        + "[spName],"
                        + "[spPercentage],"

                        + "[numberPrintedId],"

                        + "[sn_userName],"
                        + "[date_added_Localdb],"
                        + "[Delet_fromServer],"
                        + "[status] "

                        + ") "
                        + "values ("
                        + "@sn, "
                        + "@idHX, "
                        + "@cusName, "
                        + "@userName,"
                        + "@password, "
                        + "@regDate, "
                        + "@firstName, "
                        + "@lastName,"
                        + "@descr,"
                        + "@phone,"
                        + "@location,"
                        + "@email,"
                        + "@callerId,"
                        + "@uptimeLimit,"
                        + "@transferLimit,"


                        + "@sharedUsers,"
                        + "@moneyPercentage,"
                        + "@moneyTotal,"

                        + "@actualLimTransfer,"
                        + "@actualLimUptime,"
                        + "@actualProfileName,"



                        + "@spId,"
                        + "@is_spPercentage,"
                        + "@spName,"
                        + "@spPercentage,"

                        + "@numberPrintedId,"

                        + "@sn_userName,"
                        + "@date_added_Localdb,"
                        + "@Delet_fromServer,"
                        + "@status "
                        + "); ";
                }
                else
                {
                    if (is_insert)
                    {
                        query =
                        "INSERT OR IGNORE into user ("
                        + "[sn], "
                        + "[sn_userName], "
                        + "[cusName], "
                        + "[userName], "
                        + "[password], "
                        + "[disabled], "
                        + "[firstName], "
                        + "[lastName], "
                        + "[descr],"
                        + "[phone],"
                        + "[location],"
                        + "[email],"
                        + "[callerId],"
                        + "[actualProfileName],"
                        + "[uptimeUsed],"
                        + "[downloadUsed],"
                        + "[uploadUsed],"
                        + "[lastSeenAt],"
                        + "[activeSessions],"
                        + "[sharedUsers],"
                        + "[spId],"
                        + "[is_spPercentage],"
                        + "[spName],"
                        + "[spPercentage],"
                        + "[date_added_Localdb],"
                        + "[Delet_fromServer],"
                        + "[actualLimTransfer],"
                        + "[actualLimUptime],"
                        + "[profileTillTime],"
                        + "[profileTimeLeft],"
                        + "[idHX]"

                        + ") "
                        + "values ("
                        + "@sn, "
                        + "@sn_userName, "
                        + "@cusName, "
                        + "@userName,"
                        + "@password, "
                        + "@disabled, "
                        + "@firstName, "
                        + "@lastName,"
                        + "@descr,"
                        + "@phone,"
                        + "@location,"
                        + "@email,"
                        + "@callerId,"
                        + "@actualProfileName,"
                        + "@uptimeUsed,"
                        + "@downloadUsed,"
                        + "@uploadUsed,"
                        + "@lastSeenAt,"
                        + "@activeSessions,"
                        + "@sharedUsers,"
                        + "@spId,"
                        + "@is_spPercentage,"
                        + "@spName,"
                        + "@spPercentage,"
                        + "@date_added_Localdb,"
                        + "@Delet_fromServer,"
                        + "@actualLimTransfer,"
                        + "@actualLimUptime,"
                        + "@profileTillTime,"
                        + "@profileTimeLeft,"
                        + "@idHX"
                        + "); SELECT last_insert_rowid(); ";
                    }
                    else
                    {
                        query =
                           "update user set "
                           + "[disabled]=@disabled, "
                           + "[firstName]=@firstName, "
                           + "[lastName]=@lastName, "
                           + "[descr]=@descr,"
                           + "[phone]=@phone,"
                           + "[location]=@location,"
                           + "[email]=@email,"
                           + "[callerId]=@callerId,"
                           + "[actualProfileName]=@actualProfileName,"
                           + "[uptimeUsed]=@uptimeUsed,"
                           + "[downloadUsed]=@downloadUsed,"
                           + "[uploadUsed]=@uploadUsed,"
                           + "[lastSeenAt]=@lastSeenAt,"
                           + "[activeSessions]=@activeSessions,"
                           + "[sharedUsers]=@sharedUsers,"
                           + "[spId]=@spId,"
                           + "[spName]=@spName,"
                           + "[Delet_fromServer]=@Delet_fromServer,"
                           + "[spPercentage]=@spPercentage,"

                           + "[actualLimTransfer]=@actualLimTransfer,"
                           + "[actualLimUptime]=@actualLimUptime,"
                           + "[profileTillTime]=@profileTillTime,"
                           + "[profileTimeLeft]=@profileTimeLeft "
                           + " WHERE id = @id;";

                    }
                }
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
                return status;
            }
        }
        public static string Update_UM_user_to_LocalDB_AfterPymentGet(List<SourceCardsUserManager_fromDB> UM_Users)
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            string status = "true";
            string query =
                   "update user set "
                   + "[regDate]=@regDate, "
                   + "[moneyTotal]=@moneyTotal, "
                   + "[uptimeLimit]=@uptimeLimit, "
                   + "[transferLimit]=@transferLimit, "
                   + "[actualProfileName]=@actualProfileName,"
                   + "[actualLimUptime]=@actualLimUptime,"
                   + "[actualLimTransfer]=@actualLimTransfer,"
                   + "[profileTimeLeft]=@profileTimeLeft,"
                   + "[profileTransferLeft]=@profileTransferLeft,"
                   + "[countProfile]=@countProfile "

                   + " WHERE id = @id;";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    cnn.Execute(query, UM_Users, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public static string Update_UM_user_to_LocalDB_AfterSessionGet(List<SourceCardsUserManager_fromDB> UM_Users)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                if (UM_Users.Count == 0)
                    return "false";
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                string status = "true";
                string query =
                       "update user set "
                       + "[radius]=@radius, "
                       + "[nasPortId]=@nasPortId, "
                       + "[firstUse]=@firstUse "
                       + " WHERE id = @id;";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowEvect = cnn.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
                return status;
            }
        }

        public static string Update_UM_user_to_LocalDB_sqlite2(List<SourceCardsUserManager_fromDB> UM_Users,bool check_byID=true)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string _update_check = " WHERE id = @id;";

                _update_check = " [location]=@location," +
                    "[spId]=@spId," +
                    "[spName]=@spName," +
                    "[status]=@status," +
                    "[spPercentage]=@spPercentage," +
                    "[actualProfileName]=@actualProfileName," +
                    "[is_spPercentage]=@is_spPercentage ," +
                    "[Delet_fromServer]=@Delet_fromServer  " +
                    " WHERE id = @id;";

                if (!check_byID)
                    _update_check = " [Delet_fromServer]=@Delet_fromServer " +
                        " WHERE sn_userName = @sn_userName;";

                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                string status = "true";
                string query =
                    "update user set "
                    + "[disabled]=@disabled, "
                    + "[firstName]=@firstName, "
                    + "[lastName]=@lastName, "
                    + "[descr]=@descr,"
                    + "[phone]=@phone,"
                    + "/*[location]=@location,*/"
                    + "[email]=@email,"
                    + "[callerId]=@callerId,"
                    //+ "[actualProfileName]=@actualProfileName,"
                    + "[uptimeUsed]=@uptimeUsed,"
                    + "[downloadUsed]=@downloadUsed,"
                    + "[uploadUsed]=@uploadUsed,"
                    + "[lastSeenAt]=@lastSeenAt,"
                    + "[activeSessions]=@activeSessions,"
                    + "[sharedUsers]=@sharedUsers,"
                    //+ "[spId]=@spId,"
                    //+ "[spName]=@spName,"
                    //+ "[status]=@status,"
                    //+ "[Delet_fromServer]=@Delet_fromServer, "
                    //+ "[spPercentage]=@spPercentage,"
                    //+ "[is_spPercentage]=@is_spPercentage "

                    //+ "[actualLimTransfer]=@actualLimTransfer,"
                    //+ "[actualLimUptime]=@actualLimUptime,"
                    //+ "[profileTillTime]=@profileTillTime,"
                    //+ "[profileTimeLeft]=@profileTimeLeft "

                    + _update_check;
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        //cnn.ExecuteAsync(query, UM_Users, sqLiteTransaction);
                        var output = cnn.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
                return status;
            }
        }
        public static string Add_UM_Pyement_to_LocalDB(List<SourcePymentUserManager_fromDB> UM_Pyement, bool is_insert = true)
        {
            string status = "true";
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            try
            {
                string query = "";

                if (is_insert)
                {
                    query = "INSERT OR IGNORE into userprofile (" +
                           "[sn], " +
                           "[idHX], " +
                           "[userName], " +
                           "[sn_userName], " +
                           "[userId], " +
                           "[price], " +
                           "[validUntil], " +
                           "[profileValiday], " +
                           "[added], " +
                           "[profileName], " +
                           "[fk_sn_userName_User], " +
                           "[fk_User_localDB_id], " +
                           "[Delet_fromServer]" +
                           ") " +
                           "values (" +
                           "@sn, " +
                           "@idHX, " +
                           "@userName, " +
                           "@sn_userName, " +
                           "@userId," +
                           "@price, " +
                           "@validUntil, " +
                           "@profileValiday, " +
                           "@added, " +
                           "@profileName, " +
                           "@fk_sn_userName_User," +
                           "@fk_User_localDB_id," +
                           "@Delet_fromServer" +
                           ") ";
                }
                else
                {
                    query = "update userprofile set " +
                         //"[price]=@price, " +
                         //"[fk_sn_userName_User]=@fk_sn_userName_User, " +
                         //"[fk_User_localDB_id]=@fk_User_localDB_id, " +
                         //"[profileName]=@profileName, " +
                         "[actualLimUptime]=@actualLimUptime, " +
                         "[actualLimTransfer]=@actualLimTransfer, " +
                         "[profileValiday]=@profileValiday, " +
                         "[Delet_fromServer]=@Delet_fromServer " +
                          " WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    //cnn.ExecuteAsync(query, UM_Users, sqLiteTransaction);
                    var output = cnn.Execute(query, UM_Pyement, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return "false"; }
            return status;
        }
        public static string Add_UM_Session_to_LocalDB(List<SourceSessionUserManager_FromDB> UM_Session, bool is_insert = true)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string status = "true";
                //Set_Session_disable_LocalDB();
                try
                {
                    string query = "";
                    string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                    if (is_insert)
                    {
                        query = "INSERT OR IGNORE into session (" +
                                "[sn], " +
                                "[idHX], " +
                                "[userName], " +
                                "[sn_userName], " +
                                "[userId], " +
                                "[nasPortId], " +
                                "[callingStationId], " +
                                "[ipUser], " +
                                "[ipRouter], " +
                                "[status_str], " +
                                "[active], " +
                                "[fromTime], " +
                                "[tillTime], " +
                                "[upTime], " +
                                "[bytesDownload], " +
                                "[bytesUpload], " +
                                "[fk_sn_userName_User], " +
                                "[fk_User_localDB_id], " +
                                "[Delet_fromServer]" +
                                ") " +
                                "values (" +
                                "@sn, " +
                                "@idHX, " +
                                "@userName, " +
                                "@sn_userName, " +
                                "@userId," +
                                "@nasPortId, " +
                                "@callingStationId, " +
                                "@ipUser, " +
                                "@ipRouter," +
                                "@status_str," +
                                "@active," +
                                "@fromTime," +
                                "@tillTime," +
                                "@upTime," +
                                "@bytesDownload," +
                                "@bytesUpload," +
                                "@fk_sn_userName_User," +
                                "@fk_User_localDB_id," +
                                "@Delet_fromServer" +
                                ") ";
                    }

                    else
                    {
                        query = "update session set " +
                         //"[status]=@status, " +
                         //"[active]=@active, " +
                         //"[fromTime]=@fromTime, " +
                         //"[tillTime]=@tillTime, " +
                         //"[upTime]=@upTime, " +
                         //"[bytesDownload]=@bytesDownload, " +
                         //"[bytesUpload]=@bytesUpload, " +
                         //"[fk_sn_userName_User]=@fk_sn_userName_User, " +
                         //"[fk_User_localDB_id]=@fk_User_localDB_id, " +
                         "[Delet_fromServer]=@Delet_fromServer " +
                         //"[Delet_fromServer]=@fk_User_localDB_id " +
                         " WHERE id = @id;";

                    }
                    using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var output = cnn.Execute(query, UM_Session, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
                return status;
            }
        }
        public static void Add_Batch_Cards(BatchCard data, int server = 0,bool add_to_Last=false)
        {
            data.Rb = Global_Variable.Mk_resources.RB_code;
            List<BatchCard> s = new List<BatchCard>();
            string AddNew = "";
            if (add_to_Last==false)
                AddNew= " UPDATE my_sequence SET seq=@BatchNumber WHERE Bame='BatchCards' and Rb=@rb ;"; 
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string Qury = "INSERT into BatchCards (" +
                            "[BatchNumber], " +
                            "[AddedDate], " +
                            "[Count], " +
                            "[Sn_from], " +
                            "[Sn_to], " +
                            "[Server], " +
                            "[Rb], " +
                            "[ProfileName], " +
                            "[Rb] ," +
                            "[SpId] " +
                            ") " +
                            "values (" +
                            "@BatchNumber, " +
                            "@Date, " +
                            "@Count, " +
                            "@Sn_from, " +
                            "@Sn_to," +
                            "@Server, " +
                            "@Rb, " +
                            "@ProfileName, " +
                            "@Rb, " +
                            "@SpId " +
                            "); " +
                            AddNew;
                cnn.Open();
                //var sqLiteTransaction = cnn.BeginTransaction();
                var output = cnn.Execute(Qury, data);
                //sqLiteTransaction.Commit();
            }
        }

        //public static List<SellingPints> GetSellingPints()
        //{
        //    List<SellingPints> ilst = new List<SellingPints>();
        //    string query = "select * from SellingPints where  rb='" + Global_Variable.Mk_resources.RB_code + "'  ;";
        //    //string query = "select * from SellingPints;";
        //    try
        //    {
        //        using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
        //        {
        //            var output = cnn.Query<SellingPints>(query, new DynamicParameters());
        //            return output.ToList();
        //        }
        //    }
        //    catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        //}
        //public static SellingPints GetSellingPints(string code)
        //{
             
        //    string query = "select * from SellingPints where code='"+code+"'  and rb='" + Global_Variable.Mk_resources.RB_code+"'  ;";
        //    try
        //    {
        //        using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
        //        {
        //            var output = cnn.QueryFirstOrDefault<SellingPints>(query);
        //            return output;
        //        }
        //    }
        //    catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        //}

        public static double Get_last_SN_UserManager()
        {
            double sn = 0;
            string query = "SELECT * FROM user ORDER BY sn DESC LIMIT 1; ";
            try
            {
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    var output = cnn.QueryFirst(query, new DynamicParameters());
                    sn = output.sn;
                    return sn;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return sn; }
        }
        public static List<SourceCardsUserManager_fromDB> GetUsersManager_By_PageFilter()
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                // string Qury = "SELECT  " +
                //"0 as 'up_down_inByte' ," +
                //"0 as 'download_inByte' ," +
                //"0 as 'upload_inByte' ," +
                //"sn_user_name as 'sn_user_name' ," +
                //"tn as 'isLocalDB' ," +
                //"id_sp as 'location' ," +
                //"' ' as 'الحالة' ," +
                //"sn  as 'رقم تسلسل' ," +
                //"' ' as 'اخر ظهور' ," +
                //"selling_point as 'نقطة البيع' ," +
                //"'0' as 'الرفع' ," +
                //"'0' as 'التحميل' ," +
                //"'0' as 'اجمالي تحميل + رفع'," +
                //"'0' as 'الوقت المستخدم'," +
                //"profile as 'الباقة' ," +
                //"password as 'كلمة السر'," +
                //"username as 'الاسم'," +
                //"customer as 'customer'," +
                //"'false' as 'disabled' " +
                //"FROM users WHERE  mk_sn='" + MyDataClass.RB_SN + "';";
                //sqlite > SELECT datetime(1092941466, 'unixepoch', 'localtime');
                string Qury = "SELECT  " +
               "id," +
               "sn," +
               "sn_userName," +
               "userName," +
               "password," +
               "userName," +
               "disabled," +
               "regDate," +
               "descr," +
               "uptimeLimit," +
               "transferLimit," +
               "uptimeUsed," +
               "downloadUsed," +
               "uploadUsed," +
               "lastSeenAt," +
               "activeSessions," +
               "moneyTotal," +
               "actualProfileName," +
               "spId," +
               "spName," +
               "numberPrintedName," +
               "Delet_fromServer," +
               "status," +
               "countProfile," +
               "idHX " +
               "FROM user WHERE  Delet_fromServer=0;";


                //"0 as 'download_inByte' ," +
                //"0 as 'upload_inByte' ," +
                //"sn_user_name as 'sn_user_name' ," +
                //"tn as 'isLocalDB' ," +
                //"id_sp as 'location' ," +
                //"' ' as 'الحالة' ," +
                //"sn  as 'رقم تسلسل' ," +
                //"' ' as 'اخر ظهور' ," +
                //"selling_point as 'نقطة البيع' ," +
                //"'0' as 'الرفع' ," +
                //"'0' as 'التحميل' ," +
                //"'0' as 'اجمالي تحميل + رفع'," +
                //"'0' as 'الوقت المستخدم'," +
                //"profile as 'الباقة' ," +
                //"password as 'كلمة السر'," +
                //"username as 'الاسم'," +
                //"customer as 'customer'," +
                //"'false' as 'disabled' " +
                //"FROM users WHERE  mk_sn='" + MyDataClass.RB_SN + "';";

                var output = cnn.Query<SourceCardsUserManager_fromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<CardsUserManagerFromDB> GetUsersManager_By_PageFilter2(SourceCardsUserManager_fromMK user=null)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                {
                    string where = "WHERE u.Delet_fromServer=0 ";
                    if (user != null)
                    {
                        double sn = Int32.Parse(user.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                        string sn_userName = sn + "-" + user.userName;
                        where = where + " and u.sn_userName='" + sn_userName + "' ";
                    }
                    string Qury = "SELECT " +
                        "u.id," +
                   "u.sn," +
                   "u.sn_userName," +
                   "u.userName," +
                   "u.password," +
                   "u.userName," +
                   "u.disabled," +
                   "u.regDate," +
                   "u.descr," +
                   //"uptimeLimit," +
                   //"transferLimit," +
                   "u.uptimeUsed," +
                   "u.downloadUsed," +
                   "u.uploadUsed," +
                   "u.lastSeenAt," +
                   "u.activeSessions," +
                   "u.moneyTotal," +
                   "u.actualProfileName," +
                   "u.spId," +
                   "u.spName," +
                   "u.numberPrintedName," +
                   "u.Delet_fromServer," +
                   "u.firstUse," +
                   "u.status," +
                   "u.CusName," +
                   "u.nasPortId," +
                   "u.radius," +
                   //"countProfile," +
                   "u.idHX, " +
                   "count(c.id) as countProfile," +
                   "sum(c.profileValiday) as profileValiday," +
                   "sum(c.actualLimUptime) as uptimeLimit," +
                   "sum(c.actualLimTransfer) as transferLimit  " +
                   "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id " +
                  where +
                   "GROUP BY u.id " +
                   "ORDER BY u.id DESC ;";

                    var output = cnn.Query<CardsUserManagerFromDB>(Qury, new DynamicParameters());
                    return output.ToList();
                }
            }
        }
        public static List<string> Get_UsersManager_from_if_RunOffline()
        {
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string Qury = "SELECT userName FROM user WHERE Delet_fromServer=0 ;";
                var output = cnn.Query<string>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<string> Get_Hotspot_from_if_RunOffline()
        {
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string Qury = "SELECT userName FROM userHS WHERE Delet_fromServer=0 ;";
                var output = cnn.Query<string>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }

        public static List<CardsUserManagerFromDB> GetUsersManager_By_FirstUse(string query)
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                
                string Qury = "SELECT " +
                    "u.id," +
               "u.sn," +
               "u.sn_userName," +
               "u.userName," +
               "u.password," +
               "u.userName," +
               "u.disabled," +
               "u.regDate," +
               "u.descr," +
               //"uptimeLimit," +
               //"transferLimit," +
               "u.uptimeUsed," +
               "u.downloadUsed," +
               "u.uploadUsed," +
               "u.lastSeenAt," +
               "u.activeSessions," +
               "u.moneyTotal," +
               "u.actualProfileName," +
               "u.spId," +
               "u.spName," +
               "u.numberPrintedName," +
               "u.Delet_fromServer," +
               "u.firstUse," +
               "u.status," +
               "u.CusName," +
               "u.nasPortId," +
               "u.radius," +
               //"countProfile," +
               "u.idHX, " +
               "count(c.id) as countProfile," +
               "sum(c.profileValiday) as profileValiday," +
               "sum(c.actualLimUptime) as uptimeLimit," +
               "sum(c.actualLimTransfer) as transferLimit  " +
               "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id" +
               query +
              
               //" WHERE u.firstUse >="+start+" AND u.firstUse<="+end+"  " +

               //"WHERE u.Delet_fromServer=0 " +
               " GROUP BY u.id;";

                var output = cnn.Query<CardsUserManagerFromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<class_Report_monthly_or_Dayliy> GetUsersManager_By_FirstUse_byDays(string query, string month_or_Day = "\"%m-%Y\"")
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                string Qury = "SELECT " +
                  "strftime(" + month_or_Day + ", u.firstUse, 'unixepoch') date," +
                  "sum(c.price) as moneyTotal ," +
                  "count(u.id) as count " +
                  "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id " +
                  query +
                  " group by date;";
                var output = cnn.Query<class_Report_monthly_or_Dayliy>(Qury, new DynamicParameters());
                return output.ToList();

                //string Qury = "SELECT " +
                //   "strftime("+ month_or_Day + ", u.firstUse, 'unixepoch') date," +
                //   "sum(c.price) as moneyTotal ," +
                //   "count(u.id) as count " +
                //   "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id " +
                //   query +
                //   " group by date;";
                //var output = cnn.Query<class_Report_monthly_or_Dayliy>(Qury, new DynamicParameters());
                //return output.ToList();

            }
        }
        public static List<class_Report_monthly_or_Dayliy> GetUsersManager_By_byDays_Downloads_UptimeUsed(string query, string month_or_Day = "\"%m-%Y\"")
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                ////SELECT strftime('%Y-%m-%d', fromTime, 'unixepoch') date, sum(bytesDownload + bytesUpload) as download  ,sum(upTime) as uptime  FROM session
                ////group by date
                //string Qury = "SELECT " +
                //   "strftime('%Y-%m-%d', fromTime, 'unixepoch') date," +
                //   "sum(bytesDownload + bytesUpload) as download ," +
                //   "sum(upTime) as uptime " +
                //   "FROM session " +
                //   //"count(u.id) as count " +
                //   //"FROM session u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id " +
                //   query +
                //   " group by date;";
                var output = cnn.Query<class_Report_monthly_or_Dayliy>(query, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<SourceSessionUserManager_FromDB> Get_Nas_Port()
        {
            List<SourceSessionUserManager_FromDB> s = new List<SourceSessionUserManager_FromDB>();
            //string Qury = "SELECT DISTINCT  nas_prot   FROM sessions where mk_sn='" + MyDataClass.RB_SN + "' ORDER BY nas_prot ;";
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                string Qury = "SELECT DISTINCT  nasPortId   FROM session ORDER BY nasPortId ;";
                var output = cnn.Query<SourceSessionUserManager_FromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<SourceSessionUserManager_FromDB> Get_Radius()
        {
            List<SourceSessionUserManager_FromDB> s = new List<SourceSessionUserManager_FromDB>();
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                string Qury = "SELECT DISTINCT  ipRouter   FROM session ORDER BY ipRouter ;";
                var output = cnn.Query<SourceSessionUserManager_FromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<Class_Batch_cards> Get_Batch_Cards(string server= "usermanager")
        {
            //string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            List<Class_Batch_cards> s = new List<Class_Batch_cards>();
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string Qury = "SELECT *  FROM BatchCards where server='" + server + "' ORDER BY date DESC;";
                //if (by_servver == false) 
                // Qury = "SELECT *  FROM Batch_cards ;";
                var output = cnn.Query<Class_Batch_cards>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public static List<CardsUserManagerFromDB> GetUsersManager_By_FirstUse22(string query)
        {
            //string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(LoadLocalDB_ConnectionString()))
            {

                string Qury = "SELECT " +
                    "u.id," +
               "u.sn," +
               "u.sn_userName," +
               "u.userName," +
               "u.password," +
               "u.userName," +
               "u.disabled," +
               "u.regDate," +
               "u.descr," +
               //"uptimeLimit," +
               //"transferLimit," +
               "u.uptimeUsed," +
               "u.downloadUsed," +
               "u.uploadUsed," +
               "u.lastSeenAt," +
               "u.activeSessions," +
               "u.moneyTotal," +
               "u.actualProfileName," +
               "u.spId," +
               "u.spName," +
               "u.numberPrintedName," +
               "u.Delet_fromServer," +
               "u.firstUse," +
               "u.status," +
               "u.CusName," +
               "u.nasPortId," +
               "u.radius," +
               //"countProfile," +
               "u.idHX, " +
               "count(c.id) as countProfile," +
               "sum(c.profileValiday) as profileValiday," +
               "sum(c.actualLimUptime) as uptimeLimit," +
               "sum(c.actualLimTransfer) as transferLimit  " +
               "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id" +
               query +

               //" WHERE u.firstUse >="+start+" AND u.firstUse<="+end+"  " +

               //"WHERE u.Delet_fromServer=0 " +
               " GROUP BY u.id;";

                var output = cnn.Query<CardsUserManagerFromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }

        public static int Get_lastID_Batch_cards()
        {
            return get_BatchCards_my_sequence();
            int newId = 0;

            string rb = Global_Variable.Mk_resources.RB_SN;
            string rb_code = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(LoadLocalDB_ConnectionString()))
            {
               return get_BatchCards_my_sequence();



                string Qury = $"select seq from sqlite_sequence where name='BatchCards';";
                var output = cnn.QueryFirstOrDefault(Qury, new DynamicParameters());
                if (output != null)
                     newId = Convert.ToInt32(output.seq);
                return newId;
            }
        }
        public static Batch_cards_FromDB Search_Number_Batch_cards(string number)
        {

            using (IDbConnection cnn = new SQLiteConnection(LoadLocalDB_ConnectionString()))
            {
                string Qury = "select * from BatchCards where batchNumber='" + number+"';";
                var output = cnn.QueryFirstOrDefault<Batch_cards_FromDB>(Qury);
                return output;
            }
        }

        public static int get_BatchCards_my_sequence()
        {
            int seq = 0;
            string rb=Global_Variable.Mk_resources.RB_code;
            try
            {
                using (IDbConnection cnn = (Smart_DataAccess.GetConnSmart()))
                //using (IDbConnection cnn = new SQLiteConnection(LoadLocalDB_ConnectionString()))
                {
                    var output = cnn.QueryFirstOrDefault("select seq from my_sequence where name='BatchCards' and rb='"+rb+"';", new DynamicParameters());
                    if (output != null)
                        return Convert.ToInt32(output.seq);
                    else
                    {
                            var rowEfect = cnn.Execute("INSERT INTO my_sequence ([name],[seq],[rb]) VALUES(@name,@seq,@rb)", new { name = "BatchCards", seq = 0, rb = rb });
                            if (rowEfect > 0)
                                return seq;
                                //return output.seq;
                    }
                } 
            }
            catch (Exception ex) { MessageBox.Show("get_BatchCards_my_sequence\n"+ex.ToString()); return 0; }
            return seq;
        }
        public static DataTable RunSqlCommandAsDatatable(string Qury)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                //DataTable dt = new DataTable();
                //string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
                //using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
                //{
                //    var output = cnn.Query<CardsUserManagerFromDB>(Qury);
                //    return output.AsEnumerable();
                //}

                DataTable dt = new DataTable();
                try
                {


                    SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, Sql_DataAccess.connection_string);
                    DataTable tbFound = new DataTable();
                    adapter.Fill(tbFound);
                    dt = tbFound;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); return null; }

                return dt;
            }
        }

        #endregion

        #region All Template Fucnctionn
        public static List<SourceCardsTemplate> Get_All_SourceCardsTemplate(string type, bool all = false)
        {
            //lock (Smart_DataAccess.Lock_object)
            //{
                string query = "";
                string rb = Global_Variable.Mk_resources.RB_SN;
                string rb_code = Global_Variable.Mk_resources.RB_code;

                List<SourceCardsTemplate> ilst = new List<SourceCardsTemplate>();
                if (all)
                    query = $"SELECT  * FROM CardsTemplate WHERE rb='" + rb + "' or rb='" + rb_code + "' ;";
                else
                    query = $"SELECT  * FROM CardsTemplate where (rb='{rb}' or rb='{rb_code}')  and type='{type}' ;";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(utils.Get_SmartDB_ConnectionString()))
                    {
                        var output = cnn.Query<SourceCardsTemplate>(query, new DynamicParameters());
                        return output.ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            //}

        }

        public static SourceCardsTemplate Get_template_cards_By_Name(string name)
        {
            lock (Smart_DataAccess.Lock_object) { 
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
            string query = $"select * from CardsTemplate  where name='{name}' and (rb='{rb_sn}' or rb='{rb}' );";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var output = cnn.QueryFirstOrDefault<SourceCardsTemplate>(query);
                    return output;
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); return null; }
        }
        }
        public static SourceCardsTemplate Get_template_cards_By_id(string id)
        {
            //lock (Smart_DataAccess.Lock_object)
            //{
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                //string query = $"select * from CardsTemplate where id={id} and (rb='{rb}' or rb='{rb_sn}') ;" ;
                string query = $"select * from CardsTemplate where id={id}  ;" ;
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var output = cnn.QueryFirstOrDefault<SourceCardsTemplate>(query);
                        return output;
                    }
                }
                catch (Exception ex) { MessageBox.Show(ex.Message); return null; }
            //}
        }

        public static bool Add_New_Template(SourceCardsTemplate Template, bool is_insert = true,bool from_loadBackup=false)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                bool status = false;
                //=========== check if found==========
                if (is_insert)
                {
                    if (Get_template_cards_By_Name(Template.name) != null)
                    {
                        if(from_loadBackup==false)
                            RJMessageBox.Show("الاسم موجود مسبقا", "ok");
                        return false;
                    }

                    try
                    {
                        using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                        {
                            if (Global_Variable.Mk_resources.RB_SN != null)
                            {
                                Template.rb = Global_Variable.Mk_resources.RB_SN;
                            }
                            int a = cnn.Execute("insert into CardsTemplate ([name],[type],[values],[rb]) values (@name,@type,@values,@rb)", Template);
                            if (a > 0)
                                return true;
                        }
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                else
                {
                    string query = "update CardsTemplate set [values]=@values  WHERE id = @id;";
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var output = cnn.Execute(query, Template, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        return true;
                    }
                }
                return status;
            }
        }
        public static bool delete_template(int id)
        {
            lock (Smart_DataAccess.Lock_object)
            {
                string query = "DELETE FROM CardsTemplate where id=" + (id) + ";  ";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var affectedRows = cnn.Execute(query);
                        if (affectedRows > 0)
                            return true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return false;
            }
        }

        #endregion

        public static bool add_Edit_Profile_hotspot2(Hotspot_Local_Profile2 profle, bool is_insert = true)
        {
            bool status = false;
            string rb = Global_Variable.Mk_resources.RB_code;
            if (is_insert)
            {
                try
                {
                    string query = "select * from Hotspot_Profile_Hotspot_local where Name='" + profle.Name + "'  and rb='" + rb + "'  ;";
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        var output = cnn.Query<Hotspot_Local_Profile2>(query);
                        if( output.ToList().Count > 0)
                        {
                            RJMessageBox.Show("الاسم موجود مسبقا");
                            return false;
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                    {
                        profle.rb = rb;
                        int a = cnn.Execute("insert into Hotspot_Profile_Hotspot_local " +
                            "([Name],[Price_Sales],[Price_display],[Validity],[uptimeLimit],[transferLimit],[add_Smart_Scripts],[Save_time],[Save_download],[Save_session],[ByDayOrHour],[link_hotspot_profile],[rb],[Is_percentage],[Percentage],[PercentageType]) values" +
                            " (@Name,@Price_Sales,@Price_display,@Validity,@uptimeLimit,@transferLimit,@Add_Smart_Scripts,@Save_time,@Save_download,@Save_session,@ByDayOrHour,@link_hotspot_profile,@rb,@Is_percentage,@Percentage,@PercentageType )", profle);
                        if (a > 0)
                            return true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            }
            else
            {
                string query = "update Hotspot_Profile_Hotspot_local set " +
                    //"[Name]=@Name," +
                    "[Price_Sales]=@Price_Sales,  " +
                    "[Price_display]=@Price_display,  " +
                    "[Validity]=@Validity,  " +
                    "[uptimeLimit]=@uptimeLimit,  " +
                    "[transferLimit]=@transferLimit,  " +
                    "[add_Smart_Scripts]=@Add_Smart_Scripts,  " +
                    "[Save_time]=@Save_time,  " +
                    "[Save_download]=@Save_download,  " +
                    "[Save_session]=@Save_session,  " +
                    "[ByDayOrHour]=@ByDayOrHour,  " +
                    "[Is_percentage]=@Is_percentage,  " +
                    "[Percentage]=@Percentage,  " +
                    "[PercentageType]=@PercentageType,  " +
                    "[link_hotspot_profile]=@link_hotspot_profile  " +
                    "WHERE id = @id;";
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var output = cnn.Execute(query, profle);
                    if (output > 0)
                        return true;
                }
            }
            return status;
            
        }
        public static bool delete_Profile_Hotspot2(string id)
        {
            string query = "DELETE FROM Hotspot_Profile_Hotspot_local where id=" + (id) + ";  ";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var affectedRows = cnn.Execute(query);
                    if (affectedRows > 0)
                        return true;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
            return false;
        }
        public static Hotspot_Local_Profile2 Get_Profile_Hotspot_By_id2(string id)
        {
            string rb = Global_Variable.Mk_resources.RB_code;
            string query = "select * from Hotspot_Profile_Hotspot_local " + " where id=" + (id) + " and rb='" + rb + "' ;";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var output = cnn.QueryFirstOrDefault<Hotspot_Local_Profile2>(query);
                    return output;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        }
        public static Hotspot_Local_Profile2 Get_Profile_Hotspot_By_Name2(string id)
        {
            string rb = Global_Variable.Mk_resources.RB_code;
            string query = "select * from Hotspot_Profile_Hotspot_local " + " where Name='" + (id) + "' and rb='" + rb + "' ;";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(LoadConnectionString()))
                {
                    var output = cnn.QueryFirstOrDefault<Hotspot_Local_Profile2>(query);
                    return output;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        }



    }
}
