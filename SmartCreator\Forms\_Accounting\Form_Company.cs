﻿using SmartCreator.Data;
using SmartCreator.Entities.Accounts;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    public partial class Form_Company : RJChildForm
    {
        Company company = new SmartCreator.Entities.Accounts.Company();
        public Form_Company()
        {
            InitializeComponent();

            this.RightToLeft = RightToLeft.No; 
            this.RightToLeftLayout = false;

            rjLabel1.Font =
                rjLabel2.Font =
                rjLabel3.Font =
                rjLabel5.Font =
                rjLabel4.Font =
                rjLabel6.Font =
                rjLabel7.Font =
                rjLabel10.Font =
                rjLabel16.Font =
                
                Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            btnSave.Font =lblTitle.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);


            utils utils1 = new utils();
            utils1.Control_textSize1(this);




        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Company where Rb='{Global_Variable.Mk_resources.RB_SN}';");

            Entities.Accounts.Company sp = new Entities.Accounts.Company();
            //sp.Code = txt_code.Text;
            sp.Name = txtUsername.Text;
            sp.Name_eng = txtUsername_Eng.Text;
            sp.Phone = txtPhone.Text;
            sp.Mobail = txtMobail.Text;
            sp.Address = txtAddress.Text;
            sp.Currency_name = txtCurncey.Text;
            sp.Currency_symbol = txtCurncey_symbol.Text;
            sp.Email = txtEmail.Text;
            sp.Rb = Global_Variable.Mk_resources.RB_SN;
            List<string> Fields = new List<string>();
            string[] aFields = { "Name_eng", "Name", "Address", "Mobail", "Phone", "Currency_name", "Currency_symbol", "Rb", "Email" };
            Fields.AddRange(aFields);
            if (foundsp <= 0)
            {
                lock (Smart_DataAccess.Lock_object)
                {
                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Company");
                    if (new_sp > 0)
                    {
                        RJMessageBox.Show("تم الحفظ");
                        return;
                    }
                    else
                        RJMessageBox.Show("خطاء");
                }
            }
            else
            {
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Company>("Company", Fields, $" where Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = smart_DataAccess.UpateTable(sp, sqlquery);
                        if (r > 0)
                        {
                            RJMessageBox.Show("تم الحفظ");
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch { }
            }
        }

        private void Form_Company_Load(object sender, EventArgs e)
        {
            txtUsername.Text = "شبكة لاسليكة";
            txtUsername_Eng.Text = "Smart-Net";
            txtPhone.Text = "+967 *********";
            txtMobail.Text = "+967 ********";
            txtCurncey.Text = "ريال";
            txtCurncey_symbol.Text = "YR";
            //txtEmail.Text = "<EMAIL>";
            txtAddress.Text = "";
            GetData();
        }
        private void GetData()
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Company where Rb='{Global_Variable.Mk_resources.RB_SN}';");
            if (foundsp > 0)
            {
                //company = smart_DataAccess.Load<Company>("");
                company = smart_DataAccess.Load<Company>($"select * from Company where Rb='{Global_Variable.Mk_resources.RB_SN}'").First();
                if (company != null)
                {
                    txtUsername.Text = company.Name;
                    txtUsername_Eng.Text = company.Name_eng;
                    txtAddress.Text = company.Address;
                    txtCurncey.Text = company.Currency_name;
                    txtCurncey_symbol.Text = company.Currency_symbol;
                    txtEmail.Text = company.Email;
                    txtMobail.Text = company.Mobail;
                    txtPhone.Text = company.Phone;
                }

            }
        }
    }
}
