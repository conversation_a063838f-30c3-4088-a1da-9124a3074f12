﻿//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;

//using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Numerics;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    [AutoGenerateTable(0)]
    public class BatchCard
    {
        //public BatchCard() { }
        [PrimaryKey, AutoIncrement, Required, Unique, Browsable(false)]
        public int Id { get; set; }
        [DisplayName("الدفعه"), Required]
        public int BatchNumber { get; set; }

        [DisplayName("التاريخ")]
        public DateTime? AddedDate { get; set; }

        [DisplayName("العدد")]
        public int Count { get; set; }

        [DisplayName("من رقم")]
        public long Sn_from { get; set; }

        [DisplayName("الي رقم")]
        public long Sn_to { get; set; }

        [DisplayName("الباقة"), StringLength(100)/*, Index*/]
        public string ProfileName { get; set; }
        [Browsable(false)]
        [DisplayName("رقم نقطة البيع")]
        public string SpCode { get; set; }
        //public int? SpId { get; set; }
        //[Browsable(false)]
        [DisplayName("نقطة البيع"), StringLength(200)]
        public string SpName { get; set; }
        [Browsable(false)]
        [Default(0),/* Index,*/ DisplayName("السيرفر")]
        public int Server { get; set; } = 0;//= "UserManager";
        [Default(0), DisplayName("عدد الانتظار")]
        public int Count_waiting { get; set; } = 0;
        [Default(0), DisplayName("النشطين")]
        public int Count_active { get; set; } = 0;
        [Default(0), DisplayName("المنتيهة")]
        public int Count_finshed { get; set; } = 0;
        [Default(0), DisplayName("المحذوف من الروتر")]
        public int Count_deleteServer { get; set; } = 0;

        [Default(0), DisplayName("المتبقي في الروتر")]
        public int Count_FoundServer { get; set; } = 0;

        //public string Server { get; set; } = "UserManager";

        [StringLength(100), Browsable(false)]
        public string Rb { get; set; }

        [ Default(0)/*, Browsable(false)*/]
        public int BatchType { get; set; } = 0;  // 0=print  1=import   2=import_archive

        [DisplayName("نوع الاضافة"), Computed]
        public string BatchType_Str {
            get
            {
                if (BatchType == 2)
                    return "ارشيف";
                else if (BatchType == 1)
                    return "استيراد";
                else
                    return "طباعة";
            }
        }


        [Computed, StringLength(100)]
        public string Str_Name
        {
            //set { if(value==null) Str_Name = ""; }
            get
            {
                string name = BatchNumber+"";
                if(AddedDate.Value!=null)
                 name = BatchNumber + " -(" + AddedDate.Value.ToString("MM-yyyy")+")";
                return name;
            }
        }
    }

    public class NumberPrintCard : BatchCard
    {
        [DisplayName("رقم الطبعة"), Required]
        public int NumberPrint { get; set; }

        [Computed, StringLength(100)]
        public new string Str_Name
        {
            //set { if(value==null) Str_Name = ""; }
            get
            {
                string name = NumberPrint + "";
                if (AddedDate.Value != null)
                    name = NumberPrint + " -(" + AddedDate.Value.ToString("MM-yyyy") + ")";
                return name;
            }
        }
    }


    public class CardsStatus
    {
        public int Count { get; set; }
        public int Status { get; set; }
        public int BatchNumber { get; set; }
        public int NumberPrint { get; set; }
        public string ProfileName { get; set; }
        public int SpId { get; set; }
        public string SpCode { get; set; }

        //public List<CardsStatus> Get_Batch_Cards()
        //{
        //List<Get_Batch_Status_Cards> s = new List<Get_Batch_Status_Cards>();
        //using (IDbConnection cnn = new SQLiteConnection(_localDB_path))
        //{
        //    string qury = "  SELECT  Count(id) as Count,Status ,BatchNumber FROM UmUser  WHERE  BatchNumber > 0  " + grbBy;
        //    //string qury = "  SELECT  count(id) as count,status ,numberPrintedId FROM user  WHERE Delet_fromServer=0 and numberPrintedId IS NOT NULL and NOT numberPrintedId=0  " + grbBy;
        //    //string Qury = "SELECT *  FROM Batch_cards where server='usermanager' ORDER BY date DESC;";
        //    var output = cnn.Query<Get_Batch_Status_Cards>(qury, new DynamicParameters());
        //    return output.ToList();
        //}
        //}

    }

   
    [UniqueConstraint(nameof(Name), nameof(Rb))]
    public class My_Sequence
    {
        [PrimaryKey, AutoIncrement, Required, Unique]
        public int Id { get; set; }
        [StringLength(200)]
        public string Name { get; set; }
        [Default(0)]
        public long Seq { get; set; }
        [StringLength(200)]
        public string Rb { get; set; }

    }

}
