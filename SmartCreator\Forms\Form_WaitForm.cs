﻿using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
 
    public partial class Form_WaitForm : RJForms.RJBaseForm
    {
        public bool IsCancel=false;
        public Action Artan { get; set; }
        public Form_WaitForm(Action artan)
        {
            InitializeComponent();
            if(artan == null)
            {
                throw new ArgumentNullException();
            }
            Artan = artan;
            rjLabel1.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 8, FontStyle.Bold);
            rjLabel1.BackColor = UIAppearance.BackgroundColor;

           

        } 
        //CancellationTokenSource cancellationTokenSource = new CancellationTokenSource();
        protected override void OnLoad(EventArgs e)
        {
            base.OnLoad(e);


            Task startTask = Task.Factory.StartNew(Artan).ContinueWith(s => {  this.Close(); },TaskScheduler.FromCurrentSynchronizationContext());
            //Task.Factory.StartNew(Artan).ContinueWith(s => { Close(); }, TaskScheduler.FromCurrentSynchronizationContext(), cancellationTokenSource.Token);
           

            //Task longRunningTask = Task.Factory.StartNew((Artan) =>
            //{
            //    // This code will run in a separate thread
            //    // Check for cancellation request frequently so the task can be stopped
            //    //for (int i = 0; i < 100; i++)
            //    //{
            //    //    if (cancellationTokenSource.Token.IsCancellationRequested)
            //    //    {
            //    //        break;
            //    //    }
            //    //    Console.WriteLine($"Iteration {i}");
            //    //    Thread.Sleep(1000);
            //    //}
            //}, cancellationTokenSource.Token);

            // Let's run the task for 5 seconds and then cancel it
            //Thread.Sleep(5000);
            //cancellationTokenSource.Cancel();

        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            IsCancel = true;
            //cancellationTokenSource.Cancel();
            //Artan.Invoke();
            //Task.Factory.CancellationToken.ThrowIfCancellationRequested();
            ////Task.Factory.StartNew(Artan);
            this.Close();
            //if (rjLabel1.Image != null)
            //{
            //    rjLabel1.Image.Dispose();
            //}

            //if (Artan != null)
            //{
            //    Artan=null; 
            //    throw new ArgumentNullException();
            //}
        }

        private void rjButton2_Click(object sender, EventArgs e)
        {
            this.Close();
            //this.Hide();
        }
    }

}
