using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نسخة مبسطة من RJTabControl للاختبار في Designer
    /// </summary>
    [ToolboxItem(true)]
    [DesignTimeVisible(true)]
    public class SimpleRJTabControl : Panel
    {
        private Label testLabel;

        public SimpleRJTabControl()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // إنشاء label بسيط للاختبار
            this.testLabel = new Label();
            this.testLabel.Text = "✅ SimpleRJTabControl يعمل!\n\nهذا اختبار مبسط للتأكد من أن\nالمشكلة ليست في البنية الأساسية";
            this.testLabel.Dock = DockStyle.Fill;
            this.testLabel.TextAlign = ContentAlignment.MiddleCenter;
            this.testLabel.Font = new Font("Segoe UI", 12, FontStyle.Bold);
            this.testLabel.ForeColor = Color.FromArgb(0, 122, 204);
            this.testLabel.BackColor = Color.White;

            // إعدادات Panel
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.BorderStyle = BorderStyle.FixedSingle;
            this.Size = new Size(400, 300);

            // إضافة Label
            this.Controls.Add(this.testLabel);

            this.ResumeLayout(false);
        }

        /// <summary>
        /// خاصية اختبار بسيطة
        /// </summary>
        [Category("Simple Test")]
        [Description("Test property for designer")]
        [DefaultValue("Test Value")]
        public string TestProperty { get; set; } = "Test Value";

        /// <summary>
        /// خاصية لون الخلفية
        /// </summary>
        [Category("Simple Test")]
        [Description("Background color for test")]
        public new Color BackColor
        {
            get { return base.BackColor; }
            set { base.BackColor = value; }
        }

        /// <summary>
        /// خاصية النص
        /// </summary>
        [Category("Simple Test")]
        [Description("Text to display")]
        public string DisplayText
        {
            get { return testLabel?.Text ?? ""; }
            set 
            { 
                if (testLabel != null) 
                    testLabel.Text = value; 
            }
        }
    }
}
