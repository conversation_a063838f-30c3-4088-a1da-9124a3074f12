using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار سريع للتأكد من عدم وجود أخطاء
    /// </summary>
    public static class QuickErrorTest
    {
        /// <summary>
        /// اختبار سريع لجميع الميزات
        /// </summary>
        public static void RunQuickTest()
        {
            try
            {
                Console.WriteLine("🧪 بدء الاختبار السريع...");

                // اختبار 1: إنشاء RJTabControl
                Console.WriteLine("1️⃣ اختبار إنشاء RJTabControl...");
                var tabControl = new RJTabControl();
                Console.WriteLine("✅ تم إنشاء RJTabControl بنجاح");

                // اختبار 2: خصائص Designer
                Console.WriteLine("2️⃣ اختبار خصائص Designer...");
                var tabsCollection = tabControl.Tabs;
                Console.WriteLine($"✅ Tabs Collection متاحة: {tabsCollection != null}");
                Console.WriteLine($"✅ TabCount: {tabControl.TabCount}");
                Console.WriteLine($"✅ SelectedIndex: {tabControl.SelectedIndex}");

                // اختبار 3: إضافة تابات بالطرق المختلفة
                Console.WriteLine("3️⃣ اختبار إضافة التابات...");
                
                // الطريقة الأولى
                var tab1 = tabControl.AddTab("تاب 1");
                Console.WriteLine("✅ AddTab(string) يعمل");

                // الطريقة الثانية
                var tab2 = tabControl.AddTab("تاب 2", IconChar.Home);
                Console.WriteLine("✅ AddTab(string, IconChar) يعمل");

                // الطريقة الثالثة
                var customTab = new RJTabPage("تاب 3", IconChar.Star);
                customTab.BackColor = Color.Blue;
                tabControl.AddTab(customTab);
                Console.WriteLine("✅ AddTab(RJTabPage) يعمل");

                // اختبار 4: خصائص RJButton
                Console.WriteLine("4️⃣ اختبار خصائص RJButton...");
                tab1.BackColor = Color.Red;
                tab1.IconChar = IconChar.Play;
                tab1.IconSize = 20;
                tab1.BorderRadius = 10;
                Console.WriteLine("✅ جميع خصائص RJButton متاحة");

                // اختبار 5: Tabs Collection
                Console.WriteLine("5️⃣ اختبار Tabs Collection...");
                Console.WriteLine($"✅ عدد التابات في Collection: {tabControl.Tabs.Count}");
                
                // إضافة تاب عبر Collection
                var collectionTab = new RJTabPage("تاب Collection", IconChar.Plus);
                tabControl.Tabs.Add(collectionTab);
                Console.WriteLine("✅ إضافة تاب عبر Collection يعمل");

                // اختبار 6: SelectedTab و SelectedIndex
                Console.WriteLine("6️⃣ اختبار التحكم في التابات...");
                tabControl.SelectedIndex = 1;
                Console.WriteLine($"✅ SelectedIndex = 1, SelectedTab = {tabControl.SelectedTab?.Text}");
                
                tabControl.SelectedTab = tab1;
                Console.WriteLine($"✅ SelectedTab = {tab1.Text}, SelectedIndex = {tabControl.SelectedIndex}");

                // اختبار 7: RJPanel مع الحدود الجديدة
                Console.WriteLine("7️⃣ اختبار RJPanel المحدث...");
                var testPanel = new RJPanel
                {
                    BorderSize = 3,
                    BorderColor = Color.Green,
                    BorderRadius = 15
                };
                Console.WriteLine("✅ RJPanel مع BorderSize و BorderColor يعمل");

                // اختبار 8: RJTextBox مع ReadOnly
                Console.WriteLine("8️⃣ اختبار RJTextBox المحدث...");
                var testTextBox = new RJTextBox
                {
                    ReadOnly = true,
                    Text = "نص للقراءة فقط"
                };
                Console.WriteLine("✅ RJTextBox مع ReadOnly يعمل");

                // اختبار 9: إزالة تاب
                Console.WriteLine("9️⃣ اختبار إزالة التابات...");
                var initialCount = tabControl.TabCount;
                tabControl.RemoveTab(tab2);
                Console.WriteLine($"✅ إزالة تاب: {initialCount} → {tabControl.TabCount}");

                Console.WriteLine("\n🎉 جميع الاختبارات نجحت!");
                Console.WriteLine("✅ لا توجد أخطاء");
                Console.WriteLine("✅ جميع الميزات تعمل بمثالية");
                Console.WriteLine("🚀 RJTabControl جاهز للاستخدام!");

                // عرض النتيجة
                MessageBox.Show("🎉 الاختبار السريع نجح!\n\n" +
                               "✅ جميع الميزات تعمل بمثالية\n" +
                               "✅ لا توجد أخطاء\n" +
                               "✅ Designer يعمل بشكل صحيح\n" +
                               "✅ جميع خصائص RJButton متاحة\n\n" +
                               "🚀 RJTabControl جاهز للاستخدام!",
                               "نتيجة الاختبار السريع", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في الاختبار: {ex.Message}");
                Console.WriteLine($"📍 Stack Trace: {ex.StackTrace}");
                
                MessageBox.Show($"❌ خطأ في الاختبار السريع:\n\n{ex.Message}\n\n" +
                               "تحقق من Console للتفاصيل الكاملة.",
                               "خطأ في الاختبار", 
                               MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار Designer Collection
        /// </summary>
        public static void TestDesignerCollection()
        {
            try
            {
                Console.WriteLine("🎨 اختبار Designer Collection...");

                var tabControl = new RJTabControl();
                var collection = tabControl.Tabs;

                // اختبار إضافة تاب
                var newTab = new RJTabPage("تاب جديد", IconChar.Star);
                collection.Add(newTab);
                Console.WriteLine($"✅ إضافة تاب: Count = {collection.Count}");

                // اختبار الوصول بالفهرس
                var firstTab = collection[0];
                Console.WriteLine($"✅ الوصول بالفهرس: {firstTab.Text}");

                // اختبار البحث بالاسم
                newTab.Name = "TestTab";
                var foundTab = collection["TestTab"];
                Console.WriteLine($"✅ البحث بالاسم: {foundTab?.Text}");

                // اختبار Contains
                var contains = collection.Contains(newTab);
                Console.WriteLine($"✅ Contains: {contains}");

                // اختبار IndexOf
                var index = collection.IndexOf(newTab);
                Console.WriteLine($"✅ IndexOf: {index}");

                // اختبار إزالة
                collection.Remove(newTab);
                Console.WriteLine($"✅ إزالة تاب: Count = {collection.Count}");

                Console.WriteLine("🎨 Designer Collection يعمل بمثالية!");

                MessageBox.Show("🎨 اختبار Designer Collection نجح!\n\n" +
                               "✅ إضافة وإزالة التابات\n" +
                               "✅ الوصول بالفهرس والاسم\n" +
                               "✅ جميع طرق Collection\n\n" +
                               "🚀 Collection Editor جاهز!",
                               "نتيجة اختبار Collection", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في اختبار Collection: {ex.Message}");
                MessageBox.Show($"❌ خطأ في اختبار Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار شامل مع عرض النتائج
        /// </summary>
        public static void RunComprehensiveTest()
        {
            var results = new System.Text.StringBuilder();
            results.AppendLine("🧪 نتائج الاختبار الشامل:\n");

            try
            {
                // اختبار أساسي
                RunQuickTest();
                results.AppendLine("✅ الاختبار الأساسي: نجح");

                // اختبار Collection
                TestDesignerCollection();
                results.AppendLine("✅ اختبار Collection: نجح");

                // اختبار إنشاء نموذج
                var form = new Form();
                var tabControl = new RJTabControl { Dock = DockStyle.Fill };
                form.Controls.Add(tabControl);
                results.AppendLine("✅ إنشاء نموذج: نجح");

                // اختبار إضافة محتوى
                var tab = tabControl.AddTab("اختبار", IconChar.TengeSign);
                var panel = new RJPanel { Dock = DockStyle.Fill };
                tab.AddControl(panel);
                results.AppendLine("✅ إضافة محتوى: نجح");

                results.AppendLine("\n🎉 جميع الاختبارات نجحت!");
                results.AppendLine("🚀 RJTabControl جاهز للاستخدام في الإنتاج!");

                MessageBox.Show(results.ToString(), "نتائج الاختبار الشامل", 
                               MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                results.AppendLine($"\n❌ خطأ: {ex.Message}");
                MessageBox.Show(results.ToString(), "نتائج الاختبار", 
                               MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }
    }
}
