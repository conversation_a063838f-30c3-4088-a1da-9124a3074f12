﻿using DevComponents.DotNetBar;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class Form_CardsDesigen_Custom_items : RJForms.RJChildForm
    {
        public CardsTemplate card;
        Form_CardsDesigen_Graghics frm;
        bool firstLoad = true;
        public Form_CardsDesigen_Custom_items(CardsTemplate _card, Form_CardsDesigen_Graghics _frm)
        {
            InitializeComponent();

            //if (Owner != null)
            //    Location = new Point(Owner.Location.X + Owner.Width / 2 - Width / 2,
            //        Owner.Location.Y + Owner.Height / 2 - Height / 2);

            //if (_frm != null)
            //    Location = new Point(_frm.Location.X + _frm.Width / 2 - Width / 2,
            //        _frm.Location.Y + _frm.Height / 2 - Height / 2);

            this.card = _card;
            this.frm = _frm;
            txt_Page_Url.Text = "http://a.com/login";
        //http://s.com/login?username=d5555&password=

            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel13.Customizable = false;
                rjPanel1.Customizable = false;
            }
            Set_Font();
            //this.frm = frm;
        }
        private void Set_Font()
        {
            //return;
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJToggleButton))
                    {
                        RJToggleButton lbl = (RJToggleButton)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }

            Logo_Chbox.Font= QR_Chbox.Font=rjLabel19.Font = fnt;


            utils.Control_textSize(pnlClientArea);
            return;
            Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size    , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void btnSaveTemplate_Click(object sender, EventArgs e)
        {
            frm.card = card;
            this.Close();
        }

        private void btnAddImag_Click(object sender, EventArgs e)
        {
            set_logoFromPath(false);
        }

        private void set_logoFromPath(bool fromDB)
        {
            if (!fromDB)
            {
                try
                {
                    DialogResult res = openFileDialog1.ShowDialog();
                    openFileDialog1.Filter = "jpeg|*.jpg|bmp|*.bmp|all files|*.*";

                    if (res == DialogResult.OK)
                    {
                        FileInfo file = new FileInfo(openFileDialog1.FileName);
                        double sizeInBytes = file.Length;
                        try
                        {
                            Bitmap img = new Bitmap(openFileDialog1.FileName);
                            txt_LogoImage.Text = openFileDialog1.FileName;
                            frm.txt_LogoImage.Text = openFileDialog1.FileName;

                            frm.pictureBox_logo.Image = System.Drawing.Image.FromFile(openFileDialog1.FileName);
                            Logo_Chbox.Checked = true;
                            frm.Logo_Chbox.Checked = true;
                            card.cardsItems.logo.Path = openFileDialog1.FileName;
                            frm.card.cardsItems.logo.Path = openFileDialog1.FileName;
                        }
                        catch
                        {
                            //BackgroundImgCard_Chbox.Checked = false;
                            Logo_Chbox.Checked = false;
                            frm.Logo_Chbox.Checked = false;
                            
                            //MessageBox.Show("خطأ في الصوره");
                        }


                    }
                    Logo_Chbox.Checked = true;
                }
                catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
            }
        }

        private void Login_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Login_Chbox.Checked = Login_Chbox.Checked;
        }

        private void Password_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Password_Chbox.Checked = Password_Chbox.Checked;
        }

        private void Size_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Size_Chbox.Checked = Size_Chbox.Checked;
        }

        private void Squnce_Number_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Squnce_Number_Chbox.Check= Squnce_Number_Chbox.Checked;
        }

        private void validity_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.validity_Chbox.Check = validity_Chbox.Checked;
        }

        private void Price_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Price_Chbox.Check  = Price_Chbox.Checked;
        }

        private void Time_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Time_Chbox.Check = Time_Chbox.Checked;
        }

        private void Date_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Date_Print_Chbox.Check = Date_Print_Chbox.Checked;
        }

        private void QR_Chbox2_CheckedChanged(object sender, EventArgs e)
        {

        }

        private void SP_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.SP_Chbox.Checked = SP_Chbox.Checked;
        }

        private void Number_Print_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Number_Print_Chbox.Checked= Number_Print_Chbox.Checked;
        }

        private void OtherText1_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.OtherText1_Chbox.Checked = OtherText1_Chbox.Checked;
        }

        private void Logo_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Logo_Chbox.Checked= Logo_Chbox.Checked;
        }

        private void txt_LogoImage_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.txt_LogoImage.Text = txt_LogoImage.Text;
        }

        private void QR_Chbox_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.QR_Chbox.Checked= QR_Chbox.Checked;
        }

        private void Form_CardsDesigen_Custom_items_Load(object sender, EventArgs e)
        {
            firstLoad = false;
            //if (Owner != null)
            //    Location = new Point(Owner.Location.X + Owner.Width / 2 - Width / 2,
            //        Owner.Location.Y + Owner.Height / 2 - Height / 2);

        }

        private void Form_CardsDesigen_Custom_items_FormClosing(object sender, FormClosingEventArgs e)
        {
            frm.card = card;
        }
        protected override void OnShown(EventArgs e)
        {
            base.OnShown(e);
            if (Owner != null && StartPosition == FormStartPosition.Manual)
            {
                //int offset = Owner.OwnedForms.Length * 38;  // approx. 10mm
                int offset =0;  // approx. 10mm
                Point p = new Point(Owner.Left + Owner.Width / 2 - Width / 2 + offset, Owner.Top +40+ offset);
                //Point p = new Point(Owner.Left + Owner.Width / 2 - Width / 2 + offset, Owner.Top + Owner.Height / 2 - Height / 2 + offset);
                this.Location = p;
            }
        }

        private void txt_Page_Url_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return; 
            frm.card.cardsItems.QR.url = txt_Page_Url.Text;
            card.cardsItems.QR.url = txt_Page_Url.Text;
        }

        private void Number_Batch_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            frm.Number_Batch_Chbox.Checked = Number_Batch.Checked;
        }
    }
}
