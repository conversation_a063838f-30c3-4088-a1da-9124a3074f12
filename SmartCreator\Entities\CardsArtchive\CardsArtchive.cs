﻿//using ServiceStack.DataAnnotations;

using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using SmartCreator.Entities.EnumType;
using SmartCreator.Models;
using System.Data;
using System.Numerics;
using System.Runtime.InteropServices;
using SmartCreator.Data;

namespace SmartCreator.Entities.CardsArtchive
{
    public class CardsArtchive
    {
        //public CardsArtchive() { }
        [DisplayName("الحالة"), Computed]
        public string Str_Status
        {

            get
            {
                //string s = "";
                //if (DeleteFromServer == 1)
                //    return "محذوف";

                //if (ActiveSessions == 1)
                //    return "نشط + اونلاين";
                //if (Status == 0)
                //    s = "انتظار";
                //else if (Status == 2)
                //    s = "منتهي الصلاحية";
                //else if (Status == 3)
                //    s = "خطأ في الباقة";
                //else if (Status == 1)
                //{
                //    if ((UptimeLimit > 0 && UptimeUsed > 0) && ((UptimeLimit - UptimeUsed <= 0)))
                //        s = "منهي الوقت";
                //    else if ((TransferLimit > 0 && (UploadUsed + DownloadUsed) > 0) && ((UptimeLimit - UptimeUsed <= 0)))
                //        s = "منهي التنزيل";
                //    else
                //        s = "نشط";
                //}

                //if (Status == 0)
                //    return "انتظار";
                string s = (Status == 0 ? "انتظار" : (Status == 1 ? "مطبوع في النظام" : (Status == 2 ? "محذوف من الارشيف" : (Status == 3 ? "خطأ" : ""))));
                //if (Disabled == 1)
                //    return s + (" + معطل");

                return s;
            }
        }


        [PrimaryKey, AutoIncrement, Required, Unique, Browsable(false)]
        public long Id { get; set; }
        [/*Index,*/ Required, DisplayName("التسلسل")]
        public long SN { get; set; }

        [Index, Required, StringLength(250), DisplayName("اسم المستخدم")]
        public string UserName { get; set; }

        [DisplayName("كلمة المرور"), StringLength(200)]
        public string Password { get; set; }

        [DisplayName("الباقة")/*, Index*/]
        public string ProfileName { get; set; }

        [DisplayName("الدفعة"), Index]
        public int? BatchCardId { get; set; }

        [DisplayName("رقم الصفحة")]
        public int? PageNumber { get; set; }

        [ DisplayName("تاريخ الاضافة للسيرفر")]
        public DateTime? RegDate { get; set; }


        //[Index, Default(0)]
        //public int DeleteFromArchive { get; set; } = 0;

        [/*Index,*/ Default(0)]
        public int Status { get; set; } = 0;//0=waiting  1=added to server

        //[Browsable(false)]
        //public DateTime? AddedDb { get; set; }
        [Browsable(false)]
        public string Rb { get; set; }
    }


    public class BatchArtchive
    {
        [PrimaryKey, AutoIncrement, Required, Unique]
        public int Id { get; set; }
        
        [DisplayName("الدفعه"), Required/*, Index*/]
        public int BatchNumber { get; set; }
        [DisplayName("الباقة"), StringLength(100)/*, Index*/]
        public string ProfileName { get; set; }

        [DisplayName("التاريخ")]
        public DateTime? AddedDate { get; set; }
        
        [DisplayName("عدد الكروت")]
        public int Count { get; set; }

        [DisplayName("من رقم")]
        public long Sn_from { get; set; }

        [DisplayName("الي رقم")]
        public long Sn_to { get; set; }


        [Default(0), DisplayName("عدد الصفحات")]
        public int Count_Page { get; set; } = 0;

        //[Default(0), Index, DisplayName("السيرفر")]
        //public int ServerAdedd { get; set; } = 0;//= "UserManager";

        [Default(0), DisplayName("عدد الانتظار")]
        public int Count_waiting { get; set; } = 0;
        
        [Default(0), DisplayName("عدد المضاف للسيرفر")]
        public int Count_active { get; set; } = 0;

        [Default(0), DisplayName("المحذوف من الارشيف")]
        public int Count_DeleteFormArchive { get; set; } = 0;

        [StringLength(100), Browsable(false)]
        public string Rb { get; set; }

        [DisplayName("الاسم"), Computed, StringLength(100)]
        public string Str_Name
        {
            //set { if(value==null) Str_Name = ""; }
            get
            {
                string name = BatchNumber + " - (" + AddedDate.Value.ToString("MM-yyyy") + ")";
                return name;
            }
        }
    }

}
