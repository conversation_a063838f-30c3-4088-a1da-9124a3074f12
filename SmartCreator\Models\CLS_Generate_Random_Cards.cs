﻿using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models.hotspot;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Text;

namespace SmartCreator.Models
{
    public class CLS_Generate_Random_Cards
    { 
        private int inext = 0;  
        private Clss_InfoPrint info_print;
        private HashSet<string> card_copy;
        private Random rndU =new Random();

        public CLS_Generate_Random_Cards() { }
        public CLS_Generate_Random_Cards(Clss_InfoPrint _info_print, HashSet<string> _card_copy) 
        {
            info_print = _info_print;
            card_copy = _card_copy;
        }
        public New_Generate_Cards Generate_Cards(int Number_Cards_ToAdd,string mesgBtn = "يتم الاضافة",bool useProgressBar=true)
        {
            New_Generate_Cards new_Generate_Cards;
            string strUser = ""; string strPass = "";
            Dictionary<string, NewUserToAdd> dicUser = new Dictionary<string, NewUserToAdd>(); 
            try
            {
                for (int i = 0; i < Number_Cards_ToAdd; i++)//  دواره لعدد الكروت 
                {
                    inext = ((i + 1) * 100 / Number_Cards_ToAdd);//لحساب معادلة البروجراس
                    Dictionary<string, string> NUser = GenerateUserPassword_Random(card_copy);
                    if (NUser == null)
                        return null;

                    card_copy.Add(NUser["username"]);
                    NewUserToAdd NewUser_Generate = new NewUserToAdd
                    { 
                        Name = NUser["username"],
                        Password = NUser["password"]
                    };
                    dicUser.Add(NewUser_Generate.Name, NewUser_Generate);
                    strUser += "\"" + NewUser_Generate.Name + "\"" + ",";
                    strPass += "\"" + NewUser_Generate.Password + "\"" + ",";

                    if (useProgressBar)
                         Global_Variable.Update_Um_StatusBar(true, true, inext, "( " + Number_Cards_ToAdd + "  /  " + (i + 1) + " )", mesgBtn);

                }

                new_Generate_Cards = new New_Generate_Cards();
                new_Generate_Cards.status=true;
                new_Generate_Cards.strUser=strUser;
                new_Generate_Cards.strPass=strPass;
                new_Generate_Cards.dicUser=dicUser;
            }
            catch (Exception ex)
            {
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء");
                RJMessageBox.Show("Generate_Cards\n" + ex.Message);
                return null;
            }
            return new_Generate_Cards;
        }

        private Dictionary<string, string> GenerateUserPassword_Random(HashSet<string> card_copy)
        {
            Dictionary<string, string> _NewUser = new Dictionary<string, string>();

            string RandUser = "";
            string RandPassword = "";
            int countRandom = 0;

        lab:;
            RandUser = GenerateUser(info_print.User_Long, rndU, info_print.Mode_User_NumberORcharcter_Value);
            RandUser = info_print.StartCard + RandUser + info_print.EndCard;

            if (info_print.UserPassword_Pattern == 0)
                RandPassword = "";
            else if (info_print.UserPassword_Pattern == 1)
                RandPassword = RandUser;
            else
                RandPassword = GenerateUser(info_print.Password_Long, rndU, info_print.Mode_Password_NumberORcharcter_Value);

            try
            {
                if (card_copy.Add(RandUser) == false)
                {
                    if (countRandom > 100)
                    {
                        RJMessageBox.Show("يجب ان تزيد عدد ارقام  طول الكرت لتتجنب تأخر التوليد"); 
                        return null;
                    }
                    countRandom++;
                    goto lab;
                }
            }
            catch
            {
                if (countRandom > 100)
                {
                    RJMessageBox.Show("يجب ان تزيد عدد ارقام  طول الكرت لتتجنب تأخر التوليد"); 
                    return null;
                }
                countRandom++;
                goto lab;
            }

            _NewUser["username"] = RandUser;
            _NewUser["password"] = RandPassword;



            return _NewUser;
        }
        string Numbers = "0123456789";
        string characters = "abcdefghijklmnopqrstuvwxyz";
        string charactersNumber = "0123456789abcdefghijklmnopqrstuvwxyz";

        public string GenerateUser(int length, Random random, int mode)
        {
            string rand = "";
            if (mode == 0)
                rand += Numbers;
            else if (mode == 1)
                rand += characters;
            else
                rand += charactersNumber;
            StringBuilder result = new StringBuilder(length);
            for (int i = 0; i < length; i++)
            {
                result.Append(rand[random.Next(rand.Length)]);
            }
            return result.ToString();
        }


        
    }
    public class New_Generate_Cards
    {
        public Dictionary<string, NewUserToAdd> dicUser;
        public string strUser;
        public string strPass ;
        public bool status=false;
    }


    public class Clss_InfoPrint
    {
        public UmProfile profile {  get; set; }
        public SellingPoint SellingPoint { get; set; } = null;
        public int? NumberPrint { get; set; } =null;
        public int? BatchNumber { get; set; } =null;
        public bool is_RegisterAs_LastBatch { get; set; } = false;
        public DateTime regDate {  get; set; } = utils.Datetime_To_DateTime_Format_DB(DateTime.Now);
        //public double regDate {  get; set; } = utils.DateTimeToUnixTimeStamp(DateTime.Now.ToUniversalTime());
        public bool is_use_Attribut { get; set; } = false;
        public string Attribut { get; set; } = "";
        public string Group { get; set; }= "default";
        public int Selected_template_item { get; set; }
        public int Number_Cards_ToAdd { get; set; } = 1000;
        public int Number_Cards_ToAdd_DB { get; set; } = 5000;
        public string Profile_Name { get; set; }
        public string Mode_User_NumberORcharcter { get; set; }
        public int Mode_User_NumberORcharcter_Value { get; set; }
        public int User_Long { get; set; }
        public string Mode_Password_NumberORcharcter { get; set; }
        public int Mode_Password_NumberORcharcter_Value { get; set; }
        public int Password_Long { get; set; }
        public int UserPassword_Pattern { get; set; }
        public string SellingPoint_Name { get; set; }
        //public int? SellingPoint_Value { get; set; }
        public string SellingPoint_Value { get; set; }
        public string SellingPoint_Value_str { get; set; }
        public string StartCard { get; set; } = "";
        public string EndCard { get; set; } = "";
        public string ShardUser { get; set; } = "";
        public bool FirstUse { get; set; } = false;
        public bool is_comment { get; set; } = false;
        public string Comment { get; set; } = "";
        public string email { get; set; } = "";
        public string file_Name { get; set; } = "";
        public string pathfile { get; set; } = "C:\\";
        public string Custumer_UserMan { get; set; } = "admin";
        public bool Save_To_PDF { get; set; } = true;
        public bool Open_PDF_file { get; set; } = true;
        public bool SaveTo_excel { get; set; } = true;
        public bool SaveTo_script_File { get; set; } = true;
        public bool SaveTo_text_File { get; set; } = true;
        public bool RegisterAsBatch { get; set; } = true;
        public bool RegisterAs_LasBatch { get; set; } = false;
        public bool With_Archive_uniqe { get; set; } = true;
        public string TemplateId { get; set; } 
        public string TemplateName { get; set; }
        public int? NumberCard_in_Page { get; set; }


        public bool is_add_batch_cards { get; set; } = false;
        public bool is_add_batch_cards_to_Archive { get; set; } = false;
        public bool is_Add_One_Card { get; set; } = false;


        public HSLocalProfile profile_HS { get; set; }
        public string profile_Source_hotspot { get; set; }
        public string Server_hotspot { get; set; } = "all";
        public bool Smart_Validatiy_Add { get; set; } =true;
         
        public bool Smart_Validatiy_timeSave { get; set; } =true;
        public bool Smart_Validatiy_sizeSave { get; set; } =true;
        public bool Smart_Validatiy_sessionSave { get; set; } =true;
        public bool Smart_Validatiy_byDayHour { get; set; } =false; // خطاء يعني ينقص ويحسب بالساعات  وصح يعني بالايام كل يوم ينقص يوم


        public bool checkBox_Remove_FromArchive { get; set; } = true;

    }
}
