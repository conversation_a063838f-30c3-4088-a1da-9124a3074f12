﻿using SmartCreator.Forms.UserManager;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Acive_Host
{
    public partial class Form_Acive_Host_Bar : RJChildForm
    {
        bool FirstLoad=true;
        public Form_AciveUser form_AciveUser;
        //public Form_HostUser form_HostUser;
        public Form_AciveUser form_HostUser;
        public Form_IpBinding form_IpBinding;
        public Form_WalledGarden form_WalledGarden;
        bool First_form_AciveUser = true;
        bool First_form_HostUser = true;
        bool First_form_IpBinding = true;
        bool First_form_WalledGarden = true;


        public Form_Acive_Host_Bar()
        {
            InitializeComponent();
            this.Text = "النشطين والهوست";
            this.Caption = "النشطين والهوست";

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Active & Host";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
            Set_Font();
        }

        private void Form_Acive_Host_Bar_Load(object sender, EventArgs e)
        {


            timer1.Start();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            btn_Acive_Click(sender, e);
            FirstLoad = false;
        }

        private void Set_Font()
        {
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 8 * utils.ScaleFactor, FontStyle.Bold);
            //Font fnt = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 8f, true);
            btn_Acive.Font = fnt;
            btn_Host.Font = fnt;
            btn_IpBinding.Font = fnt;
            btn_WalledGarden.Font = fnt;
            
        }
        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            panel_Tab_Container.Refresh();
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void btn_Acive_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Acive);

            if (First_form_AciveUser)
            {
                First_form_AciveUser = false;
                form_AciveUser = new Form_AciveUser();
                form_AciveUser.TopLevel = false;
                form_AciveUser.IsChildForm = true;
                form_AciveUser.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_AciveUser);
                this.panel_Tab_Container.Tag = First_form_AciveUser;
                form_AciveUser.Show(); //show on desktop panel  
                form_AciveUser.BringToFront();
                form_AciveUser.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_AciveUser.BringToFront();
                form_AciveUser.Show();
                form_AciveUser.Focus();
            }
        }

        private void btn_Host_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Host);

            if (First_form_HostUser)
            {
                First_form_HostUser = false;
                form_HostUser = new Form_AciveUser("Host");
                form_HostUser.TopLevel = false;
                form_HostUser.IsChildForm = true;
                form_HostUser.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_HostUser);
                this.panel_Tab_Container.Tag = First_form_HostUser;
                form_HostUser.Show(); //show on desktop panel  
                form_HostUser.BringToFront();
                form_HostUser.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_HostUser.BringToFront();
                form_HostUser.Show();
                form_HostUser.Focus();
            }
        }

        private void btn_IpBinding_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_IpBinding);

            if (First_form_IpBinding)
            {
                First_form_IpBinding = false;
                form_IpBinding = new Form_IpBinding();
                form_IpBinding.TopLevel = false;
                form_IpBinding.IsChildForm = true;
                form_IpBinding.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_IpBinding);
                this.panel_Tab_Container.Tag = First_form_IpBinding;
                form_IpBinding.Show(); //show on desktop panel  
                form_IpBinding.BringToFront();
                form_IpBinding.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_IpBinding.BringToFront();
                form_IpBinding.Show();
                form_IpBinding.Focus();
            }
        }

        private void btn_WalledGarden_Click(object sender, EventArgs e)
        {
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_WalledGarden);

            if (First_form_WalledGarden)
            {
                First_form_WalledGarden = false;
                form_WalledGarden = new Form_WalledGarden();
                form_WalledGarden.TopLevel = false;
                form_WalledGarden.IsChildForm = true;
                form_WalledGarden.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(form_WalledGarden);
                this.panel_Tab_Container.Tag = First_form_WalledGarden;
                form_WalledGarden.Show(); //show on desktop panel  
                form_WalledGarden.BringToFront();
                form_WalledGarden.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                form_WalledGarden.BringToFront();
                form_WalledGarden.Show();
                form_WalledGarden.Focus();
            }
        }
    }
}
