﻿namespace SmartCreator.TestAndDemo
{
    partial class FormDashboard
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea2 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend2 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series2 = new System.Windows.Forms.DataVisualization.Charting.Series();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series1 = new System.Windows.Forms.DataVisualization.Charting.Series();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea4 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend4 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series4 = new System.Windows.Forms.DataVisualization.Charting.Series();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea3 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend3 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series3 = new System.Windows.Forms.DataVisualization.Charting.Series();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.lblRevenue = new SmartCreator.RJControls.RJLabel();
            this.dtpStartDate = new System.Windows.Forms.DateTimePicker();
            this.lblNumberSessionUM = new SmartCreator.RJControls.RJLabel();
            this.rjPanel6 = new SmartCreator.RJControls.RJPanel();
            this.ToggleButton_Comm = new SmartCreator.RJControls.RJToggleButton();
            this.cbCategory = new SmartCreator.RJControls.RJComboBox();
            this.dtpEndDate = new System.Windows.Forms.DateTimePicker();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel7 = new SmartCreator.RJControls.RJPanel();
            this.btn_Dashbard_Refersh = new SmartCreator.RJControls.RJButton();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.CboxServerType = new SmartCreator.RJControls.RJComboBox();
            this.CboxPeriod = new SmartCreator.RJControls.RJComboBox();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.lblNumberUserManager = new SmartCreator.RJControls.RJLabel();
            this.lbActiveCards = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel4 = new SmartCreator.RJControls.RJPanel();
            this.lblCountHotspot = new SmartCreator.RJControls.RJLabel();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel5 = new SmartCreator.RJControls.RJPanel();
            this.panelPadding = new System.Windows.Forms.Panel();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.Cbox_By_Port = new SmartCreator.RJControls.RJComboBox();
            this.Cbox_By_profile = new SmartCreator.RJControls.RJComboBox();
            this.chartSalesByProfile = new SmartCreator.RJControls.RJChart();
            this.rjPanel8 = new SmartCreator.RJControls.RJPanel();
            this.chartRevenue = new SmartCreator.RJControls.RJChart();
            this.chartNumberSales = new SmartCreator.RJControls.RJChart();
            this.chartTop5Products = new SmartCreator.RJControls.RJChart();
            this.rjPanel9 = new SmartCreator.RJControls.RJPanel();
            this.lbCountHost = new SmartCreator.RJControls.RJLabel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.rjPanel10 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.lblCountNeghibor = new SmartCreator.RJControls.RJLabel();
            this.rjPanel11 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.lbl_Rb_model = new SmartCreator.RJControls.RJLabel();
            this.rjPanel12 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.lbl_Uptime = new SmartCreator.RJControls.RJLabel();
            this.rjPanel13 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.lbl_Rb_Version = new SmartCreator.RJControls.RJLabel();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel6.SuspendLayout();
            this.rjPanel7.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.rjPanel4.SuspendLayout();
            this.rjPanel5.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartSalesByProfile)).BeginInit();
            this.rjPanel8.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.chartRevenue)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartNumberSales)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartTop5Products)).BeginInit();
            this.rjPanel9.SuspendLayout();
            this.rjPanel10.SuspendLayout();
            this.rjPanel11.SuspendLayout();
            this.rjPanel12.SuspendLayout();
            this.rjPanel13.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel10);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.rjPanel8);
            this.pnlClientArea.Controls.Add(this.rjPanel6);
            this.pnlClientArea.Controls.Add(this.chartRevenue);
            this.pnlClientArea.Controls.Add(this.Cbox_By_profile);
            this.pnlClientArea.Controls.Add(this.Cbox_By_Port);
            this.pnlClientArea.Controls.Add(this.panelPadding);
            this.pnlClientArea.Controls.Add(this.rjPanel9);
            this.pnlClientArea.Controls.Add(this.rjPanel5);
            this.pnlClientArea.Controls.Add(this.rjPanel4);
            this.pnlClientArea.Controls.Add(this.rjPanel13);
            this.pnlClientArea.Controls.Add(this.rjPanel12);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.rjPanel11);
            this.pnlClientArea.Controls.Add(this.rjPanel7);
            this.pnlClientArea.Controls.Add(this.chartSalesByProfile);
            this.pnlClientArea.Controls.Add(this.chartTop5Products);
            this.pnlClientArea.Controls.Add(this.chartNumberSales);
            this.pnlClientArea.Location = new System.Drawing.Point(1, 41);
            this.pnlClientArea.Size = new System.Drawing.Size(998, 683);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(69, 17);
            this.lblCaption.Text = "Dashboard";
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 5;
            this.rjPanel1.Controls.Add(this.rjLabel4);
            this.rjPanel1.Controls.Add(this.lblRevenue);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(846, 94);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel1.Size = new System.Drawing.Size(147, 80);
            this.rjPanel1.TabIndex = 3;
            // 
            // rjLabel4
            // 
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(3, 3);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(141, 22);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 0;
            this.rjLabel4.Text = "اجمالي المبيعات";
            this.rjLabel4.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblRevenue
            // 
            this.lblRevenue.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblRevenue.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lblRevenue.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblRevenue.LinkLabel = true;
            this.lblRevenue.Location = new System.Drawing.Point(3, 37);
            this.lblRevenue.Name = "lblRevenue";
            this.lblRevenue.Size = new System.Drawing.Size(144, 23);
            this.lblRevenue.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblRevenue.TabIndex = 1;
            this.lblRevenue.Text = "0";
            this.lblRevenue.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // dtpStartDate
            // 
            this.dtpStartDate.CustomFormat = "MMM dd, yyyy";
            this.dtpStartDate.Font = new System.Drawing.Font("Microsoft Sans Serif", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtpStartDate.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpStartDate.Location = new System.Drawing.Point(34, 12);
            this.dtpStartDate.Name = "dtpStartDate";
            this.dtpStartDate.Size = new System.Drawing.Size(122, 20);
            this.dtpStartDate.TabIndex = 4;
            this.dtpStartDate.Visible = false;
            this.dtpStartDate.ValueChanged += new System.EventHandler(this.dtpStartDate_ValueChanged);
            // 
            // lblNumberSessionUM
            // 
            this.lblNumberSessionUM.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblNumberSessionUM.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lblNumberSessionUM.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblNumberSessionUM.LinkLabel = true;
            this.lblNumberSessionUM.Location = new System.Drawing.Point(3, 37);
            this.lblNumberSessionUM.Name = "lblNumberSessionUM";
            this.lblNumberSessionUM.Size = new System.Drawing.Size(133, 23);
            this.lblNumberSessionUM.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblNumberSessionUM.TabIndex = 1;
            this.lblNumberSessionUM.Text = "0";
            this.lblNumberSessionUM.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel6
            // 
            this.rjPanel6.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel6.BorderRadius = 5;
            this.rjPanel6.Controls.Add(this.ToggleButton_Comm);
            this.rjPanel6.Controls.Add(this.cbCategory);
            this.rjPanel6.Controls.Add(this.dtpStartDate);
            this.rjPanel6.Controls.Add(this.dtpEndDate);
            this.rjPanel6.Customizable = false;
            this.rjPanel6.Location = new System.Drawing.Point(79, 190);
            this.rjPanel6.Name = "rjPanel6";
            this.rjPanel6.Size = new System.Drawing.Size(163, 28);
            this.rjPanel6.TabIndex = 2;
            this.rjPanel6.Visible = false;
            // 
            // ToggleButton_Comm
            // 
            this.ToggleButton_Comm.Activated = false;
            this.ToggleButton_Comm.Customizable = false;
            this.ToggleButton_Comm.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Comm.Location = new System.Drawing.Point(76, 12);
            this.ToggleButton_Comm.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Comm.Name = "ToggleButton_Comm";
            this.ToggleButton_Comm.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Comm.OFF_Text = "تفصيلي";
            this.ToggleButton_Comm.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Comm.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Comm.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Comm.ON_Text = "خصم العمولة";
            this.ToggleButton_Comm.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Comm.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Comm.Size = new System.Drawing.Size(115, 25);
            this.ToggleButton_Comm.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Comm.TabIndex = 84;
            this.ToggleButton_Comm.Tag = "تفصيلي";
            this.ToggleButton_Comm.Text = "#";
            this.ToggleButton_Comm.UseVisualStyleBackColor = true;
            this.ToggleButton_Comm.Visible = false;
            this.ToggleButton_Comm.CheckedChanged += new System.EventHandler(this.ToggleButton_Comm_CheckedChanged);
            // 
            // cbCategory
            // 
            this.cbCategory.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.cbCategory.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.cbCategory.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.cbCategory.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbCategory.BorderRadius = 0;
            this.cbCategory.BorderSize = 1;
            this.cbCategory.Customizable = false;
            this.cbCategory.DataSource = null;
            this.cbCategory.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.cbCategory.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.cbCategory.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.cbCategory.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.cbCategory.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.cbCategory.Items.AddRange(new object[] {
            "الكل",
            "الباقة",
            "نقطة البيع",
            "الاجهزة"});
            this.cbCategory.Location = new System.Drawing.Point(10, 36);
            this.cbCategory.Name = "cbCategory";
            this.cbCategory.Padding = new System.Windows.Forms.Padding(1);
            this.cbCategory.SelectedIndex = -1;
            this.cbCategory.Size = new System.Drawing.Size(43, 18);
            this.cbCategory.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.cbCategory.TabIndex = 3;
            this.cbCategory.Texts = "";
            this.cbCategory.Visible = false;
            // 
            // dtpEndDate
            // 
            this.dtpEndDate.CustomFormat = "MMM dd, yyyy";
            this.dtpEndDate.Font = new System.Drawing.Font("Microsoft Sans Serif", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dtpEndDate.Format = System.Windows.Forms.DateTimePickerFormat.Custom;
            this.dtpEndDate.Location = new System.Drawing.Point(27, 10);
            this.dtpEndDate.Name = "dtpEndDate";
            this.dtpEndDate.Size = new System.Drawing.Size(122, 20);
            this.dtpEndDate.TabIndex = 5;
            this.dtpEndDate.Visible = false;
            this.dtpEndDate.ValueChanged += new System.EventHandler(this.dtpEndDate_ValueChanged);
            // 
            // rjLabel3
            // 
            this.rjLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(107, 21);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(48, 22);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 2;
            this.rjLabel3.Text = "السيرفر";
            this.rjLabel3.Visible = false;
            this.rjLabel3.Click += new System.EventHandler(this.rjLabel3_Click);
            // 
            // rjPanel7
            // 
            this.rjPanel7.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel7.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel7.BorderRadius = 5;
            this.rjPanel7.Controls.Add(this.btn_Dashbard_Refersh);
            this.rjPanel7.Controls.Add(this.rjLabel3);
            this.rjPanel7.Controls.Add(this.btn_Refresh);
            this.rjPanel7.Controls.Add(this.rjLabel2);
            this.rjPanel7.Customizable = false;
            this.rjPanel7.Location = new System.Drawing.Point(17, 94);
            this.rjPanel7.Name = "rjPanel7";
            this.rjPanel7.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjPanel7.Size = new System.Drawing.Size(222, 80);
            this.rjPanel7.TabIndex = 1;
            // 
            // btn_Dashbard_Refersh
            // 
            this.btn_Dashbard_Refersh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Dashbard_Refersh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Dashbard_Refersh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Dashbard_Refersh.BorderRadius = 4;
            this.btn_Dashbard_Refersh.BorderSize = 1;
            this.btn_Dashbard_Refersh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Dashbard_Refersh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Dashbard_Refersh.FlatAppearance.BorderSize = 0;
            this.btn_Dashbard_Refersh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Dashbard_Refersh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Dashbard_Refersh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Dashbard_Refersh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Dashbard_Refersh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Dashbard_Refersh.IconChar = FontAwesome.Sharp.IconChar.Delicious;
            this.btn_Dashbard_Refersh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Dashbard_Refersh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Dashbard_Refersh.IconSize = 35;
            this.btn_Dashbard_Refersh.Location = new System.Drawing.Point(167, 21);
            this.btn_Dashbard_Refersh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Dashbard_Refersh.Name = "btn_Dashbard_Refersh";
            this.btn_Dashbard_Refersh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Dashbard_Refersh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Dashbard_Refersh.Size = new System.Drawing.Size(42, 39);
            this.btn_Dashbard_Refersh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Dashbard_Refersh.TabIndex = 51;
            this.btn_Dashbard_Refersh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Dashbard_Refersh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.toolTip1.SetToolTip(this.btn_Dashbard_Refersh, "تحديث بيانات الواجهه الرئيسية");
            this.btn_Dashbard_Refersh.UseVisualStyleBackColor = false;
            this.btn_Dashbard_Refersh.Click += new System.EventHandler(this.btn_Dashbard_Refersh_Click);
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 35;
            this.btn_Refresh.Location = new System.Drawing.Point(11, 21);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(42, 39);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 51;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.toolTip1.SetToolTip(this.btn_Refresh, "تحديث جيمع البيانات من الروتر");
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(107, 21);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(44, 22);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 0;
            this.rjLabel2.Text = "الفتــرة";
            this.rjLabel2.Visible = false;
            // 
            // CboxServerType
            // 
            this.CboxServerType.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.CboxServerType.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CboxServerType.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CboxServerType.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CboxServerType.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CboxServerType.BorderRadius = 0;
            this.CboxServerType.BorderSize = 1;
            this.CboxServerType.Customizable = false;
            this.CboxServerType.DataSource = null;
            this.CboxServerType.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CboxServerType.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CboxServerType.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CboxServerType.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CboxServerType.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CboxServerType.Items.AddRange(new object[] {
            "يوزمنجر",
            "هوتسبوت"});
            this.CboxServerType.Location = new System.Drawing.Point(11, 45);
            this.CboxServerType.Name = "CboxServerType";
            this.CboxServerType.Padding = new System.Windows.Forms.Padding(1);
            this.CboxServerType.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CboxServerType.SelectedIndex = -1;
            this.CboxServerType.Size = new System.Drawing.Size(202, 30);
            this.CboxServerType.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CboxServerType.TabIndex = 3;
            this.CboxServerType.Texts = "";
            this.CboxServerType.OnSelectedIndexChanged += new System.EventHandler(this.CboxServerType_OnSelectedIndexChanged);
            // 
            // CboxPeriod
            // 
            this.CboxPeriod.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.CboxPeriod.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CboxPeriod.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CboxPeriod.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CboxPeriod.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CboxPeriod.BorderRadius = 0;
            this.CboxPeriod.BorderSize = 1;
            this.CboxPeriod.Customizable = false;
            this.CboxPeriod.DataSource = null;
            this.CboxPeriod.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CboxPeriod.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CboxPeriod.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CboxPeriod.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CboxPeriod.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CboxPeriod.Items.AddRange(new object[] {
            "اليوم",
            "اخر 7 ايام",
            "هذا الشهر",
            "اخر 30 يوم",
            "هذا العام",
            "مخصص"});
            this.CboxPeriod.Location = new System.Drawing.Point(11, 6);
            this.CboxPeriod.Name = "CboxPeriod";
            this.CboxPeriod.Padding = new System.Windows.Forms.Padding(1);
            this.CboxPeriod.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CboxPeriod.SelectedIndex = -1;
            this.CboxPeriod.Size = new System.Drawing.Size(202, 30);
            this.CboxPeriod.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CboxPeriod.TabIndex = 1;
            this.CboxPeriod.Texts = "";
            this.CboxPeriod.OnSelectedIndexChanged += new System.EventHandler(this.cbPeriod_OnSelectedIndexChanged);
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 5;
            this.rjPanel2.Controls.Add(this.rjLabel7);
            this.rjPanel2.Controls.Add(this.lblNumberSessionUM);
            this.rjPanel2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(242, 8);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel2.Size = new System.Drawing.Size(138, 80);
            this.rjPanel2.TabIndex = 4;
            // 
            // rjLabel7
            // 
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(3, 3);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(132, 22);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 0;
            this.rjLabel7.Text = "جلسات اليوزمنجر";
            this.rjLabel7.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 5;
            this.rjPanel3.Controls.Add(this.rjLabel13);
            this.rjPanel3.Controls.Add(this.lblNumberUserManager);
            this.rjPanel3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(384, 8);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel3.Size = new System.Drawing.Size(138, 80);
            this.rjPanel3.TabIndex = 4;
            // 
            // rjLabel13
            // 
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(3, 3);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel13.Size = new System.Drawing.Size(132, 22);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 0;
            this.rjLabel13.Text = "كروت اليوزمنجر";
            this.rjLabel13.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblNumberUserManager
            // 
            this.lblNumberUserManager.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblNumberUserManager.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lblNumberUserManager.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblNumberUserManager.LinkLabel = true;
            this.lblNumberUserManager.Location = new System.Drawing.Point(3, 37);
            this.lblNumberUserManager.Name = "lblNumberUserManager";
            this.lblNumberUserManager.Size = new System.Drawing.Size(135, 23);
            this.lblNumberUserManager.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblNumberUserManager.TabIndex = 1;
            this.lblNumberUserManager.Text = "0";
            this.lblNumberUserManager.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lbActiveCards
            // 
            this.lbActiveCards.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lbActiveCards.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lbActiveCards.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbActiveCards.LinkLabel = true;
            this.lbActiveCards.Location = new System.Drawing.Point(3, 37);
            this.lbActiveCards.Name = "lbActiveCards";
            this.lbActiveCards.Size = new System.Drawing.Size(139, 23);
            this.lbActiveCards.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbActiveCards.TabIndex = 1;
            this.lbActiveCards.Text = "0";
            this.lbActiveCards.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel9
            // 
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(3, 3);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(142, 22);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 0;
            this.rjLabel9.Text = "النشطين (Acive)";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel4
            // 
            this.rjPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel4.BorderRadius = 5;
            this.rjPanel4.Controls.Add(this.lblCountHotspot);
            this.rjPanel4.Controls.Add(this.rjLabel11);
            this.rjPanel4.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel4.Customizable = false;
            this.rjPanel4.Location = new System.Drawing.Point(528, 8);
            this.rjPanel4.Name = "rjPanel4";
            this.rjPanel4.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel4.Size = new System.Drawing.Size(149, 80);
            this.rjPanel4.TabIndex = 4;
            // 
            // lblCountHotspot
            // 
            this.lblCountHotspot.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblCountHotspot.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lblCountHotspot.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblCountHotspot.LinkLabel = true;
            this.lblCountHotspot.Location = new System.Drawing.Point(7, 37);
            this.lblCountHotspot.Name = "lblCountHotspot";
            this.lblCountHotspot.Size = new System.Drawing.Size(139, 23);
            this.lblCountHotspot.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblCountHotspot.TabIndex = 1;
            this.lblCountHotspot.Text = "0";
            this.lblCountHotspot.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel11
            // 
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(3, 3);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(143, 22);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 0;
            this.rjLabel11.Text = "كروت الهوتسبوت";
            this.rjLabel11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel5
            // 
            this.rjPanel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel5.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel5.BorderRadius = 5;
            this.rjPanel5.Controls.Add(this.rjLabel9);
            this.rjPanel5.Controls.Add(this.lbActiveCards);
            this.rjPanel5.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel5.Customizable = false;
            this.rjPanel5.Location = new System.Drawing.Point(846, 8);
            this.rjPanel5.Name = "rjPanel5";
            this.rjPanel5.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel5.Size = new System.Drawing.Size(148, 80);
            this.rjPanel5.TabIndex = 4;
            // 
            // panelPadding
            // 
            this.panelPadding.Location = new System.Drawing.Point(34, 662);
            this.panelPadding.Name = "panelPadding";
            this.panelPadding.Size = new System.Drawing.Size(902, 18);
            this.panelPadding.TabIndex = 12;
            this.panelPadding.Visible = false;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // Cbox_By_Port
            // 
            this.Cbox_By_Port.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Cbox_By_Port.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.Cbox_By_Port.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.Cbox_By_Port.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Cbox_By_Port.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_By_Port.BorderRadius = 3;
            this.Cbox_By_Port.BorderSize = 1;
            this.Cbox_By_Port.Customizable = false;
            this.Cbox_By_Port.DataSource = null;
            this.Cbox_By_Port.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Cbox_By_Port.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Cbox_By_Port.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_By_Port.Font = new System.Drawing.Font("Droid Sans Arabic", 7.8F);
            this.Cbox_By_Port.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Cbox_By_Port.Items.AddRange(new object[] {
            "الاجهزة بحسب اكبر مبيعات",
            "بحسب اكثر استهلاك للجيجا",
            " بجسب اكثر استهلاك للوقت"});
            this.Cbox_By_Port.Location = new System.Drawing.Point(808, 430);
            this.Cbox_By_Port.Name = "Cbox_By_Port";
            this.Cbox_By_Port.Padding = new System.Windows.Forms.Padding(1);
            this.Cbox_By_Port.SelectedIndex = -1;
            this.Cbox_By_Port.Size = new System.Drawing.Size(179, 30);
            this.Cbox_By_Port.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Cbox_By_Port.TabIndex = 3;
            this.Cbox_By_Port.Texts = "";
            this.Cbox_By_Port.OnSelectedIndexChanged += new System.EventHandler(this.Cbox_By_Port_OnSelectedIndexChanged);
            // 
            // Cbox_By_profile
            // 
            this.Cbox_By_profile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Cbox_By_profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.Cbox_By_profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.Cbox_By_profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Cbox_By_profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_By_profile.BorderRadius = 3;
            this.Cbox_By_profile.BorderSize = 1;
            this.Cbox_By_profile.Customizable = false;
            this.Cbox_By_profile.DataSource = null;
            this.Cbox_By_profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.Cbox_By_profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.Cbox_By_profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Cbox_By_profile.Font = new System.Drawing.Font("Droid Sans Arabic", 7.8F);
            this.Cbox_By_profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Cbox_By_profile.Items.AddRange(new object[] {
            "الباقات بحسب اكبر مبيعات",
            "بحسب اكثر استهلاك للجيجا",
            " بجسب اكثر استهلاك للوقت"});
            this.Cbox_By_profile.Location = new System.Drawing.Point(809, 198);
            this.Cbox_By_profile.Name = "Cbox_By_profile";
            this.Cbox_By_profile.Padding = new System.Windows.Forms.Padding(1);
            this.Cbox_By_profile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.Cbox_By_profile.SelectedIndex = -1;
            this.Cbox_By_profile.Size = new System.Drawing.Size(179, 30);
            this.Cbox_By_profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Cbox_By_profile.TabIndex = 3;
            this.Cbox_By_profile.Texts = "";
            this.Cbox_By_profile.OnSelectedIndexChanged += new System.EventHandler(this.Cbox_By_profile_OnSelectedIndexChanged);
            // 
            // chartSalesByProfile
            // 
            this.chartSalesByProfile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chartSalesByProfile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea2.AlignmentOrientation = ((System.Windows.Forms.DataVisualization.Charting.AreaAlignmentOrientations)((System.Windows.Forms.DataVisualization.Charting.AreaAlignmentOrientations.Vertical | System.Windows.Forms.DataVisualization.Charting.AreaAlignmentOrientations.Horizontal)));
            chartArea2.AxisX.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea2.AxisX.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea2.AxisX.LineWidth = 2;
            chartArea2.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea2.AxisX.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea2.AxisX.MajorTickMark.LineWidth = 2;
            chartArea2.AxisY.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea2.AxisY.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea2.AxisY.LineWidth = 2;
            chartArea2.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea2.AxisY.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea2.AxisY.MajorTickMark.LineWidth = 2;
            chartArea2.AxisY.MinorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea2.Name = "ChartArea1";
            this.chartSalesByProfile.ChartAreas.Add(chartArea2);
            legend2.Alignment = System.Drawing.StringAlignment.Far;
            legend2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            legend2.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F);
            legend2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            legend2.IsTextAutoFit = false;
            legend2.Name = "Legend1";
            this.chartSalesByProfile.Legends.Add(legend2);
            this.chartSalesByProfile.Location = new System.Drawing.Point(521, 181);
            this.chartSalesByProfile.Name = "chartSalesByProfile";
            this.chartSalesByProfile.Palette = System.Windows.Forms.DataVisualization.Charting.ChartColorPalette.Pastel;
            series2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            series2.ChartArea = "ChartArea1";
            series2.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Doughnut;
            series2.LabelForeColor = System.Drawing.Color.White;
            series2.Legend = "Legend1";
            series2.Name = "Series1";
            this.chartSalesByProfile.Series.Add(series2);
            this.chartSalesByProfile.Size = new System.Drawing.Size(474, 228);
            this.chartSalesByProfile.TabIndex = 13;
            this.chartSalesByProfile.Text = "rjChart1";
            // 
            // rjPanel8
            // 
            this.rjPanel8.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel8.BorderRadius = 5;
            this.rjPanel8.Controls.Add(this.CboxServerType);
            this.rjPanel8.Controls.Add(this.CboxPeriod);
            this.rjPanel8.Customizable = false;
            this.rjPanel8.Location = new System.Drawing.Point(17, 8);
            this.rjPanel8.Name = "rjPanel8";
            this.rjPanel8.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjPanel8.Size = new System.Drawing.Size(222, 80);
            this.rjPanel8.TabIndex = 4;
            // 
            // chartRevenue
            // 
            this.chartRevenue.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.chartRevenue.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea1.AxisX.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea1.AxisX.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea1.AxisX.LineWidth = 2;
            chartArea1.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea1.AxisX.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea1.AxisX.MajorTickMark.LineWidth = 2;
            chartArea1.AxisY.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea1.AxisY.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea1.AxisY.LineWidth = 2;
            chartArea1.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea1.AxisY.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea1.AxisY.MajorTickMark.LineWidth = 2;
            chartArea1.AxisY.MinorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea1.Name = "ChartArea1";
            this.chartRevenue.ChartAreas.Add(chartArea1);
            legend1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            legend1.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F);
            legend1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            legend1.IsTextAutoFit = false;
            legend1.Name = "Legend1";
            this.chartRevenue.Legends.Add(legend1);
            this.chartRevenue.Location = new System.Drawing.Point(9, 181);
            this.chartRevenue.Name = "chartRevenue";
            this.chartRevenue.Palette = System.Windows.Forms.DataVisualization.Charting.ChartColorPalette.Excel;
            series1.ChartArea = "ChartArea1";
            series1.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.SplineArea;
            series1.LabelForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            series1.Legend = "Legend1";
            series1.Name = "Series1";
            this.chartRevenue.Series.Add(series1);
            this.chartRevenue.Size = new System.Drawing.Size(506, 228);
            this.chartRevenue.TabIndex = 14;
            this.chartRevenue.Text = "rjChart1";
            // 
            // chartNumberSales
            // 
            this.chartNumberSales.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.chartNumberSales.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea4.AxisX.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea4.AxisX.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea4.AxisX.LineWidth = 2;
            chartArea4.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea4.AxisX.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea4.AxisX.MajorTickMark.LineWidth = 2;
            chartArea4.AxisY.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea4.AxisY.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea4.AxisY.LineWidth = 2;
            chartArea4.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea4.AxisY.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea4.AxisY.MajorTickMark.LineWidth = 2;
            chartArea4.AxisY.MinorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea4.Name = "ChartArea1";
            this.chartNumberSales.ChartAreas.Add(chartArea4);
            legend4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            legend4.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F);
            legend4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            legend4.IsTextAutoFit = false;
            legend4.Name = "Legend1";
            this.chartNumberSales.Legends.Add(legend4);
            this.chartNumberSales.Location = new System.Drawing.Point(9, 416);
            this.chartNumberSales.Name = "chartNumberSales";
            this.chartNumberSales.Palette = System.Windows.Forms.DataVisualization.Charting.ChartColorPalette.Excel;
            series4.ChartArea = "ChartArea1";
            series4.LabelForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            series4.Legend = "Legend1";
            series4.Name = "Series1";
            this.chartNumberSales.Series.Add(series4);
            this.chartNumberSales.Size = new System.Drawing.Size(506, 226);
            this.chartNumberSales.TabIndex = 15;
            this.chartNumberSales.Text = "rjChart2";
            // 
            // chartTop5Products
            // 
            this.chartTop5Products.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.chartTop5Products.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea3.AxisX.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea3.AxisX.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea3.AxisX.LineWidth = 2;
            chartArea3.AxisX.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea3.AxisX.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea3.AxisX.MajorTickMark.LineWidth = 2;
            chartArea3.AxisY.LabelStyle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            chartArea3.AxisY.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea3.AxisY.LineWidth = 2;
            chartArea3.AxisY.MajorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea3.AxisY.MajorTickMark.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(168)))), ((int)(((byte)(166)))), ((int)(((byte)(168)))));
            chartArea3.AxisY.MajorTickMark.LineWidth = 2;
            chartArea3.AxisY.MinorGrid.LineColor = System.Drawing.Color.FromArgb(((int)(((byte)(231)))), ((int)(((byte)(238)))), ((int)(((byte)(246)))));
            chartArea3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            chartArea3.Name = "ChartArea1";
            this.chartTop5Products.ChartAreas.Add(chartArea3);
            legend3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            legend3.Font = new System.Drawing.Font("Microsoft Sans Serif", 8.5F);
            legend3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            legend3.IsTextAutoFit = false;
            legend3.Name = "Legend1";
            this.chartTop5Products.Legends.Add(legend3);
            this.chartTop5Products.Location = new System.Drawing.Point(521, 416);
            this.chartTop5Products.Name = "chartTop5Products";
            this.chartTop5Products.Palette = System.Windows.Forms.DataVisualization.Charting.ChartColorPalette.Excel;
            series3.ChartArea = "ChartArea1";
            series3.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Bar;
            series3.LabelForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            series3.Legend = "Legend1";
            series3.Name = "Series1";
            this.chartTop5Products.Series.Add(series3);
            this.chartTop5Products.Size = new System.Drawing.Size(474, 226);
            this.chartTop5Products.TabIndex = 16;
            this.chartTop5Products.Text = "rjChart3";
            // 
            // rjPanel9
            // 
            this.rjPanel9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel9.BorderRadius = 5;
            this.rjPanel9.Controls.Add(this.lbCountHost);
            this.rjPanel9.Controls.Add(this.rjLabel5);
            this.rjPanel9.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel9.Customizable = false;
            this.rjPanel9.Location = new System.Drawing.Point(683, 8);
            this.rjPanel9.Name = "rjPanel9";
            this.rjPanel9.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel9.Size = new System.Drawing.Size(157, 80);
            this.rjPanel9.TabIndex = 4;
            // 
            // lbCountHost
            // 
            this.lbCountHost.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lbCountHost.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lbCountHost.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbCountHost.LinkLabel = true;
            this.lbCountHost.Location = new System.Drawing.Point(7, 37);
            this.lbCountHost.Name = "lbCountHost";
            this.lbCountHost.Size = new System.Drawing.Size(144, 23);
            this.lbCountHost.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbCountHost.TabIndex = 1;
            this.lbCountHost.Text = "0";
            this.lbCountHost.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel5
            // 
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(3, 3);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(151, 22);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 0;
            this.rjLabel5.Text = "المتصلين (Host)";
            this.rjLabel5.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel10
            // 
            this.rjPanel10.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel10.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel10.BorderRadius = 5;
            this.rjPanel10.Controls.Add(this.rjLabel6);
            this.rjPanel10.Controls.Add(this.lblCountNeghibor);
            this.rjPanel10.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel10.Customizable = false;
            this.rjPanel10.Location = new System.Drawing.Point(683, 94);
            this.rjPanel10.Name = "rjPanel10";
            this.rjPanel10.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel10.Size = new System.Drawing.Size(158, 80);
            this.rjPanel10.TabIndex = 4;
            // 
            // rjLabel6
            // 
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(3, 3);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(152, 22);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 3;
            this.rjLabel6.Text = "عدد الاجهزه (Neighbor)";
            this.rjLabel6.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblCountNeghibor
            // 
            this.lblCountNeghibor.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblCountNeghibor.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lblCountNeghibor.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblCountNeghibor.LinkLabel = true;
            this.lblCountNeghibor.Location = new System.Drawing.Point(7, 37);
            this.lblCountNeghibor.Name = "lblCountNeghibor";
            this.lblCountNeghibor.Size = new System.Drawing.Size(150, 23);
            this.lblCountNeghibor.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblCountNeghibor.TabIndex = 1;
            this.lblCountNeghibor.Text = "0";
            this.lblCountNeghibor.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel11
            // 
            this.rjPanel11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel11.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel11.BorderRadius = 5;
            this.rjPanel11.Controls.Add(this.rjLabel12);
            this.rjPanel11.Controls.Add(this.lbl_Rb_model);
            this.rjPanel11.Customizable = false;
            this.rjPanel11.Location = new System.Drawing.Point(528, 94);
            this.rjPanel11.Name = "rjPanel11";
            this.rjPanel11.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel11.Size = new System.Drawing.Size(149, 80);
            this.rjPanel11.TabIndex = 1;
            // 
            // rjLabel12
            // 
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel12.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(3, 3);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel12.Size = new System.Drawing.Size(143, 22);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel12.TabIndex = 1;
            this.rjLabel12.Text = "نوعية الروتر";
            this.rjLabel12.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lbl_Rb_model
            // 
            this.lbl_Rb_model.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lbl_Rb_model.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Rb_model.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbl_Rb_model.LinkLabel = true;
            this.lbl_Rb_model.Location = new System.Drawing.Point(1, 37);
            this.lbl_Rb_model.Name = "lbl_Rb_model";
            this.lbl_Rb_model.Size = new System.Drawing.Size(145, 23);
            this.lbl_Rb_model.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbl_Rb_model.TabIndex = 1;
            this.lbl_Rb_model.Text = "0";
            this.lbl_Rb_model.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel12
            // 
            this.rjPanel12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel12.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel12.BorderRadius = 5;
            this.rjPanel12.Controls.Add(this.rjLabel1);
            this.rjPanel12.Controls.Add(this.lbl_Uptime);
            this.rjPanel12.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel12.Customizable = false;
            this.rjPanel12.Location = new System.Drawing.Point(242, 94);
            this.rjPanel12.Name = "rjPanel12";
            this.rjPanel12.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel12.Size = new System.Drawing.Size(139, 80);
            this.rjPanel12.TabIndex = 4;
            // 
            // rjLabel1
            // 
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(3, 3);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(133, 22);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 0;
            this.rjLabel1.Text = "مدة التشغيل";
            this.rjLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lbl_Uptime
            // 
            this.lbl_Uptime.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lbl_Uptime.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbl_Uptime.LinkLabel = true;
            this.lbl_Uptime.Location = new System.Drawing.Point(3, 37);
            this.lbl_Uptime.Name = "lbl_Uptime";
            this.lbl_Uptime.Size = new System.Drawing.Size(133, 23);
            this.lbl_Uptime.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbl_Uptime.TabIndex = 1;
            this.lbl_Uptime.Text = "0";
            this.lbl_Uptime.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjPanel13
            // 
            this.rjPanel13.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel13.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel13.BorderRadius = 5;
            this.rjPanel13.Controls.Add(this.rjLabel8);
            this.rjPanel13.Controls.Add(this.lbl_Rb_Version);
            this.rjPanel13.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjPanel13.Customizable = false;
            this.rjPanel13.Location = new System.Drawing.Point(384, 94);
            this.rjPanel13.Name = "rjPanel13";
            this.rjPanel13.Padding = new System.Windows.Forms.Padding(3);
            this.rjPanel13.Size = new System.Drawing.Size(138, 80);
            this.rjPanel13.TabIndex = 4;
            // 
            // rjLabel8
            // 
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Dock = System.Windows.Forms.DockStyle.Top;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(3, 3);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(132, 22);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 0;
            this.rjLabel8.Text = "اصدار النظام";
            this.rjLabel8.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lbl_Rb_Version
            // 
            this.lbl_Rb_Version.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lbl_Rb_Version.Font = new System.Drawing.Font("Verdana", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Rb_Version.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbl_Rb_Version.LinkLabel = true;
            this.lbl_Rb_Version.Location = new System.Drawing.Point(3, 37);
            this.lbl_Rb_Version.Name = "lbl_Rb_Version";
            this.lbl_Rb_Version.Size = new System.Drawing.Size(129, 23);
            this.lbl_Rb_Version.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbl_Rb_Version.TabIndex = 1;
            this.lbl_Rb_Version.Text = "0";
            this.lbl_Rb_Version.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // FormDashboard
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 1;
            this.Caption = "Dashboard";
            this.ClientSize = new System.Drawing.Size(1000, 725);
            this.FormIcon = FontAwesome.Sharp.IconChar.PieChart;
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "FormDashboard";
            this.Padding = new System.Windows.Forms.Padding(1);
            this.Text = "Dashboard";
            this.Load += new System.EventHandler(this.FormDashboard_Load);
            this.SizeChanged += new System.EventHandler(this.FormDashboard_SizeChanged);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel6.ResumeLayout(false);
            this.rjPanel7.ResumeLayout(false);
            this.rjPanel7.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel4.ResumeLayout(false);
            this.rjPanel5.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartSalesByProfile)).EndInit();
            this.rjPanel8.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.chartRevenue)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartNumberSales)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chartTop5Products)).EndInit();
            this.rjPanel9.ResumeLayout(false);
            this.rjPanel10.ResumeLayout(false);
            this.rjPanel11.ResumeLayout(false);
            this.rjPanel12.ResumeLayout(false);
            this.rjPanel13.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel1;
        //private RJControls.RJChart chartTop5Products;
        //private RJControls.RJChart chartRevenue;
        //private RJControls.RJChart chartNumberSales;
        //private RJControls.RJChart chartSalesByProfile;
        private RJControls.RJPanel rjPanel7;
        private RJControls.RJPanel rjPanel6;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJComboBox cbCategory;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel lblNumberSessionUM;
        private RJControls.RJPanel rjPanel5;
        private RJControls.RJLabel lblNumberUserManager;
        private RJControls.RJLabel rjLabel13;
        private RJControls.RJPanel rjPanel4;
        private RJControls.RJLabel lblCountHotspot;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJLabel lbActiveCards;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJLabel lblRevenue;
        private RJControls.RJLabel rjLabel7;
        private System.Windows.Forms.Panel panelPadding;
        private System.Windows.Forms.DateTimePicker dtpEndDate;
        private System.Windows.Forms.DateTimePicker dtpStartDate;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJComboBox Cbox_By_Port;
        private RJControls.RJComboBox Cbox_By_profile;
        public RJControls.RJComboBox CboxPeriod;
        private RJControls.RJToggleButton ToggleButton_Comm;
        private RJControls.RJChart chartSalesByProfile;
        private RJControls.RJChart chartRevenue;
        private RJControls.RJPanel rjPanel8;
        private RJControls.RJChart chartTop5Products;
        private RJControls.RJChart chartNumberSales;
        private RJControls.RJPanel rjPanel10;
        private RJControls.RJLabel lblCountNeghibor;
        private RJControls.RJPanel rjPanel9;
        private RJControls.RJLabel lbCountHost;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJPanel rjPanel11;
        private RJControls.RJComboBox CboxServerType;
        private RJControls.RJPanel rjPanel13;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJLabel lbl_Rb_Version;
        private RJControls.RJPanel rjPanel12;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel lbl_Uptime;
        private RJControls.RJLabel rjLabel12;
        private RJControls.RJLabel lbl_Rb_model;
        private RJControls.RJButton btn_Refresh;
        private System.Windows.Forms.ToolTip toolTip1;
        private RJControls.RJButton btn_Dashbard_Refersh;
        private RJControls.RJLabel rjLabel6;
    }
}