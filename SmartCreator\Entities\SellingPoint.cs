﻿//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Models;
using System;
using System.ComponentModel;

//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    [UniqueConstraint(nameof(Code), nameof(Rb))]
    public class SellingPoint
    {
        //public SellingPoint() { }
        
        [PrimaryKey, AutoIncrement,Required,Unique] 
        public int Id { get; set; }

        [DisplayName("رقم النقطة"), StringLength(100),Required]
        public string Code { get; set; }
        
        [DisplayName("اسم النقطة"), StringLength(200),Required]
        public string UserName { get; set; }
       
        [DisplayName("بادئة الكروت"), StringLength(100)]
        public string Prefixes { get; set; }
        
        [DisplayName("نهاية الكروت"), StringLength(100)]
        public string Suffixes { get; set; }
        
        [DisplayName("العنوان"), StringLength(100)]
        public string Address { get; set; }

        [DisplayName("رقم الهاتف"), StringLength(100)]
        public string Phone { get; set; }


        [Default(0),Browsable(false)]
        public int Is_percentage { get; set; }
        [DisplayName("احتساب عمولة"),Computed]
        public bool Is_percentage_str
        {
            get
            {
                return Convert.ToBoolean(Is_percentage);
            }
        }
        [DisplayName("قيمة العمولة"), Default(0)]
        public float Percentage { get; set; } = 0;
        [ Default(0),Browsable(false)]    
        public int PercentageType { get; set; } = 0;
        [DisplayName("طريقة حساب العمولة"), Default(0),Computed]
        public string PercentageType_Srt {
            get
            {
                if (PercentageType == 0)
                    return "نسبة مئويه";
                else 
                    return "قيمة ثابتة";
            }
        }
        [Default(0), Browsable(false)]
        public int Is_percentage_Custom { get; set; }


        [Browsable(false)]
        public int Is_Alert { get; set; }
        [Computed,DisplayName("التنبيهات")]
        public bool Is_Alert_str
        {
            get
            {
                return Convert.ToBoolean(Is_Alert);
            }
        }
        [Default(0), Browsable(false)]
        public int Count_Soon { get; set; } = 0;
        [Default(0), Browsable(false)]
        public int Count_Finsh { get; set; } = 0;
        [Default(0), Browsable(false)]
        public int Is_Alert_Custom { get; set; }



        [Default(0),Browsable(false)]    
        public int UseAccounting { get; set; } = 0;
        [DisplayName("مرتبط بالنظام المالي"), Default(0)]

        public bool UseAccounting_ٍStr
        {
            get
            {
                return Convert.ToBoolean(UseAccounting);
            }
        }

        [Browsable(false)]
        public string Rb { get; set; } = "";
        [Browsable(false)]
        public string Rb_Sn { get; set; } = "";



    }


    [UniqueConstraint(nameof(SpCode), nameof(Rb), nameof(ProfileName))]
    public class Comm_SellingPoint
    {
        [PrimaryKey, AutoIncrement, Required, Unique]
        public int Id { get; set; }
        public string SpCode { get; set; }
        [DisplayName("الباقة")]
        public string ProfileName { get; set; }
        public int Is_percentage { get; set; }
        [DisplayName("احتساب عموله"), Computed]
        public bool Is_percentage_str
        {
            get
            {
                return Convert.ToBoolean(Is_percentage);
            }
        }
        [DisplayName("قيمة العمولة"), Default(0)]
        public float Percentage { get; set; } = 0;
        public int PercentageType { get; set; } = 0;
        [DisplayName("طريقة حساب العمولة"), Default(0), Computed]
        public string PercentageType_Srt
        {
            get
            {
                if (PercentageType == 0)
                    return "نسبة مئويه";
                else
                    return "قيمة ثابتة";
            }
        }
        [Browsable(false)]
        public string Rb { get; set; } = "";
        [Browsable(false)]
        public string Rb_Sn { get; set; } = "";

    }

    [UniqueConstraint(nameof(SpCode), nameof(Rb), nameof(ProfileName))]
    public class Alert_SellingPoint
    {
        [PrimaryKey, AutoIncrement, Required, Unique]
        public int Id { get; set; }
        //[DisplayName("نقطة البيع")]
        public string SpCode { get; set; }
        [DisplayName("الباقة")]
        public string ProfileName { get; set; }
        public int Is_Alert { get; set; }
        [Computed, DisplayName("نشط")]
        public bool Is_Alert_str
        {
            get
            {
                return Convert.ToBoolean(Is_Alert);
            }
        }
        [Computed, DisplayName("نسبه اقتراب الانتهاء%")]
        public int Count_Soon { get; set; } = 0;
        [Computed, DisplayName("نسبة انتهاء الكروت%")]
        public int Count_Finsh { get; set; } = 0;
        [Browsable(false)]
        public string Rb { get; set; } = "";
        [Browsable(false)]
        public string Rb_Sn { get; set; } = "";


    }

}
