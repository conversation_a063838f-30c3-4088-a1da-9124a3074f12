using System;

namespace SmartCreator.Data.CustomORM
{
    /// <summary>
    /// تحديد اسم الجدول في قاعدة البيانات
    /// </summary>
    [AttributeUsage(AttributeTargets.Class)]
    public class TableAttribute : Attribute
    {
        public string Name { get; }
        
        public TableAttribute(string name)
        {
            Name = name;
        }
    }

    /// <summary>
    /// تحديد اسم العمود في قاعدة البيانات
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class ColumnAttribute : Attribute
    {
        public string Name { get; }
        public string? DataType { get; set; }
        public int? MaxLength { get; set; }
        public bool IsNullable { get; set; } = true;
        
        public ColumnAttribute(string name)
        {
            Name = name;
        }
    }

    /// <summary>
    /// تحديد المفتاح الأساسي
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class KeyAttribute : Attribute
    {
        public bool IsAutoIncrement { get; set; } = true;
    }

    /// <summary>
    /// تحديد المفتاح الخارجي
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class ForeignKeyAttribute : Attribute
    {
        public string ReferencedTable { get; }
        public string ReferencedColumn { get; }
        
        public ForeignKeyAttribute(string referencedTable, string referencedColumn = "Id")
        {
            ReferencedTable = referencedTable;
            ReferencedColumn = referencedColumn;
        }
    }

    /// <summary>
    /// استبعاد الخاصية من عمليات قاعدة البيانات
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class NotMappedAttribute : Attribute
    {
    }

    /// <summary>
    /// تحديد فهرس على العمود
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class IndexAttribute : Attribute
    {
        public string? Name { get; set; }
        public bool IsUnique { get; set; } = false;
    }

    /// <summary>
    /// تحديد قيمة افتراضية للعمود
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class DefaultValueAttribute : Attribute
    {
        public object Value { get; }
        
        public DefaultValueAttribute(object value)
        {
            Value = value;
        }
    }

    /// <summary>
    /// تحديد أن العمود مطلوب (NOT NULL)
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class RequiredAttribute : Attribute
    {
        public string? ErrorMessage { get; set; }
    }

    /// <summary>
    /// تحديد الحد الأقصى لطول النص
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class MaxLengthAttribute : Attribute
    {
        public int Length { get; }
        
        public MaxLengthAttribute(int length)
        {
            Length = length;
        }
    }

    /// <summary>
    /// تحديد نطاق القيم المسموحة
    /// </summary>
    [AttributeUsage(AttributeTargets.Property)]
    public class RangeAttribute : Attribute
    {
        public object Minimum { get; }
        public object Maximum { get; }
        
        public RangeAttribute(object minimum, object maximum)
        {
            Minimum = minimum;
            Maximum = maximum;
        }
    }
}
