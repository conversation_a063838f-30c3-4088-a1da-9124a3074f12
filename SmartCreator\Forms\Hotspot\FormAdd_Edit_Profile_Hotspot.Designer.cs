﻿namespace SmartCreator.Forms.Hotspot
{
    partial class FormAdd_Edit_Profile_Hotspot
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.rjCheckBox_group = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SizeDownload = new SmartCreator.RJControls.RJComboBox();
            this.groupBox3 = new System.Windows.Forms.GroupBox();
            this.CBox_profile_hotspot = new SmartCreator.RJControls.RJComboBox();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.CheckBox_byDayOrHour = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_session = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_download = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_Save_time = new SmartCreator.RJControls.RJCheckBox();
            this.CheckBox_SmartScript = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.btnSave = new SmartCreator.RJControls.RJButton();
            this.lblTitle = new SmartCreator.RJControls.RJLabel();
            this.txt_price_salse = new SmartCreator.RJControls.RJTextBox();
            this.txt_uptime_Hour = new SmartCreator.RJControls.RJTextBox();
            this.txt_profileName = new SmartCreator.RJControls.RJTextBox();
            this.txt_Download = new SmartCreator.RJControls.RJTextBox();
            this.txt_Validity = new SmartCreator.RJControls.RJTextBox();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.txt_price_display = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.txt_precent = new SmartCreator.RJControls.RJTextBox();
            this.rjCheckBox_precent = new SmartCreator.RJControls.RJCheckBox();
            this.CBOX_precent_type = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel26 = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.groupBox3.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.CBOX_precent_type);
            this.pnlClientArea.Controls.Add(this.txt_precent);
            this.pnlClientArea.Controls.Add(this.rjLabel12);
            this.pnlClientArea.Controls.Add(this.CBox_SizeDownload);
            this.pnlClientArea.Controls.Add(this.CheckBox_SmartScript);
            this.pnlClientArea.Controls.Add(this.groupBox3);
            this.pnlClientArea.Controls.Add(this.groupBox1);
            this.pnlClientArea.Controls.Add(this.btnSave);
            this.pnlClientArea.Controls.Add(this.lblTitle);
            this.pnlClientArea.Controls.Add(this.txt_price_display);
            this.pnlClientArea.Controls.Add(this.txt_price_salse);
            this.pnlClientArea.Controls.Add(this.txt_uptime_Hour);
            this.pnlClientArea.Controls.Add(this.txt_profileName);
            this.pnlClientArea.Controls.Add(this.txt_Download);
            this.pnlClientArea.Controls.Add(this.txt_Validity);
            this.pnlClientArea.Controls.Add(this.rjLabel26);
            this.pnlClientArea.Controls.Add(this.rjLabel1);
            this.pnlClientArea.Controls.Add(this.rjLabel8);
            this.pnlClientArea.Controls.Add(this.rjLabel2);
            this.pnlClientArea.Controls.Add(this.rjCheckBox_precent);
            this.pnlClientArea.Controls.Add(this.rjLabel13);
            this.pnlClientArea.Controls.Add(this.rjLabel6);
            this.pnlClientArea.Controls.Add(this.rjLabel4);
            this.pnlClientArea.Controls.Add(this.rjLabel5);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(686, 367);
            this.pnlClientArea.TabIndex = 3;
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(204, 22);
            this.lblCaption.Text = "FormAdd_Edit_Profile_Hotspot";
            // 
            // rjCheckBox_group
            // 
            this.rjCheckBox_group.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjCheckBox_group.AutoSize = true;
            this.rjCheckBox_group.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox_group.BorderSize = 1;
            this.rjCheckBox_group.Check = false;
            this.rjCheckBox_group.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjCheckBox_group.Customizable = false;
            this.rjCheckBox_group.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjCheckBox_group.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjCheckBox_group.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox_group.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjCheckBox_group.Location = new System.Drawing.Point(2, 15);
            this.rjCheckBox_group.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjCheckBox_group.Name = "rjCheckBox_group";
            this.rjCheckBox_group.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.rjCheckBox_group.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjCheckBox_group.Size = new System.Drawing.Size(207, 21);
            this.rjCheckBox_group.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjCheckBox_group.TabIndex = 15;
            this.rjCheckBox_group.Text = "بوروفايل الهوتسبوت الافتراضي";
            this.rjCheckBox_group.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjCheckBox_group.UseVisualStyleBackColor = true;
            // 
            // rjLabel12
            // 
            this.rjLabel12.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel12.AutoSize = true;
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Font = new System.Drawing.Font("Cairo", 8F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(295, 88);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.Size = new System.Drawing.Size(25, 20);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.rjLabel12.TabIndex = 132;
            this.rjLabel12.Text = "ايام";
            // 
            // rjLabel13
            // 
            this.rjLabel13.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(176, 59);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel13.Size = new System.Drawing.Size(39, 17);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.rjLabel13.TabIndex = 130;
            this.rjLabel13.Text = "(H.M)";
            // 
            // CBox_SizeDownload
            // 
            this.CBox_SizeDownload.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SizeDownload.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SizeDownload.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SizeDownload.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SizeDownload.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SizeDownload.BorderRadius = 10;
            this.CBox_SizeDownload.BorderSize = 1;
            this.CBox_SizeDownload.Customizable = false;
            this.CBox_SizeDownload.DataSource = null;
            this.CBox_SizeDownload.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SizeDownload.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SizeDownload.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SizeDownload.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_SizeDownload.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SizeDownload.Items.AddRange(new object[] {
            "ميجابايت",
            "جيجابايت"});
            this.CBox_SizeDownload.Location = new System.Drawing.Point(451, 149);
            this.CBox_SizeDownload.Name = "CBox_SizeDownload";
            this.CBox_SizeDownload.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SizeDownload.SelectedIndex = -1;
            this.CBox_SizeDownload.Size = new System.Drawing.Size(113, 29);
            this.CBox_SizeDownload.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SizeDownload.TabIndex = 6;
            this.CBox_SizeDownload.Texts = "";
            // 
            // groupBox3
            // 
            this.groupBox3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox3.Controls.Add(this.rjCheckBox_group);
            this.groupBox3.Controls.Add(this.CBox_profile_hotspot);
            this.groupBox3.Location = new System.Drawing.Point(30, 197);
            this.groupBox3.Name = "groupBox3";
            this.groupBox3.Size = new System.Drawing.Size(235, 101);
            this.groupBox3.TabIndex = 127;
            this.groupBox3.TabStop = false;
            // 
            // CBox_profile_hotspot
            // 
            this.CBox_profile_hotspot.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_profile_hotspot.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_profile_hotspot.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_profile_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_profile_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_hotspot.BorderRadius = 10;
            this.CBox_profile_hotspot.BorderSize = 1;
            this.CBox_profile_hotspot.Customizable = false;
            this.CBox_profile_hotspot.DataSource = null;
            this.CBox_profile_hotspot.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_profile_hotspot.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_profile_hotspot.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_profile_hotspot.Location = new System.Drawing.Point(23, 49);
            this.CBox_profile_hotspot.Name = "CBox_profile_hotspot";
            this.CBox_profile_hotspot.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_profile_hotspot.SelectedIndex = -1;
            this.CBox_profile_hotspot.Size = new System.Drawing.Size(172, 29);
            this.CBox_profile_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_profile_hotspot.TabIndex = 16;
            this.CBox_profile_hotspot.Texts = "";
            // 
            // groupBox1
            // 
            this.groupBox1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.groupBox1.Controls.Add(this.CheckBox_byDayOrHour);
            this.groupBox1.Controls.Add(this.CheckBox_Save_session);
            this.groupBox1.Controls.Add(this.CheckBox_Save_download);
            this.groupBox1.Controls.Add(this.CheckBox_Save_time);
            this.groupBox1.Location = new System.Drawing.Point(271, 191);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(389, 154);
            this.groupBox1.TabIndex = 128;
            this.groupBox1.TabStop = false;
            // 
            // CheckBox_byDayOrHour
            // 
            this.CheckBox_byDayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_byDayOrHour.AutoSize = true;
            this.CheckBox_byDayOrHour.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.BorderSize = 1;
            this.CheckBox_byDayOrHour.Check = false;
            this.CheckBox_byDayOrHour.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDayOrHour.Customizable = false;
            this.CheckBox_byDayOrHour.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_byDayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDayOrHour.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDayOrHour.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_byDayOrHour.Location = new System.Drawing.Point(3, 119);
            this.CheckBox_byDayOrHour.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDayOrHour.Name = "CheckBox_byDayOrHour";
            this.CheckBox_byDayOrHour.Padding = new System.Windows.Forms.Padding(0, 0, 21, 0);
            this.CheckBox_byDayOrHour.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_byDayOrHour.Size = new System.Drawing.Size(368, 21);
            this.CheckBox_byDayOrHour.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDayOrHour.TabIndex = 14;
            this.CheckBox_byDayOrHour.Text = "حساب صلاحيات الايام فقط  بدون الوقت_بالايام للاشتراكات فقط";
            this.CheckBox_byDayOrHour.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_byDayOrHour.UseVisualStyleBackColor = true;
            this.CheckBox_byDayOrHour.CheckedChanged += new System.EventHandler(this.CheckBox_byDayOrHour_CheckedChanged);
            // 
            // CheckBox_Save_session
            // 
            this.CheckBox_Save_session.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_session.AutoSize = true;
            this.CheckBox_Save_session.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.BorderSize = 1;
            this.CheckBox_Save_session.Check = true;
            this.CheckBox_Save_session.Checked = true;
            this.CheckBox_Save_session.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_session.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_session.Customizable = false;
            this.CheckBox_Save_session.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_session.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_session.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_session.Location = new System.Drawing.Point(152, 88);
            this.CheckBox_Save_session.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_session.Name = "CheckBox_Save_session";
            this.CheckBox_Save_session.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_session.Size = new System.Drawing.Size(219, 21);
            this.CheckBox_Save_session.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_session.TabIndex = 13;
            this.CheckBox_Save_session.Text = "تسجيل وحفظ  جلسات للهوتسبوت ";
            this.CheckBox_Save_session.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_session.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_download
            // 
            this.CheckBox_Save_download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_download.AutoSize = true;
            this.CheckBox_Save_download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.BorderSize = 1;
            this.CheckBox_Save_download.Check = true;
            this.CheckBox_Save_download.Checked = true;
            this.CheckBox_Save_download.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_download.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_download.Customizable = false;
            this.CheckBox_Save_download.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_download.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_download.Location = new System.Drawing.Point(31, 56);
            this.CheckBox_Save_download.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_download.Name = "CheckBox_Save_download";
            this.CheckBox_Save_download.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_download.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_download.Size = new System.Drawing.Size(340, 21);
            this.CheckBox_Save_download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_download.TabIndex = 12;
            this.CheckBox_Save_download.Text = "حفظ بيانات الاستهلاك والتنزيل في حالة الانظفاء المفاجى";
            this.CheckBox_Save_download.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_download.UseVisualStyleBackColor = true;
            // 
            // CheckBox_Save_time
            // 
            this.CheckBox_Save_time.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_Save_time.AutoSize = true;
            this.CheckBox_Save_time.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.BorderSize = 1;
            this.CheckBox_Save_time.Check = true;
            this.CheckBox_Save_time.Checked = true;
            this.CheckBox_Save_time.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_Save_time.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_Save_time.Customizable = false;
            this.CheckBox_Save_time.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_Save_time.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_Save_time.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_Save_time.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_Save_time.Location = new System.Drawing.Point(98, 24);
            this.CheckBox_Save_time.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_Save_time.Name = "CheckBox_Save_time";
            this.CheckBox_Save_time.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_Save_time.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_Save_time.Size = new System.Drawing.Size(273, 21);
            this.CheckBox_Save_time.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_Save_time.TabIndex = 11;
            this.CheckBox_Save_time.Text = "حفظ الوقت في حال الانطفاء المفاجئ للروتر";
            this.CheckBox_Save_time.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_Save_time.UseVisualStyleBackColor = true;
            this.CheckBox_Save_time.CheckedChanged += new System.EventHandler(this.CheckBox_Save_time_CheckedChanged);
            // 
            // CheckBox_SmartScript
            // 
            this.CheckBox_SmartScript.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_SmartScript.AutoSize = true;
            this.CheckBox_SmartScript.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SmartScript.BorderSize = 1;
            this.CheckBox_SmartScript.Check = true;
            this.CheckBox_SmartScript.Checked = true;
            this.CheckBox_SmartScript.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_SmartScript.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SmartScript.Customizable = false;
            this.CheckBox_SmartScript.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.CheckBox_SmartScript.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SmartScript.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SmartScript.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_SmartScript.Location = new System.Drawing.Point(449, 185);
            this.CheckBox_SmartScript.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SmartScript.Name = "CheckBox_SmartScript";
            this.CheckBox_SmartScript.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_SmartScript.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_SmartScript.Size = new System.Drawing.Size(201, 21);
            this.CheckBox_SmartScript.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SmartScript.TabIndex = 10;
            this.CheckBox_SmartScript.Text = "استخدام سكربت الصلاحيات";
            this.CheckBox_SmartScript.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_SmartScript.UseVisualStyleBackColor = true;
            // 
            // rjLabel6
            // 
            this.rjLabel6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(370, 61);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(51, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 121;
            this.rjLabel6.Text = "الصلاحية";
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(568, 126);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(75, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 122;
            this.rjLabel1.Text = "كمية التحميل";
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(342, 126);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(53, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 123;
            this.rjLabel2.Text = "سعر البيع";
            // 
            // rjLabel4
            // 
            this.rjLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(220, 61);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(39, 17);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 124;
            this.rjLabel4.Text = "الوقت";
            // 
            // rjLabel5
            // 
            this.rjLabel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(576, 61);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(61, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 125;
            this.rjLabel5.Text = "اسم الباقة";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnSave.BorderRadius = 15;
            this.btnSave.BorderSize = 1;
            this.btnSave.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnSave.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnSave.FlatAppearance.BorderSize = 0;
            this.btnSave.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnSave.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnSave.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnSave.Font = new System.Drawing.Font("Droid Arabic Kufi", 14.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnSave.ForeColor = System.Drawing.Color.White;
            this.btnSave.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnSave.IconColor = System.Drawing.Color.White;
            this.btnSave.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnSave.IconSize = 24;
            this.btnSave.Location = new System.Drawing.Point(79, 304);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(143, 57);
            this.btnSave.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnSave.TabIndex = 17;
            this.btnSave.Text = "حفظ";
            this.btnSave.UseVisualStyleBackColor = false;
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // lblTitle
            // 
            this.lblTitle.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTitle.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblTitle.Font = new System.Drawing.Font("Cairo ExtraBold", 12F, System.Drawing.FontStyle.Bold);
            this.lblTitle.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lblTitle.LinkLabel = false;
            this.lblTitle.Location = new System.Drawing.Point(213, 14);
            this.lblTitle.Name = "lblTitle";
            this.lblTitle.Size = new System.Drawing.Size(267, 46);
            this.lblTitle.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lblTitle.TabIndex = 120;
            this.lblTitle.Text = "اضافة باقة جديد";
            this.lblTitle.TextAlign = System.Drawing.ContentAlignment.TopCenter;
            // 
            // txt_price_salse
            // 
            this.txt_price_salse._Customizable = false;
            this.txt_price_salse.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_price_salse.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_price_salse.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price_salse.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_price_salse.BorderRadius = 10;
            this.txt_price_salse.BorderSize = 1;
            this.txt_price_salse.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_price_salse.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price_salse.Location = new System.Drawing.Point(296, 151);
            this.txt_price_salse.MultiLine = false;
            this.txt_price_salse.Name = "txt_price_salse";
            this.txt_price_salse.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_price_salse.PasswordChar = false;
            this.txt_price_salse.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_price_salse.PlaceHolderText = null;
            this.txt_price_salse.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_price_salse.Size = new System.Drawing.Size(149, 27);
            this.txt_price_salse.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_price_salse.TabIndex = 7;
            this.txt_price_salse.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_price_salse.onTextChanged += new System.EventHandler(this.txt_price_salse_onTextChanged);
            // 
            // txt_uptime_Hour
            // 
            this.txt_uptime_Hour._Customizable = false;
            this.txt_uptime_Hour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_uptime_Hour.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_uptime_Hour.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptime_Hour.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_uptime_Hour.BorderRadius = 10;
            this.txt_uptime_Hour.BorderSize = 1;
            this.txt_uptime_Hour.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_uptime_Hour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_uptime_Hour.Location = new System.Drawing.Point(154, 86);
            this.txt_uptime_Hour.MultiLine = false;
            this.txt_uptime_Hour.Name = "txt_uptime_Hour";
            this.txt_uptime_Hour.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_uptime_Hour.PasswordChar = false;
            this.txt_uptime_Hour.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_uptime_Hour.PlaceHolderText = null;
            this.txt_uptime_Hour.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_uptime_Hour.Size = new System.Drawing.Size(124, 27);
            this.txt_uptime_Hour.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_uptime_Hour.TabIndex = 110;
            this.txt_uptime_Hour.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_profileName
            // 
            this.txt_profileName._Customizable = false;
            this.txt_profileName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_profileName.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_profileName.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_profileName.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_profileName.BorderRadius = 10;
            this.txt_profileName.BorderSize = 1;
            this.txt_profileName.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_profileName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_profileName.Location = new System.Drawing.Point(460, 86);
            this.txt_profileName.MultiLine = false;
            this.txt_profileName.Name = "txt_profileName";
            this.txt_profileName.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_profileName.PasswordChar = false;
            this.txt_profileName.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_profileName.PlaceHolderText = null;
            this.txt_profileName.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_profileName.Size = new System.Drawing.Size(200, 27);
            this.txt_profileName.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_profileName.TabIndex = 0;
            this.txt_profileName.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Download
            // 
            this.txt_Download._Customizable = false;
            this.txt_Download.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_Download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Download.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Download.BorderRadius = 10;
            this.txt_Download.BorderSize = 1;
            this.txt_Download.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_Download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Download.Location = new System.Drawing.Point(570, 151);
            this.txt_Download.MultiLine = false;
            this.txt_Download.Name = "txt_Download";
            this.txt_Download.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Download.PasswordChar = false;
            this.txt_Download.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Download.PlaceHolderText = null;
            this.txt_Download.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Download.Size = new System.Drawing.Size(90, 27);
            this.txt_Download.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_Download.TabIndex = 5;
            this.txt_Download.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Validity
            // 
            this.txt_Validity._Customizable = false;
            this.txt_Validity.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_Validity.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Validity.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Validity.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Validity.BorderRadius = 10;
            this.txt_Validity.BorderSize = 1;
            this.txt_Validity.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_Validity.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Validity.Location = new System.Drawing.Point(290, 86);
            this.txt_Validity.MultiLine = false;
            this.txt_Validity.Name = "txt_Validity";
            this.txt_Validity.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Validity.PasswordChar = false;
            this.txt_Validity.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Validity.PlaceHolderText = null;
            this.txt_Validity.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Validity.Size = new System.Drawing.Size(155, 27);
            this.txt_Validity.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_Validity.TabIndex = 2;
            this.txt_Validity.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // txt_price_display
            // 
            this.txt_price_display._Customizable = false;
            this.txt_price_display.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_price_display.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_price_display.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price_display.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_price_display.BorderRadius = 10;
            this.txt_price_display.BorderSize = 1;
            this.txt_price_display.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_price_display.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_price_display.Location = new System.Drawing.Point(154, 149);
            this.txt_price_display.MultiLine = false;
            this.txt_price_display.Name = "txt_price_display";
            this.txt_price_display.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_price_display.PasswordChar = false;
            this.txt_price_display.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_price_display.PlaceHolderText = null;
            this.txt_price_display.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_price_display.Size = new System.Drawing.Size(124, 27);
            this.txt_price_display.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_price_display.TabIndex = 8;
            this.txt_price_display.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel8
            // 
            this.rjLabel8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(157, 126);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(96, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 123;
            this.rjLabel8.Text = "السعر علي القالب";
            // 
            // txt_precent
            // 
            this.txt_precent._Customizable = false;
            this.txt_precent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_precent.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_precent.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_precent.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_precent.BorderRadius = 10;
            this.txt_precent.BorderSize = 1;
            this.txt_precent.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_precent.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_precent.Location = new System.Drawing.Point(16, 86);
            this.txt_precent.MultiLine = false;
            this.txt_precent.Name = "txt_precent";
            this.txt_precent.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_precent.PasswordChar = false;
            this.txt_precent.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_precent.PlaceHolderText = null;
            this.txt_precent.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_precent.Size = new System.Drawing.Size(129, 27);
            this.txt_precent.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_precent.TabIndex = 4;
            this.txt_precent.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjCheckBox_precent
            // 
            this.rjCheckBox_precent.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjCheckBox_precent.AutoSize = true;
            this.rjCheckBox_precent.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox_precent.BorderSize = 1;
            this.rjCheckBox_precent.Check = false;
            this.rjCheckBox_precent.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjCheckBox_precent.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjCheckBox_precent.Customizable = false;
            this.rjCheckBox_precent.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjCheckBox_precent.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjCheckBox_precent.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjCheckBox_precent.Location = new System.Drawing.Point(19, 62);
            this.rjCheckBox_precent.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjCheckBox_precent.Name = "rjCheckBox_precent";
            this.rjCheckBox_precent.Padding = new System.Windows.Forms.Padding(10, 0, 15, 0);
            this.rjCheckBox_precent.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjCheckBox_precent.Size = new System.Drawing.Size(116, 21);
            this.rjCheckBox_precent.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjCheckBox_precent.TabIndex = 133;
            this.rjCheckBox_precent.Text = "عمولة الباقة";
            this.rjCheckBox_precent.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjCheckBox_precent.UseVisualStyleBackColor = true;
            // 
            // CBOX_precent_type
            // 
            this.CBOX_precent_type.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBOX_precent_type.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBOX_precent_type.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBOX_precent_type.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBOX_precent_type.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBOX_precent_type.BorderRadius = 10;
            this.CBOX_precent_type.BorderSize = 1;
            this.CBOX_precent_type.Customizable = false;
            this.CBOX_precent_type.DataSource = null;
            this.CBOX_precent_type.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBOX_precent_type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBOX_precent_type.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBOX_precent_type.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBOX_precent_type.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBOX_precent_type.Items.AddRange(new object[] {
            "نسبة ",
            "قيمة ثابتة"});
            this.CBOX_precent_type.Location = new System.Drawing.Point(21, 145);
            this.CBOX_precent_type.Name = "CBOX_precent_type";
            this.CBOX_precent_type.Padding = new System.Windows.Forms.Padding(2);
            this.CBOX_precent_type.SelectedIndex = -1;
            this.CBOX_precent_type.Size = new System.Drawing.Size(127, 29);
            this.CBOX_precent_type.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBOX_precent_type.TabIndex = 9;
            this.CBOX_precent_type.Texts = "";
            // 
            // rjLabel26
            // 
            this.rjLabel26.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel26.AutoSize = true;
            this.rjLabel26.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel26.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel26.LinkLabel = false;
            this.rjLabel26.Location = new System.Drawing.Point(24, 126);
            this.rjLabel26.Name = "rjLabel26";
            this.rjLabel26.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel26.Size = new System.Drawing.Size(80, 17);
            this.rjLabel26.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel26.TabIndex = 136;
            this.rjLabel26.Text = "طريقة الحساب";
            // 
            // FormAdd_Edit_Profile_Hotspot
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "FormAdd_Edit_Profile_Hotspot";
            this.ClientSize = new System.Drawing.Size(696, 417);
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.DoubleBuffered = false;
            this.FormIcon = FontAwesome.Sharp.IconChar.CcDiscover;
            this.HelpButton = true;
            this.Name = "FormAdd_Edit_Profile_Hotspot";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "FormAdd_Edit_Profile_Hotspot";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.groupBox3.ResumeLayout(false);
            this.groupBox3.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJCheckBox rjCheckBox_group;
        private RJControls.RJLabel rjLabel12;
        private RJControls.RJLabel rjLabel13;
        private RJControls.RJComboBox CBox_SizeDownload;
        private System.Windows.Forms.GroupBox groupBox3;
        private RJControls.RJComboBox CBox_profile_hotspot;
        private System.Windows.Forms.GroupBox groupBox1;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJButton btnSave;
        private RJControls.RJLabel lblTitle;
        private RJControls.RJTextBox txt_price_salse;
        private RJControls.RJTextBox txt_uptime_Hour;
        private RJControls.RJTextBox txt_profileName;
        private RJControls.RJTextBox txt_Download;
        private RJControls.RJTextBox txt_Validity;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJCheckBox CheckBox_SmartScript;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJTextBox txt_price_display;
        private RJControls.RJCheckBox CheckBox_Save_download;
        private RJControls.RJCheckBox CheckBox_Save_time;
        private RJControls.RJCheckBox CheckBox_Save_session;
        private RJControls.RJCheckBox CheckBox_byDayOrHour;
        private RJControls.RJTextBox txt_precent;
        private RJControls.RJCheckBox rjCheckBox_precent;
        private RJControls.RJComboBox CBOX_precent_type;
        private RJControls.RJLabel rjLabel26;
    }
}