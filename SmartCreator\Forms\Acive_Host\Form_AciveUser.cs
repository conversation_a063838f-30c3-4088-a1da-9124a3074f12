﻿using Org.BouncyCastle.Utilities;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Windows.Interop;
using tik4net;

namespace SmartCreator.Forms.Acive_Host
{
    public partial class Form_AciveUser : RJChildForm
    {
        string Filter_Type = "Active";
        bool FirstLoad = true;
        List<Acive_Host_Users> ActiveUsers = null;
        List<Host_Users> HostUsers = null;

        public Form_AciveUser(string _filter = "Active")
        {
            InitializeComponent();
            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
                dgv.RightToLeft = RightToLeft.No;

            Filter_Type = _filter;
            if (_filter == "Active")
            {
                this.Text = "المستخدمين النشطين(Active)";
                dgv.Height = dgv.Height + pnl_HostBar.Height;
                pnl_HostBar.Visible = false;
            }
            else if (_filter == "Host")
            {
                this.Text = "جميع الموجود في الشبكة (Host)";
                pnl_HostBar.Visible = true;
                CBox_Server.Visible = false;
                lbl_Server.Visible = false;
                طـــردالعميلمنالاكتفToolStripMenuItem.Visible = false;
                طردالعميلمعمسحالكوكيزToolStripMenuItem.Visible=false;
                btnDelete.Visible = false;
                //txt_search.Location = new System.Drawing.Point(666, 10);
                txt_search.Location = new System.Drawing.Point(btnRefresh_DB.Location.X - txt_search.Width - 10, 10);
            }


            //Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 9 , FontStyle.Regular);
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            lbl_Server.Font =
            lbl_countSession.Font =
            rjLabel2.Font =
            rjLabel3.Font =
                CBox_Server.Font = fnt;

            btnDelete.Font = btnRefresh_DB.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);
            btnDelete.Font= btnRefresh_DB.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9.75f, FontStyle.Bold);

            

            txt_search.Font = new Font(txt_search.Font.FontFamily, txt_search.Font.Size );
            txt_count_Auth.Font = new Font(txt_count_Auth.Font.FontFamily, txt_count_Auth.Font.Size );
            txt_count_Bypass.Font = new Font(txt_count_Bypass.Font.FontFamily, txt_count_Bypass.Font.Size );
            txt_count_Not_Auth.Font = new Font(txt_count_Not_Auth.Font.FontFamily, txt_count_Not_Auth.Font.Size );
            CBox_Server.Font = new Font(CBox_Server.Font.FontFamily, CBox_Server.Font.Size );

            if (UIAppearance.Theme == UITheme.Light)
            {
                txt_count_Not_Auth._Customizable = txt_count_Bypass._Customizable = true;
                //lbl_Count.ForeColor = Color.Red;
                //lbl_Count.ForeColor =
                txt_count_Bypass.ForeColor= txt_count_Not_Auth.ForeColor= lbl_Count.ForeColor = Color.Red;
                txt_count_Not_Auth._Customizable = txt_count_Bypass._Customizable = true;
            }

            dgv.AllowUserToOrderColumns = true;
            dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;

            //dgv.DefaultCellStyle.Font=new Font(dgv.DefaultCellStyle.Font.FontFamily,dgv.DefaultCellStyle.Font.Size  , dgv.DefaultCellStyle.Font.Style);

            //Control_Loop(pnlClientArea);
            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dm_Session);
        }

        private void Control_Loop(System.Windows.Forms.Control ctl)
        {
            try
            {
                foreach (System.Windows.Forms.Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(System.Windows.Controls.Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }


        void dgv_format()
        {
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["Uptime"].Visible = false; } catch { }
            try { dgv.Columns["KeepaliveTimeout"].Visible = false; } catch { }
            try { dgv.Columns["LimitBytesTotal"].Visible = false; } catch { }
            try { dgv.Columns["SessionTimeLeft"].Visible = false; } catch { }
            try { dgv.Columns["Str_KeepaliveTimeout"].Visible = false; } catch { }

            //try { dgv.Columns["LimitBytesIn"].Visible = false; } catch { }
            try { dgv.Columns["IdleTime"].Visible = false; } catch { }
            //try { dgv.Columns["LimitBytesOut"].Visible = false; } catch { }
            try { dgv.Columns["Radius"].Visible = false; } catch { }
            try { dgv.Columns["BytesIn"].Visible = false; } catch { }
            try { dgv.Columns["BytesOut"].Visible = false; } catch { }
            //try { dgv.Columns["Str_Authorized"].Visible = false; } catch { }
            try { dgv.Columns["MacAddress"].Width = 150; } catch { }
            try { dgv.Columns["HostName"].Width = 150; } catch { }

            if (Filter_Type == "Host")
            {
                try { dgv.Columns["Str_Authorized"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["Str_Authorized"].Visible = true; } catch { }
                try { dgv.Columns["ProfileName"].Visible = false; } catch { }
                try { dgv.Columns["UserName"].Visible = false; } catch { }
                try { dgv.Columns["Str_SessionTimeLeft"].Visible = false; } catch { }
                try { dgv.Columns["Str_LimitBytesTotal"].Visible = false; } catch { }
                try { dgv.Columns["LoginBy"].Visible = false; } catch { }
                try { dgv.Columns["Authorized"].Visible = false; } catch { }
                try { dgv.Columns["Bypassed"].Visible = false; } catch { }
                try { dgv.Columns["ToAddress"].Visible = false; } catch { }

            }

        }
        private void LoadData()
        {
            try
            {
                Sql_DataAccess Local_DA = new Sql_DataAccess();
                List<UmUser> umUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");
                List<HSUser> hsUsers = Local_DA.Get_Not_Delet_fromServer<HSUser>("HSUser");

                if (Filter_Type == "Active")
                {
                    var active = (from t1 in Global_Variable.Acive_Users
                                  select new Acive_Host_Users
                                  {

                                      UserName = t1.UserName,
                                      ProfileName = t1.Radius ? (from r in umUsers where t1.UserName == r.UserName select r.ProfileName.ToString()).FirstOrDefault()
                                                              : (from r in hsUsers where t1.UserName == r.UserName select r.ProfileName.ToString()).FirstOrDefault(),
                                      Address = t1.Address,
                                      BridgePort = Global_Variable.Hosts_Users != null ? (from r in Global_Variable.Hosts_Users where t1.Address == r.Address select r.BridgePort.ToString()).FirstOrDefault() : "",
                                      BytesIn = t1.BytesIn,
                                      BytesOut = t1.BytesOut,
                                      Comment = t1.Comment,
                                      IdHX = t1.IdHX,
                                      IdleTime = t1.IdleTime,
                                      KeepaliveTimeout = t1.KeepaliveTimeout,
                                      LimitBytesTotal = t1.LimitBytesTotal,
                                      LoginBy = t1.LoginBy,
                                      MacAddress = t1.MacAddress,
                                      Radius = t1.Radius,
                                      Server = t1.Server,
                                      SessionTimeLeft = t1.SessionTimeLeft,
                                      Uptime = t1.Uptime,
                                      HostName = Global_Variable.Dhcp_Leases !=null ? (from r in Global_Variable.Dhcp_Leases where t1.Address == r.Address select r.HostName.ToString()).FirstOrDefault() : "",

                                  }).ToList();
                    dgv.DataSource = active;
                    ActiveUsers = active;
                    dgv_format();

                }

                else if (Filter_Type == "Host")
                {

                    var hosts = (from t1 in Global_Variable.Hosts_Users
                                 select new Host_Users
                                 {
                                     //UserName = t1.UserName,
                                     //ProfileName = t1.Radius ? (from r in umUsers where t1.UserName == r.UserName select r.ProfileName.ToString()).FirstOrDefault()
                                     //                        : (from r in hsUsers where t1.UserName == r.UserName select r.ProfileName.ToString()).FirstOrDefault(),

                                     //Status=
                                     //Str_Authorized = t1.Str_Authorized,
                                     Authorized = t1.Authorized,
                                     Bypassed = t1.Bypassed,
                                     Address = t1.Address,
                                     ToAddress = t1.ToAddress,
                                     BridgePort = t1.BridgePort,
                                     //BridgePort = (from r in Global_Variable.Hosts_Users where t1.Address == r.Address select r.BridgePort.ToString()).FirstOrDefault(),
                                     BytesIn = t1.BytesIn,
                                     BytesOut = t1.BytesOut,
                                     Comment = t1.Comment,
                                     IdHX = t1.IdHX,
                                     IdleTime = t1.IdleTime,
                                     KeepaliveTimeout = t1.KeepaliveTimeout,
                                     LimitBytesTotal = t1.LimitBytesTotal,
                                     LoginBy = t1.LoginBy,
                                     MacAddress = t1.MacAddress,
                                     Radius = t1.Radius,
                                     Server = t1.Server,
                                     SessionTimeLeft = t1.SessionTimeLeft,
                                     Uptime = t1.Uptime,
                                     HostName = Global_Variable.Dhcp_Leases !=null ? (from r in Global_Variable.Dhcp_Leases where t1.Address == r.Address select r.HostName.ToString()).FirstOrDefault() : "",


                                 }).ToList();
                    dgv.DataSource = hosts;
                    HostUsers = hosts;
                    dgv_format();

                    try
                    {
                        txt_count_Auth.Text = Global_Variable.Hosts_Users.Where(x => x.Authorized == true).Count() + "";
                        txt_count_Not_Auth.Text = Global_Variable.Hosts_Users.Where(x => x.Authorized == false && x.Bypassed == false).Count() + "";
                        txt_count_Bypass.Text = Global_Variable.Hosts_Users.Where(x => x.Bypassed == true).Count() + "";
                    }
                    catch { }
                }
            }
            catch { }
            lbl_Count.Text = "العدد " + dgv.Rows.Count.ToString();

        }

        [Obsolete]
        private void Form_AciveUser_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            Thread thread = new Thread(Refersh_All_Mk);
            thread.Start();
            btnRefresh_DB.Enabled = false;



            //if (Global_Variable.Acive_Users == null)
            //    btnRefresh_DB_Click(sender, e);
            //else
            //    LoadData();
            
            //FirstLoad = false;
        }

        [Obsolete]
        private void btnRefresh_DB_Click(object sender, EventArgs e)
        {
            Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب النشطين والهوست من الروتر", 0);


            //Task tsk = new Task(Refersh_Mk);
            //tsk.Start();
            //tsk.Wait();

            dgv.DataSource = null;
            txt_count_Auth.Text = "";
            txt_count_Bypass.Text = "";
            txt_count_Not_Auth.Text = "";

            Thread thread = new Thread(Refersh_Mk);
            thread.Start();
            btnRefresh_DB.Enabled = false;

            //Refersh_Mk();
            //LoadData();

            //dgv.DataSource = ActiveHost;
            //dgv_format();
        }

        private void Dhcp_Refresh()
        {
            Dhcp_Lease dhcp_Lease = new Dhcp_Lease();
            List<Dhcp_Lease> lease = dhcp_Lease.Get_Dhcp_Lease();
            Global_Variable.Dhcp_Leases = lease;

        }
        [Obsolete]
        private void Refersh_Mk()
        {
            isProcessRun = true;

            if (Filter_Type == "Host")
            {
                Host_Users host_Users = new Host_Users();
                //List<Host_Users> hostUser = host_Users.Get_Host_Users();
                Global_Variable.Hosts_Users = host_Users.Get_Host_Users();
            }

            //Dhcp_Refresh();

            if (Filter_Type == "Active")
            {
                Acive_Host_Users active = new Acive_Host_Users();
                //ActiveUsers = new List<Acive_Host_Users>();
                //ActiveUsers = active.Get_Acive_User();
                Global_Variable.Acive_Users = active.Get_Acive_User();
            }

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                     (System.Windows.Forms.MethodInvoker)delegate ()
                     {
                         LoadData();
                         isProcessRun = false;
                         CBox_Server.SelectedIndex = 0;
                         btnRefresh_DB.Enabled = true;
                     });

            isProcessRun = false;
            Global_Variable.Update_Um_StatusBar_Prograss(" تم عرض النشطين والهوست من الروتر", 0);

        }

        //[Obsolete]
        private void Refersh_All_Mk()
        {

            isProcessRun = true;
            try
            {
                if (Filter_Type == "Host")
                {
                    Host_Users host_Users = new Host_Users();
                    List<Host_Users> hostUser = host_Users.Get_Host_Users();
                    Global_Variable.Hosts_Users = hostUser;
                }


                Dhcp_Refresh();


                if (Filter_Type == "Active")
                {
                    Acive_Host_Users active = new Acive_Host_Users();
                    ActiveUsers = new List<Acive_Host_Users>();
                    ActiveUsers = active.Get_Acive_User();
                    Global_Variable.Acive_Users = ActiveUsers;
                }

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                         (System.Windows.Forms.MethodInvoker)delegate ()
                         {
                             isProcessRun = false;
                             CBox_Server.SelectedIndex = 0;
                             btnRefresh_DB.Enabled = true;

                             LoadData();
                             
                         });
                Global_Variable.Update_Um_StatusBar_Prograss(" تم عرض النشطين والهوست من الروتر", 0);
            }
            catch { }

            isProcessRun = false;
            FirstLoad = false;

        }


        DataGridViewCell ActiveCell = null;
        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void نسخToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.Rows.Count <= 0)
                return;

            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.Rows.Count <= 0)
                return;
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            if (string.IsNullOrEmpty(txt_search.Text))
            {
                if (Filter_Type == "Active")
                    dgv.DataSource = ActiveUsers;
                if (Filter_Type == "Host")
                    dgv.DataSource = Global_Variable.Hosts_Users;
            }
            else
            {
                if (Filter_Type == "Active")
                {
                    List<Acive_Host_Users> Active = ActiveUsers.Where(x =>
                    x.UserName.Contains(txt_search.Text)
                || x.MacAddress.Contains(txt_search.Text)
                || x.Address.Contains(txt_search.Text)
                || x.Comment.Contains(txt_search.Text)
                ).ToList();

                    dgv.DataSource = Active;
                }
                else if (Filter_Type == "Host")
                {
                    List<Host_Users> Active = HostUsers.Where(x =>
                  //x.UserName.Contains(txt_search.Text)
                  x.MacAddress.Contains(txt_search.Text)
               || x.Address.Contains(txt_search.Text)
               || x.Comment.Contains(txt_search.Text)
               ).ToList();

                    dgv.DataSource = Active;
                }

            }
            dgv_format();
            lbl_Count.Text = "العدد " + dgv.Rows.Count.ToString();
            try
            {
                txt_count_Auth.Text = Global_Variable.Hosts_Users.Where(x => x.Authorized == true).Count() + "";
                txt_count_Not_Auth.Text = Global_Variable.Hosts_Users.Where(x => x.Authorized == false && x.Bypassed == false).Count() + "";
                txt_count_Bypass.Text = Global_Variable.Hosts_Users.Where(x => x.Bypassed == true).Count() + "";
            }
            catch { }
        }

        private void CBox_Server_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            List<Acive_Host_Users> Active = null;

            if (CBox_Server.SelectedIndex == 0)
                Active = ActiveUsers;
            else if (CBox_Server.SelectedIndex == 1)
                Active = ActiveUsers.Where(x => x.Radius == true).ToList();
            else if (CBox_Server.SelectedIndex == 2)
                Active = ActiveUsers.Where(x => x.Radius == false).ToList();

            dgv.DataSource = Active;
            dgv_format();

            lbl_Count.Text = "العدد " + dgv.Rows.Count.ToString();

           
        }

        public bool isProcessRun = false;
        [Obsolete]
        public void RemoveAcive_ByID()
        {
            isProcessRun = true;
            string msg = "";
            HashSet<string> list_user = new HashSet<string>();
            try
            {

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                    {
                        RJMessageBox.Show("خطاء في الاتصال");
                        return;
                    }
                    string codeRemove = "/ip/hotspot/active/remove";
                    if (Filter_Type == "Host")
                    {
                        codeRemove = "/ip/hotspot/host/remove";
                    }
                    foreach (DataGridViewRow row in dgv.SelectedRows)
                    {
                        try
                        {
                            string id = (row.Cells["IdHX"].Value.ToString());
                            var deleteCmd = connection.CreateCommandAndParameters(codeRemove, TikSpecialProperties.Id, id);
                            deleteCmd.ExecuteNonQuery();
                        }
                        catch { }
                        
                    }
                    isProcessRun = false;
                    Refersh_Mk();
                }

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; }

        }

        [Obsolete]
        private void طـــردالعميلمنالاكتفToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (isProcessRun)
            {
              RJMessageBox.Show("هناك عملية قيد التنفيذ");
                return;
            }

            DialogResult result = RJMessageBox.Show("  هل انت متأكد من تسجيل خروج العميل النشط  ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;    
            }


            Thread thread = new Thread(RemoveAcive_ByID);
            thread.Start();


            //RemoveAcive_ByID();

        }

        [Obsolete]
        private void طردالعميلمعمسحالكوكيزToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //RemoveAcive_ByID();
            if (Filter_Type == "Host")
                return;
            try
            {

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                    {
                        RJMessageBox.Show("خطاء في الاتصال");
                        return;
                    }

                    string strUser = "";
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        strUser += "\"" + dr.Cells["UserName"].Value.ToString() + "\"" + ",";
                    }
                    BachScriptUserCutActive(strUser, true);

                    /////ip/hotspot/cookie/remove [find user=ffff]
                    //string codeRemove = "/ip/hotspot/cookie/remove";
                     
                    //foreach (DataGridViewRow row in dgv.SelectedRows)
                    //{
                    //    try
                    //    {
                    //        string name = (row.Cells["UserNAme"].Value.ToString());
                    //        var deleteCmd = connection.CreateCommandAndParameters(codeRemove, "?user", name);
                    //        deleteCmd.ExecuteNonQuery();
                    //    }
                    //    catch { }
                    //}
                    isProcessRun = false;
                }

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; }

        }

        [Obsolete]
        public void BachScriptUserCutActive(string strUser, bool WithCookie)
        {
            strUser = strUser.TrimEnd(',');
            string script = "";
            if (WithCookie)
            {
                script = "{" + Environment.NewLine +
                          ":local MyUser [:toarray (" + strUser + ")];" + Environment.NewLine +

                           ":for i from=0 to=([:len $MyUser]-1) do={" + Environment.NewLine +
                            "/ip hotspot active remove [find user=[:pick $MyUser $i]];" + Environment.NewLine +
                            "}" + Environment.NewLine +

                             ":for i from=0 to=([:len $MyUser]-1) do={" + Environment.NewLine +
                            "/ip hotspot cookie remove [find user=[:pick $MyUser $i]];" + Environment.NewLine +
                            "}" + Environment.NewLine +

                            //"/system script remove [find name=SmartScript2]; " + Environment.NewLine +
                           "}";
            }
            else
            {
                script = "{" + Environment.NewLine +
                          ":local MyUser [:toarray (" + strUser + ")];" + Environment.NewLine +

                           ":for i from=0 to=([:len $MyUser]-1) do={" + Environment.NewLine +
                            "/ip hotspot active remove [find user=[:pick $MyUser $i]];" + Environment.NewLine +
                            "}" + Environment.NewLine +


                            "/system script remove [find name=SmartScript2]; " + Environment.NewLine +
                           "}";
            }

            Mk_DataAccess.add_Script_Smart_AndRun(script,false);

            //DataAccess.CLS_DataAccess DA = new DataAccess.CLS_DataAccess();
            //string id = DA.AddUserScript(script);
        }


        private void تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }
        void MakePinding()
        {

        }

        [Obsolete]
        private void btnDelete_Click(object sender, EventArgs e)
        {
            طـــردالعميلمنالاكتفToolStripMenuItem_Click(sender,e);
        }

        private void txt_count_Not_Auth_onTextChanged(object sender, EventArgs e)
        {
            //txt_count_Not_Auth.Text = txt_count_Not_Auth.Text;
            
        }
    }
}
