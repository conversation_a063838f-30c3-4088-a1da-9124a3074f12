﻿namespace SmartCreator.Forms.Acive_Host
{
    partial class Form_AciveUser
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPane_Top = new SmartCreator.RJControls.RJPanel();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.lbl_Count = new SmartCreator.RJControls.RJLabel();
            this.lbl_Server = new SmartCreator.RJControls.RJLabel();
            this.btnDisable = new SmartCreator.RJControls.RJButton();
            this.btnEnable = new SmartCreator.RJControls.RJButton();
            this.CBox_Server = new SmartCreator.RJControls.RJComboBox();
            this.btnDelete = new SmartCreator.RJControls.RJButton();
            this.btnRefresh_DB = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dm_Session = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.نسخToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخالسطركاملToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.طـــردالعميلمنالاكتفToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnl_HostBar = new SmartCreator.RJControls.RJPanel();
            this.txt_count_Bypass = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.txt_count_Not_Auth = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.txt_count_Auth = new SmartCreator.RJControls.RJTextBox();
            this.lbl_countSession = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.rjPane_Top.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.dm_Session.SuspendLayout();
            this.pnl_HostBar.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnl_HostBar);
            this.pnlClientArea.Controls.Add(this.rjPane_Top);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 579);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(114, 22);
            this.lblCaption.Text = "Form_AciveUser";
            // 
            // rjPane_Top
            // 
            this.rjPane_Top.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPane_Top.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPane_Top.BorderRadius = 13;
            this.rjPane_Top.Controls.Add(this.btn_search);
            this.rjPane_Top.Controls.Add(this.lbl_Count);
            this.rjPane_Top.Controls.Add(this.lbl_Server);
            this.rjPane_Top.Controls.Add(this.btnDisable);
            this.rjPane_Top.Controls.Add(this.btnEnable);
            this.rjPane_Top.Controls.Add(this.CBox_Server);
            this.rjPane_Top.Controls.Add(this.btnDelete);
            this.rjPane_Top.Controls.Add(this.btnRefresh_DB);
            this.rjPane_Top.Controls.Add(this.txt_search);
            this.rjPane_Top.Customizable = false;
            this.rjPane_Top.Location = new System.Drawing.Point(13, 5);
            this.rjPane_Top.Name = "rjPane_Top";
            this.rjPane_Top.Size = new System.Drawing.Size(962, 45);
            this.rjPane_Top.TabIndex = 99;
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 2;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(307, 7);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 34);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 30;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Visible = false;
            // 
            // lbl_Count
            // 
            this.lbl_Count.AutoSize = true;
            this.lbl_Count.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Count.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Bold);
            this.lbl_Count.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Count.LinkLabel = false;
            this.lbl_Count.Location = new System.Drawing.Point(422, 14);
            this.lbl_Count.Name = "lbl_Count";
            this.lbl_Count.Size = new System.Drawing.Size(14, 15);
            this.lbl_Count.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Count.TabIndex = 93;
            this.lbl_Count.Text = "0";
            // 
            // lbl_Server
            // 
            this.lbl_Server.AutoSize = true;
            this.lbl_Server.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Server.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lbl_Server.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Server.LinkLabel = false;
            this.lbl_Server.Location = new System.Drawing.Point(199, 13);
            this.lbl_Server.Name = "lbl_Server";
            this.lbl_Server.Size = new System.Drawing.Size(43, 15);
            this.lbl_Server.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Server.TabIndex = 93;
            this.lbl_Server.Text = "السيرفر";
            // 
            // btnDisable
            // 
            this.btnDisable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDisable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderRadius = 5;
            this.btnDisable.BorderSize = 1;
            this.btnDisable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnDisable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnDisable.FlatAppearance.BorderSize = 0;
            this.btnDisable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDisable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDisable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDisable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDisable.ForeColor = System.Drawing.Color.White;
            this.btnDisable.IconChar = FontAwesome.Sharp.IconChar.Remove;
            this.btnDisable.IconColor = System.Drawing.Color.White;
            this.btnDisable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDisable.IconSize = 18;
            this.btnDisable.Location = new System.Drawing.Point(279, 7);
            this.btnDisable.Name = "btnDisable";
            this.btnDisable.Size = new System.Drawing.Size(29, 34);
            this.btnDisable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDisable.TabIndex = 52;
            this.btnDisable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDisable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnDisable.UseVisualStyleBackColor = false;
            this.btnDisable.Visible = false;
            // 
            // btnEnable
            // 
            this.btnEnable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEnable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderRadius = 5;
            this.btnEnable.BorderSize = 1;
            this.btnEnable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnEnable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnEnable.FlatAppearance.BorderSize = 0;
            this.btnEnable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEnable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEnable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEnable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnEnable.ForeColor = System.Drawing.Color.White;
            this.btnEnable.IconChar = FontAwesome.Sharp.IconChar.Check;
            this.btnEnable.IconColor = System.Drawing.Color.White;
            this.btnEnable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEnable.IconSize = 18;
            this.btnEnable.Location = new System.Drawing.Point(251, 7);
            this.btnEnable.Name = "btnEnable";
            this.btnEnable.Size = new System.Drawing.Size(29, 34);
            this.btnEnable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEnable.TabIndex = 57;
            this.btnEnable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnEnable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnEnable.UseVisualStyleBackColor = false;
            this.btnEnable.Visible = false;
            // 
            // CBox_Server
            // 
            this.CBox_Server.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Server.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Server.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Server.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server.BorderRadius = 5;
            this.CBox_Server.BorderSize = 1;
            this.CBox_Server.Customizable = false;
            this.CBox_Server.DataSource = null;
            this.CBox_Server.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Server.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Server.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.CBox_Server.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Server.Items.AddRange(new object[] {
            "الكل",
            "يوزمنجر",
            "هوتسبوت"});
            this.CBox_Server.Location = new System.Drawing.Point(3, 7);
            this.CBox_Server.Name = "CBox_Server";
            this.CBox_Server.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Server.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_Server.SelectedIndex = -1;
            this.CBox_Server.Size = new System.Drawing.Size(188, 30);
            this.CBox_Server.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Server.TabIndex = 92;
            this.CBox_Server.Texts = "";
            this.CBox_Server.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Server_OnSelectedIndexChanged);
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderRadius = 5;
            this.btnDelete.BorderSize = 1;
            this.btnDelete.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnDelete.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btnDelete.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.IconChar = FontAwesome.Sharp.IconChar.SignOut;
            this.btnDelete.IconColor = System.Drawing.Color.White;
            this.btnDelete.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDelete.IconSize = 20;
            this.btnDelete.Location = new System.Drawing.Point(735, 5);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnDelete.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnDelete.Size = new System.Drawing.Size(129, 34);
            this.btnDelete.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDelete.TabIndex = 86;
            this.btnDelete.Text = "طرد العميل";
            this.btnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnDelete.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // btnRefresh_DB
            // 
            this.btnRefresh_DB.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh_DB.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh_DB.BorderRadius = 5;
            this.btnRefresh_DB.BorderSize = 1;
            this.btnRefresh_DB.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnRefresh_DB.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.FlatAppearance.BorderSize = 0;
            this.btnRefresh_DB.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnRefresh_DB.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnRefresh_DB.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh_DB.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold);
            this.btnRefresh_DB.ForeColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconChar = FontAwesome.Sharp.IconChar.Recycle;
            this.btnRefresh_DB.IconColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh_DB.IconSize = 18;
            this.btnRefresh_DB.Location = new System.Drawing.Point(866, 7);
            this.btnRefresh_DB.Name = "btnRefresh_DB";
            this.btnRefresh_DB.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnRefresh_DB.Size = new System.Drawing.Size(87, 34);
            this.btnRefresh_DB.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh_DB.TabIndex = 50;
            this.btnRefresh_DB.Text = "تحديث";
            this.btnRefresh_DB.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh_DB.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnRefresh_DB.UseVisualStyleBackColor = false;
            this.btnRefresh_DB.Click += new System.EventHandler(this.btnRefresh_DB_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(541, 9);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث - اسم - ماك - ايبي";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(189, 26);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 29;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv.ContextMenuStrip = this.dm_Session;
            this.dgv.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(13, 57);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 35;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 35;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 35;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(962, 431);
            this.dgv.TabIndex = 98;
            this.dgv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv_MouseDown);
            // 
            // dm_Session
            // 
            this.dm_Session.ActiveMenuItem = false;
            this.dm_Session.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dm_Session.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dm_Session.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.نسخToolStripMenuItem,
            this.نسخالسطركاملToolStripMenuItem,
            this.طـــردالعميلمنالاكتفToolStripMenuItem,
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem,
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem,
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem});
            this.dm_Session.Name = "dm_Session";
            this.dm_Session.OwnerIsMenuButton = false;
            this.dm_Session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dm_Session.Size = new System.Drawing.Size(306, 136);
            // 
            // نسخToolStripMenuItem
            // 
            this.نسخToolStripMenuItem.Name = "نسخToolStripMenuItem";
            this.نسخToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.نسخToolStripMenuItem.Text = "نسخ الخلية المحدده";
            this.نسخToolStripMenuItem.Click += new System.EventHandler(this.نسخToolStripMenuItem_Click);
            // 
            // نسخالسطركاملToolStripMenuItem
            // 
            this.نسخالسطركاملToolStripMenuItem.Name = "نسخالسطركاملToolStripMenuItem";
            this.نسخالسطركاملToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.نسخالسطركاملToolStripMenuItem.Text = "نسخ الســـــطر كــــــامل";
            this.نسخالسطركاملToolStripMenuItem.Click += new System.EventHandler(this.نسخالسطركاملToolStripMenuItem_Click);
            // 
            // طـــردالعميلمنالاكتفToolStripMenuItem
            // 
            this.طـــردالعميلمنالاكتفToolStripMenuItem.Name = "طـــردالعميلمنالاكتفToolStripMenuItem";
            this.طـــردالعميلمنالاكتفToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.طـــردالعميلمنالاكتفToolStripMenuItem.Text = "طـــرد العميل من الاكتف";
            this.طـــردالعميلمنالاكتفToolStripMenuItem.Click += new System.EventHandler(this.طـــردالعميلمنالاكتفToolStripMenuItem_Click);
            // 
            // طردالعميلمعمسحالكوكيزToolStripMenuItem
            // 
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem.Name = "طردالعميلمعمسحالكوكيزToolStripMenuItem";
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem.Text = "طرد العميل مع مسح الكوكيز";
            this.طردالعميلمعمسحالكوكيزToolStripMenuItem.Click += new System.EventHandler(this.طردالعميلمعمسحالكوكيزToolStripMenuItem_Click);
            // 
            // تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem
            // 
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem.Name = "تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem";
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem.Text = "حظــر العميل او ســــماح مجــان";
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem.Click += new System.EventHandler(this.تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem_Click);
            // 
            // تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem
            // 
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem.Name = "تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem";
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem.Size = new System.Drawing.Size(305, 22);
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem.Text = "السماح للمستخدم بالدخول مجان عبر تثبت الايبي";
            this.تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem.Visible = false;
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // pnl_HostBar
            // 
            this.pnl_HostBar.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_HostBar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_HostBar.BorderRadius = 13;
            this.pnl_HostBar.Controls.Add(this.txt_count_Bypass);
            this.pnl_HostBar.Controls.Add(this.rjLabel3);
            this.pnl_HostBar.Controls.Add(this.txt_count_Not_Auth);
            this.pnl_HostBar.Controls.Add(this.rjLabel2);
            this.pnl_HostBar.Controls.Add(this.txt_count_Auth);
            this.pnl_HostBar.Controls.Add(this.lbl_countSession);
            this.pnl_HostBar.Customizable = false;
            this.pnl_HostBar.Location = new System.Drawing.Point(13, 494);
            this.pnl_HostBar.Name = "pnl_HostBar";
            this.pnl_HostBar.Size = new System.Drawing.Size(962, 71);
            this.pnl_HostBar.TabIndex = 100;
            this.pnl_HostBar.Visible = false;
            // 
            // txt_count_Bypass
            // 
            this.txt_count_Bypass._Customizable = false;
            this.txt_count_Bypass.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_count_Bypass.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Bypass.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Bypass.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Bypass.BorderRadius = 3;
            this.txt_count_Bypass.BorderSize = 1;
            this.txt_count_Bypass.Enabled = false;
            this.txt_count_Bypass.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txt_count_Bypass.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Bypass.Location = new System.Drawing.Point(11, 20);
            this.txt_count_Bypass.MultiLine = false;
            this.txt_count_Bypass.Name = "txt_count_Bypass";
            this.txt_count_Bypass.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Bypass.PasswordChar = false;
            this.txt_count_Bypass.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Bypass.PlaceHolderText = null;
            this.txt_count_Bypass.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Bypass.Size = new System.Drawing.Size(124, 26);
            this.txt_count_Bypass.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_count_Bypass.TabIndex = 91;
            this.txt_count_Bypass.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel3
            // 
            this.rjLabel3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Enabled = false;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(138, 28);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(119, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 92;
            this.rjLabel3.Text = "عدد الاجهزه (Bypass)";
            // 
            // txt_count_Not_Auth
            // 
            this.txt_count_Not_Auth._Customizable = false;
            this.txt_count_Not_Auth.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_count_Not_Auth.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Not_Auth.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Not_Auth.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Not_Auth.BorderRadius = 3;
            this.txt_count_Not_Auth.BorderSize = 1;
            this.txt_count_Not_Auth.Enabled = false;
            this.txt_count_Not_Auth.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txt_count_Not_Auth.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Not_Auth.Location = new System.Drawing.Point(280, 20);
            this.txt_count_Not_Auth.MultiLine = false;
            this.txt_count_Not_Auth.Name = "txt_count_Not_Auth";
            this.txt_count_Not_Auth.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Not_Auth.PasswordChar = false;
            this.txt_count_Not_Auth.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Not_Auth.PlaceHolderText = null;
            this.txt_count_Not_Auth.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Not_Auth.Size = new System.Drawing.Size(124, 26);
            this.txt_count_Not_Auth.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_count_Not_Auth.TabIndex = 91;
            this.txt_count_Not_Auth.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_count_Not_Auth.onTextChanged += new System.EventHandler(this.txt_count_Not_Auth_onTextChanged);
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Enabled = false;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(414, 26);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(194, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 92;
            this.rjLabel2.Text = "عدد المستخدمين الغير مسجلين بكروت";
            // 
            // txt_count_Auth
            // 
            this.txt_count_Auth._Customizable = false;
            this.txt_count_Auth.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_count_Auth.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_count_Auth.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_count_Auth.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_count_Auth.BorderRadius = 3;
            this.txt_count_Auth.BorderSize = 1;
            this.txt_count_Auth.Enabled = false;
            this.txt_count_Auth.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.txt_count_Auth.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_count_Auth.Location = new System.Drawing.Point(632, 20);
            this.txt_count_Auth.MultiLine = false;
            this.txt_count_Auth.Name = "txt_count_Auth";
            this.txt_count_Auth.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_count_Auth.PasswordChar = false;
            this.txt_count_Auth.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_count_Auth.PlaceHolderText = null;
            this.txt_count_Auth.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_count_Auth.Size = new System.Drawing.Size(124, 26);
            this.txt_count_Auth.Style = SmartCreator.RJControls.TextBoxStyle.FlaringLine;
            this.txt_count_Auth.TabIndex = 91;
            this.txt_count_Auth.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_countSession
            // 
            this.lbl_countSession.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_countSession.AutoSize = true;
            this.lbl_countSession.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_countSession.Enabled = false;
            this.lbl_countSession.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_countSession.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_countSession.LinkLabel = false;
            this.lbl_countSession.Location = new System.Drawing.Point(758, 26);
            this.lbl_countSession.Name = "lbl_countSession";
            this.lbl_countSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_countSession.Size = new System.Drawing.Size(177, 17);
            this.lbl_countSession.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_countSession.TabIndex = 92;
            this.lbl_countSession.Text = "عدد المستخدمين المسجلين بكروت";
            // 
            // Form_AciveUser
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.BorderSize = 5;
            this.Caption = "Form_AciveUser";
            this.ClientSize = new System.Drawing.Size(1000, 629);
            this.Name = "Form_AciveUser";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_AciveUser";
            this.Load += new System.EventHandler(this.Form_AciveUser_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPane_Top.ResumeLayout(false);
            this.rjPane_Top.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.dm_Session.ResumeLayout(false);
            this.pnl_HostBar.ResumeLayout(false);
            this.pnl_HostBar.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPane_Top;
        private RJControls.RJButton btn_search;
        private RJControls.RJComboBox CBox_Server;
        private RJControls.RJButton btnDelete;
        private RJControls.RJButton btnRefresh_DB;
        private RJControls.RJButton btnDisable;
        private RJControls.RJButton btnEnable;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJDataGridView dgv;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJDropdownMenu dm_Session;
        private System.Windows.Forms.ToolStripMenuItem نسخToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخالسطركاملToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem طـــردالعميلمنالاكتفToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem طردالعميلمعمسحالكوكيزToolStripMenuItem;
        private RJControls.RJLabel lbl_Server;
        private System.Windows.Forms.ToolStripMenuItem تثبيتالمستخدمبدخولمجانعبرتثبتالماكToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تثبيتالمستخدمبدخولمجانعبرتثبتالايبيToolStripMenuItem;
        private RJControls.RJLabel lbl_Count;
        private RJControls.RJPanel pnl_HostBar;
        private RJControls.RJTextBox txt_count_Not_Auth;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJTextBox txt_count_Auth;
        private RJControls.RJLabel lbl_countSession;
        private RJControls.RJTextBox txt_count_Bypass;
        private RJControls.RJLabel rjLabel3;
    }
}