﻿using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class Form_AciveInfo : RJForms.RJChildForm
    {
        public Form_AciveInfo()
        {
            InitializeComponent();

            if (Properties.Settings.Default.isActive == false)
            {
                try
                {
                    this.Hide();
                    Form_UsersInfo form_UsersInfo = new Form_UsersInfo();
                    form_UsersInfo.Show();
                    //this.Close();
                    return;
                }
                catch { }
            }

            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in rjPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }


        }

        private void Form_AciveInfo_Load(object sender, EventArgs e)
        {
            timer1.Start();





        }

        string get_startDate()
        {
            string start = "";
            try
            {
                //DateTime.Now.ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss");
                start = (Convert.ToDateTime(Properties.Settings.Default.RigesterStartDate)).ToString("yyyy-MM-dd");
            }
            catch (Exception ex) { }
            return start;
        }
        string get_endtDate()
        {
            string start = "";
            try
            {
                int pa = Properties.Settings.Default.active_Period;
                //MessageBox.Show(pa.ToString());
                //DateTime.Now.ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss");
                start = (Convert.ToDateTime(Properties.Settings.Default.RigesterStartDate)).AddDays(pa).ToString("yyyy-MM-dd");
            }
            catch (Exception ex) { }
            return start;
        }

        string get_endtDateByDays()
        {
            string start = "";
            try
            {
                int pa = Properties.Settings.Default.active_Period;
                //DateTime.Now.ToString("yyyy'-'MM'-'dd'T'HH':'mm':'ss");
                DateTime s = (Convert.ToDateTime(Properties.Settings.Default.RigesterStartDate));
                DateTime end = (Convert.ToDateTime(Properties.Settings.Default.RigesterStartDate)).AddDays(pa);
                start = (end - s).TotalDays.ToString();
            }
            catch (Exception ex) { }
            return start;
        }

        private void lbl_identifyRequest_onTextChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
                lbl_identifyRequest.Text = Global_Variable.Response_api.Identity.ToString();
        }

        private void lbl_activeType_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (Properties.Settings.Default.activationType == "desktop")
                lbl_activeType.Text = "مرتبط بالكمبيوتر";
            else
                lbl_activeType.Text = "مرتبط بالروتر";

        }

        private void lbl_RigesterName_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            lbl_RigesterName.Text = Properties.Settings.Default.RigesterName;
        }
        bool firstLoad = true;
        private void txt_licenceKey_onTextChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
                txt_licenceKey.Text = Properties.Settings.Default.snactrue;
        }

        private void lbl_RigesterStartDate_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            lbl_RigesterStartDate.Text = get_startDate();
        }

        private void lbl_RigesterEndDate_onTextChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            lbl_RigesterEndDate.Text = get_endtDate();
        }

        private void btn_renew_Click(object sender, EventArgs e)
        {
            Form_UsersInfo form_UsersInfo = new Form_UsersInfo();
            form_UsersInfo.ShowDialog();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            if (Properties.Settings.Default.activationType == "desktop")
                lbl_activeType.Text = "مرتبط بالكمبيوتر";
            else
                lbl_activeType.Text = "مرتبط بالروتر";

            if (Properties.Settings.Default.rb_active_exp <= 40)
            {
            }


            lbl_RigesterName.Text = Properties.Settings.Default.RigesterName;
            if(Properties.Settings.Default.NetworkName != "YEMEN")
                lbl_NetworkName.Text = Properties.Settings.Default.NetworkName;

            lbl_RigesterStartDate.Text = get_startDate();
            lbl_RigesterEndDate.Text = get_endtDate();
            lbl_RigesterStartBy_Day.Text = get_endtDateByDays();
            try { txt_licenceKey.Text = Properties.Settings.Default.snactrue; } catch { }
            lbl_identifyRequest.Text = Global_Variable.Response_api.Identity.ToString();
            try
            {
                double ex = -1;
                ex = Properties.Settings.Default.rb_active_exp;
                if (ex <= 40)
                {
                    lbl_RigesterStartBy_Day.Enabled = true;
                    btn_renew.Enabled = true;
                    lbl_RigesterStartBy_Day.Text = " (" + ex + ") يوم متبقي";
                }
            }
            catch { }
            lbl_identifyRequest.ForeColor = Color.Red;
            lbl_NetworkName.ForeColor = Color.Blue;
            lbl_RigesterStartDate.ForeColor = Color.Blue;
            lbl_RigesterEndDate.ForeColor = Color.Blue;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                lbl_identifyRequest.ForeColor = utils.Dgv_DarkColor;
                lbl_NetworkName.ForeColor = utils.Dgv_DarkColor;
                lbl_RigesterStartDate.ForeColor = utils.Dgv_DarkColor;
                lbl_RigesterEndDate.ForeColor = utils.Dgv_DarkColor;

            }
            firstLoad = false;
        }
    }
}
