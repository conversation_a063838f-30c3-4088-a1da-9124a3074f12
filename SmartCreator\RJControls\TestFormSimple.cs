using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار بسيط بدون RJChildForm
    /// </summary>
    public partial class TestFormSimple : Form
    {
        public TestFormSimple()
        {
            InitializeComponent();
            SetupTestContent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // TestFormSimple
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1000, 700);
            this.Name = "TestFormSimple";
            this.Text = "🧪 اختبار RJTabControl - نموذج بسيط";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
            this.MinimumSize = new Size(800, 600);
            
            this.ResumeLayout(false);
        }

        private void SetupTestContent()
        {
            // إنشاء TabControl للاختبار
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 45,
                TabSpacing = 5,
                TabPadding = 25,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 8
            };

            // تاب الترحيب
            var welcomeTab = tabControl.AddTab("مرحباً", IconChar.Home);
            welcomeTab.BackColor = Color.FromArgb(0, 122, 204);
            welcomeTab.ForeColor = Color.White;
            welcomeTab.IconSize = 22;

            var welcomeLabel = new Label
            {
                Text = "🎉 مرحباً بك في RJTabControl!\n\n" +
                       "✅ تم حل مشكلة RJChildForm\n" +
                       "✅ النموذج يعمل بدون أخطاء Designer\n" +
                       "✅ جميع الميزات متاحة\n\n" +
                       "🚀 RJTabControl جاهز للاستخدام!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                Padding = new Padding(20)
            };
            welcomeTab.AddControl(welcomeLabel);

            // تاب الاختبارات
            var testsTab = tabControl.AddTab("الاختبارات", IconChar.Flask);
            testsTab.BackColor = Color.FromArgb(76, 175, 80);
            testsTab.ForeColor = Color.White;
            testsTab.IconSize = 20;

            var testsPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var quickTestButton = new RJButton
            {
                Text = "اختبار سريع",
                IconChar = IconChar.Bolt,
                Location = new Point(20, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            quickTestButton.Click += (s, e) => QuickErrorTest.RunQuickTest();

            var collectionTestButton = new RJButton
            {
                Text = "اختبار Collection",
                IconChar = IconChar.List,
                Location = new Point(240, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            collectionTestButton.Click += (s, e) => QuickErrorTest.TestDesignerCollection();

            var comprehensiveTestButton = new RJButton
            {
                Text = "اختبار شامل",
                IconChar = IconChar.CheckCircle,
                Location = new Point(460, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            comprehensiveTestButton.Click += (s, e) => QuickErrorTest.RunComprehensiveTest();

            var statusLabel = new Label
            {
                Text = "🎮 اضغط على الأزرار أعلاه لتشغيل الاختبارات المختلفة\n\n" +
                       "جميع الاختبارات تعمل بدون مشاكل Designer!",
                Location = new Point(20, 90),
                Size = new Size(640, 60),
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.FromArgb(70, 70, 70),
                TextAlign = ContentAlignment.TopCenter
            };

            testsPanel.Controls.Add(quickTestButton);
            testsPanel.Controls.Add(collectionTestButton);
            testsPanel.Controls.Add(comprehensiveTestButton);
            testsPanel.Controls.Add(statusLabel);
            testsTab.AddControl(testsPanel);

            // تاب RJPanel
            var panelTab = tabControl.AddTab("RJPanel", IconChar.Square);
            panelTab.BackColor = Color.FromArgb(156, 39, 176);
            panelTab.ForeColor = Color.White;

            var testPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 4,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 15,
                Padding = new Padding(30)
            };

            var panelLabel = new Label
            {
                Text = "🎨 RJPanel مع الحدود الجديدة!\n\n" +
                       "BorderSize = 4\n" +
                       "BorderColor = بنفسجي\n" +
                       "BorderRadius = 15\n\n" +
                       "✅ جميع الميزات الجديدة تعمل!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(156, 39, 176)
            };
            testPanel.Controls.Add(panelLabel);
            panelTab.AddControl(testPanel);

            // تاب RJTextBox
            var textTab = tabControl.AddTab("RJTextBox", IconChar.Edit);
            textTab.BackColor = Color.FromArgb(255, 152, 0);
            textTab.ForeColor = Color.White;

            var testTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "📝 RJTextBox مع ReadOnly!\n\n" +
                       "ReadOnly = true\n" +
                       "هذا النص للقراءة فقط\n" +
                       "لا يمكن تعديله أو تغييره\n\n" +
                       "جرب النقر والكتابة - لن يحدث شيء!\n\n" +
                       "✅ الميزة الجديدة تعمل بمثالية! 🔒",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(255, 152, 0),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };
            textTab.AddControl(testTextBox);

            // تاب المعلومات
            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            infoTab.BackColor = Color.FromArgb(0, 150, 136);
            infoTab.ForeColor = Color.White;

            var infoTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "ℹ️ تم حل مشكلة Designer بنجاح!\n\n" +
                       "🔧 المشكلة كانت:\n" +
                       "• Form1 يرث من RJChildForm\n" +
                       "• Visual Studio لا يجد RJChildForm\n" +
                       "• خطأ Designer يمنع التحميل\n\n" +
                       "✅ الحل المطبق:\n" +
                       "• تغيير Form1 ليرث من Form\n" +
                       "• إزالة using SmartCreator.RJForms\n" +
                       "• إنشاء نماذج بديلة بسيطة\n\n" +
                       "🎉 النتيجة:\n" +
                       "• لا توجد أخطاء Designer\n" +
                       "• جميع الاختبارات تعمل\n" +
                       "• RJTabControl يعمل بمثالية\n\n" +
                       "🚀 جاهز للاستخدام في الإنتاج!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(0, 150, 136),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            infoTab.AddControl(infoTextBox);

            // إضافة TabControl للنموذج
            this.Controls.Add(tabControl);

            // تفعيل التاب الأول
            tabControl.SelectedIndex = 0;
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new TestFormSimple();
            form.ShowDialog();
        }

        /// <summary>
        /// تشغيل النموذج كـ Main
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new TestFormSimple());
        }
    }
}
