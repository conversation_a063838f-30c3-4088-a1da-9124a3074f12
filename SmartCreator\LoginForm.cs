﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Windows.Forms;
using SmartCreator.Settings;
using System.Diagnostics;
using SmartCreator.RJControls;
using SmartCreator.Models;
using SmartCreator.Data;
using System.Data.SQLite;
using Newtonsoft.Json;
using SmartCreator.Entities.UserManager;
using SmartCreator.db;
using SmartCreator.ViewModels;
//using AltoHttp;
using SmartCreator.Entities;
using SmartCreator.Utils;
using System.Runtime.InteropServices;
using SmartCreator.Properties;
using tik4net.Mndp;
using System.Data;
using DevComponents.DotNetBar;
using System.Windows.Controls;
using System.Threading;
using Dapper;
using System.IO;
using SmartCreator.Forms.Settings;
using Microsoft.Win32;
using System.Text;
using DevComponents.DotNetBar.Metro;
using System.Runtime.InteropServices.ComTypes;
using SmartCreator.Forms.SettingsForms;


//using tik4net.Mndp;
namespace SmartCreator
{
    public partial class LoginForm : RJForms.RJBaseForm
    {
       
        bool firstLoad = true;
        Form_LoingState Frm_State = null;
        bool DisableLoad_HSSession = false;
        bool DisableLoad_HSUsers = false;
        bool DisableLoad_UmPyment = false;
        bool DisableLoad_UmSession = false;
        bool DisableLoad_UmUsers = false;

        #region -> Constructor

        //App_Info

        //[DllImport("user32.DLL", EntryPoint = "ReleaseCapture")]
        //private extern static void ReleaseCapture();
        //[DllImport("user32.DLL", EntryPoint = "SendMessage")]
        //private extern static void SendMessage(System.IntPtr hWnd, int wMsg, int wParam, int lParam);

        //[DllImport("gdi32.dll", EntryPoint = "AddFontResourceW", SetLastError = true)]
        //public static extern int AddFontResource([In][MarshalAs(UnmanagedType.LPWStr)] string lpFileName);


        FormConnection formConnection = new FormConnection();

        [Obsolete]
        public LoginForm()
        {
            Graphics_dpi_icon();

            //checkFontIinstall();
            //this.StartPosition = FormStartPosition.CenterScreen;



            InitializeComponent();

            this.StartPosition = FormStartPosition.Manual;
            this.Top = (Screen.PrimaryScreen.Bounds.Height - this.Height) / 2;
            this.Left = (Screen.PrimaryScreen.Bounds.Width - this.Width) / 2;

            if (UIAppearance.DGV_RTL == false)
            {
                dgvMicrotikSaved.RightToLeft = RightToLeft.No;
            }

            //this.StartPosition = FormStartPosition.CenterScreen;
            //this.StartPosition = FormStartPosition.Manual;
            //this.Location = new Point(this.Location.X + (this.Width - this.Width) / 2, this.Location.Y + (this.Height - this.Height) / 2);
            //loadingCircle.Show(this);


            this.Icon = Properties.Resources.Smart_Creator;
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.None;

            AddControlBox();
            ApplyAppearanceSettings();
               
            Check_Last_SmartDB_File();

            formConnection.Check_SmartDB_File();
            formConnection.Check_CardsArchive_File();
            //formConnection.Check_ArchiveDB_File();
            App_Info appInfo = new App_Info();
            lbl_Viriion.Text=appInfo.Name;
            //pnl_domain.Location = new System.Drawing.Point(100, 95);
            //loadData();

            //Add line
            //var line = new Control();
            //line.Size = new Size(lblDescription.Width - 10, 1);
            //line.BackColor = Color.LightGray;
            //line.Location = new Point(lblDescription.Left + 5, lblTitle.Bottom + 15);
            //icoBanner.Controls.Add(line);
        }
        #endregion

        #region -> Private methods
        private void Graphics_dpi_icon()
        {
            try
            {
                Global_Variable.Graphics_dpi = CreateGraphics().DpiX;
            }
            catch { }
        }
        private void checkFontIinstall()
        {

            try
            {
                string fontDestination = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.Fonts), "DroidSansArabic.ttf");
                string fontDestination2 = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.Fonts), "DroidNaskh-Regular.ttf");
                //string fontDestination3 = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.Fonts), "DroidKufi-Bold.ttf");
                string fontDestination4 = Path.Combine(System.Environment.GetFolderPath(System.Environment.SpecialFolder.Fonts), "DroidKufi-Regular.ttf");
                //Checkfont();
                if (!File.Exists(fontDestination))
                {
                    //RJMessageBox.Show("لم يتم تثبت خطوط البرنامج اذا ضهرت هذه الرساله مره اخرى قم بتشغيل البرنامج كمسؤول ليتم تثبت خطوط البرنامج");
                    //Process.Start(@"installFont.bat");
                    Checkfont();
                }
                try
                {
                    if (File.Exists(@"installFont.bat"))
                    {
                        File.Delete(@"installFont.bat");
                    }
                }
                catch { }
            }
            catch (Exception ex) { /*MessageBox.Show(ex.Message);*/ }

        }
        private bool Checkfont()
        {
            bool status = false;
            try
            {
                try
                {
                    File.Delete(@"installFont.bat");
                }
                catch { }
                string filename = "cmd";
                string batchFilePath = @"installFont.bat";
                createFileBat();
                string argument = "/c\" " + batchFilePath + "\" ";

                ProcessStartInfo start = new ProcessStartInfo();
                start.FileName = filename;
                start.Arguments = argument;
                start.UseShellExecute = false;
                start.RedirectStandardOutput = true;
                start.CreateNoWindow = true;

                using (Process process = Process.Start(start))
                {
                    process.WaitForExit();

                    using (StreamReader reader = process.StandardOutput)
                    {
                        string result = reader.ReadToEnd();
                        status = true;
                    }
                }
            }
            catch { }
            return status;
        }
        private void createFileBat()
        {
            try
            {
                if (File.Exists(@"installFont.bat"))
                {
                    File.Delete(@"installFont.bat");
                }
                using (FileStream fs = File.Create(@"installFont.bat"))
                {
                    string batch = @"@echo off
                                    setlocal
                                    :: Function to check and obtain administrator privileges
                                    :CheckPrivileges
                                    :: Try a simple command to check for admin privileges
                                    NET FILE 1>NUL 2>NUL
                                    if '%errorlevel%' == '0' goto gotPrivileges
                                    :: Not running as administrator
                                    :getPrivileges
                                        if '%1'=='ELEV' goto gotPrivileges
                                        setlocal DisableDelayedExpansion
                                        set ""batchPath=%~f0""
                                        setlocal EnableDelayedExpansion
                                        echo Set UAC = CreateObject^(""Shell.Application""^) > ""%temp%\OEgetPrivileges.vbs""
                                        echo UAC.ShellExecute ""!batchPath!"", ""ELEV"", """", ""runas"", 1 >> ""%temp%\OEgetPrivileges.vbs""
                                        ""%temp%\OEgetPrivileges.vbs""
                                        del ""%temp%\OEgetPrivileges.vbs""
                                        exit /B
                                    :gotPrivileges
                                        endlocal & setlocal EnableDelayedExpansion
    
                                        :: Get the directory of the batch file
                                        set ""fontDir=%~dp0fonts""

                                        :: Verify that the fonts directory exists
                                        if not exist ""%fontDir%"" (
                                            echo The ""fonts"" directory does not exist in the same location .
                                            pause
                                            exit /B
                                        )

                                        echo ==============================
                                        echo Installing Fonts from: %fontDir%
                                        echo ==============================

                                        :: Install fonts (TrueType and OpenType) by copying them to the Fonts folder
                                        for %%F in (""%fontDir%\*.ttf"") do (
                                            echo Installing font: %%~nxF
                                            copy /Y ""%%F"" ""%windir%\Fonts\""
                                        )

                                        for %%F in (""%fontDir%\*.otf"") do (
                                            echo Installing font: %%~nxF
                                            copy /Y ""%%F"" ""%windir%\Fonts\""
                                        )

                                        :: Register the fonts in the Windows Registry
                                        for %%F in (""%fontDir%\*.ttf"") do (
                                            echo Registering font in registry: %%~nxF
                                            reg add ""HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Fonts"" /v ""%%~nF (TrueType)"" /t REG_SZ /d ""%%~nxF"" /f
                                        )

                                        for %%F in (""%fontDir%\*.otf"") do (
                                            echo Registering font in registry: %%~nxF
                                            reg add ""HKLM\SOFTWARE\Microsoft\Windows NT\CurrentVersion\Fonts"" /v ""%%~nF (OpenType)"" /t REG_SZ /d ""%%~nxF"" /f
                                        )

                                        echo ==============================
                                        echo Fonts installation completed.
                                        echo ==============================
                                        echo.
                                        :: Launch SmartCreator.exe
                                        start """" ""%~dp0SmartCreator.exe""
                                        echo Installation Finished..
                                        timeout /t 5 >nul
                                        exit /B";
                    
                    Byte[] title = new UTF8Encoding(true).GetBytes(batch);
                    fs.Write(title, 0, title.Length);
                }
            }
            catch (Exception Ex)
            {
                //Console.WriteLine(Ex.ToString());
            }
        }

        private void AddControlBox()
        {//Add control buttons (Maximize, close and minimize)
            try
            {
                pnlTopTitle.Controls.Add(this.btnClose);
                pnlTopTitle.Controls.Add(this.btnMinimize);
                this.btnClose.Height = 20;
                this.btnMinimize.Height = 20;

                if (UIAppearance.Language_ar)
                {
                    this.btnClose.Location = new Point(6, 5);
                    this.btnMinimize.Location = new Point(this.btnClose.Location.X + btnMinimize.Width, 5);
                    lbl_Viriion.Location = new Point(pnlTopBorder.Width - 100, 7);
                }
                else
                {
                    this.btnClose.Location = new Point(pnlTopBorder.Width - btnClose.Width - 6, 5);
                    this.btnMinimize.Location = new Point(this.btnClose.Location.X - btnMinimize.Width, 5);
                    lbl_Viriion.Location = new Point(11, 7);

                }
            }
            catch { }
        }
        private void ApplyAppearanceSettings()
        {// Apply the appearance properties of the configuration.
            try
            {
                this.PrimaryForm = true; // Set as primary form.
                this.Resizable = false; // Set that the form cannot be resized from the border.
                                        //this.BorderSize = 0; // Remove the border.
                this.BackColor = UIAppearance.BackgroundColor; // Set the back color.
                                                               //this.BorderSize = UIAppearance.FormBorderSize;//The form Border Width will be equal to the border of the user settings

                //pnlBootom.BackColor = UIAppearance.FormBorderColor;//Set form border color
                //pnlBootom.Height = UIAppearance.FormBorderSize;

                if (UIAppearance.Theme == UITheme.Light)//if the theme is LIGHT, set the maximize, minimize and close buttons to black.
                {
                    this.btnClose.Image = Properties.Resources.CloseDark;
                    this.btnMaximize.Image = Properties.Resources.MaximizeDark;
                    this.btnMinimize.Image = Properties.Resources.MinimizeDark;
                }


                pnlTopBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                pnlTopBorder.Height = 3;

                pnlLeftBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                pnlLeftBorder.Width = 3;

                pnlRightBorder.BackColor = UIAppearance.FormBorderColor;//Set form border color
                pnlRightBorder.Width = 3;

                pnlBootom.BackColor = UIAppearance.FormBorderColor;//Set form border color
                pnlBootom.Height = 3;
            }
            catch { }

        }

        [Obsolete]
        private bool Login()
        {
            if (check_variable_Login() == false)
            {
                return false;
            }
            Mk_DataAccess da = new Mk_DataAccess();
            if (da.GetResources() == false)
            {
                //MessageBox.Show(MyDataClass.Server_Architecture+"\n"+ MyDataClass.RB_SN +"\n"+ MyDataClass.RB_Soft_id + " لم تنجح عملية الاتصال بالراوتر حاول مره اخري");
                //Application.Exit();
                return false;
            }
            
            return true;
        }
        private void run_from_main()
        {
            //var user = new Models.User().Login(txtUser.Text, txtPassword.Text);
            //var mainForm = new MainForm(user);
            //mainForm.Show();
            //this.Hide();
            ////Redisplay the login form and clear fields if the main form is closed
            //mainForm.FormClosed += new FormClosedEventHandler(MainForm_Logout);
            //SaveFormVariable();
        }
        private bool check_variable_Login()
        {
            Global_Variable.Ddiable_LoadSession= Toggle_Ddiable_LoadSession.Checked;
            Global_Variable.load_by_DownloadDB= Toggle_load_by_DownloadDB.Checked;

            //Global_Variable.RunOffline = Toggle_RunOffline.Checked;

            //Global_Variable.Load_From_Last_load= Toggle_Load_From_Last_load.Checked;

            int number2API;
            Global_Variable.Server_Port = 8728;

            Global_Variable.Server_Port_SSL = 8729;
            //if (rjBy_Port.Check)
            //{
            //    if (!(int.TryParse(txtPort_api.Text, out number2API)) || txtPort_api.Text == "")
            //    {
            //        RJMessageBox.Show(" تاكد من منفذ او بورت المايكروتك ");
            //        return false;
            //    }
            //    Global_Variable.Server_Port = Convert.ToInt16(txtPort_api.Text.Trim());
            //    if (rjRB_SSH.Checked)
            //    {
            //        Global_Variable.SSL_Use = true;
            //        Global_Variable.Server_Port_SSL = Convert.ToInt16(txtPort_api.Text.Trim());
            //    }
            //    else Global_Variable.SSL_Use = false;
            //}
            if (Radio_ByIP.Checked)
            {
                if (rjBy_Port.Check)
                {
                    if (!(int.TryParse(txtPort_api.Text, out number2API)) || txtPort_api.Text == "")
                    {
                        RJMessageBox.Show(" تاكد من منفذ او بورت المايكروتك ");
                        return false;
                    }
                    Global_Variable.Server_Port = Convert.ToInt32(txtPort_api.Text.Trim());
                }


                int number2;
                if (!(int.TryParse(txt_IP_1.Text, out number2)) || txt_IP_1.Text == "" )
                {
                    MessageBox.Show(" ip تاكد من عنوان ");
                    return false;
                }
                if (!(int.TryParse(txt_IP_2.Text, out number2)) || txt_IP_2.Text == "")
                {
                    MessageBox.Show(" ip تاكد من عنوان ");
                    return false;
                }
                if (!(int.TryParse(txt_IP_3.Text, out number2)) || txt_IP_3.Text == "")
                {
                    MessageBox.Show(" ip تاكد من عنوان ");
                    return false;
                }
                if (!(int.TryParse(txt_IP_4.Text, out number2)) || txt_IP_4.Text == "")
                {
                    MessageBox.Show(" ip تاكد من عنوان ");
                    return false;
                }
                if ( (Convert.ToInt32(txt_IP_1.Text) < 0 || Convert.ToInt32(txt_IP_1.Text) > 255)   || (Convert.ToInt32(txt_IP_2.Text) < 0 || Convert.ToInt32(txt_IP_2.Text) > 255)   || (Convert.ToInt32(txt_IP_3.Text) < 0 || Convert.ToInt32(txt_IP_3.Text) > 255)    || (Convert.ToInt32(txt_IP_1.Text) < 0 || Convert.ToInt32(txt_IP_1.Text) > 255)   || (Convert.ToInt32(txt_IP_4.Text) < 0 || Convert.ToInt32(txt_IP_4.Text) > 255))
                {
                    RJMessageBox.Show("صيغة عنوان الايبي خطاء يجب ان تكون اكبر من رقم1 واقل من رقم 255 ");
                }
                string ip = txt_IP_1.Text + "." + txt_IP_2.Text + "." + txt_IP_3.Text + "." + txt_IP_4.Text;
                Global_Variable.Server_IP = ip;
                
            }
            if (Radio_ByDomain.Checked)
            {
                if (rjBy_Port_Domain.Check)
                {
                    if (!(int.TryParse(txtPort_Domain.Text, out number2API)) || txtPort_Domain.Text == "")
                    {
                        RJMessageBox.Show(" تاكد من منفذ او بورت المايكروتك ");
                        return false;
                    }
                    Global_Variable.Server_Port = Convert.ToInt32(txtPort_Domain.Text.Trim());
                }
                if (txtDomain.Text != "")
                { 
                    Global_Variable.Server_IP = txtDomain.Text.Trim();
                }
                else
                {
                    RJMessageBox.Show("ادخل عنوان السيرفر");
                    return false;
                }

            }
            if (Radio_BySmart.Checked)
            {
                if (rjBy_Port_SmartCloud.Check)
                {
                    if (!(int.TryParse(txtPort_SmartCloud.Text, out number2API)) || txtPort_SmartCloud.Text == "")
                    {
                        RJMessageBox.Show(" تاكد من منفذ او بورت المايكروتك ");
                        return false;
                    }
                    Global_Variable.Server_Port = Convert.ToInt32(txtPort_SmartCloud.Text.Trim());
                }
                if (txt_SmartCloud.Text != "")
                {
                    Global_Variable.Server_IP = txt_SmartCloud.Text.Trim();
                }
                else
                {
                    RJMessageBox.Show("ادخل عنوان السيرفر");
                    return false;
                }

            }

            Global_Variable.Server_Username = txtUser.Text;
            Global_Variable.Server_Password = txtPassword.Text;
            
          
            return true;
        }

        private void loadData_Form_Saved()
        {
            //Form_LoingState _Frm_State;
            try
            {
                try
                {
                    SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormLogin");
                    if (sourceSaveState == null)
                        Frm_State = new Form_LoingState();
                    else
                        Frm_State = JsonConvert.DeserializeObject<Form_LoingState>(sourceSaveState.values.ToString());
                }
                catch { }

                if (Frm_State == null)
                    Frm_State = new Form_LoingState();

                //if (Global_Variable.Mk_Login_data != null)
                //    Frm_State = Global_Variable.Mk_Login_data;

                if (Frm_State == null)
                    Frm_State = new Form_LoingState();

                loadData(Frm_State);
            }
            catch { }

        }

        private void loadData(Form_LoingState _Frm_State)
        {
            //SourceSaveStateFormsVariable sourceSaveState = SqlDataAccess.Get_SourceSaveStateFormsVariable("FormLogin");
            //if (sourceSaveState == null)
            //    Frm_State = new Form_LoingState();

            //Frm_State = JsonConvert.DeserializeObject<Form_LoingState>(sourceSaveState.values.ToString());

            //if (Frm_State == null)
            //    Frm_State = new Form_LoingState();


            //if (Global_Variable.Mk_Login_data != null ) 
            //    Frm_State = Global_Variable.Mk_Login_data;

            try
            {
                if (_Frm_State == null)
                    _Frm_State = new Form_LoingState();
                
                
                firstLoad = true;

                Radio_ByIP.Checked = _Frm_State.Login_By_IP;
                Radio_ByDomain.Checked = _Frm_State.Login_By_Domain;
                Radio_BySmart.Checked = _Frm_State.Login_By_SmartCloud;

                pnl_domain.Visible= _Frm_State.Login_By_Domain;
                pnl_ips.Visible= _Frm_State.Login_By_IP;
                pnl_Smart.Visible= _Frm_State.Login_By_SmartCloud;

                try
                {
                    string[] ips = _Frm_State.Mk_IP.Split(new string[] { "." }, StringSplitOptions.None);
                    txt_IP_1.Text = ips[0]; txt_IP_2.Text = ips[1]; txt_IP_3.Text = ips[2]; txt_IP_4.Text = ips[3];
                }
                catch { }

                txtDomain.Text = _Frm_State.Mk_Domain;
                txt_SmartCloud.Text = _Frm_State.Mk_SmartCloud;

                rj_Remember_user.Check = _Frm_State.UserName_Rem;
                rj_Remember_pass.Check = _Frm_State.Password_Rem;


                txt_note.Text = _Frm_State.Note;
                try { txtPort_api.Text = _Frm_State.Mk_Port_api.ToString(); } catch { }
                try { txtPort_Domain.Text = _Frm_State.Mk_Port_Domain.ToString(); } catch { }
                try { txtPort_SmartCloud.Text = _Frm_State.Mk_Port_SmartCloud.ToString(); } catch { }

                rjBy_Port.Checked = _Frm_State.Is_Use_Port;
                rjBy_Port_SmartCloud.Checked = _Frm_State.Is_Use_Port_SmartCloud;
                rjBy_Port_Domain.Checked = _Frm_State.Is_Use_Port_Domain;

                //rjBy_Port.Check = Frm_State.Is_Use_Port;
                if (_Frm_State.Login_By_IP)
                {
                    rjBy_Port.Visible = true;
                    if (_Frm_State.Is_Use_Port)
                        txtPort_api.Visible = _Frm_State.Is_Use_Port;
                }

                if (_Frm_State.Login_By_Domain)
                {
                    rjBy_Port_Domain.Visible = true;
                    if (_Frm_State.Is_Use_Port_Domain)
                    txtPort_Domain.Visible = _Frm_State.Is_Use_Port_Domain;
                }
                if (_Frm_State.Login_By_SmartCloud)
                {
                    rjBy_Port_SmartCloud.Visible = true;
                    if (_Frm_State.Is_Use_Port_SmartCloud)
                        txtPort_SmartCloud.Visible = _Frm_State.Is_Use_Port_SmartCloud;
                }

                //txtPort_SmartCloud.Visible = Frm_State.Login_By_SmartCloud;



                //rjRB_SSH.Checked = Frm_State.is_check_Port_ssh;
                //rjBy_Port.Check = Frm_State.is_Usee_Port;
                //if (rjRB_SSH.Checked)
                //txtPort_api.Text = Frm_State.Mk_Port_ssh.ToString();

                Toggle_Ddiable_LoadSession.Checked = _Frm_State.load_by_Disable_LoadSession;
                Toggle_load_by_DownloadDB.Checked = _Frm_State.load_by_DownloadDB;
                Toggle_Custom_Login.Checked = _Frm_State.load_by_Custom_Login;

                DisableLoad_HSSession = _Frm_State.DisableLoad_HSSession;
                DisableLoad_HSUsers = _Frm_State.DisableLoad_HSUsers;
                DisableLoad_UmPyment = _Frm_State.DisableLoad_UmPyment;
                DisableLoad_UmSession = _Frm_State.DisableLoad_UmSession;
                DisableLoad_UmUsers = _Frm_State.DisableLoad_UmUsers;

                Toggle_RunOffline.Checked = _Frm_State.LogIn_Without_mk;
               txtPort_SmartCloud.TextAlign=txtPort_Domain.TextAlign= txt_SmartCloud.TextAlign = txtDomain.TextAlign = txtPassword.TextAlign = txtPortSSH.TextAlign = txtPort_api.TextAlign = txtUser.TextAlign = txt_IP_1.TextAlign = txt_IP_2.TextAlign = txt_IP_3.TextAlign = txt_IP_4.TextAlign = txt_note.TextAlign = HorizontalAlignment.Center;


                try
                {
                    if (rj_Remember_user.Check)
                        txtUser.Text = _Frm_State.Mk_UserName;
                    if (rj_Remember_pass.Check)
                        txtPassword.Text = utils.Base64Decode(_Frm_State.Mk_password);
                }
                catch { }
                //txtPassword.Text = Frm_State.Mk_password;
                //rjComboBox1.DataSource= SqlDataAccess.GetRouters();
                //rjComboBox1.DisplayMember = "mk_sn";
                //rjComboBox1

                Frm_State = _Frm_State;
                firstLoad = false;

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); firstLoad = false; }
        }
        DataTable dtSavedMikrotik()
        {

            DataTable dt = new DataTable();
           
           
            dt.Columns.Add("العنوان");
            dt.Columns.Add("المستخدم");
            dt.Columns.Add("ملاحظة");

            dt.Columns.Add("id");

            //dgvMicrotikSaved.DataSource = dt;
            //dgvMicrotikSaved.Columns[0].Name = "address";
            //dgvMicrotikSaved.Columns[1].Name = "username";
            //dgvMicrotikSaved.Columns[2].Name = "note";
            //dgvMicrotikSaved.Columns[3].Name = "id";

            return dt;

        }

        private void DGV_Refresh()
        {
            dgvMicrotikSaved.ColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            //dgvMicrotikSaved.Rows.Clear();
            dgvMicrotikSaved.DataSource = null;
            DataTable dt = dtSavedMikrotik();

            List< SourceMk_login_saved> sourceMk_Login_Saved = SqlDataAccess.Ge_SourceMk_login_saved();
            if (sourceMk_Login_Saved.Count > 0)
            {
                foreach (SourceMk_login_saved itm in sourceMk_Login_Saved)
                {
                    Form_LoingState frmUser = JsonConvert.DeserializeObject<Form_LoingState>(itm.values.ToString());
                    string ip = frmUser.Mk_IP;
                     if (frmUser.Login_By_Domain)
                        ip = frmUser.Mk_Domain;

                    if (frmUser.Login_By_SmartCloud)
                        ip = frmUser.Mk_SmartCloud;


                    DataRow row = dt.NewRow();
                    row["العنوان"] = ip;
                    row["المستخدم"] = frmUser.Mk_UserName;
                    row["ملاحظة"] = frmUser.Note;
                    row["id"] = itm.id;
                    dt.Rows.Add(row);
                    //this.dgvMicrotikSaved.Rows.Add(ip, frmUser.Mk_UserName, frmUser.Note,itm.id);
                }
                dgvMicrotikSaved.DataSource= dt;
                dgvMicrotikSaved.Columns["العنوان"].Width = 180;
                dgvMicrotikSaved.Columns["id"].Visible = false;

            }


            ////List<Form_LoingState> frmUser = SqlDataAccess.GeUser_Login();
            //if (frmUser.Count > 0)
            //{
            //    dgvMicrotikSaved.Rows.Clear();
            //    dgvMicrotikSaved.DataSource = null;

            //    foreach (var itm in frmUser)
            //    {
            //        this.dgvMicrotikSaved.Rows.Add(itm.Mk_IP, itm.Mk_UserName, itm.note, itm.note);
            //    }
            //}
        }
        private void Set_Variable_Login()
        {
            Frm_State.Login_By_Domain = Radio_ByDomain.Checked;
            Frm_State.Login_By_IP = Radio_ByIP.Checked;
            Frm_State.Login_By_SmartCloud = Radio_BySmart.Checked;


            Frm_State.Mk_IP = txt_IP_1.Text + "." + txt_IP_2.Text + "." + txt_IP_3.Text + "." + txt_IP_4.Text;
            Frm_State.Mk_Domain = txtDomain.Text;
            Frm_State.Mk_SmartCloud = txt_SmartCloud.Text;

            Frm_State.Mk_UserName = txtUser.Text;
            Frm_State.Mk_password = utils.Base64Encode( txtPassword.Text);
            //Frm_State.Mk_password = txtPassword.Text;
            Frm_State.UserName_Rem = rj_Remember_user.Check;
            Frm_State.Password_Rem = rj_Remember_pass.Check;

            Frm_State.Is_Use_Port = rjBy_Port.Checked;
            Frm_State.Is_Use_Port_Domain = rjBy_Port_Domain.Checked;
            Frm_State.Is_Use_Port_SmartCloud = rjBy_Port_SmartCloud.Checked;
          
            if (rjBy_Port.Checked)
                if (Int32.TryParse(txtPort_api.Text, out int ne))
                    Frm_State.Mk_Port_api = Convert.ToInt32(txtPort_api.Text);

            if (rjBy_Port_Domain.Checked)
                if (Int32.TryParse(txtPort_Domain.Text, out int ne))
                    Frm_State.Mk_Port_Domain = Convert.ToInt32(txtPort_Domain.Text);

            if (rjBy_Port_SmartCloud.Checked)
                if (Int32.TryParse(txtPort_SmartCloud.Text, out int ne))
                    Frm_State.Mk_Port_SmartCloud = Convert.ToInt32(txtPort_SmartCloud.Text);

            //else
            //    Frm_State.Mk_Port_ssh = Convert.ToInt32(txtPort_api.Text);

            Frm_State.load_by_DownloadDB = Toggle_load_by_DownloadDB.Checked;
            Frm_State.load_by_Disable_LoadSession = Toggle_Ddiable_LoadSession.Checked;
            Frm_State.load_by_Custom_Login = Toggle_Custom_Login.Checked;
            Frm_State.Note = txt_note.Text;

            Frm_State.DisableLoad_HSSession = DisableLoad_HSSession;
            Frm_State.DisableLoad_HSUsers = DisableLoad_HSUsers;
            Frm_State.DisableLoad_UmPyment = DisableLoad_UmPyment;
            Frm_State.DisableLoad_UmSession = DisableLoad_UmSession;
            Frm_State.DisableLoad_UmUsers = DisableLoad_UmUsers;

            //bool DisableLoad_HSUsers = false;
            //bool DisableLoad_UmPyment = false;
            //bool DisableLoad_UmSession = false;
            //bool DisableLoad_UmUsers = false;

            if (DisableLoad_UmUsers)
                Frm_State.LogIn_Without_mk = Toggle_RunOffline.Checked;

            Global_Variable.Mk_Login_data = Frm_State;

        }
        private void SaveFormVariable()
        {

            Set_Variable_Login();
            string formSetting = JsonConvert.SerializeObject(Frm_State);
            Smart_DataAccess.Setting_SaveState_Forms_Variables("FormLogin", "SaveFromState", formSetting);

          
            Global_Variable.Mk_Login_data = Frm_State;
            //loadData();
            


        }
        private void Check_First_Open()
        {
            //======== get all Routers ===============

        }

        private void Login2()
        {
            ////Validate fields
            //if (string.IsNullOrWhiteSpace(txtUser.Text))
            //{
            //    lblMessage.Text = "*Please enter your username";
            //    lblMessage.Visible = true;
            //    return;
            //}
            //if (string.IsNullOrWhiteSpace(txtPassword.Text))
            //{
            //    lblMessage.Text = "*Please enter your password";
            //    lblMessage.Visible = true;
            //    return;
            //}

            ////Login
            //var user = new Models.User().Login(txtUser.Text, txtPassword.Text);

            //if (user != null)
            //{
            //    var mainForm = new MainForm(user);               
            //    mainForm.Show();
            //    this.Hide();

            //    //Redisplay the login form and clear fields if the main form is closed
            //    mainForm.FormClosed += new FormClosedEventHandler(MainForm_Logout);
            //}
            //else
            //{
            //    lblMessage.Text = "*Incorrect username or password";
            //    lblMessage.Visible = true;
            //}
        }
        private void Logout()
        {
            txtPassword.Clear();
            txtUser.Clear();           
            lblMessage.Visible = false;
            //lblCaption.Select();            
            this.Show();
        }
        #endregion

        #region -> Overrides

        protected override void CloseWindow()
        {//Override the method (To remove the message box if you want to exit the app) and simply exit the application, This is optional.
            System.Windows.Forms.Application.Exit();
        }
        #endregion

        #region -> Event Methods

        private void set_fonts()
        {
            //return;
            try
            {
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);
                //System.Drawing.Font  font2 = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
                //System.Drawing.Font  font2 = Program.GetCustomFont2(Resources.Cairo_Medium, 9, FontStyle.Regular);



                System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
                dgvMicrotikSaved.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
                dgvMicrotikSaved.ColumnHeadersHeight = (int)(35);
                //dgvMicrotikSaved.DefaultCellStyle.Font = new System.Drawing.Font(dgvMicrotikSaved.DefaultCellStyle.Font.FontFamily, dgvMicrotikSaved.DefaultCellStyle.Font.Size , dgvMicrotikSaved.DefaultCellStyle.Font.Style);



                rjLabel11.Font = rjLabel10.Font = rjLabel6.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);

                //txtDomain.Font =txt_SmartCloud.Font = Program.GetCustomFont(Resources., 9, FontStyle.Bold);
                txtDomain.Font = txt_SmartCloud.Font= new System.Drawing.Font("Tahoma", 9f, FontStyle.Bold);

                rjLabel3.Font   = rjLabel7.Font = 
                    lbl_Address.Font = lbl_Password.Font =  lbl_username.Font =
                     btnShowLoginDate.Font = btn_ShowNighboor.Font =
                     Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);


                lbl_by_ip.Font = lbl_By_Api.Font = lbl_Ssh.Font = lbl_By_Api.Font =  
                    rjBy_Port.Font = rjBy_Port_SmartCloud.Font= rjBy_Port_Domain.Font
                    = rj_Remember_pass.Font =rj_Remember_user.Font =
                    lbl_loginType.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

                //CustomFonts.Get_Custom_Font("Cairo_Medium", 9, true);

                //dgvMicrotikSaved.ColumnHeadersDefaultCellStyle.Font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, true); ;
                //dgvMicrotikSaved.ColumnHeadersHeight = 30;

                btnLogin.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Bold);
                btn_Custom_Login.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular );
                lbl_SmartCreator.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10  , FontStyle.Bold );

                //Control_Loop(this);

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }

        private void Control_Loop(System.Windows.Forms.Control ctl)
        {
            try
            {
                foreach (System.Windows.Forms.Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(System.Windows.Forms.Panel) || C.GetType() != typeof(Form) || C.GetType() != typeof(MetroForm))
                            C.Font = new System.Drawing.Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }
        [Obsolete]
        private void btnLogin_Click(object sender, EventArgs e)
        { 
            if (!Radio_ByDomain.Checked && !Radio_ByIP.Checked && !Radio_BySmart.Checked)
            { 
                RJMessageBox.Show("اختار طريقة الاتصال");
                return; 
            }
            if (Login() == false) 
                return; 

            Check_Last_LocalDB_File();
            formConnection.Get_Path_Database3();

            //formConnection.Get_Path_Database2();
            this.Hide();

            Set_Variable_Login();
            //Get_Path_Database();
            SaveFormVariable();
            formConnection.CreateDefultTemplate();

            CLS_FirstLoad fl = new CLS_FirstLoad();
            fl.button66_click();
            fl.GetProfileMode(this);

            //run_from_main();

            //Fast_Load_From_Mikrotik fs = new Fast_Load_From_Mikrotik();
            //fs.Download_Sql_From_Mikrotik();

        }

        private void txtPassword_KeyDown(object sender, KeyEventArgs e)
        {
            //if (e.KeyCode == Keys.Enter)
            //    Login();
        }

        private void MainForm_Logout(object sender, FormClosedEventArgs e)
        {
            Logout();//Log out
        }
        private void biYoutube_Click(object sender, EventArgs e)
        {
            Process.Start("https://youtube.com/rjcodeadvanceen");
        }
        private void biWebPage_Click(object sender, EventArgs e)
        {
            Process.Start("https://rjcodeadvance.com/");
        }
        private void biGitHub_Click(object sender, EventArgs e)
        {
            Process.Start("https://github.com/rjcodeadvance");
        }
        private void biFacebook_Click(object sender, EventArgs e)
        {
            Process.Start("https://www.facebook.com/RJCodeAdvance");
        }
        #endregion

        #region Database check

        //bool is_check_localDB = false;
        [Obsolete]
        public void Check_Last_SmartDB_File()
        {
            string Smartfile = $"{utils.Get_Database_Directory()}\\Smart.db";
            //FormConnection fc = new FormConnection();
            //Form_Backup fb = new Form_Backup();

            if (File.Exists(Smartfile) == false)
            {
                if (File.Exists(@"upgrade.json"))
                {
                    string contents = File.ReadAllText(@"upgrade.json");
                    if (contents != "")
                    {
                        try
                        {
                            Old_DatabaseInfo db = JsonConvert.DeserializeObject<Old_DatabaseInfo>(contents);
                            if (db != null)
                            {
                                //RJMessageBox.Show("تم اكتشاف قاعدة بيانات سابقة ");
                                //RJMessageBox.Show("هل تريد استعادة بيانات الطباعة والدفعات وقوالب الكروت من قاعدة البيانات السابقة");

                                //string msg = $"هل تريد استعادة بيانات الطباعة والدفعات وقوالب الكروت من قاعدة البيانات السابقة" + Environment.NewLine + Environment.NewLine + $"من المسار   {db.Path}";
                                //MessageBoxEx.Show(msg, "سمارت كريتور", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);


                                Form_RestorLastBackup frm = new Form_RestorLastBackup(db, "SmartDB");
                                //Form_RestoreBackups_FirstUse frm = new Form_RestoreBackups_FirstUse(db, "SmartDB");
                                //frm.ShowDialog();
                                //return;
                                //========== SmartDB =======================
                                //string connection_str = $@"Data Source={contents.Trim()}\db\SmartDB.db;";
                                //fc.Check_SmartDB_File();
                                //fb.Restor_SmartDB(connection_str);

                                //is_check_localDB = true;
                                //======== Local Db ======================
                                //var rb = Smart_DataAccess.Get_default_Connections_Db();
                                //if (rb == null)
                                //{
                                //    fc.Get_Path_Database2();

                                //    string local_connection_str = $@"Data Source={contents.Trim()}\db\localDB.db;";

                                //    fb.LoadLocal_Old_DB2(local_connection_str);
                                //}
                            }
                        }catch(Exception ex) { MessageBox.Show(ex.Message); }
                    }
                }
            }
        }

        [Obsolete]
        public void Check_Last_LocalDB_File()
        {
            string FileName = $"{utils.Get_Database_Directory()}\\db_{Global_Variable.Mk_resources.RB_SN}.db";

            //var found_rb = Smart_DataAccess.Get_default_Connections_Db();
            if (File.Exists(FileName) == false)
            {
                if (File.Exists(@"upgrade.json"))
                {
                    string contents = File.ReadAllText(@"upgrade.json");
                    if (contents != "")
                    {
                        Old_DatabaseInfo db = JsonConvert.DeserializeObject<Old_DatabaseInfo>(contents);
                        if (db != null)
                        {
                            var Is_found_last_Rb = db.Routers.Find(x => x.Rb_sn == Global_Variable.Mk_Router.mk_sn);
                            if (Is_found_last_Rb != null)
                            {
                                //======== check found db and open file =========
                                int count = 0;
                                try
                                {
                                    // test connection---
                                    // path = path.TrimEnd('\r', '\n');
                                    using (var cnn = new SQLiteConnection($@"Data Source={db.Path.Trim()}\db\localDB.db;"))
                                    {
                                        count= cnn.ExecuteScalar<int>($"SELECT count(*) FROM users WHERE mk_sn='{Global_Variable.Mk_Router.mk_sn}';");
                                    }
                                }
                                catch (Exception ex)
                                {
                                    return ;
                                }
                                if (count > 0)
                                {
                                    //string msg=$"هل تريد استعادة التقارير السابقة الخاصة بهذا الروتر "+Environment.NewLine+ Environment.NewLine + $"من المسار   {db.Path}";
                                    // MessageBoxEx.Show(msg,"سمارت كريتور", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                                    Form_RestorLastBackup frm = new Form_RestorLastBackup(db, "localDB");
                                    //Form_RestoreBackups_FirstUse frm = new Form_RestoreBackups_FirstUse(db, "localDB");
                                    frm.ShowDialog();
                                    return;
                                }
                            }
                            //========== SmartDB =======================
                            //string connection_str = $@"Data Source={contents.Trim()}\db\SmartDB.db;";
                            //fc.Check_SmartDB_File();
                            //fb.Restor_SmartDB(connection_str);

                            //is_check_localDB = true;
                            //======== Local Db ======================
                            //var rb = Smart_DataAccess.Get_default_Connections_Db();
                            //if (rb == null)
                            //{
                            //    fc.Get_Path_Database2();

                            //    string local_connection_str = $@"Data Source={contents.Trim()}\db\localDB.db;";

                            //    fb.LoadLocal_Old_DB2(local_connection_str);
                            //}
                        }
                    }
                }
            }

            //=============================================================

            return;

            //if (is_check_localDB==false)
            //    return;

            FormConnection fc = new FormConnection();
            Form_Backup fb = new Form_Backup();

            if (File.Exists(@"LastDbtoUpgrade"))
            {
                string contents = File.ReadAllText(@"LastDbtoUpgrade");
                if (contents != "")
                {
                    //======== Local Db ======================
                    var rb = Smart_DataAccess.Get_default_Connections_Db();
                    if (rb == null)
                    {
                        fc.Get_Path_Database2();
                        string local_connection_str = $@"Data Source={contents.Trim()}\db\localDB.db;";
                        fb.LoadLocal_Old_DB2(local_connection_str);
                    }

                }
            }
            //=============================================================

        }

        #endregion

        private void LoginForm_Load(object sender, EventArgs e)
        {
            //Properties.Settings.Default.DashBoardServerType = CboxServerType.SelectedIndex;
            //Properties.Settings.Default.Save();

            this.ShowInTaskbar = true;
            txtPort_api.Text = "8728";
            //txt_SmartCloud.Text = "FDE5DSD587WR.cloud.smrik.com";
            ApplyAppearanceSettings();
            set_fonts();
            //utils.Control_textSize(this);
            //utils.dgv_textSize(dgvMicrotikSaved);

            timer1.Start();



            //int curntDpi_Size = (int) (btn_Save.IconSize * 96f / CreateGraphics().DpiX);
            // int iconSize = btn_Save.IconSize;

            // int newSize = iconSize + (iconSize-curntDpi_Size);
            //     btn_Save.IconSize = newSize;
            // //RJMessageBox.Show( btn_Save.IconSize.ToString());

            //this.btn_Save.IconSize = (int)(btn_Save.IconSize + (btn_Save.IconSize - (btn_Save.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //this.btnAddRB.IconSize = (int)(btnAddRB.IconSize + (btnAddRB.IconSize - (btnAddRB.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //this.btnDeleteRB.IconSize = (int)(btnDeleteRB.IconSize + (btnDeleteRB.IconSize - (btnDeleteRB.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //this.btnLogin.IconSize = (int)(btnLogin.IconSize + (btnLogin.IconSize - (btnLogin.IconSize * 96f / Global_Variable.Graphics_dpi)));
            //this.btn_Refresh.IconSize = (int)(btn_Refresh.IconSize + (btn_Refresh.IconSize - (btn_Refresh.IconSize * 96f / Global_Variable.Graphics_dpi)));

            utils utils = new utils();
            utils.Control_textSize1(this);
            //utils.Control_textSize1(rjPanel2);

        }

        private void rjTB_DarkMode_CheckedChanged(object sender, EventArgs e)
        {
            //if (rjTB_DarkMode.Checked == true)
            //{
            //    UIAppearanceSettings.Default.Theme = 0;
            //    //UIAppearanceSettings.Default.Language_en = language;
            //}
            //else
            //{
            //    UIAppearanceSettings.Default.Theme = 1;
            //}  
            //UIAppearanceSettings.Default.Save();//Save the current values to the application settings file.
            //ApplyAppearanceSettings();

        }

        private void txtDomain_onTextChanged(object sender, EventArgs e)
        {
            //txtDomain.Text= string.Empty;
        }

        private void rjCheckBox_Domain_CheckedChanged(object sender, EventArgs e)
        {
            //if (rjCheckBox_Domain.Checked == true)
            //{
            //    //txtDomain.Visible = true;
            //    pnl_ips.Visible = false;
            //    pnl_domain.Visible = true;
            //    //lbl_loginType.Text = "IP";
            //    lbl_by_ip.Visible = true;
            //    lbl_loginType.Visible=false;

            //}
            //else
            //{
            //    //txtDomain.Visible = false;
            //    pnl_domain.Visible = false;
            //    pnl_ips.Visible = true;
            //    //lbl_loginType.Text = "رابط";
            //    lbl_by_ip.Visible = false;
            //    lbl_loginType.Visible = true;
            //}
        }

        private void txt_IP_1_Leave(object sender, EventArgs e)
        {
            Check_IPs(txt_IP_1);
            //int number2;
            //if (!(int.TryParse(txt_IP_1.Text, out number2)))
            //{
            //    RJMessageBox.Show("ip تاكد من عنوان");
            //    //MessageBox.Show(" ip تاكد من عنوان ");
            //    txt_IP_1.Focus();
            //    return;
            //}
            //if (Convert.ToInt32( txt_IP_1.Text)<0 || Convert.ToInt32(txt_IP_1.Text) > 255)
            //{
            //    RJMessageBox.Show("عنوان الايبي يجب ان يكون اكبر من 0 واقل من 255");
            //    txt_IP_1.Focus();

            //    return; 
            //}

        }
        private bool Check_IPs(RJTextBox txt)
        {
            bool check=true;
            int number2;
            
            if (!(int.TryParse(txt.Text, out number2)))
            {
                RJMessageBox.Show("ip تاكد من عنوان");
                txt.Focus();
                return false;
            }
            if (Convert.ToInt32(txt.Text) < 0 || Convert.ToInt32(txt.Text) > 255)
            {
                RJMessageBox.Show("عنوان الايبي يجب ان يكون اكبر بين 0 و 255");
                txt.Focus();

                return false;
            }
            return check;
        }

        private void txt_IP_2_Leave(object sender, EventArgs e)
        {
            Check_IPs(txt_IP_2);

        }

        private void txt_IP_3_Leave(object sender, EventArgs e)
        {
            Check_IPs(txt_IP_3);

        }

        private void txt_IP_4_Leave(object sender, EventArgs e)
        {
            Check_IPs(txt_IP_4);

        }
        private void rjRB_SSH_CheckedChanged(object sender, EventArgs e)
        {
            //if (!firstLoad)
            //    if (rjRB_SSH.Checked)
            //    {
            //        txtPort_api.Text = Global_Variable.Server_Port_SSL.ToString();
            //        Global_Variable.Mk_Login_data.is_check_Port_ssh = true;
            //        Global_Variable.Mk_Login_data.is_check_Port_api = false;
            //    }
        }

        private void rjRB_API_CheckedChanged(object sender, EventArgs e)
        {
            //if (!firstLoad)
            //    if (rjRB_API.Checked)
            //    {
            //        txtPort_api.Text = Global_Variable.Server_Port.ToString();
            //        Global_Variable.Mk_Login_data.is_check_Port_ssh = false;
            //        Global_Variable.Mk_Login_data.is_check_Port_api = true;
            //    }
        }

        [Obsolete]
        private void btnLogin_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
                Login();
        }

        private void rjRunOffline_CheckedChanged(object sender, EventArgs e)
        {
            Frm_State.LogIn_Without_mk=Toggle_RunOffline.Checked;
            Global_Variable.RunOffline = Toggle_RunOffline.Checked;
            //if (!rjRunOffline.Checked)
            //    Global_Variable.RunOffline = true;
            //else Global_Variable.RunOffline = false;
        }

        private void rjDdiable_LoadSession_CheckedChanged(object sender, EventArgs e)
        {
            if (Toggle_Ddiable_LoadSession.Checked)
                Global_Variable.Ddiable_LoadSession = true;
            else Global_Variable.Ddiable_LoadSession= false;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            try
            {
                FingerPrint f = new FingerPrint();
                Global_Variable.Pc_Code = f.Value();
            }
            catch { }


            loadData_Form_Saved();
            DGV_Refresh();
            firstLoad = false;
            this.ShowInTaskbar = true;
        }

        private void btnAddRB_Click(object sender, EventArgs e)
        {
            Set_Variable_Login();

            SaveFormVariable();
            Frm_State.Name = "MK_User";
            Frm_State.Type = "databases_users";
            string formSetting = JsonConvert.SerializeObject(Frm_State);

            //SqlDataAccess.AddUser_Login(formSetting);
            if (SqlDataAccess.AddUser_Login(formSetting) == false)
                RJMessageBox.Show("خطاء");
            DGV_Refresh();



        }

        private void btnDeleteRB_Click(object sender, EventArgs e)
        {
            int ID = -1;
            foreach (DataGridViewRow dr in dgvMicrotikSaved.SelectedRows)
            {
                ID = Convert.ToInt32(dr.Cells["id"].Value);
            }
            if (ID > -1)
            if (SqlDataAccess.delete_User_Login(ID) == false)
                RJMessageBox.Show("خطاء");
            DGV_Refresh();
        }

        private void Btn_DeActive(RJButton bnt)
        {
            //foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                //if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    //RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 9);
                    //bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 9);
            //bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }

        bool UseMNDP = false;
        private void btnShowLoginDate_Click(object sender, EventArgs e)
        {
            Btn_Active(btnShowLoginDate);
            Btn_DeActive(btn_ShowNighboor);
            UseMNDP = false;
            dgvMicrotikSaved.DataSource = null;
            //dgvMicrotikSaved.AutoGenerateColumns = false;
            dgvMicrotikSaved.DataBindings.Clear();
            dgvMicrotikSaved.ColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;

            DGV_Refresh();
        }

        private void btn_ShowNighboor_Click(object sender, EventArgs e)
        {
            Btn_Active(btn_ShowNighboor);
            Btn_DeActive(btnShowLoginDate);


            UseMNDP = true;
            dgvMicrotikSaved.ColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
            List<TikInstanceDescriptor>list =new List<TikInstanceDescriptor>();
            dgvMicrotikSaved.DataSource=list;

            ThreadStart theprogress = new ThreadStart(() => GetNigbour());
            Thread startprogress = new Thread(theprogress);
            startprogress.Start();

        }
        void GetNigbour()
        {
            try
            {

                List<TikInstanceDescriptor> items = null;
                items = (List<TikInstanceDescriptor>)MndpHelper.Discover(true);

                dgvMicrotikSaved.Invoke(
                   (System.Windows.Forms.MethodInvoker)delegate ()
                   {

                       //var items = MndpHelper.Discover(true);
                       dgvMicrotikSaved.DataSource = items;

                   });
            }
            catch { }
           
        }

        private void rjBy_Port_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            txtPort_api.Visible = rjBy_Port.Checked;
            Frm_State.Is_Use_Port = rjBy_Port.Checked;
            //if (rjBy_Port.Checked)
            //{
            //    pnl_port.Visible = true;
            //    txtPort_api.Visible = true;
            //}
            //else
            //{
            //    pnl_port.Visible = false;
            //    txtPort_api.Visible = false;
            //}
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
         ////string _localDB_path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
         //   string sourceEncrpt = @"Data Source=db\localDB.db;Password=Mypass;";
         //   string source = @"Data Source=db\localDB.db;";
         //   try
         //   {
         //       //string conn = @"Data Source=database.s3db;Password=Mypass;";
         //       SQLiteConnection connection = new SQLiteConnection(sourceEncrpt);
         //       connection.Open();
         //       //Some code
         //       connection.ChangePassword("Mypass");
         //       connection.Close();
         //   }
         //   //if it is the first time sets the password in the database
         //   catch
         //   {
         //       //string conn = @"Data Source=database.s3db;";
         //       SQLiteConnection connection = new SQLiteConnection(source);
         //       connection.SetPassword("Mypass");

         //       connection.Open();
         //       //Some code
         //       //connection.SetPassword("Mypass");
         //       //connection.ChangePassword("Mypass");
         //       connection.Close();
         //   }
        }

        [Obsolete]
        private void rjDataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (UIAppearance.Theme == UITheme.Light)
            {
                //dgvMicrotikSaved.DgvBackColor = RJColors.LightItemBackground; // Background color
                //dgvMicrotikSaved.RowsColor = RJColors.LightItemBackground; // Color of the rows
                //dgvMicrotikSaved.ColumnHeaderColor = UIAppearance.StyleColor; // background color column headings
                //dgvMicrotikSaved.ColumnHeaderTextColor = Color.WhiteSmoke; // text color column headings.
                //dgvMicrotikSaved.RowsTextColor = UIAppearance.TextColor; // Color of the text of the rows
                //dgvMicrotikSaved.GridColor = Color.Gainsboro; // Grid color

                //dgvMicrotikSaved.Rows[e.RowIndex].Cells.[e.ColumnIndex].Selected = true;
                //dgvMicrotikSaved.Columns[e.ColumnIndex].HeaderCell.Style.BackColor = RJColors.LightItemBackground;
                //dgvMicrotikSaved.Columns[e.ColumnIndex].HeaderCell.Style.ForeColor = Color.WhiteSmoke;
            }
            if (e.RowIndex < 0) { return; }
            if (UseMNDP == false)
            {
                Id_Select = -1;
                foreach (DataGridViewRow dr in dgvMicrotikSaved.SelectedRows)
                {
                    Id_Select = Convert.ToInt32(dr.Cells["id"].Value);
                }


                SourceMk_login_saved sourceMk_Login_Saved = SqlDataAccess.Ge_SourceMk_login_saved(Id_Select);
                if (sourceMk_Login_Saved != null)
                {
                    Form_LoingState frmUser = JsonConvert.DeserializeObject<Form_LoingState>(sourceMk_Login_Saved.values.ToString());
                    loadData(frmUser);
                }



                return;
                try
                {
                    SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormLogin");
                    if (sourceSaveState == null)
                        Frm_State = new Form_LoingState();
                    else
                        Frm_State = JsonConvert.DeserializeObject<Form_LoingState>(sourceSaveState.values.ToString());
                }
                catch { }

                if (Frm_State == null)
                    Frm_State = new Form_LoingState();

                //if (Global_Variable.Mk_Login_data != null)
                //    Frm_State = Global_Variable.Mk_Login_data;

                if (Frm_State == null)
                    Frm_State = new Form_LoingState();


                //int ID = -1;
                //foreach (DataGridViewRow dr in dgvMicrotikSaved.SelectedRows)
                //{
                //    ID = Convert.ToInt16(dr.Cells["ID"].Value);
                //    //ID_Last_open = ID;
                //    //MessageBox.Show(dr.Cells[1].Value.ToString());
                //}

                //DataRow[] result = new DataRow[0];
                //result = Router_Device.Select("ID=" + ID);
                ////hide_username.Checked = Convert.ToBoolean(result[0][2]);

                ////txt_Server_Domain.Text = result[0]["Mk_Domain"].ToString();
                //txtPort.Text = result[0]["Mk_Port"].ToString();
                //txt_Port_ssh.Text = result[0]["Mk_Port_ssh"].ToString();

                //txt_UserName.Text = result[0]["Mk_UserName"].ToString();
                //txt_Password.Text = DecryptString(key, result[0]["Mk_Password"].ToString());

                //txtNote.Text = result[0]["note"].ToString();
                //checkBoxSaveUsername.Checked = Convert.ToBoolean(result[0]["UserName_Rem"]);
                //checkBoxSavePassword.Checked = Convert.ToBoolean(result[0]["Password_Rem"]);
                //checkBox_DisableLoadSession.Checked = Convert.ToBoolean(result[0]["Disable_Load_Session"]);
                //checkBoxLoginWithout.Checked = Convert.ToBoolean(result[0]["LogIn_Without_mk"]);
                //checkBox_Port.Checked = Convert.ToBoolean(result[0]["is_check_Port"]);
                //checkBox_ssh.Checked = Convert.ToBoolean(result[0]["is_check_Port_ssh"]);

                //if (result[0]["login_By_IP"].ToString().ToLower() == "false")
                //{
                //    radioButtonDomain.Checked = true;
                //    txt_Server_Domain.Text = result[0]["Mk_Domain"].ToString();
                //}
                //else
                //{
                //    try
                //    {
                //        radioButtonByIP.Checked = true;
                //        string ips = result[0]["Mk_IP"].ToString();
                //        string[] split = ips.Split(new string[] { "." }, StringSplitOptions.None);
                //        txt_IP_1.Text = split[0];
                //        txt_IP_2.Text = split[1];
                //        txt_IP_3.Text = split[2];
                //        txt_IP_4.Text = split[3];
                //    }
                //    catch { }
                //}
                //if (checkBoxSaveUsername.Checked == false)
                //{

                //    txt_UserName.Text = "";
                //}
                //if (checkBoxSavePassword.Checked == false)
                //{

                //    txt_Password.Text = "";
                //}
            }
            else
            {
                if (dgvMicrotikSaved != null && dgvMicrotikSaved.Rows.Count>0)
                {

                    TikInstanceDescriptor tik = (TikInstanceDescriptor)dgvMicrotikSaved.CurrentRow.DataBoundItem;
                    txt_note.Text=tik.Identity.ToString();

                    try
                    {
                        Radio_ByIP.Checked=true;
                        rjBy_Port.Checked = false;
                        string[] ips = tik.IPv4.MapToIPv4().ToString().Split(new string[] { "." }, StringSplitOptions.None);
                        txt_IP_1.Text = ips[0]; txt_IP_2.Text = ips[1]; txt_IP_3.Text = ips[2]; txt_IP_4.Text = ips[3];
                    }
                    catch { }

                }
            }
        }

        private void txt_IP_1_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 46)
            {
                txt_IP_2.Focus();
            }
        }

        private void txt_IP_2_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 46)
                txt_IP_3.Focus();
                        
        }

        private void txt_IP_1_KeyDown(object sender, KeyEventArgs e)
        {
            
        }

        private void txt_IP_1_onTextChanged(object sender, EventArgs e)
        {
            txt_IP_1.Text = txt_IP_1.Text.TrimEnd(new char[] { '.' });
            txt_IP_1.Text = txt_IP_1.Text.TrimStart(new char[] { '.' });
        }

        private void txt_IP_2_onTextChanged(object sender, EventArgs e)
        {
            txt_IP_2.Text = txt_IP_2.Text.TrimEnd(new char[] { '.' });
            txt_IP_2.Text = txt_IP_2.Text.TrimStart(new char[] { '.' });
        }

        private void txt_IP_3_onTextChanged(object sender, EventArgs e)
        {
            txt_IP_3.Text = txt_IP_3.Text.TrimEnd(new char[] { '.' });
            txt_IP_3.Text = txt_IP_3.Text.TrimStart(new char[] { '.' });
        }

        private void txt_IP_4_onTextChanged(object sender, EventArgs e)
        {
            txt_IP_4.Text = txt_IP_4.Text.TrimEnd(new char[] { '.' });
            txt_IP_4.Text = txt_IP_4.Text.TrimStart(new char[] { '.' });
        }

        private void txt_IP_3_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 46)
                txt_IP_4.Focus();
        }

        private void txt_IP_4_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 46)
                txtUser.Focus();
        }

        [Obsolete]
        private void rjButton2_Click(object sender, EventArgs e)
        {

            Form1 form1 = new Form1();
            form1.Show();

           

            // اختبار بسيط بدون مشاكل
            SimpleTabTestForm.RunTest();
            return;
            Fast_Load_From_Mikrotik fs=new Fast_Load_From_Mikrotik();
            fs.Remove_Indexs();
            fs.Create_Indexs();
            return;
            fs.Download_Sql_From_Mikrotik();
            return;
            new FormConnections().ShowDialog();
            return;
            //Helper.InitDb();
            return;
            Dictionary<string, string> dictionary = new Dictionary<string, string>();
           
 
            //result.PrintDump(); //= {Id: 1, Name:Seed Data}

          
             
        }
        //public string GetInsertSql<T>(T model)
        //{
        //    //using (SqlConnection SqlConn = new SqlConnection("_connectionString"))
        //    //{
        //        //T GetResult;
        //        Type t = typeof(T);
        //        Type ObjType = model.GetType();

        //        string SqlParameter = "";
        //        string SqlValue = "";
        //        PropertyInfo[] prop = ObjType.GetProperties();
        //        for (int i = 0; i < prop.Length; i++)
        //        {
        //            //Check if the property is a primay key
        //            object[] attrs = prop[i].GetCustomAttributes(typeof(KeyAttribute), false);
        //        if (attrs.Length == 0)
        //        {

        //            SqlParameter = (SqlParameter == "") ? $"[{prop[i].Name}]" : $"{SqlParameter},[{prop[i].Name}]";
        //            SqlValue = (SqlValue == "") ? $"@{prop[i].Name}" : $"{SqlValue},@{prop[i].Name}";
        //            //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";

        //            //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";
        //        }

        //        }
        //        string SqlString = $"insert into [{ObjType.Name}] ({SqlParameter})values({SqlValue})";

        //    //SqlCommand SqlCmd = new SqlCommand(SqlString, SqlConn);
        //    //SqlConn.Open();
        //    //SqlCmd.ExecuteNonQuery();
        //    //SqlConn.Close();

        //    //Get Inserted data
        //    //GetResult = GetInsertData(model);
        //    //return "GetResult";
        //    return SqlString;
        //    //}
        //}
        private void rjButton3_Click(object sender, EventArgs e)
        {

            //GenTable<UmUser>();
            GenerateTable(typeof(UmUser),true);
            GenerateTable(typeof(UmPyment),true);
            GenerateTable(typeof(UmSession),true);
            return;




        //string inserDB = InsertData(new UmUser());
        Smart_DataAccess Smart_DB= new Smart_DataAccess();

            string rb = Global_Variable.Mk_resources.RB_code;
            Smart_DB.Load<SellingPoint>(x => x.Rb == rb);
            return;


            //try
            //{
            //    HttpDownloader http = new HttpDownloader(txtDomain.Text, $"{System.Windows.Forms.Application.StartupPath}\\{Path.GetFileName(txtDomain.Text)}");
            //    http.Start();
            //}
            //catch(Exception ex) { RJMessageBox.Show(ex.Message); }
            return;
            //var addnewUmUser = studentList[0];
            string sql=txtDomain.Text;
            Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
           RJMessageBox.Show( sql_DataAccess.RunSqlScript(sql)+"");
        }


        private void GenerateTable(Type type,bool WITHOUTROWID)
        {List<TableClass> tables = new List<TableClass>();
            //Type type = typeof(T);
            TableClass tc = new TableClass(type);
            tables.Add(tc);

            // Create SQL for each table
            foreach (TableClass table in tables)
            {
                string t = table.CreateTableScript(WITHOUTROWID).ToString();
            }

            
            // Total Hacked way to find FK relationships! Too lazy to fix right now
            //foreach (TableClass table in tables)
            //{
            //    foreach (KeyValuePair<String, Type> field in table.Fields)
            //    {
            //        foreach (TableClass t2 in tables)
            //        {
            //            if (field.Value.Name == t2.ClassName)
            //            {
            //                // We have a FK Relationship!
            //                Console.WriteLine("GO");
            //                Console.WriteLine("ALTER TABLE " + table.ClassName + " WITH NOCHECK");
            //                Console.WriteLine("ADD CONSTRAINT FK_" + field.Key + " FOREIGN KEY (" + field.Key + ") REFERENCES " + t2.ClassName + "(ID)");
            //                Console.WriteLine("GO");

            //            }
            //        }
            //    }
            //}
        }

        private void Toggle_RunOffline_CheckedChanged(object sender, EventArgs e)
        {
            Toggle_Ddiable_LoadSession.Checked = false;
            Toggle_load_by_DownloadDB.Checked = false;
            //Toggle_Load_From_Last_load.Checked = false;
        }
        
        private void Toggle_Ddiable_LoadSession_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            if (Toggle_Ddiable_LoadSession.Checked)
            {
                firstLoad = true;
                Toggle_load_by_DownloadDB.Checked = false;
                Toggle_Custom_Login.Checked = false;
                firstLoad = false;
            }

            //if (Toggle_Custom_Login.Checked)
            //    Toggle_Ddiable_LoadSession.Checked = false;

            //if (Toggle_load_by_DownloadDB.Checked)
            //    Toggle_Ddiable_LoadSession.Checked = false;

            //Toggle_RunOffline.Checked = false;
            //Toggle_load_by_DownloadDB.Checked = false;
            //Toggle_Load_From_Last_load.Checked = false;
        }

        private void Toggle_From_Last_load_CheckedChanged(object sender, EventArgs e)
        {
            //if (firstLoad)
            //    return;

            ////Toggle_RunOffline.Checked = false;
            //Toggle_load_by_DownloadDB.Checked = false;
            //Toggle_Ddiable_LoadSession.Checked = false;
        }

        private void Toggle_DownloadDB_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            firstLoad = true;

            if (Toggle_load_by_DownloadDB.Checked)
                Toggle_Ddiable_LoadSession.Checked=false;
            
            //Toggle_RunOffline.Checked = false;
            //Toggle_Load_From_Last_load.Checked = false;

            if(Toggle_load_by_DownloadDB.Checked)
                Toggle_Ddiable_LoadSession.Checked = false;

            firstLoad =false;   
        }

        private void dgvMicrotikSaved_Paint(object sender, PaintEventArgs e)
        {

        }
        private void dataGridView1_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            if (e.ColumnIndex == 2 && e.RowIndex >= 0)
            {
                e.PaintBackground(e.CellBounds, true);
                TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.RightToLeft | TextFormatFlags.Right);
                e.Handled = true;
            }
        }

        private void dgvMicrotikSaved_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            //if (e.RowIndex >= 0)
            //{
            //    e.PaintBackground(e.CellBounds, true);
            //    TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.Default | TextFormatFlags.VerticalCenter);
            //    e.Handled = true;
            //}

        }

        private void Radio_ByDomain_CheckedChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                firstLoad = true;
                pnl_ips.Visible = false;
                pnl_Smart.Visible = false;
                pnl_domain.Visible = true;



                Radio_ByIP.Checked = false;
                Radio_BySmart.Checked = false;
                Radio_ByDomain.Checked = true;

                rjBy_Port_Domain.Visible = true;
                rjBy_Port_SmartCloud.Visible = false;
                rjBy_Port.Visible = false;



                txtPort_api.Visible = false;
                txtPort_Domain.Visible = false;
                txtPort_SmartCloud.Visible = false;

                if (Frm_State.Is_Use_Port_Domain)
                {
                    txtPort_Domain.Visible = true;
                }
                firstLoad = false;

            }
            //lbl_loginType.Text = "IP";
            //lbl_by_ip.Visible = true;
            //lbl_loginType.Visible = false;

            //if (rjCheckBox_Domain.Checked == true)
            //{
            //    //txtDomain.Visible = true;
            //    pnl_ips.Visible = false;
            //    pnl_domain.Visible = true;
            //    //lbl_loginType.Text = "IP";
            //    lbl_by_ip.Visible = true;
            //    lbl_loginType.Visible = false;

            //}
            //else
            //{
            //    //txtDomain.Visible = false;
            //    pnl_domain.Visible = false;
            //    pnl_ips.Visible = true;
            //    //lbl_loginType.Text = "رابط";
            //    lbl_by_ip.Visible = false;
            //    lbl_loginType.Visible = true;
            //}
        }

        private void Radio_ByIP_CheckedChanged(object sender, EventArgs e)
        {
            if (!firstLoad)
            {
                firstLoad = true;

                pnl_ips.Visible = true;
                pnl_Smart.Visible = false;
                pnl_domain.Visible = false;

                Radio_ByDomain.Checked = false;
                Radio_BySmart.Checked = false;
                Radio_ByIP.Checked = true;

                //rjBy_Port_Domain.Checked = false;
                //rjBy_Port_SmartCloud.Checked = false;
                //rjBy_Port.Checked = true;

                rjBy_Port_Domain.Visible = false;
                rjBy_Port_SmartCloud.Visible = false;
                rjBy_Port.Visible = true;



                //txtPort_api.Text = Frm_State.Mk_Port_api.ToString();

                txtPort_api.Visible = false;
                txtPort_Domain.Visible = false;
                txtPort_SmartCloud.Visible = false;

                if (Frm_State.Is_Use_Port)
                {
                    txtPort_api.Visible = true;
                }
                firstLoad = false;

            }
        }

        private void Radio_BySmart_CheckedChanged(object sender, EventArgs e)
        {    if (!firstLoad)
            {
                firstLoad = true;
                pnl_ips.Visible = false;
                pnl_Smart.Visible = true;
                pnl_domain.Visible = false;


                Radio_ByDomain.Checked = false;
                Radio_ByIP.Checked = false;
                Radio_BySmart.Checked = true;

                //rjBy_Port_Domain.Checked = false;
                //rjBy_Port_SmartCloud.Checked = true;
                //rjBy_Port.Checked = false;


                rjBy_Port_Domain.Visible = false;
                rjBy_Port_SmartCloud.Visible = true;
                rjBy_Port.Visible = false;


                txtPort_SmartCloud.Visible = false;
                txtPort_api.Visible = false;
                txtPort_Domain.Visible = false;

                if (Frm_State.Is_Use_Port_SmartCloud)
                {
                    txtPort_SmartCloud.Visible = true;
                }

                firstLoad = false;
            }
        }





        private void btn_Custom_Login_Click(object sender, EventArgs e)
        {
            //FormCustomLogin f=new FormCustomLogin();
            //f.ShowDialog();
            //return;
            Form_Custom_Login form_Custom_Login = new Form_Custom_Login(Toggle_load_by_DownloadDB.Checked);
            form_Custom_Login.CheckBox_DisableLoad_HSSession.Check = DisableLoad_HSSession;
            form_Custom_Login.CheckBox_DisableLoad_HSUsers.Check = DisableLoad_HSUsers;
            form_Custom_Login.CheckBox_DisableLoad_UmPyment.Check = DisableLoad_UmPyment;
            form_Custom_Login.CheckBox_DisableLoad_UmSession.Check = DisableLoad_UmSession;
            form_Custom_Login.CheckBox_DisableLoad_UmUsers.Check = DisableLoad_UmUsers;

            form_Custom_Login.StartPosition = FormStartPosition.CenterParent;
            //form_Custom_Login.Show(this);
            form_Custom_Login.ShowDialog(this);

            DisableLoad_HSSession = form_Custom_Login.CheckBox_DisableLoad_HSSession.Check;
            DisableLoad_HSUsers = form_Custom_Login.CheckBox_DisableLoad_HSUsers.Check;
            DisableLoad_UmPyment = form_Custom_Login.CheckBox_DisableLoad_UmPyment.Check;
            DisableLoad_UmSession = form_Custom_Login.CheckBox_DisableLoad_UmSession.Check;
            DisableLoad_UmUsers = form_Custom_Login.CheckBox_DisableLoad_UmUsers.Check;


        }

        private void pnlTopTitle_MouseDown(object sender, MouseEventArgs e)
        {
            //ReleaseCapture();
            //SendMessage(this.Handle, 0x112, 0xf012, 0);
        }

        private void rjPanel1_MouseDown(object sender, MouseEventArgs e)
        {
            //ReleaseCapture();
            //SendMessage(this.Handle, 0x112, 0xf012, 0);
        }

        private void lbl_SmartCreator_MouseDown(object sender, MouseEventArgs e)
        {
            //ReleaseCapture();
            //SendMessage(this.Handle, 0x112, 0xf012, 0);
        }

        private void rjButton4_Click(object sender, EventArgs e)
        {
            if (UseMNDP)
            {
                dgvMicrotikSaved.ColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
                List<TikInstanceDescriptor> list = new List<TikInstanceDescriptor>();

                GetNigbour();
            }  //btn_ShowNighboor_Click(sender, e);
            else
                DGV_Refresh();
        }

        private void rjBy_Port_Domain_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            txtPort_Domain.Visible = rjBy_Port_Domain.Checked;
            Frm_State.Is_Use_Port_Domain = rjBy_Port_Domain.Checked;
        }

        private void rjBy_Port_SmartCloud_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            txtPort_SmartCloud.Visible = rjBy_Port_SmartCloud.Checked;
            Frm_State.Is_Use_Port_SmartCloud= rjBy_Port_SmartCloud.Checked;
        }
        private int Id_Select = -1;
        private void btn_Save_Click(object sender, EventArgs e)
        {
            try
            {
                //Font = new Font(btn_Save.Name, btn_Save.Font.Size * 96f / CreateGraphics().DpiX, btn_Save.Font.Style, btn_Save.Font.Unit, btn_Save.Font.GdiCharSet, btn_Save.Font.GdiVerticalFont);
                ////Font = new Font(btn_Save.Name, 8.25f * 96f / CreateGraphics().DpiX, Font.Style, Font.Unit, Font.GdiCharSet, Font.GdiVerticalFont);

                //btn_Save.IconSize = (int)(btn_Save.IconSize * 96f / CreateGraphics().DpiX);
                //RJMessageBox.Show( btn_Save.IconSize.ToString());

                btn_Save.IconSize = 25;


                if (Id_Select == -1) return;

                SaveFormVariable();
                Frm_State.Id = Id_Select;
                //Frm_State.Name = "MK_User";
                //Frm_State.Type = "databases_users";
                string formSetting = JsonConvert.SerializeObject(Frm_State);


                if (SqlDataAccess.AddUser_Login(formSetting, false, Id_Select) == false)
                    RJMessageBox.Show("خطاء");
                RJMessageBox.Show("تم الحفظ");


                //DGV_Refresh();

                //int ID = -1;
                //foreach (DataGridViewRow dr in dgvMicrotikSaved.SelectedRows)
                //{
                //    ID = Convert.ToInt16(dr.Cells["id"].Value);
                //}


                //SourceMk_login_saved sourceMk_Login_Saved = SqlDataAccess.Ge_SourceMk_login_saved(ID);
                //if (sourceMk_Login_Saved != null)
                //{
                //    Form_LoingState frmUser = JsonConvert.DeserializeObject<Form_LoingState>(sourceMk_Login_Saved.values.ToString());
                //    loadData(frmUser);
                //}
            }
            catch { }

        }

        private void Toggle_Custom_Login_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            if (Toggle_Custom_Login.Checked)
            {
                firstLoad = true;
                Toggle_Ddiable_LoadSession.Checked = false;
                firstLoad=false;
            }
        }

        private void LoginForm_MouseDown(object sender, MouseEventArgs e)
        {
            return;
        }
    }





    public class Old_DatabaseInfo
    {
        public bool ForceUpdate { get; set; } = false;
        public string Path { get; set; }
        public double CurrentVersion { get; set; } = 8.02;
        public List< Old_Router> Routers { get; set; } =new List<Old_Router>();
    }
    public class Old_Router
    {
        public string Rb_sn { get; set; }
        public string Rb_code { get; set; }
        public bool ForceUpdate { get; set; } = false;
    }
}
