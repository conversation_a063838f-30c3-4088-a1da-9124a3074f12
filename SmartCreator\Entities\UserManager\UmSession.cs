﻿//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;

//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ToolTip;

namespace SmartCreator.Entities.UserManager
{


    //CREATE INDEX UmSession_idx_bb530fdb ON UmSession(DeleteFromServer, Sn);
    //CREATE INDEX UmSession_idx_adaa03a3 ON UmSession(NasPortId , FromTime);
    //CREATE INDEX UmSession_idx_ddd70e93 ON UmSession(DeleteFromServer, Fk_Sn_Name);
    //CREATE INDEX UmSession_idx_6d4dd0a5 ON UmSession(CallingStationId, Fk_Sn_Name DESC);

    //CREATE INDEX UmSession_idx_6cd2fe2d ON UmSession(Fk_Sn_Name, Sn DESC);



    [CompositeIndex("DeleteFromServer", "Sn")]
    [CompositeIndex("NasPortId", "FromTime")]
    [CompositeIndex("DeleteFromServer", "Fk_Sn_Name")]
    [CompositeIndex("CallingStationId", "Fk_Sn_Name DESC")]
    [CompositeIndex("Fk_Sn_Name", "Sn DESC")]

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class UmSession: BaseSession
    {
        public UmSession() { }

        //[PrimaryKey, AutoIncrement,Unique, Required]
        //public int? Id { get; set; }
        [Browsable(false), Index]
        public long Sn { get; set; }
        
        [StringLength(50)]
        public string IdHX { get; set; }

        [Default(0), Browsable(false)]
        public int Active { get; set; }=0;
       
        [PrimaryKey,Required, Unique, StringLength(250)/*,Browsable(false)*/]
        public string Sn_Name { get; set; }

        [Browsable(false)]
        public int Status { get; set; } = 0;

        //[StringLength(150), Browsable(false)]

        //[/*ForeignKey(typeof(UmUser), OnDelete = "CASCADE"),*/ 
            
        [ForeignKey(typeof(UmUser), OnDelete = "CASCADE", ColumnName = "Sn_Name"), Index]
        public string Fk_Sn_Name { get; set; }

        //[Browsable(false)]
        //[ForeignKey(typeof(UmUser), OnDelete = "CASCADE"),Index]
        //public int UmUserId { get; set; }

        //[Computed,Browsable(false)]
        //public  UmUser UmUser { get; set; }

    }
}
