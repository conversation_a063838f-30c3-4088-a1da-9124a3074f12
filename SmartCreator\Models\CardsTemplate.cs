﻿using Newtonsoft.Json;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Data;

namespace SmartCreator.Models
{
    public class SourceCardsTemplate //from db
    {
        public int id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public string values { get; set; }
        public string rb { get; set; }

        public override string ToString()
        {
            return name;
        }
        //public CardsSetting setingCard = new CardsSetting();
        //public CardsItems cardsItems = new CardsItems();
        //public string type { get; set; } = "design";

      public bool CreateDefaultTemplate(bool from_loadBackup= false)
        {
            try
            {
                CardsTableDesg1 card = new CardsTableDesg1();

                card.name = "Table_Default";
                card.type = "table_Desigen1";
                card.setingCard.NumberCulum = 3;
                card.setingCard.card_border_Size = .5f;
                card.setingCard.Fixed_Width_Card = false;
                card.setingCard.Show_border_Midell = true;

                card = new CardsTableDesg1();
                card.cardsItems.info1.title_text = "شبكة لاسليكة";
                card.cardsItems.info1.Enable = true;

                card.cardsItems.login.title_text = "اسم الدخول";
                card.cardsItems.login.title_show = true;
                card.cardsItems.login.Enable = true;

                card.cardsItems.Password.title_text = "كلمة المرور";
                //card.cardsItems.Password.title_show = true;
                //card.cardsItems.Password.Enable = false;

                card.cardsItems.Time.title_text = "الوقت";
                card.cardsItems.Time.title_show = true;
                card.cardsItems.Time.Enable = true;
                card.cardsItems.Time.unit_show = true;
                card.cardsItems.Time.unit_format = 0;

                card.cardsItems.Price.title_text = "السعر";
                card.cardsItems.Price.title_show = true;
                card.cardsItems.Price.Enable = true;
                card.cardsItems.Price.unit_show = true;


                card.cardsItems.Validity.title_text = "الصلاحية";
                card.cardsItems.Validity.title_show = true;
                card.cardsItems.Validity.Enable = false;
                card.cardsItems.Validity.unit_show = true;
                card.cardsItems.Validity.unit_format = 0;

                card.cardsItems.Size.title_text = "كمية التحميل";
                card.cardsItems.Size.title_show = true;
                card.cardsItems.Size.Enable = true;
                card.cardsItems.Size.unit_format = 0;

                card.cardsItems.SP.title_text = "نقطة البيع";
                card.cardsItems.SP.title_show = true;
                card.cardsItems.SP.Enable = false;
                card.cardsItems.SP.unit_format = 0;

                card.cardsItems.SN.title_text = "التسلسل";
                card.cardsItems.SN.title_show = true;
                card.cardsItems.SN.Enable = false;

                card.cardsItems.Date_Print.title_text = "تاريخ الطباعة";
                card.cardsItems.Date_Print.title_show = true;
                card.cardsItems.Date_Print.Enable = false;
                card.cardsItems.Date_Print.unit_format = 0;

                card.cardsItems.Number_Print.title_text = "رقم الطبعة";
                card.cardsItems.Number_Print.title_show = true;
                card.cardsItems.Number_Print.Enable = false;       
                
                card.cardsItems.BatchNumber.title_text = "رقم الدفعة";
                card.cardsItems.BatchNumber.title_show = true;
                card.cardsItems.BatchNumber.Enable = false;

                card.cardsItems.info3.title_text = "للتواصل والاستفسار الاتصال علي:7777777";
                card.cardsItems.info3.title_show = true;
                card.cardsItems.info3.Enable = true;

                card.cardsItems.info4.title_text = "اعلان 1";
                card.cardsItems.info4.title_show = true;
                card.cardsItems.info4.Enable = false;

                SourceCardsTemplate sourceCardTemplate = new SourceCardsTemplate();

                sourceCardTemplate.name = "default";
                sourceCardTemplate.type = "table_Desigen1";
                sourceCardTemplate.values = JsonConvert.SerializeObject(card);
                if (SqlDataAccess.Add_New_Template(sourceCardTemplate, true,from_loadBackup))
                {
                    return true;
                }
                else
                   return false;
            }
            catch {/* MessageBox.Show("save_template_To_DB" + "\n" + ex.Message.ToString());*/ }
            return false;
        }



    }
    public class CardsTemplate
    {
        //public CardsTemplate() {
        [PrimaryKey,AutoIncrement,Unique,Required]
        public int id { get; set; }
        public string name { get; set; }
       
        public CardsSetting setingCard = new CardsSetting();
        public CardsItems cardsItems = new CardsItems();
        public string type { get; set; } = "design";
        public string rb { get; set; }

    }

    public class CardsSetting
    {
        public string name { get; set; }
        public string type { get; set; }

        public bool enable_background { get; set; } = false;
        public string path_saved_file { get; set; } = "";
        public string path_background { get; set; } = "";
        public int quilty_image { get; set; } = 0;
        public string currency { get; set; } = "RY";
        public decimal card_width { get; set; } = 68;
        public decimal card_height { get; set; } = 16;
        public decimal space_horizontal_margin { get; set; } = 1;
        public decimal Space_vertical_margin { get; set; } = 1;
        public bool card_border_enable { get; set; } = false;
        public float card_border_Size { get; set; } = 0.5f;
        public string card_border_Color { get; set; } = "black";
        public string proile_link { get; set; } = "";
        public string proile_HS_link { get; set; } = "";
        public string proile_HS_Local_link { get; set; } = "";
        //public string type { get; set; } = "cards_template";
        public decimal Number_Pages_X { get; set; } = 10;
        public decimal Number_Pages_Y { get; set; } = 5;
        public decimal Number_Pages_Size { get; set; } = 6;
        public bool Number_Pages { get; set; } = true;

        public bool Note_On_Pages { get; set; } = true;
        public decimal Note_On_Pages_X { get; set; } = 40;
        public decimal Note_On_Pages_Y { get; set; } = 5;
        public string Note_On_Pages_text { get; set; } = "";
        public decimal Note_On_Pages_Size { get; set; } = 6;
        public int NoteType_onPage { get; set; } = 0;



    }

    public class PropertyItme
    {
        public bool Enable { get; set; } = false;
        public decimal x { get; set; } = 30;
        public decimal y { get; set; } = 8;

    }

    public class PropertyItemText : PropertyItme
    {
        public int font_size { get; set; } = 10;
        public bool Blod { get; set; } = false;
        public string Font { get; set; } = "arial";
        public string Color { get; set; } = "black";
        public bool italic { get; set; } = false;
        public bool title_show { get; set; } = true;
        public string title_text { get; set; } = "";
        public bool unit_show { get; set; } = true;
        public int unit_format { get; set; } = 0;//بالايام-0  بالاسابيع1  بالشهور2
        public string title_Color { get; set; } = "black";

    }

    public class PropertyItemImage : PropertyItme
    {
        public decimal item_dimension_w { get; set; } = 12;
        public decimal item_dimension_y { get; set; } = 12;
        public string url { get; set; }= "http://s.net/login";
        //public decimal item_width { get; set; }
        //public decimal item_height { get; set; }
    }

    public class Date_Print : PropertyItemText
    {
        public string format { get; set; } = "dd/MM/yyyy";
    }
    public class Selling_PointTemplate : PropertyItemText
    {
        public bool Show_ByNumber_OR_Name { get; set; } = false;
    }
    public class Logo : PropertyItemImage
    {
        public string Path { get; set; } = "";
    }

    public class format_Validity : PropertyItemText
    {

        public string day { get; set; } = "يوم";
        public string week { get; set; } = "اسبوع";
        public string month { get; set; } = "شهر";
        //public Validity_Uint_format week { get; set; } = new Validity_Uint_format {name="اسابيع",shortcut="w" };
        //public Validity_Uint_format month { get; set; } = new Validity_Uint_format {name="شهر",shortcut="m" };
    }
    public class format_Time : PropertyItemText
    {

        public string miniute { get; set; } = "دقائق";
        public string hour { get; set; } = "ساعة";
        public string day { get; set; } = "يوم";
    
    }
    public class format_Size : PropertyItemText
    {

        public string miga { get; set; } = "ميجا";
        public string giga { get; set; } = "جيجا";

    }

    //public class Validity_Uint_format
    //{
    //    public string name { get; set; } = "يوم";
    //    public string shortcut { get; set; } = "d";
    //}
    public class CardsItems
    {
        public PropertyItemText SN { get; set; } = new PropertyItemText();
        public PropertyItemText login { get; set; } = new PropertyItemText();
        public PropertyItemText Password { get; set; } = new PropertyItemText();
        public format_Time Time { get; set; } = new format_Time();
        public format_Size Size { get; set; } = new format_Size();
        public format_Validity Validity { get; set; } = new format_Validity();

        //public PropertyItemText Validity { get; set; } = new PropertyItemText();
        public Selling_PointTemplate SP { get; set; } = new Selling_PointTemplate();
        //public string loc_SP_name_or_Number {  get; set; }
        public PropertyItemText Price { get; set; } = new PropertyItemText();
        public PropertyItemText Other_Text1 { get; set; } = new PropertyItemText();
        public PropertyItemText Other_Text2 { get; set; } = new PropertyItemText();
        public Date_Print Date_Print { get; set; } = new Date_Print();
        public PropertyItemText Number_Print { get; set; } = new PropertyItemText();
        public PropertyItemText BatchNumber { get; set; } = new PropertyItemText();
        public Logo logo { get; set; } = new Logo();
        //public ProprtyItemImage logo2 { get; set; } = new ProprtyItemImage();
        //public ProprtyItemImage logo3 { get; set; } = new ProprtyItemImage();
        public PropertyItemImage QR { get; set; } = new PropertyItemImage();
        public Logo back1 { get; set; } = new Logo();

    }
    public class CardsSettingForTable : CardsSetting
    {
        public int NumberCulum { get; set; } = 3;
        public bool Fixed_Width_Card { get; set; } = false;
        public bool Show_border_Midell { get; set; } = true;
        public bool Show_Border_Row { get; set; } = false;
        public bool Show_Border_Coulum { get; set; } = false;
        public float size_Border_Midell { get; set; } = .5f;
        public float Padding_Cell { get; set; } = 2.5f;

    }
    public class CardsTableDesg1
    {
        public string name { get; set; }
        public CardsSettingForTable setingCard = new CardsSettingForTable();
        public ItemsCardTableDesg1 cardsItems = new ItemsCardTableDesg1();
        public string type { get; set; } = "table_Desigen1";



    }
    public class ItemsCardTableDesg1 : CardsItems
    {
        public PropertyItemText info1 { get; set; } = new PropertyItemText();
        public PropertyItemText info2 { get; set; } = new PropertyItemText();
        public PropertyItemText info3 { get; set; } = new PropertyItemText();
        public PropertyItemText info4 { get; set; } = new PropertyItemText();
        public PropertyItemText info5 { get; set; } = new PropertyItemText();
    }
    public class CardTableDesg1Setting
    {
        public bool    enable_background { get; set; } = false;
        public string  path_saved_file { get; set; } = "";
        public string  path_background { get; set; } = "";
        public int     quilty_image { get; set; } = 0;
        public string  currency { get; set; } = "RY";
        public decimal card_width { get; set; } = 68;
        public decimal card_height { get; set; } = 16;
        public decimal space_horizontal_margin { get; set; } = 1;
        public decimal Space_vertical_margin { get; set; } = 1;
        public bool    card_border_enable { get; set; } = false;
        public int     card_border_Size { get; set; } = 1;
        public string  card_border_Color { get; set; } = "black";
        public string  proile_link { get; set; } = "";
        public string  proile_HS_link { get; set; } = "";
        public string  type { get; set; } = "cards_table";
        public decimal Number_Pages_X { get; set; } = 10;
        public decimal Number_Pages_Y { get; set; } = 5;
        public decimal Number_Pages_Size { get; set; } = 6;
        public bool    Number_Pages { get; set; } = true;

        public bool    Note_On_Pages { get; set; } = true;
        public decimal Note_On_Pages_X { get; set; } = 40;
        public decimal Note_On_Pages_Y { get; set; } = 5;
        public string  Note_On_Pages_text { get; set; } = "";
        public decimal Note_On_Pages_Size { get; set; } = 6;
        public int     NoteType_onPage { get; set; } = 0;


    }




}
