﻿using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.SSHClient;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Devices
{
    public partial class Form_OpentWrt_Manage : RJChildForm
    {
        readonly ClientManager clientManager = new ClientManager();

        public Form_OpentWrt_Manage()
        {
            InitializeComponent();
            set_font();
            txtHost.Text = "*********";
            txtUsername.Text = "root";
            txtPassword.Text = "admin.123";
            txtPort.Text = "22";
            txtLog.TextAlign= HorizontalAlignment.Left;
            CBox_Device_Type.SelectedIndex = 0;
            txt_ssid_2g.Text = ".364-Smart-Net-79090937";
            txt_ssid_5g.Text = ".1364-Smart-H-Net-79090937";

        }

        void set_font()
        {
            //Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 10*utils.ScaleFactor, FontStyle.Bold);
            utils.Control_textSize(pnlClientArea);
        }

        private void rjLabel9_Click(object sender, EventArgs e)
        {

        }

        private void btnConnect_Click(object sender, EventArgs e)
        {
            clientManager.Connect(txtHost.Text, txtUsername.Text, txtPassword.Text, int.Parse(txtPort.Text));

            txtLog.Text += clientManager.IsConnected ? "Connected." : clientManager.Message;
            txtLog.Text += Environment.NewLine;
        }
        private void btnDisconnect_Click(object sender, EventArgs e)
        {
            clientManager.Disconnect();
        }
        private void tabControl1_DrawItem(object sender, DrawItemEventArgs e)
        {
            Font fntTab;
            Brush bshBack;
            Brush bshFore;
            if (e.Index == this.tabControl1.SelectedIndex)
            {
                fntTab = new Font(e.Font, FontStyle.Bold);
                bshBack = new System.Drawing.Drawing2D.LinearGradientBrush(e.Bounds, Color.LightSkyBlue, Color.LightGreen, System.Drawing.Drawing2D.LinearGradientMode.BackwardDiagonal);
                bshFore = Brushes.Blue;
            }
            else
            {
                fntTab = e.Font;
                bshBack = new SolidBrush(Color.White);
                bshFore = new SolidBrush(Color.Black);
            }
            string tabName = this.tabControl1.TabPages[e.Index].Text;
            StringFormat sftTab = new StringFormat(StringFormatFlags.NoClip);
            sftTab.Alignment = StringAlignment.Center;
            sftTab.LineAlignment = StringAlignment.Center;
            e.Graphics.FillRectangle(bshBack, e.Bounds);
            Rectangle recTab = e.Bounds;
            recTab = new Rectangle(recTab.X, recTab.Y + 4, recTab.Width, recTab.Height - 4);
            e.Graphics.DrawString(tabName, fntTab, bshFore, recTab, sftTab);

        }

        private void Form_OpentWrt_Manage_FormClosing(object sender, FormClosingEventArgs e)
        {
            clientManager.Disconnect();
        }

        private void chkBoxAuto_CheckedChanged(object sender, EventArgs e)
        {
            timer1.Enabled = chkBoxAuto.Checked;
            //btnSend.Enabled = !chkBoxAuto.Checked;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            if (clientManager.IsConnected)
            {
                DataModel data = clientManager.GetData();

                if (int.TryParse(data.Signal, out int signal))
                {
                    cirPbSignal.Value = signal + 100;
                    cirPbSignal.Text = data.Signal;
                }

                if (int.TryParse(data.TX_Bitrate, out int txBitrate))
                {
                    cirPbTXBitrate.Value = txBitrate;
                    cirPbTXBitrate.Text = data.TX_Bitrate;
                }

                if (int.TryParse(data.RX_Bitrate, out int rxBitrate))
                {
                    cirPbRXBitrate.Value = rxBitrate;
                    cirPbRXBitrate.Text = data.RX_Bitrate;
                }
            }

        }
    }

}
