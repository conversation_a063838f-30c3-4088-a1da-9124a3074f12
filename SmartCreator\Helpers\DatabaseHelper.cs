﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Helpers
{
    public class DatabaseHelper
    {
        private readonly string _connectionString;
        private readonly string _databasePath;

        public DatabaseHelper()
        {
            _databasePath = ConfigurationManager.AppSettings["DatabasePath"] ?? "MikroTikManager.db";
            _connectionString = $"Data Source={_databasePath};Version=3;";
        }

        public string ConnectionString => _connectionString;

        /// <summary>
        /// الحصول على connection string بطريقة static (قاعدة البيانات الافتراضية)
        /// </summary>
        public static string GetConnectionString()
        {
            var databasePath = ConfigurationManager.AppSettings["DatabasePath"] ?? "MikroTikManager.db";
            return $"Data Source={databasePath};Version=3;";
        }

        /// <summary>
        /// الحصول على connection string لقاعدة البيانات المحلية LocalDB.db
        /// </summary>
        public static string GetLocalDbConnectionString()
        {
            return ConfigurationManager.ConnectionStrings["LocalDbConnection"].ConnectionString;
        }

        /// <summary>
        /// تنفيذ Migration لإضافة عمود StatusCode
        /// </summary>
        public static void RunStatusColumnMigration()
        {
            try
            {
                var connectionString = GetLocalDbConnectionString();
                using (var connection = new SQLiteConnection(connectionString))
                {
                    connection.Open();

                    // التحقق من وجود العمود أولاً
                    var checkColumnQuery = "PRAGMA table_info(HSUsers)";
                    using (var checkCommand = new SQLiteCommand(checkColumnQuery, connection))
                    {
                        using (var reader = checkCommand.ExecuteReader())
                        {
                            bool statusCodeColumnExists = false;
                            while (reader.Read())
                            {
                                if (reader["name"].ToString() == "StatusCode")
                                {
                                    statusCodeColumnExists = true;
                                    break;
                                }
                            }

                            // إذا كان العمود غير موجود، أضفه
                            if (!statusCodeColumnExists)
                            {
                                reader.Close();

                                // قراءة وتنفيذ Migration
                                var migrationPath = Path.Combine(Application.StartupPath, "DAL", "Migrations", "AddStatusColumn.sql");
                                if (File.Exists(migrationPath))
                                {
                                    var migrationSql = File.ReadAllText(migrationPath);
                                    var commands = migrationSql.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                                    foreach (var commandText in commands)
                                    {
                                        var trimmedCommand = commandText.Trim();
                                        if (!string.IsNullOrEmpty(trimmedCommand) && !trimmedCommand.StartsWith("--"))
                                        {
                                            using (var command = new SQLiteCommand(trimmedCommand, connection))
                                            {
                                                command.ExecuteNonQuery();
                                            }
                                        }
                                    }

                                    System.Diagnostics.Debug.WriteLine("✅ تم تنفيذ Migration لإضافة عمود StatusCode بنجاح");
                                }
                                else
                                {
                                    // إضافة العمود يدوياً إذا لم يوجد ملف Migration
                                    var addColumnSql = "ALTER TABLE HSUsers ADD COLUMN StatusCode INTEGER DEFAULT 0";
                                    using (var command = new SQLiteCommand(addColumnSql, connection))
                                    {
                                        command.ExecuteNonQuery();
                                    }

                                    System.Diagnostics.Debug.WriteLine("✅ تم إضافة عمود StatusCode يدوياً");
                                }
                            }
                            else
                            {
                                System.Diagnostics.Debug.WriteLine("ℹ️ عمود StatusCode موجود بالفعل");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنفيذ Migration: {ex.Message}");
                throw;
            }
        }

        public void InitializeDatabase()
        {
            try
            {
                if (!File.Exists(_databasePath))
                {
                    SQLiteConnection.CreateFile(_databasePath);
                }

                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();
                    CreateTables(connection);
                    UpdateExistingTables(connection);
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void CreateTables(SQLiteConnection connection)
        {
            // جدول الراوترات
            var createRoutersTable = @"
                CREATE TABLE IF NOT EXISTS Routers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    Name TEXT NOT NULL,
                    IpAddress TEXT NOT NULL,
                    Port INTEGER DEFAULT 8728,
                    Username TEXT NOT NULL,
                    Password TEXT NOT NULL,
                    ConnectionType TEXT NOT NULL CHECK(ConnectionType IN ('API', 'SSH')),
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    LastConnected DATETIME,
                    SystemInfo TEXT,
                    LicenseInfo TEXT,
                    RouterBoardInfo TEXT,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                );";

            // جداول الهوتسبوت
            var createHotspotServersTable = @"
                CREATE TABLE IF NOT EXISTS HotspotServers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    ServerId TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    Interface TEXT,
                    AddressPool TEXT,
                    Profile TEXT,
                    IdleTimeout TEXT,
                    KeepaliveTimeout TEXT,
                    LoginTimeout TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createHotspotProfilesTable = @"
                CREATE TABLE IF NOT EXISTS HotspotProfiles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    ProfileId TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    HtmlDirectory TEXT,
                    HttpProxy TEXT,
                    HttpCookieLifetime TEXT,
                    SmtpServer TEXT,
                    SplitUserDomain TEXT,
                    UseRadius INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createHotspotUsersTable = @"
                CREATE TABLE IF NOT EXISTS HotspotUsers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    UserId TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    Password TEXT,
                    Profile TEXT,
                    Server TEXT,
                    Comment TEXT,
                    Disabled INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            // جدول HSUsers المحسن للنظام الجديد
            var createHSUsersTable = @"
                CREATE TABLE IF NOT EXISTS HSUsers (
                    Sn_Name TEXT PRIMARY KEY NOT NULL,
                    SN INTEGER NOT NULL,
                    Id TEXT,
                    Name TEXT NOT NULL,
                    Password TEXT,
                    Profile TEXT,
                    Server TEXT,
                    Comment TEXT,
                    Disabled INTEGER DEFAULT 0,
                    LimitUptime INTEGER DEFAULT 0,
                    LimitBytesIn INTEGER DEFAULT 0,
                    LimitBytesOut INTEGER DEFAULT 0,
                    LimitBytesTotal INTEGER DEFAULT 0,
                    UptimeSeconds INTEGER DEFAULT 0,
                    BytesIn INTEGER DEFAULT 0,
                    BytesOut INTEGER DEFAULT 0,
                    PacketsIn INTEGER DEFAULT 0,
                    PacketsOut INTEGER DEFAULT 0,
                    IsDeletedFromRouter INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
                ) WITHOUT ROWID;";

            // جداول اليوزمنجر
            var createUMProfilesTable = @"
                CREATE TABLE IF NOT EXISTS UMProfiles (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    ProfileId TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    NameForUsers TEXT,
                    OverrideSharedUsers TEXT,
                    Price TEXT,
                    StartsAt TEXT,
                    ExpiresAfter TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMLimitationsTable = @"
                CREATE TABLE IF NOT EXISTS UMLimitations (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    LimitationId TEXT NOT NULL,
                    Name TEXT NOT NULL,
                    LimitationType TEXT,
                    LimitationValue TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMProfileLimitationsTable = @"
                CREATE TABLE IF NOT EXISTS UMProfileLimitations (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    ProfileId TEXT NOT NULL,
                    LimitationId TEXT NOT NULL,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMUsersTable = @"
                CREATE TABLE IF NOT EXISTS UMUsers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    UserId TEXT NOT NULL,
                    Customer TEXT,
                    Username TEXT NOT NULL,
                    Password TEXT,
                    SharedUsers TEXT,
                    Disabled INTEGER DEFAULT 0,
                    ActualProfile TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMCustomersTable = @"
                CREATE TABLE IF NOT EXISTS UMCustomers (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    CustomerId TEXT NOT NULL,
                    Login TEXT NOT NULL,
                    Password TEXT,
                    Signup TEXT,
                    TimeLeft TEXT,
                    Disabled INTEGER DEFAULT 0,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMPaymentsTable = @"
                CREATE TABLE IF NOT EXISTS UMPayments (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    PaymentId TEXT NOT NULL,
                    Customer TEXT,
                    Gateway TEXT,
                    TransactionId TEXT,
                    Amount TEXT,
                    DateTime TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            var createUMSessionsTable = @"
                CREATE TABLE IF NOT EXISTS UMSessions (
                    Id INTEGER PRIMARY KEY AUTOINCREMENT,
                    RouterId INTEGER NOT NULL,
                    SessionId TEXT NOT NULL,
                    Customer TEXT,
                    Username TEXT,
                    NasIpAddress TEXT,
                    CallingStationId TEXT,
                    CalledStationId TEXT,
                    AcctStartTime TEXT,
                    AcctStopTime TEXT,
                    AcctSessionTime TEXT,
                    AcctInputOctets TEXT,
                    AcctOutputOctets TEXT,
                    IsActive INTEGER DEFAULT 1,
                    IsDeleted INTEGER DEFAULT 0,
                    CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (RouterId) REFERENCES Routers(Id)
                );";

            // تنفيذ إنشاء الجداول
            using (var command = new SQLiteCommand(createRoutersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createHotspotServersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createHotspotProfilesTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createHotspotUsersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createHSUsersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMProfilesTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMLimitationsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMProfileLimitationsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMUsersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMCustomersTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMPaymentsTable, connection))
                command.ExecuteNonQuery();

            using (var command = new SQLiteCommand(createUMSessionsTable, connection))
                command.ExecuteNonQuery();

            // إنشاء الفهارس لتحسين الأداء
            CreateIndexes(connection);
        }

        private void CreateIndexes(SQLiteConnection connection)
        {
            var indexes = new[]
            {
                "CREATE INDEX IF NOT EXISTS idx_routers_ipaddress ON Routers(IpAddress);",
                "CREATE INDEX IF NOT EXISTS idx_hotspot_servers_router ON HotspotServers(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_hotspot_profiles_router ON HotspotProfiles(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_hotspot_users_router ON HotspotUsers(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_profiles_router ON UMProfiles(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_limitations_router ON UMLimitations(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_profile_limitations_router ON UMProfileLimitations(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_users_router ON UMUsers(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_customers_router ON UMCustomers(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_payments_router ON UMPayments(RouterId);",
                "CREATE INDEX IF NOT EXISTS idx_um_sessions_router ON UMSessions(RouterId);"
            };

            foreach (var indexSql in indexes)
            {
                using (var command = new SQLiteCommand(indexSql, connection))
                    command.ExecuteNonQuery();
            }
        }

        private void UpdateExistingTables(SQLiteConnection connection)
        {
            try
            {
                // التحقق من وجود عمود IsDeleted في جدول Routers
                var checkColumnSql = @"
                    SELECT COUNT(*) as count
                    FROM pragma_table_info('Routers')
                    WHERE name='IsDeleted'";

                using (var command = new SQLiteCommand(checkColumnSql, connection))
                {
                    var count = Convert.ToInt32(command.ExecuteScalar());

                    if (count == 0)
                    {
                        // إضافة عمود IsDeleted إذا لم يكن موجوداً
                        var addColumnSql = "ALTER TABLE Routers ADD COLUMN IsDeleted INTEGER DEFAULT 0";
                        using (var addCommand = new SQLiteCommand(addColumnSql, connection))
                        {
                            addCommand.ExecuteNonQuery();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                // في حالة فشل التحديث، نتجاهل الخطأ لأن الجدول قد يكون جديداً
                System.Diagnostics.Debug.WriteLine($"تحديث قاعدة البيانات: {ex.Message}");
            }
        }
    }
}
