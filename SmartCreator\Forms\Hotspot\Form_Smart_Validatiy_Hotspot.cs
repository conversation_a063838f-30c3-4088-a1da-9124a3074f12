﻿using iTextSharp.text.pdf;
using Renci.SshNet;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.IO;
using System.Threading;
using System.Windows;
using System.Windows.Forms;
using tik4net;
using FontStyle = System.Drawing.FontStyle;
using MessageBox = System.Windows.Forms.MessageBox;
using Point = System.Drawing.Point;

namespace SmartCreator.Forms.Hotspot
{
    public partial class Form_Smart_Validatiy_Hotspot : RJChildForm
    {
        public Form_Smart_Validatiy_Hotspot()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }


            lbl_Title1.Font =
                lbl_descr2.Font =
                lbl_Title1.Font =
                rjLabel1.Font =
                Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);

            lbl_descr.Font =
                rjLabel8.Font =
                rjLabel3.Font =
                lbl_Save_session.Font =
                rjLabel5.Font =
                rjLabel6.Font =
                rjLabel2.Font =
                rjLabel7.Font =
                rjLabel4.Font =
                lbl_DayOrHour.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            btn_AddScript_toMickrotik.Font= btn_Upgrade.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);
            lbl_note.Font= Program.GetCustomFont(Resources.DroidSansArabic, 12f, FontStyle.Regular);
            init_dgv();

            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, System.Drawing.FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = 35;


            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
        }

        

        DataTable dt = new DataTable();
        private void init_dgv()
        {
            //DataTable dt = new DataTable();
            dt.Columns.Add("البروفايل");
            dt.Columns.Add("الحالة");
            dt.Columns.Add("onlogin");
            dt.Columns.Add("onlogout");
            dt.Columns.Add("idx");

            DataGridViewLinkColumn links_ReAdd = new DataGridViewLinkColumn();
            links_ReAdd.UseColumnTextForLinkValue = false;
            links_ReAdd.DataPropertyName = "install";
            links_ReAdd.Name = "install";
            links_ReAdd.HeaderText = "تثبيت";
            links_ReAdd.Text = "تثبيت";
            //links_ReAdd.ActiveLinkColor = Color.Black;
            //links_ReAdd.LinkBehavior = LinkBehavior.SystemDefault;
            //links_ReAdd.LinkColor = Color.Blue;
            //links_ReAdd.TrackVisitedState = true;
            //links_ReAdd.VisitedLinkColor = Color.Red;

            DataGridViewLinkColumn links_Remove = new DataGridViewLinkColumn();
            links_Remove.UseColumnTextForLinkValue = false;
            links_Remove.DataPropertyName = "remove";
            links_Remove.Name = "remove";
            links_Remove.HeaderText = "الغاء";
            links_Remove.Text = "تثبيت";

            //Font font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9 * utils.ScaleFactor, System.Drawing.FontStyle.Regular);
            //links_ReAdd.DefaultCellStyle.Font = font;


            dgv.DataSource = dt;
            dgv.Columns.Add(links_ReAdd);
            dgv.Columns.Add(links_Remove);

            


        }

        [Obsolete]
        private void Form_Smart_Validatiy_Hotspot_Load(object sender, EventArgs e)
        {

            //loadData();
            timer1.Start();
            return;
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            List<Hotspot_Source_Profile> profile = new List<Hotspot_Source_Profile>();
            profile = mk_DataAccess.GetProfileHotspot();

            TableLayoutPanel tableLayoutPanel = new TableLayoutPanel();
            tableLayoutPanel.Name = "table_Profile";
            tableLayoutPanel.RightToLeft = RightToLeft.Yes;
            tableLayoutPanel.BackColor = Color.Cyan;

            tableLayoutPanel.RowCount = profile.Count;
            tableLayoutPanel.ColumnCount = 4;
            tableLayoutPanel.Location = new Point(19, 17);
            tableLayoutPanel.Width = 893;
            tableLayoutPanel.Height = 90 * profile.Count;

            tableLayoutPanel.Anchor = AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom | AnchorStyles.Top;
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25f));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25f));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25f));
            tableLayoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 25f));




            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.Cairo_Medium, 12 * utils.ScaleFactor, FontStyle.Bold);

            int i = 2;
            //tableLayoutPanel.RowCount = profile.Count + 1;

            foreach (Hotspot_Source_Profile p in profile)
            {
                Label lbl = new Label();
                lbl.Text = p.Name;
                lbl.Font = lbl_font;
                lbl.RightToLeft = RightToLeft.Yes;
                tableLayoutPanel.Controls.Add(lbl, 0, i);

                RJLabel rjLbl_status = new RJLabel();
                rjLbl_status.Text = "غير مضاف";
                rjLbl_status.RightToLeft = RightToLeft.Yes;

                rjLbl_status.LinkLabel = true;
                rjLbl_status.Font = lbl_font;
                tableLayoutPanel.Controls.Add(rjLbl_status, 1, i);



                RJLabel rJLabel = new RJLabel();
                rJLabel.Text = "اضافة";
                rJLabel.RightToLeft = RightToLeft.Yes;

                rJLabel.Font = lbl_font;
                rJLabel.LinkLabel = true;
                rJLabel.LiveSetting = System.Windows.Forms.Automation.AutomationLiveSetting.Polite;
                tableLayoutPanel.Controls.Add(rJLabel, 2, i);

                RJLabel _lbl_reAdd = new RJLabel();
                _lbl_reAdd.Text = "اعادة اضافة";
                _lbl_reAdd.RightToLeft = RightToLeft.Yes;

                _lbl_reAdd.Font = lbl_font;
                _lbl_reAdd.LinkLabel = true;
                _lbl_reAdd.LiveSetting = System.Windows.Forms.Automation.AutomationLiveSetting.Polite;
                tableLayoutPanel.Controls.Add(_lbl_reAdd, 3, i);

                i++;
            }

        }

        [Obsolete]
        private void loadData()
        {

            //DataGridViewLinkColumn links_ReAdd = new DataGridViewLinkColumn();
            //links_ReAdd.UseColumnTextForLinkValue = true;
            //links_ReAdd.DataPropertyName = "install";
            //links_ReAdd.HeaderText = "الحدث"; 
            //links_ReAdd.Text = "تثبيت";
            //links_ReAdd.ActiveLinkColor = Color.Black;
            //links_ReAdd.LinkBehavior = LinkBehavior.SystemDefault;
            //links_ReAdd.LinkColor = Color.Blue;
            //links_ReAdd.TrackVisitedState = true;
            //links_ReAdd.VisitedLinkColor = Color.Red;
            //Font font=Program.GetCustomFont(Resources.DroidKufi_Regular,9*utils.ScaleFactor,System.Drawing.FontStyle.Regular);
            //links_ReAdd.DefaultCellStyle.Font = font;

            //dgv.DataSource = dt;

            //dt.Columns.Add("اضافة سكربت الصلاحية");
            //dt.Columns.Add("اعادة التثبيت");

            //DataGridViewLinkColumn links_add = new DataGridViewLinkColumn();


            //links_add.UseColumnTextForLinkValue = true;
            //links_add.HeaderText = "تثبيت "; links_add.Text = "تثبيت ";
            //links_add.DataPropertyName = "btnAdd";
            ////links_add.ActiveLinkColor = Color.Black;
            ////links_add.LinkBehavior = LinkBehavior.SystemDefault;
            //links_add.LinkColor = Color.Blue;
            ////links_add.TrackVisitedState = true;
            //links_add.VisitedLinkColor = Color.Red;

            //dgv.Columns.Add(links);




            //DataGridViewLinkColumn AddButton = new DataGridViewLinkColumn();
            ////DataGridViewButtonColumn EditButton = new DataGridViewButtonColumn();
            ////EditButton.FlatStyle = FlatStyle.Popup;
            //AddButton.Name = "Add";
            ////EditButton.UseColumnTextForButtonValue = true;
            //AddButton.HeaderText = "تثبيت ";
            //AddButton.DataPropertyName = "btnAdd";
            //AddButton.Text = "اضافة";
            //AddButton.ActiveLinkColor=Color.Red;
            //AddButton.LinkColor = Color.Blue;


            //DataGridViewButtonColumn DeletButton = new DataGridViewButtonColumn();
            //DeletButton.FlatStyle = FlatStyle.Popup;
            //DeletButton.Name = "DeletButton";
            //DeletButton.UseColumnTextForButtonValue = true;
            //DeletButton.HeaderText = " اعادة تثبيت";
            //DeletButton.DataPropertyName = "DeletButton";
            //DeletButton.Text = "حذف";


            //Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            //Global_Variable.Source_HS_Profile = mk_DataAccess.GetProfileHotspot();

            List<Hotspot_Source_Profile> profile = Global_Variable.Source_HS_Profile;
            try
            {
                DataTable dtp = new DataTable();
                dtp = new DataTable();

                dtp.Columns.Add("البروفايل");
                dtp.Columns.Add("الحالة");
                dtp.Columns.Add("onlogin");
                dtp.Columns.Add("onlogout");
                dtp.Columns.Add("idx");

                dgv.DataSource = null;

                foreach (Hotspot_Source_Profile p in profile)
                {
                    bool install_Login = false;
                    bool install_Logout = false;

                    DataRow row = dtp.NewRow();
                    row["البروفايل"] = p.Name;
                    row["onlogin"] = p.Onlogin;
                    row["onlogout"] = p.Onlogout;
                    row["idx"] = p.idHX;

                    if (p.Onlogin.ToLower().Contains("SmartLogIn.rsc".ToLower()))
                        install_Login = true;
                    if (p.Onlogout.ToLower().Contains("SmartLogOut.rsc".ToLower()))
                        install_Logout = true;

                    if (install_Login && install_Logout)
                        row["الحالة"] = "مضاف";
                    else
                        row["الحالة"] = "غير مضاف";

                    dtp.Rows.Add(row);
                }
                dgv.DataSource = dtp;


                foreach (DataGridViewRow row1 in dgv.Rows)
                {
                    row1.Cells["install"].Value = "تثبيت";
                    if (row1.Cells["الحالة"].Value.ToString() == "مضاف")
                    {
                        row1.Cells["install"].Value = "اعادة تثبيت";
                        row1.Cells["remove"].Value = "الغاء تثبيت";
                        row1.DefaultCellStyle.ForeColor = Color.Red;
                    }
                    //row1.Cells["d"].Value = "";
                }
                dgv.Columns["onlogin"].Visible = false;
                dgv.Columns["onlogout"].Visible = false;
                dgv.Columns["idx"].Visible = false;

                //dgv.Columns[0].DisplayIndex = 2;
                dgv.Columns["install"].DisplayIndex = 3;
                dgv.Columns["remove"].DisplayIndex = 4;
                dgv.ClearSelection();

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }

        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            
            rjLabel1.Location = new System.Drawing.Point(panel1.Width / 2 - rjLabel1.Width / 2, 57);
            btn_AddScript_toMickrotik.Location = new System.Drawing.Point(panel1.Width / 2 - btn_AddScript_toMickrotik.Width / 2, 9);

            lbl_Title1.Location = new System.Drawing.Point(rjPanel1.Width / 2 - lbl_Title1.Width / 2, 6);
            lbl_descr.Location = new System.Drawing.Point(rjPanel1.Width / 2 - lbl_descr.Width / 2, 40);

            flowLayoutPanel2.Location = new System.Drawing.Point((panel1.Width / 2 - flowLayoutPanel2.Width / 2) -15, 90);
            flowLayoutPanel1.Location = new System.Drawing.Point(panel1.Width / 2 - flowLayoutPanel1.Width / 2 -15, 128);

            this.Refresh();
            rjPanel2.Refresh();
            panel1.Refresh();
            dgv.Refresh();

        }
        private void radio_Add_SideLastSetting_CheckedChanged(object sender, EventArgs e)
        {
            if (Radio_Add_SideLastSetting.Checked)
                Radio_RemoeLastSetting.Checked = false;
        }
        private void radio_RemoeLastSetting_CheckedChanged(object sender, EventArgs e)
        {
            if (Radio_RemoeLastSetting.Checked)
                Radio_Add_SideLastSetting.Checked = false;
        }
        [Obsolete]
        private void btn_AddScript_toMickrotik_Click(object sender, EventArgs e)
        {
            if (status_install)
            {
                DialogResult result2 = RJMessageBox.Show("  هل انت متأكد من الغاء تثبيت نظام ادارة صلاحيات الهوتسبوت ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result2 == DialogResult.No)
                    return;

                Mk_DataAccess mk = new Mk_DataAccess();
                mk.Remove_SmartValidity();
                btn_Refresh_Click(sender, e);
                RJMessageBox.Show("تم الفاء تثبيت اضافات ادارة صلاحيات الهوتسبوت");
                return;
            }
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من تثبيت ادارة صلاحيات الهوتسبوت ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;


            string SmartLogIn = @"{:do {:global SmartLogIn;:global hotspotUserIndexes;
:local userId ($hotspotUserIndexes->""$user"");
:if ([:len $userId] = 0) do={:set userId $user;}
:local ue [/ip hotspot user get $userId email];
:if ([:len [:find $ue ""befor""]]!=0) do={:local f [$SmartLogIn $userId $ue $""mac-address"" $user];}
} on-error= {}}";
            createLocal_File(SmartLogIn, @"locales\SmartLogIn.rsc");

            string SmartLogOut = @"{:do {:global SmartLogOut;:global hotspotUserIndexes;
:local userId ($hotspotUserIndexes->""$user"");
:if ([:len $userId] = 0) do={:set userId $user;}	
:local e [ /ip hotspot user get $userId email ];
:if ([:len [:find $e ""after""]]!=0) do={  
:local v ($""uptime-secs"".""|"".$""bytes-in"".""|"".$""bytes-out"".""|"".$interface.""|"".$address.""|"".$""mac-address""); 
:local fun [$SmartLogOut $userId $e [:tostr $v]  $user];};
} on-error= {}}";
            createLocal_File(SmartLogOut, @"locales\SmartLogOut.rsc");

            string SmartSaveTimeSizeLeft = @"{
:global GetStrToArr;:global GetArrToStr;:global hotspotUserIndexes;
/ip hotspot active ;
:foreach i in=[find where !radius] do={
:local uname [get $i user];	
:local userId ($hotspotUserIndexes->""$uname"");
:if ([:len $userId] = 0) do={:set userId $uname;};
:local userVal [ /ip hotspot user get $userId ];
:if ([:len [:find ($userVal->""email"") ""after""]]!=0) do={ 
    :do {
        :local earr [:toarray [$GetStrToArr ($userVal->""email"") ""'"" "",""]];
		:local carr [:toarray [$GetArrToStr ($userVal->""comment"") "";"" "",""]];
        :local Timeleft 0;:local Sizletf 0;
		#:put ""$uname"";
        :if ( ($earr->8) = true ) do={
                :local SessionTimeLeft [get $i session-time-left];
                :if ([:len $SessionTimeLeft] > 0) do={
                    :set Timeleft $SessionTimeLeft;
                    :set ($carr->1) $SessionTimeLeft;
            }
        }
        :if ( ($earr->9) = true ) do={
            :local utotal ($userVal->""limit-bytes-total""); 
			:if ([:len $utotal] > 0) do={
				:local UserOut (($userVal->""bytes-in"") + ($userVal->""bytes-out""));
				:local SessionOut ([get $i bytes-in] + [get $i bytes-out]); 
					:set Sizletf ( ($utotal - $UserOut) - $SessionOut);
					:if ( $Sizletf <= 0 ) do={:set Sizletf 0 };
					:if ( ($earr->8) = true ) do={ :set ($carr->2) $Sizletf;} else={:set ($carr->1) $Sizletf; };
            }
        }
        :if ( ($Timeleft > 0) || ($Sizletf > 0) ) do={
            :local cout [$GetArrToStr [:tostr $carr] "","" "";""];
            [ /ip hotspot user set $userId comment=$cout  ];
        };
    } on-error={:log info  message=""script SaveTimeSizeLeft error on user --> $uname"";};}}}
	";
            createLocal_File(SmartSaveTimeSizeLeft, @"locales\SmartSaveTimeSizeLeft.rsc");

            string SmartHotspotMangment = @"#==============================function Convert  ============================================
:global GetStrToArr do={
  while condition=[find $1 $2] do={
   set $1 (""$[pick $1 0 ([find $1 $2]) ]"".""\"""".$3.""\"""".""$[pick $1 ([find $1 $2]+1) ([len $1])]"")}
  return (""\"""".$1.""\"""")
}
:global GetArrToStr do={
  while condition=[find $1 $2] do={
   set $1 (""$[pick $1 0 ([find $1 $2]) ]"".$3.""$[pick $1 ([find $1 $2]+1) ([len $1])]"")}
  return $1
 }
#==============================function times ============================================
:global MinuteToNow do={
:if ([:len $1] = 8) do= {
    :local hour [ :pick $1 0 2 ];
    :local mint [ :pick $1 3 5 ];
    :local mintNow (($hour*60)+$mint);
    :return ($mintNow+($2*24*60));
} else= {
	:if ([:len $1] = 20) do= {
		:local hour [ :pick $1 12 14 ];
		:local mint [ :pick $1 15 17 ];
		:local mintNow (($hour*60)+$mint);
		:return   ($mintNow+($2*24*60));
		} else= {
			:local hour [:pick $1 11 13];
			:local mint [ :pick $1 14 16 ];
			:local mintNow (($hour*60)+$mint);
			:return ($mintNow+($2*24*60));
		};		
};}

:global DaysToNow do={
:local num 0;
:do {
	:local montharray ( ""jan"",""feb"",""mar"",""apr"",""may"",""jun"",""jul"",""aug"",""sep"",""oct"",""nov"",""dec"" );
	:local monthdays ( 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31 );
	:if ( [:len $1] = 20 ) do= {
		:local days [ :pick $1 4 6 ];
		:local monthtxt [ :pick $1 0 3 ];
		:local year [ :pick $1 7 11 ];
		:local months ([ :find $montharray $monthtxt] );
		:for nodays from=0 to=$months do={:set days ( $days + [ :pick $monthdays $nodays ] );}; 
		:set num ($days + $year * 365);
		return $num;
	} else= {
		:local days [ :pick $1 8 10 ];
		:local year [ :pick $1 0 4 ];
		:local months [ :pick $1 5 7 ];
		:for nodays from=1 to=$months do={:set days ( $days + [ :pick $monthdays $nodays ] );}; 
		:set num ($days + $year * 365);
		return $num;
	   };	
} on-error={:log info message=""Script DaysToNow error for --> $1"";};
return $num;
}

#profil'days'price'sp'Batch!numPrint'datetimePrint'ByDayHour'mac'timeSave'sizeSave'sessionSave'@smart.befor
#====================function on Login User =================================
#/import SmartValidity/Scripts/SmartLogIn.rsc 
:global SmartLogIn do={
    :do {
		:global GetStrToArr;:global GetArrToStr;:log info ""logged in $4 "";	
        :local d ([ /system clock get date ]."" "".[ /system clock get time]);:local c $d;
		:local userVal [ /ip hotspot user get $1 ];
        :local uc ($userVal->""comment"");
        :local Aval [:toarray [$GetStrToArr $2 ""'"" "",""]];
        :local mac 00:00:00:00:00:00 ; :set ($Aval->11) ""@smart.after"";
		:loca after [$GetArrToStr [:tostr $Aval] "";"" ""'""];
        :if ( ($Aval->7) = true ) do={:set mac  $3;};       
        :if ( ($Aval->8) = true ) do={
			:local limitUptime ($userVal->""limit-uptime"");
			:if ([:len $limitUptime] <= 0) do={:set limitUptime 0;};
			:set c (""$c;$limitUptime"");};    
        :if ( ($Aval->9) = true ) do={
			:local utotal ($userVal->""limit-bytes-total""); 
			:if ( $utotal <=0 ) do={:set utotal 0;}; 
			:set c (""$c;$utotal"")}; 
        :if ([:len $uc] != 0) do={:set c (""$c;"".$uc)};	
        [ /ip hotspot user set $1 mac-address=$mac email=$after comment=$c ];
    } on-error={:log info message=""script SmartLogIn error on user --> $4"";}
}

#====================function on LogOut User =================================
#/import SmartValidity/Scripts/SmartLogOut.rsc;
:global SmartLogOut do={
    :do { 
		:global GetStrToArr;:global GetArrToStr;:global SaveHSessionUser; 
		:log info ""logged out $4 "";
		:delay 1s;
        :local userVal [ /ip hotspot user get $1 ]; 
        :local cAval [:toarray [$GetStrToArr [:tostr ($userVal->""comment"")] "";"" "",""]];
        :local eAval [:toarray [$GetStrToArr $2 ""'"" "",""]]; 
        :local Timeleft 0;:local Sizletf 0;
		:if ( ($eAval->8) = true ) do={ 
            :local uptimeU ($userVal->""uptime"");
            :local limitUptime ($userVal->""limit-uptime"");
            :if ([:len $limitUptime] > 0) do={
                :set Timeleft ( ($limitUptime - $uptimeU) );
                    if ($Timeleft < 00:00:00) do={set Timeleft 0};
                :set ($cAval->1) $Timeleft; 
        };};      
        :if ( ($eAval->9) = true ) do={ 
            :local utotal ($userVal->""limit-bytes-total""); 
            :local UserOut (($userVal->""bytes-in"") + ($userVal->""bytes-out"")); 
            :if ([:len $utotal] > 0) do={ 
                :set Sizletf ( ($utotal - $UserOut));
                if ($Sizletf < 0) do={set Sizletf 0};
                :if ( ($eAval->8) = true ) do={ :set ($cAval->2) $Sizletf;} else={:set ($cAval->1) $Sizletf; }; 
        };};
        if ( ($Timeleft > 0) || ($Sizletf > 0) ) do={ 
            :local cout [$GetArrToStr [:tostr $cAval] "","" "";""]; 
            [ /ip hotspot user set $1 comment=$cout  ]
        }
        :if ( ($eAval->10) = true ) do={
		:log info ""Go to SaveHSessionUser for $4"";
            :local cs [$SaveHSessionUser $4 $3];
        } 
    } on-error={:log info message=""script SmartLogOut error on user --> $4"";} 
} 

#========= function to Save Active Session On LogOount ========================
:global SaveHSessionUser do={ 
 :do { 
	:log info ""SaveHSessionUser $1"";
    :local dt [/system clock get date];:local tm [/system clock get time];
    :local Sessiondate ($dt."" "".$tm) ;
    :local f (""SmartValidity/Sessions/$1""."".smart"") 
    :local json ($2 . ""|"" . $Sessiondate) 
    #:log info ""$json"";  
    :do {	 
      :local lastContent [/file get $f contents];	 
      /file set $f contents=($lastContent.""\n"". $json);	
      } on-error={
       :local fileName (""$f"".""\00"");
      /file print file=$fileName ;
      :delay 3s
      /file set $f contents=$json;	
     }
      return (""true"");
    } on-error={:log info message=""script SaveHSession error on user --> $1"";} 
  }
  

#=========================================
#/system scheduler remove [find name=""SmartCheckAfterShutdown""];
#/system scheduler remove [find name=""SmartDisableExpirHSUser""];
#/system scheduler remove [find name=""SmartImportAllFunction""];
#/system scheduler remove [find name=""SmartSaveTimeSizeLeft""];

#==================

";
            createLocal_File(SmartHotspotMangment, @"locales\SmartHotspotMangment.rsc");

            string SmartDisableExpirHSUser = @"{
	#Created By Smart-Solution;
    :global DaysToNow;:global MinuteToNow;
	:global GetStrToArr;:global GetArrToStr;
    :global hotspotUserIndexes [:toarray """"];
    /ip hotspot user;
	:foreach i in=[find where disabled=no ] do={
	:local userVal ({[get $i]});:local uname [get $i name];	
		:if ( [:len [:find [get $i email] ""@smart""]] !=0 ) do={ 
			:do {:set ($hotspotUserIndexes->""$uname"") $i ;
		} on-error= { :log info message=""script add hotspotUserIndexes error on user --> $uname""; }};}; 
	    
    :foreach k,i in=$hotspotUserIndexes do= {
        :do {            
			:local userArr [/ip hotspot user get $i];
            :local email ($userArr->""email"");
            :local comment ($userArr->""comment"");
            :if (([:len [:find $email ""after""]]!=0) && [:len $comment] != 0 ) do= {				
				:local earr [:toarray [$GetStrToArr $email ""'"" "",""]];
				:local carr [:toarray [$GetArrToStr $comment "";"" "",""]];             
				:local offsetCut ($earr->1);                
				:local daysCard [$DaysToNow ($carr->0)];
                :local minutesCard [$MinuteToNow ($carr->0) ($daysCard + $offsetCut) ];               
			    :local daysSys [$DaysToNow ([/system clock get date] ."" "". [/system clock get time])];
			    :local minutesSys [$MinuteToNow [/system clock get time] $daysSys]; 			    
			    :if ( $minutesCard <= $minutesSys ) do= {
					:log info ""Disabling Hotspot User ($k) --> first logged in --> ($carr->0) ---> By SmartCreator"";                   
                    [ /ip hotspot user disable $i ];
					[ /ip hotspot active remove [find name=$k]];
                };            
				:if ( ($earr->6) = true ) do= {
					:if (( $offsetCut + $daysCard) >  $daysSys ) do= {
                        :local DaysSub ( ($offsetCut - ($daysSys - $daysCard)) );
						:local DaysRemain ($DaysSub * (24 * 60 * 60));
                        :if ( $DaysSub > 0  && $DaysRemain < ($offsetCut * (24 * 60 * 60)) ) do= {
							[/ip  hotspot user set $i limit-uptime=($DaysRemain + ($userArr->""uptime"") )];
                        };
                    };
                };  
            };	
        } on-error= { :log info message=""script DisableExpirUserHotspot error on user --> $k""; }
    }
}
";
            createLocal_File(SmartDisableExpirHSUser, @"locales\SmartDisableExpirHSUser.rsc");

            string SmartCheckAfterShutdown = @"{
:global hotspotUserIndexes [:toarray """"];
:global GetStrToArr;:global GetArrToStr;
/ip hotspot user;
:foreach i in=[find where disabled=no] do={
:local userVals [get $i];
:local uname ($userVals->""name"");
:local email ($userVals->""email"");
:if ([:len [:find $email ""@smart""]]!=0) do={ 
	:do {
		:set ($hotspotUserIndexes->""$uname"") $i;
		:if ([:len [:find $email ""after""]]!=0) do={
			:local earr [:toarray [$GetStrToArr $email ""'"" "",""]];
			:local carr [:toarray [$GetArrToStr ($userVals->""comment"") "";"" "",""]];
			:local Timeleft [:totime ($carr->1)];				          
			:if ( ($earr->8) = true) do={
				:local limitUptime ($userVals->""limit-uptime"");
					:if ([:len $limitUptime] > 0 ) do={
						:local uptime ($userVals->""uptime"");               
						:if ( $Timeleft > 0  && ( $Timeleft < ($limitUptime - $uptime) ) ) do={      
							[/ip  hotspot user set $i limit-uptime=($Timeleft + $uptime )];
				}}};
			:if (($earr->9) = true) do={
				:local limitBytesTotal ($userVals->""limit-bytes-total"");
				:if ([:len $limitBytesTotal] > 0 ) do={:local Sizletf 0;
					:if ( ($earr->8) = true ) do={ :set Sizletf ($carr->2);} else={:set Sizletf ($carr->1); };										
					:if ( $Sizletf > 0 && ($Sizletf < $limitBytesTotal) ) do={
						:local bytesUsed ( ($userVals->""bytes-in"") + ($userVals->""bytes-out"") );
						[ /ip  hotspot user set $i limit-bytes-total=($Sizletf + $bytesUsed )];
			
			};};};};
	} on-error={:log info message=(""script Check_Session_TimeLeft_After_Shutdown error on user -->$uname"" );}}}}
	";
            createLocal_File(SmartCheckAfterShutdown, @"locales\SmartCheckAfterShutdown.rsc");


            check_port_mk_befor();
            using (var client = new SftpClient(Global_Variable.Server_IP, ssh_port, Global_Variable.Server_Username, Global_Variable.Server_Password))
            {
                try { client.Connect(); }
                catch (Exception ex)
                {
                    if (ex.Message == "The connection was closed by the server:  (ServiceNotAvailable)")
                    {
                        System.Windows.MessageBox.Show("للكتابة او القراءه علي الملفات ftp المستخدم ليس لديه صلاحيات ssh   \n\n" + ex.Message); return;
                    }
                    MessageBox.Show("خطاء بالاتصال بالمايكروتك قد يكون المستخدم محدود الصلاحيات \n\n" + ex.Message);
                }

                if (client.IsConnected)
                {
                    CreateServerDirectoryIfItDoesntExist("SmartValidity/Scripts", client);
                    CreateServerDirectoryIfItDoesntExist("SmartValidity/Sessions", client);

                    UploadFiles(client, @"locales\SmartLogIn.rsc", "SmartValidity/Scripts/*SmartLogIn.rsc");
                    UploadFiles(client, @"locales\SmartLogOut.rsc", "SmartValidity/Scripts/*SmartLogOut.rsc");
                    UploadFiles(client, @"locales\SmartHotspotMangment.rsc", "SmartValidity/Scripts/*SmartHotspotMangment.rsc");
                    UploadFiles(client, @"locales\SmartSaveTimeSizeLeft.rsc", "SmartValidity/Scripts/*SmartSaveTimeSizeLeft.rsc");
                    UploadFiles(client, @"locales\SmartDisableExpirHSUser.rsc", "SmartValidity/Scripts/*SmartDisableExpirHSUser.rsc");
                    UploadFiles(client, @"locales\SmartCheckAfterShutdown.rsc", "SmartValidity/Scripts/*SmartCheckAfterShutdown.rsc");


                    //Global_Variable.Update_Um_StatusBar_Prograss($"تم رفع ملفات الصفحه ", 0);

                }
            }
            rest_port_mk_after();
            try
            {
                File.Delete(@"locales\SmartLogIn.rsc");
                File.Delete(@"locales\SmartLogOut.rsc");
                File.Delete(@"locales\SmartSaveTimeSizeLeft.rsc");
                File.Delete(@"locales\SmartHotspotMangment.rsc");
                File.Delete(@"locales\SmartDisableExpirHSUser.rsc");
                File.Delete(@"locales\SmartCheckAfterShutdown.rsc");
            }
            catch { }

            //=========== add schular ==========
            //Mk_DataAccess mk=new Mk_DataAccess();
            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_DataAccess.Mk_Conn(connection) == false)
                    return;

                Mk_DataAccess.AddScheduler_mikrotik("SmartImportAllFunction", "/import SmartValidity/Scripts/*SmartHotspotMangment.rsc;", "startup", "00:00:00", connection);
                Mk_DataAccess.AddScheduler_mikrotik("SmartDisableExpirHSUser", "/import SmartValidity/Scripts/*SmartDisableExpirHSUser.rsc;", "startup", "04:00:00", connection);
                Mk_DataAccess.AddScheduler_mikrotik("SmartSaveTimeSizeLeft", "/import SmartValidity/Scripts/*SmartSaveTimeSizeLeft.rsc;", "startup", "00:10:00", connection);
                Mk_DataAccess.AddScheduler_mikrotik("SmartCheckAfterShutdown", $"delay 2s;{Environment.NewLine}/import SmartValidity/Scripts/*SmartHotspotMangment.rsc;"+
                    $"{Environment.NewLine}/import SmartValidity/Scripts/*SmartCheckAfterShutdown.rsc;", "startup", "00:00:00", connection);
                


                Mk_DataAccess.AddScheduler_mikrotik("installSmartImportAllFunction", "/import SmartValidity/Scripts/*SmartHotspotMangment.rsc; delay 1s; /system scheduler remove [find name=\"installSmartImportAllFunction\"];", "startup", "00:00:05", connection);

                Thread.Sleep(2000);
            }

            //Mk_DataAccess.add_Script_Smart_AndRun(ImportHotspotManagment(), false);
            //Mk_DataAccess_old mko = new Mk_DataAccess_old();
            //if (mko.RunImport_File("SmartValidity/Scripts/SmartHotspotMangment.rsc"))
            //{
            //    RJMessageBox.Show("تم تثبيت اضافات ادارة الصلاحيات");
            //}
            RJMessageBox.Show("تم تثبيت اضافات ادارة الصلاحيات");
            Check_If_installed();
        }

        private string ImportHotspotManagment()
        {
            string script = @"";
            return script;
        }
        private void createLocal_File(string conntent, string pathFile)
        {
            try
            {
                StreamWriter sw = new StreamWriter(pathFile);
                sw.WriteLine(conntent);
                sw.Close();
            }
            catch (Exception e)
            {
                MessageBox.Show("Exception:\n " + e.Message);
            }

        }
        [Obsolete]
        private bool UploadFiles(SftpClient client, string LocalFile, string UploadFile)
        {
            bool add = false;
            string workingdirectory = "/";
            try
            {
                //using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                //{
                //client.Connect();
                client.ChangeDirectory(workingdirectory);
                using (var fileStream = new FileStream(LocalFile, FileMode.Open))
                {
                    //Global_Variable.Update_Um_StatusBar_Prograss($"رفع المف  {UploadFile} ", 0);

                    FileInfo file = new FileInfo(LocalFile);

                    var fileSize = file.Length;
                    //var fileSize = client.GetAttributes(UploadFile).Size;
                    long downloadedBytes = 0;
                    int progress = 0;
                    Action<ulong> progressCallback = (downloaded) =>
                    {
                        downloadedBytes = (long)downloaded;
                        progress = (int)((downloadedBytes * 100) / fileSize);
                        Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke((System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progress; });
                    };


                    client.BufferSize = 4 * 1024; // bypass Payload error large files
                    client.UploadFile(fileStream, (UploadFile), progressCallback);
                }
                add = true;
                //}
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            return add;
        }
        private void CreateServerDirectoryIfItDoesntExist(string serverDestinationPath, SftpClient sftpClient)
        {
            try
            {
                if (serverDestinationPath[0] == '/')
                    serverDestinationPath = serverDestinationPath.Substring(1);

                string[] directories = serverDestinationPath.Split('/');
                for (int i = 0; i < directories.Length; i++)
                {
                    string dirName = string.Join("/", directories, 0, i + 1);
                    if (!sftpClient.Exists(dirName))
                        sftpClient.CreateDirectory(dirName);
                }
            }
            catch { }
        }
        public string ssh_last_state = "false";
        public string id_ssh = "";
        public int ssh_port = 22;
        public DataTable dt_service = null;
        [Obsolete]
        public bool check_port_mk_befor()
        {
            bool result = false;
            Mk_DataAccess DA2 = new Mk_DataAccess();
            dt_service = DA2.GetService();
            try
            {
                DataRow[] foundRows = dt_service.Select("[name] = " + "'ssh'");
                ssh_last_state = foundRows[0]["disabled"].ToString();
                ssh_port = Convert.ToInt32(foundRows[0]["port"].ToString());
                Global_Variable.Mk_Login_data.Mk_Port_ssh = ssh_port;
                id_ssh = foundRows[0]["id"].ToString();
                result = true;
            }
            catch { }

            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "false");
                result = true;
            }
            return result;
        }
        [Obsolete]
        public void rest_port_mk_after()
        {
            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "true");
            }
        }

        bool status_install = false;
        bool re_install = false;
        [Obsolete]
        private void Check_If_installed()
        {
             status_install = false;
             re_install = false;
            Mk_DataAccess mk = new Mk_DataAccess();

            //bool SmartLogIn = mk.Check_file_found("SmartValidity/Scripts/SmartLogIn.rsc");
            //bool SmartLogOut = mk.Check_file_found("SmartValidity/Scripts/SmartLogOut.rsc");
            //bool SmartSaveTimeSizeLeft = mk.Check_file_found("SmartValidity/Scripts/SmartSaveTimeSizeLeft.rsc");
            //bool SmartHotspotMangment = mk.Check_file_found("SmartValidity/Scripts/SmartHotspotMangment.rsc");
            //bool SmartDisableExpirHSUser = mk.Check_file_found("SmartValidity/Scripts/SmartDisableExpirHSUser.rsc");
            //bool SmartCheckAfterShutdown = mk.Check_file_found("SmartValidity/Scripts/SmartCheckAfterShutdown.rsc");

            //bool sch_SmartImportAllFunction = mk.Check_scheduler_found("SmartImportAllFunction");
            //bool sch_SmartSaveTimeSizeLeft = mk.Check_scheduler_found("SmartSaveTimeSizeLeft");
            //bool sch_SmartDisableExpirHSUser = mk.Check_scheduler_found("SmartDisableExpirHSUser");
            //bool sch_SmartCheckAfterShutdown = mk.Check_scheduler_found("SmartCheckAfterShutdown");
            //bool env_SaveHSessionUser = mk.Check_environment_Script_found("SaveHSessionUser");
            //bool env_DaysToNowV6 = mk.Check_environment_Script_found("DaysToNowV6");

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_DataAccess.Mk_Conn(connection) == false)
                    return;

                if (mk.Check_File_Found("SmartValidity/Scripts/*SmartHotspotMangment.rsc", connection))
                {
                    if (mk.Check_File_Found("SmartValidity/Scripts/*SmartLogIn.rsc", connection))
                    {
                        if (mk.Check_File_Found("SmartValidity/Scripts/*SmartLogOut.rsc", connection))
                        {
                            if (mk.Check_File_Found("SmartValidity/Scripts/*SmartSaveTimeSizeLeft.rsc", connection))
                            {
                                if (mk.Check_File_Found("SmartValidity/Scripts/*SmartDisableExpirHSUser.rsc", connection))
                                {
                                    if (mk.Check_File_Found("SmartValidity/Scripts/*SmartCheckAfterShutdown.rsc", connection))
                                    {
                                        if (mk.Check_scheduler_found("SmartImportAllFunction", connection))
                                        {
                                            if (mk.Check_scheduler_found("SmartSaveTimeSizeLeft", connection))
                                            {
                                                if (mk.Check_scheduler_found("SmartDisableExpirHSUser", connection))
                                                {
                                                    if (mk.Check_scheduler_found("SmartCheckAfterShutdown", connection))
                                                    {
                                                        if (mk.Check_environment_Script_found("SaveHSessionUser", connection))
                                                        {
                                                            if (mk.Check_environment_Script_found("DaysToNow", connection))
                                                            {
                                                                status_install = true;
                                                            }
                                                        }
                                                        else
                                                            re_install = true;
                                                    }
                                                    else
                                                        re_install = true;
                                                }
                                                else
                                                    re_install = true;
                                            }
                                            else
                                                re_install = true;
                                        }
                                        else
                                            re_install = true;

                                    }
                                    else
                                        re_install = true;

                                }
                                else
                                    re_install = true;
                            }
                            else
                                re_install = true;
                        }
                        else
                            re_install = true;
                    }
                    else
                    {
                        re_install = true;
                    }
                }
            }
            if (status_install == false && re_install == false)
            {
                btn_AddScript_toMickrotik.Style = ControlStyle.Solid;
                btn_AddScript_toMickrotik.Design = ButtonDesign.Confirm;
                btn_AddScript_toMickrotik.Text = "تثبيت نظام الصلاحيات الي المايكروتك";
                lbl_note.Text = "لا يعمل - لم يتم تثبيت اضافات الصلاحيات";
                if (UIAppearance.Theme == UITheme.Light)
                    lbl_note.ForeColor = Color.Red;
            }
            else if (re_install)
            {
                btn_AddScript_toMickrotik.Style = ControlStyle.Solid;
                btn_AddScript_toMickrotik.Design = ButtonDesign.Cancel;
                btn_AddScript_toMickrotik.Text = "اصلاح واعادة تثبيت نظام الصلاحيات";
                lbl_note.Text = "لا يعمل - هناك بعض السكربتات ناقصة";
                if (UIAppearance.Theme == UITheme.Light)
                    lbl_note.ForeColor = Color.Red;

            }
            else
            {
                btn_AddScript_toMickrotik.Style = ControlStyle.Solid;
                btn_AddScript_toMickrotik.Design = ButtonDesign.Delete;
                btn_AddScript_toMickrotik.Text = "حذف نظام الصلاحيات من المايكروتك";
                lbl_note.Text = " يعمل - نظام الصلاحيات يعمل بشكل جيد";
                if (UIAppearance.Theme == UITheme.Light)
                    lbl_note.ForeColor = Color.Blue;
            }

            btn_AddScript_toMickrotik.TextAlign = ContentAlignment.MiddleCenter;
            btn_AddScript_toMickrotik.ImageAlign = ContentAlignment.MiddleCenter;
            btn_AddScript_toMickrotik.TextImageRelation = TextImageRelation.Overlay;
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            loadData();

            Check_If_installed();
        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            Global_Variable.Source_HS_Profile = mk_DataAccess.GetProfileHotspot();
            loadData();
            Check_If_installed();
        }

        [Obsolete]
        private void dgv_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                if (dgv.Columns[e.ColumnIndex].Name == "install")
                {
                    //if (RJMessageBox.Show("هل متاكد من  تثبيت الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                        string idx = dgv.CurrentRow.Cells["idx"].Value.ToString();
                        string onlogin = dgv.CurrentRow.Cells["onlogin"].Value.ToString();
                        string onlogout = dgv.CurrentRow.Cells["onlogout"].Value.ToString();

                        string status = dgv.CurrentRow.Cells["الحالة"].Value.ToString();
                        if (status == "مضاف")
                        {
                            if (RJMessageBox.Show("هل متاكد من  اعادة تثبيت واصلاح اضافة الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                                return;

                            
                            string newOnlogin = onlogin.Replace("/import SmartValidity/Scripts/*SmartLogIn.rsc;", "");
                            string newOnlogout = onlogout.Replace("/import SmartValidity/Scripts/*SmartLogOut.rsc;", "");

                            if (Radio_RemoeLastSetting.Checked)
                            {
                                onlogin = "";
                                onlogout = "";
                            }

                            newOnlogin = "/import SmartValidity/Scripts/*SmartLogIn.rsc;"   + Environment.NewLine  + newOnlogin;
                            newOnlogout = "/import SmartValidity/Scripts/*SmartLogOut.rsc;" + Environment.NewLine  + newOnlogout;

                            if (mk_DataAccess.Update_ProfileHotspot(idx, newOnlogin, newOnlogout))
                            {
                                RJMessageBox.Show("تم تثبيت الاضافة علي البروفايل بنجاح");
                            }
                        }
                        else
                        {
                            if (RJMessageBox.Show("هل متاكد من  تثبيت  اضافة الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                                return;

                            if (Radio_RemoeLastSetting.Checked)
                            {
                                onlogin = "";
                                onlogout = "";
                            }

                            string newOnlogin = "/import SmartValidity/Scripts/*SmartLogIn.rsc;" + Environment.NewLine +  onlogin;
                            string newOnlogout = "/import SmartValidity/Scripts/*SmartLogOut.rsc;" + Environment.NewLine +  onlogout;

                            if (mk_DataAccess.Update_ProfileHotspot(idx, newOnlogin, newOnlogout))
                            {
                                RJMessageBox.Show("تم تثبيت الاضافة علي البروفايل بنجاح");
                            }
                        }


                        Global_Variable.Source_HS_Profile = mk_DataAccess.GetProfileHotspot();
                        loadData();
                        Check_If_installed();
                    }
                }
               else  if (dgv.Columns[e.ColumnIndex].Name == "remove")
                {
                    //if (RJMessageBox.Show("هل متاكد من  تثبيت الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                        string idx = dgv.CurrentRow.Cells["idx"].Value.ToString();
                        string onlogin = dgv.CurrentRow.Cells["onlogin"].Value.ToString();
                        string onlogout = dgv.CurrentRow.Cells["onlogout"].Value.ToString();

                        string status = dgv.CurrentRow.Cells["الحالة"].Value.ToString();
                        if (status == "مضاف")
                        {
                            if (RJMessageBox.Show("هل متاكد من  الغاء تثبيت  اضافة الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                                return;

                            string newOnlogin = onlogin.Replace("/import SmartValidity/Scripts/*SmartLogIn.rsc;", "");
                            newOnlogin = newOnlogin.TrimStart('\r', '\n');
                            //string newOnlogin = onlogin.Replace("/import SmartValidity/Scripts/SmartLogIn.rsc;", "");
                            string newOnlogout = onlogout.Replace("/import SmartValidity/Scripts/*SmartLogOut.rsc;", "");
                            newOnlogout = newOnlogout.TrimStart('\r', '\n');

                            //string newOnlogout = onlogout.Replace("/import SmartValidity/Scripts/SmartLogOut.rsc;", "");

                            //newOnlogin = "/import SmartValidity/Scripts/SmartLogIn.rsc;" + Environment.NewLine + Environment.NewLine + newOnlogin;
                            //newOnlogout = "/import SmartValidity/Scripts/SmartLogOut.rsc;" + Environment.NewLine + Environment.NewLine + newOnlogout;

                            if (mk_DataAccess.Update_ProfileHotspot(idx, newOnlogin, newOnlogout))
                            {
                                RJMessageBox.Show("تم الغاء تثبيت الاضافة علي البروفايل بنجاح");
                            }
                        }
                        //else
                        //{
                        //    if (RJMessageBox.Show("هل متاكد من  تثبيت  اضافة الصلاحية علي البروفايل المحدد ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                        //        return;
                        //    string newOnlogin = "/import SmartValidity/Scripts/SmartLogIn.rsc;" + Environment.NewLine + Environment.NewLine + onlogin;
                        //    string newOnlogout = "/import SmartValidity/Scripts/SmartLogOut.rsc;" + Environment.NewLine + Environment.NewLine + onlogout;

                        //    if (mk_DataAccess.Update_ProfileHotspot(idx, newOnlogin, newOnlogout))
                        //    {
                        //        RJMessageBox.Show("تم تثبيت الاضافة علي البروفايل بنجاح");
                        //    }
                        //}


                        Global_Variable.Source_HS_Profile = mk_DataAccess.GetProfileHotspot();
                        loadData();
                        Check_If_installed();
                    }
                }
                 
            }
        }

        [Obsolete]
        private void btn_Upgrade_Click(object sender, EventArgs e)
        {
            using (Form_WaitForm frm=new Form_WaitForm(add_SmartScripts_To_users))
            {
                frm.ShowDialog();
            }
            //Thread thread = new Thread(add_SmartScripts_To_users);
            //Global_Variable.StartThreadProcessFromMK = true;
            //thread.Start();
        }

        private void btn_add_ScriptSmart_Click(object sender, EventArgs e)
        {
            
        }

        [Obsolete]
        private void add_SmartScripts_To_users()
        {
            try
            {
                if (Global_Variable.Source_Users_HotSpot == null)
                {
                    RJMessageBox.Show("قم بجلب او تحديث الكروت من الروتر");
                    return;
                }
                if (RJMessageBox.Show(" هل متاكد من  ترقية نظام الصلاحيات الي الاصدار الاحدث يجب عمل باكب او نسخه احتياطية للروتر في حال اردت الروجوع للاصدار القديم ?", "تنبية", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.No)
                {
                    return;
                }
                
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", "يتم الان تجهيز الكروت  ذات الصلاحيه القديمة");

                List<SourceCardsHotspot_fromMK> user = new List<SourceCardsHotspot_fromMK>();

                foreach (SourceCardsHotspot_fromMK hotspt in Global_Variable.Source_Users_HotSpot)
                {
                    SourceCardsHotspot_fromMK hs = hotspt;
                    string profile = hotspt.profileHotspot;
                    int Validity = 0;
                    int Price_Sales = 0;
                    int sp = 0;
                    int NumberPrint = 0;
                    int BatchCardid = 0;
                    string dateprint = "0";
                    string dateprint_comment_after = "0";
                    string byDayHour = "false";
                    string firstUse = "false";
                    string timeSave = "true";
                    string sizeSave = "true";
                    string sessionSave = "true";

                    string firstLogin = "0";
                    if (hotspt.comment.Contains("#") && hotspt.comment.Contains("%"))
                    {
                        try
                        {
                            string tmp = hotspt.comment.Replace("%", ";");
                            tmp = tmp.Replace("#", " ");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            dateprint = Convert.ToDateTime(split[0]).ToString("yyyy-MM-dd-hh-mm");
                            firstUse = split[1];
                        }
                        catch { }
                        try
                        {
                            if (hotspt.email.Contains("!"))
                            {

                                string tmp = hotspt.email.Replace("!", ";");
                                tmp = tmp.Replace("%", ";");
                                tmp = tmp.Replace("@", ";");
                                string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                                try { Validity = Convert.ToInt32(split[0]); } catch { };
                                try { Price_Sales = Convert.ToInt32(split[2]); } catch { };
                                profile = split[1];
                                //Price_Sales = split[2];
                                if (split[3] == "d.npdf")
                                    byDayHour = "true";
                            }
                        }
                        catch { }

                        string email =
                        profile + "'" +
                        Validity + "'" +
                        Price_Sales + "'" +
                        sp + "'" +
                        //NumberPrint + "'" +
                         "0!0'" +
                        dateprint + "'"
                        + byDayHour + "'" +
                        firstUse + "'" +
                        timeSave + "'" +
                        sizeSave + "'" +
                        sessionSave + "'@smart.befor";

                        hs.email = email;
                        hs.comment = "";
                        user.Add(hs);
                    }


                    if (hotspt.comment.Contains("^") && hotspt.comment.Contains("*"))
                    {
                        try
                        {
                            string tmp = hotspt.comment.Replace("%", ";");
                            tmp = tmp.Replace("!", " ");
                            tmp = tmp.Replace("%", ";");
                            tmp = tmp.Replace("^", ";");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            firstLogin = utils.StringDatetimeToUnixTimeStamp(split[0]).ToString();
                            dateprint_comment_after = split[1];
                            dateprint = Convert.ToDateTime(split[1]).ToString("yyyy-MM-dd-hh-mm");
                        }
                        catch { }
                        try
                        {
                            string tmp = hotspt.email.Replace("!", ";");
                            tmp = tmp.Replace("%", ";");
                            tmp = tmp.Replace("@", ";");
                            string[] split = tmp.Split(new string[] { ";" }, StringSplitOptions.None);
                            try { Validity = Convert.ToInt32(split[0]); } catch { };
                            try { Price_Sales = Convert.ToInt32(split[2]); } catch { };
                            profile = split[1];

                            if (split[3] == "d.npdf")
                                byDayHour = "true";
                        }
                        catch { }

                        string email =
                        profile + "'" +
                        Validity + "'" +
                        Price_Sales + "'" +
                        sp + "'" +
                        NumberPrint + "'" +
                        dateprint + "'"
                        + byDayHour + "'" +
                        firstUse + "'" +
                        timeSave + "'" +
                        sizeSave + "'" +
                        sessionSave + "'@smart.after";


                        hs.email = email;

                        if (Global_Variable.Mk_resources.version <= 6)
                            hs.comment = dateprint_comment_after;
                        else
                            hs.comment = Convert.ToDateTime(dateprint_comment_after).ToString("yyyy-MM-dd hh:mm:ss");


                        user.Add(hs);
                    }

                }
                if (user.Count <= 0)
                {
                    RJMessageBox.Show("لا يوجد كروت فيها اصدار نظام الصلاحيات من الاصدارات القديمة ");
                    return;
                }
                string result = Mk_DataAccess.Add_To_UserHotspot_SmartScript(user);
                //Global_Variable.StartThreadProcessFromMK = false;
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", "تم العميلة");

            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false;
                Global_Variable.Update_Um_StatusBar(true, true, 0, "", " حدث مشكلة اثناء المعالجة");

            }

        }

    }
}
