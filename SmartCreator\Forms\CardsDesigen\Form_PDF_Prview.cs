﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Entities;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Models.hotspot;

namespace SmartCreator.Forms.CardsDesigen
{
    public partial class Form_PDF_Prview : RJChildForm
    {
        public CardsTemplate card;
        public CardsTableDesg1 cardTable1;
        public string pathfile = "";
        public string type_template;
        Smart_DataAccess Smart_DA = new Smart_DataAccess();
        public Form_PDF_Prview(string templateName="default")
        {
            InitializeComponent();
            //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
            //   (MethodInvoker)delegate ()
            //   {
                   try
                   {
                       txt_houre.Text = "4";
                       txt_download.Text = "500";
                       CBox_SizeDownload.SelectedIndex = 0;
                       txt_price.Text = "100";
                       txt_validatiy.Text = "4";

                       txtNumberCard.Text = "51";
                       cbox_UserPassword_Pattern.SelectedIndex = 0;
                       cbox_User_NumberORcharcter.SelectedIndex = 0;
                       cbox_Pass_NumberORcharcter.SelectedIndex = 0;
                       txt_longUsers.Text = "8";
                       txt_longPassword.Text = "6";

                       CBox_TemplateCards.Text = templateName;
                       CBox_TemplateCards.Enabled = false;

                       set_fonts();
                       utils.Control_textSize(this);
                   }
                   catch { }
               //});


            

        }
        private void set_fonts()
        {
            btnAdd.TextAlign = ContentAlignment.MiddleCenter;

            if (UIAppearance.Theme == UITheme.Dark)
                pnl_Menul_profile.Customizable = false;

            txtNumberCard.RightToLeft
                = txt_StartCard.RightToLeft
                = txt_EndCard.RightToLeft
                = txt_longUsers.RightToLeft
                = txt_longPassword.RightToLeft = RightToLeft.No;


            Font lbl1 = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
             lbl_startCard.Font = lbl_endCards.Font = lbl_UserPassword_Pattern.Font=
             lbl_TemplateCards.Font = lbl_SellingPoint.Font= rjLabel1.Font=
             lbl_User_NumberORcharcter.Font = lbl_Pass_NumberORcharcter.Font = lbl_User_NumberORcharcter.Font = lbl_By_Number_Cards.Font=
             lbl1;

          
               

            cbox_User_NumberORcharcter.Font = cbox_UserPassword_Pattern.Font = cbox_Pass_NumberORcharcter.Font =
                CBox_SellingPoint.Font = CBox_SizeDownload.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            //checkBox_Add_Smart_Validatiy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
            lbl_price.Font =  lbl_houre.Font = lbl_validatiy.Font = lbl_download.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            btnAdd.Font =  Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
            //lbl_price.Font = rjLabel2.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            lbl_Title1.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            return;
        }

        private bool check_fields()
        {
            int numberChik;
            if (!(int.TryParse(txt_houre.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد الساعات بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_validatiy.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صلاحية الايام بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_price.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل السعر بشكل صحيح ");
                return false;
            }
            if (!(int.TryParse(txt_download.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل كمية التحميل بشكل صحيح ");
                return false;
            }
            if (Convert.ToInt32(txt_download.Text) > 0)
                if (CBox_SizeDownload.SelectedIndex == -1 || CBox_SizeDownload.Text == "")
                {
                    RJMessageBox.Show("حدد وحده التحميل");
                    return false;
                }

            if (!(int.TryParse(txtNumberCard.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد الكروت بشكل صحيح ");
                return false;
            }
            if (Convert.ToInt32(txtNumberCard.Text) < 2)
            {
                RJMessageBox.Show("عند اضافة كروت عشوائي يجب ان يكون اقل عدد للكروت 2 كروت");
                return false;
            }
            if (!(int.TryParse(txt_longUsers.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح الى طول اسم المستخدم");
                return false;
            }
            if ((Convert.ToInt16(txt_longUsers.Text) + txt_StartCard.Text.Length + txt_EndCard.Text.Length) < 3)
            {
                RJMessageBox.Show("يجب ان يكون طول رقم الكرت مع البادئة والاحقة اكبر من 4");
                //return false;
            }
            if (!(int.TryParse(txt_longPassword.Text, out numberChik)))
            {
                RJMessageBox.Show(" ادخل عدد صحيح في طول كلمة السر ");
                return false;
            }
            if (cbox_User_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم المستخدم");
                return false;
            }
            if (cbox_Pass_NumberORcharcter.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد نمط اسم كلمة السر");
                return false;
            }
            if (cbox_UserPassword_Pattern.SelectedIndex == -1)
            {
                RJMessageBox.Show(" حدد صيغة الكرت ");
                return false;
            }
            return true;
        }
        private void Get_SellingPoint()
        {
            //try
            //{
            //    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            //    CBox_SellingPoint = smart_DataAccess.Get_ComboBox_SellingPoint();
            //}
            //catch { }

            Smart_DataAccess da = new Smart_DataAccess();
            try
            {


                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }
            catch { }

        }
        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (check_fields() == false)
                return;

            saveFileDialogAddUser.FileName = "file1.pdf";
            saveFileDialogAddUser.Filter = "pdf files (*.pdf)|*.pdf|All files (*.*)|*.*";
            saveFileDialogAddUser.FileName = "test_Date_" + DateTime.Now.ToString("HHmmss") + "_" + DateTime.Now.ToString("yyyyMMdd");
            saveFileDialogAddUser.InitialDirectory =utils.Get_TempCards_Pdf_Directory();
            if (saveFileDialogAddUser.ShowDialog() == DialogResult.OK)
            {
                pathfile = saveFileDialogAddUser.FileName;
            }
            else
                return;

            Clss_InfoPrint clss_InfoPrint=new Clss_InfoPrint();
            clss_InfoPrint.NumberPrint = 81;
            clss_InfoPrint.BatchNumber = 109;

            clss_InfoPrint.Selected_template_item = CBox_TemplateCards.SelectedIndex;
            clss_InfoPrint.Number_Cards_ToAdd = Convert.ToInt32(txtNumberCard.Text);
            clss_InfoPrint.Number_Cards_ToAdd_DB = Convert.ToInt32(txtNumberCard.Text);
            //clss_InfoPrint.Profile_Name = frm.CBox_Profile.SelectedValue.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_User_NumberORcharcter_Value = cbox_User_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.User_Long = (Convert.ToInt32(txt_longUsers.Text));
            clss_InfoPrint.Mode_Password_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedItem.ToString();
            clss_InfoPrint.Mode_Password_NumberORcharcter_Value = cbox_Pass_NumberORcharcter.SelectedIndex;
            clss_InfoPrint.Password_Long = Convert.ToInt32(txt_longPassword.Text);
            clss_InfoPrint.UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;
            if (CBox_SellingPoint.Text != "" && CBox_SellingPoint.SelectedValue != null)
            {
                try
                {
                    clss_InfoPrint.SellingPoint_Name = CBox_SellingPoint.Text;
                    clss_InfoPrint.SellingPoint_Value = CBox_SellingPoint.SelectedValue.ToString();
                    clss_InfoPrint.SellingPoint_Value_str = CBox_SellingPoint.SelectedValue.ToString();
                    clss_InfoPrint.SellingPoint = Smart_DA.Get_SellingPoint_Code(CBox_SellingPoint.SelectedValue.ToString());
                }
                catch { }
            }
            clss_InfoPrint.StartCard = txt_StartCard.Text.Trim();
            clss_InfoPrint.EndCard = txt_EndCard.Text.Trim();
            clss_InfoPrint.pathfile = pathfile;


            CLS_Generate_Random_Cards cLS_Genrate_Cards = new CLS_Generate_Random_Cards(clss_InfoPrint, new HashSet<string>());
            New_Generate_Cards new_Generate_Cards = cLS_Genrate_Cards.Generate_Cards(Convert.ToInt32(txtNumberCard.Text), "", false);

            UmProfile profile = new UmProfile();
            profile.Name = "test";
            profile.Price_Disply = (txt_price.Text);
            profile.Price = Convert.ToInt32(txt_price.Text);
            profile.UptimeLimit = Convert.ToDouble(txt_houre.Text)*60*60;
            profile.Validity = Convert.ToDouble(txt_validatiy.Text);
            if (txt_download.Text != "0")
            {
                if (CBox_SizeDownload.SelectedIndex == 0)
                    profile.TransferLimit = (Convert.ToDouble(txt_download.Text) * 1024 * 1024);
                if (CBox_SizeDownload.SelectedIndex == 1)
                    profile.TransferLimit = (Convert.ToDouble(txt_download.Text) * 1024 * 1024 * 1024);
            }

            //if (CBox_SizeDownload.SelectedIndex==0)
            //    profile.TransferLimit = Convert.ToDouble(txt_download.Text)*1024*1024;
            //else if (CBox_SizeDownload.SelectedIndex == 1)
            //    profile.TransferLimit = Convert.ToDouble(txt_download.Text)*1024*1024;

            clss_InfoPrint.profile = profile;
            print_pdf(new_Generate_Cards.dicUser,clss_InfoPrint, card,cardTable1);

            this.Hide();
        }

        public void print_pdf()
        {

        }
        public void print_pdf(Dictionary<string, NewUserToAdd> dicUsers , Clss_InfoPrint clss_InfoPrint , CardsTemplate card=null , CardsTableDesg1 cardTable1=null)
        {
           
            //UmProfile profile = clss_InfoPrint.profile;

            Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName =clss_InfoPrint.profile.Name;
            string price = clss_InfoPrint.profile.Price_Disply;
            string Validity = clss_InfoPrint.profile.Validity.ToString();
            string time = clss_InfoPrint.profile.UptimeLimit.ToString(); 
 
            string sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
            string SP = "";
            string numberPrintId = "81";
            string batchNumberId = "81";
            string DatePrint = "105";
            string Note_On_Pages_text = "";


            if (card != null)
            {
                //card = new CardsTemplate();
                //card = JsonConvert.DeserializeObject<CardsTemplate>(Sourcecard.values);
                profileName = card.name;
                if (card.cardsItems.Price.Enable)
                {
                    if (card.cardsItems.Price.unit_show)
                    {
                        price = price + " " + card.setingCard.currency.ToString();
                    }
                    if (card.cardsItems.Price.title_show)
                    {
                        //price = price + " " + card.setingCard.currency.ToString();
                        price = card.cardsItems.Price.title_text + " " + price;

                    }
                }
                if (card.cardsItems.Validity.Enable)
                {
                    try
                    {
                        Validity = (clss_InfoPrint.profile.Validity.ToString());
                        if (clss_InfoPrint.profile.Validity > 0)
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(clss_InfoPrint.profile.Validity.ToString(), card.cardsItems.Validity);
                        else
                            Validity = "مفتوح";
                    }
                    catch { }
                }
                if (card.cardsItems.Time.Enable)
                {
                    time = clss_InfoPrint.profile.UptimeLimit.ToString();
                    try
                    {
                        if (clss_InfoPrint.profile.UptimeLimit > 0)
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(clss_InfoPrint.profile.UptimeLimit, card.cardsItems.Time);
                        else
                            time = "مفتوح";
                    }
                    catch { }

                }
                if (card.cardsItems.Size.Enable)
                {
                    sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
                    if (clss_InfoPrint.profile.TransferLimit > 0)
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, card.cardsItems.Size);
                    else
                        time = "مفتوح";
                }

                if (card.cardsItems.BatchNumber.Enable)
                {
                    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence()+1;
                    batchNumberId = batchNumber.ToString();

                    if (batchNumberId != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            batchNumberId = card.cardsItems.Number_Print.title_text + " " + batchNumberId;
                        }
                    }
                }
                if (card.cardsItems.Number_Print.Enable)
                {
                    int Number_Number = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint")+1;
                    numberPrintId = Number_Number.ToString();

                    if (numberPrintId != "")
                    {
                        if (card.cardsItems.Number_Print.title_show)
                        {
                            numberPrintId = card.cardsItems.Number_Print.title_text + " " + numberPrintId;
                        }
                    }
                }
                if (card.cardsItems.Date_Print.Enable)
                {
                    string format = card.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    if (card.cardsItems.Date_Print.title_show)
                    {
                        DatePrint = card.cardsItems.Date_Print.title_text + " " + DatePrint;
                    }
                }
                //if (card.cardsItems.Number_Print.Enable)
                //{
                //    //int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence(); 
                //    int Number_Number = (int)Smart_DA.Get_BatchCards_My_Sequence("NumberPrint"); 
                //    numberPrintId = Number_Number.ToString();
                //}
                //if (card.cardsItems.BatchNumber.Enable)
                //{
                //    int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                //    batchNumberId = batchNumber.ToString();
                //}
                if (card.cardsItems.SP.Enable)
                {
                    if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != null)
                    {
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());

                        if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (Show_sp.Code).ToString();
                        else
                            SP = (Show_sp.UserName).ToString();
                        if (card.cardsItems.SP.title_show)
                        {
                            SP = card.cardsItems.SP.title_text + " " + SP;
                        }
                    }
                }

                if (card.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (card.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = card.setingCard.Note_On_Pages_text;
                    }
                    else if (card.setingCard.NoteType_onPage == 1)
                    {
                        string format = card.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (card.setingCard.NoteType_onPage == 2)
                    {
                        Note_On_Pages_text = SP;
                    }
                }
            }
            else if(cardTable1!=null)
            {
                //cardTable1 = new CardsTableDesg1();
                //cardTable1 = JsonConvert.DeserializeObject<CardsTableDesg1>(Sourcecard.values);
                profileName = cardTable1.name;
                if (cardTable1.cardsItems.Price.Enable)
                {
                    price = clss_InfoPrint.profile.Price_Disply;
                    if (cardTable1.cardsItems.Price.unit_show)
                    {
                        price = clss_InfoPrint.profile.Price_Disply + " " + cardTable1.setingCard.currency.ToString();
                    }
                }
                if (cardTable1.cardsItems.Validity.Enable)
                {
                    try
                    {
                        Validity = (clss_InfoPrint.profile.Validity.ToString());
                        if (clss_InfoPrint.profile.Validity > 0)
                            Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(clss_InfoPrint.profile.Validity.ToString(), cardTable1.cardsItems.Validity);
                        else
                            Validity = "مفتوح";
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Time.Enable)
                {
                    time = clss_InfoPrint.profile.UptimeLimit.ToString();
                    try
                    {
                        if (clss_InfoPrint.profile.UptimeLimit > 0)
                        {
                            time = utils.Get_Seconds_in_Houre_or_DaysHoure(clss_InfoPrint.profile.UptimeLimit, cardTable1.cardsItems.Time);
                        }
                        else
                            time = "مفتوح";
                    }
                    catch { }

                }
                if (cardTable1.cardsItems.Size.Enable)
                {
                    sizeTransfer = clss_InfoPrint.profile.TransferLimit.ToString();
                    if (clss_InfoPrint.profile.TransferLimit > 0)
                        sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size);
                    else
                        time = "مفتوح";
                }
                if (cardTable1.cardsItems.Date_Print.Enable)
                {
                    string format = cardTable1.cardsItems.Date_Print.format;
                    DateTime now = DateTime.Now;
                    DatePrint = now.ToString("dd-MM-yyyy");
                    try
                    {
                        DatePrint = (now.ToString(format));
                    }
                    catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                }
                if (cardTable1.cardsItems.BatchNumber.Enable)
                {
                    try
                    {
                        int batchNumber = (int)Smart_DA.Get_BatchCards_My_Sequence();
                        batchNumberId = (batchNumber + 1).ToString();
                    }
                    catch { }
                }
                if (cardTable1.cardsItems.Number_Print.Enable)
                {
                    if (numberPrintId != "")
                    {
                        if (cardTable1.cardsItems.Number_Print.title_show)
                        {
                            numberPrintId = cardTable1.cardsItems.Number_Print.title_text + " " + numberPrintId;
                        }
                    }
                }
                if (cardTable1.cardsItems.SP.Enable)
                {
                    try
                    {
                        if (clss_InfoPrint.SellingPoint != null)
                        //if (clss_InfoPrint.SellingPoint_Value != "" && clss_InfoPrint.SellingPoint_Value != "-1" && clss_InfoPrint.SellingPoint_Value_str != null)
                        {
                            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                            //SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(clss_InfoPrint.SellingPoint_Value.ToString());
                            //if (Show_sp != null)
                            {
                                if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                                    SP = (clss_InfoPrint.SellingPoint.Code).ToString();
                                else
                                    SP = (clss_InfoPrint.SellingPoint.UserName).ToString();
                            }
                            if (card.cardsItems.SP.title_show)
                            {
                                SP = card.cardsItems.SP.title_text + " " + SP;
                            }
                        }
                    }
                    catch { }
                }
                if (cardTable1.setingCard.Note_On_Pages)
                {
                    Note_On_Pages_text = "";
                    if (cardTable1.setingCard.NoteType_onPage == 0)
                    {
                        Note_On_Pages_text = cardTable1.setingCard.Note_On_Pages_text;
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 1)
                    {
                        string format = cardTable1.cardsItems.Date_Print.format;
                        DateTime now = DateTime.Now;
                        Note_On_Pages_text = now.ToString("dd-MM-yyyy");
                        try
                        {
                            Note_On_Pages_text = (now.ToString(format));
                        }
                        catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                    }
                    else if (cardTable1.setingCard.NoteType_onPage == 2)
                    {
                        if (cardTable1.cardsItems.SP.Show_ByNumber_OR_Name)
                            SP = (clss_InfoPrint.SellingPoint.Code).ToString();
                        else
                            SP = (clss_InfoPrint.SellingPoint.UserName).ToString();

                        Note_On_Pages_text = SP;
                    }
                }
            }

            Cardsdata.Add("profile", profileName);
            Cardsdata.Add("price", price);
            Cardsdata.Add("Validity", Validity);
            Cardsdata.Add("time", time);
            Cardsdata.Add("sizeTransfer", sizeTransfer);
            Cardsdata.Add("sp", SP);
            Cardsdata.Add("batchNumber", batchNumberId);
            Cardsdata.Add("numberPrint", numberPrintId);
            Cardsdata.Add("DatePrint", DatePrint);
            Cardsdata.Add("pathfile", pathfile);
            Cardsdata.Add("Note_On_Pages_text", Note_On_Pages_text);
              
            CLS_Print print = new CLS_Print();
              
            if (card!= null)  
                print.Print_To_Pdf(dicUsers, Cardsdata, card, pathfile);

            else if(cardTable1 != null)
                print.Print_To_Pdf_table1(dicUsers, Cardsdata, cardTable1, pathfile);
            try
            {
                System.Diagnostics.Process.Start(pathfile);
            }
            catch { }

            //print.printPdf_New()
            //print.printPdf_New(NewUser2, Newpassword, sn, CBox_TemplateCards.SelectedValue.ToString(), data, pathfile, "0", CBox_TemplateCards.SelectedValue.ToString(), template_cards, template_items_cards_details);
            //printPdf_New_tmp(NewUser, Newpassword, Newpassword);
            //MessageBox.Show(" تم انشاء عينة من الكروت  ");
            //if (checkBoxSaveDefulte.Checked)
            //try
            //{
            //    System.Diagnostics.Process.Start(pathfile);
            //}
            //catch { }
        }

        private void lbl_TemplateCards_Click(object sender, EventArgs e)
        {

        }

        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void Form_PDF_Prview_Load(object sender, EventArgs e)
        {
            Get_SellingPoint();
            

        }
    }
}
