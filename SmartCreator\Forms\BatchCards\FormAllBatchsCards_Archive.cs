﻿//using ServiceStack;
//using ServiceStack.OrmLite;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Forms.BatchCards;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAllBatchsCards_Archive : RJChildForm
    {
        bool firstLoad = true;
        //OrmLiteConnectionFactory Local_dbFactory = null;
        //OrmLiteConnectionFactory Smart_dbFactory = null;
        //OrmLiteConnectionFactory Archive_dbFactory = null;
        Archive_DataAccess ArchiveDB = null;

        public FormAllBatchsCards_Archive()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            Smart_DataAccess sd = new Smart_DataAccess();
            //All_SP = sd.Get_Any<SellingPoint>();

            //Local_dbFactory = Sql_DataAccess.Get_dbFactory();
            //Smart_dbFactory = sd.dbFactory;
            ArchiveDB=new Archive_DataAccess();

            this.Text = "دفعات كروت الارشيف المعلقة";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Batch Cards Archive";
            }

            //dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersHeight = 40;
            btnAddNew.Font = btn_Add_To_Mikrotik.Font = btn_import_FromFile.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9, FontStyle.Bold);

            rjLabel4.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            rjLabel4.ForeColor = Color.Red;


            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);
            sideMenu();


           
            //utils.Control_textSize(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            utils.Control_textSize(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        void sideMenu()
        {
            //return;
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                //rjPane_Top.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);
            }
            else
            {
                rjPanel_back_side.Width = 260;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                //rjPane_Top.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 13, panel1.Location.Y - 1);
                //rjPane_Top.Location = new Point(panel1.Width + 13, 6);
                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Height-(panel1.Height - dgv.Location.Y));
            }
            //rjPanel_Page.Width = dgv.Width;
            panel1.Refresh();
            this.Refresh();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            //Init_dgv();
            //rjButton1_Click(sender, e);
            LoadDataGridviewData();

            firstLoad = false;
        }

        private void FormAllBatchsCards_Archive_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
        }
        public void LoadDataGridviewData()
        {
            loadData();
            //Get_Batch_Status_Cards Batch_Status = new Get_Batch_Status_Cards();
            //List<Get_Batch_Status_Cards> Status = Batch_Status.Get_Batch_Cards(Toggle_By_SP.Checked, Toggle_By_Profile.Checked, Toggle_By_Batch.Checked, Toggle_By_Status.Checked);

            //Batch_cards_FromDB cardsUserManagerFromDB = new Batch_cards_FromDB();
            //List<Batch_cards_FromDB> users = cardsUserManagerFromDB.Get_Batch_Cards(Status, Toggle_By_SP.Checked, Toggle_By_Profile.Checked, Toggle_By_Batch.Checked);

            //dgv.DataSource = users;
            //update_header_DGV();


        }

        private void btnDelete_Click(object sender, EventArgs e)
        {

        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            ////BatchArtchive batch = (BatchArtchive)dgv.CurrentRow.DataBoundItem;
            //Form_Print_FromArchive frm = new Form_Print_FromArchive();
            //frm.ShowDialog();

            Form_Add_Cards_ToArchive frm = new Form_Add_Cards_ToArchive();
            frm.ShowDialog();
            loadData();
            return;
            FormAddUsersManager formAddUsersManager = new FormAddUsersManager();
            //formAddUsersManager.btnAdd_Fast.Visible = false;
            formAddUsersManager.btn_add_One.Visible = false;
            formAddUsersManager.lbl_Title1.Text = "اخر عمليات دفعات الارشيف";
            formAddUsersManager.ShowDialog();
        }

        private void update_header_DGV()
        {
            try
            {
                if (dgv.DataSource == null)
                    return;
                //dgv.Columns["Str_Name"].DisplayIndex = 0;
                try { dgv.Columns["BatchNumber"].DisplayIndex = 0; } catch { }
                try {dgv.Columns["ProfileName"].DisplayIndex = 1; } catch { }
                try {dgv.Columns["Count"].DisplayIndex = 2; } catch { }
                try {dgv.Columns["AddedDate"].DisplayIndex = 3; } catch { }

                if (dgv.DataSource == null)
                    return;
                try {dgv.Columns["Sn_from"].DisplayIndex = 4; } catch { }
                try {dgv.Columns["Sn_to"].DisplayIndex = 5; } catch { }
                try {dgv.Columns["Count_Page"].DisplayIndex = 6; } catch { }
                try {dgv.Columns["Count_waiting"].DisplayIndex = 7; } catch { }
                try {dgv.Columns["Count_active"].DisplayIndex = 8; } catch { }
                try {dgv.Columns["Count_DeleteFormArchive"].DisplayIndex = 9; } catch { }
                //dgv.Columns["Str_Name"].DisplayIndex = 8;

                //dgv.Columns["Count_Page"].Width = 130;
                //dgv.Columns["Count_waiting"].Width = 130;
                //dgv.Columns["Count_active"].Width = 150;
                //dgv.Columns["Count_DeleteFormArchive"].Width = 150;
                //dgv.Columns["AddedDate"].Width = 150;


                try { dgv.Columns["Id"].Visible =false; } catch { }
                try { dgv.Columns["Str_Name"].Visible =false; } catch { }

            }
            catch { }

            try
            {
                //try { dgv.Columns["Count_deleteServer"].Width = 150; } catch { }
                try { dgv.Columns["Count"].Width = 150; } catch { }
                try { dgv.Columns["AddedDate"].Width = 150; } catch { }
                try { dgv.Columns["Count_Page"].Width = 150; } catch { }
                try { dgv.Columns["Count_waiting"].Width = 150; } catch { }
                try { dgv.Columns["Count_active"].Width = 150; } catch { }
                try { dgv.Columns["Count_DeleteFormArchive"].Width = 150; } catch { }
                //DGV_detail.Columns["BatchNumber"].DisplayIndex = 0;
            }
            catch { }
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            sideMenu();
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            //string fileName = "CardsArchive.db";
            string fileName =utils.Get_Database_Directory()+ "\\CardsArchive.db";
             //string Connection_string = "Data Source=db\\" + fileName + ";";
            //conn.LocalDB_path = "db\\" + fileName;

            Connections_Db conn = new Connections_Db
            {
                ArchiveDB_path = fileName,
                //ArchiveDB_path = @"db\CardsArchive.db",
                Connection_string= utils.Get_CardsArchive_ConnectionString(),
                //Connection_string= "Data Source=db\\" + fileName + ";",
                Default =1,
                Type= "SQLlite",
                FileName= "CardsArchive.db",
                Mk_code=Global_Variable.Mk_resources.RB_code,
                Mk_sn=Global_Variable.Mk_resources.RB_SN,
                Name= "CardsArchive",
                Soft_id=Global_Variable.Mk_resources.RB_Soft_id,
                //provider=SqliteDialect.Provider,
                
            };
            clss_CreateNewDatabase Ndb = new clss_CreateNewDatabase();
            if (Ndb.create_Archive_db(conn))
            {

            }

            return;
            if (File.Exists(conn.ArchiveDB_path))
            {

                //return;
            }



        }
        private void loadData()
        {
            string Rb = Global_Variable.Mk_resources.RB_code;
            string Rb_sn = Global_Variable.Mk_resources.RB_SN;
            string filter = "";
            if (txt_search.Text.Trim() != "")
            {
                
                filter += $"and ProfileName LIKE '%{txt_search.Text}%' " +
                    $" OR BatchNumber LIKE '%{txt_search.Text}%' "+
                    $" OR AddedDate LIKE '%{txt_search.Text}%' ";

            }

            dgv.DataSource = ArchiveDB.GetListAnyDB<BatchArtchive>($"select * from BatchArtchive where ( Rb='{Rb}' or Rb='{Rb_sn}' )  {filter}");

            update_header_DGV();


        }
        private void rjButton2_Click(object sender, EventArgs e)
        {
            rjButton1_Click(sender, e);
            loadData();
            
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
            {
                BatchArtchive batch = (BatchArtchive)dgv.Rows[e.RowIndex].DataBoundItem;
                FormAllCards_ِArchive frm = new FormAllCards_ِArchive(batch);
                frm.ShowDialog();

            }
        }

 
        private void ViewCards_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count > 0)
            {
                BatchArtchive batch = (BatchArtchive)dgv.CurrentRow.DataBoundItem;
                FormAllCards_ِArchive frm = new FormAllCards_ِArchive(batch);
                frm.ShowDialog();

            }
        }

        private void Delete_ToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void PrintToMK_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count > 0)
            {
                try
                {
                    BatchArtchive batch = (BatchArtchive)dgv.CurrentRow.DataBoundItem;
                    Form_Print_FromArchive frm = new Form_Print_FromArchive(batch);
                    frm.ShowDialog();
                }
                catch { }

            }
        }
        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV();
        }

        private void btnDelete_Click_1(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من حذف الكروت من الارشيف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                try
                {
                    string Id = dgv.CurrentRow.Cells["Id"].Value.ToString();
                    string BatchNumber = dgv.CurrentRow.Cells["BatchNumber"].Value.ToString();
                    if (ArchiveDB.Delete<BatchArtchive>($"delete from BatchArtchive where  Id={Id}"))
                    //if (ArchiveDB.DeleteById<BatchArtchive>(Id))
                    {
                        if (ArchiveDB.Delete<CardsArtchive>($"delete from CardsArtchive where  BatchCardId={BatchNumber}"))
                        {

                            RJMessageBox.Show("تم حذف الدفعه مع كروتها بنجاح");
                        }
                    }
                    loadData();
                }

                catch { }
            }
        }

        private void btn_Add_To_Mikrotik_Click(object sender, EventArgs e)
        {
            //BatchArtchive batch = (BatchArtchive)dgv.CurrentRow.DataBoundItem;
            Form_Print_FromArchive frm = new Form_Print_FromArchive();
            frm.ShowDialog();

            //Form_Add_Cards_ToArchive frm = new Form_Add_Cards_ToArchive();
            //frm.ShowDialog();
            //loadData();
            //return;
            //FormAddUsersManager formAddUsersManager = new FormAddUsersManager();
            //formAddUsersManager.btnAdd_Fast.Visible = false;
            //formAddUsersManager.btn_add_One.Visible = false;
            //formAddUsersManager.lbl_Title1.Text = "اخر عمليات دفعات الارشيف";
            //formAddUsersManager.ShowDialog();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            loadData();
        }

        private void FormAllBatchsCards_Archive_SizeChanged(object sender, EventArgs e)
        {
            if (this.Width > 1300)
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
            else
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
                
            }
            update_header_DGV();
        }

        private void btn_All_cards_Click(object sender, EventArgs e)
        {
                FormAllCards_ِArchive frm = new FormAllCards_ِArchive();
                frm.ShowDialog();

        }
    }
}
