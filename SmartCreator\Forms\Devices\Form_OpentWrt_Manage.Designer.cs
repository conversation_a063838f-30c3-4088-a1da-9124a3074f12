﻿namespace SmartCreator.Forms.Devices
{
    partial class Form_OpentWrt_Manage
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel19 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Device_Type = new SmartCreator.RJControls.RJComboBox();
            this.txtPassword = new SmartCreator.RJControls.RJTextBox();
            this.txtUsername = new SmartCreator.RJControls.RJTextBox();
            this.txtHost = new SmartCreator.RJControls.RJTextBox();
            this.txtPort = new SmartCreator.RJControls.RJTextBox();
            this.btnConnect = new SmartCreator.RJControls.RJButton();
            this.btnDisconnect = new SmartCreator.RJControls.RJButton();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.lbl_cpu_type = new SmartCreator.RJControls.RJLabel();
            this.lbl_viriton = new SmartCreator.RJControls.RJLabel();
            this.lbl_deviceName = new SmartCreator.RJControls.RJLabel();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.grpBoxRXBitrate = new System.Windows.Forms.GroupBox();
            this.cirPbRXBitrate = new CircularProgressBar.CircularProgressBar();
            this.grpBoxTXBitrate = new System.Windows.Forms.GroupBox();
            this.cirPbTXBitrate = new CircularProgressBar.CircularProgressBar();
            this.grpBoxSignal = new System.Windows.Forms.GroupBox();
            this.cirPbSignal = new CircularProgressBar.CircularProgressBar();
            this.chkBoxAuto = new System.Windows.Forms.CheckBox();
            this.grpBoxLog = new System.Windows.Forms.GroupBox();
            this.txtLog = new SmartCreator.RJControls.RJTextBox();
            this.CBox_Server_Profile = new SmartCreator.RJControls.RJComboBox();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.tabControl1 = new System.Windows.Forms.TabControl();
            this.tabPage_SSiD = new System.Windows.Forms.TabPage();
            this.txt_ssid_2g = new SmartCreator.RJControls.RJTextBox();
            this.txt_ssid_5g = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.tabPage_Netowrk = new System.Windows.Forms.TabPage();
            this.tabPage_Syetem = new System.Windows.Forms.TabPage();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.grpBoxRXBitrate.SuspendLayout();
            this.grpBoxTXBitrate.SuspendLayout();
            this.grpBoxSignal.SuspendLayout();
            this.grpBoxLog.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.tabControl1.SuspendLayout();
            this.tabPage_SSiD.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.tabControl1);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.rjButton1);
            this.pnlClientArea.Controls.Add(this.grpBoxLog);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 588);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(170, 22);
            this.lblCaption.Text = "Form_OpentWrt_Manage";
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 5;
            this.rjPanel1.Controls.Add(this.rjLabel19);
            this.rjPanel1.Controls.Add(this.rjLabel18);
            this.rjPanel1.Controls.Add(this.rjLabel17);
            this.rjPanel1.Controls.Add(this.rjLabel16);
            this.rjPanel1.Controls.Add(this.rjLabel13);
            this.rjPanel1.Controls.Add(this.CBox_Device_Type);
            this.rjPanel1.Controls.Add(this.txtPassword);
            this.rjPanel1.Controls.Add(this.txtUsername);
            this.rjPanel1.Controls.Add(this.txtHost);
            this.rjPanel1.Controls.Add(this.txtPort);
            this.rjPanel1.Controls.Add(this.btnConnect);
            this.rjPanel1.Controls.Add(this.btnDisconnect);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(10, 7);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(967, 80);
            this.rjPanel1.TabIndex = 36;
            // 
            // rjLabel19
            // 
            this.rjLabel19.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel19.AutoSize = true;
            this.rjLabel19.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel19.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel19.LinkLabel = false;
            this.rjLabel19.Location = new System.Drawing.Point(189, 10);
            this.rjLabel19.Name = "rjLabel19";
            this.rjLabel19.Size = new System.Drawing.Size(45, 17);
            this.rjLabel19.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel19.TabIndex = 115;
            this.rjLabel19.Text = "البورت";
            // 
            // rjLabel18
            // 
            this.rjLabel18.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(314, 10);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.Size = new System.Drawing.Size(78, 17);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel18.TabIndex = 115;
            this.rjLabel18.Text = "كلمة المرور";
            // 
            // rjLabel17
            // 
            this.rjLabel17.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(486, 10);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(95, 17);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 115;
            this.rjLabel17.Text = "اسم المستخدم";
            // 
            // rjLabel16
            // 
            this.rjLabel16.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(677, 10);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(69, 17);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 115;
            this.rjLabel16.Text = "الايبي   IP";
            // 
            // rjLabel13
            // 
            this.rjLabel13.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Sans Arabic", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(844, 10);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.Size = new System.Drawing.Size(70, 17);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 115;
            this.rjLabel13.Text = "نوع الجهاز";
            // 
            // CBox_Device_Type
            // 
            this.CBox_Device_Type.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Device_Type.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Device_Type.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Device_Type.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Device_Type.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Device_Type.BorderRadius = 5;
            this.CBox_Device_Type.BorderSize = 1;
            this.CBox_Device_Type.Customizable = false;
            this.CBox_Device_Type.DataSource = null;
            this.CBox_Device_Type.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Device_Type.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Device_Type.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Device_Type.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Device_Type.Items.AddRange(new object[] {
            "OpenWrt",
            "Ubnqu",
            "TPlink",
            "Confast"});
            this.CBox_Device_Type.Location = new System.Drawing.Point(803, 33);
            this.CBox_Device_Type.Name = "CBox_Device_Type";
            this.CBox_Device_Type.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Device_Type.SelectedIndex = -1;
            this.CBox_Device_Type.Size = new System.Drawing.Size(154, 32);
            this.CBox_Device_Type.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Device_Type.TabIndex = 114;
            this.CBox_Device_Type.Texts = "";
            // 
            // txtPassword
            // 
            this.txtPassword._Customizable = false;
            this.txtPassword.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPassword.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPassword.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPassword.BorderRadius = 10;
            this.txtPassword.BorderSize = 1;
            this.txtPassword.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txtPassword.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPassword.Location = new System.Drawing.Point(276, 36);
            this.txtPassword.MultiLine = false;
            this.txtPassword.Name = "txtPassword";
            this.txtPassword.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPassword.PasswordChar = false;
            this.txtPassword.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPassword.PlaceHolderText = null;
            this.txtPassword.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPassword.Size = new System.Drawing.Size(170, 27);
            this.txtPassword.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPassword.TabIndex = 108;
            this.txtPassword.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtUsername
            // 
            this.txtUsername._Customizable = false;
            this.txtUsername.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtUsername.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtUsername.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtUsername.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtUsername.BorderRadius = 10;
            this.txtUsername.BorderSize = 1;
            this.txtUsername.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txtUsername.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtUsername.Location = new System.Drawing.Point(452, 36);
            this.txtUsername.MultiLine = false;
            this.txtUsername.Name = "txtUsername";
            this.txtUsername.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtUsername.PasswordChar = false;
            this.txtUsername.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtUsername.PlaceHolderText = null;
            this.txtUsername.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtUsername.Size = new System.Drawing.Size(170, 27);
            this.txtUsername.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtUsername.TabIndex = 108;
            this.txtUsername.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtHost
            // 
            this.txtHost._Customizable = false;
            this.txtHost.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtHost.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtHost.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtHost.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtHost.BorderRadius = 10;
            this.txtHost.BorderSize = 1;
            this.txtHost.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txtHost.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtHost.Location = new System.Drawing.Point(627, 36);
            this.txtHost.MultiLine = false;
            this.txtHost.Name = "txtHost";
            this.txtHost.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtHost.PasswordChar = false;
            this.txtHost.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtHost.PlaceHolderText = null;
            this.txtHost.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtHost.Size = new System.Drawing.Size(170, 27);
            this.txtHost.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtHost.TabIndex = 108;
            this.txtHost.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtPort
            // 
            this.txtPort._Customizable = false;
            this.txtPort.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtPort.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtPort.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtPort.BorderRadius = 10;
            this.txtPort.BorderSize = 1;
            this.txtPort.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txtPort.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtPort.Location = new System.Drawing.Point(177, 36);
            this.txtPort.MultiLine = false;
            this.txtPort.Name = "txtPort";
            this.txtPort.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtPort.PasswordChar = false;
            this.txtPort.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtPort.PlaceHolderText = null;
            this.txtPort.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtPort.Size = new System.Drawing.Size(92, 27);
            this.txtPort.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtPort.TabIndex = 110;
            this.txtPort.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btnConnect
            // 
            this.btnConnect.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnConnect.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnConnect.BorderRadius = 5;
            this.btnConnect.BorderSize = 1;
            this.btnConnect.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnConnect.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnConnect.FlatAppearance.BorderSize = 0;
            this.btnConnect.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnConnect.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnConnect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnConnect.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnConnect.ForeColor = System.Drawing.Color.White;
            this.btnConnect.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.btnConnect.IconColor = System.Drawing.Color.White;
            this.btnConnect.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnConnect.IconSize = 20;
            this.btnConnect.Location = new System.Drawing.Point(6, 7);
            this.btnConnect.Name = "btnConnect";
            this.btnConnect.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnConnect.Size = new System.Drawing.Size(151, 31);
            this.btnConnect.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnConnect.TabIndex = 24;
            this.btnConnect.Text = "اتصال";
            this.btnConnect.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnConnect.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnConnect.UseVisualStyleBackColor = false;
            this.btnConnect.Click += new System.EventHandler(this.btnConnect_Click);
            // 
            // btnDisconnect
            // 
            this.btnDisconnect.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisconnect.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisconnect.BorderRadius = 5;
            this.btnDisconnect.BorderSize = 1;
            this.btnDisconnect.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnDisconnect.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnDisconnect.FlatAppearance.BorderSize = 0;
            this.btnDisconnect.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDisconnect.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDisconnect.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDisconnect.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnDisconnect.ForeColor = System.Drawing.Color.White;
            this.btnDisconnect.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.btnDisconnect.IconColor = System.Drawing.Color.White;
            this.btnDisconnect.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDisconnect.IconSize = 20;
            this.btnDisconnect.Location = new System.Drawing.Point(6, 44);
            this.btnDisconnect.Name = "btnDisconnect";
            this.btnDisconnect.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnDisconnect.Size = new System.Drawing.Size(151, 31);
            this.btnDisconnect.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDisconnect.TabIndex = 24;
            this.btnDisconnect.Text = "قطع اتصال";
            this.btnDisconnect.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnDisconnect.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnDisconnect.UseVisualStyleBackColor = false;
            this.btnDisconnect.Click += new System.EventHandler(this.btnDisconnect_Click);
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 0;
            this.rjPanel2.Controls.Add(this.lbl_cpu_type);
            this.rjPanel2.Controls.Add(this.lbl_viriton);
            this.rjPanel2.Controls.Add(this.lbl_deviceName);
            this.rjPanel2.Controls.Add(this.rjLabel10);
            this.rjPanel2.Controls.Add(this.rjLabel9);
            this.rjPanel2.Controls.Add(this.rjLabel8);
            this.rjPanel2.Controls.Add(this.rjLabel15);
            this.rjPanel2.Controls.Add(this.rjLabel14);
            this.rjPanel2.Controls.Add(this.rjLabel11);
            this.rjPanel2.Controls.Add(this.rjLabel7);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(434, 93);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(543, 192);
            this.rjPanel2.TabIndex = 116;
            // 
            // lbl_cpu_type
            // 
            this.lbl_cpu_type.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_cpu_type.AutoSize = true;
            this.lbl_cpu_type.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_cpu_type.Font = new System.Drawing.Font("Verdana", 9F);
            this.lbl_cpu_type.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_cpu_type.LinkLabel = false;
            this.lbl_cpu_type.Location = new System.Drawing.Point(30, 21);
            this.lbl_cpu_type.Name = "lbl_cpu_type";
            this.lbl_cpu_type.Size = new System.Drawing.Size(87, 14);
            this.lbl_cpu_type.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_cpu_type.TabIndex = 114;
            this.lbl_cpu_type.Text = "lbl_cpu_type";
            // 
            // lbl_viriton
            // 
            this.lbl_viriton.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_viriton.AutoSize = true;
            this.lbl_viriton.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_viriton.Font = new System.Drawing.Font("Verdana", 9F);
            this.lbl_viriton.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_viriton.LinkLabel = false;
            this.lbl_viriton.Location = new System.Drawing.Point(318, 65);
            this.lbl_viriton.Name = "lbl_viriton";
            this.lbl_viriton.Size = new System.Drawing.Size(68, 14);
            this.lbl_viriton.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_viriton.TabIndex = 114;
            this.lbl_viriton.Text = "lbl_viriton";
            // 
            // lbl_deviceName
            // 
            this.lbl_deviceName.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_deviceName.AutoSize = true;
            this.lbl_deviceName.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_deviceName.Font = new System.Drawing.Font("Verdana", 9F);
            this.lbl_deviceName.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_deviceName.LinkLabel = false;
            this.lbl_deviceName.Location = new System.Drawing.Point(321, 25);
            this.lbl_deviceName.Name = "lbl_deviceName";
            this.lbl_deviceName.Size = new System.Drawing.Size(65, 14);
            this.lbl_deviceName.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_deviceName.TabIndex = 114;
            this.lbl_deviceName.Text = "Horus-AP";
            // 
            // rjLabel10
            // 
            this.rjLabel10.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(455, 139);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel10.Size = new System.Drawing.Size(57, 17);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 113;
            this.rjLabel10.Text = "المعمارية";
            this.rjLabel10.Click += new System.EventHandler(this.rjLabel9_Click);
            // 
            // rjLabel9
            // 
            this.rjLabel9.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(442, 101);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(72, 17);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 113;
            this.rjLabel9.Text = "اصدار الكرنال";
            this.rjLabel9.Click += new System.EventHandler(this.rjLabel9_Click);
            // 
            // rjLabel8
            // 
            this.rjLabel8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(447, 61);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(70, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 113;
            this.rjLabel8.Text = "اصدار الجهاز";
            // 
            // rjLabel15
            // 
            this.rjLabel15.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(163, 101);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel15.Size = new System.Drawing.Size(55, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 113;
            this.rjLabel15.Text = "حجم الرام";
            // 
            // rjLabel14
            // 
            this.rjLabel14.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(150, 56);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel14.Size = new System.Drawing.Size(68, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 113;
            this.rjLabel14.Text = "حجم الذاكرة";
            // 
            // rjLabel11
            // 
            this.rjLabel11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(150, 21);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(65, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 113;
            this.rjLabel11.Text = "نوع المعالج";
            // 
            // rjLabel7
            // 
            this.rjLabel7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(451, 21);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(64, 17);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 113;
            this.rjLabel7.Text = "اسم الجهاز";
            // 
            // grpBoxRXBitrate
            // 
            this.grpBoxRXBitrate.Controls.Add(this.cirPbRXBitrate);
            this.grpBoxRXBitrate.Location = new System.Drawing.Point(144, 44);
            this.grpBoxRXBitrate.Name = "grpBoxRXBitrate";
            this.grpBoxRXBitrate.Size = new System.Drawing.Size(130, 143);
            this.grpBoxRXBitrate.TabIndex = 118;
            this.grpBoxRXBitrate.TabStop = false;
            this.grpBoxRXBitrate.Text = "RX";
            // 
            // cirPbRXBitrate
            // 
            this.cirPbRXBitrate.AnimationFunction = WinFormAnimation.KnownAnimationFunctions.Liner;
            this.cirPbRXBitrate.AnimationSpeed = 500;
            this.cirPbRXBitrate.BackColor = System.Drawing.Color.Transparent;
            this.cirPbRXBitrate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cirPbRXBitrate.Font = new System.Drawing.Font("Microsoft Sans Serif", 18F, System.Drawing.FontStyle.Bold);
            this.cirPbRXBitrate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cirPbRXBitrate.InnerColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.cirPbRXBitrate.InnerMargin = 2;
            this.cirPbRXBitrate.InnerWidth = -1;
            this.cirPbRXBitrate.Location = new System.Drawing.Point(3, 19);
            this.cirPbRXBitrate.MarqueeAnimationSpeed = 2000;
            this.cirPbRXBitrate.Maximum = 300;
            this.cirPbRXBitrate.Name = "cirPbRXBitrate";
            this.cirPbRXBitrate.OuterColor = System.Drawing.Color.Gray;
            this.cirPbRXBitrate.OuterMargin = -25;
            this.cirPbRXBitrate.OuterWidth = 26;
            this.cirPbRXBitrate.ProgressColor = System.Drawing.Color.Blue;
            this.cirPbRXBitrate.ProgressWidth = 18;
            this.cirPbRXBitrate.SecondaryFont = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.cirPbRXBitrate.Size = new System.Drawing.Size(124, 121);
            this.cirPbRXBitrate.StartAngle = 90;
            this.cirPbRXBitrate.Step = 1;
            this.cirPbRXBitrate.SubscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbRXBitrate.SubscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbRXBitrate.SubscriptText = "MBit/s";
            this.cirPbRXBitrate.SuperscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbRXBitrate.SuperscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbRXBitrate.SuperscriptText = "";
            this.cirPbRXBitrate.TabIndex = 11;
            this.cirPbRXBitrate.Text = "1";
            this.cirPbRXBitrate.TextMargin = new System.Windows.Forms.Padding(0);
            this.cirPbRXBitrate.Value = 1;
            // 
            // grpBoxTXBitrate
            // 
            this.grpBoxTXBitrate.Controls.Add(this.cirPbTXBitrate);
            this.grpBoxTXBitrate.Location = new System.Drawing.Point(277, 44);
            this.grpBoxTXBitrate.Name = "grpBoxTXBitrate";
            this.grpBoxTXBitrate.Size = new System.Drawing.Size(130, 143);
            this.grpBoxTXBitrate.TabIndex = 117;
            this.grpBoxTXBitrate.TabStop = false;
            this.grpBoxTXBitrate.Text = "TX";
            // 
            // cirPbTXBitrate
            // 
            this.cirPbTXBitrate.AnimationFunction = WinFormAnimation.KnownAnimationFunctions.Liner;
            this.cirPbTXBitrate.AnimationSpeed = 500;
            this.cirPbTXBitrate.BackColor = System.Drawing.Color.Transparent;
            this.cirPbTXBitrate.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cirPbTXBitrate.Font = new System.Drawing.Font("Microsoft Sans Serif", 18F, System.Drawing.FontStyle.Bold);
            this.cirPbTXBitrate.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cirPbTXBitrate.InnerColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.cirPbTXBitrate.InnerMargin = 2;
            this.cirPbTXBitrate.InnerWidth = -1;
            this.cirPbTXBitrate.Location = new System.Drawing.Point(3, 19);
            this.cirPbTXBitrate.MarqueeAnimationSpeed = 2000;
            this.cirPbTXBitrate.Maximum = 300;
            this.cirPbTXBitrate.Name = "cirPbTXBitrate";
            this.cirPbTXBitrate.OuterColor = System.Drawing.Color.Gray;
            this.cirPbTXBitrate.OuterMargin = -25;
            this.cirPbTXBitrate.OuterWidth = 26;
            this.cirPbTXBitrate.ProgressColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(0)))));
            this.cirPbTXBitrate.ProgressWidth = 18;
            this.cirPbTXBitrate.SecondaryFont = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.cirPbTXBitrate.Size = new System.Drawing.Size(124, 121);
            this.cirPbTXBitrate.StartAngle = 90;
            this.cirPbTXBitrate.Step = 1;
            this.cirPbTXBitrate.SubscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbTXBitrate.SubscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbTXBitrate.SubscriptText = "MBit/s";
            this.cirPbTXBitrate.SuperscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbTXBitrate.SuperscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbTXBitrate.SuperscriptText = "";
            this.cirPbTXBitrate.TabIndex = 11;
            this.cirPbTXBitrate.Text = "1";
            this.cirPbTXBitrate.TextMargin = new System.Windows.Forms.Padding(0);
            this.cirPbTXBitrate.Value = 1;
            // 
            // grpBoxSignal
            // 
            this.grpBoxSignal.Controls.Add(this.cirPbSignal);
            this.grpBoxSignal.Location = new System.Drawing.Point(9, 44);
            this.grpBoxSignal.Name = "grpBoxSignal";
            this.grpBoxSignal.Size = new System.Drawing.Size(130, 143);
            this.grpBoxSignal.TabIndex = 116;
            this.grpBoxSignal.TabStop = false;
            this.grpBoxSignal.Text = "Signal";
            // 
            // cirPbSignal
            // 
            this.cirPbSignal.AnimationFunction = WinFormAnimation.KnownAnimationFunctions.Liner;
            this.cirPbSignal.AnimationSpeed = 500;
            this.cirPbSignal.BackColor = System.Drawing.Color.Transparent;
            this.cirPbSignal.Dock = System.Windows.Forms.DockStyle.Fill;
            this.cirPbSignal.Font = new System.Drawing.Font("Microsoft Sans Serif", 18F, System.Drawing.FontStyle.Bold);
            this.cirPbSignal.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(64)))), ((int)(((byte)(64)))), ((int)(((byte)(64)))));
            this.cirPbSignal.InnerColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.cirPbSignal.InnerMargin = 2;
            this.cirPbSignal.InnerWidth = -1;
            this.cirPbSignal.Location = new System.Drawing.Point(3, 19);
            this.cirPbSignal.MarqueeAnimationSpeed = 2000;
            this.cirPbSignal.Name = "cirPbSignal";
            this.cirPbSignal.OuterColor = System.Drawing.Color.Gray;
            this.cirPbSignal.OuterMargin = -25;
            this.cirPbSignal.OuterWidth = 26;
            this.cirPbSignal.ProgressColor = System.Drawing.Color.FromArgb(((int)(((byte)(128)))), ((int)(((byte)(255)))), ((int)(((byte)(128)))));
            this.cirPbSignal.ProgressWidth = 18;
            this.cirPbSignal.SecondaryFont = new System.Drawing.Font("Microsoft Sans Serif", 10F);
            this.cirPbSignal.Size = new System.Drawing.Size(124, 121);
            this.cirPbSignal.StartAngle = 90;
            this.cirPbSignal.Step = 1;
            this.cirPbSignal.SubscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbSignal.SubscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbSignal.SubscriptText = "dBm";
            this.cirPbSignal.SuperscriptColor = System.Drawing.Color.FromArgb(((int)(((byte)(166)))), ((int)(((byte)(166)))), ((int)(((byte)(166)))));
            this.cirPbSignal.SuperscriptMargin = new System.Windows.Forms.Padding(0);
            this.cirPbSignal.SuperscriptText = "";
            this.cirPbSignal.TabIndex = 11;
            this.cirPbSignal.Text = "-68";
            this.cirPbSignal.TextMargin = new System.Windows.Forms.Padding(0);
            this.cirPbSignal.Value = 68;
            // 
            // chkBoxAuto
            // 
            this.chkBoxAuto.AutoSize = true;
            this.chkBoxAuto.Location = new System.Drawing.Point(360, 15);
            this.chkBoxAuto.Name = "chkBoxAuto";
            this.chkBoxAuto.Size = new System.Drawing.Size(47, 19);
            this.chkBoxAuto.TabIndex = 10;
            this.chkBoxAuto.Text = "Live";
            this.chkBoxAuto.UseVisualStyleBackColor = true;
            this.chkBoxAuto.CheckedChanged += new System.EventHandler(this.chkBoxAuto_CheckedChanged);
            // 
            // grpBoxLog
            // 
            this.grpBoxLog.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.grpBoxLog.Controls.Add(this.txtLog);
            this.grpBoxLog.Location = new System.Drawing.Point(10, 510);
            this.grpBoxLog.Name = "grpBoxLog";
            this.grpBoxLog.Size = new System.Drawing.Size(971, 78);
            this.grpBoxLog.TabIndex = 121;
            this.grpBoxLog.TabStop = false;
            this.grpBoxLog.Text = "Log";
            // 
            // txtLog
            // 
            this.txtLog._Customizable = false;
            this.txtLog.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtLog.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtLog.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtLog.BorderRadius = 0;
            this.txtLog.BorderSize = 0;
            this.txtLog.Dock = System.Windows.Forms.DockStyle.Fill;
            this.txtLog.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtLog.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtLog.Location = new System.Drawing.Point(3, 19);
            this.txtLog.MultiLine = true;
            this.txtLog.Name = "txtLog";
            this.txtLog.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtLog.PasswordChar = false;
            this.txtLog.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtLog.PlaceHolderText = null;
            this.txtLog.ScrollBars = System.Windows.Forms.ScrollBars.Vertical;
            this.txtLog.Size = new System.Drawing.Size(965, 56);
            this.txtLog.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtLog.TabIndex = 0;
            this.txtLog.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // CBox_Server_Profile
            // 
            this.CBox_Server_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Server_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Server_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Server_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_Profile.BorderRadius = 5;
            this.CBox_Server_Profile.BorderSize = 1;
            this.CBox_Server_Profile.Customizable = false;
            this.CBox_Server_Profile.DataSource = null;
            this.CBox_Server_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Server_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.CBox_Server_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Server_Profile.Items.AddRange(new object[] {
            "wlan0"});
            this.CBox_Server_Profile.Location = new System.Drawing.Point(85, 9);
            this.CBox_Server_Profile.Name = "CBox_Server_Profile";
            this.CBox_Server_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Server_Profile.SelectedIndex = -1;
            this.CBox_Server_Profile.Size = new System.Drawing.Size(271, 31);
            this.CBox_Server_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Server_Profile.TabIndex = 123;
            this.CBox_Server_Profile.Texts = "";
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 5;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold);
            this.rjButton1.ForeColor = System.Drawing.Color.White;
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.rjButton1.IconColor = System.Drawing.Color.White;
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 20;
            this.rjButton1.Location = new System.Drawing.Point(773, 432);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(94, 31);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton1.TabIndex = 124;
            this.rjButton1.Text = "Ping";
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton1.UseVisualStyleBackColor = false;
            // 
            // rjPanel3
            // 
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 0;
            this.rjPanel3.Controls.Add(this.chkBoxAuto);
            this.rjPanel3.Controls.Add(this.CBox_Server_Profile);
            this.rjPanel3.Controls.Add(this.grpBoxSignal);
            this.rjPanel3.Controls.Add(this.grpBoxTXBitrate);
            this.rjPanel3.Controls.Add(this.rjLabel2);
            this.rjPanel3.Controls.Add(this.grpBoxRXBitrate);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(10, 93);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(418, 192);
            this.rjPanel3.TabIndex = 125;
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Cairo", 9F, System.Drawing.FontStyle.Bold);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(8, 8);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(55, 23);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 115;
            this.rjLabel2.Text = "Interface";
            // 
            // tabControl1
            // 
            this.tabControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabControl1.Controls.Add(this.tabPage_SSiD);
            this.tabControl1.Controls.Add(this.tabPage_Netowrk);
            this.tabControl1.Controls.Add(this.tabPage_Syetem);
            this.tabControl1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold);
            this.tabControl1.Location = new System.Drawing.Point(10, 291);
            this.tabControl1.Name = "tabControl1";
            this.tabControl1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.tabControl1.RightToLeftLayout = true;
            this.tabControl1.SelectedIndex = 0;
            this.tabControl1.Size = new System.Drawing.Size(965, 219);
            this.tabControl1.TabIndex = 114;
            this.tabControl1.DrawItem += new System.Windows.Forms.DrawItemEventHandler(this.tabControl1_DrawItem);
            // 
            // tabPage_SSiD
            // 
            this.tabPage_SSiD.Controls.Add(this.txt_ssid_2g);
            this.tabPage_SSiD.Controls.Add(this.txt_ssid_5g);
            this.tabPage_SSiD.Controls.Add(this.rjLabel4);
            this.tabPage_SSiD.Controls.Add(this.rjLabel1);
            this.tabPage_SSiD.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.tabPage_SSiD.Location = new System.Drawing.Point(4, 34);
            this.tabPage_SSiD.Name = "tabPage_SSiD";
            this.tabPage_SSiD.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage_SSiD.Size = new System.Drawing.Size(957, 181);
            this.tabPage_SSiD.TabIndex = 0;
            this.tabPage_SSiD.Text = " اشارة الواي فاي";
            this.tabPage_SSiD.UseVisualStyleBackColor = true;
            // 
            // txt_ssid_2g
            // 
            this.txt_ssid_2g._Customizable = false;
            this.txt_ssid_2g.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_ssid_2g.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_ssid_2g.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_ssid_2g.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_ssid_2g.BorderRadius = 10;
            this.txt_ssid_2g.BorderSize = 1;
            this.txt_ssid_2g.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_ssid_2g.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_ssid_2g.Location = new System.Drawing.Point(667, 20);
            this.txt_ssid_2g.MultiLine = false;
            this.txt_ssid_2g.Name = "txt_ssid_2g";
            this.txt_ssid_2g.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_ssid_2g.PasswordChar = false;
            this.txt_ssid_2g.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_ssid_2g.PlaceHolderText = null;
            this.txt_ssid_2g.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_ssid_2g.Size = new System.Drawing.Size(200, 27);
            this.txt_ssid_2g.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_ssid_2g.TabIndex = 114;
            this.txt_ssid_2g.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_ssid_5g
            // 
            this.txt_ssid_5g._Customizable = false;
            this.txt_ssid_5g.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_ssid_5g.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_ssid_5g.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_ssid_5g.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_ssid_5g.BorderRadius = 10;
            this.txt_ssid_5g.BorderSize = 1;
            this.txt_ssid_5g.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_ssid_5g.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_ssid_5g.Location = new System.Drawing.Point(378, 21);
            this.txt_ssid_5g.MultiLine = false;
            this.txt_ssid_5g.Name = "txt_ssid_5g";
            this.txt_ssid_5g.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_ssid_5g.PasswordChar = false;
            this.txt_ssid_5g.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_ssid_5g.PlaceHolderText = null;
            this.txt_ssid_5g.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_ssid_5g.Size = new System.Drawing.Size(200, 27);
            this.txt_ssid_5g.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_ssid_5g.TabIndex = 115;
            this.txt_ssid_5g.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel4
            // 
            this.rjLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo Medium", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(584, 23);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(54, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 116;
            this.rjLabel4.Text = "SSID-5G";
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo Medium", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(870, 21);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(54, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 117;
            this.rjLabel1.Text = "SSID-2G";
            // 
            // tabPage_Netowrk
            // 
            this.tabPage_Netowrk.Location = new System.Drawing.Point(4, 34);
            this.tabPage_Netowrk.Name = "tabPage_Netowrk";
            this.tabPage_Netowrk.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage_Netowrk.Size = new System.Drawing.Size(957, 181);
            this.tabPage_Netowrk.TabIndex = 1;
            this.tabPage_Netowrk.Text = "الايبي والفيلان";
            this.tabPage_Netowrk.UseVisualStyleBackColor = true;
            // 
            // tabPage_Syetem
            // 
            this.tabPage_Syetem.Location = new System.Drawing.Point(4, 34);
            this.tabPage_Syetem.Name = "tabPage_Syetem";
            this.tabPage_Syetem.Padding = new System.Windows.Forms.Padding(3);
            this.tabPage_Syetem.Size = new System.Drawing.Size(957, 181);
            this.tabPage_Syetem.TabIndex = 2;
            this.tabPage_Syetem.Text = "الصلاحيات";
            this.tabPage_Syetem.UseVisualStyleBackColor = true;
            // 
            // timer1
            // 
            this.timer1.Interval = 2000;
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // Form_OpentWrt_Manage
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "Form_OpentWrt_Manage";
            this.ClientSize = new System.Drawing.Size(1000, 638);
            this.Name = "Form_OpentWrt_Manage";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_OpentWrt_Manage";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.Form_OpentWrt_Manage_FormClosing);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.grpBoxRXBitrate.ResumeLayout(false);
            this.grpBoxTXBitrate.ResumeLayout(false);
            this.grpBoxSignal.ResumeLayout(false);
            this.grpBoxLog.ResumeLayout(false);
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            this.tabControl1.ResumeLayout(false);
            this.tabPage_SSiD.ResumeLayout(false);
            this.tabPage_SSiD.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJButton btnConnect;
        private RJControls.RJTextBox txtPort;
        private RJControls.RJTextBox txtHost;
        private RJControls.RJTextBox txtPassword;
        private RJControls.RJTextBox txtUsername;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJButton btnDisconnect;
        private System.Windows.Forms.GroupBox grpBoxRXBitrate;
        private CircularProgressBar.CircularProgressBar cirPbRXBitrate;
        private System.Windows.Forms.GroupBox grpBoxTXBitrate;
        private CircularProgressBar.CircularProgressBar cirPbTXBitrate;
        private System.Windows.Forms.GroupBox grpBoxSignal;
        private System.Windows.Forms.CheckBox chkBoxAuto;
        private CircularProgressBar.CircularProgressBar cirPbSignal;
        private System.Windows.Forms.GroupBox grpBoxLog;
        private RJControls.RJComboBox CBox_Server_Profile;
        private RJControls.RJLabel lbl_viriton;
        private RJControls.RJLabel lbl_deviceName;
        private RJControls.RJPanel rjPanel3;
        private RJControls.RJButton rjButton1;
        private System.Windows.Forms.TabControl tabControl1;
        private System.Windows.Forms.TabPage tabPage_SSiD;
        private System.Windows.Forms.TabPage tabPage_Netowrk;
        private System.Windows.Forms.TabPage tabPage_Syetem;
        private RJControls.RJComboBox CBox_Device_Type;
        private RJControls.RJLabel rjLabel13;
        private RJControls.RJLabel rjLabel19;
        private RJControls.RJLabel rjLabel18;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJLabel lbl_cpu_type;
        private RJControls.RJTextBox txt_ssid_2g;
        private RJControls.RJTextBox txt_ssid_5g;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJTextBox txtLog;
        private System.Windows.Forms.Timer timer1;
    }
}