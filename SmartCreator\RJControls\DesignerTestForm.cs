using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار دعم الـ Designer
    /// </summary>
    public partial class DesignerTestForm : Form
    {
        private RJTabControl designerTabControl;

        public DesignerTestForm()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // 
            // DesignerTestForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(8F, 16F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(800, 600);
            this.Name = "DesignerTestForm";
            this.Text = "اختبار دعم الـ Designer - RJTabControl";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
            
            // 
            // designerTabControl
            // 
            this.designerTabControl = new RJTabControl();
            this.designerTabControl.Dock = DockStyle.Fill;
            this.designerTabControl.TabHeight = 45;
            this.designerTabControl.TabSpacing = 5;
            this.designerTabControl.TabPadding = 25;
            this.designerTabControl.ContentBorderSize = 2;
            this.designerTabControl.ContentBorderColor = Color.FromArgb(0, 122, 204);
            this.designerTabControl.ContentBorderRadius = 8;
            
            // إضافة تابات تجريبية
            AddSampleTabs();
            
            this.Controls.Add(this.designerTabControl);
            this.ResumeLayout(false);
        }

        private void AddSampleTabs()
        {
            // تاب الرئيسية
            var homeTab = new RJTabPage("الرئيسية", IconChar.Home);
            homeTab.BackColor = Color.FromArgb(0, 122, 204);
            homeTab.ForeColor = Color.White;
            homeTab.IconSize = 20;
            
            var homeLabel = new Label
            {
                Text = "🏠 مرحباً بك في الصفحة الرئيسية!\n\n" +
                       "هذا النموذج يختبر دعم الـ Designer:\n\n" +
                       "✅ يمكن إضافة RJTabControl من Toolbox\n" +
                       "✅ يمكن تعديل الخصائص في Properties\n" +
                       "✅ يمكن إضافة/إزالة التابات\n" +
                       "✅ يمكن تعديل خصائص كل تاب\n" +
                       "✅ يمكن التنقل بين التابات في وضع التصميم\n\n" +
                       "🎨 جميع خصائص RJButton متاحة للتابات!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204),
                Padding = new Padding(20)
            };
            homeTab.AddControl(homeLabel);
            this.designerTabControl.Tabs.Add(homeTab);

            // تاب الإعدادات
            var settingsTab = new RJTabPage("الإعدادات", IconChar.Cog);
            settingsTab.BackColor = Color.FromArgb(76, 175, 80);
            settingsTab.ForeColor = Color.White;
            settingsTab.IconSize = 18;
            settingsTab.BorderRadius = 10;
            
            var settingsPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 3,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 12,
                Padding = new Padding(20)
            };
            
            var settingsLabel = new Label
            {
                Text = "⚙️ صفحة الإعدادات\n\n" +
                       "خصائص هذا التاب:\n" +
                       "• BackColor = أخضر\n" +
                       "• IconSize = 18\n" +
                       "• BorderRadius = 10\n" +
                       "• يحتوي على RJPanel مع حدود\n\n" +
                       "يمكن تعديل جميع هذه الخصائص\n" +
                       "من Properties في Visual Studio!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            settingsPanel.Controls.Add(settingsLabel);
            settingsTab.AddControl(settingsPanel);
            this.designerTabControl.Tabs.Add(settingsTab);

            // تاب النص
            var textTab = new RJTabPage("محرر النص", IconChar.Edit);
            textTab.BackColor = Color.FromArgb(156, 39, 176);
            textTab.ForeColor = Color.White;
            textTab.IconSize = 22;
            textTab.Style = ControlStyle.Glass;
            
            var textBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 محرر النص التفاعلي\n\n" +
                       "هذا RJTextBox داخل RJTabPage!\n\n" +
                       "خصائص التاب:\n" +
                       "• BackColor = بنفسجي\n" +
                       "• IconSize = 22\n" +
                       "• Style = Glass\n\n" +
                       "يمكنك الكتابة هنا وتجربة النص...\n\n" +
                       "جميع خصائص RJButton متاحة للتاب:\n" +
                       "- الألوان والخطوط\n" +
                       "- الأيقونات والأحجام\n" +
                       "- الأنماط والحدود\n" +
                       "- وأكثر!\n\n" +
                       "🎨 تخصيص كامل من الـ Designer!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            textTab.AddControl(textBox);
            this.designerTabControl.Tabs.Add(textTab);

            // تاب المعلومات
            var infoTab = new RJTabPage("معلومات", IconChar.InfoCircle);
            infoTab.BackColor = Color.FromArgb(255, 152, 0);
            infoTab.ForeColor = Color.White;
            infoTab.IconSize = 20;
            infoTab.BorderRadius = 8;
            
            var infoTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "ℹ️ معلومات دعم الـ Designer:\n\n" +
                       "🎯 الميزات المضافة:\n\n" +
                       "1️⃣ RJTabControlDesigner:\n" +
                       "   • إضافة/إزالة التابات من القائمة المنسدلة\n" +
                       "   • التنقل بين التابات في وضع التصميم\n" +
                       "   • دعم النقر على التابات\n\n" +
                       "2️⃣ RJTabPageCollection:\n" +
                       "   • مجموعة التابات للـ Properties\n" +
                       "   • محرر مجموعة مخصص\n" +
                       "   • دعم إضافة/تعديل/حذف التابات\n\n" +
                       "3️⃣ خصائص محسنة:\n" +
                       "   • SelectedTab - التاب النشط\n" +
                       "   • SelectedIndex - فهرس التاب النشط\n" +
                       "   • Tabs - مجموعة التابات\n\n" +
                       "4️⃣ دعم كامل لخصائص RJButton:\n" +
                       "   • جميع الألوان والخطوط\n" +
                       "   • الأيقونات والأحجام\n" +
                       "   • الأنماط والحدود\n" +
                       "   • الأحداث والسلوكيات\n\n" +
                       "🚀 الآن يمكن استخدام RJTabControl\n" +
                       "بشكل كامل في Visual Studio Designer!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(255, 152, 0),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9),
                TextAlign = HorizontalAlignment.Left
            };
            infoTab.AddControl(infoTextBox);
            this.designerTabControl.Tabs.Add(infoTab);

            // تفعيل التاب الأول
            this.designerTabControl.SelectedIndex = 0;
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new DesignerTestForm();
            form.ShowDialog();
        }
    }
}
