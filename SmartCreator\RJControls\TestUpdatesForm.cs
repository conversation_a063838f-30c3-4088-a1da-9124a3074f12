using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار التحديثات الجديدة
    /// </summary>
    public partial class TestUpdatesForm : Form
    {
        private RJTabControl tabControl;
        private RJPanel testPanel;
        private RJTextBox testTextBox;

        public TestUpdatesForm()
        {
            InitializeComponent();
            SetupControls();
            AddTestTabs();
        }

        private void InitializeComponent()
        {
            this.Text = "اختبار التحديثات الجديدة - RJPanel & RJTextBox";
            this.Size = new Size(900, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void SetupControls()
        {
            // إنشاء TabControl مع RJPanel كـ contentPanel - بألوان افتراضية
            tabControl = new RJTabControl
            {
                Location = new Point(20, 20),
                Size = new Size(850, 400),
                TabHeight = 40,
                TabSpacing = 3,
                TabPadding = 20,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 10
                // ContentBackColor و TabsPanelBackColor يبقيان افتراضيين
            };

            // إنشاء RJPanel للاختبار
            testPanel = new RJPanel
            {
                Location = new Point(20, 450),
                Size = new Size(400, 200),
                BackColor = Color.FromArgb(255, 248, 248),
                BorderSize = 3,
                BorderColor = Color.FromArgb(244, 67, 54),
                BorderRadius = 15
            };

            var panelLabel = new Label
            {
                Text = "RJPanel مع حدود جديدة!\n\nBorderSize = 3\nBorderColor = أحمر\nBorderRadius = 15",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                ForeColor = Color.FromArgb(244, 67, 54)
            };
            testPanel.Controls.Add(panelLabel);

            // إنشاء RJTextBox للاختبار
            testTextBox = new RJTextBox
            {
                Location = new Point(450, 450),
                Size = new Size(400, 200),
                MultiLine = true,
                Text = "RJTextBox مع ReadOnly!\n\n" +
                       "هذا النص للقراءة فقط.\n" +
                       "لا يمكن تعديله.\n\n" +
                       "ReadOnly = true",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 10,
                BackColor = Color.FromArgb(248, 255, 248),
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };

            this.Controls.Add(tabControl);
            this.Controls.Add(testPanel);
            this.Controls.Add(testTextBox);
        }

        private void AddTestTabs()
        {
            // تاب RJPanel
            var panelTab = tabControl.AddTab("RJPanel", IconChar.Square);
            var panelDemo = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 4,
                BorderColor = Color.FromArgb(255, 193, 7),
                BorderRadius = 20,
                Padding = new Padding(20)
                // BackColor يبقى افتراضي
            };

            var panelInfo = new Label
            {
                Text = "🎨 RJPanel المحدث!\n\n" +
                       "✅ BorderSize - سمك الحدود\n" +
                       "✅ BorderColor - لون الحدود\n" +
                       "✅ BorderRadius - نصف قطر الحدود\n\n" +
                       "هذا Panel يستخدم الخصائص الجديدة:\n" +
                       "• BorderSize = 4\n" +
                       "• BorderColor = أصفر\n" +
                       "• BorderRadius = 20",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(255, 193, 7)
            };
            panelDemo.Controls.Add(panelInfo);
            panelTab.AddControl(panelDemo);

            // تاب RJTextBox
            var textBoxTab = tabControl.AddTab("RJTextBox", IconChar.Edit);
            var textBoxDemo = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 RJTextBox المحدث!\n\n" +
                       "✅ ReadOnly - للقراءة فقط\n\n" +
                       "هذا TextBox يستخدم الخاصية الجديدة:\n" +
                       "• ReadOnly = true\n" +
                       "• لا يمكن تعديل النص\n" +
                       "• مفيد للعرض فقط\n\n" +
                       "جرب النقر والكتابة - لن يحدث شيء! 🔒",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(156, 39, 176),
                BorderRadius = 8,
                BackColor = Color.FromArgb(252, 248, 255),
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };
            textBoxTab.AddControl(textBoxDemo);

            // تاب ContentPanel
            var contentTab = tabControl.AddTab("ContentPanel", IconChar.BorderAll);
            var contentInfo = new Label
            {
                Text = "🖼️ ContentPanel المحدث!\n\n" +
                       "الآن contentPanel هو RJPanel بدلاً من Panel العادي\n\n" +
                       "✅ ContentBorderSize\n" +
                       "✅ ContentBorderColor\n" +
                       "✅ ContentBorderRadius\n" +
                       "✅ ContentBackColor\n\n" +
                       "انظر للحدود الزرقاء حول هذه المنطقة!\n" +
                       "يمكن تخصيصها بالكامل 🎨",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            contentTab.AddControl(contentInfo);

            // تاب التجربة التفاعلية
            var interactiveTab = tabControl.AddTab("تجربة", IconChar.Play);
            var interactivePanel = new Panel { Dock = DockStyle.Fill };

            // زر تغيير حدود Panel
            var changePanelButton = new RJButton
            {
                Text = "تغيير حدود RJPanel",
                IconChar = IconChar.Square,
                Location = new Point(20, 20),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            changePanelButton.Click += (s, e) => ChangePanelBorder();

            // زر تبديل ReadOnly
            var toggleReadOnlyButton = new RJButton
            {
                Text = "تبديل ReadOnly",
                IconChar = IconChar.Lock,
                Location = new Point(240, 20),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            toggleReadOnlyButton.Click += (s, e) => ToggleReadOnly();

            // زر تغيير حدود المحتوى
            var changeContentButton = new RJButton
            {
                Text = "تغيير حدود المحتوى",
                IconChar = IconChar.BorderAll,
                Location = new Point(460, 20),
                Size = new Size(200, 40),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            changeContentButton.Click += (s, e) => ChangeContentBorder();

            var instructionLabel = new Label
            {
                Text = "🎮 جرب الأزرار أعلاه لرؤية التحديثات الجديدة!",
                Location = new Point(20, 80),
                Size = new Size(640, 30),
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(70, 70, 70),
                TextAlign = ContentAlignment.MiddleCenter
            };

            interactivePanel.Controls.Add(changePanelButton);
            interactivePanel.Controls.Add(toggleReadOnlyButton);
            interactivePanel.Controls.Add(changeContentButton);
            interactivePanel.Controls.Add(instructionLabel);
            interactiveTab.AddControl(interactivePanel);
        }

        private void ChangePanelBorder()
        {
            var random = new Random();
            var colors = new[] { 
                Color.FromArgb(244, 67, 54),   // أحمر
                Color.FromArgb(76, 175, 80),   // أخضر
                Color.FromArgb(0, 122, 204),   // أزرق
                Color.FromArgb(255, 193, 7),   // أصفر
                Color.FromArgb(156, 39, 176)   // بنفسجي
            };

            testPanel.BorderSize = random.Next(1, 6);
            testPanel.BorderColor = colors[random.Next(colors.Length)];
            testPanel.BorderRadius = random.Next(5, 25);
        }

        private void ToggleReadOnly()
        {
            testTextBox.ReadOnly = !testTextBox.ReadOnly;
            testTextBox.BackColor = testTextBox.ReadOnly ? 
                Color.FromArgb(248, 255, 248) : 
                Color.White;
            
            var status = testTextBox.ReadOnly ? "للقراءة فقط 🔒" : "قابل للتعديل ✏️";
            testTextBox.Text = $"RJTextBox - {status}\n\n" +
                              $"ReadOnly = {testTextBox.ReadOnly}\n\n" +
                              "جرب الكتابة الآن!";
        }

        private void ChangeContentBorder()
        {
            var random = new Random();
            var colors = new[] { 
                Color.FromArgb(0, 122, 204),   // أزرق
                Color.FromArgb(76, 175, 80),   // أخضر
                Color.FromArgb(244, 67, 54),   // أحمر
                Color.FromArgb(255, 193, 7),   // أصفر
            };

            tabControl.ContentBorderSize = random.Next(1, 5);
            tabControl.ContentBorderColor = colors[random.Next(colors.Length)];
            tabControl.ContentBorderRadius = random.Next(0, 20);
        }

        /// <summary>
        /// تشغيل النموذج
        /// </summary>
        public static void RunTest()
        {
            var form = new TestUpdatesForm();
            form.ShowDialog();
        }
    }
}
