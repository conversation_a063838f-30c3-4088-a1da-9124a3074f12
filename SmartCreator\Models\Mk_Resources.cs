﻿using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Models
{
    public class Base_Resources
    {
        public int version { get; set; } 
        public string version_str { get; set; } 
        public string factorySoftware { get; set; }
        public string freeMemory { get; set; }
        public string totalMemory { get; set; }
        public string cpu { get; set; }
        public string cpuCount { get; set; }
        public string cpuFrequency { get; set; }
        public string cpuLoad { get; set; }
        public string freeHddSpace { get; set; }
        public string totalHddSpace { get; set; }
        public string architectureName { get; set; }
        public string boardName { get; set; }
        public string platform { get; set; }
        public string identity { get; set; }
        public string RB_Soft_id { get; set; }
        public string RB_SN { get; set; }
        public string RB_code { get; set; }
    }
    public class Mk_Resources : Base_Resources
    {
        public Mk_Resources() { }
        public string uptime {  get; set; }
        public int verisonAfter_Float { get; set; } = 48;
        //=====================================
        public string ether1_MAC {  get; set; }
        public string RB_code_v2 {  get; set; }
        public string licenseCode {  get; set; }
        public string Pc_Code {  get; set; }
    }


    public class Mk_Files
    {
        [DisplayName("اسم الملف")]
        public string Name { get; set; }
        public string FullName { get; set; }
        [DisplayName("نوع الملف")]
        public string Type { get; set; }
        //=====================================

        //[DisplayName("حجم الملف")]
        [Browsable(false)]
        public double Size { get; set; }

        [DisplayName("حجم الملف")]
        public string Str_Size
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(Size.ToString());
                else
                    return utils.ConvertSize_Get_En(Size.ToString());
            }
        }


        [DisplayName("تاريخ الانشاء")]
        public DateTime? Creation_time { get; set; } = null;
        [DisplayName("المحتوى"),Browsable(false)]
        public string Contents { get; set; }
        public string Status {
            get
            {
                string backupFile = $"{utils.Get_Backup_Directory()}\\{Name}";
                if(File.Exists(backupFile))
                {
                    return "";
                }

                return "";
            }
        }
    }
}
