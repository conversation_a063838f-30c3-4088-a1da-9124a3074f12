﻿using DevComponents.DotNetBar;
using DevComponents.DotNetBar.Controls;
using DevComponents.Editors;
using SmartCreator.Forms;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.API;
using SmartCreator.RJForms;
using SmartCreator.RJForms.Private;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.IO.Compression;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Net.Mime.MediaTypeNames;
using Application = System.Windows.Forms.Application;
//using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.ViewModels
{
    public class App_Info
    {
        public App_Info() {
            //DesktopAlert.BeforeAlertDisplayed += new EventHandler(BeforeAlertDisplayed);

        }
        void BeforeAlertDisplayed(object sender, EventArgs e)
        {
            DesktopAlertWindow win = (DesktopAlertWindow)sender;
            // This is how to set custom colors when needed or do anything else
            // win.BackColor=Color.Red;
            // win.ForeColor=Color.Yellow;
        }
        public string Name { get; set; }= "9.0.6";
        public  double App_Verison { get; set; } = 9.0;
        public  double next_AppVerion { get; set; } = 9.0;

        public string AppVerion_Name { get; set; }
        public string APPId { get; set; } = "Smart9-Desktop";
        //public string APPId { get; set; } = "Smart-8.13-Desktop";
        public double AppBuilder { get; set; } = 6;
        //public double Next_AppBuilder { get; set; } = 1;
        public string rb_status { get; set; } = "Trial";
        public bool lbl_licence_type_header_active { get; set; } = false;

        //public  bool forceUpdate_updater { get; set; } = false;
        //public  string updater_Path_App { get; set; } = "";
        //public  bool forceUpdate_app { get; set; } = false;

        //public string msg_next_AppVerion { get; set; } = "";
        //public  bool Show_msg_next_AppVerion_pop { get; set; } = false;
        //public  bool notfy_finsh { get; set; } = false;
        //public  int notfy_finsh_days { get; set; } = 60;

        //MyDataClass.rb_status = "Licensed";
        //public  double AppBuilder = 16;
        //public  double Next_AppBuilder = 16;
        //public  string AppVerion_Name = "8.02.16";

    }
    public class CLS_FirstLoad
    {
        public CLS_FirstLoad() { }
        MainForm ff = null;
        Form_UsersInfo AC = null;
        public void button66_click()
        {
            //try
            //{
            //    FingerPrint f = new FingerPrint();
            //    Global_Variable.Pc_Code = f.Value();
            //}
            //catch { }

            string txt = get_licenceNumber(); 
            txt = CLS_Enpher.Enpher(txt, 5);
            txt = utils.Base64Encode(txt);
            txt = "ver=3$!" + txt;
            API_Server a = new API_Server();
            a.GetRouter2(txt);
            //API_Server.GetNotify();
            //setLoginFormDB();
            //check_firstOpen();
            //this.Hide();
            return;
        }
        public string get_licenceNumber()
        {
            //string SSResult = "";
            //string txtkay = (MyDataClass.RB_SN + "ds").ToUpper();MyDataClass.Server_Architecture
            //MyDataClass.RB_code = txtkay;
            //MyDataClass.licenseCode = txtkay;
            //string identif = GetSerial(txtkay);
            //SSResult = Base64Encode(txtkay);
            //MyDataClass.RB_code_v2 = utils.Base64Encode(MyDataClass.RB_SN + "ds&"+ MyDataClass.ether1_MAC);
            //string serialV22 = "=!ver=!2 &! RB_SN &! RB_code  &! pc_code &! sn_mac &! ether1_MAC &! Server_Architecture &! APPId  &! AppBuilder  &!  &! ";

            string V2 = "ver=3&!" +
                  Global_Variable.Mk_resources.RB_SN + "&!" +
                  Global_Variable.Mk_resources.RB_code + "&!" +
                  Global_Variable.Pc_Code + "&!" +
                  Global_Variable.Mk_resources.RB_code_v2 + "&!" +
                  Global_Variable.Mk_resources.ether1_MAC + "&!" +
                  Global_Variable.Mk_resources.architectureName + "&!" +
                  Global_Variable.App_Info.APPId + "&!" +
                  Global_Variable.App_Info.AppBuilder + "&!";

            //MessageBox.Show(V2);
            return V2;




        }

        public void GetProfileMode(LoginForm loginForm)
        {
            //LoginForm LoginForm = (LoginForm)RJMainForm.listChildForms.Find(x => x.Name == "LoginForm");

            //if (Global_Variable.Response_api == null)
            string id = "";
            string rb = "";
            try { id = Global_Variable.App_Info.AppVerion_Name + " " + Global_Variable.Mk_resources.RB_SN; } catch { }
            if (Global_Variable.Response_api.IsSuccessStatusCode == false)
            {
                MessageBox.Show(Global_Variable.Response_api.IsSuccessStatusCode.ToString());


                //if (Properties.Settings.Default.isActive)
                if (Properties.Settings.Default.isActive && Global_Variable.Mk_resources.licenseCode == Properties.Settings.Default.licenseCodeForActive)
                {
                    Properties.Settings.Default.countOpenoffline += 1;
                    Properties.Settings.Default.Save();

                    if (Properties.Settings.Default.countOpenoffline >= 10)
                    {
                        clear_cash();
                        RJMessageBox.Show($"الرجاء التحقق من اتصالك بالانترنت او تحديث البرنامج  - {id} " );
                    }
                }
                else
                {
                    clear_cash();
                    RJMessageBox.Show($"الرجاء التحقق من اتصالك بالانترنت او تحديث البرنامج  - {id} ");
                }

            }
            //if (Global_Variable.Response_api.Apdi == false && Global_Variable.Response_api != null)
            if (Global_Variable.Response_api.Apdi == false && Global_Variable.Response_api.IsSuccessStatusCode == true)
            {
                //MessageBox.Show(Global_Variable.Response_api.Apdi.ToString());
                //MessageBox.Show(Global_Variable.Response_api.IsSuccessStatusCode.ToString());
                //MessageBox.Show(Global_Variable.Response_api.IsSuccessStatusCode.ToString());

                clear_cash();
                RJMessageBox.Show($"يرجى تحديث البرنامج او مراسلة فريق عمل البرنامج  - {id} ");
                Environment.Exit(0);
                Application.Exit();
            }
            if (Global_Variable.Response_api.Rbdi == true && Global_Variable.Response_api.IsSuccessStatusCode == true)
            {
                clear_cash();
                RJMessageBox.Show($"تم ايقاف البرنامج اذا استمرت المشكلة يرجى مراسلة فريق عمل البرنامج - {id} ");
                Environment.Exit(0);
                Application.Exit();
            }
            //============================================================================
            //if (Properties.Settings.Default.countOpenoffline > 10)
            //{
            //    clear_cash();
            //    MessageBox.Show("الرجاء التحقق من اتصالك بالانترنت او تحديث البرنامج");
            //}

            //if (Global_Variable.Response_api.Apdi == false && Global_Variable.Response_api.IsSuccessStatusCode)
            //{
            //    Properties.Settings.Default.snactrue = "";
            //    clear_cash();
            //    Properties.Settings.Default.Save();
            //    MessageBox.Show("يرجى تحديث البرنامج او مراسلة فريق عمل البرنامج");
            //    Environment.Exit(0);
            //    Application.Exit();
            //    //this.Close();
            //}
            //if (Global_Variable.Response_api.Rbdi == true && Global_Variable.Response_api.IsSuccessStatusCode)
            //{
            //    Properties.Settings.Default.snactrue = "";
            //    clear_cash();
            //    Properties.Settings.Default.Save();
            //    MessageBox.Show("تم ايقاف البرنامج اذا استمرت المشكلة يرجى مراسلة فريق عمل البرنامج");
            //    Environment.Exit(0);
            //    Application.Exit();
            //    //this.Close();
            //}
            //RJMessageBox.Show("يرجى تحديث تخطي");
            if (Properties.Settings.Default.isActive == true)
            {
                if (Properties.Settings.Default.activationType == "desktop")
                {
                    if (Properties.Settings.Default.rb_active_exp > 0)
                    {
                        checkUpdate();
                        Global_Variable.App_Info.rb_status = "Licensed";
                        Global_Variable.App_Info.lbl_licence_type_header_active = true;
                        ff = new MainForm();
                        ff.StartPosition = FormStartPosition.Manual;
                        ff.Top = (Screen.PrimaryScreen.Bounds.Height - ff.Height) / 2;
                        ff.Left = (Screen.PrimaryScreen.Bounds.Width - ff.Width) / 2;
                        ff.Show();
                        //loginForm.Close();
                        loginForm.Hide();
                        if (Properties.Settings.Default.restCash)
                            clear_cash();
                        return;
                    }
                    else
                    {
                        AC = new Form_UsersInfo();
                        AC.ShowDialog();
                        if (Properties.Settings.Default.restCash)
                            clear_cash();
                        loginForm.Close();
                        return;
                    }
                }

                if (Properties.Settings.Default.activationType == "router")
                {
                    if (Properties.Settings.Default.rb_active_exp > 0 && Global_Variable.Mk_resources.licenseCode == Properties.Settings.Default.licenseCodeForActive)
                    {
                        checkUpdate();
                        Global_Variable.App_Info.rb_status = "Licensed";
                        Global_Variable.App_Info.lbl_licence_type_header_active = true;
                        ff = new MainForm();
                        ff.StartPosition = FormStartPosition.Manual;
                        ff.Top = (Screen.PrimaryScreen.Bounds.Height - ff.Height) / 2;
                        ff.Left = (Screen.PrimaryScreen.Bounds.Width - ff.Width) / 2;
                        ff.Show();
                        //loginForm.Close();
                        loginForm.Hide();
                        if (Properties.Settings.Default.restCash)
                            clear_cash();
                        return;
                    }
                    else
                    {
                        ////if (Settings.Default.isActive  && MyDataClass.licenseCode != Settings.Default.licenseCodeForActive)
                        ////{
                        ////    Settings.Default.rb_active_exp = 0;
                        ////    Settings.Default.NetworkName = "";
                        ////    Settings.Default.RigesterName = "";
                        ////    Settings.Default.mobail = "";
                        ////    Settings.Default.address = "";
                        ////    Settings.Default.country = "";
                        ////}
                        ////clear_cash();
                        AC = new Form_UsersInfo();
                        AC.ShowDialog();
                        if (Properties.Settings.Default.restCash)
                            clear_cash();
                        loginForm.Close();
                        //loginForm.Hide();
                        return;
                    }
                }
            }
            else
            {
                //if (Properties.Settings.Default.rb_active_exp > 0 && Global_Variable.Response_api != null && Global_Variable.Mk_resources.licenseCode == Properties.Settings.Default.licenseCodeForActive)
                if (Properties.Settings.Default.rb_active_exp > 0 && Global_Variable.Response_api.IsSuccessStatusCode == true && Global_Variable.Mk_resources.licenseCode == Properties.Settings.Default.licenseCodeForActive)
                {
                    checkUpdate();
                    ff = new MainForm();
                    loginForm.Hide();
                    ff.StartPosition = FormStartPosition.Manual;
                    ff.Top = (Screen.PrimaryScreen.Bounds.Height - ff.Height) / 2;
                    ff.Left = (Screen.PrimaryScreen.Bounds.Width - ff.Width) / 2;
                    ff.Show();
                    //loginForm.Close();
                    //loginForm.Hide();
                    if (Properties.Settings.Default.restCash)
                        clear_cash();
                    return;
                }
                else
                {
                    //clear_cash();
                    AC = new Form_UsersInfo();
                    loginForm.Hide();
                    AC.StartPosition = FormStartPosition.Manual;
                    AC.Top = (Screen.PrimaryScreen.Bounds.Height - AC.Height) / 2;
                    AC.Left = (Screen.PrimaryScreen.Bounds.Width - AC.Width) / 2;
                    AC.ShowDialog();
                    loginForm.Close();
                    //loginForm.Hide();
                    //return;
                }
            }
           
            Environment.Exit(0);
            return;


            if (Global_Variable.Response_api.Rbex > 0)//trial
            {

                //ff.Show();
                //this.Hide();
                //return;
            }
            else if (Global_Variable.Response_api.Rbex <= 0)
            {
                //lf.ShowDialog();
                ////Environment.Exit(0);
                ////Application.Exit();
                //this.Close();
                //return;
            }
            else
            {
                MessageBox.Show("حاول مره اخرى");
                Environment.Exit(0);
                Application.Exit();
                //this.Close();

            }

        }
        void clear_cash()
        {
            Properties.Settings.Default.snactrue = "";
            Properties.Settings.Default.isActive = false;
            Properties.Settings.Default.script_add = "";
            Properties.Settings.Default.userman_print = "";
            Properties.Settings.Default.hotspot_print = "";
            Properties.Settings.Default.RigesterName = "";
            Properties.Settings.Default.mobail = "";
            Properties.Settings.Default.address = "";
            Properties.Settings.Default.active_Period = 0;
            Properties.Settings.Default.country = "";
            Properties.Settings.Default.email = "";
            Properties.Settings.Default.licenseCodeForActive = "";
            Properties.Settings.Default.Lsn_k_type = "router";
            Properties.Settings.Default.activationType = "router";
            Properties.Settings.Default.NetworkName = "";
            Properties.Settings.Default.rb_active_exp = 0;
            Properties.Settings.Default.Save();
        }



        //=======================
        private long _RunningAlertId = 0;
        ListBox listBoxAdv1= new ListBox();
        private void AlertClicked(long alertId)
        {
            listBoxAdv1.Items.Insert(0, "Alert clicked, ID: " + alertId.ToString());
        }
        public void checkUpdate()
        {
            App_Info App = Global_Variable.App_Info;
            Response_api ServerApp = Global_Variable.Response_api;
            if (ServerApp.Next_AppVerion > App.App_Verison || ( (ServerApp.Next_AppVerion == App.App_Verison) && ServerApp.Next_AppBuilder > App.AppBuilder) )
            {
                
                if (ServerApp.ForceUpdate_App == false)
                {
                    MessageForm f = new MessageForm();
                    f.TopMost = true;
                    if (ServerApp.SowMsgNext_AppVerion_pop)
                    {
                        _RunningAlertId++;
                        eDesktopAlertColor color = (eDesktopAlertColor)(eDesktopAlertColor.Default);
                        eAlertPosition position = (eAlertPosition)(eAlertPosition.BottomRight);
                        System.Media.SystemSounds.Hand.Play();
                        DesktopAlert.Show(ServerApp.MsgNextAppVerion, "\uf005", eSymbolSet.Awesome, Color.Empty, color, position, 5, _RunningAlertId, AlertClicked);
                        //this.Alert(MyDataClass.msg_next_AppVerion, Form_Alert.enmType.Info);
                        ////this.Alert("هناك تحديث جديد للبرنامج قم بالسماح للتحديث او قم بزياره موقع البرنامج وتنزيل الاصدار الجديد من البرنامج", Form_Alert.enmType.Info);
                    }
                    if ( Properties.Settings.Default.Show_updaeForm)
                    {
                        if (ServerApp.Next_AppVerion > Properties.Settings.Default.Show_updaeForm_V || ((ServerApp.Next_AppVerion == Properties.Settings.Default.Show_updaeForm_V) && ServerApp.Next_AppBuilder > Properties.Settings.Default.Show_updaeForm_AppBuilder))
                            f.ShowDialog();
                    }
                    //DialogResult result = MessageBox.Show("هناك تحديث جديد هل تريد تحديث البرنامج", "ملاحظة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    //if (result == DialogResult.No)

                    if (f.isOK == false)
                        return;
                    //MessageBox.Show(MyDataClass.updatePath);

                    _update();
                }
                else
                {
                    _update();

                    //if (downloadUpdateFile())
                    //{
                    //    createFile_toUpdate();
                    //    try { System.Diagnostics.Process.Start("update.exe"); } catch { }
                    //    Environment.Exit(0);
                    //    Application.Exit();
                    //    //this.Close();
                    //}

                    //createFile_toUpdate();
                    //downloadUpdateFile();
                    //try { System.Diagnostics.Process.Start("update.exe"); } catch { }
                    //Environment.Exit(0);
                    //Application.Exit();
                    ////this.Close();
                }

            }
        }

        public void _update()
        {
            using (Form_WaitForm fRM = new Form_WaitForm(dowonUpdate))
            {
                fRM.ShowDialog();
                //this.Close();
            }

           
        }
        void dowonUpdate()
        {
            if (downloadUpdateFile())
            {
                createFile_toUpdate();
                try { System.Diagnostics.Process.Start("update.exe"); } catch { }
                Environment.Exit(0);
                Application.Exit();
                //this.Close();
            }
            return;
        }
        void createFile_toUpdate()
        {
            try
            {
                //if (File.Exists(@"tempCards\\text\\update"))
                if (File.Exists(utils.Get_TempCards_Text_Directory()+"\\UserManager\\update"))
                {
                    File.Delete(utils.Get_TempCards_Text_Directory() + "\\UserManager\\update");
                }
            }
            catch { }
            try
            {
                //StreamWriter sw = new StreamWriter("tempCards\\text\\update");
                StreamWriter sw = new StreamWriter(utils.Get_TempCards_Text_Directory() + "\\UserManager\\update");
                string info = "";
                string current_verion = "8.13";
                string new_verion = "8.14";
                string forceUpdate_app = "";
                string updatePath = "";
                string FielsReplace = "";
                string changLog = "";

                //Write a line of text
                sw.WriteLine(utils.Base64Encode(Global_Variable.App_Info.App_Verison.ToString()));
                sw.WriteLine(utils.Base64Encode(Global_Variable.Response_api.Next_AppVerion.ToString()));
                sw.WriteLine(utils.Base64Encode(Global_Variable.Response_api.ForceUpdate_App.ToString()));
                sw.WriteLine(Global_Variable.Response_api.UpdatePath);
                sw.WriteLine(utils.Base64Encode(Global_Variable.Response_api.FielsReplace));
                sw.WriteLine(Global_Variable.Response_api.ChangLog);
                //Write a second line of text
                //sw.WriteLine("From the StreamWriter class");
                //Close the file
                sw.Close();
            }
            catch (Exception e)
            {
                Console.WriteLine("Exception: " + e.Message);
            }
            finally
            {
                Console.WriteLine("Executing finally block.");
            }
        }
        private  bool  downloadUpdateFile()
        {
            bool status = false;
            try
            {
                //WebClient webClient = new WebClient();
                //ServicePointManager.Expect100Continue = true;
                //SecurityProtocolType defaultProtocol = ServicePointManager.SecurityProtocol;
                //ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                //webClient.DownloadFileCompleted += new AsyncCompletedEventHandler(DownloadCompleted);
                //webClient.DownloadProgressChanged += new DownloadProgressChangedEventHandler(ProgressChanged);
                //try
                //{
                //    stopwatch.Start();
                //    webClient.DownloadFileAsync(new Uri(updatePath), Dest_Download_Path);
                //}
                //catch (Exception ex) { MessageBox.Show(ex.Message); }
                //ServicePointManager.SecurityProtocol = defaultProtocol;
                //btn_Update.Enabled = false;



                WebClient webClient = new WebClient();
                string sourceFile = Global_Variable.Response_api.Updater_Path_App;
                string destFile = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                Uri uri = new Uri(sourceFile);
                string filename = System.IO.Path.GetFileName(uri.AbsolutePath);
                string filename_ext = System.IO.Path.GetExtension(uri.AbsolutePath);

                ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;
                ServicePointManager.ServerCertificateValidationCallback += (sender, cert, chain, sslPolicyErrors) => true;

                webClient.DownloadFile(sourceFile.ToString(), filename);

                if (filename_ext == ".zip")
                {
                    string p = Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location);
                    var destinationPath = Directory.GetCurrentDirectory();
                    var zipPath = destinationPath + "\\" + filename;
                    //ZipFile.ExtractToDirectory(filename, destinationPath);
                    using (ZipArchive zip = ZipFile.Open(zipPath, ZipArchiveMode.Read))
                        foreach (ZipArchiveEntry entry in zip.Entries)
                        {
                            try
                            {
                                string fileName = (entry.Name);

                                entry.ExtractToFile(p + "\\" + fileName, true);
                            }
                            catch { }
                        }
                    File.Delete(zipPath);
                    status = true;
                }
            }
            catch (Exception e) {/* MessageBox.Show(e.Message);*/ }
            //btn_update.Enabled = false;
            return status;
        }


    }

}
