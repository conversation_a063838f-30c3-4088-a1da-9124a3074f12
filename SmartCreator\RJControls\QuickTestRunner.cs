using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// كلاس لتشغيل جميع الاختبارات بسرعة
    /// </summary>
    public static class QuickTestRunner
    {
        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            var form = new Form
            {
                Text = "اختيار نوع الاختبار",
                Size = new Size(500, 400),
                StartPosition = FormStartPosition.CenterScreen,
                BackColor = Color.FromArgb(45, 45, 48)
            };

            var panel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };

            var titleLabel = new Label
            {
                Text = "🧪 اختبار RJTabControl المحدث",
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                Location = new Point(20, 20),
                Size = new Size(400, 40),
                TextAlign = ContentAlignment.MiddleCenter
            };

            var descLabel = new Label
            {
                Text = "اختر نوع الاختبار الذي تريد تشغيله:",
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(200, 200, 200),
                Location = new Point(20, 70),
                Size = new Size(400, 30),
                TextAlign = ContentAlignment.MiddleCenter
            };

            // زر الاختبار السريع
            var quickTestButton = new RJButton
            {
                Text = "اختبار سريع",
                IconChar = IconChar.Bolt,
                Location = new Point(50, 120),
                Size = new Size(150, 50),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            quickTestButton.Click += (s, e) => {
                form.Hide();
                TestRJTabControl.QuickTest();
                form.Show();
            };

            // زر الاختبار الشامل
            var fullTestButton = new RJButton
            {
                Text = "اختبار شامل",
                IconChar = IconChar.Cogs,
                Location = new Point(220, 120),
                Size = new Size(150, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            fullTestButton.Click += (s, e) => {
                form.Hide();
                SimpleTabTestForm.RunTest();
                form.Show();
            };

            // زر الاختبار النهائي
            var finalTestButton = new RJButton
            {
                Text = "الاختبار النهائي",
                IconChar = IconChar.CheckCircle,
                Location = new Point(50, 190),
                Size = new Size(150, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            finalTestButton.Click += (s, e) => {
                form.Hide();
                FinalTestForm.RunTest();
                form.Show();
            };

            // زر اختبار التحديثات
            var updatesTestButton = new RJButton
            {
                Text = "اختبار التحديثات",
                IconChar = IconChar.Sync,
                Location = new Point(220, 190),
                Size = new Size(150, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            updatesTestButton.Click += (s, e) => {
                form.Hide();
                TestUpdatesForm.RunTest();
                form.Show();
            };

            // زر الخروج
            var exitButton = new RJButton
            {
                Text = "خروج",
                IconChar = IconChar.Times,
                Location = new Point(135, 260),
                Size = new Size(150, 40),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            exitButton.Click += (s, e) => form.Close();

            // معلومات إضافية
            var infoLabel = new Label
            {
                Text = "✅ جميع الأخطاء تم إصلاحها\n" +
                       "✅ RJPanel يدعم BorderSize و BorderColor\n" +
                       "✅ RJTextBox يدعم ReadOnly\n" +
                       "✅ tabsPanel و contentPanel الآن RJPanel\n" +
                       "✅ الألوان الافتراضية محفوظة",
                Font = new Font("Segoe UI", 9),
                ForeColor = Color.FromArgb(150, 150, 150),
                Location = new Point(20, 320),
                Size = new Size(400, 80),
                TextAlign = ContentAlignment.TopCenter
            };

            panel.Controls.Add(titleLabel);
            panel.Controls.Add(descLabel);
            panel.Controls.Add(quickTestButton);
            panel.Controls.Add(fullTestButton);
            panel.Controls.Add(finalTestButton);
            panel.Controls.Add(updatesTestButton);
            panel.Controls.Add(exitButton);
            panel.Controls.Add(infoLabel);

            form.Controls.Add(panel);
            form.ShowDialog();
        }

        /// <summary>
        /// اختبار سريع للميزات الأساسية
        /// </summary>
        public static void QuickFeatureTest()
        {
            var form = new Form
            {
                Text = "اختبار سريع للميزات",
                Size = new Size(600, 500),
                StartPosition = FormStartPosition.CenterScreen
            };

            // TabControl بسيط
            var tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                ContentBorderSize = 2,
                ContentBorderColor = Color.FromArgb(0, 122, 204),
                ContentBorderRadius = 8
            };

            // تاب RJPanel
            var panelTab = tabControl.AddTab("RJPanel", IconChar.Square);
            var testPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BorderSize = 3,
                BorderColor = Color.FromArgb(76, 175, 80),
                BorderRadius = 12,
                Padding = new Padding(20)
            };
            var panelLabel = new Label
            {
                Text = "🎨 RJPanel مع الحدود الجديدة!\n\nBorderSize = 3\nBorderColor = أخضر\nBorderRadius = 12",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(76, 175, 80)
            };
            testPanel.Controls.Add(panelLabel);
            panelTab.AddControl(testPanel);

            // تاب RJTextBox
            var textTab = tabControl.AddTab("RJTextBox", IconChar.Edit);
            var testTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                Text = "📝 RJTextBox مع ReadOnly!\n\nReadOnly = true\nهذا النص للقراءة فقط\nلا يمكن تعديله\n\nجرب الكتابة - لن يحدث شيء! 🔒",
                ReadOnly = true,
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(244, 67, 54),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 11),
                TextAlign = HorizontalAlignment.Center
            };
            textTab.AddControl(testTextBox);

            // تاب المعلومات
            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            var infoLabel = new Label
            {
                Text = "🎉 تم إصلاح جميع الأخطاء!\n\n" +
                       "✅ GetRoundedPath → GetRoundedGPath\n" +
                       "✅ IconChar.Border → IconChar.BorderAll\n" +
                       "✅ RJTabControlTestForm.RunTest() أضيفت\n\n" +
                       "🚀 جميع الميزات تعمل بشكل مثالي!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            infoTab.AddControl(infoLabel);

            form.Controls.Add(tabControl);
            form.ShowDialog();
        }
    }
}
