﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Common;
using System.Data.SqlClient;
using System.Globalization;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using static SmartCreator.Models.SalesAnalysis;

namespace SmartCreator.Models
{
    public struct RevenueByDate
    {
        public string Date { get; set; }
        public decimal TotalAmount { get; set; }
    }
  
    public class Dashboard /*: DbConnection*/
    {
        //Fields & Properties
        private DateTime startDate;
        private Int32 startDate2;
        private DateTime endDate;
        private Int32 endDate2;
        private int numberDays;
        
       
      
        

        public int Server_Type { get; private set; }
        private string TableUser { get;   set; } = "UmUser";
        private string TablePyment { get;   set; } = "UmPyment";
        private string TableSession { get;   set; } = "UmSession";
        private string Price { get;   set; } = "Price";

        public int NumberAtive { get; private set; }
        public int NumUserManager_users { get;  set; }
        //public int NumUserManager_users { get; private set; }
        public int NumUserManager_session { get; private set; }
        public int NumHotspot_users { get; private set; }
        public int NumHotspot_session { get; private set; }
        //public int NumProducts { get; private set; }
        public List<KeyValuePair<string, Int64>> TopProductsList { get; private set; }
        //public List<KeyValuePair<string, double>> UnderstockList { get; private set; }
        public List<BestProduct> Top5DeviceList { get; private set; }

        public List<RevenueByDate> GrossRevenueList { get; private set; }
        //public int NumOrders { get; set; }
        public decimal TotalRevenue { get; set; }
        //public decimal TotalProfit { get; set; }

        //Constructor
        public Dashboard(int server_Type,bool Commi=false)
        {
            Server_Type = server_Type;

            if (Commi)
                Price = "TotalPrice";

            if (server_Type == 0)
            {
                TableUser = "UmUser";
                TablePyment = "UmPyment";
                TableSession = "UmSession";
            }
            else if (server_Type == 1)
            {
                TableUser = "HsUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            //else if (server_Type == 2)
            //{
            //    TableUser = "PPPUser";
            //    TablePyment = "PPPPyment";
            //    TableSession = "PPPSession";
            //}
        }
        public void Dashboard_Server(int server_Type, bool Commi = false)
        {
            Server_Type = server_Type;

            if (Commi)
                Price = "TotalPrice";

            if (server_Type == 0)
            {
                TableUser = "UmUser";
                TablePyment = "UmPyment";
                TableSession = "UmSession";
            }
           else if (server_Type == 1)
            {
                TableUser = "HsUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            //else if (server_Type == 2)
            //{
            //    TableUser = "PPPUser";
            //    TablePyment = "PPPPyment";
            //    TableSession = "PPPSession";
            //}
        }

        //Private methods
        private void GetNumberItems()
        {


            Mk_DataAccess_old cl = new Mk_DataAccess_old();

            //command.Connection = connection;
            //Get Total Number of Customers
            //command.CommandText = "select count(id) from Customer";
            NumberAtive = cl.Get_Active_count();
            NumUserManager_users = cl.Get_usermanager_count();
            NumUserManager_session = cl.Get_usermanager_session_count();
                    NumHotspot_users = cl.Get_Hotsopot_count();
                    //NumHotspot_session = cl.Get_usermanager_session_count();

                    ////Get Total Number of Suppliers
                    //command.CommandText = "select count(id) from Supplier";
                    //NumSuppliers = (int)command.ExecuteScalar();

                    ////Get Total Number of Products
                    //command.CommandText = "select count(id) from Product";
                    //NumProducts = (int)command.ExecuteScalar();

                    ////Get Total Number of Orders
                    //command.CommandText = @"select count(id) from [Order]" +
                    //                        "where OrderDate between  @fromDate and @toDate";
                    //command.Parameters.Add("@fromDate", System.Data.SqlDbType.DateTime).Value = startDate;
                    //command.Parameters.Add("@toDate", System.Data.SqlDbType.DateTime).Value = endDate;
                    //NumOrders = (int)command.ExecuteScalar();
                
    
        }
        private void GetProductAnalisys()
        {
            Sql_DataAccess DA= new Sql_DataAccess();
            try
            {
                TopProductsList = new List<KeyValuePair<string, Int64>>();
                Top5DeviceList = new List<BestProduct>();

                string query_ByNasPortId = $@"SELECT  NasPortId as name ,sum(p.{Price}) as total
                                            FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                                            WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                                    + " group by u.NasPortId "
                                                    + $" ORDER by u.{Price}  DESC "
                                                    + " LIMIT 5 ;";

                DataTable dt = DA.RunSqlCommandAsDatatable(query_ByNasPortId);
                if (dt == null)
                    return;
                Top5DeviceList = (from r in dt.AsEnumerable()
                                  select new BestProduct 
                                  {
                                      Item = r.Field<string>("name"),
                                      UnitSold = Convert.ToDouble(r.Field<double>("total"))
                                  }).OrderByDescending(x=>x.UnitSold).ToList();


                string query_ByProfile = $@"SELECT  u.ProfileName as profile ,sum(p.{Price}) as total
                                          FROM {TableUser} u INNER JOIN  {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                                           WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                              + " group by u.ProfileName "
                                              + $" order by p.{Price} desc";

                DataTable dt2 = SqlDataAccess.RunSqlCommandAsDatatable(query_ByProfile);
                foreach (DataRow row in dt2.Rows)
                    TopProductsList.Add(new KeyValuePair<string, Int64>(row["profile"].ToString(), Convert.ToInt64(row["total"].ToString())));
            }
            catch(Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        public void GetTop5DeviceAnalisys(int _type=0)
        {
           
            Sql_DataAccess DA = new Sql_DataAccess();
            try
            {
                string query_ByNasPortId = "";
                Top5DeviceList = new List<BestProduct>();

                if (_type == 0)
                {
                    query_ByNasPortId = $@"SELECT  NasPortId as name ,sum(p.{Price}) as total
                                            FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                                            WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                                    + " group by u.NasPortId "
                                                    + $" ORDER by u.{Price}  DESC "
                                                    + " LIMIT 5 ;";
                    DataTable dt = DA.RunSqlCommandAsDatatable(query_ByNasPortId);
                    if (dt == null)
                        return;
                    Top5DeviceList = (from r in dt.AsEnumerable()
                                      select new BestProduct
                                      {
                                          Item = r.Field<string>("name"),
                                          UnitSold = Convert.ToDouble(r.Field<double>("total"))
                                      }).OrderByDescending(x => x.UnitSold).ToList();
                }
                else if (_type == 1)
                {
                    query_ByNasPortId = $@"SELECT  NasPortId as name ,(sum(u.DownloadUsed + u.UploadUsed)/1024/1024/1024) as total
                                            FROM {TableUser} u 
                                            WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                                    + " group by u.NasPortId "
                                                    + $" ORDER by total  DESC "
                                                    + " LIMIT 5 ;";
                    DataTable dt = DA.RunSqlCommandAsDatatable(query_ByNasPortId);
                    if (dt == null)
                        return;
                    Top5DeviceList = (from r in dt.AsEnumerable()
                                      select new BestProduct
                                      {
                                          Item = r.Field<string>("name"),
                                          UnitSold = Convert.ToDouble(r.Field<Int64>("total"))
                                      }).OrderByDescending(x => x.UnitSold).ToList();
                }
                else if (_type == 2)
                {
                    query_ByNasPortId = $@"SELECT  NasPortId as name ,sum(u.UptimeUsed)/60/60 as total
                                            FROM {TableUser} u 
                                            WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                                   + " group by u.NasPortId "
                                                   + $" ORDER by total  DESC "
                                                   + " LIMIT 5 ;";
                    DataTable dt = DA.RunSqlCommandAsDatatable(query_ByNasPortId);
                    if (dt == null)
                        return;
                    Top5DeviceList = (from r in dt.AsEnumerable()
                                      select new BestProduct
                                      {
                                          Item = r.Field<string>("name"),
                                          UnitSold = Convert.ToDouble(r.Field<Int64>("total"))
                                      }).OrderByDescending(x => x.UnitSold).ToList();
                }

                //DataTable dt = DA.RunSqlCommandAsDatatable(query_ByNasPortId);
                //if (dt == null)
                //    return;
                //Top5DeviceList = (from r in dt.AsEnumerable()
                //                  select new BestProduct
                //                  {
                //                      Item = r.Field<string>("name"),
                //                      UnitSold = Convert.ToDouble(r.Field<double>("total"))
                //                  }).OrderByDescending(x => x.UnitSold).ToList();


            }
            catch (Exception ex) {System.Windows.Forms.MessageBox.Show("GetTop5DeviceAnalisys\n"+ex.Message); }
        }

        public void GetProfileAnalisys(int _type=0)
        {
            //0=by sales  1=by size 2= by time
            Sql_DataAccess DA = new Sql_DataAccess();
            try
            {
                string query_ByProfile = "";
                TopProductsList = new List<KeyValuePair<string, Int64>>();

                if (_type == 0)
                {
                    query_ByProfile = $@"SELECT  u.ProfileName as profile ,sum(p.{Price}) as total
                                          FROM {TableUser} u INNER JOIN  {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                                           WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                            + " group by u.ProfileName "
                                            + $" order by p.{Price} desc";
                }
                else if (_type == 1)
                {
                    query_ByProfile = $@"SELECT  u.ProfileName as profile ,sum(u.DownloadUsed + u.UploadUsed) as total
                                          FROM {TableUser} as u
                                           WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                              + " group by u.ProfileName ; "
                                              + $" order by total desc";
                }
                else if (_type == 2)
                {
                    query_ByProfile = $@"SELECT  u.ProfileName as profile ,sum(u.UptimeUsed) as total
                                          FROM {TableUser} as u
                                           WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                             + " group by u.ProfileName ; "
                                             + $" order by total desc";

                }

                DataTable dt2 = SqlDataAccess.RunSqlCommandAsDatatable(query_ByProfile);
                foreach (DataRow row in dt2.Rows)
                    TopProductsList.Add(new KeyValuePair<string, Int64>(row["profile"].ToString(), Convert.ToInt64(row["total"].ToString())));

            }
            catch (Exception ex) { System.Windows.Forms.MessageBox.Show("GetProfileAnalisys\n" + ex.Message); }
        }


        private void GetOrderAnalisys()
        {
            try
            {
                GrossRevenueList = new List<RevenueByDate>();
                //TotalProfit = 0;
                TotalRevenue = 0;

                //using (var connection = GetConnection())
                //{
                //    connection.Open();
                //    using (var command = new SqlCommand())
                //    {
                //command.Connection = connection;
                //string query = @"SELECT date(u.FirsLogin, 'unixepoch', 'localtime') as Date ,sum(p.price) as TotalAmount
                string query = $@"SELECT date(u.FirsLogin) as Date ,sum(p.{Price}) as TotalAmount
                                      FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                                      WHERE  u.FirsLogin>='" + startDate.ToString("yyyy-MM-dd") + "' and u.FirsLogin <= '" + endDate.ToString("yyyy-MM-dd") + "' "
                                           + " group by date(u.FirsLogin)";


                DataTable reader = SqlDataAccess.RunSqlCommandAsDatatable(query);
                if (reader == null)
                    return;

                var resultTable = new List<KeyValuePair<DateTime, decimal>>();
                foreach (DataRow row in reader.Rows)
                {
                    decimal price = Convert.ToDecimal(row[1].ToString());
                    DateTime date = Convert.ToDateTime(row[0].ToString());
                    resultTable.Add(
                        new KeyValuePair<DateTime, decimal>(date, price)
                        );
                    TotalRevenue += price;
                }
                //TotalProfit = TotalRevenue * 0.2m;//20%
                //reader.Close();
                //return;
                //Group by Hours
                if (numberDays <= 1)
                {
                    GrossRevenueList = (from orderList in resultTable
                                        group orderList by orderList.Key.ToString("hh tt")
                                       into order
                                        select new RevenueByDate
                                        {
                                            Date = order.Key,
                                            TotalAmount = order.Sum(amount => amount.Value)
                                        }).ToList();
                }
                //Group by Days
                else if (numberDays <= 30)
                {
                    GrossRevenueList = (from orderList in resultTable
                                        group orderList by orderList.Key.ToString("dd MMM")
                                       into order
                                        select new RevenueByDate
                                        {
                                            Date = order.Key,
                                            TotalAmount = order.Sum(amount => amount.Value)
                                        }).ToList();
                }

                //Group by Weeks
                else if (numberDays <= 92)
                {
                    GrossRevenueList = (from orderList in resultTable
                                        group orderList by CultureInfo.CurrentCulture.Calendar.GetWeekOfYear(
                                            orderList.Key, CalendarWeekRule.FirstDay, DayOfWeek.Monday)
                                       into order
                                        select new RevenueByDate
                                        {
                                            Date = "Week " + order.Key.ToString(),
                                            TotalAmount = order.Sum(amount => amount.Value)
                                        }).ToList();
                }

                //Group by Months
                else if (numberDays <= (365 * 2))
                {
                    bool isYear = numberDays <= 365 ? true : false;
                    GrossRevenueList = (from orderList in resultTable
                                        group orderList by orderList.Key.ToString("MMM yyyy")
                                       into order
                                        select new RevenueByDate
                                        {
                                            Date = isYear ? order.Key.Substring(0, order.Key.IndexOf(" ")) : order.Key,
                                            TotalAmount = order.Sum(amount => amount.Value)
                                        }).ToList();
                }

                //Group by Years
                else
                {
                    GrossRevenueList = (from orderList in resultTable
                                        group orderList by orderList.Key.ToString("yyyy")
                                       into order
                                        select new RevenueByDate
                                        {
                                            Date = order.Key,
                                            TotalAmount = order.Sum(amount => amount.Value)
                                        }).ToList();
                }
                //    }
                //}
            }
            catch { }
        }

        //Public methods
        public bool LoadData(DateTime startDate, DateTime endDate, bool forceGet = false)
        {
            endDate = new DateTime(endDate.Year, endDate.Month, endDate.Day,
                endDate.Hour, endDate.Minute, 59);
            if ((startDate != this.startDate || endDate != this.endDate  || forceGet))
            {
                this.startDate = startDate;
                //this.startDate2 = utils.DateTimeToUnixTimeStamp( startDate);
                this.endDate = endDate;
                //this.endDate2 = utils.DateTimeToUnixTimeStamp( endDate);
                this.numberDays = (endDate - startDate).Days;

                //GetNumberItems();
                //GetProductAnalisys();
                GetOrderAnalisys();
                //Console.WriteLine("Refreshed data: {0} - {1}", startDate.ToString(), endDate.ToString());
                return true;
            }
            else
            {
                //Console.WriteLine("Data not refreshed, same query: {0} - {1}", startDate.ToString(), endDate.ToString());
                return false;
            }
        }
    }

}
