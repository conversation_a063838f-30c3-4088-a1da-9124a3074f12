﻿using DevComponents.DotNetBar.Controls;
using iTextSharp.text.pdf;
using iTextSharp.text;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices.ComTypes;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.ViewModels;
using System.Threading;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_UM_Sales_Size_Times : RJChildForm
    {
        string Server_Type = "UM";
        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;
 
        public Form_UM_Sales_Size_Times(string server_Type="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv2.RightToLeft = RightToLeft.No;
            }
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
      
            this.Text = "تقارير الاستهلاك يوزمنجر";
            if (Server_Type == "HS")
                this.Text = "تقارير الاستهلاك هوتسبوت";
           
            Spanel.Width = 0;

            if (UIAppearance.Theme == UITheme.Dark)
            {
                pnl_side_sn.Customizable = false;
                rjPanel1.Customizable = false;
                rjPanel12.Customizable = false;
            }
            if (!UIAppearance.Language_ar)
            {
                this.Text = "Reports UserManager";
                if (Server_Type == "HS")
                    this.Text = "Reports Hotspot";
                this.dgv.RightToLeft = RightToLeft.No;
            }

            set_font();

            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00").AddDays(-1);
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");
            CheckBox_To_Date.Check = true;
            //ToggleButton_Monthly.Checked = true;
            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
            Server_Type = server_Type;
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
        }
        private void set_font()
        {
            //return;
            //dgv.AllowUserToOrderColumns = true;
            //dgv2.AllowUserToOrderColumns = true;

            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv2.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv2.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = utils.Control_Mesur_DPI(42);
            dgv2.ColumnHeadersHeight = utils.Control_Mesur_DPI(45);
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            CardsArtchive cardsArtchive = new CardsArtchive();
            dgv.DataSource = cardsArtchive;


            System.Drawing.Font Font_Count = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            lbl_count_Cards.Font = lbl_uptime.Font = lbl_Total_Session.Font = lbl_download.Font = Font_Count;


             
           jToggleButton_Year.Font= ToggleButton_Detail.Font= ToggleButton_Monthly.Font= rjLabel3.Font =  rjLabel11.Font = rjLabel3.Font =
               rjLabel9.Font = rjLabel4.Font = rjLabel15.Font = rjLabel16.Font = rjLabel14.Font = rjLabel17.Font =
               lbl_countSession.Font= Toggle_By_Group.Font=
               
               Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            btn_.Font=btn_Filter.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Bold);
            rjLabel25Title.Font = btn_apply.Font = Program.GetCustomFont(Resources.DroidSansArabic, 13, FontStyle.Bold);


            rjDateTime_From.Font = rjDateTime_To.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            if (utils.ScaleFactor != 1)
            {
                txt_Total_Session.Font = new System.Drawing.Font(txt_Total_Session.Font.FontFamily, txt_Total_Session.Font.Size , txt_Total_Session.Font.Style);
                txt_count_Cards.Font = new System.Drawing.Font(txt_count_Cards.Font.FontFamily, txt_count_Cards.Font.Size, txt_count_Cards.Font.Style);
                txt_uptime.Font = new System.Drawing.Font(txt_uptime.Font.FontFamily, txt_uptime.Font.Size , txt_uptime.Font.Style);
                txt_download.Font = new System.Drawing.Font(txt_download.Font.FontFamily, txt_download.Font.Size , txt_download.Font.Style);
            }

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.dgv_textSize(dgv2);
            utils.item_Contrlol_textSize(dmAll_Cards);
            return;
            Control_Loop(pnlClientArea);
        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new System.Drawing.Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            try
            {
                if(Global_Variable.Mk_resources.version>=7)
                {
                    CBox_Customer.Enabled = false;
                    return;
                }
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }

        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }

        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width > 50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = utils.Control_Mesur_DPI(260);
            }
        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }
        private void loadDgvState()
        {
            //return;
            Init_dgv_to_Default();
        }

        private void Init_dgv_to_Default()
        {
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.Visible = false;

                }
                try
                {
                    foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                    {
                        try
                        {
                            m.Checked = false;
                        }
                        catch { }
                    }
                }
                catch { }

                if (ToggleButton_Detail.Checked)
                {
                    try
                    {
                        dgv.Columns["UserName"].Visible = true;
                        //dgv.Columns["UserName"].Width = 90;
                        dgv.Columns["ProfileName"].Visible = true;
                        //dgv.Columns["ProfileName"].Width = 80;
                        dgv.Columns["Str_UptimeUsed"].Visible = true;
                        dgv.Columns["Str_Up_Down"].Visible = true;
                        //dgv.Columns["Str_Up_Down"].Width = 160;
                        dgv.Columns["Str_Up_Down"].HeaderText = "كمية الاستهلاك";
                        dgv.Columns["CountSession"].Visible = true;


                        //dgv.Columns["Str_TotalPrice"].Visible = false;
                        ////dgv.Columns["TotalPrice"].Visible = true;
                        //dgv.Columns["FirsLogin"].Visible = false;

                        UserName_ToolStripMenuItem.Checked = true;
                        Profile_ToolStripMenuItem.Checked = true;
                        Str_UptimeUsed_ToolStripMenuItem.Checked = true;
                        Str_Up_Down_ToolStripMenuItem.Checked = true;
                        CountSession_ToolStripMenuItem.Checked = true;

                        if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked)
                        {
                            if (CheckBox_SN.Check)
                                dgv.Columns["Sn"].Visible = true;

                            if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                                dgv.Columns["ProfileName"].Visible = true;

                            if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                                dgv.Columns["SpName"].Visible = true;

                            if (/*CBox_Batch.SelectedIndex != 0 || CBox_Batch.SelectedIndex != -1 ||*/ CBox_Batch.Text != "")
                                dgv.Columns["BatchCardId"].Visible = true;

                            if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                                dgv.Columns["CustomerName"].Visible = true;

                            if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                                try { dgv.Columns["NasPortId"].Visible = true; } catch { }

                            if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                                dgv.Columns["Radius"].Visible = true;
                        }
                    
                        dgv.Columns["Str_Status"].DisplayIndex = 0;
                        dgv.Columns["Sn"].DisplayIndex = 1;
                        dgv.Columns["UserName"].DisplayIndex = 2;
                        dgv.Columns["ProfileName"].DisplayIndex = 3;
                        dgv.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                        dgv.Columns["Str_Up_Down"].DisplayIndex = 5;

                        
                        //dgv.Columns["Str_Status"].Visible = true;
                        //dgv.Columns["Str_UptimeUsed"].Visible = true;
                        //dgv.Columns["Str_Up_Down"].Visible = true;


                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }

                }
                else
                {
                    try
                    {
                        dgv.Columns["Str_TotalPrice"].Visible = true;
                        //dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["date"].Visible = true;
                        dgv.Columns["Str_UptimeUsed"].Visible = true;
                        dgv.Columns["Str_Up_Down"].Visible = true;
                        dgv.Columns["count"].Visible = true;

                        DateToolStripMenuItem.Checked = true;
                        Price_ToolStripMenuItem.Checked = true;
                        Str_UptimeUsed_ToolStripMenuItem.Checked = true;
                        Str_Up_Down_ToolStripMenuItem.Checked = true;
                        count_ToolStripMenuItem.Checked = true;

                        dgv.Columns["date"].DisplayIndex = 0;
                        dgv.Columns["TotalPrice"].DisplayIndex = 1;
                        //dgv.Columns["MoneyTotal"].DisplayIndex = 2;
                        dgv.Columns["Str_UptimeUsed"].DisplayIndex = 3;
                        dgv.Columns["Str_Up_Down"].DisplayIndex = 4;
                        dgv.Columns["count"].DisplayIndex = 5;
                    }
                    catch { }
                }
            }
            catch { }

        }

        private void Init_dgv_to_Default2()
        {
            try
            {
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{
                //    column.Visible = false;

                //}
                //try
                //{
                //    foreach (ToolStripMenuItem m in View_Hide_toolStripMenuItem.DropDownItems)
                //    { m.Checked = false; }
                //}
                //catch { }

                if (ToggleButton_Detail.Checked)
                {
                    try
                    {
                        dgv.Columns["uptime"].Visible = false;
                        dgv.Columns["down+up"].Visible = false;
                        dgv.Columns["Sn_Name"].Visible = false;

                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }

                }
                else
                {
                    try
                    {
                        dgv.Columns["Str_TotalPrice"].Visible = true;
                        //dgv.Columns["TotalPrice"].Visible = true;
                        dgv.Columns["date"].Visible = true;
                        dgv.Columns["Str_UptimeUsed"].Visible = true;
                        dgv.Columns["Str_Up_Down"].Visible = true;
                        dgv.Columns["count"].Visible = true;

                        DateToolStripMenuItem.Checked = true;
                        Price_ToolStripMenuItem.Checked = true;
                        Str_UptimeUsed_ToolStripMenuItem.Checked = true;
                        Str_Up_Down_ToolStripMenuItem.Checked = true;
                        count_ToolStripMenuItem.Checked = true;

                        dgv.Columns["date"].DisplayIndex = 0;
                        dgv.Columns["TotalPrice"].DisplayIndex = 1;
                        //dgv.Columns["MoneyTotal"].DisplayIndex = 2;
                        dgv.Columns["Str_UptimeUsed"].DisplayIndex = 3;
                        dgv.Columns["Str_Up_Down"].DisplayIndex = 4;
                        dgv.Columns["count"].DisplayIndex = 5;
                    }
                    catch { }
                }
            }
            catch { }

        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            lbl_countSession.Text = "عدد جلسات الكرت المحدد";

            dgv.ContextMenuStrip = null;
            if (ToggleButton_Detail.Checked)
            {
                dgv.ContextMenuStrip = dmAll_Cards;
                FirstLoad = true;
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;
                FirstLoad=false;

                //pnl_size_time_count.Visible = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad=true;
                    ToggleButton_Detail.Checked = true;
                    FirstLoad=false;
                }
            }
            try
            {

                //lbl_avg.Visible = false;
                //txt_avg.Visible = false;

                rjDateTime_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
                rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";

                string today = DateTime.Now.ToString("MM-dd-yyyy");
                rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
            }
            catch { }
            get_report();
        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            lbl_countSession.Text = "عدد جلسات اليوم المحدد";
            dgv.ContextMenuStrip = null;
            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {

                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                //pnl_size_time_count.Visible = true;
                FirstLoad = false;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Monthly.Checked = true;
                    FirstLoad = false;
                }
            }
            try
            {
                //lbl_avg.Visible = true;
                //txt_avg.Visible = true;
                //lbl_av/g.Text = "المتوسط اليومي";

                rjDateTime_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                rjDateTime_From.CustomFormat = "MM/yyyy";
                //rjDateTime_To.CustomFormat = "MM/yyyy";
                DateTime firstDayOfMonth;
                DateTime lastDayOfMonth;
                utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                string first = firstDayOfMonth.ToString("MM-dd-yyyy");
                string last = lastDayOfMonth.ToString("MM-dd-yyyy");

                rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
                rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
            }
            catch { }

            get_report();
        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;

            lbl_countSession.Text = "عدد جلسات الشهر المحدد";

            dgv.ContextMenuStrip = null;
            CheckBox_To_Date.Check = true;
            if (jToggleButton_Year.Checked)
            {
               
                FirstLoad = true;
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                //pnl_size_time_count.Visible = true;
                FirstLoad=false;

            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                {
                    FirstLoad = true;
                    jToggleButton_Year.Checked = true;
                    FirstLoad=false;
                }
            }
            try
            {
                //lbl_avg.Visible = true;
                //txt_avg.Visible = true;

                //lbl_avg.Text = "المتوسط الشهري";

                rjDateTime_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                rjDateTime_From.CustomFormat = "yyyy";
                //rjDateTime_To.CustomFormat = "yyyy";

                //rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
                //rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
                rjDateTime_From.Value = new DateTime(DateTime.Now.Year, 1, 1);
                rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31);
                //rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31,23,59,59);

                ////rjDateTime_From.Value = new DateTime(1,DateTime.Now.Year,1,0,0,0);
                //rjDateTime_To.Value = new DateTime(12,DateTime.Now.Year,31,23,59,59);
            }
            catch { }
            get_report();
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private DataTable dt_ByDetails()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الاسم", typeof(string));
            dt.Columns.Add("الباقة", typeof(string));
            dt.Columns.Add("اجمالي الوقت", typeof(string));
            dt.Columns.Add("اجمالي الاستهلاك", typeof(string));
            dt.Columns.Add("عدد الجلسات", typeof(double));
            dt.Columns.Add("uptime", typeof(double));
            dt.Columns.Add("down+up", typeof(double));
            dt.Columns.Add("Sn_Name", typeof(string));

            return dt;
        }
        private DataTable dt_Monthly()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("التاريخ", typeof(string));
            //dt.Columns.Add("الباقة", typeof(string));
            dt.Columns.Add("اجمالي الوقت", typeof(string));
            dt.Columns.Add("اجمالي الاستهلاك", typeof(string));
            dt.Columns.Add("عدد الكروت", typeof(double));
            dt.Columns.Add("عدد الجلسات", typeof(double));
            dt.Columns.Add("uptime", typeof(double));
            dt.Columns.Add("down+up", typeof(double));
            //dt.Columns.Add("Sn_Name", typeof(string));

            return dt;
        }
        private string ColumnShow = " ";
        private void get_report()
        {
            string GroupBy = " GROUP BY s.Fk_Sn_Name ";

            dgv.DataSource = null;
            double sum = 0; double sum_download = 0; double sum_uptime = 0; double count_cards = 0;
            double total_Session_cards = 0;
            //txt_avg.Text = "0"; 
            txt_count_Cards.Text = "0"; txt_Total_Session.Text = "0";
            txt_download.Text = "0";
            //txt_sum_Sales.Text = "0"; 
            txt_uptime.Text = "00:00:00";

            string Query_firstUse = condition_detail_firstUse();
            string Select_Group = $"{ColumnShow}u.UserName,u.ProfileName,u.Sn_Name, ";
            
            string TableUser = "UmUser";
            string TablePyment = "UmPyment";
            string TableSession = "UmSession";

            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            if (ToggleButton_Detail.Checked)
            {

                //string Qury = $"SELECT   {Select_Group} sum(s.Uptime) Uptime,sum(s.BytesDownload) BytesDownload ,sum(s.BytesUpload) BytesUpload,sum(s.BytesUpload+BytesDownload) 'down+up',count(s.Fk_Sn_Name) Count   " +
                //    $" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_firstUse} {GroupBy}";

                
                if (Server_Type == "HS")
                {
                   

                }
                //Qury = $"SELECT u.*, sum(s.Uptime) UptimeUsed,sum(s.BytesDownload) DownloadUsed ,sum(s.BytesUpload) UploadUsed,count(s.Fk_Sn_Name) CountSession   " +
                //    $" FROM HsSession s INNER JOIN HSUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_firstUse} {GroupBy}";

                try
                {
                    if (Server_Type == "UM")
                    {
                        string Qury = $"SELECT u.*, sum(s.Uptime) UptimeUsed,sum(s.BytesDownload) DownloadUsed ,sum(s.BytesUpload) UploadUsed,count(s.Fk_Sn_Name) CountSession   " +
                                      $" FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_firstUse} {GroupBy}";

                        List<UmUser> users = Local_DA.Load<UmUser>(Qury);
                        dgv.DataSource = users;
                        foreach (var itm in users)
                        {
                            sum_uptime += Convert.ToDouble(itm.UptimeUsed);
                            sum_download += Convert.ToDouble(itm.UploadUsed + itm.DownloadUsed);
                            total_Session_cards += Convert.ToDouble(itm.CountSession);
                        }
                    }
                    else
                    {
                        Query_firstUse = condition_detail_firstUse(false);
                        string Qury = $"SELECT u.*, sum(u.UptimeUsed) UptimeUsed,sum(u.DownloadUsed) DownloadUsed ,sum(u.UploadUsed) UploadUsed   " +
                        $" FROM HSUser u   {Query_firstUse} GROUP BY u.Sn_Name ";

                        List<HSUser> users = Local_DA.Load<HSUser>(Qury);

                        Query_firstUse = condition_detail_firstUse(true);
                        Qury = $"SELECT u.*, count(s.Fk_Sn_Name) CountSession   " +
                        $" FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_firstUse} {GroupBy}";

                        List<HSUser> sessionCount = Local_DA.Load<HSUser>(Qury);
                        var all = (from u in users
                                   join s in sessionCount on u.Sn_Name equals s.Sn_Name
                                   select new HSUser
                                   {
                                       UserName = u.UserName,
                                       Status = u.Status,
                                       //Str_Status = u.Str_Status,
                                       SN=u.SN,
                                       ProfileHotspot=u.ProfileHotspot,
                                       ProfileName=u.ProfileName,
                                       Price=u.Price,
                                       TotalPrice = u.TotalPrice,
                                       BatchCardId=u.BatchCardId,
                                       SpName=u.SpName,
                                       LimitUptime=u.LimitUptime,
                                       Limitbytestotal=u.Limitbytestotal,
                                       UptimeLimit=u.UptimeLimit,
                                       TransferLimit=u.TransferLimit,
                                       DownloadUsed=u.DownloadUsed,
                                       FirsLogin=u.FirsLogin,
                                       NasPortId=u.NasPortId,
                                       Password=u.Password,
                                       ProfileTimeLeft=u.ProfileTimeLeft,
                                       ProfileTillTime=u.ProfileTillTime,
                                       ProfileValidity=u.ProfileValidity,
                                       ProfileTransferLeft=u.ProfileTransferLeft,
                                       //Str_DownloadUsed=u.Str_DownloadUsed,
                                       UploadUsed=u.UploadUsed,
                                       UptimeUsed=u.UptimeUsed,
                                       ValidityLimit=u.ValidityLimit,
                                       SpCode=u.SpCode,
                                       Sn_Name=u.Sn_Name,
                                       NumberPrint=u.NumberPrint,
                                       CountSession=s.CountSession,
                                       CountProfile=u.CountProfile,
                                   }).ToList();

                        dgv.DataSource = all;
                        //dgv.DataSource = users;
                        foreach (var itm in all)
                        {
                            sum_uptime += Convert.ToDouble(itm.UptimeUsed);
                            sum_download += Convert.ToDouble(itm.UploadUsed + itm.DownloadUsed);
                            total_Session_cards += Convert.ToDouble(itm.CountSession);
                        }
                    }
                    loadDgvState();
                    txt_count_Cards.Text = dgv.Rows.Count.ToString();
                    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(sum_uptime));
                    txt_Total_Session.Text = total_Session_cards.ToString();
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }

                //update_select_DGV();

                return;
            }

            else
            {
                string Query_conditon = condition_Session_By_Days_for_firstUse();
                string fitler = "'%Y-%m-%d'";
               
                if (jToggleButton_Year.Checked)
                    fitler = "'%Y-%m'";

                List<class_Report_monthly_or_Dayliy> um = new List<class_Report_monthly_or_Dayliy>();
                List<class_Report_monthly_or_Dayliy> totalDownload = new List<class_Report_monthly_or_Dayliy>();


                string Query = $"SELECT {ColumnShow} strftime({fitler}, s.FromTime) date , sum(BytesDownload + BytesUpload) as 'down+up' , sum(UpTime) as Uptime ,COUNT(DISTINCT u.Sn_Name)  Count,count(s.Fk_Sn_Name) CountSession " +
                    $"FROM {TableSession} s " +
                    $"INNER JOIN {TableUser} u ON u.Sn_Name  = s.Fk_Sn_Name " +
                    $"{Query_conditon} " +
                    $"group by strftime({fitler}, s.FromTime)";

                //if (Server_Type == "HS")
                //{
                //    Query = $"SELECT {ColumnShow} strftime({fitler}, s.FromTime) date , sum(BytesDownload + BytesUpload) as 'down+up' , sum(UpTime) as Uptime ,COUNT(DISTINCT u.Sn_Name)  Count,count(s.Fk_Sn_Name) CountSession " +
                //    $"FROM HsSession s " +
                //    $"INNER JOIN HSUser u ON u.Sn_Name  = s.Fk_Sn_Name " +
                //    $"{Query_conditon} " +
                //    $"group by strftime({fitler}, s.FromTime)";

                //}

                try
                {
                    DataTable dt = dt_Monthly();
                    DataTable dt_res = Local_DA.RunSqlCommandAsDatatable(Query);
                    foreach (DataRow itm in dt_res.Rows)
                    {
                        DataRow row = dt.NewRow();
                        row[0] = (itm["date"].ToString());

                        row[1] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(itm["uptime"]));
                        row[2] = utils.ConvertSize_Get_InArabic(itm["down+up"].ToString());

                        row[3] = itm["Count"];
                        row[4] = itm["CountSession"];
                        row[5] = itm["uptime"];
                        row[6] = itm["down+up"];

                        dt.Rows.Add(row);

                        sum_uptime += Convert.ToDouble(itm["uptime"]);
                        sum_download += Convert.ToDouble(itm["down+up"]);
                        total_Session_cards += Convert.ToDouble(itm["CountSession"]);
                        count_cards += Convert.ToDouble(itm["Count"]);
                    }
                    dgv.DataSource = dt;

                    txt_count_Cards.Text = count_cards.ToString();
                    txt_Total_Session.Text = total_Session_cards.ToString();
                    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode(Convert.ToInt32(sum_uptime));

                    //var sum_download22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("down+up"));
                    //var sum_uptime22 = dt_res.AsEnumerable().Sum(r => r.Field<double>("uptime"));

                    dgv.Columns["uptime"].Visible = false;
                    dgv.Columns["down+up"].Visible = false;
                    //loadDgvState();
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                //dgv.DataSource = Local_DA.Load<class_Report_Size_And_Times>(Query);
                //update_select_DGV();
                return;
            }

        }
        private void update_select_DGV33()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        private void update_select_DGV2()
        {
            try
            {
                string ListAll = dgv2.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv2.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        private string condition_detail_firstUse(bool DateFromTable_Session=true)
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if(DateFromTable_Session)
                conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";
            else
                conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    { nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",s.NasPortId"; }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "" && Server_Type == "UM")
                    { radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",s.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }
        private string condition_Session_By_Days_for_firstUse(bool SubQuery=false)
        {
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if(SubQuery)
            {
                DateTime d = Convert.ToDateTime(dgv.CurrentRow.Cells["التاريخ"].Value);

                str_from_Date = d.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture) ;
                str_to_Date = d.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                if(jToggleButton_Year.Checked)
                    str_to_Date = d.AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            }
            //if (ToggleButton_Monthly.Checked)
            //{
            //    //DateTime firstDayOfMonth;
            //    //DateTime lastDayOfMonth;
            //    //utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    //From_DT=utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    //To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    //From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    //To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}
            conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                        radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  ";

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                        customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }

        private void Form_UM_Sales_Size_Times_SizeChanged(object sender, EventArgs e)
        {
            panel1_side.Refresh();
            Spanel.Refresh();
            rjPanel12.Refresh();
            rjPanel_topFilter.Refresh();
            dgv.Refresh();
            txt_search.Refresh();

            //lbl_avg.Location = new Point(pnlClientArea.Width / 2 - (lbl_avg.Width / 2), lbl_avg.Location.Y);
            //txt_avg.Location = new Point(pnlClientArea.Width / 2 - (txt_avg.Width / 2), txt_avg.Location.Y);
        }

        private void Form_UM_Sales_Size_Times_Load(object sender, EventArgs e)
        {
            timer1.Start();
            //Get_Batch_cards();
        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv.Columns[columnName].Visible = elemnt.Checked;
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
            }
            catch { }
        }
        private void UserName_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[e.RowIndex].Cells["Sn_Name"].Value.ToString());
                    else
                        Sub_LocadData();
                    
                }
            }
            catch { }
        }
        private void Sub_LocadData(string _Sn_Name = "")
        {


            //long Sn_from = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_from"].Value.ToString());
            //long Sn_to = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_to"].Value.ToString());

            try
            {
                string GroupBy = "";
                string Query_conditon = "";
                string Qury = "";
                string Q_ByName = "";

                string TableUser = "UmUser";
                string TablePyment = "UmPyment";
                string TableSession = "UmSession";

                if (Server_Type == "HS")
                {
                    TableUser = "HSUser";
                    TablePyment = "HsPyment";
                    TableSession = "HsSession";
                }

                if (Toggle_By_Group.Checked)
                    GroupBy = " GROUP BY s.Fk_Sn_Name ";


                if (ToggleButton_Detail.Checked)
                {
                    Query_conditon = condition_detail_firstUse();
                    Q_ByName = $"and Fk_Sn_Name='{_Sn_Name}'";
                }
                else
                {
                    Query_conditon = condition_Session_By_Days_for_firstUse(true);
                }
                Qury = $"SELECT  s.* FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
              
                if(Toggle_By_Group.Checked)
                     Qury = $"SELECT s.*, sum(s.Uptime) Uptime,sum(s.BytesDownload) BytesDownload ,sum(s.BytesUpload) BytesUpload " +
                   $" FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy}";

                //Qury = $"SELECT  s.* FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {Q_ByName} {GroupBy} ";
                //Qury = $"SELECT u.*, sum(s.Uptime) UptimeUsed,sum(s.BytesDownload) DownloadUsed ,sum(s.BytesUpload) UploadUsed,count(s.Fk_Sn_Name) CountSession   " +
                //   $" FROM UmSession s INNER JOIN UmUser u ON s.Fk_Sn_Name = u.Sn_Name {Query_conditon} {GroupBy}";

                List<UmSession> umSession = Local_DA.Load<UmSession>(Qury);
                dgv2.DataSource = umSession;
                //txt_countSession.Text = dgv2.Rows.Count.ToString();
                dgv2.Columns["IdHX"].Visible = false;
                dgv2.Columns["UserName"].Visible = false;
                dgv2.Columns["DeleteFromServer"].Visible = false;

                dgv2.Columns["FromTime"].DisplayIndex = 0;
                dgv2.Columns["FromTime"].Width = 150;

                dgv2.Columns["TillTime"].DisplayIndex = 1;
                dgv2.Columns["TillTime"].Width = 150;

                dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 2;
                dgv2.Columns["Str_UptimeUsed"].Width = 130;

                dgv2.Columns["Str_DownloadUsed"].DisplayIndex = 3;
                dgv2.Columns["Str_DownloadUsed"].Width = 130;

                dgv2.Columns["Str_UploadUsed"].DisplayIndex = 4;
                dgv2.Columns["Str_UploadUsed"].Width = 130;

                dgv2.Columns["CallingStationId"].DisplayIndex = 5;
                dgv2.Columns["CallingStationId"].Width = 130;

                dgv2.Columns["NasPortId"].DisplayIndex = 6;
                dgv2.Columns["NasPortId"].Width = 130;

                dgv2.Columns["IpUser"].DisplayIndex = 7;
                dgv2.Columns["IpUser"].Width = 120;

                dgv2.Columns["IpRouter"].Width = 140;

                dgv2.Columns["Sn_Name"].Visible = false;
                dgv2.Columns["Fk_Sn_Name"].Visible = false;

                if (Server_Type == "HS")
                {
                    dgv2.Columns["IpRouter"].Visible = false;
                }

            }
            catch { }
            //update_select_DGV2();
        }

        private void dgv_SizeChanged(object sender, EventArgs e)
        {
            //update_select_DGV();
        }

        private void dgv2_SizeChanged(object sender, EventArgs e)
        {
        }

        private void dgv2_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV2();
        }

        private void dgv_SortCompare(object sender, DataGridViewSortCompareEventArgs e)
        {
            update_select_DGV2();

        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            //update_select_DGV2();

        }

        private void Toggle_By_Group_CheckedChanged(object sender, EventArgs e)
        {
            if(FirstLoad)
                return;
      
            if (ToggleButton_Detail.Checked)
            {
                try
                {
                    if (dgv.RowCount > 0)
                    {
                        if (ToggleButton_Detail.Checked)
                            Sub_LocadData(dgv.CurrentRow.Cells["Sn_Name"].Value.ToString());
                        else
                            Sub_LocadData();
                    }
                }
                catch { }

            }
            else
                Sub_LocadData();
            update_select_DGV2();


        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            txt_countSession.Text = "0";
            txt_count_Cards.Text = "0";
            txt_uptime.Text = "00:00:00";
            txt_download.Text = "0 B";
            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();

           
            get_report();

            try
            {
                if (dgv.Rows.Count > 0)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[0].Cells["Sn_Name"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
            FirstLoad=false;
            dgv.ContextMenuStrip = dmAll_Cards;

        }
        bool FirstLoad = true;
        private void btn_search_Click(object sender, EventArgs e)
        {

        }

        private void ToggleButton_Detail_CheckedChanged_1(object sender, EventArgs e)
        {

        }

        private void rjButton3_Click(object sender, EventArgs e)
        {
            btnPdf_Click2();
        }

        private void btnPdf_Click2()
        {
            string dateHeader = "";
            string end = "";
            string start = Convert.ToDateTime(rjDateTime_From.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
            end = start;

            if (CheckBox_To_Date.Checked)
                end = Convert.ToDateTime(rjDateTime_To.Value).ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);

            if (ToggleButton_Detail.Checked)
                dateHeader = "تقرير الاستهلاك - تفصيلي - من تاريخ  : " + start + "  الى  " + end;
            if (ToggleButton_Monthly.Checked)
                dateHeader = "تقرير يومي للاستهلاك لشهر - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("MM/yyyy", CultureInfo.InvariantCulture);
            if (jToggleButton_Year.Checked)
                dateHeader = "تقرير شهري للاستهلاك لسنة - " + Convert.ToDateTime(rjDateTime_From.Value).ToString("yyyy", CultureInfo.InvariantCulture);

            if (dgv.Rows.Count > 0)
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "PDF (*.pdf)|*.pdf";
                sfd.FileName = System.DateTime.Now.ToString("dd-MM-yyyy hh-mm-ss", CultureInfo.InvariantCulture) + "- الاستهلاك" + ".pdf";
                sfd.InitialDirectory = utils.Get_Report_Directory();

                bool fileError = false;

                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    if (File.Exists(sfd.FileName))
                    {
                        try
                        {
                            File.Delete(sfd.FileName);
                        }
                        catch (IOException ex)
                        {
                            fileError = true;
                            RJMessageBox.Show("\nليس لدى البرنامج صلاحية الكتابة على القرص\n" + ex.Message);
                        }
                    }
                    if (!fileError)
                    {
                        try
                        {
                            string fontpath = Environment.GetEnvironmentVariable("SystemRoot") + "\\fonts\\Arial.ttf";
                            BaseFont basefont = BaseFont.CreateFont(fontpath, BaseFont.IDENTITY_H, true);
                            iTextSharp.text.Font arabicFont = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.NORMAL, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_hedrcolum = new iTextSharp.text.Font(basefont, 9, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.BLACK);
                            iTextSharp.text.Font arabicFont_fotter = new iTextSharp.text.Font(basefont, 8, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);
                            iTextSharp.text.Font arabicFont_forDate = new iTextSharp.text.Font(basefont, 10, iTextSharp.text.Font.BOLD, iTextSharp.text.BaseColor.RED);

                            int count_expt_coulum = 0;
                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    count_expt_coulum += 1;
                                }
                            }

                            PdfPTable table_out = new PdfPTable(5);
                            table_out.TotalWidth = 580f;
                            table_out.LockedWidth = true;
                            table_out.DefaultCell.HorizontalAlignment = 1; //0=Left, 1=Centre, 2=Right 
                            table_out.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            table_out.HorizontalAlignment = Element.ALIGN_CENTER;
                            table_out.SpacingBefore = 2f;
                            table_out.SpacingAfter = 2f;
                            table_out.DefaultCell.Padding = 3;


                            //=============================================================
                            PdfPTable pdfTable = new PdfPTable(count_expt_coulum);
                            pdfTable.TotalWidth = 560f;
                            pdfTable.LockedWidth = true;
                            pdfTable.RunDirection = PdfWriter.RUN_DIRECTION_RTL;
                            pdfTable.DefaultCell.Padding = 3;
                            pdfTable.WidthPercentage = 100;
                            pdfTable.HorizontalAlignment = Element.ALIGN_CENTER;
                            pdfTable.DefaultCell.HorizontalAlignment = 1;
                            pdfTable.SpacingBefore = 2f;
                            pdfTable.SpacingAfter = 2f;
                            pdfTable.DefaultCell.Padding = 3;

                            PdfPCell cellfirst = new PdfPCell(new Phrase(dateHeader, arabicFont_forDate));
                            //PdfPCell cellfirst = new PdfPCell(new Phrase("تقرير من تاريخ  : " + start +"  الى  " + end, arabicFont_forDate)); 
                            cellfirst.Colspan = count_expt_coulum;
                            //cellfirst.Colspan = dgv.Columns.Count - count_expt_coulum;
                            cellfirst.HorizontalAlignment = 1;
                            cellfirst.PaddingBottom = 5;
                            pdfTable.AddCell(cellfirst);

                            foreach (DataGridViewColumn column in dgv.Columns)
                            {
                                if (column.Visible)
                                {
                                    PdfPCell cell2 = new PdfPCell(new Phrase(column.HeaderText, arabicFont_hedrcolum));
                                    cell2.HorizontalAlignment = 1;
                                    cell2.PaddingBottom = 3;
                                    pdfTable.AddCell(cell2);
                                }
                            }
                            foreach (DataGridViewRow row in dgv.Rows)
                            {
                                foreach (DataGridViewCell cell in row.Cells)
                                {
                                    //if (cell.OwningColumn.HeaderText != "download" && cell.OwningColumn.HeaderText != "uptime" && cell.OwningColumn.HeaderText != "price" && cell.OwningColumn.HeaderText != "price_percentage" && cell.OwningColumn.HeaderText != "Uptime_inSecond" && cell.OwningColumn.HeaderText != "count")
                                    if (cell.OwningColumn.Visible)
                                        if (cell.Value != null)
                                            pdfTable.AddCell(new Phrase(cell.Value.ToString(), arabicFont));
                                }
                            }
                            //======================================================================
                            PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            cell_out2.Colspan = 5;
                            cell_out2.HorizontalAlignment = 1;
                            cell_out2.PaddingBottom = 5;
                            table_out.AddCell(cell_out2);
                            //======================================================================
                            table_out.AddCell(new Phrase("اجمالي الجلسات", arabicFont_fotter));

                            table_out.AddCell(new Phrase("اجمالي الوقت", arabicFont_fotter));
                            table_out.AddCell(new Phrase("", arabicFont_fotter));
                            table_out.AddCell(new Phrase("اجمالي الاستهلاك", arabicFont_fotter));
                            table_out.AddCell(new Phrase("اجمالي الكروت", arabicFont_fotter));

                            //======================================================================
                            
                            table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            table_out.AddCell(new Phrase(txt_uptime.Text, arabicFont));
                            table_out.AddCell(new Phrase("", arabicFont));
                            table_out.AddCell(new Phrase(txt_download.Text, arabicFont));
                            table_out.AddCell(new Phrase(txt_count_Cards.Text, arabicFont));

                            //======================================================================
                            //PdfPCell cell_out2 = new PdfPCell(pdfTable);
                            //cell_out2.Colspan = 5;
                            //cell_out2.HorizontalAlignment = 1;
                            //cell_out2.PaddingBottom = 5;
                            //table_out.AddCell(cell_out2);

                            using (FileStream stream = new FileStream(sfd.FileName, FileMode.Create))
                            {
                                Document pdfDoc = new Document(PageSize.A4, 10f, 20f, 20f, 10f);
                                PdfWriter.GetInstance(pdfDoc, stream);
                                pdfDoc.Open();
                                pdfDoc.Add(table_out);
                                pdfDoc.Close();
                                stream.Close();
                            }
                            RJMessageBox.Show("تم الطباعة بنجاح", "تنبية");
                            System.Diagnostics.Process.Start(sfd.FileName);
                        }
                        catch (Exception ex)
                        {
                            RJMessageBox.Show("Error :" + ex.Message);
                        }
                    }
                }
            }
            else
            {
                RJMessageBox.Show("لا يوجد بيانات لطباعتها !!!", "Info");
            }
        }

        private void btn_Refresh2_Click(object sender, EventArgs e)
        {

        }
        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سيقوم بجلب الجلسات من الروتر وقد ياخذ وقت اطول حسب عدد الجلسات في الروتر", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;

            try
            {
                Mk_DataAccess GetData = new Mk_DataAccess();
                ThreadStart therGetData = new ThreadStart(() => Refersh_mikrotik());
                Thread startGetData = new Thread(therGetData);
                startGetData.Name = "Get Information And Data";
                startGetData.Start();
            }
            catch { }



        }

        [Obsolete]
        private void Refersh_mikrotik()
        {
            Global_Variable.StartThreadProcessFromMK = true;
            bool Syn_Users_FromFasrDB = false;
            bool Syn_Pyment_FromFasrDB = false;
            bool Syn_Session_FromFasrDB = false;

            if (Global_Variable.load_by_DownloadDB && Global_Variable.Mk_resources.version <= 6)
            {
                int count_process = 7;
                Fast_Load_From_Mikrotik fast = new Fast_Load_From_Mikrotik();
                Global_Variable.Update_Um_StatusBar_Prograss("تنزيل قاعدة بيانات اليوزمنجر  -  لتستفيد من الميزه افتح السرعه للكمبيوتر", Convert.ToInt32(1 * (100.0 / count_process)));
                if (fast.Download_Sql_From_Mikrotik())
                {

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر", Convert.ToInt32(2 * (100.0 / count_process)));
                    //Thread.Sleep(1000);
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه الكروت يوزمنجر", Convert.ToInt32(3 * (100.0 / count_process)));
                    Syn_Users_FromFasrDB = fast.Syn_UmUser_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه المبيعات والحسابات يوزمنجر", Convert.ToInt32(4 * (100.0 / count_process)));
                    Syn_Pyment_FromFasrDB = fast.Syn_Pyments_From_FastDB();
                    Global_Variable.Update_Um_StatusBar_Prograss("يتم مزامنه  الجلسات يوزمنجر", Convert.ToInt32(5 * (100.0 / count_process)));
                    Syn_Session_FromFasrDB = fast.Syn_Session_From_FastDB();

                    //---======================================================================================================

                    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب ومزامنه بيانات اليوزمنجر من الروتر", Convert.ToInt32(0 * (100.0 / count_process)));

                    fast.Create_Indexs();
                }
                try
                {
                    string Downloadfile = utils.Get_Database_Directory() + "\\" + "dbs\\temp.db";
                    string Downloadfile2 = utils.Get_Database_Directory() + "\\" + "dbs\\temp2.db";
                    string Downloadfile3 = Directory.GetCurrentDirectory() + "\\sql.bat";
                    if (File.Exists(Downloadfile))
                        File.Delete(Downloadfile);
                    if (File.Exists(Downloadfile2))
                        File.Delete(Downloadfile2);
                    if (File.Exists(Downloadfile3))
                        File.Delete(Downloadfile3);
                }
                catch { }

            }

            if (Syn_Session_FromFasrDB == false)
            //if (  ((Global_Variable.Mk_Login_data.load_by_Custom_Login == false) || (Global_Variable.Mk_Login_data.load_by_Custom_Login && Global_Variable.Mk_Login_data.DisableLoad_UmSession == false))   )
            {
                int count_process = 4;
                UserManagerProcess u = new UserManagerProcess();
                //if (Global_Variable.Ddiable_LoadSession == false)
                //{
                Global_Variable.Update_Um_StatusBar_Prograss("يتم جلب جلسات اليوزمنجر", Convert.ToInt32(1 * (100.0 / count_process)));

                Global_Variable.Source_Session_UserManager = SourceSessionUserManager_fromMK.Get_UM_Sessions();
                //RJMessageBox.Show("يتم finsh get session");

                Global_Variable.Update_Um_StatusBar_Prograss(" تم  جلب التقارير والجلسات من المايكروتك", Convert.ToInt32(2 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StartSyn();


                Global_Variable.Update_Um_StatusBar_Prograss("يتم الان مزامنة الجلسات والتقارير", Convert.ToInt32(3 * (100.0 / count_process)));

                if (Global_Variable.Source_Session_UserManager != null && Global_Variable.Source_Session_UserManager.Count > 0)
                    u.Syn_Session_to_LocalDB();
                Global_Variable.Update_Um_StatusBar_Prograss("تمت مزامنة  الجلسات والتقارير", Convert.ToInt32(0 * (100.0 / count_process)));

                Global_Variable.Update_StatusBar_StopSyn();
                //}
            }


            Global_Variable.StartThreadProcessFromMK = false;
        }

    }
}
