﻿using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager.Maintenance
{
    public partial class Form_Backup_Files_UserManager : RJChildForm
    {
        [Obsolete]
        public Form_Backup_Files_UserManager()
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            Set_Font();

            LoadData();
        }

        private void Set_Font()
        {
            //.Font = btnSave.Font = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Bold);
            //rjLabel1.Font = rjLabel4.Font = rjLabel5.Font = rjLabel9.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

        }


        private DataTable dt_dgv()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("اسم الملف");
            dt.Columns.Add("نوع الملف");
            dt.Columns.Add("حجم الملف");
            dt.Columns.Add("تاريخ الانشاء");
            dt.Columns.Add("الحالة");
            dt.Columns.Add("FullName");

            return dt;
        }

        [Obsolete]
        private void LoadData()
        {
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
          
            List<Mk_Files> um = mk_DataAccess.Get_files_From_Mikrotik_By_Type("userman backup");
            List<Mk_Files> backup = mk_DataAccess.Get_files_From_Mikrotik_By_Type("backup");
            
            var allFiles_Mk = backup.Concat(um).ToList();


           
            List<string> LUM= Directory.GetFiles(utils.Get_Backup_Directory(), "*.umb", SearchOption.AllDirectories).ToList();
            List<string> LBk = Directory.GetFiles(utils.Get_Backup_Directory(), "*.backup", SearchOption.AllDirectories).ToList();
            var allFiles_dsk = LUM.Concat(LBk).ToList();

            List<Mk_Files> final = new List<Mk_Files>();

            DataTable dt = dt_dgv();
            foreach(var itm in allFiles_Mk)
            {
                if (allFiles_dsk.Any(entry => entry.Contains(itm.Name)))
                {
                    //Do stuff
                };

            }


            dgv.DataSource = allFiles_Mk;
            dgv.Refresh();
        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            LoadData();
        }
    }
}
