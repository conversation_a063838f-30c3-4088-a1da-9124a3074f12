﻿using SmartCreator.Data;
//using SmartCreator.Entities.Accounts;
using SmartCreator.Entities.Accounts;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Utils;

namespace SmartCreator.Forms.Accounting.Partners
{
    public partial class Form_Party_Add_Edit : RJChildForm
    {
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes = false;
        Entities.Accounts.Partner partner;
        

        public Form_Party_Add_Edit()
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            //Get_Umo();
            lblTitle.Text = "اضافة حساب جديد";
            btnSave.Text = "اضافة";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Plus;

            btnSave.BackColor = RJColors.Confirm;

            //txt_line.Text = "بيانات التواصل";
            txt_code.Text = (Smart_DA.Get_BatchCards_My_Sequence("Partner") + 1).ToString();

            Set_Font();
        }
        public Form_Party_Add_Edit(Entities.Accounts.Partner _partner)
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();

            lblTitle.Text = "تعديل الحساب";
            btnSave.Text = "تعديل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Edit;
            btnSave.BackColor = RJColors.DefaultFormBorderColor;

            partner = _partner;

            txt_code.Text = partner.Code;
            txt_code.Enabled = false;
            txt_Description.Text = partner.Description;
            txt_name.Text = partner.Name;
            check_Active.Checked = Convert.ToBoolean(partner.Active);

            Set_AccountType();
            Set_Font();

        }
        private void Set_AccountType()
        {
            if (partner.Partner_type == 2)
                Radio_Customer.Checked = true;
            else if (partner.Partner_type == 3)
                Radio_Supplier.Checked = false;

            else if (partner.Partner_type == 5)
                Radio_SellingPoint.Checked = false;
            else if (partner.Partner_type == 9)
                Radio_Other.Checked = true;

        }

        private void Set_Font()
        {


            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;

            //System.Drawing.Font DGV_font = CustomFonts.Get_Custom_Font("Cairo_Medium", 10, false);
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel5.Font = rjLabel1.Font = rjLabel2.Font = rjLabel3.Font = rjLabel8.Font = rjLabel5.Font =  rjLabel7.Font =
           txt_code.Font = txt_name.Font =
           lbl_font;
            this.Focus();

            Radio_Customer.Font=Radio_SellingPoint.Font=Radio_Supplier.Font=Radio_Other.Font= Program.GetCustomFont(Resources.DroidSansArabic, 11f, FontStyle.Regular);

            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            int type = 1;

            if (Radio_Customer.Checked)
                type = 2;
            else if (Radio_Supplier.Checked)
                type = 3;
            else if (Radio_SellingPoint.Checked)
                type = 5;
            else if (Radio_Other.Checked)
                type = 9;

            if (add)
            {
                long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Partner where Code='{txt_code.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");

                if (foundsp > 0)
                {
                    RJMessageBox.Show("رقم او كود الحساب موجود مسبقا");
                    return;
                }
                if (txt_code.Text == "")
                {
                    RJMessageBox.Show("رقم الحساب مطلوب");
                    return;
                }
                if (txt_name.Text == "")
                {
                    RJMessageBox.Show("اسم الحساب مطلوب");
                    return;
                }
      
                //if (Cbox_Uom.Text == "")
                //{
                //    RJMessageBox.Show("حدد الوحده للصنف");
                //    return;
                //}

                Entities.Accounts.Partner sp = new Entities.Accounts.Partner();
                sp.Code = txt_code.Text;
                sp.Name = txt_name.Text;
                sp.Description=txt_Description.Text;
                sp.Address = txt_Address.Text;
                sp.Phone=txt_Mobail.Text;

                sp.Active = Convert.ToInt16(check_Active.Checked);
                sp.Rb = Global_Variable.Mk_resources.RB_SN;
                
                

                lock (Smart_DataAccess.Lock_object)
                {
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Code", "Name", "Address", "Phone", "Description", "Partner_type", "Rb", "Active" };

                    Fields.AddRange(aFields);


                    int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Partner");
                    if (new_sp > 0)
                    {
                        //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                        RJMessageBox.Show("تمت عمليه الاضافة");
                        succes = true;
                        int x = Smart_DA.Update_MySequence("Partner", Convert.ToInt32(txt_code.Text));
                        this.Close();
                        return;
                    }
                    RJMessageBox.Show("خطاء");
                    return;
                }
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = { "Name", "Address", "Phone", "Description", "Partner_type", "Active" };
                Fields.AddRange(aFields);
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var dataa = new Entities.Accounts.Partner();
                        dataa.Id = partner.Id;
                        dataa.Name = txt_name.Text;
                        dataa.Partner_type =type;
                        dataa.Address=txt_Address.Text;
                        dataa.Phone=txt_Mobail.Text;
                       
                        dataa.Active = Convert.ToInt16(check_Active.Checked);
                        dataa.Description = txt_Description.Text;

                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Partner>("Partner", Fields, $" where Id=@Id and   Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(dataa, sqlquery);
                        if (r > 0)
                        {
                            //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                            //RJMessageBox.Show("تمت عمليه الاضافة");
                            succes = true;
                            this.Close();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch { }
            }
            //succes = true;
            //this.Close();

        }
    }
}
