using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing.Design;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// مجموعة تابات مبسطة وآمنة للـ Designer
    /// </summary>
    [Editor(typeof(SimpleRJTabPageCollectionEditor), typeof(UITypeEditor))]
    public class SimpleRJTabPageCollection : IList<RJTabPage>, ICollection<RJTabPage>, IEnumerable<RJTabPage>
    {
        private readonly List<RJTabPage> items;
        private RJTabControl parentControl;

        public SimpleRJTabPageCollection()
        {
            this.items = new List<RJTabPage>();
        }

        /// <summary>
        /// ربط المجموعة بالكنترول الأب - آمن
        /// </summary>
        internal void SetParent(RJTabControl parent)
        {
            this.parentControl = parent;
        }

        #region Properties
        public int Count => items.Count;
        public bool IsReadOnly => false;

        public RJTabPage this[int index]
        {
            get 
            { 
                if (index < 0 || index >= items.Count) return null;
                return items[index]; 
            }
            set
            {
                if (value == null || index < 0 || index >= items.Count) return;
                
                try
                {
                    var oldTab = items[index];
                    items[index] = value;
                    
                    // إشعار الكنترول الأب بطريقة آمنة
                    NotifyParentSafely(oldTab, value, index);
                }
                catch
                {
                    // تجاهل الأخطاء في Designer
                }
            }
        }

        public RJTabPage this[string name]
        {
            get => items.Find(tab => tab?.Name == name);
        }
        #endregion

        #region Methods
        public void Add(RJTabPage item)
        {
            if (item == null) return;
            
            try
            {
                items.Add(item);
                NotifyParentSafely(null, item, items.Count - 1);
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public RJTabPage Add(string text)
        {
            try
            {
                var tab = new RJTabPage(text);
                Add(tab);
                return tab;
            }
            catch
            {
                return null;
            }
        }

        public RJTabPage Add(string text, IconChar icon)
        {
            try
            {
                var tab = new RJTabPage(text, icon);
                Add(tab);
                return tab;
            }
            catch
            {
                return null;
            }
        }

        public void Insert(int index, RJTabPage item)
        {
            if (item == null || index < 0 || index > items.Count) return;

            try
            {
                items.Insert(index, item);
                NotifyParentSafely(null, item, index);
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public bool Remove(RJTabPage item)
        {
            if (item == null) return false;
            
            try
            {
                int index = items.IndexOf(item);
                if (index >= 0)
                {
                    items.RemoveAt(index);
                    NotifyParentSafely(item, null, index);
                    return true;
                }
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
            return false;
        }

        public void RemoveAt(int index)
        {
            if (index < 0 || index >= items.Count) return;
            
            try
            {
                var item = items[index];
                items.RemoveAt(index);
                NotifyParentSafely(item, null, index);
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public void Clear()
        {
            try
            {
                var itemsCopy = new List<RJTabPage>(items);
                items.Clear();
                
                // إشعار الكنترول الأب بالمسح
                foreach (var item in itemsCopy)
                {
                    NotifyParentSafely(item, null, -1);
                }
            }
            catch
            {
                // تجاهل الأخطاء في Designer
            }
        }

        public bool Contains(RJTabPage item) => items.Contains(item);
        public void CopyTo(RJTabPage[] array, int arrayIndex) => items.CopyTo(array, arrayIndex);
        public int IndexOf(RJTabPage item) => items.IndexOf(item);

        public IEnumerator<RJTabPage> GetEnumerator() => items.GetEnumerator();
        IEnumerator IEnumerable.GetEnumerator() => GetEnumerator();
        #endregion

        #region Helper Methods
        /// <summary>
        /// إشعار الكنترول الأب بطريقة آمنة تماماً
        /// </summary>
        private void NotifyParentSafely(RJTabPage oldTab, RJTabPage newTab, int index)
        {
            try
            {
                if (parentControl != null)
                {
                    if (oldTab != null)
                    {
                        // إزالة التاب القديم
                        parentControl.RemoveTabSafely(oldTab);
                    }
                    
                    if (newTab != null)
                    {
                        // إضافة التاب الجديد
                        parentControl.AddTabSafely(newTab);
                    }
                }
            }
            catch
            {
                // تجاهل جميع الأخطاء - أولوية للـ Designer
            }
        }
        #endregion
    }

    /// <summary>
    /// محرر مجموعة مبسط وآمن
    /// </summary>
    public class SimpleRJTabPageCollectionEditor : CollectionEditor
    {
        public SimpleRJTabPageCollectionEditor(Type type) : base(type)
        {
        }

        protected override Type CreateCollectionItemType() => typeof(RJTabPage);

        protected override Type[] CreateNewItemTypes() => new Type[] { typeof(RJTabPage) };

        protected override object CreateInstance(Type itemType)
        {
            if (itemType == typeof(RJTabPage))
            {
                try
                {
                    var tab = new RJTabPage($"TabPage{GetSafeItemCount() + 1}")
                    {
                        BackColor = System.Drawing.Color.FromArgb(70, 70, 70),
                        ForeColor = System.Drawing.Color.White,
                        IconChar = IconChar.None
                    };
                    return tab;
                }
                catch
                {
                    return new RJTabPage("TabPage");
                }
            }
            return base.CreateInstance(itemType);
        }

        private int GetSafeItemCount()
        {
            try
            {
                if (Context?.Instance is RJTabControl tabControl)
                {
                    return tabControl.TabCount;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        protected override string GetDisplayText(object value)
        {
            if (value is RJTabPage tab)
            {
                return !string.IsNullOrEmpty(tab.Text) ? tab.Text : $"TabPage{tab.GetHashCode()}";
            }
            return base.GetDisplayText(value);
        }

        protected override bool CanRemoveInstance(object value) => true;
        protected override bool CanSelectMultipleInstances() => false;
    }
}
