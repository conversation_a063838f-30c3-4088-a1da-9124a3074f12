﻿namespace SmartCreator.Forms.UserManager.Reports
{
    partial class Form_Custome_Device_Print
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.ToggleButton_Detail = new SmartCreator.RJControls.RJToggleButton();
            this.ToggleButton_Monthly = new SmartCreator.RJControls.RJToggleButton();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.CheckBox_To_Date = new SmartCreator.RJControls.RJCheckBox();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.rjLabel2);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(405, 313);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(193, 22);
            this.lblCaption.Text = "Form_Custome_Device_Print";
            // 
            // rjLabel2
            // 
            this.rjLabel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(137, 12);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(126, 31);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel2.TabIndex = 94;
            this.rjLabel2.Text = "خيارات الطباعة";
            // 
            // ToggleButton_Detail
            // 
            this.ToggleButton_Detail.Activated = true;
            this.ToggleButton_Detail.Checked = true;
            this.ToggleButton_Detail.CheckState = System.Windows.Forms.CheckState.Checked;
            this.ToggleButton_Detail.Customizable = false;
            this.ToggleButton_Detail.Enabled = false;
            this.ToggleButton_Detail.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Detail.Location = new System.Drawing.Point(259, 21);
            this.ToggleButton_Detail.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Detail.Name = "ToggleButton_Detail";
            this.ToggleButton_Detail.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.OFF_Text = "تفصيلي";
            this.ToggleButton_Detail.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Detail.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Detail.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.ON_Text = "تفصيلي";
            this.ToggleButton_Detail.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Detail.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Detail.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Detail.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Detail.TabIndex = 95;
            this.ToggleButton_Detail.Tag = "تفصيلي";
            this.ToggleButton_Detail.Text = "#";
            this.ToggleButton_Detail.UseVisualStyleBackColor = true;
            // 
            // ToggleButton_Monthly
            // 
            this.ToggleButton_Monthly.Activated = false;
            this.ToggleButton_Monthly.Customizable = false;
            this.ToggleButton_Monthly.Enabled = false;
            this.ToggleButton_Monthly.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.ToggleButton_Monthly.Location = new System.Drawing.Point(146, 20);
            this.ToggleButton_Monthly.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Monthly.Name = "ToggleButton_Monthly";
            this.ToggleButton_Monthly.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.OFF_Text = "يومي";
            this.ToggleButton_Monthly.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Monthly.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Monthly.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.ON_Text = "يومي";
            this.ToggleButton_Monthly.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Monthly.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Monthly.Size = new System.Drawing.Size(91, 25);
            this.ToggleButton_Monthly.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Monthly.TabIndex = 96;
            this.ToggleButton_Monthly.Tag = "تفصيلي";
            this.ToggleButton_Monthly.Text = "#";
            this.ToggleButton_Monthly.UseVisualStyleBackColor = true;
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Enabled = false;
            this.jToggleButton_Year.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.jToggleButton_Year.Location = new System.Drawing.Point(28, 20);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = "شهري";
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = "شهري";
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(91, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 97;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            // 
            // rjPanel1
            // 
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 0;
            this.rjPanel1.Controls.Add(this.jToggleButton_Year);
            this.rjPanel1.Controls.Add(this.ToggleButton_Detail);
            this.rjPanel1.Controls.Add(this.ToggleButton_Monthly);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(11, 49);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(382, 64);
            this.rjPanel1.TabIndex = 98;
            // 
            // CheckBox_To_Date
            // 
            this.CheckBox_To_Date.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CheckBox_To_Date.AutoSize = true;
            this.CheckBox_To_Date.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.BorderSize = 1;
            this.CheckBox_To_Date.Check = true;
            this.CheckBox_To_Date.CheckAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.Checked = true;
            this.CheckBox_To_Date.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_To_Date.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_To_Date.Customizable = false;
            this.CheckBox_To_Date.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.CheckBox_To_Date.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_To_Date.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_To_Date.Location = new System.Drawing.Point(170, 14);
            this.CheckBox_To_Date.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_To_Date.Name = "CheckBox_To_Date";
            this.CheckBox_To_Date.Padding = new System.Windows.Forms.Padding(0, 0, 22, 0);
            this.CheckBox_To_Date.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_To_Date.Size = new System.Drawing.Size(180, 26);
            this.CheckBox_To_Date.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_To_Date.TabIndex = 99;
            this.CheckBox_To_Date.Text = "عرض كل جهاز في صفحه";
            this.CheckBox_To_Date.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_To_Date.UseVisualStyleBackColor = true;
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 0;
            this.rjPanel2.Controls.Add(this.CheckBox_To_Date);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(11, 121);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(382, 113);
            this.rjPanel2.TabIndex = 100;
            // 
            // Form_Custome_Device_Print
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "Form_Custome_Device_Print";
            this.ClientSize = new System.Drawing.Size(415, 363);
            this.ControlBox = false;
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.DoubleBuffered = false;
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_Custome_Device_Print";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "Form_Custome_Device_Print";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJToggleButton ToggleButton_Detail;
        private RJControls.RJToggleButton ToggleButton_Monthly;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJCheckBox CheckBox_To_Date;
    }
}