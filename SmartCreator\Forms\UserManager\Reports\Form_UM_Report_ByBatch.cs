﻿using Newtonsoft.Json;
using Org.BouncyCastle.Math;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager.Reports
{
    public partial class Form_UM_Report_ByBatch : RJChildForm
    {
        string Server_Type = "UM";
        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;
        bool firstLoad = true;
        List<SellingPoint> All_SP = new List<SellingPoint>();
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";


        public Form_UM_Report_ByBatch(string server_Type="UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            if (server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            All_SP = Smart_DA.Load<SellingPoint>($"select * from SellingPoint where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ");

            this.Text = "تقارير دفعات الكروت";
            Spanel.Width = 0;

            //if (UIAppearance.Theme == UITheme.Dark)
            //    pnl_side_sn.Customizable = false;

            if (!UIAppearance.Language_ar)
            {
                this.Text = "Reports UserManager";
                this.dgv.RightToLeft = RightToLeft.No;
            }
            if (UIAppearance.Theme == UITheme.Dark)
            {

                rjPanel1.Customizable = false;
            }
            set_font();

            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00").AddDays(-90);
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");

            CheckBox_To_Date.Check = true;

            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
            CBox_SN_Compar.SelectedIndex = 3;
            Cbox_View.SelectedIndex = 0;
            Cbox_View.label.RightToLeft = RightToLeft.Yes;
            Cbox_View.label.TextAlign = ContentAlignment.MiddleLeft;

            Server_Type = server_Type;
        }
        private void set_font()
        {
            //return;
            dgv.AllowUserToOrderColumns = true;
            DGV_detail.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            DGV_detail.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            DGV_detail.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 35;
            //DGV_detail.ColumnHeadersHeight = 35;
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;

            rjLabel3.Font=rjLabel3.Font=rjLabel4.Font=check_with_Commi.Font=rjLabel6.Font= rjLabel2.Font=
                rjLabel7.Font= rjLabel8.Font=rjLabel9.Font=rjLabel10.Font=rjLabel14.Font=rjLabel15.Font=rjLabel16.Font=rjLabel17.Font=rjLabel21.Font=

                rjDateTime_From.Font=rjDateTime_To.Font=
               
                Toggle_By_Profile.Font= Toggle_By_SP.Font= CheckBox_To_Date.Font=
                Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            btn_.Font=btn_Filter.Font=btn_apply.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.dgv_textSize(DGV_detail);
            utils.item_Contrlol_textSize(dmAll_Cards);

        }
        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }

        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }

        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width > 50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = 260;
            }
        }


        private void timer_Tick(object sender, EventArgs e)
        {
            timer.Stop();
            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();


            get_report();
            try
            {
                if (dgv.Rows.Count > 0)
                {
                    Sub_LocadData(Convert.ToInt32( dgv.Rows[0].Cells["الدفعة"].Value.ToString()));
                }
            }
            catch { }
            firstLoad = false;

        }

        private void Form_UM_Report_ByBatch_Load(object sender, EventArgs e)
        {
            timer.Start();


            
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }

        private DataTable dt_Batch()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الدفعة", typeof(double));
            dt.Columns.Add("الطبعة", typeof(double));
            dt.Columns.Add("التاريخ", typeof(string));
            dt.Columns.Add("عدد الكروت", typeof(double));
            //dt.Columns.Add("عدد الطبعات", typeof(double));
            dt.Columns.Add("الكروت المباعة", typeof(double));

            dt.Columns.Add("اجمالي المبيعات", typeof(string));
            dt.Columns.Add("الوقت المستخدم", typeof(string));
            dt.Columns.Add("الاستهلاك المستخدم", typeof(string));
            //dt.Columns.Add("عدد الجلسات", typeof(double));

            dt.Columns.Add("Price", typeof(double));
            dt.Columns.Add("UptimeUsed", typeof(double));
            dt.Columns.Add("Up_Down", typeof(double));

            dt.Columns.Add("الباقة", typeof(string));
            dt.Columns.Add("نقطة البيع", typeof(string));


            return dt;
        }
        private DataTable dt_ByDetails()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("رقم الدفعة", typeof(string));
            dt.Columns.Add("التاريخ", typeof(string));
            dt.Columns.Add("الباقة", typeof(string));
            dt.Columns.Add("نقطة البيع", typeof(string));
            dt.Columns.Add("عدد كروت الدفعة", typeof(double));
            dt.Columns.Add("عدد الكروت المباعة", typeof(double));
            dt.Columns.Add("اجمالي المبيعات", typeof(string));
            dt.Columns.Add("الوقت المستخدم", typeof(string));
            dt.Columns.Add("الاستهلاك المستخدم", typeof(string));
            //dt.Columns.Add("التحميل المستخدم", typeof(string));
            //dt.Columns.Add("الرفع المستخدم", typeof(string));
            //dt.Columns.Add("تحميل+الرفع المستخدم", typeof(string));
            //dt.Columns.Add("الوقت المسموح", typeof(string));
            //dt.Columns.Add("الاستهلاك المسموح", typeof(string));
            //dt.Columns.Add("اجمالي الجلسات", typeof(double));
            dt.Columns.Add("عدد جلسات الدفعة", typeof(double));

            dt.Columns.Add("Price", typeof(double));
            dt.Columns.Add("UptimeUsed", typeof(double));
            dt.Columns.Add("DownloadUsed", typeof(double));
            dt.Columns.Add("UploadUsed", typeof(double));
            dt.Columns.Add("Up_Down", typeof(double));
            
            //dt.Columns.Add("Upload", typeof(double));

            //dt.Columns.Add("UptimeLimt", typeof(double));
            //dt.Columns.Add("DownloadLimit", typeof(double));
            //dt.Columns.Add("count", typeof(double));
            return dt;
        }

        private void get_report()
        {
            try
            {
                try { dgv.DataSource = null; DGV_detail.DataSource = null; } catch { }
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                string Batch_whereFilter = $"WHERE BatchCard.AddedDate>='{rjDateTime_From.Value.Date.ToString("yyyy-MM-dd")}' and BatchCard.AddedDate<='{rjDateTime_To.Value.Date.AddDays(1).ToString("yyyy-MM-dd")}' and (BatchCard.Rb='{rb}' or BatchCard.Rb='{rb_sn}')  ";
                if (Server_Type == "UM")
                    Batch_whereFilter += " and BatchCard.Server = 0 ";
                if (Server_Type == "HS")
                    Batch_whereFilter += " and BatchCard.Server = 1 ";



                string Query_Get_Batch = $@"select BatchCard.*,	BatchCard.BatchNumber, 
	                                        sum(NumberPrintCard.Count) as Count  
	                                        from BatchCard INNER JOIN NumberPrintCard ON BatchCard.BatchNumber=NumberPrintCard.BatchNumber 
	                                        {Batch_whereFilter} 
                                           group by BatchCard.BatchNumber  order by BatchCard.AddedDate desc";

                List<BatchCard> ResAllbatch = Smart_DA.Load<BatchCard>(Query_Get_Batch);
                string In = "";
                foreach (BatchCard batch in ResAllbatch)
                {
                    In = In+ batch.BatchNumber + ",";
                }
                char[] charsToTrim1 = { ',' };

                string Price = "Price";
                if (check_with_Commi.Checked)
                    Price = "TotalPrice";

                string Sales_Batch = $@"SELECT  u.BatchCardId as BatchCardId,
                                                sum(u.UptimeUsed) as UptimeUsed , 
                                                sum(u.DownloadUsed+u.UploadUsed) as  Up_Down , 
                                                sum(u.{Price}) as Price , 
                                                count(u.Sn_Name) as count
                                                FROM UmUser u 
                                                WHERE u.BatchCardId IN ({In.TrimEnd(charsToTrim1)})  AND u.DownloadUsed>0 
                                                GROUP BY  u.BatchCardId 
                                                " ;
                DataTable res_user  = Local_DA.RunSqlCommandAsDatatable(Sales_Batch);
                DataTable dt = dt_Batch();
                var batch_Sales = from t1 in ResAllbatch
                                   join u in res_user.AsEnumerable() on t1.BatchNumber equals u.Field<Int64>("BatchCardId")
                                   select dt.LoadDataRow(new object[]
                                   {
                                        t1.BatchNumber,
                                        t1.BatchNumber,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,
                                        u.Field<Int64>("count"),
                                        String.Format("{0:n0}", double.TryParse(u.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(u.Field<double>("Price")) : 0),
                                        utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(u.Field<Int64>("UptimeUsed"))),
                                        utils.ConvertSize_Get_InArabic(u.Field<long>("Up_Down").ToString() ),

                                         u.Field<double>("Price"),
                                         u.Field<Int64>("UptimeUsed"),
                                         u.Field<Int64>("Up_Down"),

                                   }, false);
                if (batch_Sales.Count().ToString() != "0")
                {

                }

                dgv.DataSource = dt;
                try { dgv.Columns["الطبعة"].Visible = false; } catch { }
                try { dgv.Columns["الباقة"].Visible = false; } catch { }
                try { dgv.Columns["نقطة البيع"].Visible = false; } catch { }
                try { dgv.Columns["Price"].Visible = false; } catch { }
                try { dgv.Columns["UptimeUsed"].Visible = false; } catch { }
                try { dgv.Columns["Up_Down"].Visible = false; } catch { }

                
                //update_header_DGV(dgv);

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void get_report0()
        {
            if(Toggle_GroupBy_BatchID.Checked==false)
            {
                get_report2();
                return;
            }
            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            dgv.DataSource = null;
            string Query_Conditon = condition_detail_firstUse();

            string GroupBy = "GROUP BY  u.BatchCardId ";
            string GroupBy_Batch = "GROUP BY  BatchNumber ";

            if(Toggle_GroupBy_Profile.Checked)
            {
                GroupBy += ",u.ProfileName";
                GroupBy_Batch += ",ProfileName";
            }
            if(Toggle_GroupBy_SP.Checked)
            {
                GroupBy += ",u.SpCode";
                GroupBy_Batch += ",SpCode";
            }

            string Price = "Price";
            string TableUser = "UmUser";
            string TablePyment = "UmPyment";
            string TableSession = "UmSession";

            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            string Qury_With_CountSession = $@"WITH t1 AS (
                    SELECT u.BatchCardId
                    
                    ,sum(p.{Price}) Price
                    ,count(DISTINCT u.Sn_Name) CardCount
                    ,p.AddedDate
                    FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name=p.Fk_Sn_Name 
                    WHERE u.BatchCardId IS NOT NULL {Query_Conditon} AND u.FirsLogin IS NOT NULL AND u.UptimeUsed>0
                    {GroupBy}
                    ORDER BY u.BatchCardId DESC
                    )

                    SELECT u.BatchCardId
                    
                    ,u.Price 
                    ,s.Uptime
                    ,s.BytesDownload
                    ,s.BytesUpload
                    ,u.CardCount
                    ,s.CountSession
                    ,u.AddedDate
                    FROM t1 u INNER JOIN ( 
                    SELECT u.BatchCardId,
                    sum(s.UpTime) UpTime , 
                    sum(s.BytesDownload)  Up_Down , 
                    sum(s.BytesUpload) BytesUpload ,
                    count(s.Sn_Name) CountSession
 
                    FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name 
                    WHERE u.BatchCardId IS NOT NULL {Query_Conditon}
                    GROUP BY  u.BatchCardId  ) s ON u.BatchCardId = s.BatchCardId

                    ";



            string Qury2 = $@"SELECT u.BatchCardId
                            ,sum(p.{Price}) Price
                            ,sum(DISTINCT u.UptimeUsed) Uptime
                            ,sum(DISTINCT u.DownloadUsed) DownloadUsed
                            ,sum(DISTINCT u.UploadUsed) UploadUsed
                            ,sum(DISTINCT u.UploadUsed+u.DownloadUsed) Up_Down
                            ,count(DISTINCT u.Sn_Name) CardCount
                            FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name=p.Fk_Sn_Name 
                            WHERE u.BatchCardId IS NOT NULL AND  u.UptimeUsed>0 AND Status>0 {Query_Conditon} 
                            {GroupBy}
                            ORDER BY u.BatchCardId DESC";

            try
            {

                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                DataTable dt = dt_ByDetails();
                DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury2);
                stopwatch.Stop();
                //dgv.DataSource = tbFound;
                string Query_Batch = $"select BatchNumber ,sum(BatchCard.Count) count ,AddedDate,SpCode,ProfileName ,min(Sn_from) Sn_from,max(Sn_to) Sn_to" +
                    $" from BatchCard  " +
                    $" where AddedDate >='{str_from_Date}' AND AddedDate<='{str_to_Date}' and ( rb='{Global_Variable.Mk_resources.RB_code}' or rb='{Global_Variable.Mk_resources.RB_SN}') " +
                   
                    $"{GroupBy_Batch} ";
                
                //Query_Batch += $" and AddedDate >='{ str_from_Date}' AND AddedDate<='{str_to_Date}'  ";

                List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Query_Batch);

                var dtbatch = from user in tbFound.AsEnumerable()
                              join bt in Allbatch on user.Field<Int64>("BatchCardId") equals bt.BatchNumber
                        select dt.LoadDataRow(new object[]
                        {
                            bt.BatchNumber,
                            bt.ProfileName,
                            bt.SpCode,
                            bt.AddedDate,
                            bt.Count,
                            user.Field<Int64>("CardCount"),
                            String.Format("{0:n0}", double.TryParse(user.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(user.Field<double>("Price")) : 0),
                            utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(user.Field<Int64>("UpTime"))),
                            utils.ConvertSize_Get_InArabic(user.Field<long>("Up_Down").ToString() ),
                            0,
                            //user.Field<Int64>("CountSession"),
                            user.Field<double>("Price"),
                            user.Field<Int64>("UpTime"),
                            user.Field<Int64>("DownloadUsed"),
                            user.Field<Int64>("UploadUsed"),
                            user.Field<Int64>("Up_Down"),
            }, false);

                if (dtbatch.Count().ToString() != "0")
                {
                    //dgv.DataSource = dt;
                }
                dgv.DataSource = dt;
                
                //dgv.Columns["الاستهلاك المسموح"].Visible = false;
                try { dgv.Columns["Price"].Visible = false; } catch { }
                try { dgv.Columns["UptimeUsed"].Visible = false;} catch { }
                try {dgv.Columns["DownloadUsed"].Visible = false; } catch { }
                try {dgv.Columns["UploadUsed"].Visible = false; } catch { }
                try {dgv.Columns["Up_Down"].Visible = false; } catch { }
                try {dgv.Columns["عدد جلسات الدفعة"].Visible = false; } catch { }
                try {dgv.Columns["نقطة البيع"].Visible = false; } catch { }
                try {dgv.Columns["الباقة"].Visible = false; } catch { }
                try {dgv.Columns["التاريخ"].Visible = false; } catch { }
                
                //loadDgvState();

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            //update_select_DGV();
        }
        private void get_report2()
        {
            string TableUser = "UmUser";
            string TablePyment = "UmPyment";
            string TableSession = "UmSession";

            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            dgv.DataSource = null;
            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            //string Query_Batch = $"select BatchNumber ,sum(BatchCard.Count) count ,AddedDate,SpCode,ProfileName ,min(Sn_from) Sn_from,max(Sn_to) Sn_to" +
            //       $" from BatchCard  " +
            //       $" where AddedDate >='{str_from_Date}' AND AddedDate<='{str_to_Date}' and rb='{Global_Variable.Mk_resources.RB_code}' " +

            //       $"{GroupBy_Batch} ";


            string Batch_where = $"select * from BatchCard where AddedDate >='{str_from_Date}' AND AddedDate<='{str_to_Date}' and ( rb='{Global_Variable.Mk_resources.RB_code}' or rb='{Global_Variable.Mk_resources.RB_code}') ";

            string Query_Conditon = condition_detail_firstUse();
            string Price = "Price";

            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            string Qury2 = $@"SELECT u.BatchCardId
                            ,sum(p.{Price}) Price
                            ,u.Sn
                            ,u.ProfileName
                            ,u.SpCode
                            ,sum(DISTINCT u.UptimeUsed) Uptime
                            ,sum(DISTINCT u.DownloadUsed) DownloadUsed
                            ,sum(DISTINCT u.UploadUsed) UploadUsed
                            ,sum(DISTINCT u.UploadUsed+u.DownloadUsed) Up_Down
                            ,count(DISTINCT u.Sn_Name) CardCount
                            FROM {TableUser}  u INNER JOIN {TablePyment} p ON u.Sn_Name=p.Fk_Sn_Name 
                            WHERE u.BatchCardId IS NOT NULL AND  u.UptimeUsed>0 AND Status>0 {Query_Conditon} 
                             GROUP BY  u.Sn_Name 
                            ORDER BY u.BatchCardId DESC";
           
            try
            {
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();
                DataTable Qt2 = Local_DA.RunSqlCommandAsDatatable(Qury2);

                if (Toggle_GroupBy_BatchID.Checked)
                {
                    Batch_where += " GROUP BY BatchNumber ORDER BY BatchNumber DESC";
                    List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Batch_where);
                    DataTable dt = GrupeByBatchCardId_Group(Qt2, Allbatch);
                    dgv.DataSource = dt;
                }
                else if (Toggle_GroupBy_Profile.Checked && !Toggle_GroupBy_SP.Checked)
                {
                    Batch_where += " GROUP BY BatchNumber,ProfileName ORDER BY BatchNumber DESC";
                    List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Batch_where);
                    DataTable dt = GrupeByProfile(Qt2, Allbatch);
                    dgv.DataSource = dt;
                }
                else if (!Toggle_GroupBy_Profile.Checked && Toggle_GroupBy_SP.Checked)
                {
                    Batch_where += " GROUP BY BatchNumber,SpCode ORDER BY BatchNumber DESC";
                    List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Batch_where);
                    DataTable dt = GrupeBySP(Qt2, Allbatch);
                    dgv.DataSource = dt;
                }
                else if ( Toggle_GroupBy_Profile.Checked && Toggle_GroupBy_SP.Checked)
                {
                    Batch_where += " GROUP BY BatchNumber,SpCode,ProfileName  ORDER BY BatchNumber DESC";
                    List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Batch_where);
                    DataTable dt = GrupeBy_SP_Profile(Qt2, Allbatch);
                    dgv.DataSource = dt;
                }
                else
                {
                    Batch_where += " ORDER BY BatchNumber DESC";
                    List<BatchCard> Allbatch = Smart_DA.Load<BatchCard>(Batch_where);
                    DataTable dt = GrupeBy_BatchID(Qt2, Allbatch);
                    dgv.DataSource = dt;
                }

                //DataTable dt = dt_ByDetails();
                //foreach (var i in Allbatch)
                //{
                //    var grouped = Qt2.AsEnumerable().Where(a =>  a.Field<Int64>("BatchCardId") == i.BatchNumber && a.Field<Int64>("Sn") >= i.Sn_from && a.Field<Int64>("Sn") <= i.Sn_to )
                //        .GroupBy(x => i.BatchNumber)
                //        .Select(g => new
                //        {
                //            BatchNumber = g.Key,
                //            CardCount=g.Count(),
                //            Price =g.Sum(x => x.Field<double>("Price")),
                //            UpTime=g.Sum(x => x.Field<Int64>("UpTime")),
                //            DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                //            UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                //            Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                //        }).ToList();

                //    if (grouped.Count > 0)
                //    {
                //        DataRow dr = dt.NewRow();
                //        dr["رقم الدفعة"] =i.BatchNumber;
                //        dr["التاريخ"] =i.AddedDate;
                //        dr["عدد كروت الدفعة"] =i.Count;
                //        dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                //        dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                //        dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                //        dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                //        dr["الباقة"] = i.ProfileName;
                //        dr["نقطة البيع"] = i.SpCode;
                //        dr["Price"] = grouped.First().Price;
                //        dr["UptimeUsed"] = grouped.First().UpTime;
                //        dr["DownloadUsed"] = grouped.First().DownloadUsed;
                //        dr["UploadUsed"] = grouped.First().UploadUsed;
                //        dr["Up_Down"] = grouped.First().Up_Down;
                //        dt.Rows.Add(dr);
                //    }
                //}

                //dgv.DataSource= dt;
                try { dgv.Columns["Price"].Visible = false; } catch { }
                try { dgv.Columns["UptimeUsed"].Visible = false; } catch { }
                try { dgv.Columns["DownloadUsed"].Visible = false; } catch { }
                try { dgv.Columns["UploadUsed"].Visible = false; } catch { }
                try { dgv.Columns["الباقة"].Visible = false; } catch { }
                try { dgv.Columns["نقطة البيع"].Visible = false; } catch { }
                try { dgv.Columns["عدد جلسات الدفعة"].Visible = false; } catch { }
                try { dgv.Columns["Up_Down"].Visible = false; } catch { }
                try { dgv.Columns["التاريخ"].Visible = false; } catch { }

                if (Toggle_GroupBy_SP.Checked)
                {
                    try { dgv.Columns["نقطة البيع"].Visible = true; } catch { }
                }
                if (Toggle_GroupBy_Profile.Checked)
                {
                    try { dgv.Columns["الباقة"].Visible = true; } catch { }
                }
                if ((!Toggle_GroupBy_SP.Checked && !Toggle_GroupBy_Profile.Checked) && !Toggle_GroupBy_BatchID.Checked)
                {
                    try { dgv.Columns["نقطة البيع"].Visible = true; } catch { }
                    try { dgv.Columns["الباقة"].Visible = true; } catch { }
                    try { dgv.Columns["التاريخ"].Visible = true; } catch { }
                }
                 stopwatch.Stop();
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            //update_select_DGV();
        }

        private DataTable GrupeByBatchCardId_Group(DataTable Qt2,List<BatchCard> Allbatch)
        {
            DataTable dt = dt_ByDetails();
            foreach (var i in Allbatch)
            {
                //var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber && a.Field<Int64>("Sn") >= i.Sn_from && a.Field<Int64>("Sn") <= i.Sn_to)
                var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber )
                    .GroupBy(x => x.Field<Int64>("BatchCardId"))
                    .Select(g => new
                    {
                        BatchNumber = g.Key,
                        CardCount = g.Count(),
                        Price = g.Sum(x => x.Field<double>("Price")),
                        UpTime = g.Sum(x => x.Field<Int64>("UpTime")),
                        DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                        UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                        Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                    }).ToList();

                if (grouped.Count > 0)
                {
                    DataRow dr = dt.NewRow();
                    dr["رقم الدفعة"] = i.BatchNumber;
                    dr["التاريخ"] = i.AddedDate;
                    dr["عدد كروت الدفعة"] = i.Count;
                    dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                    dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                    dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                    dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                    dr["الباقة"] = i.ProfileName;
                    dr["نقطة البيع"] = i.SpCode;
                    dr["Price"] = grouped.First().Price;
                    dr["UptimeUsed"] = grouped.First().UpTime;
                    dr["DownloadUsed"] = grouped.First().DownloadUsed;
                    dr["UploadUsed"] = grouped.First().UploadUsed;
                    dr["Up_Down"] = grouped.First().Up_Down;
                    dt.Rows.Add(dr);
                }
            }
            return dt;


        }
        private DataTable GrupeByProfile(DataTable Qt2, List<BatchCard> Allbatch)
        {
            DataTable dt = dt_ByDetails();
            foreach (var i in Allbatch)
            {
                //var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber && a.Field<Int64>("Sn") >= i.Sn_from && a.Field<Int64>("Sn") <= i.Sn_to)
                var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber )
                     .GroupBy(x => new
                     {
                         BatchNumber = x["BatchCardId"],
                         ProfileName = x["ProfileName"]
                     })
                    //.GroupBy(s => new { i.BatchNumber, i.ProfileName })
                    //.GroupBy(s => new { i.BatchNumber, i.ProfileName })
                    //.GroupBy(x => i.BatchNumber)

                    .Select(g => new
                    {
                        BatchNumber = g.Key.BatchNumber,
                        Count = i.Count,
                        CardCount = g.Count(),
                        Price = g.Sum(x => x.Field<double>("Price")),
                        UpTime = g.Sum(x => x.Field<Int64>("UpTime")),
                        DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                        UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                        Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                    }).ToList();

                if (grouped.Count > 0)
                {
                    foreach (var b in grouped)
                    {
                        DataRow dr = dt.NewRow();
                        dr["رقم الدفعة"] = b.BatchNumber;
                        //dr["التاريخ"] = b.AddedDate;
                        dr["عدد كروت الدفعة"] = b.Count;
                        dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                        dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                        dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                        dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                        dr["الباقة"] = i.ProfileName;
                        dr["نقطة البيع"] = i.SpCode;
                        dr["Price"] = grouped.First().Price;
                        dr["UptimeUsed"] = grouped.First().UpTime;
                        dr["DownloadUsed"] = grouped.First().DownloadUsed;
                        dr["UploadUsed"] = grouped.First().UploadUsed;
                        dr["Up_Down"] = grouped.First().Up_Down;
                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;


        }
        private DataTable GrupeBySP(DataTable Qt2, List<BatchCard> Allbatch)
        {
            DataTable dt = dt_ByDetails();
            foreach (var i in Allbatch)
            {
                //var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber && a.Field<Int64>("Sn") >= i.Sn_from && a.Field<Int64>("Sn") <= i.Sn_to)
                var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber )
                    //.GroupBy(s => new { i.BatchNumber, i.SpCode })
                     .GroupBy(x => new
                     {
                         BatchNumber = x["BatchCardId"],
                         ProfileName = x["SpCode"]
                     })
                    //.GroupBy(x => i.BatchNumber)
                    .Select(g => new
                    {
                        BatchNumber = g.Key.BatchNumber,
                        CardCount = g.Count(),
                        Price = g.Sum(x => x.Field<double>("Price")),
                        UpTime = g.Sum(x => x.Field<Int64>("UpTime")),
                        DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                        UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                        Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                    }).ToList();

                if (grouped.Count > 0)
                {
                    foreach (var b in grouped)
                    {
                        DataRow dr = dt.NewRow();
                        dr["رقم الدفعة"] = b.BatchNumber;
                        //dr["التاريخ"] = i.AddedDate;
                        dr["عدد كروت الدفعة"] = i.Count;
                        dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                        dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                        dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                        dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                        dr["الباقة"] = i.ProfileName;
                        dr["نقطة البيع"] = i.SpCode;
                        dr["Price"] = grouped.First().Price;
                        dr["UptimeUsed"] = grouped.First().UpTime;
                        dr["DownloadUsed"] = grouped.First().DownloadUsed;
                        dr["UploadUsed"] = grouped.First().UploadUsed;
                        dr["Up_Down"] = grouped.First().Up_Down;
                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;


        }
        private DataTable GrupeBy_SP_Profile(DataTable Qt2, List<BatchCard> Allbatch)
        {
            DataTable dt = dt_ByDetails();
            foreach (var i in Allbatch)
            {
                var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber )
                    //.GroupBy(s => new { i.BatchNumber, i.SpCode,i.ProfileName })
                     .GroupBy(x => new
                     {
                         BatchNumber = x["BatchCardId"],
                         ProfileName = x["ProfileName"],
                         SpCode = x["SpCode"]
                     })
                    //.GroupBy(x => i.BatchNumber)
                    .Select(g => new
                    {
                        //BatchNumber = g.Key,
                        BatchNumber = g.Key.BatchNumber,
                        CardCount = g.Count(),
                        Price = g.Sum(x => x.Field<double>("Price")),
                        UpTime = g.Sum(x => x.Field<Int64>("UpTime")),
                        DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                        UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                        Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                    }).ToList();

                if (grouped.Count > 0)
                {
                    foreach (var b in grouped)
                    {
                        DataRow dr = dt.NewRow();
                        dr["رقم الدفعة"] = b.BatchNumber;
                        //dr["التاريخ"] = i.AddedDate;
                        dr["عدد كروت الدفعة"] = i.Count;
                        dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                        dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                        dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                        dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                        dr["الباقة"] = i.ProfileName;
                        dr["نقطة البيع"] = i.SpCode;
                        dr["Price"] = grouped.First().Price;
                        dr["UptimeUsed"] = grouped.First().UpTime;
                        dr["DownloadUsed"] = grouped.First().DownloadUsed;
                        dr["UploadUsed"] = grouped.First().UploadUsed;
                        dr["Up_Down"] = grouped.First().Up_Down;
                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;


        }
        private DataTable GrupeBy_BatchID(DataTable Qt2 , List<BatchCard> Allbatch)
        {

            DataTable dt = dt_ByDetails();
            foreach (var i in Allbatch)
            {
                var grouped = Qt2.AsEnumerable().Where(a => a.Field<Int64>("BatchCardId") == i.BatchNumber && a.Field<Int64>("Sn") >= i.Sn_from && a.Field<Int64>("Sn") <= i.Sn_to)
                    .GroupBy(s => i.Id)
                    //.GroupBy(x => i.BatchNumber)
                    .Select(g => new
                    {
                        BatchNumber = i.BatchNumber,
                        //BatchNumber = g.Key.BatchNumber,
                        CardCount = g.Count(),
                        Price = g.Sum(x => x.Field<double>("Price")),
                        UpTime = g.Sum(x => x.Field<Int64>("UpTime")),
                        DownloadUsed = g.Sum(x => x.Field<Int64>("DownloadUsed")),
                        UploadUsed = g.Sum(x => x.Field<Int64>("UploadUsed")),
                        Up_Down = g.Sum(x => x.Field<Int64>("Up_Down")),
                    }).ToList();

                if (grouped.Count > 0)
                {
                    foreach (var b in grouped)
                    {
                        DataRow dr = dt.NewRow();
                        dr["رقم الدفعة"] = b.BatchNumber;
                        dr["التاريخ"] = i.AddedDate;
                        dr["عدد كروت الدفعة"] = i.Count;
                        dr["عدد الكروت المباعة"] = grouped.First().CardCount;
                        dr["اجمالي المبيعات"] = String.Format("{0:n0}", grouped.First().Price);
                        dr["الوقت المستخدم"] = utils.Get_Seconds_By_clock_Mode(grouped.First().UpTime);
                        dr["الاستهلاك المستخدم"] = utils.ConvertSize_Get_InArabic(grouped.First().Up_Down.ToString());
                        dr["الباقة"] = i.ProfileName;
                        dr["نقطة البيع"] = i.SpCode;
                        dr["Price"] = grouped.First().Price;
                        dr["UptimeUsed"] = grouped.First().UpTime;
                        dr["DownloadUsed"] = grouped.First().DownloadUsed;
                        dr["UploadUsed"] = grouped.First().UploadUsed;
                        dr["Up_Down"] = grouped.First().Up_Down;
                        dt.Rows.Add(dr);
                    }
                }
            }
            return dt;
        }
        private string ColumnShow = "";
        
        private string condition_detail_firstUse()
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            conditon_date = " and u.RegDate >='" + str_from_Date + "' AND u.RegDate<='" + str_to_Date + "'  ";


            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    conditon_date = " ";
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    { nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",s.NasPortId"; }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    { radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",s.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    Sub_LocadData(Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["الدفعة"].Value.ToString()));
                }
            }
            catch { }
        }
        private void Sub_LocadData(int _BatchNumber = -1)
        {
            try
            {
                DGV_detail.DataSource = null;
                rjTextBox1.Text = "0";
            }
            catch { }
            try
            {
                string GroupBy = "Group By NumberPrint ";
                string GroupByUser = "Group By NumberPrint ";
                if (Toggle_By_Profile.Checked || Toggle_By_SP.Checked)
                {
                    GroupBy = " group by ";
                    if (Toggle_By_SP.Checked)
                        GroupBy = GroupBy + " SpCode ";
                    if (Toggle_By_Profile.Checked)
                    {
                        if (Toggle_By_SP.Checked)
                            GroupBy = GroupBy + " ,ProfileName ";
                        else
                            GroupBy = GroupBy + " ProfileName ";
                    }
                    GroupByUser = $"{GroupBy}  ";
                }

                string Server = " and Server = 0 ";
                string Batch_whereFilter = $"WHERE BatchNumber={_BatchNumber} and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}')";
                if (Server_Type == "HS")
                    Server = " and Server = 1 ";
                Batch_whereFilter = Batch_whereFilter + Server;

                string QueryNumberPrint = $"SELECT *,sum(Count) as Count,SpCode,ProfileName FROM NumberPrintCard {Batch_whereFilter}  {GroupBy}";
                List<NumberPrintCard> ResAllumberPrint = Smart_DA.Load<NumberPrintCard>(QueryNumberPrint);

                string Price = "Price";
                if (check_with_Commi.Checked)
                    Price = "TotalPrice";

                
                DataTable dt = dt_Batch();
              
                if (Toggle_By_Profile.Checked == false && Toggle_By_SP.Checked == false)
                {
                    string ResUserSalesPrint = $@"SELECT u.BatchCardId,u.NumberPrint,u.SpCode,u.ProfileName,
                                                    sum(u.UptimeUsed) as UptimeUsed , 
                                                    sum(u.DownloadUsed+u.UploadUsed) as  Up_Down , 
                                                    sum(u.{Price}) as Price , 
                                                    count(u.Sn_Name) as count
                                                    FROM UmUser u 
                                                    WHERE u.BatchCardId={_BatchNumber}  AND  u.DownloadUsed>0 
                                                    GROUP BY  u.NumberPrint ";

                    DataTable res_user = Local_DA.RunSqlCommandAsDatatable(ResUserSalesPrint);

                    var batch_Sales = from t1 in ResAllumberPrint
                                      join u in res_user.AsEnumerable() on t1.NumberPrint equals u.Field<Int64>("NumberPrint")
                                      select dt.LoadDataRow(new object[]
                                      {
                                        t1.BatchNumber,
                                        t1.NumberPrint,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,
                                        u.Field<Int64>("count"),
                                        String.Format("{0:n0}", double.TryParse(u.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(u.Field<double>("Price")) : 0),
                                        utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(u.Field<Int64>("UptimeUsed"))),
                                        utils.ConvertSize_Get_InArabic(u.Field<long>("Up_Down").ToString() ),

                                         u.Field<double>("Price"),
                                         u.Field<Int64>("UptimeUsed"),
                                         u.Field<Int64>("Up_Down"),
                                         t1.ProfileName,
                                         (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),


                                      }, false);
                    if (batch_Sales.Count().ToString() != "0")
                    {

                    }
                }

               else if (Toggle_By_Profile.Checked  && Toggle_By_SP.Checked == false)
                {
                    string ResUserSalesPrint = $@"SELECT u.BatchCardId,u.NumberPrint,u.SpCode,u.ProfileName,
                                                    sum(u.UptimeUsed) as UptimeUsed , 
                                                    sum(u.DownloadUsed+u.UploadUsed) as  Up_Down , 
                                                    sum(u.{Price}) as Price , 
                                                    count(u.Sn_Name) as count
                                                    FROM UmUser u 
                                                    WHERE u.BatchCardId={_BatchNumber}  AND  u.DownloadUsed>0 
                                                    GROUP BY  u.ProfileName ";

                    DataTable res_user = Local_DA.RunSqlCommandAsDatatable(ResUserSalesPrint);

                    var batch_Sales = from t1 in ResAllumberPrint
                                      join u in res_user.AsEnumerable() on t1.ProfileName equals u.Field<string>("ProfileName")
                                      select dt.LoadDataRow(new object[]
                                      {
                                        t1.BatchNumber,
                                        t1.NumberPrint,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,
                                        u.Field<Int64>("count"),
                                        String.Format("{0:n0}", double.TryParse(u.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(u.Field<double>("Price")) : 0),
                                        utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(u.Field<Int64>("UptimeUsed"))),
                                        utils.ConvertSize_Get_InArabic(u.Field<long>("Up_Down").ToString() ),

                                         u.Field<double>("Price"),
                                         u.Field<Int64>("UptimeUsed"),
                                         u.Field<Int64>("Up_Down"),
                                         t1.ProfileName,
                                         (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),


                                      }, false);
                    if (batch_Sales.Count().ToString() != "0")
                    {

                    }
                }

                else if (Toggle_By_Profile.Checked==false && Toggle_By_SP.Checked)
                {
                    string ResUserSalesPrint = $@"SELECT u.BatchCardId,u.NumberPrint,u.SpCode,u.ProfileName,
                                                    sum(u.UptimeUsed) as UptimeUsed , 
                                                    sum(u.DownloadUsed+u.UploadUsed) as  Up_Down , 
                                                    sum(u.{Price}) as Price , 
                                                    count(u.Sn_Name) as count
                                                    FROM UmUser u 
                                                    WHERE u.BatchCardId={_BatchNumber}  AND  u.DownloadUsed>0 
                                                    GROUP BY  u.SpCode ";

                    DataTable res_user = Local_DA.RunSqlCommandAsDatatable(ResUserSalesPrint);

                    var batch_Sales = from t1 in ResAllumberPrint
                                      join u in res_user.AsEnumerable() on t1.SpCode equals u.Field<string>("SpCode")
                                      select dt.LoadDataRow(new object[]
                                      {
                                        t1.BatchNumber,
                                        t1.NumberPrint,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,
                                        u.Field<Int64>("count"),
                                        String.Format("{0:n0}", double.TryParse(u.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(u.Field<double>("Price")) : 0),
                                        utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(u.Field<Int64>("UptimeUsed"))),
                                        utils.ConvertSize_Get_InArabic(u.Field<long>("Up_Down").ToString() ),

                                         u.Field<double>("Price"),
                                         u.Field<Int64>("UptimeUsed"),
                                         u.Field<Int64>("Up_Down"),
                                         t1.ProfileName,
                                         (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),


                                      }, false);
                    if (batch_Sales.Count().ToString() != "0")
                    {

                    }
                }


                else if (Toggle_By_Profile.Checked  && Toggle_By_SP.Checked)
                {
                    string ResUserSalesPrint = $@"SELECT u.BatchCardId,u.NumberPrint,u.SpCode,u.ProfileName,
                                                    sum(u.UptimeUsed) as UptimeUsed , 
                                                    sum(u.DownloadUsed+u.UploadUsed) as  Up_Down , 
                                                    sum(u.{Price}) as Price , 
                                                    count(u.Sn_Name) as count
                                                    FROM UmUser u 
                                                    WHERE u.BatchCardId={_BatchNumber}  AND  u.DownloadUsed>0 
                                                    GROUP BY  u.SpCode ,u.ProfileName ";

                    DataTable res_user = Local_DA.RunSqlCommandAsDatatable(ResUserSalesPrint);

                    var batch_Sales = from t1 in ResAllumberPrint
                                      join u in res_user.AsEnumerable() on t1.SpCode equals u.Field<string>("SpCode")
                                      where t1.ProfileName == u.Field<string>("ProfileName")
                                      select dt.LoadDataRow(new object[]
                                      {
                                        t1.BatchNumber,
                                        t1.NumberPrint,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,
                                        u.Field<Int64>("count"),
                                        String.Format("{0:n0}", double.TryParse(u.Field<double>("Price").ToString(), out double xx) ? Convert.ToDouble(u.Field<double>("Price")) : 0),
                                        utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(u.Field<Int64>("UptimeUsed"))),
                                        utils.ConvertSize_Get_InArabic(u.Field<long>("Up_Down").ToString() ),

                                         u.Field<double>("Price"),
                                         u.Field<Int64>("UptimeUsed"),
                                         u.Field<Int64>("Up_Down"),
                                         t1.ProfileName,
                                         (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),


                                      }, false);
                    if (batch_Sales.Count().ToString() != "0")
                    {

                    }
                }



                DGV_detail.DataSource = dt;

                try { DGV_detail.Columns["الطبعة"].DisplayIndex = 0; } catch { }

                try { DGV_detail.Columns["الدفعة"].Visible = false; } catch { }
                try { DGV_detail.Columns["Price"].Visible = false; } catch { }
                try { DGV_detail.Columns["UptimeUsed"].Visible = false; } catch { }
                try { DGV_detail.Columns["Up_Down"].Visible = false; } catch { }
                try { DGV_detail.Columns["الباقة"].DisplayIndex = 1; } catch { }
                try { DGV_detail.Columns["نقطة البيع"].Visible = true; } catch { }

                if (Toggle_By_SP.Checked || Toggle_By_Profile.Checked)
                {
                    try { DGV_detail.Columns["التاريخ"].Visible = false; } catch { }
                    try { DGV_detail.Columns["الطبعة"].Visible = false; } catch { }
                }
                if (Toggle_By_SP.Checked && Toggle_By_Profile.Checked)
                {
                    try { DGV_detail.Columns["الباقة"].DisplayIndex = 0; } catch { }
                    try { DGV_detail.Columns["نقطة البيع"].DisplayIndex = 1; } catch { }
                }
                if (Toggle_By_SP.Checked && Toggle_By_Profile.Checked == false)
                {
                    try { DGV_detail.Columns["نقطة البيع"].DisplayIndex = 0; } catch { }
                    try { DGV_detail.Columns["الباقة"].Visible = false; } catch { }

                }
                if (Toggle_By_SP.Checked==false && Toggle_By_Profile.Checked )
                {
                    try { DGV_detail.Columns["نقطة البيع"].Visible =false; } catch { }
                }


                try { rjTextBox1.Text = DGV_detail.Rows.Count.ToString(); } catch { }



            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void Sub_LocadData2(string BatchCardId = "")
        {
            try
            {
                string TableUser = "UmUser";
                string TablePyment = "UmPyment";
                string TableSession = "UmSession";

                if (Server_Type == "HS")
                {
                    TableUser = "HSUser";
                    TablePyment = "HsPyment";
                    TableSession = "HsSession";
                }

                if (dgv.Rows.Count<=0)
                    return;

                string profile = ""; string Qury = ""; string sp = "";
                if (dgv.Rows.Count < 0) 
                    return;
                
                DGV_detail.ContextMenuStrip = null;
                string Query_conditon = condition_detail_firstUse();
                BatchCardId = dgv.CurrentRow.Cells["رقم الدفعة"].Value.ToString();

                string Query_Batch = $"select BatchNumber ,AddedDate,SpCode,ProfileName ,min(Sn_from) Sn_from,max(Sn_to) Sn_to from BatchCard where BatchNumber={BatchCardId} ";
                if (Toggle_GroupBy_Profile.Checked)
                {
                    profile = dgv.CurrentRow.Cells["الباقة"].Value.ToString();
                    Query_Batch += $" and  ProfileName='{profile}' ";
                }
                if (Toggle_GroupBy_SP.Checked)
                {
                    sp = dgv.CurrentRow.Cells["نقطة البيع"].Value.ToString();
                    Query_Batch += $" and  SpCode='{sp}' ";
                }
                BatchCard batch = Smart_DA.GetAnyDB<BatchCard>(Query_Batch);
                
                try { rjTextBox1.Text = batch.BatchNumber.ToString(); } catch { }


                string Q_ByName = $"where u.BatchCardId={BatchCardId} ";
                if (Toggle_GroupBy_SP.Checked || Toggle_GroupBy_Profile.Checked)
                {
                   string SN = $"AND  u.Sn BETWEEN {batch.Sn_from} AND {batch.Sn_to}  ";

                    Q_ByName += SN;
                }
                if (Cbox_View.SelectedIndex == 0)
                {

                    Qury = $"SELECT  * FROM {TableUser} u {Q_ByName}  {Query_conditon} AND u.FirsLogin IS NOT NULL AND u.UptimeUsed>0  ";
                    //Qury = $"SELECT DISTINCT u.* FROM UmUser u INNER JOIN UmSession s ON u.Sn_Name=s.Fk_Sn_Name  {Query_conditon} {Q_ByName} {GroupBy} ";
                    
                    if(Server_Type=="HS")
                    DGV_detail.DataSource = Local_DA.Load<HSUser>(Qury);
                    else
                    DGV_detail.DataSource = Local_DA.Load<UmUser>(Qury);
                    
                    DGV_detail.ContextMenuStrip = dmAll_Cards;
                }
                else if (Cbox_View.SelectedIndex == 1)
                {
                    Qury = $"SELECT  s.* FROM {TableSession} s INNER JOIN {TableUser} u ON s.Fk_Sn_Name = u.Sn_Name {Q_ByName} {Query_conditon} AND u.FirsLogin IS NOT NULL AND u.UptimeUsed>0  ";
                    if (Server_Type == "HS")
                        DGV_detail.DataSource = Local_DA.Load<HsSession>(Qury);
                    else
                        DGV_detail.DataSource = Local_DA.Load<UmSession>(Qury);

                }
                loadDgvState();
                update_select_DGV2();
            }
            catch { }
            //update_select_DGV2();
        }
        private void update_select_DGV2()
        {
            try
            {
                string ListAll = DGV_detail.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = DGV_detail.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        Dgv_Header_Proprties Dgv_State_list = null;
        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if (Cbox_View.SelectedIndex == 1)
            {
                Init_dgv_to_Default();
                return;
            }

            if (Cbox_View.SelectedIndex == 1)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Session");
            else if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_RB_Archive");
            //else
            //    DgvState = SqlDataAccess.Get_SourceSaveStateFormsVariable("DgvUserManagerPrcess");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            //dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    DGV_detail.Columns[dv.Index].Visible = dv.Visable;
                    DGV_detail.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;
                    DGV_detail.Columns[dv.Index].Width = dv.Width;
                    foreach (ToolStripMenuItem control in toolStripMenuItem1.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            if (control.Tag != null)
                                if (control.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { /*RJMessageBox.Show(ex.Message);*/ }
            }

            try { DGV_detail.Columns["Sn_Name"].Visible = false; } catch { }
            try { DGV_detail.Columns["IdHX"].Visible = false; } catch { }
            try { DGV_detail.Columns["Status"].Visible = false; } catch { }
            try { DGV_detail.Columns["Disabled"].Visible = false; } catch { }

            try { DGV_detail.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { DGV_detail.Columns["UptimeUsed"].Visible = false; } catch { }
            try { DGV_detail.Columns["DownloadUsed"].Visible = false; } catch { }
            try { DGV_detail.Columns["UploadUsed"].Visible = false; } catch { }
            try { DGV_detail.Columns["CallerMac"].Visible = false; } catch { }
            try { DGV_detail.Columns["CountProfile"].Visible = false; } catch { }
            try { DGV_detail.Columns["CountSession"].Visible = false; } catch { }
        }
        private void Init_dgv_to_Default()
        {
            if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
            {
                try
                {
                    foreach (DataGridViewColumn column in DGV_detail.Columns)
                    {

                        column.Visible = false;
                    }



                    //dgv.Columns["Sn"].Visible = true;
                    DGV_detail.Columns["Str_Status"].Visible = true;
                    DGV_detail.Columns["Str_Status"].DisplayIndex = 0;
                    Status_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["UserName"].Visible = true;
                    dgv.Columns["UserName"].DisplayIndex = 1;
                    UserName_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["ProfileName"].Visible = true;
                    DGV_detail.Columns["ProfileName"].DisplayIndex = 3;
                    Profile_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["Str_UptimeUsed"].Visible = true;
                    DGV_detail.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                    DGV_detail.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);
                    Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_DownloadUsed"].Visible = true;
                    DGV_detail.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["Str_UploadUsed"].Visible = true;
                    DGV_detail.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["Str_Up_Down"].Visible = true;
                    DGV_detail.Columns["Str_Up_Down"].DisplayIndex = 7;
                    DGV_detail.Columns["Str_Up_Down"].Width = utils.Control_Mesur_DPI(190);
                    Str_Up_Down_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["MoneyTotal"].Visible = true;

                    DGV_detail.Columns["Str_ProfileTimeLeft"].Visible = true;
                    DGV_detail.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                    DGV_detail.Columns["Str_ProfileTimeLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["Str_ProfileTransferLeft"].Visible = true;
                    DGV_detail.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                    DGV_detail.Columns["Str_ProfileTransferLeft"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["Str_ProfileTillTime"].Visible = true;
                    DGV_detail.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                    DGV_detail.Columns["Str_ProfileTillTime"].Width = utils.Control_Mesur_DPI(150);
                    Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                    DGV_detail.Columns["LastSynDb"].Visible = false;
                    DGV_detail.Columns["LastSynDb"].DisplayIndex = 11;
                    DGV_detail.Columns["LastSynDb"].Width = utils.Control_Mesur_DPI(150);
                    //dgv.Columns["SpName"].Visible = true;

                    //dgv.Columns["Comment"].Visible = true;
                    DGV_detail.Columns["Comment"].Width = utils.Control_Mesur_DPI(150);

                    //dgv.Columns["CountProfile"].Visible = true;
                    try { DGV_detail.Columns["Sn_Name"].Visible = false; } catch { }
                    //try { dgv.Columns["Status "].Visible = false; } catch { }
                    //try { dgv.Columns["Disabled "].Visible = false; } catch { }
                    try { DGV_detail.Columns["CountProfile"].Visible = false; } catch { }
                    try { DGV_detail.Columns["CountProfile"].Width = 150; } catch { }
                    try { DGV_detail.Columns["CountSession"].Visible = false; } catch { }
                    try { DGV_detail.Columns["CountSession"].Width = 150; } catch { }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Init_dgv_to_Default"); }
            }

            else
            {
                try
                {
                    //foreach (DataGridViewColumn column in dgv.Columns)
                    //{
                    //    column.Visible = false;
                    //}

                    DGV_detail.Columns["UserName"].Visible = false;
                    DGV_detail.Columns["UserName"].DisplayIndex = 1;

                    DGV_detail.Columns["FromTime"].Visible = true;
                    DGV_detail.Columns["FromTime"].DisplayIndex = 2;
                    DGV_detail.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["TillTime"].Visible = true;
                    DGV_detail.Columns["TillTime"].DisplayIndex = 3;
                    DGV_detail.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["Str_UptimeUsed"].Visible = true;
                    DGV_detail.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                    DGV_detail.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["Str_DownloadUsed"].Visible = true;
                    DGV_detail.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                    DGV_detail.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["Str_UploadUsed"].Visible = true;
                    DGV_detail.Columns["Str_UploadUsed"].DisplayIndex = 6;
                    DGV_detail.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                    DGV_detail.Columns["CallingStationId"].Visible = true;
                    DGV_detail.Columns["CallingStationId"].DisplayIndex = 7;
                    DGV_detail.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                    DGV_detail.Columns["IpUser"].Visible = true;
                    DGV_detail.Columns["IpUser"].DisplayIndex = 8;
                    DGV_detail.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150); 

                    DGV_detail.Columns["NasPortId"].Visible = true;
                    DGV_detail.Columns["NasPortId"].DisplayIndex = 9;

                    DGV_detail.Columns["IpRouter"].Visible = true;
                    DGV_detail.Columns["IpRouter"].DisplayIndex = 10;
                    DGV_detail.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                    DGV_detail.Columns["Sn_Name"].Visible = false;
                    DGV_detail.Columns["Fk_Sn_Name"].Visible = false;
                    DGV_detail.Columns["IdHX"].Visible = false;

                }
                catch { }

            }
        }

        private void Date_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "التاريخ");
        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv.Columns[columnName].Visible = elemnt.Checked;
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
            }
            catch { }
        }
        private void Change_Items_ToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        private void CardCountToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "عدد الكروت");

        }

        private void Sales_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "اجمالي المبيعات");

        }

        private void uptime_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "الوقت المستخدم");

        }

        private void TransferUsed_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "الاستهلاك المستخدم");

        }

        private void UptimeLimit_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "الوقت المسموح");

        }

        private void TransferLimitToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "الاستهلاك المسموح");

        }

        private void SessionCountToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, "اجمالي الجلسات");

        }

        private void Cbox_View_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            Sub_LocadData();
        }

        private void Toggle_GroupBy_BatchID_CheckedChanged(object sender, EventArgs e)
        {
            if (Toggle_GroupBy_BatchID.Checked)
            {
                pnl_fillter_SP_Profile.Visible=false;
            }
            else
                pnl_fillter_SP_Profile.Visible=true;

            get_report();
        }
        DataGridViewCell ActiveCell = null;
        private void dgv2_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = DGV_detail.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = DGV_detail[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            int Server = 0;
            if (Server_Type == "HS")
                Server = 1;
            try
            {
                int id = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["الدفعة"].Value.ToString());
                BatchCard batchCard = Smart_DA.Get_Batch_byBatchNumber_And_Server(id, Server).First();

                if (Server_Type == "UM")
                {
                    FormAllCardsUserManager frm = new FormAllCardsUserManager(batchCard);
                    frm.ShowDialog();
                }
                else if (Server_Type == "HS")
                {
                    FormAllCardsHotspot frm = new FormAllCardsHotspot(batchCard);
                    frm.ShowDialog();
                }
            }
            catch { }

        }

        private void DGV_detail_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
           

        }

        private void Toggle_By_Profile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (dgv.SelectedRows.Count > 0)
                Sub_LocadData(Convert.ToInt32(dgv.SelectedRows[0].Cells["الدفعة"].Value.ToString()));
        }

        private void Toggle_By_SP_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (dgv.SelectedRows.Count > 0)
                Sub_LocadData(Convert.ToInt32(dgv.SelectedRows[0].Cells["الدفعة"].Value.ToString()));
        }

        private void btn_Refresh_Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void check_with_Commi_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            get_report();
        }

        private void rjDateTime_To_OnValueChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            get_report();
        }

        private void DGV_detail_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            int Server = 0;
            if (Server_Type == "HS")
                Server = 1;

            string ProfileName = null;
            string SellingPoint = null;

            if (Toggle_By_Profile.Checked)
                try { ProfileName = (DGV_detail.Rows[e.RowIndex].Cells["الباقة"].Value.ToString()); } catch { }
            if (Toggle_By_SP.Checked)
                try { SellingPoint = (DGV_detail.Rows[e.RowIndex].Cells["نقطة البيع"].Value.ToString()); } catch { }
            try
            {
                int id = Convert.ToInt32(dgv.CurrentRow.Cells["الدفعة"].Value.ToString());
                int id_NumberPrint = Convert.ToInt32(DGV_detail.Rows[e.RowIndex].Cells["الطبعة"].Value.ToString());

                BatchCard batchCard = Smart_DA.Get_Batch_byBatchNumber_And_Server(id, Server).First();
                NumberPrintCard numberPrintCard = Smart_DA.Get_NumberPrintCard_byNumberPrint_And_Server(id_NumberPrint, Server).First();

                if (Toggle_By_SP.Checked || Toggle_By_Profile.Checked)
                    numberPrintCard = null;

                if (Server_Type == "UM")
                {
                    FormAllCardsUserManager frm = new FormAllCardsUserManager(batchCard, numberPrintCard, SellingPoint, ProfileName);
                    frm.ShowDialog();
                }
                else if (Server_Type == "HS")
                {
                    FormAllCardsHotspot frm = new FormAllCardsHotspot(batchCard, numberPrintCard, SellingPoint, ProfileName);
                    frm.ShowDialog();
                }
            }
            catch { }
        }
    }
}
