﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Entities.Hotspot
{
    //CREATE INDEX HSUser_idx_02eb6568 ON HSUser(SN DESC);
    //CREATE INDEX HSUser_idx_49fb733a ON HSUser(DeleteFromServer, SN DESC);
    //CREATE INDEX HSUser_idx_e07cf9bb ON HSUser(DeleteFromServer, BatchCardId);
    //CREATE INDEX HSUser_idx_f28dcfa3 ON HSUser(DeleteFromServer, BatchCardId, Status);

    [CompositeIndex("SN DESC")]
    [CompositeIndex("DeleteFromServer", "SN DESC")]
    [CompositeIndex("DeleteFromServer", "BatchCardId")]
    [CompositeIndex("DeleteFromServer", "BatchCardId", "Status")]

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class HSUser: BaseCard
    {
        public HSUser() { }

        //[PrimaryKey,AutoIncrement,Unique,Required]
        //public int? Id { get; set; }

        [Computed, DisplayName("الحالة")]
        public string Str_Status
        {
            //0=waiting,1=active,2=all_finsh,3=open
            get
            {
                string s = "";
                if (DeleteFromServer == 1)
                    return "محذوف";
                else
                {
                    s = (Status == 0 ? "انتظار" : (Status == 1 ? "نشط" : (Status == 2 ? "منتهي" : (Status == 3 ? "مفتوح" : ""))));
                    if (Disabled == 1)
                        return  ("معطل");
                        //return s + (" + معطل");
                }
                return s;
            }
        }
        [StringLength(100)]
     
        [DefaultValue("'default'"), DisplayName("بروفايل الهوتسبوت")]
        public string ProfileHotspot { get; set; } = "default";
        [Default(0),Browsable(false)]
        public long Limitbytestotal { get; set; } = 0;
        [Default(0), Browsable(false)]
        public long LimitUptime { get; set; } = 0;
        [Computed, DisplayName("الوقت المسموح")]
        //[Computed, DisplayName("الوقت الفعلي المسموح")]
        public string Str_limitUptime
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(LimitUptime);
            }

        }
        [Computed, DisplayName("التنزيل المسموح")]
        //[Computed, DisplayName("التنزيل الفعلي المسموح")]
        public string Str_limitbytestotal
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(Limitbytestotal.ToString());
                else
                    return utils.ConvertSize_Get_En(Limitbytestotal.ToString());
            }
        }

        [Computed, DisplayName("الوقت المتبقي")]
        public new string Str_ProfileTimeLeft
        {
            get
            {
                //return null;
                if (LimitUptime == 0) return "غير معروف";
                double time = Math.Max(0, (LimitUptime - UptimeUsed));
                return utils.Get_Seconds_By_clock_Mode(time);
            }
        }
        [Computed, DisplayName("التحميل المتبقي")]
        public new string Str_ProfileTransferLeft
        {
            get
            {
                //return null;

                if (Limitbytestotal == 0) return "غير معروف";
                double size = Math.Max(0, (Limitbytestotal - (UploadUsed + DownloadUsed)));
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic_with_ZeroByte(size.ToString());
                else return utils.ConvertSize_Get_En_with_ZeroByte(size.ToString());
            }
        }


        [StringLength(100)]
        [DefaultValue("'all'"), DisplayName("السيرفر")]
        public string Server { get; set; } = "all";

        [StringLength(300),DisplayName("ملاحظة")]
        public string Descr { get; set; } //ملاحظة الكرت غير ملاحظه الصلاحيات

        //=============================
        [Default(1)/*,Index*/,DisplayName("صلاحيات سمارت"), Browsable(false)]
        public int SmartValidatiy_Add { get; set; } = 1;

        [Computed/*,Index*/,DisplayName("صلاحيات سمارت")]
        public bool Str_SmartValidatiy_Add
        {
            get
            {
                return Convert.ToBoolean(SmartValidatiy_Add);
            }


        }
        [Default(0), Browsable(false)]
        public int SmartValidatiy_ByDayOrHour { get; set; } = 0;
        [Default(1), Browsable(false)]
        public int SmartValidatiy_timeSave { get; set; } = 1;
        [Default(1), Browsable(false)]
        public int SmartValidatiy_sizeSave { get; set; } = 1;
        [Default(1), Browsable(false)] 
        public int SmartValidatiy_sessionSave { get; set; } = 1;
        [Default(0), Browsable(false)] 
        public int First_mac { get; set; } = 0; // مرتبط باول ماك

    }


    public class HSUser_Finsh_Cards : HSUser
    {
        public new string Str_Status
        {

            get
            {
                string s = "";

                if (Status == 0)
                    s = "انتظار";
                else if (Status == 2)
                {

                    if ((LimitUptime > 0 && UptimeUsed > 0) && ((LimitUptime - UptimeUsed <= 0)))
                        s = "منتهي الوقت";
                    else if ((Limitbytestotal > 0 && DownloadUsed > 0) && ((Limitbytestotal - (DownloadUsed + UploadUsed) <= 0)))
                        s = "منتهي التحميل";
                    else
                    {
                        if (SmartValidatiy_Add == 1)
                        {
                            if (FirsLogin != null && ValidityLimit > 0)
                            {
                                if (FirsLogin.Value.Year > 1990)
                                    if (FirsLogin.Value.AddSeconds(ValidityLimit) < DateTime.Now)
                                        s = "منتهى الصلاحية";
                            }
                        }
                    }
                }
                if (Disabled == 1)
                    return s + (" + معطل");

                return s;
            }
        }

    }

}
