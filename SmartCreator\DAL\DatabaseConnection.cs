﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.DAL
{
    /// <summary>
    /// مدير الاتصال بقواعد البيانات
    /// </summary>
    public static class DatabaseConnection
    {
        /// <summary>
        /// الحصول على اتصال بقاعدة بيانات الإعدادات Smart.db
        /// </summary>
        public static IDbConnection GetSmartConnection()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["SmartConnection"].ConnectionString;
            EnsureDatabaseExists(GetDatabasePath(connectionString));
            return new SQLiteConnection(connectionString);
        }

        /// <summary>
        /// الحصول على اتصال بقاعدة البيانات المحلية LocalDB.db
        /// </summary>
        public static IDbConnection GetLocalDbConnection()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["LocalDbConnection"].ConnectionString;
            EnsureDatabaseExists(GetDatabasePath(connectionString));
            return new SQLiteConnection(connectionString);
        }

        /// <summary>
        /// الحصول على اتصال بقاعدة البيانات الافتراضية
        /// </summary>
        public static IDbConnection GetDefaultConnection()
        {
            var connectionString = ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString;
            EnsureDatabaseExists(GetDatabasePath(connectionString));
            return new SQLiteConnection(connectionString);
        }

        /// <summary>
        /// استخراج مسار قاعدة البيانات من connection string
        /// </summary>
        private static string GetDatabasePath(string connectionString)
        {
            var builder = new SQLiteConnectionStringBuilder(connectionString);
            return builder.DataSource;
        }

        /// <summary>
        /// التأكد من وجود قاعدة البيانات وإنشاؤها إذا لم تكن موجودة
        /// </summary>
        private static void EnsureDatabaseExists(string databasePath)
        {
            try
            {
                if (!File.Exists(databasePath))
                {
                    // إنشاء المجلد إذا لم يكن موجوداً
                    var directory = Path.GetDirectoryName(databasePath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        Directory.CreateDirectory(directory);
                    }

                    // إنشاء قاعدة البيانات
                    SQLiteConnection.CreateFile(databasePath);
                    System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء قاعدة البيانات: {databasePath}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء قاعدة البيانات {databasePath}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// اختبار الاتصال بقاعدة البيانات
        /// </summary>
        public static bool TestConnection(string connectionName)
        {
            try
            {
                IDbConnection connection = null;

                switch (connectionName.ToLower())
                {
                    case "smart":
                        connection = GetSmartConnection();
                        break;
                    case "localdb":
                        connection = GetLocalDbConnection();
                        break;
                    case "default":
                        connection = GetDefaultConnection();
                        break;
                    default:
                        return false;
                }

                using (connection)
                {
                    connection.Open();
                    var command = connection.CreateCommand();
                    command.CommandText = "SELECT 1";
                    var result = command.ExecuteScalar();
                    return result != null;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ فشل اختبار الاتصال {connectionName}: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// الحصول على معلومات قاعدة البيانات
        /// </summary>
        public static string GetDatabaseInfo(string connectionName)
        {
            try
            {
                IDbConnection connection = null;
                string databasePath = "";

                switch (connectionName.ToLower())
                {
                    case "smart":
                        connection = GetSmartConnection();
                        databasePath = GetDatabasePath(ConfigurationManager.ConnectionStrings["SmartConnection"].ConnectionString);
                        break;
                    case "localdb":
                        connection = GetLocalDbConnection();
                        databasePath = GetDatabasePath(ConfigurationManager.ConnectionStrings["LocalDbConnection"].ConnectionString);
                        break;
                    case "default":
                        connection = GetDefaultConnection();
                        databasePath = GetDatabasePath(ConfigurationManager.ConnectionStrings["DefaultConnection"].ConnectionString);
                        break;
                    default:
                        return "قاعدة بيانات غير معروفة";
                }

                using (connection)
                {
                    connection.Open();

                    var fileInfo = new FileInfo(databasePath);
                    var size = fileInfo.Exists ? fileInfo.Length : 0;

                    return $"المسار: {databasePath}\nالحجم: {size:N0} بايت\nحالة الاتصال: متصل";
                }
            }
            catch (Exception ex)
            {
                return $"خطأ في الحصول على معلومات قاعدة البيانات: {ex.Message}";
            }
        }
    }
}
