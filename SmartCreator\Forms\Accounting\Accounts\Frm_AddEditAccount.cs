﻿using SmartCreator.Entities.Accounting;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;
using static System.Runtime.CompilerServices.RuntimeHelpers;

namespace SmartCreator.Forms.Accounting.Accounts
{
    public partial class Frm_AddEditAccount : RJChildForm
    {
        private readonly AccountService _accountService;
        private readonly Account? _parentAccount;
        private readonly Account? _account;
        private readonly bool _isEditMode;

        public Frm_AddEditAccount(AccountService accountService, Account? parentAccount = null, Account? account = null)
        {
            InitializeComponent();
            _accountService = accountService;
            _parentAccount = parentAccount;
            _account = account;
            _isEditMode = account != null;
            lblAccountNature.ForeColor=utils.Dgv_DarkColor;
            Set_Font();
            //this.RightToLeft = RightToLeft.Yes;
            //this.RightToLeftLayout = true;
        }
        private void Set_Font()
        {


            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;

            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel1.Font = rjLabel2.Font = rjLabel3.Font = rjLabel4.Font =  rjLabel7.Font =
            txtName.Font =
           lbl_font;
            this.Focus();

            utils utils = new utils();
            utils.Control_textSize1(this);
        }


        private void Frm_AddEditAccount_LoadAsync(object sender, EventArgs e)
        {
            SetupForm();
            using var _ = LoadAccountTypesAsync();

            if (_isEditMode && _account != null)
            {
                LoadAccountData();
            }
            else if (_parentAccount != null)
            {
                LoadParentAccountData();
            }
        }
        private void SetupForm()
        {
            this.Text = _isEditMode ? "تعديل حساب" : "إضافة حساب جديد";
            btnSave.Text = _isEditMode ? "تحديث" : "حفظ";

            if (_parentAccount != null)
            {
                lblParentAccount.Text = $"الحساب الأب: {_parentAccount.Code} - {_parentAccount.Name}";
                lblParentAccount.Visible = true;
            }
            else
            {
                lblParentAccount.Visible = false;
            }
        }

        private async Task LoadAccountTypesAsync()
        {
            cmbAccountType.Items.Clear();

            var values = Enum.GetValues(typeof(AccountType));

            foreach (AccountType type in values)
            //foreach (AccountType type in Enum.GetValues<AccountType>())
            {
                var item = new ComboBoxItem
                {
                    Text = AccountTypeInfo.GetArabicName(type),
                    Value = type
                };
                cmbAccountType.Items.Add(item);
            }

            cmbAccountType.DisplayMember = "Text";
            cmbAccountType.ValueMember = "Value";

            if (cmbAccountType.Items.Count > 0)
            {
                cmbAccountType.SelectedIndex = 0;
            }
        }

        private void LoadAccountData()
        {
            if (_account == null) return;

            txtCode.Text = _account.Code;
            txtName.Text = _account.Name;
            txtNameEnglish.Text = _account.NameEnglish;
            txtDescription.Text = _account.Description;
            chkIsActive.Checked = _account.IsActive;

            // تحديد نوع الحساب
            for (int i = 0; i < cmbAccountType.Items.Count; i++)
            {
                if (cmbAccountType.Items[i] is ComboBoxItem item &&
                    item.Value.Equals(_account.Type))
                {
                    cmbAccountType.SelectedIndex = i;
                    break;
                }
            }

            // منع تعديل الكود في وضع التعديل
            //txtCode.ReadOnly = true;
            txtCode.Enabled = false;
        }

        private void LoadParentAccountData()
        {
            if (_parentAccount == null) return;

            // تحديد نوع الحساب نفس الحساب الأب
            for (int i = 0; i < cmbAccountType.Items.Count; i++)
            {
                if (cmbAccountType.Items[i] is ComboBoxItem item &&
                    item.Value.Equals(_parentAccount.Type))
                {
                    cmbAccountType.SelectedIndex = i;
                    break;
                }
            }

            // منع تغيير نوع الحساب للحسابات الفرعية
            cmbAccountType.Enabled = false;

            // اقتراح كود للحساب الفرعي
            SuggestChildAccountCode();
        }

        /// <summary>
        /// اقتراح كود للحساب الفرعي
        /// </summary>
        private async void SuggestChildAccountCode()
        {
            if (_parentAccount == null) return;

            try
            {
                // الحصول على الحسابات الفرعية للحساب الأب
                var childAccounts = await _accountService.GetChildAccountsAsync(_parentAccount.Id);

                // إنشاء كود مقترح
                string suggestedCode = GenerateNextChildCode(_parentAccount.Code, childAccounts);
                txtCode.Text = suggestedCode;
            }
            catch (Exception ex)
            {
                // في حالة الخطأ، استخدم كود بسيط
                txtCode.Text = _parentAccount.Code + "1";
            }
        }

        /// <summary>
        /// إنشاء الكود التالي للحساب الفرعي
        /// </summary>
        private string GenerateNextChildCode(string parentCode, List<Account> childAccounts)
        {
            if (!childAccounts.Any())
            {
                return parentCode + "1";
            }

            // البحث عن أعلى رقم مستخدم
            int maxNumber = 0;
            foreach (var child in childAccounts)
            {
                if (child.Code.StartsWith(parentCode) && child.Code.Length == parentCode.Length + 1)
                {
                    string lastDigit = child.Code.Substring(parentCode.Length);
                    if (int.TryParse(lastDigit, out int number))
                    {
                        maxNumber = Math.Max(maxNumber, number);
                    }
                }
            }

            return parentCode + (maxNumber + 1).ToString();
        }

          


        private  async void btnSave_ClickAsync(object sender, EventArgs e)
        {
            if ( !await ValidateInputAsync())
                return;

            try
            {
                btnSave.Enabled = false;
                btnSave.Text = "جاري الحفظ...";

                var account = new Account
                {
                    Code = txtCode.Text.Trim(),
                    Name = txtName.Text.Trim(),
                    NameEnglish = txtNameEnglish.Text.Trim(),
                    Type = (AccountType)((ComboBoxItem)cmbAccountType.SelectedItem).Value,
                    Nature = AccountTypeInfo.GetNature((AccountType)((ComboBoxItem)cmbAccountType.SelectedItem).Value),
                    ParentId = _parentAccount?.Id,
                    Description = txtDescription.Text.Trim(),
                    IsActive = chkIsActive.Checked,
                    IsParent = false
                };

                if (_isEditMode && _account != null)
                {
                    account.Id = _account.Id;
                    account.Level = _account.Level; // الحفاظ على المستوى الحالي
                    _accountService.UpdateAccountAsync(account);
                    MessageBox.Show("تم تحديث الحساب بنجاح", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    var newAccountId = _accountService.AddAccountAsync(account);
                    MessageBox.Show($"تم إضافة الحساب بنجاح\nرقم الحساب: {newAccountId}", "نجح", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ الحساب: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                btnSave.Enabled = true;
                btnSave.Text = _isEditMode ? "تحديث" : "حفظ";
            }

        }

        private async Task<bool> ValidateInputAsync()
        {

            // التحقق من الحقول المطلوبة
            if (string.IsNullOrWhiteSpace(txtCode.Text))
            {
                MessageBox.Show("يرجى إدخال كود الحساب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("يرجى إدخال اسم الحساب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtName.Focus();
                return false;
            }

            if (cmbAccountType.SelectedItem == null)
            {
                MessageBox.Show("يرجى اختيار نوع الحساب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                cmbAccountType.Focus();
                return false;
            }

            // التحقق من صحة الكود
            if (!IsValidAccountCode(txtCode.Text.Trim()))
            {
                MessageBox.Show("كود الحساب يجب أن يحتوي على أرقام فقط", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtCode.Focus();
                return false;
            }

            // التحقق من عدم تكرار الكود (في حالة الإضافة أو تغيير الكود)
            if (!_isEditMode || (_isEditMode && _account != null && _account.Code != txtCode.Text.Trim()))
            {
                var existingAccount = await _accountService.GetAccountByCodeAsync(txtCode.Text.Trim());
                if (existingAccount != null)
                {
                    MessageBox.Show("كود الحساب موجود مسبقاً، يرجى اختيار كود آخر", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return false;
                }
            }

            // التحقق من صحة الكود الفرعي
            if (_parentAccount != null)
            {
                if (!txtCode.Text.Trim().StartsWith(_parentAccount.Code))
                {
                    MessageBox.Show($"كود الحساب الفرعي يجب أن يبدأ بكود الحساب الأب: {_parentAccount.Code}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return false;
                }

                if (txtCode.Text.Trim().Length <= _parentAccount.Code.Length)
                {
                    MessageBox.Show("كود الحساب الفرعي يجب أن يكون أطول من كود الحساب الأب", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCode.Focus();
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// التحقق من صحة كود الحساب
        /// </summary>
        private bool IsValidAccountCode(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            // التحقق من أن الكود يحتوي على أرقام فقط
            return code.All(char.IsDigit);
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }

        private void cmbAccountType_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cmbAccountType.SelectedItem is ComboBoxItem item)
            {
                var accountType = (AccountType)item.Value;
                var nature = AccountTypeInfo.GetNature(accountType);

                lblAccountNature.Text = $"طبيعة الحساب: {(nature == AccountNature.Debit ? "مدين" : "دائن")}";
                lblAccountNature.ForeColor = nature == AccountNature.Debit ? Color.Blue : Color.Red;
            }
        }











        public class ComboBoxItem
        {
            public string Text { get; set; } = string.Empty;
            public object Value { get; set; } = new object();

            public override string ToString()
            {
                return Text;
            }
        }

    }
     
}
