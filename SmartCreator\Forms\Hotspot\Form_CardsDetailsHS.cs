﻿using FontAwesome.Sharp;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.RJForms.Private;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ListView;

namespace SmartCreator.Forms.Hotspot
{
    public partial class Form_CardsDetailsHS : RJChildForm
    {
        Sql_DataAccess Local_DB;
        HSUser Users;
        bool Is_firlLoad = true;
        bool Toggle_lastStatus = true;
        string Str_lastStaus_ON = "";
        string Str_lastStaus_OFF = "";
        string LastPassword = "";
        bool lastBind = false;
        string Lastmac = "";
        string fillter_type = "From_Server";
        string OrderBy_Key = "Id";
        List<HsPyment> Hs_profiles = new List<HsPyment>();

        public Form_CardsDetailsHS(HSUser user, string _fillter_type = "From_Server")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv_profiles.RightToLeft = RightToLeft.No;
                dgv_Sessions.RightToLeft = RightToLeft.No;
            }
            Local_DB = new Sql_DataAccess();

            fillter_type = _fillter_type;
            Users = user;
            

            this.Text = "تفاصيل كرت الهوتسبوت";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Hotspot Card Detail";
            }


            Set_Font();

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv_profiles);
            utils.dgv_textSize(dgv_Sessions);
            utils.item_Contrlol_textSize(dm_profile);
            utils.item_Contrlol_textSize(dgv_Sessions);
            utils.tollstrip_textSize(ترتيبالاعمدةToolStripMenuItem);


        }
        private void Set_Font()
        {
            if(UIAppearance.Theme == UITheme.Dark)
            {
                groupBox1.BackColor = UIAppearance.ItemBackgroundColor;
                //groupBox1.BackColor=UIAppearance.StyleColor;
              
            }
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            foreach (var contrl in rjPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in rjPanel3.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in groupBox1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJCheckBox))
                    {
                        RJCheckBox textbox = (RJCheckBox)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }


            Radio_Baisc.Font = Radio_WithSession.Font = fnt;
            btn_addSmartValidity.Font = fnt;
            //rjLabel8.Font = fnt;

            //Font fntt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            Toggle_Bind_Mac.Font = fnt;
            Toggle_Status.Font = fnt;

            btnSave.Font = btn_Refresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9 , FontStyle.Bold);

            dgv_profiles.AllowUserToOrderColumns = true;
            dgv_profiles.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);
            dgv_profiles.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_profiles.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_profiles.ColumnHeadersHeight = utils.Control_Mesur_DPI(40);

            dgv_Sessions.AllowUserToOrderColumns = true;
            dgv_Sessions.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
            dgv_Sessions.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_Sessions.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_Sessions.ColumnHeadersHeight = utils.Control_Mesur_DPI(40);

            //dgv_profiles.DefaultCellStyle.Font = new System.Drawing.Font(dgv_profiles.DefaultCellStyle.Font.FontFamily, dgv_profiles.DefaultCellStyle.Font.Size , dgv_profiles.DefaultCellStyle.Font.Style);
            //dgv_Sessions.DefaultCellStyle.Font = new System.Drawing.Font(dgv_Sessions.DefaultCellStyle.Font.FontFamily, dgv_Sessions.DefaultCellStyle.Font.Size , dgv_Sessions.DefaultCellStyle.Font.Style);

            //utils.Control_textSize(pnlClientArea);
            //Control_Loop(pnlClientArea);
        }

        private void loadData()
        {
            string sql_where = " and where HSUser.DeleteFromServer = 0 ";
            string sql_where_session = " and  HsSession.DeleteFromServer = 0 ";
            string sql_where_Pyment = " and  HsPyment.DeleteFromServer = 0 ";



            if (fillter_type == "From_RB_Archive")
            {
                sql_where = "  ";
                sql_where_session = " ";
                sql_where_Pyment = "  ";
            }
            if (Users.DeleteFromServer == 1)
            {
                btnAddProfile.Enabled = false;
                btn_Refresh.Enabled = false;
            }

            txt_username.Text = Users.UserName;
            txt_Password.Text = Users.Password;

            txt_RegDate.Text = Users.RegDate.ToString();
            txt_Till_Date.Text = Users.Str_ProfileTillTime.ToString();

            txt_TransferLimit.Text = Users.Str_TransferLimit.ToString();
            txt_TransferLeft.Text = Users.Str_ProfileTransferLeft.ToString();

            txt_TimeLeft.Text = Users.Str_ProfileTimeLeft.ToString();
            txt_uptimeLimit.Text = Users.Str_UptimeLimit.ToString();

            txt_TotalDownload.Text = Users.Str_Up_Down.ToString();
            txt_TotalUptime.Text = Users.Str_UptimeUsed.ToString();

            txt_mac.Text = Users.CallerMac;
            if (Users.CallerMac == "00:00:00:00:00:00")
                txt_mac.Text = "";

            txt_Real_TransferLimit.Text = Users.Str_limitbytestotal.ToString();
            txt_Real_uptimeLimit.Text = Users.Str_limitUptime.ToString();
            txt_validay.Text = (Users.ValidityLimit / 24 / 60 / 60).ToString();

            if ( (txt_Real_TransferLimit.Text=="0")) txt_Real_TransferLimit.Text = "مفتوح"; 
            if ( (txt_Real_uptimeLimit.Text== "00:00:00")) txt_Real_uptimeLimit.Text = "مفتوح"; 
            if ( (txt_validay.Text=="0")) txt_validay.Text = "مفتوح"; 
            if ( (txt_TransferLimit.Text=="0")) txt_TransferLimit.Text = "مفتوح"; 
            if ( (txt_uptimeLimit.Text=="00:00:00")) txt_uptimeLimit.Text = "مفتوح"; 

            CheckBox_SmartScript.Check= Convert.ToBoolean(Users.SmartValidatiy_Add);
            CheckBox_byDayOrHour.Check = Convert.ToBoolean(Users.SmartValidatiy_ByDayOrHour);
            CheckBox_Save_download.Check = Convert.ToBoolean(Users.SmartValidatiy_sizeSave);
            CheckBox_Save_session.Check = Convert.ToBoolean(Users.SmartValidatiy_sessionSave);
            CheckBox_Save_time.Check = Convert.ToBoolean(Users.SmartValidatiy_timeSave);

            bool SmartFirstUseMac=false;
            

            Toggle_Status.Checked = !Convert.ToBoolean(Users.Disabled);
            Toggle_Status.ON_Text = " مفعل  + " + Users.Str_Status;

            if (Users.Disabled == 1)
                Toggle_Status.OFF_Text = "     " + Users.Str_Status;
            else Toggle_Status.OFF_Text = "  معطل + " + Users.Str_Status;



            if (Users.CallerMac == "bind")
            {
                Toggle_Bind_Mac.Checked = true;
                lastBind = true;
                txt_mac.Text = "";
            }
            //else
            try
            {
                List<HsPyment> profiles = Local_DB.Load<HsPyment>($"select * from HsPyment  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_Pyment} order by Id DESC");
                var session = Local_DB.Load<HsSession>($"select * from HsSession  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_session} order by {OrderBy_Key} DESC");
                Hs_profiles = profiles;

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                 (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     dgv_profiles.DataSource = profiles;
                     dgv_Sessions.DataSource = session;
                 });
            }
            catch { }
            //var session = db.Select<UmSession>(x => x.UmUserId == Users.Id);
            //}
            txt_CountProfile.Text = dgv_profiles.Rows.Count.ToString();
            txt_CountSession.Text = dgv_Sessions.Rows.Count.ToString();
            double sumPrice = 0;
            if (dgv_profiles.Rows.Count > 0)
            {
                foreach (DataGridViewRow row in dgv_profiles.Rows)
                {
                    sumPrice += Convert.ToDouble(row.Cells["Price"].Value);
                }
            }
            txt_TotalPrice.Text = sumPrice.ToString();
            dgv_Sessions.AllowUserToOrderColumns = true;
            //dgv_profiles.AllowUserToOrderColumns = true;
            //System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 * utils.ScaleFactor, FontStyle.Regular);
            
            //dgv_Sessions.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv_Sessions.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_Sessions.ColumnHeadersHeight = 35;


            //dgv_profiles.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv_profiles.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_profiles.ColumnHeadersHeight = 35;

            Init_dgv_Sessions_to_Default();
            //dgv_profiles.BorderStyle = BorderStyle.Fixed3D;



            LastPassword = Users.Password;
            //if(string.IsNullOrEmpty(Users.CallerMac))
            //    Lastmac = string.Empty;
            //else
            Lastmac = Users.CallerMac;

            //Toggle_lastStatus = !Convert.ToBoolean(Users.Disabled);

            if (Toggle_Status.Checked)
            {
                Toggle_lastStatus = true;
                Str_lastStaus_ON = Toggle_Status.ON_Text;
            }
            else
            {
                Toggle_lastStatus = false;
                Str_lastStaus_OFF = Toggle_Status.OFF_Text;
            }

            toolTip1.SetToolTip(btnSave, null);
            if (Users.DeleteFromServer == 1)
            {
                Toggle_Status.Checked = true;
                Toggle_Status.ON_Text = "محذوف من الروتر";
                btnSave.Design = ButtonDesign.Confirm;
                btnSave.TextAlign = ContentAlignment.MiddleLeft;
                btnSave.ImageAlign = ContentAlignment.MiddleRight;
                btnSave.TextImageRelation = TextImageRelation.ImageBeforeText;
                btnSave.RightToLeft = RightToLeft.Yes;
                btnSave.Text = "اضافة الي الروتر";
                toolTip1.SetToolTip(btnSave, "تصفير الكرت واعادة اضافتة الي النظام مرة اخرى");
                //btnSave.IconSize = 1;
                btnSave.IconChar = IconChar.Plus;
                Toggle_Status.Enabled = false;

                Toggle_Status.Font = btnSave.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                utils.item_Contrlol_textSize(Toggle_Status);
                utils.item_Contrlol_textSize(btnSave);


            }

            if (dgv_profiles.Rows.Count == 1)
            {
                int DeleteFromServer = Convert.ToInt32(dgv_profiles.Rows[0].Cells["DeleteFromServer"].Value);
                if (DeleteFromServer == 1)
                {

                    dgv_profiles.Rows[0].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                    dgv_profiles.Rows[0].DefaultCellStyle.SelectionForeColor = utils.Dgv_DarkColor; 
                    
                    //dgv_profiles.Rows[0].DefaultCellStyle.ForeColor = Color.DarkRed;
                    //dgv_profiles.Rows[0].DefaultCellStyle.SelectionForeColor = Color.DarkRed;
                }
            }
            if (dgv_Sessions.Rows.Count == 1)
            {
                int DeleteFromServer = Convert.ToInt32(dgv_Sessions.Rows[0].Cells["DeleteFromServer"].Value);
                if (DeleteFromServer == 1)
                {
                    dgv_Sessions.Rows[0].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                    dgv_Sessions.Rows[0].DefaultCellStyle.SelectionForeColor = utils.Dgv_DarkColor;
                }
            }


        }

        
        private void Form_CardsDetailsHS_Load(object sender, EventArgs e)
        {
            loadData();

            foreach (DataGridViewColumn column in dgv_profiles.Columns)
            {
                column.Visible = false;
            }

            try { dgv_profiles.Columns["Price"].Visible = true; } catch { }
            try { dgv_profiles.Columns["ProfileName"].Visible = true; } catch { }
            try { dgv_profiles.Columns["AddedDate"].Visible = true; } catch { }
            try { dgv_profiles.Columns["Str_MainProfile"].Visible = true; } catch { }

            try { dgv_profiles.Columns["ProfileName"].DisplayIndex = 0; } catch { }
            try { dgv_profiles.Columns["Price"].DisplayIndex = 1; } catch { }
            try { dgv_profiles.Columns["AddedDate"].DisplayIndex = 2; } catch { }
            try { dgv_profiles.Columns["Str_MainProfile"].DisplayIndex = 3; } catch { }

            try { dgv_Sessions.Columns["Id"].Visible = false; } catch { }
            //try { dgv_Sessions.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["Fk_Sn_Name"].Visible = false; } catch { }
            //try { dgv_Sessions.Columns["IdHX"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["contents"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["TillTime_inSecond"].Visible = false; } catch { }

 
        Is_firlLoad = false;

            if(UIAppearance.Theme==UITheme.Light)
                lbl_note.ForeColor = utils.Dgv_DarkColor;

            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 * utils.ScaleFactor , FontStyle.Regular);
            //dm_profile.Font = dgvHeader_font;
            //dm_Session.Font = dgvHeader_font;


        }

        private void Init_dgv_Sessions_to_Default()
        {
            try
            {
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{
                //    column.Visible = false;
                //}
                if (dgv_Sessions.DataSource == null)
                    return;

                dgv_Sessions.Columns["UserName"].Visible = false;
                dgv_Sessions.Columns["UserName"].DisplayIndex = 1;

                dgv_Sessions.Columns["FromTime"].Visible = true;
                dgv_Sessions.Columns["FromTime"].DisplayIndex = 2;
                dgv_Sessions.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["TillTime"].Visible = true;
                dgv_Sessions.Columns["TillTime"].DisplayIndex = 3;
                dgv_Sessions.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_UptimeUsed"].Visible = true;
                dgv_Sessions.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                dgv_Sessions.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_DownloadUsed"].Visible = true;
                dgv_Sessions.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                dgv_Sessions.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_UploadUsed"].Visible = true;
                dgv_Sessions.Columns["Str_UploadUsed"].DisplayIndex = 6;
                dgv_Sessions.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["CallingStationId"].Visible = true;
                dgv_Sessions.Columns["CallingStationId"].DisplayIndex = 7;
                dgv_Sessions.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                dgv_Sessions.Columns["IpUser"].Visible = true;
                dgv_Sessions.Columns["IpUser"].DisplayIndex = 8;
                dgv_Sessions.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["NasPortId"].Visible = true;
                dgv_Sessions.Columns["NasPortId"].DisplayIndex = 9;

                dgv_Sessions.Columns["IpRouter"].Visible = false;
                dgv_Sessions.Columns["IpRouter"].DisplayIndex = 10;
                dgv_Sessions.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                dgv_Sessions.Columns["Id"].Visible = false;
                try { dgv_Sessions.Columns["contents"].Visible = false; } catch { }
                try { dgv_Sessions.Columns["TillTime_inSecond"].Visible = false; } catch { }
                //dgv_Sessions.Columns["Sn_Name"].Visible = false;
                //dgv_Sessions.Columns["IdHX"].Visible = false;

            }
            catch { }


        }

        public bool Is_success = false;
        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            HSUser Huser = new HSUser
            {
                
               
                UploadUsed = Users.UploadUsed,
                DownloadUsed = Users.DownloadUsed,
                ValidityLimit = Users.ValidityLimit,
                Price = Users.Price,
                FirsLogin = Users.FirsLogin,
                Percentage = Users.Percentage,
                RegDate = Users.RegDate,
                SmartValidatiy_Add = Users.SmartValidatiy_Add,
                TransferLimit = Users.TransferLimit,
                UptimeLimit = Users.UptimeLimit,
                UptimeUsed = Users.UptimeUsed,
                TotalPrice = Users.TotalPrice,
                Sn_Name = Users.Sn_Name,
                CountSession = Users.CountSession,
                CountProfile = Users.CountProfile,
              

                UserName = Users.UserName,
                Password = txt_Password.Text.Trim(),
                LimitUptime = Users.LimitUptime,
                Limitbytestotal = Users.Limitbytestotal,
                Email = Users.Email,
                Server = Users.Server,
                ProfileHotspot = Users.ProfileHotspot,
                ProfileName = Users.ProfileName,
                CallerMac = Users.CallerMac,
                Disabled = Convert.ToInt32(!Toggle_Status.Checked),
                Comment = Users.Comment,
                IdHX = Users.IdHX,

            };

            int lastStatue_disable = Users.Disabled;
            HSUser last_User = Users;
            //Users.Password = txt_Password.Text.Trim();
            //Users.Disabled = Convert.ToInt32(!Toggle_Status.Checked);

            if (string.IsNullOrEmpty(txt_mac.Text))
                Huser.CallerMac = "00:00:00:00:00:00";
            else
            {
                string regex = "^([0-9A-Fa-f]{2}[:-])"
                   + "{5}([0-9A-Fa-f]{2})|"
                   + "([0-9a-fA-F]{4}\\."
                   + "[0-9a-fA-F]{4}\\."
                   + "[0-9a-fA-F]{4})$";
                Regex p = new Regex(regex);
                Match m = p.Match(txt_mac.Text);
                if (!m.Success)
                {
                    RJMessageBox.Show("صيغة عنوان الماك خاطئة");
                    return;
                }
                Huser.CallerMac = txt_mac.Text.Trim();
            }

            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            if (Users.DeleteFromServer == 1)
            {

                //HashSet<HSUser> UserTmp = mk_DataAccess.add_one_user_manager(Users);
                string id_user = mk_DataAccess.Add_one_user_hotspot(Huser);

                if (!string.IsNullOrEmpty(id_user))
                {
                    Users.IdHX = id_user;
                    //Users.SN = UserTmp.First().SN;
                    Users.ProfileName = Huser.ProfileName;
                    Users.DeleteFromServer = 0;
                    Users.UploadUsed = 0;
                    Users.DownloadUsed = 0;
                    Users.UptimeUsed = 0;
                    btn_Refresh_Click(sender, e);
                    RJMessageBox.Show("تم اضافة  الكرت الي الروتر");
                    Is_success = true;
                    this.Close();
                    //btn_Refresh_Click(sender, new EventArgs());
                }
                else
                {
                    RJMessageBox.Show("حدث مشكله اضغط علي زر تحديث من الروتر واعد المحاولة");
                }
            }
            else
            {
                

                bool status = mk_DataAccess.Edit_one_user_Hotspot(Huser);
                if (status)
                {
                    string Mac_Query = "";
                    Mac_Query = ",[CallerMac]=@CallerMac ";
                    int Update_Didable = Local_DB.Execute($"update HSUser set [Disabled]=@Disabled,[Password]=@Password,[CallerMac]=@CallerMac {Mac_Query} where Sn_Name=@Sn_Name", Users);

                    if (Update_Didable > 0)
                    {
                        //UserName = Users.UserName,
                        Users.Password = txt_Password.Text.Trim();
                        Users.LimitUptime = Huser.LimitUptime;
                        Users.Limitbytestotal = Huser.Limitbytestotal;
                        Users.Email = Huser.Email;
                        Users.Server = Huser.Server;
                        Users.ProfileHotspot = Huser.ProfileHotspot;
                        Users.ProfileName = Huser.ProfileName;
                        Users.CallerMac = Huser.CallerMac;
                        Users.Disabled = Huser.Disabled;
                        Users.Comment = Huser.Comment;
                        Users.IdHX = Huser.IdHX;
                    }
                    
                    RJMessageBox.Show("تم تعديل بيانات الكرت");
                    Is_success = true;
                    this.Close();
                }
                else
                {
                    Users = last_User;
                    RJMessageBox.Show("حدث مشكله اثناء تحديث البيانات");

                }
            }
        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {

            using (Form_WaitForm frm = new Form_WaitForm(Get_LastUpdate_User))
            {
                frm.ShowDialog();
            }
            lock (Sql_DataAccess.Lock_localDB)
            {
                try
                {
                    //Sql_DataAccess Local_DB=new Sql_DataAccess();
                    Users = Local_DB.Load<HSUser>($"select * from HSUser where Sn_Name='{Users.Sn_Name}'").First();
                    loadData();
                }
                catch { }
            }

            //loadData();
        }

        [Obsolete]
        private void Get_LastUpdate_User()
        {
            if (Users.DeleteFromServer == 1)
                return;
        
            SourceCardsHotspot_fromMK.Get_HS_user(Users.IdHX, true, true);

            List<HSUser> hSUsers = new List<HSUser>();
            hSUsers.Add(Users);
            HsPyment hsPyment = new HsPyment();
            hsPyment.Syn_HS_Pyments_to_FirstUsers(hSUsers);

            SourceCardsHotspot_fromMK.Get_HS_Session(Users.IdHX, false, true,Users.UserName);

            //if (Radio_WithSession.Checked)
            //    SourceSessionUserManager_fromMK.Get_UM_Sessions(Users.UserName, true);

            //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
            //     (System.Windows.Forms.MethodInvoker)delegate ()
            //     {
            //         loadData();
            //     });
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب بيانات الكرت من النظام");

        }

        public bool Sccess_WthiRefersh = false;
        private void btnAddProfile_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            HashSet<HSUser> users = new HashSet<HSUser>();
            users.Add( Users);
            Form_Add_Profile_Balance_HS frm = new Form_Add_Profile_Balance_HS(users);
            frm.ShowDialog();

            if (frm.success == true)
            {
                if(frm.Sccess_WthiRefersh)
                {
                    Sccess_WthiRefersh = true;
                    this.Close();
                    return;
                }
                lock (Sql_DataAccess.Lock_localDB)
                {
                    try
                    {
                        //Sql_DataAccess Local_DB=new Sql_DataAccess();
                        Users = Local_DB.Load<HSUser>($"select * from HSUser where Sn_Name='{Users.Sn_Name}'").First();
                        loadData();
                    }
                    catch { }
                }

                //if (Global_Variable.Mk_resources.version >= 7)
                //{
                //    loadData();
                //}

                //else
                //{


                //    using (Form_WaitForm form = new Form_WaitForm(Refresh_Profile_User))
                //    {
                //        form.ShowDialog();
                //    }
                //    loadData();
                //    return;

                //    Users.ProfileName = frm.CBox_Profile.Text;
                //    float price = Global_Variable.UM_Profile.Find(x => x.Name == Users.ProfileName).Price;
                //    Random aa = new Random();
                //    int a = aa.Next(1000000, 2000000);

                //    UmPyment pyment = new UmPyment();
                //    pyment.ProfileName = frm.CBox_Profile.Text;
                //    pyment.AddedDate = DateTime.Now;
                //    pyment.Price = price;
                //    pyment.MkId = Global_Variable.Mk_resources.RB_code;
                //    pyment.IdHX = "*" + a;
                //    pyment.Sn = a;
                //    pyment.Sn_Name = a + "-";
                //    pyment.UserName = Users.UserName;
                //    pyment.Fk_Sn_Name = Users.Sn_Name;
                //    if (frm.Toggle_Clear_Profile.Checked == true)
                //    {
                //        Um_profiles.Clear();
                //        loadData();
                //    }
                //    Um_profiles.Add(pyment);

                //    dgv_profiles.DataSource = null;

                //    dgv_profiles.DataSource = Um_profiles;

                //    foreach (DataGridViewColumn column in dgv_profiles.Columns)
                //    {
                //        column.Visible = false;
                //    }

                //    try { dgv_profiles.Columns["Price"].Visible = true; } catch { }
                //    try { dgv_profiles.Columns["ProfileName"].Visible = true; } catch { }
                //    try { dgv_profiles.Columns["AddedDate"].Visible = true; } catch { }

                //    //try { dgv_Sessions.Columns["Id"].Visible = false; } catch { }
                //    try { dgv_Sessions.Columns["IdHX"].Visible = false; } catch { }
                //    try { dgv_Sessions.Columns["DeleteFromServer"].Visible = false; } catch { }

                //    txt_CountProfile.Text = dgv_profiles.Rows.Count.ToString();

                //    //dgv_profiles.Rows.Add(pyment);
                //}
            }

        }

        private void Toggle_Status_CheckedChanged(object sender, EventArgs e)
        {
            if (Is_firlLoad)
                return;

            if (Toggle_Status.Checked)
            {
                if (Toggle_lastStatus != Toggle_Status.Checked)
                    Toggle_Status.ON_Text = "    تفعيل ";
            }
            else
            {
                if (Toggle_lastStatus != Toggle_Status.Checked)
                    Toggle_Status.OFF_Text = "    تعطيل    ";
                else
                    Toggle_Status.OFF_Text = Str_lastStaus_OFF.Trim();
            }
        }

        private void btn_addSmartValidity_Click(object sender, EventArgs e)
        {

        }

        private void اضافةباقةجديدةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            btnAddProfile_Click(sender, e);
        }

        private void تحديثباقاتالكرتمنالروترToolStripMenuItem_Click(object sender, EventArgs e)
        {
            
        }

        private void حسبتاريخبدايةالجلسةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            OrderBy_Key = elm.Tag.ToString();
            loadData();
        }

        private void تحديثالجلساتمنالروترToolStripMenuItem_Click(object sender, EventArgs e)
        {
             
        }
        DataGridViewCell ActiveCell = null;
        private void نسخToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv_Sessions.Rows.Count <= 0)
                return;

            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv_Sessions.Rows.Count <= 0)
                return;
            if (this.dgv_Sessions.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv_Sessions.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        private void حذفجميعجلساتالكرتToolStripMenuItem_Click(object sender, EventArgs e)
        {

        }
    }
}
