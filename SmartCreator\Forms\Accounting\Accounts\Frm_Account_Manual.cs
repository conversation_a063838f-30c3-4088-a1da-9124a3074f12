﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities.Accounting;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Service;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Controls;
using System.Windows.Forms;
using System.Xml;
//using Account = SmartCreator.Entities.Accounting.Account;


namespace SmartCreator.Forms.Accounting.Accounts
{
    public partial class Frm_Account_Manual : RJChildForm
    {
        #region Fields

        Smart_DataAccess smart_DataAccess = null;
        private readonly AccountService _accountService;
        private List<SmartCreator.Entities.Accounting.Account> _accounts = new List<Account>();
        private Account? _selectedAccount = null;
        private bool _isLoading = false;

        #endregion
        public Frm_Account_Manual()
        {
            InitializeComponent();
            DatabaseHelper _dbHelper = new DatabaseHelper();
            _accountService = new AccountService(_dbHelper);
            InitializeComponent();
            SetupTreeView();
            //SetupContextMenu();


            smart_DataAccess = new Smart_DataAccess();
            this.Text = "الدليل المحاسبي";

            //if (UIAppearance.DGV_RTL == false)
            //{
            //    dgv.RightToLeft = RightToLeft.No;
            //}
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Accounts";
            if (UIAppearance.Language_ar)
            {
                this.Text = "الدليل المحاسبي";
                //System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                //rjLabel1.Font = rjLabel5.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                //btnAddNew.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                //dgv.AllowUserToOrderColumns = true;
                //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                utils utils = new utils();
                utils.Control_textSize1(this);
            }
            //getData();
        }

        private void SetupTreeView()
        {
            treeViewAccounts.ItemHeight = 24;
            //treeViewAccounts.NodeMouseDoubleClick += (s, e) => EditAccountAsync();
        }

        private void SetupContextMenu()
        {
            //var contextMenu = new ContextMenuStrip();
            //contextMenu.RightToLeft = RightToLeft.Yes;

            // إضافة حساب رئيسي
            var addMainItem = new ToolStripMenuItem("➕ إضافة حساب رئيسي");
            addMainItem.Click += async (s, e) => AddMainAccountAsync();
            contextMenu.Items.Add(addMainItem);

            // إضافة حساب فرعي
            var addChildItem = new ToolStripMenuItem("🔗 إضافة حساب فرعي");
            addChildItem.Click += (s, e) => AddChildAccountAsync();
            contextMenu.Items.Add(addChildItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // تعديل الحساب
            var editItem = new ToolStripMenuItem("✏️ تعديل الحساب");
            editItem.Click +=  (s, e) =>  EditAccountAsync();
            contextMenu.Items.Add(editItem);

            // حذف الحساب
            var deleteItem = new ToolStripMenuItem("🗑️ حذف الحساب");
            deleteItem.Click +=  (s, e) =>  DeleteAccountAsync();
            contextMenu.Items.Add(deleteItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // كشف حساب
            var statementItem = new ToolStripMenuItem("📊 كشف حساب");
            statementItem.Click += (s, e) => ShowAccountStatement();
            contextMenu.Items.Add(statementItem);

            // حركات الحساب
            var movementsItem = new ToolStripMenuItem("📈 حركات الحساب");
            movementsItem.Click += (s, e) => ShowAccountMovements();
            contextMenu.Items.Add(movementsItem);

            contextMenu.Items.Add(new ToolStripSeparator());

            // نسخ معلومات الحساب
            var copyItem = new ToolStripMenuItem("📋 نسخ معلومات الحساب");
            copyItem.Click += (s, e) => CopyAccountInfo();
            contextMenu.Items.Add(copyItem);

            // تحديث قائمة السياق عند فتحها
            contextMenu.Opening += (s, e) =>
            {
                var hasSelection = _selectedAccount != null;
                addChildItem.Enabled = hasSelection;
                editItem.Enabled = hasSelection;
                deleteItem.Enabled = hasSelection && !(_selectedAccount?.IsParent ?? false);
                statementItem.Enabled = hasSelection;
                movementsItem.Enabled = hasSelection;
                copyItem.Enabled = hasSelection;
            };

            treeViewAccounts.ContextMenuStrip = contextMenu;
        }

        #region Data Loading

        private  void LoadAccountsAsync()
        {
            try
            {
                //_isLoading = true;
                Global_Variable.Update_Um_StatusBar_Prograss("جاري تحميل الحسابات...", 0);

                //lblStatus.Text = "جاري تحميل الحسابات...";
                //lblStatus.ForeColor = Color.Blue;

                _accounts = _accountService.GetAll(true);
                BuildAccountTree();

                Global_Variable.Update_Um_StatusBar_Prograss($"تم تحميل {_accounts.Count} حساب بنجاح", 0);
                //xlblStatus.Text = $"تم تحميل {_accounts.Count} حساب بنجاح";
                //lblStatus.ForeColor = Color.Green;

                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل الحسابات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //lblStatus.Text = "فشل في تحميل الحسابات";
                //lblStatus.ForeColor = Color.Red;
            }
            finally
            {
                _isLoading = false;
            }
        }

        #endregion

        #region Tree Building

        private void BuildAccountTree()
        {
            if (_isLoading) return;

            treeViewAccounts.BeginUpdate();
            treeViewAccounts.Nodes.Clear();

            try
            {
                var rootAccounts = _accounts
                    .Where(a => a.ParentId == null || a.ParentId == 0)
                    .OrderBy(a => a.Code)
                    .ToList();

                foreach (var account in rootAccounts)
                {
                    var node = CreateAccountNode(account);
                    treeViewAccounts.Nodes.Add(node);
                    AddChildNodes(node, account);
                }

                // توسيع المستوى الأول
                foreach (TreeNode node in treeViewAccounts.Nodes)
                {
                    node.Expand();
                }
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }

        private TreeNode CreateAccountNode(Account account)
        {
            var nodeText = $"{account.Code} - {account.Name}";
            if (account.Balance != 0)
            {
                //nodeText += $" ({account.Balance:N2})";
            }

            var node = new TreeNode(nodeText)
            {
                Tag = account,
                ImageIndex = GetAccountImageIndex(account),
                SelectedImageIndex = GetAccountImageIndex(account)
            };

            // تلوين العقدة حسب نوع الحساب
            if (account.IsParent)
            {
                node.ForeColor = Color.DarkBlue;
                node.NodeFont = new Font(treeViewAccounts.Font, FontStyle.Bold);
            }
            else if (!account.IsActive)
            {
                node.ForeColor = Color.Gray;
            }

            return node;
        }

        private void AddChildNodes(TreeNode parentNode, Account parentAccount)
        {
            var childAccounts = _accounts
                .Where(a => a.ParentId.HasValue && a.ParentId.Value == parentAccount.Id)
                .OrderBy(a => a.Code)
                .ToList();

            foreach (var childAccount in childAccounts)
            {
                var childNode = CreateAccountNode(childAccount);
                parentNode.Nodes.Add(childNode);
                AddChildNodes(childNode, childAccount);
            }
        }

        private int GetAccountImageIndex(Account account)
        {
            // يمكن إضافة أيقونات مختلفة حسب نوع الحساب
            return account.IsParent ? 0 : 1;
        }

        #endregion

        #region Event Handlers

        private void TreeViewAccounts_AfterSelect(object? sender, TreeViewEventArgs e)
        {
            _selectedAccount = e.Node?.Tag as Account;
            UpdateAccountDetails();
            UpdateButtonStates();
        }

        private void UpdateAccountDetails()
        {
            if (_selectedAccount == null)
            {
                lblAccountInfo.Text = "لم يتم اختيار حساب";
                return;
            }

            var info = new StringBuilder();
            info.AppendLine($"الكود: {_selectedAccount.Code}");
            info.AppendLine($"الاسم: {_selectedAccount.Name}");
            info.AppendLine($"الاسم الإنجليزي: {_selectedAccount.NameEnglish}");
            info.AppendLine($"النوع: {_selectedAccount.Type}");
            info.AppendLine($"الطبيعة: {_selectedAccount.Nature}");
            info.AppendLine($"المستوى: {_selectedAccount.Level}");
            info.AppendLine($"الرصيد: {_selectedAccount.Balance:N2}");
            info.AppendLine($"نشط: {(_selectedAccount.IsActive ? "نعم" : "لا")}");
            info.AppendLine($"حساب أب: {(_selectedAccount.IsParent ? "نعم" : "لا")}");

            lblAccountInfo.Text = info.ToString();
        }

        private void UpdateButtonStates()
        {
            var hasSelection = _selectedAccount != null;

            btnAddChild.Enabled = hasSelection;
            btnEdit.Enabled = hasSelection;
            btnDelete.Enabled = hasSelection && !(_selectedAccount?.IsParent ?? false);
            btnAccountStatement.Enabled = hasSelection;
            btnAccountMovements.Enabled = hasSelection;
            //btnTrialBalance.Enabled = _accounts.Any();
            //btnPrintTree.Enabled = _accounts.Any();

            //// أزرار التصدير
            //btnExportJson.Enabled = _accounts.Any();
            //btnExportXml.Enabled = _accounts.Any();
            //btnExportCsv.Enabled = _accounts.Any();
            //btnExportExcel.Enabled = _accounts.Any();
        }

        #endregion

        #region Account Operations

        private void AddMainAccountAsync()
        {
            try
            {
                var addForm = new Frm_AddEditAccount(_accountService, null);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss("تم إضافة الحساب الرئيسي بنجاح", 0);

                    //lblStatus.Text = "تم إضافة الحساب الرئيسي بنجاح";
                    //lblStatus.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void AddChildAccountAsync()
        {
            //if (_selectedAccount == null)
            //{
            //    MessageBox.Show("يرجى اختيار حساب أب لإضافة حساب فرعي إليه", "تنبيه",
            //        MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return;
            //}

            //try
            //{
            //    var addChildForm = new AddChildAccountForm(_accountService, _selectedAccount);
            //    if (addChildForm.ShowDialog() == DialogResult.OK)
            //    {
            //        await LoadAccountsAsync();
            //        lblStatus.Text = "تم إضافة الحساب الفرعي بنجاح";
            //        lblStatus.ForeColor = Color.Green;

            //        // توسيع العقدة الأب وتحديد الحساب الجديد
            //        if (addChildForm.NewAccount != null)
            //        {
            //            ExpandAndSelectAccount(addChildForm.NewAccount.Code);
            //        }
            //    }
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ في إضافة الحساب الفرعي:\n{ex.Message}",
            //        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private  void EditAccountAsync()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للتعديل", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                var editForm = new Frm_AddEditAccount(_accountService, _selectedAccount);
                if (editForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss("تم تعديل الحساب بنجاح", 0);
                    //lblStatus.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تعديل الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private   void DeleteAccountAsync()
        {
            if (_selectedAccount == null)
            {
                MessageBox.Show("يرجى اختيار حساب للحذف", "تنبيه",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (_selectedAccount.IsParent)
            {
                MessageBox.Show("لا يمكن حذف حساب أب يحتوي على حسابات فرعية", "تحذير",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            var result = MessageBox.Show($"هل أنت متأكد من حذف الحساب '{_selectedAccount.Name}'؟",
                "تأكيد الحذف", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                try
                {
                     _accountService.DeleteAccountAsync(_selectedAccount.Id);
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss("تم حذف الحساب بنجاح", 0);
                    //lblStatus.ForeColor = Color.Green;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في حذف الحساب:\n{ex.Message}",
                        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
        }

        #endregion

        #region Search and Navigation

        private void SearchAccounts()
        {
            //var searchText = txtSearch.Text.Trim();
            //if (string.IsNullOrWhiteSpace(searchText))
            //{
            //    BuildAccountTree();
            //    return;
            //}

            //var filteredAccounts = _accounts.Where(a =>
            //    a.Code.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
            //    a.Name.Contains(searchText, StringComparison.OrdinalIgnoreCase) ||
            //    (a.NameEnglish?.Contains(searchText, StringComparison.OrdinalIgnoreCase) ?? false)
            //).ToList();

            //BuildFilteredTree(filteredAccounts);
            //Global_Variable.Update_Um_StatusBar_Prograss($"تم العثور على {filteredAccounts.Count} حساب", 0);
            ////lblStatus.Text = $"تم العثور على {filteredAccounts.Count} حساب";

            ////lblStatus.ForeColor = Color.Blue;
        }

        private void BuildFilteredTree(List<Account> filteredAccounts)
        {
            return;
            treeViewAccounts.BeginUpdate();
            treeViewAccounts.Nodes.Clear();

            try
            {
                foreach (var account in filteredAccounts.OrderBy(a => a.Code))
                {
                    var node = CreateAccountNode(account);
                    treeViewAccounts.Nodes.Add(node);
                }

                treeViewAccounts.ExpandAll();
            }
            finally
            {
                treeViewAccounts.EndUpdate();
            }
        }

        private void ClearSearch()
        {
            txtSearch.Clear();
            BuildAccountTree();
            //Global_Variable.Update_Um_StatusBar_Prograss("تم مسح البحث", 0);
            //lblStatus.ForeColor = Color.Green;
        }

        private void ExpandAndSelectAccount(string accountCode)
        {
            foreach (TreeNode node in treeViewAccounts.Nodes)
            {
                if (FindAndSelectNode(node, accountCode))
                {
                    break;
                }
            }
        }

        private bool FindAndSelectNode(TreeNode node, string accountCode)
        {
            if (node.Tag is Account account && account.Code == accountCode)
            {
                treeViewAccounts.SelectedNode = node;
                node.EnsureVisible();
                return true;
            }

            foreach (TreeNode childNode in node.Nodes)
            {
                if (FindAndSelectNode(childNode, accountCode))
                {
                    node.Expand();
                    return true;
                }
            }

            return false;
        }

        #endregion

        #region Reports and Statements

        private void ShowTrialBalance()
        {
            //try
            //{
            //    var trialBalanceForm = new TrialBalanceForm(_accountService);
            //    trialBalanceForm.ShowDialog();
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ في عرض ميزان المراجعة:\n{ex.Message}",
            //        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private void ShowAccountStatement()
        {
            //if (_selectedAccount == null)
            //{
            //    MessageBox.Show("يرجى اختيار حساب لعرض كشف الحساب", "تنبيه",
            //        MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return;
            //}

            //try
            //{
            //    var statementForm = new AccountStatementForm(_selectedAccount, _accountService);
            //    statementForm.ShowDialog();
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ في عرض كشف الحساب:\n{ex.Message}",
            //        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private void ShowAccountMovements()
        {
            //if (_selectedAccount == null)
            //{
            //    MessageBox.Show("يرجى اختيار حساب لعرض حركات الحساب", "تنبيه",
            //        MessageBoxButtons.OK, MessageBoxIcon.Information);
            //    return;
            //}

            //try
            //{
            //    var movementsForm = new AccountMovementsForm(_selectedAccount, _accountService);
            //    movementsForm.ShowDialog();
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show($"خطأ في عرض حركات الحساب:\n{ex.Message}",
            //        "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            //}
        }

        private void PrintAccountTree()
        {
            try
            {
                var report = GenerateTreeReport();
                // يمكن إضافة منطق الطباعة هنا
                MessageBox.Show("سيتم إضافة وظيفة الطباعة قريباً", "معلومات",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في طباعة الشجرة:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CopyAccountInfo()
        {
            if (_selectedAccount == null) return;

            try
            {
                var info = $"كود الحساب: {_selectedAccount.Code}\n" +
                          $"اسم الحساب: {_selectedAccount.Name}\n" +
                          $"النوع: {_selectedAccount.Type}\n" +
                          $"الرصيد: {_selectedAccount.Balance:N2}";

                Clipboard.SetText(info);
                Global_Variable.Update_Um_StatusBar_Prograss("تم نسخ معلومات الحساب", 0);
                //lblStatus.ForeColor = Color.Green;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في نسخ المعلومات:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Export Functions

        private void ExportToJson()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "JSON Files (*.json)|*.json",
                            Title = "تصدير الشجرة المحاسبية إلى JSON",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.json"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var exportData = new
                            {
                                ExportDate = DateTime.Now,
                                TotalAccounts = _accounts.Count,
                                Accounts = _accounts.Select(a => new
                                {
                                    a.Id,
                                    a.Code,
                                    a.Name,
                                    a.NameEnglish,
                                    a.Type,
                                    a.Nature,
                                    a.ParentId,
                                    a.Level,
                                    a.IsParent,
                                    a.IsActive,
                                    a.Balance,
                                    a.Description
                                }).ToList()
                            };

                            var json = JsonConvert.SerializeObject(exportData, Newtonsoft.Json.Formatting.Indented);
                            File.WriteAllText(saveDialog.FileName, json, Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى JSON بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير JSON:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير JSON:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToXml()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "XML Files (*.xml)|*.xml",
                            Title = "تصدير الشجرة المحاسبية إلى XML",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.xml"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var doc = new XmlDocument();
                            var root = doc.CreateElement("ChartOfAccounts");
                            doc.AppendChild(root);

                            var exportInfo = doc.CreateElement("ExportInfo");
                            exportInfo.SetAttribute("ExportDate", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"));
                            exportInfo.SetAttribute("TotalAccounts", _accounts.Count.ToString());
                            root.AppendChild(exportInfo);

                            var accountsElement = doc.CreateElement("Accounts");
                            root.AppendChild(accountsElement);

                            foreach (var account in _accounts)
                            {
                                var accountElement = doc.CreateElement("Account");
                                accountElement.SetAttribute("Id", account.Id.ToString());
                                accountElement.SetAttribute("Code", account.Code);
                                accountElement.SetAttribute("Name", account.Name);
                                accountElement.SetAttribute("NameEnglish", account.NameEnglish ?? "");
                                accountElement.SetAttribute("Type", account.Type.ToString());
                                accountElement.SetAttribute("Nature", account.Nature.ToString());
                                accountElement.SetAttribute("ParentId", account.ParentId?.ToString() ?? "");
                                accountElement.SetAttribute("Level", account.Level.ToString());
                                accountElement.SetAttribute("IsParent", account.IsParent.ToString());
                                accountElement.SetAttribute("IsActive", account.IsActive.ToString());
                                accountElement.SetAttribute("Balance", account.Balance.ToString());
                                accountElement.SetAttribute("Description", account.Description ?? "");

                                accountsElement.AppendChild(accountElement);
                            }

                            doc.Save(saveDialog.FileName);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى XML بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير XML:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير XML:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToCsv()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "CSV Files (*.csv)|*.csv",
                            Title = "تصدير الشجرة المحاسبية إلى CSV",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var csv = new StringBuilder();

                            // إضافة العناوين
                            csv.AppendLine("Id,Code,Name,NameEnglish,Type,Nature,ParentId,Level,IsParent,IsActive,Balance,Description");

                            // إضافة البيانات
                            foreach (var account in _accounts)
                            {
                                csv.AppendLine($"{account.Id}," +
                                             $"\"{account.Code}\"," +
                                             $"\"{account.Name}\"," +
                                             $"\"{account.NameEnglish ?? ""}\"," +
                                             $"\"{account.Type}\"," +
                                             $"\"{account.Nature}\"," +
                                             $"{account.ParentId?.ToString() ?? ""}," +
                                             $"{account.Level}," +
                                             $"{account.IsParent}," +
                                             $"{account.IsActive}," +
                                             $"{account.Balance}," +
                                             $"\"{account.Description ?? ""}\"");
                            }

                            File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى CSV بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير CSV:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير CSV:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ExportToExcel()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(() =>
                {
                    try
                    {
                        // تصدير إلى CSV مع امتداد Excel
                        var saveDialog = new SaveFileDialog
                        {
                            Filter = "Excel Files (*.xlsx)|*.xlsx|CSV Files (*.csv)|*.csv",
                            Title = "تصدير الشجرة المحاسبية إلى Excel",
                            FileName = $"ChartOfAccounts_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                        };

                        if (saveDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            // إنشاء CSV مباشرة بدلاً من استدعاء ExportToCsv
                            var csv = new StringBuilder();

                            // إضافة العناوين
                            csv.AppendLine("Id,Code,Name,NameEnglish,Type,Nature,ParentId,Level,IsParent,IsActive,Balance,Description");

                            // إضافة البيانات
                            foreach (var account in _accounts)
                            {
                                csv.AppendLine($"{account.Id}," +
                                             $"\"{account.Code}\"," +
                                             $"\"{account.Name}\"," +
                                             $"\"{account.NameEnglish ?? ""}\"," +
                                             $"\"{account.Type}\"," +
                                             $"\"{account.Nature}\"," +
                                             $"{account.ParentId?.ToString() ?? ""}," +
                                             $"{account.Level}," +
                                             $"{account.IsParent}," +
                                             $"{account.IsActive}," +
                                             $"{account.Balance}," +
                                             $"\"{account.Description ?? ""}\"");
                            }

                            File.WriteAllText(saveDialog.FileName, csv.ToString(), Encoding.UTF8);

                            Global_Variable.Update_Um_StatusBar_Prograss($"تم تصدير {_accounts.Count} حساب إلى Excel بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم تصدير الشجرة المحاسبية بنجاح إلى:\n{saveDialog.FileName}\n\nيمكنك فتح الملف في Excel مباشرة.",
                                "نجح التصدير", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في تصدير Excel:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير Excel:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Import Functions

        private void  ImportFromJson()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "JSON Files (*.json)|*.json",
                            Title = "استيراد الشجرة المحاسبية من JSON"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var json = File.ReadAllText(openDialog.FileName, Encoding.UTF8);
                            var importData = JsonConvert.DeserializeObject<dynamic>(json);

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                        _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // استيراد الحسابات الجديدة
                            var importedCount = 0;
                            foreach (var accountData in importData.Accounts)
                            {
                                try
                                {
                                    var account = new Account
                                    {
                                        Code = accountData.Code,
                                        Name = accountData.Name,
                                        NameEnglish = accountData.NameEnglish,
                                        Type = (AccountType)Enum.Parse(typeof(AccountType), accountData.Type.ToString()),
                                        Nature = (AccountNature)Enum.Parse(typeof(AccountNature), accountData.Nature.ToString()),
                                        ParentId = accountData.ParentId != "" ? (int?)accountData.ParentId : null,
                                        Level = accountData.Level,
                                        IsParent = accountData.IsParent,
                                        IsActive = accountData.IsActive,
                                        Balance = accountData.Balance,
                                        Description = accountData.Description
                                    };

                                    await _accountService.AddAccountAsync(account);
                                    importedCount++;
                                }
                                catch (Exception ex)
                                {
                                    // تسجيل الخطأ ومتابعة الاستيراد
                                    Console.WriteLine($"خطأ في استيراد الحساب {accountData.Code}: {ex.Message}");
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من JSON بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد JSON:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد JSON:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void ImportFromXml()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "XML Files (*.xml)|*.xml",
                            Title = "استيراد الشجرة المحاسبية من XML"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var doc = new XmlDocument();
                            doc.Load(openDialog.FileName);

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                         _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // استيراد الحسابات الجديدة
                            var importedCount = 0;
                            var accountNodes = doc.SelectNodes("//Account");

                            if (accountNodes != null)
                            {
                                foreach (XmlNode accountNode in accountNodes)
                                {
                                    try
                                    {
                                        var account = new Account
                                        {
                                            Code = accountNode.Attributes?["Code"]?.Value ?? "",
                                            Name = accountNode.Attributes?["Name"]?.Value ?? "",
                                            NameEnglish = accountNode.Attributes?["NameEnglish"]?.Value,
                                            Type = (AccountType)Enum.Parse(typeof(AccountType), accountNode.Attributes?["Type"]?.Value ?? "Assets"),
                                            Nature = (AccountNature)Enum.Parse(typeof(AccountNature), accountNode.Attributes?["Nature"]?.Value ?? "Debit"),
                                            ParentId = string.IsNullOrEmpty(accountNode.Attributes?["ParentId"]?.Value) ? null : int.Parse(accountNode.Attributes["ParentId"].Value),
                                            Level = int.Parse(accountNode.Attributes?["Level"]?.Value ?? "1"),
                                            IsParent = bool.Parse(accountNode.Attributes?["IsParent"]?.Value ?? "false"),
                                            IsActive = bool.Parse(accountNode.Attributes?["IsActive"]?.Value ?? "true"),
                                            Balance = decimal.Parse(accountNode.Attributes?["Balance"]?.Value ?? "0"),
                                            Description = accountNode.Attributes?["Description"]?.Value
                                        };

                                        await _accountService.AddAccountAsync(account);
                                        importedCount++;
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"خطأ في استيراد حساب من XML: {ex.Message}");
                                    }
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من XML بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد XML:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد XML:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void ImportFromCsv()
        {
            try
            {
                // تشغيل في UI Thread مباشرة
                this.BeginInvoke(new MethodInvoker(async () =>
                {
                    try
                    {
                        var openDialog = new OpenFileDialog
                        {
                            Filter = "CSV Files (*.csv)|*.csv",
                            Title = "استيراد الشجرة المحاسبية من CSV"
                        };

                        if (openDialog.ShowDialog(this) == DialogResult.OK)
                        {
                            var result = MessageBox.Show("هل تريد استبدال الشجرة المحاسبية الحالية؟\n" +
                                                        "اختر 'نعم' للاستبدال الكامل أو 'لا' للإضافة فقط.",
                                "تأكيد الاستيراد", MessageBoxButtons.YesNoCancel, MessageBoxIcon.Question);

                            if (result == DialogResult.Cancel) return;

                            var lines = File.ReadAllLines(openDialog.FileName, Encoding.UTF8);

                            if (lines.Length < 2)
                            {
                                MessageBox.Show("ملف CSV فارغ أو لا يحتوي على بيانات صحيحة", "خطأ",
                                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                                return;
                            }

                            if (result == DialogResult.Yes)
                            {
                                // حذف الحسابات الموجودة
                                var existingAccounts = _accountService.GetAll();
                                foreach (var account in existingAccounts)
                                {
                                    try
                                    {
                                        _accountService.DeleteAccountAsync(account.Id);
                                    }
                                    catch { } // تجاهل أخطاء الحذف
                                }
                            }

                            // تخطي السطر الأول (العناوين)
                            var importedCount = 0;
                            for (int i = 1; i < lines.Length; i++)
                            {
                                try
                                {
                                    var fields = ParseCsvLine(lines[i]);
                                    if (fields.Length >= 12)
                                    {
                                        var account = new Account
                                        {
                                            Code = fields[1].Trim('"'),
                                            Name = fields[2].Trim('"'),
                                            NameEnglish = fields[3].Trim('"'),
                                            Type = (AccountType)Enum.Parse(typeof(AccountType), fields[4].Trim('"')),
                                            Nature = (AccountNature)Enum.Parse(typeof(AccountNature), fields[5].Trim('"')),
                                            ParentId = string.IsNullOrEmpty(fields[6]) ? null : int.Parse(fields[6]),
                                            Level = int.Parse(fields[7]),
                                            IsParent = bool.Parse(fields[8]),
                                            IsActive = bool.Parse(fields[9]),
                                            Balance = decimal.Parse(fields[10]),
                                            Description = fields[11].Trim('"')
                                        };

                                        await _accountService.AddAccountAsync(account);
                                        importedCount++;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    Console.WriteLine($"خطأ في استيراد السطر {i}: {ex.Message}");
                                }
                            }

                            LoadAccountsAsync();
                            Global_Variable.Update_Um_StatusBar_Prograss($"تم استيراد {importedCount} حساب من CSV بنجاح", 0);
                            //lblStatus.ForeColor = Color.Green;

                            MessageBox.Show($"تم استيراد {importedCount} حساب بنجاح!",
                                "نجح الاستيراد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        }
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"خطأ في استيراد CSV:\n{ex.Message}",
                            "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }));
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد CSV:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ImportFromExcel()
        {
            try
            {
                MessageBox.Show("يرجى حفظ ملف Excel بتنسيق CSV أولاً، ثم استخدام خيار 'استيراد من CSV'.",
                    "معلومات", MessageBoxButtons.OK, MessageBoxIcon.Information);
                ImportFromCsv();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في استيراد Excel:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }

        #endregion

        #region Helper Methods

        private string GenerateTreeReport()
        {
            var report = new StringBuilder();
            report.AppendLine("تقرير الشجرة المحاسبية");
            report.AppendLine("=".PadRight(50, '='));
            report.AppendLine($"تاريخ التقرير: {DateTime.Now:yyyy/MM/dd HH:mm}");
            report.AppendLine($"إجمالي الحسابات: {_accounts.Count}");
            report.AppendLine();

            foreach (var account in _accounts.OrderBy(a => a.Code))
            {
                var indent = new string(' ', (account.Level - 1) * 4);
                report.AppendLine($"{indent}{account.Code} - {account.Name} ({account.Balance:N2})");
            }

            return report.ToString();
        }

        #endregion



        private  void btnAddMainAccount_Click(object sender, EventArgs e)
        {
            //treeView1.Refresh();
            //treeView1.Nodes.Add("Account");
            //treeView1.Refresh();
            //return;
            try
            {
                var addForm = new Frm_AddEditAccount(_accountService, null);
                if (addForm.ShowDialog() == DialogResult.OK)
                {
                    LoadAccountsAsync();
                    Global_Variable.Update_Um_StatusBar_Prograss($"تم إضافة الحساب الرئيسي بنجاح", 0);
                    //lblStatus.Text = "تم إضافة الحساب الرئيسي بنجاح";
                    //lblStatus.ForeColor = Color.Green;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الحساب:\n{ex.Message}",
                    "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private  void Frm_Account_Manual_Load(object sender, EventArgs e)
        {
            //treeView1.Nodes.Add("aaaaaaaaa");
            LoadAccountsAsync();
            //this.Refresh();
            //treeView1.Refresh();
        }

        private  void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadAccountsAsync();
            //BuildAccountTree();
        }

        private  void btnEdit_Click(object sender, EventArgs e)
        {
             EditAccountAsync();
        }

        private  void btnDelete_Click(object sender, EventArgs e)
        {
            //DeleteAccountAsync();
            //treeViewAccounts.BeginUpdate();

            //treeViewAccounts.Nodes.Clear();
            //MessageBox.Show(treeViewAccounts.Nodes.Count.ToString());

            treeViewAccounts.Nodes.Add("soliman");

            //treeViewAccounts.EndUpdate();
            treeViewAccounts.Nodes[0].EnsureVisible();
            //Invalidate();
        }

        private  void btnAccountStatement_Click(object sender, EventArgs e)
        {
            //await ShowAccountStatementAsync();  
        }

        private  void btnAccountMovements_Click(object sender, EventArgs e)
        {
            //await ShowAccountMovements();

        }
    }
}
