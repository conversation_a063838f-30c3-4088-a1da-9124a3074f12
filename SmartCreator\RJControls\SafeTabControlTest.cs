using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار النسخة الآمنة من RJTabControl
    /// </summary>
    public partial class SafeTabControlTest : Form
    {
        private SafeRJTabControl safeTabControl;

        public SafeTabControlTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // safeTabControl
            // 
            this.safeTabControl = new SafeRJTabControl();
            this.safeTabControl.Dock = DockStyle.Fill;
            this.safeTabControl.TabHeight = 40;
            this.safeTabControl.TabSpacing = 3;
            this.safeTabControl.TabPadding = 20;

            // 
            // SafeTabControlTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 600);
            this.Controls.Add(this.safeTabControl);
            this.Name = "SafeTabControlTest";
            this.Text = "🛡️ اختبار SafeRJTabControl - النسخة الآمنة";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += SafeTabControlTest_Load;
        }

        private void SafeTabControlTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية
                AddTestTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ تم التحميل بنجاح!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل SafeRJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل التحميل!";
            }
        }

        private void AddTestTabs()
        {
            // تاب الترحيب
            var welcomeTab = this.safeTabControl.AddTab("مرحباً");
            if (welcomeTab != null)
            {
                var welcomeLabel = new Label
                {
                    Text = "🛡️ SafeRJTabControl يعمل بنجاح!\n\n" +
                           "✅ تم إنشاء الكنترول بدون أخطاء\n" +
                           "✅ يمكن إضافة التابات\n" +
                           "✅ التنقل بين التابات يعمل\n" +
                           "✅ جميع الخصائص الأساسية تعمل\n\n" +
                           "🎯 الآن جرب سحب SafeRJTabControl\n" +
                           "من Toolbox في Visual Studio!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.FromArgb(0, 122, 204),
                    Padding = new Padding(20)
                };
                welcomeTab.AddControl(welcomeLabel);
            }

            // تاب الاختبار
            var testTab = this.safeTabControl.AddTab("اختبار");
            if (testTab != null)
            {
                var testPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

                var addTabButton = new RJButton
                {
                    Text = "إضافة تاب جديد",
                    IconChar = IconChar.Plus,
                    Location = new Point(20, 20),
                    Size = new Size(200, 50),
                    BackColor = Color.FromArgb(76, 175, 80),
                    ForeColor = Color.White,
                    BorderRadius = 10,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold)
                };
                addTabButton.Click += AddTabButton_Click;

                var removeTabButton = new RJButton
                {
                    Text = "إزالة هذا التاب",
                    IconChar = IconChar.Minus,
                    Location = new Point(240, 20),
                    Size = new Size(200, 50),
                    BackColor = Color.FromArgb(244, 67, 54),
                    ForeColor = Color.White,
                    BorderRadius = 10,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold)
                };
                removeTabButton.Click += (s, e) => this.safeTabControl.RemoveTab(testTab);

                var infoLabel = new Label
                {
                    Text = "🧪 اختبار الوظائف:\n\n" +
                           "• إضافة تابات جديدة\n" +
                           "• إزالة التابات\n" +
                           "• التنقل بين التابات\n" +
                           "• تغيير الخصائص\n\n" +
                           $"عدد التابات الحالي: {this.safeTabControl.TabCount}\n" +
                           $"التاب النشط: {this.safeTabControl.SelectedIndex}",
                    Location = new Point(20, 90),
                    Size = new Size(400, 200),
                    Font = new Font("Segoe UI", 11),
                    ForeColor = Color.FromArgb(70, 70, 70)
                };

                testPanel.Controls.Add(addTabButton);
                testPanel.Controls.Add(removeTabButton);
                testPanel.Controls.Add(infoLabel);
                testTab.AddControl(testPanel);
            }

            // تاب المعلومات
            var infoTab = this.safeTabControl.AddTab("معلومات");
            if (infoTab != null)
            {
                var infoTextBox = new RJTextBox
                {
                    Dock = DockStyle.Fill,
                    MultiLine = true,
                    ReadOnly = true,
                    Text = "ℹ️ معلومات SafeRJTabControl:\n\n" +
                           "🎯 الهدف:\n" +
                           "إنشاء نسخة مبسطة وآمنة من RJTabControl\n" +
                           "لتحديد سبب مشكلة Designer\n\n" +
                           "🛡️ الميزات الآمنة:\n" +
                           "• Constructor مبسط\n" +
                           "• خصائص أساسية فقط\n" +
                           "• حماية شاملة من null\n" +
                           "• بدون تعقيدات غير ضرورية\n\n" +
                           "✅ إذا عمل هذا الكنترول في Designer:\n" +
                           "فالمشكلة في التعقيد الزائد في RJTabControl الأصلي\n\n" +
                           "❌ إذا لم يعمل هذا الكنترول:\n" +
                           "فالمشكلة في شيء أساسي آخر\n\n" +
                           "🔍 الخطوة التالية:\n" +
                           "جرب سحب SafeRJTabControl من Toolbox\n" +
                           "في Visual Studio Designer",
                    Style = TextBoxStyle.MatteBorder,
                    BorderSize = 2,
                    BorderColor = Color.FromArgb(0, 150, 136),
                    BorderRadius = 8,
                    Font = new Font("Segoe UI", 10),
                    TextAlign = HorizontalAlignment.Left
                };
                infoTab.AddControl(infoTextBox);
            }

            // تفعيل التاب الأول
            this.safeTabControl.SelectedIndex = 0;
        }

        private void AddTabButton_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = this.safeTabControl.AddTab($"تاب {this.safeTabControl.TabCount + 1}");
                if (newTab != null)
                {
                    var label = new Label
                    {
                        Text = $"🌟 تاب جديد #{this.safeTabControl.TabCount}\n\n" +
                               $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                               "✅ إضافة التابات تعمل بمثالية!",
                        Dock = DockStyle.Fill,
                        TextAlign = ContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 12, FontStyle.Bold),
                        ForeColor = Color.FromArgb(156, 39, 176)
                    };
                    newTab.AddControl(label);

                    // تفعيل التاب الجديد
                    this.safeTabControl.SelectedIndex = this.safeTabControl.TabCount - 1;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار النسخة الآمنة
        /// </summary>
        public static void RunSafeTest()
        {
            try
            {
                var form = new SafeTabControlTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار SafeRJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء SafeRJTabControl فقط
        /// </summary>
        public static void TestSafeControlCreation()
        {
            try
            {
                var safeControl = new SafeRJTabControl();
                
                // اختبار الخصائص
                var tabCount = safeControl.TabCount;
                var selectedIndex = safeControl.SelectedIndex;
                var tabHeight = safeControl.TabHeight;
                
                // اختبار إضافة تاب
                var tab = safeControl.AddTab("Test Tab");
                
                MessageBox.Show($"✅ SafeRJTabControl تم إنشاؤه بنجاح!\n\n" +
                               $"TabCount: {safeControl.TabCount}\n" +
                               $"SelectedIndex: {safeControl.SelectedIndex}\n" +
                               $"TabHeight: {safeControl.TabHeight}\n\n" +
                               "🎯 الآن جرب سحبه من Toolbox في Designer!",
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء SafeRJTabControl:\n\n{ex.Message}\n\n{ex.StackTrace}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
