﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Accounts;
using SmartCreator.Forms.SellingPoints;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    public partial class Form_Product : RJChildForm
    {
        Smart_DataAccess smart_DataAccess = null;
        public Form_Product()
        {
            InitializeComponent();
            smart_DataAccess = new Smart_DataAccess();
            this.Text = "الاصناف";

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Products";
            if (UIAppearance.Language_ar)
            {
                this.Text = "الاصناف";
                System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                System.Drawing.Font DGV_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                btnAddNew.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                utils utils = new utils();
                utils.Control_textSize1(this);

            }

        }
        private void getData()
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.Load<Product>($"select * from Product where  Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                dgv.DataSource = sp;

                try { dgv.Columns["Id"].Visible = false; } catch { }
                try { dgv.Columns["Rb"].Visible = false; } catch { }
                try { dgv.Columns["Description"].Visible = false; } catch { }
                try { dgv.Columns["Image"].Visible = false; } catch { }
                try {dgv.Columns["Product_uomId"].Visible = false; } catch { }
                try {dgv.Columns["Account_IncomeId"].Visible = false; } catch { }
                try {dgv.Columns["Account_ExpenseId"].Visible = false; } catch { }
                try {dgv.Columns["Active"].Visible = false; } catch { }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void Form_Product_Load(object sender, EventArgs e)
        {
            getData();
        }
       
        private DataTable dt()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الكود",typeof(string));
            dt.Columns.Add("الاسم",typeof(string));
            dt.Columns.Add("Price", typeof(float));
            
            return new DataTable();
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            Form_ProductMaintenance frm = new Form_ProductMaintenance();
            frm.ShowDialog();
            if (frm.succes)
                getData();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count>0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Product>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "Product");
                    var frm = new Form_ProductMaintenance(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (RJMessageBox.Show("هل انت متاكد من حذف الصنف", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                smart_DataAccess.DeleteById<Product>(dgv.CurrentRow.Cells["Id"].Value.ToString(), "Product");
                getData();
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            getData();
        }

        

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Product>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "Product");
                    var frm = new Form_ProductMaintenance(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }
    }
}
