﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.SellingPoints
{
    public partial class Form_SelleingPoint_Commissions : RJForms.RJChildForm
    {
        SellingPoint SP = null;
        Comm_SellingPoint sp_comm=null;
        public Form_SelleingPoint_Commissions(SellingPoint _sp)
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            CBOX_PercentageType.SelectedIndex= 0;
            SP = _sp;
            Set_Font();
            btnSave.Design = ButtonDesign.Delete;
            btnSave.Text = "تفعيل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Download;
 
        }
        private void Set_Font()
        {
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidSansArabic, 10 , FontStyle.Regular);
            lblTitle.Font = title_font;

            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            btnSave.Font =  Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel5.Font =  rjLabel8.Font = rjLabel6.Font =  CBOX_PercentageType.Font =
            lbl_font;


            dgv.AllowUserToOrderColumns = true;
            dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = (int)(40 );

            dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);


            this.Focus();

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            return;

        }


        private void getData()
        {
            try
            {
                Smart_DataAccess dataAccess = new Smart_DataAccess();
                var sp = dataAccess.Load<Comm_SellingPoint>($"select * from Comm_SellingPoint where  Rb='{Global_Variable.Mk_resources.RB_code}' and SpCode='{SP.Code}' ");

                if (sp.Count >0)
                {
                    lblTitle.Text = "تعديل عمولات  :-  " + SP.UserName;
                    btnSave.Design = ButtonDesign.Confirm;
                    btnSave.Text = "حفظ";

                }

                dgv.DataSource = sp;
               
                dgv.Columns["Id"].Visible = false;
                dgv.Columns["SpCode"].Visible = false;
                dgv.Columns["Is_percentage"].Visible = false;
                dgv.Columns["PercentageType"].Visible = false;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();

                if (btnSave.Text == "تفعيل")
                {
                    //===== اضافة عموله لكل باقه لنقطه البيع 
                    List<Comm_SellingPoint> SPComm = new List<Comm_SellingPoint>();
                    foreach (UmProfile profile in Global_Variable.UM_Profile)
                    {
                        Comm_SellingPoint comm = new Comm_SellingPoint();
                        comm.SpCode = SP.Code;
                        comm.ProfileName = profile.Name;
                        comm.Percentage = profile.Percentage;
                        comm.Rb_Sn = Global_Variable.Mk_resources.RB_SN;
                        comm.Rb = Global_Variable.Mk_resources.RB_code;
                        comm.Is_percentage = 0;
                        SPComm.Add(comm);
                    }
                    string commndInsert = $"INSERT OR IGNORE into Comm_SellingPoint (SpCode,ProfileName,Percentage,Rb_Sn,Rb) values(@SpCode,@ProfileName,@Percentage,@Rb_Sn,@Rb) ;";
                    int InsertEffecs = smart_DataAccess.Execute<Comm_SellingPoint>(commndInsert, SPComm.ToHashSet());
                    if (InsertEffecs > 0)
                    {
                        btnSave.Text = "حفظ";
                        getData();
                    }
                }

                else
                {
                    if (sp_comm == null)
                        return;

                    if (!float.TryParse(txt_Percentage.Text, out float value))
                    {
                        RJMessageBox.Show("النسبه يجب ان تكون رقم صحيح او رقم عشري");
                        return;

                    }

                    sp_comm.Percentage = (float)Convert.ToDouble(txt_Percentage.Text);
                    sp_comm.Is_percentage = Convert.ToInt32(Toggle_Active.Checked);
                    sp_comm.PercentageType = CBOX_PercentageType.SelectedIndex;

                    string comnd = $"update Comm_SellingPoint set Is_percentage=@Is_percentage,PercentageType=@PercentageType,Percentage=@Percentage where Id={sp_comm.Id}";
                    int effecs = smart_DataAccess.Execute<Comm_SellingPoint>(comnd, sp_comm);
                    if (effecs > 0)
                        RJMessageBox.Show("تم تعديل عمولة الباقه بنجاح");
                    else
                        RJMessageBox.Show("حدث خطاء");
                    getData();
                }

            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }

        private void Form_SelleingPoint_Commissions_Load(object sender, EventArgs e)
        {
            if (SP == null)
                return;
            getData();
        }

        private void rjDataGridView2_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1) //not click  on header
            {
                foreach (DataGridViewRow dr in dgv.SelectedRows)
                {
                    Comm_SellingPoint sp = dr.DataBoundItem as Comm_SellingPoint;
                    if (sp != null)
                    {
                        sp_comm= sp;
                        txt_Percentage.Text = sp.Percentage.ToString();
                        Toggle_Active.Checked=Convert.ToBoolean(sp.Is_percentage);
                        txt_ProfileNAme.Text=sp.ProfileName.ToString();
                        CBOX_PercentageType.SelectedIndex= Convert.ToInt32(sp.PercentageType);
                    }
                }
            }
        }
    }
}
