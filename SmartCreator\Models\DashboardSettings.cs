﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Models
{
    /// <summary>
    /// نموذج لحفظ حالة النافذة
    /// </summary>
    public class FormWindowInfo
    {
        /// <summary>
        /// موقع النافذة
        /// </summary>
        public Point Location { get; set; }

        /// <summary>
        /// حجم النافذة
        /// </summary>
        public Size Size { get; set; }

        /// <summary>
        /// حالة النافذة (Normal, Maximized, Minimized)
        /// </summary>
        public FormWindowState WindowState { get; set; }

        /// <summary>
        /// موقع بداية النافذة
        /// </summary>
        public FormStartPosition StartPosition { get; set; }

        /// <summary>
        /// تاريخ آخر حفظ
        /// </summary>
        public DateTime LastSaved { get; set; } = DateTime.Now;

        /// <summary>
        /// إنشاء FormWindowInfo من Form
        /// </summary>
        public static FormWindowInfo FromForm(Form form)
        {
            return new FormWindowInfo
            {
                Location = form.Location,
                Size = form.Size,
                WindowState = form.WindowState,
                StartPosition = form.StartPosition,
                LastSaved = DateTime.Now
            };
        }

        /// <summary>
        /// تطبيق الحالة على Form
        /// </summary>
        public void ApplyToForm(Form form)
        {
            try
            {
                // التأكد من أن النافذة ضمن حدود الشاشة
                if (Location.X >= 0 && Location.Y >= 0 &&
                    Location.X < Screen.PrimaryScreen.WorkingArea.Width &&
                    Location.Y < Screen.PrimaryScreen.WorkingArea.Height)
                {
                    form.Location = Location;
                }

                // تطبيق الحجم
                if (Size.Width > 0 && Size.Height > 0)
                {
                    form.Size = Size;
                }

                // تطبيق حالة النافذة
                form.WindowState = WindowState;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"خطأ في تطبيق حالة النافذة: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// نموذج لإعدادات الداش بورد
    /// </summary>
    public class DashboardSettings
    {
        /// <summary>
        /// آخر تبويب محدد
        /// </summary>
        public string LastSelectedTab { get; set; } = "Overview";

        /// <summary>
        /// التحديث التلقائي مفعل
        /// </summary>
        public bool AutoRefresh { get; set; } = true;

        /// <summary>
        /// فترة التحديث بالثواني
        /// </summary>
        public int RefreshInterval { get; set; } = 1;

        /// <summary>
        /// إظهار رسم CPU البياني
        /// </summary>
        public bool ShowCpuGraph { get; set; } = true;

        /// <summary>
        /// إظهار رسم الذاكرة البياني
        /// </summary>
        public bool ShowMemoryGraph { get; set; } = true;

        /// <summary>
        /// إظهار رسم الشبكة البياني
        /// </summary>
        public bool ShowNetworkGraph { get; set; } = false;

        /// <summary>
        /// فترة تحديث الرسوم البيانية بالميلي ثانية
        /// </summary>
        public int GraphUpdateInterval { get; set; } = 2000;

        /// <summary>
        /// الحد الأقصى لنقاط البيانات في الرسم البياني
        /// </summary>
        public int MaxDataPoints { get; set; } = 100;
    }

    /// <summary>
    /// نموذج لإعدادات إدارة المستخدمين
    /// </summary>
    public class UserManagementSettings
    {
        /// <summary>
        /// نوع المستخدم الافتراضي
        /// </summary>
        public string DefaultUserType { get; set; } = "Hotspot";

        /// <summary>
        /// مدة الصلاحية الافتراضية بالأيام
        /// </summary>
        public int DefaultValidityDays { get; set; } = 30;

        /// <summary>
        /// حد البيانات الافتراضي بالميجابايت
        /// </summary>
        public int DefaultDataLimit { get; set; } = 1024;

        /// <summary>
        /// حد السرعة الافتراضي بالكيلوبايت/ثانية
        /// </summary>
        public int DefaultSpeedLimit { get; set; } = 512;

        /// <summary>
        /// توليد كلمة المرور تلقائياً
        /// </summary>
        public bool AutoGeneratePassword { get; set; } = true;

        /// <summary>
        /// طول كلمة المرور
        /// </summary>
        public int PasswordLength { get; set; } = 8;

        /// <summary>
        /// إظهار الخيارات المتقدمة
        /// </summary>
        public bool ShowAdvancedOptions { get; set; } = false;

        /// <summary>
        /// تفعيل العمليات المجمعة
        /// </summary>
        public bool EnableBulkOperations { get; set; } = true;
    }

    /// <summary>
    /// نموذج لإعدادات إدارة الكروت
    /// </summary>
    public class CardManagementSettings
    {
        /// <summary>
        /// نوع الكارت الافتراضي
        /// </summary>
        public string DefaultCardType { get; set; } = "Hotspot";

        /// <summary>
        /// قيمة الكارت الافتراضية
        /// </summary>
        public decimal DefaultCardValue { get; set; } = 10.0m;

        /// <summary>
        /// مدة الصلاحية الافتراضية بالأيام
        /// </summary>
        public int DefaultValidityDays { get; set; } = 30;

        /// <summary>
        /// بادئة رقم الكارت
        /// </summary>
        public string CardNumberPrefix { get; set; } = "CARD";

        /// <summary>
        /// طول رقم الكارت
        /// </summary>
        public int CardNumberLength { get; set; } = 10;

        /// <summary>
        /// طباعة الكروت تلقائياً
        /// </summary>
        public bool AutoPrintCards { get; set; } = false;

        /// <summary>
        /// عدد الكروت في الصفحة الواحدة
        /// </summary>
        public int CardsPerPage { get; set; } = 6;
    }
}
