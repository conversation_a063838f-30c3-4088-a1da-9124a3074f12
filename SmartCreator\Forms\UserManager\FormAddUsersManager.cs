﻿//using DevComponents.DotNetBar;
//using DevComponents.DotNetBar.Controls;
//using DevExpress.XtraEditors.Filtering.Templates;
using CefSharp.DevTools.Profiler;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
//using ServiceStack.OrmLite;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.SellingPoints;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.TestAndDemo;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
//using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAddUsersManager : RJChildForm
    {
        private bool firstLoad = true;
        bool isFirstLoadForm = true;
        public Form_PrintUserManagerState Frm_State;
        public FormAddUsersManager()
        { 
            //this.RightToLeft = RightToLeft.Yes; 
            //this.RightToLeftLayout=true;
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            dgv.EnableHeadersVisualStyles = false;
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            if (Global_Variable.Mk_resources.version>=7)
            {
                pnl_attrbut.Visible = true;
                pnl_customer.Visible = false;
            }

            lbl_RegisterAsBatch.ForeColor=utils.Dgv_DarkColor;
            //lbl_RegisterAsBatch.ForeColor=Color.Blue;
            lbl_RegisterAs_LastBatch.ForeColor=utils.Dgv_DarkColor;
            //this.Refresh();
            //textBoxX1.BackColor =Settings.UIAppearance.StyleColor;
            //textBoxX1.BackColor = Settings.UIAppearance.BackgroundColor;
            //textBoxX1.BorderStyle = BorderStyle.None; // Remove border
            ////textBoxX1.Size = new System.Drawing.Size(230, 16);

            ////textBoxX1.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            ////textBoxX1.AutoScaleMode = AutoScaleMode.None;
            ////this.BackColor = UIAppearance.StyleColor;
            ////this.Padding = new Padding(10, padFactor, 10, padFactor);
            ////textBoxX1.Size = new System.Drawing.Size(250, 32);
            //this.Font = new Font(UIAppearance.TextFamilyName, UIAppearance.TextSize);
            //textBoxX1.ForeColor = UIAppearance.TextColor;
            ////textBoxX1.ResumeLayout(false);

            //ApplyAppearanceSettings(textBoxX1);
            //RJComboBox
            set_fonts();
        }
        private void set_fonts()
        {

            txtNumberCard.RightToLeft = txt_ShardUser.RightToLeft
                = txt_StartCard.RightToLeft
                = txt_EndCard.RightToLeft = txt_attribute.RightToLeft
                = txt_last_batchNumber.RightToLeft
                = txt_longUsers.RightToLeft
                = txt_longPassword.RightToLeft = RightToLeft.No;

            lbl_radio_fast_print.Font = lbl_radio_one_user.Font = lbl_radio_Print.Font = Program.GetCustomFont(Resources.DroidSansArabic, 10 , FontStyle.Bold);
            
            Font lbl2 = Program.GetCustomFont(Resources.DroidSansArabic, 9.75f , FontStyle.Regular);
            lbl_FirstUse.Font = lbl_excel.Font = lbl_script_File.Font = lbl_text_File.Font = lbl_RegisterAsBatch.Font
                = lbl_Save_PDF.Font = lbl_OpenAfterPrint.Font = lbl_With_Archive_uniqe.Font = lbl_RegisterAs_LastBatch.Font = lbl2;

            Font lbl1 = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            lbl_count.Font = lbl_startCard.Font = lbl_endCards.Font = lbl_UserPassword_Pattern.Font = lbl_group.Font = lbl_Attribute.Font
            = lbl_Customer.Font = lbl_profile.Font = lbl_TemplateCards.Font = lbl_SellingPoint.Font
            = lbl_User_NumberORcharcter.Font = lbl_Pass_NumberORcharcter.Font = lbl_User_NumberORcharcter.Font
            = lbl_ShardUser.Font = lbl_note.Font
            = lbl1;
            btnAdd.Font = btn_add_One.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Bold);

            cbox_User_NumberORcharcter.Font = CBox_SellingPoint.Font = cbox_UserPassword_Pattern.Font = cbox_Pass_NumberORcharcter.Font = Program.GetCustomFont(Resources.DroidSansArabic,9, FontStyle.Regular);
            lbl_Title2.Font = lbl_Title1.Font  = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Regular);

            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersHeight = 35;



            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            return;



            //return;
           //rjDataGridView1.DefaultCellStyle.Font= dgvHeader_font;

            

            //Font lbl2 = Program.GetCustomFont(Resources.DroidKufi_Regular, 9, FontStyle.Bold);
           
            //cbox_User_NumberORcharcter.Font= CBox_SellingPoint.Font = CustomFonts.Get_Custom_Font("Cairo_Regular", 9f,false);
            //cbox_UserPassword_Pattern.Font= cbox_Pass_NumberORcharcter.Font=  CustomFonts.Get_Custom_Font("Cairo_Regular", 7.75f,false);
             
            //Program.GetCustomFont(Resources.Cairo_ExtraBold, 10 * utils.ScaleFactor, FontStyle.Bold);

           
            //groupBox1.ForeColor = UIAppearance.TextColor;


            //lbl_radio_fast_print.Font = lbl_radio_Print.Font = lbl_radio_one_user.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Bold);


            dgv.AllowUserToOrderColumns = true;
            dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f * utils.ScaleFactor, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = (int)(40 * utils.ScaleFactor);

            dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size * utils.ScaleFactor, dgv.DefaultCellStyle.Font.Style);

            Control_Loop(pnlClientArea);

        }
        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJTextBox) || C.GetType() != typeof(RJComboBox))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size * utils.ScaleFactor, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }
        private void Get_Cbox_Profile()
        {
            try
            {
                var umProfil = new List<UmProfile>();
                umProfil.Add(new UmProfile { Id = 0, Name = "" });
                umProfil.AddRange(Global_Variable.UM_Profile);

                CBox_Profile.DataSource = umProfil;
                CBox_Profile.DisplayMember = "Name";
                CBox_Profile.ValueMember = "Name";
                CBox_Profile.SelectedIndex = -1;
                CBox_Profile.Text = "";


            }
            catch { }

            //try
            //{
            //    List<UmProfile> p = Global_Variable.UM_Profile;
            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("", "");
            //    foreach (UmProfile s in p)
            //        comboSource.Add(s.Name, s.Name + " (" + s.Price + ")");

            //    CBox_Profile.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile.DisplayMember = "Value";
            //    CBox_Profile.ValueMember = "Key";
            //}
            //catch { }
            
            //try
            //{
            //    //List<UserManager_Profile_UserManager> sp = Global_Variable.UM_Profile;
            //    Dictionary<string, UserManager_Profile_UserManager> comboSource = new Dictionary<string, UserManager_Profile_UserManager>();
            //    comboSource.Add("", new UserManager_Profile_UserManager());
            //    foreach (UserManager_Profile_UserManager s in Global_Variable.UM_Profile)
            //        comboSource.Add(s.Name+" ("+s.Price+")", s);

            //    CBox_Profile.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile.DisplayMember = "Key";
            //    CBox_Profile.ValueMember = "Value";
            //}
            //catch { }


        }

        private void Get_SellingPoint()
        {Smart_DataAccess da = new Smart_DataAccess();
            try
            {

                
                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }catch { }
            return;
            CBox_SellingPoint = da.Get_ComboBox_SellingPoint();
            CBox_SellingPoint.Refresh();


            //List<SellingPoint> sp = da.Get_SellingPoints();
            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    comboSource.Add(0, "");
            //    foreach (SellingPoint s in sp)
            //        comboSource.Add((int)s.Id, s.UserName);

            //    CBox_SellingPoint.DataSource = new BindingSource(comboSource, null);
            //    CBox_SellingPoint.DisplayMember = "Value";
            //    CBox_SellingPoint.ValueMember = "Key";
            //    CBox_SellingPoint.SelectedIndex = 0;
            //    CBox_SellingPoint.Text = "";


            //}
            //catch { }
        }

        [Obsolete]
        private void Get_UMCustomer()
        {
            try
            {
                if(Global_Variable.Mk_resources.version<=6)
                {
                    if(Global_Variable.UM_Customer!=null)
                    {
                        if(Global_Variable.UM_Customer.Count>0)
                        {
                            CBox_CustomerUserMan.DataSource = Global_Variable.UM_Customer;
                            CBox_CustomerUserMan.DisplayMember = "Name";
                            CBox_CustomerUserMan.ValueMember = "Name";
                        }
                        else
                        {
                            Mk_DataAccess.Get_UserManager_Customer();
                        }
                    }
                    else
                    {
                        Mk_DataAccess.Get_UserManager_Customer();
                    }
                }

                CBox_CustomerUserMan.DataSource = Global_Variable.UM_Customer;
                CBox_CustomerUserMan.DisplayMember = "Name";
                CBox_CustomerUserMan.ValueMember = "Name";
            }
            catch { }

        }

        private void Get_CBox_Attribute()
        {
            try
            {
                var umAttribute = new List<string>();
                umAttribute.Add("");
                umAttribute.AddRange(Global_Variable.UM_Attribute);

                CBox_Attribute.DataSource = umAttribute;
                //CBox_Attribute.DisplayMember = "Name";
                //CBox_Attribute.ValueMember = "Name";
                CBox_Attribute.SelectedIndex = 0;
                CBox_Attribute.Text = "";


            }
            catch { }

        }
        private void Get_CBox_group()
        {
            try
            {
                var umGroup = new List<string>();
                //umGroup.Add("");
                umGroup.AddRange(Global_Variable.UM_Group);

                CBox_group.DataSource = umGroup;
                //CBox_group.DisplayMember = "Name";
                //CBox_group.ValueMember = "Name";
                CBox_group.SelectedIndex = 0;
                CBox_group.Text = "default";


            }
            catch { }
        }


        private void FormAddUsersManager_ResizeEnd(object sender, EventArgs e)
        {
            //resize_template_cards_panel();
            //set_loaction_left_panel();
            //RJMessageBox.Show("end Resize");
        }
        
        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            set_loaction_left_panel();
        }
        public  void set_loaction_left_panel()
        {
            
            if (isFirstLoadForm)
                return;
            

            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel4.Refresh();
            panel_PreviewTempateCards.Refresh();

        }
        private void FormAddUsersManager_Load(object sender, EventArgs e)
        {
            timer1.Start();
            LoadDatagridviewData();
            resize_template_cards_panel();
        }
        private void loadFromState()
        {
            SourceSaveStateFormsVariable sourceSaveState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("FormUserManagerPrint");
           if (sourceSaveState == null)
                Frm_State=new Form_PrintUserManagerState();
           else
            Frm_State = JsonConvert.DeserializeObject<Form_PrintUserManagerState>(sourceSaveState.values.ToString());
            
            if (Frm_State == null )             
                Frm_State = new Form_PrintUserManagerState();

            Frm_State.is_add_batch_cards = false;
            Frm_State.is_add_batch_cards_to_Archive = false;
            Frm_State.is_Add_One_Card = false;
            txtNumberCard.Text = Frm_State.txtNumberCard;
            txt_StartCard.Text = Frm_State.txt_StartCard;
            txt_EndCard.Text = Frm_State.txt_EndCard;
            txt_ShardUser.Text = Frm_State.txt_ShardUser;
            txt_longUsers.Text = Frm_State.txt_longUsers;
            txt_longPassword.Text = Frm_State.txt_longPassword;

            cbox_UserPassword_Pattern.SelectedIndex = Frm_State.cbox_UserPassword_Pattern;
            cbox_User_NumberORcharcter.SelectedIndex = Frm_State.cbox_User_NumberORcharcter;
            cbox_Pass_NumberORcharcter.SelectedIndex = Frm_State.cbox_Pass_NumberORcharcter;

            checkBoxFirstUse.Check = Frm_State.checkBoxFirstUse;
            checkBoxSaveTo_PDF.Check = Frm_State.checkBoxSaveTo_PDF;
            checkBoxSaveTo_excel.Check = Frm_State.checkBoxSaveTo_excel;
            checkBoxOpenAfterPrint.Check = Frm_State.checkBoxOpenAfterPrint;
            checkBoxSaveTo_script_File.Check = Frm_State.checkBoxSaveTo_script_File;
            checkBox_With_Archive_uniqe.Check = Frm_State.checkBox_Create_without_Add_ToMicrotik;
            checkBoxSaveTo_text_File.Check = Frm_State.checkBoxSaveTo_text_File;
            checkBox_note.Check = Frm_State.checkBox_note;
            txt_note.Text = Frm_State.txt_note;
            

            //if (rj_Remember_user.Check)
            //    txtUser.Text = Frm_State.Mk_UserName;
            //if (rj_Remember_pass.Check)
            //    txtPassword.Text = Frm_State.mk_password;

            //txt_note.Text = Frm_State.note;

            //if (Frm_State.is_check_Port_api || Frm_State.is_check_Port_ssh)
            //{
            //    txtPort_api.Text = Frm_State.Mk_Port_api.ToString();
            //    rjBy_Port.Check = true;

            //}
            //rjRB_SSH.Checked = Frm_State.is_check_Port_ssh;

            //if (rjRB_SSH.Checked)
            //    txtPort_api.Text = Frm_State.Mk_Port_ssh.ToString();

            //rjDdiable_LoadSession.Check = Frm_State.Disable_Load_Session;
            //rjRunOffline.Check = Frm_State.LogIn_Without_mk;

            //List<Form_LoingState> frmUser = SqlDataAccess.GeUser_Login();
            //if (frmUser.Count > 0)
            //{
            //    dgvMicrotikSaved.Rows.Clear();
            //    dgvMicrotikSaved.DataSource = null;

            //    foreach (var itm in frmUser)
            //    {
            //        this.dgvMicrotikSaved.Rows.Add(itm.Mk_IP, itm.Mk_UserName, itm.note, itm.note);
            //    }
            //}

            //txtDomain.TextAlign = txtPassword.TextAlign = txtPortSSH.TextAlign = txtPort_api.TextAlign = txtUser.TextAlign = txt_IP_1.TextAlign = txt_IP_2.TextAlign = txt_IP_3.TextAlign = txt_IP_4.TextAlign = txt_note.TextAlign = HorizontalAlignment.Center;

            //rjComboBox1.DataSource = SqlDataAccess.GetRouters();
            //rjComboBox1.DisplayMember = "mk_sn";
            ////rjComboBox1
        }
        private void SaveFromState()
        { 
            Frm_State.txtNumberCard = txtNumberCard.Text;
            Frm_State.txt_StartCard = txt_StartCard.Text;
            Frm_State.txt_EndCard = txt_EndCard.Text;
            Frm_State.txt_ShardUser = txt_ShardUser.Text;

            Frm_State.txt_longUsers = txt_longUsers.Text;
            Frm_State.txt_longPassword = txt_longPassword.Text;

            Frm_State.cbox_UserPassword_Pattern = cbox_UserPassword_Pattern.SelectedIndex;
            Frm_State.cbox_User_NumberORcharcter = cbox_User_NumberORcharcter.SelectedIndex;
            Frm_State.cbox_Pass_NumberORcharcter = cbox_Pass_NumberORcharcter.SelectedIndex;

            Frm_State.checkBoxFirstUse = checkBoxFirstUse.Check;
            Frm_State.checkBoxSaveTo_PDF = checkBoxSaveTo_PDF.Check;
            Frm_State.checkBoxSaveTo_excel = checkBoxSaveTo_excel.Check;
            Frm_State.checkBoxOpenAfterPrint = checkBoxOpenAfterPrint.Check;
            Frm_State.checkBoxSaveTo_script_File = checkBoxSaveTo_script_File.Check;
            Frm_State.checkBox_Create_without_Add_ToMicrotik = checkBox_With_Archive_uniqe.Check;
            Frm_State.checkBoxSaveTo_text_File = checkBoxSaveTo_text_File.Check;
            Frm_State.checkBox_note = checkBox_note.Check;
            Frm_State.txt_note = txt_note.Text;


            string formSetting = JsonConvert.SerializeObject(Frm_State);
             Smart_DataAccess.Setting_SaveState_Forms_Variables("FormUserManagerPrint", "SaveFromState", formSetting);


            //if (rj_Remember_user.Check)
            //    txtUser.Text = Frm_State.Mk_UserName;
            //if (rj_Remember_pass.Check)
            //    txtPassword.Text = Frm_State.mk_password;

            //txt_note.Text = Frm_State.note;

            //if (Frm_State.is_check_Port_api || Frm_State.is_check_Port_ssh)
            //{
            //    txtPort_api.Text = Frm_State.Mk_Port_api.ToString();
            //    rjBy_Port.Check = true;

            //}
            //rjRB_SSH.Checked = Frm_State.is_check_Port_ssh;

            //if (rjRB_SSH.Checked)
            //    txtPort_api.Text = Frm_State.Mk_Port_ssh.ToString();

            //rjDdiable_LoadSession.Check = Frm_State.Disable_Load_Session;
            //rjRunOffline.Check = Frm_State.LogIn_Without_mk;

            //List<Form_LoingState> frmUser = SqlDataAccess.GeUser_Login();
            //if (frmUser.Count > 0)
            //{
            //    dgvMicrotikSaved.Rows.Clear();
            //    dgvMicrotikSaved.DataSource = null;

            //    foreach (var itm in frmUser)
            //    {
            //        this.dgvMicrotikSaved.Rows.Add(itm.Mk_IP, itm.Mk_UserName, itm.note, itm.note);
            //    }
            //}

            //txtDomain.TextAlign = txtPassword.TextAlign = txtPortSSH.TextAlign = txtPort_api.TextAlign = txtUser.TextAlign = txt_IP_1.TextAlign = txt_IP_2.TextAlign = txt_IP_3.TextAlign = txt_IP_4.TextAlign = txt_note.TextAlign = HorizontalAlignment.Center;

            //rjComboBox1.DataSource = SqlDataAccess.GetRouters();
            //rjComboBox1.DisplayMember = "mk_sn";
            ////rjComboBox1
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            pnl_left2.Height = pnl_Right2.Height + pnl_Right3.Height+pnl_Right3.Margin.Top;
            pnl_left2.Refresh();
            rjPanel4.Location = new System.Drawing.Point(rjPanel4.Location.X, rjPanel1.Location.Y);
            rjPanel4.Height = rjPanel1.Height;
            //resize_template_cards_panel();

            Get_TemplateCardsFromDB();
            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_CBox_Attribute();
            Get_CBox_group();
          
            isFirstLoadForm = false;
            firstLoad = false;

            loadFromState();
            //resize_template_cards_panel();
            try { CBox_TemplateCards.SelectedIndex = 1; } catch { }


        }
        public void LoadDatagridviewData()
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            List<NumberPrintCard> batch = new List<NumberPrintCard>();
            lock (Smart_DataAccess.Lock_object)
            {
                batch = smart_DataAccess.GetListAnyDB<NumberPrintCard>($"select * from NumberPrintCard where Server=0 and (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') order by Id DESC  LIMIT 4");
                //batch = smart_DataAccess.GetListAnyDB<BatchCard>($"select * from BatchCard where Server=0 and Rb='{Global_Variable.Mk_resources.RB_code}' order by Id DESC  LIMIT 4");

            }
            dgv.DataSource = batch;
            //rjDataGridView1.DataSource = (from t in batch select t).Take(4).ToList();
            try
            {
                foreach (DataGridViewColumn column in dgv.Columns)
                    column.Visible = false;
 
                dgv.Columns["BatchNumber"].Visible = true;
                dgv.Columns["NumberPrint"].Visible = false;
                dgv.Columns["Count"].Visible = true;
                dgv.Columns["ProfileName"].Visible = true;
                dgv.Columns["AddedDate"].Visible = true;

                dgv.Columns["BatchNumber"].DisplayIndex = 0;
                dgv.Columns["NumberPrint"].DisplayIndex = 1;
                dgv.Columns["Count"].DisplayIndex = 2;
                dgv.Columns["ProfileName"].DisplayIndex = 3;
                dgv.Columns["AddedDate"].DisplayIndex = 4;
            }
            catch { }
            
        }
      


        private void groupBox2_Resize(object sender, EventArgs e)
        {
            //set_loaction_left_panel();

        }

        private void groupBox2_LocationChanged(object sender, EventArgs e)
        {
            //set_loaction_left_panel();

        }

        private void pnlClientArea_SizeChanged(object sender, EventArgs e)
        {
            set_loaction_left_panel();

        }

        private void pnl_Right_LocationChanged(object sender, EventArgs e)
        {
            //set_loaction_left_panel();
        }

        private void pnl_left_Resize(object sender, EventArgs e)
        {
            //set_loaction_left_panel();
        }

        private void FormAddUsersManager_Resize(object sender, EventArgs e)
        {
            //set_loaction_left_panel();
        }

        private void pnl_Right_ParentChanged(object sender, EventArgs e)
        {
            //set_loaction_left_panel();
        }

        //private void FormAddUsersManager_SizeChanged(object sender, EventArgs e)
        //{
        //    set_loaction_left_panel();
        //}

        private void FormAddUsersManager_SizeChanged_1(object sender, EventArgs e)
        {
           
            //MessageBox.Show(WindowState.ToString());
            if (firstLoad )
                return; 
            resize_template_cards_panel();

            //panel_PreviewTempateCards.Height = panel_PreviewTempateCards.Height - 1;
        }
        private void resize_template_cards_panel()
        {
            if (firstLoad)
                return;
            //label1.Text=pnlClientArea.Size.ToString();
            //label2.Text= panel_PreviewTempateCards.Size.ToString();



            //if (this.Width > 1200)
            //{
            //    rjDataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            //}
            //else
            //{
            //    rjDataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            //}




            //if (pnlClientArea.Height > 680)
            //{
            //    pnl_left2.Height = pnlClientArea.Height - pnl_left.Height - 2;
            //    pnl_left2.Width = pnl_left.Width;
            //    //pnl_Right2.Height = 240;
            //    //pnl_Right2.Refresh();
            //    pnl_Right3.Height = pnlClientArea.Height - pnl_Right.Height - pnl_Right.Margin.Top - pnl_Right2.Height - pnl_Right2.Margin.Top - 2;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 4);
            //}
            //else
            //{
            //    pnl_left2.Height = 294;
            //    pnl_left2.Width = pnl_left.Width;
            //    //pnl_Right2.Height = 183;
            //    pnl_Right3.Height = 105;
            //    //pnl_Right3.Location = new Point(pnl_Right3.Location.X, pnl_Right2.Location.Y + pnl_Right2.Height + 2);
            //}

            panel_PreviewTempateCards.Refresh();
            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel1.Refresh();
            rjPanel4.Refresh();

            tableLayoutPanel1.Refresh();
            panel_PreviewTempateCards.Refresh();

            //=======================================================================
            Font lbl_Title2_font = lbl_Title2.Font;
            Bitmap b = new Bitmap(2200, 2200);
            Graphics g = Graphics.FromImage(b);
            SizeF sizeOfString = new SizeF();
            sizeOfString = g.MeasureString(lbl_Title2.Text, lbl_Title2_font);
         
            //=========================================================================
            Font lbl_Title1_font = lbl_Title1.Font;
            Bitmap b1 = new Bitmap(2200, 2200);
            Graphics g1 = Graphics.FromImage(b);
            SizeF sizeOfString1 = new SizeF();
            sizeOfString1 = g1.MeasureString(lbl_Title1.Text, lbl_Title1_font);
           
            //==========================================================================

            lbl_Title2.Location = new Point((int)((pnl_left2.Width / 2) - sizeOfString.Width / 2), 6);
            lbl_Title1.Location = new Point((int)((pnl_left.Width / 2) - sizeOfString1.Width / 2), 6);

            if (pnlClientArea.Width > 1000 && pnlClientArea.Width < 1250)
            {
                //this.WindowState == FormWindowState.Normal
                //MessageBox.Show(WindowState.ToString());

                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width) - 10;
                panel_PreviewTempateCards.Location = new Point(8, 35);
            }
            else if (pnlClientArea.Width > 1250)
            {
                //pnl_left.Refresh();
                //panel_PreviewTempateCards.Width = pnl_left2.Width / 2 ;
                //panel_PreviewTempateCards.Width = pnl_left2.Width / 2 + 150;
                //panel_PreviewTempateCards.Location = new Point((pnl_left2.Width / 2) - panel_PreviewTempateCards.Width / 2, 43);

                pnl_left.Refresh();
                panel_PreviewTempateCards.Width = pnl_left2.Width / 2 + 150;
                panel_PreviewTempateCards.Location = new Point((pnl_left2.Width / 2) - panel_PreviewTempateCards.Width / 2, 35);

            }
            else
            {
                pnl_left.Refresh();
                //panel_PreviewTempateCards.Width =  (pnl_left2.Width - panel_PreviewTempateCards.Width - 10);
                panel_PreviewTempateCards.Width = panel_PreviewTempateCards.Width + (pnl_left2.Width - panel_PreviewTempateCards.Width - 10);
                panel_PreviewTempateCards.Location = new Point(8, 35);
            }
            panel_PreviewTempateCards.Refresh();

            pnl_left.Refresh();
            pnl_left2.Refresh();
            pnl_Right.Refresh();
            pnl_Right2.Refresh();
            pnl_Right3.Refresh();
            rjPanel4.Refresh();
            rjPanel1.Refresh();
            panel_PreviewTempateCards.Refresh();

        }

        UserManagerProcess um;
        bool startPrint=false;
        string lastFile = "";
        string lastFolder = "";

        [Obsolete]
        private void btnAdd_Click(object sender, EventArgs e)
        {
            Frm_State.is_add_batch_cards = false;
            Frm_State.is_Add_One_Card = false;

            if (radio_one_user.Checked)
            {
                btn_add_One_Click(sender,e);
                return;
            }
            if (radio_fast_print.Checked)
            {
                btnAdd_Fast_Click(sender, e);
                return;
            }
            um = new UserManagerProcess();
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }

            if (um.AddCardMk_Usermanager(this, Frm_State))
            {
                Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                //startPrint=false ;
                //RJMessageBox.Show("okkk");
                SaveFromState();
            }
            //else
            //{
            //    startPrint = false;
            //}
        }

        private void CBox_TemplateCards_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) { panel_PreviewTempateCards.Controls.Clear(); return; };

            //panel_PreviewTempateCards.Controls.Clear();
            //UC_PreviewTemplateCards uC = new UC_PreviewTemplateCards(CBox_TemplateCards.Text);
            //panel_PreviewTempateCards.Controls.Add(uC);
            //uC.Show();
            //uC.Dock = DockStyle.Fill;
            //return;

            try
            {
                panel_PreviewTempateCards.Controls.Clear();
                panel_PreviewTempateCards.Refresh();
                SourceCardsTemplate sorceTemplates = SqlDataAccess.Get_template_cards_By_id(CBox_TemplateCards.SelectedValue.ToString());
               
                if (sorceTemplates.type == "design")
                {
                    CardsTemplate card = JsonConvert.DeserializeObject<CardsTemplate>(sorceTemplates.values);
                    if (card != null)
                    {
                        panel_PreviewTempateCards.Height = 140;

                        
                        UC_PreviewTemplateCards tuC = new UC_PreviewTemplateCards(CBox_TemplateCards.SelectedValue.ToString(), CBox_Profile.SelectedValue.ToString(), CBox_SellingPoint.SelectedValue.ToString());
                        panel_PreviewTempateCards.Controls.Add(tuC);
                        tuC.Dock = DockStyle.Fill;
                        tuC.Show();
                        tuC.Refresh();
                        panel_PreviewTempateCards.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;
                        if (panel_PreviewTempateCards.Height >= 140)
                            panel_PreviewTempateCards.Height = 141;

                    }
                }

                else if (sorceTemplates.type == "table_Desigen1")
                {
                    panel_PreviewTempateCards.Height = 195;
                    panel_PreviewTempateCards.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right | AnchorStyles.Bottom;


                    UC_PreviewTemplateCardsTable tuC = new UC_PreviewTemplateCardsTable(CBox_TemplateCards.SelectedValue.ToString(), CBox_Profile.SelectedValue.ToString(), CBox_SellingPoint.SelectedValue.ToString());
                    
                    //Clss_InfoPrint clss_InfoPrint = new Clss_InfoPrint();
                   //UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == CBox_Profile.SelectedValue.ToString());
                   
                    //if (profile == null)
                    //    return;

                    //clss_InfoPrint.profile = profile;
                   
                    //UC_PreviewTemplateCardsTable tuC = new UC_PreviewTemplateCardsTable(clss_InfoPrint);
                    panel_PreviewTempateCards.Controls.Add(tuC);
                    tuC.Show();
                    tuC.Dock = DockStyle.Fill;


                    if (pnlClientArea.Height >= 600)
                        panel_PreviewTempateCards.Height = 240;
                    

                    //if (pnlClientArea.Height >= 600)
                    //    //panel_PreviewTempateCards.Height = 225;
                    //    panel_PreviewTempateCards.Height = pnl_left2.Height - panel2.Height - panel2.Margin.Bottom;



                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            panel_PreviewTempateCards.Refresh();
            pnl_left.Refresh();
        }
        private void set_BackroundFromPath(string _path)
        {
                try
                {
                //pictureBox1.Image = null;

                if (_path == "")
                        return;
                    string sourcePath = Directory.GetCurrentDirectory() + "\\" + "tempCards\\cardsBack";
                    string SourcePath_File = System.IO.Path.Combine(sourcePath, _path);
                    FileInfo file = new FileInfo(SourcePath_File);
                    double sizeInBytes = file.Length;
                    try
                    {
                        Bitmap img = new Bitmap(SourcePath_File);
                        //pictureBox1.Image = System.Drawing.Image.FromFile(SourcePath_File);
                    }
                    catch
                    {
                        //pictureBox1.Image = null;
                    }
                }
                catch
                {
                    //pictureBox1.Image = null;
                }
            
        }

        private void Get_TemplateCardsFromDB()
        {
            try
            {
                List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                if (p.Count == 0)
                {
                    SourceCardsTemplate sourceCardsTemplate = new SourceCardsTemplate();
                    if (sourceCardsTemplate.CreateDefaultTemplate())
                        p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
                }

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "0");
                //p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

                if (p.Count > 0)
                    foreach (SourceCardsTemplate s in p)
                        comboSource.Add(s.name, s.id.ToString());

                CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
                CBox_TemplateCards.ValueMember = "Value";
                CBox_TemplateCards.DisplayMember = "Key";
            }
            catch { }


            //try
            //{
            //    List<SourceCardsTemplate> p = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);

            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("", "0");
            //    if (p.Count > 0)
            //    foreach (SourceCardsTemplate s in p)
            //        comboSource.Add(s.name, s.id.ToString());

            //    CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
            //    CBox_TemplateCards.ValueMember = "Value";
            //    CBox_TemplateCards.DisplayMember = "Key";
            //}
            //catch { }

            //try
            //{
            //    CBox_TemplateCards.Items.Clear();
            //}
            //catch { }
            //try
            //{
            //    List<SourceCardsTemplate> sourceCardsTemplate = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    CBox_TemplateCards.DataSource = sourceCardsTemplate;
            //    CBox_TemplateCards.DisplayMember = "name";
            //    CBox_TemplateCards.ValueMember = "id";
            //    CBox_TemplateCards.SelectedIndex = -1;

            //}
            //catch { }

            //try
            //{
            //    List<SourceCardsTemplate> sp = SqlDataAccess.Get_All_SourceCardsTemplate("all", true);
            //    if (sp.Count == 0)
            //        createDefaultTemplate();
            //    else
            //    {
            //        Dictionary<string, SourceCardsTemplate> comboSource = new Dictionary<string, SourceCardsTemplate>();
            //        foreach (SourceCardsTemplate s in sp)
            //            comboSource.Add(s.name, s);

            //        CBox_TemplateCards.DataSource = new BindingSource(comboSource, null);
            //        CBox_TemplateCards.DisplayMember = "Key";
            //        CBox_TemplateCards.ValueMember = "Value";
            //    }
            //}
            //catch { }


            //try
            //{
            //    if (dt_templateCards.Rows.Count <= 0)
            //    {
            //        CreateDefultTemplate();
            //        //SetValuToCardToGraphics();
            //    }
            //}
            //catch { }

        }
      
        private void CBox_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            //UserManager_Profile_UserManager prf = CBox_Profile.SelectedValue as UserManager_Profile_UserManager;
            //if (prf == null)
            //    return;
            //string profileName = prf.Name;
            string profileName = CBox_Profile.SelectedValue.ToString();

            List<SourceCardsTemplate> sorceTemplates = SqlDataAccess.Get_All_SourceCardsTemplate("", true);
           
            foreach (SourceCardsTemplate s in sorceTemplates)
            { 
                if (s.type == "design")
                {
                    CardsTemplate card = JsonConvert.DeserializeObject<CardsTemplate>(s.values);
                    if (card != null)
                    {
                        if (card.setingCard.proile_link == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                //if (s.name == CBox_TemplateCards.Items[i].ToString())
                                //{
                                //    CBox_TemplateCards.SelectedIndex = i;
                                //    return;
                                //}
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                            }
                        }
                    }
                }
                else if (s.type == "table_Desigen1")
                {
                    CardsTableDesg1 card = JsonConvert.DeserializeObject<CardsTableDesg1>(s.values);
                    if (card != null)
                    {
                        if (card.setingCard.proile_link == profileName)
                        {
                            for (int i = 0; i < CBox_TemplateCards.Items.Count; ++i)
                            {
                                var selectedItem = (KeyValuePair<string, string>)CBox_TemplateCards.Items[i];
                                string text = selectedItem.Key;
                                string Value = selectedItem.Value;
                                if (s.name == text)
                                {
                                    CBox_TemplateCards.SelectedIndex = i;
                                    return;
                                }
                                //DataRowView itemhData = CBox_TemplateCards.Items[i] as DataRowView;

                                //string Pname = "";
                                //if (itemhData != null)
                                //    Pname = (itemhData.Row["name"].ToString());
                                //if (profileName == Pname)
                                //{
                                //    CBox_TemplateCards.SelectedIndex = i;
                                //    return;
                                //}
                            }
                        }
                    }
                }

                //else
                //{
                //    try
                //    {
                //        if(CBox_TemplateCards.Items.Count>=1)
                //            CBox_TemplateCards.SelectedIndex = 1;
                //    }
                //    catch { }
                //}
                
            }

            //if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) return;

            CBox_TemplateCards.SelectedIndex = 0;
            CBox_TemplateCards.Text = "";

            //if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0) return;

            try { CBox_TemplateCards.SelectedIndex = 1; } catch { }


            //try { CBox_TemplateCards.SelectedIndex = 1; } catch { }
        }
        

        private void pnl_left2_Resize(object sender, EventArgs e)
        {
            panel_PreviewTempateCards.Refresh();

        }

        private void pnlClientArea_Resize_1(object sender, EventArgs e)
        {
            //resize_template_cards_panel();

            //rjPanel1.Refresh();
            //panel_PreviewTempateCards.Anchor = AnchorStyles.Top | AnchorStyles.Left | AnchorStyles.Right;

            //panel_PreviewTempateCards.Refresh();
        }

        private void btn_AddProfile_Click(object sender, EventArgs e)
        {
           FormProfileUserManager formProfileUserManager = new FormProfileUserManager();
            formProfileUserManager.ShowDialog();
            Get_Cbox_Profile();
            CBox_Profile.SelectedIndex = CBox_Profile.Items.Count-1;
        }

        private void btn_Add_template_Click(object sender, EventArgs e)
        {
            try
            {
                Form_CardsDesigen_Graghics form_CardsDesigen_Graghics = new Form_CardsDesigen_Graghics();
                form_CardsDesigen_Graghics.ShowDialog();
                Get_TemplateCardsFromDB();
                CBox_TemplateCards.SelectedIndex = CBox_TemplateCards.Items.Count - 1;
            }
            catch { }
        }
        private void btnAddSellingPoint_Click(object sender, EventArgs e)
        {
            try
            {
                Form_SellingPoint form_SellingPoint = new Form_SellingPoint();
                form_SellingPoint.ShowDialog();

                Smart_DataAccess dataAccess = new Smart_DataAccess();

                CBox_SellingPoint = dataAccess.Get_ComboBox_SellingPoint();
                CBox_SellingPoint.Refresh();
                CBox_SellingPoint.DataSource = dataAccess.Get_ComboBox_SellingPoint().DataSource;

                CBox_SellingPoint.SelectedIndex = CBox_SellingPoint.Items.Count - 1;
                //SellingPints sp = (SellingPints)CBox_SellingPoint.SelectedValue;
            }
            catch { }
        }

        private void btn_OpenFolderDefault_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("explorer.exe", (Frm_State.PathFolderPrint));
                //System.Diagnostics.Process.Start("explorer.exe", Path.GetDirectoryName(Frm_State.PathFolderPrint));
            }
            catch (Exception ex) { RJMessageBox.Show("btn_OpenFolderDefault_Click \n" + ex.Message.ToString()); }
        }

        private void btn_OpenLastFile_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start(Frm_State.path_saved_file);
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }

        }

        [Obsolete]
        private void btnAdd_Fast_Click(object sender, EventArgs e)
        {
            Frm_State.is_add_batch_cards = true;
            um = new UserManagerProcess();
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }


            if (!check_port_mk_befor())
            {
                MessageBox.Show(" ssh خطا بالاتصال مع بورت");
                return;
            }
            if (um.AddCardMk_Usermanager(this, Frm_State))
            {
                Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                Frm_State.is_add_batch_cards = false;

                //startPrint=false ;
                SaveFromState();
            }

        }
        public string ssh_last_state = "false";
        public string id_ssh = "";
        public int ssh_port = 22;
        public DataTable dt_service = null;


        [Obsolete]
        public bool check_port_mk_befor()
        {
            bool result = false;
            Mk_DataAccess DA2 = new Mk_DataAccess();
            dt_service = DA2.GetService();
            try
            {
                DataRow[] foundRows = dt_service.Select("[name] = " + "'ssh'");
                ssh_last_state = foundRows[0]["disabled"].ToString();
                ssh_port = Convert.ToInt32(foundRows[0]["port"].ToString());
                Global_Variable.Mk_Login_data.Mk_Port_ssh = ssh_port;
                id_ssh = foundRows[0]["id"].ToString();
                result = true;
            }
            catch { }

            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "false");
                result = true;
            }
            return result;
        }

        [Obsolete]
        public void rest_port_mk_after()
        {
            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "true");
            }
        }

        [Obsolete]
        public void remove_file_import_after_print(string fileName)
        {

            Mk_DataAccess da = new Mk_DataAccess();
            string result2 = da.remove_file_import_after_print(fileName);

        }

        private void checkBox_RegisterAsBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAsBatch.Checked == true)
                checkBox_RegisterAs_LastBatch.Checked = false;
            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAsBatch.Checked = true;
        }

        private void checkBox_RegisterAs_LastBatch_CheckedChanged(object sender, EventArgs e)
        {
            if (checkBox_RegisterAs_LastBatch.Checked == true)
                checkBox_RegisterAsBatch.Checked = false;

            else if (checkBox_RegisterAsBatch.Checked == false && checkBox_RegisterAs_LastBatch.Check == false)
                checkBox_RegisterAs_LastBatch.Checked = true;
        }

        [Obsolete]
        private void btn_add_One_Click(object sender, EventArgs e)
        {
            if (CBox_TemplateCards.SelectedIndex == -1 || CBox_TemplateCards.SelectedIndex == 0)
            {
                RJMessageBox.Show("يجب اختيار قالب للطباعة");
                return;
            }

            Frm_State.is_Add_One_Card = true;
            try
            {
                int Userlength = 8;
                int Passwordlength = 6;
                int Usermode = 0;
                int Passwordmode = 0;
                if(utils.check_Filed_Intiger_with_Msg(txt_longUsers.Text))
                {
                    Userlength = Convert.ToInt32( txt_longUsers.Text);
                }
                if (utils.check_Filed_Intiger_with_Msg(txt_longPassword.Text))
                {
                    Passwordmode = Convert.ToInt32(txt_longPassword.Text);
                }
                if (string.IsNullOrEmpty(cbox_User_NumberORcharcter.Text) || cbox_User_NumberORcharcter.SelectedIndex==-1)
                {
                    RJMessageBox.Show("حدد نمط اسم المستخدم");
                    return;
                }
                if (string.IsNullOrEmpty(cbox_Pass_NumberORcharcter.Text) || cbox_Pass_NumberORcharcter.SelectedIndex == -1)
                {
                    RJMessageBox.Show("حدد نمط  كلمة المرور  ");
                    return;
                }

                Usermode = cbox_User_NumberORcharcter.SelectedIndex;
                Passwordmode = cbox_Pass_NumberORcharcter.SelectedIndex;

                frm_Input_Dailog_New_User frm = new frm_Input_Dailog_New_User(Userlength,Passwordlength,Usermode,Passwordmode); 
                frm.ShowDialog();
                if (frm.add)
                {
                    um = new UserManagerProcess();
                    if (um.AddCardMk_Usermanager(this, Frm_State, frm.txt_Name.Text, frm.txt_password.Text))
                    {
                        Frm_State.path_saved_file = um.Frm_State.path_saved_file;
                        Frm_State.PathFolderPrint = um.Frm_State.PathFolderPrint;
                        Frm_State.is_Add_One_Card = false;

                        //startPrint=false ;
                        SaveFromState();
                    }
                }
            }
            catch (Exception ex) {   MessageBox.Show(ex.Message); }
            return;
           
        }

        private void CBox_SellingPoint_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            try
            {
                Smart_DataAccess dba = new Smart_DataAccess();
                SellingPoint sellingPoint = dba.Get_SellingPoint_Code(CBox_SellingPoint.SelectedValue.ToString());

                if(sellingPoint != null)
                {
                    txt_StartCard.Text = sellingPoint.Prefixes.Trim();
                    txt_EndCard.Text= sellingPoint.Suffixes.Trim();
                }
            }
            catch { }
        }

        [Obsolete]
        private void rjButton7_Click(object sender, EventArgs e)
        {

            FormDashboard formDashBoard = (FormDashboard)RJMainForm.listChildForms.Find(x => x.Name == "FormDashboard");
            if(formDashBoard!=null)
            {
                Thread thread = new Thread(formDashBoard.LoadAll);
                thread.Start();
            }
            Form_Cards_UserManager formcards = (Form_Cards_UserManager)RJMainForm.listChildForms.Find(x => x.Name == "Form_Cards_UserManager");
            if(formcards!=null)
            {
                if (formcards.formAllCardsUserManager != null)
                    formcards.formAllCardsUserManager.loadData();

                if (formcards.formAllCardsUserManager_RB_Archive != null)
                    formcards.formAllCardsUserManager_RB_Archive.loadData();

                if (formcards.formAll_From_Finsh_Cards != null)
                    formcards.formAll_From_Finsh_Cards.loadData();

                if (formcards.formAllBatchsCards != null)
                    formcards.formAllBatchsCards.LoadDataGridviewData();

                if (formcards.formAll_From_Session_Cards != null)
                    formcards.formAll_From_Session_Cards.LoadDataGridviewData();
            }

             
           //foreach (var frm in RJMainForm.listChildForms)
           // {
           //     string name = frm.Name;
               
           // }

        }

        private void panel_PreviewTempateCards_Paint(object sender, PaintEventArgs e)
        {

        }

        private void btn_add_One_Click_1(object sender, EventArgs e)
        {
            FormAddUsersManager2 formAddUsersManager2 = new FormAddUsersManager2();
            formAddUsersManager2.ShowDialog();
        }
    }
}
