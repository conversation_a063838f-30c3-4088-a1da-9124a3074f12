﻿using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class frm_Input_Dailog_New_Template : RJForms.RJChildForm
    {
        public bool add=false;
        public frm_Input_Dailog_New_Template()
        {
            InitializeComponent();
            this.Text = "ادخل اسم جديد للقالب";
            if (UIAppearance.Language_ar==false)
                this.Text = "enter new template";

            txt_Name.Focus();

            utils utils = new utils();
            utils.Control_textSize1(pnlClientArea);

        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (txt_Name.Text.Trim() == "")
            {
                RJMessageBox.Show("ادخل اسم للقالب");
                return;
            }
            add = true;
            this.Close();
        }

        private void txt_profileName_MouseDown(object sender, MouseEventArgs e)
        {

        }

        private void btnSave_KeyDown(object sender, KeyEventArgs e)
        {
            try
            {
                if (e.KeyCode == Keys.Enter)
                {
                    if (txt_Name.Text == "")
                    {
                        RJMessageBox.Show("ادخل اسم للقالب");
                        return;
                    }
                    add= true;
                    this.Close();
                }
            }
            catch { }
        }
    }
}
