﻿using Newtonsoft.Json.Converters;
using SmartCreator.Data;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data.SQLite;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using tik4net;
using static System.Net.Mime.MediaTypeNames;
using Dapper;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using System.Windows.Media.Animation;
using System.IO;
using SmartCreator.Models.API;

namespace SmartCreator.Models.hotspot
{
    public class SourceCardsHotspot
    {
    }
    public class SourceCardsHotspot_fromMK
    {
        #region fileds
        public string id { get; set; }
        public string userName { get; set; }
        public string password { get; set; } = "";
        public string profileHotspot { get; set; }
        public double limitUptime { get; set; } = 0;
        public double uptime { get; set; } = 0;
        public double bytesOut { get; set; } = 0;
        public double bytesIn { get; set; } = 0;
        public double limitbytestotal { get; set; } = 0;
        public string disabled { get; set; }= "0";
        public string comment { get; set; } //comment
        public string email { get; set; }
        public string macAddress { get; set; }
        public string server { get; set; } = "all";
        public string address { get; set; } 


        #endregion

        [Obsolete]
        public static List<SourceCardsHotspot_fromMK> Get_HS_user(string name = "", bool searchByID = true, bool is_syn = false)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsHotspot_fromMK> users = null;
            Global_Variable.Source_Users_Hotspot_ForPrint = new HashSet<string>();
            try
            {
                string code = Properties.Settings.Default.hotspot_print;
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    users = new List<SourceCardsHotspot_fromMK>();

                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code);
                    else
                    {
                        if (searchByID)
                            loadCmd = connection.CreateCommandAndParameters(code, ".id", name);
                        else
                            loadCmd = connection.CreateCommandAndParameters(code, "name", name);
                    }




                    //var loadCmd = connection.CreateCommandAndParameters(code, "nextid", "*956D");
                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceCardsHotspot_fromMK card = new SourceCardsHotspot_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", "");
                            card.userName = item.GetResponseFieldOrDefault("name", "");
                            card.password = item.GetResponseFieldOrDefault("password", "");
                            card.profileHotspot = item.GetResponseFieldOrDefault("profile", "");
                            card.limitUptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("limit-uptime", "0"));
                            card.uptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime", "0"));
                            card.bytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-out", "0"));
                            card.bytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-in", "0"));
                            card.limitbytestotal = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-total", "0"));
                            card.disabled = item.GetResponseFieldOrDefault("disabled", "false");
                            card.email = item.GetResponseFieldOrDefault("email", "");
                            card.comment = item.GetResponseFieldOrDefault("comment", "");
                            card.macAddress = item.GetResponseFieldOrDefault("mac-address", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.server = ((item.GetResponseFieldOrDefault("server", "all")));
                            card.address = ((item.GetResponseFieldOrDefault("address", null)));
                            users.Add(card);
                            Global_Variable.Source_Users_Hotspot_ForPrint.Add(card.userName);
                        }
                        catch { }
                    }
                    Global_Variable.Source_Users_Hotspot_ForPrint.Remove("default-trial");
                    if(name=="")
                        users.RemoveAt(0);

                    stopwatch.Stop();
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });

                    string ss =
                      (
                        (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                         " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                        " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                      );

                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب كروت الهوتسبوت", "");


                    if (users.Count > 0)
                    {
                        if (is_syn)
                        {
                            SourceCardsHotspot_fromMK u = new SourceCardsHotspot_fromMK();
                            u.Syn_HS_Users_to_LocalDB(users);
                        }
                    }

                    return users;
                }

            }
            catch (Exception ex)
            {
                
                    //MessageBox.Show("لم يتم جلب كروت الهوتسبوت قم بتحديث الكروت من عمليات الكروت\n",ex.Message);
            }
            return users;

        }

        [Obsolete]
        public static List<HsSession> Get_HS_Session(string name = "", bool searchByID = true, bool is_syn = false,string username=null)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            List<HsSession> Sessions = new List<HsSession>();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/file/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                   
                    
                    ITikCommand loadCmd;
                    if (name == "")
                        loadCmd = connection.CreateCommandAndParameters(code, "type", ".smart file");
                    else
                    {
                        if (searchByID)
                            loadCmd = connection.CreateCommandAndParameters(code, ".id", name, "type", ".smart file");
                        else
                            loadCmd = connection.CreateCommandAndParameters(code, "name", $"SmartValidity/Sessions/{username}.smart");
                    }
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        try
                        {
                            
                            
                            string contents = item.GetResponseFieldOrDefault("contents", "");
                            string UserName = item.GetResponseFieldOrDefault("name", "");
                            //$uptime."!&".$"bytes-in"."!&".$"bytes-out"."!&".$"bytes-total"."!&".$interface."!&".$address."!&".$"mac-address"."!&"."$user"."!&".$TillTime); 
                            //$uptime."|".$"bytes-in"."|".$"bytes-out"."|".$interface."|".$address."|".$"mac-address"."|"."TillTime"); 
                            string[] Files = contents.Split(new string[] { "\n" }, StringSplitOptions.None);
                            for (int i = 0; i < Files.Length; i++)
                            {
                                try
                                {
                                    string[] Res = Files[i].Split(new string[] { "|" }, StringSplitOptions.None);
                                    if (Res.Length < 6)
                                        continue;
                                    HsSession card = new HsSession();
                                    card.contents = Files[i];
                                    card.UserName = Path.GetFileNameWithoutExtension(UserName);
                                    try { card.UpTime = Convert.ToInt64(Res[0]); } catch { }
                                    try{ card.BytesUpload = Convert.ToInt64(Res[1]);} catch { }
                                    try{card.BytesDownload = Convert.ToInt64(Res[2]);} catch { }
                                  
                                    try{card.NasPortId = (Res[3]);} catch { }
                                    try{card.IpUser = (Res[4]);} catch { }
                                    try{card.CallingStationId = (Res[5]);} catch { }
                                    //try{card.UserName = (Res[6]);} catch { }

                                    DateTime? date = !string.IsNullOrEmpty(Res[6]) ? utils.String_To_Datetime_By_V_MK(Res[6]) : null;
                                    try{card.TillTime = date;} catch { }
                                    try{card.FromTime = date.Value.AddSeconds(-card.UpTime); } catch { }
                                    try{ card.TillTime_inSecond = utils.StringDatetimeToUnixTimeStamp(Res[6]); } catch { }
                                    Sessions.Add(card);
                                }
                                catch(Exception ex) { /*MessageBox.Show(ex.Message);*/ }
                            }
                        }
                        catch { }
                    }
                   
                    stopwatch.Stop();
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    string ss =
                     (
                       (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString()) +
                        " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                       " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString())
                     );

                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") مدة جلب جلسات الهوتسبوت", "");

                    if (Sessions.Count > 0)
                    {
                        bool WithremoveSession=true;
                        //if(name !="" )
                        //    WithremoveSession = true;
                        if (is_syn)
                        {
                            HsSession u = new HsSession();
                            u.Syn_HS_Session(Sessions,WithremoveSession,username);
                        }
                    }

                    return Sessions;
                }

            }
            catch (Exception ex)
            {

                //MessageBox.Show("لم يتم جلب كروت الهوتسبوت قم بتحديث الكروت من عمليات الكروت\n",ex.Message);
            }
            return Sessions;

        }


        [Obsolete]
        public bool Remove_files_Session(bool WithremoveSession,string username=null)
        {
            bool status = false;
            if (WithremoveSession == true && username != null)
            {

                try
                {
                    string script = $"/file remove [find where name=SmartValidity/Sessions/{username}.smart];\r\n";
                    Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                    status = true;
                }
                catch { }
            }
            else if (WithremoveSession)
            {
                try
                {
                    string script = "/file remove [find where type=\".smart file\"];\r\n";
                    Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                    status = true;

                }
                catch { }
            }
            return status;

        }




        string profil = "";
        int smartValidatiy_Add = 0;
        int day = 0;
        float price = 0;
        string sp = null;
        int? numPrint = 0;
        int? BatchId = 0;
        Int32 datetimePrint1 = 0;
        DateTime? datetimePrint =null;
        int byDayOrHour = 0;
        int first_mac = 0;
        int timeSave = 0;
        int sizeSave = 0;
        int sessionSave = 0;
        string @smart = "";
        string descr = "";

        DateTime? firstlogin=null;

        public void Syn_HS_Users_to_LocalDB(List<SourceCardsHotspot_fromMK> users = null)
        {
            try
            {
                bool by_search = true;
                DateTime date_added_Localdb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now);

                //double date_added_Localdb = utils.DateTimeToUnixTimeStamp(DateTime.Now);
                List<SellingPoint> SP = new List<SellingPoint>();
                //List<UserManager_Profile_UserManager> profile = Global_Variable.UM_Profile;

                if (users == null)
                {
                    users = Global_Variable.Source_Users_HotSpot;
                    by_search = false;
                }

                //# profil'day'price'sp'numPrint'datetimePrint'byDayOrHour'mac'timeSave'sizeSave'sessionSave'@smart.befor
               

                var HS_Users = (from u in users.AsEnumerable()
                                select new HSUser
                                {
                                    IdHX = u.id,
                                    SN = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                    Sn_Name = Int32.Parse(u.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber).ToString() + "-" + u.userName,
                                    UserName = u.userName,
                                    Password = u.password,
                                    ProfileHotspot = u.profileHotspot,
                                    LimitUptime = ((long)u.limitUptime),
                                    UptimeLimit = ((long)u.limitUptime),
                                    
                                    Disabled = Convert.ToInt32(Convert.ToBoolean(u.disabled)),
                                    Comment = u.comment,
                                    Email = u.email,
                                    CallerMac = u.macAddress,
                                    //address = u.address,
                                    Server = u.server,
                                    UptimeUsed = ((long)u.uptime),
                                    DownloadUsed = ((long)u.bytesOut),
                                    //bytesOut = (u.bytesOut),
                                    UploadUsed = ((long)u.bytesIn),
                                    //bytesIn = (u.bytesIn),
                                    Limitbytestotal = ((long)u.limitbytestotal),
                                    TransferLimit = ((long)u.limitbytestotal),
                                  
                                    //firstUse = string.IsNullOrEmpty(u.comment) ? 0 : Get_First_login_From_Comment(u.comment),
                                    AddedDb = date_added_Localdb,
                                    LastSynDb = date_added_Localdb,
                                    DeleteFromServer = 0,
                                    Status = Get_Status_Cards(u),
                                    MkId=Global_Variable.Mk_resources.RB_SN,

                                    CountProfile = 1,
                                    //smartValidatiy_Add = smartValidatiy_Add,
                                    //actualProfileName = u.profileHotspot,
                                    //actualLimTransfer = u.actualProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.transferLimit).LastOrDefault(),
                                    //actualLimUptime = u.actualProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.uptimeLimit).LastOrDefault(),

                                    //profileTillTime = 0,
                                    //profileTimeLeft = u.actualProfileName == null ? 0 : (((from v in profile.AsEnumerable() where (v.Name == u.actualProfileName) select v.uptimeLimit).LastOrDefault()) - (utils.GetTimeCard_InSeconds(u.uptimeUsed))),

                                }).ToList();

                Add_HSUser_toDB(HS_Users);
                     Sql_DataAccess LDB_DA =  new Sql_DataAccess();

                List<HSUser> sourceCardsUsers = LDB_DA.Get_Not_Delet_fromServer<HSUser>("HSUser");

                
                //List<SourceCardsHotspot_fromDB> sourceCardsUsers = Get_Hotspot_NotDeleteFromServer();

                // set Delet_fromServer=1 
                if (by_search == false)
                    LDB_DA.Set_Delet_fromServer_As_disable<UmUser>("HSUser");

                if ((sourceCardsUsers != null || sourceCardsUsers.Count == 0 ) && HS_Users.Count>0)
                {
                    LDB_DA.Set_NotDeletFromServer<HSUser>("HSUser", HS_Users);
                }

                    //// inner join  becaus  عشان نظمن ان كل عنصر معه الايدي حق قاعدة البيانات تبعه
                    var um = (from user in HS_Users
                          join dbUser in sourceCardsUsers on user.Sn_Name equals dbUser.Sn_Name
                          where dbUser.Sn_Name == user.Sn_Name
                          select new HSUser
                          {
                              //Id = dbUser.Id,
                              //userName = u.userName,
                              Sn_Name = dbUser.Sn_Name,
                              Password = user.Password,
                              ProfileHotspot = user.ProfileHotspot,
                              LimitUptime = (user.LimitUptime),
                              Disabled = Convert.ToInt32(Convert.ToBoolean(user.Disabled)),
                              Email = Get_Email_Cards_info( user.Email),
                              Comment = user.Comment,
                              CallerMac = user.CallerMac,
                              //address = user.address,
                              Server = user.Server,
                              UptimeUsed = (user.UptimeUsed),
                              DownloadUsed = (user.DownloadUsed),
                              //bytesOut = (user.bytesOut),
                              UploadUsed = (user.UploadUsed),
                              //bytesIn = (user.bytesIn),
                              //TransferLimit = (user.TransferLimit),
                              Limitbytestotal = (user.Limitbytestotal),
                              FirsLogin = string.IsNullOrEmpty(user.Comment) ? null : Get_First_login_From_Comment(user.Comment),
                              //FirsLogin = string.IsNullOrEmpty(user.Comment) ? 0 : Get_First_login_From_Comment(user.Comment),
                              AddedDb = date_added_Localdb,
                              DeleteFromServer = 0, 
                              Status = Get_Status_Cards(new SourceCardsHotspot_fromMK { uptime = user.UptimeUsed, limitUptime = user.LimitUptime, limitbytestotal = user.Limitbytestotal, bytesIn = user.DownloadUsed, bytesOut = user.UploadUsed }),
                              //actualProfileName = u.profileHotspot,

                               //= dbUser.UptimeLimit == 0 ? user.UptimeLimit : dbUser.UptimeLimit,
                              UptimeLimit = dbUser.UptimeLimit == 0 ? user.LimitUptime : dbUser.UptimeLimit,
                              TransferLimit = dbUser.TransferLimit == 0 ? user.Limitbytestotal : dbUser.TransferLimit,
                              ProfileName=(dbUser.ProfileName==null || dbUser.ProfileName == "") ?  profil  : dbUser.ProfileName,
                              
                              ValidityLimit = (day > 0  ? day : 0) ,
                              //ValidityLimit=(dbUser.ValidityLimit <= 0) ? (day > 0  ? day : 0) : dbUser.ValidityLimit,
                              ProfileValidity=(dbUser.ProfileValidity <= 0) ? (day > 0  ? day : 0) : dbUser.ProfileValidity,
                             
                              Price=(dbUser.Price <= 0) ? (price > 0  ? price : 0) : dbUser.Price,
                              //Price_Disply = (dbUser.Price_Disply == "") ? (price > 0  ? price.ToString() : "0") : dbUser.Price_Disply,
                              //TotalPrice = (dbUser.TotalPrice <= 0) ? (price > 0  ? price : 0) : dbUser.TotalPrice,
                              TotalPrice = (dbUser.TotalPrice <= 0) ? Get_TotalPrice((dbUser.ProfileName == null || dbUser.ProfileName == "") ? profil : dbUser.ProfileName ,price) : dbUser.TotalPrice,
                              
                              //SpId = (dbUser.SpId == null || dbUser.SpId == 0) ? (sp > 0 ? sp : 0) : dbUser.SpId,
                              SpCode = (dbUser.SpCode == null || dbUser.SpCode == "") ? ( sp ) : dbUser.SpCode,
                              BatchCardId = (dbUser.BatchCardId == null || dbUser.BatchCardId == 0) ? (BatchId > 0 ? BatchId : null) : dbUser.BatchCardId,
                             
                              NumberPrint = (dbUser.NumberPrint == null || dbUser.NumberPrint == 0) ? (numPrint > 0 ? numPrint : null) : dbUser.NumberPrint,
                              
                              RegDate = (dbUser.RegDate == null ) ? (datetimePrint !=null? datetimePrint : null) : dbUser.RegDate,
                              Descr = (dbUser.Descr == null || dbUser.Descr == "") ? descr : user.Descr,

                              First_mac=first_mac,
                              SmartValidatiy_Add=smartValidatiy_Add,
                              SmartValidatiy_ByDayOrHour=byDayOrHour,
                              SmartValidatiy_timeSave=timeSave,
                              SmartValidatiy_sizeSave=sizeSave,
                              SmartValidatiy_sessionSave=sessionSave,

                          }).ToList();

                if (um.Count > 0)
                    Add_HSUser_toDB(um,false,false,true);
                else if (HS_Users.Count > 0)
                    Add_HSUser_toDB(HS_Users,false,false,false);
            }
            catch (Exception e) { RJMessageBox.Show("Syn_HS_Users_to_LocalDB\n" + e.Message); }
        } 
        private float Get_TotalPrice(string profile, float price)
        {
            if (price <= 0)
                return price;

            string ProfileName = profile;
            float totalPrice = (price);
            try
            {
                int Is_percentage = 0;
                float Percentage = 0;
                int PercentageType = 0;

                HSLocalProfile hsprofle = Global_Variable.Hotspot_Profile.Find(x => x.Name==ProfileName);
                if (hsprofle != null)
                {
                    Is_percentage = hsprofle.Is_percentage;
                    Percentage = hsprofle.Percentage;
                    PercentageType = hsprofle.PercentageType;

                }
                else
                {
                    UmProfile profle = Global_Variable.UM_Profile.Find(x => x.Name == ProfileName);
                    if (profle != null)
                    {
                        Is_percentage = profle.Is_percentage;
                        Percentage = profle.Percentage;
                        PercentageType = profle.PercentageType;
                    }
                }
                float percentage = 0;
                if (Is_percentage == 0)
                    return price;
                 
                     
                        percentage = Percentage;
                        if (PercentageType == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                     
                
            }
            catch { }
            return totalPrice;
        }

        public bool Add_HSUser_toDB(List<HSUser> HS_Users, bool is_insert = true, bool AddAfterPrint = false, bool check_byID = true)
        {
            string _update_check = " WHERE Sn_Name = @Sn_Name;";

            if (!check_byID)
                _update_check =
                    " WHERE Sn_Name = @Sn_Name;";

            bool status = true;
            string query = "";
            if (is_insert)
            {
                query =
                    "INSERT OR IGNORE into HSUser ("
                    + "[SN], "
                    + "[UserName], "
                    + "[Password], "
                    + "[ProfileName], "

                    + "[TotalPrice], "
                    + "[Price_Disply], "
                    + "[BatchCardId], "
                    + "[NumberPrint], "
                    + "[SpName], "

                    + "[RegDate], "
                    + "[FirsLogin], "
                    + "[Comment], "
                    + "[LastSynDb], "

                    + "[CountProfile], "
                    + "[CountSession], "
                    + "[PageNumber], "
                    + "[Sn_Name], "

                    + "[DeleteFromServer], "
                    + "[ValidityLimit], "
                    + "[UptimeLimit], "
                    + "[TransferLimit], "

                    + "[UptimeUsed], "
                    + "[DownloadUsed], "
                    + "[UploadUsed], "
                    + "[ProfileTillTime], "

                    + "[ProfileTimeLeft], "
                    + "[ProfileTransferLeft], "
                    + "[ProfileValidity],"
                    + "[Price],"

                    + "[Percentage],"
                    + "[PercentageType],"
                    + "[Disabled],"
                    + "[Status],"

                    + "[SpCode],"
                    + "[Email],"
                    + "[NasPortId],"
                    + "[MkId], "

                    + "[IdHX], "
                    + "[CallerMac], "
                    + "[ProfileHotspot], "
                    + "[Server], "
                    + "[Descr], "
                    + "[Limitbytestotal], "
                    + "[LimitUptime], "

                    + "[SmartValidatiy_Add], "
                    + "[SmartValidatiy_ByDayOrHour], "
                    + "[SmartValidatiy_timeSave], "
                    + "[SmartValidatiy_sizeSave], "
                    + "[SmartValidatiy_sessionSave], "
                    + "[PageNumber], "
                    + "[First_mac] "

                    + ") "
                    + "values ("
                    + "@SN, "
                    + "@UserName, "
                    + "@Password, "
                    + "@ProfileName,"

                    + "@TotalPrice, "
                    + "@Price_Disply, "
                    + "@BatchCardId, "
                    + "@NumberPrint, "
                    + "@SpName,"

                    + "@RegDate,"
                    + "@FirsLogin,"
                    + "@Comment,"
                    + "@LastSynDb,"

                    + "@CountProfile,"
                    + "@CountSession,"
                    + "@PageNumber,"
                    + "@Sn_Name,"

                    + "@DeleteFromServer,"
                    + "@ValidityLimit,"
                    + "@UptimeLimit,"
                    + "@TransferLimit,"

                    + "@UptimeUsed,"
                    + "@DownloadUsed,"
                    + "@UploadUsed,"
                    + "@ProfileTillTime,"

                    + "@ProfileTimeLeft,"
                    + "@ProfileTransferLeft,"
                    + "@ProfileValidity,"
                    + "@Price,"

                    + "@Percentage,"
                    + "@PercentageType,"
                    + "@Disabled,"
                    + "@Status,"

                    + "@SpCode,"
                    + "@Email,"
                    + "@NasPortId,"
                    + "@MkId, "

                    + "@IdHX, "
                    + "@CallerMac, "
                    + "@ProfileHotspot, "
                    + "@Server, "
                    + "@Descr, "
                    + "@Limitbytestotal, "
                    + "@LimitUptime, "

                    + "@SmartValidatiy_Add, "
                    + "@SmartValidatiy_ByDayOrHour, "
                    + "@SmartValidatiy_timeSave, "
                    + "@SmartValidatiy_sizeSave, "
                    + "@SmartValidatiy_sessionSave, "
                    + "@PageNumber, "
                    + "@First_mac "

                    + "); ";
            }
            else
            {
                query =
                   "update HSUser set "
                   + "[Password]=@Password, "
                   + "[ProfileHotspot]=@ProfileHotspot, "
                   + "[LimitUptime]=@LimitUptime, "
                   + "[Disabled]=@Disabled,"
                   + "[Comment]=@Comment,"
                   + "[Email]=@Email,"
                   + "[CallerMac]=@CallerMac,"
                   + "[Server]=@Server,"
                   + "[UptimeUsed]=@UptimeUsed,"
                   + "[DownloadUsed]=@DownloadUsed,"
                   + "[UploadUsed]=@UploadUsed,"
                   + "[Limitbytestotal]=@Limitbytestotal,"
                   + "[FirsLogin]=@FirsLogin,"
                   + "[UptimeLimit]=@UptimeLimit,"
                   + "[TransferLimit]=@TransferLimit,"
                   + "[ProfileName]=@ProfileName,"
                   + "[DeleteFromServer]=@DeleteFromServer,"
                   + "[ProfileValidity]=@ProfileValidity,"
                   + "[ValidityLimit]=@ValidityLimit,"
                   + "[Price]=@Price,"
                   + "[Price_Disply]=@Price_Disply,"
                   + "[TotalPrice]=@TotalPrice,"
                   + "[SpCode]=@SpCode,"
                   + "[BatchCardId]=@BatchCardId,"
                   + "[NumberPrint]=@NumberPrint,"
                   + "[RegDate]=@RegDate,"
                   + "[Descr]=@Descr,"
                   + "[First_mac]=@First_mac,"
                   + "[SmartValidatiy_Add]=@SmartValidatiy_Add,"
                   + "[SmartValidatiy_ByDayOrHour]=@SmartValidatiy_ByDayOrHour,"
                   + "[SmartValidatiy_timeSave]=@SmartValidatiy_timeSave,"
                   + "[SmartValidatiy_sizeSave]=@SmartValidatiy_sizeSave,"
                   + "[SmartValidatiy_sessionSave]=@SmartValidatiy_sessionSave,"
                   + "[Status]=@Status "
                   + _update_check;

            }

            try
            {
                using (var cnn = Sql_DataAccess.GetConnection())
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var rowsEfect = cnn.Execute(query, HS_Users, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
            return status;
        }

        private string Get_Email_Cards_info(string source_email)
        {
            try
            {
                firstlogin = null;
                descr = "";
                profil = "";
                day = 0;
                price = 0;
                sp = "";
                numPrint = null;
                BatchId = null;
                datetimePrint=null;
                byDayOrHour = 0;
                first_mac = 0;
                timeSave = 0;
                sizeSave = 0;
                sessionSave = 0;
                smartValidatiy_Add = 0;
            
                if (source_email.Contains("@smart."))
                {
                    smartValidatiy_Add = 1;
                    //"profil'day'price'sp'numPrint'datetimePrint'byDayOrHour'mac'timeSave'sizeSave'sessionSave'@smart.befor\r\n";
                    string[] Res_split = source_email.Split(new string[] { "'" }, StringSplitOptions.None);
                    try { profil = Res_split[0]; } catch { };
                    try { day = int.Parse(Res_split[1]) * 24 * 60 * 60; } catch { }
                    try { price = float.Parse(Res_split[2]); } catch { }
                    try { sp = (Res_split[3]); } catch { }
                    //try { sp = int.Parse(Res_split[3]); } catch { }
                    try { 
                        if(Res_split[4].Contains("!"))
                        {
                            string[] split = Res_split[4].Split(new string[] { "!" }, StringSplitOptions.None);
                            if(split[0]!="0")
                                BatchId = int.Parse(split[0]);
                            if (split[1] != "0")
                                numPrint = int.Parse(split[1]);

                        }
                        else
                            if (Res_split[4] != "0")
                                BatchId = int.Parse(Res_split[4]); 

                    } catch { }
                    try { datetimePrint = utils.String_To_Datetim(Res_split[5], "yyyy-MM-dd-hh-mm"); } catch { }
                    //try { datetimePrint = utils.StringDatetimeToUnixTimeStamp(Res_split[5], "yyyy-MM-dd-hh-mm"); } catch { }
                    try { byDayOrHour = Convert.ToInt16(Convert.ToBoolean(Res_split[6])); } catch { }
                    try { first_mac = Convert.ToInt16(Convert.ToBoolean(Res_split[7])); } catch { }
                    try { timeSave = Convert.ToInt16(Convert.ToBoolean(Res_split[8])); } catch { }
                    try { sizeSave = Convert.ToInt16(Convert.ToBoolean( Res_split[9])); } catch { }
                    try { sessionSave = Convert.ToInt16(Convert.ToBoolean(Res_split[10])); } catch { }
                }
            }
            catch { }
            return source_email;
            }
        
        private int Get_Status_Cards(SourceCardsHotspot_fromMK u)
        {
            //0=waiting,1=active,2=all_finsh,3=open
            //int status = 0;
            if (u.uptime == 0) 
                return 0;
            else
            {
                if (u.limitbytestotal > 0)
                {
                    if ((u.bytesOut + u.bytesIn) >= u.limitbytestotal) return 2;
                }

                if (u.limitUptime > 0)
                {
                    if (u.uptime >= u.limitUptime) return 2;
                }
                if (smartValidatiy_Add == 1)
                {
                    if (firstlogin != null && day > 0)
                    {
                        if (firstlogin.Value.Year > 1990)
                            if (firstlogin.Value.AddSeconds(day) < DateTime.Now)
                                return 2;
                    }
                    //else if (day == 0)
                    //    return 3;

                    if (u.limitUptime == 0 && u.limitbytestotal == 0 && day == 0)
                        return 3;
                }
                else
                {
                     if (u.limitUptime == 0 && u.limitbytestotal==0 && day == 0)
                        return 3;
                }

                return 1;
            }

         //return status;
        }
        private DateTime? Get_First_login_From_Comment(string comment)
        {
            DateTime? firstlogin=new DateTime();
            //double firstlogin = 0;
            try
            {
                 
                if (comment.Contains(";"))
                {
                    string[] Res_split = comment.Split(new string[] { ";" }, StringSplitOptions.None);
                    firstlogin = (utils.String_To_Datetime_By_V_MK(Res_split[0]));
                    //firstlogin = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(Res_split[0]));
                    //timeSave=Res_split[1];
                    //sizeSave=Res_split[2];
                    if (timeSave > 0)
                    {
                        if (sizeSave > 0)
                        {

                            descr = Res_split[3];
                        }
                        else
                        {

                            descr = Res_split[2];


                        }
                    }
                    else if (sizeSave > 0)
                    {
                        descr = Res_split[2];
                    }
                    else descr = Res_split[1];

                }
                else
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(comment.Trim()))
                        {
                            if (DateTime.TryParse(comment, out DateTime dateTime))
                                return utils.Datetime_To_DateTime_Format_DB(dateTime);
                            else
                                descr = comment;
                        }
                    }
                    catch { }
                }
            }
            catch { }
            return firstlogin;
        }

        private double Get_First_login_From_Comment2(string comment)
        {
            double firstlogin = 0;
            try
            {
                if (comment.Contains(";"))
                {
                    string[] Res_split = comment.Split(new string[] { ";" }, StringSplitOptions.None);
                    firstlogin = utils.DateTimeToUnixTimeStamp(Convert.ToDateTime(Res_split[0]));
                    //timeSave=Res_split[1];
                    //sizeSave=Res_split[2];
                    if (timeSave > 0)
                    {
                        if (sizeSave > 0)
                        {

                            descr = Res_split[3];
                        }
                        else
                        {

                            descr = Res_split[2];


                        }
                    }
                    else if (sizeSave > 0)
                    {
                        descr = Res_split[2];
                    }
                    else descr = Res_split[1];

                }
                else
                {
                    try
                    {
                        if (!string.IsNullOrEmpty(comment.Trim()))
                        {
                            if (DateTime.TryParse(comment, out DateTime dateTime))
                                return utils.DateTimeToUnixTimeStamp(dateTime);
                            else
                                descr = comment;
                        }
                    }
                    catch { }
                }
            }
            catch { }
            return firstlogin;
        }
        
        public  List<SourceCardsHotspot_fromDB> Get_Hotspot_NotDeleteFromServer()
        {

            //string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                var output = cnn.Query<SourceCardsHotspot_fromDB>("select * from userHS where Delet_fromServer=0; ", new DynamicParameters());
                return output.ToList();
            }
        }
        public  void Set_Users_HS_disable_LocalDB()
        {
            string query = "UPDATE userHS SET Delet_fromServer = 1  WHERE Delet_fromServer = 0;";

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
        [Obsolete]
        public  List<SourceCardsHotspot_fromMK> Get_one_HS_User(string name, bool is_syn = true)
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<SourceCardsHotspot_fromMK> users = null;

            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/ip/hotspot/user/print"; 

                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    users = new List<SourceCardsHotspot_fromMK>();

                    var loadCmd = connection.CreateCommandAndParameters(code, "name", name);
                    //var loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    //var response = loadCmd.ExecuteAsync();
                    //DateTime? nulldt = null;
                    foreach (var item in response)
                    {
                        try
                        {
                            SourceCardsHotspot_fromMK card = new SourceCardsHotspot_fromMK();
                            card.id = item.GetResponseFieldOrDefault(".id", "");
                            card.userName = item.GetResponseFieldOrDefault("name", "");
                            card.password = item.GetResponseFieldOrDefault("password", "");
                            card.profileHotspot = item.GetResponseFieldOrDefault("profile", "");
                            card.limitUptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("limit-uptime", "0"));
                            card.uptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime", "0"));
                            card.bytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-out", "0"));
                            card.bytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-in", "0"));
                            card.limitbytestotal = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-total", "0"));
                            card.disabled = item.GetResponseFieldOrDefault("disabled", "false");
                            card.email = item.GetResponseFieldOrDefault("email", "");
                            card.comment = item.GetResponseFieldOrDefault("comment", "");
                            card.macAddress = item.GetResponseFieldOrDefault("mac-address", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.server = ((item.GetResponseFieldOrDefault("server", "all")));
                            card.address = ((item.GetResponseFieldOrDefault("address", null)));
                            users.Add(card);

                        }
                        catch { }
                    }
                    stopwatch.Stop();
                    if (users.Count > 0)
                    {
                        UserManagerProcess u = new UserManagerProcess();
                        if (is_syn)
                            Syn_HS_Users_to_LocalDB(users);
                    }
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    return users;
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("لم يتم جلب الكرت من  الهوتسبوت قم بتحديث    \n", ex.Message);

            }
            return users;

        }
        [Obsolete]
        public SourceCardsHotspot_fromMK Get_one_HS_User(string name)
        {
            SourceCardsHotspot_fromMK card = new SourceCardsHotspot_fromMK();
            try
            {
                //string code = Properties.Settings.Default.userman_print;
                string code = "/ip/hotspot/user/print";

                string valueName = "name";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters(code, valueName, name);
                    var response = loadCmd.ExecuteList();
                    foreach (var item in response)
                    {
                        try
                        {
                            card.id = item.GetResponseFieldOrDefault(".id", "");
                            card.userName = item.GetResponseFieldOrDefault("name", "");
                            card.password = item.GetResponseFieldOrDefault("password", "");
                            card.profileHotspot = item.GetResponseFieldOrDefault("profile", "");
                            card.limitUptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("limit-uptime", "0"));
                            card.uptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime", "0"));
                            card.bytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-out", "0"));
                            card.bytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-in", "0"));
                            card.limitbytestotal = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-total", "0"));
                            card.disabled = item.GetResponseFieldOrDefault("disabled", "false");
                            card.email = item.GetResponseFieldOrDefault("email", "");
                            card.comment = item.GetResponseFieldOrDefault("comment", "");
                            card.macAddress = item.GetResponseFieldOrDefault("mac-address", null);
                            card.disabled = ((item.GetResponseFieldOrDefault("disabled", "false")));
                            card.server = ((item.GetResponseFieldOrDefault("server", "all")));
                            card.address = ((item.GetResponseFieldOrDefault("address", null)));
                            //Global_Variable.Source_Users_UserManager_ForPrint.Add(card.userName);
                        }
                        catch { }
                    }
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke((MethodInvoker)delegate () { Global_Variable.Uc_StatusBar.txt_time.Text = stopwatch.Elapsed.ToString("mm\\:ss\\.ff"); });
                    return card;
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("لم يتم جلب الكرت من  الهوتسبوت قم بتحديث    \n", ex.Message);
            }
            return card;

        }
    }

    public class SourceCardsHotspot_fromDB
    {
        #region fileds
        public int id { get; set; }
        public double sn { get; set; }
        public string idHX { get; set; }
        public string userName { get; set; }
        public string password { get; set; }
        public string sn_userName { get; set; }
        public string profileHotspot { get; set; }
        public double limitUptime { get; set; } = 0;
        public double uptime { get; set; } = 0;
        public double bytesOut { get; set; } = 0;
        public double bytesIn { get; set; } = 0;
        public double limitbytestotal { get; set; } = 0;

        public int disabled { get; set; } = 0;
        public string comment { get; set; }
        public string descr { get; set; }
        public string email { get; set; }
        public string macAddress { get; set; }
        public string server { get; set; }
        public string address { get; set; }

        public double? regDate { get; set; }
        public double uptimeLimit { get; set; } = 0;//from profile الوقت المحدد للكرت
        public double transferLimit { get; set; } = 0;
        public double profileValiday { get; set; } = 0;
        public double lastSeenAt { get; set; } = 0;
        public double pricePercentage { get; set; } = 0;
        public double price { get; set; } = 0;
        public double priceDisplay { get; set; } = 0;
        public string actualProfileName { get; set; } = "";
        public double profileTillTime { get; set; }
        public double profileTimeLeft { get; set; }
        public double profileTransferLeft { get; set; }
        public int? spId { get; set; }
        //public string SpCode { get; set; }
        public int is_spPercentage { get; set; }
        public string spName { get; set; } = "";
        public double spPercentage { get; set; } = 0;
        public int? numberPrintedId { get; set; }
        public int Delet_fromServer { get; set; } = 0;
        public int countProfile { get; set; }
        public string mkId { get; set; }
        public string radius { get; set; }
        public double firstUse { get; set; } // اول تسجيل دخول
        public string nasPortId { get; set; }
        public double? date_added_Localdb { get; set; }
        public int status { get; set; }

        //=============================
        public int smartValidatiy_Add { get; set; } = 0;
        public int smartValidatiy_byDayHour { get; set; } = 0;
        public int smartValidatiy_timeSave { get; set; } = 0;
        public int smartValidatiy_sizeSave { get; set; } = 0;
        public int smartValidatiy_sessionSave { get; set; } = 0;
        public int first_mac { get; set; } = 0; // مرتبط باول ماك
      
        #endregion
    }


    public class CardsUserHotspot_Display_FromDB
    {
        private int id;
        private string idHX;
        private double sn;
        private string userName;
        private string password = "";
        private string profileHotspot ;
        private double limitUptime;
        private double uptime;
        private double bytesOut;
        private double bytesIn;
        private double limitbytestotal;
        //private int disabled = 0;
        private string comment;
        //private string email;
        private string macAddress;
        //private string server;
        private string address;

        private double regDate;
        private DateTime? _regDate;

        private double uptimeLimit = 0;
        private double transferLimit = 0;

        private double lastSeenAt = 0;
        //private DateTime? _lastSeenAt;
        private double pricePercentage=0;
        private double priceDisplay = 0;
        private double price = 0;
        private string actualProfileName = "";
        private double profileTillTime;
        private double profileTimeLeft;
        private double profileTransferLeft;
        //private int countProfile;
        private int? spId;
        private string spName = "";
        private int numberPrintedId;
        private string sn_userName;
        private int delet_fromServer ;
        private int status;

        private double firstUse = 0;
        private double profileValiday = 0;
        //private string radius;
        private string nasPortId;
      


        #region fileds

        [DisplayName("التسلسل")]
        public double Sn { get => sn; set => sn = value; }
        [DisplayName("الحالة")]
        public string Str_Status
        {
            get
            {
                //if (activeSessions == 1)
                //    return "نشط + اونلاين";
                string s = (status == 0 ? "انتظار" : (status == 1 ? "نشط" : (status == 2 ? "منتهي" : (status == 3 ? "" : ""))));
                if (disabled == 1)
                    return s + (" + معطل");
                return s;
            }
        }
        [DisplayName("الاسم")]
        public string UserName { get => userName; set => userName = value; }
        [DisplayName("كلمة المرور")]
        public string Password { get => password; set => password = value; }
        [DisplayName("الباقة")]
        public string ActualProfileName { get => actualProfileName; set => actualProfileName = value; }

        [DisplayName("السعر")]
        public string Str_price
        {
            get
            {
                //try { row.Cells["moneyTotal"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["moneyTotal"].Value)); } catch { row.Cells["moneyTotal"].Value = 0; };

                return String.Format("{0:n0}", price);
            }
        }

        [DisplayName("سعر العرض")]

        public string Str_priceDisplay
        {
            get
            {
                //try { row.Cells["moneyTotal"].Value = String.Format("{0:n0}", Convert.ToDouble(row.Cells["moneyTotal"].Value)); } catch { row.Cells["moneyTotal"].Value = 0; };

                return String.Format("{0:n0}", priceDisplay);
            }
        }
        [DisplayName("السعر")]
        public double Price { get => price; set => price = value; }
        [DisplayName("بروفايل الهوتسبوت")]
        public string ProfileHotspot { get => profileHotspot; set => profileHotspot = value; }
        //[DisplayName("عدد الباقات")]
        //public int CountProfile { get => countProfile; set => countProfile = value; }
        public int? SpId { get => spId; set => spId = value; }
        [DisplayName("نقطة البيع")]
        public string SpName { get => spName; set => spName = value; }
        [DisplayName("الدفعه")]
        public int NumberPrintedId { get => numberPrintedId; set => numberPrintedId = value; }
        [DisplayName("وقت الباقة المسموح")]
        public string Str_UptimeLimit
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptimeLimit);
            }

        }
        [DisplayName("تنزيل الباقة المسموح")]
        public string Str_TransferLimit
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(transferLimit.ToString());
                else
                    return utils.ConvertSize_Get_En(transferLimit.ToString());
            }
        }
        [DisplayName("الوقت الفعلي المسموح")]
        public string Str_limitUptime
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(limitUptime);
            }

        }
        [DisplayName("التنزيل الفعلي المسموح")]
        public string Str_limitbytestotal
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(limitbytestotal.ToString());
                else
                    return utils.ConvertSize_Get_En(limitbytestotal.ToString());
            }
        }

        [DisplayName("الوقت المستخدم")]
        public string Str_UptimeUsed
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptime);
            }
        }
        [DisplayName("التحميل المستخدم")]
        public string Str_DownloadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(bytesOut.ToString());
                else return utils.ConvertSize_Get_En(bytesOut.ToString());

            }
        }
        [DisplayName("الرقع المستخدم")]
        public string Str_UploadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(bytesIn.ToString());
                else return utils.ConvertSize_Get_En(bytesIn.ToString());

            }
        }
        [DisplayName("التحميل+الرفع المستخدم")]                                                                                     
        public string Str_Up_Down
        {
            //get
            //{
            //    if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic((bytesIn + bytesOut).ToString());
            //    else return utils.ConvertSize_Get_En((bytesIn + bytesOut).ToString());

            //}

            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic((bytesIn+bytesOut).ToString());
                else return utils.ConvertSize_Get_En((bytesIn + bytesOut).ToString());

            }
        }
        [DisplayName("تاريخ الاضافة")]
        public DateTime? dt_RegDate
        {
            get
            {
                if (RegDate > 0)
                    return utils.UnixTimeStampToDateTime(regDate);
                else return null;
            }
        }
        [DisplayName("اخر ضهور")]
        public DateTime? dt_LastSeenAt
        {
            get
            {
                if (lastSeenAt > 0)
                    return utils.UnixTimeStampToDateTime(lastSeenAt);
                else return null;
            }
        }
        [DisplayName("اول دخول")]
        public DateTime? dt_FirstUse
        {
            get
            {
                if (firstUse > 0)
                    return utils.UnixTimeStampToDateTime(firstUse);
                else return null;
            }
        }
        [DisplayName("تاريخ الانتهاء")]
        public DateTime? Str_ProfileTillTime
        {
            get
            {
                if (firstUse > 0 && profileValiday > 0)
                    return utils.UnixTimeStampToDateTime(firstUse + profileValiday);
                else return null;
            }
        }
        [DisplayName("الوقت المتبقي")]
        public string Str_ProfileTimeLeft
        {
            get
            {
                if (limitUptime == 0) return "غير محدد";
                double time = Math.Max(0, (uptimeLimit - uptime));
                return utils.Get_Seconds_By_clock_Mode(time);
            }
        }
        [DisplayName("التحميل المتبقي")]
        public string Str_ProfileTransferLeft
        {
            get
            {
                if (limitbytestotal == 0) return "غير محدد";
                double size = Math.Max(0, (transferLimit - ((bytesIn + bytesOut))));
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic_with_ZeroByte(size.ToString());
                else return utils.ConvertSize_Get_En_with_ZeroByte(size.ToString());
            }
        }
        [DisplayName("تعليق")]
        public string Comment { get => comment; set => comment = value; } //comment
        [DisplayName("ملاحظة")]
        public string descr { get; set; }
        [DisplayName("السيرفر")]
        public string server { get; set; }
        [DisplayName("الايميل")]
        public string email { get; set; }
        public int Status { get => status; set => status = value; }

        public int Id { get => id; set => id = value; }
        public string IdHX { get => idHX; set => idHX = value; }
        public string Sn_userName { get => sn_userName; set => sn_userName = value; }
        public int Delet_fromServer { get => delet_fromServer; set => delet_fromServer = value; }
        public double ProfileTillTime { get => profileTillTime; set => profileTillTime = value; }
        public double ProfileTimeLeft { get => profileTimeLeft; set => profileTimeLeft = value; }
        public double ProfileTransferLeft { get => profileTransferLeft; set => profileTransferLeft = value; }
        public double ProfileValiday { get => profileValiday; set => profileValiday = value; }
        public double FirstUse { get => firstUse; set => firstUse = value; }
     
        public double UptimeLimit { get => uptimeLimit; set => uptimeLimit = value; }
        public double Uptime { get => uptime; set => uptime = value; }
        public double BytesOut { get => bytesOut; set => bytesOut = value; }
        public double BytesIn { get => bytesIn; set => bytesIn = value; }
        public double Limitbytestotal { get => limitbytestotal; set => limitbytestotal = value; }
        public double TransferLimit { get => transferLimit; set => transferLimit = value; }
        public double LastSeenAt { get => lastSeenAt; set => lastSeenAt = value; }
        //public int disabled {}
        public int disabled { get; set; }
        public double RegDate { get => regDate; set => regDate = value; }
        [DisplayName("الجهاز")]
        public string NasPortId { get => nasPortId; set => nasPortId = value; }

        //=============================
        public int smartValidatiy_Add { get; set; } 
        public int smartValidatiy_byDayHour { get; set; } 
        public int smartValidatiy_timeSave { get; set; } 
        public int smartValidatiy_sizeSave { get; set; } 
        public int smartValidatiy_sessionSave { get; set; }
        public string MacAddress { get => macAddress; set => macAddress = value; }
        public int first_mac { get; set; } // مرتبط باول ماك


        public List<CardsUserHotspot_Display_FromDB> Get_Hotspot_from_DB(SourceCardsHotspot_fromMK user = null ,string filter="")
        {
            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
            {
                string where = " WHERE u.Delet_fromServer=0 "+filter;
                if (user != null)
                {
                    double sn = Int32.Parse(user.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                    string sn_userName = sn + "-" + user.userName;
                    where = where + " and u.sn_userName='" + sn_userName + "' ";
                }
                string Qury = "SELECT * from userHS u " +
                where +
                 //" GROUP BY u.id " +
                 " ORDER BY u.id DESC ;";

                var output = cnn.Query<CardsUserHotspot_Display_FromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }

        public  List<CardsUserHotspot_Display_FromDB> Get_UsersHotspot_By_FirstUse(string query)
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {

                string Qury = "SELECT * FROM userHS u  " +
                 query +
               //" WHERE u.firstUse >="+start+" AND u.firstUse<="+end+"  " +
               //"WHERE u.Delet_fromServer=0 " +
               " GROUP BY u.id;";

                var output = cnn.Query<CardsUserHotspot_Display_FromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }
        public  List<class_Report_monthly_or_Dayliy> Get_UsersHotspot_By_FirstUse_byDays(string query, string month_or_Day = "\"%m-%Y\"")
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                string Qury = "SELECT *," +
                   "strftime(" + month_or_Day + ", u.firstUse, 'unixepoch') date," +
                   "sum(u.price) as moneyTotal ," +
                   "count(u.id) as count " +
                   "FROM userHS u  " +
                   query +
                   " group by date;";
                var output = cnn.Query<class_Report_monthly_or_Dayliy>(Qury, new DynamicParameters());
                return output.ToList();

            }
        }
        public  List<class_Report_monthly_or_Dayliy> Get_UsersHotspot_By_byDays_Downloads_UptimeUsed(string query, string month_or_Day = "\"%m-%Y\"")
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {
                ////SELECT strftime('%Y-%m-%d', fromTime, 'unixepoch') date, sum(bytesDownload + bytesUpload) as download  ,sum(upTime) as uptime  FROM session
                ////group by date
                //string Qury = "SELECT " +
                //   "strftime('%Y-%m-%d', fromTime, 'unixepoch') date," +
                //   "sum(bytesDownload + bytesUpload) as download ," +
                //   "sum(upTime) as uptime " +
                //   "FROM session " +
                //   //"count(u.id) as count " +
                //   //"FROM session u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id " +
                //   query +
                //   " group by date;";
                var output = cnn.Query<class_Report_monthly_or_Dayliy>(query, new DynamicParameters());
                return output.ToList();
            }
        }

        #endregion
    }

}
