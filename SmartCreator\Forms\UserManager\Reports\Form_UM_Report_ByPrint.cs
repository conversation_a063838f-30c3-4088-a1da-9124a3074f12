﻿using Org.BouncyCastle.Math;
using SmartCreator.Data;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager.Reports
{
    public partial class Form_UM_Report_ByPrint : RJChildForm
    {
        string Server_Type = "UM";
        bool FirstLoad=true;
        int PW = 230;
        //Image bgImage;
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";


        //OrmLiteConnectionFactory dbFactory = null;
        Smart_DataAccess Smart_DA;
        Sql_DataAccess Local_DA;

        public Form_UM_Report_ByPrint(string server_Type = "UM")
        {
            InitializeComponent();

           

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                dgv_Detail.RightToLeft = RightToLeft.No;
            }
            Server_Type = server_Type;
            if (Server_Type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            sideMenu();
            set_font();
            try
            {
                string today = DateTime.Now.ToString("yyyy-MM-dd");
                Date_From.Value = Convert.ToDateTime(today + "  00:00:00").AddDays(-30);
                Date_To.Value = Convert.ToDateTime(today + "  23:59:59");
                CheckBox_To_Date.Check = true;
                ToggleButton_Monthly.Checked = true;
            }
            catch { }
            if (UIAppearance.Theme == UITheme.Dark)
            {

                rjPanel1.Customizable = false;
                rjPanel3.Customizable = false;
            }
            //if (utils.ScaleFactor != 1)
            //{
            //    txt_sum_Sales.Font = new Font(txt_sum_Sales.Font.FontFamily, txt_sum_Sales.Font.Size , txt_sum_Sales.Font.Style);
            //    txt_count_Cards.Font = new Font(txt_count_Cards.Font.FontFamily, txt_count_Cards.Font.Size , txt_count_Cards.Font.Style);
            //}
            utils utils = new utils();
            utils.Control_textSize1(this);


        }
        void sideMenu()
        {
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                PW = 0;
                panel1.Location = new Point(10, rjPanel_back_side.Location.Y);
                panel1.Width = pnlClientArea.Width - 30;
            }
            else
            {
                rjPanel_back_side.Width = utils.Control_Mesur_DPI(230);
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 30;
                panel1.Location = new Point(rjPanel_back_side.Width + 20, rjPanel_back_side.Location.Y);
            }
            rjPanel_back_side.Refresh();
            panel1.Refresh();
            rjPanel2.Refresh();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();


            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();

            Get_Batch();


            get_report();
            FirstLoad = false;

        }
        private void set_font()
        {
            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;

            dgv_Detail.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv_Detail.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_Detail.ColumnHeadersHeight = 40;
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;

            Toggle_By_Profile.Font= Toggle_By_SP.Font=
            ToggleButton_Detail.Font = ToggleButton_Monthly.Font = jToggleButton_Year.Font =
                rjLabel3.Font = rjLabel7.Font = rjLabel11.Font =
                Date_From.Font = Date_To.Font =
                rjLabel9.Font = rjLabel4.Font = rjLabel15.Font = rjLabel14.Font = rjLabel1.Font = check_with_Commi.Font = CheckBox_To_Date.Font

                = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            rjLabel19.Font = rjLabel5.Font = rjLabel2.Font = rjLabel18.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            Date_From.Font = Date_To.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);

            btn_.Font = btn_more.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Bold);
            rjLabel25Title.Font = btn_apply.Font = Program.GetCustomFont(Resources.DroidSansArabic, 13, FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv);
            utils.dgv_textSize(dgv_Detail);
            //utils.item_Contrlol_textSize();

        }
        private void Get_SellingPoint()
        {
            try
            {
                CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }
            catch { }

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }
        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;
        }

        private void Form_UM_Report_ByPrint_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }

        private void btn_more_Click(object sender, EventArgs e)
        {
            sideMenu();
        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            try
            {
                if (ToggleButton_Detail.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Monthly.Checked = false;
                    jToggleButton_Year.Checked = false;
                    //dgv_Detail.Visible = false;
                    //pnl_size_time_count.Visible = false;
                    dgv_Detail.DataSource = null;
                    FirstLoad = false;
                }
                else
                {
                    if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                    {
                        FirstLoad=true;
                        ToggleButton_Detail.Checked = true;
                        FirstLoad = false;
                    }
                }
            }
            catch { }

            //lbl_avg.Visible = false;
            //txt_avg.Visible = false;

            Date_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            Date_From.CustomFormat = "dd-MM-yyyy  وقت  HH:mm:ss";
            Date_To.CustomFormat = "dd-MM-yyyy  وقت  HH:mm:ss";

            string today = DateTime.Now.ToString("MM-dd-yyyy");
            Date_From.Value = Convert.ToDateTime(today + "  00:00:00");


            get_report();
        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                if (FirstLoad)
                    return;
                try
                {
                    CheckBox_To_Date.Check = true;
                    if (ToggleButton_Monthly.Checked)
                    {
                        FirstLoad &= true;
                        ToggleButton_Detail.Checked = false;
                        jToggleButton_Year.Checked = false;
                        //pnl_size_time_count.Visible = true;
                        dgv_Detail.Visible = true;
                        FirstLoad = false;

                    }
                    else
                    {
                        if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                        {
                            FirstLoad = true;
                            ToggleButton_Monthly.Checked = true;
                            FirstLoad = false;
                        }
                    }
                }
                catch { }
                //lbl_avg.Visible = true;
                //txt_avg.Visible = true;
                //lbl_avg.Text = "المتوسط اليومي";

                Date_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                Date_From.CustomFormat = "MM/yyyy";
                //rjDateTime_To.CustomFormat = "MM/yyyy";
                DateTime firstDayOfMonth;
                DateTime lastDayOfMonth;
                utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
                string first = firstDayOfMonth.ToString("MM-dd-yyyy");
                string last = lastDayOfMonth.ToString("MM-dd-yyyy");

                Date_From.Value = Convert.ToDateTime(first + "  00:00:00");
                Date_To.Value = Convert.ToDateTime(last + "  23:59:59");

            }
            catch { }
            get_report();

        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            try
            {
                CheckBox_To_Date.Check = true;
                if (jToggleButton_Year.Checked)
                {
                    FirstLoad = true;
                    ToggleButton_Detail.Checked = false;
                    ToggleButton_Monthly.Checked = false;
                    //pnl_size_time_count.Visible = true;
                    dgv_Detail.Visible = true;
                    FirstLoad = false;
                }
                else
                {
                    if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                    {
                        FirstLoad = true;
                        jToggleButton_Year.Checked = true;
                        FirstLoad = false;
                    }
                }
            }
            catch { }
            try
            {
                //lbl_avg.Visible = true;
                //txt_avg.Visible = true;

                //lbl_avg.Text = "المتوسط الشهري";

                Date_From.Format = DateTimePickerFormat.Custom;
                //rjDateTime_To.Format = DateTimePickerFormat.Custom;
                Date_From.CustomFormat = "yyyy";
                //rjDateTime_To.CustomFormat = "yyyy";

                //rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
                //rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
                Date_From.Value = new DateTime(DateTime.Now.Year, 1, 1);
                Date_To.Value = new DateTime(DateTime.Now.Year, 12, 31);
                //rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31,23,59,59);

                ////rjDateTime_From.Value = new DateTime(1,DateTime.Now.Year,1,0,0,0);
                //rjDateTime_To.Value = new DateTime(12,DateTime.Now.Year,31,23,59,59);
            }
            catch { }
            get_report();
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void get_report()
        {
            try
            {
                dgv.DataSource = null;
                string Query_conditon = condition_detail_firstUse();
                string Price = "Price";

                if (check_with_Commi.Checked)
                    Price = "TotalPrice";

                string Qury = "";
                long count = 0;
                double sumR = 0;
                long sum_uptime = 0;
                long sum_download = 0;
                txt_count_Cards.Text = "0";
                txt_sum_Sales.Text = "0";

                if (ToggleButton_Detail.Checked)
                {
                    Qury = $" SELECT u.SN 'الرقم التسلسلي'" +
                        $",p.UserName  'الاسم' " +
                        $",p.ProfileName  'الباقة' " +
                        $",p.AddedDate 'تاريخ الاضافة' " +
                        $",p.ProfileUptimeLimit  UptimeLimit " +
                        $",p.ProfileTransferLimit  TransferLimit " +
                        $",(p.{Price}) as  'السعر' " +
                        $" FROM {TablePyment}  p INNER JOIN {TableUser} u  ON p.Fk_Sn_Name = u.Sn_Name " +
                        $"{Query_conditon}  ORDER by u.SN DESC";

                    if (Server_Type == "HS")
                        Qury = $" SELECT u.SN 'الرقم التسلسلي'" +
                        $",p.UserName  'الاسم' " +
                        $",p.ProfileName  'الباقة' " +
                        $",p.AddedDate 'تاريخ الاضافة' " +
                        $",p.ProfileUptimeLimit  UptimeLimit " +
                        $",p.ProfileTransferLimit  TransferLimit " +
                        $",(p.{Price}) as  'السعر' " +
                        $" FROM {TablePyment}  p INNER JOIN {TableUser}  u  ON p.Fk_Sn_Name = u.Sn_Name " +
                        $"{Query_conditon}  ORDER by u.SN DESC";


                    DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);
                    dgv.DataSource = tbFound;
                    //dgv_Detail.DataSource = tbFound;

                    try
                    {

                        sum_download += tbFound.AsEnumerable().Sum(x => x.Field<Int64>("TransferLimit"));
                        sum_uptime += tbFound.AsEnumerable().Sum(x => x.Field<Int64>("UptimeLimit"));
                        sumR += tbFound.AsEnumerable().Sum(x => x.Field<double>("السعر"));

                        txt_sum_Sales.Text = String.Format("{0:n0}", sumR);
                        txt_count_Cards.Text = dgv.Rows.Count.ToString();
                        txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                        txt_uptime.Text = utils.Get_Seconds_By_clock_Mode((sum_uptime));
                    }
                    catch { }

                    try
                    {
                        //try { dgv.Columns["Price"].Visible = false; } catch { }
                        try { dgv.Columns["TransferLimit"].Visible = false; } catch { }
                        try { dgv.Columns["UptimeLimit"].Visible = false; } catch { }
                    }
                    catch { }

                }
                else
                {
                    string fitler = "'%Y-%m-%d'";
                    if (jToggleButton_Year.Checked)
                        fitler = "'%Y-%m'";
                    try
                    {

                        Qury = "SELECT " +
                         $"strftime({fitler},p.AddedDate) Date" +
                         $",sum(p.{Price}) as Price " +
                         $",count(p.Sn_Name) as Count " +
                         $",sum(p.ProfileUptimeLimit) as UptimeLimit " +
                         $",sum(p.ProfileTransferLimit) as TransferLimit " +
                         $"FROM UmUser u INNER JOIN UmPyment p ON u.Sn_Name = p.Fk_Sn_Name " +
                         $"{Query_conditon} " +
                         $" group by strftime({fitler},p.AddedDate);";

                        if (Server_Type == "HS")
                            Qury = "SELECT " +
                         $"strftime({fitler},p.AddedDate) Date" +
                         $",sum(p.{Price}) as Price " +
                         $",count(p.Id) as Count " +
                         $",sum(p.ProfileUptimeLimit) as UptimeLimit " +
                         $",sum(p.ProfileTransferLimit) as TransferLimit " +
                         $"FROM HSUser u INNER JOIN HsPyment p ON u.Sn_Name = p.Fk_Sn_Name " +
                         $"{Query_conditon} " +
                         $" group by strftime({fitler},p.AddedDate);";

                        DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);
                        //dgv.DataSource = tbFound;

                        DataTable dt = new DataTable();
                        dt.Columns.Add("التاريخ", typeof(string));
                        dt.Columns.Add("السعر");
                        dt.Columns.Add("عدد الكروت", typeof(double));
                        dt.Columns.Add("اجمالي رصيد تحميل الباقات", typeof(string));
                        dt.Columns.Add("اجمالي رصيد وقت الباقات", typeof(string));

                        dt.Columns.Add("Price", typeof(double));
                        dt.Columns.Add("UptimeLimit", typeof(double));
                        dt.Columns.Add("TransferLimit", typeof(double));

                        foreach (DataRow row in tbFound.Rows)
                        {
                            DataRow rr = dt.NewRow();
                            rr[0] = row["date"].ToString();
                            rr[1] = String.Format("{0:n0}", Convert.ToDouble(row["Price"].ToString()));
                            rr[2] = row["Count"].ToString();
                            rr[3] = utils.ConvertSize_Get_InArabic(row["TransferLimit"].ToString());
                            rr[4] = utils.Get_Seconds_By_clock_Mode(Convert.ToDouble(row["UptimeLimit"].ToString()));
                            rr[5] = Convert.ToDouble(row["Price"].ToString());
                            rr[6] = Convert.ToDouble(row["UptimeLimit"].ToString());
                            rr[7] = Convert.ToDouble(row["TransferLimit"].ToString());


                            try { sumR += (long)(Convert.ToDouble(row["Price"].ToString())); } catch { }
                            try { count += (long)(Convert.ToDouble(row["Count"].ToString())); } catch { }
                            try { sum_uptime += (long)(Convert.ToDouble(row["UptimeLimit"].ToString())); } catch { }
                            try { sum_download += (long)(Convert.ToDouble(row["TransferLimit"].ToString())); } catch { }

                            dt.Rows.Add(rr);
                        }
                        dgv.DataSource = dt;
                        //dgv_Detail.DataSource = dt;
                        txt_sum_Sales.Text = String.Format("{0:n0}", sumR);
                        txt_count_Cards.Text = count.ToString();
                        txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                        txt_uptime.Text = utils.Get_Seconds_By_clock_Mode((sum_uptime));
                        try
                        {
                            try { dgv.Columns["Price"].Visible = false; } catch { }
                            try { dgv.Columns["TransferLimit"].Visible = false; } catch { }
                            try { dgv.Columns["UptimeLimit"].Visible = false; } catch { }
                        }
                        catch { }
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        private string ColumnShow = "";
        private string condition_detail_firstUse(string _from = null, string _to = null)
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (Date_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (Date_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (Date_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if (_from != null)
                str_from_Date = _from;
            if (_to != null)
                str_to_Date = _to;

            conditon_date = " WHERE p.AddedDate >='" + str_from_Date + "' AND p.AddedDate<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked)
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND p.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }


                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }
        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            sideMenu();
        }
        private void btn__Click_1(object sender, EventArgs e)
        {
            get_report();
        }
        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    Sub_LocadData((dgv.Rows[e.RowIndex].Cells["التاريخ"].Value.ToString()));
                }
            }
            catch { }
        }
        private void Sub_LocadData(string date)
        {
            dgv_Detail.DataSource = null;
            string to = "";
            if (ToggleButton_Monthly.Checked)
                to = date + " 23:59:59";
            if (jToggleButton_Year.Checked)
            {
                date = date + "-01 00:00:00";
                DateTime dt = utils.String_To_Datetim(date, "yyyy-MM-dd hh:mm:ss");
                DateTime first = new DateTime(dt.Year, dt.Month, 1);
                DateTime last = first.AddMonths(1).AddSeconds(-1);
                to = last.ToString("yyyy-MM-dd hh:mm:ss");
            }
            string Query_conditon = condition_detail_firstUse(date, to);
            string Price = "Price";
            if (check_with_Commi.Checked)
                Price = "TotalPrice";

            string key = "Sn_Name";
            if (Server_Type == "HS")
                key = "Id";
            string Qury = "";
            if (ToggleButton_Detail.Checked == false)
            {
                Qury = $" SELECT u.SN 'الرقم التسلسلي'" +
                    $",p.UserName  'الاسم' " +
                    $",p.ProfileName  'الباقة' " +
                    $",p.AddedDate 'تاريخ الاضافة' " +
                    $",p.ProfileUptimeLimit  UptimeLimit " +
                    $",p.ProfileTransferLimit  TransferLimit " +
                    $",(p.{Price}) as  'السعر' " +
                    $" FROM {TablePyment}  p INNER JOIN {TableUser} u  ON p.Fk_Sn_Name = u.Sn_Name " +
                    $"{Query_conditon}  ORDER by u.SN DESC";

                if (Server_Type == "HS")

                    Qury = $" SELECT u.SN 'الرقم التسلسلي'" +
                    $",p.UserName  'الاسم' " +
                    $",p.ProfileName  'الباقة' " +
                    $",p.AddedDate 'تاريخ الاضافة' " +
                    $",p.ProfileUptimeLimit  UptimeLimit " +
                    $",p.ProfileTransferLimit  TransferLimit " +
                    $",(p.{Price}) as  'السعر' " +
                    $" FROM {TablePyment}  p INNER JOIN {TableUser}  u  ON p.Fk_Sn_Name = u.Sn_Name " +
                    $"{Query_conditon}  ORDER by u.SN DESC";

                if (Toggle_By_Profile.Checked)
                {
                    Qury = "SELECT " +
                        $"p.ProfileName  'الباقة' " +
                        $",sum(p.{Price}) as الاجمالي " +
                        $",count(p.{key}) as العدد " +
                        $",sum(p.ProfileUptimeLimit) as UptimeLimit " +
                        $",sum(p.ProfileTransferLimit) as TransferLimit " +
                        $"FROM {TableUser} u INNER JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name " +
                        $"{Query_conditon} " +
                        $" group by p.ProfileName;";
                }

                DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);
                dgv_Detail.DataSource = tbFound;
                update_select_DGV2();

                //if (Toggle_By_Profile.Checked)
                //{
                //    long count = 0;
                //    foreach (DataRow row in tbFound.Rows)
                //    {
                //        try { count += (long)(Convert.ToDouble(row["العدد"].ToString())); } catch { }
                //    }
                //    try
                //    {
                //        long selected = 0;
                //        foreach (DataGridViewRow row in dgv_Detail.SelectedRows)
                //        {
                //            //currQty += row.Cells["qty"].Value;
                //            try { selected += (long)(Convert.ToDouble(row.Cells["العدد"].Value)); } catch { }
                //        }

                //        //long selected= 0;
                //        //foreach(DataRow row in dgv_Detail.SelectedRows)
                //        //{
                //        //    try { selected += (long)(Convert.ToDouble(row["العدد"].ToString())); } catch { }
                //        //}
                //        string lblDescription = "( " + count + "  /  " + selected + " )";
                //        Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
                //    }
                //    catch { }

                //}

                //try
                //{

                //    sum_download += tbFound.AsEnumerable().Sum(x => x.Field<Int64>("TransferLimit"));
                //    sum_uptime += tbFound.AsEnumerable().Sum(x => x.Field<Int64>("UptimeLimit"));
                //sumR += tbFound.AsEnumerable().Sum(x => x.Field<double>("السعر"));

                //    txt_sum_Sales.Text = String.Format("{0:n0}", sumR);
                //txt_count_Cards.Text = dgv_Detail.Rows.Count.ToString();
                //    txt_download.Text = utils.ConvertSize_Get_InArabic(sum_download.ToString());
                //    txt_uptime.Text = utils.Get_Seconds_By_clock_Mode((sum_uptime));
                //}
                //catch { }

                try
                {
                    //try { dgv.Columns["Price"].Visible = false; } catch { }
                    try { dgv_Detail.Columns["TransferLimit"].Visible = false; } catch { }
                    try { dgv_Detail.Columns["UptimeLimit"].Visible = false; } catch { }
                }
                catch { }
                //update_select_DGV2();


            }

        }

        private void update_select_DGV2()
        {
            if (ToggleButton_Detail.Checked == false)
            {
                if (Toggle_By_Profile.Checked)
                {
                    long count = 0;
                    foreach (DataGridViewRow row in dgv_Detail.Rows)
                    {
                        try { count += (long)(Convert.ToDouble(row.Cells["العدد"].Value)); } catch { }
                    }
                    try
                    {
                        long selected = 0;
                        foreach (DataGridViewRow row in dgv_Detail.SelectedRows)
                        {
                            //currQty += row.Cells["qty"].Value;
                            try { selected += (long)(Convert.ToDouble(row.Cells["العدد"].Value)); } catch { }
                        }
                        string lblDescription = "( " + count + "  /  " + selected + " )";
                        Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
                    }
                    catch { }

                }
                else
                {
                    try
                    {
                        string ListAll = dgv_Detail.Rows.Count.ToString();
                        string ListSelected = dgv_Detail.SelectedRows.Count.ToString();
                        string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                        Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
                    }
                    catch { }
                }
            }
            else
            {
                try
                {
                    string ListAll = dgv.Rows.Count.ToString();
                    string ListSelected = dgv.SelectedRows.Count.ToString();
                    string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                    Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
                }
                catch { }
            }
        }

        private void dgv_Detail_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV2();
        }

        private void Toggle_By_Profile_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            get_report();
        }

        private void check_with_Commi_CheckedChanged(object sender, EventArgs e)
        {
            if (FirstLoad)
                return;
            get_report();
        }
    }

}
