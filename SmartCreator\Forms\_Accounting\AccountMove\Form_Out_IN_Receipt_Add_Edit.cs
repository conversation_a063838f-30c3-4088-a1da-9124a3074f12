﻿using Dapper;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting.AccountMove
{
    public partial class Form_Out_IN_Receipt_Add_Edit : RJChildForm
    {
        string Filter_Type = "Out";
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes = false;
        Entities.Accounts.Account account;

        Entities.Accounts.AccountMove accountMove;
        List<Entities.Accounts.AccountMove> receiptDetails = new List<Entities.Accounts.AccountMove>();
        Entities.Accounts.AccountMove receipt = new Entities.Accounts.AccountMove();


        //public Form_Out_IN_Receipt_Add_Edit(string _Type = "Out")
        //{
        //    InitializeComponent();
        //    Smart_DA = new Smart_DataAccess();
        //    Filter_Type = _Type;
        //    this.Text = "سند صرف";
        //    lblTitle.Text = "اضافة سند صرف";

        //    if (Filter_Type == "In")
        //    {
        //        this.Text = "سند قبض";
        //        lblTitle.Text = "اضافة سند قبض";
        //    }

        //    Get_CashPayment();
        //    Get_Party_Account();
        //    Get_Expense_Income();
        //    InitializeControls();
        //    Set_Font();

        //    ConfigureDataGridView();
            
        //    //Cbox_Type_Receipt.SelectedIndex = 0;
        //    //Set_Font();
        //}

        public Form_Out_IN_Receipt_Add_Edit(Entities.Accounts.AccountMove _accountMove=null, string _Type = "Out")
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            accountMove = _accountMove;
            //account = _account;
            add = false;

            Get_CashPayment();
            Get_Party_Account();
            Get_Expense_Income();
            Set_Font();



            LoadReceiptData();
            ConfigureDataGridView();

            Cbox_Type_Receipt.Enabled = false;
            Cbox_CashPayment.Enabled = false;
        }

        private void InitializeControls()
        {
            txt_code.Text = (Smart_DA.Get_BatchCards_My_Sequence("AccountMove") + 1).ToString();
            date.Value = DateTime.Now;
            Cbox_Type_Receipt.SelectedIndex = 0;
        }

        private void LoadReceiptData()
        {
            if (accountMove != null)
            {
                txt_code.Text = accountMove.Code;
                date.Value = accountMove.Date;
                txt_Ref.Text = accountMove.Ref;

                if (accountMove.Move_type == "in_receipt")
                    Cbox_Type_Receipt.SelectedIndex = 1;
                else
                    Cbox_Type_Receipt.SelectedIndex = 0;

                if (Cbox_CashPayment.Items.Count > 0)
                {
                    var cashPayment = ((KeyValuePair<int, string>)Cbox_CashPayment.SelectedItem);
                    if (cashPayment.Key == accountMove.AccountId)
                        Cbox_CashPayment.SelectedValue = accountMove.AccountId;
                }

                if (Cbox_Party.Items.Count > 0)
                {
                    var party = ((KeyValuePair<int, string>)Cbox_Party.SelectedItem);
                    if (party.Key == accountMove.PartnerId)
                        Cbox_Party.SelectedValue = accountMove.PartnerId;
                }

                Get_Items();
            }
        }

        private void Get_CashPayment()
        {
            try
            {

                var partner = Smart_DA.Load<Entities.Accounts.Partner>($"select * from Partner where Partner_type=1 and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                Cbox_CashPayment.Items.Clear();

                foreach (Entities.Accounts.Partner _account in partner)
                {
                    Cbox_CashPayment.Items.Add(_account);
                }

                if (account != null)
                {
                    Cbox_CashPayment.SelectedItem = account;
                }

            }
            catch { }
            //try
            //{
            //    var sp = Smart_DA.Load<Entities.Accounts.Partner>($"select * from Partner where Partner_type=1 and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
            //    if (sp == null)
            //        return;

            //    Dictionary<int, string> comboSource = new Dictionary<int, string>();
            //    if (sp != null)
            //    {
            //        foreach (Entities.Accounts.Partner s in sp)
            //            comboSource.Add(s.Id, s.Name);

            //        Cbox_CashPayment.DataSource = new BindingSource(comboSource, null);
            //        Cbox_CashPayment.DisplayMember = "Value";
            //        Cbox_CashPayment.ValueMember = "Key";
            //        Cbox_CashPayment.SelectedIndex = -1;
            //        Cbox_CashPayment.Text = "";
            //        //Cbox_CashPayment.SelectedValue = null;
            //    }
            //}
            //catch { }
        }

        private void Get_Party_Account()
        {
            try
            {
                var sp = Smart_DA.Load<Entities.Accounts.Partner>($"select * from Partner where Partner_type IN (2,5,6,9) and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                if (sp == null)
                    return;

                Dictionary<int, string> comboSource = new Dictionary<int, string>();
                if (sp != null)
                {
                    foreach (Entities.Accounts.Partner s in sp)
                        comboSource.Add(s.Id, s.Name);

                    Cbox_Party.DataSource = new BindingSource(comboSource, null);
                    Cbox_Party.DisplayMember = "Value";
                    Cbox_Party.ValueMember = "Key";
                    Cbox_Party.SelectedIndex = -1;
                    Cbox_Party.Text = "";
                    //Cbox_Party.SelectedValue = null;


                }
            }
            catch { }
        }

        private void Get_Expense_Income()
        {
            try
            {
                List<Entities.Accounts.Account> C_accont = null;
                if (Filter_Type == "Out")
                {
                    C_accont = Smart_DA.Load<Entities.Accounts.Account>($"select * from Account where AccountType IN ('1','3') and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                }
                else if (Filter_Type == "In")
                {
                    C_accont = Smart_DA.Load<Entities.Accounts.Account>($"select * from Account where AccountType='9' and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                }

                Dictionary<int, string> comboSource = new Dictionary<int, string>();
                if (C_accont != null)
                {
                    foreach (Entities.Accounts.Account s in C_accont)
                        comboSource.Add(s.Id, s.Name);

                    Cbox_Expense_Income.DataSource = new BindingSource(comboSource, null);
                    Cbox_Expense_Income.DisplayMember = "Value";
                    Cbox_Expense_Income.ValueMember = "Key";
                    Cbox_Expense_Income.SelectedIndex = -1;
                    //Cbox_Expense_Income.SelectedValue = null;
                    Cbox_Expense_Income.Text = "";


                }
            }
            catch { }
        }
        private void Get_Items()
        {
            try
            {
                ConfigureDataGridView();

                if (!add)
                {
                    List<Entities.Accounts.AccountMove> C_accont = Smart_DA.Load<Entities.Accounts.AccountMove>($"select * from AccountMove where AccountId={accountMove.Id} and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                    dgv.DataSource = C_accont;
                    receiptDetails = C_accont;

                    if (C_accont != null)
                    {
                        ConfigureDataGridView();
                    }
                }
                else
                {
                    ConfigureDataGridView();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void ConfigureDataGridView()
        {
            string accountTitle = "بند المصروف";
            if (Cbox_Expense_Income.SelectedIndex == 1)
                accountTitle = "بند الايراد";

            try
            {
                dgv.AutoGenerateColumns = false;
                dgv.Columns.Clear();

                dgv.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "PartnerName",
                    HeaderText = "الجهة",
                    DataPropertyName = "PartnerId",
                    Width = 200
                });


                // إضافة الأعمدة المطلوبة
                dgv.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "AccountName",
                    HeaderText = accountTitle,
                    DataPropertyName = "AccountId",
                    Width = 200
                });


                dgv.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "price",
                    HeaderText = "المبلغ",
                    DataPropertyName = "price",
                    Width = 150,
                    DefaultCellStyle = new DataGridViewCellStyle { Format = "N2" }
                });

                dgv.Columns.Add(new DataGridViewTextBoxColumn
                {
                    Name = "Ref",
                    HeaderText = "البيان",
                    DataPropertyName = "Ref",
                    Width = 200
                });

                // تحديث البيانات
                dgv.DataSource = null;
                dgv.DataSource = receiptDetails;

                // تحديث أسماء الحسابات والأطراف
                foreach (DataGridViewRow row in dgv.Rows)
                {
                    var detail = (Entities.Accounts.AccountMove)row.DataBoundItem;
                    row.Cells["AccountName"].Value = GetAccountName(detail.AccountId);
                    row.Cells["PartnerName"].Value = GetPartnerName(detail.PartnerId);
                }
            }
            catch { }
        }

        private void Set_Font()
        {

            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel5.Font = rjLabel1.Font = rjLabel3.Font = rjLabel5.Font = rjLabel7.Font =
           txt_code.Font = rjLabel9.Font =
           rjLabel1.Font = rjLabel2.Font =
           rjLabel4.Font = rjLabel6.Font =
           rjLabel7.Font = rjLabel8.Font =
           lbl_font;
            this.Focus();

            Cbox_CashPayment.Font =
            Cbox_Party.Font =
            Cbox_Type_Receipt.Font =
            Cbox_Expense_Income.Font =
                Program.GetCustomFont(Resources.DroidSansArabic, 10, FontStyle.Regular);

            utils utils = new utils();
            utils.Control_textSize1(this);
        }
        private string GetAccountName(int? accountId)
        {
            try
            {
                var account = Smart_DA.LoadSingleById<Entities.Accounts.Account>(accountId.ToString(), "Account");
                return account?.Name ?? "";
            }
            catch
            {
                return "";
            }
        }

        private string GetPartnerName(int? partnerId)
        {
            try
            {
                var partner = Smart_DA.LoadSingleById<Entities.Accounts.Partner>(partnerId.ToString(), "Partner");
                return partner?.Name ?? "";
            }
            catch
            {
                return "";
            }
        }

        private bool ValidateDetailInput()
        {
            if (Cbox_Expense_Income.SelectedValue == null)
            {
                if (Cbox_Expense_Income.SelectedIndex == 0)
                    RJMessageBox.Show("الرجاء اختيار بند المصروف");
                else
                    RJMessageBox.Show("الرجاء اختيار بند الايراد");

                return false;
            }

            if (string.IsNullOrEmpty(txt_Price.Text))
            {
                RJMessageBox.Show("الرجاء ادخال المبلغ");
                return false;
            }

            if (!decimal.TryParse(txt_Price.Text, out decimal price) || price <= 0)
            {
                RJMessageBox.Show("الرجاء ادخال مبلغ صحيح");
                return false;
            }

            return true;
        }

        private Entities.Accounts.AccountMove CreateReceiptParent()
        {
            try
            {
                double credit = 0; double debit = 0;

                if (Cbox_Expense_Income.SelectedIndex == 0)
                    credit = Convert.ToDouble(txt_Price.Text);
                else
                    debit = Convert.ToDouble(txt_Price.Text);


                return new Entities.Accounts.AccountMove
                {
                    AccountId = Convert.ToInt32(Cbox_CashPayment.SelectedValue),
                    PartnerId = Convert.ToInt32(Cbox_Party.SelectedValue),
                    Move_type = "out_receipt",
                    Date = date.Value,
                    debit = debit,
                    credit = credit,
                    Ref = txt_Ref2.Text,
                    Rb = Global_Variable.Mk_resources.RB_SN
                };
            }
            catch
            {
                RJMessageBox.Show("حدث خطأ أثناء إنشاء تفاصيل السند");
                return null;
            }
        }
        private Entities.Accounts.AccountMove CreateReceiptDetail()
        {
            try
            {
                double credit = 0; double debit = 0;

                if (Cbox_Expense_Income.SelectedIndex == 0)
                    credit = Convert.ToDouble(txt_Price.Text);
                else
                    debit = Convert.ToDouble(txt_Price.Text);


                return new Entities.Accounts.AccountMove
                {

                    AccountId = Convert.ToInt32(Cbox_CashPayment.SelectedValue),
                    PartnerId = Convert.ToInt32(Cbox_Party.SelectedValue),
                    Move_type = "out_receipt",
                    Date = date.Value,
                    debit = debit,
                    credit = credit,
                    Ref = txt_Ref2.Text,
                    //ParentId= (int?)accountMove.Sequence,
                    Rb = Global_Variable.Mk_resources.RB_SN

                };
            }
            catch
            {
                RJMessageBox.Show("حدث خطأ أثناء إنشاء تفاصيل السند");
                return null;
            }
        }

        private void RefreshDataGrid()
        {
            dgv.DataSource = null;
            dgv.DataSource = receiptDetails;
            ConfigureDataGridView();
        }

        private void ClearDetailFields()
        {
            txt_Price.Text = "";
            txt_Ref2.Text = "";
            Cbox_Expense_Income.SelectedIndex = -1;
        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var detail = (Entities.Accounts.AccountMove)dgv.Rows[e.RowIndex].DataBoundItem;
                if (detail != null)
                {
                    // تحديث حقول التفاصيل بالقيم المحددة
                    Cbox_Expense_Income.SelectedValue = detail.AccountId;
                    Cbox_Party.SelectedValue = detail.PartnerId;
                    txt_Price.Text = detail.credit.ToString();
                    txt_Ref2.Text = detail.Ref;
                }
            }
        }

        private void dgv_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Delete && dgv.SelectedRows.Count > 0)
            {
                if (RJMessageBox.Show("هل تريد حذف السطر المحدد؟", "تأكيد", MessageBoxButtons.YesNo) == DialogResult.Yes)
                {
                    var detail = (Entities.Accounts.AccountMove)dgv.SelectedRows[0].DataBoundItem;
                    receiptDetails.Remove(detail);
                    RefreshDataGrid();
                }
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (ValidateForm())
                {
                    if (add)
                        SaveNewReceipt2();

                    else
                        UpdateReceipt();

                    succes = true;
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show(ex.Message);
            }
        }

        private bool ValidateForm()
        {
            if (Cbox_CashPayment.Text == "")
            {
                RJMessageBox.Show("الرجاء اختيار الخزينة");
                return false;
            }

            if (Cbox_Type_Receipt.Text == "")
            {
                RJMessageBox.Show("الرجاء اختيار نوع السند");
                return false;
            }

            if (receiptDetails.Count == 0)
            {
                RJMessageBox.Show("الرجاء اضافة تفاصيل السند");
                return false;
            }
            if (!decimal.TryParse(txt_Price.Text, out decimal debit))
            {
                RJMessageBox.Show("الرجاء كتابة المبلغ بطريقه صحيحه");
                return false;
            }
            if (debit == 0)
            {
                MessageBox.Show("الرجاء إدخال  مبلغ", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        private void SaveNewReceipt()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                // Save main receipt
                var receipt = new Entities.Accounts.AccountMove
                {
                    Sequence = Convert.ToInt32(txt_code.Text),
                    Date = date.Value,
                    Ref = txt_Ref.Text,
                    AccountId = Convert.ToInt32(Cbox_CashPayment.SelectedValue),
                    PartnerId = Convert.ToInt32(Cbox_Party.SelectedValue),
                    Move_type = Cbox_Type_Receipt.SelectedIndex == 0 ? "out_receipt" : "in_receipt",
                    Rb = Global_Variable.Mk_resources.RB_SN
                };

                List<string> fields = new List<string> { "Sequence", "Date", "Ref", "AccountId", "PartnerId", "Move_type", "Rb" };
                int receiptId = Smart_DA.InsertTable(fields, receipt, "AccountMove");

                if (receiptId > 0)
                {
                    fields.Add("ParentId");
                    // Save receipt details
                    foreach (var detail in receiptDetails)
                    {
                        detail.ParentId = receiptId;
                        int receiptIdDetail = Smart_DA.InsertTable(fields, detail, "AccountMove");
                    }


                    Smart_DA.Update_MySequence("AccountMove", Convert.ToInt32(txt_code.Text));
                    RJMessageBox.Show("تم حفظ السند بنجاح");
                }
                else
                {
                    RJMessageBox.Show("حدث خطأ أثناء حفظ السند");
                }
            }
        }


        private void SaveNewReceipt2()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                // Save main receipt
                var receipt = new Entities.Accounts.AccountMove
                {
                    //Sequence = Convert.ToInt32(txt_code.Text),
                    Date = date.Value,
                    Ref = txt_Ref.Text,
                    //AccountId = Convert.ToInt32(Cbox_CashPayment.SelectedValue),
                    PartnerId = Convert.ToInt32(Cbox_Party.SelectedValue),
                    Move_type = Cbox_Type_Receipt.SelectedIndex == 0 ? "out_receipt" : "in_receipt",
                    Rb = Global_Variable.Mk_resources.RB_SN,
                    Details = receiptDetails
                };

                CreateJournalEntry(receipt);

                //List<string> fields = new List<string> { "Sequence", "Date", "Ref", "AccountId", "PartnerId", "Move_type", "Rb" };
                //int receiptId = Smart_DA.InsertTable(fields, receipt, "AccountMove");

                //if (receiptId > 0)
                //{
                //    //fields.Add("ParentId");
                //    // Save receipt details
                //    //foreach (var detail in receiptDetails)
                //    //{
                //    //    detail.ParentId = receiptId;
                //    //    int receiptIdDetail = Smart_DA.InsertTable(fields, detail, "AccountMove");


                //    //}


                //    //Smart_DA.Update_MySequence("AccountMove", Convert.ToInt32(txt_code.Text));
                //    //RJMessageBox.Show("تم حفظ السند بنجاح");
                //}
                //else
                //{
                //    RJMessageBox.Show("حدث خطأ أثناء حفظ السند");
                //}
            }
        }


        public bool CreateJournalEntry(Entities.Accounts.AccountMove entry)
        {
            try
            {
                using (var connection = Smart_DataAccess.GetConnSmart())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            // Insert journal entry
                            var result = connection.Execute(
                                @"INSERT INTO AccountMove ( Date, Ref, AccountId,PartnerId,Move_type,Rb)
                                  VALUES ( @Date, @Ref, @AccountId, @PartnerId, @Move_type, @Rb)",
                                new
                                {
                                    //entry.Sequence,
                                    entry.Date,
                                    entry.Ref,
                                    entry.AccountId,
                                    entry.PartnerId,
                                    entry.Move_type,
                                    entry.Rb,
                                    //CreatedAt = DateTime.Now
                                }
                            );

                            if (result == 0)
                            {
                                transaction.Rollback();
                                return false;
                            }

                            // Get the inserted entry ID
                            var entryId = connection.ExecuteScalar<int>("SELECT last_insert_rowid()");
                            var updateSequence = connection.Execute($@"update AccountMove set [Sequence]={entryId} where Id={entryId}");

                            // Insert details and update account balances
                            foreach (var detail in entry.Details)
                            {
                                // Insert detail
                                result = connection.Execute(
                                       @"INSERT INTO AccountMove (ParentId, Date, Ref, AccountId,PartnerId,Move_type,Rb)
                                            VALUES (@ParentId, @Date, @Ref, @AccountId, @PartnerId, @Move_type, @Rb)",
                                    new
                                    {

                                        entry.ParentId,
                                        entry.Date,
                                        entry.Ref,
                                        entry.AccountId,
                                        entry.PartnerId,
                                        entry.Move_type,
                                        entry.Rb,
                                        //CreatedAt = DateTime.Now
                                    }
                                );

                                if (result == 0)
                                {
                                    transaction.Rollback();
                                    return false;
                                }

                                //// Update account balance
                                //_accountService.UpdateAccountBalance(detail.AccountId, detail.Debit, true);
                                //_accountService.UpdateAccountBalance(detail.AccountId, detail.Credit, false);
                            }

                            transaction.Commit();
                            return true;
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (SQLiteException)
            {
                return false;
            }
        }

        private void UpdateReceipt()
        {
            lock (Smart_DataAccess.Lock_object)
            {
                // Update main receipt
                accountMove.Code = txt_code.Text;
                accountMove.Date = date.Value;
                accountMove.Ref = txt_Ref.Text;
                accountMove.AccountId = Convert.ToInt32(Cbox_CashPayment.SelectedValue);
                accountMove.PartnerId = Convert.ToInt32(Cbox_Party.SelectedValue);
                accountMove.Move_type = Cbox_Type_Receipt.SelectedIndex == 0 ? "Out" : "In";

                List<string> fields = new List<string> { "Code", "Date", "Ref", "AccountId", "PartnerId", "Move_type" };
                string sqlQuery = UtilsSql.GetUpdateSql<Entities.Accounts.AccountMove>("AccountMove", fields, $" where Id={accountMove.Id} and Rb='{Global_Variable.Mk_resources.RB_SN}'");

                if (Smart_DA.UpateTable(accountMove, sqlQuery) > 0)
                {
                    // Delete old details
                    Smart_DA.Execute($"DELETE FROM AccountMove WHERE AccountId={accountMove.Id} and Rb='{Global_Variable.Mk_resources.RB_SN}'");

                    // Save new details
                    foreach (var detail in receiptDetails)
                    {
                        detail.AccountId = accountMove.Id;
                        Smart_DA.InsertTable(fields, detail, "AccountMove");
                    }

                    RJMessageBox.Show("تم تحديث السند بنجاح");
                }
                else
                {
                    RJMessageBox.Show("حدث خطأ أثناء تحديث السند");
                }
            }
        }

        private void Form_Out_IN_Receipt_Add_Edit_Load(object sender, EventArgs e)
        {
            dgv.CellClick += dgv_CellClick;
            dgv.KeyDown += dgv_KeyDown;
        }
        private DataTable dt_table()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الجهه", typeof(string));
            dt.Columns.Add("البند", typeof(string));
            dt.Columns.Add("المبلغ", typeof(double));
            dt.Columns.Add("البيان", typeof(string));

            return dt;
        }
        private void btnAdd_Click_1(object sender, EventArgs e)
        {
            if (Cbox_Party.Text == "")
            {
                RJMessageBox.Show("حدد الجهه المستفيده");
                return;
            }
            if (Cbox_Expense_Income.Text == "")
            {
                RJMessageBox.Show("حدد بند السند");
                return;
            }

            receiptDetails.Add(CreateReceiptDetail());
            dgv.DataSource = receiptDetails;
        }
        





    }
}
