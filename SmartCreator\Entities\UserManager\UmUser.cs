﻿//using ServiceStack.DataAnnotations;
//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using System;
using System.Collections.Generic;
using System.ComponentModel;
//using System.ComponentModel.DataAnnotations.Schema;

//using System.ComponentModel.DataAnnotations.Schema;
using System.Data;
using System.Data.SqlClient;


//using System.ComponentModel.DataAnnotations;
//using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.UI.Design.Directives;
using System.Windows.Forms;

namespace SmartCreator.Entities.UserManager
{

    //CREATE INDEX  IF NOT EXISTS UmUser_idx_0056e50e ON UmUser(SpCode);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_02eb6568 ON UmUser(SN DESC);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_d5970c3d ON UmUser(FirsLogin);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_e7c209d4 ON UmUser(NasPortId);

    //CREATE INDEX  IF NOT EXISTS UmUser_idx_07fd7236 ON UmUser(Status, SpCode);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_47c65bd0 ON UmUser(Status, SN DESC);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_188647fd ON UmUser(NasPortId, FirsLogin);

    //CREATE INDEX  IF NOT EXISTS UmUser_idx_49fb733a ON UmUser(DeleteFromServer, SN DESC);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_8c6ed1da ON UmUser(DeleteFromServer, Sn_Name DESC);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_5be90d09 ON UmUser(SpCode, ProfileName, BatchCardId);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_f28dcfa3 ON UmUser(DeleteFromServer, BatchCardId, Status);

    //CREATE INDEX  IF NOT EXISTS UmUser_idx_38953c22 ON UmUser(DeleteFromServer, Status, SN DESC);
    //CREATE INDEX  IF NOT EXISTS UmUser_idx_637a3bf8 ON UmUser(DeleteFromServer, ProfileName, Status, SN DESC);

    //[System.Reflection.Obfuscation(ApplyToMembers = false)]


    [CompositeIndex("SN DESC")]
    [CompositeIndex("Status", "SN DESC")]
    [CompositeIndex("NasPortId", "FirsLogin")]

    [CompositeIndex("DeleteFromServer", "SN DESC")]
    [CompositeIndex("DeleteFromServer", "Sn_Name DESC")]
    [CompositeIndex("SpCode", "ProfileName", "BatchCardId")]
    [CompositeIndex("DeleteFromServer", "BatchCardId", "Status")]

    //[CompositeIndex("DeleteFromServer", "Status", "SN DESC")]
    //[CompositeIndex("DeleteFromServer", "ProfileName", "Status", "SN DESC")]


    //[System.Reflection.Obfuscation(Feature = "renaming")]
    public class UmUser: BaseCard
    {

        //[PrimaryKey, AutoIncrement, Required,Unique]
        //public int Id { get; set; }

        [DisplayName("الحالة"), Computed]
        public string Str_Status
        {

            get
            {
                //string s = "";
                //if (DeleteFromServer == 1)
                //    return "محذوف";

                //if (ActiveSessions == 1)
                //    return "نشط + اونلاين";
                //if (Status == 0)
                //    s = "انتظار";
                //else if (Status == 2)
                //    s = "منتهي الصلاحية";
                //else if (Status == 3)
                //    s = "خطأ في الباقة";
                //else if (Status == 1)
                //{
                //    if ((UptimeLimit > 0 && UptimeUsed > 0) && ((UptimeLimit - UptimeUsed <= 0)))
                //        s = "منهي الوقت";
                //    else if ((TransferLimit > 0 && (UploadUsed + DownloadUsed) > 0) && ((UptimeLimit - UptimeUsed <= 0)))
                //        s = "منهي التنزيل";
                //    else
                //        s = "نشط";
                //}

                if (DeleteFromServer == 1)
                    return ("محذوف");
                    //return ("مؤرشف");
                //return s + (" + مؤرشف");

                if (ActiveSessions == 1)
                    return "نشط + اونلاين";
                string s = (Status == 0 ? "انتظار" : (Status == 1 ? "نشط" : (Status == 2 ? "منتهي" : (Status == 3 ? "خطأ في الباقة" : ""))));
                if (Disabled == 1)
                    return s + (" + معطل");

                return s;
            }
        }
        [StringLength(200), Browsable(false)] 
        public string Radius { get; set; }
        [ DisplayName("مستخدم يوزمنجر"), StringLength(200)]
        public string CustomerName { get; set; }
        [Browsable(false)]
        public string FirstName { get; set; }
        [Browsable(false)]
        public string LastName { get; set; }
        [Browsable(false)]
        public string Phone { get; set; }
        [Browsable(false)]
        public string Location { get; set; }
        [Browsable(false)]
        public string SharedUsers { get; set; }

        [DisplayName("اخر ضهور")]
        public DateTime? LastSeenAt { get; set; }

        [Default(0),Browsable(false)]
        public int? ActiveSessions { get; set; }=0;
        //public bool? ActiveSessions { get; set; }=false;

        //[Computed]
        [/*Reference,*/ Browsable(false)]
        [Computed]

        public ICollection<UmPyment> UmPyments { get; set; }
        //public virtual ICollection<UmPyment> UmPyments { get; set; }
        
        [/*Reference,*/ Browsable(false)]
        [Computed]
        public  ICollection<UmSession> UmSessions { get; set; }

        public static implicit operator DataGridViewRow(UmUser v)
        {
            throw new NotImplementedException();
        }


        //public UmUser()
        //{
        //    UmPyments = new HashSet<UmPyment>();
        //    UmSessions = new HashSet<UmSession>();
        //}
    }

    public class UMUser_Finsh_Cards :UmUser
    {
        public new string Str_Status
        {

            get
            {
                string s = "";
                //if (DeleteFromServer == 1)
                //    return "محذوف";

                //if (ActiveSessions == 1)
                //    return "نشط + اونلاين";
                if (Status == 0)
                    s = "انتظار";
                else if (Status == 2)
                    s = "منتهي الصلاحية";
                else if (Status == 3)
                    s = "خطأ في الباقة";
                else if (Status == 1)
                {
                    if ((UptimeLimit > 0 && UptimeUsed > 0) && ((UptimeLimit - UptimeUsed <= 0)))
                        s = "منتهي الوقت";
                    else if ( (TransferLimit > 0 &&  DownloadUsed > 0 ) && ((TransferLimit - (DownloadUsed+UploadUsed) <= 0)))
                        s = "منتهي التحميل";
                    else
                        s = "نشط";
                }

                //Status = 2;

                //if (ActiveSessions == 1)
                //    return "نشط + اونلاين";
                //string s = (Status == 0 ? "انتظار" : (Status == 1 ? "نشط" : (Status == 2 ? "منتهي" : (Status == 3 ? "خطأ في الباقة" : ""))));
                if (Disabled == 1)
                    return s + (" + معطل");

                return s;
            }
        }

    }
}
