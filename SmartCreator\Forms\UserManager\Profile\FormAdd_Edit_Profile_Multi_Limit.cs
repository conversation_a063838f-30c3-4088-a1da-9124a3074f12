﻿using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using tik4net;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAdd_Edit_Profile_Multi_Limit : RJForms.RJChildForm
    {
        UmProfile profile_eidt;
        public bool succes = false;
        public bool add = true;
        public FormAdd_Edit_Profile_Multi_Limit()
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            
            btnSave.BackColor = RJColors.Confirm;
            btnAddLimit.BackColor = RJColors.Confirm;
            lblTitle.Text = "Add new profile";
            this.Text = "Add new profile";
            btnSave.Text = "Add";

            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "اضافة باقة جديد";
                this.Text = "اضافة باقة";
                btnSave.Text = "اضافة";
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12, FontStyle.Regular);
                //lblTitle.Font = title_font;
                //btnSave.Font = title_font;
            }
            //txt_Download.Text = "0";
            //txt_SpeedUpload.Text = "0";
            //txt_SpeedDown.Text = "0";
            txt_price.Text = "0";
            txt_price_display.Text = "0";
            //txt_uptime_Hour.Text = "0";
            //txt_uptime_Minut.Text = "0";
            txt_Validity.Text = "0";
            //txt_precent.Text = "0";
            //CBox_SizeDownload.SelectedIndex = 0;
            //comboBoxShardUser.SelectedIndex = 0;
            //CBox_speedDownlad.SelectedIndex = 0;
            //CBox_speedUpload.SelectedIndex = 0;

            //CBox_profile_hotspot.Text = "default";
            try
            {
                //CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                //CBox_profile_hotspot.DisplayMember = "Name";
                //CBox_profile_hotspot.ValueMember = "ID";

                //CBox_profile_hotspot.SelectedIndex = 0;

            }
            catch { }

            Set_Font();

            //txt_from.Text = "00:00:00";
            //txt_to.Text = "23:59:59";
            //timer1.Start();

        }

        public FormAdd_Edit_Profile_Multi_Limit(UmProfile profile)
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            Set_Font();
            btnSave.BackColor = UIAppearance.StyleColor;
            btnAddLimit.BackColor = UIAppearance.StyleColor;
            dgv.DataSource = profile.Limits;

            DataGridViewButtonColumn EditButton = new DataGridViewButtonColumn();
            EditButton.FlatStyle = FlatStyle.Popup;
            EditButton.Name = "Edit";
            EditButton.UseColumnTextForButtonValue = true;
            EditButton.HeaderText = " ";
            EditButton.DataPropertyName = "btnEdit";
            EditButton.Text = "تعديل";

            DataGridViewButtonColumn DeletButton = new DataGridViewButtonColumn();
            DeletButton.FlatStyle = FlatStyle.Popup;
            DeletButton.Name = "DeletButton";
            DeletButton.UseColumnTextForButtonValue = true;
            DeletButton.HeaderText = " ";
            DeletButton.DataPropertyName = "DeletButton";
            DeletButton.Text = "حذف";

            dgv.Columns.Add(EditButton);
            dgv.Columns.Add(DeletButton);


            //txt_from.Text = "00:00:00";
            //txt_to.Text = "23:59:59";

            profile_eidt = profile;
            lblTitle.Text = "Edit profile";
            this.Text = "Edit profile";
            btnSave.Text = "edit";
            btnSave.BackColor = RJColors.Confirm;


            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "تعديل الباقة ";
                this.Text = "تعديل باقة";
                btnSave.Text = "تعديل";
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12, FontStyle.Regular);
                //lblTitle.Font = title_font;
                //btnSave.Font = title_font;

                //System.Drawing.Font DGV_font = Program.GetCustomFont(Resources.Cairo_Medium, 10, FontStyle.Regular);
                //dgv.Font = DGV_font;
                //btnAddLimit.Font = DGV_font;
                //dgv.ColumnHeaderHeight = 30;
            }



            txt_profileName.Text = profile.Name;
            txt_profileName.Enabled = false;
            //txt_profileName.BackColor = RJColors.DarkTextColor;
            txt_profileName.BackColor = Color.FromArgb(240, 245, 249);

            //txt_profileName.Font = RJColors.DarkTextColor;
            txt_Validity.Text = profile.Validity.ToString();
            comboBoxShardUser.Text = profile.SharedUsers;
            txt_price.Text = profile.Price.ToString();
            txt_price_display.Text = profile.Price_Disply;
            //txt_precent.Text = "0";

            dgv.Columns["Id"].Visible = false;
            dgv.Columns["IdHX"].Visible = false;
            dgv.Columns["Sn_Name"].Visible = false;


            CBOX_precent_type.SelectedIndex = profile.PercentageType;
            txt_precent.Text = profile.Percentage.ToString();
            rjCheckBox_precent.Check = Convert.ToBoolean(profile.Is_percentage);

        }

        private void Set_Font()
        {
            Font fnt= Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                         lbl.Font = fnt;
                    }
                }
                catch { }
            }
            rjCheckBox_precent.Font = fnt;
            CBOX_precent_type.Font= fnt;
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);
            lblTitle.Font = title_font;
            btnSave.Font = btnSave.Font = btnAddLimit.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9, FontStyle.Bold); ;
            //btnAddLimit.Font = title_font;

            //lblTitle.Font = btnSave.Font = Program.GetCustomFont(Resources.Cairo_ExtraBold, 14, FontStyle.Bold);


            dgv.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv.ColumnHeadersHeight = (int)(40 );

            //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size, dgv.DefaultCellStyle.Font.Style);

            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);

            utils utils = new utils();
            utils.Control_textSize1(this);


            //Control_Loop(pnlClientArea);

        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size, C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Get_UMCustomer()
        {
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                //CBox_Customer.Text = "";
            }
            catch { }
        }

        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (check() == false)
                return;
            succes = true;
            this.Close();

            //Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            //if (smart_DataAccess._Edit_UserManager_Limition(connection, prf))
            //{
            //    if (dataAccess._Edit_UserManager_Profile_Limition(connection, prf))

            //        RJMessageBox.Show("تمت العملية بنجاح");
            //}
            //succes = false;
        }
        [Obsolete]
        bool check()
        {
            if (txt_profileName.Text == "")
            {
                RJMessageBox.Show("اكتب اسم البروفايل");
                return false;
            }
            var item = Global_Variable.UM_Profile.SingleOrDefault(x => x.Name == txt_profileName.Text);
            if (item != null && add)
            {
                RJMessageBox.Show("اسم البروفايل مكرر");
                return false;
            }

            int number2;
            if (!(int.TryParse(txt_Validity.Text, out number2)))
            {
                RJMessageBox.Show("الصلاحية يجب ان تكون رقم");
                return false;
            }


            if (!(int.TryParse(txt_price.Text, out number2)))
            {
                RJMessageBox.Show("السعر يجب ان تكون رقم");
                return false;
            }


            if (!(int.TryParse(comboBoxShardUser.Text, out number2)))
            {
                if (comboBoxShardUser.Text != "off")
                {
                    RJMessageBox.Show("عدد المستخدمين غير صحيح");
                    return false;
                }

            }
            if (CBox_Customer.Text == "" || CBox_Customer.SelectedIndex == -1)
            {
                if (Global_Variable.Mk_resources.version <= 6)
                {
                    RJMessageBox.Show("اختر مستخدم اليوزمنجر");
                    return false;
                }
            }

            //=========================================
            UmProfile prf = new UmProfile();
            prf.Name = txt_profileName.Text.Trim();
            prf.Price = Convert.ToInt32(txt_price.Text);
            prf.Price_Disply = (txt_price_display.Text);
            prf.Str_Validity = txt_Validity.Text + "d";
            prf.Validity = Convert.ToInt32(txt_Validity.Text);
            if (comboBoxShardUser.SelectedIndex != 0)
                prf.SharedUsers = comboBoxShardUser.Text;
            prf.Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check);
            prf.Percentage = Convert.ToInt32(txt_precent.Text);
            prf.PercentageType = CBOX_precent_type.SelectedIndex;

            prf.Owner = CBox_Customer.Text;
            //============================

            prf.Str_uptimeLimit = prf.UptimeLimit.ToString();
            prf.Owner = prf.Owner;
            Mk_DataAccess dataAccess = new Mk_DataAccess();

            prf.IdHX = profile_eidt.IdHX;
            prf.Sn_Name = profile_eidt.Sn_Name;
            prf.IdHX_limt = profile_eidt.IdHX_limt;
            prf.IdHX_prfileLimt = profile_eidt.IdHX_prfileLimt;
            prf.Id=profile_eidt.Id;

            using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
            {
                if (Mk_DataAccess.Mk_Conn(connection) == false)
                    return false;

                if (dataAccess._Edit_UserManager_Profile(connection, prf))
                {
                    Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                    List<string> Fields = new List<string>();
                    string[] aFields = { "Is_percentage", "Price_Disply", "Percentage", "PercentageType" };
                    Fields.AddRange(aFields);
                    lock (Smart_DataAccess.Lock_object)
                    {
                        //var data = new UmProfile
                        //{
                        //    Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check),
                        //    Price_Disply = txt_price_display.Text,
                        //    Percentage = (float)Convert.ToDouble(txt_precent.Text),
                        //    PercentageType = CBOX_precent_type.SelectedIndex
                        //};
                        string sqlquery = UtilsSql.GetUpdateSql<UmProfile>("UmProfile", Fields, $" where Sn_Name=@Sn_Name and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) and DeleteFromServer=0");
                        //string sqlquery = UtilsSql.GetUpdateSql<UmProfile>(Fields);
                        int r = sql_DataAccess.UpateTable(prf, sqlquery);
                    }

                    RJMessageBox.Show("تمت العملية بنجاح");
                }
            }
            //======================================================================================
            succes = true;
            return true;
        }


        private void FormAdd_Edit_Profile_Multi_Limit_Load(object sender, EventArgs e)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                lbl_customer.Visible = false;
                CBox_Customer.Visible = false;
            }
            else
                Get_UMCustomer();
        }

        private void rjDataGridView1_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {

                string id = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
                UmLimitation umLimitation = Global_Variable.Source_limtition.Find(x => x.IdHX == id);
                UmProfile_Limtition ProfieLimitation = Global_Variable.Source_profile_limtition.Find(x => x.Profile == profile_eidt.Name && x.Limitation == umLimitation.Name);

                var frm = new Form_Edit_Limit_Profile(umLimitation, profile_eidt, ProfieLimitation);
                frm.add = false;
                frm.ShowDialog();
                if (frm.succes)
                {
                    //refersh_From_MK();
                    //loadData();
                    succes = true;
                    this.Close();
                }
            }
        }

        private void txt_price_onTextChanged(object sender, EventArgs e)
        {
            txt_price_display.Text = txt_price.Text;
        }

        private void btnAddLimit_Click(object sender, EventArgs e)
        {
            string id = profile_eidt.IdHX;
            var frm = new Form_Edit_Limit_Profile(profile_eidt);
            frm.add = true;
            frm.ShowDialog();
            if (frm.succes)
            {
                //refersh_From_MK();
                //loadData();
                succes = true;
                this.Close();
            }
        }

        [Obsolete]
        private void rjDataGridView1_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {
            // Check deleted rows
            if (e.RowIndex >= 0)
            {
                if (dgv.Columns[e.ColumnIndex].Name == "DeletButton")
                {
                    Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                    if (RJMessageBox.Show("هل متاكد من حذف التحديد ?", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question) == DialogResult.Yes)
                    {
                        string id = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
                        if (mk_DataAccess.DeletLimition_Usermanager(id))
                        {
                            string _name = dgv.CurrentRow.Cells["Name"].Value.ToString();

                            //UmLimitation umLimitation = Global_Variable.Source_limtition.Find(x => x.IdHX == id);
                            string ProfieLimitation = Global_Variable.Source_profile_limtition.Find(x => x.Profile == profile_eidt.Name && x.Limitation == _name).IdHX;
                            mk_DataAccess.Delet_ProfileLimition_Usermanager(ProfieLimitation);
                            succes = true;
                            this.Close();
                            return;
                        }
                    }
                }

                else if (dgv.Columns[e.ColumnIndex].Name == "Edit")
                {
                    string id = dgv.CurrentRow.Cells["IdHX"].Value.ToString();
                    UmLimitation umLimitation = Global_Variable.Source_limtition.Find(x => x.IdHX == id);
                    UmProfile_Limtition ProfieLimitation = Global_Variable.Source_profile_limtition.Find(x => x.Profile == profile_eidt.Name && x.Limitation == umLimitation.Name);

                    var frm = new Form_Edit_Limit_Profile(umLimitation, profile_eidt, ProfieLimitation);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        //refersh_From_MK();
                        //loadData();
                        succes = true;
                        this.Close();
                    }
                }
            }
        }
    }
}
