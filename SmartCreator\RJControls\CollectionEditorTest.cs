using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار Collection Editor المبسط
    /// </summary>
    public partial class CollectionEditorTest : Form
    {
        private SafeRJTabControl editorTestControl;

        public CollectionEditorTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // editorTestControl
            // 
            this.editorTestControl = new SafeRJTabControl();
            this.editorTestControl.Dock = DockStyle.Fill;
            this.editorTestControl.TabHeight = 45;
            this.editorTestControl.TabSpacing = 3;
            this.editorTestControl.TabPadding = 20;

            // 
            // CollectionEditorTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Controls.Add(this.editorTestControl);
            this.Name = "CollectionEditorTest";
            this.Text = "🎨 اختبار Collection Editor - SafeRJTabControl";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += CollectionEditorTest_Load;
        }

        private void CollectionEditorTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية
                AddEditorTestTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ Collection Editor جاهز!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل Collection Editor:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ Collection Editor فشل!";
            }
        }

        private void AddEditorTestTabs()
        {
            // تاب التعليمات
            var instructionsTab = this.editorTestControl.AddTab("التعليمات");
            instructionsTab.BackColor = Color.FromArgb(0, 122, 204);
            instructionsTab.ForeColor = Color.White;

            var instructionsLabel = new Label
            {
                Text = "🎨 اختبار Collection Editor:\n\n" +
                       "✅ تم إضافة SimpleTabCollectionEditor\n" +
                       "✅ خاصية Tabs تحتوي على [Editor] attribute\n" +
                       "✅ يمكن الآن النقر على [...] في Properties\n\n" +
                       "🧪 خطوات الاختبار في Designer:\n" +
                       "1. اسحب SafeRJTabControl من Toolbox\n" +
                       "2. افتح Properties Panel\n" +
                       "3. ابحث عن خاصية Tabs\n" +
                       "4. انقر على [...] بجانب Tabs\n" +
                       "5. يجب أن يفتح Collection Editor\n\n" +
                       "📊 النتائج المتوقعة:\n" +
                       "• إذا فتح Editor: المشكلة في RJTabPageCollectionEditor المعقد\n" +
                       "• إذا لم يفتح أو ظهر خطأ: المشكلة في Collection Editor نفسه\n\n" +
                       "🎯 هذا الاختبار سيحدد السبب الدقيق للمشكلة!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleLeft,
                Font = new Font("Segoe UI", 11),
                ForeColor = Color.White,
                Padding = new Padding(20)
            };
            instructionsTab.AddControl(instructionsLabel);

            // تاب الاختبار التفاعلي
            var interactiveTab = this.editorTestControl.AddTab("اختبار تفاعلي");
            interactiveTab.BackColor = Color.FromArgb(76, 175, 80);
            interactiveTab.ForeColor = Color.White;

            var interactivePanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var infoLabel = new Label
            {
                Text = "🎮 اختبار Collection Editor تفاعلي:\n\n" +
                       $"عدد التابات الحالي: {this.editorTestControl.Tabs.Count}\n" +
                       $"التاب النشط: {this.editorTestControl.SelectedIndex}\n\n" +
                       "استخدم الأزرار أدناه لاختبار الوظائف:",
                Location = new Point(0, 0),
                Size = new Size(800, 100),
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70)
            };

            var addTabButton = new RJButton
            {
                Text = "إضافة تاب عبر Collection",
                IconChar = IconChar.Plus,
                Location = new Point(20, 120),
                Size = new Size(250, 50),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            addTabButton.Click += AddTabViaCollection_Click;

            var removeTabButton = new RJButton
            {
                Text = "إزالة آخر تاب",
                IconChar = IconChar.Minus,
                Location = new Point(290, 120),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(244, 67, 54),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            removeTabButton.Click += RemoveLastTab_Click;

            var clearAllButton = new RJButton
            {
                Text = "مسح جميع التابات",
                IconChar = IconChar.Trash,
                Location = new Point(510, 120),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            clearAllButton.Click += (s, e) => {
                this.editorTestControl.Tabs.Clear();
                AddEditorTestTabs(); // إعادة إضافة التابات الأساسية
            };

            var statusLabel = new Label
            {
                Text = "💡 نصيحة: جرب أيضاً فتح Properties في Visual Studio\n" +
                       "والنقر على [...] بجانب خاصية Tabs لفتح Collection Editor",
                Location = new Point(20, 190),
                Size = new Size(700, 50),
                Font = new Font("Segoe UI", 10, FontStyle.Italic),
                ForeColor = Color.FromArgb(100, 100, 100)
            };

            interactivePanel.Controls.Add(infoLabel);
            interactivePanel.Controls.Add(addTabButton);
            interactivePanel.Controls.Add(removeTabButton);
            interactivePanel.Controls.Add(clearAllButton);
            interactivePanel.Controls.Add(statusLabel);
            interactiveTab.AddControl(interactivePanel);

            // تاب معلومات Collection Editor
            var editorInfoTab = this.editorTestControl.AddTab("معلومات Editor");
            editorInfoTab.BackColor = Color.FromArgb(63, 81, 181);
            editorInfoTab.ForeColor = Color.White;

            var editorInfoTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "🎨 معلومات SimpleTabCollectionEditor:\n\n" +
                       "📋 الميزات المضافة:\n" +
                       "• وراثة من CollectionEditor\n" +
                       "• CreateCollectionItemType() → RJTabPage\n" +
                       "• CreateNewItemTypes() → RJTabPage[]\n" +
                       "• CreateInstance() → إنشاء تاب جديد\n" +
                       "• GetDisplayText() → عرض اسم التاب\n" +
                       "• CanRemoveInstance() → السماح بالحذف\n" +
                       "• CanSelectMultipleInstances() → تحديد واحد فقط\n\n" +
                       "🔧 الإعدادات:\n" +
                       "• [Editor(typeof(SimpleTabCollectionEditor), typeof(UITypeEditor))]\n" +
                       "• [DesignerSerializationVisibility(DesignerSerializationVisibility.Content)]\n\n" +
                       "🎯 الهدف:\n" +
                       "تحديد ما إذا كانت المشكلة في Collection Editor نفسه\n" +
                       "أم في التعقيد الزائد في RJTabPageCollectionEditor الأصلي\n\n" +
                       "📊 مقارنة مع RJTabPageCollectionEditor:\n" +
                       "• SimpleTabCollectionEditor: مبسط وأساسي\n" +
                       "• RJTabPageCollectionEditor: معقد مع ميزات متقدمة\n\n" +
                       "🧪 النتيجة المتوقعة:\n" +
                       "إذا عمل SimpleTabCollectionEditor في Designer\n" +
                       "فالمشكلة في التعقيد الزائد في النسخة الأصلية\n\n" +
                       "⚠️ ملاحظة:\n" +
                       "إذا لم يعمل حتى هذا Editor المبسط\n" +
                       "فالمشكلة في شيء أساسي آخر في RJTabControl",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(63, 81, 181),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 9),
                TextAlign = HorizontalAlignment.Left
            };
            editorInfoTab.AddControl(editorInfoTextBox);

            // تفعيل التاب الأول
            this.editorTestControl.SelectedIndex = 0;
        }

        private void AddTabViaCollection_Click(object sender, EventArgs e)
        {
            try
            {
                var newTab = new RJTabPage($"Editor Tab {this.editorTestControl.Tabs.Count + 1}");
                newTab.BackColor = Color.FromArgb(233, 30, 99);
                newTab.ForeColor = Color.White;

                var label = new Label
                {
                    Text = $"🎨 تاب من Collection Editor!\n\n" +
                           $"رقم التاب: {this.editorTestControl.Tabs.Count + 1}\n" +
                           $"تم إنشاؤه في: {DateTime.Now:HH:mm:ss}\n\n" +
                           "✅ Collection مع Editor يعمل!",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 12, FontStyle.Bold),
                    ForeColor = Color.White
                };
                newTab.AddControl(label);

                // إضافة عبر Collection
                this.editorTestControl.Tabs.Add(newTab);

                // تفعيل التاب الجديد
                this.editorTestControl.SelectedIndex = this.editorTestControl.Tabs.Count - 1;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إضافة التاب عبر Collection:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void RemoveLastTab_Click(object sender, EventArgs e)
        {
            try
            {
                var collection = this.editorTestControl.Tabs;
                if (collection.Count > 1) // الاحتفاظ بتاب واحد على الأقل
                {
                    var lastTab = collection[collection.Count - 1];
                    collection.Remove(lastTab);
                }
                else
                {
                    MessageBox.Show("لا يمكن حذف جميع التابات!\nيجب الاحتفاظ بتاب واحد على الأقل.",
                                   "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إزالة التاب:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار Collection Editor
        /// </summary>
        public static void RunEditorTest()
        {
            try
            {
                var form = new CollectionEditorTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار Collection Editor:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
