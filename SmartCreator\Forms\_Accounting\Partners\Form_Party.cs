﻿using SmartCreator.Data;
using SmartCreator.Entities.Accounts;
using SmartCreator.Forms.Accounting;
using SmartCreator.Forms.Accounting.Partners;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting
{
    public partial class Form_Party : RJChildForm
    {
        Smart_DataAccess smart_DataAccess = null;
        public Form_Party()
        {
            InitializeComponent();
            smart_DataAccess = new Smart_DataAccess();
            this.Text = "الحسابات";

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            this.Text = "Accounts";
            if (UIAppearance.Language_ar)
            {
                this.Text = "الحسابات";
                //System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                rjLabel1.Font=rjLabel5.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                btnAddNew.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                utils utils = new utils();
                utils.Control_textSize1(this);
            }

        }
        private void getData()
        {
            try
            {
                
                var sp = smart_DataAccess.Load<Entities.Accounts.Partner>($"select * from Partner where Partner_type  IN (2,5,6,9) and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                dgv.DataSource = sp;
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.Visible = false;
                }

                try { dgv.Columns["Code"].Visible = true; } catch { }
                try { dgv.Columns["Name"].Visible = true; } catch { }
                try { dgv.Columns["Phone"].Visible = true; } catch { }
                try { dgv.Columns["Address"].Visible = true; } catch { }
                try { dgv.Columns["Str_Active"].Visible = true; } catch { }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }


        private void Form_Party_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjTextBox1.Refresh();
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            Form_Party_Add_Edit frm=new Form_Party_Add_Edit();
            frm.ShowDialog();
            if(frm.succes)
                getData();
        }

        private void Form_Party_Load(object sender, EventArgs e)
        {
            getData();
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count > 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Entities.Accounts.Partner>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "Partner");
                    var frm = new Form_Party_Add_Edit(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (RJMessageBox.Show("هل انت متاكد من الحذف", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                smart_DataAccess.DeleteById<Entities.Accounts.Partner>(dgv.CurrentRow.Cells["Id"].Value.ToString(), "Partner");
                getData();
            }
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Entities.Accounts.Partner>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "Partner");
                    var frm = new Form_Party_Add_Edit(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            getData();
        }
    }
}
