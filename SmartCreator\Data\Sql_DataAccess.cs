﻿using Dapper;
//using ServiceStack.OrmLite;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using static Microsoft.EntityFrameworkCore.DbLoggerCategory;
using iTextSharp.text.pdf;
using System.Security.Principal;
using System.Windows.Forms;
using SmartCreator.Models.hotspot;
using iTextSharp.text;
using System.Runtime.CompilerServices;
using System.Linq.Expressions;
using System.IO;
//using ServiceStack;
//using Mysqlx.Crud;

namespace SmartCreator.Data
{
    public class Sql_DataAccess  
    {
        public static int _dbType = 0;//sqlite
        public static string connection_string = @"";
        public static string db_File_Name = "";
        //public static string connection_string = @"Data Source=db\localDB21.db";
        public static string DefualtDBType = "SQLlite";
        public static string username_db = "";
        public static string password_db = "";
        public IDbConnection Connection_db;
        //private OrmLiteConnectionFactory dbFactory = null;

        public static readonly object Lock_localDB = new object();

        public Sql_DataAccess()
        {
                //connection_string = $"Data Source={Directory.GetParent(AppContext.BaseDirectory)}\\dbs\\Smart.db;";
        }

        public static IDbConnection GetConnection(string _connection_string=null,string _DefualtDBType= "SQLlite")
        {
            return new SQLiteConnection(connection_string);

            string connection_string2 = "Data Source=" + AppContext.BaseDirectory +"\\db\\"+ db_File_Name+"; ";
            return new SQLiteConnection(connection_string2);

            //if(_connection_string == null)
            //    _connection_string = connection_string;

            //if (_DefualtDBType == null)
            //    _DefualtDBType = DefualtDBType;


            //if (DefualtDBType == "SQLlite")
            return new SQLiteConnection(connection_string);
            //else if (DefualtDBType == "SQL Server")
            //    return new SqlConnection(connection_string);
            //else if (DefualtDBType == "SQL Server Compact")
            //    return new SqlConnection(connection_string);
            //else if (DefualtDBType == "MySql")
            //    return new MySqlConnection(connection_string);
            //else if (DefualtDBType == "MariaDb")
            //    return new MySqlConnection(connection_string);


            //else if (DefualtDBType == "PostgreSql")
            //    return new NpgsqlConnection(connection_string);
            //else if (DefualtDBType == "Oracle")
            //    return new SqlConnection(connection_string);            
            //else if (DefualtDBType == "Firebird")
            //    return new SqlConnection(connection_string);


            //else //("SQLlite")
            //    return new SQLiteConnection(connection_string);


        }

        //public static IOrmLiteDialectProvider Get_Provider()
        //{
        //    return SqliteDialect.Provider;
        //    var rb = Sql_DataAccess.Get_default_Connections_Db();
        //    Dictionary<string, IOrmLiteDialectProvider> dictionary = new Dictionary<string, IOrmLiteDialectProvider>();
        //    //dictionary.Add("SQL Server", SqlServerDialect.Provider);
        //    //dictionary.Add("MySql", MySqlDialect.Provider);
        //    dictionary.Add("SQLlite", SqliteDialect.Provider);
        //    IOrmLiteDialectProvider provider = dictionary[rb.Type];
        //    return provider;

        //}

        //public static OrmLiteConnectionFactory Get_dbFactory()
        //{
        //    return new OrmLiteConnectionFactory(Sql_DataAccess.connection_string, Sql_DataAccess.Get_Provider());
        //}

        //public static List<T> Get_List_Any_From_Db<T>()
        //{
        //    lock (Lock_localDB)
        //    {
        //        string query = "SELECT * from Connections_Db WHERE [Default]=1;";
        //        try
        //        {
        //            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
        //            {
        //                return cnn.Query<T>(query, new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}
        //public List<T> GetListAnyDB<T>(string query)
        //{
        //    lock (Lock_localDB)
        //    {
        //        try
        //        {
        //            using (var con = GetConnection())
        //            {
        //                return con.Query<T>(query, new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}

        //=========================================

        public List<T> Load<T>(string query)
        {
            //lock (Lock_localDB)
            //{
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.Query<T>(query, new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            //}
        }

        public List<T> LoadAsync<T>(string query)
        {
            //lock (Lock_localDB)
            //{
            try
            {
                using (var con = GetConnection())
                {
                    return con.QueryAsync<T>(query).Result.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            //}
        }

        public List<T> Load<T>()
        {
            //lock (Lock_localDB)
            //{
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.Query<T>($"select * from {typeof(T).Name}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            //}
        }  
      
        public List<T> Load<T>(Expression<Func<T, bool>> expression)
        {
            lock (Lock_localDB)
            {
                try
                {
                    Func<T, bool> func = expression.Compile();
                    Predicate<T> predicate = func.Invoke;

                    using (var con = GetConnection())
                    {
                        return con.Query<T>($"select * from {typeof(T).Name}", new DynamicParameters()).ToList();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
      
        public T LoadSingleById<T>(string id)
        {
            lock (Lock_localDB)
            {
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.QueryFirstOrDefault<T>($"select * from {typeof(T).Name} where Sn_Name={id}", new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            }
        }
       
        public T LoadSingle<T>(string query)
        {
            //lock (Lock_localDB)
            //{
                try
                {
                    using (var con = GetConnection())
                    {
                        return con.QueryFirstOrDefault<T>(query, new DynamicParameters());
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
            //}
        }
        
        //public  List<string> load(string Query)
        //{
        //    lock (Lock_localDB)
        //    {
        //        try
        //        {
        //            using (var con = GetConnection())
        //            {
        //                return con.Query<string>(Query, new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }

        //    //using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadLocalDB_ConnectionString()))
        //    //{
        //    //    string Qury = "SELECT userName FROM user WHERE Delet_fromServer=0 ;";
        //    //    var output = cnn.Query<string>(Qury, new DynamicParameters());
        //    //    return output.ToList();
        //    //}
        //}
        
        public bool DeleteById<T>(string id)
        {
            lock (Lock_localDB)
            {
                bool status = false;

                string tableName = GetType().Name;
                //string id = (string)table.GetId();
                //string iid = (string)table.ToId();
                //string iid = (string)table.id();

                string query = "DELETE FROM " + tableName + " where Sn_Name=" + (id) + ";  ";
                using (var con = GetConnection())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
      
        public bool Delete<T>(string Query)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                string tableName = GetType().Name;
                //string query = "DELETE FROM " + tableName + " where Id=" + (id) + ";  ";
                using (var con = GetConnection())
                {
                    try
                    {
                        //var affectedRows = con.Execute(query);
                        //if (affectedRows > 0)
                        //    return true;

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var affectedRows = con.Execute(Query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
        
        public long Get_int_FromDB(string query)
        {

            //lock (Lock_localDB)
            //{
                try
                {
                    using (var cnn = GetConnection())
                    {
                        return cnn.ExecuteScalar<int>(query);
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return 0; }
            //}
        }

        public int Execute<T>(string Query,T data)
        {
            lock (Lock_localDB)
            {
                int affectedRows = 0;
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query,data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
                return affectedRows;
            }
        }

        public int Execute<T>(string Query, HashSet<T>data)
        {
            lock (Lock_localDB)
            {
                int affectedRows = 0;
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query, data, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                return affectedRows;
            }
        }

        public int Execute(string Query)
        {
            lock (Lock_localDB)
            {
                int affectedRows = 0;
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        affectedRows = con.Execute(Query,sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                return affectedRows;
            }
        }


        //=======================================


        //public T Get_Any_From_Db<T>(string query)
        //{
        //    lock (Lock_localDB)
        //    {
        //        //string query = "SELECT * from Connections_Db WHERE [Default]=1;";
        //        try
        //        {
        //            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
        //            {
        //                return cnn.QuerySingle<T>(query, new DynamicParameters());

        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}

        //public static bool Delete_Any_ById_From_Db(string table,string id)
        //{
        //    lock (Lock_localDB)
        //    {
        //        bool status = false;

        //        //string tableName = table.GetType().Name;
        //        //string id = (string)table.GetId();
        //        //string iid = (string)table.ToId();
        //        //string iid = (string)table.id();

        //        string query = "DELETE FROM " + table + " where Id=" + (id) + ";  ";
        //        using (var con = GetConnection())
        //        {
        //            try
        //            {
        //                var affectedRows = con.Execute(query);
        //                if (affectedRows > 0)
        //                    return true;

        //                //con.Open();
        //                //var sqLiteTransaction = con.BeginTransaction();
        //                //con.Execute(query, sqLiteTransaction);
        //                //sqLiteTransaction.Commit();
        //                //status = true;
        //            }
        //            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
        //        }
        //        return status;
        //    }
        //}

        //public static List<Connections_Db> Get_List_Connections_Db()
        //{
        //    lock (Lock_localDB)
        //    {
        //        string query = "SELECT * from Connections_Db;";
        //        try
        //        {
        //            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
        //            {
        //                return cnn.Query<Connections_Db>(query, new DynamicParameters()).ToList();
        //            }
        //        }
        //        catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
        //    }
        //}

        public static   Connections_Db Get_default_Connections_Db(int id=-1)
        {
            //lock (Lock_localDB)
            //{
                string rb = Global_Variable.Mk_resources.RB_code;
                string query = "SELECT * from Connections_Db WHERE [Default]=1 and Mk_code='" + rb + "';";
                //string query = "SELECT * from Connections_Db WHERE [Default]=1 ;";
                if (id > 0)
                    //query = "SELECT * from Connections_Db WHERE [Id]="+id+" ;";
                    query = "SELECT * from Connections_Db WHERE [Id]=" + id + " and Mk_code='" + rb + "';";
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                    {
                        return cnn.QuerySingleOrDefault<Connections_Db>(query);
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
            //}
        }

        public static bool AddNew_Connection_string(string localDB_path, string fileName, string comment, string Resources, string Type = "SQLlite", string Connection_string = "Data Sourc=localDB1.db;", string Name = "SQLlite", string Username_db = "", string Password_db = "")
        {
            lock (Lock_localDB)
            {
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                    {
                        var affectedRows = cnn.Execute("insert into Connections_Db (" +
                              "[Soft_id]" +
                              ",[Mk_sn]" +
                              ",[Mk_code]" +
                              ",[Comment]" +
                              ",[LocalDB_path]" +
                              ",[FileName]" +
                              ",[Resources]" +
                              ",[Type]" +
                              ",[Connection_string]" +
                              ",[Name]" +
                              ",[Username_db]" +
                              ",[Password_db]" +
                              ") " +
                              "values (" +
                              "@Soft_id," +
                              "@Mk_sn" +
                              ",@Mk_code" +
                              ",@Comment" +
                              ",@LocalDB_path" +
                              ",@FileName" +
                              ",@Resources" +
                              ",@Type" +
                              ",@Connection_string" +
                              ",@Name" +
                              ",@Username_db" +
                              ",@Password_db" +
                              ")",
                                      new
                                      {
                                          Soft_id = Global_Variable.Mk_resources.RB_Soft_id,
                                          Mk_sn = Global_Variable.Mk_resources.RB_SN,
                                          Mk_code = Global_Variable.Mk_resources.RB_code,
                                          Comment = comment,
                                          LocalDB_path = localDB_path,
                                          FileName = fileName,
                                          Resources = Resources,
                                          Type = Type,
                                          Connection_string = "Data Sourc=" + localDB_path + "; ",
                                          Name = "SQLlite",
                                          Username_db = "",
                                          Password_db = ""
                                      });
                        if (affectedRows > 0)
                            return true;
                        else return false;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
            }
        }

        public static bool Add_Edit_Connection_string(Connections_Db db,bool Edit=false,bool defaultDB=false)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    string Qury = "";
                    using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                    {
                        if (defaultDB)
                            Qury = "update Connections_Db set [Default]=0 WHERE Mk_code = @Mk_code; ";

                        Qury = Qury + "insert into Connections_Db (" +
                                 "[Soft_id]" +
                                 ",[Mk_sn]" +
                                 ",[Mk_code]" +
                                 ",[Comment]" +
                                 ",[LocalDB_path]" +
                                 ",[FileName]" +
                                 ",[Resources]" +
                                 ",[Type]" +
                                 ",[Connection_string]" +
                                 ",[Name]" +
                                 ",[Default]" +
                                 ",[Username_db]" +
                                 ",[Password_db]" +
                                 ") " +
                                 "values (" +
                                 "@Soft_id," +
                                 "@Mk_sn" +
                                 ",@Mk_code" +
                                 ",@Comment" +
                                 ",@LocalDB_path" +
                                 ",@FileName" +
                                 ",@Resources" +
                                 ",@Type" +
                                 ",@Connection_string" +
                                 ",@Name" +
                                 ",@Default" +
                                 ",@Username_db" +
                                 ",@Password_db" +
                                 ")";
                        if (Edit)
                        {
                            Qury = "";
                            if (defaultDB)
                                Qury = "update Connections_Db set [Default]=0 WHERE Mk_code = @Mk_code; ";

                            Qury = Qury + "update Connections_Db set " +
                               " [Comment]=@Comment" +
                               ",[Name]=@Name" +
                               ",[Default]=@Default" +
                               ",[LocalDB_path]=@LocalDB_path" +
                               ",[Type]=@Type" +
                               ",[Connection_string]=@Connection_string" +
                               ",[Username_db]=@Username_db" +
                               ",[Password_db]=@Password_db" +
                               " WHERE Id = @Id;";
                        }
                        try
                        {
                            var affectedRows = cnn.Execute(Qury, db);
                            if (affectedRows > 0)
                                status = true;

                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }



                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return status;
            }
        }

        public void test_deper()
        {

            using (var con = GetConnection())
            {
                var products = con.Query<UmUser>("SELECT * FROM UmUser").ToList();
            }


            return;
            //var connectionString = "Server=localhost;Initial Catalog=DapperDB;Integrated Security=true;TrustServerCertificate=True";
            var connectionString = @"data source=ALSAIDI\SQLEXPRESS;initial catalog=SmartCreator;integrated security=True; TrustServerCertificate = True";
            var connectionString2 = @"data source=ALSAIDI\SQLEXPRESS;initial catalog=db1;integrated security=True; TrustServerCertificate = True";
            var connectionString3 = @"Data Source=db\localDB21.db";



            //
            // Connect to the database 
            var sql = "SELECT * FROM UmUsers ";
            var sql2 = "SELECT * FROM user ";

            using (var con = new SqlConnection(connectionString))
            {
                var products = con.Query<UmUser>(sql).ToList();
            }
            using (var con = new SqlConnection(connectionString2))
            {
                var products = con.Query<UmUser>(sql).ToList();

            }
    
            using (var con3 = GetConnection())
            {
                var products = con3.Query<SourceCardsUserManager_fromDB>(sql2).ToList();

            }


            //using (IDbConnection cnn = new SQLiteConnection(connectionString))
            //{
            //    //var affectedRows = cnn.Execute("insert into setting_Mk_Login_Saved ([values]) values (@values)", new { values = value });
            //    //if (affectedRows > 0)
            //    //    //return true;
            //}

        }

        #region UserManager Method
        public bool Add_UMUser_ToDB(List<UmUser> UM_Users, bool is_insert = true, bool check_byID = true,bool update_AfterPymentGet=false,bool update_AfterSessionGet=false)
        {
            lock (Lock_localDB)
            {
                bool status = false;


                string query = "";
                if (is_insert)
                {
                    query =
                        "INSERT OR IGNORE into UmUser ("
                        + "[IdHX], "
                        + "[Sn], "
                        + "[UserName], "
                        + "[Password], "
                        + "[ProfileName], "

                        + "[CallerMac], "
                        + "[ProfileValidity], "
                        + "[UptimeLimit], "
                        + "[TransferLimit], "
                        + "[UptimeUsed], "

                        + "[DownloadUsed], "
                        + "[UploadUsed], "
                        + "[ProfileTillTime], "
                        + "[ProfileTimeLeft], "
                        + "[ProfileTransferLeft], "

                        + "[Price], "
                        + "[Percentage], "
                        + "[PercentageType], "
                        + "[TotalPrice], "
                        + "[Price_Disply], "

                        + "[RegDate], "
                        //+ "[FirsLogin], "
                        + "[Disabled], "
                        + "[Status], "
                        + "[SpCode], "

                        + "[SpName], "
                        + "[BatchCardId], "
                        + "[NumberPrint], "
                        + "[Comment], "
                        //+ "[Email], "
                        //+ "[NasPortId], "

                        + "[Sn_Name], "
                        + "[MkId], "
                        //+ "[DeleteFromServer], "
                        + "[AddedDb], "
                        //+ "[LastSynDb], "

                        //+ "[Radius], "
                        + "[CustomerName], "
                        //+ "[FirstName], "
                        //+ "[LastName], "
                        //+ "[Phone], "

                        //+ "[Location], "
                        //+ "[SharedUsers], "
                        + "[LastSeenAt], "
                        + "[ActiveSessions], "
                        + "[MkId], "
                        + "[PageNumber], "
                        + "[SN_Archive], "
                        + "[ValidityLimit] "
                        + ") "

                        + "values ("

                        + "@IdHX, "
                        + "@SN, "
                        + "@UserName, "
                        + "@Password,"
                        + "@ProfileName, "

                        + "@CallerMac, "
                        + "@ProfileValidity, "
                        + "@UptimeLimit,"
                        + "@TransferLimit,"
                        + "@UptimeUsed,"

                        + "@DownloadUsed,"
                        + "@UploadUsed,"
                        + "@ProfileTillTime,"
                        + "@ProfileTimeLeft,"
                        + "@ProfileTransferLeft,"

                        + "@Price,"
                        + "@Percentage,"
                        + "@PercentageType,"
                        + "@TotalPrice,"
                        + "@Price_Disply,"

                        + "@RegDate,"
                        //+ "@FirsLogin,"
                        + "@Disabled,"
                        + "@Status,"
                        + "@SpCode,"

                        + "@SpName,"
                        + "@BatchCardId,"
                        + "@NumberPrint,"
                        + "@Comment,"
                        //+ "@Email,"
                        //+ "@NasPortId,"

                        + "@Sn_Name,"
                        + "@MkId,"
                        //+ "@DeleteFromServer, "
                        + "@AddedDb, "
                        //+ "@LastSynDb, "

                        //+ "@Radius, "
                        + "@CustomerName, "
                        //+ "@FirstName, "
                        //+ "@LastName, "
                        //+ "@Phone, "

                        //+ "@Location, "
                        //+ "@SharedUsers, "
                        + "@LastSeenAt, "
                        + "@ActiveSessions, "
                        + "@MkId, "
                        + "@PageNumber, "
                        + "@SN_Archive, "
                        + "@ValidityLimit "
                        + "); ";
                }
                else
                {
                    string _update_check = " WHERE Sn_Name=@Sn_Name;";
                    _update_check = " " +
                        //",[CustomerName]=@CustomerName" +
                        //",[Location]=@Location" +
                        ",[SpCode]=@SpCode" +
                        ",[SpName]=@SpName" +
                        ",[Password]=@Password" +
                        ",[Status]=@Status" +
                        ",[Percentage]=@Percentage" +
                        ",[ProfileName]=@ProfileName" +
                        //",[LastSynDb]=@LastSynDb " +
                        //",[MkId]=@MkId " +
                        ",[DeleteFromServer]=@DeleteFromServer  " +
                        " WHERE Sn_Name = @Sn_Name;";
                        //" WHERE Id = @Id;";

                    //if (!check_byID)
                    //    _update_check = " ,[DeleteFromServer]=@DeleteFromServer  WHERE Sn_Name=@Sn_Name;";

                    query =
                        "update UmUser set "
                        //+ " [CustomerName]=@CustomerName "
                        + "[Disabled]=@Disabled "
                        + ",[Password]=@Password "
                        //+ ",[FirstName]=@FirstName "
                        //+ ",[LastName]=@LastName "
                        + ",[Comment]=@Comment"
                        //+ ",[Phone]=@Phone"
                        //+ ",[Email]=@Email"
                        + ",[CallerMac]=@CallerMac"
                        + ",[UptimeUsed]=@UptimeUsed"
                        + ",[DownloadUsed]=@DownloadUsed"
                        + ",[UploadUsed]=@UploadUsed"
                        + ",[LastSeenAt]=@LastSeenAt"
                        + ",[ActiveSessions]=@ActiveSessions"
                        //+ ",[SharedUsers]=@SharedUsers"
                         + ",[ProfileTransferLeft]=@ProfileTransferLeft"
                        //+ ",[actualLimUptime]=@actualLimUptime"
                        + ",[ProfileTillTime]=@ProfileTillTime"
                        + ",[ProfileTimeLeft]=@ProfileTimeLeft "
                        //+ ",[Location]=@Location"
                        //+ ",[SpId]=@SpId"
                        //+ ",[SpName]=@SpName"
                        //+ ",[Status]=@Status"
                        //+ ",[Percentage]=@Percentage"
                        //+ ",[ProfileName]=@ProfileName"
                        //+ ",[DeleteFromServer]=@DeleteFromServer"
                        //+ ",[DeleteFromServer]=0"


                        + _update_check;
                    //+ " WHERE UmUserId = @UmUserId;";

                    if (update_AfterPymentGet)
                    {
                        query =
                         "update UmUser set "
                         + " [RegDate]=@RegDate "
                         + ",[TotalPrice]=@TotalPrice "
                         + ",[Price]=@Price "
                         + ",[UptimeLimit]=@UptimeLimit "
                         + ",[TransferLimit]=@TransferLimit "
                         + ",[ProfileName]=@ProfileName"
                         //+ ",[actualLimUptime]=@actualLimUptime"
                         //+ ",[actualLimTransfer]=@actualLimTransfer"
                         + ",[ProfileTimeLeft]=@ProfileTimeLeft"
                         + ",[ProfileTransferLeft]=@ProfileTransferLeft "
                         + ",[ValidityLimit]=@ValidityLimit "
                         //+ " ,[LastSynDb]=@LastSynDb "
                         + " ,[CountProfile]=@CountProfile "
                           //ProfileValidity = u.ValidityLimit,

                         //+ ",[countProfile]=@countProfile "
                         +" WHERE Sn_Name = @Sn_Name;";
                         //+ " WHERE Id = @Id;";
                    }
                    if (update_AfterSessionGet)
                    {
                        query =
                       "update UmUser set "
                       + "[Radius]=@Radius, "
                       + "[NasPortId]=@NasPortId, "
                       + "[FirsLogin]=@FirsLogin, "
                       //+ "[UptimeUsed]=@UptimeUsed, "
                       //+ "[DownloadUsed]=@DownloadUsed, "
                       //+ "[UploadUsed]=@UploadUsed, "
                       + "[CountSession]=@CountSession "

                       //+ "[LastSynDb]=@LastSynDb "
                       + " WHERE Sn_Name = @Sn_Name;";
                        //+ " WHERE Id = @Id;";
                                             
                    }

                }

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var rowEfect = con.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }

                return status;
            }
        }
        public bool Add_UMUser_ToDB_v7(List<UmUser> UM_Users, bool is_insert = true, bool check_byID = true,bool update_AfterPymentGet=false,bool update_AfterSessionGet=false)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                string query = "";
                if (is_insert)
                {
                    query =
                        "INSERT OR IGNORE into UmUser ("
                        + "[IdHX], "
                        + "[SN], "
                        + "[Sn_Name], "
                        + "[UserName], "
                        + "[Password], "
                        + "[Disabled], "
                        + "[Attributes], "
                        + "[Group], "
                        + "[Comment], "
                        + "[CallerMac], "
                        + "[SharedUsers], "
                        + "[DeleteFromServer], "

                        + "[MkId] "
                        + ") "

                        + "values ("
                        + "@IdHX, "
                        + "@SN, "
                        + "@Sn_Name, "
                        + "@UserName, "
                        + "@Password,"
                        + "@Disabled, "
                        + "@Attributes, "
                        + "@Group, "
                        + "@Comment, "
                        + "@CallerMac, "
                        + "@SharedUsers, "
                        + "@DeleteFromServer, "
                        + "@MkId "
                        + "); ";
                }
                else
                {
                    query =
                        "update UmUser set "
                        + "[Disabled]=@Disabled "
                        + ",[Password]=@Password "
                        + ",[Attributes]=@Attributes "
                        + ",[Group]=@Group "
                        + ",[Comment]=@Comment"
                        + ",[CallerMac]=@CallerMac"
                        + ",[SharedUsers]=@SharedUsers"
                        + ",[DeleteFromServer]=@DeleteFromServer "
                        //+ ",[MkId]=@MkId"
                        + " WHERE Sn_Name=@Sn_Name;"
                       ;

                    if (update_AfterPymentGet)
                    {
                        query =
                         "update UmUser set "
                         + " [RegDate]=@RegDate "
                         + ",[TotalPrice]=@TotalPrice "
                         + ",[Price]=@Price "
                         + ",[UptimeLimit]=@UptimeLimit "
                         + ",[TransferLimit]=@TransferLimit "
                         + ",[ProfileName]=@ProfileName"
                         //+ ",[actualLimUptime]=@actualLimUptime"
                         //+ ",[actualLimTransfer]=@actualLimTransfer"
                         + ",[ProfileTimeLeft]=@ProfileTimeLeft"
                         + ",[ProfileTransferLeft]=@ProfileTransferLeft "
                         + ",[ValidityLimit]=@ValidityLimit "
                         //+ " ,[LastSynDb]=@LastSynDb "
                         + " ,[CountProfile]=@CountProfile "

                         + " ,[Status]=@Status "

                         //+ ",[countProfile]=@countProfile "
                         + " WHERE Sn_Name = @Sn_Name;";
                         //+ " WHERE Id = @Id;";
                    }
                    if (update_AfterSessionGet)
                    {
                        query =
                       "update UmUser set "
                       + "[Radius]=@Radius, "
                       + "[NasPortId]=@NasPortId, "
                       + "[FirsLogin]=@FirsLogin, "
                       + "[UptimeUsed]=@UptimeUsed, "
                       + "[DownloadUsed]=@DownloadUsed, "
                       + "[UploadUsed]=@UploadUsed, "
                       + "[CountSession]=@CountSession, "

                       + "[LastSynDb]=@LastSynDb "
                       + " WHERE Sn_Name = @Sn_Name;";
                       //+ " WHERE Id = @Id;";
                    }

                }

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var rowEfect = con.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }

                return status;
            }
        }

        public bool Update_UMUser_ToDB(List<UmUser> UM_Users, bool check_byID = true)
        {
            lock (Lock_localDB)
            {
                bool status=false;
            string _update_check = " WHERE Sn_Name = @Sn_Name;";

            _update_check = " " +
                "[Location]=@Location," +
                "[SpCode]=@SpCode," +
                "[SpName]=@SpName," +
                "[Status]=@Status," +
                "[Percentage]=@Percentage," +
                "[ProfileName]=@ProfileName," +
                "[LastSynDb]=@LastSynDb, " +
                "[DeleteFromServer]=@DeleteFromServer  " +
                " WHERE Sn_Name = @Sn_Name;";

            if (!check_byID)
                _update_check = " [DeleteFromServer]=@DeleteFromServer  WHERE Sn_Name = @Sn_Name;";

            string query =
                "update UmUser set "
                + "[Disabled]=@Disabled, "
                + "[FirstName]=@FirstName, "
                + "[LastName]=@LastName, "
                + "[Comment]=@Comment,"
                + "[Phone]=@Phone,"
                + "/*[Location]=@Location,*/"
                + "[Email]=@Email,"
                + "[CallerMac]=@CallerMac,"
                //+ "[actualProfileName]=@actualProfileName,"
                + "[UptimeUsed]=@UptimeUsed,"
                + "[DownloadUsed]=@DownloadUsed,"
                + "[UploadUsed]=@UploadUsed,"
                + "[LastSeenAt]=@LastSeenAt,"
                + "[ActiveSessions]=@ActiveSessions,"
                + "[SharedUsers]=@SharedUsers,"
                //+ "[spId]=@spId,"
                //+ "[spName]=@spName,"
                //+ "[status]=@status,"
                //+ "[Delet_fromServer]=@Delet_fromServer, "
                //+ "[spPercentage]=@spPercentage,"
                //+ "[is_spPercentage]=@is_spPercentage "

                //+ "[actualLimTransfer]=@actualLimTransfer,"
                //+ "[actualLimUptime]=@actualLimUptime,"
                //+ "[profileTillTime]=@profileTillTime,"
                //+ "[profileTimeLeft]=@profileTimeLeft "

                + _update_check;
          
                try
                {
                    using (var con = GetConnection())
                    {
                        //con.ExecuteAsync(query, UM_Users, sqLiteTransaction);


                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var output = con.Execute(query, UM_Users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }

        public bool Set_Delet_fromServer_As_disable<T>(string table)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                //string table = NameClass.GetType().Name;
                string query = "UPDATE  " + table + " SET DeleteFromServer = 1  WHERE DeleteFromServer = 0;";
                if (table == new UmUser().GetType().Name)
                {
                    query = "UPDATE " + table + " SET DeleteFromServer = 1 ,ActiveSessions = 0 WHERE DeleteFromServer = 0;";
                }
                if (table == new UmSession().GetType().Name)
                {
                    query = "UPDATE " + table + " SET DeleteFromServer = 1 ,Active = 0 WHERE DeleteFromServer = 0;";
                }
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        int effectCount = con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
        public int Set_DeletFromServer_AsDisable<T>(string table)
        {
            int EffectCount = 0;
            lock (Lock_localDB)
            {
                bool status = false;
                //string table = NameClass.GetType().Name;
                string query = "UPDATE  " + table + " SET DeleteFromServer = 1  WHERE DeleteFromServer = 0;";
                if (table == new UmUser().GetType().Name)
                {
                    query = "UPDATE " + table + " SET DeleteFromServer = 1 ,ActiveSessions = 0 WHERE DeleteFromServer = 0;";
                }
                if (table == new UmSession().GetType().Name)
                {
                    query = "UPDATE " + table + " SET DeleteFromServer = 1 ,Active = 0 WHERE DeleteFromServer = 0;";
                }

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        EffectCount= con.Execute(query, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return EffectCount;
            }
        }

        public bool Set_Delet_fromServer<T>(string table,HashSet<T> users,string pk= "Sn_Name")
        {
            lock (Lock_localDB)
            {
                bool status = false;
                //string table = NameClass.GetType().Name;
                //update UmUser set DeleteFromServer = 1 where Id = 1367832
                //string query = "update UmUser set DeleteFromServer = 1 where Id = @Id ;";
                string query = $"UPDATE {table}  SET [DeleteFromServer]=1  WHERE {pk}=@{pk} ;";
                if (table == new UmUser().GetType().Name)
                {
                    query = $"UPDATE {table} SET [DeleteFromServer]=1  WHERE {pk}=@{pk} ;";
                }

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var effect = con.Execute(query, users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }
        public bool Set_NotDeletFromServer<T>(string table, List<T> users)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                string query = "UPDATE  " + table + " SET [DeleteFromServer]=0  WHERE Sn_Name=@Sn_Name ;";
                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var effect = con.Execute(query, users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }

        public bool Process_Cards_OnDB<T>(string table, HashSet<T> users, string typeProcess = "DeleteFromServer")
        {
            lock (Lock_localDB)
            {
                bool status = false;

                //string table = NameClass.GetType().Name;
                //update UmUser set DeleteFromServer = 1 where Id = 1367832
                //string query = "update UmUser set DeleteFromServer = 1 where Id = @Id ;";
                string query = "";

                if (typeProcess == "DeleteFromServer")
                    query = "UPDATE  " + table + " SET [DeleteFromServer]=1  WHERE Sn_Name=@Sn_Name ;";

                else if (typeProcess == "Enabled")
                    query = "UPDATE  " + table + " SET [Disabled]=0  WHERE Sn_Name=@Sn_Name ;";

                else if (typeProcess == "Disabled")
                    query = "UPDATE  " + table + " SET [Disabled]=1  WHERE Sn_Name=@Sn_Name ;";

                else if (typeProcess == "RestCards")
                    query = "UPDATE  " + table + " SET [UptimeUsed]=0,[UploadUsed]=0,[DownloadUsed]=0,[Status]=0  WHERE Sn_Name=@Sn_Name ;";
                
                else if (typeProcess == "RestCards")
                    query = "UPDATE  " + table + " SET [UptimeUsed]=0,[UploadUsed]=0,[DownloadUsed]=0,[Status]=0  WHERE Sn_Name=@Sn_Name ;";
                                
                else if (typeProcess == "caller_bind")
                    query = "UPDATE  " + table + " SET [CallerMac]='bind'  WHERE Sn_Name=@Sn_Name ;";
                                
                else if (typeProcess == "Remove_caller_bind")
                    query = "UPDATE  " + table + " SET [CallerMac]=''  WHERE Sn_Name=@Sn_Name ;";

                else
                    return false;

                using (var con = GetConnection())
                {
                    try
                    {
                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        var effect = con.Execute(query, users, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        status = true;
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                }
                return status;
            }
        }

        public List<T> Get_Not_Delet_fromServer<T>(string table)
        {
            //lock (Lock_localDB)
            //{
                List<T> res = new List<T>();

                //string table = NameClass.GetType().Name;
                string query = "select * from " + table + " where DeleteFromServer=0;";
                using (var con = GetConnection())
                {
                    try
                    {
                        var output = con.Query<T>(query, new DynamicParameters());
                        if (output != null)
                            res = output.ToList();

                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
                return res;
            //}
        }

        public  bool Add_UMPyement_ToDB(List<UmPyment> UM_Pyement, bool is_insert = true,bool check_byID = true)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    string query = "";

                    if (is_insert)
                    {
                        query = "INSERT OR IGNORE into UmPyment (" +
                               " [Sn] " +
                               ",[IdHX]" +
                               ",[Sn_Name] " +
                               ",[DeleteFromServer] " +

                               ",[Fk_Sn_Name] " +
                               //",[UmUserId] " +
                               ",[UserName] " +
                               ",[ProfileName] " +

                               ",[Price] " +
                               ",[Percentage] " +
                               ",[PercentageType] " +
                               ",[TotalPrice] " +

                               ",[ProfileValidity] " +
                               ",[ProfileUptimeLimit] " +
                               ",[ProfileTransferLimit] " +
                               ",[AddedDate]" +

                               ",[AddedDb]" +
                               ",[MkId]" +
                               //",[LastSynDb]" +
                               ",[state]" +

                               ") " +

                               "values (" +
                               " @Sn " +
                               ",@IdHX " +
                               ",@Sn_Name " +
                               ",@DeleteFromServer " +

                               ",@Fk_Sn_Name" +
                               //",@UmUserId " +
                               ",@UserName " +
                               ",@ProfileName " +

                               ",@Price " +
                               ",@Percentage " +
                               ",@PercentageType" +
                               ",@TotalPrice" +

                               ",@ProfileValidity" +
                               ",@ProfileUptimeLimit " +
                               ",@ProfileTransferLimit " +
                               ",@AddedDate" +

                               ",@AddedDb " +
                               ",@MkId" +

                               //",@LastSynDb " +
                               ",@state " +
                               ") ";
                    }
                    else
                    {
                        string _update_check = " WHERE Sn_Name=@Sn_Name;";


                        query = "update UmPyment set " +
                                //" [LastSynDb]=@LastSynDb, " +
                                " [TotalPrice]=@TotalPrice, " +
                                " [Price]=@Price, " +
                                //" [ProfileName]=@ProfileName, " +
                                 "[ProfileUptimeLimit]=@ProfileUptimeLimit, " +
                                 "[ProfileTransferLimit]=@ProfileTransferLimit, " +
                                 "[ProfileValidity]=@ProfileValidity, " +
                                 "[state]=@state, " +
                                 "[DeleteFromServer]=@DeleteFromServer " +

                             _update_check;
                        //" WHERE Id = @Id;";
                        //if (!check_byID)
                        //    query = "update UmPyment set [DeleteFromServer]=@DeleteFromServer  WHERE Sn_Name=@Sn_Name;";

                    }
                    using (var con = GetConnection())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            var effectRows = con.Execute(query, UM_Pyement, sqLiteTransaction);
                            sqLiteTransaction.Commit();
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return status;
            }
        }

        public bool Add_HSPyement_ToDB(List<HsPyment> HS_Pyement)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    string query = "";

                     
                        query = "INSERT OR IGNORE into HsPyment (" +
                               "[DeleteFromServer] " +

                               ",[Fk_Sn_Name] " +
                               ",[MainProfile] " +
                               ",[UserName] " +
                               ",[ProfileName] " +

                               ",[Price] " +
                               ",[Percentage] " +
                               ",[PercentageType] " +
                               ",[TotalPrice] " +

                               ",[ProfileValidity] " +
                               ",[ProfileUptimeLimit] " +
                               ",[ProfileTransferLimit] " +
                               ",[AddedDate]" +

                               ",[AddedDb]" +
                               ",[MkId]" +
                               ",[state]" +

                               ") " +

                               "values (" +
                               "@DeleteFromServer " +

                               ",@Fk_Sn_Name" +
                               ",@MainProfile " +
                               ",@UserName " +
                               ",@ProfileName " +

                               ",@Price " +
                               ",@Percentage " +
                               ",@PercentageType" +
                               ",@TotalPrice" +

                               ",@ProfileValidity" +
                               ",@ProfileUptimeLimit " +
                               ",@ProfileTransferLimit " +
                               ",@AddedDate" +

                               ",@AddedDb " +
                               ",@MkId" +

                               //",@LastSynDb " +
                               ",@state " +
                               ") ";
                    
                    //else
                    //{
                    //    string _update_check = " WHERE Sn_Name=@Sn_Name;";


                    //    query = "update UmPyment set " +
                    //            //" [LastSynDb]=@LastSynDb, " +
                    //            " [TotalPrice]=@TotalPrice, " +
                    //            " [Price]=@Price, " +
                    //             //" [ProfileName]=@ProfileName, " +
                    //             "[ProfileUptimeLimit]=@ProfileUptimeLimit, " +
                    //             "[ProfileTransferLimit]=@ProfileTransferLimit, " +
                    //             "[ProfileValidity]=@ProfileValidity, " +
                    //             "[state]=@state, " +
                    //             "[DeleteFromServer]=@DeleteFromServer " +

                    //         _update_check;
                    //    //" WHERE Id = @Id;";
                    //    //if (!check_byID)
                    //    //    query = "update UmPyment set [DeleteFromServer]=@DeleteFromServer  WHERE Sn_Name=@Sn_Name;";

                    //}
                    using (var con = GetConnection())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            var effectRows = con.Execute(query, HS_Pyement, sqLiteTransaction);
                            sqLiteTransaction.Commit();
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false;

                            
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return false; }
                return status;
            }
        }

        public bool Add_UMSession_ToDB(List<UmSession> UM_Session, bool is_insert = true, bool check_byID = true)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    string query = "";
                    if (is_insert)
                    {
                        query = "INSERT OR IGNORE into UmSession (" +
                                "[Sn], " +
                                "[IdHX], " +
                                "[Active], " +
                                "[Sn_Name], " +
                                "[Status], " +
                                "[Fk_Sn_Name], " +
                                //"[UmUserId], " +

                                "[UserName], " +
                                "[NasPortId], " +
                                "[CallingStationId], " +
                                "[IpUser], " +
                                "[IpRouter], " +
                                "[FromTime], " +
                                "[TillTime], " +
                                "[UpTime], " +
                                "[BytesDownload], " +
                                "[BytesUpload], " +
                                "[DeleteFromServer], " +
                                "[AddedDb]," +
                                "[MkId]," +
                                "[LastSynDb]" +
                                ") " +
                                "values (" +
                                "@Sn, " +
                                "@IdHX, " +
                                "@Active, " +
                                "@Sn_Name, " +
                                "@Status," +
                                "@Fk_Sn_Name, " +
                                //"@UmUserId, " +

                                "@UserName, " +
                                "@NasPortId," +
                                "@CallingStationId," +
                                "@IpUser," +
                                "@IpRouter," +
                                "@FromTime," +
                                "@TillTime," +
                                "@UpTime," +
                                "@BytesDownload," +
                                "@BytesUpload," +
                                "@DeleteFromServer," +
                                "@AddedDb," +
                                "@MkId," +
                                "@LastSynDb" +
                                ") ";
                    }

                    else
                    {
                        string _update_check = " WHERE Sn_Name=@Sn_Name;";
                        query = "update UmSession set " +
                         "[DeleteFromServer]=@DeleteFromServer, " +
                         //"[CallingStationId]=@CallingStationId, " +
                         //"[NasPortId]=@NasPortId, " +
                         //"[IpUser]=@IpUser, " +
                         //"[IpRouter]=@IpRouter, " +
                         //"[FromTime]=@FromTime, " +
                         "[TillTime]=@TillTime, " +
                         "[UpTime]=@UpTime, " +
                         "[Active]=@Active " +
                         //"[LastSynDb]=@LastSynDb " +
                         _update_check;
                        //" WHERE id = @id;";

                        if (!check_byID)
                            query = "update UmSession set " +
                                 "[DeleteFromServer]=@DeleteFromServer," +
                                 //"[CallingStationId]=@CallingStationId, " +
                                 //"[NasPortId]=@NasPortId, " +
                                 //"[IpUser]=@IpUser, " +
                                 //"[IpRouter]=@IpRouter, " +
                                 //"[FromTime]=@FromTime, " +
                                 "[TillTime]=@TillTime, " +
                                 "[UpTime]=@UpTime, " +
                                 "[Active]=@Active " +
                                 //"[LastSynDb]=@LastSynDb " +
                                 
                                "  WHERE Sn_Name=@Sn_Name;";

                    }

                    using (var con = GetConnection())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                           int effectRows= con.Execute(query, UM_Session, sqLiteTransaction);
                            sqLiteTransaction.Commit();
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }

        public bool Add_HSession_ToDB(List<HsSession> HM_Session, bool is_insert = true, bool check_byID = true)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    string query = "";
                    if (is_insert)
                    {
                        query = "INSERT OR IGNORE into HsSession (" +
                                //"[Sn], " +
                                //"[IdHX], " +
                                //"[Active], " +
                                //"[Sn_Name], " +
                                //"[Status], " +
                                "[Fk_Sn_Name], " +
                                "[TillTime_inSecond], " +

                                "[UserName], " +
                                "[NasPortId], " +
                                "[CallingStationId], " +
                                "[IpUser], " +
                                "[IpRouter], " +
                                "[FromTime], " +
                                "[TillTime], " +
                                "[UpTime], " +
                                "[BytesDownload], " +
                                "[BytesUpload], " +
                                "[DeleteFromServer], " +
                                "[AddedDb]," +
                                "[MkId]," +
                                "[LastSynDb]" +
                                ") " +
                                "values (" +
                                //"@Sn, " +
                                //"@IdHX, " +
                                //"@Active, " +
                                //"@Sn_Name, " +
                                //"@Status," +
                                "@Fk_Sn_Name, " +
                                "@TillTime_inSecond, " +

                                "@UserName, " +
                                "@NasPortId," +
                                "@CallingStationId," +
                                "@IpUser," +
                                "@IpRouter," +
                                "@FromTime," +
                                "@TillTime," +
                                "@UpTime," +
                                "@BytesDownload," +
                                "@BytesUpload," +
                                "@DeleteFromServer," +
                                "@AddedDb," +
                                "@MkId," +
                                "@LastSynDb" +
                                ") ";
                    }
 
                    using (var con = GetConnection())
                    {
                        try
                        {
                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            int effectRows = con.Execute(query, HM_Session, sqLiteTransaction);
                            sqLiteTransaction.Commit();
                            status = true;
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }


        public static List<CardsUserManagerFromDB> GetUsersManager_By_FirstUse(string query)
        {
            string localDB_Path = @"Data Source=.\" + Global_Variable.Mk_Router.localDB_path + ";Version=3;";
            using (IDbConnection cnn = new SQLiteConnection(localDB_Path))
            {

                string Qury = "SELECT " +
                    "u.id," +
               "u.sn," +
               "u.sn_userName," +
               "u.userName," +
               "u.password," +
               "u.userName," +
               "u.disabled," +
               "u.regDate," +
               "u.descr," +
               //"uptimeLimit," +
               //"transferLimit," +
               "u.uptimeUsed," +
               "u.downloadUsed," +
               "u.uploadUsed," +
               "u.lastSeenAt," +
               "u.activeSessions," +
               "u.moneyTotal," +
               "u.actualProfileName," +
               "u.SpCode," +
               "u.spName," +
               "u.numberPrintedName," +
               "u.Delet_fromServer," +
               "u.firstUse," +
               "u.status," +
               "u.CusName," +
               "u.nasPortId," +
               "u.radius," +
               //"countProfile," +
               "u.idHX, " +
               "count(c.id) as countProfile," +
               "sum(c.profileValiday) as profileValiday," +
               "sum(c.actualLimUptime) as uptimeLimit," +
               "sum(c.actualLimTransfer) as transferLimit  " +
               "FROM user u LEFT JOIN userprofile c ON u.id = c.fk_User_localDB_id" +
               query +

               //" WHERE u.firstUse >="+start+" AND u.firstUse<="+end+"  " +

               //"WHERE u.Delet_fromServer=0 " +
               " GROUP BY u.id;";

                var output = cnn.Query<CardsUserManagerFromDB>(Qury, new DynamicParameters());
                return output.ToList();
            }
        }


        #endregion
        public DataTable RunSqlCommandAsDatatable(string Qury)
        {
            DataTable dt = new DataTable();
            try
            {
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, connection_string);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                dt = tbFound;
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); return null; }

            return dt;
        }
        public bool RunSqlScript(string sql)
        {
            lock (Lock_localDB)
            {
                bool status = false;
                try
                {
                    using (IDbConnection cnn = new SQLiteConnection(connection_string))
                    {
                        cnn.Open();
                        var sqLiteTransaction = cnn.BeginTransaction();
                        var rowsEfect = cnn.ExecuteScalar(sql, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                        RJMessageBox.Show(rowsEfect.ToString());
                        status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); status = false; }
                return status;
            }
        }
        //public List<T> Get_Any<T>()
        //{
        //    lock (Lock_localDB)
        //    {
        //        using var db = dbFactory.Open();
        //        var table = db.Select<T>();
        //        return table;
        //    }
        //}

        //public T Get_Any_byId<T>(int? id)
        //{
        //    lock (Lock_localDB)
        //    {
        //        if (id == null)
        //            return default(T);

        //        using var db = dbFactory.Open();
        //        T table = db.SingleById<T>(id);
        //        return table;
        //    }
        //}
        public long Get_last_SN_UserManager()
        {
            lock (Lock_localDB)
            {
                long sn = 0;
                //string query = "SELECT * from UmUser orderby Sn_Name LImit 1;";
                //string query = "SELECT Sn from UmUser orderby by Sn desc LImit 1;";
                string query = "SELECT Sn from UmUser;";
                try
                {
                    using (var con = GetConnection())
                    {
                        //return cnn.ExecuteScalar<int>(query);
                        //long aa = cnn.ExecuteScalar<int>(query);

                        //sn = con.ExecuteScalar<long>(query);
                        //if (aa.count > 0)
                        //    sn = lst.First().SN;
                        //return sn;


                       HashSet<long> users = new HashSet<long>(Load<long>("SELECT Sn FROM UmUser WHERE DeleteFromServer=0;"));
                        if (users.Count > 0)
                            sn = users.Last();
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); return default; }
                return sn;
            }
        }
        //public double Get_last_SN_UserManager()
        //{
        
        //    lock (Lock_localDB)
        //    {
        //        double sn = 0;
        //        using var db = dbFactory.Open();
        //        var lst = db.Select<UmUser>().OrderByDescending(a => a.Sn_Name)
        //            //.Where(a=>a.DeleteFromServer==0)
        //            .Take(1)
        //            .ToList();
        //        if (lst.Count > 0)
        //            sn = lst.First().SN;
        //        return sn;
        //    }
        //}

        //public List< BatchCard> Get_Batch_byBatchNumber22(int id)
        //{
        //    lock (Lock_localDB)
        //    {
        //        try
        //        {
        //            //using var db = dbFactory.Open();
        //            var table = db.Select<BatchCard>(a => a.BatchNumber == id);
        //            //var table = db.Select<BatchCard>(a => a.BatchNumber == id);
        //        }catch(Exception ex) { RJMessageBox.Show(ex.Message); }

        //        return table;
        //    }
        //}
        //public List<BatchCard> Get_Batch_byBatchNumber_And_Server23(int id, int server = 0)
        //{
        //    using var db = dbFactory.Open();
        //    //var table = db.Select<BatchCard>(a => a.BatchNumber == id);
        //    //if (table.Count == 0)
        //      var  table = db.Select<BatchCard>(a => a.BatchNumber == id && a.Server == server);

        //    return table;
        //}
        //public void Add_Batch_Card3s2(BatchCard data, int server = 0, bool add_to_Last = false)
        //{
        //    try
        //    {
        //        data.Rb = Global_Variable.Mk_resources.RB_code;
        //        using var db = dbFactory.Open();

        //        if (add_to_Last == false)
        //            db.UpdateOnly(() => new My_Sequence { Seq = data.BatchNumber }, where: p => p.Name == "BatchCards" && p.Rb == data.Rb);

        //        var effect = db.Insert(data);
        //    }
        //    catch (Exception ex) { RJMessageBox.Show("error add batch number to db \n" + ex.Message); }
        //}

        //public long get_BatchCards_my_sequen3ce2(string name= "BatchCards")
        //{
        //    long seq = 0;
        //    using var db = dbFactory.Open();
        //    var lst = db.Select<My_Sequence>().Where(a => a.Name == name && a.Rb == Global_Variable.Mk_resources.RB_code);
        //    if (lst != null && lst.Count()!=0)
        //        return lst.First().Seq;
        //    else
        //    {
        //        db.Insert(new My_Sequence {Name = "BatchCards", Seq = 0,Rb = Global_Variable.Mk_resources.RB_code });
        //    }
        //    return seq;
        //}

        public int  Remove_UmUser_FormDB<T>(HashSet<T> Users,string tableName= "UmUser")
        {
            int rowsEfect = 0;
            lock (Lock_localDB)
            {
                //bool status = false;
                try
                {
                    using (var cnn = GetConnection())
                    {
                        cnn.Open();
                        string sql = $"DELETE FROM {tableName} WHERE Sn_Name=@Sn_Name";
                        string sqlpyment = "DELETE FROM UmPyment WHERE Fk_Sn_Name=@Sn_Name";
                        string sqlsession = "DELETE FROM UmSession WHERE Fk_Sn_Name=@Sn_Name";

                        var sqLiteTransaction = cnn.BeginTransaction();
                         rowsEfect = cnn.Execute(sql,Users, sqLiteTransaction);

                        if (tableName == "UmUser")
                        {
                            var rowsEfect2 = cnn.Execute(sqlpyment, Users, sqLiteTransaction);
                            var rowsEfect3 = cnn.Execute(sqlsession, Users, sqLiteTransaction);
                        }
                        if (tableName == "HSUser")
                        {
                            string sqlHssession = "DELETE FROM HSSession WHERE Fk_Sn_Name=@Sn_Name";
                            var rowsEfect3 = cnn.Execute(sqlHssession, Users, sqLiteTransaction);
                        }
                        sqLiteTransaction.Commit();
                        //RJMessageBox.Show(rowsEfect.ToString());
                        //status = true;
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                return rowsEfect;
            }
        }

          }
}
