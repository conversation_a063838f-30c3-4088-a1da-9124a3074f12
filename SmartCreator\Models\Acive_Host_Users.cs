﻿using SmartCreator.Data;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using tik4net;

namespace SmartCreator.Models
{
    public class Acive_Host_Users
    {
        public Acive_Host_Users() { }

        public string IdHX { get; set; }
        //public string Status { get; set; }

        [DisplayName("الاسم")]
        public string UserName { get; set; }
        [DisplayName("الباقة")]
        public string ProfileName { get; set; }

        public double Uptime { get; set; } = 0;
        [DisplayName("وقت الجلسة"), Computed]
        public string Str_Uptime
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(Uptime);
                //return String.Format("{0:n0}", TotalPrice);
            }
        }

       
        public double BytesIn { get; set; }
        public double BytesOut { get; set; }

        [DisplayName("التحميل"), Computed]
        public string Str_BytesOut
        {
            get
            {
                return utils.ConvertSize_Get_InArabic(BytesOut.ToString());
            }
        }


        [DisplayName("الرفع"), Computed]
        public string Str_BytesIn
        {
            get
            {
                return utils.ConvertSize_Get_InArabic(BytesIn.ToString());
            }
        }

        public double SessionTimeLeft { get; set; }
        [DisplayName("الوقت المتبقي"), Computed]
        public string Str_SessionTimeLeft
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(SessionTimeLeft);
            }
        }

        public double LimitBytesTotal { get; set; }
        [DisplayName("التحميل المتبقي"), Computed]
        public string Str_LimitBytesTotal
        {
            get
            {
                return utils.ConvertSize_Get_InArabic(LimitBytesTotal.ToString());
            }
        }

        [DisplayName("الايبي")]
        public string Address { get; set; }
        [DisplayName("الماك")]
        public string MacAddress { get; set; }

        [DisplayName("السيرفر")]
        public string Server { get; set; }
        [DisplayName("المنفذ")]
        public string BridgePort { get; set; }
        [DisplayName("اسم الجهاز")]
        public string HostName { get; set; }

        [DisplayName("دخول بواسطة")]
        public string LoginBy { get; set; }
        public double KeepaliveTimeout { get; set; }
        [DisplayName("مهلة البقاء نشط(Keep alive Timeout)"), Computed]
        public string Str_KeepaliveTimeout
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(KeepaliveTimeout);
            }
        }

        public double IdleTime { get; set; }
        [DisplayName("وقت الخمول"), Computed]
        public string Str_IdleTime
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(IdleTime);
            }
        }
        [DisplayName("تعليق")]
        public string Comment { get; set; }
        [DisplayName("يوزمنجر")]
        public bool Radius { get; set; }=false;


        //[Obsolete]
        public List<Acive_Host_Users> Get_Acive_User()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<Acive_Host_Users> users = new List<Acive_Host_Users>();
            try
            {
                //string code = Properties.Settings.Default.userman_print;  
                string code = "/ip/hotspot/active/print";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    code = "/ip/hotspot/active/print";
                }
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    ITikCommand loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();



                    stopwatch.Stop();
                    foreach (var item in response)
                    {
                        try
                        {
                            //{ApiReSentence:.id=*E0B0A|server=Ngoom|user=1624932179|address=**********|mac-address=F6:06:80:F8:C3:0C|login-by=http-chap|uptime=23m51s|session-time-left=3h36m9s|
                            //idle-time=0s|keepalive-timeout=3m|bytes-in=28040678|bytes-out=164681944|packets-in=331966|packets-out=135692|limit-bytes-in=419430400|limit-bytes-out=419430400|limit-bytes-total=419430400|radius=true}

                            //{ApiReSentence:.id=*A000A06|server=hotspot1|user=4647814614|address=*********|mac-address=CE:18:06:6D:34:56|login-by=cookie|uptime=4m51s|session-time-left=4h20m10s|
                            //idle-time=0s|keepalive-timeout=2m|bytes-in=6137900|bytes-out=11076653|packets-in=10263|packets-out=13346|limit-bytes-total=26858708|radius=false|comment=mar/19/2025 04:57:15;04:25:01;26858708}
                            bool Radius = false;
                            Acive_Host_Users card = new Acive_Host_Users();
                            try
                            {
                                Radius = Convert.ToBoolean(item.GetResponseFieldOrDefault("radius", "false"));
                                card.Radius = Radius;
                            }
                            catch { }
                            card.IdHX = item.GetResponseFieldOrDefault(".id", null);
                            card.Server = item.GetResponseFieldOrDefault("Server", "");
                            card.UserName = item.GetResponseFieldOrDefault("user", "");
                            card.Address = item.GetResponseFieldOrDefault("address", "");
                            card.MacAddress = item.GetResponseFieldOrDefault("mac-address", "");
                            card.LoginBy = item.GetResponseFieldOrDefault("login-by", "");
                            card.Uptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime", "0"));
                            card.SessionTimeLeft = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("session-time-left", "0"));
                            card.IdleTime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("idle-time", "0"));
                            card.KeepaliveTimeout = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("keepalive-timeout", "0"));
                            card.Comment = (item.GetResponseFieldOrDefault("comment", ""));

                            try { card.BytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-in", "0")); } catch { }
                            try { card.BytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-out", "0")); } catch { }

                            //try { card.LimitBytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-in", "0")); }catch{ }
                            //try { card.LimitBytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-out", "0")); }catch{ }
                            try { card.LimitBytesTotal = Convert.ToDouble(item.GetResponseFieldOrDefault("limit-bytes-total", null)); } catch { }
                            users.Add(card);
                        }
                        catch { }
                    }
                    Global_Variable.Acive_Users = users;

                    return users;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                //if (ex.Message.Contains("database is not acceseble, yet"))
                //RJMessageBox.Show("لايمكن القراءه من قاعده بيانات اليوزمنجر في الروتر قم باصلاح قاعده بيانات اليوزمنجر");
                //else
                //    MessageBox.Show("لم يتم جلب كروت اليوزمنجر قم بتحديث الكروت من عمليات الكروت");
            }
            return users;
        }



    }

    public class Host_Users: Acive_Host_Users
    {

        public bool Authorized { get; set; }

        public bool Bypassed { get; set; }

        [DisplayName("الحالة")]
        public string Str_Authorized
        {
            get
            {
                if (Authorized)
                    return "مسجل بكرت";
                else if (Bypassed)
                    return "سماح مجان";
                else
                    return "غير مسجل";
            }
        }


        [DisplayName("2الايبي")]
        public string ToAddress { get; set; }

        public List<Host_Users> Get_Host_Users()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<Host_Users> users = new List<Host_Users>();
            try
            {
                //string code = Properties.Settings.Default.userman_print;  
                string code = "/ip/hotspot/host/print";
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    code = "/ip/hotspot/host/print";
                }
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    ITikCommand loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();



                    stopwatch.Stop();
                    foreach (var item in response)
                    {
                        try
                        {
                            Host_Users card = new Host_Users();
                            //{ApiReSentence:.id=*1E|mac-address=BC:32:5F:C9:AD:6B|address=***********|to-address=***********|server=hotspot1|uptime=21h23m44s|idle-time=6s|idle-timeout=5m|host-dead-time=6s|bridge-port=ether3|bytes-in=4939455|bytes-out=3328046|packets-in=18678|packets-out=12880|found-by=UDP :33794 -> **************:8802|authorized=false|bypassed=true|comment=DVR}
                            card.IdHX = item.GetResponseFieldOrDefault(".id", null);
                            card.Server = item.GetResponseFieldOrDefault("server", "");
                            card.Authorized = Convert.ToBoolean(item.GetResponseFieldOrDefault("authorized", "false"));
                            card.Bypassed = Convert.ToBoolean(item.GetResponseFieldOrDefault("bypassed", "false"));
                            card.Address = item.GetResponseFieldOrDefault("address", "");
                            card.ToAddress = item.GetResponseFieldOrDefault("to-address", "");
                            card.MacAddress = item.GetResponseFieldOrDefault("mac-address", "");
                            card.Uptime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("uptime", "0"));
                            card.IdleTime = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("idle-time", "0"));
                            card.KeepaliveTimeout = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("keepalive-timeout", "0"));
                            card.Comment = (item.GetResponseFieldOrDefault("comment", ""));
                            card.BridgePort = (item.GetResponseFieldOrDefault("bridge-port", ""));

                            try { card.BytesIn = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-in", "0")); } catch { }
                            try { card.BytesOut = Convert.ToDouble(item.GetResponseFieldOrDefault("bytes-out", "0")); } catch { }

                            users.Add(card);
                        }
                        catch { }
                    }
                    Global_Variable.Hosts_Users = users;

                    return users;
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            return users;
        }

    }
    public class Dhcp_Lease
    {
        public string IdHX { get; set; }
        //[DisplayName("العنوان")]
        public string Address { get; set; }
        public string MacAddress { get; set; }
        public string HostName { get; set; }
        public string ExpiresAfter { get; set; }

        public List<Dhcp_Lease> Get_Dhcp_Lease()
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            List<Dhcp_Lease> users = new List<Dhcp_Lease>();
            try
            {
                string code = "/ip/dhcp-server/lease/print";
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    ITikCommand loadCmd = connection.CreateCommandAndParameters(code);
                    var response = loadCmd.ExecuteList();
                    stopwatch.Stop();
                    foreach (var item in response)
                    {
                        try
                        {
                            Dhcp_Lease card = new Dhcp_Lease();
                            card.IdHX = item.GetResponseFieldOrDefault(".id", null);
                            card.Address = item.GetResponseFieldOrDefault("address", "");
                            card.MacAddress = item.GetResponseFieldOrDefault("mac-address", "");
                            card.HostName = item.GetResponseFieldOrDefault("host-name", "");
                            //card.ExpiresAfter = utils.GetTimeCard_InSeconds(item.GetResponseFieldOrDefault("expires-after", "0"));
                            users.Add(card);
                        }
                        catch { }
                    }
                    Global_Variable.Dhcp_Leases = users;

                    return users;
                }
            }
            catch 
            {
            }
            return users;
        }


    }

    public class Neighbor_Device
    {

    }
}
