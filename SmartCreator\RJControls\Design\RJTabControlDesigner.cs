using System;
using System.Collections;
using System.ComponentModel;
using System.ComponentModel.Design;
using System.Drawing;
using System.Windows.Forms;
using System.Windows.Forms.Design;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls.Design
{
    /// <summary>
    /// مصمم RJTabControl للـ Visual Studio Designer
    /// </summary>
    public class RJTabControlDesigner : ParentControlDesigner
    {
        private RJTabControl TabControl => (RJTabControl)Control;
        private DesignerVerbCollection verbs;

        public override DesignerVerbCollection Verbs
        {
            get
            {
                if (verbs == null)
                {
                    verbs = new DesignerVerbCollection();
                    verbs.Add(new DesignerVerb("إضافة تاب", OnAddTab));
                    verbs.Add(new DesignerVerb("إزالة تاب", OnRemoveTab));
                    verbs.Add(new DesignerVerb("التاب التالي", OnNextTab));
                    verbs.Add(new DesignerVerb("التاب السابق", OnPreviousTab));
                }
                return verbs;
            }
        }

        public override void Initialize(IComponent component)
        {
            base.Initialize(component);
            
            // تمكين التصميم
            EnableDesignMode(TabControl, "RJTabControl");
            
            // إضافة تاب افتراضي إذا لم يكن موجود
            if (TabControl.TabCount == 0)
            {
                AddDefaultTab();
            }
        }

        protected override void OnPaintAdornments(PaintEventArgs pe)
        {
            base.OnPaintAdornments(pe);
            
            // رسم حدود التصميم
            if (TabControl != null)
            {
                using (var pen = new Pen(SystemColors.ControlDark, 1))
                {
                    pen.DashStyle = System.Drawing.Drawing2D.DashStyle.Dash;
                    pe.Graphics.DrawRectangle(pen, 0, 0, TabControl.Width - 1, TabControl.Height - 1);
                }
            }
        }

        protected override bool GetHitTest(Point point)
        {
            // السماح بالنقر على التابات في وضع التصميم
            if (TabControl != null)
            {
                var hitTab = GetTabAt(point);
                if (hitTab != null)
                {
                    // تفعيل التاب المنقور عليه
                    TabControl.SelectedTab = hitTab;
                    return true;
                }
            }
            return base.GetHitTest(point);
        }

        private RJTabPage GetTabAt(Point point)
        {
            // البحث عن التاب في النقطة المحددة
            foreach (RJTabPage tab in TabControl.Tabs)
            {
                if (tab.Bounds.Contains(point))
                {
                    return tab;
                }
            }
            return null;
        }

        private void OnAddTab(object sender, EventArgs e)
        {
            // إضافة تاب جديد
            var host = GetService(typeof(IDesignerHost)) as IDesignerHost;
            if (host != null)
            {
                using (var transaction = host.CreateTransaction("إضافة تاب"))
                {
                    try
                    {
                        var newTab = new RJTabPage($"TabPage{TabControl.TabCount + 1}");
                        newTab.IconChar = IconChar.None;
                        
                        // إضافة التاب
                        TabControl.Tabs.Add(newTab);
                        
                        // تفعيل التاب الجديد
                        TabControl.SelectedTab = newTab;
                        
                        // إشعار التغيير
                        RaiseComponentChanged(TypeDescriptor.GetProperties(TabControl)["Tabs"], null, TabControl.Tabs);
                        
                        transaction.Commit();
                    }
                    catch
                    {
                        transaction.Cancel();
                        throw;
                    }
                }
            }
        }

        private void OnRemoveTab(object sender, EventArgs e)
        {
            // إزالة التاب النشط
            if (TabControl.SelectedTab != null && TabControl.TabCount > 0)
            {
                var host = GetService(typeof(IDesignerHost)) as IDesignerHost;
                if (host != null)
                {
                    using (var transaction = host.CreateTransaction("إزالة تاب"))
                    {
                        try
                        {
                            var tabToRemove = TabControl.SelectedTab;
                            TabControl.Tabs.Remove(tabToRemove);
                            
                            // إشعار التغيير
                            RaiseComponentChanged(TypeDescriptor.GetProperties(TabControl)["Tabs"], null, TabControl.Tabs);
                            
                            transaction.Commit();
                        }
                        catch
                        {
                            transaction.Cancel();
                            throw;
                        }
                    }
                }
            }
        }

        private void OnNextTab(object sender, EventArgs e)
        {
            // الانتقال للتاب التالي
            if (TabControl.TabCount > 1)
            {
                var currentIndex = TabControl.SelectedIndex;
                var nextIndex = (currentIndex + 1) % TabControl.TabCount;
                TabControl.SelectedIndex = nextIndex;
            }
        }

        private void OnPreviousTab(object sender, EventArgs e)
        {
            // الانتقال للتاب السابق
            if (TabControl.TabCount > 1)
            {
                var currentIndex = TabControl.SelectedIndex;
                var prevIndex = currentIndex == 0 ? TabControl.TabCount - 1 : currentIndex - 1;
                TabControl.SelectedIndex = prevIndex;
            }
        }

        private void AddDefaultTab()
        {
            // إضافة تاب افتراضي
            var defaultTab = new RJTabPage("TabPage1");
            defaultTab.IconChar = IconChar.None;
            TabControl.Tabs.Add(defaultTab);
        }

        protected override void PreFilterProperties(IDictionary properties)
        {
            base.PreFilterProperties(properties);
            
            // إخفاء خصائص غير مرغوب فيها
            properties.Remove("BackgroundImage");
            properties.Remove("BackgroundImageLayout");
            properties.Remove("AutoScroll");
            properties.Remove("AutoScrollMargin");
            properties.Remove("AutoScrollMinSize");
        }

        protected override void PostFilterProperties(IDictionary properties)
        {
            base.PostFilterProperties(properties);
            
            // إضافة خصائص مخصصة للتصميم
            if (properties["Tabs"] is PropertyDescriptor tabsProperty)
            {
                properties["Tabs"] = TypeDescriptor.CreateProperty(
                    typeof(RJTabControlDesigner),
                    tabsProperty,
                    new Attribute[] {
                        new CategoryAttribute("RJ Code Advance"),
                        new DescriptionAttribute("Collection of tabs in the control")
                    });
            }
        }

        public override bool CanParent(Control control)
        {
            // منع إضافة كنترولات مباشرة للـ TabControl
            // يجب إضافتها للتابات
            return false;
        }

        protected override Control GetParentForComponent(IComponent component)
        {
            // توجيه الكنترولات للتاب النشط
            if (TabControl?.SelectedTab != null)
            {
                return TabControl.SelectedTab.ContentPanel as Control;
            }
            return base.GetParentForComponent(component);
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                verbs?.Clear();
                verbs = null;
            }
            base.Dispose(disposing);
        }
    }
}
