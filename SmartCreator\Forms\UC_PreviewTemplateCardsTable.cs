﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;

namespace SmartCreator.Forms
{
    public partial class UC_PreviewTemplateCardsTable : UserControl
    {
        public CardsTableDesg1 card;
        private bool firstLoad = true;
        private bool saveToClass = true;
        private bool selectTemplateFromDrowpDown = true;
        //private bool selectTemplateFromCheckBox = true;
        public static string pathfile = "";
        bool addDeletTemplate = false;

        bool mouseClicked = false;
        public DataTable dt_templateCards;
        UmProfile profile;
        SourceCardsTemplate sorcCard;
        string SP_code;
        public UC_PreviewTemplateCardsTable()
        {
            InitializeComponent();
            utils.Control_textSize(tableLayoutPanel1);
            //utils.dgv_textSize(dgv);
            return;

        }
        string templateID; string _Name; string price; string transferLimit; string uptimeLimit; string Validity; string numberPrint; string SP_id;
        
        public UC_PreviewTemplateCardsTable(string templateId,string profileName,string _SP)
        {
            InitializeComponent();
            InitTable();
            profile = Global_Variable.UM_Profile.Find(x => x.Name == profileName);
            SP_code = _SP;
            SP_id = _SP;
            if (profile == null)
                return;
            
            //disableAll_Lable();
            Get_TemplateCardsItemsGraphics(templateId);
            SetValuToCardToGraphics();
            show_Profile_info(templateId, profile.Name, profile.Price.ToString(), profile.TransferLimit.ToString(), profile.UptimeLimit.ToString(), profile.Validity.ToString(), numberPrint, SP_id);
            //CBox_Curncey.Visible = true;
            utils.Control_textSize(tableLayoutPanel1);
        }
        public UC_PreviewTemplateCardsTable(Clss_InfoPrint clss_InfoPrint = null)
        {
            InitializeComponent();
            InitTable();
            //profile = Global_Variable.UM_Profile.Find(x => x.Name == profileName);
            //SP_code = _SP;
            //SP_id = _SP;
            //if (profile == null)
                //return;

            //disableAll_Lable();
            Get_TemplateCardsItemsGraphics("0");
            SetValuToCardToGraphics();
            show_Profile_info("0", profile.Name, profile.Price.ToString(), profile.TransferLimit.ToString(), profile.UptimeLimit.ToString(), profile.Validity.ToString(), numberPrint, SP_id);
            //CBox_Curncey.Visible = true;
            utils.Control_textSize(tableLayoutPanel1);
        }

        public UC_PreviewTemplateCardsTable(string _templateID, string _name, string _price, string _transferLimit, string _uptimeLimit, string _Validity, string _numberBrint, string _sp)
        {
            InitializeComponent();
            InitTable();

            templateID = _templateID;
            _name = _Name;
            if (_price == "")
                _price= "0";
            price = _price;
            if (_transferLimit =="")
                _transferLimit = "0";
            transferLimit = _transferLimit;
            if (_uptimeLimit=="")
                _uptimeLimit = "0";
            uptimeLimit = _uptimeLimit;
            if (_Validity=="")
                _Validity = "0";
            Validity = _Validity;
            numberPrint = _numberBrint;
            SP_id = _sp;

            //disableAll_Lable();
            Get_TemplateCardsItemsGraphics(_templateID);
            SetValuToCardToGraphics();
            show_Profile_info(templateID, _Name, price, transferLimit, uptimeLimit, Validity, numberPrint, SP_id);
            //CBox_Curncey.Visible = true;
            utils.Control_textSize(tableLayoutPanel1);
        }

        private void InitTable()
        {
            tableLayoutPanel1.RowStyles[1].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[1].Height = 0;

            tableLayoutPanel1.RowStyles[3].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[3].Height = 0;

            tableLayoutPanel1.RowStyles[5].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[5].Height = 0;

            tableLayoutPanel1.RowStyles[8].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[8].Height = 0;

            tableLayoutPanel1.RowStyles[9].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[9].Height = 0;

            tableLayoutPanel1.RowStyles[10].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[10].Height = 0;

            tableLayoutPanel1.RowStyles[11].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[11].Height = 0;

            //tableLayoutPanel1.RowStyles[12].SizeType = SizeType.Absolute;
            //tableLayoutPanel1.RowStyles[12].Height = 0;

            tableLayoutPanel1.RowStyles[13].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[13].Height = 0;

            tableLayoutPanel1.RowStyles[14].SizeType = SizeType.Absolute;
            tableLayoutPanel1.RowStyles[14].Height = 0;

        
        }
        private void Set_Font()
        {
            foreach (var contrl in this.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(System.Windows.Forms.Label))
                    {
                        System.Windows.Forms.Label textbox = (System.Windows.Forms.Label)contrl;
                        textbox.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 8f*utils.ScaleFactor, false, GraphicsUnit.Point, 0);
                    }
                }
                catch { }
            }
            //Login_Lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);
            //Password_Lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 10f, false, GraphicsUnit.Point, 0);

        }
        private void disableAll_Lable()
        {
            foreach (Control lb in tableLayoutPanel1.Controls)
            {
                if (lb.GetType() == typeof(RJCheckBox))
                {
                    RJCheckBox ch = new RJCheckBox();
                    //System.Windows.Forms.CheckBox ch = new System.Windows.Forms.CheckBox();
                    ch = (RJCheckBox)lb;
                    ch.Checked = false;
                }
            }
        }
        private void Get_TemplateCardsItemsGraphics(string templateName)
        {
            if (addDeletTemplate) return;
            sorcCard = new SourceCardsTemplate();
            try
            {
                sorcCard = SqlDataAccess.Get_template_cards_By_id(templateName);
                //card = SqlDataAccess.Get_template_cards_By_id(CBox_TemplateCards.SelectedItem.ToString());
                if (sorcCard == null)
                {
                    card = new CardsTableDesg1();
                    card.setingCard.NumberCulum = 3;
                    card.setingCard.card_border_Size = .5f;
                    card.setingCard.Fixed_Width_Card = false;
                    card.setingCard.Show_border_Midell = true;

                    card = new CardsTableDesg1();
                    card.cardsItems.info1.title_text = "شبكة سمارت اللاسليكة";
                    card.cardsItems.info1.Enable = true;

                    card.cardsItems.login.title_text = "اسم الدخول";
                    card.cardsItems.login.title_show = true;
                    card.cardsItems.login.Enable = true;

                    card.cardsItems.Password.title_text = "كلمة المرور";
                    card.cardsItems.Password.title_show = true;
                    card.cardsItems.Password.Enable = false;

                    card.cardsItems.Time.title_text = "الوقت";
                    card.cardsItems.Time.title_show = true;
                    card.cardsItems.Time.Enable = true;

                    card.cardsItems.Price.title_text = "السعر";
                    card.cardsItems.Price.title_show = true;
                    card.cardsItems.Price.Enable = true;

                    card.cardsItems.Validity.title_text = "الصلاحية";
                    card.cardsItems.Validity.title_show = true;
                    card.cardsItems.Validity.Enable = false;

                    card.cardsItems.Size.title_text = "كمية التحميل";
                    card.cardsItems.Size.title_show = true;
                    card.cardsItems.Size.Enable = false;

                    card.cardsItems.SP.title_text = "نقطة البيع";
                    card.cardsItems.SP.title_show = true;
                    card.cardsItems.SP.Enable = false;

                    card.cardsItems.SN.title_text = "التسلسل";
                    card.cardsItems.SN.title_show = true;
                    card.cardsItems.SN.Enable = false;

                    card.cardsItems.Date_Print.title_text = "تاريخ الطباعة";
                    card.cardsItems.Date_Print.title_show = true;
                    card.cardsItems.Date_Print.Enable = false;

                    card.cardsItems.Number_Print.title_text = "رقم الدفعة";
                    card.cardsItems.Number_Print.title_show = true;
                    card.cardsItems.Number_Print.Enable = false;

                    card.cardsItems.info3.title_text = "للتواصل والاستفسار الاتصال علي:7777777";
                    card.cardsItems.info3.title_show = true;
                    card.cardsItems.info3.Enable = true;

                    card.cardsItems.info4.title_text = "اعلان 1";
                    card.cardsItems.info4.title_show = true;
                    card.cardsItems.info4.Enable = false;
                }
                else
                    card = JsonConvert.DeserializeObject<CardsTableDesg1>(sorcCard.values);
            }
            catch (Exception ex) { /*MessageBox.Show("Get_TemplateCardsItemsGraphics   " + ex.Message);*/ }
        }

        public void SetValuToCardToGraphics()
        {
            if (card == null) return;
            set_value_For_item();
            selectTemplateFromDrowpDown = false;
        }
        void DisableBorder_All()
        {
            return;
            Label ll = new Label();
            foreach (Control lbl in tableLayoutPanel1.Controls)
            {
                if (lbl.GetType() == typeof(Label))
                {
                    ll = (Label)lbl;
                    ll.BackColor = Color.Transparent;
                    //ll.BackColor = Color.White;
                }
            }
        }
        void DisableBorder_All(Control elment)
        {
            return;
            Label ll = new Label();
            RJLabel elm = new RJLabel();

            foreach (Control lbl in tableLayoutPanel1.Controls)
            {
                if (lbl.GetType() == typeof(RJLabel))
                {
                    ll = (RJLabel)lbl;
                    //ll.BorderStyle = BorderStyle.None;
                    //ll.BackColor = Color.White;
                    ll.BackColor = Color.FromArgb(250, 252, 253);
                    ll.ForeColor = Color.FromArgb(132, 129, 132);

                }
            }

            if (elment.GetType() == typeof(RJLabel))
            {
                ll = (Label)elment;
                ll.BackColor = Color.Gold;
                ll.ForeColor = Color.Black;
                //groupBox_Abaad.Visible = false;
                //groupBox_FontAndSize.Visible = true;
                //panel_ColorBack.Visible = false;


            }
            elment.BringToFront();
        }
        void set_value_For_item()
        {
            //============info1=================
            if (card.cardsItems.info1.Enable == false)
            {
                tableLayoutPanel1.RowStyles[0].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[0].Height = 0;
            }
            else
            {
                TXT_info1.Text = card.cardsItems.info1.title_text;
                Set_Proprties_For_Item(TXT_info1, card.cardsItems.info1);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info1, card.cardsItems.info1, null);
            }
            //============info2=================
            if (card.cardsItems.info2.Enable == false)
            {
                tableLayoutPanel1.RowStyles[1].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[1].Height = 0;

            }
            else
            {
                TXT_info2.Text = card.cardsItems.info2.title_text;
                Set_Proprties_For_Item(TXT_info2, card.cardsItems.info2);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info2, card.cardsItems.info2, null);
            }
            //============login=================
            if (card.cardsItems.login.Enable == false)
            {
                tableLayoutPanel1.RowStyles[2].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[2].Height = 0;
            }
            else
            {
                txt_lbl_username.Text = card.cardsItems.login.title_text;
                Set_Proprties_For_Item(TXT_USerName, card.cardsItems.login);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_USerName, card.cardsItems.login, txt_lbl_username);
            }
            //============password=================
            if (card.cardsItems.Password.Enable == false)
            {
                tableLayoutPanel1.RowStyles[3].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[3].Height = 0;
            }
            else
            {
                txt_lbl_password.Text = card.cardsItems.Password.title_text;
                Set_Proprties_For_Item(TXT_Password, card.cardsItems.Password);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Password, card.cardsItems.Password, txt_lbl_password);
            }
            //============Price=================
            if (card.cardsItems.Price.Enable == false)
            {
                tableLayoutPanel1.RowStyles[7].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[7].Height = 0;
            }
            else
            {
                txt_lbl_price.Text = card.cardsItems.Price.title_text;
                Set_Proprties_For_Item(TXT_Price, card.cardsItems.Price);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Price, card.cardsItems.Price, txt_lbl_price);
            }
            //============Lbl_Time=================
            if (card.cardsItems.Time.Enable == false)
            {
                tableLayoutPanel1.RowStyles[4].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[4].Height = 0;
            }
            else
            {
                txt_lbl_time.Text = card.cardsItems.Time.title_text;
                Set_Proprties_For_Item(TXT_Uptime, card.cardsItems.Time);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Uptime, card.cardsItems.Time, txt_lbl_time);
            }
            //============Lbl_SizeTransfer=================
            if (card.cardsItems.Size.Enable == false)
            {
                tableLayoutPanel1.RowStyles[6].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[6].Height = 0;
            }
            else
            {
                txt_lbl_download.Text = card.cardsItems.Size.title_text;
                Set_Proprties_For_Item(TXT_SizeTransfer, card.cardsItems.Size);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SizeTransfer, card.cardsItems.Size, txt_lbl_download);
            }
            //============Lbl_validity=================
            if (card.cardsItems.Validity.Enable == false)
            {
                tableLayoutPanel1.RowStyles[5].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[5].Height = 0;
            }
            else
            {
                txt_lbl_validy.Text = card.cardsItems.Validity.title_text;
                Set_Proprties_For_Item(TXT_Validate, card.cardsItems.Validity);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_Validate, card.cardsItems.Validity, txt_lbl_validy);
            }
            //============lbl_Squ_Nuber=================
            if (card.cardsItems.SN.Enable == false)
            {
                tableLayoutPanel1.RowStyles[9].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[9].Height = 0;
            }
            else
            {
                txt_lbl_SN.Text = card.cardsItems.SN.title_text;
                Set_Proprties_For_Item(TXT_SQ, card.cardsItems.SN);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SQ, card.cardsItems.SN, txt_lbl_SN);
            }
            //============SP=================
            if (card.cardsItems.SP.Enable == false)
            {
                tableLayoutPanel1.RowStyles[8].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[8].Height = 0;
            }
            else
            {
                txt_lbl_SP.Text = card.cardsItems.SP.title_text;
                Set_Proprties_For_Item(TXT_SP, card.cardsItems.SP);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_SP, card.cardsItems.SP, txt_lbl_SP);
            }
            //============Lbl_Number_Print=================
            if (card.cardsItems.Number_Print.Enable == false)
            {
                tableLayoutPanel1.RowStyles[11].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[11].Height = 0;
            }
            else
            {
                txt_lbl_NumberPrint.Text = card.cardsItems.Number_Print.title_text;
                Set_Proprties_For_Item(TXT_NumberPrint, card.cardsItems.Number_Print);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_NumberPrint, card.cardsItems.Number_Print, txt_lbl_NumberPrint);
            }
            //============TXT_DatePrint=================
            if (card.cardsItems.Date_Print.Enable == false)
            {
                tableLayoutPanel1.RowStyles[10].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[10].Height = 0;
            }
            else
            {
                txt_lbl_DatePrint.Text = card.cardsItems.Date_Print.title_text;
                Set_Proprties_For_Item(TXT_DatePrint, card.cardsItems.Date_Print);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_DatePrint, card.cardsItems.Date_Print, txt_lbl_DatePrint);
            }
            //============info3=================
            if (card.cardsItems.info3.Enable == false)
            {
                tableLayoutPanel1.RowStyles[12].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[12].Height = 0;
            }
            else
            {
                TXT_info3.Text = card.cardsItems.info3.title_text;
                Set_Proprties_For_Item(TXT_info3, card.cardsItems.info3);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info3, card.cardsItems.info3, null);
            }
            //============info4=================
            if (card.cardsItems.info4.Enable == false)
            {
                tableLayoutPanel1.RowStyles[13].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[13].Height = 0;
            }
            else
            {
                TXT_info4.Text = card.cardsItems.info4.title_text;
                Set_Proprties_For_Item(TXT_info4, card.cardsItems.info4);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info4, card.cardsItems.info4, null);
            }

            //============info5=================
            if (card.cardsItems.info5.Enable == false)
            {
                tableLayoutPanel1.RowStyles[tableLayoutPanel1.RowCount-1].SizeType = SizeType.Absolute;
                tableLayoutPanel1.RowStyles[tableLayoutPanel1.RowCount - 1].Height = 0;
            }
            else
            {
                TXT_info5.Text = card.cardsItems.info5.title_text;
                Set_Proprties_For_Item(TXT_info5, card.cardsItems.info5);
                Change_Font_Size_Bold_Color_Control_In_Imag(TXT_info5, card.cardsItems.info5, null);
            }
            firstLoad = false;
            selectTemplateFromDrowpDown = false;
            saveToClass = true;
        }
        void Set_Proprties_For_Item(Control lbl, PropertyItemText loc)
        {
            return;
            try
            {
                 
                Color color = ColorTranslator.FromHtml(loc.Color);
                lbl.ForeColor = color;
                Color colorTitle = ColorTranslator.FromHtml(loc.title_Color);
            }
            catch (Exception e) { MessageBox.Show(e.Message); }
        }
        void Change_Font_Size_Bold_Color_Control_In_Imag(Control lbl, PropertyItemText loc, Control txt_title)
        {
            if (lbl.GetType() == typeof(RJTextBox))
            {
                lbl = (RJTextBox)lbl;
                Color color = ColorTranslator.FromHtml(loc.Color);
                lbl.ForeColor = color;
                try
                {
                    if (txt_title != null)
                    {
                        Color colorTitle = ColorTranslator.FromHtml(loc.title_Color);
                        txt_title.ForeColor = colorTitle;
                    }
                }
                catch { }
            }
            
        }

       public void show_Profile_info(string templateID, string name, string price, string transferLimit, string uptimeLimit, string Validity, string numberBrint1, string SP_id)
        {
            


            CardsTableDesg1 cardTable1 = new CardsTableDesg1();
            //Dictionary<string, string> Cardsdata = new Dictionary<string, string>();
            string profileName = _Name;

           
            if (price == "")
                price = "0";

            if (transferLimit == "")
                transferLimit = "0";
           string sizeTransfer = transferLimit;

            if (uptimeLimit == "")
                uptimeLimit = "0";
        
            if (Validity == "")
                Validity = "0";


            string time = uptimeLimit;  // or  time="5h";
            string DatePrint = "";
            cardTable1 = new CardsTableDesg1();
            cardTable1 = card;
            if (cardTable1.cardsItems.Price.Enable)
            {
                if (cardTable1.cardsItems.Price.unit_show)
                {
                    price = price + " " + cardTable1.setingCard.currency.ToString();
                }
                TXT_Price.Text = price;
            }
            if (cardTable1.cardsItems.Validity.Enable)
            {
                if (cardTable1.cardsItems.Validity.unit_show)
                {
                    if (Validity != "" || Validity != "مفتوح" || Validity != "0")
                    {
                        Validity = utils.Get_Days_in_WeeksDays_or_MonthsWeekDays(Validity, cardTable1.cardsItems.Validity.unit_format);
                    }
                }
                TXT_Validate.Text = Validity;

            }
            if (cardTable1.cardsItems.Time.Enable)
            {
                try
                {
                    if (time != "" || time != "مفتوح" || time != "0" || time != "00:00:00")
                    {
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                        time = utils.Get_Seconds_in_Houre_or_DaysHoure(time, cardTable1.cardsItems.Time.unit_format, cardTable1.cardsItems.Time.unit_show);
                    }
                }
                catch { }
                TXT_Uptime.Text = time;

            }
            if (cardTable1.cardsItems.Size.Enable)
            {
                if (sizeTransfer != "" || sizeTransfer.ToLower() != "0b" || sizeTransfer != "0")
                {
                    sizeTransfer = utils.ConvertSize_Get_MB_or_GM(sizeTransfer, cardTable1.cardsItems.Size.unit_format, cardTable1.cardsItems.Size.unit_show);
                }
                TXT_SizeTransfer.Text = sizeTransfer;
            }
            if (cardTable1.cardsItems.Date_Print.Enable)
            {
                string format = cardTable1.cardsItems.Date_Print.format;
                DateTime now = DateTime.Now;
                DatePrint = now.ToString("dd-MM-yyyy");
                try
                {
                    DatePrint = (now.ToString(format));
                }
                catch (Exception ex) { MessageBox.Show("صيغة التاريخ التي ادخلتها خطاء\n" + format + "\n" + ex.Message); }
                TXT_DatePrint.Text = DatePrint;

            }
            if (cardTable1.cardsItems.Number_Print.Enable)
            {
                int batchNumber = SqlDataAccess.Get_lastID_Batch_cards();
                TXT_NumberPrint.Text = (batchNumber+1).ToString();
            }
            //if (cardTable1.cardsItems.SP.Enable)
            //{
            //    if (SP_code != "" && SP_code != "0" && SP_code != "-1")
            //    {
            //        try
            //        {

            //            SellingPints Show_sp = SqlDataAccess.GetSellingPints(SP_code);
            //            if (Show_sp != null)
            //            {
            //                if (card.cardsItems.SP.Show_ByNumber_OR_Name)
            //                    TXT_SP.Text = (Show_sp.code).ToString();
            //                else
            //                    TXT_SP.Text = (Show_sp.name).ToString();
            //            }
            //        }
            //        catch { }
            //    }
            //}
            if (card.cardsItems.SP.Enable)
            {
                if (SP_id != "" && SP_id != "0" && SP_id != "-1")
                {
                    try
                    {
                        string _sp = "";
                        Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                       
                        SellingPoint Show_sp = smart_DataAccess.Get_SellingPoint_Code(SP_id);
                        if (Show_sp != null)
                        {
                            if (card.cardsItems.SP.Show_ByNumber_OR_Name)
                            {
                                TXT_SP.Text = (Show_sp.Code).ToString();
                                _sp = (Show_sp.Code).ToString();
                            }
                            else
                            {
                                TXT_SP.Text = (Show_sp.UserName).ToString();
                                _sp = (Show_sp.UserName).ToString();
                            }
                        }
                        if (card.cardsItems.SP.title_show)
                        {
                            TXT_SP.Text = card.cardsItems.SP.title_text + " " + _sp;
                        }
                    }
                    catch { }
                }
            }

        }

         private void UC_PreviewTemplateCardsTable_Load(object sender, EventArgs e)
        {
            
            foreach (var contrl in this.tableLayoutPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJTextBox))
                    {
                        RJTextBox textbox = (RJTextBox)contrl;

                        textbox.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
                    }
                }
                catch { }
            }
        }
    }
}
