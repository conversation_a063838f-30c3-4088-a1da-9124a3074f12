﻿using Dapper;
using Newtonsoft.Json;
using SmartCreator.DAL;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Service
{
    /// <summary>
    /// خدمة إدارة إعدادات التطبيق
    /// </summary>
    public class AppSettingsService : IDisposable
    {
        public AppSettingsService()
        {
            // تهيئة قاعدة البيانات عند الحاجة
            DatabaseInitializer.InitializeSmartDatabase();
        }

        /// <summary>
        /// حفظ إعداد
        /// </summary>
        public async Task SaveSettingAsync<T>(string category, string key, T value, string description = null)
        {
            try
            {
                var stringValue = value?.ToString();
                var dataType = typeof(T).Name;

                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    // التحقق من وجود الإعداد
                    var existingSetting = await connection.QueryFirstOrDefaultAsync<AppSetting>(
                        "SELECT * FROM AppSettings WHERE Category = @Category AND SettingKey = @SettingKey",
                        new { Category = category, SettingKey = key }
                    );

                    if (existingSetting != null)
                    {
                        // تحديث الإعداد الموجود
                        await connection.ExecuteAsync(@"
                            UPDATE AppSettings
                            SET SettingValue = @SettingValue, DataType = @DataType, UpdatedAt = @UpdatedAt, Description = @Description
                            WHERE Category = @Category AND SettingKey = @SettingKey",
                            new
                            {
                                SettingValue = stringValue,
                                DataType = dataType,
                                UpdatedAt = DateTime.Now,
                                Description = description ?? existingSetting.Description,
                                Category = category,
                                SettingKey = key
                            }
                        );
                    }
                    else
                    {
                        // إنشاء إعداد جديد
                        await connection.ExecuteAsync(@"
                            INSERT INTO AppSettings (Category, SettingKey, SettingValue, DataType, Description, CreatedAt, UpdatedAt, IsRequired, IsEditable)
                            VALUES (@Category, @SettingKey, @SettingValue, @DataType, @Description, @CreatedAt, @UpdatedAt, @IsRequired, @IsEditable)",
                            new
                            {
                                Category = category,
                                SettingKey = key,
                                SettingValue = stringValue,
                                DataType = dataType,
                                Description = description ?? $"إعداد {key} في مجموعة {category}",
                                CreatedAt = DateTime.Now,
                                UpdatedAt = DateTime.Now,
                                IsRequired = false,
                                IsEditable = true
                            }
                        );
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ الإعداد {category}.{key}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الإعداد: {ex.Message}");
            }
        }

        /// <summary>
        /// استرجاع إعداد
        /// </summary>
        public async Task<T> GetSettingAsync<T>(string category, string key, T defaultValue = default(T))
        {
            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    var setting = await connection.QueryFirstOrDefaultAsync<AppSetting>(
                        "SELECT * FROM AppSettings WHERE Category = @Category AND SettingKey = @SettingKey",
                        new { Category = category, SettingKey = key }
                    );

                    if (setting != null && !string.IsNullOrEmpty(setting.SettingValue))
                    {
                        // محاولة تحويل القيمة إلى النوع المطلوب
                        if (typeof(T) == typeof(string))
                        {
                            return (T)(object)setting.SettingValue;
                        }
                        else if (typeof(T) == typeof(int))
                        {
                            if (int.TryParse(setting.SettingValue, out int intValue))
                                return (T)(object)intValue;
                        }
                        else if (typeof(T) == typeof(bool))
                        {
                            if (bool.TryParse(setting.SettingValue, out bool boolValue))
                                return (T)(object)boolValue;
                        }
                        else if (typeof(T) == typeof(decimal))
                        {
                            if (decimal.TryParse(setting.SettingValue, out decimal decimalValue))
                                return (T)(object)decimalValue;
                        }
                        else
                        {
                            // محاولة تحويل JSON للأنواع المعقدة
                            try
                            {
                                return JsonConvert.DeserializeObject<T>(setting.SettingValue);
                            }
                            catch
                            {
                                // إذا فشل JSON، محاولة التحويل المباشر
                                return (T)Convert.ChangeType(setting.SettingValue, typeof(T));
                            }
                        }

                        System.Diagnostics.Debug.WriteLine($"✅ تم استرجاع الإعداد {category}.{key}");
                    }

                    System.Diagnostics.Debug.WriteLine($"⚠️ لم يتم العثور على الإعداد {category}.{key} - استخدام القيمة الافتراضية");
                    return defaultValue;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استرجاع الإعداد: {ex.Message}");
                return defaultValue;
            }
        }

        /// <summary>
        /// حفظ إعدادات تسجيل الدخول
        /// </summary>
        public async Task SaveLoginSettingsAsync(string ipAddress, int port, string username, bool rememberMe)
        {
            await SaveSettingAsync("Login", "LastIpAddress", ipAddress, "آخر عنوان IP مستخدم");
            await SaveSettingAsync("Login", "LastPort", port, "آخر منفذ مستخدم");
            await SaveSettingAsync("Login", "LastUsername", username, "آخر اسم مستخدم");
            await SaveSettingAsync("Login", "RememberMe", rememberMe, "تذكر بيانات تسجيل الدخول");
        }

        /// <summary>
        /// استرجاع إعدادات تسجيل الدخول
        /// </summary>
        public async Task<(string ipAddress, int port, string username, bool rememberMe)> GetLoginSettingsAsync()
        {
            var ipAddress = await GetSettingAsync("Login", "LastIpAddress", "***********");
            var port = await GetSettingAsync("Login", "LastPort", 8728);
            var username = await GetSettingAsync("Login", "LastUsername", "admin");
            var rememberMe = await GetSettingAsync("Login", "RememberMe", false);

            return (ipAddress, port, username, rememberMe);
        }

        /// <summary>
        /// حفظ إعدادات الواجهة
        /// </summary>
        public async Task SaveUISettingsAsync(string theme, string language, bool autoRefresh, int refreshInterval)
        {
            await SaveSettingAsync("UI", "Theme", theme, "سمة الواجهة");
            await SaveSettingAsync("UI", "Language", language, "لغة التطبيق");
            await SaveSettingAsync("UI", "AutoRefresh", autoRefresh, "التحديث التلقائي");
            await SaveSettingAsync("UI", "RefreshInterval", refreshInterval, "فترة التحديث بالثواني");
        }

        /// <summary>
        /// استرجاع إعدادات الواجهة
        /// </summary>
        public async Task<(string theme, string language, bool autoRefresh, int refreshInterval)> GetUISettingsAsync()
        {
            var theme = await GetSettingAsync("UI", "Theme", "Default");
            var language = await GetSettingAsync("UI", "Language", "ar");
            var autoRefresh = await GetSettingAsync("UI", "AutoRefresh", true);
            var refreshInterval = await GetSettingAsync("UI", "RefreshInterval", 1);

            return (theme, language, autoRefresh, refreshInterval);
        }

        /// <summary>
        /// حذف إعداد
        /// </summary>
        public async Task DeleteSettingAsync(string category, string key)
        {
            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    var rowsAffected = await connection.ExecuteAsync(
                        "DELETE FROM AppSettings WHERE Category = @Category AND SettingKey = @SettingKey",
                        new { Category = category, SettingKey = key }
                    );

                    if (rowsAffected > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم حذف الإعداد {category}.{key}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف الإعداد: {ex.Message}");
            }
        }

        /// <summary>
        /// حذف جميع إعدادات مجموعة معينة
        /// </summary>
        public async Task DeleteCategorySettingsAsync(string category)
        {
            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    var rowsAffected = await connection.ExecuteAsync(
                        "DELETE FROM AppSettings WHERE Category = @Category",
                        new { Category = category }
                    );

                    if (rowsAffected > 0)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ تم حذف {rowsAffected} إعداد من مجموعة {category}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف إعدادات المجموعة: {ex.Message}");
            }
        }

        /// <summary>
        /// إعادة تعيين الإعدادات للقيم الافتراضية
        /// </summary>
        public async Task ResetToDefaultsAsync()
        {
            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    // حذف جميع الإعدادات الحالية
                    await connection.ExecuteAsync("DELETE FROM AppSettings");

                    // إعادة تهيئة قاعدة البيانات مع البيانات الافتراضية
                    DatabaseInitializer.InitializeSmartDatabase();

                    System.Diagnostics.Debug.WriteLine("✅ تم إعادة تعيين جميع الإعدادات للقيم الافتراضية");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تعيين الإعدادات: {ex.Message}");
            }
        }

        #region Batch Operations

        /// <summary>
        /// حفظ إعدادات متعددة دفعة واحدة
        /// </summary>
        public async Task SaveSettingsBatchAsync(string category, Dictionary<string, object> settings)
        {
            if (string.IsNullOrEmpty(category) || settings == null || !settings.Any())
                return;

            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    connection.Open();
                    using (var transaction = connection.BeginTransaction())
                    {
                        try
                        {
                            foreach (var setting in settings)
                            {
                                var settingValue = setting.Value != null ? JsonConvert.SerializeObject(setting.Value) : null;
                                var dataType = GetDataTypeName(setting.Value?.GetType());

                                await connection.ExecuteAsync(@"
                                    INSERT OR REPLACE INTO AppSettings
                                    (Category, SettingKey, SettingValue, DataType, UpdatedAt)
                                    VALUES (@Category, @SettingKey, @SettingValue, @DataType, @UpdatedAt)",
                                    new
                                    {
                                        Category = category,
                                        SettingKey = setting.Key,
                                        SettingValue = settingValue,
                                        DataType = dataType,
                                        UpdatedAt = DateTime.Now
                                    }, transaction);
                            }

                            transaction.Commit();
                            System.Diagnostics.Debug.WriteLine($"✅ تم حفظ {settings.Count} إعداد في مجموعة {category}");
                        }
                        catch
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ الإعدادات المتعددة: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// استرجاع إعدادات متعددة دفعة واحدة
        /// </summary>
        public async Task<Dictionary<string, T>> GetSettingsBatchAsync<T>(string category, Dictionary<string, T> defaultValues)
        {
            var result = new Dictionary<string, T>();

            if (string.IsNullOrEmpty(category) || defaultValues == null)
                return result;

            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    connection.Open();
                    var keys = defaultValues.Keys.ToList();
                    var placeholders = string.Join(",", keys.Select((_, i) => $"@key{i}"));

                    var parameters = new DynamicParameters();
                    parameters.Add("Category", category);
                    for (int i = 0; i < keys.Count; i++)
                    {
                        parameters.Add($"key{i}", keys[i]);
                    }

                    var settings = await connection.QueryAsync<AppSetting>($@"
                        SELECT * FROM AppSettings
                        WHERE Category = @Category AND SettingKey IN ({placeholders})",
                        parameters);

                    foreach (var defaultValue in defaultValues)
                    {
                        var setting = settings.FirstOrDefault(s => s.SettingKey == defaultValue.Key);

                        if (setting != null && !string.IsNullOrEmpty(setting.SettingValue))
                        {
                            try
                            {
                                var value = JsonConvert.DeserializeObject<T>(setting.SettingValue);
                                result[defaultValue.Key] = value;
                            }
                            catch
                            {
                                result[defaultValue.Key] = defaultValue.Value;
                            }
                        }
                        else
                        {
                            result[defaultValue.Key] = defaultValue.Value;
                        }
                    }
                }

                System.Diagnostics.Debug.WriteLine($"✅ تم استرجاع {result.Count} إعداد من مجموعة {category}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استرجاع الإعدادات المتعددة: {ex.Message}");

                // إرجاع القيم الافتراضية في حالة الخطأ
                foreach (var defaultValue in defaultValues)
                {
                    result[defaultValue.Key] = defaultValue.Value;
                }
            }

            return result;
        }

        /// <summary>
        /// حفظ إعدادات نموذج كامل دفعة واحدة
        /// </summary>
        public async Task SaveFormSettingsBatchAsync<T>(string formName, T settingsObject) where T : class
        {
            if (string.IsNullOrEmpty(formName) || settingsObject == null)
                return;

            try
            {
                var properties = typeof(T).GetProperties();
                var settings = new Dictionary<string, object>();

                foreach (var property in properties)
                {
                    var value = property.GetValue(settingsObject);
                    settings[property.Name] = value;
                }

                await SaveSettingsBatchAsync(formName, settings);
                System.Diagnostics.Debug.WriteLine($"✅ تم حفظ إعدادات نموذج {formName} ({properties.Length} خاصية)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات النموذج: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// استرجاع إعدادات نموذج كامل دفعة واحدة
        /// </summary>
        public async Task<T> GetFormSettingsBatchAsync<T>(string formName, T defaultSettings = null) where T : class, new()
        {
            if (string.IsNullOrEmpty(formName))
                return defaultSettings ?? new T();

            try
            {
                using (var connection = DatabaseConnection.GetSmartConnection())
                {
                    var settings = await connection.QueryAsync<AppSetting>(@"
                        SELECT * FROM AppSettings
                        WHERE Category = @Category",
                        new { Category = formName });

                    var result = defaultSettings ?? new T();
                    var properties = typeof(T).GetProperties();

                    foreach (var property in properties)
                    {
                        var setting = settings.FirstOrDefault(s => s.SettingKey == property.Name);

                        if (setting != null && !string.IsNullOrEmpty(setting.SettingValue))
                        {
                            try
                            {
                                var value = JsonConvert.DeserializeObject(setting.SettingValue, property.PropertyType);
                                property.SetValue(result, value);
                            }
                            catch
                            {
                                // الاحتفاظ بالقيمة الافتراضية في حالة فشل التحويل
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine($"✅ تم استرجاع إعدادات نموذج {formName}");
                    return result;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استرجاع إعدادات النموذج: {ex.Message}");
                return defaultSettings ?? new T();
            }
        }

        /// <summary>
        /// حفظ إعدادات تسجيل الدخول دفعة واحدة (تلقائي محسن)
        /// </summary>
        public async Task SaveLoginSettingsBatchAsync(LoginSettings loginSettings)
        {
            if (loginSettings == null)
                return;

            // إنشاء نسخة للحفظ (بدون كلمة المرور الواضحة لأسباب أمنية)
            var settingsToSave = new Dictionary<string, object>
            {
                ["LastIpAddress"] = loginSettings.IpAddress ?? "",
                ["LastPort"] = loginSettings.Port,
                ["LastUsername"] = loginSettings.Username ?? "",
                ["RememberCredentials"] = loginSettings.RememberCredentials,
                ["SavePassword"] = loginSettings.SavePassword,
                ["PasswordBase64"] = loginSettings.PasswordBase64 ?? "",
                ["UseSSL"] = loginSettings.UseSSL,
                ["ConnectionTimeout"] = loginSettings.ConnectionTimeout,
                ["AutoLogin"] = loginSettings.AutoLogin,
                ["LastLoginTime"] = loginSettings.LastLoginTime,
                ["LoginAttempts"] = loginSettings.LoginAttempts,
                //["AutoRefreshInterval"] = loginSettings.AutoRefreshInterval
            };

            await SaveSettingsBatchAsync("Login", settingsToSave);
        }

        /// <summary>
        /// حفظ إعدادات تسجيل الدخول تلقائياً (باستخدام الدالة العامة)
        /// </summary>
        public async Task SaveLoginSettingsAutoAsync(LoginSettings loginSettings)
        {
            if (loginSettings == null)
                return;

            // استخدام الدالة العامة للحفظ التلقائي
            await SaveObjectBatchAsync("LoginAuto", loginSettings);
        }

        /// <summary>
        /// استرجاع إعدادات تسجيل الدخول دفعة واحدة (تلقائي)
        /// </summary>
        public async Task<LoginSettings> GetLoginSettingsBatchAsync()
        {
            // إنشاء كائن افتراضي للحصول على القيم الافتراضية
            var defaultLoginSettings = new LoginSettings();

            // تحويل الكائن الافتراضي إلى Dictionary
            var defaultSettingsDict = ObjectToDictionary(defaultLoginSettings, "Last");

            // إضافة المفاتيح المخصصة
            var defaultSettings = new Dictionary<string, object>
            {
                ["LastIpAddress"] = "***********",
                ["LastPort"] = 8728,
                ["LastUsername"] = "admin",
                ["RememberCredentials"] = false,
                ["SavePassword"] = false,
                ["PasswordBase64"] = "",
                ["UseSSL"] = false,
                ["ConnectionTimeout"] = 5000,
                ["AutoLogin"] = false,
                ["LastLoginTime"] = DateTime.MinValue
            };

            var settings = await GetSettingsBatchAsync("Login", defaultSettings);

            // إنشاء كائن LoginSettings جديد
            var loginSettings = new LoginSettings
            {
                IpAddress = settings["LastIpAddress"]?.ToString() ?? "***********",
                Port = Convert.ToInt32(settings["LastPort"]),
                Username = settings["LastUsername"]?.ToString() ?? "admin",
                RememberCredentials = Convert.ToBoolean(settings["RememberCredentials"]),
                SavePassword = Convert.ToBoolean(settings["SavePassword"]),
                PasswordBase64 = settings["PasswordBase64"]?.ToString() ?? "",
                UseSSL = Convert.ToBoolean(settings["UseSSL"]),
                ConnectionTimeout = Convert.ToInt32(settings["ConnectionTimeout"]),
                AutoLogin = Convert.ToBoolean(settings["AutoLogin"]),
                LastLoginTime = Convert.ToDateTime(settings["LastLoginTime"])
            };

            return loginSettings;
        }

        /// <summary>
        /// استرجاع إعدادات تسجيل الدخول تلقائياً (باستخدام الدالة العامة)
        /// </summary>
        public async Task<LoginSettings> GetLoginSettingsAutoAsync()
        {
            // استخدام الدالة العامة للاسترجاع التلقائي
            return await GetObjectBatchAsync<LoginSettings>("LoginAuto");
        }

        /// <summary>
        /// حفظ إعدادات الداش بورد تلقائياً (باستخدام الدالة العامة)
        /// </summary>
        public async Task SaveDashboardSettingsAutoAsync(DashboardSettings dashboardSettings)
        {
            if (dashboardSettings == null)
                return;

            // استخدام الدالة العامة للحفظ التلقائي
            await SaveObjectBatchAsync("DashboardAuto", dashboardSettings);
        }

        /// <summary>
        /// استرجاع إعدادات الداش بورد تلقائياً (باستخدام الدالة العامة)
        /// </summary>
        public async Task<DashboardSettings> GetDashboardSettingsAutoAsync()
        {
            // استخدام الدالة العامة للاسترجاع التلقائي
            return await GetObjectBatchAsync<DashboardSettings>("DashboardAuto");
        }

        /// <summary>
        /// حفظ أي كائن تلقائياً (عام)
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="category">فئة الإعدادات</param>
        /// <param name="obj">الكائن المراد حفظه</param>
        /// <param name="keyPrefix">بادئة المفاتيح (اختيارية)</param>
        public async Task SaveObjectBatchAsync<T>(string category, T obj, string keyPrefix = "") where T : class
        {
            if (obj == null)
                return;

            var settings = ObjectToDictionary(obj, keyPrefix);
            await SaveSettingsBatchAsync(category, settings);
        }

        /// <summary>
        /// استرجاع أي كائن تلقائياً (عام)
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="category">فئة الإعدادات</param>
        /// <param name="defaultObj">الكائن الافتراضي (اختياري)</param>
        /// <param name="keyPrefix">بادئة المفاتيح (اختيارية)</param>
        /// <returns>الكائن المسترجع</returns>
        public async Task<T> GetObjectBatchAsync<T>(string category, T defaultObj = null, string keyPrefix = "") where T : class, new()
        {
            // إنشاء كائن افتراضي إذا لم يتم توفيره
            if (defaultObj == null)
                defaultObj = new T();

            var defaultSettings = ObjectToDictionary(defaultObj, keyPrefix);
            var settings = await GetSettingsBatchAsync(category, defaultSettings);

            return DictionaryToObject<T>(settings, keyPrefix);
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// تحويل كائن إلى Dictionary تلقائياً باستخدام Reflection
        /// </summary>
        /// <typeparam name="T">نوع الكائن</typeparam>
        /// <param name="obj">الكائن المراد تحويله</param>
        /// <param name="prefix">بادئة للمفاتيح (اختيارية)</param>
        /// <returns>Dictionary يحتوي على خصائص الكائن</returns>
        private Dictionary<string, object> ObjectToDictionary<T>(T obj, string prefix = "") where T : class
        {
            var dictionary = new Dictionary<string, object>();

            if (obj == null)
                return dictionary;

            var type = typeof(T);
            var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            foreach (var property in properties)
            {
                try
                {
                    // تجاهل الخصائص التي لا يمكن قراءتها
                    if (!property.CanRead)
                        continue;

                    var value = property.GetValue(obj);
                    var key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}{property.Name}";

                    // معالجة القيم الفارغة
                    if (value == null)
                    {
                        dictionary[key] = GetDefaultValueForType(property.PropertyType);
                    }
                    else
                    {
                        dictionary[key] = value;
                    }
                }
                catch (Exception ex)
                {
                    // في حالة خطأ، استخدم القيمة الافتراضية
                    System.Diagnostics.Debug.WriteLine($"خطأ في قراءة خاصية {property.Name}: {ex.Message}");
                    dictionary[property.Name] = GetDefaultValueForType(property.PropertyType);
                }
            }

            return dictionary;
        }

        /// <summary>
        /// تحويل Dictionary إلى كائن تلقائياً باستخدام Reflection
        /// </summary>
        /// <typeparam name="T">نوع الكائن المراد إنشاؤه</typeparam>
        /// <param name="dictionary">القاموس المحتوي على البيانات</param>
        /// <param name="prefix">بادئة المفاتيح (اختيارية)</param>
        /// <returns>كائن من النوع المحدد</returns>
        private T DictionaryToObject<T>(Dictionary<string, object> dictionary, string prefix = "") where T : class, new()
        {
            var obj = new T();
            var type = typeof(T);
            var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);

            foreach (var property in properties)
            {
                try
                {
                    // تجاهل الخصائص التي لا يمكن كتابتها
                    if (!property.CanWrite)
                        continue;

                    var key = string.IsNullOrEmpty(prefix) ? property.Name : $"{prefix}{property.Name}";

                    if (dictionary.ContainsKey(key))
                    {
                        var value = dictionary[key];
                        var convertedValue = ConvertValueToType(value, property.PropertyType);
                        property.SetValue(obj, convertedValue);
                    }
                }
                catch (Exception ex)
                {
                    // في حالة خطأ، استخدم القيمة الافتراضية
                    System.Diagnostics.Debug.WriteLine($"خطأ في تعيين خاصية {property.Name}: {ex.Message}");
                }
            }

            return obj;
        }

        /// <summary>
        /// الحصول على القيمة الافتراضية لنوع معين
        /// </summary>
        /// <param name="type">النوع</param>
        /// <returns>القيمة الافتراضية</returns>
        private object GetDefaultValueForType(Type type)
        {
            if (type == null)
                return null;

            if (type.IsValueType)
                return Activator.CreateInstance(type);

            return null;
        }

        /// <summary>
        /// تحويل قيمة إلى نوع محدد
        /// </summary>
        /// <param name="value">القيمة</param>
        /// <param name="targetType">النوع المطلوب</param>
        /// <returns>القيمة المحولة</returns>
        private object ConvertValueToType(object value, Type targetType)
        {
            if (value == null)
                return GetDefaultValueForType(targetType);

            if (targetType.IsAssignableFrom(value.GetType()))
                return value;

            try
            {
                // التعامل مع Nullable types
                if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    targetType = Nullable.GetUnderlyingType(targetType);
                }

                // تحويل النوع
                return Convert.ChangeType(value, targetType);
            }
            catch
            {
                return GetDefaultValueForType(targetType);
            }
        }

        /// <summary>
        /// الحصول على اسم نوع البيانات
        /// </summary>
        private string GetDataTypeName(Type type)
        {
            if (type == null)
                return "String";

            if (type == typeof(string))
                return "String";
            else if (type == typeof(int) || type == typeof(int?))
                return "Integer";
            else if (type == typeof(bool) || type == typeof(bool?))
                return "Boolean";
            else if (type == typeof(DateTime) || type == typeof(DateTime?))
                return "DateTime";
            else if (type == typeof(decimal) || type == typeof(decimal?) ||
                     type == typeof(double) || type == typeof(double?) ||
                     type == typeof(float) || type == typeof(float?))
                return "Decimal";
            else if (type == typeof(long) || type == typeof(long?))
                return "Long";
            else if (type.IsEnum)
                return "Enum";
            else if (type.IsArray || type.IsGenericType)
                return "Array";
            else
                return "Object";
        }

        /// <summary>
        /// تحويل القيمة إلى النوع المطلوب
        /// </summary>
        private T ConvertValue<T>(string value, T defaultValue = default(T))
        {
            if (string.IsNullOrEmpty(value))
                return defaultValue;

            try
            {
                var targetType = typeof(T);

                // التعامل مع Nullable types
                if (targetType.IsGenericType && targetType.GetGenericTypeDefinition() == typeof(Nullable<>))
                {
                    targetType = Nullable.GetUnderlyingType(targetType);
                }

                // تحويل حسب النوع
                if (targetType == typeof(string))
                    return (T)(object)value;
                else if (targetType == typeof(int))
                    return (T)(object)int.Parse(value);
                else if (targetType == typeof(bool))
                    return (T)(object)bool.Parse(value);
                else if (targetType == typeof(DateTime))
                    return (T)(object)DateTime.Parse(value);
                else if (targetType == typeof(decimal))
                    return (T)(object)decimal.Parse(value);
                else if (targetType == typeof(double))
                    return (T)(object)double.Parse(value);
                else if (targetType == typeof(float))
                    return (T)(object)float.Parse(value);
                else if (targetType == typeof(long))
                    return (T)(object)long.Parse(value);
                else if (targetType.IsEnum)
                    return (T)Enum.Parse(targetType, value);
                else
                {
                    // للكائنات المعقدة، استخدم JSON
                    return JsonConvert.DeserializeObject<T>(value);
                }
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// التحقق من صحة اسم المجموعة والمفتاح
        /// </summary>
        private bool IsValidCategoryAndKey(string category, string key)
        {
            return !string.IsNullOrWhiteSpace(category) &&
                   !string.IsNullOrWhiteSpace(key) &&
                   category.Length <= 100 &&
                   key.Length <= 100;
        }

        #endregion

        public void Dispose()
        {
            // لا حاجة لتنظيف موارد مع Dapper - الاتصالات تُدار تلقائياً
        }
    }
}
