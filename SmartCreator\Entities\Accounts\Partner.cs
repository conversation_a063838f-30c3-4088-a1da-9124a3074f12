﻿using CefSharp.DevTools.CSS;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.Accounts
{
    public class Partner
    {
        //public Partner() { }
        //public Partner(int id) { }
        [System.ComponentModel.DataAnnotations.Required, PrimaryKey]
        public int Id { get; set; }
        [DisplayName("التسلسل")]
        public string Code { get; set; }
        [DisplayName("الاسم")]
        public string Name { get; set; }
        [DisplayName("الاسم كامل")]
        public string Display_name { get; set; }
       
        [DisplayName("الوصف")]
        public string Description { get; set; }
        [DisplayName("الصوره")]
        public byte[] Image { get; set; }
        [DisplayName("العنوان")]
        public string Address { get; set; }
        [DisplayName("الهاتف")]
        public string Phone { get; set; }
        [DisplayName("الايميل")]
        public string Email { get; set; }
        [DisplayName("اسم العملة")]
        public string Currency_name { get; set; }
        [DisplayName("رمز العملة")]
        public string Currency_symbol { get; set; }
        [DisplayName("معامل التقريب")]
        public string Currency_rounding { get; set; }
        [DisplayName("عدد الارقام العشرية")]
        public string Currency_decimal_places { get; set; }

        public DateTime Created { get; set; } = DateTime.Now;
        public DateTime? Updated { get; set; } = null;
        public int Active { get; set; } = 1;
        [DisplayName("نشط")]

        public bool Str_Active
        {
            get
            {
                return Convert.ToBoolean(Active);
            }
        }
        public int Partner_type { get; set; } = 2;
        [NotMapped]
        public string Str_Partner_type {

            get
            {
               string txt= PartnerTypeChoices.Where(p => p.Value == Partner_type.ToString()).First().Text;
               return txt;
            }
        } 

        [NotMapped]
        public static readonly List<SelectListItem> PartnerTypeChoices = new List<SelectListItem>
        {
            new SelectListItem { Value = "1", Text = "خزينة / طريقة دفع" },
            new SelectListItem { Value = "2", Text = "عميل" },
            new SelectListItem { Value = "5", Text = "مورد" },
            new SelectListItem { Value = "6", Text = "نقطة بيع" },
            new SelectListItem { Value = "9", Text = "اخرى" }
        };
        public string Rb {  get; set; } 
    }
}
