﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Entities;

namespace SmartCreator.TestAndDemo
{
    public partial class FormUserProfile : RJForms.RJChildForm
    {
        public FormUserProfile()
        {
            
            InitializeComponent();
        }
        public FormUserProfile(User user)
        {
            
            InitializeComponent();
            if (user == null) 
                return;
            txtUsername.Text = user.Username;
            txtFirstName.Text = user.FirstName;
            txtLastName.Text = user.LastName;
            txtEmail.Text = user.Email;
            txtPhoneNumber.Text = user.Phone;
            //dpBirthdate.Value = user.Birthdate;
            //pbPhoto.Image = user.ProfileImagePath;
            
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
          
        }

        private void btnAddPhoto_Click(object sender, EventArgs e)
        {

        }

        private void btnDeletePhoto_Click(object sender, EventArgs e)
        {

        }
    }
}
