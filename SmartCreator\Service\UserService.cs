﻿using Dapper;
using SmartCreator.Data;
using SmartCreator.Entities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Service
{
    /// <summary>
    /// خدمة إدارة المستخدمين
    /// </summary>
    public class UserService
    {
        private readonly DatabaseHelper _dbHelper;
        private readonly AuditService _auditService;

        public UserService(DatabaseHelper dbHelper, AuditService auditService)
        {
            _dbHelper = dbHelper;
            _auditService = auditService;
        }

        /// <summary>
        /// جلب جميع المستخدمين
        /// </summary>
        public async Task<List<User>> GetAllUsersAsync(bool activeOnly = true)
        {
            //using var connection = _dbHelper.GetConnection();
            //var sql = activeOnly ?
            //    "SELECT * FROM Users WHERE IsActive = 1 ORDER BY FullName" :
            //    "SELECT * FROM Users ORDER BY FullName";

            //var users = await connection.QueryAsync<User>(sql);

            //// جلب الأدوار لكل مستخدم
            //foreach (var user in users)
            //{
            //    user.Roles = await GetUserRolesAsync(user.Id);
            //    user.Permissions = await GetUserPermissionsAsync(user.Id);
            //}

            //return users.ToList();
            return new List<User>();
        }

        /// <summary>
        /// جلب مستخدم بالمعرف
        /// </summary>
        public async Task<User?> GetUserByIdAsync(int id)
        {
            using var connection = _dbHelper.GetConnection();
            var user = await connection.QuerySingleOrDefaultAsync<User>(
                "SELECT * FROM Users WHERE Id = @Id", new { Id = id });

            //if (user != null)
            //{
            //    user.Roles = await GetUserRolesAsync(user.Id);
            //    user.Permissions = await GetUserPermissionsAsync(user.Id);
            //}

            return user;
        }

        /// <summary>
        /// جلب مستخدم باسم المستخدم
        /// </summary>
        public async Task<User?> GetUserByUsernameAsync(string username)
        {
            using var connection = _dbHelper.GetConnection();
            var user = await connection.QuerySingleOrDefaultAsync<User>(
                "SELECT * FROM Users WHERE Username = @Username", new { Username = username });

            //if (user != null)
            //{
            //    user.Roles = await GetUserRolesAsync(user.Id);
            //    user.Permissions = await GetUserPermissionsAsync(user.Id);
            //}

            return user;
        }

        /// <summary>
        /// جلب مستخدم بالبريد الإلكتروني
        /// </summary>
        public async Task<User?> GetUserByEmailAsync(string email)
        {
            using var connection = _dbHelper.GetConnection();
            var user = await connection.QuerySingleOrDefaultAsync<User>(
                "SELECT * FROM Users WHERE Email = @Email", new { Email = email });

            //if (user != null)
            //{
            //    user.Roles = await GetUserRolesAsync(user.Id);
            //    user.Permissions = await GetUserPermissionsAsync(user.Id);
            //}

            return user;
        }

        /// <summary>
        /// إضافة مستخدم جديد
        /// </summary>
        public async Task<int> AddUserAsync(User user, string password, int? createdBy = null)
        {
            // التحقق من عدم وجود اسم المستخدم أو البريد الإلكتروني
            if (await IsUsernameExistsAsync(user.Username))
                throw new InvalidOperationException("اسم المستخدم موجود مسبقاً");

            if (await IsEmailExistsAsync(user.Email))
                throw new InvalidOperationException("البريد الإلكتروني موجود مسبقاً");

            // تشفير كلمة المرور
            var (hash, salt) = HashPassword(password);
            user.PasswordHash = hash;
            user.Salt = salt;
            user.CreatedDate = DateTime.Now;
            user.CreatedBy = createdBy;
            user.UpdateFullName();

            using var connection = _dbHelper.GetConnection();
            var sql = @"
                INSERT INTO Users (Username, Email, PasswordHash, Salt, FirstName, LastName, FullName,
                                 Phone, Department, Position, IsActive, CreatedDate, CreatedBy,
                                 LastPasswordChangeDate, MustChangePassword)
                VALUES (@Username, @Email, @PasswordHash, @Salt, @FirstName, @LastName, @FullName,
                        @Phone, @Department, @Position, @IsActive, @CreatedDate, @CreatedBy,
                        @LastPasswordChangeDate, @MustChangePassword);
                SELECT last_insert_rowid();";

            user.LastPasswordChangeDate = DateTime.Now;
            var userId = await connection.QuerySingleAsync<int>(sql, user);

            // تسجيل الحدث
            await _auditService.LogAsync(AuditActions.Create, "Users", userId, user.Username,
                null, user, createdBy, "User");

            return userId;
        }

        /// <summary>
        /// تحديث مستخدم
        /// </summary>
        public async Task<bool> UpdateUserAsync(User user, int? modifiedBy = null)
        {
            var existingUser = await GetUserByIdAsync(user.Id);
            if (existingUser == null)
                return false;

            // التحقق من عدم تكرار اسم المستخدم أو البريد الإلكتروني
            if (existingUser.Username != user.Username && await IsUsernameExistsAsync(user.Username))
                throw new InvalidOperationException("اسم المستخدم موجود مسبقاً");

            if (existingUser.Email != user.Email && await IsEmailExistsAsync(user.Email))
                throw new InvalidOperationException("البريد الإلكتروني موجود مسبقاً");

            user.UpdatedDate = DateTime.Now;
            user.ModifiedBy = modifiedBy;
            user.UpdateFullName();

            using var connection = _dbHelper.GetConnection();
            var sql = @"
                UPDATE Users SET
                    Username = @Username, Email = @Email, FirstName = @FirstName, LastName = @LastName,
                    FullName = @FullName, Phone = @Phone, Department = @Department, Position = @Position,
                    IsActive = @IsActive, IsLocked = @IsLocked, ModifiedDate = @ModifiedDate,
                    ModifiedBy = @ModifiedBy, Notes = @Notes, ProfileImagePath = @ProfileImagePath
                WHERE Id = @Id";

            var parameters = new
            {
                user.Id,
                user.Username,
                user.Email,
                user.FirstName,
                user.LastName,
                user.FullName,
                user.Phone,
                user.Department,
                user.Position,
                user.IsActive,
                user.IsLocked,
                ModifiedDate = user.UpdatedDate,
                user.ModifiedBy,
                user.Notes,
                user.ProfileImagePath
            };

            var result = await connection.ExecuteAsync(sql, parameters);

            // تسجيل الحدث
            await _auditService.LogAsync(AuditActions.Update, "Users", user.Id, user.Username,
                existingUser, user, modifiedBy, "User");

            return result > 0;
        }

        /// <summary>
        /// حذف مستخدم (حذف منطقي)
        /// </summary>
        public async Task<bool> DeleteUserAsync(int id, int? deletedBy = null)
        {
            var user = await GetUserByIdAsync(id);
            if (user == null)
                return false;

            using var connection = _dbHelper.GetConnection();
            var sql = "UPDATE Users SET IsActive = 0, ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy WHERE Id = @Id";

            var result = await connection.ExecuteAsync(sql, new
            {
                Id = id,
                ModifiedDate = DateTime.Now,
                ModifiedBy = deletedBy
            });

            // تسجيل الحدث
            await _auditService.LogAsync(AuditActions.Delete, "Users", id, user.Username,
                user, null, deletedBy, "User");

            return result > 0;
        }

        /// <summary>
        /// تغيير كلمة المرور
        /// </summary>
        public async Task<bool> ChangePasswordAsync(int userId, string currentPassword, string newPassword, int? changedBy = null)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            // التحقق من كلمة المرور الحالية
            if (!VerifyPassword(currentPassword, user.PasswordHash, user.Salt))
                throw new UnauthorizedAccessException("كلمة المرور الحالية غير صحيحة");

            return await ResetPasswordAsync(userId, newPassword, changedBy);
        }

        /// <summary>
        /// إعادة تعيين كلمة المرور
        /// </summary>
        public async Task<bool> ResetPasswordAsync(int userId, string newPassword, int? resetBy = null)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            var (hash, salt) = HashPassword(newPassword);

            using var connection = _dbHelper.GetConnection();
            var sql = @"
                UPDATE Users SET
                    PasswordHash = @PasswordHash, Salt = @Salt,
                    LastPasswordChangeDate = @LastPasswordChangeDate,
                    MustChangePassword = 1, FailedLoginAttempts = 0, IsLocked = 0,
                    ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                WHERE Id = @Id";

            var result = await connection.ExecuteAsync(sql, new
            {
                Id = userId,
                PasswordHash = hash,
                Salt = salt,
                LastPasswordChangeDate = DateTime.Now,
                ModifiedDate = DateTime.Now,
                ModifiedBy = resetBy
            });

            // تسجيل الحدث
            await _auditService.LogAsync(AuditActions.PasswordReset, "Users", userId, user.Username,
                null, null, resetBy, "User");

            return result > 0;
        }

        /// <summary>
        /// قفل/إلغاء قفل المستخدم
        /// </summary>
        public async Task<bool> ToggleUserLockAsync(int userId, bool isLocked, int? modifiedBy = null)
        {
            var user = await GetUserByIdAsync(userId);
            if (user == null)
                return false;

            using var connection = _dbHelper.GetConnection();
            var sql = @"
                UPDATE Users SET
                    IsLocked = @IsLocked, FailedLoginAttempts = 0,
                    ModifiedDate = @ModifiedDate, ModifiedBy = @ModifiedBy
                WHERE Id = @Id";

            var result = await connection.ExecuteAsync(sql, new
            {
                Id = userId,
                IsLocked = isLocked,
                ModifiedDate = DateTime.Now,
                ModifiedBy = modifiedBy
            });

            // تسجيل الحدث
            var action = isLocked ? AuditActions.AccountLocked : AuditActions.AccountUnlocked;
            await _auditService.LogAsync(action, "Users", userId, user.Username,
                null, null, modifiedBy, "User");

            return result > 0;
        }

        ///// <summary>
        ///// جلب أدوار المستخدم
        ///// </summary>
        //public async Task<List<Role>> GetUserRolesAsync(int userId)
        //{
        //    using var connection = _dbHelper.GetConnection();
        //    var sql = @"
        //        SELECT r.* FROM Roles r
        //        INNER JOIN UserRoles ur ON r.Id = ur.RoleId
        //        WHERE ur.UserId = @UserId AND ur.IsActive = 1 AND r.IsActive = 1
        //        AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > @Now)
        //        ORDER BY r.Priority DESC, r.DisplayName";

        //    var roles = await connection.QueryAsync<Role>(sql, new { UserId = userId, Now = DateTime.Now });
        //    return roles.ToList();
        //}

        /// <summary>
        /// جلب صلاحيات المستخدم
        /// </summary>
        public async Task<List<string>> GetUserPermissionsAsync(int userId)
        {
            using var connection = _dbHelper.GetConnection();

            // التحقق من وجود جدول UserPermissions وعمود IsGranted
            var hasUserPermissionsTable = false;
            var hasIsGrantedColumn = false;

            try
            {
                var tableExists = await connection.QuerySingleAsync<int>(
                    "SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='UserPermissions'");
                hasUserPermissionsTable = tableExists > 0;

                if (hasUserPermissionsTable)
                {
                    var columnExists = await connection.QuerySingleAsync<int>(
                        "SELECT COUNT(*) FROM pragma_table_info('UserPermissions') WHERE name='IsGranted'");
                    hasIsGrantedColumn = columnExists > 0;
                }
            }
            catch
            {
                // في حالة الخطأ، استخدم الصلاحيات من الأدوار فقط
                hasUserPermissionsTable = false;
                hasIsGrantedColumn = false;
            }

            string sql;
            if (hasUserPermissionsTable && hasIsGrantedColumn)
            {
                // استخدام الاستعلام الكامل مع UserPermissions
                sql = @"
                    SELECT DISTINCT p.Name FROM Permissions p
                    INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
                    INNER JOIN UserRoles ur ON rp.RoleId = ur.RoleId
                    WHERE ur.UserId = @UserId AND ur.IsActive = 1 AND rp.IsActive = 1 AND p.IsActive = 1
                    AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > @Now)

                    UNION

                    SELECT p.Name FROM Permissions p
                    INNER JOIN UserPermissions up ON p.Id = up.PermissionId
                    WHERE up.UserId = @UserId AND up.IsActive = 1 AND p.IsActive = 1
                    AND up.IsGranted = 1 -- Grant
                    AND (up.ExpiryDate IS NULL OR up.ExpiryDate > @Now)

                    EXCEPT

                    SELECT p.Name FROM Permissions p
                    INNER JOIN UserPermissions up ON p.Id = up.PermissionId
                    WHERE up.UserId = @UserId AND up.IsActive = 1 AND p.IsActive = 1
                    AND up.IsGranted = 0 -- Deny
                    AND (up.ExpiryDate IS NULL OR up.ExpiryDate > @Now)";
            }
            else
            {
                // استخدام الصلاحيات من الأدوار فقط
                sql = @"
                    SELECT DISTINCT p.Name FROM Permissions p
                    INNER JOIN RolePermissions rp ON p.Id = rp.PermissionId
                    INNER JOIN UserRoles ur ON rp.RoleId = ur.RoleId
                    WHERE ur.UserId = @UserId AND ur.IsActive = 1 AND rp.IsActive = 1 AND p.IsActive = 1
                    AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > @Now)";
            }

            var permissions = await connection.QueryAsync<string>(sql, new { UserId = userId, Now = DateTime.Now });
            return permissions.ToList();
        }

        /// <summary>
        /// التحقق من وجود اسم المستخدم
        /// </summary>
        public async Task<bool> IsUsernameExistsAsync(string username)
        {
            using var connection = _dbHelper.GetConnection();
            var count = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Users WHERE Username = @Username", new { Username = username });
            return count > 0;
        }

        /// <summary>
        /// التحقق من وجود البريد الإلكتروني
        /// </summary>
        public async Task<bool> IsEmailExistsAsync(string email)
        {
            using var connection = _dbHelper.GetConnection();
            var count = await connection.QuerySingleAsync<int>(
                "SELECT COUNT(*) FROM Users WHERE Email = @Email", new { Email = email });
            return count > 0;
        }

        /// <summary>
        /// تشفير كلمة المرور
        /// </summary>
        private (string hash, string salt) HashPassword(string password)
        {
            using var rng = RandomNumberGenerator.Create();
            var saltBytes = new byte[32];
            rng.GetBytes(saltBytes);
            var salt = Convert.ToBase64String(saltBytes);

            using var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, 10000, HashAlgorithmName.SHA256);
            var hash = Convert.ToBase64String(pbkdf2.GetBytes(32));

            return (hash, salt);
        }

        /// <summary>
        /// التحقق من كلمة المرور
        /// </summary>
        private bool VerifyPassword(string password, string hash, string salt)
        {
            var saltBytes = Convert.FromBase64String(salt);
            using var pbkdf2 = new Rfc2898DeriveBytes(password, saltBytes, 10000, HashAlgorithmName.SHA256);
            var computedHash = Convert.ToBase64String(pbkdf2.GetBytes(32));
            return computedHash == hash;
        }

        /// <summary>
        /// جلب المستخدمين الذين لديهم صلاحية معينة
        /// </summary>
        public async Task<List<User>> GetUsersWithPermissionAsync(string permissionName)
        {
            using var connection = _dbHelper.GetConnection();
            var sql = @"
                SELECT DISTINCT u.*
                FROM Users u
                INNER JOIN UserRoles ur ON u.Id = ur.UserId
                INNER JOIN RolePermissions rp ON ur.RoleId = rp.RoleId
                INNER JOIN Permissions p ON rp.PermissionId = p.Id
                WHERE p.Name = @PermissionName
                AND ur.IsActive = 1
                AND rp.IsActive = 1
                AND p.IsActive = 1
                AND u.IsActive = 1
                AND (ur.ExpiryDate IS NULL OR ur.ExpiryDate > @Now)";

            var users = await connection.QueryAsync<User>(sql, new { PermissionName = permissionName, Now = DateTime.Now });
            return users.ToList();
        }

        /// <summary>
        /// جلب جميع المستخدمين (اسم مختصر للتوافق)
        /// </summary>
        public async Task<List<User>> GetAllAsync(bool activeOnly = true)
        {
            return await GetAllUsersAsync(activeOnly);
        }

        /// <summary>
        /// جلب مستخدم بالمعرف (اسم مختصر للتوافق)
        /// </summary>
        public async Task<User?> GetByIdAsync(int id)
        {
            return await GetUserByIdAsync(id);
        }
    }


}
