﻿using Renci.SshNet;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Entities;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using SmartCreator.Utils;
using System.Net;
using SmartCreator.SSHClient;
using System.Diagnostics;
using Dapper;
using SmartCreator.db;
using iTextSharp.text.pdf.qrcode;
using System.Windows.Markup;
using SmartCreator.Models.API;
using System.Globalization;
using Org.BouncyCastle.Asn1.X500;

namespace SmartCreator.ViewModels
{
    public class Fast_Load_From_Mikrotik
    {
        Sql_DataAccess Local_DA = null;
        Smart_DataAccess Smart_DA = null;
        public List<SellingPoint> sp = null;
        private DataTable dtusers = null;
        private DataTable dtpyment = null;
        private DataTable dtsession = null;

        public Fast_Load_From_Mikrotik()
        {
            Local_DA = new Sql_DataAccess();
            Smart_DA = new Smart_DataAccess();
            sp = Smart_DA.Get_SellingPoints();
        }


        public static string Fast_loadDB = "";
        public string ssh_last_state = "false";
        public string id_ssh = "";
        public int ssh_port = 22;
        public DataTable dt_service = null;

        [Obsolete]
        public bool check_port_mk_befor()
        {
            bool result = false;
            Mk_DataAccess DA2 = new Mk_DataAccess();
            dt_service = DA2.GetService();
            try
            {
                DataRow[] foundRows = dt_service.Select("[name] = " + "'ssh'");
                ssh_last_state = foundRows[0]["disabled"].ToString();
                ssh_port = Convert.ToInt32(foundRows[0]["port"].ToString());
                Global_Variable.Mk_Login_data.Mk_Port_ssh = ssh_port;
                id_ssh = foundRows[0]["id"].ToString();
                result = true;
            }
            catch { }

            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "false");
                result = true;
            }
            return result;
        }

        [Obsolete]
        public void rest_port_mk_after()
        {
            if (ssh_last_state == "true")
            {
                Mk_DataAccess enable = new Mk_DataAccess();
                string result2 = enable.enable_disable_port_ssh(id_ssh, "true");
            }
        }

        private void DownloadProgresBar(ulong uploaded)
        {
            // Update progress bar on foreground thread

            Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke(
                  (System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = (int)uploaded; });
        }



        [Obsolete]
        public bool Download_Sql_From_Mikrotik(string RB_File_Name = "user-manager/sqldb")
        {
            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();
            bool status = false;
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();

            if (Properties.Settings.Default.script_add!= utils.Base64Decode("L3N5c3RlbS9zY3JpcHQvYWRk"))
                return false;
           
            
            UserManager_Database_Info um = mk_DataAccess.Get_Info_UserManager_Database();
            if (um == null)
                return false;
            if (um.db_path == null)
                return false;

            //string localFileDB = Global_Variable.Mk_Router.mk_sn + "Smart.db";
            string localFileDB = "temp.db";
            //string localFileDB = "sqldb";
            //string localFileDB2 =  "logsqldb";

            RB_File_Name = um.db_path + "/sqldb";

            if (Global_Variable.Mk_resources.version >= 7)
                RB_File_Name = um.db_path + "/um5.sqlite";


            //RB_File_Name = localFileDB;
            //string RB_File_Name2 = um.db_path + "/logsqldb";

            check_port_mk_befor();
            string Downloadfile = utils.Get_Database_Directory() + "\\" + localFileDB;
            //string Downloadfile = Directory.GetCurrentDirectory() + "\\" + "db\\" + localFileDB;
            try
            {
                using (var sftpClient = new SftpClient(Global_Variable.Server_IP, ssh_port, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    // Connect to the SFTP server
                    sftpClient.Connect();
                    using (Stream file1 = System.IO.File.Create(Downloadfile))
                    {
                        // Calculate the file size
                        var fileSize = sftpClient.GetAttributes(RB_File_Name).Size;

                        // Initialize the progress variables
                        long downloadedBytes = 0;
                        int progress = 0;

                        // Define the progress callback delegate
                        Action<ulong> progressCallback = (downloaded) =>
                        {
                            downloadedBytes = (long)downloaded;

                            // Calculate the progress percentage
                            progress = (int)((downloadedBytes * 100) / fileSize);

                            // Track the progress bar
                            //TrackProgress(progress);
                            Global_Variable.Uc_StatusBar.rjProgressBar1.Invoke((System.Windows.Forms.MethodInvoker)delegate { Global_Variable.Uc_StatusBar.rjProgressBar1.Value = progress; });

                        };

                        // Download the file with progress reporting
                        sftpClient.DownloadFile(RB_File_Name, file1, progressCallback);
                    }
                    status = true;
                    Fast_loadDB = Downloadfile;
                    stopwatch.Stop();
                    string ss =
                       (
                        
                         
                         (stopwatch.Elapsed.Hours.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Hours.ToString() : stopwatch.Elapsed.Hours.ToString())+
                          " : " + (stopwatch.Elapsed.Minutes.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Minutes.ToString() : stopwatch.Elapsed.Minutes.ToString()) +
                         " : " + (stopwatch.Elapsed.Seconds.ToString().Length == 1 ? "0" + stopwatch.Elapsed.Seconds.ToString() : stopwatch.Elapsed.Seconds.ToString()) 
                       );
                    Global_Variable.Update_Um_StatusBar(true, false, 0, "(" + ss + ") اجمالي مدة جلب بيانات الروتر", "");

                    //Thread.Sleep(7000);
                }
            }
            catch (Exception ex) { /*RJMessageBox.Show(ex.Message);*/ }
            rest_port_mk_after();

            //stopwatch.Stop();
            //Global_Variable.Update_Um_StatusBar(true, false, 0, "( " + stopwatch.Elapsed.TotalSeconds + " )", "");

            //if (status)
            //{
            //    Syn_UmUser_From_FastDB();
            //    Syn_Pyments_From_FastDB();
            //    Syn_Session_From_FastDB();
            //    Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر بالطريقه الحديثه الثانية", 0);

            //}
            if (status)
            {
                //return  CheckDownload2(Downloadfile);

                //Fast_loadDB = "db\\sqldb";
                Fast_loadDB = $"{utils.Get_Database_Directory()}\\temp.db";
                //Fast_loadDB = "db\\temp.db";
                //status = true;
                bool repir = runRepire();

                 //repir= false;

                if (repir == false)
                {
                    Fast_loadDB = $"{utils.Get_Database_Directory()}\\temp2.db";
                    //Fast_loadDB = "db\\temp2.db";
                    //return CheckDownload2(Downloadfile);
                    bool check = CheckDownload2(Downloadfile);
                    if (check)
                    {
                        Remove_Indexs();
                        return true;
                    }

                    return check;
                }
                else
                {
                    Remove_Indexs();
                }
                return repir;
                //Fast_loadDB = "db\\temp.db";

            }

            return status;
        }

        [Obsolete]
        private bool CheckDownload(string paht)
        {
            bool status = false;
            try
            {
                string bat = ".\\sqlite3.exe .\\sqldb \".recover\" |  .\\sqlite3.exe temp.db";
                System.Diagnostics.Process.Start(@"db\sql.bat");

                String command = $"{AppDomain.CurrentDomain.BaseDirectory} db\\sql.bat";

                //System.Diagnostics.ProcessStartInfo ProcessInfo = new ProcessStartInfo("cmd.exe", "/c " + command);
                //// ProcessInfo.CreateNoWindow = true;
                //ProcessInfo.UseShellExecute = true;

                //System.Diagnostics.Process.Start(ProcessInfo);
                //status = true;

                Process process = new Process();
                int exitcode;
                ProcessStartInfo processInfo = new ProcessStartInfo("cmd.exe")
                {
                    CreateNoWindow = false,
                    UseShellExecute = true,
                    WorkingDirectory = @"C:\Windows\System32",
                    RedirectStandardError = false,
                    RedirectStandardOutput = false,
                    RedirectStandardInput = false,
                    //Verb = "runas",
                    //Arguments = @"/run /s server_name e:\batname.bat"
                    Arguments = "/C " + AppDomain.CurrentDomain.BaseDirectory + @"db\sql.bat"
                };

                //Console.WriteLine("Starting scheduled task");
                process = Process.Start(processInfo);
                Console.ReadKey();
                process.WaitForExit();

                //ExecuteCommand(bat);
                //ExecuteScript(bat);
                status = true;
            }
            catch { }
            return status;
        }

        private bool CheckDownload2(string paht)
        {
            bool status = false;
            Global_Variable.Update_Um_StatusBar_Prograss("تم جلب البيانات من الروتر - يتم الان اعداد المزامنه", 0);

            try
            {
                try
                {
                    //System.IO.File.Delete("db\\temp.db");
                    System.IO.File.Delete($"{utils.Get_Database_Directory()}\\temp2.db");
                    //System.IO.File.Delete("db\\temp2.db");
                }
                catch { }
                string filename = "cmd";

                //runRepire();
                //return true;
                //string batchFilePath = @"d:\powershellscript.bat";
                string batchFilePath = @"sql.bat";
                createFileBat();

                //string batchFilePath = $"{AppDomain.CurrentDomain.BaseDirectory} db\\sql.bat";


                //System.Diagnostics.Process.Start(batchFilePath);
                //return true;
                string argument = "/c\" " + batchFilePath + "\" ";

                ProcessStartInfo start = new ProcessStartInfo();
                start.FileName = filename;
                start.Arguments = argument;
                start.UseShellExecute = false;
                start.RedirectStandardOutput = true;
                start.CreateNoWindow = true;


                using (Process process = Process.Start(start))
                {
                    process.WaitForExit();

                    using (StreamReader reader = process.StandardOutput)
                    {
                        string result = reader.ReadToEnd();
                        Fast_loadDB = $"{utils.Get_Database_Directory()}\\temp2.db";
                        //Fast_loadDB = "db\\temp2.db";
                        //Fast_loadDB = "db\\sqldb";
                        status = true;
                    }
                }



            }
            catch { }
            return status;
        }
        private void createFileBat()
        {
            try
            {
                // Check if file already exists. If yes, delete it.
                if (File.Exists(@"sql.bat"))
                {
                    File.Delete(@"sql.bat");
                }

                // Create a new file
                using (FileStream fs = File.Create(@"sql.bat"))
                {
                    // Add some text to file
                    //Byte[] title = new UTF8Encoding(true).GetBytes(".\\sqlite3.exe .\\db\\temp.db \".recover\"   | .\\db\\sqlite3.exe .\\db\\temp2.db");
                    Byte[] title = new UTF8Encoding(true).GetBytes(".\\sqlite3.exe ..\\dbs\\temp.db \".recover\"   | .\\sqlite3.exe ..\\dbs\\temp2.db");
                    //Byte[] title = new UTF8Encoding(true).GetBytes("..\\sqlite3.exe .\\dbs\\temp.db \".recover\"   | ..\\db\\sqlite3.exe ..\\db\\temp2.db");
                    fs.Write(title, 0, title.Length);
                }
            }
            catch (Exception Ex)
            {
                //Console.WriteLine(Ex.ToString());
            }
        }
        void ExecuteScript(string script)

        {

            //System.IO.File.Delete("db\\job.bat");
            System.IO.File.Delete("db\\temp.db");

            //System.IO.File.WriteAllText("db\\job.bat", script,Encoding.UTF8);

            var startinfo = new ProcessStartInfo(@"cmd.exe")

            {

                CreateNoWindow = false,

                UseShellExecute = false,

                RedirectStandardOutput = true,

                RedirectStandardError = true,

                Arguments = "/C " + AppDomain.CurrentDomain.BaseDirectory + @"db\sql.bat"

                //Arguments = "/C " + AppDomain.CurrentDomain.BaseDirectory + @"\job.bat"

            };

            var process = new Process { StartInfo = startinfo };

            process.Start();

            var reader = process.StandardOutput;

            while (!reader.EndOfStream)

            {

                var nextLine = reader.ReadLine();

                if (!string.IsNullOrEmpty(nextLine))

                {

                    //lb.Items.Insert(0, "Console> " + nextLine);

                    Application.DoEvents();

                }

            }

            process.WaitForExit();

        }
        void ExecuteCommand(string command)
        {
            System.IO.File.Delete("db\\temp.db");

            int exitCode;
            ProcessStartInfo processInfo;
            Process process;

            processInfo = new ProcessStartInfo("cmd.exe", "/c " + command);
            processInfo.CreateNoWindow = true;
            processInfo.UseShellExecute = false;
            // *** Redirect the output ***
            processInfo.RedirectStandardError = true;
            processInfo.RedirectStandardOutput = true;

            process = Process.Start(processInfo);
            process.WaitForExit();

            // *** Read the streams ***
            // Warning: This approach can lead to deadlocks, see Edit #2
            string output = process.StandardOutput.ReadToEnd();
            string error = process.StandardError.ReadToEnd();

            exitCode = process.ExitCode;

            Console.WriteLine("output>>" + (String.IsNullOrEmpty(output) ? "(none)" : output));
            Console.WriteLine("error>>" + (String.IsNullOrEmpty(error) ? "(none)" : error));
            Console.WriteLine("ExitCode: " + exitCode.ToString(), "ExecuteCommand");
            process.Close();
        }
        [Obsolete]
        public bool downDb(string RB_File_Name = "user-manager/sqldb")
        {
            bool status = false;

            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            UserManager_Database_Info um = mk_DataAccess.Get_Info_UserManager_Database();

            if (um == null)
                return false;

            string localFileDB = Global_Variable.Mk_Router.mk_sn + "Smart.db";
            RB_File_Name = um.db_path + "/sqldb";
            check_port_mk_befor();
            string Downloadfile = Directory.GetCurrentDirectory() + "\\" + "db\\" + localFileDB;

            //string host = "remote_host";
            //string username = "user";
            //string password = "password";
            string dbPath = RB_File_Name;
            //string dbPath = "/path/to/database.sqlite";
            string backupPath = Downloadfile;
            //string backupPath = "/path/to/backup_database.sqlite";


            try
            {
                using (var sshClient = new SshClient(Global_Variable.Server_IP, ssh_port, Global_Variable.Server_Username, Global_Variable.Server_Password))

                //using (var sshClient = new SshClient(Global_Variable.Server_IP, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    sshClient.Connect();
                    //RJMessageBox.Show("تم الاتصال بالجهاز البعيد عبر SSH.");

                    // تنفيذ أمر النسخ
                    //string command = $"sqlite3.exe {dbPath} '.backup {backupPath}'";
                    //string command = $"sqlite3 {dbPath} '.backup {dbPath}1'";
                    //string command = $"sqlite3 {dbPath}";
                    string command = $"/tool user-manager customer print ;";
                    var result = sshClient.RunCommand(command);

                    if (string.IsNullOrEmpty(result.Error))
                    {
                        RJMessageBox.Show("تم إنشاء النسخة الاحتياطية بنجاح.");
                    }
                    else
                    {
                        RJMessageBox.Show("حدث خطأ: " + result.Error);
                    }

                    sshClient.Disconnect();
                }
            }
            catch (Exception ex)
            {
                RJMessageBox.Show("حدث خطأ أثناء النسخ: " + ex.Message);
            }


            return status;
        }

        double filesizedownloaded = 0, filesize = 0;



        [Obsolete]
        private bool FtpDownloadFile()
        {
            Global_Variable.Update_Um_StatusBar(false, false, 0, "( " + "0" + " )", "");

            Stopwatch stopwatch = new Stopwatch();
            stopwatch.Start();

            bool status = false;

            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            UserManager_Database_Info um = mk_DataAccess.Get_Info_UserManager_Database();

            //string localFileDB = "LocalSmart.db";
            string localFileDB = Global_Variable.Mk_Router.mk_sn + "Smart.db";
            Fast_loadDB = localFileDB;

            string RB_File_Name = @"ftp://" + Global_Variable.Server_IP + "/";
            //RB_File_Name = um.db_path + "/sqldb";

            string Downloadfile = Directory.GetCurrentDirectory() + "\\" + "db\\" + localFileDB;
            try
            {
                ftp ftpClient = new ftp(RB_File_Name, Global_Variable.Server_Username, Global_Variable.Server_Password);
                //return ftpClient.Download(um.db_path + "/sqldb", Downloadfile,um.size);
                status = ftpClient.download(um.db_path + "/sqldb", Downloadfile, um.size);
                //return true;
            }
            catch { }

            stopwatch.Stop();
            int totalSeconds = (int)stopwatch.Elapsed.TotalSeconds;
            int seconds = totalSeconds % 60;
            int minutes = totalSeconds / 60;
            string time = minutes + ":" + seconds;
            //Thread.Sleep(7000);
            Global_Variable.Update_Um_StatusBar(true, false, 0, "اجمالي الوقت لتحميل البيانات ( " + time + " )", "");


            return status;






            int errorOccured = 0;
            while (errorOccured < 1)
            {
                FileStream outputStream = new FileStream(Downloadfile, FileMode.Create);
                FtpWebRequest reqFTP = (FtpWebRequest)FtpWebRequest.Create(new Uri(RB_File_Name));
                reqFTP.Credentials = new NetworkCredential(Global_Variable.Server_Username, Global_Variable.Server_Password);
                try
                {
                    reqFTP.Method = WebRequestMethods.Ftp.DownloadFile;
                    reqFTP.UseBinary = true;
                    FtpWebResponse response = (FtpWebResponse)reqFTP.GetResponse();
                    Stream ftpStream = response.GetResponseStream();
                    long cl = response.ContentLength;
                    int bufferSize = 2048;
                    int readCount;
                    byte[] buffer = new byte[bufferSize];
                    readCount = ftpStream.Read(buffer, 0, bufferSize);
                    while (readCount > 0)
                    {
                        outputStream.Write(buffer, 0, readCount);
                        readCount = ftpStream.Read(buffer, 0, bufferSize);
                    }
                    status = true;
                    ftpStream.Close();
                    outputStream.Close();
                    response.Close();
                    errorOccured++;
                }
                catch (Exception er)
                {
                    outputStream.Close();
                    RJMessageBox.Show(er.Message);
                }

            }
            return status;
        }
        public bool Add_Folder_Sessions_to_RB(string file_ScriptName)
        {
            bool add = false;
            string CurrentDirectory = Directory.GetCurrentDirectory();
            //string path_html = CurrentDirectory + "\\" + "test";
            string path_html = CurrentDirectory + "\\" + file_ScriptName;
            //Process.Start(path_html); 
            string uploadfile = path_html;
            string workingdirectory = "/";
            try
            {
                using (var client = new SftpClient(Global_Variable.Server_IP, Global_Variable.Mk_Login_data.Mk_Port_ssh, Global_Variable.Server_Username, Global_Variable.Server_Password))
                {
                    client.Connect();
                    client.ChangeDirectory(workingdirectory);
                    using (var fileStream = new FileStream(uploadfile, FileMode.Open))
                    {
                        client.BufferSize = 4 * 1024; // bypass Payload error large files
                        client.UploadFile(fileStream, Path.GetFileName(uploadfile));
                    }
                    add = true;
                }
            }
            catch { }
            return add;
        }

        private float Compute_Percent(UmPyment py)
        {
            //List<UmProfile> profile = Global_Variable.UM_Profile;
            UmProfile profile = Global_Variable.UM_Profile.Find(x => x.Name == py.ProfileName);
            if (profile == null) return py.TotalPrice;
            //else if(profile.Is_percentage)
            return 0;
        }

        public bool Syn_UmUser_From_FastDB()
        {

            //GetAll_Data_From_FastDB();
            //return;
            DataTable customer = null;
            bool status = false;
            try
            {
                dtusers = Get_Data_From_FastDB(@"SELECT * FROM user");
                if (dtusers == null)
                    return false;
                customer = Get_Data_From_FastDB(@"SELECT * FROM customer");

                //dtpyment = Get_Data_From_FastDB(@"SELECT payment.*,userprofile.profileId as profileId from payment JOIN userprofile on payment.userId=userprofile.userId");
                //dtsession = Get_Data_From_FastDB(@"SELECT * FROM session");
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }

            List<UmProfile> profile = Global_Variable.UM_Profile;

            Global_Variable.Source_Users_UserManager = new List<SourceCardsUserManager_fromMK>();
            SourceCardsUserManager_fromMK su = new SourceCardsUserManager_fromMK
            {
                userName = "",
                downloadUsed = "0",
            };
            Global_Variable.Source_Users_UserManager.Add(su);
           var umUser = (from user in dtusers.AsEnumerable()
                          select new UmUser
                          {
                              IdHX = "*" + user.Field<Int64>("id").ToString("X"),
                              SN = user.Field<Int64>("id"),
                              Sn_Name = user.Field<Int64>("id") + "-" + user.Field<string>("userName"),
                              CustomerName = (from v in customer.AsEnumerable() where v.Field<Int64>("id") == user.Field<Int64>("custId") select v.Field<string>("userName").ToString()).FirstOrDefault(),
                              UserName = user.Field<string>("userName"),
                              Password = user.Field<string>("password"),
                              Disabled = (int)user.Field<Int64>("disabled"),
                              FirstName = user.Field<string>("firstName"),
                              LastName = user.Field<string>("lastName"),
                              Comment = user.Field<string>("descr"),
                              Phone = user.Field<string>("phone"),
                              Location = user.Field<string>("location"),
                              Email = user.Field<string>("email"),
                              CallerMac = user.Field<string>("callerId"),
                              UptimeUsed = user.Field<Int64>("uptimeUsed"),
                              DownloadUsed = user.Field<Int64>("downloadUsed"),
                              UploadUsed = user.Field<Int64>("uploadUsed"),

                              //LastSeenAt= utils.String_UTC_ToDatetime_By_V_MK(user.Field<string>("lastSeenAt")),
                              //LastSeenAt= utils.String_UTC_ToDatetime_By_V_MK(user.Field<string>("lastSeenAt")),

                              LastSeenAt = user.Field<Int64>("lastSeenAt") != 0 ? utils.UnixTimeStampToDateTime(user.Field<Int64>("lastSeenAt")) : null,

                              SharedUsers = user.Field<Int64>("sharedUsers").ToString(),


                              SpCode = string.IsNullOrEmpty(user.Field<string>("location")) ? null : (from v in sp.AsEnumerable() where v.Code == user.Field<string>("location") select v.Code).FirstOrDefault(),
                              SpName = string.IsNullOrEmpty(user.Field<string>("location")) ? null : (from v in sp.AsEnumerable() where (v.Code == user.Field<string>("location")) select v.UserName).FirstOrDefault() ?? null,

                              DeleteFromServer = 0,
                              //=========================
                              ProfileName = user.Field<string>("actualProfileName"),
                              AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                              TransferLimit = (long)(string.IsNullOrEmpty(user.Field<string>("actualProfileName")) ? 0 : (from v in profile.AsEnumerable() where (v.Name == user.Field<string>("actualProfileName")) select v.TransferLimit).LastOrDefault()),
                              UptimeLimit = (long)(string.IsNullOrEmpty(user.Field<string>("actualProfileName")) ? 0 : (from v in profile.AsEnumerable() where (v.Name == user.Field<string>("actualProfileName")) select v.UptimeLimit).LastOrDefault()),
                              ValidityLimit = (long)(string.IsNullOrEmpty(user.Field<string>("actualProfileName")) ? 0 : (from v in profile.AsEnumerable() where (v.Name == user.Field<string>("actualProfileName")) select v.Validity).LastOrDefault()),

                              MkId = Global_Variable.Mk_resources.RB_SN,
                          }).ToList();


            if (umUser.Count == 0)
            {
                return false;
            }
            if (Local_DA.Add_UMUser_ToDB(umUser))
            {

            }

            int effectRows = Local_DA.Set_DeletFromServer_AsDisable<UmUser>("UmUser");
            Local_DA.Set_NotDeletFromServer("UmUser", umUser);
            List<UmUser> um_not_delet = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");


            // inner join  becaus  عشان نظمن ان كل عنصر معه الايدي حق قاعدة البيانات تبعه
            var um = (from u in umUser
                      join s in um_not_delet on u.Sn_Name equals s.Sn_Name
                      where u.Sn_Name == s.Sn_Name
                      select new UmUser
                      {
                          //Id = s.Id,
                          Sn_Name = u.Sn_Name,
                          Disabled = u.Disabled,
                          Password = u.Password,
                          CustomerName = u.CustomerName,
                          FirstName = u.FirstName,
                          LastName = u.LastName,
                          Comment = u.Comment,
                          Phone = u.Phone,
                          Location = u.Location,
                          Email = u.Email,
                          CallerMac = u.CallerMac,
                          UptimeUsed = u.UptimeUsed,
                          DownloadUsed = u.DownloadUsed,
                          UploadUsed = u.UploadUsed,
                          LastSeenAt = u.LastSeenAt,
                          ActiveSessions = u.ActiveSessions,
                          SharedUsers = u.SharedUsers,

                          SpCode = s.SpCode != null ? s.SpCode : u.SpCode,
                          //SpId = s.SpId != null ? s.SpId : u.SpId,
                          SpName = s.SpName != null ? s.SpName : u.SpName,

                          //Percentage = s.Percentage != 0 ? s.Percentage : s.Percentage,
                          //is_sPercentage = s.is_spPercentage != 0 ? s.is_spPercentage : s.is_spPercentage,

                          Status =
                                (((u.ProfileName == null || u.ProfileName == "") && (u.DownloadUsed) > 0) ? 2 ://"انتهى الرصيد" :
                                (((u.ProfileName != null && u.ProfileName != "") && (u.DownloadUsed) == 0) ? 0 :  //"انتظار"
                                (((u.ProfileName != null && u.ProfileName != "") && (u.DownloadUsed) > 0) ? 1 : // "نشط" :
                                (((u.ProfileName == null || u.ProfileName == "") && (u.DownloadUsed) == 0) ? 3 : 5 //"خطأ في الباقة" :
                                )))
                                ),
                          DeleteFromServer = 0,
                          //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                          //=========================
                          ProfileName = string.IsNullOrEmpty(u.ProfileName) ? s.ProfileName : u.ProfileName,
                          MkId = Global_Variable.Mk_resources.RB_SN,
                          //actualProfileName = u.actualProfileName,
                          //actualLimDownload=0,
                          //actualLimUpload = 0,
                          //actualLimTransfer = u.actualLimTransfer,
                          //actualLimUptime = u.actualLimUptime,
                          //profileTillTime = 0,
                          //profileTimeLeft = u.profileTimeLeft,
                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMUser_ToDB(um, false);
            //else if (umUser.Count > 0)
            //    Local_DA.Add_UMUser_ToDB(umUser, false, false);


            // if (um.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(um);
            //else if (umUser.Count > 0)
            //    sql_DataAccess.Update_UMUser_ToDB(umUser, false);
            return true;

        }
        public bool Syn_Pyments_From_FastDB()
        {
            //DataTable dtPyment = Get_Data_From_FastDB(@"SELECT payment.*,userprofile.profileId as profileId from payment JOIN userprofile on payment.userId=userprofile.userId");
            //DataTable dtPyment = Get_Data_From_FastDB(@"SELECT * FROM payment");

            List<UmProfile> profile = Global_Variable.UM_Profile;
            List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");

            dtpyment = Get_Data_From_FastDB(@"SELECT payment.*,userprofile.profileId as profileId from payment JOIN userprofile on payment.userId=userprofile.userId");

            if (dtpyment == null)
                return false;

            Global_Variable.Source_Pyment_UserManager = new List<SourcePymentUserManager_fromMK>();
            SourcePymentUserManager_fromMK su = new SourcePymentUserManager_fromMK
            {
                userName = "",
                idHX = "0",
            };
            Global_Variable.Source_Pyment_UserManager.Add(su);
            //Global_Variable.Source_Pyment_UserManager = SourcePymentUserManager_fromMK.get_Pyment_user()
            var PY_Users = (from pyment in dtpyment.AsEnumerable()
                            join umuser in sourceCardsUsers on pyment.Field<Int64>("userId") equals umuser.SN
                            //where umuser.UserName == pyment.Field<string>("userName")
                            select new UmPyment
                            {

                                IdHX = "*" + pyment.Field<Int64>("id").ToString("X"),
                                Sn = pyment.Field<Int64>("id"),
                                Sn_Name = pyment.Field<Int64>("id") + "-" + umuser.UserName,
                                UserName = umuser.UserName,
                                Price = pyment.Field<Int64>("price2") > 0 ? (pyment.Field<Int64>("price2") / 100) : pyment.Field<Int64>("price2"),
                                //TotalPrice = pyment.Field<Int64>("price2") > 0 ?(pyment.Field<Int64>("price2")/100): pyment.Field<Int64>("price2"),
                                //TotalPrice = Convert.ToInt32(pyment.Field<Int64>("price2")),
                                AddedDate = pyment.Field<Int64>("transStart") > 0 ? utils.UnixTimeStampToDateTime(pyment.Field<Int64>("transStart")) : null,
                                Fk_Sn_Name = umuser.Sn_Name,
                                //UmUserId = umuser.Id,
                                DeleteFromServer = 0,
                                ProfileName = (from v in profile.AsEnumerable() where (v.Sn == pyment.Field<Int64>("profileId") ) select v.Name).FirstOrDefault() ?? "",
                                //ProfileName = (from v in profile.AsEnumerable() where (v.Sn == pyment.Field<Int64>("profileId") && v.Rb == Global_Variable.Mk_resources.RB_code) select v.Name).FirstOrDefault() ?? "",
                                //ProfileName = (umuser.ProfileName != null || umuser.ProfileName != "" ? umuser.ProfileName : ((from v in profile where (v.Price2.ToString() == pyment.price2.ToString()) select v.Name).FirstOrDefault() ?? "")),
                                AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                MkId = Global_Variable.Mk_resources.RB_SN,

                            }).ToList();

            if (PY_Users.Count == 0)
                return false;

            if (Local_DA.Add_UMPyement_ToDB(PY_Users, true))
            {
                
            }

            int effectRows = Local_DA.Set_DeletFromServer_AsDisable<UmPyment>("UmPyment");
            Local_DA.Set_NotDeletFromServer("UmPyment", PY_Users);
            List<UmPyment> sourcePayment = Local_DA.Get_Not_Delet_fromServer<UmPyment>("UmPyment");


            //============ update pyement to not delete from server and id UserLocalDB ============

            var um = (from py in PY_Users
                      join s in sourcePayment on py.Sn_Name equals s.Sn_Name
                      where py.Sn_Name == s.Sn_Name
                      select new UmPyment
                      {
                          Sn_Name = s.Sn_Name,
                          //Fk_Sn_Name=s.Sn_Name,
                          //Id = s.Id,
                          //UmUserId = py.UmUserId,
                          UserName = py.UserName,
                          Price = py.Price,
                          TotalPrice = py.TotalPrice == 0 ? Get_TotalPrice(py.ProfileName, py.Price) : py.TotalPrice,
                          //TotalPrice = Get_TotalPrice(py.ProfileName,py.Price),
                          //TotalPrice = py.TotalPrice,
                          ProfileName = py.ProfileName,
                          DeleteFromServer = 0,
                          ProfileTransferLimit = ((long)((string.IsNullOrEmpty(py.ProfileName)) ? 0 : ((from v in profile where (py.ProfileName == v.Name) select v.TransferLimit).LastOrDefault()))),
                          ProfileUptimeLimit = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select v.UptimeLimit).LastOrDefault()) : 0)),
                          ProfileValidity = ((long)((py.ProfileName != null && py.ProfileName != "") ? ((from v in profile where (py.ProfileName == v.Name) select (v.Validity * 24 * 60 * 60)).LastOrDefault()) : 0)),
                          AddedDate = py.AddedDate,
                          //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                          //uptimeUsed=sp!=null ? sp.uptimeUsed
                          //downloadUsed
                          //uploadUsed

                      }).ToList();

            if (um.Count > 0)
                Local_DA.Add_UMPyement_ToDB(um, false);
            //else if (PY_Users.Count > 0)
            //    Local_DA.Add_UMPyement_ToDB(PY_Users, false, false);

            //====================  update users if  profie not set on table user and regDate ==============================
            var profile_groub = (from b in um
                                 group b by b.UserName into g
                                 select new
                                 {
                                     //id = g.First().Id,
                                     //Sn_Name = g.First().Sn_Name,
                                     userName = g.Key,
                                     moneyTotal = g.Sum(x => (x.TotalPrice)),
                                     price = g.Sum(x => (x.Price)),
                                     //price = g.Sum(x => (x.Price)),

                                     uptimeLimit = g.Sum(x => x.ProfileUptimeLimit),
                                     transferLimit = g.Sum(x => x.ProfileTransferLimit),
                                     ValidityLimit = g.Sum(x => x.ProfileValidity),

                                     profileName = g.Last().ProfileName,
                                     //actualLimTransfer = g.Last().ProfileTransferLimit,
                                     //actualLimUptime = g.Last().ProfileUptimeLimit,
                                     //profileValidity = g.Last().ProfileValidity,

                                     regDate = g.First().AddedDate,
                                     countProfile = g.Count()
                                 }).ToList();


            var Users_update = (from pg in profile_groub
                                join u in sourceCardsUsers on pg.userName equals u.UserName /*into uj*/
                                //from su in uj.DefaultIfEmpty()
                                select new UmUser
                                {
                                    //Id = u.Id,
                                    Sn_Name = u.Sn_Name,
                                    RegDate = (u.RegDate == null ? pg.regDate : u.RegDate),
                                    //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    //TotalPrice = u.TotalPrice == 0 || u.TotalPrice < pg.moneyTotal ? pg.moneyTotal : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                    //Price = u.Price == 0 || u.TotalPrice < pg.moneyTotal ? pg.moneyTotal : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها

                                    TotalPrice = u.TotalPrice,
                                    Price = u.TotalPrice, //


                                    //UptimeLimit = u.UptimeLimit <= pg.uptimeLimit ? pg.uptimeLimit : u.UptimeLimit,
                                    //TransferLimit = u.TransferLimit <= pg.transferLimit ? pg.transferLimit : u.TransferLimit,
                                    //ValidityLimit = u.ValidityLimit <= pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,

                                    UptimeLimit = u.UptimeLimit,
                                    TransferLimit = u.TransferLimit,
                                    ValidityLimit = u.ValidityLimit,
                                    ProfileValidity = u.ValidityLimit,


                                    ProfileName = pg.profileName,

                                    //actualLimTransfer = pg.actualLimTransfer,
                                    //actualLimUptime = pg.actualLimUptime,

                                    CountProfile = u.CountProfile == 0 || u.CountProfile < pg.countProfile ? pg.countProfile : u.CountProfile,
                                    ProfileTimeLeft = (pg.uptimeLimit - u.UptimeUsed),
                                    ProfileTransferLeft = (pg.transferLimit - (u.DownloadUsed + u.UploadUsed)),
                                    ProfileTillTime = u.ProfileTillTime != null ? (u.ProfileTillTime.Value.AddDays(pg.ValidityLimit)) : null,
                                    //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                }).ToList();

            //SqlDataAccess.Update_UM_user_to_LocalDB_AfterPymentGet(Users_update);
            Local_DA.Add_UMUser_ToDB(Users_update, false, true, true, false);

            return true;
        }
        private float Get_TotalPrice(string profile, float price)
        {
            if (price <= 0)
                return price;


            float totalPrice = price;
            try
            {
                UmProfile profle = Global_Variable.UM_Profile.Find(x => x.Name == profile);
                float percentage = 0;
                if (profle != null)
                {
                    if (profle.Is_percentage == 1)
                    {

                        percentage = profle.Percentage;
                        if (profle.PercentageType == 0)
                        {
                            float percentage_value = (price * percentage) / 100;
                            totalPrice = price - percentage_value;
                        }
                        else
                        {
                            totalPrice = price - percentage;
                        }
                    }
                }
            }
            catch { }
            return totalPrice;
        }

        public void Syn_Session_From_FastDB2(List<SourceSessionUserManager_fromMK> Ssession = null)
        {
            DataTable dtSession = Get_Data_From_FastDB(@"SELECT * FROM session");
            DataTable dtUser = Get_Data_From_FastDB(@"SELECT * FROM user");

            //List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");
            //if (Ssession == null)
            //{
            //Ssession = Global_Variable.Source_Session_UserManager;
            //by_search = false;
            //}
            //if (Ssession == null && sourceCardsUsers == null)
            //return;

            var sess_Users = (from session in dtSession.AsEnumerable()
                              join umuser in dtUser.AsEnumerable() on session.Field<Int64>("userId") equals umuser.Field<Int64>("id")
                              //where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")

                              //from us in user.DefaultIfEmpty()
                              select new UmSession
                              {
                                  IdHX = "*" + session.Field<Int64>("userId").ToString("X"),
                                  Sn = session.Field<Int64>("id"),
                                  //Sn = Int32.Parse(session.id.TrimStart(new char[] { '*' }), NumberStyles.HexNumber),
                                  UserName = umuser.Field<string>("userName"),
                                  Sn_Name = session.Field<Int64>("id") + "-" + umuser.Field<string>("userName"),
                                  NasPortId = session.Field<string>("nasPortId"),
                                  //CallingStationId = session.Field<string>("callingStationId"),
                                  CallingStationId = System.Text.Encoding.Default.GetString(session.Field<byte[]>("callingStationId")),

                                  //IpUser = session.Field<string>("ipUser"),
                                  //IpUser =           System.Text.Encoding.Default.GetString(session.Field<byte[]>("ipRouter")) ,
                                  IpUser = Encoding.UTF8.GetString(session.Field<byte[]>("ipRouter")),
                                  IpRouter = Encoding.UTF8.GetString(session.Field<byte[]>("ipUser")),

                                  //IpRouter = System.Text.Encoding.Default.GetString(session.Field<byte[]>("ipUser")),
                                  //IpRouter = session.Field<string>("ipRouter"),
                                  //Console.WriteLine(System.Text.Encoding.Default.GetString(DerivedClassObject.Entry));


                                  //Status = session.status,
                                  //status_str = session.status,
                                  Active = ((int)session.Field<Int64>("active")),
                                  //Active = session.active == null ? 0 : (session.active == "no" ? 0 : 1),
                                  FromTime = session.Field<Int64>("fromTime") > 0 ? utils.UnixTimeStampToDateTime(session.Field<Int64>("fromTime")) : null,
                                  TillTime = session.Field<Int64>("tillTime") > 0 ? utils.UnixTimeStampToDateTime(session.Field<Int64>("tillTime")) : null,
                                  UpTime = (session.Field<Int64>("upTime")),
                                  BytesDownload = session.Field<Int64>("bytesDownload"),
                                  BytesUpload = session.Field<Int64>("bytesUpload"),

                                  Fk_Sn_Name = umuser.Field<Int64>("id") + "-" + umuser.Field<string>("userName"),
                                  //Fk_Sn_Name = umuser.Sn_Name,
                                  //UmUserId = (int)umuser.Field<Int64>("id"),
                                  AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                  MkId = Global_Variable.Mk_resources.RB_SN,
                                  DeleteFromServer = 0
                              }).ToList();

            if (Local_DA.Add_UMSession_ToDB(sess_Users))
            {

            }

            //SqlDataAccess.Add_UM_Session_to_LocalDB(sess_Users);
            //============ update session to not delete from server  ============

            List<UmSession> sourceSession = Local_DA.Get_Not_Delet_fromServer<UmSession>("UmSession");

            Local_DA.Set_Delet_fromServer_As_disable<UmSession>("UmSession");

            var um = (from u in sess_Users
                      join s in sourceSession on u.Sn_Name equals s.Sn_Name
                      //where u.sn_userName == s.sn_userName
                      select new UmSession
                      {
                          FromTime = (s.FromTime),
                          TillTime = (s.TillTime),
                          UpTime = (s.UpTime),
                          BytesDownload = (s.BytesDownload),
                          BytesUpload = (s.BytesUpload),
                          IpUser = (s.IpUser),
                          //IpRouter = System.Text.Encoding.Default.GetString(session.Field<byte[]>("ipUser")),
                          IpRouter = (s.IpRouter),

                          DeleteFromServer = 0,
                          LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                          //=========================

                      }).ToList();
            if (um.Count > 0)
                Local_DA.Add_UMSession_ToDB(um, false);
            else if (sess_Users.Count > 0)
                Local_DA.Add_UMSession_ToDB(sess_Users, false, false);


            //sql_DataAccess.Add_UMSession_ToDB(um,false);

            //============== update user table first use ============
            List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");

            if (um.Count > 0)
            {
                var SessionLQ = (from r in sess_Users
                                 group r by r.UserName into g
                                 select g.OrderBy(r => r.FromTime).First()).ToList();

                var Users_update = (from ses in SessionLQ
                                    join u in sourceCardsUsers on ses.UserName equals u.UserName
                                    where u.FirsLogin == null
                                    select new UmUser
                                    {
                                        //Id = u.Id,
                                        Sn_Name = u.Sn_Name,
                                        FirsLogin = ses.FromTime,
                                        Radius = u.Radius != "" || u.Radius != null ? ses.IpRouter : u.Radius,
                                        NasPortId = u.NasPortId != "" || u.NasPortId != null ? ses.NasPortId : u.NasPortId,
                                        LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                    }).ToList();


                //SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                Local_DA.Add_UMUser_ToDB(Users_update, false, true, false, true);
                //============== update pyment table first use ============
            }
        }
        public bool Syn_Session_From_FastDB()
        {

            dtsession = Get_Data_From_FastDB(@"SELECT userId,id,nasPortId,active,fromTime,tillTime,bytesDownload,bytesUpload,upTime
                                            ,callingStationId
                                            , ipRouter
                                            , hex(ipRouter) as ipRouter1
                                            , quote(ipRouter)  as ipRouter2
                                            , ipUser 
                                            , hex(ipUser) as ipUser1 
                                            , quote(ipUser) as ipUser2 
                                            , cast(ipRouter as varchar) ipRouter4 
                                            FROM session");
            //dtsession = Get_Data_From_FastDB(@"SELECT * FROM session");
            if (dtsession == null)
                return false;


            Global_Variable.Source_Session_UserManager = new List<SourceSessionUserManager_fromMK>();
            SourceSessionUserManager_fromMK su = new SourceSessionUserManager_fromMK
            {
                userName = "",
                idHX = "0",
            };
            Global_Variable.Source_Session_UserManager.Add(su);

            List<UmUser> sourceCardsUsers = Local_DA.Get_Not_Delet_fromServer<UmUser>("UmUser");

            var sess_Users = (from session in dtsession.AsEnumerable()
                              join umuser in sourceCardsUsers on session.Field<Int64>("userId") equals umuser.SN
                              //where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")
                              //from us in user.DefaultIfEmpty()
                              select new UmSession
                              {

                                  IdHX = "*" + session.Field<Int64>("id").ToString("X"),
                                  Sn = session.Field<Int64>("id"),
                                  UserName = umuser.UserName,
                                  Sn_Name = session.Field<Int64>("id") + "-" + umuser.UserName,
                                  NasPortId = session.Field<string>("nasPortId").ToString(),
                                  CallingStationId = Encoding.Default.GetString(session.Field<byte[]>("callingStationId")),
                                  //IpUser = ByteArray2String(session.Field<byte[]>("IpUser")),

                                  //IpRouter = repl(session.Field<string>("IpRouter2").ToString()),



                                  //IpUser = Encoding.UTF8.GetString(session.Field<byte[]>("IpUser")),
                                  //IpRouter = hexToString(session.Field<string>("IpRouter2")),
                                  //IpRouter = ByteArray2String(session.Field<byte[]>("IpRouter")),
                                  //IpRouter = Encoding.UTF8.GetString(session.Field<byte[]>("IpRouter")),

                                  //IpUser = hexToString(session.Field<string>("IpUser")) ,
                                  //IpUser = ByteArray2String(session.Field<byte[]>("IpUser")) ,
                                  //IpUser =  Encoding.UTF8.GetString(Enumerable.Range(0, session.Field<string>("IpUser").Length / 2).Select(_ => Convert.ToByte(session.Field<string>("IpUser").Substring(_ * 2, 2), 16)).ToArray()),
                                  //IpUser = FromHexString(session.Field<string>("IpUser")),
                                  //IpUser = Encoding.Default.GetString(session.Field<byte[]>("IpUser"),0, session.Field<byte[]>("IpUser").Length-1),
                                  //IpUser = (session.Field<string>("ipRouter").ToString("X2")),
                                  //IpRouter = Encoding.Default.GetString(session.Field<byte[]>("IpRouter"),0, session.Field<byte[]>("IpRouter").Length-1),
                                  //IpRouter = Encoding.GetEncoding("UTF-16").GetString(session.Field<byte[]>("IpRouter")),
                                  //Status = session.status,
                                  //status_str = session.status,

                                  Active = ((int)session.Field<Int64>("active")),
                                  FromTime = session.Field<Int64>("fromTime") > 0 ? utils.UnixTimeStampToDateTime(session.Field<Int64>("fromTime")) : null,
                                  TillTime = session.Field<Int64>("tillTime") > 0 ? utils.UnixTimeStampToDateTime(session.Field<Int64>("tillTime")) : null,
                                  UpTime = (session.Field<Int64>("upTime")),
                                  BytesDownload = session.Field<Int64>("bytesDownload"),
                                  BytesUpload = session.Field<Int64>("bytesUpload"),
                                  Fk_Sn_Name = umuser.Sn_Name,
                                  //UmUserId = umuser.Id,
                                  AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                  MkId = Global_Variable.Mk_resources.RB_SN,
                                  DeleteFromServer = 0
                              }).ToList();

            if (sess_Users.Count == 0)
                return false;
            if (Local_DA.Add_UMSession_ToDB(sess_Users))
            {

            }
            //Local_DA.Set_NotDeletFromServer("UmSession", sess_Users);

            //SqlDataAccess.Add_UM_Session_to_LocalDB(sess_Users);
            //============ update session to not delete from server  ============

            //List<UmSession> sourceSession = Local_DA.Get_Not_Delet_fromServer<UmSession>("UmSession");

            int effectCount = Local_DA.Set_DeletFromServer_AsDisable<UmSession>("UmSession");
            //var um = (from u in sess_Users
            //          select new UmSession
            //          {
            //              Id = u.Id,

            //              FromTime = (u.FromTime),
            //              TillTime = (u.TillTime),
            //              UpTime = (u.UpTime),
            //              BytesDownload = (u.BytesDownload),
            //              BytesUpload = (u.BytesUpload),
            //              IpUser = u.IpUser,
            //              IpRouter = u.IpRouter,
            //              DeleteFromServer = 0,
            //              LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

            //              //=========================

            //          }).ToList();
            //var um = (from u in sess_Users
            //           join s in sourceSession on u.Sn_Name equals s.Sn_Name
            //           //where u.sn_userName == s.sn_userName
            //           select new UmSession
            //           {
            //               Id = s.Id,
            //               FromTime = (s.FromTime),
            //               TillTime =(s.TillTime),
            //               UpTime = (s.UpTime),
            //               BytesDownload = (s.BytesDownload),
            //               BytesUpload = (s.BytesUpload),
            //               IpUser=s.IpUser,
            //               IpRouter=s.IpRouter,
            //               DeleteFromServer = 0,
            //               LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

            //               //=========================

            //           }).ToList();
            //if (um.Count > 0)
            //    Local_DA.Add_UMSession_ToDB(um, false);
            //else if (sess_Users.Count > 0)
            Local_DA.Add_UMSession_ToDB(sess_Users, false, false);


            //sql_DataAccess.Add_UMSession_ToDB(um,false);
            List<UmSession> sourceSession = Local_DA.Get_Not_Delet_fromServer<UmSession>("UmSession");
            //============== update user table first use ============
            if (sourceSession.Count > 0)
            {
                //var SessionLQ = (from r in sourceSession
                //                 group r by r.UserName into g
                //                 select g.OrderBy(r => r.FromTime).First()).ToList();

                //var Users_update = (from ses in SessionLQ
                //                    join u in sourceCardsUsers on ses.Fk_Sn_Name equals u.Sn_Name
                //                    where u.FirsLogin == null
                //                    select new UmUser
                //                    {
                //                        Sn_Name = u.Sn_Name,
                //                        FirsLogin = ses.FromTime,
                //                        Radius = string.IsNullOrEmpty(u.Radius) ? ses.IpRouter : u.Radius,
                //                        NasPortId = u.NasPortId != "" || u.NasPortId != null ? ses.NasPortId : u.NasPortId,
                //                        //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                //                    }).ToList();
                var SessionLQ = (from r in sess_Users
                                 group r by r.UserName into g
                                 select new
                                 {
                                     UserName = g.Key,
                                     Sn_Name = g.First().Sn_Name,
                                     FromTime = g.OrderBy(x => x.FromTime).First().FromTime,
                                     UptimeUsed = g.Sum(a => a.UpTime),
                                     DownloadUsed = g.Sum(r => r.BytesDownload),
                                     UploadUsed = g.Sum(r => r.BytesUpload),
                                     Radius = g.First().IpRouter,
                                     NasPortId = g.First().NasPortId,
                                     CountSession = g.Count(),
                                 });

                var Users_update = (from ses in SessionLQ
                                    join u in sourceCardsUsers on ses.UserName equals u.UserName
                                    //where u.FirsLogin == null
                                    select new UmUser
                                    {
                                        UserName = u.UserName,
                                        Sn_Name = u.Sn_Name,

                                        FirsLogin = u.FirsLogin == null ? ses.FromTime : u.FirsLogin,
                                        Radius = string.IsNullOrEmpty(u.Radius) ? ses.Radius : u.Radius,
                                        NasPortId = string.IsNullOrEmpty(u.NasPortId) ? ses.NasPortId : u.NasPortId,
                                        CountSession = ses.CountSession > u.CountSession ? ses.CountSession : u.CountSession,
                                    }).ToList();

                //SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                Local_DA.Add_UMUser_ToDB(Users_update, false, true, false, true);
                //============== update pyment table first use ============
            }
            return true;
        }
        string repl(string um)
        {
            string result = string.Concat(Enumerable
                            .Range(0, um.Length / 2)
                            .Select(i => (char)int.Parse(um.Substring(2 * i, 2), NumberStyles.HexNumber)));

            return um;
        }
        string repl2(byte[] bytes)
        {
            return "um";
        }
        public string FromHexString(string hexString)
        {
            var bytes = new byte[hexString.Length / 2];
            for (var i = 0; i < bytes.Length; i++)
            {
                bytes[i] = Convert.ToByte(hexString.Substring(i * 2, 2), 16);
            }
            string str = Encoding.Unicode.GetString(bytes);
            return str; // returns: "Hello world" for "48656C6C6F20776F726C64"
        }
        private string ByteArray2String(byte[] bytes)
        {

            // From byte array to string
            string sss = System.Text.Encoding.UTF8.GetString(bytes, 0, bytes.Length);

            char[] chars = new char[bytes.Length / sizeof(char)];
            System.Buffer.BlockCopy(bytes, 0, chars, 0, bytes.Length);
            string str = new string(chars);

            var table = (Encoding.Default.GetString(
                 bytes,
                 0,
                 bytes.Length - 1)).Split(new string[] { "\r\n", "\r", "\n" },
                                             StringSplitOptions.None);


            //string result22 = Encoding.Unicode.GetString(bytes.AsSpan(2));
            string CallingStationId = Encoding.Default.GetString(bytes);
            string someString = Encoding.ASCII.GetString(bytes);
            string result = Encoding.Unicode.GetString(bytes);
            string result2 = Encoding.ASCII.GetString(bytes);

            var value = Encoding.GetEncoding("UTF-16").GetString(bytes);
            var value2 = Encoding.GetEncoding("UTF-8").GetString(bytes);

            //char[] chars = new char[bytes.Length/2 ];
            //char[] chars = new char[bytes.Length / 2];

            for (int i = 0; i < chars.Length; i++)
                chars[i] = BitConverter.ToChar(bytes, i * 2);
            string d = new string(chars);
            return d;
        }
        private string hexToString(String hexString)
        {

            try
            {


                //DECLARE A VARIABLE TO RETURN
                string ascii = string.Empty;

                //SPLIT THE HEX STRING BASED ON SPACE (ONE SPACE BETWEEN TWO NUMBERS)
                string[] hexSplit = hexString.Split(' ');

                //LOOP THROUGH THE EACH HEX SPLIT
                foreach (String hex in hexSplit)
                {
                    // CONVERT THE NUMBER TO BASE 16
                    int value = Convert.ToInt32(hex, 16);

                    // GET THE RESPECTIVE CHARACTER
                    //string stringValue22 = Convert.ToString(hex, 16);
                    string stringValue = Char.ConvertFromUtf32(value);
                    char charValue = (char)value;

                    //APPEND THE STRING
                    ascii += charValue;
                }

                //RETURN THE STRING
                return ascii;
            }
            catch (Exception ex) { Console.WriteLine(ex.Message); }

            return string.Empty;
        }
        static string GetString(byte[] bytes)
        {
            char[] chars = new char[bytes.Length / sizeof(char)];
            System.Buffer.BlockCopy(bytes, 0, chars, 0, bytes.Length);
            return new string(chars);
        }
        private DataTable Get_Data_From_FastDB(string Qury)
        {


            string connection_string = @"Data Source=" + Fast_loadDB + ";";

            DataTable dt = new DataTable();
            try
            {
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, connection_string);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                dt = tbFound;
            }
            catch (Exception ex)
            {
                Global_Variable.Update_Um_StatusBar_Prograss("خطاء في ملف قاعده البيانات المنزلة", -1);
                //MessageBox.Show(ex.Message + "\nGet_Data_From_FastDB"); /*return null;*/
                //return Get_Data_From_Command(connection_string, Qury);
            }
            return dt;
        }
        private DataTable Get_Data_From_Command(string connection_string, string Qury)
        {
            DataTable dt = new DataTable();



            var sql = "SELECT * FROM authors";

            try
            {
                using var connection = new SQLiteConnection(connection_string);
                connection.Open();

                using var command = new SQLiteCommand(Qury, connection);

                using var reader = command.ExecuteReader();
                if (reader.HasRows)
                {
                    while (reader.Read())
                    {
                        var id = reader.GetInt32(0);
                        var firstName = reader.GetString(1);
                        var lastName = reader.GetString(2);
                        Console.WriteLine($"{id}\t{firstName}\t{lastName}");
                    }
                }
                else
                {
                    MessageBox.Show("No data found.");
                }

            }
            catch (SQLiteException ex)
            {
                MessageBox.Show(ex.Message);
            }

            return dt;
        }

        private bool runRepire()
        {
            bool status = false;
            try
            {
                string connection_string = @"Data Source=" + Fast_loadDB+";";
                //string connection_string = utils.Get_LocalDB_ConnectionString_WithoutPass(Fast_loadDB) ;
                //string Qury = @"PRAGMA writable_schema = 1;
                //            delete from sqlite_master where type in ('index', 'trigger');
                //            PRAGMA writable_schema = 0;
                //            VACUUM;";

                string Qury = @"PRAGMA journal_mode=OFF;
                            PRAGMA synchronous=OFF;
                            PRAGMA writable_schema = 1;
                            delete from sqlite_master where type in ('index', 'trigger');
                            PRAGMA writable_schema = 0;
                            VACUUM;";

                var conn = new SQLiteConnection(connection_string);
                SQLiteCommand cmd2 = new SQLiteCommand(Qury, conn);
                conn.Open();
                int h = cmd2.ExecuteNonQuery();
                conn.Close();
                status = true;
            }
            catch { }

            //return true;
            return status;
        }

        public void Remove_Indexs()
        {
            return;
            bool status = false;
            try
            {
                //string connection_string = @"Data Source=" + Fast_loadDB;
                List<string> indexes = new List<string>();
                lock (Sql_DataAccess.Lock_localDB)
                {
                    try
                    {
                        using (var con = Sql_DataAccess.GetConnection())
                        {
                            indexes = con.Query<string>(@"SELECT name  from sqlite_master where type in ('index');", new DynamicParameters()).ToList();
                        }
                    }
                    catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                }
                if (indexes.Count > 0)
                {
                    string qindex = "";
                    foreach (string index in indexes)
                    {
                        if (!index.Contains("autoindex"))
                            qindex += $" DROP INDEX IF EXISTS \"{index}\" ;\n";
                    }
                    qindex += "PRAGMA optimize;\r\nPRAGMA VACUUM;";
                    lock (Sql_DataAccess.Lock_localDB)
                    {
                        try
                        {
                            using (var con = Sql_DataAccess.GetConnection())
                            {
                                var a = con.Execute(qindex);
                            }
                        }
                        catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                    }

                }
            }
            catch { }

            //return true;

        }
        public void Create_Indexs()
        {
            //var rb = Smart_DataAccess.Get_default_Connections_Db();
            //clss_CreateNewDatabase ff = new clss_CreateNewDatabase();
            //ff.create_default_db(rb);


            string qindex = @"  CREATE INDEX IF NOT EXISTS UmUser_idx_001b4956 ON UmUser(DeleteFromServer);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_49fb733a ON UmUser(DeleteFromServer, SN DESC);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_02eb6568 ON UmUser(SN DESC);

                                CREATE INDEX IF NOT EXISTS UmUser_idx_f28dcfa3 ON UmUser(DeleteFromServer, BatchCardId, Status);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_e07cf9bb ON UmUser(DeleteFromServer, BatchCardId);

                                CREATE INDEX IF NOT EXISTS UmSession_idx_001b4956 ON UmSession(DeleteFromServer);
                                CREATE INDEX IF NOT EXISTS UmSession_idx_bb530fdb ON UmSession(DeleteFromServer, Sn);


                                CREATE INDEX IF NOT EXISTS HSUser_idx_02eb6568 ON HSUser(SN DESC);
                                CREATE INDEX IF NOT EXISTS HSUser_idx_e43b0e22 ON HSUser(Status, DeleteFromServer, SN DESC);
                                CREATE INDEX IF NOT EXISTS HSUser_idx_f28dcfa3 ON HSUser(DeleteFromServer, BatchCardId, Status);

                                CREATE INDEX IF NOT EXISTS HSUser_idx_f28dcfa3 ON HSUser(DeleteFromServer, BatchCardId, Status);
                                CREATE INDEX IF NOT EXISTS HSUser_idx_e07cf9bb ON HSUser(DeleteFromServer, BatchCardId);
                                CREATE INDEX IF NOT EXISTS HSUser_idx_e43b0e22 ON HSUser(Status, DeleteFromServer, SN DESC);
                                CREATE INDEX IF NOT EXISTS HSSession_idx_49be2e56 ON HSSession(DeleteFromServer, Id DESC);


                                CREATE INDEX IF NOT EXISTS UmPyment_idx_811fb391 ON UmPyment(Fk_Sn_Name);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_d5970c3d ON UmUser(FirsLogin);
                                CREATE INDEX IF NOT EXISTS UmSession_idx_180135e3 ON UmSession(FromTime);
                                CREATE INDEX IF NOT EXISTS UmPyment_idx_c6d9b548 ON UmPyment(AddedDate);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_9e0ac6a9 ON UmUser(BatchCardId);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_0056e50e ON UmUser(SpCode);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_07fd7236 ON UmUser(Status, SpCode);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_e7c209d4 ON UmUser(NasPortId);
                                CREATE INDEX IF NOT EXISTS UmSession_idx_adaa03a3 ON UmSession(NasPortId, FromTime);
                                CREATE INDEX IF NOT EXISTS UmUser_idx_188647fd ON UmUser(NasPortId, FirsLogin);
                                CREATE INDEX IF NOT EXISTS UmSession_idx_811fb391 ON UmSession(Fk_Sn_Name);
";


            try
            {
                using (var con = Sql_DataAccess.GetConnection())
                {
                    var a = con.Execute(qindex);
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }



            //FormConnection formConnection = new FormConnection();
            //formConnection.Get_Path_Database2();
        }
        private void checkdb()
        {
            string connection_string = @"Data Source=" + Fast_loadDB;

            DataTable dt = new DataTable();
            try
            {
                string qyery = @"PRAGMA writable_schema = 1;
                                    delete from sqlite_master where type in ('index', 'trigger');
                                    PRAGMA writable_schema = 0;
                                    VACUUM;";

                SQLiteDataAdapter adapter = new SQLiteDataAdapter(qyery, connection_string);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                dt = tbFound;
            }
            catch (Exception ex) { MessageBox.Show(ex.Message + "\nGet_Data_From_FastDB"); /*return null;*/ }

        }

        [Obsolete]
        public List<SourceCardsUserManager_fromMK> Get_UmUser_Form_Last_Syn()
        {
            // قبل جلب البيانات نشوف كم عدد الذي مش محذوف في القاعده المحليه ونشوف العدد في الروتر والفارق هو عدد الدواره من اخر سيريال نمبر

            List<SourceCardsUserManager_fromMK> users = new List<SourceCardsUserManager_fromMK>();
            Mk_DataAccess_old mk = new Mk_DataAccess_old();
            double count_Server_UM = mk.Get_usermanager_count();
            double last_SN_DB = 0;
            double count_NotDeleteFromServer = 0;


            lock (Sql_DataAccess.Lock_localDB)
            {
                try
                {
                    //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    count_NotDeleteFromServer = Local_DA.Get_int_FromDB($"select count(*) from UmUser where DeleteFromServer=0 ");
                    //count_NotDeleteFromServer = db.Count<UmUser>(x => x.DeleteFromServer == 0);
                    //last_SN_DB = db.Select<UmUser>(c => c.DeleteFromServer == 0).OrderByDescending(x => x.Sn_Name).Take(1).Last().SN;
                    last_SN_DB = Local_DA.Get_int_FromDB("select SN from UmUser where DeleteFromServer=0 order by Id Desc LIMIT 1 ");
                    //}
                }
                catch { }
            }
            double countUserForSearch = count_NotDeleteFromServer - count_Server_UM;
            if (countUserForSearch > 0)
            {
                users = SourceCardsUserManager_fromMK.Get_UM_user_FromSN_ToSN(last_SN_DB + 1, last_SN_DB + countUserForSearch);
            }
            return users;

        }
        [Obsolete]
        public List<SourcePymentUserManager_fromMK> Get_UmPyment_Form_Last_Syn()
        {
            List<SourcePymentUserManager_fromMK> users = new List<SourcePymentUserManager_fromMK>();

            Mk_DataAccess_old mk = new Mk_DataAccess_old();
            double count_Server_UM = mk.Get_pyment_count();
            double last_SN_DB = 0;
            double count_NotDeleteFromServer = 0;

            lock (Sql_DataAccess.Lock_localDB)
            {
                //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                //{
                //    count_NotDeleteFromServer = db.Count<UmPyment>(x => x.DeleteFromServer == 0);
                //    last_SN_DB = db.Select<UmPyment>(c => c.DeleteFromServer == 0).OrderByDescending(x => x.Sn).Take(1).Last().Sn;
                //}
                count_NotDeleteFromServer = Local_DA.Get_int_FromDB($"select count(*) from UmPyment where DeleteFromServer=0 ");
                last_SN_DB = Local_DA.Get_int_FromDB("select SN from UmPyment where DeleteFromServer=0 order by Id Desc LIMIT 1 ");
            }
            double countUserForSearch = count_NotDeleteFromServer - count_Server_UM;
            if (countUserForSearch > 0)
            {
                mk.Add_Payment_FromSn_ToSn(last_SN_DB + 1, last_SN_DB + countUserForSearch);
                //users = SourcePymentUserManager_fromMK.Get_UMPyment_FromSN_ToSN(last_SN_DB+1, last_SN_DB+countUserForSearch);
            }
            return users;
        }

    }
}
