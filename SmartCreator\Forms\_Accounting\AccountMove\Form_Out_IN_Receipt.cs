﻿using SmartCreator.Data;
using SmartCreator.Forms.Accounting.Accounts;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting.AccountMove
{
    public partial class Form_Out_IN_Receipt : RJChildForm
    {
        Smart_DataAccess smart_DataAccess = null;
        string Filter_Type = "Out";
        public Form_Out_IN_Receipt(string _Type = "Out")
        {
            InitializeComponent();
            Filter_Type = _Type;
            smart_DataAccess = new Smart_DataAccess();
            this.Text = "سندات الصرف";
            //this.Text = "السندات";

            if (Filter_Type == "Expense")
                this.Text = "سندات القبض";

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
           
            if (UIAppearance.Language_ar)
            {
                //System.Drawing.Font title_font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Bold);
                rjLabel1.Font = rjLabel5.Font = Program.GetCustomFont(Resources.DroidSansArabic, 11, FontStyle.Regular);
                btnAddNew.Font = btnEdit.Font = btnDelete.Font = btnRefresh.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);

                dgv.AllowUserToOrderColumns = true;
                dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f, FontStyle.Bold);
                dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //utils utils = new utils();
                //utils.Control_textSize1(this);
            }

            utils utils1 = new utils();
            utils1.Control_textSize1(this);

            getData();
        }
        private DataTable dt_table()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("التسلسل", typeof(double));
            dt.Columns.Add("الجهه", typeof(string));
            dt.Columns.Add("البند", typeof(string));
            dt.Columns.Add("المبلغ", typeof(Int64));
            dt.Columns.Add("التاريخ", typeof(DateTime));
            dt.Columns.Add("البيان", typeof(string));

            return dt;
        }
        private void getData()
        {
            try
            {
               List< Entities.Accounts.AccountMove> accountMove=new List<Entities.Accounts.AccountMove>();
                if (Filter_Type == "Out")
                {
                    //DataTable tbFound = smart_DataAccess.RunSqlCommandAsDatatable($"select * from AccountMove where ParentId is null and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                    accountMove = smart_DataAccess.Load<Entities.Accounts.AccountMove>($"select * from AccountMove where Move_type='out_receipt' and entry_type='parent' and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                }
                if (Filter_Type == "In")
                {
                    accountMove = smart_DataAccess.Load<Entities.Accounts.AccountMove>($"select * from AccountMove where Move_type='in_receipt' and entry_type='parent'  and Rb='{Global_Variable.Mk_resources.RB_SN}' ");
                }

                dgv.DataSource = accountMove;


                //DataTable dt = dt_table();
                //foreach (var itm in accountMove)
                //{
                //    try
                //    {
                //        DataRow row = dt.NewRow();
                //        row["التسلسل"] = itm.Sequence;
                //        //row["المبلغ"] = 0;
                //        row["المبلغ"] = itm.Move_type == "out_receipt" ? Convert.ToInt64(itm.credit) : Convert.ToInt64(itm.debit);
                //        row["التاريخ"] = itm.Date;
                //        row["البيان"] = itm.Ref;

                //        dt.Rows.Add(row);
                //    }
                //    catch { }
                //}
                //dgv.DataSource = dt;


                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    column.Visible = false;
                }

                try { dgv.Columns["Sequence"].HeaderText = "رقم السند"; } catch { }
                try { dgv.Columns["Sequence"].Visible = true; } catch { }
                try { dgv.Columns["PartnerId"].Visible = true; } catch { }
                try { dgv.Columns["AccountId"].Visible = true; } catch { }
                try { dgv.Columns["Date"].Visible = true; } catch { }
                try { dgv.Columns["Ref"].Visible = true; } catch { }

                if (Filter_Type == "Out")
                {
                    try { dgv.Columns["AccountId"].HeaderText = "بند المصروف"; } catch { }
                    try { dgv.Columns["debit"].HeaderText = "المبلغ"; } catch { }
                    try { dgv.Columns["debit"].Visible = true; } catch { }
                }
                if (Filter_Type == "In")
                {
                    try { dgv.Columns["AccountId"].HeaderText = "بند الايراد"; } catch { }
                    try { dgv.Columns["credit"].HeaderText = "المبلغ"; } catch { }
                    try { dgv.Columns["credit"].Visible = true; } catch { }
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Entities.Accounts.AccountMove>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "AccountMove");
                    var frm = new Form_Out_IN_Receipt_Add_Edit(sp);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }

        private void Form_Out_IN_Receipt_SizeChanged(object sender, EventArgs e)
        {
            this.Refresh();
            rjTextBox1.Refresh();
        }

        private void btnAddNew_Click(object sender, EventArgs e)
        {
            try
            {
                Form_Out_IN_Receipt_Add_Edit frm = new Form_Out_IN_Receipt_Add_Edit(null, Filter_Type);
                frm.ShowDialog();
                if (frm.succes)
                    getData();
            }
            catch { }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {

            if (dgv.SelectedRows.Count > 0)
            {
                try
                {
                    Smart_DataAccess dataAccess = new Smart_DataAccess();
                    var sp = dataAccess.LoadSingleById<Entities.Accounts.AccountMove>(Convert.ToInt32(dgv.CurrentRow.Cells["Id"].Value.ToString()) + "", "AccountMove");
                    var frm = new Form_Out_IN_Receipt_Add_Edit(sp, Filter_Type);
                    frm.add = false;
                    frm.ShowDialog();
                    if (frm.succes)
                    {
                        getData();
                    }
                }
                catch { }
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (RJMessageBox.Show("هل انت متاكد من الحذف", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {

                Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                smart_DataAccess.DeleteById<Entities.Accounts.Account>(dgv.CurrentRow.Cells["Id"].Value.ToString(), "AccountMove");
                getData();
            }
        }


    }
}
