using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;
using SmartCreator.RJControls.Design;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج اختبار RJTabControl الجديد
    /// </summary>
    public class RJTabControlTestForm : Form
    {
        private RJTabControl tabControl;
        private Panel buttonPanel;

        public RJTabControlTestForm()
        {
            InitializeComponent();
            SetupTabControl();
            SetupButtons();
            AddSampleTabs();
        }

        private void InitializeComponent()
        {
            this.Text = "RJTabControl Test - التابات الجديدة";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);
        }

        private void SetupTabControl()
        {
            // إنشاء TabControl - بألوان افتراضية
            tabControl = new RJTabControl
            {
                Dock = DockStyle.Fill,
                TabHeight = 40,
                TabSpacing = 3,
                TabPadding = 20,
                ShowCloseButtons = false
            };

            // ربط الأحداث
            tabControl.TabChanged += TabControl_TabChanged;
            tabControl.TabAdded += TabControl_TabAdded;
            tabControl.TabRemoved += TabControl_TabRemoved;

            this.Controls.Add(tabControl);
        }

        private void SetupButtons()
        {
            // Panel للأزرار
            buttonPanel = new Panel
            {
                Dock = DockStyle.Bottom,
                Height = 60,
                BackColor = Color.FromArgb(30, 30, 30),
                Padding = new Padding(10)
            };

            // زر إضافة تاب
            var addTabButton = new RJButton
            {
                Text = "إضافة تاب",
                IconChar = IconChar.Plus,
                Size = new Size(120, 40),
                Location = new Point(10, 10),
                BackColor = Color.FromArgb(0, 122, 204),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            addTabButton.Click += AddTabButton_Click;

            // زر إزالة تاب
            var removeTabButton = new RJButton
            {
                Text = "إزالة تاب",
                IconChar = IconChar.Minus,
                Size = new Size(120, 40),
                Location = new Point(140, 10),
                BackColor = Color.FromArgb(204, 0, 0),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            removeTabButton.Click += RemoveTabButton_Click;

            // زر مسح الكل
            var clearTabsButton = new RJButton
            {
                Text = "مسح الكل",
                IconChar = IconChar.Trash,
                Size = new Size(120, 40),
                Location = new Point(270, 10),
                BackColor = Color.FromArgb(150, 150, 150),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            clearTabsButton.Click += ClearTabsButton_Click;

            // زر تغيير النمط
            var styleButton = new RJButton
            {
                Text = "تغيير النمط",
                IconChar = IconChar.Palette,
                Size = new Size(120, 40),
                Location = new Point(400, 10),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            styleButton.Click += StyleButton_Click;

            // زر تغيير حدود المحتوى
            var borderButton = new RJButton
            {
                Text = "حدود المحتوى",
                IconChar = IconChar.BorderAll,
                Size = new Size(120, 40),
                Location = new Point(530, 10),
                BackColor = Color.FromArgb(156, 39, 176),
                ForeColor = Color.White,
                BorderRadius = 8
            };
            borderButton.Click += BorderButton_Click;

            buttonPanel.Controls.Add(addTabButton);
            buttonPanel.Controls.Add(removeTabButton);
            buttonPanel.Controls.Add(clearTabsButton);
            buttonPanel.Controls.Add(styleButton);
            buttonPanel.Controls.Add(borderButton);

            this.Controls.Add(buttonPanel);
        }

        private void AddSampleTabs()
        {
            // تاب الرئيسية
            var homeTab = tabControl.AddTab("الرئيسية", IconChar.Home);
            var homeLabel = new RJLabel
            {
                Text = "مرحباً بك في الصفحة الرئيسية! 🏠\n\nهذا تاب يرث من RJButton",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(0, 122, 204)
            };
            homeTab.AddControl(homeLabel);

            // تاب الإعدادات
            var settingsTab = tabControl.AddTab("الإعدادات", IconChar.Cog);
            var settingsPanel = new RJPanel
            {
                Dock = DockStyle.Fill,
                BackColor = Color.FromArgb(250, 250, 250),
                BorderRadius = 10,
                BorderSize = 1,
                BorderColor = Color.FromArgb(200, 200, 200)
            };
            var settingsLabel = new RJLabel
            {
                Text = "صفحة الإعدادات ⚙️\n\nيمكنك تخصيص التطبيق هنا",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(70, 70, 70)
            };
            settingsPanel.Controls.Add(settingsLabel);
            settingsTab.AddControl(settingsPanel);

            // تاب المعلومات
            var infoTab = tabControl.AddTab("معلومات", IconChar.InfoCircle);
            var infoTextBox = new RJTextBox
            {
                MultiLine = true,
                Text = "معلومات التطبيق:\n\n" +
                       "• RJTabControl جديد كلياً\n" +
                       "• كل تاب هو RJButton\n" +
                       "• لا تكرار في الكود\n" +
                       "• استخدام مباشر لـ RJControls\n" +
                       "• بساطة وقوة\n" +
                       "• contentPanel الآن RJPanel مع حدود\n" +
                       "• RJTextBox يدعم ReadOnly",
                Dock = DockStyle.Fill,
                Style = TextBoxStyle.MatteBorder,
                BackColor = Color.White,
                Font = new Font("Segoe UI", 11),
                ReadOnly = true,
                BorderSize = 0
            };
            infoTab.AddControl(infoTextBox);
        }

        #region Event Handlers
        private void TabControl_TabChanged(object sender, TabChangedEventArgs e)
        {
            Console.WriteLine($"تم تغيير التاب من '{e.PreviousTab?.Text}' إلى '{e.CurrentTab?.Text}'");
        }

        private void TabControl_TabAdded(object sender, TabEventArgs e)
        {
            Console.WriteLine($"تم إضافة التاب: {e.Tab.Text}");
        }

        private void TabControl_TabRemoved(object sender, TabEventArgs e)
        {
            Console.WriteLine($"تم إزالة التاب: {e.Tab.Text}");
        }

        private void AddTabButton_Click(object sender, EventArgs e)
        {
            var tabNumber = tabControl.TabCount + 1;
            var newTab = tabControl.AddTab($"تاب {tabNumber}", IconChar.File);
            
            var label = new RJLabel
            {
                Text = $"محتوى التاب رقم {tabNumber}\n\nتم إنشاؤه ديناميكياً! 🎉",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12),
                ForeColor = Color.FromArgb(50, 50, 50)
            };
            newTab.AddControl(label);
        }

        private void RemoveTabButton_Click(object sender, EventArgs e)
        {
            if (tabControl.ActiveTab != null)
            {
                tabControl.RemoveTab(tabControl.ActiveTab);
            }
        }

        private void ClearTabsButton_Click(object sender, EventArgs e)
        {
            tabControl.ClearTabs();
        }

        private void StyleButton_Click(object sender, EventArgs e)
        {
            // تدوير الأنماط
            var currentStyle = tabControl.TabStyle.StyleType;
            switch (currentStyle)
            {
                case TabStyleType.Modern:
                    tabControl.TabStyle = TabStyle.Blue;
                    tabControl.TabStyle.StyleType = TabStyleType.Chrome;
                    break;
                case TabStyleType.Chrome:
                    tabControl.TabStyle = TabStyle.Green;
                    tabControl.TabStyle.StyleType = TabStyleType.VSCode;
                    break;
                case TabStyleType.VSCode:
                    tabControl.TabStyle = TabStyle.Dark;
                    tabControl.TabStyle.StyleType = TabStyleType.Classic;
                    break;
                default:
                    tabControl.TabStyle = TabStyle.Default;
                    tabControl.TabStyle.StyleType = TabStyleType.Modern;
                    break;
            }
        }

        private void BorderButton_Click(object sender, EventArgs e)
        {
            // تدوير أنماط الحدود
            if (tabControl.ContentBorderSize == 1)
            {
                // حدود سميكة ملونة
                tabControl.ContentBorderSize = 3;
                tabControl.ContentBorderColor = Color.FromArgb(0, 122, 204);
                tabControl.ContentBorderRadius = 10;
            }
            else if (tabControl.ContentBorderSize == 3)
            {
                // حدود خضراء مدورة
                tabControl.ContentBorderSize = 2;
                tabControl.ContentBorderColor = Color.FromArgb(76, 175, 80);
                tabControl.ContentBorderRadius = 15;
                // لا نغير ContentBackColor - نتركه افتراضي
            }
            else if (tabControl.ContentBorderSize == 2)
            {
                // حدود حمراء
                tabControl.ContentBorderSize = 4;
                tabControl.ContentBorderColor = Color.FromArgb(244, 67, 54);
                tabControl.ContentBorderRadius = 5;
                // لا نغير ContentBackColor - نتركه افتراضي
            }
            else
            {
                // العودة للحالة الافتراضية
                tabControl.ContentBorderSize = 1;
                tabControl.ContentBorderColor = Color.FromArgb(200, 200, 200);
                tabControl.ContentBorderRadius = 0;
                // لا نغير ContentBackColor - نتركه افتراضي
            }
        }
        #endregion

        /// <summary>
        /// تشغيل النموذج من أي مكان
        /// </summary>
        public static void RunTest()
        {
            var form = new RJTabControlTestForm();
            form.ShowDialog();
        }
    }

    /// <summary>
    /// كلاس لتشغيل النموذج
    /// </summary>
    public static class RJTabControlTestRunner
    {
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new RJTabControlTestForm());
        }

        /// <summary>
        /// تشغيل النموذج من أي مكان
        /// </summary>
        public static void RunTest()
        {
            RJTabControlTestForm.RunTest();
        }
    }
}
