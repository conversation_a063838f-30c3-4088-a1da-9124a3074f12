﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="pictureBox_QR.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAGUAAABlCAYAAABUfC3PAAAABGdBTUEAALGPC/xhBQAAAAlwSFlzAAAO
        vAAADrwBlbxySQAAE35JREFUeF7tnXXIZUUYxte1E3NNBN0FsVtsRcVYXDtRQXEtLOz8x07s2FVQELsT
        C+xWDKxVsRW7O4/8hu8Z3js7M3fuued+Xtf7wsM538w778Rzzjt57jeiGkjfyYCUPpQBKX0oA1L6UAak
        9KEMSOlDGZDShzIgpQ8lScpvv/1Wvf766z3Ba6+9Vn355ZdDOeXl66+/dvoxO99++231999/D2nGhfhP
        P/00mh788ssvXo8yTZo0KarXLbBLXu3KiyRJwdDUU09djRw5sic47bTThnLKy3nnnZcsx0033VRUyQMO
        OCCaftppp61eeOEFp/PXX3+5MsX0mgB14OHqmpSpppqqJxgxYkR1+umnD+WUF0iJ2QCQUiL7779/ND14
        6aWXhrQqR0pMpynQpiXSF6Tknp6QFJ463f/vSbnooouqRx99tDYeeuihap555nG2LCkQ8uabb1ZrrLFG
        FIsuuqgnYtZZZ3V2HnnkEWdzjz32qNZaa61oOgsaW2ksCPvuu+9cORBLyqhRo1xeYZpOQJvZh6hxUsik
        G/n999+r+eef39kKSXnxxRdb8kphjjnmcAMQyZZbbhnVC3HuuedG30bCbLglZYEFFqj++OOPoZh68thj
        j7WUo3FSyKAbSZGCdEIKdiRbbLFFVC8EpJTIf5qUV155pZowYUI1ceLELO644w7/JKZIYdTz/vvvO1ck
        yM2BJZdc0oWNHz++2muvvaqLL77Y573CCit497DIIov49Lvvvns144wzehuWlE8++cSnx9YXX3wxFJMm
        5c8//6zuvPPOlrqlQNtQJ2RYSaEyNi4GGmuTTTZpS4pciECFaGzZoZMmjDgakGGsiLD+GlcmPfKac845
        fZwl5ZlnnvHhpNeQGEmRgt2NN97Yx+UA4ZQBGVZSyNjGpUBFJDn3ZYUKrbjiit4GpKiSkDLNNNP4OIvN
        N9/c65EXrk5xlpRnn33W5a+41OjLkoLd/xUpVIC4H3/80eGHH36oll9+eW9j7733dmHEffjhh24EhmsK
        se2223q3gT0aVXEXXHCBC0eee+65aqaZZnLhXAekZDp6ZryCtWHD55prruqnn35ytmiwECJFRCucPkEN
        xb3iuPK3ZEBKQEqYNoZw9NW0DEhpQ0qsMw9JofKCOnmF62+hRAakJEiBhJNPPrm65pprHHbddVdPTEjK
        mWeeWW2//fbVdttt5+zJfeGSGBYTTjzD2RJiBqRkSGHYKrFrX7nJY+noKyf/C1KYp0hypDBXUBpIYdgq
        CUlhmUWVtqRwb0mx85RzzjnHx+UkRQpv4EYbbeTjcmAO96+Qctddd7kG5+lJgfhjjz22paFipFDht956
        y6Wh4lzfeOMNn+7WW2/1eW222WbV7bff7twRYLFR5Vt99dV9ODqQpLLcfPPNXZGCOzz66KO9vRxYxZAb
        HVZSSiqIWL0UKehIr51dzehVJjsIsPdMMO3ySWl5c+6r1IbVHVZS6kjOfZVKbkZvEZJiJde4KVLqSs9J
        efDBB13DUtA6YNJHRbGVIkVPWSw9YK99uummaymXABHEcWW2/tFHH7k0KrPIsGH0T1wlISk///yzi68L
        2sy+wY2SguG5557bFbQb6CnPkaJONZZ+vvnmc2WxFRVIwzIMZLz33nvVYost5tNdeumlnpTnn3++WnDB
        BX3cq6++6sIRSwpukjdbenVAm/WMlKaRc180nl2QLEXTC5K9QCOkxJ7IpmA7et4OrroPSSkpB6MtXAY2
        cEuskxFOWg2JQWrpnrhek8JplhJJksIZKBquV3jqqadcPpCAyznssMOqQw891F1xU6rImmuu6RorZsPi
        hBNOcGnBwQcf7FaAZWO99dbzcUceeWR1xhln+HT0UwikUCZrs2mkBh+hJEkZTsktSNr9lJwwF7FuKQXe
        wn6XJCk0hHUrqYax8TFIsBXaA4ThQlIuKkeKtcNxI2y0A6TIXqxMCNdUeQXppdCNJElhOEhj8RRz/f77
        76OZ0amiE4I0zNRVSPz9yy+/PJkeo6FbbrmlWmqppTwY0paQ8vbbb/syMsJaZpllWuzEwAIljY0wU8fP
        qywffPCBCyc/jpgq3IL83n33XaeHoMtgQfGfffZZyx5NHcl29PaJTU0e2REMN6YElh0kkKfJYwgaS0Il
        U9vBoaQWJEsFffKWDfo0CcPr1EQVYhHS87DZh8guSNaVRkhJFb7XpNhzX3ZBEilpGHSmGFLka7myf24L
        b/03C4nyzQxTmbTZeGHppZf2egBSFBeSQrzytqRwr3DpxETxQikptrxygYCHrS9IufHGG90xIBrPHnIA
        J510kpuggcsuu8zrrbzyytX999/v4wQOMtCnoANWWmml6oYbbvDxnAmT8FkEdmTTThC5t3mhGxPOZUmP
        qx06p0hhVMdch/Iwz2F5XuXFhnXf/xopuf0UdgsRCsYyusJZtmA+EhM6SOnxJFL5mDSxIGknjyFypLBd
        gVCva6+9drK0Ql+TglARhXdCit15tNIvpFDHMK3QU1KY6eLP99tvP4dTTz3V7QKC4447zsUBzmXZ1zdF
        CjrHH3+8t4HbktAAyosrM3jpWZx11lm+POiNHj3a2x8zZowvE0A3ZgPbSs+VRUPZyPUp1JP0rKOdeOKJ
        LXlZcBBepHz88ccteX/11VcuvJ0kScEwHRlXwC6fOjrO6yqO+UsJKYC0urLsgIT5cI+vtukE+o1ff/3V
        69shMffMDwhnUGG3gy2wrfToM8hQXIoU1Vv3dPQqbwoI3kVpQddrX6HYrVdIkYTzlBwpAu5ApIRChXKk
        MNqRpOYp6NhBgAW2Jeh3MyRuJ5Bi09EllEiSFJ4i/DJgcXL99dd3FQUHHnjgkFblNq9wAYpjZCa59957
        fTgQeZ2QwnBT6TlZnzo4UUrKcsst5+sFFl98cR8nUrBD/2frZbHTTju1tI3yDYUFTqXhzeXsQYlkO3p2
        8mhInhi+aqJBqDBXXADCFZdCOGAyJeFeaSCPeQqV74SUfffdd7J8uyGFujDooF6qn+IsKbbsFtT1yiuv
        dOlkA28RI4YH25bdtk1OsqSMHPKjwHZgXG0hcuEChSrZo0fXkkLnGdqU1CHF1sneA0uKzccKD4UdfUFM
        ihSJ4nJ2rWRJsQW2Q+I6QkM1QYqVOqTkYPuUlJBHp6R0KsWkrL322m4pI4etttrKfdyJUEjmBIqjAbUc
        YUnhyXvnnXda9GyDMuwljLidd97ZNbga4PHHH3ezf5btGQKTv+xcddVVLhxsuumm/q1g6Kxw+r+FF17Y
        hRPPvr7S58CKgcoHOI+muLvvvtu79rpSTAqFDl/3EMSnRl82rSWFBmY5XHE5QBakSNS/ADa5lAd9xuef
        f+7jmI/IBm+h0nG1o68mMKwz+lLUGRLbGX0OISlW7M4jnTkjIwkuUDYgRULj/adIYUiI2xB0EAEwNFU4
        rsGOYCwprGFZGwKvOY2IUAHcl40L3RduiTiGotZ9PfHEE84NYevss8/2NtDnd1skOVL4iRCls2DbQW8e
        V47FKm6VVVbx9gDuS3F8MaAyhbBlykmSFAqsHTTuw8mj/CZL9ylS5CZCIVzpiQ/vWXmVPVyP7CheNiEQ
        Hd4Q7mWHq+6RHCmqoxXCmTziBkkDKSyuIti1HT1xtIGEL5hpD8pkgW7Xv80SSp0ZfR2h0N2OvkJJkZKT
        cEaPK0bIw5ISjr723HNPT0IIuoQSSZLCE6EJD+CoD4UEkKLwb775pqXwIoVC8hRKT2tWilM47oh8FI4O
        oxtsUmHciyocSikprECo7NgmT/ImX/tGSbADKToGyxvDaXqVl8mj8qWMuCXZHD9+vI/jLVI9uHa99sXv
        pdCP4N8BQz1GNOD88893ywaKS70p99xzj9fjBwtYNUVoCD6TIJx43kIJcWxQ0VGTV+rABlJCCmH8/ors
        PfDAA75eXDnMEQppaGTVl8MQ2Fd5+UKZPPVGKJwrw37IIJy+h9V22eEhSNXFSpKU3OSxif0UDsYpjhFQ
        HSl9U6ww+LDuxR5bTQl26fiVphQMDkrKFEpPSUntPFLQuqTYSlpSuLdxqXsmtJ2SwtvbBCn2PidFpPA6
        brPNNq7DxMcfc8wxbrMHcAQUfyk9Ci89nl69yujstttuPs525rg2wtvhiCOOcG5FlbOkMHSWHn2IRkTo
        XnfddX5ji2V3pQGlpHCOQHW2YCWBvifWuXPynjypL+B3YUqIKX5TLHKjrxRETjfAZ2tQgFhSLHgAUpPH
        EKXuK9aYhPGQ2NMsOXQ9+mqalCYAKTSCZLhISQmkMOIaNlI4mslxH9wMkzkLTrjryWGfBD3C0bUzfxqR
        MIDO9NNP31JIwBs088wz+/SAvxU/77zz+nzXXXfdljeF0/XK24JRj44YUU725bFL3BJLLNGSf4wU0nBs
        l0FBO7CRteqqq/q8R40a5b0CozTbNhyzVbvlJEkKiWNjeIQ4GQ/vrc+mf1EcjYmPVZwF++QS9KmA4njK
        bTlkD+He/i2x4VxJr7/D0ywpUjR5pK9IgfR4Cfov2WfyKFJsR89V9+0kSUpdCUmR4HaaPrZaR3i61aAg
        5b5ye/QWkJKa0Tc+JK4jFICf3lCBObaqQqXeFCrFaXn0AKsAvO6Kt6QoXk++BWF6o6SnON0jTz/9dEve
        rFArvXS4cgJfpKCXGqgQrgkuYO1LcZCSKkdOGieFvoiKAutDKRBHRhUnsJdy2223ubcFN8bVHiW1pLCk
        s+yyyzqdEAw5yQPhrWSVQHFXXHGFt4GrsXnz4KBD3gzvEXT5FIK3SLp27c+Ct4KfVFRe9KkicJZZZvFx
        2O+6TxlOodJhZQVLCiOqlEuxM3pIYZChOOYTMUGfBpNeajsYvTqTxxBdj74oiNxBL0QNyBVSeLpisB09
        pLAyENOzM/oYKYqzQlgJKeQPKWGeSpeD1YeUWDlCSZLCEUt2BxlONg3s6kNUCkmnSoPEwMKl0p1yyilR
        HXD55Zd78kJSeIts/ha2n8NOTHCLl1xyyWR5gkMOOcTvu4RYaKGFWnT10Ws7SZKSmzx2C/yw3Q7OCWdw
        lS6cPKYkJKUUNFwnwgOVmzz2fEGy9HVNwaa3pFBoW/CwEnVJYSld6XKw5UqREo7MJNznSGFwENanRIpJ
        YWbNq85cow44HalOOiSF2TMryLgxYGftlpTZZ5/d7eejg34K6KTelBlmmKGlTCwmKi5FCu6LPRVsk7f+
        90uMFPJVO40bN65Wv1xECk/Tww8/7ApQFzQ8BcVe6L7o6CEM0EhM8CSWFMqB/5ZuDqk3mzmQysQbxZBV
        cSlSaPyxY8c6u+S/4447+nDsWFL4pwM8VISXzktCKX5TenlC0g6JaUz70ZAlpQmwWiChweoMiXXqPkbK
        sJ77sqTwSuopSAEdCixJkYIe39JDBmD2zJsiP85wVnHdQPWAFNmmfLHvU4ijfugB7plkSq8vSWFHkSen
        HdgMUwFTpBDPTJ2tY4DtXXbZxds4/PDDXZji64BzWSKGfkk/fwhmm202X0dLCn0ILgsd9O0/RehLUnLb
        wRZUSJJzX1aoUNMLktiQvRys+6JDpw+J6U3xpFABuRPd2wXJ3GE8K1bHgnBLinVl9m+ukKJ0kKJJoU3D
        fY4UOnq5dvIGnUpfkMLqAZ9BAw5esLNJxcFRRx1VXX311S6c46A0QkxY1UVPdqw9bOywww7O3oYbbujL
        RwNTRsKJx90qzYUXXuj6N+mts846vkwckUUoOwTgbhXHsVWV47777nM6nUpfuK/S0Vdu8shZXexK18Iu
        SOb2U1h2seks6JtKpKf7Kf1CCg2quJAUKqxKDxcpNs+YTLGkPPnkk85NAX5+AzcC2KuwpOA6pMfHQ6SV
        rgVvm6QJUugnlG8IXK/y5ajvFEEK4IAFnSegk2a7VbAdJ7uc0tt6663dDqDVFeyyTROkXH/99T7fEPRF
        bKSRL6sYUwwpFhoSW0jsESPtp6QgaYIUBgIxHaDfuo/lXSr/GVJiYklp4ixxE6TQNnWIsFKLFE6qk3k7
        6EMbpJM+hSGsbPCpuFwWbmGfffZxfhuw6cWTiR73dLKKS+Gggw7ytplTcMSVcNKyoaY4fotGk0fKxKxe
        ehtssIEvbwjSihS+XFCZuOqrg3ZSi5SSCREFQ08F7IQUnmYJ6WWDJXNN6LDBvEVl4Utf0hKeA4fjZI+B
        AidpCCetXWaxp1mIk22uKmsMlhTazOp3/SVXjpQ60gkpdkhsJTw4ASmS3JDYonSVOLfMkkNIio2jTUuk
        mBTcBJnUReofOiMhKfxyaswGv/XCMJPxP7t6/MC/GoB7ha+22mp+8wp7/FNo4gAjNtnDNdqfp0qRgg2O
        u2IbG3xvrzSA/JQ3D4feXvKweo2T0i2oGOA+R0oOTB45jyW3aN0j9/qbt9KeaWZZRHEcxlM5bJlAjhT6
        R+Wpjl7uTMdWFQ+QvifFohtSaPB2gg66Speb0Vvk3Fdq9MUkkTkJ4aH0hBT7FDWNuqTYiWBKIMUenIAU
        NRqkWJsWfF0mCd+UuqTYNuz6Q1TcBP9bBEO9AB9mIlSGzyliOiEmTZrkl8Vzgg66Smd/k4sG5IGzdgW+
        tELQZTZu41gtQHBNbMopHFtyjaGQl7VBPduVHUmSQuJewUosvgTtJKVrwy1CycXHwlJibZSmSZIykH9P
        BqT0oQxI6UMZkNKHMiClD2VASt9JVf0Dehnxo3K0sPIAAAAASUVORK5CYII=
</value>
  </data>
</root>