﻿using Dapper;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.DAL
{
    /// <summary>
    /// مدير إنشاء وتهيئة قواعد البيانات
    /// </summary>
    public static class DatabaseInitializer
    {
        /// <summary>
        /// تهيئة جميع قواعد البيانات
        /// </summary>
        public static void InitializeAllDatabases()
        {
            try
            {
                InitializeSmartDatabase();
                InitializeLocalDatabase();
                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة جميع قواعد البيانات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة قواعد البيانات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// تهيئة قاعدة بيانات الإعدادات Smart.db
        /// </summary>
        public static void InitializeSmartDatabase()
        {
            using (var connection = DatabaseConnection.GetSmartConnection())
            {
                connection.Open();

                // إنشاء جدول FormStates
                var createFormStatesTable = @"
                    CREATE TABLE IF NOT EXISTS FormStates (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        FormName TEXT NOT NULL,
                        StateKey TEXT NOT NULL,
                        StateValue TEXT,
                        DataType TEXT DEFAULT 'String',
                        Description TEXT,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsActive BOOLEAN DEFAULT 1,
                        UNIQUE(FormName, StateKey)
                    )";

                connection.Execute(createFormStatesTable);

                // إنشاء جدول AppSettings
                var createAppSettingsTable = @"
                    CREATE TABLE IF NOT EXISTS AppSettings (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Category TEXT NOT NULL,
                        SettingKey TEXT NOT NULL,
                        SettingValue TEXT,
                        DataType TEXT DEFAULT 'String',
                        DefaultValue TEXT,
                        Description TEXT,
                        IsRequired BOOLEAN DEFAULT 0,
                        IsEditable BOOLEAN DEFAULT 1,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UpdatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(Category, SettingKey)
                    )";

                connection.Execute(createAppSettingsTable);

                // إنشاء فهارس للأداء
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_FormStates_FormName ON FormStates(FormName)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_FormStates_StateKey ON FormStates(StateKey)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_AppSettings_Category ON AppSettings(Category)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_AppSettings_SettingKey ON AppSettings(SettingKey)");

                // إدراج البيانات الأولية
                SeedSmartDatabase(connection);

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة قاعدة بيانات Smart.db");
            }
        }

        /// <summary>
        /// تهيئة قاعدة البيانات المحلية LocalDB.db
        /// </summary>
        public static void InitializeLocalDatabase()
        {
            using (var connection = DatabaseConnection.GetLocalDbConnection())
            {
                connection.Open();

                // إنشاء جدول LocalUsers
                var createLocalUsersTable = @"
                    CREATE TABLE IF NOT EXISTS LocalUsers (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        Username TEXT NOT NULL UNIQUE,
                        Password TEXT,
                        Email TEXT,
                        Phone TEXT,
                        FullName TEXT,
                        UserType TEXT DEFAULT 'Hotspot',
                        IsActive BOOLEAN DEFAULT 1,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        LastLoginAt DATETIME,
                        MacAddress TEXT,
                        IpAddress TEXT,
                        Notes TEXT
                    )";

                connection.Execute(createLocalUsersTable);

                // إنشاء جدول LocalSessions
                var createLocalSessionsTable = @"
                    CREATE TABLE IF NOT EXISTS LocalSessions (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        SessionId TEXT NOT NULL UNIQUE,
                        UserId INTEGER NOT NULL,
                        StartTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                        EndTime DATETIME,
                        DurationSeconds INTEGER,
                        BytesUploaded INTEGER DEFAULT 0,
                        BytesDownloaded INTEGER DEFAULT 0,
                        IpAddress TEXT,
                        MacAddress TEXT,
                        SessionType TEXT DEFAULT 'Hotspot',
                        Status TEXT DEFAULT 'Active',
                        TerminationReason TEXT,
                        Notes TEXT,
                        FOREIGN KEY (UserId) REFERENCES LocalUsers(Id)
                    )";

                connection.Execute(createLocalSessionsTable);

                // إنشاء جدول LocalPayments
                var createLocalPaymentsTable = @"
                    CREATE TABLE IF NOT EXISTS LocalPayments (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        UserId INTEGER NOT NULL,
                        Amount DECIMAL(18,2) NOT NULL,
                        Currency TEXT DEFAULT 'USD',
                        PaymentMethod TEXT,
                        PaymentDate DATETIME DEFAULT CURRENT_TIMESTAMP,
                        Status TEXT DEFAULT 'Completed',
                        ReferenceNumber TEXT,
                        Description TEXT,
                        ServiceType TEXT,
                        ServiceDurationDays INTEGER,
                        ServiceExpiryDate DATETIME,
                        CardId INTEGER,
                        Notes TEXT,
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (UserId) REFERENCES LocalUsers(Id)
                    )";

                connection.Execute(createLocalPaymentsTable);

                // إنشاء جدول LocalCards
                var createLocalCardsTable = @"
                    CREATE TABLE IF NOT EXISTS LocalCards (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        CardNumber TEXT NOT NULL UNIQUE,
                        CardPassword TEXT,
                        CardType TEXT DEFAULT 'Hotspot',
                        CardValue DECIMAL(18,2) NOT NULL,
                        Currency TEXT DEFAULT 'USD',
                        ValidityDays INTEGER NOT NULL,
                        DataLimitMB INTEGER DEFAULT -1,
                        SpeedLimitKbps INTEGER DEFAULT -1,
                        Status TEXT DEFAULT 'New',
                        CreatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                        UsedAt DATETIME,
                        ExpiryDate DATETIME,
                        UsedByUserId INTEGER,
                        UsedByUsername TEXT,
                        CardGroup TEXT,
                        Notes TEXT
                    )";

                connection.Execute(createLocalCardsTable);

                // إنشاء جدول ActivityLogs
                var createActivityLogsTable = @"
                    CREATE TABLE IF NOT EXISTS ActivityLogs (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        ActivityType TEXT NOT NULL,
                        Description TEXT NOT NULL,
                        UserId INTEGER,
                        Username TEXT,
                        IpAddress TEXT,
                        MacAddress TEXT,
                        Severity TEXT DEFAULT 'Info',
                        Source TEXT DEFAULT 'System',
                        AdditionalData TEXT,
                        Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        IsRead BOOLEAN DEFAULT 0
                    )";

                connection.Execute(createActivityLogsTable);

                // إنشاء فهارس للأداء
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_LocalUsers_Username ON LocalUsers(Username)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_LocalSessions_SessionId ON LocalSessions(SessionId)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_LocalSessions_UserId ON LocalSessions(UserId)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_LocalPayments_UserId ON LocalPayments(UserId)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_LocalCards_CardNumber ON LocalCards(CardNumber)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_ActivityLogs_ActivityType ON ActivityLogs(ActivityType)");
                connection.Execute("CREATE INDEX IF NOT EXISTS IX_ActivityLogs_Timestamp ON ActivityLogs(Timestamp)");

                System.Diagnostics.Debug.WriteLine("✅ تم تهيئة قاعدة البيانات المحلية LocalDB.db");
            }
        }

        /// <summary>
        /// إدراج البيانات الأولية في قاعدة بيانات الإعدادات
        /// </summary>
        private static void SeedSmartDatabase(IDbConnection connection)
        {
            // التحقق من وجود إعدادات افتراضية
            var existingSettings = connection.QueryFirstOrDefault<int>(
                "SELECT COUNT(*) FROM AppSettings WHERE Category = 'Database'"
            );

            if (existingSettings == 0)
            {
                // إدراج الإعدادات الافتراضية
                var defaultSettings = @"
                    INSERT INTO AppSettings (Category, SettingKey, SettingValue, DataType, DefaultValue, Description) VALUES
                    ('Database', 'ConnectionTimeout', '30', 'Integer', '30', 'مهلة الاتصال بقاعدة البيانات بالثواني'),
                    ('UI', 'Theme', 'Default', 'String', 'Default', 'سمة واجهة المستخدم'),
                    ('UI', 'Language', 'ar', 'String', 'ar', 'لغة التطبيق'),
                    ('UI', 'AutoRefresh', 'true', 'Boolean', 'true', 'التحديث التلقائي'),
                    ('UI', 'RefreshInterval', '1', 'Integer', '1', 'فترة التحديث بالثواني'),
                    ('Login', 'LastIpAddress', '***********', 'String', '***********', 'آخر عنوان IP مستخدم'),
                    ('Login', 'LastPort', '8728', 'Integer', '8728', 'آخر منفذ مستخدم'),
                    ('Login', 'LastUsername', 'admin', 'String', 'admin', 'آخر اسم مستخدم'),
                    ('Login', 'RememberMe', 'false', 'Boolean', 'false', 'تذكر بيانات تسجيل الدخول')
                ";

                connection.Execute(defaultSettings);
                System.Diagnostics.Debug.WriteLine("✅ تم إدراج الإعدادات الافتراضية");
            }
        }

        /// <summary>
        /// إعادة تعيين قاعدة البيانات (حذف وإعادة إنشاء)
        /// </summary>
        public static void ResetDatabase(string databaseType)
        {
            try
            {
                switch (databaseType.ToLower())
                {
                    case "smart":
                        ResetSmartDatabase();
                        break;
                    case "localdb":
                        ResetLocalDatabase();
                        break;
                    case "all":
                        ResetSmartDatabase();
                        ResetLocalDatabase();
                        break;
                    default:
                        throw new ArgumentException($"نوع قاعدة البيانات غير معروف: {databaseType}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تعيين قاعدة البيانات {databaseType}: {ex.Message}");
                throw;
            }
        }

        private static void ResetSmartDatabase()
        {
            using (var connection = DatabaseConnection.GetSmartConnection())
            {
                connection.Open();
                connection.Execute("DROP TABLE IF EXISTS FormStates");
                connection.Execute("DROP TABLE IF EXISTS AppSettings");
            }
            InitializeSmartDatabase();
        }

        private static void ResetLocalDatabase()
        {
            using (var connection = DatabaseConnection.GetLocalDbConnection())
            {
                connection.Open();
                connection.Execute("DROP TABLE IF EXISTS ActivityLogs");
                connection.Execute("DROP TABLE IF EXISTS LocalCards");
                connection.Execute("DROP TABLE IF EXISTS LocalPayments");
                connection.Execute("DROP TABLE IF EXISTS LocalSessions");
                connection.Execute("DROP TABLE IF EXISTS LocalUsers");
            }
            InitializeLocalDatabase();
        }
    }
}
