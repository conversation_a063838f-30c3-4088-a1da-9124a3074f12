﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Data
{
    public class UtilsSql
    {
        public static string GetInsertSql<T>()
        {
            Type t = typeof(T);
            //Type ObjType = t.GetType();
            //Type ObjType = t.GetType();

            string SqlParameter = "";
            string SqlValue = "";
            PropertyInfo[] prop = t.GetProperties();
            for (int i = 0; i < prop.Length; i++)
            {
                //Check if the property is a primay key
                object[] attrs = prop[i].GetCustomAttributes(typeof(KeyAttribute), false);
                if (attrs.Length == 0 || prop[i].Name.ToLower() != "Id")
                {

                    SqlParameter = (SqlParameter == "") ? $"[{prop[i].Name}]" : $"{SqlParameter},[{prop[i].Name}]";
                    SqlValue = (SqlValue == "") ? $"@{prop[i].Name}" : $"{SqlValue},@{prop[i].Name}";
                    //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";
                    //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";
                }

            }
            string SqlString = $"insert into [{t.Name}] ({SqlParameter})values({SqlValue})";
            return SqlString;
        }

        public static string GetInsertSql<T>(List<string> Fields,string table)
        {
            //Type t = typeof(T);
            //Type ObjType = t.GetType();
            //Type ObjType = t.GetType();

            string SqlParameter = "";
            string SqlValue = "";
            foreach (string Field in Fields) {
            //for (int i = 0; i < prop.Length; i++)
            //{
                //Check if the property is a primay key
                //object[] attrs = prop[i].GetCustomAttributes(typeof(KeyAttribute), false);
                //if (attrs.Length == 0 || prop[i].Name.ToLower() != "Id")
                //{

                    SqlParameter = (SqlParameter == "") ? $"[{Field}]" : $"{SqlParameter},[{Field}]";
                    SqlValue = (SqlValue == "") ? $"@{Field}" : $"{SqlValue},@{Field}";
                    //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";
                    //SqlValue = (SqlValue == "") ? $"'{prop[i].GetValue(model).ToString()}'" : $"{SqlValue},'{prop[i].GetValue(model).ToString()}'";
                //}

            }
            string SqlString = $"insert into [{table}] ({SqlParameter})values({SqlValue})";
            //string SqlString = $"insert into [{t.Name}] ({SqlParameter})values({SqlValue})";
            return SqlString;
        }

         public static string GetUpdateSql<T>(string tableName, List<string> Fields,string where= "where Id=@Id")
        {
            //Type t = typeof(T);
            string SqlParameter = "";
            foreach (string field in Fields)
            {
                    SqlParameter = (SqlParameter == "") ? $"[{field}]=@{field}" : $"{SqlParameter},[{field}]=@{field}";
            }
            string SqlString = $"update {tableName} set  {SqlParameter} {where}";
            //string SqlString = $"update {t.Name} set  {SqlParameter} {where}";
            return SqlString;
        }
        public static string GetUpdateAllSql<T>( string where = "Id=@Id")
        {
            Type t = typeof(T);

            string SqlParameter = "";
            PropertyInfo[] prop = t.GetProperties();
            for (int i = 0; i < prop.Length; i++)
            {
                //Check if the property is a primay key
                object[] attrs = prop[i].GetCustomAttributes(typeof(KeyAttribute), false);
                if (attrs.Length == 0 || prop[i].Name!="Id")
                {

                    SqlParameter = (SqlParameter == "") ? $"[{prop[i].Name}]=@{prop[i].Name}" : $"{SqlParameter},[{prop[i].Name}]=@{prop[i].Name}";
                }

            }
            string SqlString = $"update {t.Name} set  {SqlParameter} {where}";
            return SqlString;
        }

        private static string CreateWhereClause<T>(Expression<Func<T, bool>> predicate)
        {
            StringBuilder p = new StringBuilder(predicate.Body.ToString());
            var pName = predicate.Parameters.First();
            p.Replace(pName.Name + ".", "");
            p.Replace("==", "=");
            p.Replace("AndAlso", "and");
            p.Replace("OrElse", "or");
            p.Replace("\"", "\'");
            return p.ToString();
        }
       
        public static string SqlSelect<T>(Expression<Func<T, bool>> predicate)
        {
            Type t = typeof(T);
            Type ObjType = t.GetType();
            string command = string.Format("Select *  from {0} where {1} ", t.Name , CreateWhereClause(predicate));
            return command;
        }

        //private string CreateSelectCommand<T>(int maxCount = 0)
        //{
        //    Type t = typeof(T);
        //    Type ObjType = t.GetType();
        //    //string selectMax = maxCount > 0 ? "Limit " + maxCount.ToString()  : " ";
        //    string command = string.Format("Select *  from {0} ", t.Name);
        //    return command;
        //}        
        //private string CreateSelectCommand(int maxCount = 0)
        //{
        //    string selectMax = maxCount > 0 ? "TOP " + maxCount.ToString() + " * " : "*";
        //    string command = string.Format("Select {0} from {1}", selectMax, _tableName);
        //    return command;
        //}


    }
}
