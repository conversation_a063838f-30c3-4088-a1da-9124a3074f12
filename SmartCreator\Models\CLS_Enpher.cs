﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Models
{
    public class CLS_Enpher
    {
        public CLS_Enpher() { }
        public static char pher(char ch, int k)
        {
            if (!char.IsLetter(ch))
            {
                return ch;
            }
            char d = char.IsUpper(ch) ? 'A' : 'a';
            return (char)((((ch + k) - d) % 26) + d);
        }
        public static string Enpher(string input, int k)
        {
            string output = string.Empty;
            foreach (char ch in input)
                output += pher(ch, k);

            return output;
        }
        public static string Depher(string input, int k)
        {
            string output = string.Empty;

            foreach (char ch in input)
            {
                output += pher(ch, k);
            }
            return Enpher(input, 26 - k);
        }

    }
}
