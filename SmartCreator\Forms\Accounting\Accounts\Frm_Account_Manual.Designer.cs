﻿namespace SmartCreator.Forms.Accounting.Accounts
{
    partial class Frm_Account_Manual
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.lblAccountDetails = new SmartCreator.RJControls.RJLabel();
            this.lblAccountInfo = new SmartCreator.RJControls.RJLabel();
            this.btnAccountMovements = new SmartCreator.RJControls.RJButton();
            this.btnAccountStatement = new SmartCreator.RJControls.RJButton();
            this.btnAddChild = new SmartCreator.RJControls.RJButton();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.treeViewAccounts = new System.Windows.Forms.TreeView();
            this.panel1 = new System.Windows.Forms.Panel();
            this.txtSearch = new SmartCreator.RJControls.RJTextBox();
            this.btnEdit = new SmartCreator.RJControls.RJButton();
            this.btnAddMain = new SmartCreator.RJControls.RJButton();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.btnDelete = new SmartCreator.RJControls.RJButton();
            this.contextMenu = new System.Windows.Forms.ContextMenuStrip(this.components);
            this.addMainItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.panel2.SuspendLayout();
            this.panel1.SuspendLayout();
            this.contextMenu.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 584);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(93, 17);
            this.lblCaption.Text = "الدليل المحاسبي";
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 10;
            this.rjPanel1.Controls.Add(this.lblAccountDetails);
            this.rjPanel1.Controls.Add(this.lblAccountInfo);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(531, 6);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(450, 563);
            this.rjPanel1.TabIndex = 2;
            // 
            // lblAccountDetails
            // 
            this.lblAccountDetails.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblAccountDetails.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.lblAccountDetails.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lblAccountDetails.LinkLabel = false;
            this.lblAccountDetails.Location = new System.Drawing.Point(25, 15);
            this.lblAccountDetails.Name = "lblAccountDetails";
            this.lblAccountDetails.Size = new System.Drawing.Size(394, 35);
            this.lblAccountDetails.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lblAccountDetails.TabIndex = 170;
            this.lblAccountDetails.Text = "📋 تفاصيل الحساب المحدد:";
            this.lblAccountDetails.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lblAccountInfo
            // 
            this.lblAccountInfo.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lblAccountInfo.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.lblAccountInfo.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lblAccountInfo.LinkLabel = false;
            this.lblAccountInfo.Location = new System.Drawing.Point(25, 54);
            this.lblAccountInfo.Name = "lblAccountInfo";
            this.lblAccountInfo.Size = new System.Drawing.Size(394, 53);
            this.lblAccountInfo.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lblAccountInfo.TabIndex = 170;
            this.lblAccountInfo.Text = "📋 تفاصيل الحساب المحدد:";
            this.lblAccountInfo.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // btnAccountMovements
            // 
            this.btnAccountMovements.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAccountMovements.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAccountMovements.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAccountMovements.BorderRadius = 10;
            this.btnAccountMovements.BorderSize = 1;
            this.btnAccountMovements.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnAccountMovements.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAccountMovements.FlatAppearance.BorderSize = 0;
            this.btnAccountMovements.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAccountMovements.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAccountMovements.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAccountMovements.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold);
            this.btnAccountMovements.ForeColor = System.Drawing.Color.White;
            this.btnAccountMovements.IconChar = FontAwesome.Sharp.IconChar.Edit;
            this.btnAccountMovements.IconColor = System.Drawing.Color.White;
            this.btnAccountMovements.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAccountMovements.IconSize = 1;
            this.btnAccountMovements.Location = new System.Drawing.Point(288, 59);
            this.btnAccountMovements.Name = "btnAccountMovements";
            this.btnAccountMovements.Size = new System.Drawing.Size(184, 40);
            this.btnAccountMovements.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAccountMovements.TabIndex = 168;
            this.btnAccountMovements.Text = "📈 حركات الحساب";
            this.btnAccountMovements.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAccountMovements.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnAccountMovements.UseVisualStyleBackColor = false;
            this.btnAccountMovements.Click += new System.EventHandler(this.btnAccountMovements_Click);
            // 
            // btnAccountStatement
            // 
            this.btnAccountStatement.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAccountStatement.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAccountStatement.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAccountStatement.BorderRadius = 10;
            this.btnAccountStatement.BorderSize = 1;
            this.btnAccountStatement.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnAccountStatement.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAccountStatement.FlatAppearance.BorderSize = 0;
            this.btnAccountStatement.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAccountStatement.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAccountStatement.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAccountStatement.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold);
            this.btnAccountStatement.ForeColor = System.Drawing.Color.White;
            this.btnAccountStatement.IconChar = FontAwesome.Sharp.IconChar.Edit;
            this.btnAccountStatement.IconColor = System.Drawing.Color.White;
            this.btnAccountStatement.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAccountStatement.IconSize = 1;
            this.btnAccountStatement.Location = new System.Drawing.Point(192, 2);
            this.btnAccountStatement.Name = "btnAccountStatement";
            this.btnAccountStatement.Size = new System.Drawing.Size(164, 40);
            this.btnAccountStatement.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAccountStatement.TabIndex = 168;
            this.btnAccountStatement.Text = "📊 كشف حساب";
            this.btnAccountStatement.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAccountStatement.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnAccountStatement.UseVisualStyleBackColor = false;
            this.btnAccountStatement.Click += new System.EventHandler(this.btnAccountStatement_Click);
            // 
            // btnAddChild
            // 
            this.btnAddChild.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddChild.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddChild.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAddChild.BorderRadius = 10;
            this.btnAddChild.BorderSize = 1;
            this.btnAddChild.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnAddChild.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddChild.FlatAppearance.BorderSize = 0;
            this.btnAddChild.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnAddChild.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnAddChild.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddChild.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold);
            this.btnAddChild.ForeColor = System.Drawing.Color.White;
            this.btnAddChild.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAddChild.IconColor = System.Drawing.Color.White;
            this.btnAddChild.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddChild.IconSize = 24;
            this.btnAddChild.Location = new System.Drawing.Point(362, 2);
            this.btnAddChild.Name = "btnAddChild";
            this.btnAddChild.Size = new System.Drawing.Size(135, 40);
            this.btnAddChild.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddChild.TabIndex = 167;
            this.btnAddChild.Text = "اضافة فرعي";
            this.btnAddChild.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAddChild.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnAddChild.UseVisualStyleBackColor = false;
            this.btnAddChild.Click += new System.EventHandler(this.btnAddMainAccount_Click);
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.panel2);
            this.rjPanel2.Controls.Add(this.panel1);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(12, 6);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(513, 563);
            this.rjPanel2.TabIndex = 2;
            // 
            // panel2
            // 
            this.panel2.Controls.Add(this.treeViewAccounts);
            this.panel2.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel2.Location = new System.Drawing.Point(0, 105);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(513, 458);
            this.panel2.TabIndex = 172;
            // 
            // treeViewAccounts
            // 
            this.treeViewAccounts.Dock = System.Windows.Forms.DockStyle.Fill;
            this.treeViewAccounts.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.treeViewAccounts.FullRowSelect = true;
            this.treeViewAccounts.HideSelection = false;
            this.treeViewAccounts.Location = new System.Drawing.Point(0, 0);
            this.treeViewAccounts.Name = "treeViewAccounts";
            this.treeViewAccounts.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.treeViewAccounts.RightToLeftLayout = true;
            this.treeViewAccounts.Size = new System.Drawing.Size(513, 458);
            this.treeViewAccounts.TabIndex = 0;
            // 
            // panel1
            // 
            this.panel1.Controls.Add(this.btnAccountMovements);
            this.panel1.Controls.Add(this.btnAddChild);
            this.panel1.Controls.Add(this.btnAccountStatement);
            this.panel1.Controls.Add(this.txtSearch);
            this.panel1.Controls.Add(this.btnEdit);
            this.panel1.Controls.Add(this.btnAddMain);
            this.panel1.Controls.Add(this.btnRefresh);
            this.panel1.Controls.Add(this.btnDelete);
            this.panel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(513, 105);
            this.panel1.TabIndex = 171;
            // 
            // txtSearch
            // 
            this.txtSearch._Customizable = false;
            this.txtSearch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtSearch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtSearch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearch.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtSearch.BorderRadius = 5;
            this.txtSearch.BorderSize = 1;
            this.txtSearch.Font = new System.Drawing.Font("Segoe UI", 12F);
            this.txtSearch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtSearch.Location = new System.Drawing.Point(52, 53);
            this.txtSearch.MultiLine = false;
            this.txtSearch.Name = "txtSearch";
            this.txtSearch.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtSearch.PasswordChar = false;
            this.txtSearch.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtSearch.PlaceHolderText = "بحث";
            this.txtSearch.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtSearch.Size = new System.Drawing.Size(220, 32);
            this.txtSearch.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtSearch.TabIndex = 166;
            this.txtSearch.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btnEdit
            // 
            this.btnEdit.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEdit.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEdit.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEdit.BorderRadius = 5;
            this.btnEdit.BorderSize = 1;
            this.btnEdit.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnEdit.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnEdit.FlatAppearance.BorderSize = 0;
            this.btnEdit.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEdit.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEdit.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEdit.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold);
            this.btnEdit.ForeColor = System.Drawing.Color.White;
            this.btnEdit.IconChar = FontAwesome.Sharp.IconChar.Edit;
            this.btnEdit.IconColor = System.Drawing.Color.White;
            this.btnEdit.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEdit.IconSize = 24;
            this.btnEdit.Location = new System.Drawing.Point(87, 2);
            this.btnEdit.Name = "btnEdit";
            this.btnEdit.Size = new System.Drawing.Size(29, 34);
            this.btnEdit.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEdit.TabIndex = 168;
            this.btnEdit.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnEdit.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnEdit, "تعديل الحساب");
            this.btnEdit.UseVisualStyleBackColor = false;
            this.btnEdit.Click += new System.EventHandler(this.btnEdit_Click);
            // 
            // btnAddMain
            // 
            this.btnAddMain.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddMain.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddMain.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAddMain.BorderRadius = 5;
            this.btnAddMain.BorderSize = 1;
            this.btnAddMain.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnAddMain.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnAddMain.FlatAppearance.BorderSize = 0;
            this.btnAddMain.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnAddMain.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnAddMain.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAddMain.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold);
            this.btnAddMain.ForeColor = System.Drawing.Color.White;
            this.btnAddMain.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAddMain.IconColor = System.Drawing.Color.White;
            this.btnAddMain.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAddMain.IconSize = 24;
            this.btnAddMain.Location = new System.Drawing.Point(157, 2);
            this.btnAddMain.Name = "btnAddMain";
            this.btnAddMain.Size = new System.Drawing.Size(29, 34);
            this.btnAddMain.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAddMain.TabIndex = 167;
            this.btnAddMain.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAddMain.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnAddMain, "اضافة حساب رئيسي");
            this.btnAddMain.UseVisualStyleBackColor = false;
            this.btnAddMain.Click += new System.EventHandler(this.btnAddMainAccount_Click);
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 1;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRefresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 18;
            this.btnRefresh.Location = new System.Drawing.Point(122, 2);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(29, 34);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 169;
            this.btnRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh, "تحديث شجره الحسابات");
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderRadius = 5;
            this.btnDelete.BorderSize = 1;
            this.btnDelete.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnDelete.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btnDelete.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btnDelete.IconColor = System.Drawing.Color.White;
            this.btnDelete.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDelete.IconSize = 20;
            this.btnDelete.Location = new System.Drawing.Point(52, 2);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnDelete.Size = new System.Drawing.Size(29, 34);
            this.btnDelete.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDelete.TabIndex = 170;
            this.btnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDelete.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.toolTip1.SetToolTip(this.btnDelete, "حذف الحساب");
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // contextMenu
            // 
            this.contextMenu.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.addMainItem});
            this.contextMenu.Name = "contextMenu";
            this.contextMenu.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.contextMenu.Size = new System.Drawing.Size(186, 26);
            // 
            // addMainItem
            // 
            this.addMainItem.Name = "addMainItem";
            this.addMainItem.Size = new System.Drawing.Size(185, 22);
            this.addMainItem.Text = "➕ إضافة حساب رئيسي";
            // 
            // Frm_Account_Manual
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "الدليل المحاسبي";
            this.ClientSize = new System.Drawing.Size(1000, 634);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Frm_Account_Manual";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "الدليل المحاسبي";
            this.Load += new System.EventHandler(this.Frm_Account_Manual_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel2.ResumeLayout(false);
            this.panel2.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.contextMenu.ResumeLayout(false);
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel1;
        private RJControls.RJPanel rjPanel2;
        private System.Windows.Forms.TreeView treeViewAccounts;
        private RJControls.RJTextBox txtSearch;
        private RJControls.RJButton btnEdit;
        private RJControls.RJButton btnAddMain;
        private System.Windows.Forms.ContextMenuStrip contextMenu;
        private System.Windows.Forms.ToolStripMenuItem addMainItem;
        private RJControls.RJLabel lblAccountDetails;
        private RJControls.RJLabel lblAccountInfo;
        private RJControls.RJButton btnAddChild;
        private RJControls.RJButton btnAccountMovements;
        private RJControls.RJButton btnAccountStatement;
        private RJControls.RJButton btnDelete;
        private RJControls.RJButton btnRefresh;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Panel panel2;
        private System.Windows.Forms.Panel panel1;
    }
}