﻿using CefSharp.DevTools.Profiler;
using FontAwesome.Sharp;
using SmartCreator.Data;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_CardsDetails : RJChildForm
    {
        Sql_DataAccess Local_DB;
        UmUser Users;
        bool Is_firlLoad = true;
        bool Toggle_lastStatus = true;
        string Str_lastStaus_ON = "";
        string Str_lastStaus_OFF = "";
        string LastPassword = "";
        bool lastBind = false;
        string Lastmac = "";
        string fillter_type = "From_Server";
        List<UmPyment> Um_profiles = new List<UmPyment>();
        public Form_CardsDetails(UmUser user,string _fillter_type= "From_Server")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (UIAppearance.DGV_RTL == false)
            {
                dgv_Sessions.RightToLeft = RightToLeft.No;
                dgv_profiles.RightToLeft = RightToLeft.No;
            }
            Local_DB = new Sql_DataAccess();
            
            fillter_type = _fillter_type;
            Users = user;
            if(Global_Variable.Mk_resources.version>=7)
            {
                RemoveProfile_ToolStripMenuItem.Visible = true;
            }
            
            this.Text = "تفاصيل الكرت";
            if (UIAppearance.Language_ar==false)
            {
                this.Text = "Card Detail";
            }

            if (UIAppearance.Theme == UITheme.Light)
                lbl_note.ForeColor = utils.Dgv_DarkColor;

            Set_Font();

            utils.Control_textSize(pnlClientArea);
            utils.dgv_textSize(dgv_profiles);
            utils.dgv_textSize(dgv_Sessions);

            utils.item_Contrlol_textSize(dm_Session);
            utils.item_Contrlol_textSize(dm_profile);
            utils.tollstrip_textSize(ترتيبالاعمدةToolStripMenuItem);

        }
        private void Set_Font()
        {
            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv_profiles);
            //utils.dgv_textSize(dgv_Sessions);
            //return;


            //return;
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            foreach (var contrl in rjPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }

            foreach (var contrl in rjPanel3.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }
            //foreach (var contrl in groupBox.Controls)
            //{
            //    try
            //    {
            //        if (contrl.GetType() == typeof(RJControls.RJCheckBox))
            //        {
            //            RJCheckBox textbox = (RJCheckBox)contrl;
            //            textbox.Font = fnt;
            //        }
            //    }
            //    catch { }
            //}

            //dgv_profiles.ColumnHeaderFont = fnt;
            //dgv_Sessions.ColumnHeaderFont = fnt;
            rjLabel5.Font = rjLabel2.Font = rjLabel6.Font = rjLabel7.Font = fnt;

            Radio_Baisc.Font= rjLabel9.Font= rjLabel10.Font= Radio_WithSession.Font= rjLabel8.Font = fnt;
            //Radio_WithSession.Font = fnt;
            //rjLabel8.Font = fnt;

            //Font fntt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            Toggle_Bind_Mac.Font = fnt;
            Toggle_Status.Font= fnt;

            btnAddProfile.Font=btnSave.Font=btn_Refresh.Font= Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            dgv_profiles.AllowUserToOrderColumns = true;
            dgv_profiles.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
            dgv_profiles.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_profiles.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_profiles.ColumnHeadersHeight = (int)(40);

            dgv_Sessions.AllowUserToOrderColumns = true;
            dgv_Sessions.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
            dgv_Sessions.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv_Sessions.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_Sessions.ColumnHeadersHeight = (int)(40);


            //dgv_profiles.DefaultCellStyle.Font = new System.Drawing.Font(dgv_profiles.DefaultCellStyle.Font.FontFamily, dgv_profiles.DefaultCellStyle.Font.Size , dgv_profiles.DefaultCellStyle.Font.Style);
            //dgv_Sessions.DefaultCellStyle.Font = new System.Drawing.Font(dgv_Sessions.DefaultCellStyle.Font.FontFamily, dgv_Sessions.DefaultCellStyle.Font.Size , dgv_Sessions.DefaultCellStyle.Font.Style);


            //Control_Loop(pnlClientArea);
            //utils.Control_textSize(pnlClientArea);
        }
        
        void loadData()
        {
            string sql_where = " and where UmUser.DeleteFromServer = 0 ";
            string sql_where_session = " and  UmSession.DeleteFromServer = 0 ";
            string sql_where_Pyment = " and  UmPyment.DeleteFromServer = 0 ";



            if (fillter_type == "From_RB_Archive")
            {
                sql_where = "  ";
                sql_where_session = " ";
                sql_where_Pyment = "  ";
            }
            if(Users.DeleteFromServer==1)
            {
                btnAddProfile.Enabled= false;
                btn_Refresh.Enabled= false;
            }

            txt_username.Text = Users.UserName;
            txt_Password.Text = Users.Password;

            txt_RegDate.Text = Users.RegDate.ToString();
            txt_Till_Date.Text = Users.Str_ProfileTillTime.ToString();

            txt_TransferLimit.Text = Users.Str_TransferLimit.ToString();
            txt_TransferLeft.Text = Users.Str_ProfileTransferLeft.ToString();

            txt_TimeLeft.Text = Users.Str_ProfileTimeLeft.ToString();
            txt_uptimeLimit.Text = Users.Str_UptimeLimit.ToString();

            txt_TotalDownload.Text = Users.Str_Up_Down.ToString();
            txt_TotalUptime.Text = Users.Str_UptimeUsed.ToString();

            txt_mac.Text = Users.CallerMac;

            Toggle_Status.Checked = !Convert.ToBoolean(Users.Disabled);
            Toggle_Status.ON_Text = " مفعل  + " + Users.Str_Status;

            if (Users.Disabled == 1)
                Toggle_Status.OFF_Text = "     " + Users.Str_Status;
            else Toggle_Status.OFF_Text = "  معطل + " + Users.Str_Status;



            if (Users.CallerMac == "bind")
            {
                Toggle_Bind_Mac.Checked = true;
                lastBind = true;
                txt_mac.Text = "";
            }
            //else
            try
            {
                //var profiles = db.Select<UmPyment>($"select * from UmPyment  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_Pyment} order by Sn DESC");
                //string query = $"select * from UmPyment  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_Pyment} order by Sn DESC";
                List<UmPyment> profiles = Local_DB.Load<UmPyment>($"select * from UmPyment  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_Pyment} order by Sn DESC");

                var session = Local_DB.Load<UmSession>($"select * from UmSession  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_session} ");
                //var session = Local_DB.Load<UmSession>($"select * from UmSession  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_session} order by {OrderBy_Key} DESC");

                Um_profiles = profiles;
                //string f = $"select * from UmPyment  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_Pyment} order by Sn DESC";
                //string ff = $"select * from UmSession  where Fk_Sn_Name='{Users.Sn_Name}' {sql_where_session} order by {OrderBy_Key} DESC";
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                 (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     dgv_profiles.DataSource = profiles;
                     dgv_Sessions.DataSource = session;
                 });


            }
            catch { }
                //var session = db.Select<UmSession>(x => x.UmUserId == Users.Id);
            //}

            txt_CountProfile.Text = dgv_profiles.Rows.Count.ToString();
            txt_CountSession.Text = dgv_Sessions.Rows.Count.ToString();

            double sumPrice = 0;

            if (dgv_profiles.Rows.Count > 0)
            {
                foreach (DataGridViewRow row in dgv_profiles.Rows)
                {
                    sumPrice += Convert.ToDouble(row.Cells["Price"].Value);
                }
            }
            txt_TotalPrice.Text = sumPrice.ToString();

            //dgv_Sessions.AllowUserToOrderColumns = true;
            //dgv_profiles.AllowUserToOrderColumns = true;
            //System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            //dgv_Sessions.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv_Sessions.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_Sessions.ColumnHeadersHeight = 35;


            //dgv_profiles.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv_profiles.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv_profiles.ColumnHeadersHeight = 35;

            Init_dgv_Sessions_to_Default();
            //dgv_profiles.BorderStyle = BorderStyle.Fixed3D;



            LastPassword = Users.Password;
            //if(string.IsNullOrEmpty(Users.CallerMac))
            //    Lastmac = string.Empty;
            //else
                Lastmac = Users.CallerMac;

            //Toggle_lastStatus = !Convert.ToBoolean(Users.Disabled);

            if (Toggle_Status.Checked)
            {
                Toggle_lastStatus = true;
                Str_lastStaus_ON = Toggle_Status.ON_Text;
            }
            else
            {
                Toggle_lastStatus = false;
                Str_lastStaus_OFF = Toggle_Status.OFF_Text;
            }

            toolTip1.SetToolTip(btnSave, null);
            if (Users.DeleteFromServer == 1)
            {
                Toggle_Status.Checked = true;
                Toggle_Status.ON_Text = "محذوف من الروتر";
                btnSave.Design = ButtonDesign.Confirm;
                btnSave.TextAlign=ContentAlignment.MiddleLeft;
                btnSave.ImageAlign=ContentAlignment.MiddleRight;
                btnSave.TextImageRelation = TextImageRelation.ImageBeforeText;
                btnSave.RightToLeft = RightToLeft.Yes;
                btnSave.Text = "اضافة الي الروتر";
                toolTip1.SetToolTip(btnSave, "تصفير الكرت واعادة اضافتة الي النظام مرة اخرى");
                //btnSave.IconSize = 1;
                btnSave.IconChar=IconChar.Plus;
                Toggle_Status.Enabled = false;

                Toggle_Status.Font =btnSave.Font= Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular); 
                utils.item_Contrlol_textSize(Toggle_Status);
                utils.item_Contrlol_textSize(btnSave);


            }

            if (dgv_profiles.Rows.Count == 1)
            {
                int DeleteFromServer = Convert.ToInt32(dgv_profiles.Rows[0].Cells["DeleteFromServer"].Value);
                if (DeleteFromServer == 1)
                {
                    dgv_profiles.Rows[0].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                    dgv_profiles.Rows[0].DefaultCellStyle.SelectionForeColor = utils.Dgv_DarkColor;
                }
            }
            if (dgv_Sessions.Rows.Count == 1)
            {
                int DeleteFromServer = Convert.ToInt32(dgv_Sessions.Rows[0].Cells["DeleteFromServer"].Value);
                if (DeleteFromServer == 1)
                {
                    dgv_Sessions.Rows[0].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                    dgv_Sessions.Rows[0].DefaultCellStyle.SelectionForeColor = utils.Dgv_DarkColor;
                }
            }


        }
        private void Form_CardsDetails_Load(object sender, EventArgs e)
        {
            loadData();

            foreach (DataGridViewColumn column in dgv_profiles.Columns)
            {
                column.Visible = false;
            }

            try { dgv_profiles.Columns["Price"].Visible = true; } catch { }
            try { dgv_profiles.Columns["ProfileName"].Visible = true; } catch { }
            try { dgv_profiles.Columns["AddedDate"].Visible = true; } catch { }

            try { dgv_Sessions.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["Fk_Sn_Name"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["IdHX"].Visible = false; } catch { }
            try { dgv_Sessions.Columns["DeleteFromServer"].Visible = false; } catch { }

            Is_firlLoad = false;

            lbl_note.ForeColor = utils.Dgv_DarkColor;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            //dm_profile.Font = dgvHeader_font;
            //dm_Session.Font = dgvHeader_font;



        }


        private void Init_dgv_Sessions_to_Default()
        {
            try
            {
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{
                //    column.Visible = false;
                //}

                dgv_Sessions.Columns["UserName"].Visible = false;
                dgv_Sessions.Columns["UserName"].DisplayIndex = 1;

                dgv_Sessions.Columns["FromTime"].Visible = true;
                dgv_Sessions.Columns["FromTime"].DisplayIndex = 2;
                dgv_Sessions.Columns["FromTime"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["TillTime"].Visible = true;
                dgv_Sessions.Columns["TillTime"].DisplayIndex = 3;
                dgv_Sessions.Columns["TillTime"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_UptimeUsed"].Visible = true;
                dgv_Sessions.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                dgv_Sessions.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_DownloadUsed"].Visible = true;
                dgv_Sessions.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                dgv_Sessions.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["Str_UploadUsed"].Visible = true;
                dgv_Sessions.Columns["Str_UploadUsed"].DisplayIndex = 6;
                dgv_Sessions.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["CallingStationId"].Visible = true;
                dgv_Sessions.Columns["CallingStationId"].DisplayIndex = 7;
                dgv_Sessions.Columns["CallingStationId"].Width = utils.Control_Mesur_DPI(150);


                dgv_Sessions.Columns["IpUser"].Visible = true;
                dgv_Sessions.Columns["IpUser"].DisplayIndex = 8;
                dgv_Sessions.Columns["IpUser"].Width = utils.Control_Mesur_DPI(150);

                dgv_Sessions.Columns["NasPortId"].Visible = true;
                dgv_Sessions.Columns["NasPortId"].DisplayIndex = 9;

                dgv_Sessions.Columns["IpRouter"].Visible = true;
                dgv_Sessions.Columns["IpRouter"].DisplayIndex = 10;
                dgv_Sessions.Columns["IpRouter"].Width = utils.Control_Mesur_DPI(150);


                dgv_Sessions.Columns["Sn_Name"].Visible = false;
                dgv_Sessions.Columns["IdHX"].Visible = false;

            }
            catch { }


        }

        public  bool Is_success = false;
        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            int lastStatue_disable = Users.Disabled;
            UmUser last_User = Users;

;            Users.Password = txt_Password.Text.Trim();
            //Users.CallerMac = txt_mac.Text.Trim();
            Users.Disabled = Convert.ToInt32(!Toggle_Status.Checked);


            if (Toggle_Bind_Mac.Checked)
            {
                Users.Caller_id_yes_no = "yes";
                //if (Lastmac != Users.CallerMac)
                    Users.CallerMac = "bind";

            }
            else
            {
                if (string.IsNullOrEmpty(txt_mac.Text))
                    Users.CallerMac = "";
                else
                    Users.CallerMac = txt_mac.Text.Trim();
            }


            string res = "";
            Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
            if (Users.DeleteFromServer == 1)
            {
                HashSet<UmUser> UserTmp = mk_DataAccess.add_one_user_manager(Users);

                if (UserTmp.Count > 0)
                {
                    Users.IdHX = UserTmp.First().IdHX;
                    Users.SN = UserTmp.First().SN;
                    Users.ProfileName = UserTmp.First().ProfileName;
                    Users.DeleteFromServer = 0;
                    Users.UploadUsed = 0;
                    Users.DownloadUsed = 0;
                    Users.UptimeUsed = 0;
                    //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                    //(System.Windows.Forms.MethodInvoker)delegate ()
                    //{

                    //});


                    btn_Refresh_Click(sender, e);
                    //if (Global_Variable.Mk_resources.version >= 7)
                    //{

                    //}
                    RJMessageBox.Show("تم اضافة  الكرت الي الروتر");
                    Is_success = true;
                    this.Close();


                    //btn_Refresh_Click(sender, new EventArgs());
                }
                else
                {
                    RJMessageBox.Show("حدث مشكله اضغط علي زر تحديث من الروتر واعد المحاولة");

                }
            }
            else
            {
                //if ((LastPassword != txt_Password.Text.Trim()) ||( Lastmac != Users.CallerMac) || (Toggle_lastStatus != Toggle_Status.Checked))
                //if (LastPassword != txt_Password.Text.Trim() || Lastmac != Users.CallerMac || Toggle_lastStatus != Convert.ToBoolean(Users.Disabled))
                {
                    bool status = mk_DataAccess.Edit_one_user_manager(Users);
                    if (status)
                    {
                        string Mac_Query = "";
                        Mac_Query = ",[CallerMac]=@CallerMac ";
                        //if (Toggle_Bind_Mac.Checked)
                        //{
                        //    Mac_Query = ",[CallerMac]=@CallerMac ";
                        //}
                        //else
                        //{
                        //    if (txt_mac.Text != "")
                        //        Users.CallerMac = txt_mac.Text.Trim();
                        //    else
                        //        Users.CallerMac = "";

                        //    Mac_Query = ",[CallerMac]=@CallerMac ";
                        //}
                        int Update_Didable = Local_DB.Execute($"update UmUser set [Disabled]=@Disabled,[Password]=@Password {Mac_Query} where Sn_Name=@Sn_Name", Users);


                        //if (Toggle_lastStatus != Toggle_Status.Checked)
                        //{
                        //}
                        //if (LastPassword != txt_Password.Text.Trim())
                        //{

                        //}
                        RJMessageBox.Show("تم تعديل بيانات الكرت");
                        Is_success = true;
                        this.Close();
                    }
                    else
                    {
                        Users = last_User;
                        RJMessageBox.Show("حدث مشكله اثناء تحديث البيانات");

                    }


                    //var profiles = Local_DB.Load<UmPyment>($"select * from UmPyment where Fk_Sn_Name='{Users.Sn_Name}' ");
                    ////var profiles = db.Select<UmPyment>(x => x.Fk_Sn_Name == Users.Sn_Name);
                    //dgv_profiles.DataSource = profiles;

                    //var session = Local_DB.Load<UmSession>($"select * from UmSession where Fk_Sn_Name='{Users.Sn_Name}' ");
                    ////var session = db.Select<UmSession>(x => x.Fk_Sn_Name == Users.Sn_Name);
                    //dgv_Sessions.DataSource = session;
                    //}


                }
            }
        }

        [Obsolete]
        private void btnAddProfile_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            HashSet<UmUser> users = new HashSet<UmUser>();
            users.Add(Users);
            Form_Add_Profile_Balance frm = new Form_Add_Profile_Balance(users);
            frm.ShowDialog();

            if (frm.success == true)
            {
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    loadData();
                }

                else
                {


                    using (Form_WaitForm form = new Form_WaitForm(Refresh_Profile_User))
                    {
                        form.ShowDialog();
                    }
                    loadData();
                    return;

                    Users.ProfileName = frm.CBox_Profile.Text;
                    float price = Global_Variable.UM_Profile.Find(x => x.Name == Users.ProfileName).Price;
                    Random aa = new Random();
                    int a = aa.Next(1000000, 2000000);

                    UmPyment pyment = new UmPyment();
                    pyment.ProfileName = frm.CBox_Profile.Text;
                    pyment.AddedDate = DateTime.Now;
                    pyment.Price = price;
                    pyment.MkId = Global_Variable.Mk_resources.RB_code;
                    pyment.IdHX = "*" + a;
                    pyment.Sn = a;
                    pyment.Sn_Name = a + "-";
                    pyment.UserName = Users.UserName;
                    pyment.Fk_Sn_Name = Users.Sn_Name;
                    if (frm.Toggle_Clear_Profile.Checked == true)
                    {
                        Um_profiles.Clear();
                        loadData();
                    }
                    Um_profiles.Add(pyment);

                    dgv_profiles.DataSource = null;

                    dgv_profiles.DataSource = Um_profiles;

                    foreach (DataGridViewColumn column in dgv_profiles.Columns)
                    {
                        column.Visible = false;
                    }

                    try { dgv_profiles.Columns["Price"].Visible = true; } catch { }
                    try { dgv_profiles.Columns["ProfileName"].Visible = true; } catch { }
                    try { dgv_profiles.Columns["AddedDate"].Visible = true; } catch { }

                    //try { dgv_Sessions.Columns["Id"].Visible = false; } catch { }
                    try { dgv_Sessions.Columns["IdHX"].Visible = false; } catch { }
                    try { dgv_Sessions.Columns["DeleteFromServer"].Visible = false; } catch { }

                    txt_CountProfile.Text = dgv_profiles.Rows.Count.ToString();

                    //dgv_profiles.Rows.Add(pyment);
                }
            }
        }

        [Obsolete]
        private void btn_Refresh_Click(object sender, EventArgs e)
        {

            using (Form_WaitForm frm = new Form_WaitForm(Get_LastUpdate_User))
            {
                frm.ShowDialog();
            }
            lock (Sql_DataAccess.Lock_localDB)
            {
                try
                {
                    //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                    //{
                    //    string id = Users.Sn_Name;
                    //    Users = db.Select<UmUser>(x => x.Sn_Name == Users.Sn_Name).First();
                    //}
                    Users = Local_DB.Load<UmUser>($"select * from UmUser where Sn_Name='{Users.Sn_Name}'").First();

                }
                catch { }
            }
        }

        [Obsolete]
        private void Get_LastUpdate_User()
        {
            if (Users.DeleteFromServer == 1)
                return;
            //SourceCardsUserManager_fromMK MkSourceUM=new SourceCardsUserManager_fromMK();
            SourceCardsUserManager_fromMK.Get_UM_user(Users.IdHX, true, true);

            SourcePymentUserManager_fromMK.get_Pyment_user(Users.UserName, true);
            if (Radio_WithSession.Checked)
                SourceSessionUserManager_fromMK.Get_UM_Sessions(Users.UserName, true);

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                 (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     loadData();
                 });
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب بيانات الكرت من النظام");

        }

        [Obsolete]
        private void Refresh_Session_User()
        {
            if (Users.DeleteFromServer == 1)
                return;

            //SourceCardsUserManager_fromMK.Get_UM_user(Users.IdHX, true, true);

            //SourcePymentUserManager_fromMK.get_Pyment_user(Users.UserName, true);
            //if (Radio_WithSession.Checked)
            SourceSessionUserManager_fromMK.Get_UM_Sessions(Users.UserName, true);

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                 (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     loadData();
                 });
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب جلسات  الكرت من النظام");

        }

        [Obsolete]
        private void Refresh_Profile_User()
        {
            if (Users.DeleteFromServer == 1)
                return;
            //SourceCardsUserManager_fromMK.Get_UM_user(Users.IdHX, true, true);

            SourcePymentUserManager_fromMK.get_Pyment_user(Users.UserName, true);
            //if (Radio_WithSession.Checked)
                //SourceSessionUserManager_fromMK.Get_UM_Sessions(Users.UserName, true);

            //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
            //     (System.Windows.Forms.MethodInvoker)delegate ()
            //     {
            //         loadData();
            //     });
            //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب جلسات  الكرت من النظام");

        }

        private void Toggle_Status_CheckedChanged(object sender, EventArgs e)
        {
            //return;
            if (Is_firlLoad)
                return;

            if (Toggle_Status.Checked)
            {
                if (Toggle_lastStatus != Toggle_Status.Checked)
                    Toggle_Status.ON_Text = "    تفعيل ";
            }
            else
            {
                if (Toggle_lastStatus != Toggle_Status.Checked)
                    Toggle_Status.OFF_Text = "    تعطيل    ";
                else
                    Toggle_Status.OFF_Text = Str_lastStaus_OFF.Trim();
            }

            //if (User.Disabled == 1)
            //if (Toggle_Status.Checked)
            //{
            //    if(User.Status == 0)
            //        Toggle_Status.ON_Text = "    مفعل+انتظار  " ;
            //    else if (User.Status == 1)
            //        Toggle_Status.ON_Text = "    مفعل+نشط  ";
            //    else if (User.Status == 2)
            //        Toggle_Status.ON_Text = "    مفعل+منتهي  ";
            //    else if (User.Status == 3)
            //      Toggle_Status.ON_Text = "مفعل + مشكله بروفايل";

            //}

        }
        Color myColor = ColorTranslator.FromHtml("#55ff55");
        private void dgv_profiles_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            int DeleteFromServer = Convert.ToInt32(dgv_profiles.Rows[e.RowIndex].Cells["DeleteFromServer"].Value);
            if (DeleteFromServer == 1)
            {
                //dgv_profiles.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkRed;
                //dgv_profiles.Rows[e.RowIndex].DefaultCellStyle.ForeColor = myColor;
                dgv_profiles.Rows[e.RowIndex].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;

            }

        }
        private void dgv_Sessions_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            int DeleteFromServer = Convert.ToInt32(dgv_Sessions.Rows[e.RowIndex].Cells["DeleteFromServer"].Value);
            if (DeleteFromServer == 1)
            {
                dgv_Sessions.Rows[e.RowIndex].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
            }
        }
        DataGridViewCell ActiveCell = null;

        private void نسخToolStripMenuItem_Click(object sender, EventArgs e)
        {
             if(dgv_Sessions.Rows.Count <= 0)
                return;

            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void نسخالسطركاملToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv_Sessions.Rows.Count <= 0)
                return;
            if (this.dgv_Sessions.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv_Sessions.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        [Obsolete]
        private void تحديثالجلساتمنالروترToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            using (Form_WaitForm frm = new Form_WaitForm(Refresh_Session_User))
            {
                frm.ShowDialog();
            }
            loadData();
        }

        [Obsolete]
        private void حذفجميعجلساتالكرتToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if(dgv_Sessions.Rows.Count <= 0)
                return;
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف جلسات الكرت المحدد من اليوزمنجر");

            using (Form_WaitForm frm = new Form_WaitForm(DelectUser_ByID))
            {
                frm.ShowDialog();
            }
            loadData();
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم حذف جلسات الكرت المحدد من اليوزمنجر");

        }

        private void اضافةباقةجديدةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            btnAddProfile_Click(sender, e);
        }

        [Obsolete]
        private void حذفالباقةالمحددهToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            //typeProcess = "DeleteFromServer";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف باقة الكرت المحدد من اليوزمنجر");

            using (Form_WaitForm frm = new Form_WaitForm(Delet_Profile_ByID))
            {
                frm.ShowDialog();
            }
            loadData();
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم حذف باقة الكرت المحدد من اليوزمنجر");

        }

        [Obsolete]
        private void تحديثباقاتالكرتمنالروترToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (Users.DeleteFromServer == 1)
            {
                RJMessageBox.Show("الكرت محذوف من السيرفر");
                return;
            }
            using (Form_WaitForm frm = new Form_WaitForm(Refresh_Profile_User))
            {
                frm.ShowDialog();
            }
            loadData();
        }
        string OrderBy_Key = "Sn";
        private void OrderBy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            //Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());
            
            OrderBy_Key = elm.Tag.ToString();
            loadData();
        }
        [Obsolete]
        public void Delet_Profile_ByID()
        {
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<UmProfile> dbUser = new HashSet<UmProfile>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv_Sessions.Rows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmProfile)row.DataBoundItem);
                        _DataGridViewRow.Add(row);
                    }
                }
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmProfile> ResUsers = mk_DataAccess.Delete_UmProfile_ByID(dbUser);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                    //RJMessageBox.Show("");
                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض العناصر");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Sql_DataAccess da = new Sql_DataAccess();
                    da.Set_Delet_fromServer("UmProfile", dbUser);
                    foreach (var itm in _DataGridViewRow)
                    {

                        itm.Cells["DeleteFromServer"].Value = 1;

                    }
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }

        }


        [Obsolete]
        public void DelectUser_ByID()
        {
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<UmSession> dbUser = new HashSet<UmSession>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv_Sessions.Rows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmSession)row.DataBoundItem);
                        _DataGridViewRow.Add(row);
                    }
                }
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmSession> ResUsers = mk_DataAccess.Delete_UmSession_ByID(dbUser);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                    //RJMessageBox.Show("");
                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض العناصر");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Sql_DataAccess da = new Sql_DataAccess();
                    da.Set_Delet_fromServer("UmSession", dbUser);
                    foreach (var itm in _DataGridViewRow)
                    {

                        itm.Cells["DeleteFromServer"].Value = 1;

                    }
                    //loadData();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }

        }

        private void dgv_Sessions_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv_Sessions.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv_Sessions[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }

        private void txt_username_onTextChanged(object sender, EventArgs e)
        {
            txt_username.Text= Users.UserName;
        }

        private void Toggle_Bind_Mac_CheckedChanged(object sender, EventArgs e)
        {
            if (Toggle_Bind_Mac.Checked)
            {
                txt_mac.Enabled = false;
                txt_mac.BackColor = Color.FromArgb(171, 171, 171);
                toolTip1.SetToolTip(Toggle_Bind_Mac, "ربط الكرت بماك الجهاز عند الدخول اول مره او في المره القادمة");

                //txt_mac.BackColor = UIAppearance.dar;
            }
            else
            {
                txt_mac.Enabled = true;
                txt_mac.BackColor = UIAppearance.BackgroundColor;
                toolTip1.SetToolTip(Toggle_Bind_Mac, null);

            }
        }

        private void dgv_Sessions_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            //if (e.RowIndex >= 0)
            //{
            //    //if (e.Value.GetType()
            //    e.PaintBackground(e.CellBounds, true);
            //    TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.VerticalCenter);
            //    //TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.Default | TextFormatFlags.VerticalCenter );
            //    e.Handled = true;

            //}
        }

        private void dgv_profiles_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            //if (e.RowIndex >= 0)
            //{
            //    //if (e.Value.GetType()
            //    e.PaintBackground(e.CellBounds, true);
            //    TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.VerticalCenter);
            //    //TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.Default | TextFormatFlags.VerticalCenter );
            //    e.Handled = true;

            //}
        }
    }
}
