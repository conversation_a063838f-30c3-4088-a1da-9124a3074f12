﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    public class RJPanel : Panel
    {
        /// <summary>
        /// This control doesn't have many additional customization properties,
        /// just set the background color according to the theme set by the appearance settings,
        /// and be able to set a radius to the edge of the control.
        /// </summary>
        ///

        #region -> Fields
        private bool customizable; // Gets or sets if the appearance colors are customizable
        private int borderRadius; // Gets or sets the border radius
        private int borderSize = 0; // Gets or sets the border size
        private Color borderColor = Color.RoyalBlue; // Gets or sets the border color
        private bool ឭ;
        #endregion
        //
        // Summary:
        //     Gets or sets a value indicating whether right-to-left mirror placement is turned
        //     on. Default value is false.
        [Description("Indicates whether right-to-left mirror placement is turned on. ")]
        [DefaultValue(false)]
        [Browsable(true)]
        [Category("RJ Code Advance")]
        public bool RightToLeftLayout
        {
            get
            {
                return ឭ;
            }
            set
            {
                if (ឭ != value)
                {
                    ឭ = value;  
                    if (base.IsHandleCreated)
                    {
                        RecreateHandle();
                    }
                }
            }
        }

        #region -> Properties
        [Category("RJ Code Advance")]
        [Description("Gets or sets whether the control's appearance colors are customizable")]
        public bool Customizable
        {
            get { return customizable; }
            set { customizable = value; }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the border radius")]
        public int BorderRadius
        {
            get { return borderRadius; }
            set
            {
                borderRadius = value;
                this.Invalidate(); // Redraw the control to update the appearance of the control.
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the border size")]
        public int BorderSize
        {
            get { return borderSize; }
            set
            {
                if (value >= 0)
                {
                    borderSize = value;
                    this.Invalidate(); // Redraw the control to update the appearance.
                }
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the border color")]
        public Color BorderColor
        {
            get { return borderColor; }
            set
            {
                borderColor = value;
                this.Invalidate(); // Redraw the control to update the appearance.
            }
        }
        #endregion

        #region -> Private methods
        private void ApplyAppearanceSettings()
        {// Apply appearance settings
            if (customizable == false)
            {
                this.BackColor = Settings.UIAppearance.ItemBackgroundColor;
            }
        }
        #endregion

        #region -> Overridden methods
        protected override void OnHandleCreated(EventArgs e)
        {
            base.OnHandleCreated(e);
            ApplyAppearanceSettings();
        }
        protected override void OnPaint(PaintEventArgs e)
        {
            base.OnPaint(e);
            Utils.RoundedControl.RegionAndSmoothed(this, borderRadius, e.Graphics);

            // Draw border if borderSize > 0
            if (borderSize > 0)
            {
                using (Pen borderPen = new Pen(borderColor, borderSize))
                {
                    borderPen.Alignment = System.Drawing.Drawing2D.PenAlignment.Inset;

                    if (borderRadius > 0)
                    {
                        // Draw rounded border
                        using (System.Drawing.Drawing2D.GraphicsPath path = Utils.RoundedControl.GetRoundedGPath(this.ClientRectangle, borderRadius))
                        {
                            e.Graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.AntiAlias;
                            e.Graphics.DrawPath(borderPen, path);
                        }
                    }
                    else
                    {
                        // Draw rectangular border
                        Rectangle borderRect = new Rectangle(0, 0, this.Width - 1, this.Height - 1);
                        e.Graphics.DrawRectangle(borderPen, borderRect);
                    }
                }
            }
        }
        #endregion

    }
}
