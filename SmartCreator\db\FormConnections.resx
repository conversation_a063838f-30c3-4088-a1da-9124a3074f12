﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="rjPictureBox1.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAAKYAAACmCAYAAABQiPR3AAAABGdBTUEAALGPC/xhBQAAI3JJREFUeF7t
        nQl4VNXZx80ySSb7DgkhBAgJW0gCRJaI7KuRHVEQRUEoVRCqoiKKoiJ8KCoi+66IKyrWpba2LvWj1lZF
        RNxRcKna2n1vv/d7/2dybs7cnElmMjPhTnLu8/ye1jBz19+87znnnuU0s5nNbOHb2jGFzACmupbpzHm1
        XMEsbQIzGbkPMIiR+y9icMxoxmytZFNFg2BSrLuZx5knmd8wv63lu1r+qPBnhb82EXUfQN3/7xkcE8c/
        zLxRy1PMvcw9zDUM5IbQENlIHAEb5IN4SxhI9ygjhVNFU8X6B/OvWshB/FcB54bzBH9jpNAQWZV4MwN5
        ZzFG3FOw6QSU8uGBSemcKFw4kAJLeVVxIa2MuFLYWMZsIdgg4uUM0u4XjE5A3QPzi4w2+ZSZV0DFFVVU
        0rs/9Rs3icbNWUhnXXK5xfnXraFZ16+ly++9nxZv2l+PlQdepJufeFnLki0P1fv8hSvuEPuT1MxfIo4z
        cPw06l8zhUr7DhTnkt2ukKKio7Xn7SdqxJXCqhHWyBrAJkVUI6FMu7qb7xNIB+GkbBBMynXPqx/Qxtc+
        oU2vfyrY8usTzEna+uYXtP3wV17sOPK1YNfRb5nf1mP3se98ovv8ztr9SeRxtvGxcXycB9jM54VzvPXg
        zy3JpdSQGSJD4gAFViOsKqsRVbP1Y5CaTzJSRL8ioZRvzEWXiqiz8J69XtJBOCmbKtfe9/8QMexRJJdS
        S5GlwLcefNVL3uqJ5wppY2JjtffNhk7U5cwQptVJKiMjZJSp+f8Y3Y0TQMLTx0wQ0U8KKOWTUQfi6R5u
        S8cur5RWRtzZN90pIi2EjY2L095fBYiKipYqaTHToitTC5lfMKhRNigjRBx94QIvCWX0a60CNgVIu/PI
        NyLSQtiNrx23ZPUjskpJjzBI98OYFhVFpZBIF/9h6t0EnYhGwvAgZZWRFUUBKaqPiCrTvYyio5iIFhQp
        +yVGK6Qq44ZDHxkRTxGqqH5KCkGfYboxEZfiESXRxFNPyL6jzqaZ194mIqOR0VnoJO3e/0xdukcF9R0G
        ZVA3ExHbagY1bK8y5MhZ82nFwz8RFRaUeXQ3xuAcpKQy3ZedMcwuKKLnB8z1jOPlhJR/YKwLQMq+7v5n
        uPLyOdcef6e9CQZnA0kh6MU3301tOxarbadSzouZeMaRG9okv2EsKREl17/yHu1+1wjZEkAEve2Hh2jC
        pUspLiFBlfM9Jp1x5LaJsd7SQEq0pe19//f1LtAQuSB6rn3+DU7tw9XUDjlXMI5M6ccZq1x510vv8oUY
        KVsikHP1M7+kxNQ0KSb4kHFk1PyWESfpTk6hTb88rr0oQ8sAcqZl5ahioldTJuO4DTVxcZII8Ut3Pi5e
        k+kuyhD5nD3/CnLFW+VM8DWTwThus8QEGW3zxRsc3UUZIpubHnuR0nPa8nOOsp43g3bNNMZxm5eYUVFR
        lJqZbSJnC+OS1RtZyjbi+arPm3mVSWUct3mJKUlKTafywSNpyxsntRdqiAwQJbuefoaoP+ieM/NzJnLE
        BChzInoaQSMPCNn/rMmUnJ5J0TEx2udbi/PFdBf1oOh4t/3ELUGLepTTzOtW0463zatJpzJw/DnUpqgz
        Jaak8XNz1XuWsamZ5MrKp9OirLdAzhczo//ZVLrySUrtNZiiYur394uOjqY4dyJLmmMkdQiIjHNuWU9V
        YyZQWnYuxbhcunIkP88YajtpEZVteoPcHXrQaXWvJ50vZubAiVS+8xjzHhVfu49SywZpBQV2SYfPuIQu
        uOF2I2ozINO0jIzx/Bx00RFAyKTiSuq5/hBV7PmIeu//nDNjz8gTs2LX++LkK+//1BI0vWosxaZkcPiv
        /0sEkDQ2Ll7cICNqaIGE6N0lRUzJyCJ3SqoQURcZJRASgaXnPb8Qwab3AyfFc414MSUQFH8r3/6OkBS/
        vqjoBgvT9UTFmwYja+Ooablzrz6izTGRJXQnNy4ikNGx9MYD1GvLYRFYeu+vE1LSIsRUEVF0xzEhaLvz
        llFKj4EUk5zuM5KqqLImp2cIWXMKOlBRzwoh7KgLFrQKaaV8Y2ZfRn1G1ggBcws7WpFQpuXoRn78Eilj
        x8VbqNfmt0R0rNz3mfb5SVqcmBIRRfd+LH6RMpJmDT2X4vM6UVRsoyP6LBABpLAum7SquB3LKr3kRQ/t
        5ft/5JgXADLdAlW63sPHWuKhYoKmGykfrlcK2FgkVJEi5k1ZQmX3/op6bTvikfH+49pnpaPFimlHiLr7
        A46m74obBVFza+ZTWu8R5Mpo41dE1SHFtcsL0GCMBw2kyFLmnPZFAim1pHLoGCGLP0Ao9buQS+4XSNGk
        bEi3QJUOzWyBiqcCCdG8k9Sldz0RZUWmKbQaMe0IUfd8yPtCRD1qyYr0n3nmNBFZY5JSmyxsQ0ACiZRa
        AlH8BUKp31X3C3THbioQMCYxWQiYXFrlLSHfv2BFtNNqxdQh03/FLk9kLd/2jiVs56V7RFEg84zJlrQx
        iSlhEfdUAPHQ7GaXr/3cNVS28Q2PgHw/IGCoJdRhxPQDCCuLAqq0Utxua56n4mvuo8J5d1DuuEsoc9BU
        Su7aj+LbdKC43EKBFBkP31e7a6hQJcMx43IKxDkkl/QRwmVUT6S8aVdS/vSl1PXmp0TtGNehk6/yvk+0
        9yTcGDFDwb4THnn5IVbsRvGA5cXD3c4CI80BRWQ7MiLrgOz5514jJOp05U7tZ8o2v6ndrzymdQ5SOD4/
        SIfsUMnnrr2mU4wR0wHIiKwFsqN4AYl0/87o9hnpGDENjsSIaXAkRkyDIzFiGhyJEdPgSIyYBkdixDQ4
        EiOmwZEYMQ2OxIhpcCRGTIMjMWIaHIkR0+BIjJgGR2LENDgSI6bBkRgxHUrJjQcotXwIuTt0J3dhN0HG
        wAnU4+5D1PsBZ/Y6DyVGTAeCXulxOYWesUGY8ayWqFgXZfSvaRU/TCOmQxFj3WuvWSX99LFGTAdtrU7M
        LssfFlMtuguVVD5gPPUUqbz+XD8tDSOmQ0E6F9PbYKhwLeK6W4GUwIhpcCRGTIMjiTgxU8oGiRSnuxhD
        yyGhoMTTGuF57o5d6NQS05WRK2aV0F2MoeXgSs+VUgI8f2cv2Ye2PEx/orsYQ8ug66pnPBOW1YkZGUv2
        YUkV1FJ1F2WIfNwduqlpHDh29V0vMVEoTqsYasqaLQxESkipmT8/cpbsw+u62NRsyh4xS0wPqLtQQ+SA
        eUc9q454RUqJ82vldqJccRSblk0Fs1aIGdF0F21wLhDSldlW1B10z7cW54uJec110zhjGT8ImlYxzKR4
        h4OUjderrrQcrZAxMfVSufPFrBo9nhZtuJ96Vg8R85DLv0tkik/sWEbZoy40ad4hSBnj8zpyjTuVn5M+
        QnYu70Prfvo2tS/pwWXNCGpg718zhTa//hlt+fVJWrrjcbGovnYifL4omebjcttTWuVwaj97pUn3zYgV
        GTlVe6b5Zhl9zFkPIZc/8Bxtev1T2vPe76lDt16RJ+bWN78Qa9dgDR1cyFU7DlBBSXexcoP8nBd8MxBJ
        ZbqX0bT9RbcaUUMEJCyct5Yy+p0lomJsSmadjLpnwiBldy7vSwvX76WNr31Cu9/9nbUuUUSLKYGgm3/1
        GUfQA9R31NmUnJHZ8HIitdFUioqyjpHVf1QJk4orxCIISM/R8YkiADS2kke8281FsaG07mdHOPOd4Of3
        rdfzBC1CTAkE3frG57Tpl8dFFB08ZRalZGb5t+aNKmtyOsvqKQIkdqoTtuTGx1vsnOc6VAHT+4zyioSW
        hJDHj/ubkpFNxRVVdMcLh8Xz2frGSdrLKVv3HEGLElMFkm7jz236pSfVj7t4IRV170VJaen+L86EzynC
        xrhTWNoML2kTO5cLcXPGzokYeSFc6c0HhXS5Z82j9KoxQjwZ/SAfrlMV0J9IqCLT9KTLrqG7Xz4mngOi
        I8qPuudlp8WKqQJJtx/+itP9CSuaQlQsj5eem8eiWjfAf2qlleJGueK95JUCo5gAiePaFFoiAywVCBlA
        zuiLhNg6IHvhnNsEkEj3GSmWBILJ44i1hvjYrvQc0YAthcN5Cun4vC3xcD0ByCeBhNnt2lOXytMtEVFm
        hIg73v6N9pk0RqsQ044UVaR9rjzhJkpZqyecS22LiikxNc3/yNoY2I8ispC5VgZBrdg6ILtE9+8CdV+M
        ehzr2LrzChAIGMP7z+9cSl16e0voaSlpuoh2WqWYOqSsSP+4yYisUtjpV95IZ0ycIYTNad9BSKtrQ20J
        QD4s1prdrlBEwAE102jyomWWgEJCrmiiuS5UEuowYjYChMUDkMLWSXucbj34c7py26M0Z9U9NGb2pSLa
        llYNpDZiFVyPwFj19lRKLKMcZMNbMylcSZ/+VH7mCBo58xKacvl1XuLh2mQExHWHU0BfGDGDYM+x74S4
        O9/5hra/5Ym2iCSqwFJi+dClzFdsfVhE4qlLrrcYfeECIffA8dMbZeT582jUrO8JqdR9LN60n1YeeInu
        PfSRdUwpG85FCofzRFFm+1tfnhLxGsOI2cxImWUkVpFy+wOEklKpYL+7+Ri6Y0cSRkyDIzFiGhyJEdPg
        SIyYBkdixDQ4EiOmwTGseOQFuvTOXfT9O3ZQpnc/2+PMaKaaKWIKmWjmlG9GzBbGonvuo9PHTKSiHhWU
        kplNyemZ5E5OpYTEJIpnbP0X/sv8sZbfM/DhMPMUs4wZwsQyzb4ZMSMciDh69gLqWNZbiAgBY2Jdvjt3
        Nw5k/RfzNwbCQtTNzDAGUbVZIqoRMwK5++V3adzcRUJGRENXXHwwIjYGRP0HI6Pq08wIJqyR1IgZYdRc
        spjSsnMblbFfaRZNP7M9nTekkK6YWkp3fa+S1n+/N7129wh6Y8NIOnjjGfTEimracGkfumZ6N1o6rSsN
        6ZVL1T2yqahNEu/bZ88pRFNI+gzTjQlLBDViRgizrl8jKi6u+Hi7KBaTq9vRunkVQr7fPTKR/nRgMv3p
        8cn0lyem0N8PThX8++lp9B/mnz+cKsDf/sr/Dv7w2CTBd/zdH64cRCMq2zQkKQRFBN3KlDIhFdSI6XBQ
        hiyuPF2UHeWzUpEyfr7vbCHi3w9OEfL937PnBAWkVSW9cGQRdcpL1kmKNA9BVzDFTEgENWI6FJQjB9RM
        paTUdJah3kQFtHBCFzpxX52MOrlCBST9M0deSLplUV8qKUjRCYrKEgQdywRd/jRiOhC0O2blF4jatXw+
        EkTIV9cNE0L+VyNRuEHq/+6RSbTi/B5UnK+NoH9i5jLxTJM3I6bDQOpOz21bb9iGFBLpNRSpOlj++uQU
        envzaLp4VEddiv8LE1RqN2I6CNS40SB+2mneUu69qp+ozDhBSBVUopDin73lTOrWPtUuJ1L7c0yTau6t
        TkyU3c6au4iqJ6DH+jkC9FbHuCTd55sLnIe9gtMu200v/s9Q+htHJ50YTgFl0O8enURzx3SkeJdXExZq
        7u8wPZiA5Gx1YlYMGUXx7kSKdcUxLkFcQoIYUrHtrS+13wk3kDIuwa0+UNEO+emeGsdFyYZA9ETZ0x3n
        VVlDAz0qRWWM33K2KjExHgev7eQ1q3Tp3U+M2dF9L5zopESN+6sHxtN/n9EL4GRQ9vRRMQoocrYqMTFb
        xdxVGyijDSZjqLtpGOG4dNcTtOvd32q/Fy7QaI5XivI8AMqTqOCcihp3qICcmnInIudKxq8KUatL5TuP
        fEOYVuXeQx+LEY9g8+ufNruUaBLCq0V5/wEiJaTUPexIA+XO7YurqNS7zRMVopsYN9Pg1urEdAKogKGd
        Um0SQnPQ1w9OCEmkfHR5Ne27uj8tnlRCVSWZVNMvn8b3b0cLajrTqovKaPWcXvTTNUPok91niZq1bh+h
        AG2eN13Qk9zxXmVOyImmpAblNGI2M5Ayp6DQS0pUdPBKMRgpr57WVUiYkxZPye5YSk6IFTXkGI5Wrpho
        Af4bkoDURBelJ7tobFUePb9qcNgklWVOW4UIcvZifKZ0S8yeA4eIyoHuZhpCx8zrbvOq7KBJCLXvYCo6
        dy+opIzkOCGh3K+/xMZEWZJCoA92jAu5oJCzV0e8WvU6vwYrQ5aYhaU9xKxtuptpCA2IluKtTt3D4ZTb
        j/751FTtA/WX+5b2FxFS3a+dtnn5lN+ugOXw3VUOUS1cgqKds8xbTlkZ0qZ0S0yXK15Md6K7oYbQgE4Z
        6vvvUFV2UJbTRCRaev0KOvT2Ufr1+x/Smx9+Inj6xZdp+/376dLFP6B+1dVU0L6wnqwQdHSftnR065iQ
        yvkc19RTODorx8LKbD2ZelHTEhP0OnMEbcGMtJqbaggOREu1DRUpPNhypcqRzaOpR4c0LzkTExNp1bq7
        6NjnX9HH3/xO8OFvvqX3v/yajn72OR3++FPa/eAjNOXc86iwQ5GXoEjx2B+afVDD1h0zULCfS8Z0Ut8O
        +YyaXmK64hKofPBII2cYwGx10TF1FYDLJ5aIDry6B9gUENn8lVMFkh45fkJE0+nnz/ISFPtBhENqRzlR
        d9xAObZtrP0ctRUhLzEBpvZDg/PVaHA+2rxtey0V9BhSG9JDHS0lTZVTcuTTk7T7oUeouEuJJSdAag+V
        nDjH+eO8oiaoFzXriQnwVgQdVBE9r937lBE0SNADXe3sG4oKjy+ClRMR9NmXX6WS0q715Hxr46iQlDn9
        iZqWmDHu5Hp9ABE9k1IzqKhHOc28bjXtePtr7Y03+AZlS09XNs89RbREQ7rugYWKYOVEORSpffDw4RSr
        TI6L/WG/wcqJ76+s3/A+n7E6F1tipvc7izr9YJtYWUH+TYJfTpw7kVIzc0QUnbV8jZHUT+ztlmvmlDdL
        N7Zg5QQ/evWQV+TEfpbP6C4GrumOGQgoFmSmxFnnxbzAWNPVWGJmDpwoFtHvtubHYv2ZaK4IyX9TQRRF
        tzFIaiJp42DpE5nGES3Ra0j3oMJBQ3Lu3P+QSNs6ISWInLsfetSrzBnK8qatiQt9N6107iVmxa73qfe+
        E+J/i5fto/SqsRSToB+dB9RImlPQQfR1NNG0DnsaH9Atu9k7afiSE7KhLAn5dFJKIO+Sq6+lhIS6QIUu
        bRj3ozteIDSUzuuLWbuYEhZ7wn/32vYOFV+7j5KKKylKM1pPggqTGk3TsjwRdfiMS+iCG25vlbLa0/iS
        yaFtIvIXnZwIKguvuJKOnvhCK6QKPtO1ew/xHc93o8SQ3mDbN9GRBK9D5f1hrHTuU0wVSFq+45gQNGvo
        uRSf18mz1k3dDrXgQrCqg13WjmWVrULYqlHjvd70YLitP01EePeNHkChfC2okxNR0N/yJlJ6cnKKdS0Y
        Z44e67pj+QvE9pHO/RNTIqLo7g9qy6LPU+H8dZTed7RYGcxeo/cFZFWFTU7PEMLapR11wQIh7vL9P4q4
        5qqbHnuRlmx+UCxjKK8b5UsMKNM9IBVImZUaJ1IcJIJMoZTTnj4HDBok3gDpZFRBSlejJtohQ9F85COd
        ByamF1wWrbzvE/Gd8u2edN/uvGViaTtXRhu/RVVRpcXcPBAXa+ignCblRVk2p30RFfWsEBJjecDew8d6
        lkFhmaXQs2+60wJyr3j4JwEz55b11j7GzL7M2n+fkTXimJ179RHnkCvWHyoSHX/FtH8pqZSQ5N385u+b
        nsnVBeSKrUuZoZYTlZa5ymtBNAfh/XljZU1gL2tuWthHvKfXHcdfNOl8LxOEmDZENN37ca2oR7lsekTI
        mlszX6zvGN+2yGdNPxBQlgVSYpRrAUSWQGgVyI03L4Gi7kPdP9IzjonaNs5BnpPufCWPLB/oV6N6mwzv
        exQOOfdc2Y+SlN5I6NTRWA0dPPD4QUpOqUvnw8rbBF2ZQzrP8m42eoAJnZg6hKx7PuT9vseyvmtVpDov
        3SOEzRw0VZRXY5JSPWs11p1ci+P19SMbHfF4ksug6Fdp/26o5Xxx7VCvKLVyzVouZ36plVHFns4x4Ram
        jdEdIxBs5cyXmPCKqQOy1gnrKa+Ws7CIsDLKokiQO3auEDexqAfFY5XbtGwWOMWRAkfFxIjziklMptjU
        LHIXlBBW5VVTuT9ve+78XgUlepe3LEIpJ3qro9+l3PeCRYtFbyOdjHbOu3A2xcV5fjw4p28fCl7Mis4Z
        qpiYfrv5xWwMq0iwWxGXiwYCRWApMaJv4bw7KP/cayyZM8+YLEju2o+SS6ss3IXdmK5C9LjcOtztS8Xf
        k0v6en0epJUPpYzqSWJ/edOupPzpS6n93DXU+apdVLb5Ta/zwfmJ8+RzTqsc5vUj8ucBoiauVgTQn7JT
        52IrQoVKTkw1o0bM+Zct8lvMWXPmsph1UyGGogKEMUkY+lG7T3SFc56YgSCjr6iEKTJLynce8waSq6JL
        5N/tnxe8V7fPPR+J4+B4OK7unCSJHXtZTWr+1sjVig9AJ1/7m5dQyHnHPO/IvHHnHnr/i99oRbRz1fIb
        KMFd1zaLSWCDFROD5Gy9jSJbTCeDlC7vrb9vfDCSUYkcouc5ynWhlrNf1yyv8UGvvXNMK6GOq2+40UtM
        zEwcbEP7ZWcXGzGbi9jkDOtGY8ppTBuoeygq9sgBMSFDKOVEOylGUcpjYCwQehLZBfTFuo1bKDGp7jU1
        psoOtsnI3krAGDHDhSom5kHHlNO6h6JijxxSTBAKOVHrz8+si3bgrs1b6T0/0zi49saVXhHzwPXVQfct
        XTu33F7pM2KGC3dhd66VewQa1DPHr1Rujxz2hm9fcqYnuWjr5X3po52+X2EiUkLKqCjr4dOFc+fRW368
        9VGxp/JQ9JayV/oYI2a4cBf1tCo/WAnCHzExe4Y6DPeWtXfUe4+tkxNAaIxsvOXCMmuWjYeXDRTRCJUq
        29sVkcJRuVL37Q8Vvb278flTqWuMRRO6mDJmc4G3XbK5CCMOv3248Qdob/iedM50MVDMLocvOXEcRB7s
        A+2UKEsiRao1fQAp12/dHlAKB68ePkLpGaHvxjdxoHeljzFihouckRdQlKvuLY4/7X323jZVAwb47GAB
        OXUjGxsD6RuRMlApwYYdu7wqPqHqxmdrjficMWKGi5zRs73E9Le9Tx1B6E8HCzmysXrwYO3kBZLRZ9XQ
        hu27Ai5TqtgrPv6+/28MW/+A/2WMmOGi6Pt3UXRConXD/W3v+8nqwV7pfNqMmSxf/XSuguiJyIoIuvOB
        h8S7b7xmvOHW2+jx539Mrx97n97mIoG/jei+GF1TI2Zglue27NxuIRn/g25+cp/Mg4wRM1wULdzAYgbe
        3mdP53gv7W+3NABJ0SEDrxiPnfzS7+/5wxXLllOC0iM/FON/NB1X1jJGzHBRsuIxinHXdRGbNbyDX22Z
        wN55duEP/BsCEW5wDijTyk4cIFg5NR1XVjNGzHCBd+noNCLbMlFu9LfDA4ZUoOFcRk10zr38qqsdISfK
        tKGU094/gClgjJjhJHvELIqKrXuA/vb4hrz2qAk5A0np4SRUciKNY6JZuQ8GNfIMxogZTrosf8gzw0nt
        PQ4kneMB40HjgcvvdyntSs++0viQ2+YgFHJq0vidTCJjxAwnwaRzgAesVoTQFDRo6FAxQ0ZLkFOTxvsx
        MYwRM9zY03mg3cTQQUMtb6JtMzUtze9ht+GmqXL6SONZjNhapZgdFtxF7WYup3YzrhN0WrKNKu87rv1s
        sNjbMwNt+5PlTfuCTpjmBRWiF157/ZRHz6bI2VAax9bqxMwefj7FJHEEinPXkZBE+ecsFT3Udd8JBvR2
        V9M5HligwxHwcHULiaJChNS+95ED9NKv3zylggYip677HWOlcWytSsyye1+n2FT9kn0Y9IbhFbrvBUve
        OVd6DV0e1adtwJ0fkP6R1vFddNaQ+0Jqx5BapPfpsy6g+x97wm9JN+7aQxfN/x6NHDdONJ4HG339lVMT
        LX/BiNq43FpXxNx3gvImL9GOb+8wd42IbtrvBQkisRo1IdbTTZj7B1EWQuNB6xaxhxCQdNCwYTR8zBia
        edHFYvAYxumgHyV6n0PEXhWVlJmVTUlJSRQXHy/kxhudUFSsGpPTR7Q8g7GiJbZWl8ohSdebn6Iu1z1I
        XZZ56L7uJTHITPf5UGGPmiMq29DRLU1bFcJXapdANAA5MKIRHS8AegZBRF8dPfCdcMuJldls0fJuJpnx
        2lpl5ady32d1Iywx2vGBk9rPhRJd1Ny0sG+Tx8sg2mLtHMy6hgmuOuXVj6BNIdxy2nqqA/Gmx761SjFP
        FRgHrza4lxSkNHmkowSCYtY1zIgBSRGJMUNGYU6iT1HR83zqoAJaN69CjNlBSwGkkf8eTjltIFpaNXF1
        M2I2I/YGd4gTzDBcO5AUZVBICjDJAloA0Hb6xIpqMU0NZgTBcAiM2vz7wSmiPyWar5Bmm1nOr5h8RrsZ
        MZuZ7mtfoISCUi85QzWvuQ4ID2FBQ3MnodwaLjnRUqBOxlXLDMbFaLffMuKDmE4FM0/obqYhhKBlwFYR
        khUDe3tfcxMOOQ88+2Nqm59vnw3vXSab8bk9zWAWVzFwqvPVez2VAd0NNYQMVITypv6gnpxNaUIKNaGU
        E4PX8gsK7FL+kSliohif22zmL4z4UkK7LtRh/u1ha88z1KGTMyUxNuC3QuEgFHJCyoLCQruU/2A6Mw1K
        iQ0rBDzDiKiJck98XmcxqxlunO6GGkKHvQkJyMkLmtqMFCqCkdOHlP9hBjNeDekNbd2Y5xhLTvyKccNQ
        FjKChhd7ZQhg8oJILXOiTKlJ32Axo12b3NeGBX8g5w4GodazIyNo88CVIZ2ckAFzpWPdxVOZ2gORc8Wq
        1ZSdk6uT8lYmjQl4g5xJzAoGC07W7VQRFH0L0Y3LlEFDjA850bE4HAvaB0pjcr7y5mE6e/IUSqnfJAQg
        pVcHjaZsCLVYZwXL9NYTFB1e0bfQSBoGWE70bsoeNsOrYzFeXfozaVa4aUjOzKwscinjzWtB7RtlSgS8
        kGyInr4FBYqkmG4PM5tBVIxxMc1NwYF1lFBbj2/b0St6otzZs0MabV9SdcoE9SWn/P8K6I3ekfG7ohPI
        Zhf0Z4yngmSnVlS8B1ZFRUTF+Goja2D0uPMVKjj/hnoLDOAtkZzV7flVg09JmydmkutWqO/VVMsWBq8a
        G20SCnaTgmKNv8YlBUpExaB/pH5MyydlxSwVRlhPx+WeG16jjos3U7vzl1PG6ePIlZbj6WEfn+glpQrS
        O6aRGVuVR7fMLgvp0n6+eHR5NU0b1F5M56JOk62A1I3XjE2q5AS72SXF8mpYmNK3pAApKbpOVgxlgLAy
        umLKPsyOln/eNVR06Xohbc/1h0S5S/dAIwUpXsnKJ6njoo1CvtwxF1N61RhLQCEh3w8M8Qh0qRgIim5k
        mHJQSoqIFqpIKmXEpFeY0tA+nWEt3zCbmHaMz3ffzblBUjTQS0lrmO8zjYuqgujKDwQzo6H2L6MsHpgU
        F9FWyotZ1CBwuxnLhMQy+pbc+LiQGSKAUEkt5ZJAMhwLouHYkA3nIoXDkoVYsQKLA0jxcD2WfHydgQrI
        oMz2CINVwzAM4d+M12ekpDKSYj53dMq9b2l/Me/mK7cPE5O52iProbuG0/5rB4hJYzHJKyZRrSrJFCMY
        G5AR4JzOY3IZbdc1J2yQFL8WKSqGYcqIirUBsdoVFhbCGi66i/SNjLa18loC10oso6+UWQKp7UjJdUAk
        3XfUfQpqjyVEq5UN5yKFE9L56CHuJ1JCTC41hWnDoLkFvbwBUuVY5seMNgBAUjQ1QVTMUAxZ5WSuWSlx
        Yqw6xENaxt8hID6HnuX4no9UrXI1g/NyRIQMdJMRFTcTsqYzamSFsIgAkNb/CBsMtZJr0X0+fEC+zxgp
        4CJmIqNKiCjk68HHMrinEFRmKb9/+A1UXHzxGIM3N1VMDpPAtKhNjawyAqgRtoJBlL2MUcX9OdO0iNv8
        SOkwASnmeoR4mMEM0W88I+XDD1UKiPvRlOgDQWWWwv7GMYikobhXqozYP44TluYfp2+QVkZZu7jyxqsC
        I/LiQSNiQGSwirmfgdASKXZTgFhyP1IwgAiH4yHK4Rz6MIgkOD8pHc5ZiocKI8QLd+qTkVTNTvIeyXsj
        74e8NhmxcT2QEPcW14Jzb7UyNmWTAssHLUUGEEAKLZFiNwV1P1IwII8nzwHn47TNfo/kvbFfm4zYABKe
        4ms57bT/B8rsbkQAuHk2AAAAAElFTkSuQmCC
</value>
  </data>
  <metadata name="contextMenuStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <metadata name="imageList1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>172, 17</value>
  </metadata>
  <data name="imageList1.ImageStream" mimetype="application/x-microsoft.net.object.binary.base64">
    <value>
        ********************************************************************************
        LCBDdWx0dXJlPW5ldXRyYWwsIFB1YmxpY0tleVRva2VuPWI3N2E1YzU2MTkzNGUwODkFAQAAACZTeXN0
        ZW0uV2luZG93cy5Gb3Jtcy5JbWFnZUxpc3RTdHJlYW1lcgEAAAAERGF0YQcCAgAAAAkDAAAADwMAAADC
        BwAAAk1TRnQBSQFMAgEBAgEAAVgBAAFYAQABEAEAARABAAT/AQkBAAj/AUIBTQE2AQQGAAE2AQQCAAEo
        AwABQAMAARADAAEBAQABCAYAAQQYAAGAAgABgAMAAoABAAGAAwABgAEAAYABAAKAAgADwAEAAcAB3AHA
        AQAB8AHKAaYBAAEzBQABMwEAATMBAAEzAQACMwIAAxYBAAMcAQADIgEAAykBAANVAQADTQEAA0IBAAM5
        AQABgAF8Af8BAAJQAf8BAAGTAQAB1gEAAf8B7AHMAQABxgHWAe8BAAHWAucBAAGQAakBrQIAAf8BMwMA
        AWYDAAGZAwABzAIAATMDAAIzAgABMwFmAgABMwGZAgABMwHMAgABMwH/AgABZgMAAWYBMwIAAmYCAAFm
        AZkCAAFmAcwCAAFmAf8CAAGZAwABmQEzAgABmQFmAgACmQIAAZkBzAIAAZkB/wIAAcwDAAHMATMCAAHM
        AWYCAAHMAZkCAALMAgABzAH/AgAB/wFmAgAB/wGZAgAB/wHMAQABMwH/AgAB/wEAATMBAAEzAQABZgEA
        ATMBAAGZAQABMwEAAcwBAAEzAQAB/wEAAf8BMwIAAzMBAAIzAWYBAAIzAZkBAAIzAcwBAAIzAf8BAAEz
        AWYCAAEzAWYBMwEAATMCZgEAATMBZgGZAQABMwFmAcwBAAEzAWYB/wEAATMBmQIAATMBmQEzAQABMwGZ
        AWYBAAEzApkBAAEzAZkBzAEAATMBmQH/AQABMwHMAgABMwHMATMBAAEzAcwBZgEAATMBzAGZAQABMwLM
        AQABMwHMAf8BAAEzAf8BMwEAATMB/wFmAQABMwH/AZkBAAEzAf8BzAEAATMC/wEAAWYDAAFmAQABMwEA
        AWYBAAFmAQABZgEAAZkBAAFmAQABzAEAAWYBAAH/AQABZgEzAgABZgIzAQABZgEzAWYBAAFmATMBmQEA
        AWYBMwHMAQABZgEzAf8BAAJmAgACZgEzAQADZgEAAmYBmQEAAmYBzAEAAWYBmQIAAWYBmQEzAQABZgGZ
        AWYBAAFmApkBAAFmAZkBzAEAAWYBmQH/AQABZgHMAgABZgHMATMBAAFmAcwBmQEAAWYCzAEAAWYBzAH/
        AQABZgH/AgABZgH/ATMBAAFmAf8BmQEAAWYB/wHMAQABzAEAAf8BAAH/AQABzAEAApkCAAGZATMBmQEA
        AZkBAAGZAQABmQEAAcwBAAGZAwABmQIzAQABmQEAAWYBAAGZATMBzAEAAZkBAAH/AQABmQFmAgABmQFm
        ATMBAAGZATMBZgEAAZkBZgGZAQABmQFmAcwBAAGZATMB/wEAApkBMwEAApkBZgEAA5kBAAKZAcwBAAKZ
        Af8BAAGZAcwCAAGZAcwBMwEAAWYBzAFmAQABmQHMAZkBAAGZAswBAAGZAcwB/wEAAZkB/wIAAZkB/wEz
        AQABmQHMAWYBAAGZAf8BmQEAAZkB/wHMAQABmQL/AQABzAMAAZkBAAEzAQABzAEAAWYBAAHMAQABmQEA
        AcwBAAHMAQABmQEzAgABzAIzAQABzAEzAWYBAAHMATMBmQEAAcwBMwHMAQABzAEzAf8BAAHMAWYCAAHM
        AWYBMwEAAZkCZgEAAcwBZgGZAQABzAFmAcwBAAGZAWYB/wEAAcwBmQIAAcwBmQEzAQABzAGZAWYBAAHM
        ApkBAAHMAZkBzAEAAcwBmQH/AQACzAIAAswBMwEAAswBZgEAAswBmQEAA8wBAALMAf8BAAHMAf8CAAHM
        Af8BMwEAAZkB/wFmAQABzAH/AZkBAAHMAf8BzAEAAcwC/wEAAcwBAAEzAQAB/wEAAWYBAAH/AQABmQEA
        AcwBMwIAAf8CMwEAAf8BMwFmAQAB/wEzAZkBAAH/ATMBzAEAAf8BMwH/AQAB/wFmAgAB/wFmATMBAAHM
        AmYBAAH/AWYBmQEAAf8BZgHMAQABzAFmAf8BAAH/AZkCAAH/AZkBMwEAAf8BmQFmAQAB/wKZAQAB/wGZ
        AcwBAAH/AZkB/wEAAf8BzAIAAf8BzAEzAQAB/wHMAWYBAAH/AcwBmQEAAf8CzAEAAf8BzAH/AQAC/wEz
        AQABzAH/AWYBAAL/AZkBAAL/AcwBAAJmAf8BAAFmAf8BZgEAAWYC/wEAAf8CZgEAAf8BZgH/AQAC/wFm
        AQABIQEAAaUBAANfAQADdwEAA4YBAAOWAQADywEAA7IBAAPXAQAD3QEAA+MBAAPqAQAD8QEAA/gBAAHw
        AfsB/wEAAaQCoAEAA4ADAAH/AgAB/wMAAv8BAAH/AwAB/wEAAf8BAAL/AgAD/xwABHcB/zoAAncBGwN3
        IwAL3QUAB90BdwMbAncjAAvdBQAH3QR3ARsBdyMAC90FAAfdBnc7AAR3JAAL3QUAC90lAAvdBQAL3SUA
        C90FAAvdZQAL3QUAC90lAAvdBQAL3SUAC90FAAvd4gABQgFNAT4HAAE+AwABKAMAAUADAAEQAwABAQEA
        AQEFAAGAFwAD/wEAA/8B4AQAA/8BwAQAAeABAwHgBQAB4AEDAeAFAAHgAQMB4AUAA/8B4QQAAeABAwHg
        AQMEAAHgAQMB4AEDBAAB4AEDAeABAwQABP8EAAHgAQMB4AEDBAAB4AEDAeABAwQAAeABAwHgAQMEAAT/
        BAAE/wQABP8EAAs=
</value>
  </data>
</root>