﻿//using PhoneNumbers;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
//using static DevExpress.Utils.Drawing.Helpers.NativeMethods;

namespace SmartCreator.Forms
{
    public partial class Form_UsersInfo : RJForms.RJChildForm
    {
        bool FirstLoad = true;
        public Form_UsersInfo()
        {

            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            this.Text = "بيانات الترخيص";

            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in rjPanel1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel textbox = (RJLabel)contrl;
                        textbox.Font = fnt;
                    }
                }
                catch { }
            }

            rjLabel10.Font= rjLabel11.Font = fnt;
            combo_type.Font= combo_country.Font = fnt;
            lbl_sinec_end.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            combo_type.Font=combo_country.Font = fnt;
            lblTitle.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 13, FontStyle.Bold);
            lbl_sinec_end.ForeColor = utils.Dgv_DarkColor;

            rjLabel13.Font = lbl_agent.Font= lbl_descript.Font = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Regular);
            rjLabel11.ForeColor = utils.Dgv_DarkColor;
            lbl_descript.ForeColor = utils.Dgv_DarkColor;


            fillContryList();
            try
            {
                txt_Identfier.Text = Global_Variable.Response_api.Identity.ToString();
                txt_username.Text = Properties.Settings.Default.RigesterName;

                if (Properties.Settings.Default.NetworkName != "YEMEN")
                    txt_network_Name.Text = Properties.Settings.Default.NetworkName;

                //txt_network_Name.Text = Properties.Settings.Default.NetworkName;
                txt_mobail.Text = Properties.Settings.Default.mobail;
                txt_address.Text = Properties.Settings.Default.address;
                txt_email.Text = Properties.Settings.Default.email;
                combo_country.Text = Properties.Settings.Default.country;
                txt_RB_Code.Text = Global_Variable.Mk_resources.RB_code;
                txtK2.Text = Properties.Settings.Default.snactrue;

                

            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            combo_type.SelectedIndex = 0;

            btn_active.Select();

            utils.Control_textSize(pnlClientArea);
        }
        bool Is_actve_byCode = false;
        private void Form_UsersInfo_Load(object sender, EventArgs e)
        {
            FirstLoad = false;
            if (Global_Variable.Mk_resources.RB_SN == "" || Global_Variable.Mk_resources.RB_SN == null)
            {
                RJMessageBox.Show("erorr get router board data");
                Application.Exit();
                //return;
            }
            try
            {
                if (combo_country.Text.ToString().Trim() == "اليمن" || combo_country.Text.ToString().Trim().ToUpper() == "YEMEN")
                { combo_country.SelectedIndex = getIndexByValue(967); }
                //{ combo_country.SelectedValue = 967; }

            }
            catch { }
            try
            {
                if (combo_country.Text.ToString().Trim() == "مصر") { { combo_country.SelectedIndex = getIndexByValue(20); } }
            }
            catch { }
            try
            {
                if (combo_country.Text.ToString().Trim() == "العراق") { { combo_country.SelectedIndex = getIndexByValue(964); } }
            }
            catch { }
            try
            {
                if (combo_country.Text.ToString().Trim() == "فلسطين") { { combo_country.SelectedIndex = getIndexByValue(970); } }
            }
            catch { }
            try
            {
                if (combo_country.Text.ToString().Trim() == "السعودية") { { combo_country.SelectedIndex = getIndexByValue(966); } }
            }
            catch { }
            try
            {
                if (combo_country.Text.ToString().Trim() == "اخرى") { combo_country.SelectedIndex = -1; }
            }
            catch { }


            if (Properties.Settings.Default.isActive == true)
            {
                btn_active.Enabled = false;
                btn_byCode.Enabled = false;
                btn_move.Enabled =   false;
                pnl_active.Enabled = false;
                //txtK2.Enabled =      false;
                combo_type.Enabled = false;

                if (Properties.Settings.Default.Lsn_k_type == "desktop")
                    combo_type.SelectedIndex = 1;
          

                double ex = Properties.Settings.Default.rb_active_exp;

                //MessageBox.Show(ex.ToString());

                double v = (ex < 0 ? ex * -1 : ex);
                string strDay = " يوم";
                if (v <= 10) strDay = " ايام";

                if (Global_Variable.Response_api.Notify_finsh == true)
                    if (ex <= Global_Variable.Response_api.Notify_finsh_days && Global_Variable.Response_api.Notify_finsh)
                    //if (ex <= 40)
                    {
                        btn_active.Enabled = true;
                        btn_byCode.Enabled = true;
                        btn_move.Enabled = false;
                        pnl_active.Visible = true;
                        txtK2.Enabled = true;
                        //txtK2.Text = "";

                        string se = " متبقي " + ex + strDay;
                        lbl_sinec_end.Text = se;
                        lbl_sinec_end.Visible = true;
                        this.Text = se;
                        //btn_active.Text = "تـفعيـل";
                        //btn_byCode.Text = "تفعيل اونلاين بارسال طلب";
                        //Is_actve_byCode = true;
                        //btn_active.Enabled = true;
                    }
                if (ex < 0)
                {
                    string se = se = "منتهي منذ " + (ex < 0 ? ex * -1 : ex) + strDay;
                    lbl_sinec_end.Text = se;
                    this.Text = se;
                    btn_move.Enabled = true;
                    txtK2.Text = "";
                    combo_type.Enabled = true;
                    btn_active.Enabled = true;

                }
            }

            checkFields();
        }
        int getIndexByValue(int value)
        {
            int index = -1;
            for (int i = 0; i <= allcountry.Count; i++)
            {
                if (allcountry.ElementAt(i).Value == value)
                {
                    return i;
                }
            }

            return index;
        }
        private void checkFields()
        {
            return;
            if (Properties.Settings.Default.isActive)
            {
                if (Properties.Settings.Default.NetworkName == null || Properties.Settings.Default.NetworkName == "")
                {
                    txt_network_Name.Enabled = true;
                    btn_active.Enabled = true;
                }
                else

                { txt_network_Name.Enabled = false; txt_network_Name.Style = TextBoxStyle.MatteBorder; txt_network_Name.BorderColor = UIAppearance.TextColor; }

                if (Properties.Settings.Default.RigesterName == "" || Properties.Settings.Default.RigesterName == null)
                    txt_username.Enabled = true;
                else
                {
                    txt_username.Enabled = false;
                    txt_username.Style = TextBoxStyle.MatteBorder;
                    txt_username.BorderColor = UIAppearance.TextColor;

                    btn_active.Enabled = true;
                }

                if (Properties.Settings.Default.address == "" || Properties.Settings.Default.address == null)
                    txt_address.Enabled = true;
                else
                {
                    txt_address.Enabled = false;
                    txt_address.Style = TextBoxStyle.MatteBorder;
                    txt_address.BorderColor = UIAppearance.TextColor;

                    btn_active.Enabled = true;
                }

                //if (Settings.Default.email == "" || Settings.Default.email == null)
                //    txt_email.Enabled = true;
                //else
                //{
                bool isVemail = IsValidEmail(Properties.Settings.Default.email);
                if (isVemail == false)
                {
                    txt_email.Enabled = true;
                    btn_active.Enabled = true;
                }
                else
                {
                    txt_email.Enabled = false;
                    txt_email.Style = TextBoxStyle.MatteBorder;
                    txt_email.BorderColor = UIAppearance.TextColor;

                }

                //}

                if (Properties.Settings.Default.country == "" || Properties.Settings.Default.country == null)
                    combo_country.Enabled = true;
                else
                {
                    combo_country.Enabled = false;
                    btn_active.Enabled = true;
                }


                if (Properties.Settings.Default.mobail == "" || Properties.Settings.Default.mobail == null)
                {
                    txt_mobail.Enabled = true;
                    combo_country.Enabled = true;
                }
                else
                {

                    if (checkPhoneFromServer())
                    {
                        txt_mobail.Enabled = false;
                        //txt_mobail.Style=SetStyle
                        //txt_mobail.BorderStyle = TextBoxStyle.MatteBorder;
                        txt_mobail.Style = TextBoxStyle.MatteBorder;
                        txt_mobail.BorderColor = UIAppearance.TextColor;


                    }
                    else
                    {
                        txt_mobail.Enabled = true;
                        combo_country.Enabled = true;
                        if (Properties.Settings.Default.rb_active_exp > 40)
                            btn_active.Text = "تحديث";
                    }
                }

                if (Properties.Settings.Default.rb_active_exp > 0)
                    if ((Properties.Settings.Default.NetworkName == "" || Properties.Settings.Default.NetworkName == null) || (Properties.Settings.Default.RigesterName == "" || Properties.Settings.Default.RigesterName == null) || (Properties.Settings.Default.mobail == "" || Properties.Settings.Default.mobail == null) || (Properties.Settings.Default.address == "") || (Properties.Settings.Default.email == "" || Properties.Settings.Default.email == null) || (Properties.Settings.Default.country == "" || Properties.Settings.Default.country == null))
                    {
                        btn_active.Text = "تحديث";
                        Is_actve_byCode = false;
                        txtK2.Enabled = false;
                        btn_active.Enabled = true;

                    }
            }
            this.Refresh();

            //bool isVemail2 = IsValidEmail(Settings.Default.email);
            //if (isVemail2 == false)
            //{
            //    txt_email.Enabled = true;
            //    btn_active.Enabled = true;
            //}
            //else
            //{
            //    txt_email.Enabled = false;

            //}




        }

        public static bool IsValidEmail(string email)
        {
            string emailPattern = @"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$";

            if (string.IsNullOrEmpty(email))
                return false;

            Regex regex = new Regex(emailPattern);
            return regex.IsMatch(email);
        }

        bool checkPhoneFromServer()
        {
            bool isVaild = false;
            try
            {
                //KeyValuePair<string, int> selectedItem = (KeyValuePair<string, int>)combo_country.SelectedItem;
                //string key = selectedItem.Key;
                //int value = selectedItem.Value;
                if (combo_country.SelectedValue.ToString() == "967")
                {
                    if (txt_mobail.Text.Length == 9)
                    {
                        string f = txt_mobail.Text.Substring(0, 2);
                        //MessageBox.Show(f);
                        if (f == "78")
                            return true;
                    }
                }

                //======================================
                var regions = CultureInfo.GetCultures(CultureTypes.SpecificCultures).Select(x => new RegionInfo(x.LCID));
                var englishRegion = regions.FirstOrDefault(region => region.EnglishName.Contains(Global_Variable.CountryList.Rows[combo_country.SelectedIndex][0].ToString()));
                var countryAbbrev = englishRegion.TwoLetterISORegionName;
                //==========================================
                //var phoneNumberUtil = PhoneNumberUtil.GetInstance();
                //string phonNumberTXT = Properties.Settings.Default.mobail.Replace(txtPostCode.Text, "");
                ////string phonNumberTXT = Properties.Settings.Default.mobail.Replace(txtPostCode.Text, "");
                //var phoneNumber = phoneNumberUtil.Parse(phonNumberTXT, Global_Variable.CountryList.Rows[combo_country.SelectedIndex]["twoLetterCode"].ToString());
                ////var phoneNumber = phoneNumberUtil.Parse(phonNumberTXT, countryAbbrev);
                //var isValid = phoneNumberUtil.IsValidNumber(phoneNumber);
                //isVaild = Convert.ToBoolean(isValid);
            }
            catch (Exception ex) { /*MessageBox.Show(ex.Message);*/ }
            return isVaild;
        }
        Dictionary<string, int> allcountry = null;
        void fillContryList()
        {
            CountryCodes countrys = new CountryCodes();
            allcountry = countrys.getCountryCodes();


            combo_country.DataSource = new BindingSource(allcountry, null);
            //combo_country.DataSource = new BindingSource(allcountry.OrderBy(mapping => mapping.Key), null);
            combo_country.DisplayMember = "Key";
            combo_country.ValueMember = "Value";


            //CountryCodes countrys_Code = new CountryCodes();
            //Dictionary<string, int> allcountry_code = countrys_Code.getCountryCodes2();
            //combo_country.DataSource = new BindingSource(allcountry_code.OrderBy(mapping => mapping.Key), null);
            //combo_country.DisplayMember = "Key";
            //combo_country.ValueMember = "Value";

        }

        private void combo_country_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                txtPostCode.Text = "+" + combo_country.SelectedValue.ToString();
            }
            catch (Exception ex) { }

        }

        private void btn_active_Click(object sender, EventArgs e)
        {
            if (txt_username.Text == "")
            {
                RJMessageBox.Show("الاسم مطلوب");
                return;
            }
            if (txt_network_Name.Text == "")
            {
                RJMessageBox.Show("اسم الشبكة مطلوب");
                return;
            }

            if (txt_address.Text.Count() > 100 || txt_mobail.Text.Count() > 100 || txt_network_Name.Text.Count() > 100 || txt_username.Text.Count() > 100 || combo_country.Text.Count() > 100)
            {
                RJMessageBox.Show("اقصى عدد 100 حرف لكل مربع ");
                return;
            }

            get_acitive();

        }
        private void get_acitive()
        {
            if (txtK2.Text.Trim() == "")
            {
                RJMessageBox.Show("االرجاء ادخال كود التفعيل");
                return;
            }
            DialogResult result = RJMessageBox.Show("ملاحظة : هل انت متاكد من تفعيل البرنامج ", "ملاحظة", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            string NameCountry = "";
            try
            {
                //NameCountry = MyDataClass.CountryList.Rows[combo_country.SelectedIndex][0].ToString();
            }
            catch { }
            if (result == DialogResult.Yes)
            {
                var data = new Dictionary<string, string>
                    {
                        {"RB_SN",  Global_Variable.Mk_resources.RB_SN},
                        {"username", txt_username.Text.Trim()},
                        {"network_Name", txt_network_Name.Text.Trim()},
                        {"address", txt_address.Text.Trim()},
                        {"country", NameCountry.Trim()},
                        {"mobail", txtPostCode.Text + txt_mobail.Text.Trim()},
                        {"email", txt_email.Text.Trim()},
                        {"identif", txt_Identfier.Text},
                        {"txtK1", txt_RB_Code.Text.Trim()},
                        {"txtK2", txtK2.Text.Trim()},
                        {"APPID", Global_Variable.App_Info.APPId},
                        {"pc_code", Global_Variable.Pc_Code},
                        {"type", combo_type.SelectedIndex.ToString()},
                    };

                API_Server server = new API_Server();
                if (server.RBAcive(data))
                {
                    RJMessageBox.Show("تم التفعيل بنجاح");
                    RJMessageBox.Show("قم باعادة تشغيل البرنامج مره اخرى");
                    Environment.Exit(0);
                    Application.Exit();
                }
                else
                {
                    MessageBox.Show("خطاء في عمليه التنشيط");

                }

            }

        }

        private void txt_Identfier_onTextChanged(object sender, EventArgs e)
        {
            txt_Identfier.Text = Global_Variable.Response_api.Identity.ToString();
        }

        private void txt_RB_Code_onTextChanged(object sender, EventArgs e)
        {
            if (combo_type.SelectedIndex == 0)
                txt_RB_Code.Text = Global_Variable.Mk_resources.RB_code;
            else
                txt_RB_Code.Text = Global_Variable.Pc_Code;
        }

        private void combo_type_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (combo_type.SelectedIndex == 0)
            {
                txt_RB_Code.Text = Global_Variable.Mk_resources.RB_code;
                rjLabel1.Text = "كــــود الروتــــر";
                rjLabel1.ForeColor=UIAppearance.TextColor;
            }
            else
            {
                txt_RB_Code.Text = Global_Variable.Pc_Code;
                rjLabel1.Text = "كود الكمبيوتر";
                rjLabel1.ForeColor=utils.Dgv_DarkColor;

            }
        }

        private void btn_CopyIdentfier_Click(object sender, EventArgs e)
        {
            Clipboard.SetText(txt_Identfier.Text.Trim());
        }

        private void btn_CopyRB_Code_Click(object sender, EventArgs e)
        {
            Clipboard.SetText(txt_RB_Code.Text.Trim());
        }

 

        private void lbl_agent_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("https://smrtye.com/agent/");
            }
            catch { }
        }

        private void lbl_descript_Click(object sender, EventArgs e)
        {
            try
            {
                System.Diagnostics.Process.Start("http://smrtye.com/blog/detail/1/");
            }
            catch { }
        }
    }
}
