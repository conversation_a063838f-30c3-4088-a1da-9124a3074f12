﻿using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.EnumType;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Windows.Forms;

namespace SmartCreator.Forms.SellingPoints
{
    public partial class Form_Sales_SellingPoint : RJChildForm
    {

        Smart_DataAccess Smart_DA = null;
        Sql_DataAccess Local_DA = null;
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";
        string Server_Type = "UM";
        

    public Form_Sales_SellingPoint()
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            set_font();

            if (UIAppearance.Theme == UITheme.Dark)
            {
                
                rjPanel1.Customizable = false;
            }

        }
        public Form_Sales_SellingPoint(string srverType="UM")
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            if (srverType == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

            set_font();


        }
        private void set_font()
        {
            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel1.Customizable = false;
                pnl_side_sn.Customizable = false;

            }

           

            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            this.Text = "تقارير الحجم والوقت لليوزمنجر";
            Spanel.Width = 0;

            //if (UIAppearance.Theme == UITheme.Dark)
            //    pnl_side_sn.Customizable = false;

            if (!UIAppearance.Language_ar)
            {
                this.Text = "Reports UserManager";
                this.dgv.RightToLeft = RightToLeft.No;
            }


            string today = DateTime.Now.ToString("yyyy-MM-dd");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00").AddMonths(-2);
            rjDateTime_To.Value = Convert.ToDateTime(today + "  23:59:59");
            CheckBox_To_Date.Check = true;

            panel1_side.BackColor = UIAppearance.FormBorderColor;
            panel2_side.BackColor = UIAppearance.FormBorderColor;
            panel3_side.BackColor = UIAppearance.FormBorderColor;
            CBox_SN_Compar.SelectedIndex = 3;
            Cbox_View.SelectedIndex = 0;
            Cbox_View.label.RightToLeft = RightToLeft.Yes;
            Cbox_View.label.TextAlign = ContentAlignment.MiddleLeft;


            dgv.AllowUserToOrderColumns = true;
            dgv2.AllowUserToOrderColumns = true;
            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv2.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dgv2.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            //dgv2.ColumnHeadersHeight = 40;
            btn_.Font = btn_apply.Font = btn_Filter.Font = btn_Fix.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Bold);
            rjLabel21.Font = rjLabel3.Font = rjLabel7.Font = rjLabel5.Font = rjLabel6.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;

            rjDateTime_From.Font = rjDateTime_To.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);

            //utils.dgv_textSize(dgv);
            //utils.dgv_textSize(dgv2);
            //utils.Control_textSize(pnlClientArea);

            utils utils = new utils();
            utils.Control_textSize1(this);




        }
        private void Get_SellingPoint()
        {
            CBox_SellingPoint.DataSource = Smart_DA.Get_BindingSource_SellingPoint();
            CBox_SellingPoint.DisplayMember = "Value";
            CBox_SellingPoint.ValueMember = "Key";
            CBox_SellingPoint.SelectedIndex = 0;
            CBox_SellingPoint.Text = "";

        }
        private void Get_Batch()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                CBox_Customer.Enabled = false;
                return;
            }
            try
            {
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
                CBox_Customer.label.RightToLeft = RightToLeft.No;
                CBox_Customer.RightToLeft = RightToLeft.No;

            }
            catch { }

        }

        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);
                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
            CBox_Profile.RightToLeft = RightToLeft.No;
            CBox_Profile.label.TextAlign = ContentAlignment.MiddleCenter;
            CBox_Profile.label.RightToLeft = RightToLeft.No;

        }
        private void Get_Nas_Port()
        {
            try
            {
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Nas_Port();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.nasPortId, s.nasPortId);

                CBox_Port.DataSource = new BindingSource(comboSource, null);
                CBox_Port.DisplayMember = "Value";
                CBox_Port.ValueMember = "Key";
                CBox_Port.SelectedIndex = 0;
                CBox_Port.Text = "";
                CBox_Port.RightToLeft = RightToLeft.No;
                CBox_Port.label.RightToLeft = RightToLeft.No;



            }
            catch { }
        }

        private void Get_Radius()
        {
            try
            {
                //List<SourceSessionUserManager_FromDB> sp = SqlDataAccess.Get_Radius();
                List<SourceSessionUserManager_FromDB> sp = Smart_DA.Get_Radius();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (SourceSessionUserManager_FromDB s in sp)
                    comboSource.Add(s.ipRouter, s.ipRouter);

                CBox_Radius.DataSource = new BindingSource(comboSource, null);
                CBox_Radius.DisplayMember = "Value";
                CBox_Radius.ValueMember = "Key";
                CBox_Radius.SelectedIndex = 0;
                CBox_Radius.Text = "";
                CBox_Radius.label.RightToLeft = RightToLeft.No;
                CBox_Radius.RightToLeft = RightToLeft.No;


            }
            catch { }
        }

        private void SideMenu()
        {
            if (Spanel.Width > 50)
            {
                Spanel.Width = 0;
            }
            else
            {
                Spanel.Width = 260;
            }
        }
        void sideMenuFix()
        {
            //if (Spanel.Width >= 200)
            //{
            //    Spanel.Width = 0;

            //    panel1.Location = new Point(10, Spanel.Location.Y);
            //    //dgv.Location = new Point(10, Spanel.Location.Y);
            //    //dgv2.Location = new Point(10, Spanel.Location.Y);
            //    //rjPanel12.Location = new Point(10, Spanel.Location.Y);


            //    panel1.Width = pnlClientArea.Width - 30;
            //    //dgv.Width = pnlClientArea.Width - 30;
            //    //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

            //    //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width-pnlClientArea.Padding.Left-pnlClientArea.Padding.Right-10;
            //    //rjPanel_back_side.Location = new Point(pnlClientArea.Width - rjPanel_back_side.Width - 10, panel1.Location.Y);

            //}
            //else
            //{
            Spanel.Width = 260;
            panel1.Width = pnlClientArea.Width - Spanel.Width - 10;
            panel1.Location = new Point(Spanel.Width + 10, panel1.Location.Y - 2);
            //panel1.Location = new Point(Spanel.Width + 20, Spanel.Location.Y);

            //panel1.Width =  705;
            //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width;
            //panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

            //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);

            //}
            Spanel.Refresh();
            panel1.Refresh();
            //rjPanel2.Refresh();
            //rjPanel_btns.Refresh();

        }


        private void btn_apply_Click(object sender, EventArgs e)
        {
            get_report();
            Spanel.Width = 0;
        }

        private void ToggleButton_Detail_CheckedChanged(object sender, EventArgs e)
        {
            if (ToggleButton_Detail.Checked)
            {
                ToggleButton_Monthly.Checked = false;
                jToggleButton_Year.Checked = false;

                //pnl_size_time_count.Visible = false;
            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !jToggleButton_Year.Checked)
                    ToggleButton_Detail.Checked = true;
            }

            //lbl_avg.Visible = false;
            //txt_avg.Visible = false;

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "dd-MM-yyyy HH:mm:ss";
            rjDateTime_To.CustomFormat = "dd-MM-yyyy HH:mm:ss";

            string today = DateTime.Now.ToString("MM-dd-yyyy");
            rjDateTime_From.Value = Convert.ToDateTime(today + "  00:00:00");
        }

        private void ToggleButton_Monthly_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox_To_Date.Check = true;
            if (ToggleButton_Monthly.Checked)
            {
                ToggleButton_Detail.Checked = false;
                jToggleButton_Year.Checked = false;
                //pnl_size_time_count.Visible = true;

            }
            else
            {
                if (!ToggleButton_Detail.Checked && !jToggleButton_Year.Checked)
                    ToggleButton_Monthly.Checked = true;
            }
            //lbl_avg.Visible = true;
            //txt_avg.Visible = true;
            //lbl_avg.Text = "المتوسط اليومي";

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "MM/yyyy";
            //rjDateTime_To.CustomFormat = "MM/yyyy";
            DateTime firstDayOfMonth;
            DateTime lastDayOfMonth;
            utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            string first = firstDayOfMonth.ToString("MM-dd-yyyy");
            string last = lastDayOfMonth.ToString("MM-dd-yyyy");

            rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
            rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
        }

        private void jToggleButton_Year_CheckedChanged(object sender, EventArgs e)
        {
            CheckBox_To_Date.Check = true;
            if (jToggleButton_Year.Checked)
            {
                ToggleButton_Detail.Checked = false;
                ToggleButton_Monthly.Checked = false;
                //pnl_size_time_count.Visible = true;

            }
            else
            {
                if (!ToggleButton_Monthly.Checked && !ToggleButton_Detail.Checked)
                    jToggleButton_Year.Checked = true;
            }
            //lbl_avg.Visible = true;
            //txt_avg.Visible = true;

            //lbl_avg.Text = "المتوسط الشهري";

            rjDateTime_From.Format = DateTimePickerFormat.Custom;
            //rjDateTime_To.Format = DateTimePickerFormat.Custom;
            rjDateTime_From.CustomFormat = "yyyy";
            //rjDateTime_To.CustomFormat = "yyyy";

            //rjDateTime_From.Value = Convert.ToDateTime(first + "  00:00:00");
            //rjDateTime_To.Value = Convert.ToDateTime(last + "  23:59:59");
            rjDateTime_From.Value = new DateTime(DateTime.Now.Year, 1, 1);
            rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31);
            //rjDateTime_To.Value = new DateTime(DateTime.Now.Year, 12, 31,23,59,59);

            ////rjDateTime_From.Value = new DateTime(1,DateTime.Now.Year,1,0,0,0);
            //rjDateTime_To.Value = new DateTime(12,DateTime.Now.Year,31,23,59,59);
        }

        private void btn__Click(object sender, EventArgs e)
        {
            get_report();
        }

        private DataTable dt_ByDetails()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("SpCode", typeof(string));
            dt.Columns.Add("نقطة البيع", typeof(string));
            dt.Columns.Add("عدد الكروت", typeof(string));
            dt.Columns.Add("الكروت المباعة", typeof(string));
            dt.Columns.Add("الكروت المتبقية", typeof(string));

            dt.Columns.Add("عدد الدفعات", typeof(string));
            dt.Columns.Add("عدد الباقات", typeof(string));
           
            dt.Columns.Add("الحالة", typeof(string));
            return dt;
        }
        private DataTable dt_Sub_ByDetails()
        {
            DataTable dt = new DataTable();
            //dt.Columns.Add("SpCode", typeof(string));
            //dt.Columns.Add("نقطة البيع", typeof(string));

            dt.Columns.Add("الدفعة", typeof(string));
            dt.Columns.Add("الباقة", typeof(string));
            dt.Columns.Add("التاريخ", typeof(string));
            dt.Columns.Add("عدد الكروت", typeof(string));
            dt.Columns.Add("الكروت المباعة", typeof(string));
            dt.Columns.Add("الكروت المتبقية", typeof(string));
            
            //dt.Columns.Add("عدد الدفعات", typeof(string));
            //dt.Columns.Add("عدد الباقات", typeof(string));
            dt.Columns.Add("الحالة", typeof(string));
            return dt;
        }
        
        private string ColumnShow = "";
        private void get_report()
        {
            dgv.DataSource = null;
            string Query_firstUse = condition_detail_firstUse();
            //string Query_firstUse_ForUser = condition_detail_firstUse_For_UmUser();

            //string Price = "Price";

            //if (Toggle_Price.Checked)
                //Price = "TotalPrice";
            string Qury = @$"SELECT  u.SpCode , u.SpName ,count(u.Sn_Name) as count,u.Status ,t.CountActive ,count(DISTINCT u.BatchCardId) as CountBatch,count(DISTINCT u.ProfileName) as CountProfile FROM {TableUser} as u 
	                            INNER JOIN (SELECT u.SpCode ,count(u.Sn_Name) as CountActive ,u.Status FROM {TableUser} u LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
		                            WHERE u.SpCode IS NOT NULL AND u.Status IN(1,2) {Query_firstUse}
		                            Group BY u.SpCode  ) t
                            ON u.SpCode = t.SpCode
                            LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
                            WHERE u.SpCode IS NOT NULL {Query_firstUse}
                            Group BY u.SpCode 
                            ";
            try
            {
                Stopwatch stopwatch = new Stopwatch();
                stopwatch.Start();

                DataTable dt = dt_ByDetails();
                DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);

                stopwatch.Stop();

                foreach (DataRow itm in tbFound.Rows)
                {
                    DataRow row = dt.NewRow();
                    row["SpCode"] = itm["SpCode"];
                    row["نقطة البيع"] = itm["SpCode"]+"-"+ itm["SpName"];
                    row["عدد الكروت"] = itm["count"];
                    row["الكروت المباعة"] = itm["CountActive"];
                    row["الكروت المتبقية"] = Convert.ToInt32(itm["count"]) - Convert.ToInt32( itm["CountActive"]);
                    row["عدد الدفعات"] = itm["CountBatch"];
                    row["عدد الباقات"] = itm["CountProfile"];
                    row["الحالة"] = Get_Status_SP(Convert.ToInt32(itm["count"]), Convert.ToInt32(itm["CountActive"]));
                    dt.Rows.Add(row);
                }
                dgv.DataSource = dt;
                dgv.Columns["SpCode"].Visible = false;
                //txt_countDevice.Text = dgv.Rows.Count.ToString();
                //loadDgvState();

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            //update_select_DGV();
        }
        private string Get_Status_SP(double count,double CountActive,int alert=75,int finsh=98)
        {
            string status = "";
            //z = (y/(x/100))
            double z = CountActive / (count / 100);
            if (z >= finsh)
            {
                return status = "تم الانتهاء من بيع الكروت";
            }
            else if (z >= alert)
            {
                return status = "قرب الاتنهاء من بيع الكروت";
            }
           
            return status;
        }
        private void update_select_DGV2()
        {
            try
            {
                string ListAll = dgv2.Rows.Count.ToString();
                //if(CBox_PageCount.SelectedIndex == 0)
                // ListAll = totalRows.ToString();
                string ListSelected = dgv2.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }
        private string condition_detail_firstUse()
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            conditon_date = " and p.AddedDate >='" + str_from_Date + "' AND p.AddedDate<='" + str_to_Date + "'  ";
            //conditon_date = " and u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";


            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    //if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    //{ nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",s.NasPortId"; }

                    //if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    //{ radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",s.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }
        private string condition_Session_By_Days_for_firstUse(bool SubQuery = false)
        {
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            if (SubQuery)
            {
                DateTime d = Convert.ToDateTime(dgv.CurrentRow.Cells["التاريخ"].Value);

                str_from_Date = d.ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                str_to_Date = d.AddDays(1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
                if (jToggleButton_Year.Checked)
                    str_to_Date = d.AddMonths(1).AddDays(-1).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            }
            //if (ToggleButton_Monthly.Checked)
            //{
            //    //DateTime firstDayOfMonth;
            //    //DateTime lastDayOfMonth;
            //    //utils.GetMonthBoundaries(DateTime.Now.Month, DateTime.Now.Year, out firstDayOfMonth, out lastDayOfMonth);
            //    //From_DT=utils.DateTimeToUnixTimeStamp(firstDayOfMonth);
            //    //To_DT = utils.DateTimeToUnixTimeStamp(lastDayOfMonth);
            //}
            //if (jToggleButton_Year.Checked)
            //{
            //    //From_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 1, 1));
            //    //To_DT = utils.DateTimeToUnixTimeStamp(new DateTime(DateTime.Now.Year, 12, 31, 23, 59, 59));
            //}
            conditon_date = " WHERE s.FromTime >='" + str_from_Date + "' AND s.FromTime<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                        sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  ";

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                        batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  ";

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                        nas_port = " AND s.NasPortId='" + CBox_Port.Text.ToString() + "'  ";

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                        radius = " AND s.Radius='" + CBox_Radius.Text.ToString() + "'  ";

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                        customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  ";

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }

                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;

            return conditon;
        }

        private string condition_detail_firstUse_For_UmUser()
        {
            ColumnShow = "";
            string conditon_date = "";

            string str_from_Date = (rjDateTime_From.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);
            string str_to_Date = (rjDateTime_From.Value.Date).ToString("yyyy-MM-dd", CultureInfo.InvariantCulture) + " " + "23:59:59";

            if (CheckBox_To_Date.Checked)
                str_to_Date = (rjDateTime_To.Value).ToString("yyyy-MM-dd HH:mm:ss", CultureInfo.InvariantCulture);

            conditon_date = " WHERE u.FirsLogin >='" + str_from_Date + "' AND u.FirsLogin<='" + str_to_Date + "'  ";

            string profile = " ";
            string sp = "";
            string nas_port = "";
            string radius = "";
            string customer = "";
            string batch = "";
            string SN = "";
            try
            {
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "" || CBox_Port.Text != "" || CBox_Customer.Text != "" || CBox_Batch.Text != "" || CheckBox_SN.Checked || CBox_Radius.Text != "")
                {
                    if (CBox_Profile.SelectedIndex != 0 && CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                        profile = " AND u.ProfileName='" + CBox_Profile.Text.ToString() + "'  ";

                    if (CBox_SellingPoint.SelectedIndex != 0 && CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                    { sp = " AND u.SpCode=" + CBox_SellingPoint.SelectedValue.ToString() + "  "; ColumnShow += ",u.SpName"; }

                    if (CBox_Batch.SelectedIndex != 0 && CBox_Batch.SelectedIndex != -1 && CBox_Batch.Text != "")
                    { batch = " AND u.BatchCardId=" + CBox_Batch.SelectedValue.ToString() + "  "; ColumnShow += ",u.BatchCardId"; }

                    if (CBox_Port.SelectedIndex != 0 && CBox_Port.SelectedIndex != -1 && CBox_Port.Text != "")
                    { nas_port = " AND u.NasPortId='" + CBox_Port.Text.ToString() + "'  "; ColumnShow += ",u.NasPortId"; }

                    if (CBox_Radius.SelectedIndex != 0 && CBox_Radius.SelectedIndex != -1 && CBox_Radius.Text != "")
                    { radius = " AND u.Radius='" + CBox_Radius.Text.ToString() + "'  "; ColumnShow += ",u.Radius"; }

                    if (CBox_Customer.SelectedIndex != 0 && CBox_Customer.SelectedIndex != -1 && CBox_Customer.Text != "")
                    { customer = " AND u.CustomerName='" + CBox_Customer.Text.ToString() + "'  "; ColumnShow += ",u.CustomerName"; }

                    if (CheckBox_SN.Check && CBox_SN_Compar.SelectedIndex != 0 && CBox_SN_Compar.SelectedIndex != -1 && CBox_SN_Compar.Text != "")
                    {
                        ColumnShow += ",u.Sn";

                        if (CBox_SN_Compar.Text.ToString() == "بين")
                        {
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text) && utils.check_Filed_Intiger_with_Msg(txt_SN_End.Text))
                                SN = "AND (u.Sn BETWEEN " + txt_SN_Start.Text + " AND " + txt_SN_End.Text + ") ";
                        }

                        if (CBox_SN_Compar.Text.ToString() == "=")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn=" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == ">")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn>" + txt_SN_Start.Text + ") ";
                        if (CBox_SN_Compar.Text.ToString() == "<")
                            if (utils.check_Filed_Intiger_with_Msg(txt_SN_Start.Text))
                                SN = "AND (u.Sn <" + txt_SN_Start.Text + ") ";
                    }


                    if (ColumnShow != "")
                    {
                        char[] charsToTrim1 = { ',' };

                        ColumnShow = ColumnShow.TrimStart() + ",";
                        ColumnShow = ColumnShow.TrimStart(charsToTrim1);

                    }
                }
            }

            catch (Exception ex) { MessageBox.Show(ex.Message); }
            string conditon = conditon_date + profile + sp + nas_port + radius + customer + batch + SN;
            return conditon;
        }

        private void timer_Tick(object sender, EventArgs e)
        {
            timer.Stop();

            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Nas_Port();
            Get_Radius();
            Get_Batch();

            get_report();
            try
            {
                if (dgv.Rows.Count > 0)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[0].Cells["جهاز البث"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
            //try
            //{
            //    dgv.Rows[0].Selected = true;
            //}
            //catch { }

            rjComboBox1.SelectedIndex = 0;
        }

        private void Form_Sales_SellingPoint_Load(object sender, EventArgs e)
        {
            timer.Start();
        }
        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            try
            {
                if (e.RowIndex > -1)
                {
                    if (ToggleButton_Detail.Checked)
                        Sub_LocadData(dgv.Rows[e.RowIndex].Cells["SpCode"].Value.ToString());
                    else
                        Sub_LocadData();
                }
            }
            catch { }
        }
        private void Sub_LocadData(string _Name = "")
        {
            if (string.IsNullOrEmpty(_Name))
                return;
            try
            {
                _Name= dgv.CurrentRow.Cells["SpCode"].Value.ToString();
                dgv2.ContextMenuStrip = null;
                dgv2.DataSource = null;

                string GroupBy = "";
                string GroupBy_Btch = "";
                string Query_conditon = condition_detail_firstUse();
                string Qury = "";
                string Q_Where = "";

                if(Toggle_BatchNumber.Checked)
                {
                    GroupBy = " Group BY u.BatchCardId";
                    GroupBy_Btch = " Group BY BatchNumber";
                    Q_Where = $" AND u.BatchCardId=t.BatchCardId";
                }
                if(Toggle_ProfileName.Checked)
                {
                    GroupBy = " Group BY u.ProfileName";
                    GroupBy_Btch = " Group BY ProfileName";
                    Q_Where = $" AND  u.BatchCardId=t.BatchCardId";
                }
                if (Toggle_ProfileName.Checked && Toggle_BatchNumber.Checked)
                {
                    GroupBy = " Group BY u.ProfileName,u.BatchCardId";
                    GroupBy_Btch = " Group BY ProfileName,BatchNumber";
                    Q_Where = $" AND u.ProfileName=t.ProfileName AND u.BatchCardId=t.BatchCardId";
                } 

                Qury = @$"SELECT u.SpCode 
                                ,u.SpName 
                                ,count(u.Sn_Name) as count
                                ,t.Status 
                                ,COALESCE(t.CountActive, 0) as CountActive 
                                ,u.BatchCardId
                                ,u.ProfileName

	                                FROM {TableUser} as u 
	                                INNER JOIN 
	                                (SELECT u.SpCode ,count(u.Sn_Name) as CountActive ,u.Status,u.ProfileName,u.BatchCardId FROM {TableUser} u LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
		                                WHERE u.SpCode='{_Name}' AND u.Status IN(1,2)  {Query_conditon}
		                                {GroupBy}
	                                ) t
	                                ON u.SpCode = t.SpCode {Q_Where}
                                    LEFT JOIN {TablePyment} p ON u.Sn_Name = p.Fk_Sn_Name
	
                                WHERE u.SpCode='{_Name}' {Query_conditon}
                                {GroupBy}
                                ";
                try
                {
                    Stopwatch stopwatch = new Stopwatch();
                    stopwatch.Start();

                    DataTable dt = dt_Sub_ByDetails();
                    DataTable tbFound = Local_DA.RunSqlCommandAsDatatable(Qury);

                    stopwatch.Stop();
                    int Server = 0;//userManager
                    if (Server_Type == "HS")
                        Server = 1;
                   List<BatchCard> btch = Smart_DA.GetListAnyDB<BatchCard>($"select * from BatchCard where Server={Server} and (Rb='{Global_Variable.Mk_resources.RB_code}' or  Rb='{Global_Variable.Mk_resources.RB_SN}' ) and SpCode='{_Name}'   {GroupBy_Btch}");
                   //List<BatchCard> btch = Smart_DA.GetListAnyDB<BatchCard>($"select * from BatchCard where BatchType=0 and Rb='{Global_Variable.Mk_resources.RB_code}' and SpCode='{_Name}'   {GroupBy_Btch}");
                    foreach (DataRow itm in tbFound.Rows)
                    {
                        DataRow row = dt.NewRow();
                        row["الدفعة"] = itm["BatchCardId"];
                        row["الباقة"] = itm["ProfileName"];
                        row["التاريخ"] = Get_Date_batch(btch, itm);
                        row["عدد الكروت"] = itm["count"];
                        row["الكروت المباعة"] = itm["CountActive"];
                        row["الكروت المتبقية"] = Convert.ToInt32(itm["count"]) - Convert.ToInt32(itm["CountActive"]);
                        //row["عدد الدفعات"] = itm["CountBatch"];
                        //row["عدد الباقات"] = itm["CountProfile"];
                        row["الحالة"] = Get_Status_SP(Convert.ToInt32(itm["count"]), Convert.ToInt32(itm["CountActive"]));
                        dt.Rows.Add(row);
                    }
                    dgv2.DataSource = dt;

                    dgv2.Columns["الدفعة"].Visible = false;
                    dgv2.Columns["الباقة"].Visible = false;
                    dgv2.Columns["التاريخ"].Visible = false;
                   
                    if (Toggle_BatchNumber.Checked)
                    {
                        dgv2.Columns["الدفعة"].Visible = true;
                        dgv2.Columns["التاريخ"].Visible = true;
                    }
                    if (Toggle_ProfileName.Checked)
                    {
                        dgv2.Columns["الباقة"].Visible = true;
                    }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
                //update_select_DGV();



                //loadDgvState();
            }
            catch { }
            //update_select_DGV2();
        }
        private string Get_Date_batch(List<BatchCard> batchCard, DataRow  numberid)
        {
            string date = "";

           if(Toggle_BatchNumber.Checked)
            {
                try
                {
                    date = (from b in batchCard
                            where b.BatchNumber.ToString() == numberid["BatchCardId"].ToString()
                            select b.AddedDate.Value.Date.ToString("yyyy-MM-dd")
                        ).FirstOrDefault();
                }
                catch { }
            }
            return date;
        }

        Dgv_Header_Proprties Dgv_State_list = null;
        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if (Cbox_View.SelectedIndex == 1)
            {
                Init_dgv_to_Default();
                return;
            }

            if (Cbox_View.SelectedIndex == 1)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Session");
            else if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_RB_Archive");
            //else
            //    DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvUserManagerPrcess");

            if (DgvState == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());
            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                //SaveFromState();
                return;
            }
            //dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv2.Columns[dv.Index].Visible = dv.Visable;
                    dgv2.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;
                    dgv2.Columns[dv.Index].Width = dv.Width;
                    foreach (ToolStripMenuItem control in toolStripMenuItem1.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            if (control.Tag != null)
                                if (control.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            }

            try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv2.Columns["IdHX"].Visible = false; } catch { }
            try { dgv2.Columns["Status"].Visible = false; } catch { }
            try { dgv2.Columns["Disabled"].Visible = false; } catch { }

            try { dgv2.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv2.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv2.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv2.Columns["CallerMac"].Visible = false; } catch { }
            try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
            try { dgv2.Columns["CountSession"].Visible = false; } catch { }
        }
        private void Init_dgv_to_Default()
        {
            if (Cbox_View.SelectedIndex == 0 || Cbox_View.SelectedIndex == 2)
            {
                try
                {
                    foreach (DataGridViewColumn column in dgv2.Columns)
                    {

                        column.Visible = false;
                    }



                    //dgv.Columns["Sn"].Visible = true;
                    dgv2.Columns["Str_Status"].Visible = true;
                    dgv2.Columns["Str_Status"].DisplayIndex = 0;
                    Status_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["UserName"].Visible = true;
                    dgv.Columns["UserName"].DisplayIndex = 1;
                    UserName_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["ProfileName"].Visible = true;
                    dgv2.Columns["ProfileName"].DisplayIndex = 3;
                    Profile_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UptimeUsed"].Width = 150;
                    Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].Width = 150;

                    //dgv.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].Width = 150;

                    dgv2.Columns["Str_Up_Down"].Visible = true;
                    dgv2.Columns["Str_Up_Down"].DisplayIndex = 7;
                    dgv2.Columns["Str_Up_Down"].Width = 190;
                    Str_Up_Down_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["MoneyTotal"].Visible = true;

                    dgv2.Columns["Str_ProfileTimeLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                    dgv2.Columns["Str_ProfileTimeLeft"].Width = 150;
                    Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTransferLeft"].Visible = true;
                    dgv2.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                    dgv2.Columns["Str_ProfileTransferLeft"].Width = 150;
                    Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["Str_ProfileTillTime"].Visible = true;
                    dgv2.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                    dgv2.Columns["Str_ProfileTillTime"].Width = 150;
                    Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                    dgv2.Columns["LastSynDb"].Visible = false;
                    dgv2.Columns["LastSynDb"].DisplayIndex = 11;
                    dgv2.Columns["LastSynDb"].Width = 150;
                    //dgv.Columns["SpName"].Visible = true;

                    //dgv.Columns["Comment"].Visible = true;
                    dgv2.Columns["Comment"].Width = 150;

                    //dgv.Columns["CountProfile"].Visible = true;
                    try { dgv2.Columns["Sn_Name"].Visible = false; } catch { }
                    //try { dgv.Columns["Status "].Visible = false; } catch { }
                    //try { dgv.Columns["Disabled "].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Visible = false; } catch { }
                    try { dgv2.Columns["CountProfile"].Width = 150; } catch { }
                    try { dgv2.Columns["CountSession"].Visible = false; } catch { }
                    try { dgv2.Columns["CountSession"].Width = 150; } catch { }

                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Init_dgv_to_Default"); }
            }

            else
            {
                try
                {
                    //foreach (DataGridViewColumn column in dgv.Columns)
                    //{
                    //    column.Visible = false;
                    //}

                    dgv2.Columns["UserName"].Visible = false;
                    dgv2.Columns["UserName"].DisplayIndex = 1;

                    dgv2.Columns["FromTime"].Visible = true;
                    dgv2.Columns["FromTime"].DisplayIndex = 2;
                    dgv2.Columns["FromTime"].Width = 150;

                    dgv2.Columns["TillTime"].Visible = true;
                    dgv2.Columns["TillTime"].DisplayIndex = 3;
                    dgv2.Columns["TillTime"].Width = 150;

                    dgv2.Columns["Str_UptimeUsed"].Visible = true;
                    dgv2.Columns["Str_UptimeUsed"].DisplayIndex = 4;
                    dgv2.Columns["Str_UptimeUsed"].Width = 150;

                    dgv2.Columns["Str_DownloadUsed"].Visible = true;
                    dgv2.Columns["Str_DownloadUsed"].DisplayIndex = 5;
                    dgv2.Columns["Str_DownloadUsed"].Width = 150;

                    dgv2.Columns["Str_UploadUsed"].Visible = true;
                    dgv2.Columns["Str_UploadUsed"].DisplayIndex = 6;
                    dgv2.Columns["Str_UploadUsed"].Width = 150;

                    dgv2.Columns["CallingStationId"].Visible = true;
                    dgv2.Columns["CallingStationId"].DisplayIndex = 7;
                    dgv2.Columns["CallingStationId"].Width = 150;


                    dgv2.Columns["IpUser"].Visible = true;
                    dgv2.Columns["IpUser"].DisplayIndex = 8;
                    dgv2.Columns["IpUser"].Width = 150;

                    dgv2.Columns["NasPortId"].Visible = true;
                    dgv2.Columns["NasPortId"].DisplayIndex = 9;

                    dgv2.Columns["IpRouter"].Visible = true;
                    dgv2.Columns["IpRouter"].DisplayIndex = 10;
                    dgv2.Columns["IpRouter"].Width = 150;


                    dgv2.Columns["Sn_Name"].Visible = false;
                    dgv2.Columns["Fk_Sn_Name"].Visible = false;
                    dgv2.Columns["IdHX"].Visible = false;

                }
                catch { }

            }
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            SideMenu();
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            get_report();
        }

        private void Toggle_BatchNumber_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                Sub_LocadData(dgv.CurrentRow.Cells["SpCode"].Value.ToString());
            }
            catch { }
        }

        private void Toggle_ProfileName_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                Sub_LocadData(dgv.CurrentRow.Cells["SpCode"].Value.ToString());
            }catch { }
        }

        private void rjComboBox1_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (rjComboBox1.SelectedIndex == 1)
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }

        }
    }
}
