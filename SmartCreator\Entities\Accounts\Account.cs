﻿using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;
//using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
 


namespace SmartCreator.Entities.Accounts
{
    public class Account
    {
        [PrimaryKey]
        public int Id { get; set; }
        [Required, StringLength(150), DisplayName("رقم الحساب")]
        public string Code { get; set; }
        [Required, StringLength(250), DisplayName("اسم الحساب")]
        public string Name { get; set; }
        public string AccountType { get; set; } = "5";
        [Required, StringLength(250), DisplayName("الوصف")]
        public string Description { get; set; }
        [StringLength(250), DisplayName("الرصيد")]
        public decimal Balance { get; set; } = 0;
        public string Rb { get; set; } 
        [NotMapped]
        [Required, StringLength(100), DisplayName("النوع")]
        public string Str_AccountType
        {

            get
            {
                string txt = AccountTypeChoices.Where(p => p.Value == AccountType.ToString()).First().Text;
                return txt;
            }
        }

        public override string ToString()
        {
            return Name ?? string.Empty;
        }

        [NotMapped]
        public static  readonly List<SelectListItem> AccountTypeChoices = new List<SelectListItem>
        {
            new SelectListItem { Value = "1", Text = "مصروفات" },
            new SelectListItem { Value = "3", Text = "مصروفات عامة" },
            new SelectListItem { Value = "5", Text = "مبيعات" },
            new SelectListItem { Value = "6", Text = "مردود مبيعات" },
            new SelectListItem { Value = "9", Text = "ايرادات اخرى" }
        };

    }

    public class SelectListItem
    {
        public string Value { get; set; }
        public string Text { get; set; }
    }
}
