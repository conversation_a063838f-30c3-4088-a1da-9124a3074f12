﻿using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static iTextSharp.awt.geom.Point2D;

namespace SmartCreator.Forms.Hotspot
{
    public partial class FormAdd_Edit_Profile_Hotspot : RJForms.RJChildForm
    {
        public bool succes = false;
        public bool add = true;
        Smart_DataAccess Smart_DA;
        HSLocalProfile profile_eidt;

        public FormAdd_Edit_Profile_Hotspot()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);


            //utils.Control_textSize(pnlClientArea);
            Set_Font();
            Smart_DA = new Smart_DataAccess();

            lblTitle.Text = "Add new profile";
            this.Text = "Add new profile";
            btnSave.Text = "Add";
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "اضافة باقة جديد";
                this.Text = "اضافة باقة";
                btnSave.Text = "اضافة";
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12, FontStyle.Regular);
                //lblTitle.Font = title_font;
                //btnSave.Font = title_font;
            }
            txt_Download.Text = "0";
            txt_price_salse.Text = "0";
            txt_price_display.Text = "0";
            txt_uptime_Hour.Text = "0";
            //txt_uptime_Minut.Text = "0";
            txt_Validity.Text = "0";
            txt_precent.Text = "0";
            CBox_SizeDownload.SelectedIndex = 0;
            CBOX_precent_type.SelectedIndex = 0;

            //CBox_profile_hotspot.Text = "default";
            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";

                CBox_profile_hotspot.SelectedIndex = 0;
                CBox_profile_hotspot.Text = "";

            }
            catch { }
            timer1.Start();
        }

        public FormAdd_Edit_Profile_Hotspot(HSLocalProfile profile)
        {
            InitializeComponent();
            Set_Font();
            if (profile == null)
                return;
            Smart_DA = new Smart_DataAccess();

            profile_eidt = profile;
            lblTitle.Text = "Edit profile Hotspot";
            this.Text = "Edit profile Hotspot";
            btnSave.Text = "edit";
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "تعديل باقة الهوتسبوت ";
                this.Text = "تعديل باقة هوتسبوت";
                btnSave.Text = "تعديل";
                //System.Drawing.Font title_font = Program.GetCustomFont(Resources.Cairo_Medium, 12, FontStyle.Regular);
                //lblTitle.Font = title_font;
                //btnSave.Font = title_font;
            }
            txt_profileName.Text = profile.Name;
            txt_profileName.Enabled = false;
            //===========================
            string transfer = utils.ConvertSize_Get_En(profile.TransferLimit.ToString());
            
            if (transfer.Contains("GB"))
                CBox_SizeDownload.SelectedIndex = 1;
            else
                CBox_SizeDownload.SelectedIndex = 0;

            txt_Download.Text = utils.ConvertSize_Get_Without_Uint(profile.TransferLimit.ToString());

            //if (transfer.Contains("."))
            //{
            //    CBox_SizeDownload.SelectedIndex = 0;
            //    txt_Download.Text = utils.ConvertSize_Get_InMB_without_Uint(profile.transferLimit.ToString());
            //}
            //else
            //{
            //    if (transfer.Contains("GB"))
            //        CBox_SizeDownload.SelectedIndex = 1;
            //    else
            //        CBox_SizeDownload.SelectedIndex = 0;

            //    txt_Download.Text = utils.ConvertSize_Get_Without_Uint(profile.transferLimit.ToString());

            //}
            //======================================== 

            int hour_uptime = ((Int32)((Convert.ToInt32(profile.UptimeLimit)) / 3600));
            txt_uptime_Hour.Text = hour_uptime.ToString();

            int minut_uptime = ((int)(Convert.ToInt32(profile.UptimeLimit) / 60 % 60));
            if (minut_uptime > 0)
                txt_uptime_Hour.Text = (hour_uptime + "." + minut_uptime).ToString();

            //txt_uptime_Hour.Text = ((Int32)((Convert.ToInt32(profile.uptimeLimit)) / 3600)).ToString();
            //txt_uptime_Minut.Text = ((Int32)(Convert.ToInt32(profile.uptimeLimit) / 60 % 60)).ToString();
            txt_Validity.Text = profile.Validity.ToString();
            CheckBox_SmartScript.Checked = Convert.ToBoolean(profile.Add_Smart_Scripts);
            CheckBox_Save_time.Checked = Convert.ToBoolean(profile.Save_time);
            CheckBox_Save_download.Checked = Convert.ToBoolean(profile.Save_download);
            CheckBox_Save_session.Checked = Convert.ToBoolean(profile.Save_session);
            CheckBox_byDayOrHour.Checked = Convert.ToBoolean(profile.ByDayOrHour);
            txt_price_salse.Text = profile.Price.ToString();
            txt_price_display.Text = profile.Price_Display.ToString();
            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";
                CBox_profile_hotspot.Text = "";

            }
            catch { }
            if (profile.Link_hotspot_profile != null)
                CBox_profile_hotspot.Text = profile.Link_hotspot_profile.ToString();

            if (profile.Link_hotspot_profile.ToString() != null)
            if (profile.Link_hotspot_profile.ToString() != "")
                rjCheckBox_group.Checked = true;

            txt_precent.Text = "0";

            CBOX_precent_type.SelectedIndex = profile.PercentageType;
            txt_precent.Text = profile.Percentage.ToString();
            rjCheckBox_precent.Check = Convert.ToBoolean(profile.Is_percentage);

        }

        private void Set_Font()
        {
            
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        //lbl.Font = CustomFonts.Get_Custom_Font("DroidSansArabic", 8f, false, GraphicsUnit.Point, 0);
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }

            foreach (var contrl in groupBox1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }

            CBox_SizeDownload.Font = fnt;
            //rjLabel11.Font = fnt;
            //rjLabel3.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 8.25f, FontStyle.Bold);
            //rjLabel3.ForeColor=Color.Blue;
            CBox_SizeDownload.Font = fnt;
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12 , FontStyle.Bold);
            lblTitle.Font = title_font;
            btnSave.Font = title_font;
            //btnAddLimit.Font = title_font;

            rjLabel1.Font = rjLabel12.Font =
                rjLabel2.Font = rjLabel26.Font
                = CheckBox_byDayOrHour.Font =
                CheckBox_Save_download.Font =
                CheckBox_Save_download.Font =
                CheckBox_Save_session.Font =
                //CheckBox_SmartScript.Font=
                fnt;
                 //Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            CheckBox_SmartScript.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9   , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);
            return;

        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }


        private void btnSave_Click(object sender, EventArgs e)
        {
            if (check() == false)
                return;
            succes = true;
            this.Close();
            RJMessageBox.Show("تمت العملية بنجاح");
        }

        bool check()
        {
            if (txt_profileName.Text == "")
            {
                RJMessageBox.Show("اكتب اسم البروفايل");
                return false;
            }

            //var item = Global_Variable.Hotspot_Profile.SingleOrDefault(x => x.Name == txt_profileName.Text.Trim());
            //if (item != null && add)
            //{
            //    RJMessageBox.Show("اسم البروفايل مكرر");
            //    return false;
            //}

            int number2;
            float number5;
            if (!(int.TryParse(txt_Validity.Text, out number2)))
            {
                RJMessageBox.Show("الصلاحية يجب ان تكون رقم");
                return false;
            }
            if (!(float.TryParse(txt_uptime_Hour.Text, out number5)))
            {
                RJMessageBox.Show("الوقت ساعات يجب ان تكون رقم صحيح او رقم عشري");
                return false;
            }
            //if (!(int.TryParse(txt_uptime_Minut.Text, out number2)))
            //{
            //    RJMessageBox.Show("الوقت بالدقائق يجب ان تكون رقم");
            //    return false;
            //}
            if (!(float.TryParse(txt_Download.Text, out number5)))
            {
                RJMessageBox.Show("كمية التحميل  يجب ان تكون رقم صحيح او رقم عشري");
                return false;
            }
            if (!(float.TryParse(txt_price_salse.Text, out float number62)))
            {
                RJMessageBox.Show("السعر يجب ان تكون رقم");
                return false;
            }
            if (!(float.TryParse(txt_precent.Text, out float numberf62)))
            {
                RJMessageBox.Show("السعر يجب ان تكون رقم صحيح او رقم عشري");
                return false;
            }
            //if (!(int.TryParse(txt_price_display.Text, out number2)))
            //{
            //    RJMessageBox.Show("سعر العرض يجب ان تكون رقم");
            //    return false;
            //}

            double downloadSize = 0;
            double downloadSpeed = 0;
            double UploadSpeed = 0;

            //=====================================
            if (CBox_SizeDownload.SelectedIndex == 0)
                downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024);
            if (CBox_SizeDownload.SelectedIndex == 1)
                downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024 * 1024);
            if (CBox_SizeDownload.SelectedIndex == -1 && txt_Download.Text != "0")
            {
                RJMessageBox.Show("اختر وحدة الحجم");
                return false;
            }
            //=====================================


            //======================================
            //HSLocalProfile prf = new HSLocalProfile();
            HSLocalProfile Localprf = new HSLocalProfile();

            Localprf.Name = txt_profileName.Text.Trim();
            Localprf.Price = Convert.ToInt32(txt_price_salse.Text);
            Localprf.Price_Display = (txt_price_display.Text);
            Localprf.TransferLimit = downloadSize;
            if (txt_uptime_Hour.Text.Contains("."))
            {
                string[] split = txt_uptime_Hour.Text.Split(new string[] { "." }, StringSplitOptions.None);
                if (split.Length > 0)
                {
                    Localprf.UptimeLimit = (Convert.ToDouble(split[0]) * 60 * 60) + (Convert.ToInt32(split[1]) * 60);
                }
            }
            else
                Localprf.UptimeLimit = (Convert.ToDouble(txt_uptime_Hour.Text) * 60 * 60);

            //prf.uptimeLimit = (Convert.ToDouble(txt_uptime_Hour.Text) *60*60)+(Convert.ToInt32(txt_uptime_Minut.Text)*60);
            Localprf.Add_Smart_Scripts = Convert.ToInt16(CheckBox_SmartScript.Checked);
            //prf.Validity_str = txt_Validity.Text + "d";
            Localprf.Validity = Convert.ToInt32(txt_Validity.Text);
            Localprf.Save_time = Convert.ToInt16(CheckBox_Save_time.Check);
            Localprf.Save_download = Convert.ToInt16(CheckBox_Save_download.Check);
            Localprf.Save_session = Convert.ToInt16(CheckBox_Save_session.Check);
            Localprf.ByDayOrHour = Convert.ToInt16(CheckBox_byDayOrHour.Check);
            Localprf.Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check);
            Localprf.Percentage = (float)Convert.ToDouble(txt_precent.Text);
            Localprf.PercentageType = CBOX_precent_type.SelectedIndex;
            Localprf.Rb = Global_Variable.Mk_resources.RB_SN;

            if (rjCheckBox_group.Checked)
                Localprf.Link_hotspot_profile = CBox_profile_hotspot.Text;
          
           

            lock (Smart_DataAccess.Lock_object)
            {
                var check = Smart_DA.Load<HSLocalProfile>($"select * from HSLocalProfile where Rb='{Global_Variable.Mk_resources.RB_code}' and Name='{txt_profileName.Text}' ; ");
               
                List<string> Fields = new List<string>();
                string[] aFields = { 
                    "Validity", "UptimeLimit", "TransferLimit", "Price", 
                    "Price_Display", "Link_hotspot_profile",
                     "Add_Smart_Scripts", "Save_session", "Save_download", 
                    "ByDayOrHour", "Save_time", "Is_percentage", 
                    "Percentage", "PercentageType","Rb","Name"
                };Fields.AddRange(aFields);

                if (add)
                {
                    if (check.Count > 0)
                    {
                        RJMessageBox.Show("الاسم موجود من قبل");
                        return false;
                    }

                    int count = Smart_DA.InsertTable<HSLocalProfile>(Fields,Localprf, "HSLocalProfile");
                 
                }
                else
                {
                 
                    lock (Smart_DataAccess.Lock_object)
                    {
                        Localprf.Id = profile_eidt.Id;
                        Fields.Add("Id");
                        string sqlquery = UtilsSql.GetUpdateSql<HSLocalProfile>("HSLocalProfile",Fields, "where Id=@Id");
                        int r = Smart_DA.UpateTable(Localprf, sqlquery);
                    }

                    //Localprf.Id = profile_eidt.Id;
                    //db.UpdateOnly(() => new HSLocalProfile
                    //{
                    //    Validity = Localprf.Validity,
                    //    UptimeLimit = Localprf.UptimeLimit,
                    //    TransferLimit = Localprf.TransferLimit,
                    //    Price = Localprf.Price,
                    //    Price_Display = Localprf.Price_Display,
                    //    Link_hotspot_profile = Localprf.Link_hotspot_profile,
                    //    Add_Smart_Scripts = Localprf.Add_Smart_Scripts,
                    //    Save_session = Localprf.Save_session,
                    //    Save_download = Localprf.Save_download,
                    //    ByDayOrHour = Localprf.ByDayOrHour,
                    //    Save_time = Localprf.Save_time,


                    //    Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check),
                    //    Percentage = (float)Convert.ToDouble(txt_precent.Text),
                    //    PercentageType = CBOX_precent_type.SelectedIndex,

                    //}, where: x => x.Id == Localprf.Id);

                }
                //}
            }

            //if (add)
            //{
            //    if (SqlDataAccess.add_Edit_Profile_hotspot(prf, true) == false)
            //        return false;
            //}
            //else
            //{
            //    prf.id = profile_eidt.id;
            //    if (SqlDataAccess.add_Edit_Profile_hotspot(prf, false) == false)
            //        return false;
            //}
            //======================================================================================
            succes = true;
            return true;
        }

        private void txt_price_salse_onTextChanged(object sender, EventArgs e)
        {
            txt_price_display.Text=txt_price_salse.Text;
        }

        private void timer1_Tick(object sender, EventArgs e)
        {

        }

        private void CheckBox_Save_time_CheckedChanged(object sender, EventArgs e)
        {
            if(CheckBox_Save_time.Checked)
                CheckBox_byDayOrHour.Check=false;
        }

        private void CheckBox_byDayOrHour_CheckedChanged(object sender, EventArgs e)
        {
            if (CheckBox_byDayOrHour.Checked)
                CheckBox_Save_time.Check = false;
        }
    }
}
