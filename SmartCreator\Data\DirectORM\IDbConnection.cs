using SmartCreator.Data.DirectORM;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Extension methods for direct database access without services
    /// </summary>
    public static class DbConnectionExtensions
    {
        /// <summary>
        /// Select records using lambda expression
        /// </summary>
        public static List<T> Select<T>(this IDbConnection db, Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            var query = new SqlExpressionBuilder<T>();
            if (predicate != null)
                query.Where(predicate);

            return db.ExecuteQuery<T>(query.ToSelectStatement());
        }

        /// <summary>
        /// Get single record
        /// </summary>
        public static T Single<T>(this IDbConnection db, Expression<Func<T, bool>> predicate) where T : class, new()
        {
            var results = db.Select(predicate);
            if (results.Count == 0)
                throw new InvalidOperationException("No records found");
            if (results.Count > 1)
                throw new InvalidOperationException("Multiple records found");
            return results[0];
        }

        /// <summary>
        /// Get single record by ID
        /// </summary>
        public static T SingleById<T>(this IDbConnection db, object id) where T : class, new()
        {
            var tableName = typeof(T).Name;
            var sql = $"SELECT * FROM {tableName} WHERE Id = @Id";
            var results = db.ExecuteQuery<T>(sql, new { Id = id });
            return results.Single();
        }

        /// <summary>
        /// Get first record or default
        /// </summary>
        public static T? FirstOrDefault<T>(this IDbConnection db, Expression<Func<T, bool>> predicate) where T : class, new()
        {
            var results = db.Select(predicate);
            return results.FirstOrDefault();
        }

        /// <summary>
        /// Count records
        /// </summary>
        public static int Count<T>(this IDbConnection db, Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            var query = new SqlExpressionBuilder<T>();
            if (predicate != null)
                query.Where(predicate);

            var sql = query.ToCountStatement();
            return db.ExecuteScalar<int>(sql);
        }

        /// <summary>
        /// Check if records exist
        /// </summary>
        public static bool Exists<T>(this IDbConnection db, Expression<Func<T, bool>> predicate) where T : class, new()
        {
            return db.Count(predicate) > 0;
        }

        /// <summary>
        /// Get scalar value
        /// </summary>
        public static TResult Scalar<T, TResult>(this IDbConnection db, Expression<Func<T, TResult>> selector, Expression<Func<T, bool>>? predicate = null) where T : class, new()
        {
            var query = new SqlExpressionBuilder<T>();
            if (predicate != null)
                query.Where(predicate);

            var sql = query.ToScalarStatement(selector);
            return db.ExecuteScalar<TResult>(sql);
        }

        /// <summary>
        /// Get scalar value with simple query
        /// </summary>
        public static TResult Scalar<TResult>(this IDbConnection db, SqlExpressionResult<TResult> expression)
        {
            return db.ExecuteScalar<TResult>(expression.ToSql());
        }

        /// <summary>
        /// Start building SqlExpression
        /// </summary>
        public static SqlExpression<T> From<T>(this IDbConnection db) where T : class, new()
        {
            return new SqlExpression<T>(db);
        }

        /// <summary>
        /// Get column values
        /// </summary>
        public static List<TColumn> Column<TColumn>(this IDbConnection db, string sql) where TColumn : new()
        {
            return db.ExecuteQuery<TColumn>(sql);
        }

        /// <summary>
        /// Get distinct column values
        /// </summary>
        public static HashSet<TColumn> ColumnDistinct<TColumn>(this IDbConnection db, string sql) where TColumn : new()
        {
            var results = db.Column<TColumn>(sql);
            return new HashSet<TColumn>(results);
        }

        /// <summary>
        /// Get dictionary from query
        /// </summary>
        public static Dictionary<TKey, TValue> Dictionary<TKey, TValue>(this IDbConnection db, string sql) where TKey : notnull
        {
            using var command = db.CreateCommand();
            command.CommandText = sql;

            var result = new Dictionary<TKey, TValue>();
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var key = (TKey)reader[0];
                var value = (TValue)reader[1];
                result[key] = value;
            }
            return result;
        }

        /// <summary>
        /// Get lookup (one-to-many dictionary)
        /// </summary>
        public static Dictionary<TKey, List<TValue>> Lookup<TKey, TValue>(this IDbConnection db, string sql) where TKey : notnull
        {
            using var command = db.CreateCommand();
            command.CommandText = sql;

            var result = new Dictionary<TKey, List<TValue>>();
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var key = (TKey)reader[0];
                var value = (TValue)reader[1];

                if (!result.ContainsKey(key))
                    result[key] = new List<TValue>();

                result[key].Add(value);
            }
            return result;
        }

        /// <summary>
        /// Insert record
        /// </summary>
        public static void Insert<T>(this IDbConnection db, T entity) where T : class
        {
            var sql = SqlGenerator.GenerateInsert<T>();
            db.ExecuteNonQuery(sql, entity);
        }

        /// <summary>
        /// Update record
        /// </summary>
        public static void Update<T>(this IDbConnection db, T entity) where T : class
        {
            var sql = SqlGenerator.GenerateUpdate<T>();
            db.ExecuteNonQuery(sql, entity);
        }

        /// <summary>
        /// Delete records
        /// </summary>
        public static int Delete<T>(this IDbConnection db, Expression<Func<T, bool>> predicate) where T : class, new()
        {
            var query = new SqlExpressionBuilder<T>();
            query.Where(predicate);
            var sql = query.ToDeleteStatement();
            return db.ExecuteNonQuery(sql);
        }

        /// <summary>
        /// Execute query and return results
        /// </summary>
        public static List<T> ExecuteQuery<T>(this IDbConnection db, string sql, object? parameters = null) where T : new()
        {
            // Log SQL if logger is available
            DirectDbContext.SqlLogger?.Invoke(sql);

            using var command = db.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            var results = new List<T>();
            using var reader = command.ExecuteReader();
            while (reader.Read())
            {
                var item = SqlHelper.MapFromReader<T>(reader);
                results.Add(item);
            }
            return results;
        }

        /// <summary>
        /// Execute scalar query
        /// </summary>
        private static T ExecuteScalar<T>(this IDbConnection db, string sql, object? parameters = null)
        {
            // Log SQL if logger is available
            DirectDbContext.SqlLogger?.Invoke(sql);

            using var command = db.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            var result = command.ExecuteScalar();
            return (T)Convert.ChangeType(result!, typeof(T));
        }

        /// <summary>
        /// Execute non-query
        /// </summary>
        private static int ExecuteNonQuery(this IDbConnection db, string sql, object? parameters = null)
        {
            // Log SQL if logger is available
            DirectDbContext.SqlLogger?.Invoke(sql);

            using var command = db.CreateCommand();
            command.CommandText = sql;

            if (parameters != null)
                SqlHelper.AddParameters(command, parameters);

            return command.ExecuteNonQuery();
        }
    }
}
