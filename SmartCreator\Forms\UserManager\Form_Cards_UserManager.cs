﻿using SmartCreator.Forms.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class Form_Cards_UserManager : RJChildForm
    {
        public FormAllBatchsCards formAllBatchsCards;
        public FormAllCardsUserManager formAllCardsUserManager;
        public FormAllCardsUserManager formAllCardsUserManager_RB_Archive;
        public FormAllCardsUserManager formAll_From_Finsh_Cards;
        public Form_AllSession_UserManager formAll_From_Session_Cards;
        bool First_formAllBatchsCards = true;
        bool First_formAllCardsUserManager = true;
        bool First_formAllCardsUserManager_From_RB_Archive = true;
        bool First_formAllCards_From_Finsh_Cards = true;
        bool First_formAllCards_From_Session_Cards = true;



        public Form_Cards_UserManager()
        {

            InitializeComponent();
            utils utils = new utils();
            utils.Control_textSize1(this);


            //this.Text = "جميع الكروت من الروتر";
            //this.Caption = "جميع الكروت من الروتر";

            this.Text = "ادارة كروت اليوزمنجر";
            this.Caption = "ادارة كروت اليوزمنجر";
            //if (UIAppearance.Theme == UITheme.Dark)
            //{
            //    pnl_side_sn.Customizable = false;
            //    pnl_side_datePrint.Customizable = false;
            //}

            if (UIAppearance.Language_ar == false)
            {
                this.Text = "Cards UserManager Manag";
                this.Caption = "Cards UserManager Manag";
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.Yes;
            }
            else
            {
                tableLayoutPanel_Top_Btn.RightToLeft = RightToLeft.No;
            }
            Set_Font();
        }
        private void Set_Font()
        {
            //return;
            Font fnt = Program.GetCustomFont(Resources.DroidKufi_Bold, 8*utils.ScaleFactor, FontStyle.Bold);
            //Font fnt = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 8f, true);
            btn_All_Cards_RB_Title.Font= fnt;
            btn_Batch_Cards_Title.Font= fnt;
            btn_Finsh_Cards_Title.Font= fnt;
            btn_Sessions_Cards_Title.Font= fnt;
            btn_All_Cards_From_RB_Archive_Title.Font= fnt;
        }
        
        
        private void pnlClientArea_Resize(object sender, EventArgs e)
        {
            panel_Tab_Container.Refresh();
        }
        private void Btn_Active(RJButton bnt)
        {
            bnt.Style = ControlStyle.Solid;
            bnt.BorderSize = 1;
            bnt.Invalidate();
            bnt.Refresh();
            bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
            bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
            bnt.BorderSize = 1;

        }
        private void Btn_DeActive()
        {
            foreach (Control contrl in tableLayoutPanel_Top_Btn.Controls)
            {
                if (contrl.GetType() == typeof(RJControls.RJButton))
                {
                    RJButton bnt = (RJButton)contrl;
                    bnt.Style = ControlStyle.Glass;
                    bnt.Invalidate();
                    bnt.Refresh();
                    bnt.FlatAppearance.MouseOverBackColor = ColorEditor.Darken(bnt.BackColor, 12);
                    bnt.FlatAppearance.MouseDownBackColor = ColorEditor.Darken(bnt.BackColor, 6);
                }

            }
        }

        private void Form_Cards_UserManager_Load(object sender, EventArgs e)
        {
            //btn_All_Cards_RB_Title_Click(sender, e);
            timer1.Start();

        }

        private void btn_All_Cards_RB_Title_Click(object sender, EventArgs e)
        {
          
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_All_Cards_RB_Title);

            if (First_formAllCardsUserManager)
            {
                First_formAllCardsUserManager = false;
                formAllCardsUserManager = new FormAllCardsUserManager("From_Server");
                formAllCardsUserManager.TopLevel = false;
                formAllCardsUserManager.IsChildForm = true;
                formAllCardsUserManager.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAllCardsUserManager);
                this.panel_Tab_Container.Tag = formAllCardsUserManager;
                formAllCardsUserManager.Show(); //show on desktop panel  
                formAllCardsUserManager.BringToFront();
                formAllCardsUserManager.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAllCardsUserManager.BringToFront();
                formAllCardsUserManager.Show();
                formAllCardsUserManager.Focus();
            }
            //this.Text = "جميع الكروت من الروتر";
            //lblCaption.Text = "جميع الكروت من الروتر";
            this.Refresh();
        }

        private void btn_Batch_Cards_Title_Click(object sender, EventArgs e)
        {
           
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Batch_Cards_Title);

            if (First_formAllBatchsCards)
            {
                First_formAllBatchsCards = false;
                formAllBatchsCards = new FormAllBatchsCards();
                formAllBatchsCards.TopLevel = false;
                //formCardsByNumberPrint.FormBorderStyle = FormBorderStyle.None;
                formAllBatchsCards.IsChildForm = true;
                formAllBatchsCards.Dock = DockStyle.Fill;

                //form_AddUserManger.dgvUsersUM.DataSource = MyDataClass.SourceForPrint_UM_Users;

                this.panel_Tab_Container.Controls.Add(formAllBatchsCards);
                this.panel_Tab_Container.Tag = formAllBatchsCards;
                //formCardsByNumberPrint.BringToFront();
                //formCardsByNumberPrint.Show();

                formAllBatchsCards.Show(); //show on desktop panel  
                formAllBatchsCards.BringToFront();
                formAllBatchsCards.Focus();

                //formAllBatchsCards.LoadDataGridviewData();
            }
            else
            {
                formAllBatchsCards.BringToFront();
                formAllBatchsCards.Show();
                formAllBatchsCards.Focus();
            }
            //this.Text = "دفعات الكروت يوزمنجر";
            //lblCaption.Text = "دفعات الكروت يوزمنجر";
            this.Refresh();

        }

        private void Form_Cards_UserManager_FormClosed(object sender, FormClosedEventArgs e)
        {
            //if (formAllCardsUserManager != null)
            //    formAllCardsUserManager.SaveFromState();
            //if (formAllBatchsCards != null)
            //    formAllBatchsCards.SaveFromState();
            //if (formAll_From_Finsh_Cards != null)
            //    formAll_From_Finsh_Cards.SaveFromState();
            //if (formAllCardsUserManager_RB_Archive != null)
            //    formAllCardsUserManager_RB_Archive.SaveFromState();
            
            
            
            //    if (formAllCardsUserManager_RB_Archive != null)
            //        formAllCardsUserManager_RB_Archive.SaveFromState();
        }

        private void timer1_Tick(object sender, EventArgs e)
        { 
            timer1.Stop();
            btn_All_Cards_RB_Title_Click(sender, e);
           
        }

        private void btn_All_Cards_From_RB_Archive_Title_Click(object sender, EventArgs e)
        {
            
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_All_Cards_From_RB_Archive_Title);

            if (First_formAllCardsUserManager_From_RB_Archive)
            {
                First_formAllCardsUserManager_From_RB_Archive = false;
                formAllCardsUserManager_RB_Archive = new FormAllCardsUserManager("From_RB_Archive");
                formAllCardsUserManager_RB_Archive.TopLevel = false;
                formAllCardsUserManager_RB_Archive.IsChildForm = true;
                formAllCardsUserManager_RB_Archive.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAllCardsUserManager_RB_Archive);
                this.panel_Tab_Container.Tag = formAllCardsUserManager_RB_Archive;
                formAllCardsUserManager_RB_Archive.Show(); //show on desktop panel  
                formAllCardsUserManager_RB_Archive.BringToFront();
                formAllCardsUserManager_RB_Archive.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAllCardsUserManager_RB_Archive.BringToFront();
                formAllCardsUserManager_RB_Archive.Show();
                formAllCardsUserManager_RB_Archive.Focus();
            }
            //this.Text = " كروت  الروتر مع الارشيف";
            //this.Caption = " كروت  الروتر مع الارشيف";
            //lblCaption.Text = " كروت  الروتر مع الارشيف";
            this.Refresh();

        }

        private void btn_Finsh_Cards_Title_Click(object sender, EventArgs e)
        {
           
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Finsh_Cards_Title);

            if (First_formAllCards_From_Finsh_Cards)
            {
                First_formAllCards_From_Finsh_Cards = false;
                formAll_From_Finsh_Cards = new FormAllCardsUserManager("From_Finsh_Cards");
                formAll_From_Finsh_Cards.TopLevel = false;
                formAll_From_Finsh_Cards.IsChildForm = true;
                formAll_From_Finsh_Cards.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAll_From_Finsh_Cards);
                this.panel_Tab_Container.Tag = formAll_From_Finsh_Cards;
                formAll_From_Finsh_Cards.Show(); //show on desktop panel  
                formAll_From_Finsh_Cards.BringToFront();
                formAll_From_Finsh_Cards.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAll_From_Finsh_Cards.BringToFront();
                formAll_From_Finsh_Cards.Show();
                formAll_From_Finsh_Cards.Focus();
            }
            //this.Text = "  الكروت المنتهية يوزمنجر  ";
            //this.Caption = "  الكروت المنتهية يوزمنجر  ";
            //lblCaption.Text = "  الكروت المنتهية يوزمنجر  ";
            this.Refresh();

        }

        private void btn_Sessions_Cards_Title_Click(object sender, EventArgs e)
        {
            
            Btn_DeActive();
            Btn_DeActive();
            Btn_Active(btn_Sessions_Cards_Title);

            if (First_formAllCards_From_Session_Cards)
            {
                First_formAllCards_From_Session_Cards = false;
                formAll_From_Session_Cards = new Form_AllSession_UserManager();
                formAll_From_Session_Cards.TopLevel = false;
                formAll_From_Session_Cards.IsChildForm = true;
                formAll_From_Session_Cards.Dock = DockStyle.Fill;

                this.panel_Tab_Container.Controls.Add(formAll_From_Session_Cards);
                this.panel_Tab_Container.Tag = formAll_From_Finsh_Cards;
                formAll_From_Session_Cards.Show(); //show on desktop panel  
                formAll_From_Session_Cards.BringToFront();
                formAll_From_Session_Cards.Focus();

                //formAllCardsUserManager.LoadDataGridviewData();
            }
            else
            {
                formAll_From_Session_Cards.BringToFront();
                formAll_From_Session_Cards.Show();
                formAll_From_Session_Cards.Focus();
            }
            //this.Text = "جلسات اليوزمنجر";
            //lblCaption.Text = "جلسات اليوزمنجر";
            //this.Refresh();

        }
    }
}
