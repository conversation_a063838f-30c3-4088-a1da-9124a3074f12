﻿namespace SmartCreator.Forms.Hotspot
{
    partial class FormAllCardsHotspot
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.btn_Collaps = new SmartCreator.RJControls.RJButton();
            this.Spanel = new System.Windows.Forms.Panel();
            this.rjPanel_back_side = new SmartCreator.RJControls.RJPanel();
            this.rjButton3 = new SmartCreator.RJControls.RJButton();
            this.CBox_NumberPrint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel25 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Didabled = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel24 = new SmartCreator.RJControls.RJLabel();
            this.txt_SN_End = new SmartCreator.RJControls.RJTextBox();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.btn_apply = new SmartCreator.RJControls.RJButton();
            this.txt_SN_Start = new SmartCreator.RJControls.RJTextBox();
            this.CheckBox_SN = new SmartCreator.RJControls.RJCheckBox();
            this.CBox_SN_Compar = new SmartCreator.RJControls.RJComboBox();
            this.Date_To = new SmartCreator.RJControls.RJDatePicker();
            this.Date_From = new SmartCreator.RJControls.RJDatePicker();
            this.CheckBox_byDatePrint = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_to = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_UMProfile = new SmartCreator.RJControls.RJCheckBox();
            this.CBox_Server_hotspot = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel17 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Staus = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel14 = new SmartCreator.RJControls.RJLabel();
            this.CBox_profile_Source_hotspot = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel16 = new SmartCreator.RJControls.RJLabel();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel15 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.panel3_side = new System.Windows.Forms.Panel();
            this.rjLabel25Title = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile_HotspotLocal = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel26 = new SmartCreator.RJControls.RJLabel();
            this.CBox_Profile = new SmartCreator.RJControls.RJComboBox();
            this.pnl_side_Count_Session = new SmartCreator.RJControls.RJPanel();
            this.fpnl_showServer = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_Show_onlyServer = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel27 = new SmartCreator.RJControls.RJLabel();
            this.fpnl_showArchive = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_Show_Archive = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_ByCountSession = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            this.ToggleButton_ByCountProfile = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_Finsh_Cards = new SmartCreator.RJControls.RJPanel();
            this.btn_RemoveFinsh_Validaty = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_Download = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_Uptime = new SmartCreator.RJControls.RJButton();
            this.btn_RemoveFinsh_All = new SmartCreator.RJControls.RJButton();
            this.panel2_side = new System.Windows.Forms.Panel();
            this.panel1_side = new System.Windows.Forms.Panel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_NoSmartScript = new SmartCreator.RJControls.RJCheckBox();
            this.pnl_side_sn = new SmartCreator.RJControls.RJPanel();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.pnl_side_datePrint = new SmartCreator.RJControls.RJPanel();
            this.rjLabel13 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel12 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel20 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel19 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.lbl_byProfile = new SmartCreator.RJControls.RJLabel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.View_Hide_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SaveDGVToolStripMenuItem = new FontAwesome.Sharp.IconMenuItem();
            this.toolStripSeparator5 = new System.Windows.Forms.ToolStripSeparator();
            this.Status_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.UserName_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Password_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SN_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_price_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.SellingPoint_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.BachCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.NumberPrint_toolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_TransferLimit_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_limitUptime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_limitbytestotal_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UptimeUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_DownloadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_UploadUsed_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTimeLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTransferLeft_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_Up_Down_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_RegDate_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_LastSeenAt_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.dt_FirstUse_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_ProfileTillTime_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.الايامالمتبقيةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Descr_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.comment_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Count_profile_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.CountSession_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.profileHotspot_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Sn_Archive_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.server_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.email_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.PageNumberToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Str_SmartValidatiy_Add_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Restor_ColumnToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripMenuItem2 = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator1 = new System.Windows.Forms.ToolStripSeparator();
            this.نسخCtrlcToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.نسخالسطركاملToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالىملفاكسلToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالىملفنصيToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمفقطToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.حذفالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.حذفجلساتالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator3 = new System.Windows.Forms.ToolStripSeparator();
            this.تعطيلالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تفعيلالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator4 = new System.Windows.Forms.ToolStripSeparator();
            this.طباعةالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.اعادةشحنالكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Remove_SP_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.btnEnable = new SmartCreator.RJControls.RJButton();
            this.rjButton2 = new SmartCreator.RJControls.RJButton();
            this.btnDisable = new SmartCreator.RJControls.RJButton();
            this.btn_search_Router = new SmartCreator.RJControls.RJButton();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.jToggleButton_Year = new SmartCreator.RJControls.RJToggleButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.timer_SideBar = new System.Windows.Forms.Timer(this.components);
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.btnRefresh_DB = new SmartCreator.RJControls.RJButton();
            this.btn_Filter = new SmartCreator.RJControls.RJButton();
            this.btnFirst = new SmartCreator.RJControls.RJButton();
            this.btnNext = new SmartCreator.RJControls.RJButton();
            this.btnLast = new SmartCreator.RJControls.RJButton();
            this.btnPrev = new SmartCreator.RJControls.RJButton();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.btnDelete = new SmartCreator.RJControls.RJButton();
            this.btn_add_ScriptSmart = new SmartCreator.RJControls.RJButton();
            this.lbl_Filter = new SmartCreator.RJControls.RJLabel();
            this.CBox_OrderBy = new SmartCreator.RJControls.RJComboBox();
            this.CBox_SearchBy = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.CheckBox_orderBy = new SmartCreator.RJControls.RJCheckBox();
            this.rjPane_Top = new SmartCreator.RJControls.RJPanel();
            this.btn_RemoveFinsh = new SmartCreator.RJControls.RJButton();
            this.rjPanel_Page = new SmartCreator.RJControls.RJPanel();
            this.Panel_Pages = new System.Windows.Forms.Panel();
            this.rjLabel11 = new SmartCreator.RJControls.RJLabel();
            this.CBox_PageCount = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel18 = new SmartCreator.RJControls.RJLabel();
            this.panel2 = new System.Windows.Forms.Panel();
            this.rjLabel21 = new SmartCreator.RJControls.RJLabel();
            this.txtAllCountRows = new SmartCreator.RJControls.RJTextBox();
            this.txtCurrentPageindex = new SmartCreator.RJControls.RJTextBox();
            this.txtTotalPages = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel22 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel23 = new SmartCreator.RJControls.RJLabel();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.rjPanel3.SuspendLayout();
            this.Spanel.SuspendLayout();
            this.rjPanel_back_side.SuspendLayout();
            this.pnl_side_Count_Session.SuspendLayout();
            this.fpnl_showServer.SuspendLayout();
            this.fpnl_showArchive.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            this.flowLayoutPanel2.SuspendLayout();
            this.pnl_side_Finsh_Cards.SuspendLayout();
            this.pnl_side_sn.SuspendLayout();
            this.pnl_side_datePrint.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.dmAll_Cards.SuspendLayout();
            this.rjPane_Top.SuspendLayout();
            this.rjPanel_Page.SuspendLayout();
            this.Panel_Pages.SuspendLayout();
            this.panel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.Spanel);
            this.pnlClientArea.Controls.Add(this.rjPanel_Page);
            this.pnlClientArea.Controls.Add(this.pnl_side_datePrint);
            this.pnlClientArea.Controls.Add(this.rjLabel9);
            this.pnlClientArea.Controls.Add(this.lbl_byProfile);
            this.pnlClientArea.Controls.Add(this.rjPane_Top);
            this.pnlClientArea.Controls.Add(this.rjLabel2);
            this.pnlClientArea.Controls.Add(this.lbl_Filter);
            this.pnlClientArea.Controls.Add(this.pnl_side_sn);
            this.pnlClientArea.Controls.Add(this.rjButton2);
            this.pnlClientArea.Controls.Add(this.jToggleButton_Year);
            this.pnlClientArea.Controls.Add(this.btn_add_ScriptSmart);
            this.pnlClientArea.Controls.Add(this.btn_search_Router);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Controls.Add(this.rjLabel4);
            this.pnlClientArea.Controls.Add(this.lbl_note);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 579);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(124, 17);
            this.lblCaption.Text = "FormAllCardsHotspot";
            // 
            // rjPanel3
            // 
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 0;
            this.rjPanel3.Controls.Add(this.btn_Collaps);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Dock = System.Windows.Forms.DockStyle.Left;
            this.rjPanel3.Location = new System.Drawing.Point(0, 0);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(7, 579);
            this.rjPanel3.TabIndex = 56;
            this.rjPanel3.Visible = false;
            // 
            // btn_Collaps
            // 
            this.btn_Collaps.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Collaps.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Collaps.BorderRadius = 0;
            this.btn_Collaps.BorderSize = 0;
            this.btn_Collaps.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_Collaps.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_Collaps.FlatAppearance.BorderSize = 0;
            this.btn_Collaps.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Collaps.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Collaps.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Collaps.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Collaps.ForeColor = System.Drawing.Color.White;
            this.btn_Collaps.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_Collaps.IconColor = System.Drawing.Color.White;
            this.btn_Collaps.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Collaps.IconSize = 24;
            this.btn_Collaps.Location = new System.Drawing.Point(0, 0);
            this.btn_Collaps.Name = "btn_Collaps";
            this.btn_Collaps.Size = new System.Drawing.Size(7, 79);
            this.btn_Collaps.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Collaps.TabIndex = 0;
            this.btn_Collaps.Text = "H\r\nI\r\nD\r\nE";
            this.btn_Collaps.UseVisualStyleBackColor = false;
            this.btn_Collaps.Click += new System.EventHandler(this.btn_Collaps_Click);
            // 
            // Spanel
            // 
            this.Spanel.AutoScroll = true;
            this.Spanel.Controls.Add(this.rjPanel_back_side);
            this.Spanel.Controls.Add(this.panel2_side);
            this.Spanel.Controls.Add(this.panel1_side);
            this.Spanel.Dock = System.Windows.Forms.DockStyle.Left;
            this.Spanel.Location = new System.Drawing.Point(7, 0);
            this.Spanel.Name = "Spanel";
            this.Spanel.Size = new System.Drawing.Size(297, 579);
            this.Spanel.TabIndex = 57;
            this.Spanel.Visible = false;
            // 
            // rjPanel_back_side
            // 
            this.rjPanel_back_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_back_side.BorderRadius = 0;
            this.rjPanel_back_side.Controls.Add(this.rjButton3);
            this.rjPanel_back_side.Controls.Add(this.CBox_NumberPrint);
            this.rjPanel_back_side.Controls.Add(this.rjLabel25);
            this.rjPanel_back_side.Controls.Add(this.CBox_Didabled);
            this.rjPanel_back_side.Controls.Add(this.rjLabel24);
            this.rjPanel_back_side.Controls.Add(this.txt_SN_End);
            this.rjPanel_back_side.Controls.Add(this.rjButton1);
            this.rjPanel_back_side.Controls.Add(this.btn_apply);
            this.rjPanel_back_side.Controls.Add(this.txt_SN_Start);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_SN);
            this.rjPanel_back_side.Controls.Add(this.CBox_SN_Compar);
            this.rjPanel_back_side.Controls.Add(this.Date_To);
            this.rjPanel_back_side.Controls.Add(this.Date_From);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_byDatePrint);
            this.rjPanel_back_side.Controls.Add(this.lbl_to);
            this.rjPanel_back_side.Controls.Add(this.CheckBox_UMProfile);
            this.rjPanel_back_side.Controls.Add(this.CBox_Server_hotspot);
            this.rjPanel_back_side.Controls.Add(this.rjLabel17);
            this.rjPanel_back_side.Controls.Add(this.CBox_Staus);
            this.rjPanel_back_side.Controls.Add(this.rjLabel14);
            this.rjPanel_back_side.Controls.Add(this.CBox_profile_Source_hotspot);
            this.rjPanel_back_side.Controls.Add(this.rjLabel16);
            this.rjPanel_back_side.Controls.Add(this.CBox_SellingPoint);
            this.rjPanel_back_side.Controls.Add(this.rjLabel15);
            this.rjPanel_back_side.Controls.Add(this.CBox_Batch);
            this.rjPanel_back_side.Controls.Add(this.rjLabel3);
            this.rjPanel_back_side.Controls.Add(this.panel3_side);
            this.rjPanel_back_side.Controls.Add(this.rjLabel25Title);
            this.rjPanel_back_side.Controls.Add(this.CBox_Profile_HotspotLocal);
            this.rjPanel_back_side.Controls.Add(this.rjLabel26);
            this.rjPanel_back_side.Controls.Add(this.CBox_Profile);
            this.rjPanel_back_side.Controls.Add(this.pnl_side_Finsh_Cards);
            this.rjPanel_back_side.Controls.Add(this.pnl_side_Count_Session);
            this.rjPanel_back_side.Customizable = false;
            this.rjPanel_back_side.Dock = System.Windows.Forms.DockStyle.Fill;
            this.rjPanel_back_side.Location = new System.Drawing.Point(0, 1);
            this.rjPanel_back_side.Margin = new System.Windows.Forms.Padding(0);
            this.rjPanel_back_side.Name = "rjPanel_back_side";
            this.rjPanel_back_side.Size = new System.Drawing.Size(297, 577);
            this.rjPanel_back_side.TabIndex = 57;
            // 
            // rjButton3
            // 
            this.rjButton3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton3.BorderRadius = 5;
            this.rjButton3.BorderSize = 1;
            this.rjButton3.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton3.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton3.FlatAppearance.BorderSize = 0;
            this.rjButton3.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton3.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton3.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton3.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.rjButton3.ForeColor = System.Drawing.Color.White;
            this.rjButton3.IconChar = FontAwesome.Sharp.IconChar.Remove;
            this.rjButton3.IconColor = System.Drawing.Color.White;
            this.rjButton3.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton3.IconSize = 20;
            this.rjButton3.Location = new System.Drawing.Point(20, 8);
            this.rjButton3.Name = "rjButton3";
            this.rjButton3.Size = new System.Drawing.Size(29, 25);
            this.rjButton3.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton3.TabIndex = 107;
            this.rjButton3.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton3.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.rjButton3.UseVisualStyleBackColor = false;
            this.rjButton3.Click += new System.EventHandler(this.rjButton3_Click);
            // 
            // CBox_NumberPrint
            // 
            this.CBox_NumberPrint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_NumberPrint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_NumberPrint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_NumberPrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_NumberPrint.BorderRadius = 5;
            this.CBox_NumberPrint.BorderSize = 1;
            this.CBox_NumberPrint.Customizable = false;
            this.CBox_NumberPrint.DataSource = null;
            this.CBox_NumberPrint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_NumberPrint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_NumberPrint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_NumberPrint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_NumberPrint.Location = new System.Drawing.Point(17, 104);
            this.CBox_NumberPrint.Name = "CBox_NumberPrint";
            this.CBox_NumberPrint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_NumberPrint.SelectedIndex = -1;
            this.CBox_NumberPrint.Size = new System.Drawing.Size(125, 32);
            this.CBox_NumberPrint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_NumberPrint.TabIndex = 104;
            this.CBox_NumberPrint.Texts = "";
            this.CBox_NumberPrint.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel25
            // 
            this.rjLabel25.AutoSize = true;
            this.rjLabel25.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel25.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel25.LinkLabel = false;
            this.rjLabel25.Location = new System.Drawing.Point(72, 88);
            this.rjLabel25.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel25.Name = "rjLabel25";
            this.rjLabel25.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.rjLabel25.Size = new System.Drawing.Size(43, 17);
            this.rjLabel25.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel25.TabIndex = 105;
            this.rjLabel25.Text = "الطبعة";
            // 
            // CBox_Didabled
            // 
            this.CBox_Didabled.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Didabled.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Didabled.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Didabled.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Didabled.BorderRadius = 5;
            this.CBox_Didabled.BorderSize = 1;
            this.CBox_Didabled.Customizable = false;
            this.CBox_Didabled.DataSource = null;
            this.CBox_Didabled.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Didabled.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Didabled.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Didabled.Font = new System.Drawing.Font("Cairo", 8.999999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Didabled.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Didabled.Location = new System.Drawing.Point(152, 272);
            this.CBox_Didabled.Name = "CBox_Didabled";
            this.CBox_Didabled.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Didabled.SelectedIndex = -1;
            this.CBox_Didabled.Size = new System.Drawing.Size(118, 32);
            this.CBox_Didabled.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Didabled.TabIndex = 104;
            this.CBox_Didabled.Texts = "";
            this.CBox_Didabled.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel24
            // 
            this.rjLabel24.AutoSize = true;
            this.rjLabel24.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel24.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel24.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel24.LinkLabel = false;
            this.rjLabel24.Location = new System.Drawing.Point(191, 254);
            this.rjLabel24.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel24.Name = "rjLabel24";
            this.rjLabel24.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel24.Size = new System.Drawing.Size(45, 17);
            this.rjLabel24.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel24.TabIndex = 105;
            this.rjLabel24.Text = "التفعيل";
            this.rjLabel24.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // txt_SN_End
            // 
            this.txt_SN_End._Customizable = false;
            this.txt_SN_End.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_End.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_End.BorderRadius = 5;
            this.txt_SN_End.BorderSize = 1;
            this.txt_SN_End.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_SN_End.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_End.Location = new System.Drawing.Point(78, 311);
            this.txt_SN_End.MultiLine = false;
            this.txt_SN_End.Name = "txt_SN_End";
            this.txt_SN_End.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_End.PasswordChar = false;
            this.txt_SN_End.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_End.PlaceHolderText = null;
            this.txt_SN_End.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_End.Size = new System.Drawing.Size(60, 28);
            this.txt_SN_End.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_End.TabIndex = 63;
            this.txt_SN_End.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_SN_End.onTextChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjButton1
            // 
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 8;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.rjButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.None;
            this.rjButton1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.rjButton1.IconSize = 24;
            this.rjButton1.Location = new System.Drawing.Point(74, 497);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(147, 33);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjButton1.TabIndex = 65;
            this.rjButton1.Text = "مسح جميع الفلترة";
            this.rjButton1.UseVisualStyleBackColor = false;
            this.rjButton1.Click += new System.EventHandler(this.rjButton1_Click);
            // 
            // btn_apply
            // 
            this.btn_apply.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_apply.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.BorderRadius = 8;
            this.btn_apply.BorderSize = 1;
            this.btn_apply.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_apply.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_apply.FlatAppearance.BorderSize = 0;
            this.btn_apply.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_apply.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_apply.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_apply.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_apply.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_apply.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_apply.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_apply.IconSize = 24;
            this.btn_apply.Location = new System.Drawing.Point(195, 497);
            this.btn_apply.Name = "btn_apply";
            this.btn_apply.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_apply.Size = new System.Drawing.Size(86, 33);
            this.btn_apply.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_apply.TabIndex = 66;
            this.btn_apply.Text = "تطبيق";
            this.btn_apply.UseVisualStyleBackColor = false;
            this.btn_apply.Visible = false;
            this.btn_apply.Click += new System.EventHandler(this.btn_apply_Click);
            // 
            // txt_SN_Start
            // 
            this.txt_SN_Start._Customizable = false;
            this.txt_SN_Start.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_SN_Start.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_SN_Start.BorderRadius = 5;
            this.txt_SN_Start.BorderSize = 1;
            this.txt_SN_Start.Font = new System.Drawing.Font("Verdana", 10F);
            this.txt_SN_Start.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_SN_Start.Location = new System.Drawing.Point(147, 311);
            this.txt_SN_Start.MultiLine = false;
            this.txt_SN_Start.Name = "txt_SN_Start";
            this.txt_SN_Start.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_SN_Start.PasswordChar = false;
            this.txt_SN_Start.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_SN_Start.PlaceHolderText = null;
            this.txt_SN_Start.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_SN_Start.Size = new System.Drawing.Size(60, 28);
            this.txt_SN_Start.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_SN_Start.TabIndex = 64;
            this.txt_SN_Start.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_SN_Start.onTextChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CheckBox_SN
            // 
            this.CheckBox_SN.AutoSize = true;
            this.CheckBox_SN.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.BorderSize = 1;
            this.CheckBox_SN.Check = false;
            this.CheckBox_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_SN.Customizable = false;
            this.CheckBox_SN.Font = new System.Drawing.Font("Droid Sans Arabic", 8F);
            this.CheckBox_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_SN.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_SN.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_SN.Location = new System.Drawing.Point(182, 315);
            this.CheckBox_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_SN.Name = "CheckBox_SN";
            this.CheckBox_SN.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_SN.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_SN.Size = new System.Drawing.Size(86, 21);
            this.CheckBox_SN.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_SN.TabIndex = 62;
            this.CheckBox_SN.Text = "التسلسل";
            this.CheckBox_SN.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_SN.UseVisualStyleBackColor = true;
            this.CheckBox_SN.CheckedChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CBox_SN_Compar
            // 
            this.CBox_SN_Compar.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SN_Compar.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SN_Compar.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SN_Compar.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.BorderRadius = 5;
            this.CBox_SN_Compar.BorderSize = 1;
            this.CBox_SN_Compar.Customizable = false;
            this.CBox_SN_Compar.DataSource = null;
            this.CBox_SN_Compar.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SN_Compar.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SN_Compar.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SN_Compar.Font = new System.Drawing.Font("Tahoma", 8F);
            this.CBox_SN_Compar.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SN_Compar.Items.AddRange(new object[] {
            "<",
            ">",
            "=",
            "بين"});
            this.CBox_SN_Compar.Location = new System.Drawing.Point(9, 309);
            this.CBox_SN_Compar.Name = "CBox_SN_Compar";
            this.CBox_SN_Compar.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SN_Compar.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SN_Compar.SelectedIndex = -1;
            this.CBox_SN_Compar.Size = new System.Drawing.Size(64, 28);
            this.CBox_SN_Compar.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SN_Compar.TabIndex = 61;
            this.CBox_SN_Compar.Texts = "";
            this.CBox_SN_Compar.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // Date_To
            // 
            this.Date_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_To.BorderRadius = 3;
            this.Date_To.BorderSize = 1;
            this.Date_To.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_To.Customizable = false;
            this.Date_To.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_To.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_To.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_To.Location = new System.Drawing.Point(7, 218);
            this.Date_To.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_To.Name = "Date_To";
            this.Date_To.Padding = new System.Windows.Forms.Padding(2);
            this.Date_To.Size = new System.Drawing.Size(132, 32);
            this.Date_To.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_To.TabIndex = 59;
            this.Date_To.Value = new System.DateTime(2024, 9, 24, 20, 3, 55, 357);
            this.Date_To.OnValueChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // Date_From
            // 
            this.Date_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.Date_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Date_From.BorderRadius = 3;
            this.Date_From.BorderSize = 1;
            this.Date_From.CustomFormat = "dd/MMM/yyyy  |  hh:mm:ss";
            this.Date_From.Customizable = false;
            this.Date_From.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Date_From.Format = System.Windows.Forms.DateTimePickerFormat.Short;
            this.Date_From.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Date_From.Location = new System.Drawing.Point(152, 218);
            this.Date_From.MinimumSize = new System.Drawing.Size(120, 25);
            this.Date_From.Name = "Date_From";
            this.Date_From.Padding = new System.Windows.Forms.Padding(2);
            this.Date_From.Size = new System.Drawing.Size(120, 32);
            this.Date_From.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.Date_From.TabIndex = 60;
            this.Date_From.Value = new System.DateTime(2024, 9, 27, 0, 0, 0, 0);
            this.Date_From.OnValueChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // CheckBox_byDatePrint
            // 
            this.CheckBox_byDatePrint.AutoSize = true;
            this.CheckBox_byDatePrint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.BorderSize = 1;
            this.CheckBox_byDatePrint.Check = false;
            this.CheckBox_byDatePrint.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_byDatePrint.Customizable = false;
            this.CheckBox_byDatePrint.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_byDatePrint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_byDatePrint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_byDatePrint.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_byDatePrint.Location = new System.Drawing.Point(171, 196);
            this.CheckBox_byDatePrint.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_byDatePrint.Name = "CheckBox_byDatePrint";
            this.CheckBox_byDatePrint.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_byDatePrint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_byDatePrint.Size = new System.Drawing.Size(85, 21);
            this.CheckBox_byDatePrint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_byDatePrint.TabIndex = 55;
            this.CheckBox_byDatePrint.Text = "الطباعة";
            this.CheckBox_byDatePrint.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_byDatePrint.UseVisualStyleBackColor = true;
            this.CheckBox_byDatePrint.CheckedChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // lbl_to
            // 
            this.lbl_to.AutoSize = true;
            this.lbl_to.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_to.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_to.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_to.LinkLabel = false;
            this.lbl_to.Location = new System.Drawing.Point(70, 197);
            this.lbl_to.Name = "lbl_to";
            this.lbl_to.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_to.Size = new System.Drawing.Size(27, 17);
            this.lbl_to.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_to.TabIndex = 56;
            this.lbl_to.Text = "الي";
            // 
            // CheckBox_UMProfile
            // 
            this.CheckBox_UMProfile.AutoSize = true;
            this.CheckBox_UMProfile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_UMProfile.BorderSize = 1;
            this.CheckBox_UMProfile.Check = true;
            this.CheckBox_UMProfile.Checked = true;
            this.CheckBox_UMProfile.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_UMProfile.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_UMProfile.Customizable = false;
            this.CheckBox_UMProfile.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_UMProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_UMProfile.IconColor = System.Drawing.Color.White;
            this.CheckBox_UMProfile.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_UMProfile.Location = new System.Drawing.Point(149, 31);
            this.CheckBox_UMProfile.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_UMProfile.Name = "CheckBox_UMProfile";
            this.CheckBox_UMProfile.Padding = new System.Windows.Forms.Padding(2, 0, 18, 0);
            this.CheckBox_UMProfile.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_UMProfile.Size = new System.Drawing.Size(107, 21);
            this.CheckBox_UMProfile.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.CheckBox_UMProfile.TabIndex = 45;
            this.CheckBox_UMProfile.Text = "باقة سمارت";
            this.CheckBox_UMProfile.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_UMProfile.UseVisualStyleBackColor = true;
            this.CheckBox_UMProfile.CheckedChanged += new System.EventHandler(this.CheckBox_UMProfile_CheckedChanged);
            // 
            // CBox_Server_hotspot
            // 
            this.CBox_Server_hotspot.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Server_hotspot.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Server_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Server_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_hotspot.BorderRadius = 5;
            this.CBox_Server_hotspot.BorderSize = 1;
            this.CBox_Server_hotspot.Customizable = false;
            this.CBox_Server_hotspot.DataSource = null;
            this.CBox_Server_hotspot.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Server_hotspot.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Server_hotspot.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Server_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Server_hotspot.Location = new System.Drawing.Point(12, 160);
            this.CBox_Server_hotspot.Name = "CBox_Server_hotspot";
            this.CBox_Server_hotspot.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Server_hotspot.SelectedIndex = -1;
            this.CBox_Server_hotspot.Size = new System.Drawing.Size(125, 32);
            this.CBox_Server_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Server_hotspot.TabIndex = 33;
            this.CBox_Server_hotspot.Texts = "";
            this.CBox_Server_hotspot.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel17
            // 
            this.rjLabel17.AutoSize = true;
            this.rjLabel17.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel17.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel17.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel17.LinkLabel = false;
            this.rjLabel17.Location = new System.Drawing.Point(61, 141);
            this.rjLabel17.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel17.Name = "rjLabel17";
            this.rjLabel17.Size = new System.Drawing.Size(44, 17);
            this.rjLabel17.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel17.TabIndex = 35;
            this.rjLabel17.Text = "السيرفر";
            this.rjLabel17.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Staus
            // 
            this.CBox_Staus.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Staus.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Staus.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Staus.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Staus.BorderRadius = 5;
            this.CBox_Staus.BorderSize = 1;
            this.CBox_Staus.Customizable = false;
            this.CBox_Staus.DataSource = null;
            this.CBox_Staus.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Staus.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Staus.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Staus.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Staus.Items.AddRange(new object[] {
            "",
            "انتظار",
            "نشط",
            "منتهي"});
            this.CBox_Staus.Location = new System.Drawing.Point(17, 53);
            this.CBox_Staus.Name = "CBox_Staus";
            this.CBox_Staus.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Staus.SelectedIndex = -1;
            this.CBox_Staus.Size = new System.Drawing.Size(125, 32);
            this.CBox_Staus.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Staus.TabIndex = 33;
            this.CBox_Staus.Texts = "";
            this.CBox_Staus.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel14
            // 
            this.rjLabel14.AutoSize = true;
            this.rjLabel14.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel14.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel14.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel14.LinkLabel = false;
            this.rjLabel14.Location = new System.Drawing.Point(76, 33);
            this.rjLabel14.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel14.Name = "rjLabel14";
            this.rjLabel14.Size = new System.Drawing.Size(37, 17);
            this.rjLabel14.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel14.TabIndex = 35;
            this.rjLabel14.Text = "الحالة";
            this.rjLabel14.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_profile_Source_hotspot
            // 
            this.CBox_profile_Source_hotspot.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_profile_Source_hotspot.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_profile_Source_hotspot.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_profile_Source_hotspot.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_Source_hotspot.BorderRadius = 5;
            this.CBox_profile_Source_hotspot.BorderSize = 1;
            this.CBox_profile_Source_hotspot.Customizable = false;
            this.CBox_profile_Source_hotspot.DataSource = null;
            this.CBox_profile_Source_hotspot.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_profile_Source_hotspot.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_profile_Source_hotspot.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_profile_Source_hotspot.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_profile_Source_hotspot.Location = new System.Drawing.Point(152, 160);
            this.CBox_profile_Source_hotspot.Name = "CBox_profile_Source_hotspot";
            this.CBox_profile_Source_hotspot.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_profile_Source_hotspot.SelectedIndex = -1;
            this.CBox_profile_Source_hotspot.Size = new System.Drawing.Size(120, 32);
            this.CBox_profile_Source_hotspot.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_profile_Source_hotspot.TabIndex = 33;
            this.CBox_profile_Source_hotspot.Texts = "";
            this.CBox_profile_Source_hotspot.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel16
            // 
            this.rjLabel16.AutoSize = true;
            this.rjLabel16.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel16.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel16.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel16.LinkLabel = false;
            this.rjLabel16.Location = new System.Drawing.Point(156, 141);
            this.rjLabel16.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel16.Name = "rjLabel16";
            this.rjLabel16.Size = new System.Drawing.Size(107, 17);
            this.rjLabel16.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel16.TabIndex = 35;
            this.rjLabel16.Text = "بروفايل الهوتسبوت";
            this.rjLabel16.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(7, 272);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(125, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 33;
            this.CBox_SellingPoint.Texts = "";
            this.CBox_SellingPoint.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel15
            // 
            this.rjLabel15.AutoSize = true;
            this.rjLabel15.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel15.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel15.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel15.LinkLabel = false;
            this.rjLabel15.Location = new System.Drawing.Point(47, 254);
            this.rjLabel15.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel15.Name = "rjLabel15";
            this.rjLabel15.Size = new System.Drawing.Size(52, 17);
            this.rjLabel15.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel15.TabIndex = 35;
            this.rjLabel15.Text = "نقطع بيع";
            this.rjLabel15.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Location = new System.Drawing.Point(152, 104);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(120, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 33;
            this.CBox_Batch.Texts = "";
            this.CBox_Batch.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Batch_OnSelectedIndexChanged);
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(197, 88);
            this.rjLabel3.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.Size = new System.Drawing.Size(43, 17);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 35;
            this.rjLabel3.Text = "الدفعه";
            // 
            // panel3_side
            // 
            this.panel3_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(0)))), ((int)(((byte)(192)))), ((int)(((byte)(0)))));
            this.panel3_side.Dock = System.Windows.Forms.DockStyle.Right;
            this.panel3_side.Location = new System.Drawing.Point(295, 0);
            this.panel3_side.Margin = new System.Windows.Forms.Padding(0);
            this.panel3_side.Name = "panel3_side";
            this.panel3_side.Size = new System.Drawing.Size(2, 577);
            this.panel3_side.TabIndex = 52;
            // 
            // rjLabel25Title
            // 
            this.rjLabel25Title.AutoSize = true;
            this.rjLabel25Title.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel25Title.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.rjLabel25Title.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel25Title.LinkLabel = false;
            this.rjLabel25Title.Location = new System.Drawing.Point(92, 1);
            this.rjLabel25Title.Name = "rjLabel25Title";
            this.rjLabel25Title.Size = new System.Drawing.Size(108, 31);
            this.rjLabel25Title.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel25Title.TabIndex = 31;
            this.rjLabel25Title.Text = "فلتره بحسب";
            // 
            // CBox_Profile_HotspotLocal
            // 
            this.CBox_Profile_HotspotLocal.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile_HotspotLocal.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.RecentlyUsedList;
            this.CBox_Profile_HotspotLocal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile_HotspotLocal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_HotspotLocal.BorderRadius = 5;
            this.CBox_Profile_HotspotLocal.BorderSize = 1;
            this.CBox_Profile_HotspotLocal.Customizable = false;
            this.CBox_Profile_HotspotLocal.DataSource = null;
            this.CBox_Profile_HotspotLocal.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile_HotspotLocal.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile_HotspotLocal.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile_HotspotLocal.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile_HotspotLocal.Location = new System.Drawing.Point(154, 53);
            this.CBox_Profile_HotspotLocal.Name = "CBox_Profile_HotspotLocal";
            this.CBox_Profile_HotspotLocal.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile_HotspotLocal.SelectedIndex = -1;
            this.CBox_Profile_HotspotLocal.Size = new System.Drawing.Size(113, 32);
            this.CBox_Profile_HotspotLocal.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile_HotspotLocal.TabIndex = 33;
            this.CBox_Profile_HotspotLocal.Texts = "";
            this.CBox_Profile_HotspotLocal.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // rjLabel26
            // 
            this.rjLabel26.AutoSize = true;
            this.rjLabel26.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel26.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel26.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel26.LinkLabel = false;
            this.rjLabel26.Location = new System.Drawing.Point(136, 319);
            this.rjLabel26.Name = "rjLabel26";
            this.rjLabel26.Size = new System.Drawing.Size(12, 14);
            this.rjLabel26.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel26.TabIndex = 106;
            this.rjLabel26.Text = "-";
            // 
            // CBox_Profile
            // 
            this.CBox_Profile.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Profile.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Profile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Profile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.BorderRadius = 5;
            this.CBox_Profile.BorderSize = 1;
            this.CBox_Profile.Customizable = false;
            this.CBox_Profile.DataSource = null;
            this.CBox_Profile.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Profile.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Profile.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Profile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Profile.Location = new System.Drawing.Point(154, 53);
            this.CBox_Profile.Name = "CBox_Profile";
            this.CBox_Profile.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Profile.SelectedIndex = -1;
            this.CBox_Profile.Size = new System.Drawing.Size(113, 32);
            this.CBox_Profile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Profile.TabIndex = 33;
            this.CBox_Profile.Texts = "";
            this.CBox_Profile.Visible = false;
            this.CBox_Profile.OnSelectedIndexChanged += new System.EventHandler(this.btn_search_Click);
            // 
            // pnl_side_Count_Session
            // 
            this.pnl_side_Count_Session.AutoScroll = true;
            this.pnl_side_Count_Session.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_Count_Session.BorderRadius = 10;
            this.pnl_side_Count_Session.Controls.Add(this.fpnl_showServer);
            this.pnl_side_Count_Session.Controls.Add(this.fpnl_showArchive);
            this.pnl_side_Count_Session.Controls.Add(this.flowLayoutPanel1);
            this.pnl_side_Count_Session.Controls.Add(this.flowLayoutPanel2);
            this.pnl_side_Count_Session.Customizable = true;
            this.pnl_side_Count_Session.Location = new System.Drawing.Point(7, 347);
            this.pnl_side_Count_Session.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_side_Count_Session.Name = "pnl_side_Count_Session";
            this.pnl_side_Count_Session.Padding = new System.Windows.Forms.Padding(2);
            this.pnl_side_Count_Session.Size = new System.Drawing.Size(265, 145);
            this.pnl_side_Count_Session.TabIndex = 57;
            // 
            // fpnl_showServer
            // 
            this.fpnl_showServer.Controls.Add(this.ToggleButton_Show_onlyServer);
            this.fpnl_showServer.Controls.Add(this.rjLabel27);
            this.fpnl_showServer.Dock = System.Windows.Forms.DockStyle.Top;
            this.fpnl_showServer.Location = new System.Drawing.Point(2, 108);
            this.fpnl_showServer.Name = "fpnl_showServer";
            this.fpnl_showServer.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.fpnl_showServer.Size = new System.Drawing.Size(261, 35);
            this.fpnl_showServer.TabIndex = 58;
            this.fpnl_showServer.Visible = false;
            // 
            // ToggleButton_Show_onlyServer
            // 
            this.ToggleButton_Show_onlyServer.Activated = false;
            this.ToggleButton_Show_onlyServer.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Show_onlyServer.AutoSize = true;
            this.ToggleButton_Show_onlyServer.Customizable = false;
            this.ToggleButton_Show_onlyServer.Location = new System.Drawing.Point(211, 3);
            this.ToggleButton_Show_onlyServer.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_Show_onlyServer.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_onlyServer.Name = "ToggleButton_Show_onlyServer";
            this.ToggleButton_Show_onlyServer.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_onlyServer.OFF_Text = null;
            this.ToggleButton_Show_onlyServer.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Show_onlyServer.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_onlyServer.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_onlyServer.ON_Text = null;
            this.ToggleButton_Show_onlyServer.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Show_onlyServer.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_onlyServer.Size = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_onlyServer.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Show_onlyServer.TabIndex = 52;
            this.ToggleButton_Show_onlyServer.Tag = "تفصيلي";
            this.ToggleButton_Show_onlyServer.Text = "#";
            this.ToggleButton_Show_onlyServer.UseVisualStyleBackColor = true;
            this.ToggleButton_Show_onlyServer.CheckedChanged += new System.EventHandler(this.ToggleButton_Show_onlyServer_CheckedChanged);
            // 
            // rjLabel27
            // 
            this.rjLabel27.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel27.AutoSize = true;
            this.rjLabel27.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel27.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel27.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel27.LinkLabel = false;
            this.rjLabel27.Location = new System.Drawing.Point(93, 7);
            this.rjLabel27.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel27.Name = "rjLabel27";
            this.rjLabel27.Size = new System.Drawing.Size(118, 17);
            this.rjLabel27.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel27.TabIndex = 55;
            this.rjLabel27.Text = "عرض كروت الروتر فقط";
            // 
            // fpnl_showArchive
            // 
            this.fpnl_showArchive.Controls.Add(this.ToggleButton_Show_Archive);
            this.fpnl_showArchive.Controls.Add(this.rjLabel5);
            this.fpnl_showArchive.Dock = System.Windows.Forms.DockStyle.Top;
            this.fpnl_showArchive.Location = new System.Drawing.Point(2, 73);
            this.fpnl_showArchive.Name = "fpnl_showArchive";
            this.fpnl_showArchive.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.fpnl_showArchive.Size = new System.Drawing.Size(261, 35);
            this.fpnl_showArchive.TabIndex = 58;
            this.fpnl_showArchive.Visible = false;
            // 
            // ToggleButton_Show_Archive
            // 
            this.ToggleButton_Show_Archive.Activated = false;
            this.ToggleButton_Show_Archive.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_Show_Archive.AutoSize = true;
            this.ToggleButton_Show_Archive.Customizable = false;
            this.ToggleButton_Show_Archive.Location = new System.Drawing.Point(211, 3);
            this.ToggleButton_Show_Archive.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_Show_Archive.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_Archive.Name = "ToggleButton_Show_Archive";
            this.ToggleButton_Show_Archive.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_Archive.OFF_Text = null;
            this.ToggleButton_Show_Archive.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_Show_Archive.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_Show_Archive.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_Archive.ON_Text = null;
            this.ToggleButton_Show_Archive.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_Show_Archive.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_Show_Archive.Size = new System.Drawing.Size(50, 25);
            this.ToggleButton_Show_Archive.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_Show_Archive.TabIndex = 52;
            this.ToggleButton_Show_Archive.Tag = "تفصيلي";
            this.ToggleButton_Show_Archive.Text = "#";
            this.ToggleButton_Show_Archive.UseVisualStyleBackColor = true;
            this.ToggleButton_Show_Archive.CheckedChanged += new System.EventHandler(this.ToggleButton_Show_Archive_CheckedChanged);
            // 
            // rjLabel5
            // 
            this.rjLabel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(25, 7);
            this.rjLabel5.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.Size = new System.Drawing.Size(186, 17);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 55;
            this.rjLabel5.Text = "عرض الكروت المحذوفه  (المؤرشفة)";
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.Controls.Add(this.ToggleButton_ByCountSession);
            this.flowLayoutPanel1.Controls.Add(this.rjLabel6);
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Top;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(2, 40);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel1.Size = new System.Drawing.Size(261, 33);
            this.flowLayoutPanel1.TabIndex = 56;
            // 
            // ToggleButton_ByCountSession
            // 
            this.ToggleButton_ByCountSession.Activated = false;
            this.ToggleButton_ByCountSession.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_ByCountSession.AutoSize = true;
            this.ToggleButton_ByCountSession.Customizable = false;
            this.ToggleButton_ByCountSession.Location = new System.Drawing.Point(211, 3);
            this.ToggleButton_ByCountSession.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_ByCountSession.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountSession.Name = "ToggleButton_ByCountSession";
            this.ToggleButton_ByCountSession.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountSession.OFF_Text = null;
            this.ToggleButton_ByCountSession.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_ByCountSession.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountSession.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountSession.ON_Text = null;
            this.ToggleButton_ByCountSession.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_ByCountSession.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountSession.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.ToggleButton_ByCountSession.Size = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountSession.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_ByCountSession.TabIndex = 52;
            this.ToggleButton_ByCountSession.Tag = "تفصيلي";
            this.ToggleButton_ByCountSession.Text = "#";
            this.ToggleButton_ByCountSession.UseVisualStyleBackColor = true;
            this.ToggleButton_ByCountSession.CheckedChanged += new System.EventHandler(this.ToggleButton_ByCountSession_CheckedChanged);
            // 
            // rjLabel6
            // 
            this.rjLabel6.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(28, 7);
            this.rjLabel6.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.Size = new System.Drawing.Size(183, 17);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 55;
            this.rjLabel6.Text = "عرض الكروت بحسب اكثر عدد جلسات";
            // 
            // flowLayoutPanel2
            // 
            this.flowLayoutPanel2.Controls.Add(this.ToggleButton_ByCountProfile);
            this.flowLayoutPanel2.Controls.Add(this.rjLabel8);
            this.flowLayoutPanel2.Dock = System.Windows.Forms.DockStyle.Top;
            this.flowLayoutPanel2.Location = new System.Drawing.Point(2, 2);
            this.flowLayoutPanel2.Name = "flowLayoutPanel2";
            this.flowLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel2.Size = new System.Drawing.Size(261, 38);
            this.flowLayoutPanel2.TabIndex = 57;
            // 
            // ToggleButton_ByCountProfile
            // 
            this.ToggleButton_ByCountProfile.Activated = false;
            this.ToggleButton_ByCountProfile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.ToggleButton_ByCountProfile.AutoSize = true;
            this.ToggleButton_ByCountProfile.Customizable = false;
            this.ToggleButton_ByCountProfile.Location = new System.Drawing.Point(211, 3);
            this.ToggleButton_ByCountProfile.Margin = new System.Windows.Forms.Padding(0, 3, 0, 3);
            this.ToggleButton_ByCountProfile.MinimumSize = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountProfile.Name = "ToggleButton_ByCountProfile";
            this.ToggleButton_ByCountProfile.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountProfile.OFF_Text = null;
            this.ToggleButton_ByCountProfile.OFF_TextColor = System.Drawing.Color.Gray;
            this.ToggleButton_ByCountProfile.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.ToggleButton_ByCountProfile.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountProfile.ON_Text = null;
            this.ToggleButton_ByCountProfile.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.ToggleButton_ByCountProfile.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.ToggleButton_ByCountProfile.Size = new System.Drawing.Size(50, 25);
            this.ToggleButton_ByCountProfile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.ToggleButton_ByCountProfile.TabIndex = 52;
            this.ToggleButton_ByCountProfile.Tag = "تفصيلي";
            this.ToggleButton_ByCountProfile.Text = "#";
            this.ToggleButton_ByCountProfile.UseVisualStyleBackColor = true;
            this.ToggleButton_ByCountProfile.CheckedChanged += new System.EventHandler(this.ToggleButton_ByCountProfile_CheckedChanged);
            // 
            // rjLabel8
            // 
            this.rjLabel8.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(34, 7);
            this.rjLabel8.Margin = new System.Windows.Forms.Padding(0, 7, 0, 3);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.Size = new System.Drawing.Size(177, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 55;
            this.rjLabel8.Text = "عرض الكروت بحسب اكثر عدد باقات";
            // 
            // pnl_side_Finsh_Cards
            // 
            this.pnl_side_Finsh_Cards.AutoScroll = true;
            this.pnl_side_Finsh_Cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_Finsh_Cards.BorderRadius = 10;
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_Validaty);
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_Download);
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_Uptime);
            this.pnl_side_Finsh_Cards.Controls.Add(this.btn_RemoveFinsh_All);
            this.pnl_side_Finsh_Cards.Customizable = true;
            this.pnl_side_Finsh_Cards.Location = new System.Drawing.Point(7, 343);
            this.pnl_side_Finsh_Cards.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_side_Finsh_Cards.Name = "pnl_side_Finsh_Cards";
            this.pnl_side_Finsh_Cards.Padding = new System.Windows.Forms.Padding(2);
            this.pnl_side_Finsh_Cards.Size = new System.Drawing.Size(265, 137);
            this.pnl_side_Finsh_Cards.TabIndex = 58;
            this.pnl_side_Finsh_Cards.Visible = false;
            // 
            // btn_RemoveFinsh_Validaty
            // 
            this.btn_RemoveFinsh_Validaty.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_Validaty.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Validaty.BorderRadius = 8;
            this.btn_RemoveFinsh_Validaty.BorderSize = 1;
            this.btn_RemoveFinsh_Validaty.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_Validaty.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_Validaty.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Validaty.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Validaty.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_Validaty.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_Validaty.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Validaty.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_Validaty.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Validaty.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_Validaty.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Validaty.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Validaty.IconSize = 24;
            this.btn_RemoveFinsh_Validaty.Location = new System.Drawing.Point(2, 101);
            this.btn_RemoveFinsh_Validaty.Name = "btn_RemoveFinsh_Validaty";
            this.btn_RemoveFinsh_Validaty.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Validaty.Size = new System.Drawing.Size(261, 33);
            this.btn_RemoveFinsh_Validaty.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_Validaty.TabIndex = 52;
            this.btn_RemoveFinsh_Validaty.Text = "حذف الكروت المنتهية الصلاحية";
            this.btn_RemoveFinsh_Validaty.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Validaty.Click += new System.EventHandler(this.btn_RemoveFinsh_Validaty_Click_1);
            // 
            // btn_RemoveFinsh_Download
            // 
            this.btn_RemoveFinsh_Download.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_Download.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.BorderRadius = 8;
            this.btn_RemoveFinsh_Download.BorderSize = 1;
            this.btn_RemoveFinsh_Download.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_Download.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_Download.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Download.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Download.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_Download.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_Download.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Download.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_Download.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_Download.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Download.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Download.IconSize = 24;
            this.btn_RemoveFinsh_Download.Location = new System.Drawing.Point(2, 68);
            this.btn_RemoveFinsh_Download.Name = "btn_RemoveFinsh_Download";
            this.btn_RemoveFinsh_Download.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Download.Size = new System.Drawing.Size(261, 33);
            this.btn_RemoveFinsh_Download.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_Download.TabIndex = 51;
            this.btn_RemoveFinsh_Download.Text = "حذف الكروت المنتهية التحميل";
            this.btn_RemoveFinsh_Download.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Download.Click += new System.EventHandler(this.btn_RemoveFinsh_Download_Click);
            // 
            // btn_RemoveFinsh_Uptime
            // 
            this.btn_RemoveFinsh_Uptime.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_Uptime.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.BorderRadius = 8;
            this.btn_RemoveFinsh_Uptime.BorderSize = 1;
            this.btn_RemoveFinsh_Uptime.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_Uptime.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_Uptime.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_Uptime.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_Uptime.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_Uptime.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_Uptime.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_Uptime.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_Uptime.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_Uptime.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_Uptime.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_Uptime.IconSize = 24;
            this.btn_RemoveFinsh_Uptime.Location = new System.Drawing.Point(2, 35);
            this.btn_RemoveFinsh_Uptime.Name = "btn_RemoveFinsh_Uptime";
            this.btn_RemoveFinsh_Uptime.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_Uptime.Size = new System.Drawing.Size(261, 33);
            this.btn_RemoveFinsh_Uptime.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_Uptime.TabIndex = 51;
            this.btn_RemoveFinsh_Uptime.Text = "حذف الكروت المنتهية الوقت";
            this.btn_RemoveFinsh_Uptime.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_Uptime.Click += new System.EventHandler(this.btn_RemoveFinsh_Uptime_Click);
            // 
            // btn_RemoveFinsh_All
            // 
            this.btn_RemoveFinsh_All.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_RemoveFinsh_All.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.BorderRadius = 8;
            this.btn_RemoveFinsh_All.BorderSize = 1;
            this.btn_RemoveFinsh_All.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_RemoveFinsh_All.Dock = System.Windows.Forms.DockStyle.Top;
            this.btn_RemoveFinsh_All.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh_All.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh_All.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_RemoveFinsh_All.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_RemoveFinsh_All.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh_All.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh_All.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh_All.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_RemoveFinsh_All.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh_All.IconSize = 24;
            this.btn_RemoveFinsh_All.Location = new System.Drawing.Point(2, 2);
            this.btn_RemoveFinsh_All.Name = "btn_RemoveFinsh_All";
            this.btn_RemoveFinsh_All.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh_All.Size = new System.Drawing.Size(261, 33);
            this.btn_RemoveFinsh_All.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_RemoveFinsh_All.TabIndex = 51;
            this.btn_RemoveFinsh_All.Text = "حذف جميع الكروت المنتيهة";
            this.btn_RemoveFinsh_All.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh_All.Click += new System.EventHandler(this.btn_RemoveFinsh_All_Click);
            // 
            // panel2_side
            // 
            this.panel2_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel2_side.Dock = System.Windows.Forms.DockStyle.Top;
            this.panel2_side.Location = new System.Drawing.Point(0, 0);
            this.panel2_side.Name = "panel2_side";
            this.panel2_side.Size = new System.Drawing.Size(297, 1);
            this.panel2_side.TabIndex = 51;
            // 
            // panel1_side
            // 
            this.panel1_side.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(128)))), ((int)(((byte)(255)))));
            this.panel1_side.Dock = System.Windows.Forms.DockStyle.Bottom;
            this.panel1_side.Location = new System.Drawing.Point(0, 578);
            this.panel1_side.Name = "panel1_side";
            this.panel1_side.Size = new System.Drawing.Size(297, 1);
            this.panel1_side.TabIndex = 50;
            this.panel1_side.Visible = false;
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(386, 240);
            this.rjLabel2.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.Size = new System.Drawing.Size(66, 17);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 55;
            this.rjLabel2.Text = "الهوتسبوت";
            this.rjLabel2.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.rjLabel2.Visible = false;
            // 
            // CheckBox_NoSmartScript
            // 
            this.CheckBox_NoSmartScript.AutoSize = true;
            this.CheckBox_NoSmartScript.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_NoSmartScript.BorderSize = 1;
            this.CheckBox_NoSmartScript.Check = false;
            this.CheckBox_NoSmartScript.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_NoSmartScript.Customizable = false;
            this.CheckBox_NoSmartScript.Font = new System.Drawing.Font("Cairo", 8.249999F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.CheckBox_NoSmartScript.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_NoSmartScript.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_NoSmartScript.Location = new System.Drawing.Point(151, 24);
            this.CheckBox_NoSmartScript.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_NoSmartScript.Name = "CheckBox_NoSmartScript";
            this.CheckBox_NoSmartScript.Padding = new System.Windows.Forms.Padding(2, 0, 0, 0);
            this.CheckBox_NoSmartScript.Size = new System.Drawing.Size(17, 21);
            this.CheckBox_NoSmartScript.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_NoSmartScript.TabIndex = 45;
            this.CheckBox_NoSmartScript.UseVisualStyleBackColor = true;
            // 
            // pnl_side_sn
            // 
            this.pnl_side_sn.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_sn.BorderRadius = 10;
            this.pnl_side_sn.Controls.Add(this.rjLabel10);
            this.pnl_side_sn.Controls.Add(this.CheckBox_NoSmartScript);
            this.pnl_side_sn.Controls.Add(this.rjLabel1);
            this.pnl_side_sn.Customizable = true;
            this.pnl_side_sn.Location = new System.Drawing.Point(366, 296);
            this.pnl_side_sn.Margin = new System.Windows.Forms.Padding(2);
            this.pnl_side_sn.Name = "pnl_side_sn";
            this.pnl_side_sn.Padding = new System.Windows.Forms.Padding(2);
            this.pnl_side_sn.Size = new System.Drawing.Size(229, 80);
            this.pnl_side_sn.TabIndex = 53;
            this.pnl_side_sn.Visible = false;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Verdana", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(83, 17);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.Size = new System.Drawing.Size(12, 14);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 44;
            this.rjLabel10.Text = "-";
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(8, 24);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(140, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 54;
            this.rjLabel1.Text = "ليس ضمن نظام الصلاحيات";
            this.rjLabel1.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnl_side_datePrint
            // 
            this.pnl_side_datePrint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnl_side_datePrint.BorderRadius = 10;
            this.pnl_side_datePrint.Controls.Add(this.rjLabel13);
            this.pnl_side_datePrint.Controls.Add(this.rjLabel12);
            this.pnl_side_datePrint.Controls.Add(this.rjLabel20);
            this.pnl_side_datePrint.Controls.Add(this.rjLabel19);
            this.pnl_side_datePrint.Customizable = true;
            this.pnl_side_datePrint.Location = new System.Drawing.Point(676, 288);
            this.pnl_side_datePrint.Name = "pnl_side_datePrint";
            this.pnl_side_datePrint.Size = new System.Drawing.Size(229, 88);
            this.pnl_side_datePrint.TabIndex = 52;
            this.pnl_side_datePrint.Visible = false;
            // 
            // rjLabel13
            // 
            this.rjLabel13.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel13.AutoSize = true;
            this.rjLabel13.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel13.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel13.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel13.LinkLabel = false;
            this.rjLabel13.Location = new System.Drawing.Point(134, 11);
            this.rjLabel13.Name = "rjLabel13";
            this.rjLabel13.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel13.Size = new System.Drawing.Size(25, 22);
            this.rjLabel13.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel13.TabIndex = 53;
            this.rjLabel13.Text = "من";
            // 
            // rjLabel12
            // 
            this.rjLabel12.AutoSize = true;
            this.rjLabel12.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel12.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel12.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel12.LinkLabel = false;
            this.rjLabel12.Location = new System.Drawing.Point(129, 51);
            this.rjLabel12.Name = "rjLabel12";
            this.rjLabel12.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel12.Size = new System.Drawing.Size(29, 22);
            this.rjLabel12.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel12.TabIndex = 54;
            this.rjLabel12.Text = "الي";
            // 
            // rjLabel20
            // 
            this.rjLabel20.AutoSize = true;
            this.rjLabel20.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel20.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel20.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel20.LinkLabel = false;
            this.rjLabel20.Location = new System.Drawing.Point(174, 54);
            this.rjLabel20.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel20.Name = "rjLabel20";
            this.rjLabel20.Size = new System.Drawing.Size(46, 17);
            this.rjLabel20.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel20.TabIndex = 35;
            this.rjLabel20.Text = "الطباعة";
            this.rjLabel20.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel19
            // 
            this.rjLabel19.AutoSize = true;
            this.rjLabel19.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel19.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel19.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel19.LinkLabel = false;
            this.rjLabel19.Location = new System.Drawing.Point(179, 32);
            this.rjLabel19.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel19.Name = "rjLabel19";
            this.rjLabel19.Size = new System.Drawing.Size(31, 17);
            this.rjLabel19.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel19.TabIndex = 35;
            this.rjLabel19.Text = "تاريخ";
            this.rjLabel19.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(445, 223);
            this.rjLabel9.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.Size = new System.Drawing.Size(37, 17);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 35;
            this.rjLabel9.Text = "الباقه";
            this.rjLabel9.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.rjLabel9.Visible = false;
            // 
            // lbl_byProfile
            // 
            this.lbl_byProfile.AutoSize = true;
            this.lbl_byProfile.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_byProfile.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_byProfile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_byProfile.LinkLabel = false;
            this.lbl_byProfile.Location = new System.Drawing.Point(445, 178);
            this.lbl_byProfile.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_byProfile.Name = "lbl_byProfile";
            this.lbl_byProfile.Size = new System.Drawing.Size(68, 17);
            this.lbl_byProfile.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_byProfile.TabIndex = 56;
            this.lbl_byProfile.Text = "باقة سمارت";
            this.lbl_byProfile.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            this.lbl_byProfile.Visible = false;
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.None;
            this.dgv.ContextMenuStrip = this.dmAll_Cards;
            this.dgv.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(15, 56);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 35;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 35;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 35;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(964, 457);
            this.dgv.TabIndex = 58;
            this.dgv.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellClick);
            this.dgv.CellDoubleClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellDoubleClick);
            this.dgv.CellFormatting += new System.Windows.Forms.DataGridViewCellFormattingEventHandler(this.dgv_CellFormatting);
            this.dgv.CellValueChanged += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellValueChanged);
            this.dgv.RowPrePaint += new System.Windows.Forms.DataGridViewRowPrePaintEventHandler(this.dgv_RowPrePaint);
            this.dgv.RowsAdded += new System.Windows.Forms.DataGridViewRowsAddedEventHandler(this.dgv_RowsAdded);
            this.dgv.SelectionChanged += new System.EventHandler(this.dgv_Users_SelectionChanged);
            this.dgv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv_MouseDown);
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.View_Hide_toolStripMenuItem,
            this.Restor_ColumnToolStripMenuItem,
            this.toolStripMenuItem2,
            this.toolStripSeparator1,
            this.نسخCtrlcToolStripMenuItem,
            this.نسخالسطركاملToolStripMenuItem,
            this.تصديرالىملفاكسلToolStripMenuItem,
            this.تصديرالىملفنصيToolStripMenuItem,
            this.toolStripSeparator2,
            this.حذفالكروتالمحددةToolStripMenuItem,
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem,
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem,
            this.حذفجلساتالكروتالمحددةToolStripMenuItem,
            this.toolStripSeparator3,
            this.تعطيلالكروتالمحددةToolStripMenuItem,
            this.تفعيلالكروتالمحددةToolStripMenuItem,
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem,
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem,
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem,
            this.toolStripSeparator4,
            this.طباعةالكروتالمحددةToolStripMenuItem,
            this.اعادةشحنالكروتالمحددةToolStripMenuItem,
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem,
            this.Remove_SP_ToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(285, 468);
            // 
            // View_Hide_toolStripMenuItem
            // 
            this.View_Hide_toolStripMenuItem.Checked = true;
            this.View_Hide_toolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.View_Hide_toolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.SaveDGVToolStripMenuItem,
            this.toolStripSeparator5,
            this.Status_ToolStripMenuItem,
            this.UserName_ToolStripMenuItem,
            this.Password_ToolStripMenuItem,
            this.SN_ToolStripMenuItem,
            this.Str_price_ToolStripMenuItem,
            this.Profile_ToolStripMenuItem,
            this.SellingPoint_ToolStripMenuItem,
            this.BachCards_ToolStripMenuItem,
            this.NumberPrint_toolStripMenuItem,
            this.Str_UptimeLimit_ToolStripMenuItem,
            this.Str_TransferLimit_ToolStripMenuItem,
            this.Str_limitUptime_ToolStripMenuItem,
            this.Str_limitbytestotal_ToolStripMenuItem,
            this.Str_UptimeUsed_ToolStripMenuItem,
            this.Str_DownloadUsed_ToolStripMenuItem,
            this.Str_UploadUsed_ToolStripMenuItem,
            this.Str_ProfileTimeLeft_ToolStripMenuItem,
            this.Str_ProfileTransferLeft_ToolStripMenuItem,
            this.Str_Up_Down_ToolStripMenuItem,
            this.dt_RegDate_ToolStripMenuItem,
            this.dt_LastSeenAt_ToolStripMenuItem,
            this.dt_FirstUse_ToolStripMenuItem,
            this.Str_ProfileTillTime_ToolStripMenuItem,
            this.الايامالمتبقيةToolStripMenuItem,
            this.Descr_ToolStripMenuItem,
            this.comment_ToolStripMenuItem,
            this.Count_profile_ToolStripMenuItem,
            this.CountSession_ToolStripMenuItem,
            this.profileHotspot_ToolStripMenuItem,
            this.Sn_Archive_ToolStripMenuItem,
            this.server_ToolStripMenuItem,
            this.email_ToolStripMenuItem,
            this.PageNumberToolStripMenuItem,
            this.Str_SmartValidatiy_Add_ToolStripMenuItem});
            this.View_Hide_toolStripMenuItem.Name = "View_Hide_toolStripMenuItem";
            this.View_Hide_toolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.View_Hide_toolStripMenuItem.Text = "عرض واخفاء الاعمدة";
            // 
            // SaveDGVToolStripMenuItem
            // 
            this.SaveDGVToolStripMenuItem.Font = new System.Drawing.Font("Segoe UI", 10F);
            this.SaveDGVToolStripMenuItem.IconChar = FontAwesome.Sharp.IconChar.FloppyDisk;
            this.SaveDGVToolStripMenuItem.IconColor = System.Drawing.Color.White;
            this.SaveDGVToolStripMenuItem.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.SaveDGVToolStripMenuItem.IconSize = 70;
            this.SaveDGVToolStripMenuItem.Name = "SaveDGVToolStripMenuItem";
            this.SaveDGVToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SaveDGVToolStripMenuItem.Text = "حفظ ترتيب الجدول كافتراضي";
            this.SaveDGVToolStripMenuItem.Click += new System.EventHandler(this.SaveDGVToolStripMenuItem_Click);
            // 
            // toolStripSeparator5
            // 
            this.toolStripSeparator5.Name = "toolStripSeparator5";
            this.toolStripSeparator5.Size = new System.Drawing.Size(242, 6);
            // 
            // Status_ToolStripMenuItem
            // 
            this.Status_ToolStripMenuItem.Checked = true;
            this.Status_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Status_ToolStripMenuItem.Name = "Status_ToolStripMenuItem";
            this.Status_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Status_ToolStripMenuItem.Tag = "Str_Status";
            this.Status_ToolStripMenuItem.Text = "الحــــالــــة";
            this.Status_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // UserName_ToolStripMenuItem
            // 
            this.UserName_ToolStripMenuItem.Checked = true;
            this.UserName_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.UserName_ToolStripMenuItem.Name = "UserName_ToolStripMenuItem";
            this.UserName_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.UserName_ToolStripMenuItem.Tag = "UserName";
            this.UserName_ToolStripMenuItem.Text = "الاســـــــم";
            this.UserName_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Password_ToolStripMenuItem
            // 
            this.Password_ToolStripMenuItem.Checked = true;
            this.Password_ToolStripMenuItem.CheckState = System.Windows.Forms.CheckState.Checked;
            this.Password_ToolStripMenuItem.Name = "Password_ToolStripMenuItem";
            this.Password_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Password_ToolStripMenuItem.Tag = "Password";
            this.Password_ToolStripMenuItem.Text = "كلمة المرور";
            this.Password_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SN_ToolStripMenuItem
            // 
            this.SN_ToolStripMenuItem.Name = "SN_ToolStripMenuItem";
            this.SN_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SN_ToolStripMenuItem.Tag = "Sn";
            this.SN_ToolStripMenuItem.Text = "الرقم التسلسلي";
            this.SN_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_price_ToolStripMenuItem
            // 
            this.Str_price_ToolStripMenuItem.Name = "Str_price_ToolStripMenuItem";
            this.Str_price_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_price_ToolStripMenuItem.Tag = "Str_Price";
            this.Str_price_ToolStripMenuItem.Text = "الســـــــعر";
            this.Str_price_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Profile_ToolStripMenuItem
            // 
            this.Profile_ToolStripMenuItem.Name = "Profile_ToolStripMenuItem";
            this.Profile_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Profile_ToolStripMenuItem.Tag = "ProfileName";
            this.Profile_ToolStripMenuItem.Text = "البــــــــاقة";
            this.Profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // SellingPoint_ToolStripMenuItem
            // 
            this.SellingPoint_ToolStripMenuItem.Name = "SellingPoint_ToolStripMenuItem";
            this.SellingPoint_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.SellingPoint_ToolStripMenuItem.Tag = "SpName";
            this.SellingPoint_ToolStripMenuItem.Text = "نقـــــطة البيع";
            this.SellingPoint_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // BachCards_ToolStripMenuItem
            // 
            this.BachCards_ToolStripMenuItem.Name = "BachCards_ToolStripMenuItem";
            this.BachCards_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.BachCards_ToolStripMenuItem.Tag = "BatchCardId";
            this.BachCards_ToolStripMenuItem.Text = "رقم الـــــدفعــــــه";
            this.BachCards_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // NumberPrint_toolStripMenuItem
            // 
            this.NumberPrint_toolStripMenuItem.Name = "NumberPrint_toolStripMenuItem";
            this.NumberPrint_toolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.NumberPrint_toolStripMenuItem.Tag = "NumberPrint";
            this.NumberPrint_toolStripMenuItem.Text = "رقم الـــطبــــعــــة";
            this.NumberPrint_toolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeLimit_ToolStripMenuItem
            // 
            this.Str_UptimeLimit_ToolStripMenuItem.Name = "Str_UptimeLimit_ToolStripMenuItem";
            this.Str_UptimeLimit_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UptimeLimit_ToolStripMenuItem.Tag = "Str_UptimeLimit";
            this.Str_UptimeLimit_ToolStripMenuItem.Text = "وقت  الباقة المسموح";
            this.Str_UptimeLimit_ToolStripMenuItem.Visible = false;
            this.Str_UptimeLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_TransferLimit_ToolStripMenuItem
            // 
            this.Str_TransferLimit_ToolStripMenuItem.Name = "Str_TransferLimit_ToolStripMenuItem";
            this.Str_TransferLimit_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_TransferLimit_ToolStripMenuItem.Tag = "Str_TransferLimit";
            this.Str_TransferLimit_ToolStripMenuItem.Text = "تنزيل الباقة المسموح";
            this.Str_TransferLimit_ToolStripMenuItem.Visible = false;
            this.Str_TransferLimit_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_limitUptime_ToolStripMenuItem
            // 
            this.Str_limitUptime_ToolStripMenuItem.Name = "Str_limitUptime_ToolStripMenuItem";
            this.Str_limitUptime_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_limitUptime_ToolStripMenuItem.Tag = "Str_limitUptime";
            this.Str_limitUptime_ToolStripMenuItem.Text = "الوقت المسموح";
            this.Str_limitUptime_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_limitbytestotal_ToolStripMenuItem
            // 
            this.Str_limitbytestotal_ToolStripMenuItem.Name = "Str_limitbytestotal_ToolStripMenuItem";
            this.Str_limitbytestotal_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_limitbytestotal_ToolStripMenuItem.Tag = "Str_limitbytestotal";
            this.Str_limitbytestotal_ToolStripMenuItem.Text = "التنزيل المسموح";
            this.Str_limitbytestotal_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UptimeUsed_ToolStripMenuItem
            // 
            this.Str_UptimeUsed_ToolStripMenuItem.Name = "Str_UptimeUsed_ToolStripMenuItem";
            this.Str_UptimeUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UptimeUsed_ToolStripMenuItem.Tag = "Str_UptimeUsed";
            this.Str_UptimeUsed_ToolStripMenuItem.Text = "الوقت المستخدم";
            this.Str_UptimeUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_DownloadUsed_ToolStripMenuItem
            // 
            this.Str_DownloadUsed_ToolStripMenuItem.Name = "Str_DownloadUsed_ToolStripMenuItem";
            this.Str_DownloadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_DownloadUsed_ToolStripMenuItem.Tag = "Str_DownloadUsed";
            this.Str_DownloadUsed_ToolStripMenuItem.Text = "التحميل المستخدم";
            this.Str_DownloadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_UploadUsed_ToolStripMenuItem
            // 
            this.Str_UploadUsed_ToolStripMenuItem.Name = "Str_UploadUsed_ToolStripMenuItem";
            this.Str_UploadUsed_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_UploadUsed_ToolStripMenuItem.Tag = "Str_UploadUsed";
            this.Str_UploadUsed_ToolStripMenuItem.Text = "الرقع المستخدم";
            this.Str_UploadUsed_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTimeLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Name = "Str_ProfileTimeLeft_ToolStripMenuItem";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Tag = "Str_ProfileTimeLeft";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Text = "الوقت المتبقي";
            this.Str_ProfileTimeLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTransferLeft_ToolStripMenuItem
            // 
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Name = "Str_ProfileTransferLeft_ToolStripMenuItem";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Tag = "Str_ProfileTransferLeft";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Text = "التحميل المتبقي";
            this.Str_ProfileTransferLeft_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_Up_Down_ToolStripMenuItem
            // 
            this.Str_Up_Down_ToolStripMenuItem.Name = "Str_Up_Down_ToolStripMenuItem";
            this.Str_Up_Down_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_Up_Down_ToolStripMenuItem.Tag = "Str_Up_Down";
            this.Str_Up_Down_ToolStripMenuItem.Text = "تحميـــــل+رفــــع";
            this.Str_Up_Down_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_RegDate_ToolStripMenuItem
            // 
            this.dt_RegDate_ToolStripMenuItem.Name = "dt_RegDate_ToolStripMenuItem";
            this.dt_RegDate_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_RegDate_ToolStripMenuItem.Tag = "RegDate";
            this.dt_RegDate_ToolStripMenuItem.Text = "تـــاريخ الاضــــافة";
            this.dt_RegDate_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_LastSeenAt_ToolStripMenuItem
            // 
            this.dt_LastSeenAt_ToolStripMenuItem.Name = "dt_LastSeenAt_ToolStripMenuItem";
            this.dt_LastSeenAt_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_LastSeenAt_ToolStripMenuItem.Tag = "LastSeenAt";
            this.dt_LastSeenAt_ToolStripMenuItem.Text = "اخــــــر ظهــــور";
            this.dt_LastSeenAt_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // dt_FirstUse_ToolStripMenuItem
            // 
            this.dt_FirstUse_ToolStripMenuItem.Name = "dt_FirstUse_ToolStripMenuItem";
            this.dt_FirstUse_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.dt_FirstUse_ToolStripMenuItem.Tag = "FirsLogin";
            this.dt_FirstUse_ToolStripMenuItem.Text = "اول دخـــــــــول";
            this.dt_FirstUse_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_ProfileTillTime_ToolStripMenuItem
            // 
            this.Str_ProfileTillTime_ToolStripMenuItem.Name = "Str_ProfileTillTime_ToolStripMenuItem";
            this.Str_ProfileTillTime_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_ProfileTillTime_ToolStripMenuItem.Tag = "Str_ProfileTillTime";
            this.Str_ProfileTillTime_ToolStripMenuItem.Text = "تــــاريخ الانتــــهاء";
            this.Str_ProfileTillTime_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // الايامالمتبقيةToolStripMenuItem
            // 
            this.الايامالمتبقيةToolStripMenuItem.Name = "الايامالمتبقيةToolStripMenuItem";
            this.الايامالمتبقيةToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.الايامالمتبقيةToolStripMenuItem.Tag = "Str_DaysLeft";
            this.الايامالمتبقيةToolStripMenuItem.Text = "الايام المتبقية";
            this.الايامالمتبقيةToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Descr_ToolStripMenuItem
            // 
            this.Descr_ToolStripMenuItem.Name = "Descr_ToolStripMenuItem";
            this.Descr_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Descr_ToolStripMenuItem.Tag = "descr";
            this.Descr_ToolStripMenuItem.Text = "ملاحــظة";
            this.Descr_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // comment_ToolStripMenuItem
            // 
            this.comment_ToolStripMenuItem.Name = "comment_ToolStripMenuItem";
            this.comment_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.comment_ToolStripMenuItem.Tag = "Comment";
            this.comment_ToolStripMenuItem.Text = "تعلـــــــيق";
            this.comment_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Count_profile_ToolStripMenuItem
            // 
            this.Count_profile_ToolStripMenuItem.Name = "Count_profile_ToolStripMenuItem";
            this.Count_profile_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Count_profile_ToolStripMenuItem.Tag = "CountProfile";
            this.Count_profile_ToolStripMenuItem.Text = "عدد الباقات";
            this.Count_profile_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // CountSession_ToolStripMenuItem
            // 
            this.CountSession_ToolStripMenuItem.Name = "CountSession_ToolStripMenuItem";
            this.CountSession_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.CountSession_ToolStripMenuItem.Tag = "CountSession";
            this.CountSession_ToolStripMenuItem.Text = "عدد جلسات الكرت";
            this.CountSession_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // profileHotspot_ToolStripMenuItem
            // 
            this.profileHotspot_ToolStripMenuItem.Name = "profileHotspot_ToolStripMenuItem";
            this.profileHotspot_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.profileHotspot_ToolStripMenuItem.Tag = "ProfileHotspot";
            this.profileHotspot_ToolStripMenuItem.Text = "بروفايل الهوتسبوت";
            this.profileHotspot_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Sn_Archive_ToolStripMenuItem
            // 
            this.Sn_Archive_ToolStripMenuItem.Name = "Sn_Archive_ToolStripMenuItem";
            this.Sn_Archive_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Sn_Archive_ToolStripMenuItem.Tag = "Sn_Archive";
            this.Sn_Archive_ToolStripMenuItem.Text = "تسلسل ارشيف المعلقات";
            this.Sn_Archive_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // server_ToolStripMenuItem
            // 
            this.server_ToolStripMenuItem.Name = "server_ToolStripMenuItem";
            this.server_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.server_ToolStripMenuItem.Tag = "server";
            this.server_ToolStripMenuItem.Text = "السيرفر";
            this.server_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // email_ToolStripMenuItem
            // 
            this.email_ToolStripMenuItem.Name = "email_ToolStripMenuItem";
            this.email_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.email_ToolStripMenuItem.Tag = "Email";
            this.email_ToolStripMenuItem.Text = "الايميل";
            this.email_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // PageNumberToolStripMenuItem
            // 
            this.PageNumberToolStripMenuItem.Name = "PageNumberToolStripMenuItem";
            this.PageNumberToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.PageNumberToolStripMenuItem.Tag = "PageNumber";
            this.PageNumberToolStripMenuItem.Text = "رقم الصفحة";
            this.PageNumberToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Str_SmartValidatiy_Add_ToolStripMenuItem
            // 
            this.Str_SmartValidatiy_Add_ToolStripMenuItem.Name = "Str_SmartValidatiy_Add_ToolStripMenuItem";
            this.Str_SmartValidatiy_Add_ToolStripMenuItem.Size = new System.Drawing.Size(245, 26);
            this.Str_SmartValidatiy_Add_ToolStripMenuItem.Tag = "Str_SmartValidatiy_Add";
            this.Str_SmartValidatiy_Add_ToolStripMenuItem.Text = "صلاحيات سمارت";
            this.Str_SmartValidatiy_Add_ToolStripMenuItem.Click += new System.EventHandler(this.Change_Items_ToolStripMenuItem_Click);
            // 
            // Restor_ColumnToolStripMenuItem
            // 
            this.Restor_ColumnToolStripMenuItem.Name = "Restor_ColumnToolStripMenuItem";
            this.Restor_ColumnToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.Restor_ColumnToolStripMenuItem.Text = "استعادة الاعمدة الافتراضيه";
            this.Restor_ColumnToolStripMenuItem.Click += new System.EventHandler(this.Restor_ColumnToolStripMenuItem_Click);
            // 
            // toolStripMenuItem2
            // 
            this.toolStripMenuItem2.Name = "toolStripMenuItem2";
            this.toolStripMenuItem2.Size = new System.Drawing.Size(284, 22);
            this.toolStripMenuItem2.Text = "عرض معلومات الكرت المحدد";
            this.toolStripMenuItem2.Click += new System.EventHandler(this.toolStripMenuItem2_Click);
            // 
            // toolStripSeparator1
            // 
            this.toolStripSeparator1.Name = "toolStripSeparator1";
            this.toolStripSeparator1.Size = new System.Drawing.Size(281, 6);
            // 
            // نسخCtrlcToolStripMenuItem
            // 
            this.نسخCtrlcToolStripMenuItem.Name = "نسخCtrlcToolStripMenuItem";
            this.نسخCtrlcToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.نسخCtrlcToolStripMenuItem.Text = "نسخ                 ctrl+c";
            this.نسخCtrlcToolStripMenuItem.Click += new System.EventHandler(this.نسخCtrlcToolStripMenuItem_Click);
            // 
            // نسخالسطركاملToolStripMenuItem
            // 
            this.نسخالسطركاملToolStripMenuItem.Name = "نسخالسطركاملToolStripMenuItem";
            this.نسخالسطركاملToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.نسخالسطركاملToolStripMenuItem.Text = "نسخ السطر كامل ";
            this.نسخالسطركاملToolStripMenuItem.Click += new System.EventHandler(this.نسخالسطركاملToolStripMenuItem_Click);
            // 
            // تصديرالىملفاكسلToolStripMenuItem
            // 
            this.تصديرالىملفاكسلToolStripMenuItem.Name = "تصديرالىملفاكسلToolStripMenuItem";
            this.تصديرالىملفاكسلToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تصديرالىملفاكسلToolStripMenuItem.Text = "تصدير الى ملف اكسل";
            this.تصديرالىملفاكسلToolStripMenuItem.Click += new System.EventHandler(this.تصديرالىملفاكسلToolStripMenuItem_Click);
            // 
            // تصديرالىملفنصيToolStripMenuItem
            // 
            this.تصديرالىملفنصيToolStripMenuItem.DropDownItems.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.تصديرالاسمفقطToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem,
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem});
            this.تصديرالىملفنصيToolStripMenuItem.Name = "تصديرالىملفنصيToolStripMenuItem";
            this.تصديرالىملفنصيToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تصديرالىملفنصيToolStripMenuItem.Text = "تصدير الى ملف نصي";
            // 
            // تصديرالاسمفقطToolStripMenuItem
            // 
            this.تصديرالاسمفقطToolStripMenuItem.Name = "تصديرالاسمفقطToolStripMenuItem";
            this.تصديرالاسمفقطToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمفقطToolStripMenuItem.Text = "تصدير الاسم فقط";
            this.تصديرالاسمفقطToolStripMenuItem.Click += new System.EventHandler(this.تصديرالاسمفقطToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور";
            this.تصديرالاسمكلمةالمرورToolStripMenuItem.Click += new System.EventHandler(this.تصديرالاسمكلمةالمرورToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورالباقةToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة";
            this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem.Click += new System.EventHandler(this.تصديرالاسمكلمةالمرورالباقةToolStripMenuItem_Click);
            // 
            // تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem
            // 
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Name = "تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem";
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Size = new System.Drawing.Size(309, 22);
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Text = "تصدير الاسم + كلمة المرور + الباقة + نقطة البيع";
            this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem.Click += new System.EventHandler(this.تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem_Click);
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(281, 6);
            // 
            // حذفالكروتالمحددةToolStripMenuItem
            // 
            this.حذفالكروتالمحددةToolStripMenuItem.Name = "حذفالكروتالمحددةToolStripMenuItem";
            this.حذفالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.حذفالكروتالمحددةToolStripMenuItem.Text = "حذف الكروت المحددة";
            this.حذفالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // حذفالكروتالمحددةمنالارشيفToolStripMenuItem
            // 
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem.Name = "حذفالكروتالمحددةمنالارشيفToolStripMenuItem";
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem.Text = "حذف الكروت المحددة من  الارشيف";
            this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem.Click += new System.EventHandler(this.حذفالكروتالمحددةمنالارشيفToolStripMenuItem_Click);
            // 
            // حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem
            // 
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem.Name = "حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem";
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem.Text = "حذف الكروت المحددة من الراوتر والارشيف";
            this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem.Click += new System.EventHandler(this.حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem_Click);
            // 
            // حذفجلساتالكروتالمحددةToolStripMenuItem
            // 
            this.حذفجلساتالكروتالمحددةToolStripMenuItem.Name = "حذفجلساتالكروتالمحددةToolStripMenuItem";
            this.حذفجلساتالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.حذفجلساتالكروتالمحددةToolStripMenuItem.Text = "حذف جلسات سمارت للكروت المحددة";
            this.حذفجلساتالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.حذفجلساتالكروتالمحددةToolStripMenuItem_Click);
            // 
            // toolStripSeparator3
            // 
            this.toolStripSeparator3.Name = "toolStripSeparator3";
            this.toolStripSeparator3.Size = new System.Drawing.Size(281, 6);
            // 
            // تعطيلالكروتالمحددةToolStripMenuItem
            // 
            this.تعطيلالكروتالمحددةToolStripMenuItem.Name = "تعطيلالكروتالمحددةToolStripMenuItem";
            this.تعطيلالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تعطيلالكروتالمحددةToolStripMenuItem.Text = "تعطيل الكروت المحددة ";
            this.تعطيلالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.btnDisable_Click);
            // 
            // تفعيلالكروتالمحددةToolStripMenuItem
            // 
            this.تفعيلالكروتالمحددةToolStripMenuItem.Name = "تفعيلالكروتالمحددةToolStripMenuItem";
            this.تفعيلالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تفعيلالكروتالمحددةToolStripMenuItem.Text = "تفعيل الكروت المحددة";
            this.تفعيلالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.btnEnable_Click);
            // 
            // تصفيرعدادالكروتالمحددةToolStripMenuItem
            // 
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem.Name = "تصفيرعدادالكروتالمحددةToolStripMenuItem";
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem.Text = "تصفير عداد الكروت المحددة";
            this.تصفيرعدادالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.تصفيرعدادالكروتالمحددةToolStripMenuItem_Click);
            // 
            // ربطالكروتباولجهازاستخدامToolStripMenuItem
            // 
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem.Name = "ربطالكروتباولجهازاستخدامToolStripMenuItem";
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem.Text = "ربط الكروت باول جهاز استخدام";
            this.ربطالكروتباولجهازاستخدامToolStripMenuItem.Visible = false;
            // 
            // الغاءربطالكروتباولجهازاستخدامToolStripMenuItem
            // 
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem.Name = "الغاءربطالكروتباولجهازاستخدامToolStripMenuItem";
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem.Text = "الغاء ربط الماك المرتبط من الكروت المحدده";
            this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem.Click += new System.EventHandler(this.الغاءربطالكروتباولجهازاستخدامToolStripMenuItem_Click);
            // 
            // toolStripSeparator4
            // 
            this.toolStripSeparator4.Name = "toolStripSeparator4";
            this.toolStripSeparator4.Size = new System.Drawing.Size(281, 6);
            // 
            // طباعةالكروتالمحددةToolStripMenuItem
            // 
            this.طباعةالكروتالمحددةToolStripMenuItem.Name = "طباعةالكروتالمحددةToolStripMenuItem";
            this.طباعةالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.طباعةالكروتالمحددةToolStripMenuItem.Text = "طباعة الكروت المحددة";
            this.طباعةالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.طباعةالكروتالمحددةToolStripMenuItem_Click);
            // 
            // اعادةشحنالكروتالمحددةToolStripMenuItem
            // 
            this.اعادةشحنالكروتالمحددةToolStripMenuItem.Name = "اعادةشحنالكروتالمحددةToolStripMenuItem";
            this.اعادةشحنالكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.اعادةشحنالكروتالمحددةToolStripMenuItem.Text = "اضافة رصيد للكروت المحدده";
            this.اعادةشحنالكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.اعادةشحنالكروتالمحددةToolStripMenuItem_Click);
            // 
            // تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem
            // 
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem.Name = "تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem";
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem.Text = "تغير نقطة البيع للكروت المحددة";
            this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem.Click += new System.EventHandler(this.تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem_Click);
            // 
            // Remove_SP_ToolStripMenuItem
            // 
            this.Remove_SP_ToolStripMenuItem.Name = "Remove_SP_ToolStripMenuItem";
            this.Remove_SP_ToolStripMenuItem.Size = new System.Drawing.Size(284, 22);
            this.Remove_SP_ToolStripMenuItem.Text = "حذف نقطة البيع من الكروت المحدده";
            this.Remove_SP_ToolStripMenuItem.Click += new System.EventHandler(this.Remove_SP_ToolStripMenuItem_Click);
            // 
            // btnEnable
            // 
            this.btnEnable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEnable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnEnable.BorderRadius = 5;
            this.btnEnable.BorderSize = 1;
            this.btnEnable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnEnable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnEnable.FlatAppearance.BorderSize = 0;
            this.btnEnable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnEnable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnEnable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnEnable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnEnable.ForeColor = System.Drawing.Color.White;
            this.btnEnable.IconChar = FontAwesome.Sharp.IconChar.Check;
            this.btnEnable.IconColor = System.Drawing.Color.White;
            this.btnEnable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnEnable.IconSize = 20;
            this.btnEnable.Location = new System.Drawing.Point(789, 5);
            this.btnEnable.Name = "btnEnable";
            this.btnEnable.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnEnable.Size = new System.Drawing.Size(28, 33);
            this.btnEnable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnEnable.TabIndex = 70;
            this.btnEnable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnEnable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnEnable.UseVisualStyleBackColor = false;
            this.btnEnable.Click += new System.EventHandler(this.btnEnable_Click);
            // 
            // rjButton2
            // 
            this.rjButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjButton2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderRadius = 5;
            this.rjButton2.BorderSize = 1;
            this.rjButton2.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton2.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.rjButton2.FlatAppearance.BorderSize = 0;
            this.rjButton2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton2.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.rjButton2.ForeColor = System.Drawing.Color.White;
            this.rjButton2.IconChar = FontAwesome.Sharp.IconChar.Edit;
            this.rjButton2.IconColor = System.Drawing.Color.White;
            this.rjButton2.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton2.IconSize = 19;
            this.rjButton2.Location = new System.Drawing.Point(270, 188);
            this.rjButton2.Name = "rjButton2";
            this.rjButton2.Size = new System.Drawing.Size(29, 34);
            this.rjButton2.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton2.TabIndex = 64;
            this.rjButton2.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.rjButton2.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.rjButton2.UseVisualStyleBackColor = false;
            this.rjButton2.Visible = false;
            // 
            // btnDisable
            // 
            this.btnDisable.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDisable.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnDisable.BorderRadius = 5;
            this.btnDisable.BorderSize = 1;
            this.btnDisable.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnDisable.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnDisable.FlatAppearance.BorderSize = 0;
            this.btnDisable.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnDisable.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnDisable.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDisable.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDisable.ForeColor = System.Drawing.Color.White;
            this.btnDisable.IconChar = FontAwesome.Sharp.IconChar.Remove;
            this.btnDisable.IconColor = System.Drawing.Color.White;
            this.btnDisable.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDisable.IconSize = 19;
            this.btnDisable.Location = new System.Drawing.Point(817, 5);
            this.btnDisable.Name = "btnDisable";
            this.btnDisable.Size = new System.Drawing.Size(28, 33);
            this.btnDisable.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDisable.TabIndex = 65;
            this.btnDisable.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDisable.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnDisable.UseVisualStyleBackColor = false;
            this.btnDisable.Click += new System.EventHandler(this.btnDisable_Click);
            // 
            // btn_search_Router
            // 
            this.btn_search_Router.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search_Router.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_search_Router.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search_Router.BorderRadius = 1;
            this.btn_search_Router.BorderSize = 1;
            this.btn_search_Router.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search_Router.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search_Router.FlatAppearance.BorderSize = 0;
            this.btn_search_Router.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_search_Router.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_search_Router.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search_Router.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search_Router.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search_Router.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlassLocation;
            this.btn_search_Router.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search_Router.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search_Router.IconSize = 24;
            this.btn_search_Router.Location = new System.Drawing.Point(198, 193);
            this.btn_search_Router.Name = "btn_search_Router";
            this.btn_search_Router.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search_Router.Size = new System.Drawing.Size(30, 25);
            this.btn_search_Router.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search_Router.TabIndex = 61;
            this.btn_search_Router.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search_Router.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search_Router.UseVisualStyleBackColor = false;
            this.btn_search_Router.Visible = false;
            // 
            // btn_search
            // 
            this.btn_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 2;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(647, 8);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 30);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 60;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Tahoma", 10F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(674, 8);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(115, 28);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 59;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // jToggleButton_Year
            // 
            this.jToggleButton_Year.Activated = false;
            this.jToggleButton_Year.AutoSize = true;
            this.jToggleButton_Year.Customizable = false;
            this.jToggleButton_Year.Location = new System.Drawing.Point(282, 193);
            this.jToggleButton_Year.MinimumSize = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Name = "jToggleButton_Year";
            this.jToggleButton_Year.OFF_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.OFF_Text = null;
            this.jToggleButton_Year.OFF_TextColor = System.Drawing.Color.Gray;
            this.jToggleButton_Year.OFF_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(171)))), ((int)(((byte)(171)))), ((int)(((byte)(171)))));
            this.jToggleButton_Year.ON_BackBorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.ON_Text = null;
            this.jToggleButton_Year.ON_TextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.jToggleButton_Year.ON_ToggleColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.jToggleButton_Year.Size = new System.Drawing.Size(50, 25);
            this.jToggleButton_Year.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.jToggleButton_Year.TabIndex = 66;
            this.jToggleButton_Year.Tag = "تفصيلي";
            this.jToggleButton_Year.Text = "#";
            this.jToggleButton_Year.UseVisualStyleBackColor = true;
            this.jToggleButton_Year.Visible = false;
            // 
            // rjLabel4
            // 
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Cairo", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(188, 82);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.Size = new System.Drawing.Size(183, 23);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 68;
            this.rjLabel4.Text = "ترتيب الكروت بحسب اكثر عدد جلسات";
            this.rjLabel4.Visible = false;
            // 
            // btnRefresh
            // 
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 1;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRefresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 18;
            this.btnRefresh.Location = new System.Drawing.Point(88, 5);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(29, 33);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 63;
            this.btnRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh, "تحديث الكروت من الروتر");
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // timer_SideBar
            // 
            this.timer_SideBar.Interval = 15;
            this.timer_SideBar.Tick += new System.EventHandler(this.timer_SideBar_Tick);
            // 
            // btnRefresh_DB
            // 
            this.btnRefresh_DB.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh_DB.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh_DB.BorderRadius = 5;
            this.btnRefresh_DB.BorderSize = 1;
            this.btnRefresh_DB.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnRefresh_DB.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btnRefresh_DB.FlatAppearance.BorderSize = 0;
            this.btnRefresh_DB.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(51)))), ((int)(((byte)(149)))), ((int)(((byte)(106)))));
            this.btnRefresh_DB.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(48)))), ((int)(((byte)(139)))), ((int)(((byte)(99)))));
            this.btnRefresh_DB.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh_DB.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btnRefresh_DB.ForeColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconChar = FontAwesome.Sharp.IconChar.Recycle;
            this.btnRefresh_DB.IconColor = System.Drawing.Color.White;
            this.btnRefresh_DB.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh_DB.IconSize = 18;
            this.btnRefresh_DB.Location = new System.Drawing.Point(872, 5);
            this.btnRefresh_DB.Name = "btnRefresh_DB";
            this.btnRefresh_DB.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnRefresh_DB.Size = new System.Drawing.Size(87, 33);
            this.btnRefresh_DB.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh_DB.TabIndex = 89;
            this.btnRefresh_DB.Text = "تحديث";
            this.btnRefresh_DB.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnRefresh_DB.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnRefresh_DB, "تحديث الكروت في الواجة");
            this.btnRefresh_DB.UseVisualStyleBackColor = false;
            this.btnRefresh_DB.Click += new System.EventHandler(this.btnRefresh_DB_Click);
            // 
            // btn_Filter
            // 
            this.btn_Filter.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Filter.BorderRadius = 5;
            this.btn_Filter.BorderSize = 1;
            this.btn_Filter.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Filter.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Filter.FlatAppearance.BorderSize = 0;
            this.btn_Filter.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Filter.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Filter.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Filter.ForeColor = System.Drawing.Color.White;
            this.btn_Filter.IconChar = FontAwesome.Sharp.IconChar.Filter;
            this.btn_Filter.IconColor = System.Drawing.Color.White;
            this.btn_Filter.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Filter.IconSize = 17;
            this.btn_Filter.Location = new System.Drawing.Point(11, 5);
            this.btn_Filter.Name = "btn_Filter";
            this.btn_Filter.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Filter.Size = new System.Drawing.Size(76, 33);
            this.btn_Filter.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Filter.TabIndex = 90;
            this.btn_Filter.Text = "فلترة";
            this.btn_Filter.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Filter.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btn_Filter, "فلتره");
            this.btn_Filter.UseVisualStyleBackColor = false;
            this.btn_Filter.Click += new System.EventHandler(this.btn_Filter_Click);
            // 
            // btnFirst
            // 
            this.btnFirst.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnFirst.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnFirst.BorderRadius = 5;
            this.btnFirst.BorderSize = 1;
            this.btnFirst.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnFirst.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnFirst.FlatAppearance.BorderSize = 0;
            this.btnFirst.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnFirst.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnFirst.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnFirst.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnFirst.ForeColor = System.Drawing.Color.White;
            this.btnFirst.IconChar = FontAwesome.Sharp.IconChar.BackwardStep;
            this.btnFirst.IconColor = System.Drawing.Color.White;
            this.btnFirst.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnFirst.IconSize = 18;
            this.btnFirst.Location = new System.Drawing.Point(-3, 3);
            this.btnFirst.Name = "btnFirst";
            this.btnFirst.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnFirst.Size = new System.Drawing.Size(29, 32);
            this.btnFirst.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnFirst.TabIndex = 89;
            this.btnFirst.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnFirst.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnFirst, "اخر صفحة");
            this.btnFirst.UseVisualStyleBackColor = false;
            this.btnFirst.Click += new System.EventHandler(this.btnFirst_Click);
            // 
            // btnNext
            // 
            this.btnNext.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNext.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnNext.BorderRadius = 5;
            this.btnNext.BorderSize = 1;
            this.btnNext.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnNext.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnNext.FlatAppearance.BorderSize = 0;
            this.btnNext.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnNext.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnNext.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnNext.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnNext.ForeColor = System.Drawing.Color.White;
            this.btnNext.IconChar = FontAwesome.Sharp.IconChar.ArrowLeft;
            this.btnNext.IconColor = System.Drawing.Color.White;
            this.btnNext.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnNext.IconSize = 18;
            this.btnNext.Location = new System.Drawing.Point(26, 3);
            this.btnNext.Name = "btnNext";
            this.btnNext.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnNext.Size = new System.Drawing.Size(29, 32);
            this.btnNext.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnNext.TabIndex = 89;
            this.btnNext.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnNext.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnNext, "الصفحة التاليه");
            this.btnNext.UseVisualStyleBackColor = false;
            this.btnNext.Click += new System.EventHandler(this.btnNext_Click);
            // 
            // btnLast
            // 
            this.btnLast.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLast.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnLast.BorderRadius = 5;
            this.btnLast.BorderSize = 1;
            this.btnLast.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnLast.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnLast.FlatAppearance.BorderSize = 0;
            this.btnLast.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnLast.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnLast.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnLast.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnLast.ForeColor = System.Drawing.Color.White;
            this.btnLast.IconChar = FontAwesome.Sharp.IconChar.ForwardStep;
            this.btnLast.IconColor = System.Drawing.Color.White;
            this.btnLast.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnLast.IconSize = 18;
            this.btnLast.Location = new System.Drawing.Point(82, 3);
            this.btnLast.Name = "btnLast";
            this.btnLast.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnLast.Size = new System.Drawing.Size(29, 32);
            this.btnLast.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnLast.TabIndex = 90;
            this.btnLast.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnLast.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnLast, "اول صفحة");
            this.btnLast.UseVisualStyleBackColor = false;
            this.btnLast.Click += new System.EventHandler(this.btnLast_Click);
            // 
            // btnPrev
            // 
            this.btnPrev.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPrev.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnPrev.BorderRadius = 5;
            this.btnPrev.BorderSize = 1;
            this.btnPrev.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnPrev.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnPrev.FlatAppearance.BorderSize = 0;
            this.btnPrev.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnPrev.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnPrev.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnPrev.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnPrev.ForeColor = System.Drawing.Color.White;
            this.btnPrev.IconChar = FontAwesome.Sharp.IconChar.ArrowRight;
            this.btnPrev.IconColor = System.Drawing.Color.White;
            this.btnPrev.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnPrev.IconSize = 18;
            this.btnPrev.Location = new System.Drawing.Point(55, 3);
            this.btnPrev.Name = "btnPrev";
            this.btnPrev.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnPrev.Size = new System.Drawing.Size(29, 32);
            this.btnPrev.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnPrev.TabIndex = 90;
            this.btnPrev.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnPrev.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.toolTip1.SetToolTip(this.btnPrev, "الصفحة السابقة");
            this.btnPrev.UseVisualStyleBackColor = false;
            this.btnPrev.Click += new System.EventHandler(this.btnPrev_Click);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // btnDelete
            // 
            this.btnDelete.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDelete.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.BorderRadius = 5;
            this.btnDelete.BorderSize = 1;
            this.btnDelete.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btnDelete.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btnDelete.FlatAppearance.BorderSize = 0;
            this.btnDelete.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btnDelete.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btnDelete.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnDelete.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnDelete.ForeColor = System.Drawing.Color.White;
            this.btnDelete.IconChar = FontAwesome.Sharp.IconChar.TrashAlt;
            this.btnDelete.IconColor = System.Drawing.Color.White;
            this.btnDelete.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnDelete.IconSize = 20;
            this.btnDelete.Location = new System.Drawing.Point(844, 5);
            this.btnDelete.Name = "btnDelete";
            this.btnDelete.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btnDelete.Size = new System.Drawing.Size(28, 33);
            this.btnDelete.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnDelete.TabIndex = 85;
            this.btnDelete.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnDelete.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnDelete.UseVisualStyleBackColor = false;
            this.btnDelete.Click += new System.EventHandler(this.btnDelete_Click);
            // 
            // btn_add_ScriptSmart
            // 
            this.btn_add_ScriptSmart.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_add_ScriptSmart.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_add_ScriptSmart.BorderRadius = 5;
            this.btn_add_ScriptSmart.BorderSize = 1;
            this.btn_add_ScriptSmart.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btn_add_ScriptSmart.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_add_ScriptSmart.FlatAppearance.BorderSize = 0;
            this.btn_add_ScriptSmart.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_add_ScriptSmart.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_add_ScriptSmart.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_add_ScriptSmart.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.btn_add_ScriptSmart.ForeColor = System.Drawing.Color.White;
            this.btn_add_ScriptSmart.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_add_ScriptSmart.IconColor = System.Drawing.Color.White;
            this.btn_add_ScriptSmart.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_add_ScriptSmart.IconSize = 18;
            this.btn_add_ScriptSmart.Location = new System.Drawing.Point(275, 97);
            this.btn_add_ScriptSmart.Name = "btn_add_ScriptSmart";
            this.btn_add_ScriptSmart.Size = new System.Drawing.Size(186, 40);
            this.btn_add_ScriptSmart.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_add_ScriptSmart.TabIndex = 86;
            this.btn_add_ScriptSmart.Text = "تحديث السكربت للاصدرا الجديد";
            this.btn_add_ScriptSmart.UseVisualStyleBackColor = false;
            this.btn_add_ScriptSmart.Visible = false;
            this.btn_add_ScriptSmart.Click += new System.EventHandler(this.btn_add_ScriptSmart_Click);
            // 
            // lbl_Filter
            // 
            this.lbl_Filter.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Filter.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.lbl_Filter.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Filter.LinkLabel = false;
            this.lbl_Filter.Location = new System.Drawing.Point(398, 171);
            this.lbl_Filter.Name = "lbl_Filter";
            this.lbl_Filter.Size = new System.Drawing.Size(308, 26);
            this.lbl_Filter.Style = SmartCreator.RJControls.LabelStyle.Custom;
            this.lbl_Filter.TabIndex = 88;
            this.lbl_Filter.Text = "فلترة";
            this.lbl_Filter.Visible = false;
            // 
            // CBox_OrderBy
            // 
            this.CBox_OrderBy.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_OrderBy.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_OrderBy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_OrderBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_OrderBy.BorderRadius = 5;
            this.CBox_OrderBy.BorderSize = 1;
            this.CBox_OrderBy.Customizable = false;
            this.CBox_OrderBy.DataSource = null;
            this.CBox_OrderBy.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_OrderBy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_OrderBy.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_OrderBy.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.CBox_OrderBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_OrderBy.Location = new System.Drawing.Point(185, 5);
            this.CBox_OrderBy.Name = "CBox_OrderBy";
            this.CBox_OrderBy.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_OrderBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_OrderBy.SelectedIndex = -1;
            this.CBox_OrderBy.Size = new System.Drawing.Size(145, 33);
            this.CBox_OrderBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_OrderBy.TabIndex = 100;
            this.CBox_OrderBy.Texts = "";
            this.CBox_OrderBy.OnSelectedIndexChanged += new System.EventHandler(this.CBox_OrderBy_OnSelectedIndexChanged);
            // 
            // CBox_SearchBy
            // 
            this.CBox_SearchBy.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_SearchBy.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SearchBy.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SearchBy.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SearchBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.BorderRadius = 5;
            this.CBox_SearchBy.BorderSize = 1;
            this.CBox_SearchBy.Customizable = false;
            this.CBox_SearchBy.DataSource = null;
            this.CBox_SearchBy.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SearchBy.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SearchBy.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SearchBy.Font = new System.Drawing.Font("Droid Sans Arabic", 8.25F);
            this.CBox_SearchBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SearchBy.Items.AddRange(new object[] {
            "الاسم",
            "المرور",
            "التسلسل",
            " نقطة بيع"});
            this.CBox_SearchBy.Location = new System.Drawing.Point(548, 8);
            this.CBox_SearchBy.Name = "CBox_SearchBy";
            this.CBox_SearchBy.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SearchBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CBox_SearchBy.SelectedIndex = -1;
            this.CBox_SearchBy.Size = new System.Drawing.Size(100, 30);
            this.CBox_SearchBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SearchBy.TabIndex = 99;
            this.CBox_SearchBy.Texts = "";
            // 
            // rjLabel7
            // 
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(331, 12);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(48, 17);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 101;
            this.rjLabel7.Text = "ترتيب بــ:";
            // 
            // CheckBox_orderBy
            // 
            this.CheckBox_orderBy.AutoSize = true;
            this.CheckBox_orderBy.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_orderBy.BorderSize = 1;
            this.CheckBox_orderBy.Check = true;
            this.CheckBox_orderBy.Checked = true;
            this.CheckBox_orderBy.CheckState = System.Windows.Forms.CheckState.Checked;
            this.CheckBox_orderBy.Cursor = System.Windows.Forms.Cursors.Hand;
            this.CheckBox_orderBy.Customizable = false;
            this.CheckBox_orderBy.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.CheckBox_orderBy.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CheckBox_orderBy.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CheckBox_orderBy.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.CheckBox_orderBy.Location = new System.Drawing.Point(105, 11);
            this.CheckBox_orderBy.MinimumSize = new System.Drawing.Size(0, 21);
            this.CheckBox_orderBy.Name = "CheckBox_orderBy";
            this.CheckBox_orderBy.Padding = new System.Windows.Forms.Padding(0, 0, 20, 0);
            this.CheckBox_orderBy.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.CheckBox_orderBy.Size = new System.Drawing.Size(79, 21);
            this.CheckBox_orderBy.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CheckBox_orderBy.TabIndex = 98;
            this.CheckBox_orderBy.Text = "تنازلي";
            this.CheckBox_orderBy.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.CheckBox_orderBy.UseVisualStyleBackColor = true;
            this.CheckBox_orderBy.CheckedChanged += new System.EventHandler(this.CheckBox_orderBy_CheckedChanged);
            // 
            // rjPane_Top
            // 
            this.rjPane_Top.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPane_Top.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPane_Top.BorderRadius = 13;
            this.rjPane_Top.Controls.Add(this.btn_RemoveFinsh);
            this.rjPane_Top.Controls.Add(this.btn_search);
            this.rjPane_Top.Controls.Add(this.CBox_SearchBy);
            this.rjPane_Top.Controls.Add(this.btnRefresh);
            this.rjPane_Top.Controls.Add(this.btnRefresh_DB);
            this.rjPane_Top.Controls.Add(this.btnDelete);
            this.rjPane_Top.Controls.Add(this.btn_Filter);
            this.rjPane_Top.Controls.Add(this.btnEnable);
            this.rjPane_Top.Controls.Add(this.btnDisable);
            this.rjPane_Top.Controls.Add(this.txt_search);
            this.rjPane_Top.Controls.Add(this.CBox_OrderBy);
            this.rjPane_Top.Controls.Add(this.rjLabel7);
            this.rjPane_Top.Controls.Add(this.CheckBox_orderBy);
            this.rjPane_Top.Customizable = false;
            this.rjPane_Top.Location = new System.Drawing.Point(22, 6);
            this.rjPane_Top.Name = "rjPane_Top";
            this.rjPane_Top.Size = new System.Drawing.Size(962, 45);
            this.rjPane_Top.TabIndex = 102;
            // 
            // btn_RemoveFinsh
            // 
            this.btn_RemoveFinsh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btn_RemoveFinsh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(234)))), ((int)(((byte)(79)))), ((int)(((byte)(82)))));
            this.btn_RemoveFinsh.BorderRadius = 8;
            this.btn_RemoveFinsh.BorderSize = 1;
            this.btn_RemoveFinsh.Design = SmartCreator.RJControls.ButtonDesign.Custom;
            this.btn_RemoveFinsh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_RemoveFinsh.FlatAppearance.BorderSize = 0;
            this.btn_RemoveFinsh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(219)))), ((int)(((byte)(74)))), ((int)(((byte)(77)))));
            this.btn_RemoveFinsh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(205)))), ((int)(((byte)(69)))), ((int)(((byte)(72)))));
            this.btn_RemoveFinsh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_RemoveFinsh.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_RemoveFinsh.ForeColor = System.Drawing.Color.White;
            this.btn_RemoveFinsh.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_RemoveFinsh.IconColor = System.Drawing.Color.White;
            this.btn_RemoveFinsh.IconFont = FontAwesome.Sharp.IconFont.Brands;
            this.btn_RemoveFinsh.IconSize = 24;
            this.btn_RemoveFinsh.Location = new System.Drawing.Point(378, 5);
            this.btn_RemoveFinsh.Name = "btn_RemoveFinsh";
            this.btn_RemoveFinsh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_RemoveFinsh.Size = new System.Drawing.Size(216, 33);
            this.btn_RemoveFinsh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_RemoveFinsh.TabIndex = 52;
            this.btn_RemoveFinsh.Text = "حذف الكروت المنتيهة";
            this.btn_RemoveFinsh.UseVisualStyleBackColor = false;
            this.btn_RemoveFinsh.Visible = false;
            this.btn_RemoveFinsh.Click += new System.EventHandler(this.btn_RemoveFinsh_Validaty_Click);
            // 
            // rjPanel_Page
            // 
            this.rjPanel_Page.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel_Page.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel_Page.BorderRadius = 0;
            this.rjPanel_Page.Controls.Add(this.Panel_Pages);
            this.rjPanel_Page.Controls.Add(this.panel2);
            this.rjPanel_Page.Customizable = false;
            this.rjPanel_Page.Location = new System.Drawing.Point(22, 516);
            this.rjPanel_Page.Name = "rjPanel_Page";
            this.rjPanel_Page.Size = new System.Drawing.Size(962, 42);
            this.rjPanel_Page.TabIndex = 103;
            // 
            // Panel_Pages
            // 
            this.Panel_Pages.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Panel_Pages.Controls.Add(this.rjLabel11);
            this.Panel_Pages.Controls.Add(this.CBox_PageCount);
            this.Panel_Pages.Controls.Add(this.rjLabel18);
            this.Panel_Pages.Location = new System.Drawing.Point(720, 0);
            this.Panel_Pages.Name = "Panel_Pages";
            this.Panel_Pages.Size = new System.Drawing.Size(236, 38);
            this.Panel_Pages.TabIndex = 92;
            // 
            // rjLabel11
            // 
            this.rjLabel11.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel11.AutoSize = true;
            this.rjLabel11.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel11.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel11.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel11.LinkLabel = false;
            this.rjLabel11.Location = new System.Drawing.Point(202, 8);
            this.rjLabel11.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel11.Name = "rjLabel11";
            this.rjLabel11.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel11.Size = new System.Drawing.Size(32, 17);
            this.rjLabel11.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel11.TabIndex = 35;
            this.rjLabel11.Text = "عرض";
            this.rjLabel11.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // CBox_PageCount
            // 
            this.CBox_PageCount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_PageCount.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_PageCount.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_PageCount.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_PageCount.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_PageCount.BorderRadius = 12;
            this.CBox_PageCount.BorderSize = 1;
            this.CBox_PageCount.Customizable = false;
            this.CBox_PageCount.DataSource = null;
            this.CBox_PageCount.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_PageCount.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_PageCount.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_PageCount.Font = new System.Drawing.Font("Tahoma", 9F);
            this.CBox_PageCount.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_PageCount.Items.AddRange(new object[] {
            "100",
            "200",
            "500",
            "1000",
            "2000",
            "5000",
            "10000",
            "20000",
            "30000",
            "50000",
            "100000",
            "500000"});
            this.CBox_PageCount.Location = new System.Drawing.Point(93, 3);
            this.CBox_PageCount.Name = "CBox_PageCount";
            this.CBox_PageCount.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_PageCount.SelectedIndex = -1;
            this.CBox_PageCount.Size = new System.Drawing.Size(97, 30);
            this.CBox_PageCount.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_PageCount.TabIndex = 91;
            this.CBox_PageCount.Texts = "";
            this.CBox_PageCount.OnSelectedIndexChanged += new System.EventHandler(this.CBox_PageCount_OnSelectedIndexChanged);
            // 
            // rjLabel18
            // 
            this.rjLabel18.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel18.AutoSize = true;
            this.rjLabel18.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel18.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel18.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel18.LinkLabel = false;
            this.rjLabel18.Location = new System.Drawing.Point(11, 9);
            this.rjLabel18.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel18.Name = "rjLabel18";
            this.rjLabel18.Size = new System.Drawing.Size(68, 17);
            this.rjLabel18.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel18.TabIndex = 35;
            this.rjLabel18.Text = "في الصفحة";
            // 
            // panel2
            // 
            this.panel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel2.Controls.Add(this.rjLabel21);
            this.panel2.Controls.Add(this.txtAllCountRows);
            this.panel2.Controls.Add(this.btnFirst);
            this.panel2.Controls.Add(this.btnNext);
            this.panel2.Controls.Add(this.txtCurrentPageindex);
            this.panel2.Controls.Add(this.btnLast);
            this.panel2.Controls.Add(this.btnPrev);
            this.panel2.Controls.Add(this.txtTotalPages);
            this.panel2.Controls.Add(this.rjLabel22);
            this.panel2.Controls.Add(this.rjLabel23);
            this.panel2.Location = new System.Drawing.Point(3, 0);
            this.panel2.Name = "panel2";
            this.panel2.Size = new System.Drawing.Size(711, 38);
            this.panel2.TabIndex = 93;
            // 
            // rjLabel21
            // 
            this.rjLabel21.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel21.AutoSize = true;
            this.rjLabel21.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel21.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel21.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel21.LinkLabel = false;
            this.rjLabel21.Location = new System.Drawing.Point(628, 10);
            this.rjLabel21.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel21.Name = "rjLabel21";
            this.rjLabel21.Size = new System.Drawing.Size(68, 17);
            this.rjLabel21.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel21.TabIndex = 35;
            this.rjLabel21.Text = "العدد الكلي";
            // 
            // txtAllCountRows
            // 
            this.txtAllCountRows._Customizable = false;
            this.txtAllCountRows.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txtAllCountRows.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtAllCountRows.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtAllCountRows.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtAllCountRows.BorderRadius = 5;
            this.txtAllCountRows.BorderSize = 1;
            this.txtAllCountRows.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtAllCountRows.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtAllCountRows.Location = new System.Drawing.Point(548, 5);
            this.txtAllCountRows.MultiLine = false;
            this.txtAllCountRows.Name = "txtAllCountRows";
            this.txtAllCountRows.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtAllCountRows.PasswordChar = false;
            this.txtAllCountRows.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtAllCountRows.PlaceHolderText = null;
            this.txtAllCountRows.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtAllCountRows.Size = new System.Drawing.Size(70, 25);
            this.txtAllCountRows.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtAllCountRows.TabIndex = 95;
            this.txtAllCountRows.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtCurrentPageindex
            // 
            this.txtCurrentPageindex._Customizable = false;
            this.txtCurrentPageindex.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtCurrentPageindex.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCurrentPageindex.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtCurrentPageindex.BorderRadius = 5;
            this.txtCurrentPageindex.BorderSize = 1;
            this.txtCurrentPageindex.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtCurrentPageindex.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtCurrentPageindex.Location = new System.Drawing.Point(192, 9);
            this.txtCurrentPageindex.MultiLine = false;
            this.txtCurrentPageindex.Name = "txtCurrentPageindex";
            this.txtCurrentPageindex.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtCurrentPageindex.PasswordChar = false;
            this.txtCurrentPageindex.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtCurrentPageindex.PlaceHolderText = null;
            this.txtCurrentPageindex.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtCurrentPageindex.Size = new System.Drawing.Size(47, 25);
            this.txtCurrentPageindex.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtCurrentPageindex.TabIndex = 95;
            this.txtCurrentPageindex.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txtTotalPages
            // 
            this.txtTotalPages._Customizable = false;
            this.txtTotalPages.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txtTotalPages.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtTotalPages.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txtTotalPages.BorderRadius = 5;
            this.txtTotalPages.BorderSize = 1;
            this.txtTotalPages.Font = new System.Drawing.Font("Tahoma", 9F);
            this.txtTotalPages.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txtTotalPages.Location = new System.Drawing.Point(114, 7);
            this.txtTotalPages.MultiLine = false;
            this.txtTotalPages.Name = "txtTotalPages";
            this.txtTotalPages.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txtTotalPages.PasswordChar = false;
            this.txtTotalPages.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txtTotalPages.PlaceHolderText = null;
            this.txtTotalPages.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txtTotalPages.Size = new System.Drawing.Size(47, 25);
            this.txtTotalPages.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txtTotalPages.TabIndex = 95;
            this.txtTotalPages.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel22
            // 
            this.rjLabel22.AutoSize = true;
            this.rjLabel22.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel22.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel22.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel22.LinkLabel = false;
            this.rjLabel22.Location = new System.Drawing.Point(243, 12);
            this.rjLabel22.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel22.Name = "rjLabel22";
            this.rjLabel22.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel22.Size = new System.Drawing.Size(39, 17);
            this.rjLabel22.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel22.TabIndex = 35;
            this.rjLabel22.Text = "صفحة";
            this.rjLabel22.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // rjLabel23
            // 
            this.rjLabel23.AutoSize = true;
            this.rjLabel23.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel23.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel23.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel23.LinkLabel = false;
            this.rjLabel23.Location = new System.Drawing.Point(165, 11);
            this.rjLabel23.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel23.Name = "rjLabel23";
            this.rjLabel23.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel23.Size = new System.Drawing.Size(23, 17);
            this.rjLabel23.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel23.TabIndex = 35;
            this.rjLabel23.Text = "من";
            this.rjLabel23.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // lbl_note
            // 
            this.lbl_note.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(46, 560);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(447, 17);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 104;
            this.lbl_note.Text = "لمزيد من الخيارات اضغط بزر الماوس الايمن  -  لتفاصيل الكرت نقرتين بالماوس علي الس" +
    "طر";
            // 
            // FormAllCardsHotspot
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "FormAllCardsHotspot";
            this.ClientSize = new System.Drawing.Size(1000, 629);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "FormAllCardsHotspot";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "FormAllCardsHotspot";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormAllCardsHotspot_FormClosing);
            this.Load += new System.EventHandler(this.FormAllCardsHotspot_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.rjPanel3.ResumeLayout(false);
            this.Spanel.ResumeLayout(false);
            this.rjPanel_back_side.ResumeLayout(false);
            this.rjPanel_back_side.PerformLayout();
            this.pnl_side_Count_Session.ResumeLayout(false);
            this.fpnl_showServer.ResumeLayout(false);
            this.fpnl_showServer.PerformLayout();
            this.fpnl_showArchive.ResumeLayout(false);
            this.fpnl_showArchive.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.flowLayoutPanel1.PerformLayout();
            this.flowLayoutPanel2.ResumeLayout(false);
            this.flowLayoutPanel2.PerformLayout();
            this.pnl_side_Finsh_Cards.ResumeLayout(false);
            this.pnl_side_sn.ResumeLayout(false);
            this.pnl_side_sn.PerformLayout();
            this.pnl_side_datePrint.ResumeLayout(false);
            this.pnl_side_datePrint.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.dmAll_Cards.ResumeLayout(false);
            this.rjPane_Top.ResumeLayout(false);
            this.rjPane_Top.PerformLayout();
            this.rjPanel_Page.ResumeLayout(false);
            this.Panel_Pages.ResumeLayout(false);
            this.Panel_Pages.PerformLayout();
            this.panel2.ResumeLayout(false);
            this.panel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJPanel rjPanel3;
        private RJControls.RJButton btn_Collaps;
        private System.Windows.Forms.Panel Spanel;
        private RJControls.RJPanel rjPanel_back_side;
        private RJControls.RJPanel pnl_side_sn;
        private RJControls.RJLabel rjLabel10;
        private RJControls.RJPanel pnl_side_datePrint;
        private RJControls.RJLabel rjLabel13;
        private RJControls.RJLabel rjLabel12;
        private RJControls.RJLabel rjLabel20;
        private RJControls.RJLabel rjLabel19;
        private RJControls.RJComboBox CBox_Server_hotspot;
        private RJControls.RJLabel rjLabel17;
        private RJControls.RJComboBox CBox_Staus;
        private RJControls.RJLabel rjLabel14;
        private RJControls.RJComboBox CBox_profile_Source_hotspot;
        private RJControls.RJLabel rjLabel16;
        private RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJLabel rjLabel15;
        private RJControls.RJComboBox CBox_Batch;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJComboBox CBox_Profile_HotspotLocal;
        private RJControls.RJLabel rjLabel9;
        private System.Windows.Forms.Panel panel3_side;
        private RJControls.RJLabel rjLabel25Title;
        private System.Windows.Forms.Panel panel2_side;
        private System.Windows.Forms.Panel panel1_side;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJButton btnEnable;
        private RJControls.RJButton rjButton2;
        private RJControls.RJButton btnDisable;
        private RJControls.RJButton btn_search_Router;
        private RJControls.RJButton btn_search;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJToggleButton jToggleButton_Year;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJButton btnRefresh;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem View_Hide_toolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Status_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SN_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem UserName_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Password_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_price_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem SellingPoint_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem BachCards_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_TransferLimit_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UptimeUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_DownloadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_UploadUsed_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_Up_Down_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_RegDate_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_LastSeenAt_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem dt_FirstUse_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTillTime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTimeLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_ProfileTransferLeft_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Descr_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Count_profile_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem CountSession_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem toolStripMenuItem2;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator1;
        private System.Windows.Forms.ToolStripMenuItem نسخCtrlcToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem نسخالسطركاملToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالىملفاكسلToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالىملفنصيToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem حذفالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حذفالكروتالمحددةمنالارشيفToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حذفالكروتالمحددةمنالراوتروالارشيفToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem حذفجلساتالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator3;
        private System.Windows.Forms.ToolStripMenuItem تعطيلالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تفعيلالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصفيرعدادالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem ربطالكروتباولجهازاستخدامToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem الغاءربطالكروتباولجهازاستخدامToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator4;
        private System.Windows.Forms.ToolStripMenuItem طباعةالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem اعادةشحنالكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تغيرنقطةالبيعللكروتالمحددةToolStripMenuItem;
        private System.Windows.Forms.Timer timer_SideBar;
        private System.Windows.Forms.ToolTip toolTip1;
        private System.Windows.Forms.Timer timer1;
        private System.Windows.Forms.ToolStripMenuItem profileHotspot_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem server_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem email_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem comment_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_limitUptime_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Str_limitbytestotal_ToolStripMenuItem;
        private RJControls.RJButton btnDelete;
        private RJControls.RJButton btn_add_ScriptSmart;
        private RJControls.RJCheckBox CheckBox_NoSmartScript;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel2;
        private RJControls.RJLabel lbl_Filter;
        private RJControls.RJComboBox CBox_Profile;
        private System.Windows.Forms.ToolStripMenuItem PageNumberToolStripMenuItem;
        private RJControls.RJLabel lbl_byProfile;
        private RJControls.RJCheckBox CheckBox_UMProfile;
        private System.Windows.Forms.ToolStripMenuItem Str_SmartValidatiy_Add_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Restor_ColumnToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمفقطToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem تصديرالاسمكلمةالمرورالباقةنقطةالبيعToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Remove_SP_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem الايامالمتبقيةToolStripMenuItem;
        private RJControls.RJButton btnRefresh_DB;
        private RJControls.RJButton btn_Filter;
        private RJControls.RJComboBox CBox_SearchBy;
        private RJControls.RJComboBox CBox_OrderBy;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJCheckBox CheckBox_orderBy;
        private RJControls.RJPanel rjPane_Top;
        private RJControls.RJButton btn_RemoveFinsh;
        private RJControls.RJPanel pnl_side_Count_Session;
        private System.Windows.Forms.FlowLayoutPanel fpnl_showArchive;
        private RJControls.RJToggleButton ToggleButton_Show_Archive;
        private RJControls.RJLabel rjLabel5;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private RJControls.RJToggleButton ToggleButton_ByCountSession;
        private RJControls.RJLabel rjLabel6;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private RJControls.RJToggleButton ToggleButton_ByCountProfile;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJPanel pnl_side_Finsh_Cards;
        private RJControls.RJButton btn_RemoveFinsh_Download;
        private RJControls.RJButton btn_RemoveFinsh_Uptime;
        private RJControls.RJButton btn_RemoveFinsh_All;
        private RJControls.RJCheckBox CheckBox_byDatePrint;
        private RJControls.RJLabel lbl_to;
        private RJControls.RJDatePicker Date_To;
        private RJControls.RJDatePicker Date_From;
        private RJControls.RJTextBox txt_SN_End;
        private RJControls.RJTextBox txt_SN_Start;
        private RJControls.RJCheckBox CheckBox_SN;
        private RJControls.RJComboBox CBox_SN_Compar;
        private RJControls.RJButton rjButton1;
        private RJControls.RJButton btn_apply;
        private RJControls.RJPanel rjPanel_Page;
        private System.Windows.Forms.Panel Panel_Pages;
        private RJControls.RJLabel rjLabel11;
        private RJControls.RJComboBox CBox_PageCount;
        private RJControls.RJLabel rjLabel18;
        private System.Windows.Forms.Panel panel2;
        private RJControls.RJLabel rjLabel21;
        private RJControls.RJTextBox txtAllCountRows;
        private RJControls.RJButton btnFirst;
        private RJControls.RJButton btnNext;
        private RJControls.RJTextBox txtCurrentPageindex;
        private RJControls.RJButton btnLast;
        private RJControls.RJButton btnPrev;
        private RJControls.RJTextBox txtTotalPages;
        private RJControls.RJLabel rjLabel22;
        private RJControls.RJLabel rjLabel23;
        private RJControls.RJButton btn_RemoveFinsh_Validaty;
        private RJControls.RJComboBox CBox_Didabled;
        private RJControls.RJLabel rjLabel24;
        private System.Windows.Forms.ToolStripMenuItem NumberPrint_toolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Sn_Archive_ToolStripMenuItem;
        private RJControls.RJComboBox CBox_NumberPrint;
        private RJControls.RJLabel rjLabel25;
        private RJControls.RJLabel rjLabel26;
        private FontAwesome.Sharp.IconMenuItem SaveDGVToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator5;
        private RJControls.RJLabel lbl_note;
        private RJControls.RJButton rjButton3;
        private System.Windows.Forms.FlowLayoutPanel fpnl_showServer;
        private RJControls.RJToggleButton ToggleButton_Show_onlyServer;
        private RJControls.RJLabel rjLabel27;
    }
}