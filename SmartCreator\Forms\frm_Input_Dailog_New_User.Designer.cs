﻿namespace SmartCreator.Forms
{
    partial class frm_Input_Dailog_New_User
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.btnAdd = new SmartCreator.RJControls.RJButton();
            this.txt_Name = new SmartCreator.RJControls.RJTextBox();
            this.txt_password = new SmartCreator.RJControls.RJTextBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.btnRandomUser = new SmartCreator.RJControls.RJButton();
            this.btnRandomPassword = new SmartCreator.RJControls.RJButton();
            this.pnlClientArea.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.btnRandomPassword);
            this.pnlClientArea.Controls.Add(this.btnRandomUser);
            this.pnlClientArea.Controls.Add(this.rjLabel1);
            this.pnlClientArea.Controls.Add(this.rjLabel5);
            this.pnlClientArea.Controls.Add(this.btnAdd);
            this.pnlClientArea.Controls.Add(this.txt_password);
            this.pnlClientArea.Controls.Add(this.txt_Name);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(440, 130);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(71, 22);
            this.lblCaption.Text = "New User";
            // 
            // rjLabel5
            // 
            this.rjLabel5.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Cairo Medium", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(330, 7);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(42, 23);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 34;
            this.rjLabel5.Text = "الاسم ";
            // 
            // btnAdd
            // 
            this.btnAdd.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderRadius = 14;
            this.btnAdd.BorderSize = 1;
            this.btnAdd.Design = SmartCreator.RJControls.ButtonDesign.Normal;
            this.btnAdd.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAdd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAdd.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btnAdd.ForeColor = System.Drawing.Color.White;
            this.btnAdd.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btnAdd.IconColor = System.Drawing.Color.White;
            this.btnAdd.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAdd.IconSize = 24;
            this.btnAdd.Location = new System.Drawing.Point(152, 75);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.Size = new System.Drawing.Size(113, 35);
            this.btnAdd.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAdd.TabIndex = 33;
            this.btnAdd.Text = "موافق";
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // txt_Name
            // 
            this.txt_Name._Customizable = false;
            this.txt_Name.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_Name.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Name.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Name.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Name.BorderRadius = 10;
            this.txt_Name.BorderSize = 1;
            this.txt_Name.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_Name.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Name.Location = new System.Drawing.Point(263, 33);
            this.txt_Name.MultiLine = false;
            this.txt_Name.Name = "txt_Name";
            this.txt_Name.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Name.PasswordChar = false;
            this.txt_Name.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Name.PlaceHolderText = null;
            this.txt_Name.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Name.Size = new System.Drawing.Size(160, 27);
            this.txt_Name.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_Name.TabIndex = 0;
            this.txt_Name.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_password
            // 
            this.txt_password._Customizable = false;
            this.txt_password.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_password.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_password.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_password.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_password.BorderRadius = 10;
            this.txt_password.BorderSize = 1;
            this.txt_password.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_password.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_password.Location = new System.Drawing.Point(53, 33);
            this.txt_password.MultiLine = false;
            this.txt_password.Name = "txt_password";
            this.txt_password.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_password.PasswordChar = false;
            this.txt_password.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_password.PlaceHolderText = null;
            this.txt_password.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_password.Size = new System.Drawing.Size(160, 27);
            this.txt_password.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_password.TabIndex = 32;
            this.txt_password.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Cairo Medium", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(107, 7);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel1.Size = new System.Drawing.Size(68, 23);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 34;
            this.rjLabel1.Text = "كلمة المرور";
            // 
            // btnRandomUser
            // 
            this.btnRandomUser.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRandomUser.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRandomUser.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRandomUser.BorderRadius = 5;
            this.btnRandomUser.BorderSize = 1;
            this.btnRandomUser.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRandomUser.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRandomUser.FlatAppearance.BorderSize = 0;
            this.btnRandomUser.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRandomUser.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRandomUser.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRandomUser.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRandomUser.ForeColor = System.Drawing.Color.White;
            this.btnRandomUser.IconChar = FontAwesome.Sharp.IconChar.Random;
            this.btnRandomUser.IconColor = System.Drawing.Color.White;
            this.btnRandomUser.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRandomUser.IconSize = 18;
            this.btnRandomUser.Location = new System.Drawing.Point(232, 33);
            this.btnRandomUser.Name = "btnRandomUser";
            this.btnRandomUser.Size = new System.Drawing.Size(29, 27);
            this.btnRandomUser.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRandomUser.TabIndex = 58;
            this.btnRandomUser.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnRandomUser.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnRandomUser.UseVisualStyleBackColor = false;
            this.btnRandomUser.Click += new System.EventHandler(this.btnRandomUser_Click);
            // 
            // btnRandomPassword
            // 
            this.btnRandomPassword.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRandomPassword.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRandomPassword.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRandomPassword.BorderRadius = 5;
            this.btnRandomPassword.BorderSize = 1;
            this.btnRandomPassword.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRandomPassword.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRandomPassword.FlatAppearance.BorderSize = 0;
            this.btnRandomPassword.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRandomPassword.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRandomPassword.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRandomPassword.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRandomPassword.ForeColor = System.Drawing.Color.White;
            this.btnRandomPassword.IconChar = FontAwesome.Sharp.IconChar.Random;
            this.btnRandomPassword.IconColor = System.Drawing.Color.White;
            this.btnRandomPassword.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRandomPassword.IconSize = 18;
            this.btnRandomPassword.Location = new System.Drawing.Point(23, 33);
            this.btnRandomPassword.Name = "btnRandomPassword";
            this.btnRandomPassword.Size = new System.Drawing.Size(29, 27);
            this.btnRandomPassword.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRandomPassword.TabIndex = 58;
            this.btnRandomPassword.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnRandomPassword.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnRandomPassword.UseVisualStyleBackColor = false;
            this.btnRandomPassword.Click += new System.EventHandler(this.btnRandomPassword_Click);
            // 
            // frm_Input_Dailog_New_User
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "New User";
            this.ClientSize = new System.Drawing.Size(450, 180);
            this.ControlBox = false;
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.Name = "frm_Input_Dailog_New_User";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Resizable = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "New User";
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJButton btnAdd;
        public RJControls.RJTextBox txt_password;
        public RJControls.RJTextBox txt_Name;
        private RJControls.RJButton btnRandomPassword;
        private RJControls.RJButton btnRandomUser;
    }
}