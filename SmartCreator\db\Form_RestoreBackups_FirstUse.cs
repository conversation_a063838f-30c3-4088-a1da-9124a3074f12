﻿//using DevComponents.DotNetBar;
//using DevComponents.DotNetBar.Metro;
using Microsoft.Win32;
using SmartCreator.Forms;
using SmartCreator.Forms.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Windows.Forms.DataVisualization.Charting;

namespace SmartCreator.db
{
    public partial class Form_RestoreBackups_FirstUse : Form
    {
        Old_DatabaseInfo old_DatabaseInfo;
        string DB_type = "SmartDB";
        FormConnection fc = new FormConnection();
        Form_Backup fb = new Form_Backup();
        //string Rb_Sn;
        public Form_RestoreBackups_FirstUse()
        {
            InitializeComponent();
            btnYes.Select();
        }        
        public Form_RestoreBackups_FirstUse(Old_DatabaseInfo _old_DatabaseInfo,string _DB_type= "SmartDB")
        {
            InitializeComponent();
            old_DatabaseInfo = _old_DatabaseInfo;
            DB_type = _DB_type;
            //Rb_Sn = rb_sn;
            btnYes.Select();


        }

        public void MirrorLocations(Control ctl)
        {
            // إجراء تعديل الأدوات حسب اللغة المستخدمة

            try
            {
                foreach (Control C in ctl.Controls)
                {
                    C.Location = new Point(C.Parent.ClientRectangle.Width
                   - C.Size.Width - C.Location.X, C.Location.Y);
                    if (System.Convert.ToBoolean(C.Anchor & AnchorStyles.Left))
                        C.Anchor = (C.Anchor | AnchorStyles.Right) ^ AnchorStyles.Left;
                    else if (System.Convert.ToBoolean(C.Anchor & AnchorStyles.Right))
                        C.Anchor = (C.Anchor | AnchorStyles.Left) ^ AnchorStyles.Right;
                    if (C.Dock == DockStyle.Right)
                        C.Dock = DockStyle.Left;
                    else if (C.Dock == DockStyle.Left)
                        C.Dock = DockStyle.Right;
                    if (C.Controls.Count > 0)
                        MirrorLocations(C);
                    Application.DoEvents();
                }
            }
            catch
            {
            }
        }

        private void Form_RestoreBackups_FirstUse_Load(object sender, EventArgs e)
        {
            try
            {
                PicIcon.Parent = PicHeader;
                PicIcon.BackColor = Color.Transparent;
                ArabicLanguage();
            }
            catch
            {

            }
        }
        private void ArabicLanguage()
        {
            try
            {
                //RightToLeft = RightToLeft.Yes;
                //g.MirrorLocations(btnNo);
                //g.MirrorLocations(btnYes);

                MirrorLocations(this);

                //g.MirrorLocations(this);
                lblWait.RightToLeft = RightToLeft.Yes;
                PicIcon.Location = new Point(27, 8);
                lblWait.Text = $" اكتشف ان هناك بيانات سابقه لهذا الروتر في المسار \n   {old_DatabaseInfo.Path.Trim()} \n  هل تريد مزامنة قاعدة البيانات واستيراد البيانات ، " + Environment.NewLine + "";
                btnNo.Text = "&لا";
                btnYes.Text = "&نعم";

            }
            catch
            {

            }
        }

        [Obsolete]
        public void LoadData()
        {
            if (DB_type == "SmartDB")
            {
                //string connection_str = $@"Data Source={old_DatabaseInfo.Path.Trim()}\db\SmartDB.db;";
                fc.Check_SmartDB_File(); 
                fb.Restor_SmartDB_firstLoad(old_DatabaseInfo); 


                //foreach (var db in old_DatabaseInfo.Routers)
                //    fb.Restor_SmartDB(connection_str,db.Rb_sn);
            }
            else if(DB_type== "localDB")
            {
                string connection_str = $@"Data Source={old_DatabaseInfo.Path.Trim()}\db\localDB.db;";
                fc.Get_Path_Database3();
                //fc.Get_Path_Database2();
                fb.LoadLocal_Old_DB2(connection_str);
            }
        }

        [Obsolete]
        private void btnYes_Click(object sender, EventArgs e)
        {
            switch (RJMessageBox.Show(" قد يستغرق وقت في عمليه المزامنة والاستعادة لا تقم باغلاق البرنامج حتى يكتمل هل تريد الاستمرار", "سمارت كريتور", MessageBoxButtons.OKCancel, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1))
            {
                case System.Windows.Forms.DialogResult.OK:
                    using (Form_WaitForm fRM = new Form_WaitForm(LoadData))
                        fRM.ShowDialog();

                    //using (Form_WaitForm frm = new Form_WaitForm(LoadData))
                    //{
                    //    //LoadData();
                    //}
                    break;
                case System.Windows.Forms.DialogResult.Cancel:
                    
                    break;

            }
            this.Close();
        }

        private void btnNo_Click(object sender, EventArgs e)
        {
            this.Close();
        }
    }
}
