﻿using DevComponents.DotNetBar.Controls;
using SmartCreator.Data;
using SmartCreator.Entities.CardsArtchive;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAllCards_ِArchive : RJChildForm
    {
        Archive_DataAccess ArchiveDB = null;
        BatchArtchive batchArtchive = null;
        public FormAllCards_ِArchive()
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            ArchiveDB = new Archive_DataAccess();
            set_font();
            sideMenu();
            this.Text = $" جميع كروت الارشيف";

        }
        public FormAllCards_ِArchive(BatchArtchive batch)
        {
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            this.Text = $"كروت الدفعة {batch.BatchNumber}";
            batchArtchive = batch;
            ArchiveDB = new Archive_DataAccess();
            set_font();
            sideMenu();

        }
        void set_font()
        {
            //dgv.AllowUserToOrderColumns = true;
            //System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;

            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;
            //dgv.DefaultCellStyle.Font = new System.Drawing.Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);


            rjLabel1.Font = rjLabel11.Font = rjLabel14.Font = rjLabel2.Font = rjLabel20.Font = rjLabel3.Font = rjLabel6.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            rjLabel25Title.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8 , FontStyle.Bold);
            rjLabel25Title.Font = btnRefresh.Font= Program.GetCustomFont(Resources.DroidKufi_Bold, 9 , FontStyle.Bold);

            //Control_Loop(pnlClientArea);
            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);
            utils.item_Contrlol_textSize(dmAll_Cards);
        }
        

        public void LoadDataGridviewData()
        {
            //Get_Batch_Status_Cards Batch_Status = new Get_Batch_Status_Cards();
            //List<Get_Batch_Status_Cards> Status = Batch_Status.Get_Batch_Cards(Toggle_By_SP.Checked, Toggle_By_Profile.Checked, Toggle_By_Batch.Checked, Toggle_By_Status.Checked);

            //Batch_cards_FromDB cardsUserManagerFromDB = new Batch_cards_FromDB();
            //List<Batch_cards_FromDB> users = cardsUserManagerFromDB.Get_Batch_Cards(Status, Toggle_By_SP.Checked, Toggle_By_Profile.Checked, Toggle_By_Batch.Checked);

            //dgv.DataSource = users;
            //update_header_DGV();
        }

        private void FormAllCards_ِArchive_Load(object sender, EventArgs e)
        {
            loadData();
        }

        private void timer1_Tick(object sender, EventArgs e)
        {

        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            loadData();
        }

        private void loadData()
        {
            string filter = "";
            if (txt_search.Text.Trim() != "")
            {
                filter += $" and UserName LIKE '%{txt_search.Text}%' ";
            }

            string Rb = Global_Variable.Mk_resources.RB_code;
            string Rb_sn = Global_Variable.Mk_resources.RB_SN;
            if (batchArtchive != null)

                dgv.DataSource = ArchiveDB.GetListAnyDB<CardsArtchive>($"select * from CardsArtchive where BatchCardId={batchArtchive.BatchNumber }  and ( Rb='{ Rb}' or Rb='{Rb_sn}')  {filter} ");
            else
                dgv.DataSource = ArchiveDB.GetListAnyDB<CardsArtchive>($"select * from CardsArtchive where ( Rb='{Rb}' or Rb='{Rb_sn}')   {filter} ");
                //dgv.DataSource = ArchiveDB.GetListAnyDB<CardsArtchive>("select * from CardsArtchive where Rb='" + Rb + "'");

            update_header_DGV();


        }
        private void update_header_DGV()
        {
            try
            {
                //foreach (DataGridViewColumn column in dgv.Columns)
                //    column.Visible = false;
                //dgv.Columns["ProfileName"].Visible = false;

                //dgv.Columns["Id"].Visible = false;
                //dgv.Columns["AddedDb"].Visible = false;
                //dgv.Columns["Rb"].Visible = false;
                dgv.Columns["Status"].Visible = false;

                dgv.Columns["Str_Status"].DisplayIndex = 0;
                dgv.Columns["Str_Status"].Width = 150;
                //dgv.Columns["SN"].DisplayIndex = 0;
                //dgv.Columns["BatchCardId"].DisplayIndex = 1;
                dgv.Columns["RegDate"].Width = 170;
                dgv.Columns["PageNumber"].Width = 120;
                dgv.Columns["UserName"].Width = 150;

            }
            catch { }
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            //if (e.RowIndex > -1)
            //{
            //    BatchArtchive batch = (BatchArtchive)dgv.Rows[e.RowIndex].DataBoundItem;
            //    FormAllCards_ِArchive frm = new FormAllCards_ِArchive(batch);
            //    frm.ShowDialog();


            //    //string batchnumber = dgv.Rows[e.RowIndex].Cells["BatchCardId"].ToString();
            //    //string batchnumber = dgv.Rows[e.RowIndex].Cells["BatchCardId"].ToString();
            //    //row.DataBoundItem
            //}
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            sideMenu();
        }
        void sideMenu()
        {
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);

            }
            else
            {
                rjPanel_back_side.Width = 260;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 13, panel1.Location.Y - 1);
                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Height-(panel1.Height - dgv.Location.Y));

            }

        }

        private void dgv_SelectionChanged(object sender, EventArgs e)
        {
            if (this.Width > 1300)
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
            else
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
                //update_header_DGV();
            }
            update_header_DGV();
            update_select_DGV();
        }
        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {

            DialogResult = RJMessageBox.Show("هل تريد حذف الكروت المحدده من الارشيف", "تحذير", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (DialogResult == DialogResult.No)
                return;

            List< CardsArtchive> __Users= new List<CardsArtchive> ();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                CardsArtchive card=new CardsArtchive ();
                card.UserName = (string)row.Cells["UserName"].Value;
                __Users.Add(card);
            }
            if (__Users.Count == 0)
            {
                RJMessageBox.Show("لا يوجد كروت للحذف");
                return;
            }
            DialogResult result = RJMessageBox.Show("  هل انت متأكد من حذف الكروت من الارشيف ", "رسالة تاكيد?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.Yes)
            {
                try
                {
                    //string Id = dgv.CurrentRow.Cells["Id"].Value.ToString();
                    //string BatchNumber = dgv.CurrentRow.Cells["BatchNumber"].Value.ToString();
                    //if (ArchiveDB.Delete<BatchArtchive>($"delete from BatchArtchive where  Id={Id}"))
                    ////if (ArchiveDB.DeleteById<BatchArtchive>(Id))
                    {
                       //int effect= ArchiveDB.Execute<CardsArtchive>("",__Users);

                        //if (ArchiveDB.Execute<CardsArtchive>("", __Users)>0)
                        if (ArchiveDB.Execute<CardsArtchive>($"delete from CardsArtchive where  UserName=@UserName;",__Users )>0)
                        {

                            RJMessageBox.Show("تم حذف الكروت من الارشيف بنجاح");
                            loadData();
                        }
                    }
                }

                catch { }
            }
        }

        private void dgv_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {
            try
            {
                int status = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Status"].Value);
                if (status == 1)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.DarkRed;
                }
            }
            catch { }
        }

        private void btn_search_Click(object sender, EventArgs e)
        {
            loadData();
        }

        private void dgv_SizeChanged(object sender, EventArgs e)
        {
            if (this.Width > 1300)
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            }
            else
            {
                dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

            }
            update_header_DGV();
        }
    }
}
