﻿using SmartCreator.RJForms;
using SmartCreator.Settings;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Devices
{
    public partial class Form_Ubiquiti_Manage : RJChildForm
    {
        public Form_Ubiquiti_Manage()
        {
            InitializeComponent();
            this.Text = "ادارة اجهزة OpenWrt";
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "OpenWrt Manage";
            }
            
        }
    }
}
