﻿using CefSharp.DevTools.Profiler;
using Dapper;
using DevComponents.DotNetBar;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.RJForms;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel.Composition.Primitives;
using System.Data;
using System.Data.SQLite;
using System.Drawing;
using System.Drawing.Drawing2D;
using System.IO;
using System.Linq;
using System.Security.Cryptography;
using System.Text;
using System.Web.Profile;
using System.Web.Routing;
using System.Windows.Forms;
using System.Windows.Markup;
using static System.Net.Mime.MediaTypeNames;

namespace SmartCreator.Forms.Settings
{
    public partial class Form_Backup : RJChildForm
    {
        Sql_DataAccess sql_DataAccess = new Sql_DataAccess();
        Smart_DataAccess Smart_DB = new Smart_DataAccess();
        string Global_Rb="";

        public Form_Backup()
        {
            InitializeComponent();
            sql_DataAccess = new Sql_DataAccess();
            Smart_DB = new Smart_DataAccess();

            utils utils = new utils();
            utils.Control_textSize1(this);



        }
        public Form_Backup(string rb = "")
        {
            InitializeComponent();
            sql_DataAccess = new Sql_DataAccess();
            Smart_DB = new Smart_DataAccess();
            Global_Rb = rb;

            utils utils = new utils();
            utils.Control_textSize1(this);


        }

        private void btn_SmartDB_Click(object sender, EventArgs e)
        {
            try
            {
                openFileDialog1.Filter = "SmartDB|*.db|all files|*.*";
                openFileDialog1.RestoreDirectory = true;
                openFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "db\\";

                DialogResult res = openFileDialog1.ShowDialog();

                if (res == DialogResult.OK)
                {
                    FileInfo file = new FileInfo(openFileDialog1.FileName);
                    try
                    {
                        txt_path_SmartDB.Text = file.FullName;
                        //txt_path_SmartDB.Text = Path.GetFileName(openFileDialog1.FileName);
                    }
                    catch (Exception ex)
                    {
                    }
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }

        }

        private void btn_localDB_Click(object sender, EventArgs e)
        {
            try
            {
                openFileDialog1.Filter = "all files|*.*";
                openFileDialog1.RestoreDirectory = true;
                //openFileDialog1.InitialDirectory = Directory.GetCurrentDirectory() + "\\" + "db\\";

                DialogResult res = openFileDialog1.ShowDialog();
                //openFileDialog1.Filter = "db|*.db|sqlite3|all files|*.*";

                if (res == DialogResult.OK)
                {
                    FileInfo file = new FileInfo(openFileDialog1.FileName);
                    try
                    {
                        txt_path_LocalDB.Text = file.FullName;
                        //txt_path_LocalDB.Text = Path.GetFileName(openFileDialog1.FileName);
                    }
                    catch (Exception ex)
                    {
                    }
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }

            try
            {
                string connection_string_SmartDB = @"Data Source=" + txt_path_LocalDB.Text.Trim() + ";";
                //LoadLocal_Old_DB2(connection_string_SmartDB);

                string Qury = "SELECT DISTINCT  mk_sn   FROM users ;";
                try
                {
                    using (var cnn = new SQLiteConnection(connection_string_SmartDB))
                    //using (var cnn = Sql_DataAccess.GetConnection())
                    {
                        List<string> RB= cnn.Query<string>(Qury, new DynamicParameters()).ToList();
                        rjComboBox1.DataSource = RB;
                    }

                   
                }
                catch (Exception ex) { MessageBox.Show("DISTINCT SN\n" + ex.Message); }





            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }

        }

        [Obsolete]
        private void btn_RestorDB_Click(object sender, EventArgs e)
        {
            if (txt_path_LocalDB.Text == "")
                return;

            using (Form_WaitForm fRM = new Form_WaitForm(SmartLocalDBRestor))
                fRM.ShowDialog();

            //string connection_string_SmartDB = @"Data Source=" + txt_path_LocalDB.Text.Trim() + ";";
            //LoadLocal_Old_DB2(connection_string_SmartDB);
            return;
        }

        [Obsolete]
        void SmartLocalDBRestor()
        {
            string connection_string_SmartDB = @"Data Source=" + txt_path_LocalDB.Text.Trim() + ";";
            LoadLocal_Old_DB2(connection_string_SmartDB);
        }
        private void LoadLocal_Old_DB()
        {

            string connection_string_localDB = @"Data Source=" + txt_path_LocalDB.Text.Trim();
            List<SourceCardsUserManager_fromDB> sourceCards = new List<SourceCardsUserManager_fromDB>();

            string rb = Global_Variable.Mk_resources.RB_SN;
            List<UmProfile> profile = Global_Variable.UM_Profile;

            List<UmUser> umUsers = new List<UmUser>();
            List<UmPyment> umPyment = new List<UmPyment>();
            List<UmSession> umSession = new List<UmSession>();

            List<Old_UmUsers> old_UmUsers = new List<Old_UmUsers>();
            List<Old_UmPayments> old_UmPayments = new List<Old_UmPayments>();
            List<Old_UmSession> old_UmSessions = new List<Old_UmSession>();

            List<NumberPrintCard> NumberPrint = Smart_DB.Load<NumberPrintCard>($"SELECT * FROM NumberPrintCard where Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'");
            var sellingPoint = Smart_DB.Load<SellingPoint>($"select * from SellingPoint where  Rb='{Global_Variable.Mk_resources.RB_code}'  or Rb='{Global_Variable.Mk_resources.RB_SN}' ");


            try
            {

                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    old_UmUsers = cnn.Query<Old_UmUsers>($"select * from users where mk_sn='{rb}'").ToList();
                }
                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    old_UmPayments = cnn.Query<Old_UmPayments>($"select * from payments where mk_sn='{rb}'").ToList();
                }
               
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }


            foreach (var row in old_UmUsers)
            {
                try
                {
                    UmUser UM = new UmUser();

                    UM.UserName = row.username;
                    UM.Password = row.password;
                    UM.SN = (long)row.sn;
                    UM.IdHX = "*" + UM.SN.ToString("X");

                    UM.ProfileName = string.IsNullOrEmpty(row.profile) || row.profile != "انتهى الرصيد" || row.profile != "خطاء عند الاظافة" ? row.profile : null;
                    UM.CustomerName = row.customer;

                    UM.SpCode = row.id_sp;
                    UM.SpName = (from v in sellingPoint where (v.Code == row.id_sp) select v.UserName).LastOrDefault();

                    int? BatchCardId = null;
                    try { BatchCardId = (from np in NumberPrint where (UM.SN >= np.Sn_from && UM.SN <= np.Sn_to) select np.NumberPrint).FirstOrDefault(); } catch { }
                    UM.NumberPrint = BatchCardId;
                    UM.BatchCardId = BatchCardId;

                    UM.Sn_Name = row.sn_user_name;
                    UM.Comment = row.comment;
                    UM.MkId = row.mk_sn;
                    UM.DeleteFromServer = 0;
                    //UM.TotalPrice = (float)row.price;
                    //UM.Price = (float)row.price;
                    //UM.MkId = Global_Variable.Mk_resources.RB_SN;
                    UM.Status = 0;

                    umUsers.Add(UM);

                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
            }
            if (umUsers.Count == 0)
                return;

            //sql_DataAccess.Add_UMUser_ToDB(umUsers);

            //=+++++++++++++++++++++++++  payment +++++++++++++++++++++++++++++++++++++++++++++++++

            //List<UmUser> sourceCardsUsers = sql_DataAccess.Get_Not_Delet_fromServer<UmUser>("UmUser");
            var PY_pyment = (from pyment in old_UmPayments
                            join umuser in umUsers on pyment.fk_sn_user_name equals umuser.Sn_Name
                            select new UmPyment
                            {
                                //IdHX = Double.TryParse(pyment.Field<string>("sn_pyment"), out double sn) == true?  "*"+Convert.ToDouble(pyment.Field<string>("sn_pyment")).ToString("X"):null,
                              
                                IdHX = (Int32.TryParse(pyment.sn_pyment, out Int32 Sn) == true ? Convert.ToDouble(pyment.sn_pyment).ToString("X") : ""),
                                Sn = (long)(Int32.TryParse(pyment.sn_pyment, out Int32 y) == true ? Convert.ToDouble(pyment.sn_pyment) : 0),
                                UserName = pyment.username,
                                Sn_Name = pyment.sn_pyment_name,
                                AddedDate = (pyment.add_date),
                                //AddedDate = utils.String_To_Datetim(pyment.add_date.ToString()),
                                Fk_Sn_Name = pyment.fk_sn_user_name,
                               
                                
                                Price = (float)Convert.ToDouble(pyment.price),
                                TotalPrice = (float)Convert.ToDouble(pyment.price_percentage),
                                ProfileName = getProfile(umuser.ProfileName, pyment.price.ToString(), profile),
                                //ProfileName = (umuser.ProfileName != null || umuser.ProfileName != "" ? umuser.ProfileName : ((from v in profile where (v.Price.ToString() == pyment.Field<string>("price").ToString()) select v.Name).FirstOrDefault() ?? "")),
                                //AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                MkId = Global_Variable.Mk_resources.RB_SN,

                                ProfileTransferLimit = ((long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select v.TransferLimit).LastOrDefault()) : 0)),
                                ProfileUptimeLimit = ((long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select v.UptimeLimit).LastOrDefault()) : 0)),
                                ProfileValidity = (long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select (v.Validity * 24 * 60 * 60)).LastOrDefault()) : 0),
                                //AddedDate = py.AddedDate
                                //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                
                            }).ToList();

            //============اضافة اليوزر الذي معه دفوعات فقط والباقي يهملة ==========
            var New_Users = (from pyment in PY_pyment join umuser in umUsers on pyment.Fk_Sn_Name equals umuser.Sn_Name   select umuser).ToList();



            if (New_Users.Count > 0)
            {
                if (sql_DataAccess.Add_UMUser_ToDB(New_Users))
                {
                    //sql_DataAccess.Add_UMUser_ToDB(umUsers,false);
                    sql_DataAccess.Add_UMPyement_ToDB(PY_pyment);
                    //sql_DataAccess.Add_UMPyement_ToDB(PY_pyment, false);

                    //====================  update users if  profie not set on table user and regDate ==============================

                    var profile_groub = (from b in PY_pyment
                                         group b by b.Fk_Sn_Name into g
                                         select new
                                         {
                                             Sn_Name = g.Key,
                                             TotalPrice = g.Sum(x => (x.Price)),
                                             Price =   g.Sum(x => (x.Price)),
                                            
                                             uptimeLimit = g.Sum(x => x.ProfileUptimeLimit),
                                             transferLimit = g.Sum(x => x.ProfileTransferLimit),
                                             ValidityLimit = g.Sum(x => x.ProfileValidity),

                                             profileName = g.Last().ProfileName,
                                             //actualLimTransfer = g.Last().ProfileTransferLimit,
                                             //actualLimUptime = g.Last().ProfileUptimeLimit,
                                             //profileValidity = g.Last().ProfileValidity,

                                             regDate = g.First().AddedDate,
                                             countProfile = g.Count()
                                         }).ToList();


                    var Users_update = (from pg in profile_groub
                                        join u in New_Users on pg.Sn_Name equals u.Sn_Name 
                                        select new UmUser
                                        {
                                            RegDate = pg.regDate,
                                            //RegDate = (u.RegDate == null ? pg.regDate : u.RegDate),
                                            //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                            TotalPrice = u.TotalPrice == 0 || u.TotalPrice < pg.TotalPrice ? pg.TotalPrice : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                            Price = u.TotalPrice == 0 || u.TotalPrice < pg.TotalPrice ? pg.TotalPrice : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها

                                            UptimeLimit = u.UptimeLimit <= pg.uptimeLimit ? pg.uptimeLimit : u.UptimeLimit,
                                            TransferLimit = u.TransferLimit <= pg.transferLimit ? pg.transferLimit : u.TransferLimit,
                                            ValidityLimit = u.ValidityLimit <= pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,
                                            ProfileValidity = u.ProfileValidity <= pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,
                                            ProfileName = pg.profileName,

                                            //actualLimTransfer = pg.actualLimTransfer,
                                            //actualLimUptime = pg.actualLimUptime,

                                            CountProfile = pg.countProfile,
                                            ProfileTimeLeft = (pg.uptimeLimit - u.UptimeUsed),
                                            ProfileTransferLeft = (pg.transferLimit - (u.DownloadUsed + u.UploadUsed)),
                                            ProfileTillTime = u.ProfileTillTime != null ? (u.ProfileTillTime.Value.AddDays(pg.ValidityLimit)) : null,
                                            //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                            Sn_Name=u.Sn_Name,
                                        }).ToList();

                    sql_DataAccess.Add_UMUser_ToDB(Users_update, false, true, true, false);



                }



            }
            //==============

            //double CountSession = 0;
            //using (SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT count(id) as count  FROM sessions where mk_sn='" + rb + "'", connection_string_localDB))
            //{
            //    DataTable tbFound = new DataTable();
            //    adapter.Fill(tbFound);
            //    dtsession = tbFound;
            //    CountSession = Convert.ToDouble(dtsession.Rows[0]["count"].ToString());
            //}
            //double forCount = 0;

            //forCount = CountSession / 500000;
            //if (CountSession / 500000 != 1)
            //    forCount = (int)forCount + 1;

            //long OFFSET = 0;

            //long LIMIT = 500000;
            ////long Start_LIMIT = 500000;

            //for (int i = 0; i < forCount; i++)
            //{
            //    using (SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT *  FROM sessions where mk_sn='" + rb + "' LIMIT " + LIMIT + "  OFFSET " + OFFSET + ";", connection_string_localDB))
            //    {
            //        DataTable tbFound = new DataTable();
            //        adapter.Fill(tbFound);
            //        dtsession = tbFound;
            //    }
            //    OFFSET = OFFSET + LIMIT;

            //    sysn_Session(dtsession, sourceCardsUsers);
            //}

            old_UmUsers.Clear();
            old_UmPayments.Clear();

            ////=+++++++++++++++++++++++++  Session +++++++++++++++++++++++++++++++++++++++++++++++++

            double CountSession = 0;
            using (SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT count(id) as count  FROM sessions where mk_sn='" + rb + "'", connection_string_localDB))
            {
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                CountSession = Convert.ToDouble(tbFound.Rows[0]["count"].ToString());
            }
            double forCount = 0;
            long LIMIT = 80000;
            forCount = CountSession / LIMIT;
            if (CountSession / LIMIT != 1)
                forCount = (int)forCount + 1;
            long OFFSET = 0;
            //long Start_LIMIT = 500000;


            for (int i = 0; i < forCount; i++)
            {
                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    old_UmSessions = cnn.Query<Old_UmSession>($"SELECT *  FROM sessions where mk_sn='{rb}' LIMIT  {LIMIT}  OFFSET {OFFSET} ;").ToList();
                    //old_UmSessions = cnn.Query<Old_UmSession>($"select * from sessions where mk_sn='{rb}'").ToList();
                }
                OFFSET = OFFSET + LIMIT;

                var sess_Users = (from session in old_UmSessions
                                  join umuser in New_Users on session.fk_sn_user_name equals umuser.Sn_Name
                                  select new UmSession
                                  {
                                      IdHX ="*"+ Convert.ToInt64( Convert.ToDouble(session.sn_sess)).ToString("X"),
                                      //IdHX = (double.TryParse(session.sn_sess, out double Sn)?Convert.ToInt64( Convert.ToDouble(session.sn_sess)).ToString("X") : ""),
                                      //IdHX = Get_Idx(session.sn_sess),
                                      //Sn = Get_Idx_Sn(session.sn_sess),
                                      //IdHX =  Convert.ToInt64(session.sn_sess).ToString("X"),
                                      Sn = (long)Convert.ToDouble(session.sn_sess),
                                      //Sn = (Int64.TryParse(session.sn_sess, out Int64 Snn) ? Convert.ToInt64(session.sn_sess) : 0),
                                      UserName = session.username,
                                      Sn_Name = session.sn_sess_name,
                                      NasPortId = session.nas_prot,
                                      CallingStationId = session.mac,
                                      IpUser = session.user_ip,
                                      IpRouter = session.ip_host,
                                      FromTime = (session.Date_from),
                                      //FromTime = utils.String_To_Datetim(session.Date_from),
                                      TillTime = (session.till_time),
                                      //TillTime = utils.String_To_Datetim(session.Field<string>("till_time")),

                                      //TillTime = session.tillTime != null ? utils.String_To_Datetime_By_V_MK(session.tillTime) : null,
                                      UpTime = (long)(session.uptime),
                                      BytesDownload = (long)(session.download),
                                      BytesUpload = (long)(session.upload),
                                      //BytesDownload = long.Parse(session.bytesDownload),
                                      //BytesUpload = long.Parse(session.bytesUpload),

                                      Fk_Sn_Name = session.fk_sn_user_name,
                                      //UmUserId = umuser.Id,
                                      //AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      MkId = Global_Variable.Mk_resources.RB_SN,

                                      DeleteFromServer = 0
                                  }).ToList();

                if (sess_Users.Count == 0)
                    continue;

                if (sql_DataAccess.Add_UMSession_ToDB(sess_Users))
                {
                    //============== update user table first use ============

                    var SessionLQ = (from r in sess_Users
                                     group r by r.Fk_Sn_Name into g
                                     //orderby(g => r.FromTime)
                                     select new
                                     {
                                         Fk_Sn_Name = g.First().Fk_Sn_Name,
                                         //UmUserId = g.First().UmUserId,
                                         //FromTime = g.OrderBy(r => r.FromTime).First(),
                                         FromTime = g.First().FromTime,

                                         UptimeUsed = g.Sum(x => x.UpTime),
                                         DownloadUsed = g.Sum(x => x.BytesDownload),
                                         UploadUsed = g.Sum(x => x.BytesUpload),

                                         IpRouter = g.First().IpRouter,
                                         NasPortId = g.First().NasPortId,

                                     }).ToList();

                    //g.OrderBy(r => r.FromTime).First()).ToList();

                    var Users_update = (from ses in SessionLQ
                                        join u in New_Users on ses.Fk_Sn_Name equals u.Sn_Name
                                        //where u.FirsLogin == null
                                        select new UmUser
                                        {
                                            //Id = u.Id,
                                            FirsLogin = ses.FromTime,
                                            Radius = u.Radius != "" || u.Radius != null ? ses.IpRouter : u.Radius,
                                            NasPortId = u.NasPortId != "" || u.NasPortId != null ? ses.NasPortId : u.NasPortId,

                                            UptimeUsed = u.UptimeUsed == 0 ? ses.UptimeUsed : u.UptimeUsed,
                                            DownloadUsed = u.DownloadUsed == 0 ? ses.DownloadUsed : u.DownloadUsed,
                                            UploadUsed = u.UploadUsed == 0 ? ses.UploadUsed : u.UploadUsed,
                                            //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                            Sn_Name=u.Sn_Name,
                                            //TransferLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.transferLimit).LastOrDefault()),
                                            //UptimeLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.uptimeLimit).LastOrDefault()),
                                            //ValidityLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.Validity).LastOrDefault()),


                                        }).ToList();

                    using (var con = Sql_DataAccess.GetConnection())
                    {
                        try
                        {
                            string query =
                                        "update UmUser set "
                                        + "[Radius]=@Radius, "
                                        + "[NasPortId]=@NasPortId, "
                                        + "[FirsLogin]=@FirsLogin, "
                                        + "[UptimeUsed]=@UptimeUsed, "
                                        + "[DownloadUsed]=@DownloadUsed, "
                                        + "[UploadUsed]=@UploadUsed, "
                                        + "[Status]=1 "
                                        + " WHERE Sn_Name = @Sn_Name;";

                            con.Open();
                            var sqLiteTransaction = con.BeginTransaction();
                            int effect=con.Execute(query, Users_update, sqLiteTransaction);
                            sqLiteTransaction.Commit();

                        }
                        catch (Exception ex) { MessageBox.Show("LoadLocal_Old_DB\n" + ex.Message); }
                    }
                    //SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                    //sql_DataAccess.Add_UMUser_ToDB(Users_update, false, true, false, true);

                }
            }

        }

        [Obsolete]
        public bool LoadLocal_Old_DB2(string path)
        {

            string connection_string_localDB = path;
            try
            {
                ///test connection---
                path = path.TrimEnd('\r', '\n');
                connection_string_localDB = path;
                SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT count(*) FROM users;", path);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
            }
            catch (Exception ex)
            {
                MessageBox.Show("خطاء في  ملف قاعدة البيانات المحلية \n" + ex.Message);
                return false;
            }
            List<SourceCardsUserManager_fromDB> sourceCards = new List<SourceCardsUserManager_fromDB>();

            
            string rb = Global_Variable.Mk_resources.RB_SN;
            if(rjCheckBox1.Check)
            {
                rb = rjComboBox1.Text;
            }


            //=============== get profile ================
            Mk_DataAccess mk = new Mk_DataAccess();
            if(Global_Variable.Source_profile==null)
            Global_Variable.Source_profile = mk.GetSource_UserManager_Profile(); 
            if (Global_Variable.Source_limtition == null)
                Global_Variable.Source_limtition = mk.GetSource_UserManager_Limit();
            if (Global_Variable.Source_profile_limtition == null)
                Global_Variable.Source_profile_limtition = mk.GetSource_UserManager_Profile_Limtition();


            UmProfile uprofile = new UmProfile();
            Global_Variable.UM_Profile = uprofile.Get_UMProfile();
            List<UmProfile> profile = Global_Variable.UM_Profile;



            List<UmUser> umUsers = new List<UmUser>();
            List<UmPyment> umPyment = new List<UmPyment>();
            List<UmSession> umSession = new List<UmSession>();

            List<Old_UmUsers> old_UmUsers = new List<Old_UmUsers>();
            List<Old_UmPayments> old_UmPayments = new List<Old_UmPayments>();
            List<Old_UmSession> old_UmSessions = new List<Old_UmSession>();

            List<NumberPrintCard> NumberPrint = Smart_DB.Load<NumberPrintCard>($"SELECT * FROM NumberPrintCard where Rb='{Global_Variable.Mk_resources.RB_SN}'");
            var sellingPoint = Smart_DB.Load<SellingPoint>($"select * from SellingPoint where   Rb='{Global_Variable.Mk_resources.RB_SN}' ");

            try
            {
                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    string qury = $@"SELECT us.username as UserName
	                               ,us.password as Password
	                               ,us.sn+0 as SN
	                               ,us.profile as ProfileName
	                               ,us.customer as CustomerName
	                               ,us.customer as CustomerName
	                               ,us.id_sp as SpCode
	                               ,us.sn_user_name as Sn_Name
	                               ,us.mk_sn as MkId
	                               ,sum(us.price) as Price
	                               ,sum(us.price) as TotalPrice
	   
                             FROM users as us 
			                            INNER JOIN payments as py  on us.sn_user_name = py.fk_sn_user_name 
			                            WHERE us.mk_sn ='{rb}' and  py.mk_sn ='{rb}'
			                            GROUP BY py.fk_sn_user_name 
			                            ";

                    umUsers = cnn.Query<UmUser>(qury).ToList();
                }
                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    string qury = $@"SELECT us.username as UserName
	 
	                               ,us.sn_pyment+0 as Sn
	                               ,us.sn_pyment_name as Sn_Name
	                               ,us.add_date as AddedDate
	                               ,us.fk_sn_user_name as Fk_Sn_Name
	                               ,us.price as Price
	                               ,us.price_percentage as TotalPrice
	                               ,us.mk_sn as MkId
	   
                             FROM payments as us 
			                            INNER JOIN users as py  on us.fk_sn_user_name = py.sn_user_name 
			                            WHERE us.mk_sn ='{rb}'  and  py.mk_sn ='{rb}'
			                            GROUP BY us.fk_sn_user_name 
			                            ";

                    umPyment = cnn.Query<UmPyment>(qury).ToList();
                }
            }
            catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }
            List<UmUser> New_Umuser = new List<UmUser>();
            
            foreach (var row in umUsers)
            {
                try
                {
                    UmUser UM = new UmUser();

                    UM.UserName = row.UserName;
                    UM.Password = row.Password;
                    UM.SN = (long)row.SN;
                    UM.IdHX = "*" + UM.SN.ToString("X");

                    UM.ProfileName = string.IsNullOrEmpty(row.ProfileName) || row.ProfileName != "انتهى الرصيد" || row.ProfileName != "خطاء عند الاظافة" ? row.ProfileName : null;
                    UM.CustomerName = row.CustomerName;

                    UM.SpCode = row.SpCode;
                    UM.SpName = (from v in sellingPoint where (v.Code == row.SpCode) select v.UserName).LastOrDefault();

                    int? BatchCardId = null;
                    try { BatchCardId = (from np in NumberPrint where (UM.SN >= np.Sn_from && UM.SN <= np.Sn_to) select np.NumberPrint).FirstOrDefault(); } catch { }
                    UM.NumberPrint = BatchCardId;
                    UM.BatchCardId = BatchCardId;

                    UM.Sn_Name = row.Sn_Name;
                    UM.Comment = row.Comment;
                    UM.MkId = row.MkId;
                    UM.DeleteFromServer = 0;
                    //UM.TotalPrice = (float)row.price;
                    //UM.Price = (float)row.price;
                    //UM.MkId = Global_Variable.Mk_resources.RB_SN;
                    UM.Status = 0;

                    New_Umuser.Add(UM);

                }
                catch (Exception ex) { MessageBox.Show(ex.Message); }
            }
            if (New_Umuser.Count == 0)
                return false;

            //sql_DataAccess.Add_UMUser_ToDB(umUsers);

            //=+++++++++++++++++++++++++  payment +++++++++++++++++++++++++++++++++++++++++++++++++

            //List<UmUser> sourceCardsUsers = sql_DataAccess.Get_Not_Delet_fromServer<UmUser>("UmUser");
            var PY_pyment = (from pyment in umPyment
                             join umuser in New_Umuser on pyment.Fk_Sn_Name equals umuser.Sn_Name
                             select new UmPyment
                             {
                                 IdHX = (pyment.Sn).ToString("X"),
                                 Sn = (long)(pyment.Sn) ,
                                 UserName = pyment.UserName,
                                 Sn_Name = pyment.Sn_Name,
                                 AddedDate = (pyment.AddedDate),
                                 //AddedDate = utils.String_To_Datetim(pyment.add_date.ToString()),
                                 Fk_Sn_Name = pyment.Fk_Sn_Name,


                                 Price = (pyment.Price),
                                 TotalPrice = (pyment.TotalPrice),
                                 ProfileName = getProfile(umuser.ProfileName, pyment.Price.ToString(), profile),
                                 //ProfileName = (umuser.ProfileName != null || umuser.ProfileName != "" ? umuser.ProfileName : ((from v in profile where (v.Price.ToString() == pyment.Field<string>("price").ToString()) select v.Name).FirstOrDefault() ?? "")),
                                 //AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                 MkId = Global_Variable.Mk_resources.RB_SN,

                                 ProfileTransferLimit = ((long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select v.TransferLimit).LastOrDefault()) : 0)),
                                 ProfileUptimeLimit = ((long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select v.UptimeLimit).LastOrDefault()) : 0)),
                                 ProfileValidity = (long)((!string.IsNullOrEmpty(_ProfileName)) ? ((from v in profile where (_ProfileName == v.Name) select (v.Validity * 24 * 60 * 60)).LastOrDefault()) : 0),
                                 //AddedDate = py.AddedDate
                                 //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                             }).ToList();

            //============اضافة اليوزر الذي معه دفوعات فقط والباقي يهملة ==========
            //var New_Users = (from pyment in PY_pyment join umuser in umUsers on pyment.Fk_Sn_Name equals umuser.Sn_Name select umuser).ToList();

            //sql_DataAccess.Add_UMPyement_ToDB(PY_pyment);

            if (PY_pyment.Count > 0)
            {
                if (sql_DataAccess.Add_UMUser_ToDB(New_Umuser))
                {
                    //sql_DataAccess.Add_UMUser_ToDB(umUsers,false);
                    sql_DataAccess.Add_UMPyement_ToDB(PY_pyment);
                    //sql_DataAccess.Add_UMPyement_ToDB(PY_pyment, false);

                    //====================  update users if  profie not set on table user and regDate ==============================

                    var profile_groub = (from b in PY_pyment
                                         group b by b.Fk_Sn_Name into g
                                         select new
                                         {
                                             Sn_Name = g.Key,
                                             TotalPrice = g.Sum(x => (x.Price)),
                                             Price = g.Sum(x => (x.Price)),

                                             uptimeLimit = g.Sum(x => x.ProfileUptimeLimit),
                                             transferLimit = g.Sum(x => x.ProfileTransferLimit),
                                             ValidityLimit = g.Sum(x => x.ProfileValidity),

                                             profileName = g.Last().ProfileName,
                                             //actualLimTransfer = g.Last().ProfileTransferLimit,
                                             //actualLimUptime = g.Last().ProfileUptimeLimit,
                                             //profileValidity = g.Last().ProfileValidity,

                                             regDate = g.First().AddedDate,
                                             countProfile = g.Count()
                                         }).ToList();


                    var Users_update = (from pg in profile_groub
                                        join u in New_Umuser on pg.Sn_Name equals u.Sn_Name
                                        select new UmUser
                                        {
                                            RegDate = pg.regDate,
                                            //RegDate = (u.RegDate == null ? pg.regDate : u.RegDate),
                                            //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                            
                                            //TotalPrice = u.TotalPrice == 0 || u.TotalPrice < pg.TotalPrice ? pg.TotalPrice : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها
                                            //Price = u.TotalPrice == 0 || u.TotalPrice < pg.TotalPrice ? pg.TotalPrice : u.TotalPrice, //  اذا مابش سعر او كان اقل يعني تم اضافة باقه جديد واذا اكبر يعني انه حذف الباقة من اليوزمنجر ما نعدلها

                                            TotalPrice =  u.TotalPrice, 
                                            Price =  u.TotalPrice, //


                                            //UptimeLimit = u.UptimeLimit <= pg.uptimeLimit ? pg.uptimeLimit : u.UptimeLimit,
                                            //TransferLimit = u.TransferLimit <= pg.transferLimit ? pg.transferLimit : u.TransferLimit,
                                            //ValidityLimit = u.ValidityLimit <= pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,
                                            //ProfileValidity = u.ProfileValidity <= pg.ValidityLimit ? pg.ValidityLimit : u.ValidityLimit,

                                            UptimeLimit =  u.UptimeLimit,
                                            TransferLimit =  u.TransferLimit,
                                            ValidityLimit =  u.ValidityLimit,
                                            ProfileValidity = u.ValidityLimit,

                                            ProfileName = pg.profileName,

                                            //actualLimTransfer = pg.actualLimTransfer,
                                            //actualLimUptime = pg.actualLimUptime,

                                            CountProfile = pg.countProfile,
                                            //ProfileTimeLeft = (pg.uptimeLimit - u.UptimeUsed),
                                            //ProfileTransferLeft = (pg.transferLimit - (u.DownloadUsed + u.UploadUsed)),
                                            //ProfileTillTime = u.ProfileTillTime != null ? (u.ProfileTillTime.Value.AddDays(pg.ValidityLimit)) : null,
                                            //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                            Sn_Name = u.Sn_Name,
                                        }).ToList();

                    sql_DataAccess.Add_UMUser_ToDB(Users_update, false, true, true, false);
                }
            }
            //==============
            ////=+++++++++++++++++++++++++  Session +++++++++++++++++++++++++++++++++++++++++++++++++

            string queryUpdateUser = $@"SELECT ses.fk_sn_user_name as Sn_Name, 
	                                   ses.Date_from as FirsLogin ,
	                                   sum(ses.uptime) as UptimeUsed,
	                                   sum(ses.download) as DownloadUsed,
	                                   sum(ses.upload) as UploadUsed,
	                                   ses.ip_host as Radius,
	                                   ses.nas_prot as NasPortId
	                                   FROM sessions as ses 
	                                   INNER JOIN users as us  on ses.fk_sn_user_name = us.sn_user_name 
	                                   WHERE us.mk_sn ='{rb}'  AND ses.mk_sn ='{rb}'
	                                   GROUP BY ses.fk_sn_user_name HAVING MIN(ses.Date_from)";
            
            using (var cnn = new SQLiteConnection(connection_string_localDB))
            {
                //string query = @$"";
                var mSessions = cnn.Query<UmUser>(queryUpdateUser).ToList();

                using (var con = Sql_DataAccess.GetConnection())
                {
                    try
                    {
                        string query2 =
                                    "update UmUser set "
                                    + "[Radius]=@Radius, "
                                    + "[NasPortId]=@NasPortId, "
                                    + "[FirsLogin]=@FirsLogin, "
                                    + "[UptimeUsed]=@UptimeUsed, "
                                    + "[DownloadUsed]=@DownloadUsed, "
                                    + "[UploadUsed]=@UploadUsed, "
                                    + "[Status]=1 "
                                    + " WHERE Sn_Name = @Sn_Name;";

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        int effect = con.Execute(query2, mSessions, sqLiteTransaction);
                        sqLiteTransaction.Commit();
                    }
                    catch (Exception ex) { MessageBox.Show("LoadLocal_Old_DB2 Session\n" + ex.Message); }
                }
            }


            double CountSession = 0;
            using (SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT count(id) as count  FROM sessions where mk_sn='" + rb + "'", connection_string_localDB))
            {
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                CountSession = Convert.ToDouble(tbFound.Rows[0]["count"].ToString());
            }
            double forCount = 0;
            long LIMIT = 200000;
            forCount = CountSession / LIMIT;
            if (CountSession / LIMIT != 1)
                forCount = (int)forCount + 1;
            long OFFSET = 0;
            //long Start_LIMIT = 500000;


            for (int i = 0; i < forCount; i++)
            {
                using (var cnn = new SQLiteConnection(connection_string_localDB))
                {
                    string query = @$"";
                    old_UmSessions = cnn.Query<Old_UmSession>($"SELECT *  FROM sessions where mk_sn='{rb}' LIMIT  {LIMIT}  OFFSET {OFFSET} ;").ToList();
                }
                OFFSET = OFFSET + LIMIT;

                var sess_Users = (from session in old_UmSessions
                                  join umuser in umUsers on session.fk_sn_user_name equals umuser.Sn_Name
                                  select new UmSession
                                  {
                                      IdHX = "*" + Convert.ToInt64(Convert.ToDouble(session.sn_sess)).ToString("X"),
                                      //IdHX = (double.TryParse(session.sn_sess, out double Sn)?Convert.ToInt64( Convert.ToDouble(session.sn_sess)).ToString("X") : ""),
                                      //IdHX = Get_Idx(session.sn_sess),
                                      //Sn = Get_Idx_Sn(session.sn_sess),
                                      //IdHX =  Convert.ToInt64(session.sn_sess).ToString("X"),
                                      Sn = (long)Convert.ToDouble(session.sn_sess),
                                      //Sn = (Int64.TryParse(session.sn_sess, out Int64 Snn) ? Convert.ToInt64(session.sn_sess) : 0),
                                      UserName = session.username,
                                      Sn_Name = session.sn_sess_name,
                                      NasPortId = session.nas_prot,
                                      CallingStationId = session.mac,
                                      IpUser = session.user_ip,
                                      IpRouter = session.ip_host,
                                      FromTime = (session.Date_from),
                                      //FromTime = utils.String_To_Datetim(session.Date_from),
                                      TillTime = (session.till_time),
                                      //TillTime = utils.String_To_Datetim(session.Field<string>("till_time")),

                                      //TillTime = session.tillTime != null ? utils.String_To_Datetime_By_V_MK(session.tillTime) : null,
                                      UpTime = (long)(session.uptime),
                                      BytesDownload = (long)(session.download),
                                      BytesUpload = (long)(session.upload),
                                      //BytesDownload = long.Parse(session.bytesDownload),
                                      //BytesUpload = long.Parse(session.bytesUpload),

                                      Fk_Sn_Name = session.fk_sn_user_name,
                                      //UmUserId = umuser.Id,
                                      //AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      //LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                      MkId = Global_Variable.Mk_resources.RB_SN,

                                      DeleteFromServer = 0
                                  }).ToList();

                if (sess_Users.Count == 0)
                    continue;

                if (sql_DataAccess.Add_UMSession_ToDB(sess_Users))
                {
                }
            
            
            }


            return true;
        }


        string _ProfileName = null;
        string getProfile(string ProfileName, string price, List<UmProfile> profile)
        {
            _ProfileName = null;
            try
            {
                _ProfileName = (ProfileName != null || ProfileName != "" ? ProfileName : ((from v in profile where (v.Price.ToString() == price) select v.Name).FirstOrDefault() ?? ""));
            }
            catch { }
            return _ProfileName;
        }
        private string Get_Idx(string sn)
        {
            bool is_int = Int64.TryParse(sn, out Int64 Sna);
            string idx = "";
            try
            {
                 is_int = double.TryParse(sn, out double sSna);

                if (is_int)
                {
                    idx =  Convert.ToDouble(sn).ToString("X");
                }
                //idx = Int64.TryParse(sn, out Int64 Sn) ? Convert.ToInt64(sn).ToString("X") : "";
            }
            catch { }
            try
            {
                //string IdHX = (Int32.TryParse(sn, out Int32 Sn) == true ? Convert.ToDouble(sn).ToString("X") : "");
                //var Sn2 = (long)(Int32.TryParse(sn, out Int32 y) == true ? Convert.ToDouble(sn) : 0);

                //var Sn22 = (long)(double.TryParse(sn, out double yd) == true ? Convert.ToDouble(sn) : 0);

                string IdHX2 = (double.TryParse(sn, out double Snd) == true ?Convert.ToInt64( Convert.ToDouble(sn)).ToString("X") : "");

                //is_int = double.TryParse(sn, out double sSna);

                //if (is_int)
                //{
                //    idx =  Convert.ToDouble(sn).ToString("X");
                //}
                idx = IdHX2;
                //idx = Int64.TryParse(sn, out Int64 Sn) ? Convert.ToInt64(sn).ToString("X") : "";
            }
            catch { }
            return idx;
        }
        private long Get_Idx_Sn(string Id)
        {
            long idx  = (long)Convert.ToDouble(Id);



            return idx;
        }
        //=+++++++++++++++++++++++++  Session +++++++++++++++++++++++++++++++++++++++++++++++++
        private void sysn_Session(DataTable dtsession, List<UmUser> sourceCardsUsers)
        {
            //=+++++++++++++++++++++++++  Session +++++++++++++++++++++++++++++++++++++++++++++++++
            var sess_Users = (from session in dtsession.AsEnumerable()
                              join umuser in sourceCardsUsers on session.Field<string>("fk_sn_user_name") equals umuser.Sn_Name
                              //where session.status.ToLower().Contains("stop") || session.status.ToLower().Contains("close")
                              //from us in user.DefaultIfEmpty()
                              select new UmSession
                              {
                                  IdHX = (Int32.TryParse(session.Field<string>("sn_sess"), out Int32 Sn) == true ? Convert.ToDouble(session.Field<string>("sn_sess")).ToString("X") : ""),
                                  Sn = (long)(Int32.TryParse(session.Field<string>("sn_sess"), out Int32 y) == true ? Convert.ToDouble(session.Field<string>("sn_sess")) : 0),
                                  UserName = session.Field<string>("username"),
                                  Sn_Name = session.Field<string>("sn_sess_name"),
                                  NasPortId = session.Field<string>("nas_prot"),
                                  CallingStationId = session.Field<string>("mac"),
                                  IpUser = session.Field<string>("user_ip"),
                                  IpRouter = session.Field<string>("ip_host"),
                                  //Status = session.status,
                                  //status_str = session.status,
                                  //Active = session.active == null ? 0 : (session.active == "no" ? 0 : 1),
                                  FromTime = utils.String_To_Datetim(session.Field<string>("Date_from")),
                                  TillTime = utils.String_To_Datetim(session.Field<string>("till_time")),

                                  //TillTime = session.tillTime != null ? utils.String_To_Datetime_By_V_MK(session.tillTime) : null,
                                  UpTime = (long)Convert.ToDouble(session.Field<Int64>("uptime")),
                                  BytesDownload = (long)Convert.ToDouble(session.Field<Int64>("download")),
                                  BytesUpload = (long)Convert.ToDouble(session.Field<Int64>("upload")),
                                  //BytesDownload = long.Parse(session.bytesDownload),
                                  //BytesUpload = long.Parse(session.bytesUpload),

                                  Fk_Sn_Name = session.Field<string>("fk_sn_user_name"),
                                  //UmUserId = umuser.Id,
                                  AddedDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                  LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),
                                  MkId = Global_Variable.Mk_resources.RB_SN,

                                  DeleteFromServer = 0
                              }).ToList();

            if (sql_DataAccess.Add_UMSession_ToDB(sess_Users))
            {
                //============== update user table first use ============

                var SessionLQ = (from r in sess_Users
                                 group r by r.Fk_Sn_Name into g
                                 //orderby(g => r.FromTime)
                                 select new
                                 {
                                     Fk_Sn_Name = g.First().Fk_Sn_Name,
                                     //UmUserId = g.First().UmUserId,
                                     //FromTime = g.OrderBy(r => r.FromTime).First(),
                                     FromTime = g.First().FromTime,

                                     UptimeUsed = g.Sum(x => x.UpTime),
                                     DownloadUsed = g.Sum(x => x.BytesDownload),
                                     UploadUsed = g.Sum(x => x.BytesUpload),

                                     IpRouter = g.First().IpRouter,
                                     NasPortId = g.First().NasPortId,

                                 }).ToList();

                //g.OrderBy(r => r.FromTime).First()).ToList();

                var Users_update = (from ses in SessionLQ
                                    join u in sourceCardsUsers on ses.Fk_Sn_Name equals u.Sn_Name
                                    where u.FirsLogin == null
                                    select new UmUser
                                    {
                                        //Id = u.Id,
                                        FirsLogin = ses.FromTime,
                                        Radius = u.Radius != "" || u.Radius != null ? ses.IpRouter : u.Radius,
                                        NasPortId = u.NasPortId != "" || u.NasPortId != null ? ses.NasPortId : u.NasPortId,

                                        UptimeUsed = u.UptimeUsed == 0 ? ses.UptimeUsed : u.UptimeUsed,
                                        DownloadUsed = u.DownloadUsed == 0 ? ses.DownloadUsed : u.DownloadUsed,
                                        UploadUsed = u.UploadUsed == 0 ? ses.UploadUsed : u.UploadUsed,
                                        LastSynDb = utils.Datetime_To_DateTime_Format_DB(DateTime.Now),

                                        //TransferLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.transferLimit).LastOrDefault()),
                                        //UptimeLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.uptimeLimit).LastOrDefault()),
                                        //ValidityLimit = (long)(u.ProfileName == null ? 0 : (from v in profile.AsEnumerable() where (v.Name == u.ProfileName) select v.Validity).LastOrDefault()),


                                    }).ToList();

                using (var con = Sql_DataAccess.GetConnection())
                {
                    try
                    {
                        string query =
                                    "update UmUser set "
                                    + "[Radius]=@Radius, "
                                    + "[NasPortId]=@NasPortId, "
                                    + "[FirsLogin]=@FirsLogin, "
                                    + "[UptimeUsed]=@UptimeUsed, "
                                    + "[DownloadUsed]=@DownloadUsed, "
                                    + "[UploadUsed]=@UploadUsed, "
                                    + "[LastSynDb]=@LastSynDb "
                                    + " WHERE Id = @Id;";

                        con.Open();
                        var sqLiteTransaction = con.BeginTransaction();
                        con.Execute(query, Users_update, sqLiteTransaction);
                        sqLiteTransaction.Commit();

                    }
                    catch (Exception ex) { MessageBox.Show("sysn_Session"+ex.Message); }
                }
                //SqlDataAccess.Update_UM_user_to_LocalDB_AfterSessionGet(Users_update);
                sql_DataAccess.Add_UMUser_ToDB(Users_update, false, true, false, true);

            }
        }
        private void set_BackroundFromPath(bool fromDB)
        {


        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            var path = @"d:\home\site\wwwroot\content\data";
            var dbPath = $"{path}{Path.DirectorySeparatorChar}ghost.db";
            var dbBackupPath = $"{path}{Path.DirectorySeparatorChar}backup.db";
            Console.WriteLine($"Backup running in {System.AppContext.BaseDirectory}");
            Console.WriteLine($"About to backup db from {dbPath} to {dbBackupPath}");
            using (var source = new SQLiteConnection($"Data Source={dbPath}; Version=3;"))
            using (var destination = new SQLiteConnection($"Data Source={dbBackupPath}; Version=3;"))
            {
                source.Open();
                destination.Open();
                source.BackupDatabase(destination, "main", "main", -1, null, 0);
            }
            Console.WriteLine("Backed up db successfully");
        }

        private void btn_RestorSmartDB_Click(object sender, EventArgs e)
        {
            if (txt_path_SmartDB.Text == "")
                return;

            //string connection_string_SmartDB = @"Data Source=" + txt_path_SmartDB.Text.Trim() + ";";
            using (Form_WaitForm fRM = new Form_WaitForm(SmartDBRestor))
                fRM.ShowDialog();

            //Restor_SmartDB(@"Data Source=" + txt_path_SmartDB.Text.Trim() + ";");
        }
        void SmartDBRestor()
        {
            Restor_SmartDB(@"Data Source=" + txt_path_SmartDB.Text.Trim() + ";");
        }
        public bool Restor_SmartDB(string path,string sn=null)
        {
            if (sn != null)
                Global_Rb = sn;

            string connection_string_SmartDB = path;
            //string connection_string_SmartDB = @"Data Source=" + txt_path_SmartDB.Text.Trim()+";";
            try
            {
                ///test connection---
                path = path.TrimEnd('\r', '\n');
                connection_string_SmartDB = path;
                SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT * FROM table_login;", path);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
            }
            catch(Exception ex) {
                //MessageBox.Show("  خطاء في  ملف قاعدة البيانات \n"+ ex.Message);
                return false;
            }

             
            Form_Login_Setting(connection_string_SmartDB);
            Get_GUI_Setting(connection_string_SmartDB);
            Get_SellingPoing(connection_string_SmartDB);
            GetAll_Printed_Number(connection_string_SmartDB);
            get_table_info();
            Get_TemplateCards(connection_string_SmartDB);

            return true;
        }
        public bool Restor_SmartDB_firstLoad(Old_DatabaseInfo old_DatabaseInfo)
        {
            string connection_str = $@"Data Source={old_DatabaseInfo.Path.Trim()}\db\SmartDB.db;";
            //MessageBox.Show("connection_str\n  " + connection_str);

            try
            {
                SQLiteDataAdapter adapter = new SQLiteDataAdapter("SELECT * FROM table_login;", connection_str);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
            }
            catch (Exception ex)
            {
                //MessageBox.Show("  خطاء في  ملف قاعدة البيانات \n"+ ex.Message);
                return false;
            }

            CreateDefultTemplate();

            Form_Login_Setting(connection_str);

            foreach (var db in old_DatabaseInfo.Routers)
            {
                Global_Rb = db.Rb_sn;
                Get_GUI_Setting(connection_str);
                Get_SellingPoing(connection_str);
                GetAll_Printed_Number(connection_str);
                get_table_info();
                Get_TemplateCards(connection_str);

                //Restor_SmartDB(connection_str, db.Rb_sn);
            }

            return true;
        }

        public void CreateDefultTemplate()
        {
            SourceCardsTemplate sourceCardsTemplate2 = new SourceCardsTemplate();
            if (sourceCardsTemplate2.CreateDefaultTemplate(true))
            {
            }
        }

        private void Get_GUI_Setting(string source)
        {
            try
            {

                DataTable dt = new DataTable();
                try
                {
                    string Qury = "SELECT  * FROM Form_Printing_Setting ;";
                    SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);
                    DataTable tbFound = new DataTable();
                    adapter.Fill(tbFound);
                    //=========================================================
                    Form_PrintUserManagerState Frm_State = new Form_PrintUserManagerState();
                    if (tbFound.Rows.Count > 0)
                    {
                        Frm_State.txtNumberCard = tbFound.Rows[0]["Number_Cards_ToAdd"].ToString();
                        Frm_State.txt_StartCard = tbFound.Rows[0]["Start_Cards_By"].ToString();
                        Frm_State.txt_EndCard = tbFound.Rows[0]["End_Cards_By"].ToString();
                        Frm_State.txt_ShardUser = tbFound.Rows[0]["ShardUser"].ToString();

                        Frm_State.txt_longUsers = tbFound.Rows[0]["Num_Long_Name"].ToString();
                        Frm_State.txt_longPassword = tbFound.Rows[0]["Num_Long_Password"].ToString();

                        try { Frm_State.cbox_UserPassword_Pattern = Convert.ToInt16(tbFound.Rows[0]["UserPassword_Pattern"]); } catch { }
                        try { Frm_State.cbox_User_NumberORcharcter = Convert.ToInt16(tbFound.Rows[0]["User_NumberORcharcter"]); } catch { }
                        try { Frm_State.cbox_Pass_NumberORcharcter = Convert.ToInt16(tbFound.Rows[0]["Pass_NumberORcharcter"]); } catch { }

                        try { Frm_State.checkBoxFirstUse = Convert.ToBoolean(tbFound.Rows[0]["Is_First_Use_Cards"]); } catch { }
                        try { Frm_State.checkBoxSaveTo_PDF = Convert.ToBoolean(tbFound.Rows[0]["Open_File_After_Print"]); } catch { }
                        Frm_State.checkBoxSaveTo_excel = true;
                        try { Frm_State.checkBoxOpenAfterPrint = Convert.ToBoolean(tbFound.Rows[0]["Open_File_After_Print"]); } catch { }
                        Frm_State.checkBoxSaveTo_script_File = true;
                        Frm_State.checkBox_Create_without_Add_ToMicrotik = false;
                        Frm_State.checkBoxSaveTo_text_File = true;
                        Frm_State.checkBox_note = false;
                        Frm_State.txt_note = "";
                        Frm_State.CBox_CustomerUserMan = tbFound.Rows[0]["Customer_UserMan"].ToString();
                        try { Frm_State.path_saved_file = tbFound.Rows[0]["Last_Bath_File"].ToString(); } catch { }
                        try { Frm_State.PathFolderPrint = Path.GetDirectoryName(tbFound.Rows[0]["Last_Bath_File"].ToString()); } catch { }

                        string formSetting = JsonConvert.SerializeObject(Frm_State);
                        Smart_DataAccess.Setting_SaveState_Forms_Variables("FormUserManagerPrint", "SaveFromState", formSetting,Global_Rb);

                        //RJMessageBox.Show("تم حفظ اعدادت الطباعة");
                    }
                }
                catch (Exception ex)
                {

                }
            }
            catch { }
        }

        public void Form_Login_Setting(string source)
        {

            string Qury = "";
            DataTable dt = new DataTable();
            Qury = "SELECT * FROM table_login;";
            SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);
            DataTable tbFound = new DataTable();
            adapter.Fill(tbFound);
            if (tbFound.Rows.Count > 0)
            {
                for (int i = 1; i < tbFound.Rows.Count; i++)
                {
                    try
                    {
                        Form_LoingState Frm_State = new Form_LoingState();
                        try
                        {
                            string ips = tbFound.Rows[i]["Mk_IP"].ToString();
                            string[] split = ips.Split(new string[] { "." }, StringSplitOptions.None);
                            Frm_State.Mk_IP = split[0] + "." + split[1] + "." + split[2] + "." + split[3];
                        }
                        catch { }
                        Frm_State.rb_code = Global_Variable.Mk_resources.RB_SN;

                        //Frm_State.rb_ = Global_Variable.Mk_resources.RB_code;
                        Frm_State.Login_By_IP = true;
                        Frm_State.Login_By_Domain = false;
                        Frm_State.Login_By_SmartCloud = false;

                        Frm_State.Is_Use_Port = Convert.ToBoolean(tbFound.Rows[i]["is_check_Port"]);
                        Frm_State.Is_Use_Port_Domain = Convert.ToBoolean(tbFound.Rows[i]["is_check_Port"]);
                        Frm_State.Is_Use_Port_SmartCloud = Convert.ToBoolean(tbFound.Rows[i]["is_check_Port"]);
                        //Frm_State.Is_Use_Port_Domain = false;
                        //Frm_State.Is_Use_Port_SmartCloud = false;

                        if (Frm_State.Is_Use_Port)
                            if (Int32.TryParse(tbFound.Rows[i]["Mk_Port"].ToString(), out int ne))
                            {
                                Frm_State.Mk_Port_api = Convert.ToInt32(tbFound.Rows[i]["Mk_Port"].ToString());
                                Frm_State.Mk_Port_Domain = Convert.ToInt32(tbFound.Rows[i]["Mk_Port"].ToString());

                            }
                        if (tbFound.Rows[i]["login_By_IP"].ToString().ToLower() == "false")
                        {
                            Frm_State.Login_By_Domain = true;
                            Frm_State.Is_Use_Port_Domain = Frm_State.Is_Use_Port;
                            Frm_State.Login_By_IP = false;
                            Frm_State.Mk_Domain = tbFound.Rows[i]["Mk_Domain"].ToString();
                            Frm_State.Mk_Port_Domain = Frm_State.Mk_Port_api;
                        }

                        Frm_State.Mk_Domain = tbFound.Rows[i]["Mk_Domain"].ToString();
                        Frm_State.Mk_SmartCloud = "";
                        Frm_State.Mk_UserName = tbFound.Rows[i]["Mk_UserName"].ToString();
                        string key = "b14ca5898a4e4133bbce2ea2315a1916";
                        Frm_State.Mk_password = utils.Base64Encode(DecryptString(key, tbFound.Rows[i]["Mk_Password"].ToString()));
                        //Frm_State.Mk_password = utils.Base64Encode(txtPassword.Text);
                        //Frm_State.Mk_password = txtPassword.Text;
                        Frm_State.UserName_Rem = Convert.ToBoolean(tbFound.Rows[i]["UserName_Rem"].ToString());
                        Frm_State.Password_Rem = Convert.ToBoolean(tbFound.Rows[i]["Password_Rem"]);

                        Frm_State.load_by_DownloadDB = false;
                        Frm_State.load_by_Disable_LoadSession = Convert.ToBoolean(tbFound.Rows[i]["Disable_Load_Session"]); ;
                        Frm_State.load_by_Custom_Login = false;
                        Frm_State.Note = tbFound.Rows[i]["note"].ToString();
                        Frm_State.DisableLoad_HSSession = Convert.ToBoolean(tbFound.Rows[i]["LogIn_Without_mk"]);
                        Frm_State.DisableLoad_HSUsers = Convert.ToBoolean(tbFound.Rows[i]["LogIn_Without_mk"]);
                        Frm_State.DisableLoad_UmPyment = Convert.ToBoolean(tbFound.Rows[i]["LogIn_Without_mk"]);
                        Frm_State.DisableLoad_UmSession = Convert.ToBoolean(tbFound.Rows[i]["LogIn_Without_mk"]);
                        Frm_State.DisableLoad_UmUsers = Convert.ToBoolean(tbFound.Rows[i]["LogIn_Without_mk"]);

                        Global_Variable.Mk_Login_data = Frm_State;
                        if (i == 0)
                        {
                            string formSetting0 = JsonConvert.SerializeObject(Frm_State);
                            Smart_DataAccess.Setting_SaveState_Forms_Variables("FormLogin", "SaveFromState", formSetting0);
                            //Global_Variable.Mk_Login_data = Frm_State;
                        }

                        Frm_State.Name = "MK_User";
                        Frm_State.Type = "databases_users";
                        string formSetting = JsonConvert.SerializeObject(Frm_State);

                        //SqlDataAccess.AddUser_Login(formSetting);
                        if (SqlDataAccess.AddUser_Login(formSetting) == false)
                        {
                            //RJMessageBox.Show("خطاء");
                        }

                    }
                    catch (Exception ex) { MessageBox.Show(ex.Message); }
                }
                //RJMessageBox.Show("تم استعادة مستخدمين الدخول بنجاح");
            }

        }

        private void Get_SellingPoing(string source)
        {
            try
            {
                string Qury = "SELECT * FROM Selling_points;";
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);

                for (int i = 0; i < tbFound.Rows.Count; i++)
                {
                    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    if (Global_Variable.Mk_resources.RB_SN == null)
                        rb = Global_Rb;

                    long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM SellingPoint where Code='{(tbFound.Rows[i]["id"]).ToString()}'  and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{rb}'  ) ;");
                    if (foundsp > 0)
                    {
                        //RJMessageBox.Show("رقم نقطه البيع موجوده مسبقا");
                        continue;
                    }

                    SellingPoint sp = new SellingPoint();
                    sp.Code = (tbFound.Rows[i]["id"]).ToString();
                    sp.UserName = tbFound.Rows[i]["Name"].ToString();
                    sp.Address = tbFound.Rows[i]["address"].ToString();
                    sp.Suffixes = tbFound.Rows[i]["suffixes"].ToString();
                    sp.Prefixes = tbFound.Rows[i]["prefix"].ToString();
                    sp.Is_percentage = Convert.ToInt16(Convert.ToBoolean(tbFound.Rows[i]["is_percentage"].ToString()));
                    sp.Percentage = string.IsNullOrEmpty(tbFound.Rows[i]["sp_percentage"].ToString()) ? 0 : Convert.ToInt16(tbFound.Rows[i]["sp_percentage"].ToString());
                    sp.PercentageType = 0;
                    sp.Is_percentage_Custom = 0;

                    sp.UseAccounting = 0;

                    if (Global_Rb != "")
                        sp.Rb = Global_Rb;
                    else
                        sp.Rb = Global_Variable.Mk_resources.RB_SN;

                    if (Global_Rb != "")
                        sp.Rb_Sn = Global_Rb;
                    else
                        sp.Rb_Sn = Global_Variable.Mk_resources.RB_SN;

                    //sp.Phone = tbFound.Rows[i]["Name"].ToString();

                    //sp.Is_Alert = 0;
                    //sp.Is_Alert_Custom = 0;
                    //sp.Count_Soon = 0;
                    //sp.Count_Finsh = 0;
                    lock (Smart_DataAccess.Lock_object)
                    {
                        List<string> Fields = new List<string>();
                        string[] aFields = { "Code", "UserName", "Address", "Suffixes", "Prefixes", "Percentage", "Is_percentage", "PercentageType", "UseAccounting", "Is_percentage_Custom", "Rb", "Rb_Sn", "Phone"
                                         ,"Is_Alert"
                                         ,"Is_Alert_Custom"
                                         ,"Count_Soon"
                                         ,"Count_Finsh"
                    };
                        Fields.AddRange(aFields);
                        int new_sp = smart_DataAccess.InsertTable(Fields, sp, "SellingPoint");
                        if (new_sp > 0)
                        {
                            //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                            //RJMessageBox.Show("تمت عمليه الاضافة");
                            //succes = true;
                            //this.Close();
                            //return;
                        }
                        //RJMessageBox.Show("خطاء");
                    }
                    //}

                    //comboSource.Add(Convert.ToInt32(tbFound.Rows[i]["id"]), tbFound.Rows[i]["Name"].ToString());
                }
                //RJMessageBox.Show("تم استعادة نقاط البيع");
            }
            catch { }
        }

        private void Create_Seq()
        {
            try
            {
                string rb = Global_Variable.Mk_resources.RB_SN;
                if (Global_Rb != "")
                    rb = Global_Rb;


                string Query = "insert into My_Sequence ( [Name],[Seq],[Rb] ) values(@Name,@Seq,@Rb); ";

                using (var conn = Smart_DataAccess.GetConnSmart())
                {
                    try { var rwo_seq = conn.ExecuteScalar<My_Sequence>(Query, new My_Sequence { Name = "BatchCards", Seq = 0, Rb = rb }); } catch { }
                    var rwo_seq2 = conn.ExecuteScalar<My_Sequence>(Query, new My_Sequence { Name = "NumberPrint", Seq = 0, Rb = rb });
                }
            }
            catch { }
        }
        private void GetAll_Printed_Number(string source)
        {
            try
            {
                ////DataAccess.CLS_DBAccess sp = new DataAccess.CLS_DBAccess();
                //DataAccess.CLS_DBAcess_V2 sp = new DataAccess.CLS_DBAcess_V2();
                //Printed_Number = new DataTable();
                //Printed_Number = sp.GetAll_Printed_Number("usermanager");

                string rb = Global_Variable.Mk_resources.RB_SN;
                if (Global_Rb != "")
                    rb = Global_Rb;

                string Qury = "SELECT  * FROM cards_Printed where mk_sn='" + rb + "';";
                //string Qury = "SELECT  * FROM cards_Printed ;";
                //string Qury = "SELECT  * FROM cards_Printed where mk_sn='" + Global_Variable.Mk_resources.RB_SN + "' AND type='" + "usermanager" + "' ;";
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);
                DataTable tbFound = new DataTable();
                adapter.Fill(tbFound);
                //=========================================================
                Smart_DataAccess Smart_DA = new Smart_DataAccess();

                Create_Seq();

                for (int i = 0; i < tbFound.Rows.Count; i++)
                {
                    long sn_from = (long)Convert.ToDouble(tbFound.Rows[i]["sn_from"].ToString());
                    long sn_to = (long)Convert.ToDouble(tbFound.Rows[i]["sn_to"].ToString());

                    NumberPrintCard data = new NumberPrintCard();
                    BatchCard Batchdata = new BatchCard();

                    data.Id = Convert.ToInt32(tbFound.Rows[i]["id"].ToString());
                    Batchdata.Id = data.Id;

                    data.Sn_from = sn_from;
                    Batchdata.Sn_from = sn_from;

                    data.Sn_to = sn_to;
                    Batchdata.Sn_to = sn_to;

                    data.BatchNumber = Convert.ToInt32(tbFound.Rows[i]["id"].ToString());
                    Batchdata.BatchNumber = Convert.ToInt32(tbFound.Rows[i]["id"].ToString());

                    data.NumberPrint = Convert.ToInt32(tbFound.Rows[i]["id"].ToString());
                    //Batchdata.NumberPrint = Convert.ToInt32(tbFound.Rows[i]["id"].ToString());

                    data.ProfileName = tbFound.Rows[i]["profile"].ToString();
                    Batchdata.ProfileName = tbFound.Rows[i]["profile"].ToString();

                    data.AddedDate =utils.String_To_Datetim((tbFound.Rows[i]["date_add"].ToString()));
                    Batchdata.AddedDate =utils.String_To_Datetim((tbFound.Rows[i]["date_add"].ToString()));
                  
                    //data.AddedDate = Convert.ToDateTime(tbFound.Rows[i]["date_add"].ToString());
                    //Batchdata.AddedDate = Convert.ToDateTime(tbFound.Rows[i]["date_add"].ToString());

                    data.Count = (int)(sn_to - sn_from) + 1;
                    Batchdata.Count = (int)(sn_to - sn_from) + 1;


                    //data.Rb = tbFound.Rows[i]["mk_sn"].ToString();
                    //Batchdata.Rb = tbFound.Rows[i]["mk_sn"].ToString();

                    //if(Global_Rb!="")
                    //{

                    //    data.Rb = Global_Rb;
                    //    Batchdata.Rb = Global_Rb;

                    //}
                    //else
                    //{
                    //    data.Rb = Global_Variable.Mk_Router.mk_sn;
                    //    Batchdata.Rb = Global_Variable.Mk_Router.mk_sn;

                    //}
                    data.Rb = rb;
                    Batchdata.Rb = rb;

                    data.SpCode = null;
                    Batchdata.SpCode = null;

                    if (tbFound.Rows[i]["type"].ToString() == "usermanager")
                        data.Server = 0;//UserManager;
                    else
                        data.Server = 1;//Hotspot;

                    Batchdata.Server = data.Server;

                    data.BatchType = 0;//print;
                    Batchdata.BatchType = 0;//print;

                    Smart_DA.Add_NumberPrint_Cards(data, data.Server, false);
                    Smart_DA.Add_Batch_Cards(Batchdata, Batchdata.Server, false);
                }


                
                //RJMessageBox.Show("تم استعادة طبعات الكروت ");
                //string Qury = "insert into cards_Printed ([profile], [date_add], [sn_from], [sn_to],[type],mk_sn) values(?, ?, ?, ?,?,?)";
                //SQLiteCommand insertCommand = new SQLiteCommand(Qury, conn);
                //insertCommand.Parameters.AddWithValue("@profile", profile);
                //insertCommand.Parameters.AddWithValue("@date_add", DateTime.Now.ToString("yyyy/MM/dd hh:mm:ss"));
                //insertCommand.Parameters.AddWithValue("@sn_from", sn_from);
                //insertCommand.Parameters.AddWithValue("@sn_to", sn_to);
                //insertCommand.Parameters.AddWithValue("@type", "usermanager");
                //insertCommand.Parameters.AddWithValue("@mk_sn", MyDataClass.RB_SN);

            }
            catch { }
        }
        private void Get_TemplateCards(string source)
        {

            DataTable dt_TemplateCards = new DataTable();
            try
            {
                string type = "cards_template";
                string Qury = "SELECT  * FROM template_cards where type='" + type + "';";
                //string Qury = "SELECT  * FROM template_cards where mk_sn=" + MyDataClass.RB_SN + " ;";
                SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);

                adapter.Fill(dt_TemplateCards);
            }
            catch { }
            try
            {

                for (int x = 0; x < dt_TemplateCards.Rows.Count; x++)
                {
                    DataTable template_cards = new DataTable(); //Get_template_cards
                    string Qury = $"select * from template_cards where template_cards.ID_template={Convert.ToInt32(dt_TemplateCards.Rows[x]["ID_template"])}; ";
                    SQLiteDataAdapter adapter = new SQLiteDataAdapter(Qury, source);
                    adapter.Fill(template_cards);
                    //========================================================================================
                    DataTable template_items_cards_details = new DataTable();
                    Qury = $"select * from template_items_cards  INNER JOIN  template_graghics_cards_item ON template_items_cards.ID_items=template_graghics_cards_item.template_item_cards_id where template_items_cards.template_cards_id={Convert.ToInt32(dt_TemplateCards.Rows[x]["ID_template"])}; ";
                    SQLiteDataAdapter adapter2 = new SQLiteDataAdapter(Qury, source);
                    adapter2.Fill(template_items_cards_details);
                    //===================================================
                    CardsTemplate card = new CardsTemplate();
                    try
                    {
                        try { card.setingCard.enable_background = Convert.ToBoolean(template_cards.Rows[0]["enable_background"]); } catch { }
                        if (template_items_cards_details.Rows.Count <= 0) { continue; }
                        card.setingCard.path_saved_file = template_cards.Rows[0]["path_saved_file"].ToString();
                        card.setingCard.path_background = template_cards.Rows[0]["path_background"].ToString();
                        try { card.setingCard.currency = template_cards.Rows[0]["currency"].ToString(); } catch { }
                        try { card.setingCard.card_width = Convert.ToDecimal(template_cards.Rows[0]["card_width"]); } catch { }
                        try { card.setingCard.card_height = Convert.ToDecimal(template_cards.Rows[0]["card_height"]); } catch { }
                        try { card.setingCard.space_horizontal_margin = Convert.ToDecimal(template_cards.Rows[0]["space_horizontal_margin"]); } catch { }
                        try { card.setingCard.Space_vertical_margin = Convert.ToDecimal(template_cards.Rows[0]["Space_vertical_margin"]); } catch { }
                        try { card.setingCard.card_border_enable = Convert.ToBoolean(template_cards.Rows[0]["card_border_enable"]); } catch { }
                        //try { card.setingCard.card_border_Size = (float)Convert.ToDecimal(template_cards.Rows[0]["card_border_Size"]); } catch {}
                        try { card.setingCard.card_border_Size = Convert.ToInt16(template_cards.Rows[0]["card_border_Size"]); } catch { }
                        //try { card.setingCard.card_border_Color = System.Drawing.ColorTranslator.FromHtml(template_cards.Rows[0]["card_border_Color"].ToString()); } catch  {}
                        try { card.setingCard.card_border_Color = (template_cards.Rows[0]["card_border_Color"].ToString()); } catch { }
                        try { card.setingCard.Number_Pages = Convert.ToBoolean(template_cards.Rows[0]["Number_Pages"]); } catch { }
                        try
                        {
                            card.setingCard.Number_Pages_Size = Convert.ToDecimal(template_cards.Rows[0]["Number_Pages_size"]);
                            card.setingCard.Number_Pages_X = Convert.ToDecimal(template_cards.Rows[0]["Number_page_X"]);
                            card.setingCard.Number_Pages_Y = Convert.ToDecimal(template_cards.Rows[0]["Number_Page_Y"]);
                        }
                        catch (Exception ex)
                        { }
                        try
                        {
                            card.setingCard.proile_link = template_cards.Rows[0]["profile_link"].ToString();
                        }
                        catch { }
                    }
                    catch (Exception ex) { /*MessageBox.Show("SetValuToCardToGraphics   " + ex.Message);*/ }
                    //==========================================================


                    for (int i = 0; i <= template_items_cards_details.Rows.Count - 1; i++)
                    {
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "login")
                        {
                            card.cardsItems.login = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "login_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "password")
                        {
                            card.cardsItems.Password = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "password_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Squ_Number")
                        {
                            card.cardsItems.SN = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Squ_Number_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Time")
                        {
                            card.cardsItems.Time = set_value_To_All_Loc_Time(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Time_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Size")
                        {
                            card.cardsItems.Size = set_value_To_All_Loc_Size(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Size_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Price")
                        {
                            card.cardsItems.Price = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Price_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Validity")
                        {
                            card.cardsItems.Validity = set_value_To_All_Loc_Validity(i, template_items_cards_details);
                        }
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Validity_title")
                        //{
                        //}
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "OtherText1")
                        {
                            card.cardsItems.Other_Text1 = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "OtherText2")
                        {
                            card.cardsItems.Other_Text2 = set_value_To_All_Loc(i, template_items_cards_details);
                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Date_Print")
                        {
                            card.cardsItems.Date_Print = set_value_To_Date_Print(i, template_items_cards_details);

                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "Number_Print")
                        {
                            card.cardsItems.Number_Print = set_value_To_All_Loc(i, template_items_cards_details);
                        }

                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "SP")
                        {
                            card.cardsItems.SP = set_value_To_Selling_PointTemplate(i, template_items_cards_details);
                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "SP_name_or_Number")
                        {

                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "logo")
                        {
                            card.cardsItems.logo = set_value_To_All_Loc_img(i, template_items_cards_details);
                        }
                        if (template_items_cards_details.Rows[i]["item_name"].ToString() == "QR")
                        {
                            card.cardsItems.logo = set_value_To_All_Loc_img(i, template_items_cards_details);
                        }

                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "back1")
                        //{
                        //    set_value_To_All_Loc(i);
                        //    //set_value_To_All_Loc_img
                        //    loc_SP = loc_itemtemp;
                        //    //loc_back1 = loc_itemtemp;
                        //}
                        //if (template_items_cards_details.Rows[i]["item_name"].ToString() == "back2")
                        //{
                        //    set_value_To_All_Loc_img(i);
                        //    loc_back2 = loc_itemtemp;
                        //    loc_SP_name_or_Number = loc_itemtemp;

                        //}
                    }

                    //=========================================
                    try
                    {
                        SourceCardsTemplate tmplate = new SourceCardsTemplate();
                        if (Global_Rb != "")
                        {
                            tmplate.rb = Global_Rb;
                            card.rb = Global_Rb;
                        }
                        else
                        {
                            tmplate.rb = Global_Variable.Mk_Router.mk_sn;
                            card.rb = Global_Variable.Mk_Router.mk_sn;
                        }
                        tmplate.name = dt_TemplateCards.Rows[x]["name_template"].ToString();
                        tmplate.type = "design";

                        card.setingCard.name = tmplate.name;
                        card.setingCard.type = "design";
                        

                        tmplate.values = JsonConvert.SerializeObject(card);
                        if (SqlDataAccess.Add_New_Template(tmplate))
                        {
                            //RJMessageBox.Show("تم انشاء القالب بنجاح", "ok");
                        }
                    }
                    catch (Exception ex) { /*MessageBox.Show("save_template_To_DB" + "\n" + ex.Message.ToString());*/ }
                }

            }
            catch { }

            //RJMessageBox.Show("تم استعادة قوالب التصميم ");

        }

        private void get_table_info()
        {
            try
            {
                CardsTableDesg1 card = new CardsTableDesg1();


                StreamReader streamReader = new StreamReader(@"infCrd.data");
                string text = streamReader.ReadToEnd();
                streamReader.Close();
                string[] split = text.Split(new string[] { "\n" }, StringSplitOptions.None);
                int textFontSize = 9;
                try { textFontSize = Convert.ToInt32(split[7]); } catch { }
                card.cardsItems.info1.title_text = split[0];
                card.cardsItems.info2.title_text = split[1];
                card.cardsItems.info3.title_text = split[2];
                card.cardsItems.info4.title_text = split[3];
                card.cardsItems.info5.title_text = split[4];

                card.cardsItems.info1.font_size = card.cardsItems.info2.font_size = card.cardsItems.info3.font_size = card.cardsItems.info4.font_size = textFontSize;
                card.setingCard.currency = split[5];

                try { card.setingCard.NumberCulum = Convert.ToInt32(split[6]); } catch { }

                string chek_temp = split[8];
                string[] split_chek = chek_temp.Split(new string[] { "&" }, StringSplitOptions.None);

                card.cardsItems.info1.Enable = Convert.ToBoolean(split_chek[0]);
                card.cardsItems.info2.Enable = Convert.ToBoolean(split_chek[1]);
                card.cardsItems.login.Enable = Convert.ToBoolean(split_chek[2]);
                card.cardsItems.Password.Enable = Convert.ToBoolean(split_chek[3]);
                card.cardsItems.Time.Enable = Convert.ToBoolean(split_chek[4]);
                card.cardsItems.Validity.Enable = Convert.ToBoolean(split_chek[5]);
                card.cardsItems.Size.Enable = Convert.ToBoolean(split_chek[6]);
                card.cardsItems.Price.Enable = Convert.ToBoolean(split_chek[7]);
                card.cardsItems.info3.Enable = Convert.ToBoolean(split_chek[8]);
                card.cardsItems.info4.Enable = Convert.ToBoolean(split_chek[9]);
                card.cardsItems.info5.Enable = Convert.ToBoolean(split_chek[10]);
                card.cardsItems.SN.Enable = Convert.ToBoolean(split_chek[11]);
                try
                {
                    SourceCardsTemplate tmplate = new SourceCardsTemplate();
                    if (Global_Rb != "")
                    {
                        tmplate.rb = Global_Rb;
                        //card.rb = Global_Rb;
                    }
                    else
                    {
                        tmplate.rb = Global_Variable.Mk_Router.mk_sn;
                        //card.rb = Global_Variable.Mk_Router.mk_sn;
                    }

                    tmplate.name = "Default1";
                    tmplate.type = "table_Desigen1";
                    card.setingCard.name = "table_Desigen1";
                    card.setingCard.type = "table_Desigen1";
                    card.setingCard.proile_HS_link = "";
                    card.setingCard.proile_link = "";

                    tmplate.values = JsonConvert.SerializeObject(card);
                    if (SqlDataAccess.Add_New_Template(tmplate))
                    {
                        //RJMessageBox.Show("تم انشاء القالب بنجاح", "ok");
                    }
                }
                catch (Exception ex) { }
                //RJMessageBox.Show("تم استعادة قوالب جداول البيانات ");
            }
            catch { }
        }

        private PropertyItemText set_value_To_All_Loc(int indexRow, DataTable template_items_cards_details)
        {
            PropertyItemText loc_itemtemp = new PropertyItemText();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
        private format_Validity set_value_To_All_Loc_Validity(int indexRow, DataTable template_items_cards_details)
        {
            format_Validity loc_itemtemp = new format_Validity();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
        private format_Time set_value_To_All_Loc_Time(int indexRow, DataTable template_items_cards_details)
        {
            format_Time loc_itemtemp = new format_Time();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
        private format_Size set_value_To_All_Loc_Size(int indexRow, DataTable template_items_cards_details)
        {
            format_Size loc_itemtemp = new format_Size();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
            
        private Date_Print set_value_To_Date_Print(int indexRow, DataTable template_items_cards_details)
        {
            Date_Print loc_itemtemp = new Date_Print();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
        private Selling_PointTemplate set_value_To_Selling_PointTemplate(int indexRow, DataTable template_items_cards_details)
        {
            Selling_PointTemplate loc_itemtemp = new Selling_PointTemplate();
            try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
            try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
            try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
            try { loc_itemtemp.font_size = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
            try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
            try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
            try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
            try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
            try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
            try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
            try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
            //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

            return loc_itemtemp;

        }
        
        //private Logo set_value_To_Logo(int indexRow, DataTable template_items_cards_details)
        //{
        //    Logo loc_itemtemp = new Logo();
        //    try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
        //    try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
        //    try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = true; }
        //    try { loc_itemtemp. = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.font_size = 9; }
        //    try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
        //    try { loc_itemtemp.Font = (template_items_cards_details.Rows[indexRow]["item_Font"].ToString()); } catch { loc_itemtemp.Font = "tahoma"; }
        //    try { loc_itemtemp.Blod = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { loc_itemtemp.Blod = false; }
        //    try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { loc_itemtemp.italic = false; }
        //    try { loc_itemtemp.title_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { loc_itemtemp.title_show = false; }
        //    try { loc_itemtemp.title_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { loc_itemtemp.title_text = "title"; }
        //    try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { loc_itemtemp.unit_show = false; }
        //    //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }

        //    return loc_itemtemp;

        //}

        private Logo set_value_To_All_Loc_img(int indexRow, DataTable template_items_cards_details)
        {
            Logo loc_itemtemp = new Logo();
            try
            {
                try { loc_itemtemp.x = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_X"]); } catch { loc_itemtemp.x = 1; }
                try { loc_itemtemp.y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Y"]); } catch { loc_itemtemp.y = 1; }
                try { loc_itemtemp.Enable = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_enable"]); } catch { loc_itemtemp.Enable = false; }
                //try { loc_itemtemp.Size_text = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["item_Size_text"]); } catch { loc_itemtemp.Size_text = 10; }
                //try { loc_itemtemp.Color = (template_items_cards_details.Rows[indexRow]["item_Color"].ToString()); } catch { loc_itemtemp.Color = "black"; }
                //حطينا  العرض والارتفاع في حقل الخط والعريض

                try { loc_itemtemp.item_dimension_w = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Font"]); } catch { }
                try { loc_itemtemp.item_dimension_y = Convert.ToDecimal(template_items_cards_details.Rows[indexRow]["item_Blod"]); } catch { }

                //try { loc_itemtemp.Font = ""; } catch { }
                //try { loc_itemtemp.Blod = false; } catch { }

                //try { loc_itemtemp.italic = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_italic"]); } catch { }
                //try { loc_itemtemp.address_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_address_show"]); } catch { }
                //try { loc_itemtemp.address_text = (template_items_cards_details.Rows[indexRow]["item_address_show_text"].ToString()); } catch { }
                //try { loc_itemtemp.unit_show = Convert.ToBoolean(template_items_cards_details.Rows[indexRow]["item_unit_show"]); } catch { }

                //try { loc_itemtemp.id_item = Convert.ToInt16(template_items_cards_details.Rows[indexRow]["ID_items"]); } catch { }
            }
            catch (Exception ex)
            {
                MessageBox.Show("set_value_To_All_Loc_img  " + ex.Message.ToString());
            }
            return loc_itemtemp;
        }

        public static string DecryptString(string key, string cipherText)
        {
            string result = "";
            try
            {
                byte[] iv = new byte[16];
                byte[] buffer = Convert.FromBase64String(cipherText);

                using (Aes aes = Aes.Create())
                {
                    aes.Key = Encoding.UTF8.GetBytes(key);
                    aes.IV = iv;
                    ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                    using (MemoryStream memoryStream = new MemoryStream(buffer))
                    {
                        using (CryptoStream cryptoStream = new CryptoStream((Stream)memoryStream, decryptor, CryptoStreamMode.Read))
                        {
                            using (StreamReader streamReader = new StreamReader((Stream)cryptoStream))
                            {
                                result = streamReader.ReadToEnd();
                                //return streamReader.ReadToEnd();
                            }
                        }
                    }
                }
            }
            catch { }
            return result;
        }

        private void btn_Smart_Setting_Click(object sender, EventArgs e)
        {
            RJSettingsForm rJSettingsForm = new RJSettingsForm();
            rJSettingsForm.ShowDialog();
        }

        private void rjCheckBox1_CheckedChanged(object sender, EventArgs e)
        {
            if (rjCheckBox1.Check)
                rjPanel3.Visible = true;
            else
                rjPanel3.Visible = false;
        }
    }

    class Old_UmUsers
    {
        public long id { get; set; }
        public double sn { get; set; }
        public string username { get; set; }
        public string password { get; set; }
        public string profile { get; set; }
        public string customer { get; set; }
        public string selling_point { get; set; }
        public string print_date { get; set; } = null;
        public string id_sp { get; set; }
        public string Number_printed { get; set; }
        public string sn_user_name { get; set; } 
        public string comment { get; set; } 
        public string mk_sn { get; set; }
        public double Percentage { get; set; } = 0;
        public double price { get; set; } = 0;
        public double price_percentage { get; set; } = 0;
        public DateTime? add_date { get; set; } = null;



    }

    class Old_UmPayments
    {
        public long id { get; set; }
        public string sn_pyment_name { get; set; }
        public string sn_pyment { get; set; }
        public string username { get; set; }
        public double price { get; set; } = 0;
        public DateTime? add_date { get; set; } = null;
        public string mk_sn { get; set; }
        public string fk_sn_user_name { get; set; }
        public double percentage { get; set; } = 0;
        public double price_percentage { get; set; } =0;


    }
    class Old_UmSession
    {
        //public Old_UmSession() { }
        public long id { get; set; }
        public string sn_sess_name { get; set; }
        public string sn_sess { get; set; }
        public string username { get; set; }
        public double uptime { get; set; } = 0;
        public DateTime? Date_from { get; set; } = null;
        public DateTime? till_time { get; set; } = null;
        public double download { get; set; } = 0;
        public double upload { get; set; } 
        public string user_ip { get; set; }
        public string mac { get; set; }
        public string nas_prot { get; set; }
        public string ip_host { get; set; }
        public string mk_sn { get; set; }
        public string fk_sn_user_name { get; set; }
        public long fk_user_localDB { get; set; }


    }

}