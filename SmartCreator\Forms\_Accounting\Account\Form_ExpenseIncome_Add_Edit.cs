﻿using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms.Accounting.Accounts
{
    public partial class Form_ExpenseIncome_Add_Edit : RJChildForm
    {
        string Filter_Type = "Expense";
        Smart_DataAccess Smart_DA;
        public bool add = true;
        public bool succes = false;
        Entities.Accounts.Account account;

        public Form_ExpenseIncome_Add_Edit(string _Type= "Expense")
        {
            InitializeComponent();
            Smart_DA = new Smart_DataAccess();
            Filter_Type = _Type;
            this.Text = "بنود الايرادات";
            lblTitle.Text= "اضافة بند ايراد";

            if (Filter_Type == "Expense")
            {
                this.Text = "بنود المصروفات";
                lblTitle.Text = "اضافة بند مصروف";

            }

        }
        public Form_ExpenseIncome_Add_Edit(Entities.Accounts.Account _account,string _Type = "Expense")
        {
            InitializeComponent();
            account = _account;
            //Filter_Type = _Type;
            Smart_DA = new Smart_DataAccess();

            Filter_Type = account.AccountType;
            txt_name.Text =account.Name;

            lblTitle.Text = $"تعديل بند الايراد - {_account.Name}";

            if (Filter_Type == "Expense")
            {
                this.Text = "بنود المصروفات";
                lblTitle.Text = $"تعديل بند المصروف - {_account.Name}";
            }

            lblTitle.Text = "تعديل الحساب";
            btnSave.Text = "تعديل";
            btnSave.IconChar = FontAwesome.Sharp.IconChar.Edit;
            btnSave.BackColor = RJColors.DefaultFormBorderColor;

            txt_name.Text = account.Name;

            Set_Font();

        }
        private void Set_Font()
        {
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 14, FontStyle.Bold);
            btnSave.Font = title_font;
            lblTitle.Font = title_font;
            System.Drawing.Font lbl_font = Program.GetCustomFont(Resources.DroidSansArabic, 12, FontStyle.Regular);
            btnSave.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9.75f, FontStyle.Bold);

            rjLabel1.Font = txt_name.Font =
           lbl_font;
            this.Focus();
            
            utils utils = new utils();
            utils.Control_textSize1(this);
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
            string type = "9";
            if (Filter_Type == "Expense")
            {
                type = "3";
            }

            if (add)
            {
                try
                {
                    long foundsp = smart_DataAccess.Get_int_FromDB($"SELECT COUNT(*) FROM Account where Name='{txt_name.Text}'  and  Rb='{Global_Variable.Mk_resources.RB_SN}';");

                    if (txt_name.Text == "")
                    {
                        RJMessageBox.Show("اسم الحساب مطلوب");
                        return;
                    }

                    if (foundsp > 0)
                    {
                        RJMessageBox.Show(" اسم الحساب موجود مسبقا");
                        return;
                    }

                    string code = (smart_DataAccess.Get_BatchCards_My_Sequence("Account") + 1).ToString();
                    Entities.Accounts.Account sp = new Entities.Accounts.Account();
                    sp.Name = txt_name.Text;
                    sp.Code = code;
                    sp.AccountType = type;
                    sp.Rb = Global_Variable.Mk_resources.RB_SN;

                    lock (Smart_DataAccess.Lock_object)
                    {
                        List<string> Fields = new List<string>();
                        string[] aFields = { "Name", "Code", "AccountType", "Rb" };

                        Fields.AddRange(aFields);


                        int new_sp = smart_DataAccess.InsertTable(Fields, sp, "Account");
                        if (new_sp > 0)
                        {
                            //smart_DataAccess.InsertTable<SellingPoint>(Fields, sp);
                            RJMessageBox.Show("تمت عمليه الاضافة");
                            succes = true;
                            //int x = Smart_DA.Update_MySequence("Account", Convert.ToInt32(txt_code.Text));
                            this.Close();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch(Exception ex) { MessageBox.Show(ex.Message); }
            }
            else
            {
                Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                List<string> Fields = new List<string>();
                string[] aFields = { "Name"};
                Fields.AddRange(aFields);
                try
                {
                    lock (Smart_DataAccess.Lock_object)
                    {
                        var dataa = new Entities.Accounts.Account();
                        dataa.Id = account.Id;
                        dataa.Name = txt_name.Text;
                        dataa.AccountType = type;

                        string sqlquery = UtilsSql.GetUpdateSql<Entities.Accounts.Partner>("Account", Fields, $" where Id=@Id and   Rb='{Global_Variable.Mk_resources.RB_SN}'");
                        int r = sql_DataAccess.UpateTable(dataa, sqlquery);
                        if (r > 0)
                        {
                            //RJMessageBox.Show("تمت عمليه الاضافة");
                            succes = true;
                            this.Close();
                            return;
                        }
                        RJMessageBox.Show("خطاء");
                        return;
                    }
                }
                catch { }
            }
            //succes = true;
            //this.Close();
        }


    }
}
