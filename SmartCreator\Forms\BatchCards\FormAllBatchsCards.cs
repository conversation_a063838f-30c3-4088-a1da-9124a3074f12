﻿using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Forms.BatchCards;
using SmartCreator.Forms.Hotspot;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using System.Windows.Interop;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAllBatchsCards : RJChildForm
    {
        bool firstLoad = true;
        Sql_DataAccess Local_DB = null;
        Smart_DataAccess Smart_DB = null;
        string TableUser = "UmUser";
        string TablePyment = "UmPyment";
        string TableSession = "UmSession";

        List<SellingPoint> All_SP = new List<SellingPoint>();
        private string ServerType = "UM";
        public FormAllBatchsCards(string type = "UM")
        {
            InitializeComponent();

            utils utils = new utils();
            utils.Control_textSize1(this);

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
                DGV_detail.RightToLeft = RightToLeft.No;
            }

            if (type == "HS")
            {
                TableUser = "HSUser";
                TablePyment = "HsPyment";
                TableSession = "HsSession";
            }
            sideMenu();
            ServerType = type;
            Date_To.Value = DateTime.Now;
            Date_From.Value = DateTime.Now.AddMonths(-4);

            Local_DB = new Sql_DataAccess();
            Smart_DB = new Smart_DataAccess();
            All_SP = Smart_DB.Load<SellingPoint>($"select * from SellingPoint where (Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' )");

            this.Text = "دفعات كروت اليوزمنجر";
            if (type == "HS")
                this.Text = "دفعات كروت الهوتسبوت";

            if (UIAppearance.Language_ar == false)
            {
                //this.Text = "Batch Cards UserManager";
            }

            Set_Font();
            //utils.Control_textSize(pnlClientArea);
            //utils.dgv_textSize(dgv);
            //utils.dgv_textSize(DGV_detail);

            //if (UIAppearance.Theme == UITheme.Light)
                lbl_note.ForeColor = utils.Dgv_DarkColor;

        }

        private void Set_Font()
        {
            //dgv.AllowUserToOrderColumns = true;
            //DGV_detail.AllowUserToOrderColumns = true;

            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //DGV_detail.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 35;
            //DGV_detail.ColumnHeadersHeight = 35;
            //dgv.RowHeadersWidthSizeMode = DataGridViewRowHeadersWidthSizeMode.EnableResizing;


            //dgv.AllowUserToOrderColumns = true;
            //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = (int)(40 );
            //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);

            //DGV_detail.AllowUserToOrderColumns = true;
            //DGV_detail.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
            //DGV_detail.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //DGV_detail.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //DGV_detail.ColumnHeadersHeight = (int)(40 );

            //DGV_detail.DefaultCellStyle.Font = new Font(DGV_detail.DefaultCellStyle.Font.FontFamily, DGV_detail.DefaultCellStyle.Font.Size , DGV_detail.DefaultCellStyle.Font.Style);



            System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //DGV_detail.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //DGV_detail.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;

            System.Drawing.Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9f , FontStyle.Regular);

            rjLabel7.Font = lbl_Filter.Font = rjLabel3.Font =
            rjLabel2.Font = lbl_to.Font =
            rjLabel1.Font =
            rjLabel13.Font =
            rjLabel20.Font =
            rjLabel21.Font =
            CBox_OrderBy.Font =
            CBox_SellingPoint.Font =
            Toggle_By_Profile.Font =
            Toggle_By_SP.Font =
            lbl_note.Font =
           fnt;
            btn_apply.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 11f , FontStyle.Bold);

            btn_Filter.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9 , FontStyle.Bold);



            if (UIAppearance.Theme == UITheme.Light)
                lbl_note.ForeColor = Color.Red;

            //utils.Control_textSize(pnlClientArea);

            return;
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
              
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void timer1_Tick(object sender, EventArgs e)
        {

            timer1.Stop();
            fill_Combo_orderBy();

            CBox_OrderBy.SelectedIndex = 1;
            Get_Cbox_Profile();
            Get_SellingPoint();


            LoadDataGridviewData();
            update_header_DGV(dgv);
            firstLoad = false;
        }
        private void FormAllBatchsCards_Load(object sender, EventArgs e)
        {
            timer1.Start();
        }


        private void Get_Cbox_Profile()
        {
            try
            {
                List<UmProfile> sp = Global_Variable.UM_Profile;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("", "");
                foreach (UmProfile user in sp)
                {
                    comboSource.Add(user.Name, user.Name);

                    CBox_Profile.DataSource = new BindingSource(comboSource, null);
                    CBox_Profile.DisplayMember = "Value";
                    CBox_Profile.ValueMember = "Key";
                    CBox_Profile.SelectedIndex = 0;
                    CBox_Profile.Text = "";
                }
            }
            catch { }
        }
        private void Get_SellingPoint()
        {
            try
            {
                Smart_DataAccess da = new Smart_DataAccess();
                List<SellingPoint> sp = da.Get_SellingPoints();
                //List<SellingPoint> sp =da.Get_Any<SellingPoint>();
                Dictionary<int, string> comboSource = new Dictionary<int, string>();
                comboSource.Add(0, "");
                foreach (SellingPoint s in sp)
                    comboSource.Add((int)s.Id, s.UserName);

                CBox_SellingPoint.DataSource = new BindingSource(comboSource, null);
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";
            }
            catch { }
        }
        private void fill_Combo_orderBy()
        {
            try
            {
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("SN", "رقم الدفعة");
                comboSource.Add("UserName", "تاريخ الدفعة");

                CBox_OrderBy.DataSource = new BindingSource(comboSource, null);
                CBox_OrderBy.DisplayMember = "Value";
                CBox_OrderBy.ValueMember = "Key";
                CBox_OrderBy.SelectedIndex = 0;
            }
            catch { }

        }


        public void LoadDataGridviewData(int _BatchNumber = -1)
        {
            LocadData();
            return;
            List<CardsStatus> ResStatus = null;
            List<BatchCard> Resdeleted = null;
            List<BatchCard> ResAllbatch = null;
            string Batch_whereFilter = "Server = 0 ";
            if (ServerType == "HS")
                Batch_whereFilter = "Server = 1 ";
            string User_whereFilter = "";


            if (CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
            {
                Batch_whereFilter += " and ProfileName='" + CBox_Profile.Text + "' ";
                if (CBox_Profile.Text != "")
                    User_whereFilter += "ProfileName='" + CBox_Profile.Text + "' ";
            }
            if (CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
            {
                Batch_whereFilter += " and SpId=" + CBox_SellingPoint.SelectedValue + " ";

                if (CBox_Profile.Text != "")
                    User_whereFilter += " and SpId=" + CBox_SellingPoint.SelectedValue + "";
                else
                    User_whereFilter += "SpId=" + CBox_SellingPoint.SelectedValue + "";
            }
            if (CheckBox_byDatePrint.Check)
            {
                Batch_whereFilter += " and AddedDate >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND AddedDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "")
                    User_whereFilter += "  RegDate >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND RegDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";

            }


            if (_BatchNumber > -1)
            {
                Batch_whereFilter += " and BatchNumber=" + _BatchNumber + " ";

                if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "")
                    User_whereFilter += " and  BatchNumber=" + _BatchNumber + " ";
                else
                    User_whereFilter += " BatchNumber=" + _BatchNumber + " ";
            }

            //using (var db = Smart_dbFactory.Open())
            //{
            //    //======== all batch===========
            //    var allbatch = db.From<BatchCard>().OrderByDescending(f => f.Id).Where(Batch_whereFilter)
            //           .GroupBy(a => a.BatchNumber)
            //           .Select<BatchCard>((t1) => new
            //           {
            //               t1,
            //               Count = Sql.Sum(t1.Count),
            //               Sn_from = Sql.Min(t1.Sn_from),
            //               Sn_to = Sql.Max(t1.Sn_to),
            //           });
            //    ResAllbatch = db.SqlList<BatchCard>(allbatch);
            //}
            //using (var db = Local_dbFactory.Open())
            //{
            //    //======== status cards =================
            //    if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "")
            //    {
            //        var quser = db.From<UmUser>().Where(User_whereFilter)
            //            .GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.Status })
            //            .Select<UmUser>((t1) => new { BatchNumber = t1.BatchCardId, Status = t1.Status, Count = Sql.Count(t1.Sn_Name) });
            //        ResStatus = db.Select<CardsStatus>(quser);
            //    }
            //    else
            //    {
            //        var quser = db.From<UmUser>().GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.Status })
            //            .Select<UmUser>((t1) => new { BatchNumber = t1.BatchCardId, Status = t1.Status, Count = Sql.Count(t1.Sn_Name) });
            //        ResStatus = db.Select<CardsStatus>(quser);
            //    }
            //    //======== status by DeleteFromServer ==============
            //    if (CBox_Profile.Text != "" || CBox_SellingPoint.Text != "")
            //    {
            //        var deleted = db.From<UmUser>()
            //            .Where(User_whereFilter)
            //        .GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.DeleteFromServer })
            //        .Select<UmUser>((t1) => new
            //        {
            //            BatchNumber = t1.BatchCardId,
            //            Count_deleteServer = Sql.Count(t1.Sn_Name),
            //        });
            //        Resdeleted = db.Select<BatchCard>(deleted);
            //    }
            //    else
            //    {
            //        var deleted = db.From<UmUser>()
            //        .GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.DeleteFromServer })
            //        .Select<UmUser>((t1) => new
            //        {
            //            BatchNumber = t1.BatchCardId,
            //            Count_deleteServer = Sql.Count(t1.Sn_Name),
            //        });
            //        Resdeleted = db.Select<BatchCard>(deleted);
            //    }


            //    //================================
            //    var batch_update = (from t1 in ResAllbatch
            //                        select new BatchCard
            //                        {
            //                            //Id = t1.Id,
            //                            ProfileName = t1.ProfileName,
            //                            SpName = t1.SpName,
            //                            BatchNumber = t1.BatchNumber,
            //                            AddedDate = t1.AddedDate.Value.Date,
            //                            Count = t1.Count,
            //                            Sn_from = t1.Sn_from,
            //                            Sn_to = t1.Sn_to,
            //                            Count_waiting = (from r in ResStatus
            //                                             where t1.BatchNumber == r.BatchNumber && r.Status == 0
            //                                             select r.Count).FirstOrDefault(),
            //                            Count_active = (from r in ResStatus
            //                                            where t1.BatchNumber == r.BatchNumber && r.Status == 1
            //                                            select r.Count).FirstOrDefault(),

            //                            Count_finshed = (from r in ResStatus
            //                                             where t1.BatchNumber == r.BatchNumber && r.Status == 2
            //                                             select r.Count).FirstOrDefault(),
            //                            Count_deleteServer = (from r in Resdeleted
            //                                                  where t1.BatchNumber == r.BatchNumber && r.Count_deleteServer == 1
            //                                                  select r.Count).FirstOrDefault(),
            //                        }).ToList();

            //    if (_BatchNumber > -1)
            //    {
            //        //if (CBox_OrderBy.SelectedIndex == 0)
            //        //{
            //        //    if(CheckBox_orderBy.Check)
            //        //        batch_update = batch_update.OrderByDescending(x => x.BatchNumber).ToList();
            //        //    else
            //        //        batch_update = batch_update.OrderBy(x => x.BatchNumber).ToList();

            //        //}
            //        //else if (CBox_OrderBy.SelectedIndex == 1)
            //        //   {
            //        //    if (CheckBox_orderBy.Check)
            //        //        batch_update = batch_update.OrderByDescending(x => x.AddedDate).ToList();
            //        //    else
            //        //        batch_update = batch_update.OrderBy(x => x.AddedDate).ToList();
            //        //}

            //        DGV_detail.DataSource = batch_update;

            //    }
            //    else
            //    {
            //        dgv.DataSource = batch_update;
            //        DGV_detail.DataSource = null;
            //        update_header_DGV();

            //    }

            //}

        }

        private void LocadData2()
        {
            try
            {
                try { dgv.DataSource = null; DGV_detail.DataSource = null; } catch { }
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                string Batch_whereFilter = " AddedDate>='" + Date_From.Value.Date.ToString("yyyy-MM-dd") + "' and AddedDate<='" + Date_To.Value.Date.AddDays(1).ToString("yyyy-MM-dd") + $"' and ( Rb='{rb}' or Rb='{rb_sn}' )  ";

                if (ServerType == "HS")
                    Batch_whereFilter += " and Server = 1 ";
                else if (ServerType == "UM")
                    Batch_whereFilter += " and Server = 0 ";

                //string Batch_whereFilter = " where Server=0 ";
                //string User_whereFilter  = " where Server=0 ";
                //string GroupBy = " GROUP BY BatchNumber ; ";

                //if (CBox_Profile.SelectedIndex != -1 && CBox_Profile.Text != "")
                //{
                //    Batch_whereFilter += " and ProfileName='" + CBox_Profile.Text + "' ";
                //    User_whereFilter  += " and ProfileName='" + CBox_Profile.Text + "' ";
                //}
                //if (CBox_SellingPoint.SelectedIndex != -1 && CBox_SellingPoint.Text != "")
                //{
                //    Batch_whereFilter += " and SpId=" + CBox_SellingPoint.SelectedValue + " ";
                //    User_whereFilter  += " and SpId=" + CBox_SellingPoint.SelectedValue + "";
                //}
                //if (CheckBox_byDatePrint.Check)
                //{
                //    Batch_whereFilter += " and AddedDate >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND AddedDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                //    User_whereFilter  += " and RegDate >='"   + Date_From.Value.ToString("yyyy-MM-dd") + "' AND RegDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                //}

                List<BatchCard> ResAllbatch = null;
                List<CardsStatus> ResStatus = null;
                List<BatchCard> Resdeleted = null;
                List<BatchCard> FoundRB = null;

                //using (var db = Smart_dbFactory.Open())
                //{
                //var allbatch = db.From<BatchCard>().Where(f => f.Server == 0 && f.AddedDate >= Date_From.Value.Date && f.AddedDate <= Date_To.Value.Date.AddDays(1))
                //var allbatch = db.From<BatchCard>().Where(Batch_whereFilter)
                //    .OrderByDescending(f => f.AddedDate)
                //       .GroupBy(a => new { a.BatchNumber })
                //       .Select<BatchCard>((t1) => new
                //       {
                //           t1,
                //           Count = Sql.Sum(t1.Count),
                //           Sn_from = Sql.Min(t1.Sn_from),
                //           Sn_to = Sql.Max(t1.Sn_to),
                //       });

                //ResAllbatch = db.SqlList<BatchCard>(allbatch);

                string q = $"select *, sum(Count) as Count,min(Sn_from) as Sn_from ,max(Sn_to) as Sn_to ,BatchType from BatchCard where {Batch_whereFilter}  group by BatchNumber  order by AddedDate desc";
                ResAllbatch = Smart_DB.Load<BatchCard>(q);
                //}
                //using (var db = Local_dbFactory.Open())
                //{
                if (ServerType == "UM")
                    //ResStatus = db.SqlList<CardsStatus>("SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId,Status ");
                    ResStatus = Local_DB.Load<CardsStatus>("SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId,Status ");
                //ResStatus = Local_DB.GetListAnyDB<CardsStatus>("SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId,Status ");
                else if (ServerType == "HS")
                    ResStatus = Local_DB.Load<CardsStatus>("SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId,Status ");
                //ResStatus = db.SqlList<CardsStatus>("SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId,Status ");

                if (ServerType == "UM")
                {
                    Resdeleted = Local_DB.Load<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");
                    //Resdeleted = db.SqlList<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");
                }
                else if (ServerType == "HS")
                    Resdeleted = Local_DB.Load<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");
                //Resdeleted = db.SqlList<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");


                if (ServerType == "UM")
                {
                    FoundRB = Local_DB.Load<BatchCard>("SELECT Count(Sn_Name) as Count_FoundServer ,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId ");
                    //Resdeleted = db.SqlList<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM UmUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");
                }
                else if (ServerType == "HS")
                    Resdeleted = Local_DB.Load<BatchCard>("SELECT Count(Sn_Name) as Count_FoundServer ,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=0 " + "" + "  group by BatchCardId ");
                //Resdeleted = db.SqlList<BatchCard>("SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM HSUser WHERE DeleteFromServer=1 " + "" + "  group by BatchCardId ");


                var batch_update = (from t1 in ResAllbatch
                                    select new BatchCard
                                    {
                                        //Id = t1.Id,
                                        ProfileName = t1.ProfileName,
                                        SpName = (from r in All_SP
                                                  where t1.SpCode == r.Code
                                                  select r.UserName.ToString()).FirstOrDefault(),

                                        BatchNumber = t1.BatchNumber,
                                        AddedDate = t1.AddedDate.Value.Date,
                                        Count = t1.Count,
                                        Sn_from = t1.Sn_from,
                                        Sn_to = t1.Sn_to,

                                        Count_waiting = (from r in ResStatus
                                                         where t1.BatchNumber == r.BatchNumber && r.Status == 0
                                                         select r.Count).FirstOrDefault(),

                                        Count_active = (from r in ResStatus
                                                        where t1.BatchNumber == r.BatchNumber && r.Status == 1
                                                        select r.Count).FirstOrDefault(),

                                        Count_finshed = (from r in ResStatus
                                                         where t1.BatchNumber == r.BatchNumber && r.Status == 2
                                                         select r.Count).FirstOrDefault(),

                                        Count_deleteServer = (from r in Resdeleted
                                                              where t1.BatchNumber == r.BatchNumber
                                                              select r.Count_deleteServer).FirstOrDefault(),

                                        //Count_FoundServer = (from r in FoundRB
                                        //                      where t1.BatchNumber == r.BatchNumber
                                        //                      select r.Count_FoundServer).FirstOrDefault(),


                                        Count_FoundServer = t1.Count - (from r in Resdeleted
                                                                        where t1.BatchNumber == r.BatchNumber
                                                                        select r.Count_deleteServer).FirstOrDefault(),

                                        BatchType = t1.BatchType,
                                    }).ToList();

                //dgv.DataSource = batch_update;

                if (CBox_OrderBy.SelectedIndex == 0)
                {
                    if (CheckBox_orderBy.Check)
                        batch_update = batch_update.OrderByDescending(x => x.BatchNumber).ToList();
                    else
                        batch_update = batch_update.OrderBy(x => x.BatchNumber).ToList();

                }
                else if (CBox_OrderBy.SelectedIndex == 1)
                {
                    if (CheckBox_orderBy.Check)
                        batch_update = batch_update.OrderByDescending(x => x.AddedDate).ToList();
                    else
                        batch_update = batch_update.OrderBy(x => x.AddedDate).ToList();
                }
                dgv.DataSource = batch_update;

                update_header_DGV(dgv);
                DGV_detail.DataSource = null;

                //}
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        private DataTable dt_Batch()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("الدفعة", typeof(string));
            dt.Columns.Add("التاريخ", typeof(string));
            dt.Columns.Add("العدد", typeof(double));
            dt.Columns.Add("الموجود في الروتر", typeof(double));

            dt.Columns.Add("عدد الانتظار", typeof(double));
            dt.Columns.Add("النشطين", typeof(double));
            dt.Columns.Add("المنتهيه", typeof(double));
            dt.Columns.Add("المحذوف من الروتر", typeof(double));

            return dt;
        }
        private void LocadData()
        {
            try
            {
                try { dgv.DataSource = null; DGV_detail.DataSource = null; } catch { }
                string rb = Global_Variable.Mk_resources.RB_code;
                string rb_sn = Global_Variable.Mk_resources.RB_SN;
                string Batch_whereFilter = $"WHERE BatchCard.AddedDate>='{Date_From.Value.Date.ToString("yyyy-MM-dd")}' and BatchCard.AddedDate<='{Date_To.Value.Date.AddDays(1).ToString("yyyy-MM-dd")}' and ( BatchCard.Rb='{rb}' or BatchCard.Rb='{rb_sn}') and ( NumberPrintCard.Rb='{rb}' or NumberPrintCard.Rb='{rb_sn}' ) ";
                
                if (ServerType == "UM")
                    Batch_whereFilter += " and BatchCard.Server = 0 ";
                if (ServerType == "HS")
                    Batch_whereFilter += " and BatchCard.Server = 1 ";


                string Query_Get_Batch = $@"select BatchCard.*,	BatchCard.BatchNumber, 
	                                        sum(NumberPrintCard.Count) as Count , 
	                                        
	                                        BatchCard.BatchType 
	                                        from BatchCard INNER JOIN NumberPrintCard ON BatchCard.BatchNumber=NumberPrintCard.BatchNumber 
	                                        {Batch_whereFilter} 
                                           group by NumberPrintCard.BatchNumber  order by BatchCard.AddedDate desc";

                List<BatchCard> ResAllbatch = Smart_DB.Load<BatchCard>(Query_Get_Batch);
                string qur = $"SELECT Count(Sn_Name) as Count ,Status,BatchCardId as BatchNumber FROM {TableUser} WHERE DeleteFromServer=0  group by BatchCardId,Status ";
                List<CardsStatus> ResStatus = Local_DB.Load<CardsStatus>(qur);

                //string q2 = $"SELECT Count(Sn_Name) as Count_deleteServer ,BatchCardId as BatchNumber FROM {TableUser} WHERE DeleteFromServer=1   group by BatchCardId";
                //List<BatchCard> Resdeleted = Local_DB.Load<BatchCard>(q2);

                //string q23 = $"SELECT Count(Sn_Name) as Count_FoundServer ,BatchCardId as BatchNumber FROM {TableUser} WHERE DeleteFromServer=0   group by BatchCardId ";
                // List<BatchCard> FoundRB = Local_DB.Load<BatchCard>(q23);

                DataTable dt = dt_Batch();
                var batch_update = from t1 in ResAllbatch
                                   select dt.LoadDataRow(new object[]
                                   {
                                        t1.BatchNumber,
                                        t1.AddedDate.Value.Date.ToString("yyyy-MM-dd"),
                                        t1.Count,

                                        //(from r in FoundRB
                                        //                where t1.BatchNumber == r.BatchNumber
                                        //                select r.Count_FoundServer).FirstOrDefault(),

                                        (from r in ResStatus
                                                where t1.BatchNumber == r.BatchNumber
                                                select r).Sum(x => x.Count),

                                        (from r in ResStatus
                                                    where t1.BatchNumber == r.BatchNumber && r.Status == 0
                                                    select r.Count).FirstOrDefault(),

                                         (from r in ResStatus
                                                    where t1.BatchNumber == r.BatchNumber && r.Status == 1
                                                    select r.Count).FirstOrDefault(),

                                        (from r in ResStatus
                                                    where t1.BatchNumber == r.BatchNumber && r.Status == 2
                                                    select r.Count).FirstOrDefault(),

                                        //(from r in Resdeleted
                                        //                where t1.BatchNumber == r.BatchNumber
                                        //                select r.Count_deleteServer).FirstOrDefault(),

                                   t1.Count - (from r in ResStatus
                                                where t1.BatchNumber == r.BatchNumber
                                                select r).Sum(x => x.Count),
                                    
                                   }, false);
                if (batch_update.Count().ToString() != "0")
                {

                }

                dgv.DataSource = dt;
                //update_header_DGV(dgv);
                DGV_detail.DataSource = null;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void Sub_LocadData(int _BatchNumber = -1)
        {
            try
            {
                DGV_detail.DataSource = null;
            }
            catch { }
            try
            {
                string GroupBy = "Group By NumberPrint ";
                string GroupByUser = "Group By NumberPrint,Status";
                if (Toggle_By_Profile.Checked || Toggle_By_SP.Checked)
                {
                    GroupBy = " group by ";
                    if (Toggle_By_SP.Checked)
                        GroupBy = GroupBy + " SpCode ";
                    if (Toggle_By_Profile.Checked)
                    {
                        if (Toggle_By_SP.Checked)
                            GroupBy = GroupBy + " ,ProfileName ";
                        else
                            GroupBy = GroupBy + " ProfileName ";
                    }

                    GroupByUser = $"{GroupBy} ,Status ";
                }


                string Server = " and Server = 0 ";
                string Batch_whereFilter = $"WHERE BatchNumber={_BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ";
                if (ServerType == "HS")
                    Server = " and Server = 1 ";
                Batch_whereFilter = Batch_whereFilter + Server;

                List<NumberPrintCard> ResAllumberPrint = null;
                string QueryNumberPrint = $"SELECT *,sum(Count) as Count FROM NumberPrintCard {Batch_whereFilter}  {GroupBy}";


                ResAllumberPrint = Smart_DB.Load<NumberPrintCard>(QueryNumberPrint);


                //string CountUserStatus = $"SELECT ProfileName,SpCode, BatchCardId,NumberPrint,Status,count(Sn_Name) as Count FROM {TableUser} WHERE    NumberPrint IS NOT NULL and BatchCardId={_BatchNumber} {GroupByUser}";
                string CountUserStatus = $"SELECT ProfileName,SpCode, BatchCardId as BatchNumber,NumberPrint,Status,count(Sn_Name) as Count FROM {TableUser} WHERE DeleteFromServer=0 and NumberPrint IS NOT NULL and BatchCardId={_BatchNumber} {GroupByUser}";
                //string CountDeleteFromServer = $"SELECT BatchCardId,NumberPrint,Status,count(Sn_Name) as Count FROM {TableUser} WHERE DeleteFromServer=0 and BatchCardId={_BatchNumber} Group By NumberPrint,Status";
                //DataTable dt = Local_DB.RunSqlCommandAsDatatable(CountUserStatus);


                List<CardsStatus> ResStatus = Local_DB.Load<CardsStatus>(CountUserStatus);

                //if (dt.Rows.Count <= 0)
                //    return;

                if (Toggle_By_Profile.Checked == false && Toggle_By_SP.Checked == false)
                {
                    var batch_update = (from t1 in ResAllumberPrint
                                            //join state in dt.AsEnumerable() on t1.NumberPrint equals state.Field<Int64>("NumberPrint")
                                        select new NumberPrintCard
                                        {
                                            NumberPrint = t1.NumberPrint,
                                            BatchType = t1.BatchType,
                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,

                                            SpName = (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from r in ResStatus
                                                             where t1.NumberPrint == r.NumberPrint && r.Status == 0
                                                             select r.Count).FirstOrDefault(),

                                            Count_active = (from r in ResStatus
                                                            where t1.NumberPrint == r.NumberPrint && r.Status == 1
                                                            select r.Count).FirstOrDefault(),

                                            Count_finshed = (from r in ResStatus
                                                             where t1.NumberPrint == r.NumberPrint && r.Status == 2
                                                             select r.Count).FirstOrDefault(),

                                            Count_FoundServer = (from r in ResStatus
                                                                 where t1.NumberPrint == r.NumberPrint
                                                                 select r).Sum(x => x.Count),

                                            Count_deleteServer =  t1.Count - (from r in ResStatus
                                                                    where t1.NumberPrint == r.NumberPrint
                                                                              select r).Sum(x=>x.Count),

                                            //Count_waiting = (int)(from r in dt.AsEnumerable() where t1.NumberPrint == r.Field<Int64>("NumberPrint") && r.Field<Int64>("Status") == 0 select r.Field<Int64>("Count")).FirstOrDefault() ,
                                            //Count_active = (int)(from r in dt.AsEnumerable() where t1.NumberPrint == r.Field<Int64>("NumberPrint") && r.Field<Int64>("Status") == 1 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_finshed = (int)(from r in dt.AsEnumerable() where t1.NumberPrint == r.Field<Int64>("NumberPrint") && r.Field<Int64>("Status") == 2 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_FoundServer = (int)dt.AsEnumerable().Where(x => x.Field<Int64>("NumberPrint") == t1.NumberPrint).Sum(x => x.Field<Int64>("Count")),
                                            //Count_deleteServer = t1.Count - (int)dt.AsEnumerable().Where(x => x.Field<Int64>("NumberPrint") == t1.NumberPrint).Sum(x => x.Field<Int64>("Count")),


                                        }).ToList();
                    DGV_detail.DataSource = batch_update;
                    try
                    {
                        update_header_DGV_detail();
                        DGV_detail.Columns["NumberPrint"].DisplayIndex = 0;
                        DGV_detail.Columns["ProfileName"].DisplayIndex = 1;
                        DGV_detail.Columns["Count"].DisplayIndex = 2;
                    }
                    catch { }

                }
                else if (Toggle_By_Profile.Checked && Toggle_By_SP.Checked == false)
                {
                    var batch_update = (from t1 in ResAllumberPrint
                                        select new NumberPrintCard
                                        {
                                            NumberPrint = t1.NumberPrint,
                                            BatchType = t1.BatchType,
                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,
                                            SpName = (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from r in ResStatus
                                                             where t1.ProfileName == r.ProfileName && r.Status == 0
                                                             select r.Count).FirstOrDefault(),

                                            Count_active = (from r in ResStatus
                                                            where t1.ProfileName == r.ProfileName && r.Status == 1
                                                            select r.Count).FirstOrDefault(),

                                            Count_finshed = (from r in ResStatus
                                                             where t1.ProfileName == r.ProfileName && r.Status == 2
                                                             select r.Count).FirstOrDefault(),

                                            Count_FoundServer = (from r in ResStatus
                                                                 where t1.ProfileName == r.ProfileName
                                                                 select r).Sum(x => x.Count),



                                            Count_deleteServer = t1.Count - (from r in ResStatus
                                                                             where t1.ProfileName == r.ProfileName
                                                                             select r).Sum(x => x.Count),
                                            //Count_waiting = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && r.Field<Int64>("Status") == 0 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_active = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && r.Field<Int64>("Status") == 1 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_finshed = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && r.Field<Int64>("Status") == 2 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_FoundServer = (int)dt.AsEnumerable().Where(x => x.Field<string>("ProfileName") == t1.ProfileName).Sum(x => x.Field<Int64>("Count")),
                                            //Count_deleteServer = t1.Count - (int)dt.AsEnumerable().Where(x => x.Field<string>("ProfileName") == t1.ProfileName).Sum(x => x.Field<Int64>("Count")),



                                        }).ToList();
                    DGV_detail.DataSource = batch_update;
                    try
                    {
                        update_header_DGV_detail();
                        DGV_detail.Columns["NumberPrint"].Visible = false;
                        DGV_detail.Columns["SpName"].Visible = false;
                        DGV_detail.Columns["ProfileName"].DisplayIndex = 0;
                        DGV_detail.Columns["Count"].DisplayIndex = 2;
                    }
                    catch { }

                }
                else if (Toggle_By_Profile.Checked == false && Toggle_By_SP.Checked == true)
                {
                    var batch_update = (from t1 in ResAllumberPrint
                                        select new NumberPrintCard
                                        {
                                            NumberPrint = t1.NumberPrint,
                                            BatchType = t1.BatchType,
                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,
                                            SpName = (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from r in ResStatus
                                                             where t1.SpCode == r.SpCode && r.Status == 0
                                                             select r.Count).FirstOrDefault(),

                                            Count_active = (from r in ResStatus
                                                            where t1.SpCode == r.SpCode && r.Status == 1
                                                            select r.Count).FirstOrDefault(),

                                            Count_finshed = (from r in ResStatus
                                                             where t1.SpCode == r.SpCode && r.Status == 2
                                                             select r.Count).FirstOrDefault(),

                                            Count_FoundServer = (from r in ResStatus
                                                                 where t1.SpCode == r.SpCode
                                                                 select r).Sum(x => x.Count),

                                            Count_deleteServer = t1.Count - (from r in ResStatus
                                                                             where t1.SpCode == r.SpCode
                                                                             select r).Sum(x => x.Count),

                                            //Count_waiting = (int)(from r in dt.AsEnumerable() where t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 0 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_active = (int)(from r in dt.AsEnumerable() where t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 1 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_finshed = (int)(from r in dt.AsEnumerable() where t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 2 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_FoundServer = (int)dt.AsEnumerable().Where(x => x.Field<string>("SpCode") == t1.SpCode).Sum(x => x.Field<Int64>("Count")),
                                            //Count_deleteServer = t1.Count - (int)dt.AsEnumerable().Where(x => x.Field<string>("SpCode") == t1.SpCode).Sum(x => x.Field<Int64>("Count")),



                                        }).ToList();
                    DGV_detail.DataSource = batch_update;
                    try
                    {
                        update_header_DGV_detail();
                        DGV_detail.Columns["ProfileName"].Visible = false;
                        DGV_detail.Columns["NumberPrint"].Visible = false;
                        DGV_detail.Columns["SpName"].DisplayIndex = 0;
                        DGV_detail.Columns["Count"].DisplayIndex = 1;
                    }
                    catch { }


                }
                else if (Toggle_By_Profile.Checked == true && Toggle_By_SP.Checked == true)
                {
                    var batch_update = (from t1 in ResAllumberPrint
                                        select new NumberPrintCard
                                        {
                                            NumberPrint = t1.NumberPrint,
                                            BatchType = t1.BatchType,
                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,
                                            SpName = (from r in All_SP where t1.SpCode == r.Code select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from r in ResStatus
                                                             where t1.SpCode == r.SpCode && t1.ProfileName == r.ProfileName && r.Status == 0
                                                             select r.Count).FirstOrDefault(),

                                            Count_active = (from r in ResStatus
                                                            where t1.SpCode == r.SpCode && t1.ProfileName == r.ProfileName && r.Status == 1
                                                            select r.Count).FirstOrDefault(),

                                            Count_finshed = (from r in ResStatus
                                                             where t1.SpCode == r.SpCode && t1.ProfileName == r.ProfileName && r.Status == 2
                                                             select r.Count).FirstOrDefault(),

                                            Count_FoundServer = (from r in ResStatus
                                                                 where t1.SpCode == r.SpCode && t1.ProfileName == r.ProfileName
                                                                 select r).Sum(x => x.Count),


                                            Count_deleteServer = t1.Count - (from r in ResStatus
                                                                             where t1.SpCode == r.SpCode && t1.ProfileName == r.ProfileName
                                                                             select r).Sum(x => x.Count),

                                            //Count_waiting = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 0 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_active = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 1 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_finshed = (int)(from r in dt.AsEnumerable() where t1.ProfileName == r.Field<string>("ProfileName") && t1.SpCode == r.Field<string>("SpCode") && r.Field<Int64>("Status") == 2 select r.Field<Int64>("Count")).FirstOrDefault(),
                                            //Count_FoundServer = (int)dt.AsEnumerable().Where(x => x.Field<string>("SpCode") == t1.SpCode && x.Field<string>("ProfileName") == t1.ProfileName).Sum(x => x.Field<Int64>("Count")),
                                            //Count_deleteServer = t1.Count - (int)dt.AsEnumerable().Where(x => x.Field<string>("SpCode") == t1.SpCode && x.Field<string>("ProfileName") == t1.ProfileName).Sum(x => x.Field<Int64>("Count")),



                                        }).ToList();
                    DGV_detail.DataSource = batch_update;
                    try
                    {
                        update_header_DGV_detail();
                        DGV_detail.Columns["SpName"].DisplayIndex = 0;
                        DGV_detail.Columns["ProfileName"].DisplayIndex = 1;
                        DGV_detail.Columns["Count"].DisplayIndex = 2;
                    }
                    catch { }

                }
                //update_header_DGV_detail();

                try
                {
                    //DGV_detail.Columns["ProfileName"].Visible = true;
                    //DGV_detail.Columns["SpName"].Visible = false;

                    //if (Toggle_By_Profile.Checked)
                    //    DGV_detail.Columns["ProfileName"].Visible = true;


                    //DGV_detail.Columns["NumberPrint"].Visible = false;
                    ////DGV_detail.Columns["Count_deleteServer"].Width = 150;
                    ////DGV_detail.Columns["BatchType_Str"].Width = 150;
                    ////DGV_detail.Columns["Count_FoundServer"].Width = 150;


                    //if (Toggle_By_Profile.Checked || Toggle_By_SP.Checked)
                    //    DGV_detail.Columns["AddedDate"].Visible = false;

                    //DGV_detail.Columns["ProfileName"].DisplayIndex = 1;
                    //DGV_detail.Columns["SpName"].DisplayIndex = 0;


                }
                catch { }

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }
        private void Sub_LocadData2(int _BatchNumber = -1)
        {
            try
            {
                if (Toggle_By_SP.Checked)
                {
                    Sub_LocadData_by_SP(_BatchNumber);
                    return;
                }

                List<BatchCard> ResAllbatch = null;
                long Sn_from = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_from"].Value.ToString());
                long Sn_to = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_to"].Value.ToString());
                int BatchType = (int)dgv.SelectedRows[0].Cells["BatchType"].Value;

                if (Toggle_By_Profile.Checked)
                {
                    ResAllbatch = Smart_DB.Load<BatchCard>($"select *,count(*) as 'count' , min(Sn_from) as Sn_from , max(Sn_to) as Sn_to from BatchCard where BatchNumber={_BatchNumber} and Rb='{Global_Variable.Mk_resources.RB_code}' group by ProfileName ");

                }
                else
                    ResAllbatch = Smart_DB.Load<BatchCard>($"select * from BatchCard where BatchNumber={_BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) ");

                //ResAllbatch = db.Select<BatchCard>(f => f.BatchNumber == _BatchNumber && f.Rb == Global_Variable.Mk_resources.RB_code);
                //}
                //using (var db = Local_dbFactory.Open())
                //{
                string SN_Filter = "where SN>=" + Sn_from + " and SN<=" + Sn_to + " ";

                List<UmUser> UMuserBatch = new List<UmUser>();
                List<HSUser> HSuserBatch = new List<HSUser>();

                if (ServerType == "UM")
                    UMuserBatch = Local_DB.Load<UmUser>("SELECT * FROM UmUser " + SN_Filter + "  ");
                //UMuserBatch = db.SqlList<UmUser>("SELECT * FROM UmUser " + SN_Filter + "  ");
                else if (ServerType == "HS")
                    HSuserBatch = Local_DB.Load<HSUser>("SELECT * FROM HSUser " + SN_Filter + "  ");
                //HSuserBatch = db.SqlList<HSUser>("SELECT * FROM HSUser " + SN_Filter + "  ");

                if (ServerType == "UM")
                {
                    var batch_update = (from t1 in ResAllbatch
                                        select new BatchCard
                                        {
                                            BatchType = BatchType,
                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,

                                            SpName = (from r in All_SP
                                                      where t1.SpCode == r.Code
                                                      select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from u in UMuserBatch
                                                             where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 0 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                             select u).Count(),

                                            Count_active = (from u in UMuserBatch
                                                            where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                            select u).Count(),

                                            Count_finshed = (from u in UMuserBatch
                                                             where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 2 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                             select u).Count(),

                                            Count_deleteServer = (from u in UMuserBatch
                                                                  where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                                  select u).Count(),
                                            Count_FoundServer = t1.Count - (from u in UMuserBatch
                                                                            where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                                            select u).Count(),



                                        }).ToList();
                    DGV_detail.DataSource = batch_update;

                }
                else if (ServerType == "HS")
                {
                    var batch_update = (from t1 in ResAllbatch
                                        select new BatchCard
                                        {

                                            ProfileName = t1.ProfileName,
                                            BatchNumber = t1.BatchNumber,
                                            AddedDate = t1.AddedDate.Value,
                                            Count = t1.Count,
                                            Sn_from = t1.Sn_from,
                                            Sn_to = t1.Sn_to,

                                            SpName = (from r in All_SP
                                                      where t1.SpCode == r.Code
                                                      select r.UserName.ToString()).FirstOrDefault(),

                                            Count_waiting = (from u in HSuserBatch
                                                             where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 0 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                             select u).Count(),

                                            Count_active = (from u in HSuserBatch
                                                            where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                            select u).Count(),

                                            Count_finshed = (from u in HSuserBatch
                                                             where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 0 && u.Status == 2 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                             select u).Count(),

                                            Count_deleteServer = (from u in HSuserBatch
                                                                  where t1.ProfileName == u.ProfileName && u.DeleteFromServer == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                                  select u).Count(),
                                        }).ToList();
                    DGV_detail.DataSource = batch_update;

                }


                try
                {


                    DGV_detail.Columns["ProfileName"].Visible = true;
                    DGV_detail.Columns["SpName"].Visible = true;
                    DGV_detail.Columns["Str_Name"].Visible = false;
                    DGV_detail.Columns["BatchType_Str"].Width = 150;
                    DGV_detail.Columns["AddedDate"].Visible = false;


                    if (Toggle_By_Profile.Checked)
                        DGV_detail.Columns["SpName"].Visible = false;
                    DGV_detail.Columns["BatchNumber"].Visible = false;
                    DGV_detail.Columns["Count_deleteServer"].Width = 150;
                    if (!Toggle_By_Profile.Checked && !Toggle_By_SP.Checked)
                    {
                        DGV_detail.Columns["AddedDate"].Visible = true;
                        DGV_detail.Columns["AddedDate"].Width = 150;
                    }
                    DGV_detail.Columns["ProfileName"].DisplayIndex = 0;
                    DGV_detail.Columns["SpName"].DisplayIndex = 1;
                }
                catch { }
                update_header_DGV_detail();

                //}
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }



        private void Sub_LocadData_by_SP(int _BatchNumber = -1)
        {
            List<BatchCard> ResAllbatch = null;
            long Sn_from = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_from"].Value.ToString());
            long Sn_to = (long)Convert.ToDouble(dgv.SelectedRows[0].Cells["Sn_to"].Value.ToString());


            //using (var db = Smart_dbFactory.Open())
            //{
            if (Toggle_By_Profile.Checked)
            {
                ResAllbatch = Smart_DB.Load<BatchCard>($"select *,count(*) as 'count' , min(Sn_from) as Sn_from , max(Sn_to) as Sn_to from BatchCard where BatchNumber={_BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) and SpCode is not null group by ProfileName,SpCode ");

                //var allbatch = db.From<BatchCard>().Where(f => f.BatchNumber == _BatchNumber && f.SpCode != null)
                //   .GroupBy(a => new { a.ProfileName, a.SpCode })
                //   .Select<BatchCard>((t1) => new
                //   {
                //       t1,
                //       Count = Sql.Sum(t1.Count),
                //       Sn_from = Sql.Min(t1.Sn_from),
                //       Sn_to = Sql.Max(t1.Sn_to),
                //   });
                //ResAllbatch = db.SqlList<BatchCard>(allbatch);
            }
            else
            {
                ResAllbatch = Smart_DB.Load<BatchCard>($"select *,count(*) as 'count' , min(Sn_from) as Sn_from , max(Sn_to) as Sn_to from BatchCard where BatchNumber={_BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}' ) and SpCode is not null group by SpCode ");

                //var allbatch = db.From<BatchCard>().Where(f => f.BatchNumber == _BatchNumber && f.SpCode != null)
                //   .GroupBy(a => new { a.SpCode })
                //   .Select<BatchCard>((t1) => new
                //   {
                //       t1,
                //       Count = Sql.Sum(t1.Count),
                //       Sn_from = Sql.Min(t1.Sn_from),
                //       Sn_to = Sql.Max(t1.Sn_to),
                //   });
                //ResAllbatch = db.Select<BatchCard>(allbatch);

            }
            //}
            //using (var db = Local_dbFactory.Open())
            //{
            string SN_Filter = "where SN>=" + Sn_from + " and SN<=" + Sn_to + " ";



            var userBatch = Local_DB.Load<UmUser>("SELECT * FROM UmUser " + SN_Filter + "  ");
            //var userBatch = db.SqlList<UmUser>("SELECT * FROM UmUser " + SN_Filter + "  ");


            //var userBatch = db.Select<UmUser>().Where(a => a.BatchCardId == _BatchNumber && a.SpId != null);

            var batch_update = (from t1 in ResAllbatch
                                select new BatchCard
                                {

                                    ProfileName = t1.ProfileName,
                                    BatchNumber = t1.BatchNumber,
                                    AddedDate = t1.AddedDate.Value,
                                    Count = t1.Count,
                                    Sn_from = t1.Sn_from,
                                    Sn_to = t1.Sn_to,

                                    SpName = (from r in All_SP
                                              where t1.SpCode == r.Code
                                              select r.UserName.ToString()).FirstOrDefault(),

                                    Count_waiting = (from u in userBatch
                                                     where t1.SpCode == u.SpCode && u.DeleteFromServer == 0 && u.Status == 0 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                     select u).Count(),

                                    Count_active = (from u in userBatch
                                                    where t1.SpCode == u.SpCode && u.DeleteFromServer == 0 && u.Status == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                    select u).Count(),

                                    Count_finshed = (from u in userBatch
                                                     where t1.SpCode == u.SpCode && u.DeleteFromServer == 0 && u.Status == 2 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                     select u).Count(),

                                    Count_deleteServer = (from u in userBatch
                                                          where t1.SpCode == u.SpCode && u.DeleteFromServer == 1 && u.SN >= t1.Sn_from && u.SN <= t1.Sn_to
                                                          select u).Count(),
                                }).ToList();

            DGV_detail.DataSource = batch_update;

            DGV_detail.Columns["ProfileName"].Visible = false;
            DGV_detail.Columns["SpName"].Visible = true;

            if (Toggle_By_Profile.Checked)
                DGV_detail.Columns["ProfileName"].Visible = true;


            DGV_detail.Columns["BatchNumber"].Visible = false;
            //DGV_detail.Columns["Count_deleteServer"].Width = 150;
            //DGV_detail.Columns["BatchType_Str"].Width = 150;
            //DGV_detail.Columns["Count_FoundServer"].Width = 150;


            if (Toggle_By_Profile.Checked || Toggle_By_SP.Checked)
                DGV_detail.Columns["AddedDate"].Visible = false;

            DGV_detail.Columns["ProfileName"].DisplayIndex = 1;
            DGV_detail.Columns["SpName"].DisplayIndex = 0;

            update_header_DGV_detail();
            //update_header_DGV(DGV_detail);
            //}
        }



        private void update_header_DGV(RJDataGridView _dgv)
        {
            return;
            if (_dgv == null)
                return;
            try
            {
                try { _dgv.Columns["ProfileName"].Visible = false; } catch { }
                try { _dgv.Columns["SpName"].Visible = false; } catch { }
                try { _dgv.Columns["Str_Name"].Visible = false; } catch { }

                if (CBox_Profile.Text != "")
                    try { _dgv.Columns["ProfileName"].Visible = true; } catch { }
                if (CBox_SellingPoint.Text != "")
                    try { _dgv.Columns["SpName"].Visible = true; } catch { }

                try { _dgv.Columns["Count_deleteServer"].Width = 150; } catch { }
                try { _dgv.Columns["Count_waiting"].Width = 150; } catch { }
                try { _dgv.Columns["BatchType_Str"].Width = 150; } catch { }
                try { _dgv.Columns["Count_FoundServer"].Width = 150; } catch { }
                try { _dgv.Columns["Count_finshed"].Width = 120; } catch { }
                try { _dgv.Columns["BatchType"].Visible = false; } catch { }

                try { _dgv.Columns["BatchNumber"].DisplayIndex = 0; } catch { }
                try { _dgv.Columns["AddedDate"].DisplayIndex = 1; } catch { }
                try { _dgv.Columns["Count"].DisplayIndex = 2; } catch { }
                try { _dgv.Columns["Sn_from"].DisplayIndex = 3; } catch { }
                try { _dgv.Columns["Sn_to"].DisplayIndex = 4; } catch { }

                try { _dgv.Columns["Count_deleteServer"].DisplayIndex = 5; } catch { }
                try { _dgv.Columns["Count_FoundServer"].DisplayIndex = 6; } catch { }

                try { _dgv.Columns["Count_waiting"].DisplayIndex = 7; } catch { }
                try { _dgv.Columns["Count_active"].DisplayIndex = 8; } catch { }
                try { _dgv.Columns["Count_finshed"].DisplayIndex = 9; } catch { }
            }
            catch { }
        }
        private void update_header_DGV_detail()
        {
            if (DGV_detail == null)
                return;
            try
            {
                try { DGV_detail.Columns["BatchNumber"].Visible = false; } catch { }
                try { DGV_detail.Columns["Str_Name"].Visible = false; } catch { }
                try { DGV_detail.Columns["BatchType"].Visible = false; } catch { }

                try { DGV_detail.Columns["Count_deleteServer"].Width = 150; } catch { }
                try { DGV_detail.Columns["BatchType_Str"].Width = 150; } catch { }
                try { DGV_detail.Columns["Count_FoundServer"].Width = 150; } catch { }
                try { DGV_detail.Columns["Count_waiting"].Width = 150; } catch { }
                try { DGV_detail.Columns["Count_deleteServer"].Width = 150; } catch { }
                try { DGV_detail.Columns["Count_finshed"].Width = 120; } catch { }


                DGV_detail.Columns["NumberPrint"].DisplayIndex = 0;
                DGV_detail.Columns["ProfileName"].DisplayIndex = 1;
                DGV_detail.Columns["Count"].DisplayIndex = 2;
                DGV_detail.Columns["AddedDate"].DisplayIndex = 3;

                DGV_detail.Columns["Sn_from"].DisplayIndex = 4;
                DGV_detail.Columns["Sn_to"].DisplayIndex = 5;

                DGV_detail.Columns["Count_FoundServer"].DisplayIndex = 6;

                DGV_detail.Columns["Count_waiting"].DisplayIndex = 7;
                DGV_detail.Columns["Count_active"].DisplayIndex = 8;
                DGV_detail.Columns["Count_finshed"].DisplayIndex = 9;
                DGV_detail.Columns["Count_deleteServer"].DisplayIndex = 10;
                DGV_detail.Columns["SpName"].DisplayIndex = 11;
                DGV_detail.Columns["BatchType"].DisplayIndex = 12;
                

            }
            catch { }
        }

        private void Toggle_By_SP_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            if (dgv.SelectedRows.Count > 0)
                Sub_LocadData(Convert.ToInt32(dgv.SelectedRows[0].Cells["الدفعة"].Value.ToString()));
        }
        private void Toggle_By_Profile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (dgv.SelectedRows.Count > 0)
                Sub_LocadData(Convert.ToInt32(dgv.SelectedRows[0].Cells["الدفعة"].Value.ToString()));
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
        }

        public void SaveFromState()
        {
            //try
            //{
            //    Dgv_State_list = new Dgv_Header_Proprties();
            //    dvalue = new Dictionary<int, Dgv_Header_Values>();
            //    foreach (DataGridViewColumn column in dgv.Columns)
            //    {
            //        Dgv_Header_Values dgv_Header_Values = new Dgv_Header_Values();
            //        dgv_Header_Values.Visable = column.Visible;
            //        dgv_Header_Values.HeaderText = column.HeaderText;
            //        dgv_Header_Values.Name = column.Name;
            //        dgv_Header_Values.DisplayIndex = column.DisplayIndex;
            //        dgv_Header_Values.Index = column.Index;
            //        dgv_Header_Values.Width = column.Width;

            //        dvalue[column.Index] = dgv_Header_Values;
            //    }
            //    Dgv_State_list.items = dvalue;

            //    string formSetting = JsonConvert.SerializeObject(Dgv_State_list);
            //    SqlDataAccess.Setting_SaveState_Forms_Variables("DgvUserManagerPrcess", "SaveControlState", formSetting);
            //}
            //catch { }


        }

        private void dgv_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
                Sub_LocadData(Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["الدفعة"].Value.ToString()));


            //Sub_LocadData(Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["BatchNumber"].Value.ToString()));

            //if (e.RowIndex > -1) //not click  on header
            //{
            //    int id = Convert.ToInt32( dgv.Rows[e.RowIndex].Cells["BatchNumber"].Value.ToString());
            //    using (var db = dbFactory.Open())
            //    {
            //        //======== all batch===========
            //        var allbatch = db.From<BatchCard>()
            //            .OrderByDescending(f => f.Id)
            //            .Where(t1 =>  t1.Server == 0 && t1.BatchNumber == id )
            //            .GroupBy<UmUser>((t1) => new {  t1.BatchCardId , t1.Status, t1.ProfileName })
            //            .Select<BatchCard>((t1) => new
            //            {
            //                t1,
            //                Count = Sql.Sum(t1.Count),
            //                Sn_from = Sql.Min(t1.Sn_from),
            //                Sn_to = Sql.Max(t1.Sn_to),
            //            });
            //        var ResAllbatch = db.SqlList<BatchCard>(allbatch);

            //        //======== status cards =================
            //        var quser = db.From<UmUser>()
            //            .GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.Status,t1.ProfileName })
            //            .Select<UmUser>((t1) => new
            //            {
            //                BatchNumber = t1.BatchCardId,
            //                Status = t1.Status,
            //                Count = Sql.Count(t1.Id),
            //                ProfileName = t1.ProfileName,


            //            });
            //        ResStatus = db.Select<CardsStatus>(quser);

            //        //======== status by DeleteFromServer ==============
            //        var deleted = db.From<UmUser>()
            //            .GroupBy<UmUser>((t1) => new { t1.BatchCardId, t1.DeleteFromServer,t1.ProfileName })
            //            .Select<UmUser>((t1) => new
            //            {
            //                BatchNumber = t1.BatchCardId,
            //                Count_deleteServer = Sql.Count(t1.Id),
            //                ProfileName=t1.ProfileName,
            //            });
            //        Resdeleted = db.Select<BatchCard>(deleted);


            //        //================================
            //        var batch_update = (from t1 in ResAllbatch
            //                            select new BatchCard
            //                            {
            //                                //Id = t1.Id,
            //                                //BatchNumber = t1.BatchNumber,
            //                                ProfileName = t1.ProfileName,
            //                                SpName = t1.SpName,
            //                                AddedDate = t1.AddedDate.Value.Date,
            //                                Count = t1.Count,
            //                                Sn_from = t1.Sn_from,
            //                                Sn_to = t1.Sn_to,
            //                                Count_waiting = (from r in ResStatus
            //                                                 where t1.BatchNumber == r.BatchNumber && r.Status == 0
            //                                                 select r.Count).FirstNonDefault(),
            //                                Count_active = (from r in ResStatus
            //                                                where t1.BatchNumber == r.BatchNumber && r.Status == 1
            //                                                select r.Count).FirstNonDefault(),

            //                                Count_finshed = (from r in ResStatus
            //                                                 where t1.BatchNumber == r.BatchNumber && r.Status == 2
            //                                                 select r.Count).FirstNonDefault(),
            //                                Count_deleteServer = (from r in Resdeleted
            //                                                      where t1.BatchNumber == r.BatchNumber && r.Count_deleteServer == 1
            //                                                      select r.Count).FirstNonDefault(),
            //                            }).ToList();

            //        DGV_detail.DataSource = batch_update;
            //        try
            //        {
            //            DGV_detail.Columns["BatchNumber"].Visible = false;
            //            DGV_detail.Columns["ProfileName"].Visible = true;
            //            DGV_detail.Columns["ProfileName"].DisplayIndex = 0;
            //            DGV_detail.Columns["AddedDate"].DisplayIndex = 1;
            //            DGV_detail.Columns["Count"].DisplayIndex = 2;
            //            DGV_detail.Columns["Sn_from"].DisplayIndex = 3;
            //            DGV_detail.Columns["Sn_to"].DisplayIndex = 4;

            //            DGV_detail.Columns["Count_waiting"].DisplayIndex = 5;
            //            DGV_detail.Columns["Count_active"].DisplayIndex = 6;
            //            DGV_detail.Columns["Count_finshed"].DisplayIndex = 7;
            //            DGV_detail.Columns["Count_deleteServer"].DisplayIndex = 8;
            //        }
            //        catch { }

            //    }

            //}
        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            sideMenu();
        }
        void sideMenu()
        {
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);

            }
            else
            {
                rjPanel_back_side.Width = 230;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 13, panel1.Location.Y - 1);
                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Height-(panel1.Height - dgv.Location.Y));

            }

        }

        private void FormAllBatchsCards_SizeChanged(object sender, EventArgs e)
        {
            //if (this.Width > 1300)
            //{
            //    dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            //    DGV_detail.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            //}
            //else
            //{
            //    dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            //    DGV_detail.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;
            //    update_header_DGV(dgv);
            //    update_header_DGV_detail();
            //}

        }

        private void btn_apply_Click(object sender, EventArgs e)
        {
            LoadDataGridviewData();
            //sideMenu();
        }

        private void CBox_OrderBy_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            LoadDataGridviewData();
        }

        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            int Server = 0;
            if (ServerType == "HS")
                Server = 1;
            try
            {
                int id = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["الدفعة"].Value.ToString());
                BatchCard batchCard = Smart_DB.Get_Batch_byBatchNumber_And_Server(id, Server).First();

                if (ServerType == "UM")
                {
                    FormAllCardsUserManager frm = new FormAllCardsUserManager(batchCard);
                    frm.ShowDialog();
                }
                else if (ServerType == "HS")
                {
                    FormAllCardsHotspot frm = new FormAllCardsHotspot(batchCard);
                    frm.ShowDialog();
                }
            }
            catch { }
        }

        private void DGV_detail_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            int Server = 0;
            if (ServerType == "HS")
                Server = 1;

            string ProfileName = null;
            string SellingPoint = null;

            if (Toggle_By_Profile.Checked)
                try { ProfileName = (DGV_detail.Rows[e.RowIndex].Cells["ProfileName"].Value.ToString()); } catch { }
            if (Toggle_By_SP.Checked)
                try { SellingPoint = (DGV_detail.Rows[e.RowIndex].Cells["SpCode"].Value.ToString()); } catch { }
            try
            {
                int id = Convert.ToInt32(dgv.CurrentRow.Cells["الدفعة"].Value.ToString());
                int id_NumberPrint = Convert.ToInt32(DGV_detail.Rows[e.RowIndex].Cells["NumberPrint"].Value.ToString());

                BatchCard batchCard = Smart_DB.Get_Batch_byBatchNumber_And_Server(id, Server).First();
                NumberPrintCard numberPrintCard = Smart_DB.Get_NumberPrintCard_byNumberPrint_And_Server(id_NumberPrint, Server).First();

                if (Toggle_By_SP.Checked || Toggle_By_Profile.Checked)
                    numberPrintCard = null;


                //=========================
                     //batchCard = null;
                //==========================
                if (ServerType == "UM")
                {

                    FormAllCardsUserManager frm = new FormAllCardsUserManager(batchCard, numberPrintCard, SellingPoint, ProfileName);
                    frm.ShowDialog();
                }
                else if (ServerType == "HS")
                {
                    FormAllCardsHotspot frm = new FormAllCardsHotspot(batchCard, numberPrintCard, SellingPoint, ProfileName);
                    frm.ShowDialog();
                }
            }
            catch { }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("هل متاكد من حذف الدفعات المحدده مع طبعاتهن", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف الدفعات المحددة");
            try
            {
                HashSet<BatchCard> dbBatch= new HashSet<BatchCard>();
                HashSet<NumberPrintCard> dbNumberPrint= new HashSet<NumberPrintCard>();


                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    BatchCard batchCard = new BatchCard();
                    batchCard.BatchNumber = Convert.ToInt32( row.Cells["الدفعة"].Value.ToString());
                    string qu = $"delete from BatchCard where BatchNumber={batchCard.BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'); ";
                    int delete = Smart_DB.Execute<BatchCard>(qu, batchCard);


                    if (delete > 0)
                    {
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", $" تم حذف الدفعة رقم {batchCard.BatchNumber} ");
                        //RJMessageBox.Show($"تم حذف الدفعة رقم {batchCard.BatchNumber}");

                        string qun = $"delete from NumberPrintCard where BatchNumber={batchCard.BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'); ";
                        int deleteNumber = Smart_DB.Execute<BatchCard>(qun, batchCard);
                        
                        //RJMessageBox.Show($"تم حذف الدفعة رقم {batchCard.BatchNumber}");
                        //RJMessageBox.Show($"تم حذف طبعات الدفعة  رقم {batchCard.BatchNumber}  وعددهم {deleteNumber } طبعات");


                        //string Server = " and Server = 0 ";
                        //string Batch_whereFilter = $"WHERE BatchNumber={batchCard.BatchNumber} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}') ";

                        //if (ServerType == "HS")
                        //    Server = " and Server = 1 ";
                        //Batch_whereFilter = Batch_whereFilter + Server;

                        //List<NumberPrintCard> ResAllumberPrint = new List<NumberPrintCard>();
                        //string QueryNumberPrint = $"SELECT * FROM NumberPrintCard {Batch_whereFilter}";
                        //ResAllumberPrint = Smart_DB.Load<NumberPrintCard>(QueryNumberPrint);

                        //foreach (NumberPrintCard numberPrintCard in ResAllumberPrint)
                        //{

                        //    //dbNumberPrint.Add(numberPrintCard);
                        //    //string qun = $"delete from NumberPrintCard where NumberPrint={numberPrintCard.NumberPrint} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'); ";
                        //    //int deleteNumber = Smart_DB.Execute<NumberPrintCard>(qun, numberPrintCard);

                        //    //qun = $"delete from BatchCard where BatchNumber={numberPrintCard.NumberPrint} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'); ";
                        //    //deleteNumber = Smart_DB.Execute<BatchCard>(qun, numberPrintCard);


                        //    //Global_Variable.Update_Um_StatusBar(false, true, 0, "", $" تم حذف الطبعة رقم {batchCard.BatchNumber} ");

                        //    string qun = $"delete from NumberPrintCard where BatchNumber={numberPrintCard.NumberPrint} and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'); ";
                        //    int deleteNumber = Smart_DB.Execute<BatchCard>(qun, numberPrintCard);

                        //}
                    }

                }

                LoadDataGridviewData();
                RJMessageBox.Show($"تم حذف الدفعات المحددة");



                //if (dbBatch.Count > 0)
                //{
                //    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                //    smart_DataAccess.Execute<BatchCard>("",dbBatch);

                //    lock (Smart_DataAccess.Lock_object)
                //    {
                //        int delete = Smart_DB.Execute<BatchCard>("",dbBatch);
                //        RJMessageBox.Show($"تم حذف {delete} دفعه");
                //    }
                //}

                //if (dbNumberPrint.Count > 0)
                //{
                //    Smart_DataAccess smart_DataAccess = new Smart_DataAccess();
                //    smart_DataAccess.Execute<NumberPrintCard>("delete form NumberPrintCard where ", dbNumberPrint);

                //    lock (Smart_DataAccess.Lock_object)
                //    {
                //        int delete = Smart_DB.Execute<BatchCard>("", dbBatch);
                //        RJMessageBox.Show($"تم حذف {delete} دفعه");
                //    }
                //}
                //Global_Variable.Update_Um_StatusBar(false, true, 0, "", " حدث خطا عن تنفيذ العملية");
            }
            catch (Exception ex) {   }

        }

        private void btnAddMain_Click(object sender, EventArgs e)
        {
            Form_Add_BatchCards frm= new Form_Add_BatchCards();
            frm.ShowDialog();
        }
    }
}
