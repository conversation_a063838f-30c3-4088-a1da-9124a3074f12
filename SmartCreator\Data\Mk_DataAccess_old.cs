﻿using CefSharp.DevTools.Accessibility;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
//using tik4net;

namespace SmartCreator.Data
{
    public class Mk_DataAccess_old
    {
        public MK mk;
        public Mk_DataAccess_old()
        {
            try
            {
                MK.True_IP = false;
                MK.True_UserPass = false;

                mk = new MK(Global_Variable.Server_IP);
                if (MK.True_IP)
                {
                    if (!(mk.Login(Global_Variable.Server_Username, Global_Variable.Server_Password)))
                    {
                        RJMessageBox.Show("خطاء في تسجيل الدخول للميكروتك");
                        mk.Close();
                    }
                    else
                    {
                        //virson = GetResource();
                        //getVirion();
                    }
                }
                else
                {
                    RJMessageBox.Show("خطاء في الاتصال بالسيرفر");
                    //return;

                }
                //virson = GetResource();
                //getVirion();
            }
            catch (Exception ex) { RJMessageBox.Show("erorr DataAccess = " + ex.Message.ToString()); }
        }

        public string AddUserScript2(string name)
        {
            string code = Properties.Settings.Default.script_add;
            //Remove_And_Add_script_check();
            string tt = "";
            //======================================= 
            string QuryString2 = "";
            QuryString2 = "";
            //================ADD Script=====================          
            try
            {
                mk.Send(code);
                mk.Send("=name=SmartScript");
                mk.Send("=source=" + name);
                mk.Send("disabled=no", true);

                foreach (string h in mk.Read())
                {
                    QuryString2 += h;
                }
                tt = RuntScrip2();

                //RemoveUserScript_smart();
            }
            catch { }
            //MessageBox.Show(QuryString2);   
            mk.Close();
            //------------------------------------------------------ 
            return tt;
        }
        public string RuntScrip2()
        {
            mk.Send("/system/script/print");
            mk.Send("?name=SmartScript");
            mk.Send("=.proplist=.id", true);

            string QuryString2 = "";
            foreach (string h in mk.Read())
            {
                QuryString2 += h;
            }
            string id = "";
            //MessageBox.Show(QuryString2); 
            //==========================================
            QuryString2 = QuryString2.TrimStart(new char[] { '!' });
            QuryString2 = QuryString2.TrimStart(new char[] { 'r' });
            QuryString2 = QuryString2.TrimStart(new char[] { 'e' });
            QuryString2 = QuryString2.TrimStart(new char[] { '=' });
            QuryString2 = QuryString2.TrimStart(new char[] { '.' });
            string[] split = QuryString2.Split(new string[] { "!re=." }, StringSplitOptions.None);
            //  !re=.id*D!done

            for (int i = 0; i < split.Length; i++)
            {
                //==============Get id=====================================
                int pFrom = split[i].IndexOf("id=") + "id=".Length;
                int pTo = split[i].LastIndexOf("!done");
                String matchId = split[i].Substring(pFrom, pTo - pFrom);
                id = matchId;
                // MessageBox.Show(id);
            }
            //+++++++++++++++++++++++++++++++++++++++++++++++++

            mk.Send("/system/script/run");
            mk.Send("=.id=" + id);
            mk.Send("disabled=no", true);


            string QuryString = "";
            foreach (string h in mk.Read())
            {
                QuryString += h;
            }
            //MessageBox.Show(QuryString + "rrrrun");

            //mk.Close();


            return QuryString;
        }
        public string RuntScrip2(string id)
        {

            mk.Send("/system/script/run");
            mk.Send("=.id=" + id);
            mk.Send("disabled=no", true);

            string QuryString = "";
            foreach (string h in mk.Read())
            {
                QuryString += h;
            }
            mk.Close();
            return QuryString;
        }
        public int Get_Active_count()
        {
            int count = 0;
            mk.Send("/ip/hotspot/active/print");
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_usermanager_count()
        {
            int count = 0;
            string command = "/tool/user-man/user/print";
            if (Global_Variable.Mk_resources.version >= 7)
                command = "/user-man/user/print";
            mk.Send(command);
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_pyment_count()
        {
            int count = 0;
            string command = "/tool/user-man/payment/print";
            if (Global_Variable.Mk_resources.version >= 7)
                command = "/user-man/payment/print";
            mk.Send(command);
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_Hotsopot_count()
        {
            int count = 0;
            mk.Send("/ip/hotspot/user/print");
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);

            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_usermanager_session_count()
        {
            int count = 0;
            string command = "/tool/user-man/session/print";
            if (Global_Variable.Mk_resources.version >= 7)
                command = "/user-man/session/print";
            mk.Send(command);
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32( split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_neighbor_count()
        {
            int count = 0;
            mk.Send("/ip/neighbor/print");
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }
        public int Get_Host_count()
        {
            int count = 0;
            mk.Send("/ip/hotspot/host/print");
            mk.Send("=count-only=");
            mk.Send("disabled=no", true);
            foreach (string h in mk.Read())
            {
                try
                {
                    if (h == "!done")
                        continue;
                    //MessageBox.Show(h);
                    string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
                    count = Convert.ToInt32(split[2]);
                }
                catch { }
            }
            return count;
        }

        public Dictionary<string, string> run_import_file(string name, string server = "usermanager")
        {
            int lastUserCount = 0;
            if (server == "usermanager")
            lastUserCount= Get_Hotsopot_count();
            else
                lastUserCount = Get_usermanager_count();

            Dictionary<string, string> res = new Dictionary<string, string>();
            res["status"] = "false";
            res["result"] = "";
            res["SmartErorrCards"] = "";
            res["SmartErorrProfile"] = "";

            string code = Properties.Settings.Default.script_add;
            string tt = "not success";
            string QuryString2 = "";
            try
            {
                //if (code == "")
                //    return tt;
                //mk.Send(code);
                mk.Send("/import");
                mk.Send("=file-name=" + name, true);
                //mk.Send("disabled=no", true);
                foreach (string h in mk.Read())
                {
                    QuryString2 += h;
                }
                tt = QuryString2;
                res["status"] = "true";
                res["result"] = QuryString2;

                if (QuryString2 == "!done")
                {
                    int AfterUserCount = 0;
                    if(server== "usermanager")
                        AfterUserCount=Get_usermanager_count();
                    else
                        AfterUserCount = Get_Hotsopot_count();

                    if (AfterUserCount == lastUserCount)
                    {
                        res["status"] = "false";
                        RJMessageBox.Show("خطأ قم باعادة العمليه مره اخرى");
                        return res;
                    }
                    else if (AfterUserCount > lastUserCount)
                    {
                        res["status"] = "true";
                        //Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                        //Dictionary<string, string> resultScript = mk_DataAccess.Get_environment_scripts();
                        //res["SmartErorrCards"] = resultScript["SmartErorrCards"];
                        //res["SmartErorrProfile"] = resultScript["SmartErorrProfile"];
                    }
                    //========= get envairometn  report add
                }
                //mk.Close();

            }
            catch { }
            return res;
        }
        public bool RunImport_File(string name)
        {
            bool status = false;

            string code = Properties.Settings.Default.script_add;
            string QuryString2 = "";
            try
            {
                //if (code == "")
                //    return tt;
                //mk.Send(code);

                mk.Send("/import");
                mk.Send("=file-name=" + name, true);
                //mk.Send("disabled=no", true);
                foreach (string h in mk.Read())
                {
                    QuryString2 += h;
                }
                if (QuryString2 == "!done")
                {
                    status = true;
                }
                //mk.Close();

            }
            catch { }
            return status;
        }

        public void Add_Payment_FromSn_ToSn(double From,double To)
        {
            //int count = 0;
            List<List<string>> Result = new List<List<string>>();

            for (double user = From; user <= To; user++)
            {
                string idx = "13CFBE";
                //string idx = Convert.ToInt64(user).ToString("X");
                mk.Send("/tool/user-manager/payment/print");
                //mk.Send("=.id="+idx,true);
                mk.Send("?=.id=*" + idx);
                mk.Send("disabled=no", true);
                //Result.Add(mk.Read());
                //List<string> res= mk.Read();

                foreach (string h in mk.Read())
                {
                    MessageBox.Show(h);
                    //if (h == "!done")
                    //    status = "true";
                    //else if (h != "!done")
                    //    userEror.Add(hexValue);
                }
            }

            //foreach (string h in mk.Read())
            //{
            //    try
            //    {
            //        if (h == "!done")
            //            continue;
            //        //MessageBox.Show(h);
            //        string[] split = h.Split(new string[] { "=" }, StringSplitOptions.None);
            //        count = Convert.ToInt32(split[2]);
            //    }
            //    catch { }
            //}
            //return count;
            return;
        }

    }
}
