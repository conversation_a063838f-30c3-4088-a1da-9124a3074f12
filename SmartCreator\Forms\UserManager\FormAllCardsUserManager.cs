﻿using Dapper;
using DevComponents.DotNetBar.Controls;
using Newtonsoft.Json;
using SmartCreator.Data;
using SmartCreator.Entities;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;
using SmartCreator.ViewModels;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Rebar;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAllCardsUserManager : RJChildForm
    {
        int PW;
        bool Hided;
        Dictionary<int, Dgv_Header_Values> dvalue;
        //OrmLiteConnectionFactory dbFactory = null;

        private bool firstLoad = true;
        private bool Dgv_Click_ToChangIndex = false;
        Dgv_Header_Proprties Dgv_State_list = null;
        private string fillter_type = "From_Server";
        //private string fillter = "From_Server";
        DataGridViewCell ActiveCell = null;

        Smart_DataAccess Smart_DA;
        Sql_DataAccess Local_DA;
        private int PageSize = 10000;
        private int currentPageindex = 1;
        private int totalPages = 0;
        private int totalRows = 0;

        private BatchCard Filter_BatchCards = null;
        private NumberPrintCard Filter_NumberPrintCard = null;
        private string Filter_SpCode = null;
        private string Filter_ProfileName = null;
        Color myColor = ColorTranslator.FromHtml("#55ff55");
        public FormAllCardsUserManager(string _fillter = "From_Server")
        {

            fillter_type = _fillter;
            InitializeComponent();

            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }
            InitializeFirstLoad();

        }
        public FormAllCardsUserManager(BatchCard batchCards = null, NumberPrintCard numberPrintCard = null,string filter_SpCode=null,string filter_ProfileName=null)
        {

            
            InitializeComponent();
            if (UIAppearance.DGV_RTL == false)
            {
                dgv.RightToLeft = RightToLeft.No;
            }

            Filter_BatchCards = batchCards;
            Filter_NumberPrintCard = numberPrintCard;
            Filter_ProfileName= filter_ProfileName;
            Filter_SpCode = filter_SpCode;
            fillter_type = "From_RB_Archive";
            InitializeFirstLoad();


        }

        private void InitializeFirstLoad()
        {

            utils utils = new utils();
            utils.Control_textSize1(this);


            if (fillter_type == "From_RB_Archive")
            {
                this.Text = "كروت الروتر والارشيف - يوزمنجر";
                this.lblCaption.Text = " كروت  الروتر مع الارشيف - يوزمنجر";
            }
            else if (fillter_type == "From_Finsh_Cards")
            {
                this.Text = "الكروت المنتهية - يوزمنجر";
                this.lblCaption.Text = "الكروت المنتهية - يوزمنجر";
                btn_RemoveFinsh_Validaty.Visible = true;
            }
            else if (fillter_type == "From_Server")
            {
                this.Text = "جميع الكروت من الروتر - يوزمنجر";
                this.lblCaption.Text = "جميع الكروت من الروتر - يوزمنجر";
            }

            Smart_DA = new Smart_DataAccess();
            Local_DA = new Sql_DataAccess();
            if (UIAppearance.Theme == UITheme.Dark)
            {
                pnl_side_Count_Session.Customizable = false;
                //pnl_side_datePrint.Customizable = false;
            }
            if (UIAppearance.Language_ar == false)
            {
                this.Text = "All Cards From Router";
                if (fillter_type == "From_RB_Archive")
                    this.Text = "All Cards From Router And Archive";
                if (fillter_type == "From_Finsh_Cards")
                    this.Text = "All Finsh Cards";

                this.dgv.RightToLeft = RightToLeft.No;
                this.dmAll_Cards.RightToLeft = RightToLeft.No;
            }
            else
            {
                if (fillter_type == "From_RB_Archive")
                    this.Text = "كروت الروتر والارشيف - يوزمنجر";
                else if (fillter_type == "From_Finsh_Cards")
                    this.Text = "الكروت المنتهية - يوزمنجر";
                else if (fillter_type == "From_Server")
                    this.Text = "جميع الكروت من الروتر - يوزمنجر";
            }

            if (fillter_type == "From_Finsh_Cards")
            {
                //pnl_side_Finsh_Cards.Visible = true;
                //fpnl_showArchive.Visible = true;
            }
            else
                pnl_side_Count_Session.Visible = true;

            if (fillter_type == "From_RB_Archive")
            {
                fpnl_showArchive.Visible = true;
                fpnl_showServer.Visible = true;
            }
            sideMenu();

            fill_Combo_orderBy();
            CBox_OrderBy.Text = "الرقم التسلسلي";
            CBox_SearchBy.SelectedIndex = 0;
            //CBox_OrderBy.SelectedIndex = 0;
            CBox_SN_Compar.SelectedIndex = 0;
            CBox_PageCount.SelectedIndex = 6;

            Set_Font();

        }
        private void Set_Font()
        {
            try
            {
                Date_From.Value = DateTime.Now.AddDays(-30); Date_To.Value = DateTime.Now;
                Date_From.dateText.TextAlign = Date_To.dateText.TextAlign = CBox_OrderBy.label.TextAlign = CBox_SearchBy.label.TextAlign = CBox_SellingPoint.label.TextAlign = ContentAlignment.MiddleLeft;

                Date_From.RightToLeft =
                Date_To.RightToLeft =
                txtAllCountRows.RightToLeft =
                txtCurrentPageindex.RightToLeft =
                txtTotalPages.RightToLeft =
                txt_search.RightToLeft =
                txt_SN_End.RightToLeft =
                txt_SN_Start.RightToLeft =
                CBox_Profile.RightToLeft =
                CBox_Batch.RightToLeft =
                CBox_PageCount.RightToLeft =
                RightToLeft.No;

                Date_From.dateText.TextAlign = Date_To.dateText.TextAlign = CBox_PageCount.label.TextAlign = ContentAlignment.MiddleRight;
                CBox_Batch.label.TextAlign = CBox_SN_Compar.label.TextAlign = ContentAlignment.MiddleRight;


                //return;
                //===============================
                dgv.AllowUserToOrderColumns = true;
                //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f, FontStyle.Regular);
                //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.ColumnHeadersHeight = 40;

                //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);

                //btn_Filter.Font = CustomFonts.Get_Custom_Font("DroidKufi_Regular", 10f, true);
                btn_Filter.Font = btnRefresh_DB.Font = btn_RemoveFinsh_Validaty.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 8f, FontStyle.Bold);
                rjButton1.Font = btn_apply.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 9f, FontStyle.Bold);

                Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);
                //Font fnt = Program.GetCustomFont(Resources.Cairo_Regular, 8f, FontStyle.Regular);
                //Font fnt = CustomFonts.Get_Custom_Font("Cairo_Regular", 9f, false);

                rjLabel9.Font = rjLabel18.Font = rjLabel7.Font =
                lbl_Filter.Font = rjLabel14.Font = rjLabel15.Font = rjLabel3.Font =
                rjLabel2.Font = rjLabel17.Font = lbl_to.Font =
                 rjLabel6.Font = rjLabel5.Font = rjLabel8.Font =
                rjLabel16.Font = rjLabel12.Font =

                CheckBox_orderBy.Font = CheckBox_byDatePrint.Font = CheckBox_SN.Font =
                ToggleButton_ByCountProfile.Font = ToggleButton_ByCountSession.Font = 
                ToggleButton_Show_Archive.Font =
                ToggleButton_Show_onlyServer.Font =

                 fnt;

                rjLabel1.Font = rjLabel13.Font = rjLabel4.Font = Program.GetCustomFont(Resources.DroidSansArabic, 9f, FontStyle.Regular);

                CBox_OrderBy.Font = CBox_SearchBy.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8f, FontStyle.Regular);
                //    fnt;
                //btn_Batch_Cards_Title.Font = fnt;
                //btn_Finsh_Cards_Title.Font = fnt;
                //btn_Sessions_Cards_Title.Font = fnt;
                //btn_All_Cards_From_RB_Archive_Title.Font = fnt;

                lbl_note.Font = Program.GetCustomFont(Resources.DroidSansArabic, 8f, FontStyle.Regular);
                lbl_note.ForeColor = utils.Dgv_DarkColor;
                if (UIAppearance.Theme == UITheme.Dark)
                    lbl_note.ForeColor = utils.Dgv_DarkColor;

                utils.Control_textSize(pnlClientArea);
                utils.dgv_textSize(dgv);
                utils.item_Contrlol_textSize(dmAll_Cards);
                utils.tollstrip_textSize(View_Hide_toolStripMenuItem);
                utils.tollstrip_textSize(ExportText_ToolStripMenuItem);
                //utils.tollstrip_textSize(View_Hide_toolStripMenuItem);
                return;


                Control_Loop(pnlClientArea);
            }
            catch { }
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                //dgv.AllowUserToOrderColumns = true;
                //dgv.ColumnHeadersDefaultCellStyle.Font = Program.GetCustomFont(Resources.DroidKufi_Regular, 9f , FontStyle.Regular);
                //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                //dgv.ColumnHeadersHeight = 40;

                //dgv.DefaultCellStyle.Font = new Font(dgv.DefaultCellStyle.Font.FontFamily, dgv.DefaultCellStyle.Font.Size , dgv.DefaultCellStyle.Font.Style);
                

                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJTextBox) || C.GetType() != typeof(RJComboBox))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }

        private void Init_dgv_to_Default()
        {
            try
            {
                int countdgv=dgv.Columns.Count-1;
                foreach (DataGridViewColumn column in dgv.Columns)
                {

                    column.Visible = false;
                    //column.DisplayIndex = countdgv;
                    //countdgv = countdgv - 1;
                }

                //dgv.Columns["Sn"].Visible = true;
                dgv.Columns["Str_Status"].Visible = true;
                dgv.Columns["Str_Status"].DisplayIndex = 0;
                Status_ToolStripMenuItem.Checked = true;

                dgv.Columns["UserName"].Visible = true;
                dgv.Columns["UserName"].DisplayIndex = 1;
                dgv.Columns["UserName"].Width =utils.Control_Mesur_DPI( 120);
                UserName_ToolStripMenuItem.Checked = true;

                dgv.Columns["ProfileName"].Visible = true;
                dgv.Columns["ProfileName"].DisplayIndex = 2;
                dgv.Columns["ProfileName"].Width = utils.Control_Mesur_DPI(120);
                Profile_ToolStripMenuItem.Checked = true;

                if (fillter_type != "From_Finsh_Cards")
                {
                    dgv.Columns["Password"].Visible = true;
                    dgv.Columns["Password"].DisplayIndex = 3;
                    //Password_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_UptimeLimit"].Visible = true;
                    dgv.Columns["Str_UptimeLimit"].DisplayIndex = 4;
                    dgv.Columns["Str_UptimeLimit"].Width = utils.Control_Mesur_DPI(150);
                    //Str_UptimeLimit_ToolStripMenuItem.Checked = true;

                    //dgv.Columns["Str_TransferLimit"].Visible = true;
                    dgv.Columns["Str_TransferLimit"].DisplayIndex = 5;
                    dgv.Columns["Str_TransferLimit"].Width = utils.Control_Mesur_DPI(150);
                    //Str_TransferLimit_ToolStripMenuItem.Checked = true;
                }

                dgv.Columns["Str_UptimeUsed"].Visible = true;
                dgv.Columns["Str_UptimeUsed"].DisplayIndex = 6;
                dgv.Columns["Str_UptimeUsed"].Width = utils.Control_Mesur_DPI(150);
                Str_UptimeUsed_ToolStripMenuItem.Checked = true;

                //dgv.Columns["Str_DownloadUsed"].Visible = true;
                dgv.Columns["Str_DownloadUsed"].Width = utils.Control_Mesur_DPI(150);

                //dgv.Columns["Str_UploadUsed"].Visible = true;
                dgv.Columns["Str_UploadUsed"].Width = utils.Control_Mesur_DPI(150);

                dgv.Columns["Str_Up_Down"].Visible = true;
                dgv.Columns["Str_Up_Down"].DisplayIndex = 7;
                dgv.Columns["Str_Up_Down"].Width = utils.Control_Mesur_DPI(190);
                Str_Up_Down_ToolStripMenuItem.Checked = true;

                //dgv.Columns["MoneyTotal"].Visible = true;

                dgv.Columns["Str_ProfileTimeLeft"].Visible = true;
                dgv.Columns["Str_ProfileTimeLeft"].DisplayIndex = 8;
                dgv.Columns["Str_ProfileTimeLeft"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTimeLeft_ToolStripMenuItem.Checked = true;

                dgv.Columns["Str_ProfileTransferLeft"].Visible = true;
                dgv.Columns["Str_ProfileTransferLeft"].DisplayIndex = 9;
                dgv.Columns["Str_ProfileTransferLeft"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTransferLeft_ToolStripMenuItem.Checked = true;

                dgv.Columns["Str_ProfileTillTime"].Visible = true;
                dgv.Columns["Str_ProfileTillTime"].DisplayIndex = 10;
                dgv.Columns["Str_ProfileTillTime"].Width = utils.Control_Mesur_DPI(150);
                Str_ProfileTillTime_ToolStripMenuItem.Checked = true;

                dgv.Columns["LastSynDb"].Visible = false;
                dgv.Columns["LastSynDb"].DisplayIndex = 11;
                dgv.Columns["LastSynDb"].Width = utils.Control_Mesur_DPI(150);


                dgv.Columns["SpName"].DisplayIndex = 12;
                dgv.Columns["Comment"].DisplayIndex = 13;
                dgv.Columns["Comment"].Width = utils.Control_Mesur_DPI(150);

                //dgv.Columns["CountProfile"].Visible = true;
                //try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
                //try { dgv.Columns["Status "].Visible = false; } catch { }
                //try { dgv.Columns["Disabled "].Visible = false; } catch { }

                try { dgv.Columns["CountProfile"].Visible = true; } catch { }
                try { dgv.Columns["CountProfile"].DisplayIndex = 14; } catch { }
                try { dgv.Columns["CountProfile"].Width = utils.Control_Mesur_DPI(130); } catch { }

                try { dgv.Columns["CountSession"].Visible = true; } catch { }
                try { dgv.Columns["CountSession"].Width = utils.Control_Mesur_DPI(150); } catch { }
                try { dgv.Columns["CountSession"].DisplayIndex = 15; } catch { }

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message + "\n Init_dgv_to_Default"); }


        }
        private void Get_Cbox_Profile()
        {
           
            try
            {
                var umProfil = new List<UmProfile>();
                umProfil.Add(new UmProfile { Id = 0, Name = "" });
                umProfil.AddRange(Global_Variable.UM_Profile);

                CBox_Profile.DataSource = umProfil;
                CBox_Profile.DisplayMember = "Name";
                CBox_Profile.ValueMember = "Name";
                CBox_Profile.SelectedIndex = -1;
                CBox_Profile.Text = "";


            }
            catch { }

            //try
            //{
            //    List<UmProfile> sp = Global_Variable.UM_Profile;
            //    Dictionary<string, string> comboSource = new Dictionary<string, string>();
            //    comboSource.Add("", "");
            //    foreach (UmProfile user in sp)
            //    {
            //        comboSource.Add(user.Name, user.Name);
            //    }
            //    CBox_Profile.DataSource = new BindingSource(comboSource, null);
            //    CBox_Profile.DisplayMember = "Value";
            //    CBox_Profile.ValueMember = "Key";
            //    CBox_Profile.SelectedIndex = 0;
            //    CBox_Profile.Text = "";

            //}
            //catch { }


        }
        private void Get_SellingPoint()
        {

            try
            {

                Smart_DataAccess da = new Smart_DataAccess();
                CBox_SellingPoint.DataSource = da.Get_BindingSource_SellingPoint();
                CBox_SellingPoint.DisplayMember = "Value";
                CBox_SellingPoint.ValueMember = "Key";
                CBox_SellingPoint.SelectedIndex = 0;
                CBox_SellingPoint.Text = "";

            }
            catch { }
        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return;
            try
            {
                if (Global_Variable.UM_Customer == null)
                    return;
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                CBox_Customer.Text = "";
            }
            catch { }
        }
        private void Get_Batch_cards()
        {
            try
            {
                CBox_Batch.DataSource = Smart_DA.Get_BindingSource_Cards_Batch();
                CBox_Batch.ValueMember = "Value";
                CBox_Batch.DisplayMember = "Key";
                CBox_Batch.SelectedIndex = -1;
                CBox_Batch.Text = "";
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.label.RightToLeft = RightToLeft.No;
                CBox_Batch.RightToLeft = RightToLeft.No;

            }
            catch { }
        }

        private void Get_NumberPrint(string BatchNumber)
        {
            try
            {
                CBox_NumberPrint.DataSource = Smart_DA.Get_BindingSource_Number_Print(0,BatchNumber);
                CBox_NumberPrint.ValueMember = "Value";
                CBox_NumberPrint.DisplayMember = "Key";
                CBox_NumberPrint.SelectedIndex = -1;
                CBox_NumberPrint.Text = "";
                CBox_NumberPrint.label.RightToLeft = RightToLeft.No;
                CBox_NumberPrint.label.RightToLeft = RightToLeft.No;
                CBox_NumberPrint.RightToLeft = RightToLeft.No;

            }
            catch { }
        }

        private void Get_Status()
        {
            try
            {
                //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();

                if (fillter_type == "From_Finsh_Cards")
                {
                    comboSource.Add("-1", "");
                    comboSource.Add("0", "منتهي الايام");
                    comboSource.Add("1", "منتهي الوقت");
                    comboSource.Add("2", "منتهي التحميل");
                }
                else
                {
                    comboSource.Add("-1", "");
                    comboSource.Add("0", "انتظار");
                    comboSource.Add("1", "نشط");
                    comboSource.Add("2", "منتهي");
                    //comboSource.Add("4", "معطل");
                    //comboSource.Add("5", "مفعل");
                    comboSource.Add("3", "خطاء في الباقة");
                    //comboSource.Add("4", "مؤرشف");
                }

                CBox_Staus.DataSource = new BindingSource(comboSource, null);
                CBox_Staus.DisplayMember = "Value";
                CBox_Staus.ValueMember = "Key";
                CBox_Staus.SelectedIndex = 0;
                CBox_Staus.Text = "";
            }
            catch { }
        }
        private void Get_Status_disabled()
        {
            try
            {

                //List<Class_Batch_cards> sp = SqlDataAccess.Get_Batch_Cards();
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("-1", "");
                comboSource.Add("1", "معطل");
                comboSource.Add("0", "مفعل");

                CBox_Didabled.DataSource = new BindingSource(comboSource, null);
                CBox_Didabled.DisplayMember = "Value";
                CBox_Didabled.ValueMember = "Key";
                CBox_Didabled.SelectedIndex = 0;
                CBox_Didabled.Text = "";
            }
            catch { }
        }

        private void loadDgvState()
        {
            SourceSaveStateFormsVariable DgvState = null;

            if (fillter_type == "From_Finsh_Cards")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Finsh_Cards");
            else if (fillter_type == "From_Session")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_Session");
            else if (fillter_type == "From_RB_Archive")
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("Dgv_From_RB_Archive");
            else
                DgvState = Smart_DataAccess.Get_SourceSaveStateFormsVariable("DgvUserManagerPrcess");


            if (DgvState == null)
            {
                Init_dgv_to_Default();
                SaveFromState();
                return;
            } 
            Dgv_State_list = JsonConvert.DeserializeObject<Dgv_Header_Proprties>(DgvState.values.ToString());

            if (Dgv_State_list == null)
            {
                Init_dgv_to_Default();
                SaveFromState();
                return;
            }
            dvalue = Dgv_State_list.items;
            foreach (Dgv_Header_Values dv in Dgv_State_list.items.Values)
            {
                try
                {
                    dgv.Columns[dv.Index].Visible = dv.Visable;
                    dgv.Columns[dv.Index].DisplayIndex = dv.DisplayIndex;//toolStripSeparator5
                    dgv.Columns[dv.Index].Width = dv.Width;
                    foreach (var control in View_Hide_toolStripMenuItem.DropDownItems)
                    //foreach (ToolStripMenuItem control in View_Hide_toolStripMenuItem.DropDownItems)
                    {
                        //if (control.HasDropDownItems)
                        if (control.GetType() == typeof(ToolStripMenuItem))
                        {
                            ToolStripMenuItem control1 = (ToolStripMenuItem)control;
                            if (control1.Tag != null)
                                if (control1.Tag.ToString().ToLower() == dv.Name.ToLower())
                                {
                                    control1.Checked = dv.Visable;
                                }
                        }
                    }
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            }

            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["Status"].Visible = false; } catch { }
            try { dgv.Columns["Disabled"].Visible = false; } catch { }

            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }
            //try { dgv.Columns["Status"].Visible = false; } catch { }
            //try { dgv.Columns["Id"].Visible = false; } catch { }
            //try { dgv.Columns["IdHX"].Visible = false; } catch { }
            //try { dgv.Columns["Sn_userName"].Visible = false; } catch { }
            //try { dgv.Columns["Delet_fromServer"].Visible = false; } catch { }
            //try { dgv.Columns["ProfileTillTime"].Visible = false; } catch { }
            //try { dgv.Columns["ProfileTimeLeft"].Visible = false; } catch { }
            //try { dgv.Columns["ProfileTransferLeft"].Visible = false; } catch { }
            //try { dgv.Columns["ProfileValiday"].Visible = false; } catch { }
            //try { dgv.Columns["FirstUse"].Visible = false; } catch { }
            //try { dgv.Columns["CallerId"].Visible = false; } catch { }
            //try { dgv.Columns["UptimeLimit"].Visible = false; } catch { }
            //try { dgv.Columns["TransferLimit"].Visible = false; } catch { }
            try { dgv.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv.Columns["CallerMac"].Visible = false; } catch { }
            //try { dgv.Columns["LastSeenAt"].Visible = false; } catch { }
            //try { dgv.Columns["Disabled"].Visible = false; } catch { }
            //try { dgv.Columns["RegDate"].Visible = false; } catch { }
            //try { dgv.Columns["ActiveSessions"].Visible = false; } catch { }
            //try { dgv.Columns["SpId"].Visible = false; } catch { }
            ////try { dgv.Columns["NasPortId"].Visible = false; } catch { }
            ////try { dgv.Columns["Radius"].Visible = false; } catch { }
            //try { dgv.Columns["CountProfile"].Visible = false; } catch { }
            //try { dgv.Columns["CountSession"].Visible = false; } catch { }

            Select_First_DGV();
        }
      
        public void SaveFromState()
        {
            try
            {
                Dgv_State_list = new Dgv_Header_Proprties();
                dvalue = new Dictionary<int, Dgv_Header_Values>();
                foreach (DataGridViewColumn column in dgv.Columns)
                {
                    Dgv_Header_Values dgv_Header_Values = new Dgv_Header_Values();
                    dgv_Header_Values.Visable = column.Visible;
                    dgv_Header_Values.HeaderText = column.HeaderText;
                    dgv_Header_Values.Name = column.Name;
                    dgv_Header_Values.DisplayIndex = column.DisplayIndex;
                    dgv_Header_Values.Index = column.Index;
                    dgv_Header_Values.Width = column.Width;

                    dvalue[column.Index] = dgv_Header_Values;
                }
                Dgv_State_list.items = dvalue;


                string formSetting = JsonConvert.SerializeObject(Dgv_State_list);

                if (fillter_type == "From_Finsh_Cards")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_Finsh_Cards", "SaveControlState", formSetting);
                else if (fillter_type == "Dgv_From_Session")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_Session", "SaveControlState", formSetting);
                else if (fillter_type == "From_RB_Archive")
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("Dgv_From_RB_Archive", "SaveControlState", formSetting);
                else
                    Smart_DataAccess.Setting_SaveState_Forms_Variables("DgvUserManagerPrcess", "SaveControlState", formSetting);


            }
            catch { }
        }
     
        
        
        private void update_select_DGV()
        {
            try
            {
                string ListAll = dgv.Rows.Count.ToString();
                string ListSelected = dgv.SelectedRows.Count.ToString();
                string lblDescription = "( " + ListAll + "  /  " + ListSelected + " )";
                Global_Variable.Update_Um_StatusBar(true, false, 0, lblDescription, "");
            }
            catch { }

            //Select_First_DGV();
        }

        private void Select_First_DGV()
        {
            return;
            dgv.Refresh();
           
            try
            {
                if (dgv.Rows.Count > 0)
                    dgv.CurrentCell = dgv.Rows[0].Cells["Str_Status"];
            }
            catch { }

            try
            {
                if (dgv.Rows.Count > 0)
                    dgv.Columns["Str_Status"].Selected = true;
            }
            catch { }

        }
        public void LoadDataGridviewData2()
        {
            List<UmUser> users = null;

            try
            {


                string sql_where = "";
                string sql_where_session = "";
                string sql_where_Pyment = "";
                string sql_select = "";
                string orderby = " ORDER BY ";

                if (fillter_type == "From_Server")
                {
                    sql_where = " where UmUser.DeleteFromServer = 0 ";
                    sql_where_session = " and  UmSession.DeleteFromServer = 0 ";
                    sql_where_Pyment = " and UmPyment.DeleteFromServer = 0 ";
                }
                if (fillter_type == "From_RB_Archive")
                {
                    sql_where = "  ";

                    if (ToggleButton_Show_Archive.Checked)
                    {
                        sql_where = " where UmUser.DeleteFromServer = 1 ";

                        sql_where_session = " and  UmSession.DeleteFromServer = 1 ";
                        sql_where_Pyment = " and UmPyment.DeleteFromServer = 1 ";
                    }
                    if (ToggleButton_Show_onlyServer.Checked)
                    {
                        sql_where = " where UmUser.DeleteFromServer = 0 ";

                        sql_where_session = " and  UmSession.DeleteFromServer = 0 ";
                        sql_where_Pyment = " and UmPyment.DeleteFromServer = 0 ";
                    }
                    //sql_where_session = "  ";
                    //sql_where_Pyment = "  ";
                }
                orderby = " ORDER BY SN DESC ";

                if (CBox_OrderBy.Text != "")
                {
                    orderby = $" ORDER BY {CBox_OrderBy.SelectedValue} ";

                    if (ToggleButton_ByCountSession.Checked)
                    {
                        firstLoad = true;
                        CBox_OrderBy.SelectedIndex = 19;
                        orderby = " ORDER BY count(UmSession.Sn_Name)  ";
                        firstLoad = false;
                    }
                    if (ToggleButton_ByCountProfile.Checked)
                    {
                        firstLoad = true;
                        CBox_OrderBy.SelectedIndex = 20;
                        orderby = " ORDER BY count(UmPyment.Sn_Name)  ";
                        firstLoad = false;
                    }

                    if (CheckBox_orderBy.Check)
                        orderby = orderby + " DESC ";
                }

                sql_where = Get_Fillter(sql_where);
                //sql_where_session = Get_Fillter(sql_where_session);
                //sql_where_Pyment = Get_Fillter(sql_where_Pyment);

                if (ToggleButton_ByCountSession.Checked)
                {
                    sql_select = $"select UmUser.*,count(UmSession.Fk_Sn_Name) as CountSession from UmUser  INNER JOIN UmSession  ON UmUser.Sn_Name = UmSession.Fk_Sn_Name  {sql_where} {sql_where_session} GROUP by UmUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    string c = $"WITH Get_Count AS( select UmUser.UserName,count(UmSession.Fk_Sn_Name) as CountSession from UmUser  INNER JOIN UmSession  ON UmUser.Sn_Name = UmSession.Fk_Sn_Name  {sql_where} {sql_where_session}  GROUP by UmUser.Sn_Name) SELECT count( UserName) FROM Get_Count ";
                    totalRows = (int)Local_DA.Get_int_FromDB(c);
                    //totalRows = db.Scalar<int>($"WITH Get_Count AS( select UmUser.UserName,count(UmSession.Fk_Sn_Name) as CountSession from UmUser  INNER JOIN UmSession  ON UmUser.Sn_Name = UmSession.Fk_Sn_Name  {sql_where} {sql_where_session}  GROUP by UmUser.Sn_Name) SELECT count( UserName) FROM Get_Count ");
                    //totalRows = db.Scalar<int>($"select count(UmUser.Sn_Name),count(UmSession.Fk_Sn_Name) as CountSession from UmUser  INNER JOIN UmSession  ON UmUser.Sn_Name = UmSession.Fk_Sn_Name  {sql_where}  GROUP by UmUser.Sn_Name {orderby} ");
                }
                else if (ToggleButton_ByCountProfile.Checked)
                {
                    sql_select = $"select UmUser.*,count(UmPyment.Fk_Sn_Name) as  CountProfile from UmUser INNER JOIN UmPyment ON UmUser.Sn_Name = UmPyment.Fk_Sn_Name  {sql_where} {sql_where_Pyment} GROUP by UmUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    //totalRows = db.Scalar<int>($"select count(UmUser.Sn_Name),count(UmPyment.Fk_Sn_Name) as CountProfile from UmUser  INNER JOIN UmPyment  ON UmUser.Fk_Sn_Name = UmPyment.Fk_Sn_Name  {sql_where}  GROUP by UmUser.Sn_Name  ");
                    string c = $"WITH Get_Count AS( select UmUser.UserName,count(UmPyment.Fk_Sn_Name) as CountProfile from UmUser  INNER JOIN UmPyment  ON UmUser.Sn_Name = UmPyment.Fk_Sn_Name {sql_where} {sql_where_Pyment}  GROUP by UmUser.Sn_Name) SELECT count( UserName) FROM Get_Count ";
                    totalRows = (int)Local_DA.Get_int_FromDB(c);

                }
                //select UmUser.*, count(UmPyment.Fk_Sn_Name) as CountProfile from UmUser LEFT JOIN UmPyment ON UmUser.Sn_Name = UmPyment.Fk_Sn_Name  where UmUser.DeleteFromServer = 0  GROUP by UmPyment.Fk_Sn_Name ORDER BY CountProfile DESC LIMIT 100 OFFSET 0;

                else
                {
                    sql_select = $"select * from UmUser  {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    string c = "select count(*) from UmUser  " + sql_where;
                    totalRows = (int)Local_DA.Get_int_FromDB(c);
                }

                //sql_select = $"select * from UmUser  {sql_where}  ORDER BY SN DESC LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";

                totalPages = (int)Math.Ceiling((double)totalRows / PageSize);
                txtTotalPages.Text = totalPages.ToString();
                txtCurrentPageindex.Text = currentPageindex.ToString();
                txtAllCountRows.Text = totalRows.ToString();
                try
                {
                    var umS = Local_DA.Load<UmUser>(sql_select);
                    //var umS = db.SqlList<UmUser>(sql_select);
                    users = umS;
                    //dgv.RightToLeft = RightToLeft.Yes;
                    //dgv.DefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
                }
                catch { }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }


            dgv.DataSource = users;
            update_select_DGV();


            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }

            //dgv.Columns["CountSession"].Visible = false;


            if (ToggleButton_ByCountSession.Checked)
            {
                try { dgv.Columns["CountSession"].Width = 120; } catch { }
                try { dgv.Columns["CountSession"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["CountSession"].Visible = true; } catch { }
                try { dgv.Columns["CountProfile"].Visible = false; } catch { }


                CountSession_ToolStripMenuItem.Checked = true;
                //update_select_DGV();
            }
            else if (ToggleButton_ByCountProfile.Checked)
            {
                try { dgv.Columns["CountProfile"].Width = 120; } catch { }
                try { dgv.Columns["CountProfile"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["CountProfile"].Visible = true; } catch { }
                try { dgv.Columns["CountSession"].Visible = false; } catch { }

                Count_profile_ToolStripMenuItem.Checked = true;


            }
            else
            {
                //try { dgv.Columns["CountSession"].Visible = false; } catch { }
                //CountSession_ToolStripMenuItem.Checked = false;
                //Count_profile_ToolStripMenuItem.Checked = false;

                ////dgv.Columns["CountSession"].Visible = false;
                //try { dgv.Columns["CountProfile"].Visible = false; } catch { }
                ////loadData();
            }
            disable_colum();

           Select_First_DGV();

        }

        [Obsolete]
        private void btn_search_Click(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            try
            {
                firstLoad = true;
                Cursor.Current = Cursors.WaitCursor;
                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;

                loadData();

                Cursor.Current = Cursors.Default;
                firstLoad = false;
            }
            catch { firstLoad = false; }
        }
        private void fill_Combo_orderBy()
        {
            try
            {
                //Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //foreach (DataGridViewColumn column in dgv.Columns)
                //{

                //    comboSource.Add(column.Name, column.Name);
                //}
                //CBox_OrderBy.DataSource = new BindingSource(comboSource, null);
                //CBox_OrderBy.DisplayMember = "Value";
                //CBox_OrderBy.ValueMember = "Key";
                //CBox_OrderBy.SelectedIndex = 0;
                ////CBox_SearchBy.Text = "";

                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                comboSource.Add("SN", "الرقم التسلسلي");
                comboSource.Add("UserName", "الاسم");
                comboSource.Add("Password", "كلمة المرور");
                comboSource.Add("BatchCardId", "الدفعة");
                comboSource.Add("ProfileName", "الباقة");
                comboSource.Add("SpCode", "نقطة البيع");
                comboSource.Add("UptimeLimit", "الوقت المسموح");
                comboSource.Add("TransferLimit", "التنزيل المسموح");
                comboSource.Add("UptimeUsed", "الوقت المستخدم");
                comboSource.Add("DownloadUsed+UploadUsed", "تحميل+رفع المستخدم");
                comboSource.Add("DownloadUsed", "التحميل المستخدم");
                comboSource.Add("UploadUsed", "الرفع المستخدم");
                comboSource.Add("ProfileTimeLeft", "الوقت المتبقي");
                comboSource.Add("ProfileTransferLeft", "التحميل المتبقي");
                comboSource.Add("Status", "الحالة");
                comboSource.Add("Price", "السعر");
                comboSource.Add("RegDate", "تاريخ الاضافة");
                comboSource.Add("CustomerName", "مستخدم يوزمنجر");
                comboSource.Add("Comment", "تعليق");
                comboSource.Add("CountSession", "عدد الجلسات");
                comboSource.Add("CountProfile", "عدد الباقات");

                CBox_OrderBy.DataSource = new BindingSource(comboSource, null);
                CBox_OrderBy.DisplayMember = "Value";
                CBox_OrderBy.ValueMember = "Key";
                CBox_OrderBy.SelectedIndex = 0;
                //CBox_OrderBy.Text = "";

            }
            catch { }

        }
        public void LoadDataGridviewData()
        {
            //lbl_Filter.Text = "";
            //lbl_Filter.Visible = false;
            //lbl_Filter.Text = "فلترة بحسب : ";

            //if (ToggleButton_ByCountSession.Checked)
            //{
            //    LoadData_ByCountSession();
            //}
            if (fillter_type == "From_Finsh_Cards")
            {
                Get_Finsh_Cards();
                return;
            }

            LoadDataGridviewData2();

            Select_First_DGV();
            return;


        }

        private string Get_Fillter(string sql_sorce = "")
        {
            string sql = sql_sorce;


            //string sql = "";
            if (txt_search.Text.Trim() != "")
            {
                //WHERE SALARY LIKE '%200%'
                if (CBox_SearchBy.SelectedIndex == 0)
                    sql += "and UmUser.UserName LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 1)
                    sql += "and UmUser.Password LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 2)
                    sql += "and UmUser.SN LIKE '%" + txt_search.Text + "%'  or  UmUser.SN LIKE '%" + txt_search.Text + "%' ";
                else if (CBox_SearchBy.SelectedIndex == 3)
                    sql += "and UmUser.SpName LIKE '%" + txt_search.Text + "%'  or  UmUser.SpCode LIKE '%" + txt_search.Text + "%' ";

            }

            if (CBox_Profile.Text != "" && CBox_Profile.SelectedIndex != 0)
            {
                sql += "and UmUser.ProfileName ='" + CBox_Profile.Text + "' ";
                lbl_Filter.Text += "- الباقة ";
                //lbl_Filter.Visible = true;
            }
            if (CBox_SellingPoint.Text != "" && CBox_SellingPoint.SelectedIndex != 0)
            {
                sql += "and UmUser.SpCode ='" + CBox_SellingPoint.SelectedValue.ToString() + "' ";
                lbl_Filter.Text += "-نقطة البيع";
                //lbl_Filter.Visible = true;
            }
            if (CBox_NumberPrint.Text != "" && CBox_NumberPrint.SelectedIndex != 0)
            {
                sql += "and UmUser.NumberPrint =" + CBox_NumberPrint.SelectedValue.ToString() + " ";
                lbl_Filter.Text += "-الطبعة";
                //lbl_Filter.Visible = true;
            }
            if (CBox_Batch.Text != "" && CBox_Batch.SelectedIndex != 0)
            {
                sql += "and UmUser.BatchCardId =" + CBox_Batch.SelectedValue.ToString() + " ";
                lbl_Filter.Text += "-الدفعة";
                //lbl_Filter.Visible = true;
            }
            if (CBox_Customer.Text != "" && CBox_Customer.SelectedIndex != 0)
            {
                sql += "and UmUser.CustomerName ='" + CBox_Customer.Text + "' ";
                lbl_Filter.Text += "-مستخدم يوزمنجر";
                //lbl_Filter.Visible = true;
            }
            if (CheckBox_SN.Check && CBox_SN_Compar.Text != "")
            {
                //lbl_Filter.Visible = true;
                lbl_Filter.Text += "-التسلسلي";

                if (CBox_SN_Compar.Text.ToString() == "بين")
                {
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text) && utils.check_Filed_Intiger(txt_SN_End.Text))
                        sql += "and  (UmUser.Sn BETWEEN " + txt_SN_Start.Text + " and  " + txt_SN_End.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                }

                if (CBox_SN_Compar.Text.ToString() == "=")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (UmUser.Sn =" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                if (CBox_SN_Compar.Text.ToString() == ">")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (UmUser.Sn >" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
                if (CBox_SN_Compar.Text.ToString() == "<")
                    if (utils.check_Filed_Intiger(txt_SN_Start.Text))
                        sql += "and (UmUser.Sn <" + txt_SN_Start.Text + ") ";
                    else RJMessageBox.Show(" ادخل الرقم التسلسلي بشكل صحيح ");
            }
            if (CBox_Staus.Text != "" && CBox_Staus.SelectedIndex != 0 && fillter_type != "From_Finsh_Cards")
            {
                sql += "and UmUser.Status =" + CBox_Staus.SelectedValue + " ";
                lbl_Filter.Text += "-الحالة";
                //lbl_Filter.Visible = true;
            }
            if (CBox_Didabled.Text != "" && CBox_Didabled.SelectedIndex != 0)
            {
                sql += "and UmUser.Disabled =" + CBox_Didabled.SelectedValue + " ";
                lbl_Filter.Text += "-الحالة";
                //lbl_Filter.Visible = true;
            }
            if (CheckBox_byDatePrint.Check)
            {
                //lbl_Filter.Visible = true;
                lbl_Filter.Text += " -تاريخ الطباعة";
                //WHERE date BETWEEN '2022-01-10 13:45:00' AND '2023-01-10 15:50:00';
                sql += "and UmUser.RegDate >='" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND UmUser.RegDate <='" + Date_To.Value.AddDays(1).ToString("yyyy-MM-dd") + "'  ";
                //sql += " and RegDate BETWEEN '" + Date_From.Value.ToString("yyyy-MM-dd") + "' AND '"+ Date_To.Value.ToString("yyyy-MM-dd")+"'  ";
            }
            //if (fillter_type != "From_Finsh_Cards")
            //{
            //sql += " ORDER BY SN DESC ";
            //}

            char[] charsToTrim1 = { 'a', 'n', 'd' };
            sql = sql.TrimStart();
            sql = sql.TrimStart(charsToTrim1);

            if (sql_sorce.Trim() == "" && sql.Trim() != "")
                sql = " where " + sql + " ";
            return sql;

        }
        private void Get_Finsh_Cards()
        {

            dgv.DataSource = null;
            List<UMUser_Finsh_Cards> users = null;
            string sql_where = " UmUser.DeleteFromServer = 0 ";
            string sql_select = " ";
            string orderby = "  ORDER BY SN DESC ";
            if (CBox_OrderBy.Text != "")
            {
                orderby = $" ORDER BY {CBox_OrderBy.SelectedValue} ";
                if (CheckBox_orderBy.Check)
                    orderby = orderby + " DESC ";
            }
            string cond = $" (Status == 2  " +
                          $"or  ((UptimeLimit > 0 and UptimeUsed > 0) and ((UptimeLimit - UptimeUsed <= 0)))" +
                          $"or   ((TransferLimit > 0 and  DownloadUsed > 0) and ((TransferLimit - (UploadUsed + DownloadUsed )) <= 0)  ))";


            if (CBox_Staus.Text != "" && CBox_Staus.SelectedIndex != 0)
            {
                if (CBox_Staus.SelectedIndex == 1)
                    cond = "(Status == 2  ) ";
                else if (CBox_Staus.SelectedIndex == 2)
                    cond = " (  (UptimeLimit > 0 and UptimeUsed > 0) and ((UptimeLimit - UptimeUsed <= 0))  and Status != 2 ) ";
                else if (CBox_Staus.SelectedIndex == 3)
                    cond = " ( (TransferLimit > 0 and  DownloadUsed > 0) and ((TransferLimit - (UploadUsed + DownloadUsed )) <= 0) and Status != 2 )";
            }
            sql_where = Get_Fillter(sql_where);

            //using (var db = dbFactory.Open())
            //{
            //string sql = " UmUser.DeleteFromServer = 0 ";
            //sql =  Get_Fillter(sql);

            sql_select = $"select * from UmUser WHERE {cond} and {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
            string sql_select_count = $"select count(Sn_Name) from UmUser WHERE {cond} and {sql_where}  ";

            try
            {
                users = Local_DA.Load<UMUser_Finsh_Cards>(sql_select);
                //users = db.SqlList<UMUser_Finsh_Cards>(sql_select);
            }
            catch { }


            totalRows = (int)Local_DA.Get_int_FromDB(sql_select_count);
            totalPages = (int)Math.Ceiling((double)totalRows / PageSize);
            txtTotalPages.Text = totalPages.ToString();
            txtCurrentPageindex.Text = currentPageindex.ToString();
            txtAllCountRows.Text = totalRows.ToString();

            //users = db.SqlList<UMUser_Finsh_Cards>($"SELECT *  FROM UmUser  WHERE ( " +
            //    $"Status == 2  " +
            //    $"or  ((UptimeLimit > 0 and UptimeUsed > 0) and ((UptimeLimit - UptimeUsed <= 0)))" +
            //    $"or   ((TransferLimit > 0 and  DownloadUsed > 0) and ((TransferLimit - (UploadUsed + DownloadUsed )) <= 0)  )" +
            //         $")  and " +
            //         $" {sql_where} {orderby} LIMIT {PageSize} OFFSET {(currentPageindex - 1) * PageSize}");

            //}
            try
            {
                dgv.DataSource = users;
                dgv.Columns["Str_Status"].Visible = true;
                dgv.Columns["Str_Status"].Width = 180;
                dgv.Columns["Str_Status"].DisplayIndex = 0;
                update_select_DGV();
               
            }
            catch { }
            disable_colum();
            Select_First_DGV();
        }
        void disable_colum()
        {
            try { dgv.Columns["Sn_Name"].Visible = false; } catch { }
            try { dgv.Columns["IdHX"].Visible = false; } catch { }
            try { dgv.Columns["DeleteFromServer"].Visible = false; } catch { }
            try { dgv.Columns["Status"].Visible = false; } catch { }
            try { dgv.Columns["Disabled"].Visible = false; } catch { }
            try { dgv.Columns["UptimeUsed"].Visible = false; } catch { }
            try { dgv.Columns["DownloadUsed"].Visible = false; } catch { }
            try { dgv.Columns["UploadUsed"].Visible = false; } catch { }
            try { dgv.Columns["CallerMac"].Visible = false; } catch { }
            try { dgv.Columns["NasPortId"].Visible = false; } catch { }
            //try { dgv.Columns["Radius"].Visible = false; } catch { }

        }
        private void update_header_DGV()
        {
            //dgv.AllowUserToOrderColumns = true;
            //System.Drawing.Font dgvHeader_font = Program.GetCustomFont(Resources.Cairo_Medium, 9, FontStyle.Bold);
            //dgv.ColumnHeadersDefaultCellStyle.Font = dgvHeader_font;
            //dgv.ColumnHeadersDefaultCellStyle.Alignment = DataGridViewContentAlignment.MiddleCenter;
            //dgv.ColumnHeadersHeight = 40;
            ////dgv.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.None;

            loadDgvState();


            //if (UIAppearance.Language_ar == false)
            //{
            //    foreach (DataGridViewColumn column in dgv.Columns)
            //    {
            //        try
            //        {
            //            if (column.HeaderText == "Sn") { column.HeaderText = "SN"; }
            //            else if (column.HeaderText == "Str_Status") { column.HeaderText = "Status"; }

            //            else if (column.HeaderText == "UserName") { column.HeaderText = "UserName"; }
            //            else if (column.HeaderText == "Password") { column.HeaderText = "Password"; }
            //            else if (column.HeaderText == "ActualProfileName") { column.HeaderText = "Profile"; }

            //            else if (column.HeaderText == "Str_UptimeUsed") { column.HeaderText = "Uptime Used"; }
            //            else if (column.HeaderText == "Str_Up_Down") { column.HeaderText = "Up_Down"; }
            //            else if (column.HeaderText == "Str_DownloadUsed") { column.HeaderText = "Download Used"; }
            //            else if (column.HeaderText == "Str_UploadUsed") { column.HeaderText = "Upload Used"; }

            //            else if (column.HeaderText == "MoneyTotal") { column.HeaderText = "Price"; }
            //            else if (column.HeaderText == "Str_UptimeLimit") { column.HeaderText = "Uptime Limit"; }
            //            else if (column.HeaderText == "Str_TransferLimit") { column.HeaderText = "Transfer Limit"; }

            //            else if (column.HeaderText == "Str_ProfileTimeLeft") { column.HeaderText = "Profile TimeLeft"; }
            //            else if (column.HeaderText == "ProfileTransferLeft") { column.HeaderText = "Profile TransferLeft"; }
            //            else if (column.HeaderText == "Str_ProfileTillTime") { column.HeaderText = "تاريخ الانتهاء"; }

            //            else if (column.HeaderText == "CountProfile") { column.HeaderText = "Count Profile"; }
            //            else if (column.HeaderText == "SpName") { column.HeaderText = "Selling Point"; }
            //            else if (column.HeaderText == "NumberPrintedId") { column.HeaderText = "Bach No"; }
            //            else if (column.HeaderText == "CustomerName") { column.HeaderText = "Customer"; }
            //            else if (column.HeaderText == "Descr") { column.HeaderText = "Comment"; }
            //        }
            //        catch { }
            //    }
            //}

        }
        private void dgv_Users_CellClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1) //not click  on header
            {

            }

        }

        private void dgv_Users_SelectionChanged(object sender, EventArgs e)
        {
            update_select_DGV();
        }

        [Obsolete]
        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();



            Get_Cbox_Profile();
            Get_SellingPoint();
            Get_UMCustomer();
            Get_Batch_cards();
            //Get_NumberPrint();
            Get_Status();
            Get_Status_disabled();

            if (Filter_BatchCards != null)
            {
                //try
                //{
                //    CheckBox_SN.Check = true;
                //    txt_SN_Start.Text= Filter_BatchCards.Sn_from.ToString();
                //    txt_SN_End.Text = Filter_BatchCards.Sn_to.ToString();
                //    CBox_SN_Compar.SelectedIndex = 3;
                //}
                //catch { }
                try
                {
                    for (int i = 0; i < CBox_Batch.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_Batch.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_BatchCards.BatchNumber.ToString() == Value)
                        {
                            CBox_Batch.SelectedIndex = i;
                            CBox_Batch.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }
            if (Filter_NumberPrintCard != null)
            {
                try
                {
                    CheckBox_SN.Check = true;
                    txt_SN_Start.Text = Filter_NumberPrintCard.Sn_from.ToString();
                    txt_SN_End.Text = Filter_NumberPrintCard.Sn_to.ToString();
                    CBox_SN_Compar.SelectedIndex = 3;
                    CheckBox_SN.Enabled=txt_SN_End.Enabled=txt_SN_Start.Enabled=CBox_SN_Compar.Enabled=false;
                }
                catch { }

                //try
                //{
                //    Get_NumberPrint(CBox_Batch.SelectedValue.ToString());
                //    for (int i = 0; i < CBox_NumberPrint.Items.Count; ++i)
                //    {
                //        var selectedItem = (KeyValuePair<string, string>)CBox_NumberPrint.Items[i];
                //        string text = selectedItem.Key;
                //        string Value = selectedItem.Value;
                //        if (Filter_NumberPrintCard.NumberPrint.ToString() == Value)
                //        {
                //            CBox_NumberPrint.SelectedIndex = i;
                //            CBox_NumberPrint.Enabled = false;
                //            break;
                //        }
                //    }
                //}
                //catch { }
            }
            if (Filter_ProfileName != null)
            {
                try
                {
                    for (int i = 0; i < CBox_Profile.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_Profile.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_ProfileName == Value)
                        {
                            CBox_Profile.SelectedIndex = i;
                            CBox_Profile.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }
            if (Filter_SpCode != null)
            {
                try
                {
                    for (int i = 0; i < CBox_SellingPoint.Items.Count; ++i)
                    {
                        var selectedItem = (KeyValuePair<string, string>)CBox_SellingPoint.Items[i];
                        string text = selectedItem.Key;
                        string Value = selectedItem.Value;
                        if (Filter_SpCode == Value)
                        {
                            CBox_SellingPoint.SelectedIndex = i;
                            CBox_SellingPoint.Enabled = false;
                            break;
                        }
                    }
                }
                catch { }
            }

            firstLoad = false;


            loadData();
            loadDgvState();
            update_select_DGV();
            //fill_Combo_orderBy();
        }

        private void FormAllCardsUserManager_Load(object sender, EventArgs e)
        {

            timer1.Start();
            //firstLoad=false;
        }

        private void dgv_ColumnDisplayIndexChanged(object sender, DataGridViewColumnEventArgs e)
        {
            //if (firstLoad==true)
            //    return;
            //Dgv_Header_Values v =new Dgv_Header_Values();
            //v.Index = dgv.Columns[e.Column.Index].Index;
            //v.Name = dgv.Columns[e.Column.Index].Name;
            //v.DisplayIndex = dgv.Columns[e.Column.Index].DisplayIndex;
            //v.Visable = dgv.Columns[e.Column.Index].Visible;

            //dvalue[e.Column.Index] = v;


            //string formSetting = JsonConvert.SerializeObject(Dgv_State_list);
            //SqlDataAccess.Setting_SaveState_Forms_Variables("DgvUserManagerPrcess", "SaveControlState", formSetting);
        }

        private void dgv_ColumnHeaderMouseClick(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.RowIndex == -1) //not click  on header
            {
                Dgv_Click_ToChangIndex = true;
            }
        }
        private void FormAllCardsUserManager_FormClosing(object sender, FormClosingEventArgs e)
        {
            //SaveFromState();
        }

        private void DGV_User_Color()
        {
            return;
            //Thread.Sleep(2000);
            foreach (DataGridViewRow row in dgv.Rows)
            {
                try
                {
                    string Status = row.Cells["Status"].Value.ToString();
                    string disabled = row.Cells["disabled"].Value.ToString();

                    switch (Status)
                    {
                        case "0":
                            //row.DefaultCellStyle.ForeColor = Color.Black;
                            //row.DefaultCellStyle.BackColor = Color.White;
                            break;
                        case "1":
                            row.Cells["Str_Status"].Style.BackColor = Color.Red;
                            row.Cells["Str_Status"].Style.ForeColor = Color.White;
                            //row.DefaultCellStyle.BackColor = Color.Red;
                            //row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                        case "2":
                            row.Cells["Str_Status"].Style.BackColor = Color.Yellow;
                            row.Cells["Str_Status"].Style.ForeColor = Color.Black;

                            //row.DefaultCellStyle.BackColor = Color.Yellow;
                            //row.DefaultCellStyle.ForeColor = Color.Black;
                            break;
                        case "3":
                            row.Cells["Str_Status"].Style.BackColor = Color.Black;
                            row.Cells["Str_Status"].Style.ForeColor = Color.White;


                            //row.DefaultCellStyle.BackColor = Color.Black;
                            //row.DefaultCellStyle.ForeColor = Color.White;
                            break;
                    }
                    if (disabled == "1")
                    {
                        row.Cells["Str_Status"].Style.BackColor = Color.Silver;
                        row.Cells["Str_Status"].Style.ForeColor = Color.White;

                    }
                }
                catch { }
            }
        }

        private void dgv_RowPrePaint(object sender, DataGridViewRowPrePaintEventArgs e)
        {
            return;
            try
            {
                DataGridViewRow row = dgv.Rows[e.RowIndex];
                //if ((row.Cells[e.RowIndex].ColumnIndex) == (row.Cells["Status"].ColumnIndex))
                //{
                int status = Convert.ToInt32(row.Cells["Status"].Value);
                int disabled = Convert.ToInt32(row.Cells["Disabled"].Value);

                if (status == 1)
                {
                    //row.Cells["Str_Status"].Value = "";
                    //IconButton icon = new IconButton();
                    //icon.Width = 10;
                    //icon.Height = 10;
                    //icon.IconChar =IconChar.FolderPlus;
                    //row.Cells["Str_Status"].Value =icon;

                    ////row.DefaultCellStyle.BackColor = Color.Orange;
                    row.Cells["Str_Status"].Style.BackColor = Color.Red;
                    row.Cells["Str_Status"].Style.ForeColor = Color.White;
                    //row.DefaultCellStyle.ForeColor = Color.Red;
                }
                else if (status == 2)
                {
                    row.Cells["Str_Status"].Style.BackColor = Color.Yellow;
                    row.Cells["Str_Status"].Style.ForeColor = Color.Black;
                    //row.DefaultCellStyle.ForeColor = Color.Yellow;

                }
                else if (status == 3)
                {
                    row.Cells["Str_Status"].Style.BackColor = Color.Black;
                    row.Cells["Str_Status"].Style.ForeColor = Color.White;
                    //row.DefaultCellStyle.ForeColor = Color.Black;

                }
                if (disabled == 1)
                {
                    row.Cells["Str_Status"].Style.BackColor = Color.Silver;
                    row.Cells["Str_Status"].Style.ForeColor = Color.White;
                    row.DefaultCellStyle.ForeColor = Color.Silver;

                }
            }
            catch { }
        }

        private void Status_ToolStripMenuItem_CheckedChanged(object sender, EventArgs e)
        {
            //dgv.Columns["Str_Status"].Visible = Status_ToolStripMenuItem.Checked;
        }
        void Show_And_Hide_Sub_Menu(ToolStripMenuItem elemnt, string columnName)
        {
            if (firstLoad)
                return;
            try
            {
                elemnt.Checked = !elemnt.Checked;
                dgv.Columns[columnName].Visible = elemnt.Checked;

                //dgv.Columns["ProfileName"].DisplayIndex = dgv.Columns.Count - 1;
                //dgv.Columns["UserName"].DisplayIndex=dgv.Columns.Count-1;
                //dgv.Columns["Str_Status"].DisplayIndex = dgv.Columns.Count - 1;

                //Select_First_DGV();
                  
                SaveFromState();
                //Update_Setting_In_DB_2(elemnt.Checked.ToString(), nameSetting);
            }
            catch { }
        }
        private void Change_Items_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if(firstLoad) return;
            ToolStripMenuItem elm = sender as ToolStripMenuItem;
            Show_And_Hide_Sub_Menu((ToolStripMenuItem)sender, elm.Tag.ToString());

            //Select_First_DGV();
        }

        private void timer_SideBar_Tick(object sender, EventArgs e)
        {
            if (Hided)
            {
                //Spanel.Width = PW;
                timer_SideBar.Stop();
                Hided = false;
                this.Refresh();

                //Spanel.Width = Spanel.Width + 60;
                //if (Spanel.Width >= PW)
                //{
                //    timer_SideBar.Stop();
                //    Hided = false;
                //    this.Refresh();
                //}

            }
            else
            {
                //Spanel.Width = 0;
                timer_SideBar.Stop();
                Hided = true;
                this.Refresh();

                //Spanel.Width = Spanel.Width - 60;
                //if (Spanel.Width <= 0)
                //{
                //    timer_SideBar.Stop();
                //    Hided = true;
                //    this.Refresh();
                //}
            }

        }

        private void btn_Collaps_Click(object sender, EventArgs e)
        {
            //Spanel.Visible = !Spanel.Visible;
            //if (Hided)
            //{
            //    btn_Collaps.Text = "H\nI\nD\nD\nE";
            //}
            //else
            //{
            //    btn_Collaps.Text = "S\nH\nO\nW";
            //}
            //timer_SideBar.Start();
        }

        private void pnlClientArea_Resize(object sender, EventArgs e)
        {

        }

        bool IsCancel = false;
        [Obsolete]
        private void btnRefresh_Click(object sender, EventArgs e)
        {
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله السابقة");
                return;
            }
            if (RJMessageBox.Show("سوف يقوم بجلب الكروت من الروتر قد ياخذ وقت في الجلب بحسب السرعه وكميه الكروت", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
                return;

            Thread thread = new Thread(Refresh_formMK);
            Global_Variable.StartThreadProcessFromMK = true;
            thread.Start();

            //ThreadStart theprogress1 = new ThreadStart(() => Update_UserMan_Session());
            //Thread startprogress1 = new Thread(theprogress1);
            //startprogress1.Name = "Update Swssion";
            //startprogress1.Start();

            //if (Global_Variable.StartThreadProcessFromMK)
            //{
            //    RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله ");
            //    return;
            //}
            //if (RJMessageBox.Show("سوف يقوم بجلب الكروت من الروتر قد ياخذ وقت في الجلب بحسب السرعه وكميه الكروت", "تنبية", MessageBoxButtons.YesNo) == DialogResult.No)
            //    return;
            //Thread thread = new Thread(Refresh_formMK);
            //Global_Variable.StartThreadProcessFromMK=true;
            //thread.Start();


            //using (Form_WaitForm frm = new Form_WaitForm(Refresh_formMK))
            //{
            //    Global_Variable.StartThreadProcessFromMK = true;
            //    frm.ShowDialog();
            //    if (frm.IsCancel)
            //    {
            //        //IsCancel = true;
            //        if (MkSourceUMsers != null) MkSourceUMsers.MkConnClose();
            //        //if(MkSourcePYment!=null)   MkSourcePYment.MkConnClose();
            //        //   //MkSourcePYment.MkConnClose();
            //        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء عملية جلب البيانات");
            //        //   loadData();
            //    }
            //    if (frm.IsCancel)
            //    {
            //        //if (MkSourceUMsers != null) MkSourceUMsers.MkConnClose();
            //        if (MkSourcePYment != null) MkSourcePYment.MkConnClose();
            //        //MkSourcePYment.MkConnClose();
            //        //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء عملية جلب البيانات");
            //        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء عملية جلب البيانات");

            //    }
            //    loadData();
            //    //if(IsCancel==false)
            //    //Global_Variable.StartThreadProcessFromMK = false;

            //    //IsCancel = false;
            //}
        }


        SourceCardsUserManager_fromMK MkSourceUMsers;
        SourcePymentUserManager_fromMK MkSourcePYment;

        [Obsolete]
        private void Refresh_formMK()
        {
            

            try
            {
                UserManagerProcess userManagerProcess = new UserManagerProcess();
                if (!IsCancel)
                {
                    MkSourceUMsers = new SourceCardsUserManager_fromMK();
                    Global_Variable.Update_Um_StatusBar(false, true, 50, "", "يقوم الان بجلب كروت اليوزمنجر من النظام");
                    Global_Variable.Source_Users_UserManager = MkSourceUMsers.Get_UM_user2();
                    userManagerProcess.Syn_UM_Users_to_LocalDB();
                    ////Global_Variable.Source_Users_UserManager = SourceCardsUserManager_fromMK.Get_UM_user();
                    ///
                }
                if (!IsCancel)
                {
                    MkSourcePYment = new SourcePymentUserManager_fromMK();
                    Global_Variable.Update_Um_StatusBar(false, true, 80, "", "يقوم الان بجلب المبيعات والحسابات من الروتر");
                    Global_Variable.Source_Pyment_UserManager = MkSourcePYment.get_Pyment_user2();
                    //Global_Variable.Source_Pyment_UserManager = SourcePymentUserManager_fromMK.get_Pyment_user();
                    Global_Variable.Update_Um_StatusBar(false, true, 90, "", "يتم الان مزامنه البيانات ");
                    //userManagerProcess.Syn_Pyments_to_LocalDB();
                    userManagerProcess.Syn_Pyments_to_LocalDB();

                }

                loadData();


                if (!IsCancel)
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم جلب البيانات من النظام");

                Global_Variable.StartThreadProcessFromMK = false;
                IsCancel = false;


                //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                //  (System.Windows.Forms.MethodInvoker)delegate ()
                //  {
                //      LoadDataGridviewData();
                //  });

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false; }

            Global_Variable.StartThreadProcessFromMK = false;
        }
        [Obsolete]

        private void btn_search_Router_Click(object sender, EventArgs e)
        {
            Cursor.Current = Cursors.WaitCursor;
            if (Global_Variable.StartThreadProcessFromMK)
            {
                RJMessageBox.Show("هناك عمليه اخرى علي الروتر قيد التنفيذ الرجاء الانتظار حتى اكتمال العميله ");
                return;
            }
            string name = txt_search.Text;
            //var task = Task.Run(()=>Search_formMK(name)) ;
            Task<bool> task = Task.Run(() => Search_formMK(name));
            if (task.Result)
            {
                dgv.DataSource = Local_DA.Load<UmUser>($"select * from UmUser where UserName='{name}'");

                //using (var db = dbFactory.Open())
                //{
                //    //dgv.DataSource = db.Select<UmUser>(x => x.UserName == name);
                //}
            }
            Cursor.Current = Cursors.Default;
            RJMessageBox.Show("sarch commplate");
            return;
            ThreadStart theprogress1 = new ThreadStart(() => Search_formMK(name));
            Thread startprogress1 = new Thread(theprogress1);
            startprogress1.Name = "Search Users";

            Global_Variable.StartThreadProcessFromMK = true;
            //Global_Variable.Update_Um_StatusBar(false, true, 50, "", "البحث في النظام عن المستخدم " + txt_search.Text + "");
            startprogress1.Start();
            Cursor.Current = Cursors.Default;

        }

        [Obsolete]
        private bool Search_formMK(string name)
        {
            //Cursor.Current = Cursors.WaitCursor;
            bool status = false;
            try
            {
                List<UmUser> users = new List<UmUser>();
                List<SourceCardsUserManager_fromMK> user = SourceCardsUserManager_fromMK.Get_one_UM_User(name);

                if (user.Count > 0)
                {
                    status = true;
                    List<SourcePymentUserManager_fromMK> pyment = SourcePymentUserManager_fromMK.get_Pyment_user(name);
                    //UserManagerProcess ump = new UserManagerProcess();


                }
                //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                //   (System.Windows.Forms.MethodInvoker)delegate ()
                //   {
                //       using (var db = dbFactory.Open())
                //       {
                //           dgv.DataSource = db.Select<UmUser>(x => x.UserName == name);
                //       }
                //   });
                Global_Variable.StartThreadProcessFromMK = false;

                //return;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false; }
            return status;
            Global_Variable.StartThreadProcessFromMK = false;
            //Cursor.Current = Cursors.Default;
        }
        [Obsolete]
        private bool Search_Live_User(string name)
        {
            //Cursor.Current = Cursors.WaitCursor;
            bool status = false;
            try
            {
                List<UmUser> users = new List<UmUser>();
                List<SourceCardsUserManager_fromMK> user = SourceCardsUserManager_fromMK.Get_one_UM_User(name);

                if (user.Count > 0)
                {
                    status = true;
                    List<SourcePymentUserManager_fromMK> pyment = SourcePymentUserManager_fromMK.get_Pyment_user(name);
                    //UserManagerProcess ump = new UserManagerProcess();


                }
                //Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                //   (System.Windows.Forms.MethodInvoker)delegate ()
                //   {
                //       using (var db = dbFactory.Open())
                //       {
                //           dgv.DataSource = db.Select<UmUser>(x => x.UserName == name);
                //       }
                //   });
                Global_Variable.StartThreadProcessFromMK = false;

                //return;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); Global_Variable.StartThreadProcessFromMK = false; }
            return status;
            Global_Variable.StartThreadProcessFromMK = false;
            //Cursor.Current = Cursors.Default;
        }

        private void btnRefresh_DB_Click(object sender, EventArgs e)
        {
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();

            Select_First_DGV();
        }

        private void pnlClientArea_Paint(object sender, PaintEventArgs e)
        {

        }

        private void View_Hide_toolStripMenuItem_Click(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void btn_apply_Click(object sender, EventArgs e)
        {
            //btn_Collaps_Click(sender, e);
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }



        private void ToggleButton_ByCountProfile_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            if (ToggleButton_ByCountProfile.Checked == false && ToggleButton_ByCountSession.Checked == false)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                firstLoad = false;
            }

            if ((ToggleButton_ByCountProfile.Checked))
            {
                if (ToggleButton_ByCountSession.Checked)
                {
                    firstLoad = true;
                    ToggleButton_ByCountSession.Checked = false;
                    firstLoad = false;
                }


                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }
            else
            {
                try
                {
                    if (dgv.Columns["CountProfile"].Visible)
                    {
                        dgv.Columns["CountProfile"].Visible = false;
                        Count_profile_ToolStripMenuItem.Checked = false;
                    }
                }
                catch { }

                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();

            }




            return;
            //if (ToggleButton_ByCountSession.Checked)
            //{
            //    List<UmUser> users = null;
            //    //var dbFactory = new OrmLiteConnectionFactory(Sql_DataAccess.connection_string, Sql_DataAccess.Get_Provider());
            //    using (var db = dbFactory.Open())
            //    {
            //        var q = db.From<UmUser>()
            //                    .LeftJoin<UmPyment>((k, a) => k.Sn_Name == a.Fk_Sn_Name)
            //                    //.LeftJoin<UmSession>((k, a) => k.Sn_Name == a.Fk_Sn_Name)
            //                    .GroupBy(x => x.Sn_Name)
            //                    .OrderBy(x => x.Sn_Name)
            //                    .Where(x => x.DeleteFromServer == 0)
            //                    .Select<UmUser, UmPyment>((t0, t1) => new
            //                    {
            //                        t0,
            //                        ValidityLimit = Sql.Sum(t1.ProfileValidity),
            //                        UptimeLimit = Sql.Sum(t1.ProfileUptimeLimit),
            //                        TransferLimit = Sql.Sum(t1.ProfileTransferLimit),
            //                        CountProfile = Sql.Count(t1.Sn_Name),
            //                        //CountSession = Sql.Count(t2.Id)
            //                    });
            //        q.PrintDump();
            //        users = db.SqlList<UmUser>(q);

            //        dgv.DataSource = users.AsEnumerable()
            //                        .Where(std => std.CountSession > 0)
            //                        .OrderByDescending(x => x.CountSession).ToList();

            //        CountSession_ToolStripMenuItem.Checked = true;
            //        dgv.Columns["CountProfile"].Visible = true;
            //        dgv.Columns["CountProfile"].DisplayIndex = 0;
            //    }
            //}
            //else
            //{
            //    btnRefresh_DB_Click(sender, e);
            //    Count_profile_ToolStripMenuItem.Checked = false;
            //    dgv.Columns["CountProfile"].Visible = false;
            //}

        }

        private void btn_Filter_Click(object sender, EventArgs e)
        {
            sideMenu();
            //btn_Collaps_Click(sender, e);
        }
        
        void sideMenu()
        {
            //return;
            if (rjPanel_back_side.Width >= 200)
            {
                rjPanel_back_side.Width = 0;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                //rjPane_Top.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Location.Y);
            }
            else
            {
                rjPanel_back_side.Width = utils.Control_Mesur_DPI(260);
                //rjPanel_back_side.Width = 260;
                panel1.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;
                //rjPane_Top.Width = pnlClientArea.Width - rjPanel_back_side.Width - 25;

                rjPanel_back_side.Location = new Point(panel1.Width + 13, panel1.Location.Y - 1);
                //rjPane_Top.Location = new Point(panel1.Width + 13, 6);
                //rjPanel_back_side.Location = new Point(panel1.Width + 15, panel1.Height-(panel1.Height - dgv.Location.Y));
            }
            rjPanel_Page.Width = dgv.Width;
            panel1.Refresh();
            this.Refresh();
        }

        private void txt_search_onTextChanged(object sender, EventArgs e)
        {
            return;
            if (firstLoad)
                return;

            if (txt_search.Text == "")
                loadData();
        }

        private void dgv_CellFormatting(object sender, DataGridViewCellFormattingEventArgs e)
        {

            int status = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Status"].Value);
            int disabled = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["Disabled"].Value);
            int DeleteFromServer = Convert.ToInt32(dgv.Rows[e.RowIndex].Cells["DeleteFromServer"].Value);

            if (DeleteFromServer == 1)
            {
                dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = utils.Dgv_DarkColor;
                
            }

            else if (this.dgv.Columns[e.ColumnIndex].Name == "Str_Status")
            {
                if (status == 1)
                {
                    e.CellStyle.BackColor = Color.Red;
                    e.CellStyle.ForeColor = Color.White;
                }
                else if (status == 2)
                {
                    e.CellStyle.BackColor = Color.Yellow;
                    e.CellStyle.ForeColor = Color.Black;
                    //row.DefaultCellStyle.ForeColor = Color.Yellow;

                }
                else if (status == 3)
                {
                    e.CellStyle.BackColor = Color.Black;
                    e.CellStyle.ForeColor = Color.White;
                    //row.DefaultCellStyle.ForeColor = Color.Black;

                }
                if (disabled == 1)
                {
                    dgv.Rows[e.RowIndex].DefaultCellStyle.ForeColor = Color.Silver;

                    e.CellStyle.BackColor = Color.Silver;
                    e.CellStyle.ForeColor = Color.White;
                    //e.DefaultCellStyle.ForeColor = Color.Silver;
                }

            }
            return;

        }

        private void Restor_ColumnToolStripMenuItem_Click(object sender, EventArgs e)
        {
            Init_dgv_to_Default();
        }

        private void LoadData_ByCountSession()
        {
            if (ToggleButton_ByCountSession.Checked)
            {
                dgv.DataSource = null;
                List<UmUser> users = null;


                try
                {
                    string sql_where = "";
                    string sql_select = "";
                    string orderby = " ORDER BY ";
                    if (fillter_type == "From_Server")
                        sql_where = " where UmUser.DeleteFromServer = 0 ";

                    if (fillter_type == "From_RB_Archive")
                        sql_where = "  ";

                    orderby = " ORDER BY count(UmSession.Fk_Sn_Name)  ";
                    if (CheckBox_orderBy.Check)
                        orderby = orderby + " DESC ";

                    sql_where = Get_Fillter(sql_where);

                    sql_select = $"select UmUser.*,count(UmSession.Fk_Sn_Name) as CountSession from UmUser  LEFT JOIN UmSession  ON UmUser.Sn_Name = UmSession.Fk_Sn_Name  {sql_where}  GROUP by UmUser.Sn_Name {orderby}  LIMIT  {PageSize} OFFSET {(currentPageindex - 1) * PageSize} ";
                    totalRows = (int)Local_DA.Get_int_FromDB("select count(*) from UmUser  " + sql_where);
                    totalPages = (int)Math.Ceiling((double)totalRows / PageSize);
                    txtTotalPages.Text = totalPages.ToString();
                    txtCurrentPageindex.Text = currentPageindex.ToString();
                    txtAllCountRows.Text = totalRows.ToString();
                    var umS = Local_DA.Load<UmUser>(sql_select);
                    //var umS = db.SqlList<UmUser>(sql_select);
                    users = umS;
                }
                catch (Exception ex) { RJMessageBox.Show(ex.Message); }

                dgv.DataSource = users;

                //loadDgvState();

                try { dgv.Columns["CountSession"].Width = 150; } catch { }
                try { dgv.Columns["CountSession"].DisplayIndex = 0; } catch { }
                try { dgv.Columns["CountSession"].Visible = true; } catch { }
                CountSession_ToolStripMenuItem.Checked = true;
                update_select_DGV();
            }
            else
            {
                CountSession_ToolStripMenuItem.Checked = false;
                dgv.Columns["CountSession"].Visible = false;
                loadData();
            }
        }

        private void ToggleButton_ByCountSession_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (ToggleButton_ByCountProfile.Checked == false && ToggleButton_ByCountSession.Checked == false)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                firstLoad = false;
            }

            if ((ToggleButton_ByCountSession.Checked))
            {
                if (ToggleButton_ByCountProfile.Checked)
                {
                    firstLoad = true;
                    ToggleButton_ByCountProfile.Checked = false;
                    firstLoad = false;
                }

                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }
            else
            {
                try
                {
                    if (dgv.Columns["CountSession"].Visible)
                    {
                        dgv.Columns["CountSession"].Visible = false;
                        CountSession_ToolStripMenuItem.Checked = false;
                    }
                }
                catch { }


                currentPageindex = 1;
                totalPages = 0;
                totalRows = 0;
                loadData();
            }



            //LoadData_ByCountSession();
        }
        //[Obsolete]
        public void loadData()
        {

            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    LoadDataGridviewData();
                    Select_First_DGV();
                     
                });
        }

        public bool isProcessRun = false;
        string typeProcess = "DeleteFromServer";

        #region DeleteSelectRows
        [Obsolete]
        public void DeleteSelectRows()
        {
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    DelectUser_ByID();
                });
        }

        [Obsolete]
        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }

            DialogResult result = RJMessageBox.Show("هل متاكد من حذف الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            typeProcess = "DeleteFromServer";
            //isProcessRun = true;
            ThreadStart theprogress;
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف الكروت المحدده من اليوزمنجر");
            if (Global_Variable.Mk_resources.version >= 7)
            {

                theprogress = new ThreadStart(() => DisableUser_ByID());
                Thread startprogress = new Thread(theprogress);
                startprogress.Start();
            }
            else
            {
                if (dgv.SelectedRows.Count <= 20)
                    theprogress = new ThreadStart(() => DisableUser_ByID());
                else
                    theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

                Thread startprogress = new Thread(theprogress);
                startprogress.Start();
            }
        }

        [Obsolete]
        public void DelectUser_ByID()
        {
            isProcessRun = true;
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                //HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmUser)row.DataBoundItem);
                        _DataGridViewRow.Add(row);
                    }
                }
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmUser> ResUsers = mk_DataAccess.Delete_UserManager_ByID(dbUser);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                isProcessRun = false;
                    is_Delete_FromArchive = false;

                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض العناصر");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Local_DA.Set_Delet_fromServer("UmUser", dbUser);
                    foreach (var itm in _DataGridViewRow)
                    {

                        itm.Cells["DeleteFromServer"].Value = 1;

                    }
                   

                    if (is_Delete_FromArchive)
                    {
                        lock (Sql_DataAccess.Lock_localDB)
                        {
                            int delete = Local_DA.Remove_UmUser_FormDB(dbUser);
                        }
                    }
                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                 (System.Windows.Forms.MethodInvoker)delegate ()
                 {
                     dgv.Refresh();
                 });
                    //loadData();
                }

                isProcessRun = false;
                is_Delete_FromArchive = false;

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; }

        }

        [Obsolete]
        public void DelectUser_ByScript(int UserCount)
        {
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                string strUser = "";
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmUser)row.DataBoundItem);
                        strUser += "\"" + row.Cells["UserName"].Value + "\"" + ",";
                        _DataGridViewRow.Add(row);
                    }
                }
                strUser = strUser.TrimEnd(new char[] { ' ',';',' ',',' }); 
                string script = ""; 
                if (Global_Variable.Mk_resources.version >= 7)
                {
                    script = "{:local MyUser [:toarray (" + strUser + ")];" + ":for i from=0 to=([:len $MyUser]-1) do={:do {/user-manager user remove numbers=[:pick $MyUser $i] } on-error={:put \"erorr\"};}}";
                }
                else
                   script = "{:local MyUser [:toarray (" + strUser + ")];" + ":for i from=0 to=([:len $MyUser]-1) do={:do {/tool user-manager user remove numbers=[:pick $MyUser $i] } on-error={:put \"erorr\"};}}";


                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, true);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    isProcessRun = false;
                    is_Delete_FromArchive = false;


                    return;
                }

                RJMessageBox.Show("انتهى من عملية الحذف");
                Local_DA.Set_Delet_fromServer("UmUser", dbUser);

                Global_Variable.Uc_StatusBar.lblDescription.Invoke((System.Windows.Forms.MethodInvoker)delegate ()
                {
                    foreach (var itm in _DataGridViewRow)
                        itm.Cells["DeleteFromServer"].Value = 1;
                    dgv.Refresh();
                });

                Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم حذف {UserCount}  كرت من اليوزمنجر");
                isProcessRun = false;
                is_Delete_FromArchive = false;


            }
                    
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; is_Delete_FromArchive = false; }

        }

        [Obsolete]
        public void RunProccess_ByScript(int UserCount)
        {
            if (Global_Variable.Mk_resources.version >= 7)
            {
                DisableUser_ByID();
                return;
            }

            isProcessRun = true;
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                string strUser = "";
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<UmUser> ListUser = new List<UmUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmUser)row.DataBoundItem);
                        ListUser.Add((UmUser)row.DataBoundItem);
                        strUser += "\"" + row.Cells["UserName"].Value + "\"" + ",";
                        _DataGridViewRow.Add(row);
                    }
                }

                if (list_user.Count <= 0)
                {
                    RJMessageBox.Show("لا يوجد كروت  محدده");
                    isProcessRun=false;
                    is_Delete_FromArchive = false;
                    return;
                }
                strUser = strUser.TrimEnd(new char[] { ' ', ';', ' ', ',' });
                string script = "";string msg = "";


                if (typeProcess == "DeleteFromServer")
                {
                    msg = "حذف";

                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do {/tool user-manager user remove numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";
                    }
                    else
                    {
                        DelectUser_ByID();
                        isProcessRun = false;
                        is_Delete_FromArchive = false;
                        //RJMessageBox.Show("غير مدعوم في مايكروتك الاصدار 7");
                        return;
                    }
                }
                else if (typeProcess == "Enabled")
                {
                    msg = "تفعيل";
                    script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do {/tool user-manager user enable numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";

                    if (Global_Variable.Mk_resources.version >= 7)
                        script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do {/user-manager/user/enable numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";


                }
                else if (typeProcess == "Disabled")
                {
                    msg = "تعطيل";
                    script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do {/tool user-manager user disable numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;}}";

                    if (Global_Variable.Mk_resources.version >= 7)
                        script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do {/user-manager/user/disable numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";

                }

                else if (typeProcess == "RestCards")
                {
                    msg = "تصفير عداد ";
                    script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do { /tool user-manager user reset-counters numbers=[:pick $usr $i] } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";

                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        isProcessRun = false;
                        is_Delete_FromArchive = false;
                        RJMessageBox.Show("غير مدعوم في مايكروتك الاصدار 7");
                        return;
                    }
                }

                else if (typeProcess == "caller_bind")
                {
                    msg = "ربط";
                    script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do { /tool user-manager user set numbers=[:pick $usr $i] caller-id-bind-on-first-use=yes } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";

                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        DisableUser_ByID();
                    }
                }
                else if (typeProcess == "Remove_caller_bind")
                {
                    msg = "الغاء ربط";
                    script = "{:local usr [:toarray (" + strUser + ")];:local us ;" + ":for i from=0 to=([:len $usr]-1) do={:do { /tool user-manager user set numbers=[:pick $usr $i] caller-id-bind-on-first-use=no caller-id=\"\" } on-error={:set us ($us.\"|\".[:pick $usr $i]);:put $us;};}}";

                    if (Global_Variable.Mk_resources.version >= 7)
                    {
                        DisableUser_ByID();
                    }
                }

                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, true);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    isProcessRun = false;
                    is_Delete_FromArchive = false;
                    return;
                }
                string[] Res_split = res["result"].Split(new string[] { ";" }, StringSplitOptions.None);
                string[] user_split = Res_split[2].Split(new string[] { "|" }, StringSplitOptions.None);  //====== check if error add user ========
                List<string> userEror = user_split.ToList();
                userEror.RemoveAt(0);
                //if (user_split.Length > 1)
                //{
                //    for (int i = 1; i < user_split.Length; i++)
                //    //    new_Generate_Cards.dicUser.Remove(user_split[i]);
                //    ////========= اضافة كروت جديده بدل الذي تكررت وحصل خطاء عند الاضافة السابقة =========
                //    //new_Generate_Cards.dicUser = GenerateIfLastErorr(new_Generate_Cards.dicUser, user_split.Length - 1, cLS_Genrate_Cards);
                //    foreach (var itm in _DataGridViewRow)
                //    {
                //        if(itm.Cells["UserName"].Value.ToString() == user_split[i])
                //            {
                //                _DataGridViewRow.Remove(itm);
                //            }
                //    }
                //}

                //Local_DA.Process_Cards_OnDB("UmUser", dbUser, typeProcess);

                foreach (var itm in _DataGridViewRow)
                {
                    string match = userEror.FirstOrDefault(stringToCheck => stringToCheck.Contains(itm.Cells["UserName"].Value.ToString()));
                    if (match != null)
                    {
                        UmUser ums = ListUser.Find(x => x.UserName == match);
                        dbUser.Remove(ums);
                        continue;
                    }

                    if (typeProcess == "Disabled")
                        itm.Cells["Disabled"].Value = 1;
                    else if (typeProcess == "Enabled")
                        itm.Cells["Disabled"].Value = 0;
                    else if (typeProcess == "DeleteFromServer")
                    {
                        
                        itm.Cells["DeleteFromServer"].Value = 1;
                    }
                    else if (typeProcess == "RestCards")
                    {
                        itm.Cells["UptimeUsed"].Value = 0;
                        itm.Cells["UploadUsed"].Value = 0;
                        itm.Cells["DownloadUsed"].Value = 0;
                        itm.Cells["Status"].Value = 0;
                    }
                    else if (typeProcess == "caller_bind")
                    {
                        itm.Cells["CallerMac"].Value = "bind";
                    }
                    else if (typeProcess == "Remove_caller_bind")
                    {
                        itm.Cells["CallerMac"].Value = "";
                    }
                }


                Local_DA.Process_Cards_OnDB("UmUser", dbUser, typeProcess);

                if (is_Delete_FromArchive)
                {
                    lock (Sql_DataAccess.Lock_localDB)
                    {
                        int delete = Local_DA.Remove_UmUser_FormDB(dbUser);
                    }
                }

                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                   (System.Windows.Forms.MethodInvoker)delegate ()
                   {
                       dgv.Refresh();
                   });

                Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم {msg} {UserCount - userEror.Count}  كرت من اليوزمنجر");

                isProcessRun = false;
                is_Delete_FromArchive = false;

            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; is_Delete_FromArchive = false; }

        }
        #endregion


        #region DisableSelectRows
        [Obsolete]
        private void btnDisable_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تعطيل الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }

            typeProcess = "Disabled";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتعطيل الكروت المحدده من اليوزمنجر");
            //isProcessRun = true;
            ThreadStart theprogress;
            if (dgv.SelectedRows.Count <= 20)
                theprogress = new ThreadStart(() => DisableUser_ByID());
            else
                theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();



            ////DisableUser_ByID();
            //using (Form_WaitForm frm = new Form_WaitForm(DisableUser_ByID))
            //{
            //    frm.ShowDialog();
            //}
            ////Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم تعطيل الكروت المحدده من اليوزمنجر");

        }

        [Obsolete]
        public void DisableSelectRows()
        {
            Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                (System.Windows.Forms.MethodInvoker)delegate ()
                {
                    DisableUser_ByID();
                });
        }

        [Obsolete]
        public void DisableUser_ByID()
        {
            isProcessRun = true;
            string msg = "";
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        dbUser.Add((UmUser)row.DataBoundItem);
                        _DataGridViewRow.Add(row);
                    }
                }

                if(dbUser.Count<=0)
                {
                    isProcessRun = false;
                    is_Delete_FromArchive = false;
                    RJMessageBox.Show("حدد كروت الروتر");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تم الغاء العملية");

                    return;
                }

                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmUser> ResUsers = mk_DataAccess.Process_UserManager_ByID(dbUser, typeProcess);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " حدث خطا عن تنفيذ العملية");
                    isProcessRun = false;
                    is_Delete_FromArchive = false;
                    //RJMessageBox.Show("تمت الم");
                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count)
                        Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في تنفيذ العملية علي بعض الكروت قم بتحديث الكروت من الروتر");

                    //else
                    //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Local_DA.Process_Cards_OnDB("UmUser", dbUser, typeProcess);

                    foreach (var itm in _DataGridViewRow)
                    {
                        if (typeProcess == "Disabled")
                        {
                            msg = "تعطيل";
                            itm.Cells["Disabled"].Value = 1;
                        }
                        else if (typeProcess == "Enabled")
                        {
                            msg = "تفعيل";
                            itm.Cells["Disabled"].Value = 0;
                        }
                        else if (typeProcess == "DeleteFromServer")
                        {
                            msg = "حذف";
                            itm.Cells["DeleteFromServer"].Value = 1;
                        }
                        else if (typeProcess == "RestCards")
                        {
                            itm.Cells["UptimeUsed"].Value = 0;
                            itm.Cells["UploadUsed"].Value = 0;
                            itm.Cells["DownloadUsed"].Value = 0;
                            itm.Cells["Status"].Value = 0;
                        }
                        else if (typeProcess == "caller_bind")
                        {
                            msg = "ربط ماك ";
                            itm.Cells["CallerMac"].Value = "bind";
                        }
                        else if (typeProcess == "Remove_caller_bind")
                        {
                            msg = "الغاء ربط ماك";
                            itm.Cells["CallerMac"].Value = "";
                        }
                    }


                    if (is_Delete_FromArchive)
                    {
                        lock (Sql_DataAccess.Lock_localDB)
                        {
                            int delete = Local_DA.Remove_UmUser_FormDB(dbUser);
                        }
                    }

                    Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                       (System.Windows.Forms.MethodInvoker)delegate ()
                       {
                           dgv.Refresh();
                       });
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", $"تم {msg} { ResUsers.Count}  كرت من اليوزمنجر");
                    //Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");


                    //loadData();
                }

                isProcessRun = false;
                is_Delete_FromArchive = false;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message);isProcessRun = false; }

        }

        #endregion

        [Obsolete]
        private void btnEnable_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تفعيل الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "Enabled";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتفعيل الكروت المحدده من اليوزمنجر");
            isProcessRun = true;
            ThreadStart theprogress;
            if (dgv.SelectedRows.Count <= 20)
                theprogress = new ThreadStart(() => DisableUser_ByID());
            else
                theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();
        }

        private void Copy_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (ActiveCell != null && ActiveCell.Value != null)
                Clipboard.SetText(ActiveCell.Value.ToString());

            ActiveCell = null;
        }

        private void Copy_AllRowToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (this.dgv.GetCellCount(DataGridViewElementStates.Selected) > 0)
            {
                try
                {
                    Clipboard.SetDataObject(this.dgv.GetClipboardContent());
                }
                catch (System.Runtime.InteropServices.ExternalException)
                {
                }

                ActiveCell = null;
            }
        }

        private void dgv_MouseDown(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                DataGridView.HitTestInfo hittestinfo = dgv.HitTest(e.X, e.Y);

                if (hittestinfo != null && hittestinfo.Type == DataGridViewHitTestType.Cell)
                {
                    ActiveCell = dgv[hittestinfo.ColumnIndex, hittestinfo.RowIndex];
                    ActiveCell.Selected = true;
                    //contextMenu.Show(dgvUserManager, new Point(e.X, e.Y));
                }

            }
        }
        void export_to_text(string choice)
        {
            if (dgv.SelectedRows.Count <= 0)
            {
                MessageBox.Show("لم تقم بتحديد كروت لتصديرها");
                return;
            }
            try
            {
                SaveFileDialog sfd = new SaveFileDialog();
                sfd.Filter = "txt (*.txt)|*.txt";
                //string FileName = "";
                sfd.FileName = $"Users-manager_{DateTime.Now.ToString("yyyyMMddhhmmss")}.txt";
                //sfd.FileName = "users.txt";
                string FileName = sfd.FileName;
                if (sfd.ShowDialog() == DialogResult.OK)
                {
                    FileName = sfd.FileName;
                    if (File.Exists(FileName))
                    {
                        try
                        {
                            File.Delete(FileName);
                        }
                        catch (IOException ex)
                        {
                            MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);
                        }
                    }
                    FileStream fs = File.Create(FileName);
                    fs.Close();
                    TextWriter writeFile = new StreamWriter(FileName, false, Encoding.UTF8);
                    writeFile.Close();
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        UmUser um = (UmUser)dr.DataBoundItem;
                        string text = "";
                        //string name = UM.UserName + " ";
                        //string pass = UM.Password + " ";
                        //string profile = UM.ProfileName + " ";
                        //string sp = UM.SpCode+ " ";

                        string name = "";
                        string pass = "";
                        string profile = "";
                        string sp = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            name = um.UserName.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.Password))
                            pass = um.Password.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            profile = um.ProfileName.ToString() + " ";
                        if (!string.IsNullOrEmpty(um.SpCode))
                            sp = um.SpCode.ToString() + " ";


                        if (choice == "name")
                            text += name;
                        if (choice == "name_pass")
                            text += name + pass;
                        if (choice == "name_pass_profile")
                            text += name + pass + profile;
                        if (choice == "name_pass_profile_sp")
                            text += name + pass + profile + sp;

                        //text += text + "\n";
                        //try { text += dgv.Rows[i].Cells["location"].Value.ToString() + " "; } catch { }

                        File.AppendAllText(FileName, text + "\n", Encoding.UTF8);

                        i = i + 1;
                    }
                    MessageBox.Show("تم التصدير بنجاح");
                    try
                    {
                        System.Diagnostics.Process.Start(FileName);
                    }
                    catch { }
                }
            }
            catch (IOException ex)
            {
                MessageBox.Show(ex.Message.ToString());
            }
        }

        private void تصديرجميعالاعمدةفيالجدولToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass_profile_sp");
        }
        private void نسخالاسمفقطToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name");
        }
        private void نسخالاسمكلمةالسرToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass");
        }
        private void نسخالاسمكملةكلمةالسرالباقةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            export_to_text("name_pass_profile");
        }
        void CreateExcel()
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "csv (*.csv)|*.csv";
            sfd.FileName = $"Users-manager_{DateTime.Now.ToString("yyyyMMddhhmmss")}.csv";
            //sfd.FileName = "users.txt";
            string FileName = sfd.FileName;

            if (sfd.ShowDialog() == DialogResult.OK)
            {
                FileName = sfd.FileName;
                if (File.Exists(FileName))
                {
                    try
                    {
                        File.Delete(FileName);
                    }
                    catch (IOException ex)
                    {
                        MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);

                        return;
                    }
                }
                FileStream fs = File.Create(FileName);
                fs.Close();

                string file_ExceltName = FileName;
                //file_ExceltName = FileName;
                try
                {
                    string[] outputCsv = new string[dgv.SelectedRows.Count];
                    //for (int i = 0; i < NewUser.Length; i++)
                    //{
                    //    outputCsv[i] += "=\"" + NewUser[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + Newpassword[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + profile + "\",";
                    //    outputCsv[i] += "=\"" + sp + "\",";
                    //}
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        UmUser um = (UmUser)dr.DataBoundItem;

                        string UserName = "";
                        string Password = "";
                        string ProfileName = "";
                        string SpCode = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            UserName = um.UserName.ToString();
                        if (!string.IsNullOrEmpty(um.Password))
                            Password = um.Password.ToString();
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            ProfileName = um.ProfileName.ToString();
                        if (!string.IsNullOrEmpty(um.SpCode))
                            SpCode = um.SpCode.ToString();




                        //outputCsv[i] += UserName + ",";
                        //outputCsv[i] += Password + ",";
                        //outputCsv[i] += ProfileName + ",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }


                        outputCsv[i] += $"=\"{UserName ?? ""}\",=\"{Password ?? ""}\",=\"{ProfileName ?? ""}\",=\"{SpCode ?? ""}\"";

                        //outputCsv[i] +="=\""+ UserName
                        //           + ",=\"" + Password
                        //           + "\"," + ProfileName
                        //           + "," + SpCode;
                        //           //+ "," + cm.OrigDocAmt.ToString()
                        //           //+ "," + cm.CreateDate.ToShortDateString();


                        //outputCsv[i] += $"\"{UserName ?? ""}\",";
                        //outputCsv[i] += $"\"{Password ?? ""}\",";
                        //outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        //outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] += $"\"{UserName ?? ""}\",";
                        //outputCsv[i] += $"\"{Password ?? ""}\",";
                        //outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        //outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] +=$" \"{UserName}\",";
                        //outputCsv[i] += $" \"{Password}\",";
                        //outputCsv[i] += $" \"{ProfileName}\",";
                        //outputCsv[i] += $" \"{SpCode}\",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        i = i + 1;
                    }

                    //for (int i = 1; (i - 1) < dgvUserManager.Rows.Count; i++)
                    //{
                    //    for (int j = 0; j < columnCount; j++)
                    //    {
                    //        outputCsv[i] += dgvUserManager.Rows[i - 1].Cells[j].Value.ToString() + ",";
                    //    }
                    //}

                    File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                    //MessageBox.Show("Data Exported Successfully !!!", "Info");

                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                    }
                    catch { }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("export_execl :" + ex.Message);
                }



            }
        }

        void CreateExcel1()
        {
            SaveFileDialog sfd = new SaveFileDialog();
            sfd.Filter = "csv (*.csv)|*.csv";
            string FileName = sfd.FileName = $"UsersManager_{DateTime.Now.ToString("yyyyMMddhhmmss")}.csv";
            if (sfd.ShowDialog() == DialogResult.OK)
            {
                if (File.Exists(FileName))
                {
                    try
                    {
                        File.Delete(FileName);
                    }
                    catch (IOException ex)
                    {
                        MessageBox.Show(" لايوجد صلاحيه علي القرص او الملف قيد الاستخدام " + "\n" + ex.Message);
                        return;
                    }
                }
                FileStream fs = File.Create(FileName);
                fs.Close();

                string file_ExceltName = FileName;



                var sb = new StringBuilder();

                var headers = dgv.Columns.Cast<DataGridViewColumn>();
                sb.AppendLine(string.Join(",", headers.Select(column => "\"" + column.HeaderText + "\"").ToArray()));

                foreach (DataGridViewRow row in dgv.Rows)
                {
                    var cells = row.Cells.Cast<DataGridViewCell>();
                    sb.AppendLine(string.Join(",", cells.Select(cell => "\"" + cell.Value + "\"").ToArray()));
                }
                string textBoxExport = sb.ToString();

                System.IO.File.WriteAllText(file_ExceltName, textBoxExport, Encoding.UTF8);

                try
                {
                    System.Diagnostics.Process.Start(file_ExceltName);
                }
                catch { }
                return;







                //file_ExceltName = FileName;
                try
                {
                    string[] outputCsv = new string[dgv.SelectedRows.Count];
                    //for (int i = 0; i < NewUser.Length; i++)
                    //{
                    //    outputCsv[i] += "=\"" + NewUser[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + Newpassword[i].ToString() + "\",";
                    //    outputCsv[i] += "=\"" + profile + "\",";
                    //    outputCsv[i] += "=\"" + sp + "\",";
                    //}
                    int i = 0;
                    foreach (DataGridViewRow dr in dgv.SelectedRows)
                    {
                        UmUser um = (UmUser)dr.DataBoundItem;

                        string UserName = "";
                        string Password = "";
                        string ProfileName = "";
                        string SpCode = "";

                        if (!string.IsNullOrEmpty(um.UserName))
                            UserName = um.UserName.ToString();
                        if (!string.IsNullOrEmpty(um.Password))
                            Password = um.Password.ToString();
                        if (!string.IsNullOrEmpty(um.ProfileName))
                            ProfileName = um.ProfileName.ToString();
                        if (!string.IsNullOrEmpty(um.SpCode))
                            SpCode = um.SpCode.ToString();




                        //outputCsv[i] += UserName + ",";
                        //outputCsv[i] += Password + ",";
                        //outputCsv[i] += ProfileName + ",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        outputCsv[i] += $"\"{UserName ?? ""}\",";
                        outputCsv[i] += $"\"{Password ?? ""}\",";
                        outputCsv[i] += $"\"{ProfileName ?? ""}\",";
                        outputCsv[i] += $"\"{SpCode ?? ""}\",";


                        //outputCsv[i] +=$" \"{UserName}\",";
                        //outputCsv[i] += $" \"{Password}\",";
                        //outputCsv[i] += $" \"{ProfileName}\",";
                        //outputCsv[i] += $" \"{SpCode}\",";
                        //try { outputCsv[i] += SpCode + ","; } catch { }

                        i = i + 1;
                    }

                    //for (int i = 1; (i - 1) < dgvUserManager.Rows.Count; i++)
                    //{
                    //    for (int j = 0; j < columnCount; j++)
                    //    {
                    //        outputCsv[i] += dgvUserManager.Rows[i - 1].Cells[j].Value.ToString() + ",";
                    //    }
                    //}

                    File.WriteAllLines(file_ExceltName, outputCsv, Encoding.UTF8);
                    //MessageBox.Show("Data Exported Successfully !!!", "Info");

                    try
                    {
                        System.Diagnostics.Process.Start(file_ExceltName);
                    }
                    catch { }
                }
                catch (Exception ex)
                {
                    MessageBox.Show("export_execl :" + ex.Message);
                }



            }
        }


        private void ExportExcelToolStripMenuItem_Click(object sender, EventArgs e)
        {
            CreateExcel();
        }

        private void dgv_DoubleClick(object sender, EventArgs e)
        {

        }

        [Obsolete]
        private void dgv_CellDoubleClick(object sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex > -1)
                get_card_detail();
        }
        [Obsolete]
        private void get_card_detail()
        {
            foreach (DataGridViewRow dr in dgv.SelectedRows)
            {
                //UmUser Last_user = dr.DataBoundItem as UmUser;
                UmUser user = dr.DataBoundItem as UmUser;

                Form_CardsDetails form_CardsDetails = new Form_CardsDetails(user, fillter_type);
                form_CardsDetails.ShowDialog();

                //dgv.ClearSelection();
                //if (form_CardsDetails.Is_success)
                //{
                Global_Variable.Uc_StatusBar.lblDescription.Invoke(
                       (System.Windows.Forms.MethodInvoker)delegate ()
                       {
                           dgv.Refresh();
                       });
                //}
                return;
            }
           
            
            
            //if (f.isSucucess)
            //{
            //    try
            //    {
            //        DataRow dr = MyDataClass.SourceForPrint_UM_Users.AsEnumerable().Where(r => ((string)r["name"]).Equals(UserNameCard)).First();
            //        //MessageBox.Show(dr["name"].ToString()); caller_id
            //        foreach (DataGridViewRow row in dgvUserManager.SelectedRows)
            //        {
            //            if (!row.IsNewRow)
            //            {
            //                //MessageBox.Show(f.isSucucess.ToString());

            //                if (f.is_password_Change)
            //                {
            //                    row.Cells["كلمة السر"].Value = f.txt_pass.Text;
            //                    dr["password"] = f.txt_pass.Text;
            //                }
            //                if (f.is_Chbox_disable_Change)
            //                {
            //                    if (f.Chbox_disable.Checked == true)
            //                    {
            //                        row.Cells["الحالة"].Value = "معطل";
            //                        dr["status"] = "معطل";
            //                    }
            //                    else
            //                    {
            //                        if (dr["download_used"].ToString() != "0")
            //                        {
            //                            row.Cells["الحالة"].Value = "نشط";
            //                            dr["status"] = "نشط";
            //                        }
            //                        if (dr["download_used"].ToString() == "0")
            //                        {
            //                            row.Cells["الحالة"].Value = "انتظار";
            //                            dr["status"] = "انتظار";
            //                        }
            //                        if (dr["profile"].ToString() == "انتهى الرصيد")
            //                        {
            //                            row.Cells["الحالة"].Value = "انتهى الرصيد";
            //                            dr["status"] = "انتهى الرصيد";
            //                        }

            //                    }
            //                    DGV_User_Color();
            //                }
            //                if (f.is_txt_mac_Change && f.is_Chbox_firstUse_Change == false && f.Chbox_firstUse.Checked == false)
            //                {
            //                    //row.Cells["caller_id"].Value = f.txt_mac.Text;
            //                    dr["caller_id"] = f.txt_mac.Text;
            //                }
            //                if (f.is_Chbox_firstUse_Change)
            //                {
            //                    //row.Cells["caller_id"].Value = "bind";
            //                    if (f.Chbox_firstUse.Checked)
            //                        dr["caller_id"] = "bind";
            //                    else
            //                        dr["caller_id"] = "";
            //                }

            //                if (f.is_sucusse_Add_profile)
            //                {
            //                    row.Cells["الباقة"].Value = f.last_profile;
            //                    dr["profile"] = f.last_profile;

            //                }
            //                if (f.do_refresh_users == true)
            //                {
            //                    if (BeginRefresh)
            //                    {
            //                        MessageBox.Show("انتظهر حتى انتهاء العملية السابقة");
            //                        return;
            //                    }

            //                    BeginRefresh = true;
            //                    Update_Um_StatusBar(false, true, 50, "", "يتم الان تحديث كروت اليوزمنجر من النظام");

            //                    ThreadStart theprogress = new ThreadStart(() => Update_UserMan_from_Router());
            //                    Thread startprogress = new Thread(theprogress);
            //                    startprogress.Name = "Update usesr";
            //                    startprogress.Start();
            //                    try
            //                    {
            //                        //if (dgvUserManager.Rows.Count == 0)
            //                        dgvUserManager.CurrentCell = dgvUserManager.Rows[0].Cells[dgvUserManager.Columns.Count];
            //                        //if (dgvUserManager.Rows.Count > 1)
            //                        //    dgvUserManager.CurrentCell = dgvUserManager.Rows[1].Cells["الاسم"];
            //                    }
            //                    catch { }

            //                    return;
            //                }


            //            }
            //        }


            //        MyDataClass.SourceForPrint_UM_Users.AcceptChanges();
            //    }
            //    catch (Exception ex) { MessageBox.Show(ex.Message.ToString()); }

            //}



            //if (f.is_password_Change)
            //    is_password_Change = true;

            //if (befor_Chbox_disable != Chbox_disable.Checked)
            //    is_Chbox_disable_Change = true;

            //if (befor_txt_mac != txt_mac.Text)
            //    is_txt_mac_Change = true;

            //if (befor_Chbox_firstUse != Chbox_firstUse.Checked)
            //    is_Chbox_firstUse_Change = true;

            //return;

            //if (f.last_profile != "")
            //{
            //    try
            //    {
            //        foreach (DataGridViewRow row in dgv.SelectedRows)
            //        {
            //            if (!row.IsNewRow)
            //            {
            //                row.DefaultCellStyle.BackColor = Color.White;
            //                row.DefaultCellStyle.ForeColor = Color.Black;
            //                row.Cells["الباقة"].Value = f.last_profile;
            //                MyDataClass.SourceForPrint_UM_Users.Rows[row.Index]["Profile"] = f.last_profile;
            //                //MessageBox.Show(MyDataClass.SourceForPrint_UM_Users.Rows[row.Index]["Name"].ToString());
            //            }
            //        }
            //        MyDataClass.SourceForPrint_UM_Users.AcceptChanges();
            //    }
            //    catch (Exception ex) { MessageBox.Show(ex.ToString()); }


            //    //Update_Um_StatusBar(false, true, 0, "", " تمت االعملية بنجاح");
            //}

        }

        [Obsolete]
        private void btnEdit_Click(object sender, EventArgs e)
        {
            if (dgv.SelectedRows.Count > 0)
                get_card_detail();

        }

        [Obsolete]
        private void DeleteCardsArchive_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("  لن تظهر تقارير واحصائيات الكروت المحذوه من الارشيف والتي ليست موجوده في الروتر \nهل متاكد من حذف الكروت المحدده من الارشيف", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }

            HashSet<UmUser> dbUser = new HashSet<UmUser>();
            List<DataGridViewRow> rw = new List<DataGridViewRow>();
            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                //if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                //{
                dbUser.Add((UmUser)row.DataBoundItem);
                rw.Add(row);
                //}
            }
            int delete = 0;
            lock (Sql_DataAccess.Lock_localDB)
            {
                delete = Local_DA.Remove_UmUser_FormDB(dbUser);
             
                //using (var db = Sql_DataAccess.Get_dbFactory().Open())
                //{
                //     delete = db.DeleteAll<UmUser>(dbUser);
                //}
            }
            if (delete > 0)
            {
                RJMessageBox.Show("تم حذف الكروت من الارشيف");
                //foreach (var itm in rw)
                //{
                //    itm.Cells["DeleteFromServer"].Value = 1;
                //}
                loadData();
            }
        }
        bool is_Delete_FromArchive = false;

        [Obsolete]
        private void DeleteServerArchiveToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("هل متاكد من حذف الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            DialogResult result2 = RJMessageBox.Show("عند حذف كروت الارشيف لن تظهر تقارير هذه الكروت", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.No)
            {
                return;
            }
            is_Delete_FromArchive = true;
            btnDelete_Click(sender, e);

        }

        [Obsolete]
        public void Delecte_Sessions_Users_ByID()
        {
            isProcessRun=true;
            HashSet<string> list_user = new HashSet<string>();
            try
            {
                HashSet<UmSession> dbUser = new HashSet<UmSession>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        List<UmSession> hs = Local_DA.Load<UmSession>($"select * from UmSession where Fk_Sn_Name='{row.Cells["Sn_Name"].Value.ToString()}' and DeleteFromServer=0");
                        if (hs != null)
                            if (hs.Count >= 0)
                            {
                                dbUser.UnionWith(hs.ToHashSet());
                               
                                list_user.Add(row.Cells["IdHX"].Value.ToString());
                                //dbUser.Add((UmSession)row.DataBoundItem);
                                _DataGridViewRow.Add(row);
                            }
                    }
                }

                if(dbUser.Count<=0)
                {
                    RJMessageBox.Show("لم يتم تحميل جلسات الكروت المحددة");
                    isProcessRun = false;
                    return;
                }
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmSession> ResUsers = mk_DataAccess.Delete_UmSession_ByID(dbUser);

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                    //RJMessageBox.Show("");
                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < dbUser.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض الجلسات");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    //Sql_DataAccess da = new Sql_DataAccess();
                    Local_DA.Set_Delet_fromServer("UmSession", dbUser);
                    //foreach (var itm in _DataGridViewRow)
                    //{
                    //    itm.Cells["DeleteFromServer"].Value = 1;
                    //}
                    //loadData();
                }

                isProcessRun = false;
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); isProcessRun = false; }

        }

        [Obsolete]
        private void DeleteSession_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (dgv.Rows.Count <= 0)
                return;

            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }

            DialogResult result = RJMessageBox.Show("هل متاكد من حذف جلسات الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
            {
                return;
            }
            //typeProcess = "DeleteFromServer";
            //isProcessRun = true;
            ThreadStart theprogress;
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بحذف جلسات الكروت المحدده من اليوزمنجر");
            
                    theprogress = new ThreadStart(() => Delecte_Sessions_Users_ByID());
                
                Thread startprogress = new Thread(theprogress);
                startprogress.Start();
            
 
        }

        [Obsolete]
        private void RestCards_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من تصفير عدداد الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "RestCards";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتصفير الكروت المحدده من اليوزمنجر");
            isProcessRun = true;
            ThreadStart theprogress;
            if (dgv.SelectedRows.Count <= 20)
                theprogress = new ThreadStart(() => DisableUser_ByID());
            else
                theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();


            //typeProcess = "RestCards";
            //if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            //{
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتصفير الكروت المحدده من اليوزمنجر");

            //    using (Form_WaitForm frm = new Form_WaitForm(DisableUser_ByID))
            //    {
            //        frm.ShowDialog();
            //    }
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم تصفير الكروت المحدده من اليوزمنجر");
            //}
        }

        [Obsolete]
        private void BindMAC_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من ربط الكروت المحدده باول جاهز يستخدم", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "caller_bind";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بربط الكروت المحدده من اليوزمنجر");
            isProcessRun = true;
            ThreadStart theprogress;
            if (dgv.SelectedRows.Count <= 20)
                theprogress = new ThreadStart(() => DisableUser_ByID());
            else
                theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();


            //typeProcess = "caller_bind";
            ////if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            ////{
            ////    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بربط الكروت المحدده من اليوزمنجر");

            //    using (Form_WaitForm frm = new Form_WaitForm(DisableUser_ByID))
            //    {
            //        frm.ShowDialog();
            //    }
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم ربط الكروت المحدده باول ماك ");
            ////}
        }

        [Obsolete]
        private void RemoveBindMAC_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (isProcessRun == true)
            {
                RJMessageBox.Show("انتظر حتى انهاء العلمية السابقة");
                return;
            }
            DialogResult result = RJMessageBox.Show("هل متاكد من الغاء ربط الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            typeProcess = "Remove_caller_bind";
            Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بالغاء ربط الكروت المحدده من اليوزمنجر");
            isProcessRun = true;
            ThreadStart theprogress;
            if (dgv.SelectedRows.Count <= 20)
                theprogress = new ThreadStart(() => DisableUser_ByID());
            else
                theprogress = new ThreadStart(() => RunProccess_ByScript(dgv.SelectedRows.Count));

            Thread startprogress = new Thread(theprogress);
            startprogress.Start();


            //typeProcess = "Remove_caller_bind";
            //if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            //{
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بالغاء ربط الكروت المحدده من اليوزمنجر");

            //    using (Form_WaitForm frm = new Form_WaitForm(DisableUser_ByID))
            //    {
            //        frm.ShowDialog();
            //    }
            //    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم الغاء ربط الكروت المحدده باول ماك ");
            //}
        }

        private void PrintCards_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            //Dictionary<string, NewUserToAdd> _dicUsers = new Dictionary<string, NewUserToAdd>() ;


            Form_PrintForm frm = new Form_PrintForm();
            frm.dgv = (dgv.SelectedRows);
            frm.ShowDialog();

            //frm.dgvSelectedRows(dgv.SelectedRows);
            //foreach (DataGridViewRow row in dgv.SelectedRows)
            //{

            //}

        }

        private void AddProfile_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            {
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يتم اضافة باقة جديده الي الكروت المحدده");

                AddProfile();
                //using (Form_WaitForm frm = new Form_WaitForm(AddProfile))
                //{
                //    frm.ShowDialog();
                //}
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم اضافة الباقة الي الكروت المحدده  ");
            }
        }
        private void AddProfile()
        {
            HashSet<string> list_user = new HashSet<string>();
            HashSet<UmUser> dbUser = new HashSet<UmUser>();
            List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                {
                    list_user.Add(row.Cells["IdHX"].Value.ToString());
                    dbUser.Add((UmUser)row.DataBoundItem);
                    _DataGridViewRow.Add(row);
                }
            }
            Form_Add_Profile_Balance frm = new Form_Add_Profile_Balance(dbUser);
            frm.ShowDialog();

            if (frm.success == true)
            {
                foreach (var itm in _DataGridViewRow)
                {
                    itm.Cells["ProfileName"].Value = frm.CBox_Profile.Text;

                    if (Convert.ToInt16(itm.Cells["Status"].Value) == 2)
                    {
                        itm.Cells["Status"].Value = 1;
                    }
                    else if (Convert.ToInt16(itm.Cells["Status"].Value) == 3)
                    {
                        itm.Cells["Status"].Value = 0;
                    }
                }
                dgv.Refresh();

            }

        }


        private void ChangeSP_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("هل متاكد من تغير نقطة بيع الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;


            if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            {
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " يقوم الان بتعديل نقطة البيع للكروت المحدده");

                Change_SellingPoint();
                //using (Form_WaitForm frm = new Form_WaitForm(Change_SellingPoint))
                //{
                //    frm.ShowDialog();
                //}
                Global_Variable.Update_Um_StatusBar(false, true, 0, "", " تم تعديل نقطة البيع للكروت المحدده ");
            }
        }
        private void Change_SellingPoint()
        {
            HashSet<string> list_user = new HashSet<string>();
            HashSet<UmUser> dbUser = new HashSet<UmUser>();
            List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

            foreach (DataGridViewRow row in dgv.SelectedRows)
            {
                if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                {
                    list_user.Add(row.Cells["IdHX"].Value.ToString());
                    dbUser.Add((UmUser)row.DataBoundItem);
                    _DataGridViewRow.Add(row);
                }
            }
            Form_Change_Cards_SellingPoint frm = new Form_Change_Cards_SellingPoint();
            frm.Users = dbUser;

            frm.ShowDialog();

            if (frm.is_success == true)
            {
                foreach (var itm in _DataGridViewRow)
                {
                    //itm.Cells["SpCode"].Value = frm.CBox_SellingPoint.SelectedValue.ToString();
                    itm.Cells["SpName"].Value = frm.CBox_SellingPoint.Text;
                }

            }

            dgv.Refresh();

        }

        private void Remove_SP_ToolStripMenuItem_Click(object sender, EventArgs e)
        {
            DialogResult result = RJMessageBox.Show("هل متاكد من حذف نقطة البيع من الكروت المحدده", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result == DialogResult.No)
                return;

            if (fillter_type == "From_Server" || fillter_type == "From_Finsh_Cards")
            {
                HashSet<string> list_user = new HashSet<string>();
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> _DataGridViewRow = new List<DataGridViewRow>();

                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    if (row.Cells["DeleteFromServer"].Value.ToString() == "0")
                    {
                        list_user.Add(row.Cells["IdHX"].Value.ToString());
                        var um = (UmUser)row.DataBoundItem;
                        um.SpCode = null;
                        um.SpName = null;
                        dbUser.Add(um);
                        _DataGridViewRow.Add(row);
                    }
                }

                lock (Smart_DataAccess.Lock_object)
                {
                    Smart_DataAccess smart_dataAccess = new Smart_DataAccess();
                    if (smart_dataAccess.Change_Selling_Point(dbUser, "", "", true))
                    {
                        foreach (var itm in _DataGridViewRow)
                        {
                            itm.Cells["SpName"].Value = null;
                            //itm.Cells["SpCode"].Value = null;
                        }

                    }
                }
                dgv.Refresh();
            }

        }

        private void rjLabel4_Click(object sender, EventArgs e)
        {

        }


        private void CBox_PageCount_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            //if(CBox_PageCount.SelectedIndex != 0)
            PageSize = Convert.ToInt32(CBox_PageCount.Text);
            //if (PageSize > totalRows)
            //{
            //    PageSize = totalRows;
            //    currentPageindex = 1;
            //    totalPages = 0;
            //    totalRows = 0;
            //}
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
        }

        private void rjButton1_Click(object sender, EventArgs e)
        {
            CBox_Profile.SelectedIndex = -1;
            CBox_Profile.Text = "";
            CBox_Staus.SelectedIndex = -1;
            CBox_Staus.Text = "";
            CBox_SearchBy.SelectedIndex = -1;
            txt_search.Text = "";
            CBox_Batch.SelectedIndex = -1;
            CBox_Batch.Text = "";
            CBox_NumberPrint.SelectedIndex = -1;
            CBox_NumberPrint.Text = "";
            CBox_Customer.SelectedIndex = -1;
            CBox_Customer.Text = "";
            CBox_Didabled.SelectedIndex = -1;
            CBox_Didabled.Text = "";
            CBox_SellingPoint.SelectedIndex = -1;
            CBox_SellingPoint.Text = "";
            CheckBox_byDatePrint.Check = false;
            CheckBox_SN.Check = false;

            if (ToggleButton_ByCountProfile.Checked)
            {
                firstLoad = true;
                ToggleButton_ByCountProfile.Checked = false;
                firstLoad = false;
            }
            if (ToggleButton_ByCountSession.Checked)
            {
                firstLoad = true;
                ToggleButton_ByCountSession.Checked = false;
                firstLoad = false;
            }
            if (ToggleButton_Show_Archive.Checked)
            {
                firstLoad = true;
                ToggleButton_Show_Archive.Checked = false;
                firstLoad = false;
            }


            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
        }


        private void btnFirst_Click(object sender, EventArgs e)
        {
            currentPageindex = totalPages;
            loadData();
            txtCurrentPageindex.Text = currentPageindex.ToString();
        }


        private void btnNext_Click(object sender, EventArgs e)
        {
            if (currentPageindex < totalPages)
            {
                currentPageindex++;
                loadData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }


        private void btnPrev_Click(object sender, EventArgs e)
        {
            if (currentPageindex > 1)
            {
                currentPageindex--;
                loadData();
                txtCurrentPageindex.Text = currentPageindex.ToString();
            }
        }


        private void btnLast_Click(object sender, EventArgs e)
        {
            currentPageindex = 1;
            loadData();
            txtCurrentPageindex.Text = currentPageindex.ToString();

        }

        private void CBox_OrderBy_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void CheckBox_orderBy_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }
        private void btn_MenuProcess_Click(object sender, EventArgs e)
        {
            try
            {
                if (UIAppearance.Language_ar)
                    dmAll_Cards.Show(btn_MenuProcess, DropdownMenuPosition.TopRight);
                //rjDropdownMenu1.Show();

                //dmAll_Cards.Show(dgv, new Point(btn_MenuProcess.Location.X, btn_MenuProcess.Location.Y));
                //dmAll_Cards.Show(new Point(btn_MenuProcess.Location.X, btn_MenuProcess.Location.Y));
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
        }

        private void SaveDGVToolStripMenuItem_Click(object sender, EventArgs e)
        {
            SaveFromState();
            RJMessageBox.Show("تم الحفظ");
        }

        private void dgv_CellContentClick(object sender, DataGridViewCellEventArgs e)
        {

        }

        private void ToggleButton_Show_Archive_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (ToggleButton_Show_Archive.Checked == true)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                ToggleButton_Show_onlyServer.Checked = false;
                firstLoad = false;
            }

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
        }

        private void dgv_CellPainting(object sender, DataGridViewCellPaintingEventArgs e)
        {
            //if (e.RowIndex >= 0 )
            //{
            //    //if (e.Value.GetType()
            //    e.PaintBackground(e.CellBounds, true);
            //    TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor,  TextFormatFlags.VerticalCenter );
            //    //TextRenderer.DrawText(e.Graphics, e.FormattedValue.ToString(), e.CellStyle.Font, e.CellBounds, e.CellStyle.ForeColor, TextFormatFlags.Default | TextFormatFlags.VerticalCenter );
            //    e.Handled = true;

            //}
        }

        [Obsolete]
        private void btn_RemoveFinsh_Validaty_Click(object sender, EventArgs e)
        {
            if (Global_Variable.Source_Pyment_UserManager == null || Global_Variable.Source_Pyment_UserManager.Count < 0 || Global_Variable.Source_Session_UserManager == null || Global_Variable.Source_Session_UserManager.Count < 0)
            {
                DialogResult result = RJMessageBox.Show("لم تقم بجلب المبيعات والجلسات حتى يتم مزامنتها في قاعدة البيانات المحلية(الارشيف) قد لا تستطيع معرفة التقارير عنها بعد حذفها \n  يفضل ان تقوم بجلب المبيعات والجلسات من اجل ان يتم حفظها في قاعدة البيانات المحلية هل تريد العودة للقائمة السابقة", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                    return;
            }
            RJMessageBox.Show("سوف يتم حذف الكروت المنتهيةالصلاحية مع جلساتهن من السيرفر ");
            DialogResult result2 = RJMessageBox.Show(" سوف ياخذ وقت طويل حسب عدد الكروت المنتهية هل انت متأكد ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
            if (result2 == DialogResult.Yes)
            {
                 
                    using (Form_WaitForm fRM = new Form_WaitForm(RemoveFinshCards))
                        fRM.ShowDialog();
                
            }
        }

        [Obsolete]
        private void RemoveFinshCards()
        {
            if (Global_Variable.Mk_resources.version <= 6)
            {
                string script = "{/tool user-manager user remove [find where !actual-profile && uptime-used>0]; }\n";
                Dictionary<string, string> res = Mk_DataAccess.add_Script_Smart_AndRun(script, false);
                if (res["status"] == "false")
                {
                    RJMessageBox.Show("Erorr\n" + res["result"]);
                    return;
                }
                RJMessageBox.Show("انتهى . اضغط تحديث الكروت من الروتر لمزامنة اخر البيانات او عيد تشغيل البرنامج");
            }

            else
            {
               List<UmUser> users = Local_DA.Load<UmUser>("select * from Umuser where Status=2 and DeleteFromServer=0");
               
                Mk_DataAccess mk_DataAccess = new Mk_DataAccess();
                HashSet<UmUser> ResUsers = mk_DataAccess.Delete_UserManager_ByID(users.ToHashSet());

                if (ResUsers.Count <= 0)
                {
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", " خطاء في عملية الحذف");
                    isProcessRun = false;
                    is_Delete_FromArchive = false;

                    return;
                }
                if (ResUsers.Count > 0)
                {
                    if (ResUsers.Count > 0 && ResUsers.Count < users.Count) Global_Variable.Update_Um_StatusBar(false, true, 0, "", "حدث خطاء في حذف بعض العناصر");

                    //else RJMessageBox.Show("تمت العلية بنجاح");
                    Global_Variable.Update_Um_StatusBar(false, true, 0, "", "تمت العلية بنجاح");

                    Local_DA.Set_Delet_fromServer("UmUser", users.ToHashSet());
                    loadData();

                }

                isProcessRun = false;
                is_Delete_FromArchive = false;



            }
        }

        private void CBox_Staus_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void CBox_Profile_OnSelectedIndexChanged(object sender, EventArgs e)
        {

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void CBox_Batch_OnSelectedIndexChanged(object sender, EventArgs e)
        {

            if (firstLoad)
                return;
             
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            Get_NumberPrint(CBox_Batch.SelectedValue.ToString());
            loadData();
           

            Cursor.Current = Cursors.Default;


        }

        private void CBox_SellingPoint_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;

            loadData();
        }

        private void panel1_SizeChanged(object sender, EventArgs e)
        {
            Select_First_DGV();
        }

        private void panel1_Resize(object sender, EventArgs e)
        {
            //Select_First_DGV();
        }

        private void dmAll_Cards_Opening(object sender, System.ComponentModel.CancelEventArgs e)
        {

        }

        private void تعديلرقمالدفعةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frm_Input_Dailog_New_Template frm = new frm_Input_Dailog_New_Template();
            frm.lbl_name.Text = "ادخل رقم الدفعة";
            frm.Text = "ادخل رقم الدفعة";
            frm.ShowDialog();
            if (frm.add)
            {
                DialogResult result = RJMessageBox.Show("هل تريد تغير رقم الدفعة للكروت المحددة", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.No)
                    return;
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> rw = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    dbUser.Add((UmUser)row.DataBoundItem);
                    rw.Add(row);
                }
                int efectRows = 0;
                lock (Sql_DataAccess.Lock_localDB)
                {
                    //UmUser um = new UmUser();
                    //um.NumberPrint
                    string Qury = $"update UmUser set [BatchCardId]={frm.txt_Name.Text.Trim()} WHERE Sn_Name=@Sn_Name; ";
                    efectRows= Local_DA.Execute<UmUser>(Qury, dbUser);
                }
                if (efectRows > 0)
                {
                    RJMessageBox.Show($"تم تعديل {efectRows}  كرت");
                    loadData();
                }

            }
       
        }

        private void تعديلرقمالطبعةToolStripMenuItem_Click(object sender, EventArgs e)
        {
            frm_Input_Dailog_New_Template frm = new frm_Input_Dailog_New_Template();
            frm.lbl_name.Text = "ادخل رقم الطبعة";
            frm.Text = "ادخل رقم الطبعة";
            frm.ShowDialog();
            if (frm.add)
            {
                DialogResult result = RJMessageBox.Show("هل تريد تغير رقم الطبعة للكروت المحددة", "تاكيد الاستمرار?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.No)
                    return;
                HashSet<UmUser> dbUser = new HashSet<UmUser>();
                List<DataGridViewRow> rw = new List<DataGridViewRow>();
                foreach (DataGridViewRow row in dgv.SelectedRows)
                {
                    dbUser.Add((UmUser)row.DataBoundItem);
                    rw.Add(row);
                }
                int efectRows = 0;
                lock (Sql_DataAccess.Lock_localDB)
                {
                    //UmUser um = new UmUser();
                    //um.NumberPrint
                    string Qury = $"update UmUser set [NumberPrint]={frm.txt_Name.Text} WHERE Sn_Name=@Sn_Name; ";
                    efectRows = Local_DA.Execute<UmUser>(Qury, dbUser);
                }
                if (efectRows > 0)
                {
                    RJMessageBox.Show($"تم تعديل {efectRows}  كرت");
                    loadData();
                }

            }

        }

        private void ToggleButton_Show_onlyServer_CheckedChanged(object sender, EventArgs e)
        {
            if (firstLoad)
                return;
            if (ToggleButton_Show_onlyServer.Checked == true)
            {
                firstLoad = true;
                CBox_OrderBy.SelectedIndex = 0;
                ToggleButton_Show_Archive.Checked = false;
                firstLoad = false;
            }

            currentPageindex = 1;
            totalPages = 0;
            totalRows = 0;
            loadData();
        }
    }
}
