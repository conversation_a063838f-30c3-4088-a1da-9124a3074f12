﻿using SmartCreator.RJForms;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.IO;
using System.Diagnostics;
using System.Threading;
using SmartCreator.Utils;
using AltoHttp;
using System.Net;

namespace SmartCreator.Forms.PageHtml
{
    public partial class Form_Check_Library_Html : RJChildForm
    {
        public Form_Check_Library_Html()
        {
            //Check_library_CefSharp();
            InitializeComponent();
            Check_library_CefSharp();
        }

        public void Check_library_CefSharp()
        {
            try
            {
                string[] Files = {
                "CefSharp.dll",
                "CefSharp.Core.dll",
                "CefSharp.Core.Runtime.dll",
                "CefSharp.WinForms.dll",
                "chrome_elf.dll",
                "icudtl.dat",
                "libcef.dll",

                "CefSharp.BrowserSubprocess.Core.dll",
                "CefSharp.BrowserSubprocess.exe",
                "chrome_100_percent.pak",
                "libEGL.dll",
                "resources.pak",
                "snapshot_blob.bin",
                "SNI.dll",
                "v8_context_snapshot.bin",
                "vk_swiftshader_icd.json",
                "vulkan-1.dll",
                "locales\\ar.pak",
                "locales\\en-US.pak",
            };
                List<string> FilesNotFound = new List<string>();

                foreach (string file in Files)
                {
                    if (!File.Exists(@file))
                    {
                        FilesNotFound.Add(file);
                    }
                }
                if (FilesNotFound.Count > 0)
                {
                    pnl_Donaload.Visible = true;

                    RJMessageBox.Show("هناك بعض المكتبات التي تحتاج الي تنزيل ");
                    DialogResult result = RJMessageBox.Show("  هل تريد تنزيل تحديث وتنزيل المكتبات الناقصة   ", "Really quit?", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result == DialogResult.No)
                        return;

                    foreach (string itm in FilesNotFound)
                    {
                        //string filename = $"{Path.GetFileNameWithoutExtension(itm)}.zip";
                        //string updatePath = $"https://smrtye.com/static/download/CefSharp/{filename}";
                        ////string updatePath = "https://smrtye.com/static/download/CefSharp/CefSharp.zip";
                        //DownloadFiles(updatePath, itm);
                    }



                }
                else
                {
                    ShowFormHtml();


                }
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }

            ShowFormHtml();

        }


        void ShowFormHtml()
        {
            pnl_Donaload.Visible = false;

            Form_Page_Editor frm = new Form_Page_Editor();
            frm.TopLevel = false;
            frm.IsChildForm = true;
            frm.Dock = DockStyle.Fill;

            this.panel1.Controls.Add(frm);
            this.panel1.Tag = frm;
            frm.Show();
            frm.BringToFront();
            frm.Focus();
        }
        HttpDownloader httpDownloader;
        void DownloadFiles(string UrlUpdate,string filename)
        {
            try
            {
                //Uri uri = new Uri(UrlUpdate.Trim());
                //string filename = System.IO.Path.GetFileName(uri.AbsolutePath);
                //string Dest_Download_Path = $"{Application.StartupPath}\\" + "tempCards\\" + filename;
                string Dest_Download_Path = $"{Application.StartupPath}\\" + filename;


                ServicePointManager.Expect100Continue = true;
                SecurityProtocolType defaultProtocol = ServicePointManager.SecurityProtocol;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls;
                System.Net.ServicePointManager.ServerCertificateValidationCallback = (senderX, certificate, chain, sslPolicyErrors) => { return true; };

                httpDownloader = new HttpDownloader(UrlUpdate, $"{Dest_Download_Path}");
                //httpDownloader = new HttpDownloader(updatePath, $"{Application.StartupPath}\\{Path.GetFileName(UrlUpdate)}");
                httpDownloader.DownloadCompleted += HttpDownloader_DownloadCompleted;
                httpDownloader.ProgressChanged += HttpDownloader_ProgressChanged;
                httpDownloader.Start();
                btn_Update.Enabled = false;
                //lbl_botom.Text = "جاري التنزيل...";
            }
            catch (Exception ex) { MessageBox.Show(ex.Message); }
        }


        private void HttpDownloader_ProgressChanged(object sender, AltoHttp.ProgressChangedEventArgs e)
        {
            progressBar1.Value = (int)e.Progress;
            lblPercent.Text = $"{e.Progress.ToString("0.00")} %";
            lblSpeed.Text = utils.ConvertSize_Get_En(e.SpeedInBytes.ToString());// ConvertSize(e.SpeedInBytes);
            //lblSpeed.Text = string.Format("{0} MB/s", (e.SpeedInBytes / 1024d / 1024d).ToString("0.00"));
            //lblDownloaded.Text = ConvertSize(httpDownloader.TotalBytesReceived);
            lblDownloaded.Text = string.Format("{0} MB", (httpDownloader.TotalBytesReceived / 1024d / 1024d).ToString("0.00"));
            //lblStatus.Text = "جاري التنزيل...";
        }

        private void HttpDownloader_DownloadCompleted(object sender, EventArgs e)
        {
            //throw new NotImplementedException();
            this.Invoke((MethodInvoker)delegate
            {
                //lblStatus.Text = "اكتمل تنزيل الملف";
                //lbl_botom.Text = "اكتمل تنزيل الملف";
                lblPercent.Text = "100%";
                //btn_Update.Enabled = true;

                //lbl_botom.Text = "يتم تثبيت الملفات....";

                Unzip_All_File();
                if (status)
                {
                    //lbl_botom.Text = "اكتمل تثبيت الملفات";
                    Thread.Sleep(5000);
                    this.Close();
                    Application.Exit();
                    Environment.Exit(0);
                }
                //else
                //    lbl_botom.Text = "حدث مشكلة اثناء تثبيت البرنامج";

                //lbl_botom.Text = "اكتمل تثبيت الملفات";

                try
                {
                    string[] gew = Directory.GetFiles($"C:\\SmartCreator9");
                    string desktopPath = Environment.GetFolderPath(Environment.SpecialFolder.Desktop);
                    File.Copy("C:\\SmartCreator9\\SmartCreator.lnk", desktopPath + "\\SmartCreator.lnk");
                }
                catch { }

                try
                {
                    Process.Start($"C:\\SmartCreator9\\SmartCreator\\SmartCreator.exe");
                    //Process.Start($"C:\\SmartCreator9\\SmartCreator\\SmartCreator.exe");
                }
                catch { }

            });
        }
        bool Pause = false;
        bool status = false;

        void Unzip_All_File()
        {

        }

        private void btn_Update_Click(object sender, EventArgs e)
        {
            Check_library_CefSharp();
        }
    }
   
}
