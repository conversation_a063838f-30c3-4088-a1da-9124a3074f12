using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Text;
using SmartCreator.Data.DirectORM;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Helper class for building SQL expressions
    /// </summary>
    public class SqlExpressionBuilder<T> where T : class, new()
    {
        private readonly string _tableName;
        private readonly List<string> _whereConditions = new();
        private readonly List<string> _orderByConditions = new();
        private readonly List<string> _groupByConditions = new();
        private string? _selectClause;
        private int? _take;
        private int? _skip;

        public SqlExpressionBuilder()
        {
            _tableName = SqlGenerator.GetTableName<T>();
        }

        /// <summary>
        /// Add WHERE condition
        /// </summary>
        public SqlExpressionBuilder<T> Where(Expression<Func<T, bool>> predicate)
        {
            var condition = ExpressionTranslator.Translate(predicate);
            _whereConditions.Add(condition);
            return this;
        }

        /// <summary>
        /// Add ORDER BY
        /// </summary>
        public SqlExpressionBuilder<T> OrderBy(Expression<Func<T, object>> selector)
        {
            var orderBy = ExpressionTranslator.TranslateOrderBy(selector);
            _orderByConditions.Add(orderBy);
            return this;
        }

        /// <summary>
        /// Add ORDER BY DESC
        /// </summary>
        public SqlExpressionBuilder<T> OrderByDescending(Expression<Func<T, object>> selector)
        {
            var orderBy = ExpressionTranslator.TranslateOrderBy(selector);
            _orderByConditions.Add($"{orderBy} DESC");
            return this;
        }

        /// <summary>
        /// Add GROUP BY
        /// </summary>
        public SqlExpressionBuilder<T> GroupBy(Expression<Func<T, object>> selector)
        {
            var groupBy = ExpressionTranslator.TranslateGroupBy(selector);
            _groupByConditions.Add(groupBy);
            return this;
        }

        /// <summary>
        /// Set custom SELECT clause
        /// </summary>
        public SqlExpressionBuilder<T> Select(string selectClause)
        {
            _selectClause = selectClause;
            return this;
        }

        /// <summary>
        /// Set TAKE (LIMIT)
        /// </summary>
        public SqlExpressionBuilder<T> Take(int count)
        {
            _take = count;
            return this;
        }

        /// <summary>
        /// Set SKIP (OFFSET)
        /// </summary>
        public SqlExpressionBuilder<T> Skip(int count)
        {
            _skip = count;
            return this;
        }

        /// <summary>
        /// Build SELECT statement
        /// </summary>
        public string ToSelectStatement()
        {
            var sql = new StringBuilder();
            sql.Append($"SELECT {_selectClause ?? "*"} FROM {_tableName}");

            if (_whereConditions.Count > 0)
                sql.Append($" WHERE {string.Join(" AND ", _whereConditions)}");

            if (_groupByConditions.Count > 0)
                sql.Append($" GROUP BY {string.Join(", ", _groupByConditions)}");

            if (_orderByConditions.Count > 0)
                sql.Append($" ORDER BY {string.Join(", ", _orderByConditions)}");

            if (_take.HasValue)
                sql.Append($" LIMIT {_take.Value}");

            if (_skip.HasValue)
                sql.Append($" OFFSET {_skip.Value}");

            return sql.ToString();
        }

        /// <summary>
        /// Build COUNT statement
        /// </summary>
        public string ToCountStatement()
        {
            var sql = new StringBuilder();
            sql.Append($"SELECT COUNT(*) FROM {_tableName}");

            if (_whereConditions.Count > 0)
                sql.Append($" WHERE {string.Join(" AND ", _whereConditions)}");

            return sql.ToString();
        }

        /// <summary>
        /// Build DELETE statement
        /// </summary>
        public string ToDeleteStatement()
        {
            var sql = new StringBuilder();
            sql.Append($"DELETE FROM {_tableName}");

            if (_whereConditions.Count > 0)
                sql.Append($" WHERE {string.Join(" AND ", _whereConditions)}");

            return sql.ToString();
        }

        /// <summary>
        /// Build scalar statement
        /// </summary>
        public string ToScalarStatement<TResult>(Expression<Func<T, TResult>> selector)
        {
            var selectClause = ExpressionTranslator.TranslateSelect(selector);
            var sql = new StringBuilder();
            sql.Append($"SELECT {selectClause} FROM {_tableName}");

            if (_whereConditions.Count > 0)
                sql.Append($" WHERE {string.Join(" AND ", _whereConditions)}");

            if (_groupByConditions.Count > 0)
                sql.Append($" GROUP BY {string.Join(", ", _groupByConditions)}");

            if (_orderByConditions.Count > 0)
                sql.Append($" ORDER BY {string.Join(", ", _orderByConditions)}");

            if (_take.HasValue)
                sql.Append($" LIMIT {_take.Value}");

            if (_skip.HasValue)
                sql.Append($" OFFSET {_skip.Value}");

            return sql.ToString();
        }
    }
}
