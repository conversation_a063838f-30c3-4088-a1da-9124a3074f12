﻿//using PhoneNumbers;
using SmartCreator.Models;
using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.ViewModels
{
    public class CountryCodes
    {
        public Dictionary<string, int> getCountryCodes()
        {
            DataTable dt = new DataTable();
            dt.Columns.Add("EnglishName");
            dt.Columns.Add("NativeName");
            dt.Columns.Add("twoLetterCode");
            dt.Columns.Add("PostCode", typeof(int));

            CultureInfo[] cinfo = CultureInfo.GetCultures(CultureTypes.AllCultures & ~CultureTypes.NeutralCultures);
            Dictionary<string, int> countryNameAndCode = new Dictionary<string, int>();

            foreach (CultureInfo cul in cinfo)
            {
                try
                {
                    char[] name = cul.Name.ToCharArray();
                    if (name.Length >= 2)
                    {

                        string twoLetterCode = "" + name[name.Length - 2] + name[name.Length - 1];
                        int code = findCountryCode(twoLetterCode);

                        //Console.WriteLine("Display Name: "+cul.DisplayName + " Name: " + cul.Name +" twoLetterCode: "+ twoLetterCode
                        //+" Country code: "+code);

                        try
                        {
                            var rigion = new RegionInfo(cul.Name);
                            string native = rigion.NativeName;

                            if (native == "Israel" || native == "إسرائيل")
                                native = "فلسطين";
                            countryNameAndCode[native] = code;

                            dt.Rows.Add(rigion.EnglishName, rigion.NativeName, twoLetterCode, code);
                        }
                        catch (ArgumentException e)
                        {
                            //MessageBox.Show(e.ToString());
                            //Console.WriteLine(e.ToString());
                        }
                    }
                }
                catch { }

            }
            Global_Variable.CountryList = dt;


            //await Task.WhenAll();
            //Console.WriteLine("Number of Countries: " + countryNameAndCode.Count);

            //foreach (var kvp in countryNameAndCode)
            //    Console.WriteLine("Country name: {0}, Country code: {1}", kvp.Key, kvp.Value);

            //printDictionaryOutput(countryNameAndCode);
            return countryNameAndCode;
        }


        private static Tuple<Dictionary<string, int>, Dictionary<string, int>> getCountryCodes3(Dictionary<string, int> a, Dictionary<string, int> b)
        {

            CultureInfo[] cinfo = CultureInfo.GetCultures(CultureTypes.AllCultures & ~CultureTypes.NeutralCultures);
            Dictionary<string, int> countryNameAndCode = new Dictionary<string, int>();

            foreach (CultureInfo cul in cinfo)
            {
                char[] name = cul.Name.ToCharArray();
                if (name.Length >= 2)
                {

                    string twoLetterCode = "" + name[name.Length - 2] + name[name.Length - 1];
                    int code = findCountryCode(twoLetterCode);

                    //Console.WriteLine("Display Name: "+cul.DisplayName + " Name: " + cul.Name +" twoLetterCode: "+ twoLetterCode
                    //+" Country code: "+code);

                    try
                    {
                        var rigion = new RegionInfo(cul.Name);
                        countryNameAndCode[rigion.EnglishName] = code;
                    }
                    catch (ArgumentException e)
                    {
                        //MessageBox.Show(e.ToString());
                        //Console.WriteLine(e.ToString());
                    }
                }

            }



            var tuple = new Tuple<Dictionary<string, int>, Dictionary<string, int>>(countryNameAndCode, countryNameAndCode);
            return tuple;
        }

        public DataTable getCountryCodes2()
        {


            CultureInfo[] cinfo = CultureInfo.GetCultures(CultureTypes.AllCultures & ~CultureTypes.NeutralCultures);
            Dictionary<string, int> countryNameAndCode = new Dictionary<string, int>();
            DataTable dt = new DataTable();
            dt.Columns.Add("EnglishName");
            dt.Columns.Add("NativeName");
            dt.Columns.Add("twoLetterCode");
            dt.Columns.Add("PostCode", typeof(int));

            foreach (CultureInfo cul in cinfo)
            {
                char[] name = cul.Name.ToCharArray();
                if (name.Length >= 2)
                {

                    string twoLetterCode = "" + name[name.Length - 2] + name[name.Length - 1];
                    int code = findCountryCode(twoLetterCode);

                    //Console.WriteLine("Display Name: "+cul.DisplayName + " Name: " + cul.Name +" twoLetterCode: "+ twoLetterCode
                    //+" Country code: "+code);

                    try
                    {
                        DataRow row = dt.NewRow();

                        var rigion = new RegionInfo(cul.Name);
                        countryNameAndCode[rigion.EnglishName] = code;

                        dt.Rows.Add(rigion.EnglishName, rigion.NativeName, code);
                    }
                    catch (ArgumentException e)
                    {
                        //MessageBox.Show(e.ToString());
                        //Console.WriteLine(e.ToString());
                    }
                }

            }


            //await Task.WhenAll();
            //Console.WriteLine("Number of Countries: " + countryNameAndCode.Count);

            //foreach (var kvp in countryNameAndCode)
            //    Console.WriteLine("Country name: {0}, Country code: {1}", kvp.Key, kvp.Value);

            //printDictionaryOutput(countryNameAndCode);
            return dt;
        }


        public static int findCountryCode(string countryShortCode)
        {
            return 967;

            //PhoneNumberUtil phoneUtil = PhoneNumberUtil.GetInstance();

            //return phoneUtil.GetCountryCodeForRegion(countryShortCode.ToUpper());

        }

        public static void printDictionaryOutput(Dictionary<string, int> countryNameAndCode)
        {
            foreach (var kvp in countryNameAndCode)
                Console.WriteLine("Country name: {0}, Country code: {1},", kvp.Key, kvp.Value);

            Console.WriteLine("var countryCodesMapping = new Dictionary<string, string>() {");
            foreach (var mapping in countryNameAndCode.OrderBy(mapping => mapping.Key))
            {
                Console.WriteLine("   {{ \"{0}\", \"{1}\" }},", mapping.Key, mapping.Value);
            }

            Console.WriteLine("};");

        }

    }

}
