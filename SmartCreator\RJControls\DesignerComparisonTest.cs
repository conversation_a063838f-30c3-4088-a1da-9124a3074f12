using System;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// نموذج مقارنة لاختبار Designer
    /// </summary>
    public partial class DesignerComparisonTest : Form
    {
        private SimpleRJTabControl simpleControl;
        private RJTabControl complexControl;
        private Label instructionsLabel;

        public DesignerComparisonTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // instructionsLabel
            // 
            this.instructionsLabel = new Label();
            this.instructionsLabel.Text = "🧪 اختبار مقارنة Designer:\n\n" +
                                         "1. SimpleRJTabControl (أعلى) - نسخة مبسطة\n" +
                                         "2. RJTabControl (أسفل) - النسخة الكاملة\n\n" +
                                         "إذا كان SimpleRJTabControl يعمل في Designer\n" +
                                         "والـ RJTabControl لا يعمل، فالمشكلة في التعقيد\n\n" +
                                         "جرب سحب كل منهما من Toolbox في Visual Studio";
            this.instructionsLabel.Location = new Point(20, 20);
            this.instructionsLabel.Size = new Size(760, 120);
            this.instructionsLabel.Font = new Font("Segoe UI", 11);
            this.instructionsLabel.ForeColor = Color.FromArgb(70, 70, 70);

            // 
            // simpleControl
            // 
            this.simpleControl = new SimpleRJTabControl();
            this.simpleControl.Location = new Point(20, 160);
            this.simpleControl.Size = new Size(760, 150);
            this.simpleControl.TestProperty = "Simple Control Works!";
            this.simpleControl.DisplayText = "✅ SimpleRJTabControl\n\nهذا الكنترول المبسط يجب أن يعمل\nفي Designer بدون مشاكل";

            // 
            // complexControl
            // 
            try
            {
                this.complexControl = new RJTabControl();
                this.complexControl.Location = new Point(20, 330);
                this.complexControl.Size = new Size(760, 200);
                
                // إضافة تاب للاختبار
                var testTab = new RJTabPage("اختبار");
                testTab.BackColor = Color.FromArgb(0, 122, 204);
                testTab.ForeColor = Color.White;
                
                var testLabel = new Label
                {
                    Text = "✅ RJTabControl الكامل\n\nإذا رأيت هذا النص، فالكنترول\nيعمل بدون مشاكل",
                    Dock = DockStyle.Fill,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    ForeColor = Color.White
                };
                testTab.AddControl(testLabel);
                this.complexControl.AddTab(testTab);
            }
            catch (Exception ex)
            {
                // إذا فشل إنشاء RJTabControl، أنشئ label بدلاً منه
                var errorLabel = new Label
                {
                    Text = $"❌ خطأ في إنشاء RJTabControl:\n\n{ex.Message}",
                    Location = new Point(20, 330),
                    Size = new Size(760, 200),
                    BackColor = Color.FromArgb(244, 67, 54),
                    ForeColor = Color.White,
                    TextAlign = ContentAlignment.MiddleCenter,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold)
                };
                this.Controls.Add(errorLabel);
            }

            // 
            // DesignerComparisonTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(800, 560);
            this.Controls.Add(this.instructionsLabel);
            this.Controls.Add(this.simpleControl);
            if (this.complexControl != null)
                this.Controls.Add(this.complexControl);
            this.Name = "DesignerComparisonTest";
            this.Text = "🧪 اختبار مقارنة Designer - Simple vs Complex";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.White;

            this.ResumeLayout(false);
        }

        /// <summary>
        /// تشغيل اختبار المقارنة
        /// </summary>
        public static void RunComparison()
        {
            try
            {
                var form = new DesignerComparisonTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل اختبار المقارنة:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء SimpleRJTabControl فقط
        /// </summary>
        public static void TestSimpleControlOnly()
        {
            try
            {
                var simpleControl = new SimpleRJTabControl();
                simpleControl.TestProperty = "Test successful!";
                simpleControl.DisplayText = "Simple control created successfully!";

                MessageBox.Show($"✅ SimpleRJTabControl تم إنشاؤه بنجاح!\n\n" +
                               $"TestProperty: {simpleControl.TestProperty}\n" +
                               $"DisplayText: {simpleControl.DisplayText}\n\n" +
                               "يمكن الآن اختبار هذا الكنترول في Designer",
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء SimpleRJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار إنشاء RJTabControl مع معلومات مفصلة
        /// </summary>
        public static void TestComplexControlDetailed()
        {
            try
            {
                var complexControl = new RJTabControl();
                
                // اختبار الخصائص واحدة تلو الأخرى
                var results = "✅ اختبار RJTabControl مفصل:\n\n";
                
                try { var count = complexControl.TabCount; results += $"TabCount: {count} ✅\n"; }
                catch (Exception ex) { results += $"TabCount: خطأ - {ex.Message} ❌\n"; }
                
                try { var index = complexControl.SelectedIndex; results += $"SelectedIndex: {index} ✅\n"; }
                catch (Exception ex) { results += $"SelectedIndex: خطأ - {ex.Message} ❌\n"; }
                
                try { var initialized = complexControl.IsInitialized; results += $"IsInitialized: {initialized} ✅\n"; }
                catch (Exception ex) { results += $"IsInitialized: خطأ - {ex.Message} ❌\n"; }
                
                try { var height = complexControl.TabHeight; results += $"TabHeight: {height} ✅\n"; }
                catch (Exception ex) { results += $"TabHeight: خطأ - {ex.Message} ❌\n"; }
                
                try { var spacing = complexControl.TabSpacing; results += $"TabSpacing: {spacing} ✅\n"; }
                catch (Exception ex) { results += $"TabSpacing: خطأ - {ex.Message} ❌\n"; }

                results += "\n🎯 إذا كانت جميع الخصائص تعمل هنا\nولكن Designer يفشل، فالمشكلة في Designer نفسه";

                MessageBox.Show(results, "نتائج الاختبار المفصل", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في إنشاء RJTabControl:\n\n{ex.Message}\n\n{ex.StackTrace}",
                               "خطأ مفصل", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
