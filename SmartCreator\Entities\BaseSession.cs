﻿using SmartCreator.Data;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.ComponentModel;



//using System.Collections.Generic;
//using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities
{
    //[System.Reflection.Obfuscation(ApplyToMembers = false)]
    public class BaseSession
    {
        //public BaseSession() { }

        [DisplayName("الاسم"),StringLength(200),Required/*,Index*/]
        public string UserName { get; set; }

        [DisplayName("الجهاز (NAS)"), StringLength(100)]
        public string NasPortId { get; set; }

        [DisplayName("الماك"),StringLength(200)]
        public string CallingStationId { get; set; }
        
        [DisplayName("الايبي (IP)"),StringLength(200)/*,Index*/]
        public string IpUser { get; set; }
        
        [DisplayName(" الراديوس (IpRouter)"), StringLength(200)/*, Index*/]
        public string IpRouter { get; set; }
        //public string acctSessionId { get; set; }// hex

        [DisplayName("بداية الجلسة"), Index]
        public DateTime? FromTime { get; set; }

        [DisplayName("الي تاريخ")/*, Index*/]
        public DateTime? TillTime { get; set; }
        //public long TillTime { get; set; }

        [Browsable(false), Default(0)]
        public long UpTime { get; set; }

        [Browsable(false), Default(0)]
        public long BytesDownload { get; set; }

        [Browsable(false),Default(0)]
        public long BytesUpload { get; set; }


        [Computed, DisplayName("الوقت المستخدم")]
        public string Str_UptimeUsed
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(UpTime);
            }
        }
        [Computed, DisplayName("التحميل المستخدم")]
        public string Str_DownloadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(BytesDownload.ToString());
                else return utils.ConvertSize_Get_En(BytesDownload.ToString());

            }
        }
        [Computed, DisplayName("الرقع المستخدم")]
        public string Str_UploadUsed
        {
            get
            {
                if (UIAppearance.Language_ar) return utils.ConvertSize_Get_InArabic(BytesUpload.ToString());
                else return utils.ConvertSize_Get_En(BytesUpload.ToString());

            }
        }



        [Default(0)]
        public int DeleteFromServer { get; set; } = 0;
        //public bool DeleteFromServer { get; set; } = false;
        [Browsable(false)]
        public DateTime? AddedDb { get; set; }
        [Browsable(false)]
        public DateTime? LastSynDb { get; set; }

        [StringLength(100), Browsable(false)]
        public string MkId { get; set; }

    }

}
