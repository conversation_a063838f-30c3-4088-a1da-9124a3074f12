﻿using SmartCreator.Properties;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace SmartCreator.Forms
{
    public partial class UC_StatusBar_Info : UserControl
    {
        public UC_StatusBar_Info()
        {
            InitializeComponent();
            //lblDescription.RightToLeft = System.Windows.Forms.RightToLeft.No;
            //lblSelected.RightToLeft = System.Windows.Forms.RightToLeft.No;


            rjPanel_top_border.BackColor = UIAppearance.FormBorderColor;
            rjPanel_top_border.Height = 3;
            //rjPanel_top_border.Height = UIAppearance.FormBorderSize;
            //if (UIAppearance.Language_ar)
            //{
                Font fnt=Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
                //Font fnt= CustomFonts.Get_Custom_Font("Cairo_SemiBold", 10f, false, GraphicsUnit.Point, 0);
                

                lblDescription.Font = fnt;

            //rjProgressBar1

                lblSelected.Font = fnt;
            utils.Control_textSize(this);
            //}
        //lblSelected.Font = Program.GetCustomFont(Resources.Cairo_SemiBold, 10, FontStyle.Regular);
            
            //lblSelected.Font = new Font("Verdana", 12);
        }
    }
}
