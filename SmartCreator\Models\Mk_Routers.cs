﻿//using ServiceStack.OrmLite;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Models
{
    public class Mk_Routers
    {
        public Mk_Routers() { }
        public string soft_id { get; set; }
        public string mk_sn { get; set; }
        public string mk_code { get; set; }
        public string comment { get; set; }
        public string localDB_path { get; set; }
        public string localDB_fileName { get; set; }
        public Base_Resources Resources { get; set; }
    }
    public class Connections_Db
    {
        public Connections_Db() { }
        public string Id { get; set; }
        public string Soft_id { get; set; }
        public string Mk_sn { get; set; }
        public string Mk_code { get; set; }
        public string Comment { get; set; }
        public string LocalDB_path { get; set; }
        public string ArchiveDB_path { get; set; }
        public string FileName { get; set; }
        public Base_Resources _Resources { get; set; } = null;
        public string Resources { get; set; } = null;
        public int Default {  get; set; }
        public string Type {  get; set; }
        public string Connection_string {  get; set; }
        public string Name {  get; set; }
        public string Username_db {  get; set; }
        public string Password_db {  get; set; }

        //public IOrmLiteDialectProvider provider { get; set; }=null;
    }
}
