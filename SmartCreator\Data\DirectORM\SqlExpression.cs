using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Linq.Expressions;
using System.Text;

namespace SmartCreator.Data.DirectORM
{
    /// <summary>
    /// Base SqlExpression class
    /// </summary>
    public abstract class SqlExpression
    {
        protected StringBuilder _selectClause = new();
        protected StringBuilder _fromClause = new();
        protected StringBuilder _whereClause = new();
        protected StringBuilder _joinClause = new();
        protected StringBuilder _groupByClause = new();
        protected StringBuilder _orderByClause = new();
        protected StringBuilder _havingClause = new();
        protected int? _take;
        protected int? _skip;

        public abstract string ToSql();
    }

    /// <summary>
    /// Typed SqlExpression for building SQL queries
    /// </summary>
    public class SqlExpression<T> : SqlExpression where T : class, new()
    {
        private readonly IDbConnection _db;
        private readonly string _tableName;

        public SqlExpression(IDbConnection db)
        {
            _db = db;
            _tableName = SqlGenerator.GetTableName<T>();
            _fromClause.Append($"FROM {_tableName}");
            _selectClause.Append("*");
        }

        /// <summary>
        /// Add WHERE condition
        /// </summary>
        public SqlExpression<T> Where(Expression<Func<T, bool>> predicate)
        {
            var whereCondition = ExpressionTranslator.Translate(predicate);

            if (_whereClause.Length == 0)
                _whereClause.Append($"WHERE {whereCondition}");
            else
                _whereClause.Append($" AND {whereCondition}");

            return this;
        }

        /// <summary>
        /// Add OR condition
        /// </summary>
        public SqlExpression<T> Or(Expression<Func<T, bool>> predicate)
        {
            var whereCondition = ExpressionTranslator.Translate(predicate);

            if (_whereClause.Length == 0)
                _whereClause.Append($"WHERE {whereCondition}");
            else
                _whereClause.Append($" OR {whereCondition}");

            return this;
        }

        /// <summary>
        /// Add JOIN
        /// </summary>
        public SqlExpression<T> Join<TJoin>() where TJoin : class, new()
        {
            var joinTable = SqlGenerator.GetTableName<TJoin>();
            var joinCondition = $"{_tableName}.{joinTable}Id = {joinTable}.Id";
            _joinClause.Append($" INNER JOIN {joinTable} ON {joinCondition}");
            return this;
        }

        /// <summary>
        /// Add JOIN with custom condition
        /// </summary>
        public SqlExpression<T> Join<TJoin>(Expression<Func<T, TJoin, bool>> joinCondition) where TJoin : class, new()
        {
            var joinTable = SqlGenerator.GetTableName<TJoin>();
            var condition = ExpressionTranslator.Translate(joinCondition);
            _joinClause.Append($" INNER JOIN {joinTable} ON {condition}");
            return this;
        }

        /// <summary>
        /// Add WHERE condition for joined table
        /// </summary>
        public SqlExpression<T> Where<TJoin>(Expression<Func<TJoin, bool>> predicate) where TJoin : class, new()
        {
            var whereCondition = ExpressionTranslator.Translate(predicate, SqlGenerator.GetTableName<TJoin>());

            if (_whereClause.Length == 0)
                _whereClause.Append($"WHERE {whereCondition}");
            else
                _whereClause.Append($" AND {whereCondition}");

            return this;
        }

        /// <summary>
        /// Custom SELECT clause
        /// </summary>
        public SqlExpressionResult<TResult> Select<TResult>(Expression<Func<T, TResult>> selector)
        {
            var selectClause = ExpressionTranslator.TranslateSelect(selector);
            return new SqlExpressionResult<TResult>(_db, selectClause, _fromClause.ToString(), _whereClause.ToString(), _joinClause.ToString(), _groupByClause.ToString(), _orderByClause.ToString(), _havingClause.ToString(), _take, _skip);
        }

        /// <summary>
        /// Custom SELECT clause for multiple tables
        /// </summary>
        public SqlExpressionResult<TResult> Select<TJoin, TResult>(Expression<Func<T, TJoin, TResult>> selector) where TJoin : class, new()
        {
            var selectClause = ExpressionTranslator.TranslateSelect(selector);
            return new SqlExpressionResult<TResult>(_db, selectClause, _fromClause.ToString(), _whereClause.ToString(), _joinClause.ToString(), _groupByClause.ToString(), _orderByClause.ToString(), _havingClause.ToString(), _take, _skip);
        }

        /// <summary>
        /// Add ORDER BY
        /// </summary>
        public SqlExpression<T> OrderBy(Expression<Func<T, object>> selector)
        {
            var orderByClause = ExpressionTranslator.TranslateOrderBy(selector);

            if (_orderByClause.Length == 0)
                _orderByClause.Append($"ORDER BY {orderByClause}");
            else
                _orderByClause.Append($", {orderByClause}");

            return this;
        }

        /// <summary>
        /// Add ORDER BY DESC
        /// </summary>
        public SqlExpression<T> OrderByDescending(Expression<Func<T, object>> selector)
        {
            var orderByClause = ExpressionTranslator.TranslateOrderBy(selector);

            if (_orderByClause.Length == 0)
                _orderByClause.Append($"ORDER BY {orderByClause} DESC");
            else
                _orderByClause.Append($", {orderByClause} DESC");

            return this;
        }

        /// <summary>
        /// Add ORDER BY with string column name
        /// </summary>
        public SqlExpression<T> OrderByDescending(string columnName)
        {
            if (_orderByClause.Length == 0)
                _orderByClause.Append($"ORDER BY {columnName} DESC");
            else
                _orderByClause.Append($", {columnName} DESC");

            return this;
        }

        /// <summary>
        /// Add GROUP BY
        /// </summary>
        public SqlExpression<T> GroupBy(Expression<Func<T, object>> selector)
        {
            var groupByClause = ExpressionTranslator.TranslateGroupBy(selector);

            if (_groupByClause.Length == 0)
                _groupByClause.Append($"GROUP BY {groupByClause}");
            else
                _groupByClause.Append($", {groupByClause}");

            return this;
        }

        /// <summary>
        /// Add TAKE (LIMIT)
        /// </summary>
        public SqlExpression<T> Take(int count)
        {
            _take = count;
            return this;
        }

        /// <summary>
        /// Add SKIP (OFFSET)
        /// </summary>
        public SqlExpression<T> Skip(int count)
        {
            _skip = count;
            return this;
        }

        /// <summary>
        /// Execute and return results
        /// </summary>
        public List<T> ToList()
        {
            return _db.ExecuteQuery<T>(ToSql());
        }

        /// <summary>
        /// Execute and return first result
        /// </summary>
        public T First()
        {
            var results = Take(1).ToList();
            if (results.Count == 0)
                throw new InvalidOperationException("No records found");
            return results[0];
        }

        /// <summary>
        /// Execute and return first result or default
        /// </summary>
        public T? FirstOrDefault()
        {
            var results = Take(1).ToList();
            return results.FirstOrDefault();
        }

        /// <summary>
        /// Build SQL string
        /// </summary>
        public override string ToSql()
        {
            var sql = new StringBuilder();
            sql.Append($"SELECT {_selectClause} ");
            sql.Append($"{_fromClause}");

            if (_joinClause.Length > 0)
                sql.Append($"{_joinClause}");

            if (_whereClause.Length > 0)
                sql.Append($" {_whereClause}");

            if (_groupByClause.Length > 0)
                sql.Append($" {_groupByClause}");

            if (_havingClause.Length > 0)
                sql.Append($" {_havingClause}");

            if (_orderByClause.Length > 0)
                sql.Append($" {_orderByClause}");

            if (_take.HasValue)
                sql.Append($" LIMIT {_take.Value}");

            if (_skip.HasValue)
                sql.Append($" OFFSET {_skip.Value}");

            return sql.ToString();
        }
    }

    /// <summary>
    /// SqlExpression result for non-entity types
    /// </summary>
    public class SqlExpressionResult<T>
    {
        private readonly IDbConnection _db;
        private readonly string _selectClause;
        private readonly string _fromClause;
        private readonly string _whereClause;
        private readonly string _joinClause;
        private readonly string _groupByClause;
        private readonly string _orderByClause;
        private readonly string _havingClause;
        private readonly int? _take;
        private readonly int? _skip;

        public SqlExpressionResult(IDbConnection db, string selectClause, string fromClause, string whereClause, string joinClause, string groupByClause, string orderByClause, string havingClause, int? take, int? skip)
        {
            _db = db;
            _selectClause = selectClause;
            _fromClause = fromClause;
            _whereClause = whereClause;
            _joinClause = joinClause;
            _groupByClause = groupByClause;
            _orderByClause = orderByClause;
            _havingClause = havingClause;
            _take = take;
            _skip = skip;
        }

        /// <summary>
        /// Build SQL string
        /// </summary>
        public string ToSql()
        {
            var sql = new StringBuilder();
            sql.Append($"SELECT {_selectClause} ");
            sql.Append($"{_fromClause}");

            if (!string.IsNullOrEmpty(_joinClause))
                sql.Append($"{_joinClause}");

            if (!string.IsNullOrEmpty(_whereClause))
                sql.Append($" {_whereClause}");

            if (!string.IsNullOrEmpty(_groupByClause))
                sql.Append($" {_groupByClause}");

            if (!string.IsNullOrEmpty(_havingClause))
                sql.Append($" {_havingClause}");

            if (!string.IsNullOrEmpty(_orderByClause))
                sql.Append($" {_orderByClause}");

            if (_take.HasValue)
                sql.Append($" LIMIT {_take.Value}");

            if (_skip.HasValue)
                sql.Append($" OFFSET {_skip.Value}");

            return sql.ToString();
        }

        /// <summary>
        /// Execute and return scalar result
        /// </summary>
        public T ExecuteScalar()
        {
            using var command = _db.CreateCommand();
            command.CommandText = ToSql();

            var result = command.ExecuteScalar();
            return (T)Convert.ChangeType(result!, typeof(T));
        }
    }
}
