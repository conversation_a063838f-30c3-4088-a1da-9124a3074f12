﻿using Newtonsoft.Json;
using SmartCreator.db;
using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;

namespace SmartCreator.Data
{
    public class Helper
    {
        public static void InitDb()
        {

            //Connections.Show();
            //return;
        retry:
            try
            {
                ////check in first time add default data
                ////add fist employee
                //if (Kt.Db.Count<UmUser>() == 0)
                //    Kt.Db.Save(
                //        new UmUser
                //        {
                //            UserName = "<EMAIL>",
                //            //Name = "Admin",
                //            //Password = "A00000".ToSHA512Hash(),
                //            //Role = "Admin"
                //        });

            }
            catch (Exception)
            {
                //connection faile show db options
                Connections.Show();
                try
                {
                    var db = Kt.Db;
                }
                catch (Exception err)
                {
                    if (MessageBox.Show(err.Message, "CONNECTON ERROR", MessageBoxButtons.RetryCancel, MessageBoxIcon.Warning) == DialogResult.Retry)
                        goto retry;
                    Environment.Exit(0);
                }
            }
        }


        public static class ConfigDB
        {
            public static string ConnectionsFile = "connections.json";

            public static Color Theme = Color.DodgerBlue;

            public static bool AutoGenerateTables = true;
        }
    }

    public static class Kt
    {
        public static IDbConnection Db => Connections.GetConnection();
    }


    //[DebuggerStepThrough]
    //public static class Connections
    //{
    //     }




    public static class Config
    {
        public static string ConnectionsFile = "connections.json";

        public static Color Theme = Color.DodgerBlue;

        public static bool AutoGenerateTables = true;
    }
    internal class TableInfo
    {
        public int Order { get; set; }

        public Type type { get; set; }
    }

    [AttributeUsage(AttributeTargets.Class)]
    public class AutoGenerateTableAttribute : Attribute
    {
        private readonly int order;

        public AutoGenerateTableAttribute(int order = 0)
        {
            this.order = order;
        }
    }

    public class ConnectionInfo
    {
        [JsonProperty("default")]
        public bool Default { get; set; }

        [JsonProperty("type")]
        public string Type { get; set; }

        [JsonProperty("connection_string")]
        public string ConnectionString { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }
    }
}
