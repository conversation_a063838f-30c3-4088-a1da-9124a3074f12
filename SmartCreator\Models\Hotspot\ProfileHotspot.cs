﻿using Dapper;
using SmartCreator.Data;
using System;
using System.Collections.Generic;
using System.Data.SQLite;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using tik4net;
using System.ComponentModel;
using SmartCreator.Utils;
using SmartCreator.Settings;
//using ServiceStack.DataAnnotations;

namespace SmartCreator.Models.hotspot
{
    public class Hotspot_Local_Profile2  // البروفايلات المحليه الخاصه بسمارت كريتور من قاعدة البيانات
    {
        private string uptimeLimit_str;
        private string validity_str;
        private int add_Smart_Scripts;
        private int save_time;
        private int save_download;
        private int save_session;
        private int byDayOrHour;


        //[Browsable(false)]
        public int id { get; set; }

        [DisplayName("الاسم")]
        public string Name { get; set; }
        //[Browsable(false)]
        public double uptimeLimit { get; set; } = 0;//in second
        [DisplayName("الوقت")]

        public string UptimeLimit_str
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(uptimeLimit);
            }

        }
        //[Browsable(false)]
        [DisplayName("الصلاحية")]

        public double Validity { get; set; } = 0; // in days
        [DisplayName("الصلاحية")]
        //[Browsable(false)]
        public double transferLimit { get; set; } = 0; //in byte
        [DisplayName("التحميل")]

        public string transferLimit_str
        {
            get
            {
                string d = "";
                if (UIAppearance.Language_ar)
                    d = utils.ConvertSize_Get_InArabic(transferLimit.ToString());
                else
                    d = utils.ConvertSize_Get_En(transferLimit.ToString());
                return d;
            }
            set { validity_str = value; }
        }
        [DisplayName("سعر البيع")]

        public int Price_Sales { get; set; } = 0;
        [DisplayName("سعر العرض")]

        public string Price_display { get; set; } = "0";
        //[Browsable(false)]
        public int Add_Smart_Scripts { get => add_Smart_Scripts; set => add_Smart_Scripts = value; }
        public int Save_time { get => save_time; set => save_time = value; }
        public int Save_download { get => save_download; set => save_download = value; }
        public int Save_session { get => save_session; set => save_session = value; }
        public int ByDayOrHour { get => byDayOrHour; set => byDayOrHour = value; }
        [Default(0)]
        public int Is_percentage { get; set; } = 0;

        [Default(0), DisplayName("النسبة")]
        public float Percentage { get; set; } = 0;

        [Default(0), DisplayName("طريقة حساب النسبة")]
        public int PercentageType { get; set; } = 0;
        [DisplayName("سكربت الصلاحية")]
        public bool add_Smart_Scripts_str
        {
            get
            {
                return Convert.ToBoolean(add_Smart_Scripts);
            }


        }
        [DisplayName("بروفايل الهوتسبوت")]

        public string link_hotspot_profile { get; set; } = "";
        //[Browsable(false)]
        public string rb { get; set; }

        public string Add_ProfiProfileHotspotLocal_to_DB2(List<Hotspot_Local_Profile2> UM_ProfileHotspot, bool is_insert = true)
        {
            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    query = "INSERT OR IGNORE into Hotspot_Profile_Hotspot_local (" +
                            "[idHX], " +
                            "[Name], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "update Hotspot_Profile_Hotspot_local set " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                     " WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.ExecuteAsync(query, UM_ProfileHotspot, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }

        public List<Hotspot_Local_Profile2> Ge_ProfileHotspotLocal_FromDB2()
        {
            List<Hotspot_Local_Profile2> ProfileProfileHotspot = new List<Hotspot_Local_Profile2>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<Hotspot_Local_Profile2>("select * from Hotspot_Profile_Hotspot_local where   rb='" + rb + "' ; ", new DynamicParameters());
                    ProfileProfileHotspot = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return ProfileProfileHotspot;
        }
 
        public Hotspot_Local_Profile2 Ge_ProfileHotspotLocal_FromDB_by_name2(string name)
        {
            //Hotspot_Profile_hotspot ProfileProfileHotspot = new Hotspot_Profile_hotspot();

            string query = "select * from Hotspot_Profile_Hotspot_local where  Name='" + name + "'  and rb='" + Global_Variable.Mk_resources.RB_SN + "'  ;";
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    var output = cnn.QueryFirstOrDefault<Hotspot_Local_Profile2>(query);
                    return output;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        }

    }
 
    
    public class Hotspot_Source_Profile //== بروفايلات الهوتسبوت من المايكروتك
    {
        public int id { get; set; }
        public string idHX { get; set; }
        public string Name { get; set; }
        public string Onlogin { get; set; }
        public string Onlogout { get; set; }
        public int Delet_fromServer { get; set; }
        public string rb { get; set; }


        public void Syn_Source_ProfileHotspot_to_LocalDB()
        {
            if (Global_Variable.Source_HS_Profile == null)
                return;
            Add_ProfiProfileHotspot_to_LocalDB(Global_Variable.Source_HS_Profile);
            //List<Hotspot_Source_Profile> sourceProfileHotspot = Ge_ProfileHotspot_FromDB();
            Set_SourceProfileHotspot_disable_LocalDB();
            //Add_ProfiProfileHotspot_to_LocalDB(sourceProfileHotspot, false);
            Add_ProfiProfileHotspot_to_LocalDB(Global_Variable.Source_HS_Profile, false);
        }
        public string Add_ProfiProfileHotspot_to_LocalDB(List<Hotspot_Source_Profile> UM_ProfileHotspot, bool is_insert = true)
        {
            string rb = Global_Variable.Mk_resources.RB_SN;
            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    query = "INSERT OR IGNORE into Hotspot_Source_Profile (" +
                            "[idHX], " +
                            "[Name], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "update Hotspot_Source_Profile set " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                     " WHERE Name = @Name and rb='"+rb+"';";
                     //" WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.ExecuteAsync(query, UM_ProfileHotspot, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public  List<Hotspot_Source_Profile> Ge_Source_ProfileHotspot_FromDB()
        {
            List<Hotspot_Source_Profile> ProfileProfileHotspot = new List<Hotspot_Source_Profile>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<Hotspot_Source_Profile>("select * from Hotspot_Source_Profile where Delet_fromServer=0 and rb='" + rb + "' ; ", new DynamicParameters());
                    ProfileProfileHotspot = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return ProfileProfileHotspot;
        }
        public  void Set_SourceProfileHotspot_disable_LocalDB()
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE Hotspot_Source_Profile SET Delet_fromServer = 1 WHERE Delet_fromServer = 0 and rb='" + rb + "' ;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
    }


    public class Hotspot_Server_Profile 
    {
        public int id { get; set; }
        public string idHX { get; set; }
        public string Name { get; set; }
        public int Delet_fromServer { get; set; } 
        public string rb { get; set; }
        public string html_directory { get; set; }


        //[Obsolete]
        [Obsolete]
        public List<Hotspot_Server_Profile> Get_Hotspot_Server_Profil_from_Router()
        {

            List<Hotspot_Server_Profile> customer =null;
            try
            {
                using (ITikConnection connection = ConnectionFactory.CreateConnection(TikConnectionType.Api))
                {
                    if (Mk_DataAccess.Mk_Conn(connection) == false)
                        return null;
                    var loadCmd = connection.CreateCommandAndParameters("/ip/hotspot/profile/print");
                    var response = loadCmd.ExecuteList();

                 customer = new List<Hotspot_Server_Profile>();

                    foreach (var item in response)
                    {
                        Hotspot_Server_Profile sourceCustomer = new Hotspot_Server_Profile();
                        //double sn = 0;
                        string Number_sequence = item.GetResponseFieldOrDefault(".id", "0");
                        //Number_sequence = Number_sequence.TrimStart(new char[] { '*' });
                        sourceCustomer.idHX = Number_sequence;

                        //Number_sequence = Int32.Parse(Number_sequence, NumberStyles.HexNumber).ToString();

                        //sn = Convert.ToDouble(Number_sequence);
                        sourceCustomer.Name = item.GetResponseFieldOrDefault("name", "");
                        sourceCustomer.html_directory = item.GetResponseFieldOrDefault("html-directory", "");
                        sourceCustomer.Delet_fromServer = 0;
                        sourceCustomer.rb = Global_Variable.Mk_resources.RB_SN;

                        customer.Add(sourceCustomer);
                    }

                }
            }
            catch { }
            return customer;
        }
        
        
        public void Syn_Source_ProfileHotspot_to_LocalDB()
        {
            Add_ProfiProfileHotspot_to_LocalDB(Global_Variable.Source_HS_Profile);
            //List<Hotspot_Source_Profile> sourceProfileHotspot = Ge_ProfileHotspot_FromDB();
            Set_SourceProfileHotspot_disable_LocalDB();
            //Add_ProfiProfileHotspot_to_LocalDB(sourceProfileHotspot, false);
            Add_ProfiProfileHotspot_to_LocalDB(Global_Variable.Source_HS_Profile, false);
        }
        public string Add_ProfiProfileHotspot_to_LocalDB(List<Hotspot_Source_Profile> UM_ProfileHotspot, bool is_insert = true)
        {
            string rb = Global_Variable.Mk_resources.RB_SN;
            string status = "true";
            try
            {
                string query = "";
                if (is_insert)
                {
                    query = "INSERT OR IGNORE into Hotspot_Source_Profile (" +
                            "[idHX], " +
                            "[Name], " +
                            "[rb], " +
                            "[Delet_fromServer] " +
                            ") " +
                            "values (" +
                            "@idHX, " +
                            "@Name, " +
                            "@rb, " +
                            "@Delet_fromServer " +
                            ") ";
                }

                else
                {
                    query = "update Hotspot_Source_Profile set " +
                     "[Delet_fromServer]=@Delet_fromServer " +
                     " WHERE Name = @Name and rb='" + rb + "';";
                    //" WHERE id = @id;";
                }
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    cnn.Open();
                    var sqLiteTransaction = cnn.BeginTransaction();
                    var output = cnn.ExecuteAsync(query, UM_ProfileHotspot, sqLiteTransaction);
                    sqLiteTransaction.Commit();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); status = "false"; }
            return status;
        }
        public List<Hotspot_Source_Profile> Ge_Source_ProfileHotspot_FromDB()
        {
            List<Hotspot_Source_Profile> ProfileProfileHotspot = new List<Hotspot_Source_Profile>();
            try
            {
                using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
                {
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<Hotspot_Source_Profile>("select * from Hotspot_Source_Profile where Delet_fromServer=0 and rb='" + rb + "' ; ", new DynamicParameters());
                    ProfileProfileHotspot = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return ProfileProfileHotspot;
        }
        public void Set_SourceProfileHotspot_disable_LocalDB()
        {
            string rb = Global_Variable.Mk_resources.RB_SN;

            using (IDbConnection cnn = new SQLiteConnection(SqlDataAccess.LoadConnectionString()))
            {
                cnn.Open();
                var sqLiteTransaction = cnn.BeginTransaction();
                string query = "UPDATE Hotspot_Source_Profile SET Delet_fromServer = 1 WHERE Delet_fromServer = 0 and rb='" + rb + "' ;";
                cnn.Execute(query, sqLiteTransaction);
                sqLiteTransaction.Commit();
            }
        }
    
    }



}
