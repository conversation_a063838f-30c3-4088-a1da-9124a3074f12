﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="SmartCreator.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
      <section name="SmartCreator.Settings.UIAppearanceSettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
      <section name="SmartCreator.Settings.UIAppearance" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <connectionStrings>
    <!--"ConnectionStrings": {
    "default": "server=localhost;database=PersonDb;user id=root;password=***************"
    },-->
    <!--<add name="Smartdb" connectionString="server=localhost;database=smart;UID=root;password=;" providerName="System.Data.SqlClient"/>-->
    <add name="Smartdb" connectionString="Data Source=.\db\Smart.db;Version=3;" providerName="System.Data.SqlClient" />
    <add name="AppDbContext" connectionString="data source=ALSAIDI\SQLEXPRESS;initial catalog=SmartCreator.Data.AppDbContext;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <add name="SmartDbContext" connectionString="data source=ALSAIDI\SQLEXPRESS;initial catalog=SmartCreator.Data.SmartDbContext;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" />
    <!-- قاعدة البيانات الأصلية -->
    <add name="DefaultConnection" connectionString="Data Source=MikroTikManager.db;Version=3;" providerName="System.Data.SQLite" />
    <!-- قاعدة بيانات الإعدادات -->
    <add name="SmartConnection" connectionString="Data Source=Smart.db;Version=3;" providerName="System.Data.SQLite" />
    <!-- قاعدة البيانات المحلية -->
    <add name="LocalDbConnection" connectionString="Data Source=LocalDB.db;Version=3;" providerName="System.Data.SQLite" />
  </connectionStrings>
  <!--<add name="AppDbContext" connectionString="data source=(LocalDb)\MSSQLLocalDB;initial catalog=SmartCreator.Data.AppDbContext;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework" providerName="System.Data.SqlClient" /></connectionStrings>-->
  <startup>
    <!--<TargetFramework>net6.0-windows</TargetFramework>-->
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="x86" />
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.1.0" newVersion="6.0.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-13.0.0.0" newVersion="13.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Primitives" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Caching.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Options" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Logging.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.Configuration.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.1.32.0" newVersion="3.1.32.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ComponentModel.Annotations" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.InteropServices.RuntimeInformation" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Collections.Immutable" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.1" newVersion="9.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.5" newVersion="8.0.0.5" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.4.0" newVersion="4.0.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.1.0.0" newVersion="9.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Npgsql" publicKeyToken="5d8b90d52f46fda7" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.5.0" newVersion="8.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Channels" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SQLitePCLRaw.core" publicKeyToken="1488e028ca7ab535" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.1.10.2445" newVersion="2.1.10.2445" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.5.0" newVersion="4.1.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Formats.Asn1" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.1" newVersion="9.0.0.1" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <userSettings>
    <SmartCreator.Properties.Settings>
      <setting name="userman_print" serializeAs="String">
        <value />
      </setting>
      <setting name="userman7_print" serializeAs="String">
        <value />
      </setting>
      <setting name="PathFolderBckgroundCards" serializeAs="String">
        <value />
      </setting>
      <setting name="PathFolderPrint" serializeAs="String">
        <value />
      </setting>
      <setting name="script_add" serializeAs="String">
        <value />
      </setting>
      <setting name="countOpenoffline" serializeAs="String">
        <value>10</value>
      </setting>
      <setting name="isActive" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="snactrue" serializeAs="String">
        <value />
      </setting>
      <setting name="hotspot_print" serializeAs="String">
        <value />
      </setting>
      <setting name="RigesterName" serializeAs="String">
        <value />
      </setting>
      <setting name="mobail" serializeAs="String">
        <value />
      </setting>
      <setting name="address" serializeAs="String">
        <value />
      </setting>
      <setting name="active_Period" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="country" serializeAs="String">
        <value />
      </setting>
      <setting name="email" serializeAs="String">
        <value />
      </setting>
      <setting name="licenseCodeForActive" serializeAs="String">
        <value />
      </setting>
      <setting name="Lsn_k_type" serializeAs="String">
        <value>router</value>
      </setting>
      <setting name="activationType" serializeAs="String">
        <value>router</value>
      </setting>
      <setting name="NetworkName" serializeAs="String">
        <value />
      </setting>
      <setting name="rb_active_exp" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="RigesterStartDate" serializeAs="String">
        <value />
      </setting>
      <setting name="RigesterEndDate" serializeAs="String">
        <value />
      </setting>
      <setting name="restCash" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="is_msg_Show" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="msg_Show" serializeAs="String">
        <value />
      </setting>
      <setting name="count_msg_Show" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="DashBoardServerType" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="DashBoardCommi" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="Show_updaeForm" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="Show_updaeForm_V" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="Show_updaeForm_AppBuilder" serializeAs="String">
        <value>0</value>
      </setting>
    </SmartCreator.Properties.Settings>
    <SmartCreator.Settings.UIAppearanceSettings>
      <setting name="Theme" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="ColorFormBorder" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="ChildFormMarker" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FormIconActiveMenuItem" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="MultiChildForms" serializeAs="String">
        <value>True</value>
      </setting>
      <setting name="FormBorderSize" serializeAs="String">
        <value>5</value>
      </setting>
      <setting name="Language_en" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="Style" serializeAs="String">
        <value>3</value>
      </setting>
      <setting name="Language_ar" serializeAs="String">
        <value>True</value>
      </setting>
    </SmartCreator.Settings.UIAppearanceSettings>
    <SmartCreator.Settings.UIAppearance>
      <setting name="Theme" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="Style" serializeAs="String">
        <value>0</value>
      </setting>
      <setting name="DefaultBorderColor" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="ChildFormMarker" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="FormIconActiveMenuItem" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="MultiChildForms" serializeAs="String">
        <value>False</value>
      </setting>
    </SmartCreator.Settings.UIAppearance>
  </userSettings>
  <appSettings>
    <add key="ClientSettingsProvider.ServiceUri" value="" />
  </appSettings>
  <system.web>
    <membership defaultProvider="ClientAuthenticationMembershipProvider">
      <providers>
        <add name="ClientAuthenticationMembershipProvider" type="System.Web.ClientServices.Providers.ClientFormsAuthenticationMembershipProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" />
      </providers>
    </membership>
    <roleManager defaultProvider="ClientRoleProvider" enabled="true">
      <providers>
        <add name="ClientRoleProvider" type="System.Web.ClientServices.Providers.ClientRoleProvider, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" serviceUri="" cacheTimeout="86400" />
      </providers>
    </roleManager>
  </system.web>


 
</configuration>

 