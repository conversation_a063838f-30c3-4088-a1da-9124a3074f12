﻿using iTextSharp.text;
using SmartCreator.Data;
using SmartCreator.Entities.Hotspot;
using SmartCreator.Entities.UserManager;
using SmartCreator.Models;
using SmartCreator.Properties;
using SmartCreator.RJControls;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.TrackBar;
using Font = System.Drawing.Font;

namespace SmartCreator.Forms.UserManager
{
    public partial class FormAdd_Edit_Profile : RJForms.RJChildForm
    {
        public bool succes=false;  
        public bool add=true;

       private  UmProfile profile_eidt;

        public FormAdd_Edit_Profile()
        {
            InitializeComponent();
            Set_Font();
            btnSave.BackColor = RJColors.Confirm;

            lblTitle.Text = "Add new profile";
            this.Text = "Add new profile";
            btnSave.Text = "Add";
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "اضافة باقة جديد";
                this.Text = "اضافة باقة";
                btnSave.Text = "اضافة";
               
            }
            txt_Download.Text = "0";
            txt_SpeedUpload.Text = "0";
            txt_SpeedDown.Text = "0";
            txt_price.Text = "0";
            txt_price_display.Text = "0";
            txt_uptime_Hour.Text = "0";
            //txt_uptime_Minut.Text = "0";
            txt_Validity.Text = "0";
            txt_precent.Text = "0";
            CBox_SizeDownload.SelectedIndex = 0;
            comboBoxShardUser.SelectedIndex = 0;
            CBox_speedDownlad.SelectedIndex = 0;
            CBox_speedUpload.SelectedIndex = 0;

            //CBox_profile_hotspot.Text = "default";
            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";
 
                CBox_profile_hotspot.SelectedIndex = 0;

            }
            catch { }

            txt_from.Text = "00:00:00";
            txt_to.Text = "23:59:59";

            CBOX_precent_type.SelectedIndex = 0;
            


            timer1.Start();
        }
        public FormAdd_Edit_Profile(UmProfile profile)
        {
            InitializeComponent();
            Set_Font();

            btnSave.BackColor = UIAppearance.StyleColor;
            btnAddLimit.Visible = true;
            txt_from.Text = "00:00:00";
            txt_to.Text = "23:59:59";

            profile_eidt = profile;
            lblTitle.Text = "Edit profile";
            this.Text = "Edit profile";
            btnSave.Text = "edit";

            CBOX_precent_type.SelectedIndex = 0;
            btnSave.BackColor = RJColors.Confirm;
            if (UIAppearance.Language_ar)
            {
                lblTitle.Text = "تعديل الباقة ";
                this.Text = "تعديل باقة";
                btnSave.Text = "تعديل";
                 
            }
            txt_profileName.Text = profile.Name;
            txt_profileName.Enabled = false;
            txt_profileName.BackColor = Color.FromArgb(240, 245, 249);

            //===========================
            string transfer = utils.ConvertSize_Get_En(profile.TransferLimit.ToString());
            //if (transfer.Contains("GB"))
            //    CBox_SizeDownload.SelectedIndex = 1;
            //else
            if (transfer.Contains("."))
            {
                CBox_SizeDownload.SelectedIndex = 0;
                txt_Download.Text = utils.ConvertSize_Get_InMB_without_Uint(profile.TransferLimit.ToString());
            }
            else
            {
                if (transfer.Contains("GB"))
                    CBox_SizeDownload.SelectedIndex = 1;
                else
                    CBox_SizeDownload.SelectedIndex = 0;

                txt_Download.Text = utils.ConvertSize_Get_Without_Uint(profile.TransferLimit.ToString());

            }
            //========================================
            string upload_rx = utils.ConvertSize_Get_En(profile.Upload_rx.ToString());
            if (upload_rx.Contains("MB"))
                CBox_speedUpload.SelectedIndex = 1;
            else
                CBox_speedUpload.SelectedIndex = 0;
            txt_SpeedUpload.Text = utils.ConvertSize_Get_Without_Uint(profile.Upload_rx.ToString());
            //===================================================
            string download_tx = utils.ConvertSize_Get_En(profile.Download_tx.ToString());
            if (download_tx.Contains("MB"))
                CBox_speedDownlad.SelectedIndex = 1;
            else
                CBox_speedDownlad.SelectedIndex = 0;
            txt_SpeedDown.Text = utils.ConvertSize_Get_Without_Uint(profile.Download_tx.ToString());

            if (!string.IsNullOrEmpty(txt_SpeedDown.Text) && txt_SpeedDown.Text != "0")
                rjCheckBox_Speed.Checked = true;
            if (!string.IsNullOrEmpty(txt_SpeedUpload.Text) && txt_SpeedUpload.Text != "0")
                rjCheckBox_Speed.Checked = true;
            //===================================================

            int hour_uptime = ((Int32)((Convert.ToInt32(profile.UptimeLimit)) / 3600));
            txt_uptime_Hour.Text = hour_uptime.ToString();

            int minut_uptime = ((int)(Convert.ToInt32(profile.UptimeLimit) / 60 % 60));
            if (minut_uptime > 0)
                txt_uptime_Hour.Text = (hour_uptime + "." + minut_uptime).ToString();

            //txt_uptime_Hour.Text = ((Int32)((Convert.ToInt32(profile.uptimeLimit)) / 3600)).ToString();
            //txt_uptime_Minut.Text = ((int)(Convert.ToInt32(profile.uptimeLimit) / 60 % 60)).ToString();

            txt_Validity.Text = profile.Validity.ToString();

            comboBoxShardUser.Text = profile.SharedUsers;
            txt_price.Text = profile.Price.ToString();
            txt_price_display.Text = profile.Price_Disply;
            try
            {
                CBox_profile_hotspot.DataSource = Global_Variable.Source_HS_Profile;
                CBox_profile_hotspot.DisplayMember = "Name";
                CBox_profile_hotspot.ValueMember = "ID";
            }
            catch { }

            CBox_profile_hotspot.Text = profile.GroupName.ToString();

            if (profile.GroupName.ToString() != "")
                rjCheckBox_group.Checked = true;

            txt_precent.Text = "0";

            if (profile.Weekdays.Contains(","))
            {
                string[] split = profile.Weekdays.Split(new string[] { "," }, StringSplitOptions.None);
                if (split.Length > 0)
                {
                    //"sunday,monday,tuesday,wednesday,thursday,friday,saturday"
                    for (int i = 0; i < split.Length; i++)
                    {
                        if (split[i].ToString() == "saturday")
                            check_saturday.Checked = true;
                        else if (split[i].ToString() == "sunday")
                            check_sunday.Checked = true;
                        else if (split[i].ToString() == "monday")
                            check_monday.Checked = true;
                        else if (split[i].ToString() == "tuesday")
                            check_tuesday.Checked = true;
                        else if (split[i].ToString() == "wednesday")
                            check_wednesday.Checked = true;
                        else if (split[i].ToString() == "thursday")
                            check_thursday.Checked = true;
                        else if (split[i].ToString() == "friday")
                            check_friday.Checked = true;
                    }
                }

                if (profile.Weekdays != "sunday,monday,tuesday,wednesday,thursday,friday,saturday")
                    check_weekdays.Check = true;
            }
            txt_from.Text = utils.GetTimeCard_By_clock_Mode( profile.From_time.ToString());
            txt_to.Text = utils.GetTimeCard_By_clock_Mode(profile.Till_time.ToString());


            CBOX_precent_type.SelectedIndex = profile.PercentageType;
            txt_precent.Text=profile.Percentage.ToString();
            rjCheckBox_precent.Check = Convert.ToBoolean(profile.Is_percentage);

        }

        private void Set_Font()
        {
            Font fnt = Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);
            foreach (var contrl in pnlClientArea.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font =fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in groupBox1.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
            foreach (var contrl in groupBox2.Controls)
            {
                try
                {
                    if (contrl.GetType() == typeof(RJControls.RJLabel))
                    {
                        RJLabel lbl = (RJLabel)contrl;
                        lbl.Font = fnt;
                    }
                }
                catch { }
            }
            rjCheckBox_precent.Font = fnt;
            rjCheckBox_Speed.Font = fnt;
            rjCheckBox_group.Font = fnt;
            check_weekdays.Font = fnt;
            CBOX_precent_type.Font=fnt;
            CBox_speedUpload.Font=fnt;
            CBox_speedDownlad.Font=fnt;
            CBox_SizeDownload.Font=fnt;
            lblTitle.Font  = Program.GetCustomFont(Resources.DroidKufi_Bold, 12, FontStyle.Bold);
            System.Drawing.Font title_font = Program.GetCustomFont(Resources.DroidKufi_Bold, 10, FontStyle.Bold);
            //lblTitle.Font = Program.GetCustomFont(Resources.Cairo_ExtraBold, 15, FontStyle.Bold);
            btnSave.Font = title_font;
            btnAddLimit.Font = title_font;

            utils.Control_textSize(pnlClientArea);
            return;
            Control_Loop(pnlClientArea);
        }

        private void Control_Loop(Control ctl)
        {
            try
            {
                foreach (Control C in ctl.Controls)
                {
                    try
                    {
                        if (C.GetType() != typeof(RJPanel) || C.GetType() != typeof(Panel))
                            C.Font = new Font(C.Font.FontFamily, C.Font.Size    , C.Font.Style);

                        if (C.Controls.Count > 0)
                            Control_Loop(C);

                        Application.DoEvents();
                    }
                    catch
                    {
                    }
                }
            }
            catch
            {
            }
        }


        private void CBox_SizeDownload_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            //CBox_profile_hotspot
        }

        private void FormAdd_Edit_Profile_Load(object sender, EventArgs e)
        {

            txt_profileName.BackColor = Color.FromArgb(240, 245, 249);

            if(Global_Variable.Mk_resources.version>=7)
            {
                //lbl_group.Visible = false;
                lbl_customer.Visible = false;
                rjCheckBox_group.Visible = false;
                CBox_profile_hotspot.Visible = false;
                CBox_Customer.Visible = false;
            }
            else
            Get_UMCustomer();

        }
        private void Get_UMCustomer()
        {
            if (Global_Variable.Mk_resources.version >= 7)
                return;
            try
            {
                if (Global_Variable.UM_Customer == null)
                    return;
                List<UserManager_Customer> sp = Global_Variable.UM_Customer;
                Dictionary<string, string> comboSource = new Dictionary<string, string>();
                //comboSource.Add("0", "");
                foreach (UserManager_Customer s in sp)
                    comboSource.Add(s.Name, s.Name);

                CBox_Customer.DataSource = new BindingSource(comboSource, null);
                CBox_Customer.DisplayMember = "Value";
                CBox_Customer.ValueMember = "Key";
                CBox_Customer.SelectedIndex = 0;
                //CBox_Customer.Text = "";
            }
            catch { }
        }
        void get_Profile_Hotspot()
        {
            //    CBox_profile_hotspot.Items.Clear();
            //    //DataAccess.CLS_DataAccess DA = new DataAccess.CLS_DataAccess();
            //    DataAccess.DataAcess_V2 DA = new DataAccess.DataAcess_V2();
            //    string[] Hotspot_Source_Profile = DA.GetProfileHotspot();
            //    CBox_profile_hotspot.Items.Add("");
            //    for (int i = 0; i < Hotspot_Source_Profile.Length; i++)
            //    {
            //        CBox_profile_hotspot.Items.Add(Hotspot_Source_Profile[i]);
            //    }
            //    CBox_profile_hotspot.SelectedIndex = 0;


        }

        private void timer1_Tick(object sender, EventArgs e)
        {
            timer1.Stop();
            try
            {
                CBox_profile_hotspot.SelectedIndex = 1;
            }
            catch { }
            try
            {
                CBox_profile_hotspot.SelectedIndex = 0;
            }
            catch { }
        }

        [Obsolete]
        private void btnSave_Click(object sender, EventArgs e)
        {
            if (check() == false)
                return;
            succes =true;
            this.Close();
       
           
        }

        [Obsolete]
        bool check()
        {
            try
            {
                if (txt_profileName.Text == "")
                {
                    RJMessageBox.Show("اكتب اسم البروفايل");
                    return false;
                }
                var item = Global_Variable.UM_Profile.SingleOrDefault(x => x.Name == txt_profileName.Text);

                if (item != null && add)
                {
                    RJMessageBox.Show("اسم البروفايل مكرر");
                    return false;
                }

                int number2;
                if (!(int.TryParse(txt_Validity.Text, out number2)))
                {
                    RJMessageBox.Show("الصلاحية يجب ان تكون رقم");
                    return false;
                }
                if (!(float.TryParse(txt_uptime_Hour.Text, out float Hnumber)))
                {
                    RJMessageBox.Show(" الوقت ساعات يجب ان تكون رقم صحيح او كسر عشري");
                    return false;
                }
                if (!(float.TryParse(txt_Download.Text, out float downl)))
                {
                    RJMessageBox.Show("كمية التحميل  يجب ان تكون رقم صحيح او كسر عشري");
                    return false;
                }
                if (!(int.TryParse(txt_price.Text, out number2)))
                {
                    RJMessageBox.Show("السعر يجب ان تكون رقم");
                    return false;
                }
                if (!(int.TryParse(txt_SpeedUpload.Text, out number2)))
                {
                    RJMessageBox.Show("سرعة الرفع يجب ان تكون رقم");
                    return false;
                }
                if (!(int.TryParse(txt_SpeedDown.Text, out number2)))
                {
                    RJMessageBox.Show("سرعة التحميل يجب ان تكون رقم");
                    return false;
                }
                if (!(int.TryParse(comboBoxShardUser.Text, out number2)))
                {
                    if (comboBoxShardUser.Text != "off")
                    {
                        RJMessageBox.Show("عدد المستخدمين غير صحيح");
                        return false;
                    }

                }
                if (CBox_Customer.Text == "" || CBox_Customer.SelectedIndex == -1)
                {
                    if (Global_Variable.Mk_resources.version <= 6)
                    {
                        RJMessageBox.Show("اختر مستخدم اليوزمنجر");
                        return false;
                    }
                }

                double downloadSize = 0;
                double downloadSpeed = 0;
                double UploadSpeed = 0;

                //=====================================
                if (CBox_SizeDownload.SelectedIndex == 0)
                    downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024);
                if (CBox_SizeDownload.SelectedIndex == 1)
                    downloadSize = (Convert.ToDouble(txt_Download.Text) * 1024 * 1024 * 1024);
                if (CBox_SizeDownload.SelectedIndex == -1 && txt_Download.Text != "0")
                {
                    RJMessageBox.Show("اختر وحدة الحجم");
                    return false;
                }
                //=====================================
                if (rjCheckBox_Speed.Check)
                {
                    if (CBox_speedDownlad.SelectedIndex == 0)
                        downloadSpeed = (Convert.ToDouble(txt_SpeedDown.Text) * 1024);
                    if (CBox_speedDownlad.SelectedIndex == 1)
                        downloadSpeed = (Convert.ToDouble(txt_SpeedDown.Text) * 1024 * 1024);
                    if (CBox_speedDownlad.SelectedIndex == -1 && txt_SpeedDown.Text != "0")
                    {
                        RJMessageBox.Show("اختر وحدة لسرعة الباقة");
                        return false;
                    }
                    //==============================
                    if (CBox_speedUpload.SelectedIndex == 0)
                        UploadSpeed = (Convert.ToDouble(txt_SpeedUpload.Text) * 1024);
                    if (CBox_speedUpload.SelectedIndex == 1)
                        UploadSpeed = (Convert.ToDouble(txt_SpeedUpload.Text) * 1024 * 1024);
                    if (CBox_speedUpload.SelectedIndex == -1 && txt_SpeedUpload.Text != "0")
                    {
                        RJMessageBox.Show("اختر وحدة لسرعة الباقة");
                        return false;
                    }
                }
                //======================================

                //=========================================
                UmProfile prf = new UmProfile();
                prf.Name = txt_profileName.Text.Trim();
                prf.Name_limt = txt_profileName.Text.Trim();

                prf.Price = Convert.ToInt32(txt_price.Text);
                prf.Price_Disply = (txt_price_display.Text);
                prf.Str_Validity = txt_Validity.Text + "d";
                prf.Validity = Convert.ToInt32(txt_Validity.Text);
                if (comboBoxShardUser.SelectedIndex != 0)
                    prf.SharedUsers = comboBoxShardUser.Text;
                prf.Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check);
                prf.Percentage = (float)Convert.ToDouble(txt_precent.Text);
                prf.PercentageType = CBOX_precent_type.SelectedIndex;
                prf.Owner = CBox_Customer.Text;
                //============================
                prf.TransferLimit = downloadSize;
                //prf.uptimeLimit_str = (txt_uptime_Hour.Text + "h" + txt_uptime_Minut.Text + "m");
                if (txt_uptime_Hour.Text.Contains("."))
                {
                    string[] split = txt_uptime_Hour.Text.Split(new string[] { "." }, StringSplitOptions.None);
                    if (split.Length > 0)
                    {
                        prf.UptimeLimit = (Convert.ToDouble(split[0]) * 60 * 60) + (Convert.ToInt32(split[1]) * 60);
                    }
                }
                else
                    prf.UptimeLimit = (Convert.ToDouble(txt_uptime_Hour.Text) * 60 * 60);

                prf.Str_uptimeLimit = prf.UptimeLimit.ToString();
                prf.Upload_rx = UploadSpeed.ToString();
                prf.Download_tx = downloadSpeed.ToString();

                prf.Owner = prf.Owner;
                if (rjCheckBox_group.Checked)
                    prf.GroupName = CBox_profile_hotspot.Text;
                string weekDays = "sunday,monday,tuesday,wednesday,thursday,friday,saturday";
                string fromTime = "00h00m00s";
                string toTime = "23h59m59s";
                if (check_weekdays.Checked)
                {
                    string[] Spilt_fromTime = txt_from.Text.Split(new string[] { ":" }, StringSplitOptions.None);
                    fromTime = Spilt_fromTime[0] + "h" + Spilt_fromTime[1] + "m" + Spilt_fromTime[2] + "s";
                    string[] Spilt_ToTime = txt_to.Text.Split(new string[] { ":" }, StringSplitOptions.None);
                    toTime = Spilt_ToTime[0] + "h" + Spilt_ToTime[1] + "m" + Spilt_ToTime[2] + "s";
                    weekDays = "";
                    if (check_saturday.Check)
                        weekDays += ",saturday";
                    if (check_sunday.Check)
                        weekDays += ",sunday";
                    if (check_monday.Check)
                        weekDays += ",monday";
                    if (check_tuesday.Check)
                        weekDays += ",tuesday";
                    if (check_wednesday.Check)
                        weekDays += ",wednesday";
                    if (check_thursday.Check)
                        weekDays += ",thursday";
                    if (check_friday.Check)
                        weekDays += ",friday";
                }
                prf.Weekdays = weekDays.TrimStart(',');
                prf.From_time = fromTime;
                prf.Till_time = toTime;

                //UmLimitation umLimitation = new UmLimitation {
                //    Name=prf.Name,
                //    DeleteFromServer=0,
                //    Download_tx=prf.Download_tx,
                //    Upload_rx=prf.Upload_rx,
                //    GroupName=prf.GroupName,
                //    TransferLimit=prf.TransferLimit,
                //    UptimeLimit=prf.UptimeLimit
                //};
                Mk_DataAccess dataAccess = new Mk_DataAccess();
                if (add)
                {
                    prf.Name_limt = prf.Name;
                    string id_profile = dataAccess.Add_UserManager_Profile(prf, true);
                    if (!string.IsNullOrEmpty(id_profile))
                    {
                        Smart_DataAccess sql_DataAccess = new Smart_DataAccess();
                        lock (Smart_DataAccess.Lock_object)
                        {
                            prf.IdHX = id_profile;
                            prf.Sn = Int32.Parse(id_profile.TrimStart(new char[] { '*' }), NumberStyles.HexNumber);
                            prf.Sn_Name = prf.Sn + "-" + prf.Name;
                            //prf.Rb = Global_Variable.Mk_resources.RB_code;
                            prf.Rb = Global_Variable.Mk_resources.RB_SN;

                            List<string> Fields = new List<string>();
                            string[] aFields = { "IdHX", "Sn", "Sn_Name", "Rb",
                            "Price", "Price_Disply",
                            "Is_percentage", "Percentage", "PercentageType",


                        };
                            Fields.AddRange(aFields);

                            int count = sql_DataAccess.InsertTable<UmProfile>(Fields, prf, "UmProfile");
                            succes = true;

                        }
                        RJMessageBox.Show("تمت العملية بنجاح");

                    }
                }
                else
                {
                    prf.IdHX = profile_eidt.IdHX;
                    prf.Sn_Name = profile_eidt.Sn_Name;
                    prf.IdHX_limt = profile_eidt.IdHX_limt;
                    prf.IdHX_prfileLimt = profile_eidt.IdHX_prfileLimt;
                    prf.Name_limt = profile_eidt.Name_limt;
                    //prf.Sn_Name = prf.Sn + "-" + prf.Name;
                    //prf.Rb = Global_Variable.Mk_resources.RB_code;

                    if (dataAccess.Edit_UserManager_Profile(prf))
                    {
                        Smart_DataAccess sql_DataAccess = new Smart_DataAccess();

                        List<string> Fields = new List<string>();
                        string[] aFields = { "Is_percentage", "Price_Disply", "Percentage", "PercentageType" };
                        Fields.AddRange(aFields);

                        //var prof = new UmProfile
                        //{
                        //    Is_percentage = Convert.ToInt32(rjCheckBox_precent.Check),
                        //    Price_Disply = txt_price_display.Text,
                        //    Percentage = (float)Convert.ToDouble(txt_precent.Text),
                        //    PercentageType = CBOX_precent_type.SelectedIndex,
                        //    Rb=Global_Variable.Mk_resources.RB_code,
                        //    Sn_Name=prf.Sn_Name


                        //};
                        lock (Smart_DataAccess.Lock_object)
                        {
                            string sqlquery = UtilsSql.GetUpdateSql<UmProfile>("UmProfile", Fields, $" where Sn_Name=@Sn_Name and ( Rb='{Global_Variable.Mk_resources.RB_code}' or Rb='{Global_Variable.Mk_resources.RB_SN}'  ) and DeleteFromServer=0");
                            int r = sql_DataAccess.UpateTable(prf, sqlquery);
                        }
                        RJMessageBox.Show("تمت العملية بنجاح");
                    }
                }
                //======================================================================================
                succes = true;
            }
            catch(Exception ex) { MessageBox.Show(ex.Message); }
            return true;
        }

        private void txt_price_onTextChanged(object sender, EventArgs e)
        {
            txt_price_display.Text = txt_price.Text;
        }

        private void btnAddLimit_Click(object sender, EventArgs e)
        {
            string id = profile_eidt.IdHX;
            var frm = new Form_Edit_Limit_Profile(profile_eidt);
            frm.add = true;
            frm.ShowDialog();
            if (frm.succes)
            {
                //refersh_From_MK();
                //loadData();
                succes = true; 
                this.Close();
            }
        }
    }
}
