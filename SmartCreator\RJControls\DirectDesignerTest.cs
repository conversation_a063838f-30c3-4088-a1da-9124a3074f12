using System;
using System.Drawing;
using System.Windows.Forms;
using FontAwesome.Sharp;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// اختبار مباشر لـ RJTabControl في Designer - بدون RJChildForm
    /// </summary>
    public partial class DirectDesignerTest : Form
    {
        private RJTabControl directTabControl;

        public DirectDesignerTest()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // 
            // directTabControl
            // 
            this.directTabControl = new RJTabControl();
            this.directTabControl.Dock = DockStyle.Fill;
            this.directTabControl.TabHeight = 45;
            this.directTabControl.TabSpacing = 3;
            this.directTabControl.TabPadding = 20;

            // 
            // DirectDesignerTest
            // 
            this.AutoScaleDimensions = new SizeF(8F, 16F);
            this.AutoScaleMode = AutoScaleMode.Font;
            this.ClientSize = new Size(1000, 700);
            this.Controls.Add(this.directTabControl);
            this.Name = "DirectDesignerTest";
            this.Text = "🧪 اختبار مباشر - RJTabControl في Designer";
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(45, 45, 48);

            this.ResumeLayout(false);

            // إضافة محتوى بعد التهيئة
            this.Load += DirectDesignerTest_Load;
        }

        private void DirectDesignerTest_Load(object sender, EventArgs e)
        {
            try
            {
                // إضافة تابات تجريبية
                AddDirectTestTabs();

                // عرض معلومات النجاح
                this.Text += " - ✅ يعمل بمثالية!";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحميل RJTabControl:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Text += " - ❌ فشل!";
            }
        }

        private void AddDirectTestTabs()
        {
            // تاب النجاح
            var successTab = this.directTabControl.AddTab("🎉 نجح!", IconChar.CheckCircle);
            successTab.BackColor = Color.FromArgb(76, 175, 80);
            successTab.ForeColor = Color.White;

            var successLabel = new Label
            {
                Text = "🎉 RJTabControl يعمل في Designer!\n\n" +
                       "✅ تم إنشاؤه بدون أخطاء\n" +
                       "✅ SimpleRJTabPageCollection يعمل\n" +
                       "✅ لا توجد مراجع دائرية\n" +
                       "✅ معالجة أخطاء شاملة\n\n" +
                       "🎯 الآن جرب:\n" +
                       "1. اسحب RJTabControl من Toolbox\n" +
                       "2. افتح Properties Panel\n" +
                       "3. ابحث عن خاصية Tabs\n" +
                       "4. انقر على [...] لفتح Collection Editor\n\n" +
                       "🚀 RJTabControl جاهز للاستخدام!",
                Dock = DockStyle.Fill,
                TextAlign = ContentAlignment.MiddleCenter,
                Font = new Font("Segoe UI", 12, FontStyle.Bold),
                ForeColor = Color.White,
                Padding = new Padding(20)
            };
            successTab.AddControl(successLabel);

            // تاب التعليمات
            var instructionsTab = this.directTabControl.AddTab("التعليمات", IconChar.Info);
            instructionsTab.BackColor = Color.FromArgb(0, 122, 204);
            instructionsTab.ForeColor = Color.White;

            var instructionsTextBox = new RJTextBox
            {
                Dock = DockStyle.Fill,
                MultiLine = true,
                ReadOnly = true,
                Text = "📋 تعليمات استخدام RJTabControl:\n\n" +
                       "🎯 في Visual Studio Designer:\n" +
                       "1. تأكد من بناء المشروع (Build → Rebuild Solution)\n" +
                       "2. اسحب RJTabControl من Toolbox\n" +
                       "3. افتح Properties Panel\n" +
                       "4. ابحث عن خاصية Tabs\n" +
                       "5. انقر على [...] لفتح Collection Editor\n" +
                       "6. أضف/عدل/احذف التابات\n\n" +
                       "💻 في الكود:\n" +
                       "// إضافة تاب بسيط\n" +
                       "var tab = tabControl.AddTab(\"تاب جديد\");\n\n" +
                       "// إضافة تاب مع أيقونة\n" +
                       "var tab = tabControl.AddTab(\"تاب\", IconChar.Home);\n\n" +
                       "// إضافة عبر Collection\n" +
                       "tabControl.Tabs.Add(new RJTabPage(\"تاب\"));\n\n" +
                       "🔧 الخصائص المتاحة:\n" +
                       "• TabHeight - ارتفاع التابات\n" +
                       "• TabSpacing - المسافة بين التابات\n" +
                       "• TabPadding - الحشو الداخلي\n" +
                       "• ShowCloseButtons - أزرار الإغلاق\n" +
                       "• ContentBorderSize - حجم حدود المحتوى\n" +
                       "• ContentBorderColor - لون الحدود\n" +
                       "• ContentBorderRadius - انحناء الحدود\n\n" +
                       "⚠️ إذا ظهرت مشكلة RJChildForm:\n" +
                       "1. ابني المشروع (Build → Rebuild Solution)\n" +
                       "2. أغلق Visual Studio وافتحه مرة أخرى\n" +
                       "3. احذف مجلدات bin و obj\n" +
                       "4. ابني المشروع مرة أخرى\n\n" +
                       "🚀 RJTabControl الآن جاهز للاستخدام!",
                Style = TextBoxStyle.MatteBorder,
                BorderSize = 2,
                BorderColor = Color.FromArgb(0, 122, 204),
                BorderRadius = 8,
                Font = new Font("Segoe UI", 10),
                TextAlign = HorizontalAlignment.Left
            };
            instructionsTab.AddControl(instructionsTextBox);

            // تاب الاختبار
            var testTab = this.directTabControl.AddTab("اختبار", IconChar.Play);
            testTab.BackColor = Color.FromArgb(156, 39, 176);
            testTab.ForeColor = Color.White;

            var testPanel = new Panel { Dock = DockStyle.Fill, Padding = new Padding(20) };

            var testButton = new RJButton
            {
                Text = "اختبار إضافة تاب",
                IconChar = IconChar.Plus,
                Location = new Point(20, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(76, 175, 80),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            testButton.Click += (s, e) => {
                try
                {
                    var newTab = this.directTabControl.AddTab($"تاب {this.directTabControl.TabCount + 1}", IconChar.Star);
                    newTab.BackColor = Color.FromArgb(233, 30, 99);
                    newTab.ForeColor = Color.White;

                    var label = new Label
                    {
                        Text = $"🌟 تاب جديد!\n\nتم إنشاؤه في: {DateTime.Now:HH:mm:ss}",
                        Dock = DockStyle.Fill,
                        TextAlign = ContentAlignment.MiddleCenter,
                        Font = new Font("Segoe UI", 12, FontStyle.Bold),
                        ForeColor = Color.White
                    };
                    newTab.AddControl(label);

                    this.directTabControl.SelectedTab = newTab;
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            var collectionTestButton = new RJButton
            {
                Text = "اختبار Collection",
                IconChar = IconChar.List,
                Location = new Point(240, 20),
                Size = new Size(200, 50),
                BackColor = Color.FromArgb(255, 152, 0),
                ForeColor = Color.White,
                BorderRadius = 10,
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            collectionTestButton.Click += (s, e) => {
                try
                {
                    var collection = this.directTabControl.Tabs;
                    var results = $"🧪 نتائج اختبار Collection:\n\n" +
                                 $"Count: {collection.Count}\n" +
                                 $"First Tab: {collection[0]?.Text}\n" +
                                 $"Last Tab: {collection[collection.Count - 1]?.Text}\n\n" +
                                 "✅ Collection يعمل بمثالية!";

                    MessageBox.Show(results, "نتائج الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            };

            testPanel.Controls.Add(testButton);
            testPanel.Controls.Add(collectionTestButton);
            testTab.AddControl(testPanel);

            // تفعيل التاب الأول
            this.directTabControl.SelectedIndex = 0;
        }

        /// <summary>
        /// تشغيل الاختبار المباشر
        /// </summary>
        public static void RunDirectTest()
        {
            try
            {
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);
                
                var form = new DirectDesignerTest();
                form.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل الاختبار المباشر:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// اختبار سريع لإنشاء RJTabControl
        /// </summary>
        public static void QuickTest()
        {
            try
            {
                var tabControl = new RJTabControl();
                var tab = tabControl.AddTab("اختبار سريع");
                
                MessageBox.Show($"✅ اختبار سريع نجح!\n\n" +
                               $"TabCount: {tabControl.TabCount}\n" +
                               $"SelectedIndex: {tabControl.SelectedIndex}\n" +
                               $"Tab Text: {tab.Text}\n\n" +
                               "🎯 الآن جرب في Designer!",
                               "نجح الاختبار", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
