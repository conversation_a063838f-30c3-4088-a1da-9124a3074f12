﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SmartCreator.Entities.EnumType
{
    public class EnumType
    {
    }
    public enum RDBMSProvider { MSSQLServer, MySQL, PostgreSQL, Oracle, Firebird, SQLite }
    public enum ServerType
    {
        UserManager,
        Hotspot,
        BroadBand,
    }
    public enum TypePercentage
    {
        ByValue,
        ByPercentage
    }
    //public enum PartnerType
    //{
    //    ByValue,
    //    ByPercentage
    //}

    /// <summary>
    /// أنواع الحسابات في شجرة الحسابات
    /// </summary>
    public enum AccountType
    {
        /// <summary>
        /// أصول
        /// </summary>
        Assets = 1,

        /// <summary>
        /// خصوم
        /// </summary>
        Liabilities = 2,

        /// <summary>
        /// حقوق الملكية
        /// </summary>
        Equity = 3,

        /// <summary>
        /// إيرادات
        /// </summary>
        Revenue = 4,

        /// <summary>
        /// مصروفات
        /// </summary>
        Expenses = 5,

        /// <summary>
        /// أصول ثابتة
        /// </summary>
        FixedAssets = 6,

        /// <summary>
        /// أصول متداولة
        /// </summary>
        CurrentAssets = 7,

        /// <summary>
        /// خصوم متداولة
        /// </summary>
        CurrentLiabilities = 8,

        /// <summary>
        /// خصوم طويلة الأجل
        /// </summary>
        LongTermLiabilities = 9
    }

    /// <summary>
    /// أنواع الفواتير
    /// </summary>
    public enum InvoiceType
    {
        /// <summary>
        /// فاتورة مبيعات
        /// </summary>
        Sales = 1,

        /// <summary>
        /// فاتورة مشتريات
        /// </summary>
        Purchase = 2,

        /// <summary>
        /// مردود مبيعات
        /// </summary>
        SalesReturn = 3,

        /// <summary>
        /// مردود مشتريات
        /// </summary>
        PurchaseReturn = 4
    }

    /// <summary>
    /// أدوار المستخدمين في النظام
    /// </summary>
    public enum UserRole
    {
        /// <summary>
        /// مدير النظام - صلاحيات كاملة
        /// </summary>
        Administrator = 1,

        /// <summary>
        /// مدير مالي - صلاحيات إدارية مالية
        /// </summary>
        FinancialManager = 2,

        /// <summary>
        /// محاسب - صلاحيات محاسبية
        /// </summary>
        Accountant = 3,

        /// <summary>
        /// موظف مبيعات - صلاحيات المبيعات والعملاء
        /// </summary>
        SalesEmployee = 4,

        /// <summary>
        /// موظف مشتريات - صلاحيات المشتريات والموردين
        /// </summary>
        PurchaseEmployee = 5,

        /// <summary>
        /// أمين مخزن - صلاحيات المخزون
        /// </summary>
        WarehouseKeeper = 6,

        /// <summary>
        /// مستخدم عادي - صلاحيات محدودة
        /// </summary>
        User = 7
    }

    public enum EntryStatus
    {
        Draft=0,
        Confirm = 1,
        Posted = 2,
        Cancel = 3,
    }

}
