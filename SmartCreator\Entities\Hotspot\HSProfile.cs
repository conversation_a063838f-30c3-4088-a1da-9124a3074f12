﻿using Dapper;
//using ServiceStack.DataAnnotations;
using SmartCreator.Data;
using SmartCreator.Models;
using SmartCreator.Models.hotspot;
using SmartCreator.Settings;
using SmartCreator.Utils;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SQLite;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using System.ComponentModel.DataAnnotations.Schema;

namespace SmartCreator.Entities.Hotspot
{
    [UniqueConstraint("Name", "Rb")]
    public class HSLocalProfile 
    {

        private string validity_str;

        [Required, Unique, AutoIncrement, PrimaryKey]
        public int Id { get; set; }

        [DisplayName("الاسم")]
        public string Name { get; set; }

        [Default(0)]
        public double UptimeLimit { get; set; } = 0;//in second
        [DisplayName("الوقت"),Computed]

        public string UptimeLimit_str
        {
            get
            {
                return utils.Get_Seconds_By_clock_Mode(UptimeLimit);
            }

        }
     
        [DisplayName("الصلاحية"),Default(0)]
        public double Validity { get; set; } = 0; // in days

        [Default(0)]
        public double TransferLimit { get; set; } = 0; //in byte
        
        [DisplayName("التحميل"), Computed]
        public string TransferLimit_str
        {
            get
            {
                string d = "";
                if (UIAppearance.Language_ar)
                    d = utils.ConvertSize_Get_InArabic(TransferLimit.ToString());
                else
                    d = utils.ConvertSize_Get_En(TransferLimit.ToString());
                return d;
            }
            set { validity_str = value; }
        }
       
        [DisplayName("السعر"),Default(0)]
        public float Price { get; set; } = 0;
        
        [DisplayName("سعر العرض"), Default(0), Browsable(false)]
        public string Price_Display { get; set; } = "0";
        [Browsable(false)]
        public int Is_percentage { get; set; } = 0;

        [Default(0), DisplayName("النسبة"), Browsable(false)]
        public float Percentage { get; set; } = 0;

        [Default(0), DisplayName("طريقة حساب النسبة"), Browsable(false)]
        public int PercentageType { get; set; } = 0;
        
        [Default(0), Browsable(false)]
        public int Add_Smart_Scripts { get; set; } = 1;

        [Default(0), Browsable(false)]
        public int Save_time { get; set; } = 1;

        [Default(0), Browsable(false)]
        public int Save_download { get; set; } = 1;

        [Default(0), Browsable(false)]
        public int Save_session { get; set; } = 1;

        [Default(0), Browsable(false)]
        public int ByDayOrHour { get; set; } = 0;

        [DisplayName("عدد الكروت المرتبطه")]
        public double CountCards { get; set; } = 0;




        [DisplayName("نظام الصلاحية"), Computed]
        public bool Add_Smart_Scripts_str
        {
            get
            {
                return Convert.ToBoolean(Add_Smart_Scripts);
            }

        }
        
        [DisplayName("بروفايل الهوتسبوت"), Browsable(false)]
        public string Link_hotspot_profile { get; set; } = "";
        [Browsable(false)]
        public string Rb { get; set; }

        

        public HSLocalProfile Ge_Local_Hotspot(string name)
        {
            string rb_code = Global_Variable.Mk_resources.RB_code;
            string rb = Global_Variable.Mk_resources.RB_SN;
            //string query = $"select * from HSLocalProfile where  Name='" + name + "'  and Rb='" + Global_Variable.Mk_resources.RB_code + "'  ;";
            string query = $"select * from HSLocalProfile where  Name='{name}' and (Rb='{rb}' or rb='{rb_code}' ) ; ";
            try
            {
                using (var cnn = Smart_DataAccess.GetConnSmart())
                {
                    var output = cnn.QueryFirstOrDefault<HSLocalProfile>(query);
                    return output;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); return null; }
        }

        public List<HSLocalProfile> Ge_Local_Hotspot()
        {
            List<HSLocalProfile> ProfileProfileHotspot = new List<HSLocalProfile>();
            try
            {
                using (var cnn = Smart_DataAccess.GetConnSmart())
                {
                    string rb_code = Global_Variable.Mk_resources.RB_code;
                    string rb = Global_Variable.Mk_resources.RB_SN;
                    var output = cnn.Query<HSLocalProfile>($"select * from HSLocalProfile where   Rb='{rb}' or rb='{rb_code}' ; ", new DynamicParameters());
                    ProfileProfileHotspot = output.ToList();
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return ProfileProfileHotspot;
        }
        public HSLocalProfile Ge_Local_Hotspot_ByName(string name)
        {
            if (string.IsNullOrEmpty(name))
                return null;
            HSLocalProfile profile = null;
            string rb_code = Global_Variable.Mk_resources.RB_code;
            string rb = Global_Variable.Mk_resources.RB_SN;
            string query = $"select * from HSLocalProfile where  Name='{name}' and (Rb='{rb}' or rb='{rb_code}' ) ; ";

            try
            {
                using (var cnn = Smart_DataAccess.GetConnSmart())
                {
                   
                    var output = cnn.QueryFirstOrDefault<HSLocalProfile>(query, new DynamicParameters());
                    profile= output;
                }
            }
            catch (Exception ex) { RJMessageBox.Show(ex.Message); }
            return profile;
        }


    }

}
