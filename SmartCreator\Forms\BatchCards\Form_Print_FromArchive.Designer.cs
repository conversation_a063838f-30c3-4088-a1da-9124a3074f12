﻿namespace SmartCreator.Forms.BatchCards
{
    partial class Form_Print_FromArchive
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle5 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle6 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle4 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.dmAll_Cards = new SmartCreator.RJControls.RJDropdownMenu(this.components);
            this.Copy_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.Copy_AllRowToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.toolStripSeparator2 = new System.Windows.Forms.ToolStripSeparator();
            this.DeleteCards_ToolStripMenuItem = new System.Windows.Forms.ToolStripMenuItem();
            this.btnAdd = new SmartCreator.RJControls.RJButton();
            this.txt_Page_From = new SmartCreator.RJControls.RJTextBox();
            this.lbl_From_To = new SmartCreator.RJControls.RJLabel();
            this.txt_Sn_From = new SmartCreator.RJControls.RJTextBox();
            this.txt_Page_To = new SmartCreator.RJControls.RJTextBox();
            this.lbl_To_Page = new SmartCreator.RJControls.RJLabel();
            this.txt_Sn_To = new SmartCreator.RJControls.RJTextBox();
            this.pnl_attrbut = new SmartCreator.RJControls.RJPanel();
            this.CBox_Attribute = new SmartCreator.RJControls.RJComboBox();
            this.CBox_group = new SmartCreator.RJControls.RJComboBox();
            this.lbl_Attribute = new SmartCreator.RJControls.RJLabel();
            this.txt_attribute = new SmartCreator.RJControls.RJTextBox();
            this.lbl_group = new SmartCreator.RJControls.RJLabel();
            this.pnl_customer = new SmartCreator.RJControls.RJPanel();
            this.lbl_Customer = new SmartCreator.RJControls.RJLabel();
            this.CBox_CustomerUserMan = new SmartCreator.RJControls.RJComboBox();
            this.CBox_SellingPoint = new SmartCreator.RJControls.RJComboBox();
            this.checkBoxSaveTo_excel = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_SellingPoint = new SmartCreator.RJControls.RJLabel();
            this.lbl_excel = new SmartCreator.RJControls.RJLabel();
            this.txt_note = new SmartCreator.RJControls.RJTextBox();
            this.checkBoxSaveTo_text_File = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_text_File = new SmartCreator.RJControls.RJLabel();
            this.checkBoxFirstUse = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_FirstUse = new SmartCreator.RJControls.RJLabel();
            this.checkBoxSaveTo_PDF = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_Save_PDF = new SmartCreator.RJControls.RJLabel();
            this.txt_last_batchNumber = new SmartCreator.RJControls.RJTextBox();
            this.btn_OpenLastFile = new SmartCreator.RJControls.RJButton();
            this.Radio_SN = new SmartCreator.RJControls.RJRadioButton();
            this.Radio_Page = new SmartCreator.RJControls.RJRadioButton();
            this.checkBoxSaveTo_script_File = new SmartCreator.RJControls.RJCheckBox();
            this.lbl_script_File = new SmartCreator.RJControls.RJLabel();
            this.pnl_Right = new SmartCreator.RJControls.RJPanel();
            this.lbl_Batch = new SmartCreator.RJControls.RJLabel();
            this.CBox_Batch = new SmartCreator.RJControls.RJComboBox();
            this.checkBox_RegisterAs_LastBatch = new SmartCreator.RJControls.RJCheckBox();
            this.checkBox_RegisterAsBatch = new SmartCreator.RJControls.RJCheckBox();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.tableLayoutPanel1 = new System.Windows.Forms.TableLayoutPanel();
            this.checkBox_note = new SmartCreator.RJControls.RJCheckBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.checkBox_Remove_FromArchive = new SmartCreator.RJControls.RJCheckBox();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.btn_search = new SmartCreator.RJControls.RJButton();
            this.txt_search = new SmartCreator.RJControls.RJTextBox();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.lbl_Count = new SmartCreator.RJControls.RJLabel();
            this.btnRefresh = new SmartCreator.RJControls.RJButton();
            this.dgv_freq = new SmartCreator.RJControls.RJDataGridView();
            this.pnlClientArea.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.dmAll_Cards.SuspendLayout();
            this.pnl_attrbut.SuspendLayout();
            this.pnl_customer.SuspendLayout();
            this.pnl_Right.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.tableLayoutPanel1.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_freq)).BeginInit();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.pnl_Right);
            this.pnlClientArea.Controls.Add(this.btn_OpenLastFile);
            this.pnlClientArea.Controls.Add(this.checkBoxSaveTo_PDF);
            this.pnlClientArea.Controls.Add(this.lbl_Save_PDF);
            this.pnlClientArea.Controls.Add(this.dgv_freq);
            this.pnlClientArea.Controls.Add(this.btnRefresh);
            this.pnlClientArea.Controls.Add(this.btn_search);
            this.pnlClientArea.Controls.Add(this.txt_search);
            this.pnlClientArea.Controls.Add(this.btnAdd);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.lbl_Count);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(946, 568);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(152, 22);
            this.lblCaption.Text = "استيراد الكروت من الارشيف";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToOrderColumns = true;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle5.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle5.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle5.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle5;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.ContextMenuStrip = this.dmAll_Cards;
            this.dgv.Customizable = false;
            dataGridViewCellStyle6.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle6.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle6.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle6.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.DefaultCellStyle = dataGridViewCellStyle6;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv.Location = new System.Drawing.Point(9, 279);
            this.dgv.Name = "dgv";
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle7.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 30;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 30;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(788, 273);
            this.dgv.TabIndex = 81;
            this.dgv.RowsAdded += new System.Windows.Forms.DataGridViewRowsAddedEventHandler(this.dgv_RowsAdded);
            this.dgv.SelectionChanged += new System.EventHandler(this.dgv_SelectionChanged);
            this.dgv.MouseDown += new System.Windows.Forms.MouseEventHandler(this.dgv_MouseDown);
            // 
            // dmAll_Cards
            // 
            this.dmAll_Cards.ActiveMenuItem = false;
            this.dmAll_Cards.Font = new System.Drawing.Font("Segoe UI", 9F);
            this.dmAll_Cards.ImageScalingSize = new System.Drawing.Size(19, 19);
            this.dmAll_Cards.Items.AddRange(new System.Windows.Forms.ToolStripItem[] {
            this.Copy_ToolStripMenuItem,
            this.Copy_AllRowToolStripMenuItem,
            this.toolStripSeparator2,
            this.DeleteCards_ToolStripMenuItem});
            this.dmAll_Cards.Name = "dmExample";
            this.dmAll_Cards.OwnerIsMenuButton = false;
            this.dmAll_Cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dmAll_Cards.Size = new System.Drawing.Size(179, 76);
            // 
            // Copy_ToolStripMenuItem
            // 
            this.Copy_ToolStripMenuItem.Name = "Copy_ToolStripMenuItem";
            this.Copy_ToolStripMenuItem.Size = new System.Drawing.Size(178, 22);
            this.Copy_ToolStripMenuItem.Text = "نسخ                 ctrl+c";
            this.Copy_ToolStripMenuItem.Click += new System.EventHandler(this.Copy_ToolStripMenuItem_Click);
            // 
            // Copy_AllRowToolStripMenuItem
            // 
            this.Copy_AllRowToolStripMenuItem.Name = "Copy_AllRowToolStripMenuItem";
            this.Copy_AllRowToolStripMenuItem.Size = new System.Drawing.Size(178, 22);
            this.Copy_AllRowToolStripMenuItem.Text = "نسخ السطر كامل ";
            // 
            // toolStripSeparator2
            // 
            this.toolStripSeparator2.Name = "toolStripSeparator2";
            this.toolStripSeparator2.Size = new System.Drawing.Size(175, 6);
            // 
            // DeleteCards_ToolStripMenuItem
            // 
            this.DeleteCards_ToolStripMenuItem.Name = "DeleteCards_ToolStripMenuItem";
            this.DeleteCards_ToolStripMenuItem.Size = new System.Drawing.Size(178, 22);
            this.DeleteCards_ToolStripMenuItem.Text = "حذف الكروت المحددة";
            // 
            // btnAdd
            // 
            this.btnAdd.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnAdd.BorderRadius = 7;
            this.btnAdd.BorderSize = 1;
            this.btnAdd.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnAdd.FlatAppearance.BorderSize = 0;
            this.btnAdd.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnAdd.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnAdd.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnAdd.Font = new System.Drawing.Font("Droid Arabic Kufi", 11F, System.Drawing.FontStyle.Bold);
            this.btnAdd.ForeColor = System.Drawing.Color.White;
            this.btnAdd.IconChar = FontAwesome.Sharp.IconChar.Plus;
            this.btnAdd.IconColor = System.Drawing.Color.White;
            this.btnAdd.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnAdd.IconSize = 25;
            this.btnAdd.ImageAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btnAdd.Location = new System.Drawing.Point(24, 230);
            this.btnAdd.Name = "btnAdd";
            this.btnAdd.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btnAdd.Size = new System.Drawing.Size(125, 45);
            this.btnAdd.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnAdd.TabIndex = 28;
            this.btnAdd.Text = "اضــــافـة";
            this.btnAdd.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnAdd.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btnAdd.UseVisualStyleBackColor = false;
            this.btnAdd.Click += new System.EventHandler(this.btnAdd_Click);
            // 
            // txt_Page_From
            // 
            this.txt_Page_From._Customizable = false;
            this.txt_Page_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Page_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Page_From.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Page_From.BorderRadius = 5;
            this.txt_Page_From.BorderSize = 1;
            this.txt_Page_From.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_Page_From.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Page_From.Location = new System.Drawing.Point(312, 77);
            this.txt_Page_From.MultiLine = false;
            this.txt_Page_From.Name = "txt_Page_From";
            this.txt_Page_From.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Page_From.PasswordChar = false;
            this.txt_Page_From.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Page_From.PlaceHolderText = null;
            this.txt_Page_From.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Page_From.Size = new System.Drawing.Size(167, 28);
            this.txt_Page_From.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_Page_From.TabIndex = 23;
            this.txt_Page_From.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // lbl_From_To
            // 
            this.lbl_From_To.AutoSize = true;
            this.lbl_From_To.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_From_To.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_From_To.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_From_To.LinkLabel = false;
            this.lbl_From_To.Location = new System.Drawing.Point(181, 43);
            this.lbl_From_To.Name = "lbl_From_To";
            this.lbl_From_To.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_From_To.Size = new System.Drawing.Size(94, 22);
            this.lbl_From_To.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_From_To.TabIndex = 16;
            this.lbl_From_To.Text = "الي رقم تسلسل";
            // 
            // txt_Sn_From
            // 
            this.txt_Sn_From._Customizable = false;
            this.txt_Sn_From.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Sn_From.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Sn_From.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Sn_From.BorderRadius = 5;
            this.txt_Sn_From.BorderSize = 1;
            this.txt_Sn_From.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_Sn_From.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Sn_From.Location = new System.Drawing.Point(312, 42);
            this.txt_Sn_From.MultiLine = false;
            this.txt_Sn_From.Name = "txt_Sn_From";
            this.txt_Sn_From.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Sn_From.PasswordChar = false;
            this.txt_Sn_From.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Sn_From.PlaceHolderText = null;
            this.txt_Sn_From.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Sn_From.Size = new System.Drawing.Size(167, 28);
            this.txt_Sn_From.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_Sn_From.TabIndex = 23;
            this.txt_Sn_From.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // txt_Page_To
            // 
            this.txt_Page_To._Customizable = false;
            this.txt_Page_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Page_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Page_To.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Page_To.BorderRadius = 5;
            this.txt_Page_To.BorderSize = 1;
            this.txt_Page_To.Font = new System.Drawing.Font("Verdana", 11.5F);
            this.txt_Page_To.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Page_To.Location = new System.Drawing.Point(8, 77);
            this.txt_Page_To.MultiLine = false;
            this.txt_Page_To.Name = "txt_Page_To";
            this.txt_Page_To.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Page_To.PasswordChar = false;
            this.txt_Page_To.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Page_To.PlaceHolderText = null;
            this.txt_Page_To.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Page_To.Size = new System.Drawing.Size(163, 29);
            this.txt_Page_To.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_Page_To.TabIndex = 23;
            this.txt_Page_To.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_Page_To.onTextChanged += new System.EventHandler(this.txt_Page_To_onTextChanged);
            // 
            // lbl_To_Page
            // 
            this.lbl_To_Page.AutoSize = true;
            this.lbl_To_Page.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_To_Page.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_To_Page.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_To_Page.LinkLabel = false;
            this.lbl_To_Page.Location = new System.Drawing.Point(197, 82);
            this.lbl_To_Page.Name = "lbl_To_Page";
            this.lbl_To_Page.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_To_Page.Size = new System.Drawing.Size(63, 22);
            this.lbl_To_Page.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_To_Page.TabIndex = 16;
            this.lbl_To_Page.Text = "الي صفحة";
            // 
            // txt_Sn_To
            // 
            this.txt_Sn_To._Customizable = false;
            this.txt_Sn_To.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_Sn_To.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_Sn_To.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_Sn_To.BorderRadius = 5;
            this.txt_Sn_To.BorderSize = 1;
            this.txt_Sn_To.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_Sn_To.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_Sn_To.Location = new System.Drawing.Point(8, 41);
            this.txt_Sn_To.MultiLine = false;
            this.txt_Sn_To.Name = "txt_Sn_To";
            this.txt_Sn_To.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_Sn_To.PasswordChar = false;
            this.txt_Sn_To.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_Sn_To.PlaceHolderText = null;
            this.txt_Sn_To.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_Sn_To.Size = new System.Drawing.Size(163, 28);
            this.txt_Sn_To.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_Sn_To.TabIndex = 23;
            this.txt_Sn_To.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_Sn_To.onTextChanged += new System.EventHandler(this.txt_Sn_To_onTextChanged);
            // 
            // pnl_attrbut
            // 
            this.pnl_attrbut.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_attrbut.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_attrbut.BorderRadius = 0;
            this.pnl_attrbut.Controls.Add(this.CBox_Attribute);
            this.pnl_attrbut.Controls.Add(this.CBox_group);
            this.pnl_attrbut.Controls.Add(this.lbl_Attribute);
            this.pnl_attrbut.Controls.Add(this.txt_attribute);
            this.pnl_attrbut.Controls.Add(this.lbl_group);
            this.pnl_attrbut.Customizable = false;
            this.pnl_attrbut.Location = new System.Drawing.Point(5, 119);
            this.pnl_attrbut.Name = "pnl_attrbut";
            this.pnl_attrbut.Size = new System.Drawing.Size(281, 99);
            this.pnl_attrbut.TabIndex = 29;
            this.pnl_attrbut.Visible = false;
            // 
            // CBox_Attribute
            // 
            this.CBox_Attribute.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_Attribute.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Attribute.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Attribute.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Attribute.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Attribute.BorderRadius = 5;
            this.CBox_Attribute.BorderSize = 1;
            this.CBox_Attribute.Customizable = false;
            this.CBox_Attribute.DataSource = null;
            this.CBox_Attribute.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Attribute.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Attribute.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Attribute.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Attribute.Items.AddRange(new object[] {
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"});
            this.CBox_Attribute.Location = new System.Drawing.Point(5, 36);
            this.CBox_Attribute.Name = "CBox_Attribute";
            this.CBox_Attribute.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Attribute.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_Attribute.SelectedIndex = -1;
            this.CBox_Attribute.Size = new System.Drawing.Size(163, 31);
            this.CBox_Attribute.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Attribute.TabIndex = 24;
            this.CBox_Attribute.Texts = "";
            // 
            // CBox_group
            // 
            this.CBox_group.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_group.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_group.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_group.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_group.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_group.BorderRadius = 5;
            this.CBox_group.BorderSize = 1;
            this.CBox_group.Customizable = false;
            this.CBox_group.DataSource = null;
            this.CBox_group.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_group.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_group.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_group.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_group.Items.AddRange(new object[] {
            "Sunday",
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"});
            this.CBox_group.Location = new System.Drawing.Point(5, 4);
            this.CBox_group.Name = "CBox_group";
            this.CBox_group.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_group.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_group.SelectedIndex = -1;
            this.CBox_group.Size = new System.Drawing.Size(163, 30);
            this.CBox_group.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_group.TabIndex = 23;
            this.CBox_group.Texts = "";
            // 
            // lbl_Attribute
            // 
            this.lbl_Attribute.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Attribute.AutoSize = true;
            this.lbl_Attribute.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Attribute.Font = new System.Drawing.Font("Verdana", 9F);
            this.lbl_Attribute.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Attribute.LinkLabel = false;
            this.lbl_Attribute.Location = new System.Drawing.Point(208, 47);
            this.lbl_Attribute.Name = "lbl_Attribute";
            this.lbl_Attribute.Size = new System.Drawing.Size(62, 14);
            this.lbl_Attribute.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Attribute.TabIndex = 1;
            this.lbl_Attribute.Text = "Attribute";
            // 
            // txt_attribute
            // 
            this.txt_attribute._Customizable = false;
            this.txt_attribute.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_attribute.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_attribute.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_attribute.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_attribute.BorderRadius = 5;
            this.txt_attribute.BorderSize = 1;
            this.txt_attribute.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_attribute.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_attribute.Location = new System.Drawing.Point(5, 68);
            this.txt_attribute.MultiLine = false;
            this.txt_attribute.Name = "txt_attribute";
            this.txt_attribute.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_attribute.PasswordChar = false;
            this.txt_attribute.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_attribute.PlaceHolderText = null;
            this.txt_attribute.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_attribute.Size = new System.Drawing.Size(163, 27);
            this.txt_attribute.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_attribute.TabIndex = 27;
            this.txt_attribute.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_attribute.Visible = false;
            // 
            // lbl_group
            // 
            this.lbl_group.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_group.AutoSize = true;
            this.lbl_group.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_group.Font = new System.Drawing.Font("Verdana", 9F);
            this.lbl_group.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_group.LinkLabel = false;
            this.lbl_group.Location = new System.Drawing.Point(219, 9);
            this.lbl_group.Name = "lbl_group";
            this.lbl_group.Size = new System.Drawing.Size(44, 14);
            this.lbl_group.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_group.TabIndex = 0;
            this.lbl_group.Text = "group";
            // 
            // pnl_customer
            // 
            this.pnl_customer.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_customer.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_customer.BorderRadius = 0;
            this.pnl_customer.Controls.Add(this.lbl_Customer);
            this.pnl_customer.Controls.Add(this.CBox_CustomerUserMan);
            this.pnl_customer.Customizable = false;
            this.pnl_customer.Location = new System.Drawing.Point(4, 120);
            this.pnl_customer.Name = "pnl_customer";
            this.pnl_customer.Size = new System.Drawing.Size(282, 90);
            this.pnl_customer.TabIndex = 28;
            // 
            // lbl_Customer
            // 
            this.lbl_Customer.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Customer.AutoSize = true;
            this.lbl_Customer.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Customer.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Customer.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Customer.LinkLabel = false;
            this.lbl_Customer.Location = new System.Drawing.Point(166, 9);
            this.lbl_Customer.Name = "lbl_Customer";
            this.lbl_Customer.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Customer.Size = new System.Drawing.Size(91, 17);
            this.lbl_Customer.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Customer.TabIndex = 16;
            this.lbl_Customer.Text = "مستخدم يوزمنجر";
            // 
            // CBox_CustomerUserMan
            // 
            this.CBox_CustomerUserMan.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.CBox_CustomerUserMan.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_CustomerUserMan.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_CustomerUserMan.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_CustomerUserMan.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_CustomerUserMan.BorderRadius = 5;
            this.CBox_CustomerUserMan.BorderSize = 1;
            this.CBox_CustomerUserMan.Customizable = false;
            this.CBox_CustomerUserMan.DataSource = null;
            this.CBox_CustomerUserMan.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_CustomerUserMan.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_CustomerUserMan.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_CustomerUserMan.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_CustomerUserMan.Location = new System.Drawing.Point(5, 3);
            this.CBox_CustomerUserMan.Name = "CBox_CustomerUserMan";
            this.CBox_CustomerUserMan.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_CustomerUserMan.SelectedIndex = -1;
            this.CBox_CustomerUserMan.Size = new System.Drawing.Size(160, 32);
            this.CBox_CustomerUserMan.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_CustomerUserMan.TabIndex = 22;
            this.CBox_CustomerUserMan.Texts = "";
            // 
            // CBox_SellingPoint
            // 
            this.CBox_SellingPoint.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_SellingPoint.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_SellingPoint.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_SellingPoint.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.BorderRadius = 5;
            this.CBox_SellingPoint.BorderSize = 1;
            this.CBox_SellingPoint.Customizable = false;
            this.CBox_SellingPoint.DataSource = null;
            this.CBox_SellingPoint.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_SellingPoint.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_SellingPoint.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_SellingPoint.Font = new System.Drawing.Font("Cairo", 8F);
            this.CBox_SellingPoint.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_SellingPoint.Location = new System.Drawing.Point(9, 3);
            this.CBox_SellingPoint.Name = "CBox_SellingPoint";
            this.CBox_SellingPoint.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_SellingPoint.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_SellingPoint.SelectedIndex = -1;
            this.CBox_SellingPoint.Size = new System.Drawing.Size(162, 32);
            this.CBox_SellingPoint.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_SellingPoint.TabIndex = 31;
            this.CBox_SellingPoint.Texts = "";
            // 
            // checkBoxSaveTo_excel
            // 
            this.checkBoxSaveTo_excel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_excel.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_excel.AutoSize = true;
            this.checkBoxSaveTo_excel.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_excel.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_excel.BorderSize = 1;
            this.checkBoxSaveTo_excel.Check = true;
            this.checkBoxSaveTo_excel.Checked = true;
            this.checkBoxSaveTo_excel.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxSaveTo_excel.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_excel.Customizable = false;
            this.checkBoxSaveTo_excel.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_excel.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_excel.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_excel.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_excel.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_excel.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_excel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_excel.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_excel.Location = new System.Drawing.Point(304, 70);
            this.checkBoxSaveTo_excel.Margin = new System.Windows.Forms.Padding(3, 4, 3, 3);
            this.checkBoxSaveTo_excel.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_excel.Name = "checkBoxSaveTo_excel";
            this.checkBoxSaveTo_excel.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_excel.Size = new System.Drawing.Size(21, 26);
            this.checkBoxSaveTo_excel.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_excel.TabIndex = 46;
            this.checkBoxSaveTo_excel.Text = ".";
            this.checkBoxSaveTo_excel.UseVisualStyleBackColor = false;
            // 
            // lbl_SellingPoint
            // 
            this.lbl_SellingPoint.AutoSize = true;
            this.lbl_SellingPoint.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_SellingPoint.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_SellingPoint.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_SellingPoint.LinkLabel = false;
            this.lbl_SellingPoint.Location = new System.Drawing.Point(189, 8);
            this.lbl_SellingPoint.Name = "lbl_SellingPoint";
            this.lbl_SellingPoint.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_SellingPoint.Size = new System.Drawing.Size(78, 22);
            this.lbl_SellingPoint.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_SellingPoint.TabIndex = 30;
            this.lbl_SellingPoint.Text = "نقـــطــة بيـــــع";
            // 
            // lbl_excel
            // 
            this.lbl_excel.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_excel.AutoSize = true;
            this.lbl_excel.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_excel.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_excel.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_excel.LinkLabel = false;
            this.lbl_excel.Location = new System.Drawing.Point(90, 71);
            this.lbl_excel.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_excel.Name = "lbl_excel";
            this.lbl_excel.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_excel.Size = new System.Drawing.Size(208, 22);
            this.lbl_excel.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_excel.TabIndex = 16;
            this.lbl_excel.Text = "حفظ نسخه من الكروت الي ملف اكسل";
            // 
            // txt_note
            // 
            this.txt_note._Customizable = false;
            this.txt_note.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_note.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_note.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_note.BorderRadius = 5;
            this.txt_note.BorderSize = 1;
            this.txt_note.Font = new System.Drawing.Font("Verdana", 10.5F);
            this.txt_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_note.Location = new System.Drawing.Point(45, 6);
            this.txt_note.MultiLine = false;
            this.txt_note.Name = "txt_note";
            this.txt_note.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_note.PasswordChar = false;
            this.txt_note.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_note.PlaceHolderText = null;
            this.txt_note.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_note.Size = new System.Drawing.Size(197, 28);
            this.txt_note.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_note.TabIndex = 50;
            this.txt_note.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // checkBoxSaveTo_text_File
            // 
            this.checkBoxSaveTo_text_File.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_text_File.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_text_File.AutoSize = true;
            this.checkBoxSaveTo_text_File.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_text_File.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_text_File.BorderSize = 1;
            this.checkBoxSaveTo_text_File.Check = true;
            this.checkBoxSaveTo_text_File.Checked = true;
            this.checkBoxSaveTo_text_File.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxSaveTo_text_File.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_text_File.Customizable = false;
            this.checkBoxSaveTo_text_File.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_text_File.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_text_File.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_text_File.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_text_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_text_File.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_text_File.Location = new System.Drawing.Point(304, 106);
            this.checkBoxSaveTo_text_File.Margin = new System.Windows.Forms.Padding(3, 4, 3, 3);
            this.checkBoxSaveTo_text_File.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_text_File.Name = "checkBoxSaveTo_text_File";
            this.checkBoxSaveTo_text_File.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_text_File.Size = new System.Drawing.Size(21, 26);
            this.checkBoxSaveTo_text_File.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_text_File.TabIndex = 46;
            this.checkBoxSaveTo_text_File.Text = ".";
            this.checkBoxSaveTo_text_File.UseVisualStyleBackColor = false;
            // 
            // lbl_text_File
            // 
            this.lbl_text_File.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_text_File.AutoSize = true;
            this.lbl_text_File.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_text_File.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_text_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_text_File.LinkLabel = false;
            this.lbl_text_File.Location = new System.Drawing.Point(93, 107);
            this.lbl_text_File.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_text_File.Name = "lbl_text_File";
            this.lbl_text_File.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_text_File.Size = new System.Drawing.Size(205, 22);
            this.lbl_text_File.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_text_File.TabIndex = 16;
            this.lbl_text_File.Text = "حفظ نسخة من الكروت الى ملف نصي";
            // 
            // checkBoxFirstUse
            // 
            this.checkBoxFirstUse.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxFirstUse.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxFirstUse.AutoSize = true;
            this.checkBoxFirstUse.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxFirstUse.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxFirstUse.BorderSize = 1;
            this.checkBoxFirstUse.Check = false;
            this.checkBoxFirstUse.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxFirstUse.Customizable = false;
            this.checkBoxFirstUse.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxFirstUse.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxFirstUse.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxFirstUse.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxFirstUse.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxFirstUse.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxFirstUse.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxFirstUse.IconColor = System.Drawing.Color.White;
            this.checkBoxFirstUse.Location = new System.Drawing.Point(304, 4);
            this.checkBoxFirstUse.Margin = new System.Windows.Forms.Padding(3, 4, 3, 3);
            this.checkBoxFirstUse.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxFirstUse.Name = "checkBoxFirstUse";
            this.checkBoxFirstUse.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxFirstUse.Size = new System.Drawing.Size(21, 26);
            this.checkBoxFirstUse.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxFirstUse.TabIndex = 50;
            this.checkBoxFirstUse.Text = ".";
            this.checkBoxFirstUse.UseVisualStyleBackColor = false;
            // 
            // lbl_FirstUse
            // 
            this.lbl_FirstUse.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_FirstUse.AutoSize = true;
            this.lbl_FirstUse.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_FirstUse.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_FirstUse.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_FirstUse.LinkLabel = false;
            this.lbl_FirstUse.Location = new System.Drawing.Point(179, 5);
            this.lbl_FirstUse.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_FirstUse.Name = "lbl_FirstUse";
            this.lbl_FirstUse.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_FirstUse.Size = new System.Drawing.Size(119, 22);
            this.lbl_FirstUse.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_FirstUse.TabIndex = 49;
            this.lbl_FirstUse.Text = "ربط الكروت بأول ماك";
            // 
            // checkBoxSaveTo_PDF
            // 
            this.checkBoxSaveTo_PDF.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_PDF.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_PDF.AutoSize = true;
            this.checkBoxSaveTo_PDF.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_PDF.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_PDF.BorderSize = 1;
            this.checkBoxSaveTo_PDF.Check = false;
            this.checkBoxSaveTo_PDF.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_PDF.Customizable = false;
            this.checkBoxSaveTo_PDF.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_PDF.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_PDF.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_PDF.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_PDF.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_PDF.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_PDF.Location = new System.Drawing.Point(313, 314);
            this.checkBoxSaveTo_PDF.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_PDF.Name = "checkBoxSaveTo_PDF";
            this.checkBoxSaveTo_PDF.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_PDF.Size = new System.Drawing.Size(22, 26);
            this.checkBoxSaveTo_PDF.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_PDF.TabIndex = 53;
            this.checkBoxSaveTo_PDF.Text = ".";
            this.checkBoxSaveTo_PDF.UseVisualStyleBackColor = false;
            this.checkBoxSaveTo_PDF.Visible = false;
            // 
            // lbl_Save_PDF
            // 
            this.lbl_Save_PDF.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Save_PDF.AutoSize = true;
            this.lbl_Save_PDF.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Save_PDF.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_Save_PDF.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Save_PDF.LinkLabel = false;
            this.lbl_Save_PDF.Location = new System.Drawing.Point(71, 314);
            this.lbl_Save_PDF.Name = "lbl_Save_PDF";
            this.lbl_Save_PDF.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Save_PDF.Size = new System.Drawing.Size(183, 22);
            this.lbl_Save_PDF.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Save_PDF.TabIndex = 51;
            this.lbl_Save_PDF.Text = "حفظ الى ملف PDF  بعد الطباعة";
            this.lbl_Save_PDF.Visible = false;
            // 
            // txt_last_batchNumber
            // 
            this.txt_last_batchNumber._Customizable = false;
            this.txt_last_batchNumber.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.txt_last_batchNumber.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_last_batchNumber.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.txt_last_batchNumber.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_last_batchNumber.BorderRadius = 5;
            this.txt_last_batchNumber.BorderSize = 1;
            this.txt_last_batchNumber.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.txt_last_batchNumber.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_last_batchNumber.Location = new System.Drawing.Point(265, 175);
            this.txt_last_batchNumber.MultiLine = false;
            this.txt_last_batchNumber.Name = "txt_last_batchNumber";
            this.txt_last_batchNumber.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_last_batchNumber.PasswordChar = false;
            this.txt_last_batchNumber.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_last_batchNumber.PlaceHolderText = null;
            this.txt_last_batchNumber.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_last_batchNumber.Size = new System.Drawing.Size(71, 27);
            this.txt_last_batchNumber.Style = SmartCreator.RJControls.TextBoxStyle.FlaringBorder;
            this.txt_last_batchNumber.TabIndex = 59;
            this.txt_last_batchNumber.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            // 
            // btn_OpenLastFile
            // 
            this.btn_OpenLastFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_OpenLastFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_OpenLastFile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.BorderRadius = 5;
            this.btn_OpenLastFile.BorderSize = 1;
            this.btn_OpenLastFile.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_OpenLastFile.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_OpenLastFile.FlatAppearance.BorderSize = 0;
            this.btn_OpenLastFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_OpenLastFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_OpenLastFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_OpenLastFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_OpenLastFile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconChar = FontAwesome.Sharp.IconChar.File;
            this.btn_OpenLastFile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_OpenLastFile.IconSize = 18;
            this.btn_OpenLastFile.Location = new System.Drawing.Point(132, 314);
            this.btn_OpenLastFile.Name = "btn_OpenLastFile";
            this.btn_OpenLastFile.Size = new System.Drawing.Size(31, 27);
            this.btn_OpenLastFile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_OpenLastFile.TabIndex = 52;
            this.btn_OpenLastFile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_OpenLastFile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_OpenLastFile.UseVisualStyleBackColor = false;
            this.btn_OpenLastFile.Visible = false;
            // 
            // Radio_SN
            // 
            this.Radio_SN.AutoSize = true;
            this.Radio_SN.Checked = true;
            this.Radio_SN.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_SN.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_SN.Customizable = false;
            this.Radio_SN.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_SN.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_SN.Location = new System.Drawing.Point(486, 45);
            this.Radio_SN.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_SN.Name = "Radio_SN";
            this.Radio_SN.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_SN.Size = new System.Drawing.Size(90, 21);
            this.Radio_SN.TabIndex = 56;
            this.Radio_SN.TabStop = true;
            this.Radio_SN.Text = "من تسلسل";
            this.Radio_SN.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_SN.UseVisualStyleBackColor = true;
            this.Radio_SN.CheckedChanged += new System.EventHandler(this.Radio_SN_CheckedChanged);
            // 
            // Radio_Page
            // 
            this.Radio_Page.AutoSize = true;
            this.Radio_Page.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_Page.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_Page.Customizable = false;
            this.Radio_Page.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.Radio_Page.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_Page.Location = new System.Drawing.Point(486, 82);
            this.Radio_Page.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_Page.Name = "Radio_Page";
            this.Radio_Page.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_Page.Size = new System.Drawing.Size(85, 21);
            this.Radio_Page.TabIndex = 56;
            this.Radio_Page.Text = "من صفحة";
            this.Radio_Page.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_Page.UseVisualStyleBackColor = true;
            this.Radio_Page.CheckedChanged += new System.EventHandler(this.Radio_Page_CheckedChanged);
            // 
            // checkBoxSaveTo_script_File
            // 
            this.checkBoxSaveTo_script_File.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBoxSaveTo_script_File.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBoxSaveTo_script_File.AutoSize = true;
            this.checkBoxSaveTo_script_File.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBoxSaveTo_script_File.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBoxSaveTo_script_File.BorderSize = 1;
            this.checkBoxSaveTo_script_File.Check = true;
            this.checkBoxSaveTo_script_File.Checked = true;
            this.checkBoxSaveTo_script_File.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBoxSaveTo_script_File.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBoxSaveTo_script_File.Customizable = false;
            this.checkBoxSaveTo_script_File.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBoxSaveTo_script_File.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBoxSaveTo_script_File.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBoxSaveTo_script_File.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBoxSaveTo_script_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBoxSaveTo_script_File.IconColor = System.Drawing.Color.White;
            this.checkBoxSaveTo_script_File.Location = new System.Drawing.Point(304, 39);
            this.checkBoxSaveTo_script_File.Margin = new System.Windows.Forms.Padding(3, 4, 3, 3);
            this.checkBoxSaveTo_script_File.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBoxSaveTo_script_File.Name = "checkBoxSaveTo_script_File";
            this.checkBoxSaveTo_script_File.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBoxSaveTo_script_File.Size = new System.Drawing.Size(21, 24);
            this.checkBoxSaveTo_script_File.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.checkBoxSaveTo_script_File.TabIndex = 55;
            this.checkBoxSaveTo_script_File.Text = ".";
            this.checkBoxSaveTo_script_File.UseVisualStyleBackColor = false;
            // 
            // lbl_script_File
            // 
            this.lbl_script_File.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_script_File.AutoSize = true;
            this.lbl_script_File.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_script_File.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_script_File.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_script_File.LinkLabel = false;
            this.lbl_script_File.Location = new System.Drawing.Point(82, 40);
            this.lbl_script_File.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_script_File.Name = "lbl_script_File";
            this.lbl_script_File.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_script_File.Size = new System.Drawing.Size(216, 22);
            this.lbl_script_File.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_script_File.TabIndex = 54;
            this.lbl_script_File.Text = "حفظ نسخة من الكروت في ملف سكربت";
            // 
            // pnl_Right
            // 
            this.pnl_Right.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_Right.BorderRadius = 10;
            this.pnl_Right.Controls.Add(this.txt_last_batchNumber);
            this.pnl_Right.Controls.Add(this.Radio_SN);
            this.pnl_Right.Controls.Add(this.lbl_Batch);
            this.pnl_Right.Controls.Add(this.lbl_SellingPoint);
            this.pnl_Right.Controls.Add(this.CBox_Batch);
            this.pnl_Right.Controls.Add(this.CBox_SellingPoint);
            this.pnl_Right.Controls.Add(this.txt_Sn_To);
            this.pnl_Right.Controls.Add(this.lbl_To_Page);
            this.pnl_Right.Controls.Add(this.txt_Page_To);
            this.pnl_Right.Controls.Add(this.txt_Sn_From);
            this.pnl_Right.Controls.Add(this.lbl_From_To);
            this.pnl_Right.Controls.Add(this.txt_Page_From);
            this.pnl_Right.Controls.Add(this.Radio_Page);
            this.pnl_Right.Controls.Add(this.checkBox_RegisterAs_LastBatch);
            this.pnl_Right.Controls.Add(this.pnl_customer);
            this.pnl_Right.Controls.Add(this.pnl_attrbut);
            this.pnl_Right.Controls.Add(this.checkBox_RegisterAsBatch);
            this.pnl_Right.Customizable = false;
            this.pnl_Right.Location = new System.Drawing.Point(341, 7);
            this.pnl_Right.Margin = new System.Windows.Forms.Padding(3, 20, 3, 3);
            this.pnl_Right.Name = "pnl_Right";
            this.pnl_Right.Padding = new System.Windows.Forms.Padding(10, 5, 5, 0);
            this.pnl_Right.Size = new System.Drawing.Size(595, 221);
            this.pnl_Right.TabIndex = 78;
            // 
            // lbl_Batch
            // 
            this.lbl_Batch.AutoSize = true;
            this.lbl_Batch.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Batch.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_Batch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Batch.LinkLabel = false;
            this.lbl_Batch.Location = new System.Drawing.Point(497, 8);
            this.lbl_Batch.Name = "lbl_Batch";
            this.lbl_Batch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Batch.Size = new System.Drawing.Size(63, 22);
            this.lbl_Batch.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Batch.TabIndex = 30;
            this.lbl_Batch.Text = "الــــدفعـــــة";
            // 
            // CBox_Batch
            // 
            this.CBox_Batch.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.CBox_Batch.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.CBox_Batch.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.CBox_Batch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.BorderRadius = 5;
            this.CBox_Batch.BorderSize = 1;
            this.CBox_Batch.Customizable = false;
            this.CBox_Batch.DataSource = null;
            this.CBox_Batch.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.CBox_Batch.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDownList;
            this.CBox_Batch.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.CBox_Batch.Font = new System.Drawing.Font("Verdana", 9F);
            this.CBox_Batch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.CBox_Batch.Location = new System.Drawing.Point(312, 3);
            this.CBox_Batch.Name = "CBox_Batch";
            this.CBox_Batch.Padding = new System.Windows.Forms.Padding(2);
            this.CBox_Batch.RightToLeft = System.Windows.Forms.RightToLeft.No;
            this.CBox_Batch.SelectedIndex = -1;
            this.CBox_Batch.Size = new System.Drawing.Size(167, 32);
            this.CBox_Batch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.CBox_Batch.TabIndex = 31;
            this.CBox_Batch.Texts = "";
            this.CBox_Batch.OnSelectedIndexChanged += new System.EventHandler(this.CBox_Batch_OnSelectedIndexChanged);
            // 
            // checkBox_RegisterAs_LastBatch
            // 
            this.checkBox_RegisterAs_LastBatch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBox_RegisterAs_LastBatch.AutoSize = true;
            this.checkBox_RegisterAs_LastBatch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAs_LastBatch.BorderSize = 1;
            this.checkBox_RegisterAs_LastBatch.Check = false;
            this.checkBox_RegisterAs_LastBatch.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_RegisterAs_LastBatch.Customizable = false;
            this.checkBox_RegisterAs_LastBatch.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.checkBox_RegisterAs_LastBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_RegisterAs_LastBatch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAs_LastBatch.Location = new System.Drawing.Point(376, 177);
            this.checkBox_RegisterAs_LastBatch.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_RegisterAs_LastBatch.Name = "checkBox_RegisterAs_LastBatch";
            this.checkBox_RegisterAs_LastBatch.Padding = new System.Windows.Forms.Padding(8, 0, 20, 0);
            this.checkBox_RegisterAs_LastBatch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkBox_RegisterAs_LastBatch.Size = new System.Drawing.Size(208, 21);
            this.checkBox_RegisterAs_LastBatch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.checkBox_RegisterAs_LastBatch.TabIndex = 79;
            this.checkBox_RegisterAs_LastBatch.Text = "التسجيل الي دفعة سابقة برقم";
            this.checkBox_RegisterAs_LastBatch.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_RegisterAs_LastBatch.UseVisualStyleBackColor = true;
            this.checkBox_RegisterAs_LastBatch.CheckedChanged += new System.EventHandler(this.checkBox_RegisterAs_LastBatch_CheckedChanged);
            // 
            // checkBox_RegisterAsBatch
            // 
            this.checkBox_RegisterAsBatch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.checkBox_RegisterAsBatch.AutoSize = true;
            this.checkBox_RegisterAsBatch.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAsBatch.BorderSize = 1;
            this.checkBox_RegisterAsBatch.Check = true;
            this.checkBox_RegisterAsBatch.Checked = true;
            this.checkBox_RegisterAsBatch.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_RegisterAsBatch.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_RegisterAsBatch.Customizable = false;
            this.checkBox_RegisterAsBatch.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.checkBox_RegisterAsBatch.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_RegisterAsBatch.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_RegisterAsBatch.Location = new System.Drawing.Point(322, 138);
            this.checkBox_RegisterAsBatch.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_RegisterAsBatch.Name = "checkBox_RegisterAsBatch";
            this.checkBox_RegisterAsBatch.Padding = new System.Windows.Forms.Padding(8, 0, 20, 0);
            this.checkBox_RegisterAsBatch.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.checkBox_RegisterAsBatch.Size = new System.Drawing.Size(262, 21);
            this.checkBox_RegisterAsBatch.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.checkBox_RegisterAsBatch.TabIndex = 78;
            this.checkBox_RegisterAsBatch.Text = "تسجيل عمليه الاضافة هذه كدفعه جديده";
            this.checkBox_RegisterAsBatch.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.checkBox_RegisterAsBatch.UseVisualStyleBackColor = true;
            this.checkBox_RegisterAsBatch.CheckedChanged += new System.EventHandler(this.checkBox_RegisterAsBatch_CheckedChanged);
            // 
            // rjPanel2
            // 
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 10;
            this.rjPanel2.Controls.Add(this.tableLayoutPanel1);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(7, 7);
            this.rjPanel2.Margin = new System.Windows.Forms.Padding(3, 5, 3, 3);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(328, 221);
            this.rjPanel2.TabIndex = 79;
            // 
            // tableLayoutPanel1
            // 
            this.tableLayoutPanel1.ColumnCount = 2;
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 91.7683F));
            this.tableLayoutPanel1.ColumnStyles.Add(new System.Windows.Forms.ColumnStyle(System.Windows.Forms.SizeType.Percent, 8.231708F));
            this.tableLayoutPanel1.Controls.Add(this.checkBox_note, 1, 5);
            this.tableLayoutPanel1.Controls.Add(this.checkBoxFirstUse, 1, 0);
            this.tableLayoutPanel1.Controls.Add(this.rjLabel1, 0, 4);
            this.tableLayoutPanel1.Controls.Add(this.lbl_script_File, 0, 1);
            this.tableLayoutPanel1.Controls.Add(this.checkBox_Remove_FromArchive, 1, 4);
            this.tableLayoutPanel1.Controls.Add(this.lbl_excel, 0, 2);
            this.tableLayoutPanel1.Controls.Add(this.lbl_text_File, 0, 3);
            this.tableLayoutPanel1.Controls.Add(this.lbl_FirstUse, 0, 0);
            this.tableLayoutPanel1.Controls.Add(this.checkBoxSaveTo_text_File, 1, 3);
            this.tableLayoutPanel1.Controls.Add(this.checkBoxSaveTo_script_File, 1, 1);
            this.tableLayoutPanel1.Controls.Add(this.checkBoxSaveTo_excel, 1, 2);
            this.tableLayoutPanel1.Controls.Add(this.flowLayoutPanel1, 0, 5);
            this.tableLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tableLayoutPanel1.Location = new System.Drawing.Point(0, 0);
            this.tableLayoutPanel1.Margin = new System.Windows.Forms.Padding(3, 10, 3, 3);
            this.tableLayoutPanel1.Name = "tableLayoutPanel1";
            this.tableLayoutPanel1.RowCount = 6;
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 31F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 36F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 38F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 35F));
            this.tableLayoutPanel1.RowStyles.Add(new System.Windows.Forms.RowStyle(System.Windows.Forms.SizeType.Absolute, 40F));
            this.tableLayoutPanel1.Size = new System.Drawing.Size(328, 221);
            this.tableLayoutPanel1.TabIndex = 86;
            // 
            // checkBox_note
            // 
            this.checkBox_note.AutoSize = true;
            this.checkBox_note.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_note.BorderSize = 1;
            this.checkBox_note.Check = true;
            this.checkBox_note.Checked = true;
            this.checkBox_note.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_note.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_note.Customizable = false;
            this.checkBox_note.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_note.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_note.Location = new System.Drawing.Point(304, 183);
            this.checkBox_note.Margin = new System.Windows.Forms.Padding(3, 8, 3, 3);
            this.checkBox_note.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_note.Name = "checkBox_note";
            this.checkBox_note.Padding = new System.Windows.Forms.Padding(10, 3, 0, 0);
            this.checkBox_note.Size = new System.Drawing.Size(21, 21);
            this.checkBox_note.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.checkBox_note.TabIndex = 78;
            this.checkBox_note.UseVisualStyleBackColor = true;
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(91, 145);
            this.rjLabel1.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(207, 22);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 57;
            this.rjLabel1.Text = "حذف من ارشيف المعلقات بعد الاضافة";
            // 
            // checkBox_Remove_FromArchive
            // 
            this.checkBox_Remove_FromArchive.Appearance = System.Windows.Forms.Appearance.Button;
            this.checkBox_Remove_FromArchive.AutoSize = true;
            this.checkBox_Remove_FromArchive.BackgroundImageLayout = System.Windows.Forms.ImageLayout.None;
            this.checkBox_Remove_FromArchive.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_Remove_FromArchive.BorderSize = 1;
            this.checkBox_Remove_FromArchive.Check = true;
            this.checkBox_Remove_FromArchive.Checked = true;
            this.checkBox_Remove_FromArchive.CheckState = System.Windows.Forms.CheckState.Checked;
            this.checkBox_Remove_FromArchive.Cursor = System.Windows.Forms.Cursors.Hand;
            this.checkBox_Remove_FromArchive.Customizable = false;
            this.checkBox_Remove_FromArchive.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_Remove_FromArchive.FlatAppearance.CheckedBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.checkBox_Remove_FromArchive.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(120)))), ((int)(((byte)(218)))));
            this.checkBox_Remove_FromArchive.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(70)))), ((int)(((byte)(82)))), ((int)(((byte)(180)))));
            this.checkBox_Remove_FromArchive.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.checkBox_Remove_FromArchive.Font = new System.Drawing.Font("Verdana", 9.5F);
            this.checkBox_Remove_FromArchive.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.checkBox_Remove_FromArchive.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.checkBox_Remove_FromArchive.Location = new System.Drawing.Point(304, 144);
            this.checkBox_Remove_FromArchive.Margin = new System.Windows.Forms.Padding(3, 4, 3, 3);
            this.checkBox_Remove_FromArchive.MinimumSize = new System.Drawing.Size(0, 21);
            this.checkBox_Remove_FromArchive.Name = "checkBox_Remove_FromArchive";
            this.checkBox_Remove_FromArchive.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.checkBox_Remove_FromArchive.Size = new System.Drawing.Size(21, 26);
            this.checkBox_Remove_FromArchive.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.checkBox_Remove_FromArchive.TabIndex = 58;
            this.checkBox_Remove_FromArchive.Text = ".";
            this.checkBox_Remove_FromArchive.UseVisualStyleBackColor = false;
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.Controls.Add(this.lbl_note);
            this.flowLayoutPanel1.Controls.Add(this.txt_note);
            this.flowLayoutPanel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.flowLayoutPanel1.Location = new System.Drawing.Point(0, 175);
            this.flowLayoutPanel1.Margin = new System.Windows.Forms.Padding(0);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.flowLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel1.Size = new System.Drawing.Size(301, 46);
            this.flowLayoutPanel1.TabIndex = 59;
            // 
            // lbl_note
            // 
            this.lbl_note.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(248, 8);
            this.lbl_note.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(50, 22);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_note.TabIndex = 30;
            this.lbl_note.Text = "ملاحظة";
            // 
            // btn_search
            // 
            this.btn_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.btn_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.BorderRadius = 1;
            this.btn_search.BorderSize = 1;
            this.btn_search.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_search.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_search.FlatAppearance.BorderSize = 0;
            this.btn_search.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(225)))), ((int)(((byte)(230)))), ((int)(((byte)(234)))));
            this.btn_search.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(211)))), ((int)(((byte)(215)))), ((int)(((byte)(219)))));
            this.btn_search.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_search.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconChar = FontAwesome.Sharp.IconChar.MagnifyingGlass;
            this.btn_search.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_search.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_search.IconSize = 24;
            this.btn_search.Location = new System.Drawing.Point(745, 239);
            this.btn_search.Name = "btn_search";
            this.btn_search.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_search.Size = new System.Drawing.Size(30, 29);
            this.btn_search.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_search.TabIndex = 83;
            this.btn_search.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_search.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_search.UseVisualStyleBackColor = false;
            this.btn_search.Visible = false;
            this.btn_search.Click += new System.EventHandler(this.btn_search_Click);
            // 
            // txt_search
            // 
            this.txt_search._Customizable = false;
            this.txt_search.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.txt_search.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.BorderFocusColor = System.Drawing.Color.FromArgb(((int)(((byte)(142)))), ((int)(((byte)(126)))), ((int)(((byte)(240)))));
            this.txt_search.BorderRadius = 5;
            this.txt_search.BorderSize = 1;
            this.txt_search.Font = new System.Drawing.Font("Verdana", 9F);
            this.txt_search.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.txt_search.Location = new System.Drawing.Point(776, 239);
            this.txt_search.MultiLine = false;
            this.txt_search.Name = "txt_search";
            this.txt_search.Padding = new System.Windows.Forms.Padding(10, 5, 10, 5);
            this.txt_search.PasswordChar = false;
            this.txt_search.PlaceHolderColor = System.Drawing.Color.DarkGray;
            this.txt_search.PlaceHolderText = "بحث";
            this.txt_search.ScrollBars = System.Windows.Forms.ScrollBars.None;
            this.txt_search.Size = new System.Drawing.Size(160, 25);
            this.txt_search.Style = SmartCreator.RJControls.TextBoxStyle.MatteBorder;
            this.txt_search.TabIndex = 82;
            this.txt_search.TextAlign = System.Windows.Forms.HorizontalAlignment.Center;
            this.txt_search.onTextChanged += new System.EventHandler(this.txt_search_onTextChanged);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // lbl_Count
            // 
            this.lbl_Count.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Count.AutoSize = true;
            this.lbl_Count.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Count.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F);
            this.lbl_Count.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Count.LinkLabel = false;
            this.lbl_Count.Location = new System.Drawing.Point(468, 241);
            this.lbl_Count.Name = "lbl_Count";
            this.lbl_Count.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Count.Size = new System.Drawing.Size(37, 22);
            this.lbl_Count.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Count.TabIndex = 30;
            this.lbl_Count.Text = "العدد";
            // 
            // btnRefresh
            // 
            this.btnRefresh.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRefresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btnRefresh.BorderRadius = 5;
            this.btnRefresh.BorderSize = 1;
            this.btnRefresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btnRefresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btnRefresh.FlatAppearance.BorderSize = 0;
            this.btnRefresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btnRefresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btnRefresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btnRefresh.Font = new System.Drawing.Font("Droid Arabic Kufi", 10F, System.Drawing.FontStyle.Bold);
            this.btnRefresh.ForeColor = System.Drawing.Color.White;
            this.btnRefresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btnRefresh.IconColor = System.Drawing.Color.White;
            this.btnRefresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btnRefresh.IconSize = 25;
            this.btnRefresh.Location = new System.Drawing.Point(149, 230);
            this.btnRefresh.Name = "btnRefresh";
            this.btnRefresh.Size = new System.Drawing.Size(29, 45);
            this.btnRefresh.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btnRefresh.TabIndex = 84;
            this.btnRefresh.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btnRefresh.TextImageRelation = System.Windows.Forms.TextImageRelation.TextBeforeImage;
            this.btnRefresh.UseVisualStyleBackColor = false;
            this.btnRefresh.Click += new System.EventHandler(this.btnRefresh_Click);
            // 
            // dgv_freq
            // 
            this.dgv_freq.AllowUserToAddRows = false;
            this.dgv_freq.AllowUserToDeleteRows = false;
            this.dgv_freq.AllowUserToOrderColumns = true;
            this.dgv_freq.AllowUserToResizeRows = false;
            this.dgv_freq.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv_freq.AlternatingRowsColorApply = false;
            this.dgv_freq.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv_freq.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_freq.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_freq.BorderRadius = 10;
            this.dgv_freq.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv_freq.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv_freq.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv_freq.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv_freq.ColumnHeaderHeight = 40;
            this.dgv_freq.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_freq.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv_freq.ColumnHeadersHeight = 40;
            this.dgv_freq.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv_freq.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv_freq.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv_freq.Customizable = false;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.SystemColors.Window;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.SystemColors.ControlText;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_freq.DefaultCellStyle = dataGridViewCellStyle2;
            this.dgv_freq.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv_freq.EnableHeadersVisualStyles = false;
            this.dgv_freq.GridColor = System.Drawing.Color.FromArgb(((int)(((byte)(224)))), ((int)(((byte)(224)))), ((int)(((byte)(224)))));
            this.dgv_freq.Location = new System.Drawing.Point(803, 279);
            this.dgv_freq.Name = "dgv_freq";
            this.dgv_freq.ReadOnly = true;
            this.dgv_freq.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv_freq.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv_freq.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle3.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv_freq.RowHeadersDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv_freq.RowHeadersVisible = false;
            this.dgv_freq.RowHeadersWidth = 30;
            this.dgv_freq.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv_freq.RowHeight = 30;
            this.dgv_freq.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle4.Font = new System.Drawing.Font("Tahoma", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle4.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle4.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle4.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv_freq.RowsDefaultCellStyle = dataGridViewCellStyle4;
            this.dgv_freq.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv_freq.RowTemplate.Height = 30;
            this.dgv_freq.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv_freq.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv_freq.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv_freq.Size = new System.Drawing.Size(131, 273);
            this.dgv_freq.TabIndex = 85;
            this.dgv_freq.Visible = false;
            this.dgv_freq.CellClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_freq_CellClick);
            // 
            // Form_Print_FromArchive
            // 
            this._DesktopPanelSize = false;
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.BorderSize = 5;
            this.Caption = "استيراد الكروت من الارشيف";
            this.ClientSize = new System.Drawing.Size(956, 618);
            this.ControlBox = false;
            this.DisableFormOptions = true;
            this.DisplayMaximizeButton = false;
            this.DisplayMinimizeButton = false;
            this.Name = "Form_Print_FromArchive";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Resizable = false;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterParent;
            this.Text = "استيراد الكروت من الارشيف";
            this.Load += new System.EventHandler(this.Form_Print_FromArchive_Load);
            this.Controls.SetChildIndex(this.pnlClientArea, 0);
            this.pnlClientArea.ResumeLayout(false);
            this.pnlClientArea.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.dmAll_Cards.ResumeLayout(false);
            this.pnl_attrbut.ResumeLayout(false);
            this.pnl_attrbut.PerformLayout();
            this.pnl_customer.ResumeLayout(false);
            this.pnl_customer.PerformLayout();
            this.pnl_Right.ResumeLayout(false);
            this.pnl_Right.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.tableLayoutPanel1.ResumeLayout(false);
            this.tableLayoutPanel1.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.flowLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv_freq)).EndInit();
            this.ResumeLayout(false);

        }

        #endregion

        public RJControls.RJButton btnAdd;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJLabel lbl_script_File;
        public RJControls.RJCheckBox checkBoxSaveTo_script_File;
        public RJControls.RJTextBox txt_last_batchNumber;
        private RJControls.RJLabel lbl_excel;
        public RJControls.RJCheckBox checkBoxSaveTo_excel;
        private RJControls.RJLabel lbl_text_File;
        public RJControls.RJCheckBox checkBoxSaveTo_text_File;
        private RJControls.RJButton btn_OpenLastFile;
        public RJControls.RJCheckBox checkBoxSaveTo_PDF;
        private RJControls.RJLabel lbl_Save_PDF;
        private RJControls.RJLabel lbl_FirstUse;
        public RJControls.RJCheckBox checkBoxFirstUse;
        private RJControls.RJPanel pnl_Right;
        private RJControls.RJRadioButton Radio_Page;
        private RJControls.RJRadioButton Radio_SN;
        public RJControls.RJTextBox txt_note;
        private RJControls.RJLabel lbl_SellingPoint;
        public RJControls.RJComboBox CBox_SellingPoint;
        private RJControls.RJPanel pnl_customer;
        public RJControls.RJComboBox CBox_CustomerUserMan;
        private RJControls.RJLabel lbl_Customer;
        private RJControls.RJPanel pnl_attrbut;
        public RJControls.RJComboBox CBox_Attribute;
        public RJControls.RJComboBox CBox_group;
        private RJControls.RJLabel lbl_Attribute;
        private RJControls.RJLabel lbl_group;
        public RJControls.RJTextBox txt_attribute;
        public RJControls.RJTextBox txt_Sn_To;
        private RJControls.RJLabel lbl_To_Page;
        public RJControls.RJTextBox txt_Page_To;
        public RJControls.RJTextBox txt_Sn_From;
        private RJControls.RJLabel lbl_From_To;
        public RJControls.RJTextBox txt_Page_From;
        private RJControls.RJButton btn_search;
        private RJControls.RJTextBox txt_search;
        private RJControls.RJLabel lbl_Batch;
        public RJControls.RJComboBox CBox_Batch;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJLabel lbl_Count;
        private RJControls.RJLabel rjLabel1;
        public RJControls.RJCheckBox checkBox_Remove_FromArchive;
        private RJControls.RJButton btnRefresh;
        private RJControls.RJDataGridView dgv_freq;
        private RJControls.RJDropdownMenu dmAll_Cards;
        private System.Windows.Forms.ToolStripMenuItem Copy_ToolStripMenuItem;
        private System.Windows.Forms.ToolStripMenuItem Copy_AllRowToolStripMenuItem;
        private System.Windows.Forms.ToolStripSeparator toolStripSeparator2;
        private System.Windows.Forms.ToolStripMenuItem DeleteCards_ToolStripMenuItem;
        private RJControls.RJCheckBox checkBox_RegisterAsBatch;
        private RJControls.RJCheckBox checkBox_RegisterAs_LastBatch;
        private RJControls.RJCheckBox checkBox_note;
        private System.Windows.Forms.TableLayoutPanel tableLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private RJControls.RJLabel lbl_note;
    }
}