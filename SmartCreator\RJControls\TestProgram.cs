using System;
using System.Windows.Forms;

namespace SmartCreator.RJControls
{
    /// <summary>
    /// برنامج اختبار بديل لـ RJTabControl
    /// </summary>
    public static class TestProgram
    {
        /// <summary>
        /// نقطة الدخول الرئيسية للتطبيق
        /// </summary>
        [STAThread]
        public static void Main()
        {
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            
            try
            {
                // تشغيل النموذج الرئيسي
                MainTestForm.RunMain();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل التطبيق:\n\n{ex.Message}\n\n" +
                               "تأكد من أن جميع المراجع صحيحة وأن المشروع مبني بنجاح.",
                               "خطأ في التطبيق", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار سريع
        /// </summary>
        public static void RunQuickTest()
        {
            try
            {
                QuickErrorTest.RunQuickTest();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في الاختبار السريع:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل جميع الاختبارات
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                SafeTestRunner.ShowTestMenu();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تشغيل الاختبارات:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// تشغيل اختبار Designer
        /// </summary>
        public static void RunDesignerTest()
        {
            try
            {
                DesignerSafeTestForm.RunTest();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في اختبار Designer:\n\n{ex.Message}",
                               "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
