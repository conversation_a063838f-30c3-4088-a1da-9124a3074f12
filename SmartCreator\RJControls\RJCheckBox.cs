﻿using FontAwesome.Sharp;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;
using SmartCreator.Settings;
using System.Drawing.Drawing2D;
using SmartCreator.Utils;
using SmartCreator.Models;

namespace SmartCreator.RJControls
{
    public class RJCheckBox : CheckBox
    {
        /// <summary>
        /// This class inherits from the <see cref = "CheckBox" /> class
        /// You can change the style of the button to glassy or solid (<see cref = "ControlStyle Style" />).
        /// In addition to being able to customize the colors of the background, border size, border radius, icon, etc.
        /// as long as the customizable property is set to true.
        /// Tutorial guide: https://www.youtube.com/watch?v=SAA5qDoiL4M
        /// </summary>       
        /// 

        #region -> Fields

        private bool customizable = false; // Gets or sets if the control is customizable.
        private ControlStyle style = ControlStyle.Glass; // Gets or sets the CheckBox style (Glass or Solid).
        private int borderSize = 1; // Gets or sets the border size.
        private Color borderColor = UIAppearance.StyleColor; // Gets or sets the border color.
        private IconPictureBox CheckIcon; // Gets or sets the checked checkbox icon.

        /// <Note>: ICON PICTURE BOX is provided by the library <see cref = "FontAwesome.Sharp" />
        /// Author: mkoertgen
        /// GitHub: https://github.com/awesome-inc/FontAwesome.Sharp
        /// Nuget Package: https://www.nuget.org/packages/FontAwesome.Sharp </Note>
        #endregion

        #region -> Constructor

        public RJCheckBox()
        {
            CheckIcon = new IconPictureBox(); // Initialize check icon. 
            CheckIcon.IconChar = IconChar.Check; // Set icon.
            //int rbSize2 = (int)(16 + (16 - (16 * 96f / Global_Variable.Graphics_dpi)));
             
            CheckIcon.IconSize = utils.Control_Mesur_DPI(16);
            //CheckIcon.IconSize = 16;
            //CheckIcon.IconSize = 16;
            CheckIcon.IconColor = UIAppearance.StyleColor;
            this.Cursor = Cursors.Hand;
            this.Checked = true;
            if (UIAppearance.Language_ar)
            //this.Font = new Font(UIAppearance.TextFamilyName_ar, (UIAppearance.TextSize   * 96f / Global_Variable.Graphics_dpi));
            this.Font = new Font(UIAppearance.TextFamilyName_ar, (UIAppearance.TextSize));
            else
                this.Font = new Font(UIAppearance.TextFamilyName, (UIAppearance.TextSize));
                //this.Font = new Font(UIAppearance.TextFamilyName, UIAppearance.TextSize * utils.ScaleFactor);

            this.ForeColor = UIAppearance.TextColor;
            this.MinimumSize = new Size(0, utils.Control_Mesur_DPI(21));
            // Set a Padding of 10 to the left to increase the width of the control, so that the icon and text fit and display completely.
           if (UIAppearance.Language_ar)
            this.Padding = new Padding(8, 0, 0, 0);
        else
                this.Padding = new Padding(0, 0, 8, 0);

            RightToLeftChanged += CustomCheckBox_RightToLeftChanged;

        }
        #endregion

        #region -> Properties

        [Category("RJ Code Advance")]
        [Description("Gets or Sets the CheckBox style (Glass or Solid)")]
        public ControlStyle Style
        {
            get { return style; }
            set
            {
                style = value; // Set value
                this.Image = null; // Remove check icon (when style change, checkIcon image changes, then image is stale, see next method codes)
                ApplyAppearanceSettings(); // Update or apply appearance settings.
                this.Invalidate(); // Redraw the control to update the appearance.
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the width of the border")]
        public int BorderSize
        {
            get { return borderSize; }
            set
            {
                if (value > 0) // The value must be greater than 0.
                    borderSize = value;
                this.Invalidate(); // Redraw the control to update the appearance.
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets to sets if checkbox is checked")]
        public bool Check
        {
            get { return this.Checked; }
            set { this.Checked = value; }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets whether the control's appearance colors are customizable, otherwise the appearance color is set according to the appearance settings.")]
        public bool Customizable
        {
            get { return customizable; }
            set { customizable = value; }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the border color.")]
        public Color BorderColor
        {
            get { return borderColor; }
            set
            {
                borderColor = value;
                this.Invalidate(); // Redraw the control to update the appearance.
            }
        }

        [Category("RJ Code Advance")]
        [Description("Gets or sets the color of the check icon.")]
        public Color IconColor
        {
            get { return CheckIcon.IconColor; }
            set
            {
                CheckIcon.IconColor = value;
                this.Invalidate(); // Redraw the control to update the appearance.
            }
        }

        #endregion

        #region -> Private methods

        private void ApplyAppearanceSettings()
        {// Apply appearance settings as long as the customizable property is set to false.
            if (customizable == false)
            {
                borderColor = UIAppearance.StyleColor;
                this.ForeColor = UIAppearance.TextColor;
                if (style == ControlStyle.Solid)
                    IconColor = Color.White;
                else
                    IconColor = UIAppearance.StyleColor;
            }
        }

        private void CustomCheckBox_RightToLeftChanged(object? sender, EventArgs e)
        {
            Invalidate();
        }
        #endregion

        #region -> Overridden methods

        protected override void OnPaint(PaintEventArgs pevent)
        {// This method takes care of completely overriding the paint and drawing a new control with a custom appearance.

            // Fields
            Graphics graphics = pevent.Graphics; // Graphics object.
            int cbSize = utils.Control_Mesur_DPI(15); // Size of the checkbox.
            //int cbSize = 15; // Size of the checkbox.
            int checkIconSize = CheckIcon.IconSize; // Checkbox icon size.
            Rectangle rectCheckBox = new Rectangle();


            //CheckBox checkBox = this;
            //SizeF sizeF = this.CreateGraphics().MeasureString(this.Text, this.Font);
            SizeF sizeF = TextRenderer.MeasureText(this.Text, this.Font);
            int rectSize = utils.Control_Mesur_DPI(15);
            //int rectSize = 15;
            int x;
            float textX;

            //this.Height = (int)sizeF.Height;
            //this.Width = (int)(sizeF.Width + rectSize + 5);

            if (this.RightToLeft == RightToLeft.No)
            {
                this.TextAlign = ContentAlignment.MiddleLeft;
                x = 1;
                textX = (float)(rectSize * 1.3);
            }
            else
            {
                this.TextAlign = ContentAlignment.MiddleRight;
                x = (this.Width - rectSize - 2);
                textX = this.Width - sizeF.Width - (float)(rectSize * 1.2) + checkIconSize;
            }

            int y = (this.Height - cbSize) / 2;
            //int y = (this.ClientRectangle.Y + 1 + (this.ClientRectangle.Height - rectSize)) / 2;
            Point pt = new(x, y);
            //Rectangle rectCheck = new(pt, new Size(rectSize, rectSize));
            rectCheckBox = new(pt, new Size(rectSize, rectSize));

            //rectCheckBox = new Rectangle() // Checkbox dimensions.
            //{
            //    X = 1,
            //    Y = (this.Height - cbSize) / 2, // Centered
            //    Width = cbSize,
            //    Height = cbSize
            //};



            Rectangle rectCheckIcon = new Rectangle();
            rectCheckIcon = new Rectangle() // Dimensions of the checkbox icon.
            {
                X = x + 1, // Centered
                //X = rectCheckBox.X + ((rectCheckBox.Width - checkIconSize) / 2) + 1, // Centered
                Y = ((this.Height - checkIconSize) / 2) + 2, // Centered
                Width = checkIconSize,
                Height = checkIconSize
            };

            //He drew
            using (Pen penRbBorder = new Pen(borderColor, borderSize)) // Pen to draw the border.
            using (SolidBrush brushRbFill = new SolidBrush(borderColor)) // Brush to draw the fill of the box.
            using (SolidBrush brushText = new SolidBrush(this.ForeColor)) // Brush to draw the text.
            {
                graphics.SmoothingMode = SmoothingMode.AntiAlias; // Set the smoothing mode.
                // Draw the control surface
                graphics.Clear(this.BackColor);
                // Draw the checkbox
                if (this.Checked) // Checked state.
                {
                    if (style == ControlStyle.Solid)
                        graphics.FillRectangle(brushRbFill, rectCheckBox); // Fill the box
                    graphics.DrawRectangle(penRbBorder, rectCheckBox); // Border of the box
                    graphics.DrawImage(CheckIcon.Image, rectCheckIcon); // Checkbox icon
                }
                else // State not checked.
                {
                    graphics.DrawRectangle(penRbBorder, rectCheckBox); // Border of the box
                }
                // Draw the text

                graphics.DrawString(this.Text, this.Font, brushText,
               cbSize + 8, (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2); // Y = Centered


                //graphics.DrawString(this.Text, this.Font, brushText, textX, (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2);
                //graphics.DrawString(this.Text, this.Font, brushText, textX, 0);



            }
        }

        //protected override void OnPaint(PaintEventArgs pevent)
        //{// This method takes care of completely overriding the paint and drawing a new control with a custom appearance.

        //    // Fields
        //    Graphics graphics = pevent.Graphics; // Graphics object.
        //    int cbSize = 15; // Size of the checkbox.
        //    int checkIconSize = CheckIcon.IconSize; // Checkbox icon size.
        //    Rectangle rectCheckBox = new Rectangle();

        //    int x = (TextRenderer.MeasureText(this.Text, this.Font).Width);

        //    //if (this.RightToLeft == RightToLeft.Yes)
        //    //{
        //    //     rectCheckBox = new Rectangle() // Checkbox dimensions.
        //    //    {
        //    //        X = 1-(x+cbSize) ,
        //    //         Y = (this.Height - cbSize) / 2, // Centered
        //    //        Width = cbSize,
        //    //        Height = cbSize
        //    //    };
        //    //}
        //    //else
        //    {
        //        rectCheckBox = new Rectangle() // Checkbox dimensions.
        //        {
        //            X = 1,
        //            Y = (this.Height - cbSize) / 2, // Centered
        //            Width = cbSize,
        //            Height = cbSize
        //        };
        //    }


        //    Rectangle rectCheckIcon = new Rectangle();
        //    //if (this.RightToLeft == RightToLeft.Yes)
        //    //{
        //    //    rectCheckIcon = new Rectangle() // Dimensions of the checkbox icon.
        //    //    {
        //    //        X = ((rectCheckBox.Width) ) + 1, // Centered
        //    //        Y = ((this.Height - checkIconSize) / 2) + 2, // Centered
        //    //        Width = checkIconSize,
        //    //        Height = checkIconSize
        //    //    };
        //    //}
        //    //else
        //    {

        //        rectCheckIcon = new Rectangle() // Dimensions of the checkbox icon.
        //        {
        //            X = rectCheckBox.X + ((rectCheckBox.Width - checkIconSize) / 2) + 1, // Centered
        //            Y = ((this.Height - checkIconSize) / 2) + 2, // Centered
        //            Width = checkIconSize,
        //            Height = checkIconSize
        //        };
        //    }
        //    //He drew
        //    using (Pen penRbBorder = new Pen(borderColor, borderSize)) // Pen to draw the border.
        //    using (SolidBrush brushRbFill = new SolidBrush(borderColor)) // Brush to draw the fill of the box.
        //    using (SolidBrush brushText = new SolidBrush(this.ForeColor)) // Brush to draw the text.
        //    {
        //        graphics.SmoothingMode = SmoothingMode.AntiAlias; // Set the smoothing mode.
        //        // Draw the control surface
        //        graphics.Clear(this.BackColor);
        //        // Draw the checkbox
        //        if (this.Checked) // Checked state.
        //        {
        //            if (style == ControlStyle.Solid)
        //                graphics.FillRectangle(brushRbFill, rectCheckBox); // Fill the box
        //            graphics.DrawRectangle(penRbBorder, rectCheckBox); // Border of the box
        //            graphics.DrawImage(CheckIcon.Image, rectCheckIcon); // Checkbox icon
        //        }
        //        else // State not checked.
        //        {
        //            graphics.DrawRectangle(penRbBorder, rectCheckBox); // Border of the box
        //        }
        //        // Draw the text
        //        //if (UIAppearance.Language_ar)
        //        //{
        //        //    int x = rectCheckBox.X - TextRenderer.MeasureText(this.Text, this.Font).Width;
        //        //    graphics.DrawString(this.Text, this.Font, brushText,
        //        //        cbSize + 8, (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2); // Y = Centered
        //        //}
        //        //else
        //        //{
        //        //    graphics.DrawString(this.Text, this.Font, brushText,
        //        //   cbSize + 8, (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2); // Y = Centered
        //        //}
        //        //if (this.RightToLeft == RightToLeft.Yes)
        //        //{

        //        //    //int x = rectCheckBox.X - TextRenderer.MeasureText(this.Text, this.Font).Width;
        //        //    graphics.DrawString(this.Text, this.Font, brushText,
        //        //       (rectCheckBox.X) , (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2); // Y = Centered
        //        //}
        //        //else
        //        {
        //            graphics.DrawString(this.Text, this.Font, brushText,
        //           cbSize + 8, (this.Height - TextRenderer.MeasureText(this.Text, this.Font).Height) / 2); // Y = Centered
        //        }

        //    }
        //}





        #endregion

    }
}
