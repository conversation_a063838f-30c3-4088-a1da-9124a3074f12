﻿namespace SmartCreator.Forms.UserManager.Maintenance
{
    partial class Form_Backup_Files_UserManager
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle1 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle2 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle3 = new System.Windows.Forms.DataGridViewCellStyle();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.btn_OpenFolderDefault = new SmartCreator.RJControls.RJButton();
            this.btn_OpenLastFile = new SmartCreator.RJControls.RJButton();
            this.rjRadioButton3 = new SmartCreator.RJControls.RJRadioButton();
            this.btn_All_cards = new SmartCreator.RJControls.RJButton();
            this.rjButton2 = new SmartCreator.RJControls.RJButton();
            this.rjButton1 = new SmartCreator.RJControls.RJButton();
            this.btn_Add_To_Mikrotik = new SmartCreator.RJControls.RJButton();
            this.rjRadioButton2 = new SmartCreator.RJControls.RJRadioButton();
            this.rjRadioButton1 = new SmartCreator.RJControls.RJRadioButton();
            this.rjComboBox1 = new SmartCreator.RJControls.RJComboBox();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.rjPanel2.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Controls.Add(this.dgv);
            this.pnlClientArea.Controls.Add(this.rjButton2);
            this.pnlClientArea.Controls.Add(this.rjButton1);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 522);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(201, 17);
            this.lblCaption.Text = "Form_Backup_Files_UserManager";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.None;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.dgv.ColumnHeaderHeight = 40;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle1.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle1.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            dataGridViewCellStyle1.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle1.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle1;
            this.dgv.ColumnHeadersHeight = 40;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.Gainsboro;
            this.dgv.Location = new System.Drawing.Point(18, 118);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle2.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle2.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle2.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle2.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle2.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle2.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle2.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle2;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 35;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle3.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle3.Font = new System.Drawing.Font("Segoe UI", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle3.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle3.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle3.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle3;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 35;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(957, 337);
            this.dgv.TabIndex = 0;
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 13;
            this.rjPanel2.Controls.Add(this.rjLabel1);
            this.rjPanel2.Controls.Add(this.rjComboBox1);
            this.rjPanel2.Controls.Add(this.btn_Refresh);
            this.rjPanel2.Controls.Add(this.btn_OpenFolderDefault);
            this.rjPanel2.Controls.Add(this.btn_OpenLastFile);
            this.rjPanel2.Controls.Add(this.rjRadioButton3);
            this.rjPanel2.Controls.Add(this.btn_All_cards);
            this.rjPanel2.Controls.Add(this.btn_Add_To_Mikrotik);
            this.rjPanel2.Controls.Add(this.rjRadioButton2);
            this.rjPanel2.Controls.Add(this.rjRadioButton1);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(18, 11);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(957, 98);
            this.rjPanel2.TabIndex = 1;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Cairo Medium", 10F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 25;
            this.btn_Refresh.Location = new System.Drawing.Point(291, 5);
            this.btn_Refresh.Margin = new System.Windows.Forms.Padding(0);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 3, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(31, 32);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 75;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // btn_OpenFolderDefault
            // 
            this.btn_OpenFolderDefault.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_OpenFolderDefault.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.BorderRadius = 5;
            this.btn_OpenFolderDefault.BorderSize = 1;
            this.btn_OpenFolderDefault.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_OpenFolderDefault.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_OpenFolderDefault.FlatAppearance.BorderSize = 0;
            this.btn_OpenFolderDefault.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_OpenFolderDefault.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_OpenFolderDefault.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_OpenFolderDefault.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_OpenFolderDefault.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.IconChar = FontAwesome.Sharp.IconChar.FolderOpen;
            this.btn_OpenFolderDefault.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenFolderDefault.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_OpenFolderDefault.IconSize = 20;
            this.btn_OpenFolderDefault.Location = new System.Drawing.Point(220, 5);
            this.btn_OpenFolderDefault.Name = "btn_OpenFolderDefault";
            this.btn_OpenFolderDefault.Size = new System.Drawing.Size(31, 32);
            this.btn_OpenFolderDefault.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_OpenFolderDefault.TabIndex = 73;
            this.btn_OpenFolderDefault.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_OpenFolderDefault.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_OpenFolderDefault.UseVisualStyleBackColor = false;
            // 
            // btn_OpenLastFile
            // 
            this.btn_OpenLastFile.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_OpenLastFile.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.BorderRadius = 5;
            this.btn_OpenLastFile.BorderSize = 1;
            this.btn_OpenLastFile.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_OpenLastFile.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_OpenLastFile.FlatAppearance.BorderSize = 0;
            this.btn_OpenLastFile.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_OpenLastFile.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_OpenLastFile.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_OpenLastFile.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_OpenLastFile.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconChar = FontAwesome.Sharp.IconChar.File;
            this.btn_OpenLastFile.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_OpenLastFile.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_OpenLastFile.IconSize = 18;
            this.btn_OpenLastFile.Location = new System.Drawing.Point(257, 5);
            this.btn_OpenLastFile.Name = "btn_OpenLastFile";
            this.btn_OpenLastFile.Size = new System.Drawing.Size(31, 32);
            this.btn_OpenLastFile.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_OpenLastFile.TabIndex = 74;
            this.btn_OpenLastFile.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_OpenLastFile.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_OpenLastFile.UseVisualStyleBackColor = false;
            // 
            // rjRadioButton3
            // 
            this.rjRadioButton3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjRadioButton3.AutoSize = true;
            this.rjRadioButton3.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjRadioButton3.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjRadioButton3.Customizable = false;
            this.rjRadioButton3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjRadioButton3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjRadioButton3.Location = new System.Drawing.Point(332, 12);
            this.rjRadioButton3.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjRadioButton3.Name = "rjRadioButton3";
            this.rjRadioButton3.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.rjRadioButton3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjRadioButton3.Size = new System.Drawing.Size(224, 21);
            this.rjRadioButton3.TabIndex = 0;
            this.rjRadioButton3.Text = "عرض نسخ الاعدادت فقط- باكب النظام";
            this.rjRadioButton3.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.rjRadioButton3.UseVisualStyleBackColor = true;
            // 
            // btn_All_cards
            // 
            this.btn_All_cards.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_All_cards.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_All_cards.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_All_cards.BorderRadius = 8;
            this.btn_All_cards.BorderSize = 1;
            this.btn_All_cards.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_All_cards.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_All_cards.FlatAppearance.BorderSize = 0;
            this.btn_All_cards.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_All_cards.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_All_cards.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_All_cards.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_All_cards.ForeColor = System.Drawing.Color.White;
            this.btn_All_cards.IconChar = FontAwesome.Sharp.IconChar.Archive;
            this.btn_All_cards.IconColor = System.Drawing.Color.White;
            this.btn_All_cards.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_All_cards.IconSize = 24;
            this.btn_All_cards.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_All_cards.Location = new System.Drawing.Point(749, 48);
            this.btn_All_cards.Name = "btn_All_cards";
            this.btn_All_cards.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_All_cards.Size = new System.Drawing.Size(195, 34);
            this.btn_All_cards.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_All_cards.TabIndex = 71;
            this.btn_All_cards.Text = "نسخه احتياطية لليوزمنجر";
            this.btn_All_cards.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_All_cards.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_All_cards.UseVisualStyleBackColor = false;
            // 
            // rjButton2
            // 
            this.rjButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjButton2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton2.BorderRadius = 8;
            this.rjButton2.BorderSize = 1;
            this.rjButton2.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton2.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.rjButton2.FlatAppearance.BorderSize = 0;
            this.rjButton2.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton2.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton2.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton2.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.rjButton2.ForeColor = System.Drawing.Color.White;
            this.rjButton2.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.rjButton2.IconColor = System.Drawing.Color.White;
            this.rjButton2.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton2.IconSize = 24;
            this.rjButton2.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton2.Location = new System.Drawing.Point(550, 469);
            this.rjButton2.Name = "rjButton2";
            this.rjButton2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton2.Size = new System.Drawing.Size(203, 34);
            this.rjButton2.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton2.TabIndex = 72;
            this.rjButton2.Text = "استعادة نسخه باكب للروتر";
            this.rjButton2.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton2.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton2.UseVisualStyleBackColor = false;
            // 
            // rjButton1
            // 
            this.rjButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjButton1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjButton1.BorderRadius = 8;
            this.rjButton1.BorderSize = 1;
            this.rjButton1.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.rjButton1.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.rjButton1.FlatAppearance.BorderSize = 0;
            this.rjButton1.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.rjButton1.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.rjButton1.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.rjButton1.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.rjButton1.ForeColor = System.Drawing.Color.White;
            this.rjButton1.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.rjButton1.IconColor = System.Drawing.Color.White;
            this.rjButton1.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.rjButton1.IconSize = 24;
            this.rjButton1.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton1.Location = new System.Drawing.Point(761, 469);
            this.rjButton1.Name = "rjButton1";
            this.rjButton1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjButton1.Size = new System.Drawing.Size(211, 34);
            this.rjButton1.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.rjButton1.TabIndex = 72;
            this.rjButton1.Text = "عمل نسخه احتياطيه للروتر";
            this.rjButton1.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.rjButton1.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.rjButton1.UseVisualStyleBackColor = false;
            // 
            // btn_Add_To_Mikrotik
            // 
            this.btn_Add_To_Mikrotik.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Add_To_Mikrotik.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_To_Mikrotik.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Add_To_Mikrotik.BorderRadius = 8;
            this.btn_Add_To_Mikrotik.BorderSize = 1;
            this.btn_Add_To_Mikrotik.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Add_To_Mikrotik.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(55)))), ((int)(((byte)(159)))), ((int)(((byte)(113)))));
            this.btn_Add_To_Mikrotik.FlatAppearance.BorderSize = 0;
            this.btn_Add_To_Mikrotik.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_Add_To_Mikrotik.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_Add_To_Mikrotik.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Add_To_Mikrotik.Font = new System.Drawing.Font("Droid Arabic Kufi", 9F, System.Drawing.FontStyle.Bold);
            this.btn_Add_To_Mikrotik.ForeColor = System.Drawing.Color.White;
            this.btn_Add_To_Mikrotik.IconChar = FontAwesome.Sharp.IconChar.Upload;
            this.btn_Add_To_Mikrotik.IconColor = System.Drawing.Color.White;
            this.btn_Add_To_Mikrotik.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Add_To_Mikrotik.IconSize = 24;
            this.btn_Add_To_Mikrotik.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Add_To_Mikrotik.Location = new System.Drawing.Point(503, 48);
            this.btn_Add_To_Mikrotik.Name = "btn_Add_To_Mikrotik";
            this.btn_Add_To_Mikrotik.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Add_To_Mikrotik.Size = new System.Drawing.Size(242, 34);
            this.btn_Add_To_Mikrotik.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_Add_To_Mikrotik.TabIndex = 72;
            this.btn_Add_To_Mikrotik.Text = "استعادة نسخه احتياطية لليوزمنجر";
            this.btn_Add_To_Mikrotik.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Add_To_Mikrotik.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Add_To_Mikrotik.UseVisualStyleBackColor = false;
            // 
            // rjRadioButton2
            // 
            this.rjRadioButton2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjRadioButton2.AutoSize = true;
            this.rjRadioButton2.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjRadioButton2.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjRadioButton2.Customizable = false;
            this.rjRadioButton2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjRadioButton2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjRadioButton2.Location = new System.Drawing.Point(565, 12);
            this.rjRadioButton2.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjRadioButton2.Name = "rjRadioButton2";
            this.rjRadioButton2.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.rjRadioButton2.Size = new System.Drawing.Size(158, 21);
            this.rjRadioButton2.TabIndex = 0;
            this.rjRadioButton2.Text = "عرض نسخ اليوزمنجر فقط";
            this.rjRadioButton2.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.rjRadioButton2.UseVisualStyleBackColor = true;
            // 
            // rjRadioButton1
            // 
            this.rjRadioButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjRadioButton1.AutoSize = true;
            this.rjRadioButton1.Checked = true;
            this.rjRadioButton1.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjRadioButton1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.rjRadioButton1.Customizable = false;
            this.rjRadioButton1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjRadioButton1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjRadioButton1.Location = new System.Drawing.Point(748, 12);
            this.rjRadioButton1.MinimumSize = new System.Drawing.Size(0, 21);
            this.rjRadioButton1.Name = "rjRadioButton1";
            this.rjRadioButton1.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.rjRadioButton1.Size = new System.Drawing.Size(196, 21);
            this.rjRadioButton1.TabIndex = 0;
            this.rjRadioButton1.TabStop = true;
            this.rjRadioButton1.Text = "عرض جميع ملفات النسخ الاحتياط";
            this.rjRadioButton1.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.rjRadioButton1.UseVisualStyleBackColor = true;
            // 
            // rjComboBox1
            // 
            this.rjComboBox1.AutoCompleteMode = System.Windows.Forms.AutoCompleteMode.None;
            this.rjComboBox1.AutoCompleteSource = System.Windows.Forms.AutoCompleteSource.None;
            this.rjComboBox1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.rjComboBox1.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjComboBox1.BorderRadius = 0;
            this.rjComboBox1.BorderSize = 1;
            this.rjComboBox1.Customizable = false;
            this.rjComboBox1.DataSource = null;
            this.rjComboBox1.DropDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjComboBox1.DropDownStyle = System.Windows.Forms.ComboBoxStyle.DropDown;
            this.rjComboBox1.DropDownTextColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjComboBox1.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjComboBox1.Location = new System.Drawing.Point(10, 42);
            this.rjComboBox1.Name = "rjComboBox1";
            this.rjComboBox1.Padding = new System.Windows.Forms.Padding(2);
            this.rjComboBox1.SelectedIndex = -1;
            this.rjComboBox1.Size = new System.Drawing.Size(171, 32);
            this.rjComboBox1.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.rjComboBox1.TabIndex = 76;
            this.rjComboBox1.Texts = "";
            // 
            // rjLabel1
            // 
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel1.ForeColor = System.Drawing.Color.Black;
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(12, 22);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(167, 17);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel1.TabIndex = 77;
            this.rjLabel1.Text = "تخزين خارجي -هارد-ذاكره-فلاش";
            // 
            // Form_Backup_Files_UserManager
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "Form_Backup_Files_UserManager";
            this.ClientSize = new System.Drawing.Size(1000, 572);
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_Backup_Files_UserManager";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.Text = "Form_Backup_Files_UserManager";
            this.pnlClientArea.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.rjPanel2.ResumeLayout(false);
            this.rjPanel2.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion

        private RJControls.RJDataGridView dgv;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJRadioButton rjRadioButton1;
        private RJControls.RJRadioButton rjRadioButton3;
        private RJControls.RJRadioButton rjRadioButton2;
        private RJControls.RJButton btn_All_cards;
        private RJControls.RJButton btn_Add_To_Mikrotik;
        private RJControls.RJButton rjButton2;
        private RJControls.RJButton rjButton1;
        private RJControls.RJButton btn_OpenFolderDefault;
        private RJControls.RJButton btn_OpenLastFile;
        private RJControls.RJButton btn_Refresh;
        private RJControls.RJComboBox rjComboBox1;
        private RJControls.RJLabel rjLabel1;
    }
}