﻿namespace SmartCreator.Forms.Hotspot
{
    partial class Form_Smart_Validatiy_Hotspot
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle7 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle8 = new System.Windows.Forms.DataGridViewCellStyle();
            System.Windows.Forms.DataGridViewCellStyle dataGridViewCellStyle9 = new System.Windows.Forms.DataGridViewCellStyle();
            this.rjPanel1 = new SmartCreator.RJControls.RJPanel();
            this.btn_Refresh = new SmartCreator.RJControls.RJButton();
            this.lbl_Title1 = new SmartCreator.RJControls.RJLabel();
            this.lbl_descr = new SmartCreator.RJControls.RJLabel();
            this.pnl_usermanger = new SmartCreator.RJControls.RJPanel();
            this.rjLabel10 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel9 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel3 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel5 = new SmartCreator.RJControls.RJLabel();
            this.lbl_descr2 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel2 = new SmartCreator.RJControls.RJLabel();
            this.rjLabel6 = new SmartCreator.RJControls.RJLabel();
            this.lbl_Save_session = new SmartCreator.RJControls.RJLabel();
            this.Radio_RemoeLastSetting = new SmartCreator.RJControls.RJRadioButton();
            this.Radio_Add_SideLastSetting = new SmartCreator.RJControls.RJRadioButton();
            this.rjLabel4 = new SmartCreator.RJControls.RJLabel();
            this.lbl_DayOrHour = new SmartCreator.RJControls.RJLabel();
            this.rjPanel2 = new SmartCreator.RJControls.RJPanel();
            this.panel1 = new System.Windows.Forms.Panel();
            this.btn_AddScript_toMickrotik = new SmartCreator.RJControls.RJButton();
            this.flowLayoutPanel2 = new System.Windows.Forms.FlowLayoutPanel();
            this.flowLayoutPanel1 = new System.Windows.Forms.FlowLayoutPanel();
            this.rjLabel1 = new SmartCreator.RJControls.RJLabel();
            this.dgv = new SmartCreator.RJControls.RJDataGridView();
            this.rjPanel3 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel7 = new SmartCreator.RJControls.RJLabel();
            this.btn_Upgrade = new SmartCreator.RJControls.RJButton();
            this.timer1 = new System.Windows.Forms.Timer(this.components);
            this.rjPanel4 = new SmartCreator.RJControls.RJPanel();
            this.rjLabel8 = new SmartCreator.RJControls.RJLabel();
            this.lbl_note = new SmartCreator.RJControls.RJLabel();
            this.pnlClientArea.SuspendLayout();
            this.rjPanel1.SuspendLayout();
            this.pnl_usermanger.SuspendLayout();
            this.rjPanel2.SuspendLayout();
            this.panel1.SuspendLayout();
            this.flowLayoutPanel2.SuspendLayout();
            this.flowLayoutPanel1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).BeginInit();
            this.rjPanel3.SuspendLayout();
            this.rjPanel4.SuspendLayout();
            this.SuspendLayout();
            // 
            // pnlClientArea
            // 
            this.pnlClientArea.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(240)))), ((int)(((byte)(245)))), ((int)(((byte)(249)))));
            this.pnlClientArea.Controls.Add(this.rjPanel4);
            this.pnlClientArea.Controls.Add(this.rjPanel3);
            this.pnlClientArea.Controls.Add(this.rjPanel1);
            this.pnlClientArea.Controls.Add(this.pnl_usermanger);
            this.pnlClientArea.Controls.Add(this.rjPanel2);
            this.pnlClientArea.Location = new System.Drawing.Point(5, 45);
            this.pnlClientArea.Size = new System.Drawing.Size(990, 588);
            this.pnlClientArea.SizeChanged += new System.EventHandler(this.pnlClientArea_SizeChanged);
            // 
            // lblCaption
            // 
            this.lblCaption.Size = new System.Drawing.Size(111, 17);
            this.lblCaption.Text = "صلاحيات الهوتسبوت";
            // 
            // rjPanel1
            // 
            this.rjPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel1.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel1.BorderRadius = 13;
            this.rjPanel1.Controls.Add(this.btn_Refresh);
            this.rjPanel1.Controls.Add(this.lbl_Title1);
            this.rjPanel1.Controls.Add(this.lbl_descr);
            this.rjPanel1.Customizable = false;
            this.rjPanel1.Location = new System.Drawing.Point(6, 7);
            this.rjPanel1.Name = "rjPanel1";
            this.rjPanel1.Size = new System.Drawing.Size(973, 96);
            this.rjPanel1.TabIndex = 143;
            // 
            // btn_Refresh
            // 
            this.btn_Refresh.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Refresh.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.BorderRadius = 4;
            this.btn_Refresh.BorderSize = 1;
            this.btn_Refresh.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Refresh.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Refresh.FlatAppearance.BorderSize = 0;
            this.btn_Refresh.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Refresh.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Refresh.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Refresh.Font = new System.Drawing.Font("Microsoft Sans Serif", 9.5F);
            this.btn_Refresh.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconChar = FontAwesome.Sharp.IconChar.Redo;
            this.btn_Refresh.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Refresh.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Refresh.IconSize = 24;
            this.btn_Refresh.Location = new System.Drawing.Point(8, 61);
            this.btn_Refresh.Name = "btn_Refresh";
            this.btn_Refresh.Padding = new System.Windows.Forms.Padding(0, 2, 0, 0);
            this.btn_Refresh.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_Refresh.Size = new System.Drawing.Size(40, 25);
            this.btn_Refresh.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Refresh.TabIndex = 150;
            this.btn_Refresh.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_Refresh.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Refresh.UseVisualStyleBackColor = false;
            this.btn_Refresh.Click += new System.EventHandler(this.btn_Refresh_Click);
            // 
            // lbl_Title1
            // 
            this.lbl_Title1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_Title1.AutoSize = true;
            this.lbl_Title1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Title1.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_Title1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lbl_Title1.LinkLabel = false;
            this.lbl_Title1.Location = new System.Drawing.Point(399, 6);
            this.lbl_Title1.Name = "lbl_Title1";
            this.lbl_Title1.Size = new System.Drawing.Size(218, 31);
            this.lbl_Title1.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lbl_Title1.TabIndex = 33;
            this.lbl_Title1.Text = "ادارة صلاحيات الهوتسبوت";
            // 
            // lbl_descr
            // 
            this.lbl_descr.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_descr.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_descr.FlatStyle = System.Windows.Forms.FlatStyle.System;
            this.lbl_descr.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_descr.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_descr.LinkLabel = false;
            this.lbl_descr.Location = new System.Drawing.Point(66, 40);
            this.lbl_descr.Name = "lbl_descr";
            this.lbl_descr.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_descr.Size = new System.Drawing.Size(887, 43);
            this.lbl_descr.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_descr.TabIndex = 16;
            this.lbl_descr.Text = "تكامل صلاحيات الهوتسبوت الخاصه بسمارت مع نظام المايكروتك عن طريق اضافة سكربت احتر" +
    "افية وخفيفه جدا علي اداء الروتر وتستطيع تخصيص وظائف السكربت عند الطباعه او من بر" +
    "وفايلات سمارت الخاصه بالهتوسبوت ";
            this.lbl_descr.TextAlign = System.Drawing.ContentAlignment.MiddleCenter;
            // 
            // pnl_usermanger
            // 
            this.pnl_usermanger.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.pnl_usermanger.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.pnl_usermanger.BorderRadius = 13;
            this.pnl_usermanger.Controls.Add(this.rjLabel10);
            this.pnl_usermanger.Controls.Add(this.rjLabel9);
            this.pnl_usermanger.Controls.Add(this.rjLabel3);
            this.pnl_usermanger.Controls.Add(this.rjLabel5);
            this.pnl_usermanger.Controls.Add(this.lbl_descr2);
            this.pnl_usermanger.Controls.Add(this.rjLabel2);
            this.pnl_usermanger.Controls.Add(this.rjLabel6);
            this.pnl_usermanger.Controls.Add(this.lbl_Save_session);
            this.pnl_usermanger.Customizable = false;
            this.pnl_usermanger.Location = new System.Drawing.Point(524, 188);
            this.pnl_usermanger.Name = "pnl_usermanger";
            this.pnl_usermanger.Size = new System.Drawing.Size(455, 247);
            this.pnl_usermanger.TabIndex = 24;
            // 
            // rjLabel10
            // 
            this.rjLabel10.AutoSize = true;
            this.rjLabel10.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel10.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel10.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel10.LinkLabel = false;
            this.rjLabel10.Location = new System.Drawing.Point(186, 103);
            this.rjLabel10.Name = "rjLabel10";
            this.rjLabel10.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel10.Size = new System.Drawing.Size(253, 22);
            this.rjLabel10.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel10.TabIndex = 146;
            this.rjLabel10.Text = "*  تعبئة رصيد لكروت الهوسبوت وعرض تقرير عنها";
            this.rjLabel10.UseCompatibleTextRendering = true;
            // 
            // rjLabel9
            // 
            this.rjLabel9.AutoSize = true;
            this.rjLabel9.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel9.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel9.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel9.LinkLabel = false;
            this.rjLabel9.Location = new System.Drawing.Point(187, 75);
            this.rjLabel9.Name = "rjLabel9";
            this.rjLabel9.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel9.Size = new System.Drawing.Size(252, 22);
            this.rjLabel9.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel9.TabIndex = 146;
            this.rjLabel9.Text = "*  عمل باقات محليه للهوتسبوت مشابه لليوزمنجر";
            this.rjLabel9.UseCompatibleTextRendering = true;
            // 
            // rjLabel3
            // 
            this.rjLabel3.AutoSize = true;
            this.rjLabel3.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel3.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel3.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel3.LinkLabel = false;
            this.rjLabel3.Location = new System.Drawing.Point(180, 47);
            this.rjLabel3.Name = "rjLabel3";
            this.rjLabel3.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel3.Size = new System.Drawing.Size(259, 22);
            this.rjLabel3.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel3.TabIndex = 146;
            this.rjLabel3.Text = "*   عمل صلاحيات الايام لكروت الهوتسبوت وادارتها";
            this.rjLabel3.UseCompatibleTextRendering = true;
            // 
            // rjLabel5
            // 
            this.rjLabel5.AutoSize = true;
            this.rjLabel5.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel5.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel5.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel5.LinkLabel = false;
            this.rjLabel5.Location = new System.Drawing.Point(142, 161);
            this.rjLabel5.Name = "rjLabel5";
            this.rjLabel5.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel5.Size = new System.Drawing.Size(297, 22);
            this.rjLabel5.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel5.TabIndex = 145;
            this.rjLabel5.Text = "*   ربط الكرت بعنوان ماك العميل لاول جهاز يستخدم الكرت";
            this.rjLabel5.UseCompatibleTextRendering = true;
            // 
            // lbl_descr2
            // 
            this.lbl_descr2.AutoSize = true;
            this.lbl_descr2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_descr2.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.lbl_descr2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.lbl_descr2.LinkLabel = false;
            this.lbl_descr2.Location = new System.Drawing.Point(90, 6);
            this.lbl_descr2.Name = "lbl_descr2";
            this.lbl_descr2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_descr2.Size = new System.Drawing.Size(282, 31);
            this.lbl_descr2.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.lbl_descr2.TabIndex = 33;
            this.lbl_descr2.Text = "  من اهم وظائف نظام الصلاحيات : ";
            // 
            // rjLabel2
            // 
            this.rjLabel2.AutoSize = true;
            this.rjLabel2.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel2.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel2.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel2.LinkLabel = false;
            this.rjLabel2.Location = new System.Drawing.Point(3, 220);
            this.rjLabel2.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel2.Name = "rjLabel2";
            this.rjLabel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel2.Size = new System.Drawing.Size(436, 22);
            this.rjLabel2.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel2.TabIndex = 139;
            this.rjLabel2.Text = "*   حفظ جلسات لكروت الهوتسبوت ومتابعه تفاصيل الكرت والاجهزه التي اشتغل منها";
            this.rjLabel2.UseCompatibleTextRendering = true;
            // 
            // rjLabel6
            // 
            this.rjLabel6.AutoSize = true;
            this.rjLabel6.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel6.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel6.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel6.LinkLabel = false;
            this.rjLabel6.Location = new System.Drawing.Point(124, 190);
            this.rjLabel6.Margin = new System.Windows.Forms.Padding(0);
            this.rjLabel6.Name = "rjLabel6";
            this.rjLabel6.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel6.Size = new System.Drawing.Size(315, 22);
            this.rjLabel6.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel6.TabIndex = 139;
            this.rjLabel6.Text = "*   حفظ الوقت والاستهلاك في حال الانطفاء المفاجئ للروتر ";
            this.rjLabel6.UseCompatibleTextRendering = true;
            // 
            // lbl_Save_session
            // 
            this.lbl_Save_session.AutoSize = true;
            this.lbl_Save_session.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_Save_session.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_Save_session.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_Save_session.LinkLabel = false;
            this.lbl_Save_session.Location = new System.Drawing.Point(149, 131);
            this.lbl_Save_session.Margin = new System.Windows.Forms.Padding(0);
            this.lbl_Save_session.Name = "lbl_Save_session";
            this.lbl_Save_session.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_Save_session.Size = new System.Drawing.Size(290, 22);
            this.lbl_Save_session.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_Save_session.TabIndex = 140;
            this.lbl_Save_session.Text = "*   تسجيل وحفظ مبيعات الكروت وعمل تقرير مفصل عنها";
            this.lbl_Save_session.UseCompatibleTextRendering = true;
            // 
            // Radio_RemoeLastSetting
            // 
            this.Radio_RemoeLastSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Radio_RemoeLastSetting.AutoSize = true;
            this.Radio_RemoeLastSetting.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_RemoeLastSetting.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_RemoeLastSetting.Customizable = false;
            this.Radio_RemoeLastSetting.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Radio_RemoeLastSetting.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_RemoeLastSetting.Location = new System.Drawing.Point(449, 3);
            this.Radio_RemoeLastSetting.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_RemoeLastSetting.Name = "Radio_RemoeLastSetting";
            this.Radio_RemoeLastSetting.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_RemoeLastSetting.Size = new System.Drawing.Size(24, 21);
            this.Radio_RemoeLastSetting.TabIndex = 138;
            this.Radio_RemoeLastSetting.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_RemoeLastSetting.UseVisualStyleBackColor = true;
            this.Radio_RemoeLastSetting.CheckedChanged += new System.EventHandler(this.radio_RemoeLastSetting_CheckedChanged);
            // 
            // Radio_Add_SideLastSetting
            // 
            this.Radio_Add_SideLastSetting.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.Radio_Add_SideLastSetting.AutoSize = true;
            this.Radio_Add_SideLastSetting.Checked = true;
            this.Radio_Add_SideLastSetting.CheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.Radio_Add_SideLastSetting.Cursor = System.Windows.Forms.Cursors.Hand;
            this.Radio_Add_SideLastSetting.Customizable = false;
            this.Radio_Add_SideLastSetting.Font = new System.Drawing.Font("Verdana", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.Radio_Add_SideLastSetting.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.Radio_Add_SideLastSetting.Location = new System.Drawing.Point(449, 3);
            this.Radio_Add_SideLastSetting.MinimumSize = new System.Drawing.Size(0, 21);
            this.Radio_Add_SideLastSetting.Name = "Radio_Add_SideLastSetting";
            this.Radio_Add_SideLastSetting.Padding = new System.Windows.Forms.Padding(10, 0, 0, 0);
            this.Radio_Add_SideLastSetting.Size = new System.Drawing.Size(24, 21);
            this.Radio_Add_SideLastSetting.TabIndex = 138;
            this.Radio_Add_SideLastSetting.TabStop = true;
            this.Radio_Add_SideLastSetting.UnCheckedColor = System.Drawing.Color.FromArgb(((int)(((byte)(104)))), ((int)(((byte)(110)))), ((int)(((byte)(134)))));
            this.Radio_Add_SideLastSetting.UseVisualStyleBackColor = true;
            this.Radio_Add_SideLastSetting.CheckedChanged += new System.EventHandler(this.radio_Add_SideLastSetting_CheckedChanged);
            // 
            // rjLabel4
            // 
            this.rjLabel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel4.AutoSize = true;
            this.rjLabel4.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel4.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel4.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel4.LinkLabel = false;
            this.rjLabel4.Location = new System.Drawing.Point(80, 5);
            this.rjLabel4.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.rjLabel4.Name = "rjLabel4";
            this.rjLabel4.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel4.Size = new System.Drawing.Size(363, 22);
            this.rjLabel4.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel4.TabIndex = 136;
            this.rjLabel4.Text = "اضافة بجانب الاعدادت الاخرى الموجوده في بروفايل دخول وخروج الكرت";
            this.rjLabel4.UseCompatibleTextRendering = true;
            // 
            // lbl_DayOrHour
            // 
            this.lbl_DayOrHour.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lbl_DayOrHour.AutoSize = true;
            this.lbl_DayOrHour.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_DayOrHour.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.lbl_DayOrHour.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.lbl_DayOrHour.LinkLabel = false;
            this.lbl_DayOrHour.Location = new System.Drawing.Point(29, 5);
            this.lbl_DayOrHour.Margin = new System.Windows.Forms.Padding(3, 5, 3, 0);
            this.lbl_DayOrHour.Name = "lbl_DayOrHour";
            this.lbl_DayOrHour.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_DayOrHour.Size = new System.Drawing.Size(414, 22);
            this.lbl_DayOrHour.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.lbl_DayOrHour.TabIndex = 130;
            this.lbl_DayOrHour.Text = "حذف الاعدادت الاخرى في بروفايل دخول وخروج الكرت واستبداله بنظام الصلاحيات";
            this.lbl_DayOrHour.UseCompatibleTextRendering = true;
            // 
            // rjPanel2
            // 
            this.rjPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel2.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel2.BorderRadius = 13;
            this.rjPanel2.Controls.Add(this.panel1);
            this.rjPanel2.Controls.Add(this.dgv);
            this.rjPanel2.Customizable = false;
            this.rjPanel2.Location = new System.Drawing.Point(6, 109);
            this.rjPanel2.Name = "rjPanel2";
            this.rjPanel2.Size = new System.Drawing.Size(509, 469);
            this.rjPanel2.TabIndex = 34;
            // 
            // panel1
            // 
            this.panel1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.panel1.Controls.Add(this.btn_AddScript_toMickrotik);
            this.panel1.Controls.Add(this.flowLayoutPanel2);
            this.panel1.Controls.Add(this.flowLayoutPanel1);
            this.panel1.Controls.Add(this.rjLabel1);
            this.panel1.Location = new System.Drawing.Point(8, 5);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(494, 171);
            this.panel1.TabIndex = 150;
            // 
            // btn_AddScript_toMickrotik
            // 
            this.btn_AddScript_toMickrotik.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_AddScript_toMickrotik.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_AddScript_toMickrotik.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_AddScript_toMickrotik.BorderRadius = 5;
            this.btn_AddScript_toMickrotik.BorderSize = 1;
            this.btn_AddScript_toMickrotik.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_AddScript_toMickrotik.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_AddScript_toMickrotik.FlatAppearance.BorderSize = 0;
            this.btn_AddScript_toMickrotik.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(115)))), ((int)(((byte)(97)))), ((int)(((byte)(223)))));
            this.btn_AddScript_toMickrotik.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(108)))), ((int)(((byte)(91)))), ((int)(((byte)(209)))));
            this.btn_AddScript_toMickrotik.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_AddScript_toMickrotik.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_AddScript_toMickrotik.ForeColor = System.Drawing.Color.White;
            this.btn_AddScript_toMickrotik.IconChar = FontAwesome.Sharp.IconChar.None;
            this.btn_AddScript_toMickrotik.IconColor = System.Drawing.Color.White;
            this.btn_AddScript_toMickrotik.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_AddScript_toMickrotik.IconSize = 1;
            this.btn_AddScript_toMickrotik.ImageAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_AddScript_toMickrotik.Location = new System.Drawing.Point(58, 9);
            this.btn_AddScript_toMickrotik.Name = "btn_AddScript_toMickrotik";
            this.btn_AddScript_toMickrotik.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.btn_AddScript_toMickrotik.Size = new System.Drawing.Size(333, 40);
            this.btn_AddScript_toMickrotik.Style = SmartCreator.RJControls.ControlStyle.Solid;
            this.btn_AddScript_toMickrotik.TabIndex = 21;
            this.btn_AddScript_toMickrotik.Text = "تثبيت نظام الصلاحيات الي المايكروتك";
            this.btn_AddScript_toMickrotik.TextAlign = System.Drawing.ContentAlignment.MiddleRight;
            this.btn_AddScript_toMickrotik.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_AddScript_toMickrotik.UseVisualStyleBackColor = false;
            this.btn_AddScript_toMickrotik.Click += new System.EventHandler(this.btn_AddScript_toMickrotik_Click);
            // 
            // flowLayoutPanel2
            // 
            this.flowLayoutPanel2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.flowLayoutPanel2.Controls.Add(this.Radio_Add_SideLastSetting);
            this.flowLayoutPanel2.Controls.Add(this.rjLabel4);
            this.flowLayoutPanel2.Location = new System.Drawing.Point(13, 90);
            this.flowLayoutPanel2.Name = "flowLayoutPanel2";
            this.flowLayoutPanel2.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel2.Size = new System.Drawing.Size(476, 32);
            this.flowLayoutPanel2.TabIndex = 149;
            // 
            // flowLayoutPanel1
            // 
            this.flowLayoutPanel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.flowLayoutPanel1.Controls.Add(this.Radio_RemoeLastSetting);
            this.flowLayoutPanel1.Controls.Add(this.lbl_DayOrHour);
            this.flowLayoutPanel1.Location = new System.Drawing.Point(13, 128);
            this.flowLayoutPanel1.Name = "flowLayoutPanel1";
            this.flowLayoutPanel1.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.flowLayoutPanel1.Size = new System.Drawing.Size(476, 32);
            this.flowLayoutPanel1.TabIndex = 148;
            // 
            // rjLabel1
            // 
            this.rjLabel1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel1.AutoSize = true;
            this.rjLabel1.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel1.Font = new System.Drawing.Font("Droid Arabic Kufi", 12F, System.Drawing.FontStyle.Bold);
            this.rjLabel1.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.rjLabel1.LinkLabel = false;
            this.rjLabel1.Location = new System.Drawing.Point(153, 57);
            this.rjLabel1.Name = "rjLabel1";
            this.rjLabel1.Size = new System.Drawing.Size(166, 31);
            this.rjLabel1.Style = SmartCreator.RJControls.LabelStyle.Title;
            this.rjLabel1.TabIndex = 16;
            this.rjLabel1.Text = "تخصيص البروفايلات ";
            // 
            // dgv
            // 
            this.dgv.AllowUserToAddRows = false;
            this.dgv.AllowUserToDeleteRows = false;
            this.dgv.AllowUserToResizeRows = false;
            this.dgv.AlternatingRowsColor = System.Drawing.Color.Empty;
            this.dgv.AlternatingRowsColorApply = false;
            this.dgv.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.dgv.AutoSizeColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.BackgroundColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.dgv.BorderRadius = 13;
            this.dgv.BorderStyle = System.Windows.Forms.BorderStyle.Fixed3D;
            this.dgv.CellBorderStyle = System.Windows.Forms.DataGridViewCellBorderStyle.SingleHorizontal;
            this.dgv.ColumnHeaderColor = System.Drawing.Color.MediumPurple;
            this.dgv.ColumnHeaderFont = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.dgv.ColumnHeaderHeight = 30;
            this.dgv.ColumnHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.None;
            dataGridViewCellStyle7.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle7.BackColor = System.Drawing.Color.MediumPurple;
            dataGridViewCellStyle7.Font = new System.Drawing.Font("Microsoft Sans Serif", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle7.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle7.WrapMode = System.Windows.Forms.DataGridViewTriState.True;
            this.dgv.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle7;
            this.dgv.ColumnHeadersHeight = 30;
            this.dgv.ColumnHeadersHeightSizeMode = System.Windows.Forms.DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            this.dgv.ColumnHeaderTextColor = System.Drawing.Color.White;
            this.dgv.ColumnsMode = System.Windows.Forms.DataGridViewAutoSizeColumnsMode.Fill;
            this.dgv.Customizable = false;
            this.dgv.DgvBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(255)))), ((int)(((byte)(192)))), ((int)(((byte)(192)))));
            this.dgv.EnableHeadersVisualStyles = false;
            this.dgv.GridColor = System.Drawing.Color.Gainsboro;
            this.dgv.Location = new System.Drawing.Point(8, 182);
            this.dgv.Name = "dgv";
            this.dgv.ReadOnly = true;
            this.dgv.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.dgv.RowHeaderColor = System.Drawing.Color.WhiteSmoke;
            this.dgv.RowHeadersBorderStyle = System.Windows.Forms.DataGridViewHeaderBorderStyle.Single;
            dataGridViewCellStyle8.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle8.BackColor = System.Drawing.Color.WhiteSmoke;
            dataGridViewCellStyle8.Font = new System.Drawing.Font("Segoe UI", 9F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle8.ForeColor = System.Drawing.Color.White;
            dataGridViewCellStyle8.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle8.SelectionForeColor = System.Drawing.SystemColors.HighlightText;
            dataGridViewCellStyle8.WrapMode = System.Windows.Forms.DataGridViewTriState.False;
            this.dgv.RowHeadersDefaultCellStyle = dataGridViewCellStyle8;
            this.dgv.RowHeadersVisible = false;
            this.dgv.RowHeadersWidth = 30;
            this.dgv.RowHeadersWidthSizeMode = System.Windows.Forms.DataGridViewRowHeadersWidthSizeMode.DisableResizing;
            this.dgv.RowHeight = 40;
            this.dgv.RowsColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle9.Alignment = System.Windows.Forms.DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle9.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            dataGridViewCellStyle9.Font = new System.Drawing.Font("Tahoma", 10F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            dataGridViewCellStyle9.ForeColor = System.Drawing.Color.Gray;
            dataGridViewCellStyle9.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            dataGridViewCellStyle9.SelectionForeColor = System.Drawing.Color.Gray;
            this.dgv.RowsDefaultCellStyle = dataGridViewCellStyle9;
            this.dgv.RowsTextColor = System.Drawing.Color.Gray;
            this.dgv.RowTemplate.Height = 40;
            this.dgv.SelectionBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(213)))), ((int)(((byte)(199)))), ((int)(((byte)(241)))));
            this.dgv.SelectionMode = System.Windows.Forms.DataGridViewSelectionMode.FullRowSelect;
            this.dgv.SelectionTextColor = System.Drawing.Color.Gray;
            this.dgv.Size = new System.Drawing.Size(494, 274);
            this.dgv.TabIndex = 142;
            this.dgv.CellContentClick += new System.Windows.Forms.DataGridViewCellEventHandler(this.dgv_CellContentClick);
            // 
            // rjPanel3
            // 
            this.rjPanel3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel3.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel3.BorderRadius = 13;
            this.rjPanel3.Controls.Add(this.rjLabel7);
            this.rjPanel3.Controls.Add(this.btn_Upgrade);
            this.rjPanel3.Customizable = false;
            this.rjPanel3.Location = new System.Drawing.Point(524, 441);
            this.rjPanel3.Name = "rjPanel3";
            this.rjPanel3.Size = new System.Drawing.Size(455, 137);
            this.rjPanel3.TabIndex = 147;
            // 
            // rjLabel7
            // 
            this.rjLabel7.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjLabel7.AutoSize = true;
            this.rjLabel7.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel7.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel7.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel7.LinkLabel = false;
            this.rjLabel7.Location = new System.Drawing.Point(131, 33);
            this.rjLabel7.Name = "rjLabel7";
            this.rjLabel7.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel7.Size = new System.Drawing.Size(301, 39);
            this.rjLabel7.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel7.TabIndex = 147;
            this.rjLabel7.Text = "ترقية نظام الصلاحيات في اصدار سمارت 8.0.16 وما قبله\r\n الي الاصدار الجديد من نظام " +
    "الصلاحيات";
            this.rjLabel7.UseCompatibleTextRendering = true;
            // 
            // btn_Upgrade
            // 
            this.btn_Upgrade.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btn_Upgrade.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.btn_Upgrade.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Upgrade.BorderRadius = 5;
            this.btn_Upgrade.BorderSize = 1;
            this.btn_Upgrade.Design = SmartCreator.RJControls.ButtonDesign.IconButton;
            this.btn_Upgrade.FlatAppearance.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(97)))), ((int)(((byte)(212)))));
            this.btn_Upgrade.FlatAppearance.BorderSize = 0;
            this.btn_Upgrade.FlatAppearance.MouseDownBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(235)))), ((int)(((byte)(236)))), ((int)(((byte)(237)))));
            this.btn_Upgrade.FlatAppearance.MouseOverBackColor = System.Drawing.Color.FromArgb(((int)(((byte)(220)))), ((int)(((byte)(221)))), ((int)(((byte)(222)))));
            this.btn_Upgrade.FlatStyle = System.Windows.Forms.FlatStyle.Flat;
            this.btn_Upgrade.Font = new System.Drawing.Font("Droid Arabic Kufi", 9.75F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.btn_Upgrade.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Upgrade.IconChar = FontAwesome.Sharp.IconChar.UpDown;
            this.btn_Upgrade.IconColor = System.Drawing.Color.FromArgb(((int)(((byte)(123)))), ((int)(((byte)(104)))), ((int)(((byte)(238)))));
            this.btn_Upgrade.IconFont = FontAwesome.Sharp.IconFont.Auto;
            this.btn_Upgrade.IconSize = 18;
            this.btn_Upgrade.Location = new System.Drawing.Point(26, 32);
            this.btn_Upgrade.Name = "btn_Upgrade";
            this.btn_Upgrade.Size = new System.Drawing.Size(96, 40);
            this.btn_Upgrade.Style = SmartCreator.RJControls.ControlStyle.Glass;
            this.btn_Upgrade.TabIndex = 21;
            this.btn_Upgrade.Text = "ترقية";
            this.btn_Upgrade.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.btn_Upgrade.TextImageRelation = System.Windows.Forms.TextImageRelation.ImageBeforeText;
            this.btn_Upgrade.UseVisualStyleBackColor = false;
            this.btn_Upgrade.Click += new System.EventHandler(this.btn_Upgrade_Click);
            // 
            // timer1
            // 
            this.timer1.Tick += new System.EventHandler(this.timer1_Tick);
            // 
            // rjPanel4
            // 
            this.rjPanel4.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.rjPanel4.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(250)))), ((int)(((byte)(252)))), ((int)(((byte)(253)))));
            this.rjPanel4.BorderRadius = 13;
            this.rjPanel4.Controls.Add(this.rjLabel8);
            this.rjPanel4.Controls.Add(this.lbl_note);
            this.rjPanel4.Customizable = false;
            this.rjPanel4.Location = new System.Drawing.Point(524, 109);
            this.rjPanel4.Name = "rjPanel4";
            this.rjPanel4.Size = new System.Drawing.Size(455, 73);
            this.rjPanel4.TabIndex = 148;
            // 
            // rjLabel8
            // 
            this.rjLabel8.AutoSize = true;
            this.rjLabel8.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.rjLabel8.Font = new System.Drawing.Font("Droid Sans Arabic", 9F);
            this.rjLabel8.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(132)))), ((int)(((byte)(129)))), ((int)(((byte)(132)))));
            this.rjLabel8.LinkLabel = false;
            this.rjLabel8.Location = new System.Drawing.Point(174, 7);
            this.rjLabel8.Name = "rjLabel8";
            this.rjLabel8.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.rjLabel8.Size = new System.Drawing.Size(111, 17);
            this.rjLabel8.Style = SmartCreator.RJControls.LabelStyle.Normal;
            this.rjLabel8.TabIndex = 49;
            this.rjLabel8.Text = "حالة نظام الصلاحيات";
            // 
            // lbl_note
            // 
            this.lbl_note.AutoSize = true;
            this.lbl_note.Cursor = System.Windows.Forms.Cursors.Arrow;
            this.lbl_note.Font = new System.Drawing.Font("Droid Sans Arabic", 12F);
            this.lbl_note.ForeColor = System.Drawing.Color.FromArgb(((int)(((byte)(105)))), ((int)(((byte)(103)))), ((int)(((byte)(105)))));
            this.lbl_note.LinkLabel = false;
            this.lbl_note.Location = new System.Drawing.Point(80, 39);
            this.lbl_note.Name = "lbl_note";
            this.lbl_note.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.lbl_note.Size = new System.Drawing.Size(274, 22);
            this.lbl_note.Style = SmartCreator.RJControls.LabelStyle.Subtitle;
            this.lbl_note.TabIndex = 48;
            this.lbl_note.Text = "لا يعمل - لم يتم تثبيت اضافات الصلاحيات";
            // 
            // Form_Smart_Validatiy_Hotspot
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(111)))), ((int)(((byte)(106)))), ((int)(((byte)(143)))));
            this.BorderSize = 5;
            this.Caption = "صلاحيات الهوتسبوت";
            this.ClientSize = new System.Drawing.Size(1000, 638);
            this.HelpButton = true;
            this.Location = new System.Drawing.Point(0, 0);
            this.Name = "Form_Smart_Validatiy_Hotspot";
            this.Padding = new System.Windows.Forms.Padding(5);
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.Text = "صلاحيات الهوتسبوت";
            this.Load += new System.EventHandler(this.Form_Smart_Validatiy_Hotspot_Load);
            this.pnlClientArea.ResumeLayout(false);
            this.rjPanel1.ResumeLayout(false);
            this.rjPanel1.PerformLayout();
            this.pnl_usermanger.ResumeLayout(false);
            this.pnl_usermanger.PerformLayout();
            this.rjPanel2.ResumeLayout(false);
            this.panel1.ResumeLayout(false);
            this.panel1.PerformLayout();
            this.flowLayoutPanel2.ResumeLayout(false);
            this.flowLayoutPanel2.PerformLayout();
            this.flowLayoutPanel1.ResumeLayout(false);
            this.flowLayoutPanel1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dgv)).EndInit();
            this.rjPanel3.ResumeLayout(false);
            this.rjPanel3.PerformLayout();
            this.rjPanel4.ResumeLayout(false);
            this.rjPanel4.PerformLayout();
            this.ResumeLayout(false);

        }

        #endregion
        public RJControls.RJPanel pnl_usermanger;
        private RJControls.RJLabel lbl_descr;
        private RJControls.RJButton btn_AddScript_toMickrotik;
        private RJControls.RJLabel lbl_Title1;
        private RJControls.RJDataGridView dgv;
        private RJControls.RJLabel rjLabel1;
        private RJControls.RJLabel rjLabel4;
        private RJControls.RJLabel lbl_DayOrHour;
        private RJControls.RJRadioButton Radio_RemoeLastSetting;
        private RJControls.RJRadioButton Radio_Add_SideLastSetting;
        private RJControls.RJPanel rjPanel1;
        private RJControls.RJPanel rjPanel2;
        private RJControls.RJLabel rjLabel3;
        private RJControls.RJLabel rjLabel5;
        private RJControls.RJLabel rjLabel6;
        private RJControls.RJLabel lbl_Save_session;
        private RJControls.RJLabel lbl_descr2;
        private RJControls.RJLabel rjLabel2;
        public RJControls.RJPanel rjPanel3;
        private RJControls.RJLabel rjLabel7;
        private RJControls.RJButton btn_Upgrade;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel1;
        private System.Windows.Forms.FlowLayoutPanel flowLayoutPanel2;
        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.Timer timer1;
        private RJControls.RJButton btn_Refresh;
        public RJControls.RJPanel rjPanel4;
        private RJControls.RJLabel rjLabel8;
        private RJControls.RJLabel lbl_note;
        private RJControls.RJLabel rjLabel9;
        private RJControls.RJLabel rjLabel10;
    }
}