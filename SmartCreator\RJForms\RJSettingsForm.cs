﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using SmartCreator.Properties;
using SmartCreator.RJForms;
using SmartCreator.Settings;
using SmartCreator.Utils;

namespace SmartCreator.RJForms
{
    public partial class RJSettingsForm : RJChildForm
    {
        /// <summary>
        /// This class inherits from the <see cref="RJChildForm"/>  class
        /// </summary>
        /// 

        #region -> Constructor

        public RJSettingsForm()
        {
            //if (UIAppearance.Language_ar)
            //{
            //    this.RightToLeft = RightToLeft.Yes;
            //    this.RightToLeftLayout = true;
            //}
            //This form was built by the designer.

            InitializeComponent();
            if (UIAppearance.Theme == UITheme.Dark)
            {
                rjPanel3.Customizable = false;
                rjPanel4.Customizable = false;
                rjPanel5.Customizable = false;
            }

            rjLabel1.Text = "اعدادت المظهر";

            Set_Font();


            //rjLabel1.Font=rjLabel12.Font=  Program.GetCustomFont(Resources.DroidSansArabic, 13 , FontStyle.Bold);//Default font
            //rjLabel3.Font = rjLabel2.Font=rjLabel7.Font=rjLabel16.Font=rjLabel9.Font=rjLabel11.Font=rjLabel14.Font=rjLabel18.Font=
            //    Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Regular);

            //lblVista.Font= rjLabel5.Font = rjLabel4.Font = rjLabel15.Font = rjLabel6.Font = rjLabel8.Font = rjLabel10.Font = rjLabel13.Font =
            //   Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //btnApplyChanges.Font= Program.GetCustomFont(Resources.DroidSansArabic, 12 , FontStyle.Bold);
        }
        #endregion

        #region -> Event Methods

        private void Set_Font()
        {
            lbl_show_image_number.ForeColor = Color.Blue;
            if(UIAppearance.Theme==UITheme.Dark)
                lbl_show_image_number.ForeColor= utils.Dgv_DarkColor;

            rjLabel5.Font =
               rjLabel4.Font =
               rjLabel15.Font =
               rjLabel8.Font =
               rjLabel6.Font =

               rjLabel10.Font =
               rjLabel13.Font =
               rjLabel17.Font =
               lblRestartApp.Font =
               lblVista.Font =
               rjLabel20.Font =
               rjLabel22.Font =
              Program.GetCustomFont(Resources.DroidSansArabic, 9, FontStyle.Regular);


            rjLabel21.Font =
            rjLabel3.Font =
            rjLabel2.Font =
            rjLabel16.Font =
            rjLabel7.Font =
            rjLabel9.Font =
            rjLabel11.Font =
            rjLabel14.Font =
            rjLabel18.Font =
            rjLabel19.Font =
            Program.GetCustomFont(Resources.DroidSansArabic, 9 , FontStyle.Regular);
            //Program.GetCustomFont(Resources.DroidSansArabic, 10 , FontStyle.Regular);


            //rjLabel3.Font = rjLabel2.Font = rjLabel7.Font = rjLabel16.Font = rjLabel9.Font = rjLabel11.Font = rjLabel14.Font = rjLabel18.Font =
            //Program.GetCustomFont(Resources.DroidSansArabic, 11 , FontStyle.Regular);

               
            btnApplyChanges.Font = Program.GetCustomFont(Resources.DroidKufi_Bold, 12   , FontStyle.Bold);

            utils.Control_textSize(pnlClientArea);


        }
        private void RJSettingsForm_Load(object sender, EventArgs e)
        {
            LoadAppearanceSettings(); //Load and display the current appearance settings on the form.
        Set_Font();
        }

        private void btnApplyChanges_Click(object sender, EventArgs e)
        {
            SaveAppearanceSettings(); //Save appearance settings
        }

        private void lblRestartApp_Click(object sender, EventArgs e)
        {//Restart application

            Application.Restart();
            Environment.Exit(0);
            /* Note: When executing the application from visual studio, the configuration file is saved 
              * in the folder SmartCreator.vshost.exe. And when restarting the application the configuration
              * file is obtained from the C:\Users\<USER>\AppData\Local\SmartCreator\SmartCreator.exe folder, 
			  * since after restarting the application it
              * runs independently of visual studio, so it will not load the settings you established on the 
              * first restart since it will take the file of 
			  * C:\Users\<USER>\AppData\Local\SmartCreator\SmartCreator.exe configuration. If you want to 
              * test and apply the settings established when you are developing the application, I recommend 
              * you close the application (or stop debugging) and rerun from visual studio or compile the project
              * and run the application directly from the project's bin folder.*/
        }
        #endregion

        #region -> Private methods

        private void LoadAppearanceSettings()
        {//Display the current appearance settings on the form.

            //Theme
            if (UIAppearance.Theme == UITheme.Dark)
                rbDarkTheme.Checked = true;
            else
                rbLightTheme.Checked = true;

            //Style
            cbStyles.DataSource = Enum.GetValues(typeof(UIStyle));
            cbStyles.SelectedIndex = (int)UIAppearance.Style;

            //Form Border Size
            tbmFormBorderSize.Value = UIAppearance.FormBorderSize;

            //Is Color Form Border
            tbColorFormBorder.Checked = UIAppearance.FormBorderColor == RJColors.DefaultFormBorderColor ? false : true;

            //Child Form Marker
            tbChildFormMarker.Checked = UIAppearance.ChildFormMarker;

            //Form Icon in Activated Menu Item
            tbIconMenuItem.Checked = UIAppearance.FormIconActiveMenuItem;

            //MDI Desktop Panel
            tbMultiChildForms.Checked = UIAppearance.MultiChildForms;

            //Preview
            panelBorde.Padding = new Padding(UIAppearance.FormBorderSize);
            panelBorde.BackColor = UIAppearance.FormBorderColor;
            panelBackground.BackColor = UIAppearance.BackgroundColor;

            if (UIAppearance.Style == UIStyle.Supernova)
                panelTitleBar.BackColor = ColorEditor.Darken(UIAppearance.BackgroundColor, 9);

            else panelTitleBar.BackColor = UIAppearance.PrimaryStyleColor;

            if (UIAppearance.Language_ar)
            {
                rbArabicLang.Checked = true;
                rbEnglishLang.Checked = false;
            }
            else { rbEnglishLang.Checked = true;
                rbArabicLang.Checked = false;
            }

             if (UIAppearance.DGV_RTL)
            {
                Rb_Number_Arabic.Checked = true;
                Rb_Number_English.Checked = false;
            }
            else {
                Rb_Number_English.Checked = true;
                Rb_Number_Arabic.Checked = false;
            }





        }
        private void SaveAppearanceSettings()
        { 
            //Save appearance settings
            Settings.SettingsManager.SaveAppearanceSettings(rbDarkTheme.Checked ? (int)UITheme.Dark : (int)UITheme.Light,/*Theme*/
                                                            (int)cbStyles.SelectedValue,/*Style*/
                                                            tbmFormBorderSize.Value,/*Form border size*/
                                                            tbColorFormBorder.Checked,/*Color form border*/
                                                            tbChildFormMarker.Checked,/*Child form marker*/
                                                            tbIconMenuItem.Checked,/*Form icon in activated menu item*/
                                                            tbMultiChildForms.Checked,/*Multiple child forms*/
                                                            rbArabicLang.Checked,
                                                            Rb_Number_Arabic.Checked 
                                                            );
            //Show restart message
            string msg = "Please Restart the application to view changes\nRestart now?";
            string msg_title = "Message";
            if (rbArabicLang.Checked)
            {
                msg = "من فظلك اعد تشغيل البرنامج لعرض التغيرات \n اعادة التشغيل الان";
                msg_title = "رسالة";
            }
            var result = RJMessageBox.Show(msg,
                                           msg_title,
                                            MessageBoxButtons.YesNo,
                                            MessageBoxIcon.Question);

            if (result == DialogResult.Yes)//Restart application
            {
                Application.Restart();
                Environment.Exit(0);
                 /* Note: When executing the application from visual studio, the configuration file is saved 
              * in the folder SmartCreator.vshost.exe. And when restarting the application the configuration
              * file is obtained from the C:\Users\<USER>\AppData\Local\SmartCreator\SmartCreator.exe folder, 
			  * since after restarting the application it
              * runs independently of visual studio, so it will not load the settings you established on the 
              * first restart since it will take the file of 
			  * C:\Users\<USER>\AppData\Local\SmartCreator\SmartCreator.exe configuration. If you want to 
              * test and apply the settings established when you are developing the application, I recommend 
              * you close the application (or stop debugging) and rerun from visual studio or compile the project
              * and run the application directly from the project's bin folder.*/
            }
            else //Show restart message label in case you did not reboot from the message box.
            {
                lblRestartApp.Visible = true;
            }

        }
        #endregion

        #region -> Preview

        private void rbLightTheme_CheckedChanged(object sender, EventArgs e)
        {
            if (rbLightTheme.Checked)
                panelBackground.BackColor = RJColors.LightBackground;
            else panelBackground.BackColor = RJColors.DarkBackground;

            if (cbStyles.SelectedIndex == (int)UIStyle.Supernova)
                panelTitleBar.BackColor = ColorEditor.Darken(panelBackground.BackColor, 9);
        }

        private void cbStyles_OnSelectedIndexChanged(object sender, EventArgs e)
        {
            UIStyle style = (UIStyle)cbStyles.SelectedIndex;
            switch (style)
            {
                case UIStyle.Axolotl:
                    panelTitleBar.BackColor = RJColors.Axolotl;
                    break;
                case UIStyle.FireOpal:
                    panelTitleBar.BackColor = RJColors.FireOpal;
                    break;
                case UIStyle.Forest:
                    panelTitleBar.BackColor = RJColors.Forest;
                    break;
                case UIStyle.Lisianthus:
                    panelTitleBar.BackColor = RJColors.Lisianthus;
                    break;
                case UIStyle.Neptune:
                    panelTitleBar.BackColor = RJColors.Neptune;
                    break;
                case UIStyle.Petunia:
                    panelTitleBar.BackColor = RJColors.Petunia;
                    break;
                case UIStyle.Ruby:
                    panelTitleBar.BackColor = RJColors.Ruby;
                    break;
                case UIStyle.Sky:
                    panelTitleBar.BackColor = RJColors.Sky;
                    break;
                case UIStyle.Spinel:
                    panelTitleBar.BackColor = RJColors.Spinel;
                    break;
                case UIStyle.Supernova:
                    panelTitleBar.BackColor = ColorEditor.Darken(panelBackground.BackColor, 9);
                    break;
            }
            if (tbColorFormBorder.Checked)
            {
                if (cbStyles.SelectedIndex == (int)UIStyle.Supernova)
                    panelBorde.BackColor = RJColors.FantasyColorScheme1;
                else panelBorde.BackColor = panelTitleBar.BackColor;
            }
            else panelBorde.BackColor = RJColors.DefaultFormBorderColor;
        }

        private void tbmFormBorderSize_Scroll(object sender, EventArgs e)
        {
            panelBorde.Padding = new Padding(tbmFormBorderSize.Value);
        }

        private void tbColorFormBorder_CheckedChanged(object sender, EventArgs e)
        {
            if (tbColorFormBorder.Checked)
            {
                if (cbStyles.SelectedIndex == (int)UIStyle.Supernova)
                    panelBorde.BackColor = RJColors.FantasyColorScheme1;
                else panelBorde.BackColor = panelTitleBar.BackColor;
            }
            else panelBorde.BackColor = RJColors.DefaultFormBorderColor;
        }
        #endregion

        private void lbl_show_image_number_Click(object sender, EventArgs e)
        {
            string png = "تغير الاقام من الهندي الي العربي.PNG";

            try
            {
                System.Diagnostics.Process.Start(png);
            }
            catch (Exception ex) { /*MessageBox.Show(ex.Message.ToString());*/ }

        }
    }
}
